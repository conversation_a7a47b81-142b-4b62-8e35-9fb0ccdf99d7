# University Portal API - Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3001
HOST=0.0.0.0

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/university_portal
MONGODB_OPTIONS='{"maxPoolSize": 100, "serverSelectionTimeoutMS": 5000, "socketTimeoutMS": 45000}'

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_CLUSTER_NODES=redis://localhost:7000,redis://localhost:7001,redis://localhost:7002,redis://localhost:7003,redis://localhost:7004,redis://localhost:7005
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=university-portal-api
JWT_AUDIENCE=university-portal-client

# Security Configuration
BCRYPT_ROUNDS=12
ARGON2_MEMORY_LIMIT=65536
ARGON2_TIME_COST=3
ARGON2_PARALLELISM=1

# Rate Limiting
RATE_LIMIT_WINDOW_MS=300000
RATE_LIMIT_MAX_REQUESTS=120
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=application/pdf,image/jpeg,image/png,image/gif
UPLOAD_PATH=./uploads
S3_BUCKET=university-portal-files
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your-s3-access-key
S3_SECRET_ACCESS_KEY=your-s3-secret-key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=University Portal

# MFA Configuration
MFA_ISSUER=University Portal
MFA_ALGORITHM=sha1
MFA_DIGITS=6
MFA_PERIOD=30

# Monitoring & Observability
PROMETHEUS_PORT=9090
JAEGER_ENDPOINT=http://localhost:14268/api/traces
LOG_LEVEL=info
LOG_FORMAT=json

# Socket.io Configuration
SOCKET_RATE_LIMIT_MAX=10
SOCKET_RATE_LIMIT_WINDOW=1000

# Cache Configuration
CACHE_TTL_COURSES=60
CACHE_TTL_GRADES=60
CACHE_TTL_USERS=300
CACHE_TTL_PERMISSIONS=1800

# Load Balancer Configuration
TRUST_PROXY=true
X_FORWARDED_FOR=true

# Kubernetes Configuration (for production)
KUBERNETES_NAMESPACE=university-portal
KUBERNETES_SERVICE_NAME=university-portal-api
KUBERNETES_REPLICAS=20
KUBERNETES_MAX_REPLICAS=60
KUBERNETES_CPU_TARGET=60
KUBERNETES_MEMORY_LIMIT=512Mi
KUBERNETES_CPU_LIMIT=500m

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Feature Flags
ENABLE_MFA=true
ENABLE_FILE_UPLOAD=true
ENABLE_REAL_TIME=true
ENABLE_CACHING=true
ENABLE_METRICS=true
ENABLE_TRACING=true

# Development Only
SEED_DATABASE=true
HOT_RELOAD=true
DEBUG_MODE=false
