# Payment Integration Environment Configuration

# M-Pesa Configuration
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_BUSINESS_SHORT_CODE=your_business_short_code
MPESA_PASSKEY=your_mpesa_passkey
MPESA_ENVIRONMENT=sandbox
MPESA_CALLBACK_URL=https://your-domain.com/api/payments/webhook/mpesa
MPESA_WEBHOOK_SECRET=your_webhook_secret

# JamboPay Configuration
JAMBOPAY_API_KEY=your_jambopay_api_key
JAMBOPAY_MERCHANT_ID=your_jambopay_merchant_id
JAMBOPAY_ENVIRONMENT=sandbox
JAMBOPAY_CALLBACK_URL=https://your-domain.com/api/payments/webhook/jambopay

# I&M Bank Configuration
IM_BANK_CLIENT_ID=your_im_bank_client_id
IM_BANK_CLIENT_SECRET=your_im_bank_client_secret
IM_BANK_BASE_URL=https://api.imbank.com
IM_BANK_ENVIRONMENT=sandbox

# KCB BUNI Configuration
KCB_BUNI_API_KEY=your_kcb_buni_api_key
KCB_BUNI_MERCHANT_ID=your_kcb_buni_merchant_id
KCB_BUNI_BASE_URL=https://api.buni.kcbgroup.com
KCB_BUNI_ENVIRONMENT=sandbox

# Pesalink Configuration
PESALINK_API_KEY=your_pesalink_api_key
PESALINK_MERCHANT_ID=your_pesalink_merchant_id
PESALINK_BASE_URL=https://api.pesalink.co.ke
PESALINK_ENVIRONMENT=sandbox

# Payment Security
PAYMENT_ENCRYPTION_KEY=your_32_character_encryption_key
PAYMENT_WEBHOOK_TIMEOUT=30000
PAYMENT_RETRY_ATTEMPTS=3
PAYMENT_RETRY_DELAY=30000

# Payment Limits (in KES)
PAYMENT_MIN_AMOUNT=1
PAYMENT_MAX_AMOUNT=150000
PAYMENT_DAILY_LIMIT=500000
PAYMENT_MONTHLY_LIMIT=2000000

# Notification Settings
PAYMENT_NOTIFICATION_EMAIL=true
PAYMENT_NOTIFICATION_SMS=true
PAYMENT_NOTIFICATION_PUSH=true

# Monitoring and Logging
PAYMENT_LOG_LEVEL=info
PAYMENT_AUDIT_LOG=true
PAYMENT_METRICS_ENABLED=true

# Database Configuration for Payments
PAYMENT_DB_CONNECTION_TIMEOUT=30000
PAYMENT_DB_RETRY_ATTEMPTS=3

# Rate Limiting
PAYMENT_RATE_LIMIT_WINDOW=900000
PAYMENT_RATE_LIMIT_MAX_REQUESTS=100
PAYMENT_RATE_LIMIT_STUDENT_WINDOW=3600000
PAYMENT_RATE_LIMIT_STUDENT_MAX_REQUESTS=10
