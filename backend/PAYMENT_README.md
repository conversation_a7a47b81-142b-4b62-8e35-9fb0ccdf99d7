# Kenyan Payment Integration for University Portal

## 🏦 **Overview**

This document provides a comprehensive implementation of Kenyan payment methods integration for the university portal, including M-Pesa, JamboPay, and bank payment APIs (I&M Bank, KCB BUNI, Pesalink).

## 🚀 **Quick Start**

### **1. Environment Setup**

```bash
# Copy payment environment template
cp payment.env.example .env

# Edit environment variables
nano .env
```

### **2. Install Dependencies**

```bash
npm install axios crypto jsonwebtoken
```

### **3. Database Setup**

```bash
# Run database migrations
npm run db:migrate

# Seed payment methods
npm run seed:payment-methods
```

### **4. Start Development Server**

```bash
npm run dev
```

## 📋 **Supported Payment Methods**

### **Mobile Payments**
- **M-Pesa STK Push** - Safaricom's mobile payment solution
- **JamboPay** - Multi-channel payment platform

### **Bank Payments**
- **I&M Bank API** - Direct bank transfer integration
- **KCB BUNI API** - Kenya Commercial Bank payment gateway
- **Pesalink** - Inter-bank transfer service

## 🔧 **API Endpoints**

### **Payment Methods**
```http
GET /api/payments/methods
```
Returns available payment methods with fees and limits.

### **Pending Fees**
```http
GET /api/payments/fees/pending
```
Returns student's pending fees for payment.

### **Initiate Payment**
```http
POST /api/payments/initiate
Content-Type: application/json
Authorization: Bearer <token>

{
  "feeId": "fee_id",
  "paymentMethod": "mpesa",
  "phoneNumber": "************"
}
```

### **Transaction Status**
```http
GET /api/payments/transaction/:referenceNumber
```
Returns payment transaction status and details.

### **Payment History**
```http
GET /api/payments/history?page=1&limit=20&status=completed
```
Returns student's payment history with pagination.

### **Retry Payment**
```http
POST /api/payments/retry/:transactionId
```
Retries a failed payment transaction.

### **Webhook Endpoints**
```http
POST /api/payments/webhook/mpesa
POST /api/payments/webhook/jambopay
POST /api/payments/webhook/im-bank
```

## 🔐 **Security Features**

### **Authentication & Authorization**
- JWT token-based authentication
- Role-based access control (student, admin, finance)
- Rate limiting per user and IP

### **Data Protection**
- Sensitive data encryption
- PCI compliance measures
- Audit logging for all transactions

### **Webhook Security**
- Signature verification
- IP whitelisting
- Payload validation

## 📊 **Database Schema**

### **Payment Methods**
```javascript
{
  name: String,           // "M-Pesa", "JamboPay"
  code: String,           // "mpesa", "jambopay"
  provider: String,       // "mpesa", "jambopay", "im_bank"
  isActive: Boolean,      // true/false
  fees: {
    fixed: Number,        // Fixed fee amount
    percentage: Number    // Percentage fee
  },
  limits: {
    min: Number,          // Minimum amount
    max: Number           // Maximum amount
  }
}
```

### **Payment Transactions**
```javascript
{
  student: ObjectId,      // Reference to User
  fee: ObjectId,          // Reference to Fee
  amount: Number,         // Payment amount
  currency: String,       // "KES"
  paymentMethod: ObjectId, // Reference to PaymentMethod
  providerTransactionId: String, // Provider's transaction ID
  status: String,         // pending, initiated, completed, failed
  referenceNumber: String, // Unique reference number
  metadata: {
    phoneNumber: String,
    accountNumber: String,
    bankCode: String,
    providerResponse: Object,
    callbackData: Object
  },
  initiatedAt: Date,
  completedAt: Date,
  failedAt: Date,
  failureReason: String
}
```

## 🔄 **Payment Flow**

### **1. Payment Initiation**
```
Student → Frontend → API → Payment Service → Provider API
```

### **2. Provider Processing**
```
Provider API → STK Push/Transfer → User Device → User Confirmation
```

### **3. Webhook Processing**
```
Provider → Webhook → API → Database Update → Real-time Notification
```

### **4. Reconciliation**
```
Scheduled Job → Provider API → Transaction Verification → Database Update
```

## 🧪 **Testing**

### **Unit Tests**
```bash
npm test -- --grep "Payment"
```

### **Integration Tests**
```bash
npm run test:integration -- --grep "Payment Integration"
```

### **Load Testing**
```bash
npm run test:load -- --grep "Payment Load"
```

### **Webhook Testing**
```bash
# Test M-Pesa webhook
curl -X POST http://localhost:3001/api/payments/webhook/mpesa \
  -H "Content-Type: application/json" \
  -H "x-mpesa-signature: test-signature" \
  -d '{
    "Body": {
      "stkCallback": {
        "ResultCode": 0,
        "CheckoutRequestID": "test-id",
        "ResultDesc": "Success"
      }
    }
  }'
```

## 📈 **Monitoring & Analytics**

### **Key Metrics**
- Payment success rate
- Average processing time
- Failed payment reasons
- Payment method distribution
- Daily/monthly transaction volume

### **Alerts**
- High failure rate (>5%)
- Long processing times (>30s)
- Webhook processing failures
- Reconciliation discrepancies

### **Dashboards**
- Real-time payment status
- Transaction volume trends
- Payment method performance
- Error rate monitoring

## 🚨 **Error Handling**

### **Common Error Scenarios**
1. **Network Timeouts** - Automatic retry with exponential backoff
2. **Invalid Phone Numbers** - Validation and user feedback
3. **Amount Limits** - Provider-specific limit enforcement
4. **Webhook Failures** - Retry mechanism with dead letter queue
5. **Reconciliation Discrepancies** - Manual review and resolution

### **Error Codes**
```javascript
{
  "PAYMENT_INITIATION_FAILED": "Payment could not be initiated",
  "INVALID_PHONE_NUMBER": "Phone number format is invalid",
  "AMOUNT_LIMIT_EXCEEDED": "Payment amount exceeds provider limits",
  "PAYMENT_METHOD_UNAVAILABLE": "Selected payment method is not available",
  "WEBHOOK_SIGNATURE_INVALID": "Webhook signature verification failed",
  "TRANSACTION_NOT_FOUND": "Transaction not found",
  "MAX_RETRIES_EXCEEDED": "Maximum retry attempts exceeded"
}
```

## 🔧 **Configuration**

### **Environment Variables**
```bash
# M-Pesa Configuration
MPESA_CONSUMER_KEY=your_consumer_key
MPESA_CONSUMER_SECRET=your_consumer_secret
MPESA_BUSINESS_SHORT_CODE=your_short_code
MPESA_PASSKEY=your_passkey
MPESA_ENVIRONMENT=sandbox
MPESA_CALLBACK_URL=https://your-domain.com/api/payments/webhook/mpesa

# JamboPay Configuration
JAMBOPAY_API_KEY=your_api_key
JAMBOPAY_MERCHANT_ID=your_merchant_id
JAMBOPAY_ENVIRONMENT=sandbox
JAMBOPAY_CALLBACK_URL=https://your-domain.com/api/payments/webhook/jambopay

# Bank APIs
IM_BANK_CLIENT_ID=your_client_id
IM_BANK_CLIENT_SECRET=your_client_secret
KCB_BUNI_API_KEY=your_api_key
PESALINK_API_KEY=your_api_key

# Security
PAYMENT_ENCRYPTION_KEY=your_32_character_key
PAYMENT_WEBHOOK_SECRET=your_webhook_secret
```

### **Payment Limits**
```javascript
{
  "mpesa": {
    "min": 1,
    "max": 150000,
    "daily": 300000
  },
  "jambopay": {
    "min": 1,
    "max": 100000,
    "daily": 500000
  },
  "bank_transfer": {
    "min": 100,
    "max": 1000000,
    "daily": 2000000
  }
}
```

## 🚀 **Deployment**

### **Production Checklist**
- [ ] SSL certificates configured
- [ ] Webhook URLs updated to production
- [ ] Environment variables set
- [ ] Database indexes created
- [ ] Monitoring alerts configured
- [ ] Load balancer health checks
- [ ] Backup strategy implemented

### **Docker Deployment**
```bash
# Build payment service
docker build -t university-portal-payments .

# Run with environment
docker run -d \
  --name payment-service \
  --env-file .env \
  -p 3001:3001 \
  university-portal-payments
```

### **Kubernetes Deployment**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: payment-service
  template:
    metadata:
      labels:
        app: payment-service
    spec:
      containers:
      - name: payment-service
        image: university-portal-payments:latest
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: MPESA_ENVIRONMENT
          value: "production"
```

## 📚 **Documentation**

### **API Documentation**
- [Swagger UI](http://localhost:3001/api-docs)
- [Postman Collection](./docs/postman-collection.json)
- [Webhook Specifications](./docs/webhook-specs.md)

### **Integration Guides**
- [M-Pesa Integration](./docs/mpesa-integration.md)
- [JamboPay Integration](./docs/jambopay-integration.md)
- [Bank API Integration](./docs/bank-integration.md)

### **Troubleshooting**
- [Common Issues](./docs/troubleshooting.md)
- [Error Codes Reference](./docs/error-codes.md)
- [Webhook Debugging](./docs/webhook-debugging.md)

## 🤝 **Support**

### **Contact Information**
- **Technical Support**: <EMAIL>
- **Payment Issues**: <EMAIL>
- **Emergency**: +254-XXX-XXXX

### **Business Hours**
- **Monday - Friday**: 8:00 AM - 6:00 PM EAT
- **Saturday**: 9:00 AM - 1:00 PM EAT
- **Sunday**: Closed

### **Emergency Support**
- **24/7 Hotline**: +254-XXX-XXXX
- **Email**: <EMAIL>

## 📄 **License**

This payment integration is proprietary software developed for the University Portal. All rights reserved.

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Maintainer**: University IT Department
