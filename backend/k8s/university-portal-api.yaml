apiVersion: v1
kind: Namespace
metadata:
  name: university-portal
  labels:
    name: university-portal
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: university-portal-config
  namespace: university-portal
data:
  NODE_ENV: "production"
  PORT: "3001"
  MONGODB_URI: "mongodb://mongodb-router:27017/university_portal"
  REDIS_CLUSTER_NODES: "redis://redis-node-1:7000,redis://redis-node-2:7001,redis://redis-node-3:7002,redis://redis-node-4:7003,redis://redis-node-5:7004,redis://redis-node-6:7005"
  JWT_ISSUER: "university-portal-api"
  JWT_AUDIENCE: "university-portal-client"
  ENABLE_MFA: "true"
  ENABLE_FILE_UPLOAD: "true"
  ENABLE_REAL_TIME: "true"
  ENABLE_CACHING: "true"
  ENABLE_METRICS: "true"
  ENABLE_TRACING: "true"
---
apiVersion: v1
kind: Secret
metadata:
  name: university-portal-secrets
  namespace: university-portal
type: Opaque
data:
  JWT_SECRET: eW91ci1zdXBlci1zZWNyZXQtand0LWtleS1jaGFuZ2UtaW4tcHJvZHVjdGlvbg==
  JWT_REFRESH_SECRET: eW91ci1zdXBlci1zZWNyZXQtcmVmcmVzaC1rZXktY2hhbmdlLWluLXByb2R1Y3Rpb24=
  REDIS_PASSWORD: ""
  SMTP_USER: ""
  SMTP_PASS: ""
  S3_ACCESS_KEY_ID: ""
  S3_SECRET_ACCESS_KEY: ""
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: university-portal-api
  namespace: university-portal
  labels:
    app: university-portal-api
spec:
  replicas: 20
  selector:
    matchLabels:
      app: university-portal-api
  template:
    metadata:
      labels:
        app: university-portal-api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3001"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: university-portal-api
        image: university-portal-api:latest
        ports:
        - containerPort: 3001
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        envFrom:
        - configMapRef:
            name: university-portal-config
        - secretRef:
            name: university-portal-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
      volumes:
      - name: logs
        emptyDir: {}
      - name: uploads
        emptyDir: {}
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: university-portal-api-service
  namespace: university-portal
  labels:
    app: university-portal-api
spec:
  selector:
    app: university-portal-api
  ports:
  - name: http
    port: 80
    targetPort: 3001
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: 9090
    protocol: TCP
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: university-portal-api-hpa
  namespace: university-portal
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: university-portal-api
  minReplicas: 20
  maxReplicas: 60
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 5
        periodSeconds: 60
      selectPolicy: Max
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: university-portal-ingress
  namespace: university-portal
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rate-limit: "120"
    nginx.ingress.kubernetes.io/rate-limit-window: "5m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://university-portal.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Content-Type, Authorization, X-Requested-With"
spec:
  tls:
  - hosts:
    - api.university-portal.com
    secretName: university-portal-tls
  rules:
  - host: api.university-portal.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: university-portal-api-service
            port:
              number: 80
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: university-portal-api-pdb
  namespace: university-portal
spec:
  minAvailable: 10
  selector:
    matchLabels:
      app: university-portal-api
---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: university-portal-api-monitor
  namespace: university-portal
  labels:
    app: university-portal-api
spec:
  selector:
    matchLabels:
      app: university-portal-api
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
