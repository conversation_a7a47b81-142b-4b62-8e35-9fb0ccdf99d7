import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');
const requestCount = new Counter('requests');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 100 },   // Ramp up to 100 users
    { duration: '5m', target: 100 },   // Stay at 100 users
    { duration: '2m', target: 500 },   // Ramp up to 500 users
    { duration: '5m', target: 500 },   // Stay at 500 users
    { duration: '2m', target: 1000 },  // Ramp up to 1000 users
    { duration: '5m', target: 1000 },  // Stay at 1000 users
    { duration: '2m', target: 2000 },  // Ramp up to 2000 users
    { duration: '5m', target: 2000 }, // Stay at 2000 users
    { duration: '2m', target: 5000 }, // Ramp up to 5000 users (target)
    { duration: '15m', target: 5000 }, // Sustain 5000 users for 15 minutes
    { duration: '2m', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<200'], // 95% of requests must complete below 200ms
    http_req_failed: ['rate<0.01'],   // Error rate must be below 1%
    errors: ['rate<0.01'],            // Custom error rate below 1%
    response_time: ['p(95)<200'],     // 95th percentile response time below 200ms
  },
};

// Base URL
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3001';

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'password123', role: 'student' },
  { email: '<EMAIL>', password: 'password123', role: 'student' },
  { email: '<EMAIL>', password: 'password123', role: 'lecturer' },
  { email: '<EMAIL>', password: 'password123', role: 'admin' },
];

// Global variables
let authTokens = {};
let userIds = {};

export function setup() {
  console.log('Setting up load test...');
  
  // Register test users
  for (const user of testUsers) {
    const registerResponse = http.post(`${BASE_URL}/api/auth/register`, JSON.stringify({
      email: user.email,
      password: user.password,
      name: user.email.split('@')[0],
      roles: [user.role],
      ...(user.role === 'student' && { studentId: `STU${Math.random().toString().substr(2, 6)}` }),
      ...(user.role === 'lecturer' && { staffId: `STF${Math.random().toString().substr(2, 6)}` }),
    }), {
      headers: { 'Content-Type': 'application/json' },
    });
    
    if (registerResponse.status === 201 || registerResponse.status === 409) {
      // Login to get tokens
      const loginResponse = http.post(`${BASE_URL}/api/auth/login`, JSON.stringify({
        email: user.email,
        password: user.password,
      }), {
        headers: { 'Content-Type': 'application/json' },
      });
      
      if (loginResponse.status === 200) {
        const loginData = loginResponse.json();
        authTokens[user.email] = loginData.data.tokens.accessToken;
        userIds[user.email] = loginData.data.user.id;
      }
    }
  }
  
  console.log(`Setup complete. Authenticated ${Object.keys(authTokens).length} users.`);
  return { authTokens, userIds };
}

export default function(data) {
  const userEmail = testUsers[Math.floor(Math.random() * testUsers.length)].email;
  const token = data.authTokens[userEmail];
  const userId = data.userIds[userEmail];
  
  if (!token) {
    console.error(`No token for user ${userEmail}`);
    return;
  }
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
  
  // Test different endpoints based on user role
  const userRole = testUsers.find(u => u.email === userEmail).role;
  
  switch (userRole) {
    case 'student':
      testStudentEndpoints(headers, userId);
      break;
    case 'lecturer':
      testLecturerEndpoints(headers, userId);
      break;
    case 'admin':
      testAdminEndpoints(headers, userId);
      break;
  }
  
  sleep(1); // 1 second between requests
}

function testStudentEndpoints(headers, userId) {
  const endpoints = [
    { method: 'GET', url: '/api/users/profile' },
    { method: 'GET', url: '/api/courses' },
    { method: 'GET', url: '/api/grades' },
    { method: 'GET', url: '/api/assignments' },
    { method: 'GET', url: '/api/attendance' },
    { method: 'GET', url: '/api/notifications' },
  ];
  
  const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
  const response = http.request(endpoint.method, `${BASE_URL}${endpoint.url}`, null, { headers });
  
  checkResponse(response, endpoint.url);
}

function testLecturerEndpoints(headers, userId) {
  const endpoints = [
    { method: 'GET', url: '/api/users/profile' },
    { method: 'GET', url: '/api/courses' },
    { method: 'GET', url: '/api/assignments' },
    { method: 'GET', url: '/api/attendance' },
    { method: 'GET', url: '/api/admin/users' },
    { method: 'GET', url: '/api/admin/departments' },
  ];
  
  const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
  const response = http.request(endpoint.method, `${BASE_URL}${endpoint.url}`, null, { headers });
  
  checkResponse(response, endpoint.url);
}

function testAdminEndpoints(headers, userId) {
  const endpoints = [
    { method: 'GET', url: '/api/users/profile' },
    { method: 'GET', url: '/api/admin/users' },
    { method: 'GET', url: '/api/admin/courses' },
    { method: 'GET', url: '/api/admin/departments' },
    { method: 'GET', url: '/api/admin/finance' },
    { method: 'GET', url: '/api/admin/reports' },
  ];
  
  const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
  const response = http.request(endpoint.method, `${BASE_URL}${endpoint.url}`, null, { headers });
  
  checkResponse(response, endpoint.url);
}

function checkResponse(response, endpoint) {
  const success = check(response, {
    'status is 200 or 201': (r) => r.status === 200 || r.status === 201,
    'response time < 200ms': (r) => r.timings.duration < 200,
    'no server errors': (r) => r.status < 500,
  });
  
  // Record custom metrics
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
  requestCount.add(1);
  
  if (!success) {
    console.error(`Request failed for ${endpoint}: ${response.status} - ${response.body}`);
  }
}

export function teardown(data) {
  console.log('Load test completed');
  console.log(`Total requests: ${requestCount.count}`);
  console.log(`Error rate: ${errorRate.rate}`);
  console.log(`95th percentile response time: ${responseTime.p(95)}ms`);
}
