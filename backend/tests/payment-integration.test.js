// Payment integration tests for the university portal
import request from 'supertest';
import { app } from '../server.js';
import { PaymentMethod, PaymentTransaction, Fee } from '../models/Payment.js';
import { User } from '../models/index.js';
import jwt from 'jsonwebtoken';

describe('Payment Integration Tests', () => {
  let studentToken: string;
  let adminToken: string;
  let testStudent: any;
  let testFee: any;
  let testPaymentMethod: any;

  beforeAll(async () => {
    // Create test student
    testStudent = new User({
      email: '<EMAIL>',
      password: 'password123',
      name: 'Test Student',
      roles: ['student'],
      studentId: 'STU2024001',
      department: 'Computer Science',
      level: 3,
      programme: 'BSc Computer Science'
    });
    await testStudent.save();

    // Create test fee
    testFee = new Fee({
      student: testStudent._id,
      semester: 'Fall 2024',
      academicYear: '2024/2025',
      feeType: 'tuition',
      amount: 50000,
      totalAmount: 50000,
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      description: 'Tuition fee for Fall 2024'
    });
    await testFee.save();

    // Create test payment method
    testPaymentMethod = new PaymentMethod({
      name: 'M-Pesa',
      code: 'mpesa',
      provider: 'mpesa',
      isActive: true,
      fees: {
        fixed: 0,
        percentage: 0
      },
      limits: {
        min: 1,
        max: 150000
      }
    });
    await testPaymentMethod.save();

    // Generate tokens
    studentToken = jwt.sign(
      { userId: testStudent._id, roles: testStudent.roles },
      process.env.JWT_ACCESS_SECRET!,
      { expiresIn: '15m' }
    );

    const adminUser = new User({
      email: '<EMAIL>',
      password: 'password123',
      name: 'Admin User',
      roles: ['admin']
    });
    await adminUser.save();

    adminToken = jwt.sign(
      { userId: adminUser._id, roles: adminUser.roles },
      process.env.JWT_ACCESS_SECRET!,
      { expiresIn: '15m' }
    );
  });

  afterAll(async () => {
    // Clean up test data
    await User.deleteMany({ email: { $in: ['<EMAIL>', '<EMAIL>'] } });
    await Fee.deleteMany({ student: testStudent._id });
    await PaymentMethod.deleteMany({ code: 'mpesa' });
    await PaymentTransaction.deleteMany({ student: testStudent._id });
  });

  describe('GET /api/payments/methods', () => {
    it('should return available payment methods', async () => {
      const response = await request(app)
        .get('/api/payments/methods')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/payments/methods')
        .expect(401);
    });
  });

  describe('GET /api/payments/fees/pending', () => {
    it('should return student pending fees', async () => {
      const response = await request(app)
        .get('/api/payments/fees/pending')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/payments/fees/pending')
        .expect(401);
    });
  });

  describe('POST /api/payments/initiate', () => {
    it('should initiate M-Pesa payment successfully', async () => {
      const paymentData = {
        feeId: testFee._id,
        paymentMethod: 'mpesa',
        phoneNumber: '254712345678'
      };

      const response = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${studentToken}`)
        .send(paymentData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.referenceNumber).toBeDefined();
      expect(response.body.data.amount).toBe(testFee.totalAmount);
      expect(response.body.data.paymentMethod).toBe('M-Pesa');
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${studentToken}`)
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should reject invalid payment method', async () => {
      const paymentData = {
        feeId: testFee._id,
        paymentMethod: 'invalid_method',
        phoneNumber: '254712345678'
      };

      const response = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${studentToken}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid payment method');
    });

    it('should reject payment for non-existent fee', async () => {
      const paymentData = {
        feeId: '507f1f77bcf86cd799439011', // Non-existent ObjectId
        paymentMethod: 'mpesa',
        phoneNumber: '254712345678'
      };

      const response = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${studentToken}`)
        .send(paymentData)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Fee not found');
    });
  });

  describe('GET /api/payments/transaction/:referenceNumber', () => {
    it('should return transaction status', async () => {
      // First create a transaction
      const transaction = new PaymentTransaction({
        student: testStudent._id,
        fee: testFee._id,
        amount: testFee.totalAmount,
        paymentMethod: testPaymentMethod._id,
        referenceNumber: 'TEST001',
        status: 'initiated',
        netAmount: testFee.totalAmount
      });
      await transaction.save();

      const response = await request(app)
        .get(`/api/payments/transaction/${transaction.referenceNumber}`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.referenceNumber).toBe(transaction.referenceNumber);
      expect(response.body.data.status).toBe('initiated');
    });

    it('should return 404 for non-existent transaction', async () => {
      const response = await request(app)
        .get('/api/payments/transaction/NONEXISTENT')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/payments/history', () => {
    it('should return payment history', async () => {
      const response = await request(app)
        .get('/api/payments/history')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.transactions).toBeInstanceOf(Array);
      expect(response.body.data.pagination).toBeDefined();
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/payments/history?page=1&limit=10')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(10);
    });

    it('should filter by status', async () => {
      const response = await request(app)
        .get('/api/payments/history?status=initiated')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('POST /api/payments/retry/:transactionId', () => {
    it('should retry failed payment', async () => {
      // Create a failed transaction
      const transaction = new PaymentTransaction({
        student: testStudent._id,
        fee: testFee._id,
        amount: testFee.totalAmount,
        paymentMethod: testPaymentMethod._id,
        referenceNumber: 'TEST002',
        status: 'failed',
        failureReason: 'Network timeout',
        netAmount: testFee.totalAmount,
        metadata: {
          phoneNumber: '254712345678',
          retryCount: 0,
          maxRetries: 3
        }
      });
      await transaction.save();

      const response = await request(app)
        .post(`/api/payments/retry/${transaction._id}`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('retry initiated');
    });

    it('should reject retry for non-failed transaction', async () => {
      const transaction = new PaymentTransaction({
        student: testStudent._id,
        fee: testFee._id,
        amount: testFee.totalAmount,
        paymentMethod: testPaymentMethod._id,
        referenceNumber: 'TEST003',
        status: 'completed',
        netAmount: testFee.totalAmount
      });
      await transaction.save();

      const response = await request(app)
        .post(`/api/payments/retry/${transaction._id}`)
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Webhook Endpoints', () => {
    describe('POST /api/payments/webhook/mpesa', () => {
      it('should process M-Pesa webhook successfully', async () => {
        const webhookPayload = {
          Body: {
            stkCallback: {
              ResultCode: 0,
              CheckoutRequestID: 'test-checkout-id',
              MerchantRequestID: 'test-merchant-id',
              ResultDesc: 'Success'
            }
          }
        };

        // Create a transaction to match the webhook
        const transaction = new PaymentTransaction({
          student: testStudent._id,
          fee: testFee._id,
          amount: testFee.totalAmount,
          paymentMethod: testPaymentMethod._id,
          referenceNumber: 'TEST004',
          providerTransactionId: 'test-checkout-id',
          status: 'initiated',
          netAmount: testFee.totalAmount
        });
        await transaction.save();

        const response = await request(app)
          .post('/api/payments/webhook/mpesa')
          .send(webhookPayload)
          .expect(200);

        expect(response.body.success).toBe(true);

        // Verify transaction was updated
        const updatedTransaction = await PaymentTransaction.findById(transaction._id);
        expect(updatedTransaction.status).toBe('completed');
        expect(updatedTransaction.completedAt).toBeDefined();
      });

      it('should handle failed payment webhook', async () => {
        const webhookPayload = {
          Body: {
            stkCallback: {
              ResultCode: 1,
              CheckoutRequestID: 'test-checkout-id-2',
              ResultDesc: 'User cancelled'
            }
          }
        };

        // Create a transaction to match the webhook
        const transaction = new PaymentTransaction({
          student: testStudent._id,
          fee: testFee._id,
          amount: testFee.totalAmount,
          paymentMethod: testPaymentMethod._id,
          referenceNumber: 'TEST005',
          providerTransactionId: 'test-checkout-id-2',
          status: 'initiated',
          netAmount: testFee.totalAmount
        });
        await transaction.save();

        const response = await request(app)
          .post('/api/payments/webhook/mpesa')
          .send(webhookPayload)
          .expect(200);

        expect(response.body.success).toBe(true);

        // Verify transaction was updated
        const updatedTransaction = await PaymentTransaction.findById(transaction._id);
        expect(updatedTransaction.status).toBe('failed');
        expect(updatedTransaction.failureReason).toBe('User cancelled');
      });
    });
  });

  describe('Admin Endpoints', () => {
    describe('GET /api/payments/admin/transactions', () => {
      it('should return all transactions for admin', async () => {
        const response = await request(app)
          .get('/api/payments/admin/transactions')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.transactions).toBeInstanceOf(Array);
        expect(response.body.data.summary).toBeDefined();
      });

      it('should require admin role', async () => {
        const response = await request(app)
          .get('/api/payments/admin/transactions')
          .set('Authorization', `Bearer ${studentToken}`)
          .expect(403);

        expect(response.body.success).toBe(false);
      });

      it('should support filtering', async () => {
        const response = await request(app)
          .get('/api/payments/admin/transactions?status=initiated&paymentMethod=mpesa')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.success).toBe(true);
      });
    });

    describe('POST /api/payments/admin/reconcile', () => {
      it('should reconcile payment', async () => {
        const transaction = new PaymentTransaction({
          student: testStudent._id,
          fee: testFee._id,
          amount: testFee.totalAmount,
          paymentMethod: testPaymentMethod._id,
          referenceNumber: 'TEST006',
          status: 'completed',
          netAmount: testFee.totalAmount
        });
        await transaction.save();

        const response = await request(app)
          .post('/api/payments/admin/reconcile')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({ transactionId: transaction._id })
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      it('should require admin role', async () => {
        const response = await request(app)
          .post('/api/payments/admin/reconcile')
          .set('Authorization', `Bearer ${studentToken}`)
          .send({ transactionId: '507f1f77bcf86cd799439011' })
          .expect(403);

        expect(response.body.success).toBe(false);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid phone number format', async () => {
      const paymentData = {
        feeId: testFee._id,
        paymentMethod: 'mpesa',
        phoneNumber: 'invalid-phone'
      };

      const response = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${studentToken}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle payment amount limits', async () => {
      // Create a fee with amount exceeding M-Pesa limit
      const highAmountFee = new Fee({
        student: testStudent._id,
        semester: 'Fall 2024',
        academicYear: '2024/2025',
        feeType: 'tuition',
        amount: 200000, // Exceeds M-Pesa limit
        totalAmount: 200000,
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        description: 'High amount fee'
      });
      await highAmountFee.save();

      const paymentData = {
        feeId: highAmountFee._id,
        paymentMethod: 'mpesa',
        phoneNumber: '254712345678'
      };

      const response = await request(app)
        .post('/api/payments/initiate')
        .set('Authorization', `Bearer ${studentToken}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Payment amount must be between');

      // Clean up
      await Fee.findByIdAndDelete(highAmountFee._id);
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits for payment initiation', async () => {
      const paymentData = {
        feeId: testFee._id,
        paymentMethod: 'mpesa',
        phoneNumber: '254712345678'
      };

      // Make multiple requests quickly
      const promises = Array(15).fill(null).map(() =>
        request(app)
          .post('/api/payments/initiate')
          .set('Authorization', `Bearer ${studentToken}`)
          .send(paymentData)
      );

      const responses = await Promise.allSettled(promises);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(
        (response) => response.status === 'fulfilled' && response.value.status === 429
      );
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });
});
