version: '3.8'

services:
  # MongoDB Single Instance (Development)
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb-data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    networks:
      - university-portal

  # Redis Single Instance (Development)
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - university-portal

  # University Portal API (Development with Hot Reload)
  university-portal-api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
      - "9229:9229" # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongodb:27017/university_portal
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev-jwt-secret-key
      - JWT_REFRESH_SECRET=dev-refresh-secret-key
      - SEED_DATABASE=true
      - HOT_RELOAD=true
      - DEBUG_MODE=true
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mongodb
      - redis
    networks:
      - university-portal
    command: npm run dev

  # Database Seeder
  db-seeder:
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/university_portal
      - SEED_DATABASE=true
    depends_on:
      - mongodb
    networks:
      - university-portal
    command: npm run seed
    profiles:
      - seeding

volumes:
  mongodb-data:
  redis-data:

networks:
  university-portal:
    driver: bridge
