# Default values for university-portal-api
replicaCount: 20

image:
  repository: university-portal-api
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext:
  fsGroup: 1001

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1001

service:
  type: ClusterIP
  port: 80
  targetPort: 3001
  metricsPort: 9090

ingress:
  enabled: true
  className: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rate-limit: "120"
    nginx.ingress.kubernetes.io/rate-limit-window: "5m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
  hosts:
    - host: api.university-portal.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: university-portal-tls
      hosts:
        - api.university-portal.com

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 20
  maxReplicas: 60
  targetCPUUtilizationPercentage: 60
  targetMemoryUtilizationPercentage: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 5
        periodSeconds: 60
      selectPolicy: Max

nodeSelector: {}

tolerations: []

affinity: {}

podDisruptionBudget:
  enabled: true
  minAvailable: 10

# Environment variables
env:
  NODE_ENV: "production"
  PORT: "3001"
  JWT_ISSUER: "university-portal-api"
  JWT_AUDIENCE: "university-portal-client"
  ENABLE_MFA: "true"
  ENABLE_FILE_UPLOAD: "true"
  ENABLE_REAL_TIME: "true"
  ENABLE_CACHING: "true"
  ENABLE_METRICS: "true"
  ENABLE_TRACING: "true"

# Secrets (should be provided via external secret management)
secrets:
  JWT_SECRET: ""
  JWT_REFRESH_SECRET: ""
  REDIS_PASSWORD: ""
  SMTP_USER: ""
  SMTP_PASS: ""
  S3_ACCESS_KEY_ID: ""
  S3_SECRET_ACCESS_KEY: ""

# Database configuration
database:
  uri: "mongodb://mongodb-router:27017/university_portal"
  options:
    maxPoolSize: 100
    serverSelectionTimeoutMS: 5000
    socketTimeoutMS: 45000

# Redis configuration
redis:
  cluster:
    nodes:
      - "redis://redis-node-1:7000"
      - "redis://redis-node-2:7001"
      - "redis://redis-node-3:7002"
      - "redis://redis-node-4:7003"
      - "redis://redis-node-5:7004"
      - "redis://redis-node-6:7005"

# Monitoring configuration
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
    scrapeTimeout: 10s

# Health checks
healthCheck:
  enabled: true
  path: "/health"
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Startup probe
startupProbe:
  enabled: true
  path: "/health"
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 30

# Readiness probe
readinessProbe:
  enabled: true
  path: "/health"
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
