version: '3.8'

services:
  # MongoDB Sharded Cluster
  mongodb-config:
    image: mongo:7.0
    command: mongod --configsvr --replSet configReplSet --port 27017
    ports:
      - "27017:27017"
    volumes:
      - mongodb-config-data:/data/db
    networks:
      - university-portal

  mongodb-shard1-primary:
    image: mongo:7.0
    command: mongod --shardsvr --replSet shard1 --port 27017
    ports:
      - "27018:27017"
    volumes:
      - mongodb-shard1-data:/data/db
    networks:
      - university-portal

  mongodb-shard1-secondary:
    image: mongo:7.0
    command: mongod --shardsvr --replSet shard1 --port 27017
    ports:
      - "27019:27017"
    volumes:
      - mongodb-shard2-data:/data/db
    networks:
      - university-portal

  mongodb-shard2-primary:
    image: mongo:7.0
    command: mongod --shardsvr --replSet shard2 --port 27017
    ports:
      - "27020:27017"
    volumes:
      - mongodb-shard3-data:/data/db
    networks:
      - university-portal

  mongodb-shard2-secondary:
    image: mongo:7.0
    command: mongod --shardsvr --replSet shard2 --port 27017
    ports:
      - "27021:27017"
    volumes:
      - mongodb-shard4-data:/data/db
    networks:
      - university-portal

  mongodb-shard3-primary:
    image: mongo:7.0
    command: mongod --shardsvr --replSet shard3 --port 27017
    ports:
      - "27022:27017"
    volumes:
      - mongodb-shard5-data:/data/db
    networks:
      - university-portal

  mongodb-shard3-secondary:
    image: mongo:7.0
    command: mongod --shardsvr --replSet shard3 --port 27017
    ports:
      - "27023:27017"
    volumes:
      - mongodb-shard6-data:/data/db
    networks:
      - university-portal

  mongodb-router:
    image: mongo:7.0
    command: mongos --configdb configReplSet/mongodb-config:27017 --port 27017
    ports:
      - "27024:27017"
    depends_on:
      - mongodb-config
      - mongodb-shard1-primary
      - mongodb-shard1-secondary
      - mongodb-shard2-primary
      - mongodb-shard2-secondary
      - mongodb-shard3-primary
      - mongodb-shard3-secondary
    networks:
      - university-portal

  # Redis Cluster
  redis-node-1:
    image: redis:7.0-alpine
    command: redis-server --port 7000 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --appendfilename appendonly-7000.aof
    ports:
      - "7000:7000"
    volumes:
      - redis-node-1-data:/data
    networks:
      - university-portal

  redis-node-2:
    image: redis:7.0-alpine
    command: redis-server --port 7001 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --appendfilename appendonly-7001.aof
    ports:
      - "7001:7001"
    volumes:
      - redis-node-2-data:/data
    networks:
      - university-portal

  redis-node-3:
    image: redis:7.0-alpine
    command: redis-server --port 7002 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --appendfilename appendonly-7002.aof
    ports:
      - "7002:7002"
    volumes:
      - redis-node-3-data:/data
    networks:
      - university-portal

  redis-node-4:
    image: redis:7.0-alpine
    command: redis-server --port 7003 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --appendfilename appendonly-7003.aof
    ports:
      - "7003:7003"
    volumes:
      - redis-node-4-data:/data
    networks:
      - university-portal

  redis-node-5:
    image: redis:7.0-alpine
    command: redis-server --port 7004 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --appendfilename appendonly-7004.aof
    ports:
      - "7004:7004"
    volumes:
      - redis-node-5-data:/data
    networks:
      - university-portal

  redis-node-6:
    image: redis:7.0-alpine
    command: redis-server --port 7005 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --appendfilename appendonly-7005.aof
    ports:
      - "7005:7005"
    volumes:
      - redis-node-6-data:/data
    networks:
      - university-portal

  # University Portal API
  university-portal-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGODB_URI=mongodb://mongodb-router:27017/university_portal
      - REDIS_CLUSTER_NODES=redis://redis-node-1:7000,redis://redis-node-2:7001,redis://redis-node-3:7002,redis://redis-node-4:7003,redis://redis-node-5:7004,redis://redis-node-6:7005
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
    depends_on:
      - mongodb-router
      - redis-node-1
      - redis-node-2
      - redis-node-3
      - redis-node-4
      - redis-node-5
      - redis-node-6
    networks:
      - university-portal
    restart: unless-stopped
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # NGINX Load Balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - university-portal-api
    networks:
      - university-portal
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - university-portal
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - university-portal
    restart: unless-stopped

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - university-portal
    restart: unless-stopped

volumes:
  mongodb-config-data:
  mongodb-shard1-data:
  mongodb-shard2-data:
  mongodb-shard3-data:
  mongodb-shard4-data:
  mongodb-shard5-data:
  mongodb-shard6-data:
  redis-node-1-data:
  redis-node-2-data:
  redis-node-3-data:
  redis-node-4-data:
  redis-node-5-data:
  redis-node-6-data:
  prometheus-data:
  grafana-data:

networks:
  university-portal:
    driver: bridge
