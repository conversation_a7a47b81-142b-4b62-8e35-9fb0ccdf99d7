{"name": "events", "id": "events", "version": "1.1.1", "description": "<PERSON>de's event emitter for all engines.", "keywords": ["events", "eventEmitter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listeners"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://jeditoolkit.com)", "repository": {"type": "git", "url": "git://github.com/Gozala/events.git", "web": "https://github.com/Gozala/events"}, "bugs": {"url": "http://github.com/Gozala/events/issues/"}, "main": "./events.js", "engines": {"node": ">=0.4.x"}, "devDependencies": {"mocha": "~1.21.4", "zuul": "~1.10.2"}, "scripts": {"test": "mocha --ui qunit -- tests/index.js && zuul -- tests/index.js"}, "license": "MIT"}