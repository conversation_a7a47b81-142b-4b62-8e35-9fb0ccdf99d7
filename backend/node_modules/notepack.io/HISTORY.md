<a name="2.1.3"></a>
## [2.1.3](https://github.com/darrachequesne/notepack/compare/2.1.2...2.1.3) (2018-05-14)


### Bug Fixes

* **browser:** fix utf-8 decoder ([#16](https://github.com/darrachequesne/notepack/issues/16)) ([abbf3a5](https://github.com/darrachequesne/notepack/commit/abbf3a5))



<a name="2.1.2"></a>
## [2.1.2](https://github.com/darrachequesne/notepack/compare/2.1.1...2.1.2) (2017-08-23)


### Bug Fixes

* **encode:** remove the unsafe integer check ([#15](https://github.com/darrachequesne/notepack/issues/15)) ([bb8140c](https://github.com/darrachequesne/notepack/commit/bb8140c))



<a name="2.1.1"></a>
## [2.1.1](https://github.com/darrachequesne/notepack/compare/2.1.0...2.1.1) (2017-08-08)


### Bug Fixes

* **browser:** fix decoding for strings with surrogate pairs ([#13](https://github.com/darrachequesne/notepack/issues/13)) ([a89e566](https://github.com/darrachequesne/notepack/commit/a89e566))
* **browser:** preserve the offset and length when creating a DataView ([#11](https://github.com/darrachequesne/notepack/issues/11)) ([bd91aa7](https://github.com/darrachequesne/notepack/commit/bd91aa7))



<a name="2.1.0"></a>
# [2.1.0](https://github.com/darrachequesne/notepack/compare/2.0.1...2.1.0) (2017-07-31)


### Features

* add support for toJSON method ([#8](https://github.com/darrachequesne/notepack/issues/8)) ([9345f9f](https://github.com/darrachequesne/notepack/commit/9345f9f)), closes [#7](https://github.com/darrachequesne/notepack/issues/7)



<a name="2.0.1"></a>
## [2.0.1](https://github.com/darrachequesne/notepack/compare/2.0.0...2.0.1) (2017-06-06)


### Bug Fixes

* **encode:** fix encoding for non-finite numbers ([#4](https://github.com/darrachequesne/notepack/issues/4)) ([f0ed0f3](https://github.com/darrachequesne/notepack/commit/f0ed0f3))



<a name="2.0.0"></a>
# [2.0.0](https://github.com/darrachequesne/notepack/compare/1.0.1...2.0.0) (2017-05-19)


### Features

* Add support for ArrayBuffer ([#2](https://github.com/darrachequesne/notepack/issues/2)) ([9eec8dc](https://github.com/darrachequesne/notepack/commit/9eec8dc))
* **browser:** switch from Buffer polyfill to ArrayBuffer ([#1](https://github.com/darrachequesne/notepack/issues/1)) ([8d7ce87](https://github.com/darrachequesne/notepack/commit/8d7ce87))
