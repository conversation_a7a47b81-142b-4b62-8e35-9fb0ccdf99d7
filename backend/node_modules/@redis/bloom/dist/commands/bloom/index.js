"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ADD = require("./ADD");
const CARD = require("./CARD");
const EXISTS = require("./EXISTS");
const INFO = require("./INFO");
const INSERT = require("./INSERT");
const LOADCHUNK = require("./LOADCHUNK");
const MADD = require("./MADD");
const MEXISTS = require("./MEXISTS");
const RESERVE = require("./RESERVE");
const SCANDUMP = require("./SCANDUMP");
exports.default = {
    ADD,
    add: ADD,
    CARD,
    card: CARD,
    EXISTS,
    exists: EXISTS,
    INFO,
    info: INFO,
    INSERT,
    insert: INSERT,
    LOADCHUNK,
    loadChunk: LOADCHUNK,
    MADD,
    mAdd: MADD,
    MEXISTS,
    mExists: MEXISTS,
    RESERVE,
    reserve: RESERVE,
    SCANDUMP,
    scanDump: SCANDUMP
};
