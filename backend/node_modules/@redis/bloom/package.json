{"name": "@redis/bloom", "version": "1.2.0", "license": "MIT", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/"], "scripts": {"test": "nyc -r text-summary -r lcov mocha -r source-map-support/register -r ts-node/register './lib/**/*.spec.ts'", "build": "tsc", "documentation": "typedoc"}, "peerDependencies": {"@redis/client": "^1.0.0"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@redis/test-utils": "*", "@types/node": "^18.11.18", "nyc": "^15.1.0", "release-it": "^15.6.0", "source-map-support": "^0.5.21", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "typescript": "^4.9.4"}}