import { RedisCommandArguments } from '@redis/client/dist/lib/commands';
import { Timestamp, MRangeWithLabelsOptions } from '.';
export declare const IS_READ_ONLY = true;
export declare function transformArguments(fromTimestamp: Timestamp, toTimestamp: Timestamp, filters: string | Array<string>, options?: MRangeWithLabelsOptions): RedisCommandArguments;
export { transformMRangeWithLabelsReply as transformReply } from '.';
