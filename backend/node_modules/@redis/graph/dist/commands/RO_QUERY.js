"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformReply = exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;
const _1 = require(".");
var QUERY_1 = require("./QUERY");
Object.defineProperty(exports, "FIRST_KEY_INDEX", { enumerable: true, get: function () { return QUERY_1.FIRST_KEY_INDEX; } });
exports.IS_READ_ONLY = true;
function transformArguments(graph, query, options, compact) {
    return (0, _1.pushQueryArguments)(['GRAPH.RO_QUERY'], graph, query, options, compact);
}
exports.transformArguments = transformArguments;
var QUERY_2 = require("./QUERY");
Object.defineProperty(exports, "transformReply", { enumerable: true, get: function () { return QUERY_2.transformReply; } });
