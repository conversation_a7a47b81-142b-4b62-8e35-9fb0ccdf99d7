{"name": "@dabh/diagnostics", "version": "2.0.8", "description": "Tools for debugging your node.js modules and event loop", "main": "./node", "browser": "./browser", "scripts": {"test:basic": "mocha --require test/mock.js test/*.test.js", "test:node": "mocha --require test/mock test/node.js", "test:browser": "mocha --require test/mock test/browser.js", "test:runner": "npm run test:basic && npm run test:node && npm run test:browser", "webpack:node:prod": "webpack --mode=production node/index.js -o /dev/null --json | webpack-bundle-size-analyzer", "webpack:node:dev": "webpack --mode=development node/index.js -o /dev/null --json | webpack-bundle-size-analyzer", "webpack:browser:prod": "webpack --mode=production browser/index.js -o /dev/null --json | webpack-bundle-size-analyzer", "webpack:browser:dev": "webpack --mode=development browser/index.js -o /dev/null --json | webpack-bundle-size-analyzer", "test": "nyc --reporter=text --reporter=lcov npm run test:runner"}, "repository": {"type": "git", "url": "git://github.com/DABH/diagnostics.git"}, "keywords": ["debug", "debugger", "debugging", "diagnostic", "diagnostics", "event", "loop", "metrics", "stats"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/DABH/diagnostics/issues"}, "homepage": "https://github.com/DABH/diagnostics", "devDependencies": {"assume": "2.3.x", "asyncstorageapi": "^1.0.2", "mocha": "^11.7.2", "nyc": "^17.1.0", "objstorage": "^1.0.0", "pre-commit": "github:metcalfc/pre-commit#b36c649fd5348d7604a86b7b2f3429c780d1478f", "require-poisoning": "^2.0.0", "webpack": "5.x", "webpack-bundle-size-analyzer": "^3.0.0", "webpack-cli": "6.x"}, "dependencies": {"@so-ric/colorspace": "^1.1.6", "enabled": "2.0.x", "kuler": "^2.0.0"}, "contributors": ["<PERSON><PERSON><PERSON> (https://github.com/swaagie)", "<PERSON> (https://github.com/jcrugzz)", "Sevastos (https://github.com/sevastos)"], "directories": {"test": "test"}}