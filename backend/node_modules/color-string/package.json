{"name": "color-string", "description": "Parser and generator for CSS color strings", "version": "2.1.2", "author": "<PERSON> (https://github.com/qix-)", "contributors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "LitoMore (https://github.com/LitoMore)"], "repository": "Qix-/color-string", "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=18"}, "scripts": {"test": "xo && tsd && node test.js"}, "license": "MIT", "files": ["index.js", "index.d.ts"], "xo": {"rules": {"no-cond-assign": 0, "operator-linebreak": 0, "@typescript-eslint/ban-types": 0}}, "dependencies": {"color-name": "^2.0.0"}, "devDependencies": {"tsd": "^0.31.2", "xo": "^0.60.0"}, "keywords": ["color", "colour", "rgb", "css"]}