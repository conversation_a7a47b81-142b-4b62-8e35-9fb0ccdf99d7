{"pagination": {"ListJournalKinesisStreamsForLedger": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListJournalS3Exports": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListJournalS3ExportsForLedger": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListLedgers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}}}