{"pagination": {"GetQueryResults": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListApplicationDPUSizes": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListCalculationExecutions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListCapacityReservations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListDataCatalogs": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "DataCatalogsSummary"}, "ListDatabases": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "DatabaseList"}, "ListEngineVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListExecutors": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListNamedQueries": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListPreparedStatements": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListQueryExecutions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListSessions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}, "ListTableMetadata": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "TableMetadataList"}, "ListTagsForResource": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Tags"}, "ListWorkGroups": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken"}}}