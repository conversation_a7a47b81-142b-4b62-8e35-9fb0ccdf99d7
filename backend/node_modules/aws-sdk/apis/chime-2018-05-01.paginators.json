{"pagination": {"ListAccounts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListAppInstanceAdmins": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListAppInstanceUsers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListAppInstances": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListAttendees": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListBots": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListChannelBans": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListChannelMemberships": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListChannelMembershipsForAppInstanceUser": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListChannelMessages": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListChannelModerators": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListChannels": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListChannelsModeratedByAppInstanceUser": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListMediaCapturePipelines": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListMeetings": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListPhoneNumberOrders": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListPhoneNumbers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListProxySessions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListRoomMemberships": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListRooms": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListSipMediaApplications": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListSipRules": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListUsers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListVoiceConnectorGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListVoiceConnectors": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "SearchAvailablePhoneNumbers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}}}