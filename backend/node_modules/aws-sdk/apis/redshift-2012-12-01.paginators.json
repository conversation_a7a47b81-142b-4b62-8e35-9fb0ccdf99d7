{"pagination": {"DescribeClusterDbRevisions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ClusterDbRevisions"}, "DescribeClusterParameterGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ParameterGroups"}, "DescribeClusterParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Parameters"}, "DescribeClusterSecurityGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ClusterSecurityGroups"}, "DescribeClusterSnapshots": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Snapshots"}, "DescribeClusterSubnetGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ClusterSubnetGroups"}, "DescribeClusterTracks": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "MaintenanceTracks"}, "DescribeClusterVersions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ClusterVersions"}, "DescribeClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Clusters"}, "DescribeCustomDomainAssociations": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Associations"}, "DescribeDataShares": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DataShares"}, "DescribeDataSharesForConsumer": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DataShares"}, "DescribeDataSharesForProducer": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DataShares"}, "DescribeDefaultClusterParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "DefaultClusterParameters.Marker", "result_key": "DefaultClusterParameters.Parameters"}, "DescribeEndpointAccess": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "EndpointAccessList"}, "DescribeEndpointAuthorization": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "EndpointAuthorizationList"}, "DescribeEventSubscriptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "EventSubscriptionsList"}, "DescribeEvents": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Events"}, "DescribeHsmClientCertificates": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "HsmClientCertificates"}, "DescribeHsmConfigurations": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "HsmConfigurations"}, "DescribeInboundIntegrations": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "InboundIntegrations"}, "DescribeNodeConfigurationOptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "NodeConfigurationOptionList"}, "DescribeOrderableClusterOptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "OrderableClusterOptions"}, "DescribeRedshiftIdcApplications": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "RedshiftIdcApplications"}, "DescribeReservedNodeExchangeStatus": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReservedNodeExchangeStatusDetails"}, "DescribeReservedNodeOfferings": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReservedNodeOfferings"}, "DescribeReservedNodes": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReservedNodes"}, "DescribeScheduledActions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ScheduledActions"}, "DescribeSnapshotCopyGrants": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "SnapshotCopyGrants"}, "DescribeSnapshotSchedules": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "SnapshotSchedules"}, "DescribeTableRestoreStatus": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "TableRestoreStatusDetails"}, "DescribeTags": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "TaggedResources"}, "DescribeUsageLimits": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "UsageLimits"}, "GetReservedNodeExchangeConfigurationOptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReservedNodeConfigurationOptionList"}, "GetReservedNodeExchangeOfferings": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReservedNodeOfferings"}, "ListRecommendations": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Recommendations"}}}