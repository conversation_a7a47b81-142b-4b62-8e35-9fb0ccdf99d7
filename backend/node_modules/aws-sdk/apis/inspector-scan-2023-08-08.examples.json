{"version": "1.0", "examples": {"ScanSbom": [{"input": {"outputFormat": "CYCLONE_DX_1_5", "sbom": {"bomFormat": "CycloneDX", "components": [{"name": "log4j-core", "type": "library", "purl": "pkg:maven/org.apache.logging.log4j/log4j-core@2.17.0"}], "specVersion": "1.5"}}, "output": {"sbom": {"metadata": {"properties": [{"name": "amazon:inspector:sbom_scanner:critical_vulnerabilities", "value": "0"}, {"name": "amazon:inspector:sbom_scanner:high_vulnerabilities", "value": "0"}, {"name": "amazon:inspector:sbom_scanner:medium_vulnerabilities", "value": "1"}, {"name": "amazon:inspector:sbom_scanner:low_vulnerabilities", "value": "0"}], "timestamp": "2023-11-16T02:55:34.355Z", "tools": [{"version": "9f8c30ff+20b2305b", "name": "CycloneDX SBOM API", "vendor": "Amazon Inspector"}]}, "bomFormat": "CycloneDX", "components": [{"name": "log4j-core", "type": "library", "bom-ref": "comp-1", "purl": "pkg:maven/org.apache.logging.log4j/log4j-core@2.17.0"}], "serialNumber": "urn:uuid:26de5e0a-deb4-4b38-a208-7d19c1832e8c", "specVersion": "1.5", "vulnerabilities": [{"advisories": [{"url": "https://www.oracle.com/security-alerts/cpujan2022.html"}, {"url": "https://lists.debian.org/debian-lts-announce/2021/12/msg00036.html"}, {"url": "https://cert-portal.siemens.com/productcert/pdf/ssa-784507.pdf"}, {"url": "https://lists.apache.org/thread/s1o5vlo78ypqxnzn6p8zf6t9shtq5143"}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/EVV25FXL4FU5X6X5BSL7RLQ7T6F65MRA/"}, {"url": "https://www.oracle.com/security-alerts/cpuapr2022.html"}, {"url": "https://www.oracle.com/security-alerts/cpujul2022.html"}, {"url": "https://tools.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-apache-log4j-qRuKNEbd"}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/T57MPJUW3MA6QGWZRTMCHHMMPQNVKGFC/"}, {"url": "https://issues.apache.org/jira/browse/LOG4J2-3293"}], "affects": [{"ref": "comp-1"}], "bom-ref": "vuln-1", "created": "2021-12-28T20:15:08Z", "cwes": [20, 74], "description": "Apache Log4j2 versions 2.0-beta7 through 2.17.0 (excluding security fix releases 2.3.2 and 2.12.4) are vulnerable to a remote code execution (RCE) attack when a configuration uses a JDBC Appender with a JNDI LDAP data source URI when an attacker has control of the target LDAP server. This issue is fixed by limiting JNDI data source names to the java protocol in Log4j2 versions 2.17.1, 2.12.4, and 2.3.2.", "id": "CVE-2021-44832", "properties": [{"name": "amazon:inspector:sbom_scanner:exploit_available", "value": "true"}, {"name": "amazon:inspector:sbom_scanner:exploit_last_seen_in_public", "value": "2023-01-02T00:00:00Z"}, {"name": "amazon:inspector:sbom_scanner:fixed_version:comp-1", "value": "2.17.1"}], "ratings": [{"method": "CVSSv31", "score": 6.5, "severity": "medium", "source": {"name": "NVD", "url": "https://nvd.nist.gov/vuln/detail/CVE-2021-44832"}, "vector": "CVSS:3.1/AV:N/AC:H/PR:H/UI:N/S:U/C:H/I:H/A:H"}, {"method": "other", "score": 0.02686, "severity": "none", "source": {"name": "EPSS", "url": "https://www.first.org/epss/"}, "vector": "model:v2023.03.01,date:2023-11-15T00:00:00+0000"}], "references": [{"id": "GHSA-8489-44mv-ggj8", "source": {"name": "GITHUB_SEC", "url": "https://github.com/advisories"}}, {"id": "SNYK-JAVA-ORGAPACHELOGGINGLOG4J-2327339", "source": {"name": "SNYK", "url": "https://security.snyk.io/vuln"}}], "source": {"name": "NVD", "url": "https://nvd.nist.gov/vuln/detail/CVE-2021-44832"}, "updated": "2023-11-07T03:39:43Z"}]}}, "id": "example-1", "title": "Sample ScanSbom Call"}]}}