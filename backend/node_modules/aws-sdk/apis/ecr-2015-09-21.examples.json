{"version": "1.0", "examples": {"BatchDeleteImage": [{"input": {"imageIds": [{"imageTag": "precise"}], "repositoryName": "ubuntu"}, "output": {"failures": [], "imageIds": [{"imageDigest": "sha256:examplee6d1e504117a17000003d3753086354a38375961f2e665416ef4b1b2f", "imageTag": "precise"}]}, "comments": {}, "description": "This example deletes images with the tags precise and trusty in a repository called ubuntu in the default registry for an account.", "id": "batchdeleteimages-example-*************", "title": "To delete multiple images"}], "BatchGetImage": [{"input": {"imageIds": [{"imageTag": "precise"}], "repositoryName": "ubuntu"}, "output": {"failures": [], "images": [{"imageId": {"imageDigest": "sha256:example76bdff6d83a09ba2a818f0d00000063724a9ac3ba5019c56f74ebf42a", "imageTag": "precise"}, "imageManifest": "{\n \"schemaVersion\": 1,\n \"name\": \"ubuntu\",\n \"tag\": \"precise\",\n...", "registryId": "************", "repositoryName": "ubuntu"}]}, "comments": {"output": {"imageManifest": "In this example, the imageManifest in the output JSON has been truncated."}}, "description": "This example obtains information for an image with a specified image digest ID from the repository named ubuntu in the current account.", "id": "batchgetimage-example-*************", "title": "To obtain multiple images in a single request"}], "CreateRepository": [{"input": {"repositoryName": "project-a/nginx-web-app"}, "output": {"repository": {"registryId": "************", "repositoryArn": "arn:aws:ecr:us-west-2:************:repository/project-a/nginx-web-app", "repositoryName": "project-a/nginx-web-app"}}, "comments": {"output": {"imageManifest": "In this example, the imageManifest in the output JSON has been truncated."}}, "description": "This example creates a repository called nginx-web-app inside the project-a namespace in the default registry for an account.", "id": "createrepository-example-*************", "title": "To create a new repository"}], "CreateRepositoryCreationTemplate": [{"input": {"appliedFor": ["REPLICATION", "PULL_THROUGH_CACHE"], "description": "Repos for testing images", "encryptionConfiguration": {"encryptionType": "AES256"}, "imageTagMutability": "MUTABLE", "lifecyclePolicy": "{\r\n    \"rules\": [\r\n        {\r\n            \"rulePriority\": 1,\r\n            \"description\": \"Expire images older than 14 days\",\r\n            \"selection\": {\r\n                \"tagStatus\": \"untagged\",\r\n                \"countType\": \"sinceImagePushed\",\r\n                \"countUnit\": \"days\",\r\n                \"countNumber\": 14\r\n            },\r\n            \"action\": {\r\n                \"type\": \"expire\"\r\n            }\r\n        }\r\n    ]\r\n}", "prefix": "eng/test", "repositoryPolicy": "{\r\n  \"Version\": \"2012-10-17\",\r\n  \"Statement\": [\r\n    {\r\n      \"Sid\": \"LambdaECRPullPolicy\",\r\n      \"Effect\": \"Allow\",\r\n      \"Principal\": {\r\n        \"Service\": \"lambda.amazonaws.com\"\r\n      },\r\n      \"Action\": \"ecr:BatchGetImage\"\r\n    }\r\n  ]\r\n}", "resourceTags": [{"Key": "environment", "Value": "test"}]}, "output": {"registryId": "************", "repositoryCreationTemplate": {"appliedFor": ["REPLICATION", "PULL_THROUGH_CACHE"], "createdAt": "2023-12-16T17:29:02-07:00", "description": "Repos for testing images", "encryptionConfiguration": {"encryptionType": "AES256"}, "imageTagMutability": "MUTABLE", "lifecyclePolicy": "{\r\n    \"rules\": [\r\n        {\r\n            \"rulePriority\": 1,\r\n            \"description\": \"Expire images older than 14 days\",\r\n            \"selection\": {\r\n                \"tagStatus\": \"untagged\",\r\n                \"countType\": \"sinceImagePushed\",\r\n                \"countUnit\": \"days\",\r\n                \"countNumber\": 14\r\n            },\r\n            \"action\": {\r\n                \"type\": \"expire\"\r\n            }\r\n        }\r\n    ]\r\n}", "prefix": "eng/test", "repositoryPolicy": "{\n  \"Version\" : \"2012-10-17\",\n  \"Statement\" : [ {\n    \"Sid\" : \"LambdaECRPullPolicy\",\n    \"Effect\" : \"Allow\",\n    \"Principal\" : {\n      \"Service\" : \"lambda.amazonaws.com\"\n    },\n    \"Action\" : \"ecr:BatchGetImage\"\n  } ]\n}", "resourceTags": [{"Key": "environment", "Value": "test"}], "updatedAt": "2023-12-16T17:29:02-07:00"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a repository creation template.", "id": "create-a-new-repository-creation-template-1713296923053", "title": "Create a new repository creation template"}], "DeleteRepository": [{"input": {"force": true, "repositoryName": "ubuntu"}, "output": {"repository": {"registryId": "************", "repositoryArn": "arn:aws:ecr:us-west-2:************:repository/ubuntu", "repositoryName": "ubuntu"}}, "comments": {"output": {"imageManifest": "In this example, the imageManifest in the output JSON has been truncated."}}, "description": "This example force deletes a repository named ubuntu in the default registry for an account. The force parameter is required if the repository contains images.", "id": "deleterepository-example-*************", "title": "To force delete a repository"}], "DeleteRepositoryCreationTemplate": [{"input": {"prefix": "eng"}, "output": {"registryId": "************", "repositoryCreationTemplate": {"createdAt": "2023-12-03T16:27:57.933000-08:00", "encryptionConfiguration": {"encryptionType": "AES256"}, "imageTagMutability": "MUTABLE", "prefix": "eng", "updatedAt": "2023-12-03T16:27:57.933000-08:00"}}, "comments": {"input": {}, "output": {}}, "description": "This example deletes a repository creation template.", "id": "delete-a-repository-creation-template-*************", "title": "Delete a repository creation template"}], "DeleteRepositoryPolicy": [{"input": {"repositoryName": "ubuntu"}, "output": {"policyText": "{ ... }", "registryId": "************", "repositoryName": "ubuntu"}, "comments": {}, "description": "This example deletes the policy associated with the repository named ubuntu in the current account.", "id": "deleterepositorypolicy-example-*************", "title": "To delete the policy associated with a repository"}], "DescribeRepositories": [{"input": {}, "output": {"repositories": [{"registryId": "************", "repositoryArn": "arn:aws:ecr:us-west-2:************:repository/ubuntu", "repositoryName": "ubuntu"}, {"registryId": "************", "repositoryArn": "arn:aws:ecr:us-west-2:************:repository/test", "repositoryName": "test"}]}, "comments": {"output": {}}, "description": "The following example obtains a list and description of all repositories in the default registry to which the current user has access.", "id": "describe-repositories-*************", "title": "To describe all repositories in the current account"}], "DescribeRepositoryCreationTemplates": [{"input": {"maxResults": 123, "nextToken": "", "prefixes": ["eng"]}, "output": {"nextToken": "", "registryId": "************", "repositoryCreationTemplates": [{"appliedFor": ["PULL_THROUGH_CACHE", "REPLICATION"], "createdAt": "2023-12-16T17:29:02-07:00", "encryptionConfiguration": {"encryptionType": "AES256"}, "imageTagMutability": "MUTABLE", "prefix": "eng/test", "updatedAt": "2023-12-16T19:55:02-07:00"}, {"appliedFor": ["REPLICATION"], "createdAt": "2023-12-14T17:29:02-07:00", "encryptionConfiguration": {"encryptionType": "AES256"}, "imageTagMutability": "IMMUTABLE", "prefix": "eng/replication-test", "updatedAt": "2023-12-14T19:55:02-07:00"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the contents of a repository creation template.", "id": "describe-a-repository-creation-template-*************", "title": "Describe a repository creation template"}], "GetAuthorizationToken": [{"input": {}, "output": {"authorizationData": [{"authorizationToken": "QVdTOkNEXAMPLE", "expiresAt": "2022-05-17T06:56:13.652000+00:00", "proxyEndpoint": "https://************.dkr.ecr.us-west-2.amazonaws.com"}]}, "comments": {}, "description": "This example gets an authorization token for your default registry.", "id": "getauthorizationtoken-example-1470867047084", "title": "To obtain an authorization token"}], "GetRepositoryPolicy": [{"input": {"repositoryName": "ubuntu"}, "output": {"policyText": "{\n  \"Version\" : \"2008-10-17\",\n  \"Statement\" : [ {\n    \"Sid\" : \"new statement\",\n    \"Effect\" : \"Allow\",\n    \"Principal\" : {\n     \"AWS\" : \"arn:aws:iam::************:role/CodeDeployDemo\"\n    },\n\"Action\" : [ \"ecr:GetDownloadUrlForLayer\", \"ecr:BatchGetImage\", \"ecr:BatchCheckLayerAvailability\" ]\n } ]\n}", "registryId": "************", "repositoryName": "ubuntu"}, "comments": {}, "description": "This example obtains the repository policy for the repository named ubuntu.", "id": "getrepositorypolicy-example-*************", "title": "To get the current policy for a repository"}], "ListImages": [{"input": {"repositoryName": "ubuntu"}, "output": {"imageIds": [{"imageDigest": "sha256:764f63476bdff6d83a09ba2a818f0d35757063724a9ac3ba5019c56f74ebf42a", "imageTag": "precise"}]}, "comments": {}, "description": "This example lists all of the images in the repository named ubuntu in the default registry in the current account. ", "id": "listimages-example-*************", "title": "To list all images in a repository"}], "UpdateRepositoryCreationTemplate": [{"input": {"appliedFor": ["REPLICATION"], "prefix": "eng/test", "resourceTags": [{"Key": "environment", "Value": "test"}]}, "output": {"registryId": "************", "repositoryCreationTemplate": {"appliedFor": ["REPLICATION"], "createdAt": "2023-12-16T17:29:02-07:00", "description": "Repos for testing images", "encryptionConfiguration": {"encryptionType": "AES256"}, "imageTagMutability": "MUTABLE", "lifecyclePolicy": "{\r\n    \"rules\": [\r\n        {\r\n            \"rulePriority\": 1,\r\n            \"description\": \"Expire images older than 14 days\",\r\n            \"selection\": {\r\n                \"tagStatus\": \"untagged\",\r\n                \"countType\": \"sinceImagePushed\",\r\n                \"countUnit\": \"days\",\r\n                \"countNumber\": 14\r\n            },\r\n            \"action\": {\r\n                \"type\": \"expire\"\r\n            }\r\n        }\r\n    ]\r\n}", "prefix": "eng/test", "repositoryPolicy": "{\n  \"Version\" : \"2012-10-17\",\n  \"Statement\" : [ {\n    \"Sid\" : \"LambdaECRPullPolicy\",\n    \"Effect\" : \"Allow\",\n    \"Principal\" : {\n      \"Service\" : \"lambda.amazonaws.com\"\n    },\n    \"Action\" : \"ecr:BatchGetImage\"\n  } ]\n}", "resourceTags": [{"Key": "environment", "Value": "test"}], "updatedAt": "2023-12-16T19:55:02-07:00"}}, "comments": {"input": {}, "output": {}}, "description": "This example updates a repository creation template.", "id": "update-a-repository-creation-template-1713299261276", "title": "Update a repository creation template"}]}}