{"version": "1.0", "examples": {"GetAccountSettings": [{"input": {}, "output": {"accountSettings": {"notificationSubscriptionStatus": "SUBSCRIBED"}}, "description": "Get the current account settings.", "id": "example-1", "title": "Invoke GetAccountSettings operation"}], "GetReport": [{"input": {"reportId": "report-1hVFddebtfDNJAUf", "termToken": "term-token-gPFEGk7CF4wS901w7ppYclt7"}, "output": {"documentPresignedUrl": "<Presigned S3 URL>"}, "description": "The GetReport operation is invoked on a reportId and on a optional version.\n                        Callers must provide a termToken, which is provided by the GetTermForReport\n                        operation. If callers do not provide a version, it will default to the\n                        report's latest version", "id": "example-1", "title": "Invoke GetReport operation on the latest version of a specific report"}], "GetReportMetadata": [{"input": {"reportId": "report-bqhUJF3FrQZsMJpb"}, "output": {"reportDetails": {"version": 1, "name": "Name of report", "arn": "arn:aws:artifact:us-east-1::report/report-bqhUJF3FrQZsMJpb:1", "category": "Artifact Category", "companyName": "AWS", "createdAt": "2022-05-27T23:17:00.343940Z", "description": "Description of report", "id": "report-bqhUJF3FrQZsMJpb", "periodEnd": "2022-04-01T20:32:04Z", "periodStart": "2022-04-01T20:32:04Z", "productName": "Product of report", "series": "Artifact Series", "state": "PUBLISHED", "termArn": "arn:aws:artifact:us-east-1::term/term-gLJGG12NyPtYcmtu:1"}}, "description": "The GetReportMetadata operation is invoked on a reportId and on a optional version.\n                        If callers do not provide a version, it will default to the report's latest version.", "id": "example-1", "title": "Invoke GetReportMetadata operation on the latest version of a specific report"}], "GetTermForReport": [{"input": {"reportId": "report-bqhUJF3FrQZsMJpb"}, "output": {"documentPresignedUrl": "<Presigned S3 URL>", "termToken": "term-token-gPFEGk7CF4wS901w7ppYclt7"}, "description": "The GetTermForReport operation is invoked on a reportId and on a optional version.\n                        If callers do not provide a version, it will default to the report's latest version.", "id": "example-1", "title": "Invoke GetTermForReport operation on the latest version of a specific report"}], "ListReports": [{"input": {}, "output": {"nextToken": "gPFEGk7CF4wS901w7ppYclt7gPFEGk7CF4wS901w7ppYclt7gPFEGk7CF4wS901w7ppYclt7", "reports": [{"version": 1, "name": "Name of report", "arn": "arn:aws:artifact:us-east-1::report/report-bqhUJF3FrQZsMJpb", "category": "Artifact Category", "companyName": "AWS", "description": "Description of report", "id": "report-bqhUJF3FrQZsMJpb", "periodEnd": "2022-04-01T20:32:04Z", "periodStart": "2022-04-01T20:32:04Z", "productName": "Product of report", "series": "Artifact Series", "state": "PUBLISHED"}]}, "description": "The ListReports operation returns a collection of report resources.", "id": "example-1", "title": "Invoke ListReports operation"}], "PutAccountSettings": [{"input": {"notificationSubscriptionStatus": "SUBSCRIBED"}, "output": {"accountSettings": {"notificationSubscriptionStatus": "SUBSCRIBED"}}, "description": "Set the account settings.", "id": "example-1", "title": "Invoke PutAccountSettings operation"}]}}