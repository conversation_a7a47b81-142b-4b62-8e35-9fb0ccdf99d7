{"version": "1.0", "examples": {"CheckAccessNotGranted": [{"input": {"access": [{"actions": ["s3:PutObject"]}], "policyDocument": "{\"Version\":\"2012-10-17\",\"Id\":\"123\",\"Statement\":[{\"Sid\":\"Allow<PERSON>ohnD<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::123456789012:user/JohnDoe\"},\"Action\":\"s3:GetObject\",\"Resource\":\"*\"}]}", "policyType": "RESOURCE_POLICY"}, "output": {"message": "The policy document does not grant access to perform the listed actions or resources.", "result": "PASS"}, "id": "example-1", "title": "Passing check. Restrictive identity policy."}, {"input": {"access": [{"resources": ["arn:aws:s3:::sensitive-bucket/*"]}], "policyDocument": "{\"Version\":\"2012-10-17\",\"Id\":\"123\",\"Statement\":[{\"Sid\":\"Allow<PERSON>ohn<PERSON><PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::123456789012:user/JohnDoe\"},\"Action\":\"s3:PutObject\",\"Resource\":\"arn:aws:s3:::non-sensitive-bucket/*\"}]}", "policyType": "RESOURCE_POLICY"}, "output": {"message": "The policy document does not grant access to perform the listed actions or resources.", "result": "PASS"}, "id": "example-2", "title": "Passing check. Restrictive S3 Bucket resource policy."}, {"input": {"access": [{"resources": ["arn:aws:s3:::my-bucket/*"]}], "policyDocument": "{\"Version\":\"2012-10-17\",\"Id\":\"123\",\"Statement\":[{\"Sid\":\"Allow<PERSON>ohn<PERSON><PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::123456789012:user/JohnDoe\"},\"Action\":\"s3:PutObject\",\"Resource\":\"arn:aws:s3:::my-bucket/*\"}]}", "policyType": "RESOURCE_POLICY"}, "output": {"message": "The policy document grants access to perform one or more of the listed actions or resources.", "reasons": [{"description": "One or more of the listed actions or resources in the statement with sid: AllowJohnDoe.", "statementId": "AllowJohn<PERSON><PERSON>", "statementIndex": 0}], "result": "FAIL"}, "id": "example-3", "title": "Failing check. Permissive S3 Bucket resource policy."}], "CheckNoPublicAccess": [{"input": {"policyDocument": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"<PERSON>\":\"<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::111122223333:user/JohnDoe\"},\"Action\":[\"s3:GetObject\"]}]}", "resourceType": "AWS::S3::<PERSON><PERSON>"}, "output": {"message": "The resource policy does not grant public access for the given resource type.", "result": "PASS"}, "id": "example-1", "title": "Passing check. S3 Bucket policy without public access."}, {"input": {"policyDocument": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"<PERSON>\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Action\":[\"s3:GetObject\"]}]}", "resourceType": "AWS::S3::<PERSON><PERSON>"}, "output": {"message": "The resource policy grants public access for the given resource type.", "reasons": [{"description": "Public access granted in the following statement with sid: Bob.", "statementId": "<PERSON>", "statementIndex": 0}], "result": "FAIL"}, "id": "example-2", "title": "Failing check. S3 Bucket policy with public access."}], "GenerateFindingRecommendation": [{"input": {"analyzerArn": "arn:aws:access-analyzer:us-east-1:111122223333:analyzer/a", "id": "finding-id"}, "output": {}, "id": "example-1", "title": "Successfully started generating finding recommendation"}, {"input": {"analyzerArn": "arn:aws:access-analyzer:us-east-1:111122223333:analyzer/a", "id": "!"}, "id": "example-2", "title": "Failed field validation for id value"}], "GetFindingRecommendation": [{"input": {"analyzerArn": "arn:aws:access-analyzer:us-east-1:111122223333:analyzer/a", "id": "finding-id", "maxResults": 3, "nextToken": "token"}, "output": {"completedAt": "2000-01-01T00:00:01Z", "recommendationType": "UnusedPermissionRecommendation", "recommendedSteps": [{"unusedPermissionsRecommendedStep": {"existingPolicyId": "policy-id", "recommendedAction": "DETACH_POLICY"}}, {"unusedPermissionsRecommendedStep": {"existingPolicyId": "policy-id", "recommendedAction": "CREATE_POLICY", "recommendedPolicy": "policy-content"}}], "resourceArn": "arn:aws:iam::111122223333:role/test", "startedAt": "2000-01-01T00:00:00Z", "status": "SUCCEEDED"}, "id": "example-1", "title": "Successfully fetched finding recommendation"}, {"input": {"analyzerArn": "arn:aws:access-analyzer:us-east-1:111122223333:analyzer/a", "id": "finding-id", "maxResults": 3}, "output": {"recommendationType": "UnusedPermissionRecommendation", "resourceArn": "arn:aws:iam::111122223333:role/test", "startedAt": "2000-01-01T00:00:00Z", "status": "IN_PROGRESS"}, "id": "example-2", "title": "In progress finding recommendation"}, {"input": {"analyzerArn": "arn:aws:access-analyzer:us-east-1:111122223333:analyzer/a", "id": "finding-id", "maxResults": 3}, "output": {"completedAt": "2000-01-01T00:00:01Z", "error": {"code": "SERVICE_ERROR", "message": "Service error. Please try again."}, "recommendationType": "UnusedPermissionRecommendation", "resourceArn": "arn:aws:iam::111122223333:role/test", "startedAt": "2000-01-01T00:00:00Z", "status": "FAILED"}, "id": "example-3", "title": "Failed finding recommendation"}, {"input": {"analyzerArn": "arn:aws:access-analyzer:us-east-1:111122223333:analyzer/a", "id": "!"}, "id": "example-4", "title": "Failed field validation for id value"}]}}