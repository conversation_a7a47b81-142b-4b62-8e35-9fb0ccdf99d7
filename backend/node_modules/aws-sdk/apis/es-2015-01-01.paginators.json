{"pagination": {"DescribeDomainAutoTunes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "DescribeInboundCrossClusterSearchConnections": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "DescribeOutboundCrossClusterSearchConnections": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "DescribePackages": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "DescribeReservedElasticsearchInstanceOfferings": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "DescribeReservedElasticsearchInstances": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "GetPackageVersionHistory": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "GetUpgradeHistory": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListDomainsForPackage": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListElasticsearchInstanceTypes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListElasticsearchVersions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListPackagesForDomain": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}}}