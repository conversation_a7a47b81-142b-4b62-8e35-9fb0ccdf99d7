{"pagination": {"ListCustomModels": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelSummaries"}, "ListEvaluationJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "jobSummaries"}, "ListGuardrails": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "guardrails"}, "ListImportedModels": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelSummaries"}, "ListInferenceProfiles": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "inferenceProfileSummaries"}, "ListModelCopyJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelCopyJobSummaries"}, "ListModelCustomizationJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelCustomizationJobSummaries"}, "ListModelImportJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "modelImportJobSummaries"}, "ListModelInvocationJobs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "invocationJobSummaries"}, "ListProvisionedModelThroughputs": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "provisionedModelSummaries"}}}