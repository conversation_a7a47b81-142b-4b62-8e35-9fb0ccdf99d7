<html>
<head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title>TDigest example</title>
    <script src="dist/tdigest.js"></script>
    <script>

    var td = new this.tdigest.TDigest();
    for (var i=0; i < 1000000; i++) {
        td.push(Math.random());
    };
    td.compress()

    </script>
</head>
<body>
  <pre>
TDigest of 1M samples uniform in [0..1]:
    
<script>document.write(td.summary())</script>
  </pre>
</body>
