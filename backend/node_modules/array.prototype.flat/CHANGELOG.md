# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.3.3](https://github.com/es-shims/Array.prototype.flat/compare/v1.3.2...v1.3.3) - 2024-12-15

### Commits

- [actions] split out node 10-20, and 20+ [`1afcd57`](https://github.com/es-shims/Array.prototype.flat/commit/1afcd5757db2394fd6e007e36769dfd6dff5db08)
- [Deps] update `call-bind`, `define-properties`, `es-abstract`, `es-shim-unscopables` [`152c437`](https://github.com/es-shims/Array.prototype.flat/commit/152c4375e86872c0c12788ef5247241cd6673cc6)
- [<PERSON>] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `function-bind` `npmignore`, `object-inspect`, `tape` [`e39e33d`](https://github.com/es-shims/Array.prototype.flat/commit/e39e33dd08c291b9b5736cad67fd25b52e75cfa2)
- [Tests] replace `aud` with `npm audit` [`6868723`](https://github.com/es-shims/Array.prototype.flat/commit/6868723579f62d7972c6bd0eab23554a1fc182f2)
- [Dev Deps] add missing peer dep [`800f3e3`](https://github.com/es-shims/Array.prototype.flat/commit/800f3e3de50db2cadc5d7af9d273970f81a26f96)

## [v1.3.2](https://github.com/es-shims/Array.prototype.flat/compare/v1.3.1...v1.3.2) - 2023-09-05

### Commits

- [Deps] update `define-properties`, `es-abstract` [`fb625eb`](https://github.com/es-shims/Array.prototype.flat/commit/fb625eb6935b2c59a16ca6a99348ab6bd99089ec)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`1fde275`](https://github.com/es-shims/Array.prototype.flat/commit/1fde275224a27cfc9347b22e953ad9db46823d05)

## [v1.3.1](https://github.com/es-shims/Array.prototype.flat/compare/v1.3.0...v1.3.1) - 2022-11-02

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`e339ed7`](https://github.com/es-shims/Array.prototype.flat/commit/e339ed71634921d770e8831458767e4564bfc018)
- [meta] add `auto-changelog` [`bb5cbd6`](https://github.com/es-shims/Array.prototype.flat/commit/bb5cbd64544bcdb11d0dff24ea4a18dcb5ab7fd1)
- [Deps] update `define-properties`, `es-abstract` [`8067910`](https://github.com/es-shims/Array.prototype.flat/commit/80679104268c99a3d01552024aeff5bfc39eb97e)
- [actions] update rebase action to use reusable workflow [`d4d9b28`](https://github.com/es-shims/Array.prototype.flat/commit/d4d9b28870ba950d6a19f0ad85f09a35767fbc55)
- [Dev Deps] update `aud`, `object-inspect`, `tape` [`d9d7300`](https://github.com/es-shims/Array.prototype.flat/commit/d9d730009cfe8d02ed1e0f7db0f5b4ebe7c11fae)

<!-- auto-changelog-above -->

1.3.0 / 2022-04-11
=================
  * [New] `shim`/`auto`: add `flat` to `Symbol.unscopables`
  * [Deps] update `es-abstract`
  * [actions] reuse common workflows
  * [actions] update codecov uploader
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `auto-changelog`, `object-inspect`, `safe-publish-latest`, `tape`

1.2.5 / 2021-10-01
=================
  * [readme] add github actions/codecov badges; remove travis badge
  * [Deps] update `call-bind`, `es-abstract`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `has-strict-mode`, `object-inspect`, `tape`
  * [meta] use `prepublishOnly`, for npm 7+
  * [actions] use `node/install` instead of `node/run`; use `codecov` action
  * [actions] update workflows
  * [Tests] increase coverage
  * [meta] fix changelog for v1.2.4

1.2.4 / 2020-11-18
=================
  * [meta] do not publish Github Action workflows
  * [Deps] update `es-abstract`; add `call-bind` where applicable
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape`
  * [Tests] run `nyc` on all tests
  * [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner
  * [Tests] migrate tests to Github Actions
  * [actions] add "Allow Edits" workflow
  * [actions] switch Automatic Rebase workflow to `pull_request_target` event

1.2.3 / 2019-12-12
=================
  * [Refactor] use split-up `es-abstract` (65% bundle size decrease)
  * [Deps] update `es-abstract`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `object-inspect`
  * [meta] ESnext -> ES2019
  * [meta] add `funding` field
  * [Tests] use shared travis-ci configs
  * [actions] add automatic rebasing / merge commit blocking

1.2.2 / 2019-10-10
=================
  * [Deps] update `es-abstract`, `define-properties`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `evalmd`, `object-inspect`, `safe-publish-latest`, `tape`
  * [meta] create FUNDING.yml
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops
  * [Tests] up to `node` `v12.11`, `v11.15`, `v10.16`, `v9.11`, `v8.16`, `v6.17`, `v4.9`; use `nvm install-latest-npm`

1.2.1 / 2018-02-23
=================
  * [Fix] Temporarily hack main entry, so it's compatible with other resolvers (#3)
  * [Dev Deps] update `eslint`, `nsp`, `tape`
  * [Tests] up to `node` `v9.6`, `v6.13`

1.2.0 / 2018-01-18
=================
  * [New] add "auto" entry point
  * [Fix] Move the receiver length check higher
  * [Fix] spec adjustments
  * [Refactor] adjust shouldFlatten logic
  * [Dev Deps] update `eslint`
  * [Tests] up to `node` `v9.4`

1.1.1 / 2017-11-29
=================
  * [Fix] avoid an extra hole in the array (https://github.com/es-shims/Array.prototype.flatMap/issues/1)
  * [Deps] update `es-abstract`
  * [Dev Deps] update `eslint`, `nsp`
  * [Tests] up to `node` `v9.2`, `v8.9`, `v6.12`; pin included builds to LTS.

1.1.0 / 2017-10-03
=================
  * [New] add explicit setting of “length” on target array
  * [Fix] `FlattenIntoArray`: add assertion that `thisArg` and `mapperFunction` are both passed together
  * [Tests] make coverage required

1.0.1 / 2017-10-02
=================
  * Add readme

1.0.0 / 2017-10-01
=================
  * Initial release
