{"version": 3, "file": "opentracing_api.js", "sourceRoot": "", "sources": ["../../src/test/opentracing_api.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,6BAA8B;AAC9B,sCAAwC;AAExC,SAAgB,mBAAmB;IAC/B,QAAQ,CAAC,iBAAiB,EAAE;QACxB,IAAI,MAA0B,CAAC;QAC/B,IAAI,IAAsB,CAAC;QAC3B,UAAU,CAAC;YACP,MAAM,GAAG,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;YAClC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE;YAClB,IAAM,YAAY,GAAiC;gBAC/C,iBAAiB;gBACjB,eAAe;gBACf,qBAAqB;gBACrB,oBAAoB;gBACpB,wBAAwB;aAC3B,CAAC;oCACS,MAAI;gBACX,EAAE,CAAC,MAAI,GAAG,8BAA8B,EAAE;oBACtC,aAAM,CAAC,WAAW,CAAC,MAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;;YAHP,KAAmB,UAAY,EAAZ,6BAAY,EAAZ,0BAAY,EAAZ,IAAY;gBAA1B,IAAM,MAAI,qBAAA;wBAAJ,MAAI;aAId;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,sBAAsB,EAAE;YAC7B,IAAM,KAAK,GAAiC;gBACxC,SAAS;gBACT,aAAa;gBACb,kBAAkB;gBAClB,cAAc;aACjB,CAAC;oCACS,MAAI;gBACX,EAAE,CAAC,MAAI,GAAG,uBAAuB,EAAE;oBAC/B,aAAM,CAAC,WAAW,CAAC,MAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC;;YAHP,KAAmB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK;gBAAnB,IAAM,MAAI,cAAA;wBAAJ,MAAI;aAId;YAED,QAAQ,CAAC,eAAe,EAAE;gBACtB,IAAM,SAAS,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;gBAEzC,SAAS,CAAC;oBACR,WAAW,CAAC,gBAAgB,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,8BAA8B,EAAE;oBAC/B,WAAW,CAAC,gBAAgB,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;oBAC/C,IAAM,MAAM,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;oBAC1C,IAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBACtC,aAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC;gBAEH;oBAAyB,8BAAkB;oBAA3C;;oBAIA,CAAC;oBAHW,+BAAU,GAApB,UAAqB,IAAY,EAAE,MAA+B;wBAC9D,OAAO,SAAS,CAAC;oBACrB,CAAC;oBACH,iBAAC;gBAAD,CAAC,AAJD,CAAyB,WAAW,CAAC,MAAM,GAI1C;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE;YACf,EAAE,CAAC,mBAAmB,EAAE;gBACpB,aAAM,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,MAAM,EAAE;YACb,EAAE,CAAC,mBAAmB,EAAE;gBACpB,aAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE;YACpB,EAAE,CAAC,mBAAmB,EAAE;gBACpB,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnC,aAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE;YAClB,EAAE,CAAC,mBAAmB,EAAE;gBACpB,IAAM,GAAG,GAAG,IAAI,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,kBAAkB,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;gBACtF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,eAAe,EAAE;YACtB,EAAE,CAAC,mDAAmD,EAAE;gBACpD,IAAM,MAAM,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC;gBACpC,IAAM,GAAG,GAAG,IAAI,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAClD,aAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC;AA7FD,kDA6FC;AAED,kBAAe,mBAAmB,CAAC"}