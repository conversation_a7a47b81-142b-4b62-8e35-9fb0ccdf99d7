{"version": 3, "file": "api_compatibility.js", "sourceRoot": "", "sources": ["../../src/test/api_compatibility.ts"], "names": [], "mappings": ";;AACA,6BAAsC;AACtC,kCAAsH;AAQtH;;;;;;GAMG;AACH,SAAS,sBAAsB,CAAC,YAAiC,EAAE,OAAmG;IAAtI,6BAAA,EAAA,6BAAqB,OAAA,IAAI,cAAM,EAAE,EAAZ,CAAY;IAAE,wBAAA,EAAA,YAA0C,iBAAiB,EAAE,KAAK,EAAE,uBAAuB,EAAE,KAAK,EAAC;IAElK,QAAQ,CAAC,+BAA+B,EAAE;QACtC,IAAI,MAAc,CAAC;QACnB,IAAI,IAAU,CAAC;QAEf,UAAU,CAAC;YACP,MAAM,GAAG,YAAY,EAAE,CAAC;YACxB,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE;YAEf,QAAQ,CAAC,WAAW,EAAE;gBAClB,EAAE,CAAC,sCAAsC,EAAE;oBACvC,aAAM,CAAC,cAAQ,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,OAAO,EAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACrF,aAAM,CAAC,cAAQ,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,OAAO,EAAG,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACnG,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,QAAQ,EAAE;gBACf,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,sDAAsD,EAAE;oBACrG,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;oBACnC,IAAM,WAAW,GAAG,EAAE,CAAC;oBACvB,IAAM,UAAU,GAAG,IAAI,qBAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAChD,aAAM,CAAC,cAAQ,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,uBAAe,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAChG,aAAM,CAAC,cAAQ,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,qBAAa,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC7F,aAAM,CAAC,cAAQ,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,qBAAa,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACzF,CAAC,CAAC,CAAC;gBAEH,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,sCAAsC,EAAG;oBACtF,IAAM,WAAW,GAAG,EAAE,CAAC;oBACvB,aAAM,CAAC,cAAQ,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,uBAAe,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACzF,aAAM,CAAC,cAAQ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,uBAAe,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACvG,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,SAAS,EAAE;gBAChB,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,sDAAsD,EAAE;oBACrG,IAAM,WAAW,GAAG,EAAE,CAAC;oBACvB,IAAM,UAAU,GAAG,IAAI,qBAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAChD,aAAM,CAAC,cAAQ,MAAM,CAAC,OAAO,CAAC,uBAAe,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACpF,aAAM,CAAC,cAAQ,MAAM,CAAC,OAAO,CAAC,qBAAa,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACjF,aAAM,CAAC,cAAQ,MAAM,CAAC,OAAO,CAAC,qBAAa,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACzE,aAAM,CAAC,cAAQ,MAAM,CAAC,OAAO,CAAC,qBAAa,EAAE,EAAE,MAAM,EAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC5F,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,MAAM,EAAE;YAEb,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,yCAAyC,EAAE;gBAClF,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;gBAC9C,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAC5C,aAAM,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,QAAQ,EAAE;gBACf,EAAE,CAAC,gDAAgD,EAAE;oBACjD,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBACrC,aAAM,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAvB,CAAuB,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE;YAClB,EAAE,CAAC,wCAAwC,EAAE;gBACzC,aAAM,CAAC,cAAM,OAAA,IAAI,iBAAS,CAAC,0BAAkB,EAAE,IAAI,CAAC,EAAvC,CAAuC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC1E,aAAM,CAAC,cAAM,OAAA,IAAI,iBAAS,CAAC,0BAAkB,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,EAAjD,CAAiD,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,aAAa,EAAE;YACpB,QAAQ,CAAC,WAAW,EAAE;gBAClB,EAAE,CAAC,wBAAwB,EAAE;oBACzB,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBACrC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC;oBACxC,aAAM,CAAC,cAAM,OAAA,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,EAA1B,CAA0B,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC7D,aAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,UAAU,EAAE;gBACjB,EAAE,CAAC,wBAAwB,EAAE;oBACzB,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBACrC,aAAM,CAAC,cAAM,OAAA,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAzB,CAAyB,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC5D,aAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gBACxD,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC;AAED,kBAAe,sBAAsB,CAAC"}