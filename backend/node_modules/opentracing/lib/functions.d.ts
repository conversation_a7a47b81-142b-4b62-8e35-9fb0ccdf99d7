import Reference from './reference';
import Span from './span';
import SpanContext from './span_context';
/**
 * Return a new REFERENCE_CHILD_OF reference.
 *
 * @param {SpanContext} spanContext - the parent SpanContext instance to
 *        reference.
 * @return a REFERENCE_CHILD_OF reference pointing to `spanContext`
 */
export declare function childOf(spanContext: SpanContext | Span): Reference;
/**
 * Return a new REFERENCE_FOLLOWS_FROM reference.
 *
 * @param {SpanContext} spanContext - the parent SpanContext instance to
 *        reference.
 * @return a REFERENCE_FOLLOWS_FROM reference pointing to `spanContext`
 */
export declare function followsFrom(spanContext: SpanContext | Span): Reference;
//# sourceMappingURL=functions.d.ts.map