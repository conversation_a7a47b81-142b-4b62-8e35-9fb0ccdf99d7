{"version": 3, "file": "demo.js", "sourceRoot": "", "sources": ["../../../src/examples/demo/demo.ts"], "names": [], "mappings": ";AAAA,oBAAoB;;AAEpB,qCAAuC;AAEvC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AAEnC,IAAM,MAAM,GAAG,IAAI,kBAAU,EAAE,CAAC;AAEhC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAChC,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AAC/C,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AACrC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAE/B,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;AACzC,UAAU,CAAC;IACP,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACpC,IAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;IAClE,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC7B,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC3B,KAAK,CAAC,GAAG,CAAC,EAAC,KAAK,EAAE,SAAS,EAAC,CAAC,CAAC;IAE9B,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC1B,UAAU,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,KAAK,CAAC,GAAG,CAAC,EAAC,KAAK,EAAE,MAAM,EAAC,CAAC,CAAC;QAC3B,KAAK,CAAC,MAAM,EAAE,CAAC;QACf,MAAM,CAAC,MAAM,EAAE,CAAC;QAEhB,uEAAuE;QACvE,sEAAsE;QACtE,mBAAmB;QACnB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxB,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QAC/B,KAAmB,UAAY,EAAZ,KAAA,MAAM,CAAC,KAAK,EAAZ,cAAY,EAAZ,IAAY,EAAE;YAA5B,IAAM,IAAI,SAAA;YACX,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACzB,IAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElC,OAAO,CAAC,GAAG,CAAC,SAAO,IAAI,CAAC,aAAa,EAAE,WAAM,IAAI,CAAC,UAAU,EAAE,OAAI,CAAC,CAAC;YACpE,KAAkB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;gBAAtB,IAAM,GAAG,gBAAA;gBACV,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBACxB,OAAO,CAAC,GAAG,CAAC,kBAAgB,GAAG,WAAM,KAAK,MAAG,CAAC,CAAC;aAClD;SACJ;IACL,CAAC,EAAE,GAAG,CAAC,CAAC;AACZ,CAAC,EAAE,IAAI,CAAC,CAAC"}