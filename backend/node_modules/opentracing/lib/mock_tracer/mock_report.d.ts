import { MockSpan } from './mock_span';
/**
 * Index a collection of reported MockSpans in a way that's easy to run unit
 * test assertions against.
 */
export declare class MockReport {
    spans: MockSpan[];
    private spansByUUID;
    private spansByTag;
    private debugSpans;
    private unfinishedSpans;
    constructor(spans: MockSpan[]);
    firstSpanWithTagValue(key: string, val: any): MockSpan | null;
}
export default MockReport;
//# sourceMappingURL=mock_report.d.ts.map