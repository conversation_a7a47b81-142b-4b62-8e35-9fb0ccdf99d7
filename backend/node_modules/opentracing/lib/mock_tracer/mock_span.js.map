{"version": 3, "file": "mock_span.js", "sourceRoot": "", "sources": ["../../src/mock_tracer/mock_span.ts"], "names": [], "mappings": ";AAAA,sDAAsD;;;;;;;;;;;;;;;AAEtD,sCAAwC;AAExC,+CAAyC;AAezC;;GAEG;AACH;IAA8B,4BAAgB;IAyC1C,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E,kBAAY,MAAkB;QAA9B,YACI,iBAAO,SAQV;QAPG,KAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAC1B,KAAI,CAAC,KAAK,GAAG,KAAI,CAAC,aAAa,EAAE,CAAC;QAClC,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,KAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,KAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,KAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,KAAI,CAAC,KAAK,GAAG,EAAE,CAAC;;IACpB,CAAC;IA3CD,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAElE,2BAAQ,GAAlB;QACI,OAAO,IAAI,sBAAW,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAES,oCAAiB,GAA3B,UAA4B,IAAY;QACpC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAES,2BAAQ,GAAlB,UAAmB,GAA2B;QAC1C,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,KAAkB,UAAI,EAAJ,aAAI,EAAJ,kBAAI,EAAJ,IAAI,EAAE;YAAnB,IAAM,GAAG,aAAA;YACV,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;SAC9B;IACL,CAAC;IAES,uBAAI,GAAd,UAAe,MAA8B,EAAE,SAAkB;QAC7D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACZ,MAAM,QAAA;YACN,SAAS,WAAA;SACZ,CAAC,CAAC;IACP,CAAC;IAES,0BAAO,GAAjB,UAAkB,UAAmB;QACjC,IAAI,CAAC,SAAS,GAAG,UAAU,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;IAC9C,CAAC;IAiBD,uBAAI,GAAJ;QACI,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,gCAAa,GAAb;QACI,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,6BAAU,GAAV;QACI,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC1C,CAAC;IAED,uBAAI,GAAJ;QACI,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,yBAAM,GAAN;QACI,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAEO,gCAAa,GAArB;QACI,IAAM,EAAE,GAAG,CAAA,aAAW,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAG,CAAA,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3F,IAAM,EAAE,GAAG,CAAA,aAAW,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAG,CAAA,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3F,OAAO,KAAG,EAAE,GAAG,EAAI,CAAC;IACxB,CAAC;IAED,+BAAY,GAAZ,UAAa,GAAc;IAC3B,CAAC;IAED;;OAEG;IACH,wBAAK,GAAL;QACI,IAAM,GAAG,GAAc;YACnB,IAAI,EAAQ,IAAI,CAAC,KAAK;YACtB,SAAS,EAAG,IAAI,CAAC,cAAc;YAC/B,MAAM,EAAM,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC;SAC9E,CAAC;QACF,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;YAChC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;SACzB;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IACL,eAAC;AAAD,CAAC,AAnGD,CAA8B,WAAW,CAAC,IAAI,GAmG7C;AAnGY,4BAAQ;AAqGrB,kBAAe,QAAQ,CAAC"}