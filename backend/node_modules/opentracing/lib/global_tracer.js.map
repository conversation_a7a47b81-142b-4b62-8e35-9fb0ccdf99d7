{"version": 3, "file": "global_tracer.js", "sourceRoot": "", "sources": ["../src/global_tracer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,mCAA8B;AAE9B,IAAM,UAAU,GAAG,IAAI,gBAAM,EAAE,CAAC;AAChC,IAAI,aAAa,GAAkB,IAAI,CAAC;AAExC,0DAA0D;AAC1D,EAAE;AACF,oDAAoD;AACpD,UAAU;AACV,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,8EAA8E;AAC9E,+EAA+E;AAC/E,2EAA2E;AAC3E,aAAa;AACb;IAAmC,wCAAM;IAAzC;;IAgBA,CAAC;IAdG,wCAAS,GAAT;QACI,IAAM,MAAM,GAAG,aAAa,IAAI,UAAU,CAAC;QAC3C,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACrD,CAAC;IAED,qCAAM,GAAN;QACI,IAAM,MAAM,GAAG,aAAa,IAAI,UAAU,CAAC;QAC3C,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAClD,CAAC;IAED,sCAAO,GAAP;QACI,IAAM,MAAM,GAAG,aAAa,IAAI,UAAU,CAAC;QAC3C,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IACL,2BAAC;AAAD,CAAC,AAhBD,CAAmC,gBAAM,GAgBxC;AAED,IAAM,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAExD;;;;;;GAMG;AACH,SAAgB,gBAAgB,CAAC,MAAc;IAC3C,aAAa,GAAG,MAAM,CAAC;AAC3B,CAAC;AAFD,4CAEC;AAED;;GAEG;AACH,SAAgB,YAAY;IACxB,yEAAyE;IACzE,0EAA0E;IAC1E,0EAA0E;IAC1E,SAAS;IACT,OAAO,oBAAoB,CAAC;AAChC,CAAC;AAND,oCAMC"}