{"version": 3, "file": "span.js", "sourceRoot": "", "sources": ["../src/span.ts"], "names": [], "mappings": ";;AAAA,6BAA+B;AAI/B;;;;;GAKG;AACH;IAAA;IAmNA,CAAC;IAjNG,4EAA4E;IAC5E,0BAA0B;IAC1B,4EAA4E;IAE5E;;;;OAIG;IACH,sBAAO,GAAP;QACI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,qBAAM,GAAN;QACI,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACH,+BAAgB,GAAhB,UAAiB,IAAY;QACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,6BAAc,GAAd,UAAe,GAAW,EAAE,KAAa;QACrC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACH,6BAAc,GAAd,UAAe,GAAW;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACH,qBAAM,GAAN,UAAO,GAAW,EAAE,KAAU;;QAC1B,uDAAuD;QACvD,IAAI,CAAC,QAAQ,WAAG,GAAC,GAAG,IAAG,KAAK,MAAG,CAAC;QAChC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,sBAAO,GAAP,UAAQ,WAAmC;QACvC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,kBAAG,GAAH,UAAI,aAAqC,EAAE,SAAkB;QACzD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,uBAAQ,GAAR,UAAS,SAAiB,EAAE,OAAY;QACpC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,qBAAM,GAAN,UAAO,UAAmB;QACtB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAEzB,uEAAuE;QACvE,0DAA0D;IAC9D,CAAC;IAED,4EAA4E;IAC5E,oDAAoD;IACpD,4EAA4E;IAE5E,0CAA0C;IAChC,uBAAQ,GAAlB;QACI,OAAO,IAAI,CAAC,WAAY,CAAC;IAC7B,CAAC;IAED,qCAAqC;IACrC,EAAE;IACF,yEAAyE;IACzE,qEAAqE;IACrE,kEAAkE;IACxD,sBAAO,GAAjB;QACI,OAAO,IAAI,CAAC,MAAO,CAAC;IACxB,CAAC;IAED,0BAA0B;IAChB,gCAAiB,GAA3B,UAA4B,IAAY;IACxC,CAAC;IAED,0BAA0B;IAChB,8BAAe,GAAzB,UAA0B,GAAW,EAAE,KAAa;IACpD,CAAC;IAED,0BAA0B;IAChB,8BAAe,GAAzB,UAA0B,GAAW;QACjC,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,0BAA0B;IAC1B,EAAE;IACF,wEAAwE;IACxE,uCAAuC;IAC7B,uBAAQ,GAAlB,UAAmB,aAAqC;IACxD,CAAC;IAED,0BAA0B;IAChB,mBAAI,GAAd,UAAe,aAAqC,EAAE,SAAkB;IACxE,CAAC;IAED,0BAA0B;IAC1B,EAAE;IACF,6DAA6D;IACnD,sBAAO,GAAjB,UAAkB,UAAmB;IACrC,CAAC;IACL,WAAC;AAAD,CAAC,AAnND,IAmNC;AAnNY,oBAAI;AAqNjB,kBAAe,IAAI,CAAC"}