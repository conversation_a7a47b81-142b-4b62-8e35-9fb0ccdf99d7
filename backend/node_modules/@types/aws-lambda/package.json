{"name": "@types/aws-lambda", "version": "8.10.122", "description": "TypeScript definitions for AWS Lambda", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/aws-lambda", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/darbio", "githubUsername": "darbio"}, {"name": "<PERSON>", "url": "https://github.com/skarum", "githubUsername": "skarum"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/StefH", "githubUsername": "StefH"}, {"name": "<PERSON>", "url": "https://github.com/buggy", "githubUsername": "buggy"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/y13i", "githubUsername": "y13i"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3", "githubUsername": "wwwy3y3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/OrthoDex", "githubUsername": "OrthoDex"}, {"name": "<PERSON>", "url": "https://github.com/Michael<PERSON>ner", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/daniel-cottone", "githubUsername": "daniel-cottone"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/kostya-misura", "githubUsername": "kostya-misura"}, {"name": "<PERSON>", "url": "https://github.com/coderbyheart", "githubUsername": "coderbyheart"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/palmithor", "githubUsername": "palmithor"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/daniloraisi", "githubUsername": "da<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/simonbuchan", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Haydabase", "githubUsername": "Haydabase"}, {"name": "<PERSON>", "url": "https://github.com/repl-chris", "githubUsername": "repl-chris"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/aneilbaboo", "githubUsername": "aneilbaboo"}, {"name": "<PERSON>", "url": "https://github.com/jeznag", "githubUsername": "jeznag"}, {"name": "<PERSON>", "url": "https://github.com/louislarry", "githubUsername": "louislarry"}, {"name": "<PERSON>", "url": "https://github.com/dpapukchiev", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ohookins", "githubUsername": "ohookins"}, {"name": "<PERSON>", "url": "https://github.com/trevor-leach", "githubUsername": "trevor-leach"}, {"name": "<PERSON>", "url": "https://github.com/jagregory", "githubUsername": "jagregory"}, {"name": "<PERSON>", "url": "https://github.com/dalen", "githubUsername": "dalen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/loikg", "githubUsername": "loikg"}, {"name": "<PERSON>", "url": "https://github.com/skyzenr", "githubUsername": "skyzenr"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/redlickigrzegorz", "githubUsername": "redlick<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/juancarbonel", "githubUsername": "juancarbonel"}, {"name": "<PERSON>", "url": "https://github.com/pwmcintyre", "githubUsername": "pwm<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/alex-bolenok-centralreach", "githubUsername": "alex-bolenok-centralreach"}, {"name": "<PERSON>", "url": "https://github.com/marianzange", "githubUsername": "marianzange"}, {"name": "<PERSON>", "url": "https://github.com/apepper", "githubUsername": "apepper"}, {"name": "<PERSON>", "url": "https://github.com/apalumbo", "githubUsername": "apalumbo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON>chin<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ivanmartos", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/zach-anthony", "githubUsername": "zach-anthony"}, {"name": "<PERSON>", "url": "https://github.com/savnik", "githubUsername": "savnik"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bboure", "githubUsername": "bboure"}, {"name": "<PERSON>", "url": "https://github.com/james<PERSON><PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/aphex", "githubUsername": "aphex"}, {"name": "<PERSON>", "url": "https://github.com/joe<PERSON><PERSON>patrick", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/lmanerich", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/aws-lambda"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "26c02b047153e84e22bc818d1937d17966d2594c5a21577fccf1f789a3affa81", "typeScriptVersion": "4.5"}