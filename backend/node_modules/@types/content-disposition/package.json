{"name": "@types/content-disposition", "version": "0.5.9", "description": "TypeScript definitions for content-disposition", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/content-disposition", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "bomret", "url": "https://github.com/bomret"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/content-disposition"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "eb4b56363bdb8d50fe15f9eaef87d31adef8899a61da907f3b2ca9f537c87697", "typeScriptVersion": "5.1"}