{"name": "@types/mysql", "version": "2.15.22", "description": "TypeScript definitions for mysql", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mysql", "license": "MIT", "contributors": [{"name": " <PERSON>", "url": "https://github.com/wjohnsto", "githubUsername": "wjo<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/kpping", "githubUsername": "kpping"}, {"name": "<PERSON>", "url": "https://github.com/jdmunro", "githubUsername": "jdmunro"}, {"name": "<PERSON>", "url": "https://github.com/sedenardi", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mysql"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "48f54215189654cbd3ee5449408ebabc92dac8e60d44bc0450413dd5fa7b573b", "typeScriptVersion": "4.5"}