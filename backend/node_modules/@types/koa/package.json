{"name": "@types/koa", "version": "2.13.9", "description": "TypeScript definitions for Koa", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/koa", "license": "MIT", "contributors": [{"name": "j<PERSON><PERSON>", "url": "https://github.com/jkeylu", "githubUsername": "j<PERSON>lu"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/brikou", "githubUsername": "brikou"}, {"name": "harry<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/harryparkdotio", "githubUsername": "harry<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/chatoo2412", "githubUsername": "chatoo2412"}, {"name": "<PERSON>", "url": "https://github.com/tellnes", "githubUsername": "tellnes"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pku<PERSON>ynski", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "vnoder", "url": "https://github.com/vnoder", "githubUsername": "vnoder"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/koa"}, "scripts": {}, "dependencies": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/http-errors": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}, "typesPublisherContentHash": "de2e493a1e4a779d245c6ca718a7e6c46ca20d01dcb2bfb7d509644b3ebf20c7", "typeScriptVersion": "4.5"}