# Installation
> `npm install --save @types/pg`

# Summary
This package contains type definitions for pg (http://github.com/brianc/node-postgres).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pg.

### Additional Details
 * Last updated: Wed, 07 Jul 2021 17:02:28 GMT
 * Dependencies: [@types/pg-types](https://npmjs.com/package/@types/pg-types), [@types/pg-protocol](https://npmjs.com/package/@types/pg-protocol), [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/pspeter3), and [<PERSON>](https://github.com/HoldYourWaffle).
