{"name": "@types/hapi__catbox", "version": "10.2.6", "description": "TypeScript definitions for @hapi/catbox", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hapi__catbox", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "jasonswearingen", "url": "https://github.com/jasonswearingen"}, {"name": "AJP", "githubUsername": "AJamesPhillips", "url": "https://github.com/AJamesPhillips"}, {"name": "<PERSON>", "githubUsername": "saboya", "url": "https://github.com/saboya"}, {"name": "<PERSON>", "githubUsername": "lenovouser", "url": "https://github.com/lenovouser"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/hapi__catbox"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "5b2d9a4045f95d50d0b57a79bbe5afc1d92f006eaca6371501b6521145f372d5", "typeScriptVersion": "4.5"}