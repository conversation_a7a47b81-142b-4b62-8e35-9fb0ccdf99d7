{"name": "@types/koa__router", "version": "12.0.3", "description": "TypeScript definitions for @koa/router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/koa__router", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "hellopao", "url": "https://github.com/hellopao"}, {"name": "<PERSON>", "githubUsername": "schfkt", "url": "https://github.com/schfkt"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON><PERSON>", "githubUsername": "romain-faust", "url": "https://github.com/romain-faust"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "f<PERSON>nor", "url": "https://github.com/falinor"}, {"name": "<PERSON>", "githubUsername": "jdforsythe", "url": "https://github.com/jdforsythe"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/koa__router"}, "scripts": {}, "dependencies": {"@types/koa": "*"}, "typesPublisherContentHash": "a68c56c5cff6af9e1b44bbd1ac7a5323e465a07d764bc8d7ca54d8783deed7cb", "typeScriptVersion": "4.5"}