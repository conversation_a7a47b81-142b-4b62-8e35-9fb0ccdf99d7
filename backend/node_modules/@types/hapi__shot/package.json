{"name": "@types/hapi__shot", "version": "4.1.6", "description": "TypeScript definitions for @hapi/shot", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hapi__shot", "license": "MIT", "contributors": [{"name": "AJP", "githubUsername": "AJamesPhillips", "url": "https://github.com/AJamesPhillips"}, {"name": "<PERSON>", "githubUsername": "Simon<PERSON><PERSON><PERSON>", "url": "https://github.com/SimonSchick"}, {"name": "<PERSON>", "githubUsername": "lenovouser", "url": "https://github.com/lenovouser"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/hapi__shot"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "8dd447110c4679e3a4361ec2e1353b502cfb0cf696f854ab012af04e44c4e7f5", "typeScriptVersion": "4.5"}