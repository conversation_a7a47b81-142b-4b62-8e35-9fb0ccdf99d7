{"name": "@types/hapi__mimos", "version": "4.1.4", "description": "TypeScript definitions for @hapi/mimos", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hapi__mimos", "license": "MIT", "contributors": [{"name": "AJP", "url": "https://github.com/AJamesPhillips", "githubUsername": "AJamesPhillips"}, {"name": "<PERSON>", "url": "https://github.com/lenovouser", "githubUsername": "lenovouser"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU", "githubUsername": "LinusU"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/hapi__mimos"}, "scripts": {}, "dependencies": {"@types/mime-db": "*"}, "typesPublisherContentHash": "2aa1fb7a3d13443f11c156be8294a9ccf14043c43f94cda7b914c248b5f9b862", "typeScriptVersion": "3.6"}