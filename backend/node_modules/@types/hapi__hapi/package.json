{"name": "@types/hapi__hapi", "version": "20.0.13", "description": "TypeScript definitions for @hapi/hapi", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hapi__hapi", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/rafaelsouzaf", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jhsimms", "githubUsername": "jhsimms"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/saboya", "githubUsername": "saboya"}, {"name": "<PERSON>", "url": "https://github.com/lenovouser", "githubUsername": "lenovouser"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/hapi__hapi"}, "scripts": {}, "dependencies": {"@hapi/boom": "^9.0.0", "@hapi/iron": "^6.0.0", "@hapi/podium": "^4.1.3", "@types/hapi__catbox": "*", "@types/hapi__mimos": "*", "@types/hapi__shot": "*", "@types/node": "*", "joi": "^17.3.0"}, "typesPublisherContentHash": "6d8aaf0286fdcdd1ec2fb1eecfc9c12e320bf96b802aecead192eda6d40ff65b", "typeScriptVersion": "4.1"}