# Installation
> `npm install --save @types/hapi__hapi`

# Summary
This package contains type definitions for @hapi/hapi (https://github.com/hapijs/hapi).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hapi__hapi.

### Additional Details
 * Last updated: Wed, 19 Oct 2022 18:33:05 GMT
 * Dependencies: [@types/hapi__boom](https://npmjs.com/package/@types/hapi__boom), [@types/hapi__catbox](https://npmjs.com/package/@types/hapi__catbox), [@types/hapi__iron](https://npmjs.com/package/@types/hapi__iron), [@types/hapi__mimos](https://npmjs.com/package/@types/hapi__mimos), [@types/hapi__podium](https://npmjs.com/package/@types/hapi__podium), [@types/hapi__shot](https://npmjs.com/package/@types/hapi__shot), [@types/joi](https://npmjs.com/package/@types/joi), [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [Rafael Souza Fijalkowski](https://github.com/rafaelsouzaf), [Justin Simms](https://github.com/jhsimms), [Simon Schick](https://github.com/SimonSchick), [Rodrigo Saboya](https://github.com/saboya), and [Silas Rech](https://github.com/lenovouser).
