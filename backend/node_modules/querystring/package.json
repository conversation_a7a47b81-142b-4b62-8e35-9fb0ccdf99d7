{"name": "querystring", "id": "querystring", "version": "0.2.0", "description": "Node's querystring module for all engines.", "keywords": ["commonjs", "query", "querystring"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/Gozala/querystring.git", "web": "https://github.com/Gozala/querystring"}, "bugs": {"url": "http://github.com/Gozala/querystring/issues/"}, "devDependencies": {"test": "~0.x.0", "phantomify": "~0.x.0", "retape": "~0.x.0", "tape": "~0.1.5"}, "engines": {"node": ">=0.4.x"}, "scripts": {"test": "npm run test-node && npm run test-browser && npm run test-tap", "test-browser": "node ./node_modules/phantomify/bin/cmd.js ./test/common-index.js", "test-node": "node ./test/common-index.js", "test-tap": "node ./test/tap-index.js"}, "testling": {"files": "test/tap-index.js", "browsers": {"iexplore": [9, 10], "chrome": [16, 20, 25, "canary"], "firefox": [10, 15, 16, 17, 18, "nightly"], "safari": [5, 6], "opera": [12]}}, "licenses": [{"type": "MIT", "url": "https://github.com/Gozala/enchain/License.md"}]}