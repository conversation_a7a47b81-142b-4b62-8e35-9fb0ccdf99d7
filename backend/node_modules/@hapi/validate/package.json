{"name": "@hapi/validate", "description": "Object schema validation", "version": "1.1.3", "repository": "git://github.com/hapijs/validate", "main": "lib/index.js", "files": ["lib/**/*"], "dependencies": {"@hapi/hoek": "^9.0.0", "@hapi/topo": "^5.0.0"}, "devDependencies": {"@hapi/bourne": "2.x.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "scripts": {"test": "lab -t 100 -a @hapi/code -L", "test-cov-html": "lab -r html -o coverage.html -a @hapi/code"}, "license": "BSD-3-<PERSON><PERSON>"}