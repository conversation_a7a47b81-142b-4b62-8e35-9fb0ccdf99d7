{"name": "@hapi/b64", "description": "Base64 streaming encoder and decoder", "version": "5.0.0", "repository": "git://github.com/hapijs/b64", "main": "lib/index.js", "files": ["lib"], "keywords": ["buffer", "base64", "decode", "encode", "stream"], "dependencies": {"@hapi/hoek": "9.x.x"}, "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "22.x.x", "@hapi/wreck": "17.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}