{"name": "@hapi/podium", "description": "Node compatible event emitter with extra features", "version": "4.1.3", "repository": "git://github.com/hapijs/podium", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["emitter", "event"], "dependencies": {"@hapi/hoek": "9.x.x", "@hapi/teamwork": "5.x.x", "@hapi/validate": "1.x.x"}, "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "24.x.x", "typescript": "~4.0.3"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -r html -o coverage.html -a @hapi/code -L"}, "license": "BSD-3-<PERSON><PERSON>"}