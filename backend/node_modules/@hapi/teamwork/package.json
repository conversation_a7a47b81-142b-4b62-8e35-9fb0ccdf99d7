{"name": "@hapi/teamwork", "description": "Wait for multiple callback", "version": "5.1.1", "repository": "git://github.com/hapijs/teamwork", "main": "lib/index.js", "files": ["lib"], "keywords": ["async", "flow control", "callback"], "types": "lib/index.d.ts", "engines": {"node": ">=12.0.0"}, "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {}, "devDependencies": {"@hapi/code": "8.x.x", "@hapi/eslint-plugin": "*", "@hapi/lab": "24.x.x", "typescript": "~4.0.2"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}