{"name": "color", "version": "5.0.2", "description": "Color conversion and manipulation with CSS string support", "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "keywords": ["color", "colour", "css"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON>"], "license": "MIT", "repository": "Qix-/color", "xo": {"rules": {"no-bitwise": 0, "no-cond-assign": 0, "new-cap": 0, "unicorn/prefer-module": 0, "no-mixed-operators": 0, "complexity": 0, "unicorn/numeric-separators-style": 0}}, "files": ["LICENSE", "index.js", "index.d.ts"], "scripts": {"test": "xo && tsd && mocha"}, "engines": {"node": ">=18"}, "dependencies": {"color-convert": "^3.0.1", "color-string": "^2.0.0"}, "devDependencies": {"mocha": "11.1.0", "tsd": "0.31.2", "xo": "0.60.0"}}