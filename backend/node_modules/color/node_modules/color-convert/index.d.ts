export type Channels = number;
export type RGB = [r: number, g: number, b: number];
export type HSL = [h: number, s: number, l: number];
export type HSV = [h: number, s: number, v: number];
export type CMYK = [c: number, m: number, y: number, k: number];
export type LAB = [l: number, a: number, b: number];
export type LCH = [l: number, c: number, h: number];
export type HCG = [h: number, c: number, g: number];
export type HWB = [h: number, w: number, b: number];
export type XYZ = [x: number, y: number, z: number];
export type Apple = [r16: number, g16: number, b16: number];
export type Gray = [gray: number];
export type ANSI16 = number;
export type ANSI256 = number;
export type Keyword = string;
export type HEX = string;

declare namespace route {
	type rgb = {
		hsl(from: RGB): HSL;
		hsl(...from: RGB): HSL;
		hsl(from: RGB): HSL;
		hsl(...from: RGB): HSL;
		hsv(from: RGB): HSV;
		hsv(...from: RGB): HSV;
		hwb(from: RGB): HWB;
		hwb(...from: RGB): HWB;
		cmyk(from: RGB): CMYK;
		cmyk(...from: RGB): CMYK;
		xyz(from: RGB): XYZ;
		xyz(...from: RGB): XYZ;
		lab(from: RGB): LAB;
		lab(...from: RGB): LAB;
		lch(from: RGB): LCH;
		lch(...from: RGB): LCH;
		hex(from: RGB): HEX;
		hex(...from: RGB): HEX;
		keyword(from: RGB): Keyword;
		keyword(...from: RGB): Keyword;
		ansi16(from: RGB): ANSI16;
		ansi16(...from: RGB): ANSI16;
		ansi256(from: RGB): ANSI256;
		ansi256(...from: RGB): ANSI256;
		hcg(from: RGB): HCG;
		hcg(...from: RGB): HCG;
		apple(from: RGB): Apple;
		apple(...from: RGB): Apple;
		gray(from: RGB): Gray;
		gray(...from: RGB): Gray;
	};

	type hsl = {
		rgb(from: HSL): RGB;
		rgb(...from: HSL): RGB;
		hsv(from: HSL): HSV;
		hsv(...from: HSL): HSV;
		hwb(from: HSL): HWB;
		hwb(...from: HSL): HWB;
		cmyk(from: HSL): CMYK;
		cmyk(...from: HSL): CMYK;
		xyz(from: HSL): XYZ;
		xyz(...from: HSL): XYZ;
		lab(from: HSL): LAB;
		lab(...from: HSL): LAB;
		lch(from: HSL): LCH;
		lch(...from: HSL): LCH;
		hex(from: HSL): HEX;
		hex(...from: HSL): HEX;
		keyword(from: HSL): Keyword;
		keyword(...from: HSL): Keyword;
		ansi16(from: HSL): ANSI16;
		ansi16(...from: HSL): ANSI16;
		ansi256(from: HSL): ANSI256;
		ansi256(...from: HSL): ANSI256;
		hcg(from: HSL): HCG;
		hcg(...from: HSL): HCG;
		apple(from: HSL): Apple;
		apple(...from: HSL): Apple;
		gray(from: HSL): Gray;
		gray(...from: HSL): Gray;
	};

	type hsv = {
		rgb(from: HSV): RGB;
		rgb(...from: HSV): RGB;
		hsl(from: HSV): HSL;
		hsl(...from: HSV): HSL;
		hwb(from: HSV): HWB;
		hwb(...from: HSV): HWB;
		cmyk(from: HSV): CMYK;
		cmyk(...from: HSV): CMYK;
		xyz(from: HSV): XYZ;
		xyz(...from: HSV): XYZ;
		lab(from: HSV): LAB;
		lab(...from: HSV): LAB;
		lch(from: HSV): LCH;
		lch(...from: HSV): LCH;
		hex(from: HSV): HEX;
		hex(...from: HSV): HEX;
		keyword(from: HSV): Keyword;
		keyword(...from: HSV): Keyword;
		ansi16(from: HSV): ANSI16;
		ansi16(...from: HSV): ANSI16;
		ansi256(from: HSV): ANSI256;
		ansi256(...from: HSV): ANSI256;
		hcg(from: HSV): HCG;
		hcg(...from: HSV): HCG;
		apple(from: HSV): Apple;
		apple(...from: HSV): Apple;
		gray(from: HSV): Gray;
		gray(...from: HSV): Gray;
	};

	type hwb = {
		rgb(from: HWB): RGB;
		rgb(...from: HWB): RGB;
		hsl(from: HWB): HSL;
		hsl(...from: HWB): HSL;
		hsv(from: HWB): HSV;
		hsv(...from: HWB): HSV;
		cmyk(from: HWB): CMYK;
		cmyk(...from: HWB): CMYK;
		xyz(from: HWB): XYZ;
		xyz(...from: HWB): XYZ;
		lab(from: HWB): LAB;
		lab(...from: HWB): LAB;
		lch(from: HWB): LCH;
		lch(...from: HWB): LCH;
		hex(from: HWB): HEX;
		hex(...from: HWB): HEX;
		keyword(from: HWB): Keyword;
		keyword(...from: HWB): Keyword;
		ansi16(from: HWB): ANSI16;
		ansi16(...from: HWB): ANSI16;
		ansi256(from: HWB): ANSI256;
		ansi256(...from: HWB): ANSI256;
		hcg(from: HWB): HCG;
		hcg(...from: HWB): HCG;
		apple(from: HWB): Apple;
		apple(...from: HWB): Apple;
		gray(from: HWB): Gray;
		gray(...from: HWB): Gray;

	};

	type cmyk = {
		rgb(from: CMYK): RGB;
		rgb(...from: CMYK): RGB;
		hsl(from: CMYK): HSL;
		hsl(...from: CMYK): HSL;
		hsv(from: CMYK): HSV;
		hsv(...from: CMYK): HSV;
		hwb(from: CMYK): HWB;
		hwb(...from: CMYK): HWB;
		xyz(from: CMYK): XYZ;
		xyz(...from: CMYK): XYZ;
		lab(from: CMYK): LAB;
		lab(...from: CMYK): LAB;
		lch(from: CMYK): LCH;
		lch(...from: CMYK): LCH;
		hex(from: CMYK): HEX;
		hex(...from: CMYK): HEX;
		keyword(from: CMYK): Keyword;
		keyword(...from: CMYK): Keyword;
		ansi16(from: CMYK): ANSI16;
		ansi16(...from: CMYK): ANSI16;
		ansi256(from: CMYK): ANSI256;
		ansi256(...from: CMYK): ANSI256;
		hcg(from: CMYK): HCG;
		hcg(...from: CMYK): HCG;
		apple(from: CMYK): Apple;
		apple(...from: CMYK): Apple;
		gray(from: CMYK): Gray;
		gray(...from: CMYK): Gray;
	};

	type xyz = {
		rgb(from: XYZ): RGB;
		rgb(...from: XYZ): RGB;
		hsl(from: XYZ): HSL;
		hsl(...from: XYZ): HSL;
		hsv(from: XYZ): HSV;
		hsv(...from: XYZ): HSV;
		hwb(from: XYZ): HWB;
		hwb(...from: XYZ): HWB;
		cmyk(from: XYZ): CMYK;
		cmyk(...from: XYZ): CMYK;
		lab(from: XYZ): LAB;
		lab(...from: XYZ): LAB;
		lch(from: XYZ): LCH;
		lch(...from: XYZ): LCH;
		hex(from: XYZ): HEX;
		hex(...from: XYZ): HEX;
		keyword(from: XYZ): Keyword;
		keyword(...from: XYZ): Keyword;
		ansi16(from: XYZ): ANSI16;
		ansi16(...from: XYZ): ANSI16;
		ansi256(from: XYZ): ANSI256;
		ansi256(...from: XYZ): ANSI256;
		hcg(from: XYZ): HCG;
		hcg(...from: XYZ): HCG;
		apple(from: XYZ): Apple;
		apple(...from: XYZ): Apple;
		gray(from: XYZ): Gray;
		gray(...from: XYZ): Gray;
	};

	type lab = {
		rgb(from: LAB): RGB;
		rgb(...from: LAB): RGB;
		hsl(from: LAB): HSL;
		hsl(...from: LAB): HSL;
		hsv(from: LAB): HSV;
		hsv(...from: LAB): HSV;
		hwb(from: LAB): HWB;
		hwb(...from: LAB): HWB;
		cmyk(from: LAB): CMYK;
		cmyk(...from: LAB): CMYK;
		xyz(from: LAB): XYZ;
		xyz(...from: LAB): XYZ;
		lch(from: LAB): LCH;
		lch(...from: LAB): LCH;
		hex(from: LAB): HEX;
		hex(...from: LAB): HEX;
		keyword(from: LAB): Keyword;
		keyword(...from: LAB): Keyword;
		ansi16(from: LAB): ANSI16;
		ansi16(...from: LAB): ANSI16;
		ansi256(from: LAB): ANSI256;
		ansi256(...from: LAB): ANSI256;
		hcg(from: LAB): HCG;
		hcg(...from: LAB): HCG;
		apple(from: LAB): Apple;
		apple(...from: LAB): Apple;
		gray(from: LAB): Gray;
		gray(...from: LAB): Gray;
	};

	type lch = {
		rgb(from: LCH): RGB;
		rgb(...from: LCH): RGB;
		hsl(from: LCH): HSL;
		hsl(...from: LCH): HSL;
		hsv(from: LCH): HSV;
		hsv(...from: LCH): HSV;
		hwb(from: LCH): HWB;
		hwb(...from: LCH): HWB;
		cmyk(from: LCH): CMYK;
		cmyk(...from: LCH): CMYK;
		xyz(from: LCH): XYZ;
		xyz(...from: LCH): XYZ;
		lab(from: LCH): LAB;
		lab(...from: LCH): LAB;
		hex(from: LCH): HEX;
		hex(...from: LCH): HEX;
		keyword(from: LCH): Keyword;
		keyword(...from: LCH): Keyword;
		ansi16(from: LCH): ANSI16;
		ansi16(...from: LCH): ANSI16;
		ansi256(from: LCH): ANSI256;
		ansi256(...from: LCH): ANSI256;
		hcg(from: LCH): HCG;
		hcg(...from: LCH): HCG;
		apple(from: LCH): Apple;
		apple(...from: LCH): Apple;
		gray(from: LCH): Gray;
		gray(...from: LCH): Gray;
	};

	type hex = {
		rgb(from: HEX): RGB;
		hsl(from: HEX): HSL;
		hsv(from: HEX): HSV;
		hwb(from: HEX): HWB;
		cmyk(from: HEX): CMYK;
		xyz(from: HEX): XYZ;
		lab(from: HEX): LAB;
		lch(from: HEX): LCH;
		keyword(from: HEX): Keyword;
		ansi16(from: HEX): ANSI16;
		ansi256(from: HEX): ANSI256;
		hcg(from: HEX): HCG;
		apple(from: HEX): Apple;
		gray(from: HEX): Gray;
	};

	type keyword = {
		rgb(from: Keyword): RGB;
		hsl(from: Keyword): HSL;
		hsv(from: Keyword): HSV;
		hwb(from: Keyword): HWB;
		cmyk(from: Keyword): CMYK;
		xyz(from: Keyword): XYZ;
		lab(from: Keyword): LAB;
		lch(from: Keyword): LCH;
		hex(from: Keyword): HEX;
		ansi16(from: Keyword): ANSI16;
		ansi256(from: Keyword): ANSI256;
		hcg(from: Keyword): HCG;
		apple(from: Keyword): Apple;
		gray(from: Keyword): Gray;
	};

	type ansi16 = {
		rgb(from: ANSI16): RGB;
		hsl(from: ANSI16): HSL;
		hsv(from: ANSI16): HSV;
		hwb(from: ANSI16): HWB;
		cmyk(from: ANSI16): CMYK;
		xyz(from: ANSI16): XYZ;
		lab(from: ANSI16): LAB;
		lch(from: ANSI16): LCH;
		hex(from: ANSI16): HEX;
		keyword(from: ANSI16): Keyword;
		ansi256(from: ANSI16): ANSI256;
		hcg(from: ANSI16): HCG;
		apple(from: ANSI16): Apple;
		gray(from: ANSI16): Gray;
	};

	type ansi256 = {
		rgb(from: ANSI256): RGB;
		hsl(from: ANSI256): HSL;
		hsv(from: ANSI256): HSV;
		hwb(from: ANSI256): HWB;
		cmyk(from: ANSI256): CMYK;
		xyz(from: ANSI256): XYZ;
		lab(from: ANSI256): LAB;
		lch(from: ANSI256): LCH;
		hex(from: ANSI256): HEX;
		keyword(from: ANSI256): Keyword;
		ansi16(from: ANSI256): ANSI16;
		hcg(from: ANSI256): HCG;
		apple(from: ANSI256): Apple;
		gray(from: ANSI256): Gray;
	};

	type hcg = {
		rgb(from: HCG): RGB;
		rgb(...from: HCG): RGB;
		hsl(from: HCG): HSL;
		hsl(...from: HCG): HSL;
		hsv(from: HCG): HSV;
		hsv(...from: HCG): HSV;
		hwb(from: HCG): HWB;
		hwb(...from: HCG): HWB;
		cmyk(from: HCG): CMYK;
		cmyk(...from: HCG): CMYK;
		xyz(from: HCG): XYZ;
		xyz(...from: HCG): XYZ;
		lab(from: HCG): LAB;
		lab(...from: HCG): LAB;
		lch(from: HCG): LCH;
		lch(...from: HCG): LCH;
		hex(from: HCG): HEX;
		hex(...from: HCG): HEX;
		keyword(from: HCG): Keyword;
		keyword(...from: HCG): Keyword;
		ansi16(from: HCG): ANSI16;
		ansi16(...from: HCG): ANSI16;
		ansi256(from: HCG): ANSI256;
		ansi256(...from: HCG): ANSI256;
		apple(from: HCG): Apple;
		apple(...from: HCG): Apple;
		gray(from: HCG): Gray;
		gray(...from: HCG): Gray;

	};

	type apple = {
		rgb(from: Apple): RGB;
		rgb(...from: Apple): RGB;
		hsl(from: Apple): HSL;
		hsl(...from: Apple): HSL;
		hsv(from: Apple): HSV;
		hsv(...from: Apple): HSV;
		hwb(from: Apple): HWB;
		hwb(...from: Apple): HWB;
		cmyk(from: Apple): CMYK;
		cmyk(...from: Apple): CMYK;
		xyz(from: Apple): XYZ;
		xyz(...from: Apple): XYZ;
		lab(from: Apple): LAB;
		lab(...from: Apple): LAB;
		lch(from: Apple): LCH;
		lch(...from: Apple): LCH;
		hex(from: Apple): HEX;
		hex(...from: Apple): HEX;
		keyword(from: Apple): Keyword;
		keyword(...from: Apple): Keyword;
		ansi16(from: Apple): ANSI16;
		ansi16(...from: Apple): ANSI16;
		ansi256(from: Apple): ANSI256;
		ansi256(...from: Apple): ANSI256;
		hcg(from: Apple): HCG;
		hcg(...from: Apple): HCG;
		gray(from: Apple): Gray;
		gray(...from: Apple): Gray;
	};

	type gray = {
		rgb(from: Gray): RGB;
		rgb(...from: Gray): RGB;
		hsl(from: Gray): HSL;
		hsl(...from: Gray): HSL;
		hsv(from: Gray): HSV;
		hsv(...from: Gray): HSV;
		hwb(from: Gray): HWB;
		hwb(...from: Gray): HWB;
		cmyk(from: Gray): CMYK;
		cmyk(...from: Gray): CMYK;
		xyz(from: Gray): XYZ;
		xyz(...from: Gray): XYZ;
		lab(from: Gray): LAB;
		lab(...from: Gray): LAB;
		lch(from: Gray): LCH;
		lch(...from: Gray): LCH;
		hex(from: Gray): HEX;
		hex(...from: Gray): HEX;
		keyword(from: Gray): Keyword;
		keyword(...from: Gray): Keyword;
		ansi16(from: Gray): ANSI16;
		ansi16(...from: Gray): ANSI16;
		ansi256(from: Gray): ANSI256;
		ansi256(...from: Gray): ANSI256;
		hcg(from: Gray): HCG;
		hcg(...from: Gray): HCG;
		apple(from: Gray): Apple;
		apple(...from: Gray): Apple;
	};
}

declare function route(fromModel: 'rgb'): route.rgb;
declare function route(fromModel: 'hsl'): route.hsl;
declare function route(fromModel: 'hsv'): route.hsv;
declare function route(fromModel: 'hwb'): route.hwb;
declare function route(fromModel: 'cmyk'): route.cmyk;
declare function route(fromModel: 'xyz'): route.xyz;
declare function route(fromModel: 'lab'): route.lab;
declare function route(fromModel: 'lch'): route.lch;
declare function route(fromModel: 'hex'): route.hex;
declare function route(fromModel: 'keyword'): route.keyword;
declare function route(fromModel: 'ansi16'): route.ansi16;
declare function route(fromModel: 'ansi256'): route.ansi256;
declare function route(fromModel: 'hcg'): route.hcg;
declare function route(fromModel: 'apple'): route.apple;
declare function route(fromModel: 'gray'): route.gray;

export type Convert = {
	rgb: {
		channels: Channels;
		labels: 'rgb';
		hsl: {
			(...rgb: RGB): HSL;
			raw: (...rgb: RGB) => HSL;
		};
		hsv: {
			(...rgb: RGB): HSV;
			raw: (...rgb: RGB) => HSV;
		};
		hwb: {
			(...rgb: RGB): HWB;
			raw: (...rgb: RGB) => HWB;
		};
		hcg: {
			(...rgb: RGB): HCG;
			raw: (...rgb: RGB) => HCG;
		};
		cmyk: {
			(...rgb: RGB): CMYK;
			raw: (...rgb: RGB) => CMYK;
		};
		keyword: {
			(...rgb: RGB): Keyword;
			raw: (...rgb: RGB) => Keyword;
		};
		ansi16: {
			(...rgb: RGB): ANSI16;
			raw: (...rgb: RGB) => ANSI16;
		};
		ansi256: {
			(...rgb: RGB): ANSI256;
			raw: (...rgb: RGB) => ANSI256;
		};
		apple: {
			(...rgb: RGB): Apple;
			raw: (...rgb: RGB) => Apple;
		};
		hex: {
			(...rgb: RGB): HEX;
			raw: (...rgb: RGB) => HEX;
		};
		gray: {
			(...rgb: RGB): Gray;
			raw: (...rgb: RGB) => Gray;
		};
	} & route.rgb & {
		[F in keyof route.rgb]: {
			raw: route.rgb[F];
		};
	};
	keyword: {
		channels: Channels;
		rgb: {
			(keyword: Keyword): RGB;
			raw: (keyword: Keyword) => RGB;
		};
	} & route.keyword & {
		[F in keyof route.keyword]: {
			raw: route.keyword[F];
		};
	};
	hsl: {
		channels: Channels;
		labels: 'hsl';
		rgb: {
			(...hsl: HSL): RGB;
			raw: (...hsl: HSL) => RGB;
		};
		hsv: {
			(...hsl: HSL): HSV;
			raw: (...hsl: HSL) => HSV;
		};
		hcg: {
			(...hsl: HSL): HCG;
			raw: (...hsl: HSL) => HCG;
		};
	} & route.hsl & {
		[F in keyof route.hsl]: {
			raw: route.hsl[F];
		};
	};
	hsv: {
		channels: Channels;
		labels: 'hsv';
		hcg: {
			(...hsv: HSV): HCG;
			raw: (...hsv: HSV) => HCG;
		};
		rgb: {
			(...hsv: HSV): RGB;
			raw: (...hsv: HSV) => RGB;
		};
		hsv: {
			(...hsv: HSV): HSV;
			raw: (...hsv: HSV) => HSV;
		};
		hsl: {
			(...hsv: HSV): HSL;
			raw: (...hsv: HSV) => HSL;
		};
		hwb: {
			(...hsv: HSV): HWB;
			raw: (...hsv: HSV) => HWB;
		};
		ansi16: {
			(...hsv: HSV): ANSI16;
			raw: (...hsv: HSV) => ANSI16;
		};
	} & route.hsv & {
		[F in keyof route.hsv]: {
			raw: route.hsv[F];
		};
	};
	hwb: {
		channels: Channels;
		labels: 'hwb';
		hcg: {
			(...hwb: HWB): HCG;
			raw: (...hwb: HWB) => HCG;
		};
		rgb: {
			(...hwb: HWB): RGB;
			raw: (...hwb: HWB) => RGB;
		};
	} & route.hwb & {
		[F in keyof route.hwb]: {
			raw: route.hwb[F];
		};
	};
	cmyk: {
		channels: Channels;
		labels: 'cmyk';
		rgb: {
			(...cmyk: CMYK): RGB;
			raw: (...cmyk: CMYK) => RGB;
		};
	} & route.cmyk & {
		[F in keyof route.cmyk]: {
			raw: route.cmyk[F];
		};
	};
	xyz: {
		channels: Channels;
		labels: 'xyz';
		rgb: {
			(...xyz: XYZ): RGB;
			raw: (...xyz: XYZ) => RGB;
		};
		lab: {
			(...xyz: XYZ): LAB;
			raw: (...xyz: XYZ) => LAB;
		};
	} & route.xyz & {
		[F in keyof route.xyz]: {
			raw: route.xyz[F];
		};
	};
	lab: {
		channels: Channels;
		labels: 'lab';
		xyz: {
			(...lab: LAB): XYZ;
			raw: (...lab: LAB) => XYZ;
		};
		lch: {
			(...lab: LAB): LCH;
			raw: (...lab: LAB) => LCH;
		};
	} & route.lab & {
		[F in keyof route.lab]: {
			raw: route.lab[F];
		};
	};
	lch: {
		channels: Channels;
		labels: 'lch';
		lab: {
			(...lch: LCH): LAB;
			raw: (...lch: LCH) => LAB;
		};
	} & route.lch & {
		[F in keyof route.lch]: {
			raw: route.lch[F];
		};
	};
	hex: {
		channels: Channels;
		labels: ['hex'];
		rgb: {
			(hex: HEX): RGB;
			raw: (hex: HEX) => RGB;
		};
	} & route.hex & {
		[F in keyof route.hex]: {
			raw: route.hex[F];
		};
	};
	ansi16: {
		channels: Channels;
		labels: ['ansi16'];
		rgb: {
			(ansi16: ANSI16): RGB;
			raw: (ansi16: ANSI16) => RGB;
		};
	} & route.ansi16 & {
		[F in keyof route.ansi16]: {
			raw: route.ansi16[F];
		};
	};
	ansi256: {
		channels: Channels;
		labels: ['ansi256'];
		rgb: {
			(ansi256: ANSI256): RGB;
			raw: (ansi256: ANSI256) => RGB;
		};
	} & route.ansi256 & {
		[F in keyof route.ansi256]: {
			raw: route.ansi256[F];
		};
	};
	hcg: {
		channels: Channels;
		labels: ['h', 'c', 'g'];
		rgb: {
			(...hcg: HCG): RGB;
			raw: (...hcg: HCG) => RGB;
		};
		hsv: {
			(...hcg: HCG): HSV;
			raw: (...hcg: HCG) => HSV;
		};
		hwb: {
			(...hcg: HCG): HWB;
			raw: (...hcg: HCG) => HWB;
		};
	} & route.hcg & {
		[F in keyof route.hcg]: {
			raw: route.hcg[F];
		};
	};
	apple: {
		channels: Channels;
		labels: ['r16', 'g16', 'b16'];
		rgb: {
			(...apple: Apple): RGB;
			raw: (...apple: Apple) => RGB;
		};
	} & route.apple & {
		[F in keyof route.apple]: {
			raw: route.apple[F];
		};
	};
	gray: {
		channels: Channels;
		labels: ['gray'];
		rgb: {
			(...gray: Gray): RGB;
			raw: (...gray: Gray) => RGB;
		};
		hsl: {
			(...gray: Gray): HSL;
			raw: (...gray: Gray) => HSL;
		};
		hsv: {
			(...gray: Gray): HSV;
			raw: (...gray: Gray) => HSV;
		};
		hwb: {
			(...gray: Gray): HWB;
			raw: (...gray: Gray) => HWB;
		};
		cmyk: {
			(...gray: Gray): CMYK;
			raw: (...gray: Gray) => CMYK;
		};
		lab: {
			(...gray: Gray): LAB;
			raw: (...gray: Gray) => LAB;
		};
		hex: {
			(...gray: Gray): HEX;
			raw: (...gray: Gray) => HEX;
		};
	} & route.gray & {
		[F in keyof route.gray]: {
			raw: route.gray[F];
		};
	};
};

declare const convert: Convert;
export default convert;
