{"name": "color-convert", "description": "Plain color conversion functions", "version": "3.1.2", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "Qix-/color-convert", "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.6"}, "scripts": {"test": "xo && tsd && node test/basic.js"}, "files": ["index.js", "index.d.ts", "conversions.js", "route.js"], "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0, "unicorn/prefer-exponentiation-operator": 0, "@typescript-eslint/naming-convention": 0}}, "devDependencies": {"chalk": "^5.2.0", "jimp": "^0.22.8", "tsd": "^0.28.1", "xo": "^0.54.2"}, "dependencies": {"color-name": "^2.0.0"}}