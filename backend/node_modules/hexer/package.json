{"name": "hexer", "version": "1.5.0", "description": "<PERSON><PERSON> (streaming, sync, and cli)", "keywords": ["hex", "dump", "hexdump"], "author": "<PERSON> <<EMAIL>>", "repository": "git://github.com/jcorbin/hexer.git", "main": "index.js", "homepage": "https://github.com/jcorbin/hexer", "bugs": {"url": "https://github.com/jcorbin/hexer/issues", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}], "bin": {"hexer": "./cli.js"}, "dependencies": {"ansi-color": "^0.2.1", "minimist": "^1.1.0", "process": "^0.10.0", "xtend": "^4.0.0"}, "devDependencies": {"coveralls": "^2.10.0", "error": "^5.2.0", "istanbul": "^0.3.5", "itape": "^1.5.0", "jshint": "^2.6.0", "opn": "^1.0.1", "pre-commit": "0.0.9", "tape": "^3.4.0"}, "licenses": [{"type": "MIT", "url": "http://github.com/jcorbin/hexer/raw/master/LICENSE"}], "scripts": {"check-cover": "istanbul check-coverage", "check-ls": "npm ls 1>/dev/null", "cover": "npm run test-cover -s && npm run check-cover -s", "lint": "jshint .", "test": "npm run check-ls -s && npm run lint -s && npm run cover -s", "test-cover": "istanbul cover --report html --print detail -- test/index.js", "trace": "itape test/index.js --trace", "travis": "npm run cover -s && istanbul report lcov && ((cat coverage/lcov.info | coveralls) || exit 0)", "view-cover": "opn ./coverage/index.html"}, "engines": {"node": ">= 0.10.x"}, "pre-commit": ["test"], "pre-commit.silent": true, "itape": {"trace": {"debuglog": ["hexer"], "leakedHandles": {"timeout": 5001, "debugSockets": true}, "formatStack": true}}, "private": false, "uber-ngen-version": "5.0.0"}