# Compiled source #
###################
*.com
*.class
*.dll
*.exe
*.a
*.o
*.so
*.node

# Node Waf Byproducts #
#######################
.lock-wscript
build/
autom4te.cache/

# Node Modules #
################
# Better to let npm install these from the package.json defintion
# rather than maintain this manually
node_modules/

# Packages #
############
# it's better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Logs and databases #
######################
*.log
dump.rdb
*.tap
*.xml
coverage/

# OS generated files #
######################
.DS_Store?
.DS_Store
ehthumbs.db
Icon?
Thumbs.db
