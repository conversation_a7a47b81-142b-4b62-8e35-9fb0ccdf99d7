# v1.4.0

- Addresses deprecation of Buffer constructor forms

# v1.3.0

- Addresses a dependency issue with hexer that complicated bundling for use in
  browser scripts.

# v1.2.1

- Fixes a regression in SeriesRW, which would modify the reused result if it
  happened to be an array.

# v1.2.0

- Adds support for result object reuse.

# v1.1.0

- Adds an optional lazy mode to VariableBufferRW

# v1.0.0

:cake:
