{"name": "bufrw", "version": "1.4.0", "description": "B<PERSON>er Reading and Writing", "keywords": [], "author": "<PERSON> <<EMAIL>>", "repository": "git://github.com/uber/bufrw.git", "main": "index.js", "homepage": "https://github.com/uber/bufrw", "bugs": {"url": "https://github.com/uber/bufrw/issues", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}], "dependencies": {"ansi-color": "^0.2.1", "error": "^7.0.0", "hexer": "^1.5.0", "xtend": "^4.0.0"}, "devDependencies": {"coveralls": "^2.10.0", "faucet": "0.0.1", "istanbul": "^0.3.5", "itape": "^1.5.0", "jshint": "^2.6.3", "opn": "^1.0.1", "pre-commit": "0.0.9", "tape": "^3.4.0", "uber-licence": "^1.2.0"}, "licenses": [{"type": "MIT", "url": "http://github.com/uber/bufrw/raw/master/LICENSE"}], "scripts": {"add-licence": "uber-licence", "check-licence": "uber-licence --dry", "check-ls": "npm ls 1>/dev/null", "cover": "istanbul cover --report html --print none test/index.js | faucet && istanbul report text", "lint": "jshint .", "test": "npm run check-ls -s && npm run lint -s && npm run cover -s && istanbul check-coverage", "trace": "itape test/index.js --trace", "travis": "npm run cover -s && istanbul report lcov && ((cat coverage/lcov.info | coveralls) || exit 0)", "view-cover": "opn ./coverage/index.html"}, "engines": {"node": ">= 0.10.x"}, "pre-commit": ["check-licence", "test"], "pre-commit.silent": true, "itape": {"trace": {"debuglog": ["bufrw"], "leakedHandles": {"timeout": 5001, "debugSockets": true}, "formatStack": true}}, "private": false, "uber-ngen-version": "5.0.0"}