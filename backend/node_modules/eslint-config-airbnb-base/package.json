{"name": "eslint-config-airbnb-base", "version": "15.0.0", "description": "Airbnb's base JS ESLint config, following our styleguide", "main": "index.js", "exports": {".": "./index.js", "./legacy": "./legacy.js", "./whitespace": "./whitespace.js", "./rules/best-practices": "./rules/best-practices.js", "./rules/es6": "./rules/es6.js", "./rules/node": "./rules/node.js", "./rules/style": "./rules/style.js", "./rules/errors": "./rules/errors.js", "./rules/imports": "./rules/imports.js", "./rules/strict": "./rules/strict.js", "./rules/variables": "./rules/variables.js", "./package.json": "./package.json"}, "scripts": {"prelint": "eclint check * rules/* test/*", "lint": "eslint --report-unused-disable-directives .", "pretests-only": "node ./test/requires", "tests-only": "babel-tape-runner ./test/test-*.js", "prepublishOnly": "eslint-find-rules --unused && npm test && safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "pretravis": ":", "travis": "npm run --silent tests-only", "posttravis": ":"}, "repository": {"type": "git", "url": "https://github.com/airbnb/javascript"}, "keywords": ["eslint", "eslintconfig", "config", "airbnb", "javascript", "styleguide", "es2015", "es2016", "es2017", "es2018"], "author": "<PERSON> (https://twitter.com/@jitl)", "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/jitl"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON>", "url": "https://twitter.com/hshoff"}], "license": "MIT", "bugs": {"url": "https://github.com/airbnb/javascript/issues"}, "homepage": "https://github.com/airbnb/javascript", "devDependencies": {"@babel/runtime": "^7.16.0", "babel-preset-airbnb": "^4.5.0", "babel-tape-runner": "^3.0.0", "eclint": "^2.8.1", "eslint": "^7.32.0 || ^8.2.0", "eslint-find-rules": "^4.0.0", "eslint-plugin-import": "^2.25.2", "in-publish": "^2.0.1", "safe-publish-latest": "^2.0.0", "tape": "^5.3.1"}, "peerDependencies": {"eslint": "^7.32.0 || ^8.2.0", "eslint-plugin-import": "^2.25.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "dependencies": {"confusing-browser-globals": "^1.0.10", "object.assign": "^4.1.2", "object.entries": "^1.1.5", "semver": "^6.3.0"}}