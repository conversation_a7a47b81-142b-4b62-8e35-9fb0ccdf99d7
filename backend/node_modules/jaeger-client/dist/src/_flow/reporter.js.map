{"version": 3, "sources": ["../../../src/_flow/reporter.js"], "names": [], "mappings": ";;AAaA", "file": "reporter.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Span from '../span.js';\n\ndeclare interface Reporter {\n  report(span: Span): void;\n  close(callback?: () => void): void;\n  setProcess(serviceName: string, tags: Array<Tag>): void;\n}\n\ndeclare class Sender {\n  append(span: Span, callback?: SenderCallback): void;\n  flush(callback?: SenderCallback): void;\n  close(): void;\n  setProcess(process: Process): void;\n}\n\ndeclare type SenderCallback = (numSpans: number, err?: string) => void;\n"]}