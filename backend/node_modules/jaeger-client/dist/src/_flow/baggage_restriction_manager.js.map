{"version": 3, "sources": ["../../../src/_flow/baggage_restriction_manager.js"], "names": [], "mappings": ";;AAaA", "file": "baggage_restriction_manager.js", "sourcesContent": ["// @flow\n// Copyright (c) 2017 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Restriction from '../baggage/restriction.js';\n\n/**\n * BaggageRestrictionManager is an interface for a class that manages baggage\n * restrictions for baggage keys. The manager will return a Restriction\n * for a specific baggage key which will determine whether the baggage key is\n * allowed and any other applicable restrictions on the baggage value.\n */\ndeclare interface BaggageRestrictionManager {\n  getRestriction(service: string, key: string): Restriction;\n}\n"]}