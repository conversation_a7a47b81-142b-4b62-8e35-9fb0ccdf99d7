{"version": 3, "sources": ["../../../src/_flow/sampler.js"], "names": [], "mappings": ";;AAaA", "file": "sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2019 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Span from '../span';\nimport type { SamplerApiVersion } from '../samplers/constants';\n\n/**\n * Sampler methods return SamplingDecision struct.\n */\ndeclare type SamplingDecision = {\n  sample: boolean,\n  retryable: boolean,\n  tags: ?{},\n};\n\ndeclare interface Sampler {\n  apiVersion: SamplerApiVersion;\n\n  onCreateSpan(span: Span): SamplingDecision;\n  onSetOperationName(span: Span, operationName: string): SamplingDecision;\n  onSetTag(span: Span, key: string, value: any): SamplingDecision;\n\n  close(callback: ?() => void): void;\n}\n"]}