{"version": 3, "sources": ["../../../src/propagators/zipkin_b3_text_map_codec.js"], "names": ["constants", "ZIPKIN_PARENTSPAN_HEADER", "ZIPKIN_SPAN_HEADER", "ZIPKIN_TRACE_HEADER", "ZIPKIN_SAMPLED_HEADER", "ZIPKIN_FLAGS_HEADER", "ZipkinB3TextMapCodec", "options", "_urlEncoding", "urlEncoding", "_baggagePrefix", "baggagePrefix", "TRACER_BAGGAGE_HEADER_PREFIX", "toLowerCase", "_metrics", "metrics", "Metrics", "NoopMetricFactory", "value", "encodeURIComponent", "indexOf", "_decodeURIValue", "isNaN", "parseInt", "decodeURIComponent", "e", "carrier", "baggage", "flags", "debugId", "parentId", "spanId", "traceId", "key", "Object", "prototype", "hasOwnProperty", "call", "lowerKey", "_decodeValue", "SAMPLED_MASK", "DEBUG_MASK", "JAEGER_DEBUG_HEADER", "JAEGER_BAGGAGE_HEADER", "Utils", "startsWith", "keyWithoutPrefix", "substring", "length", "_isValidZipkinId", "decodingErrors", "increment", "SpanContext", "withStringIds", "spanContext", "traceIdStr", "parentIdStr", "spanIdStr", "isDebug", "isSampled", "_encodeValue"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;IAAYA,S;;AACZ;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;;;;;AAEA,IAAMC,2BAA2B,mBAAjC;AACA,IAAMC,qBAAqB,aAA3B;AACA,IAAMC,sBAAsB,cAA5B;AACA,IAAMC,wBAAwB,cAA9B;AACA,IAAMC,sBAAsB,YAA5B;;IAEqBC,oB;AAKnB,kCAA+B;AAAA,QAAnBC,OAAmB,uEAAJ,EAAI;;AAAA;;AAC7B,SAAKC,YAAL,GAAoB,CAAC,CAACD,QAAQE,WAA9B;AACA,SAAKC,cAAL,GAAsBH,QAAQI,aAAR,IAAyBX,UAAUY,4BAAzD;AACA,SAAKF,cAAL,GAAsB,KAAKA,cAAL,CAAoBG,WAApB,EAAtB;AACA,SAAKC,QAAL,GAAgBP,QAAQQ,OAAR,IAAmB,IAAIC,iBAAJ,CAAY,IAAIC,wBAAJ,EAAZ,CAAnC;AACD;;;;iCAEYC,K,EAAuB;AAClC,UAAI,KAAKV,YAAT,EAAuB;AACrB,eAAOW,mBAAmBD,KAAnB,CAAP;AACD;;AAED,aAAOA,KAAP;AACD;;;iCAEYA,K,EAAuB;AAClC;AACA,UAAI,KAAKV,YAAL,IAAqBU,MAAME,OAAN,CAAc,GAAd,IAAqB,CAAC,CAA/C,EAAkD;AAChD,eAAO,KAAKC,eAAL,CAAqBH,KAArB,CAAP;AACD;;AAED,aAAOA,KAAP;AACD;;;qCAEgBA,K,EAAwB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAI,CAACA,KAAL,EAAY;AACV,eAAO,IAAP;AACD;;AAED,aAAO,CAACI,MAAMC,SAASL,KAAT,EAAgB,EAAhB,CAAN,CAAR;AACD;;;oCAEeA,K,EAAuB;AACrC;AACA,UAAI;AACF,eAAOM,mBAAmBN,KAAnB,CAAP;AACD,OAFD,CAEE,OAAOO,CAAP,EAAU;AACV,eAAOP,KAAP;AACD;AACF;;;4BAEOQ,O,EAA4B;AAClC,UAAIC,UAAU,EAAd;AACA,UAAIC,QAAQ,CAAZ;AACA,UAAIC,UAAU,EAAd;AACA,UAAIC,WAAW,EAAf;AACA,UAAIC,SAAS,EAAb;AACA,UAAIC,UAAU,EAAd;;AAEA,WAAK,IAAIC,GAAT,IAAgBP,OAAhB,EAAyB;AACvB,YAAIQ,OAAOC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCX,OAArC,EAA8CO,GAA9C,CAAJ,EAAwD;AACtD,cAAIK,WAAWL,IAAIpB,WAAJ,EAAf;;AAEA,kBAAQyB,QAAR;AACE,iBAAKrC,wBAAL;AACE6B,yBAAW,KAAKS,YAAL,CAAkBb,QAAQO,GAAR,CAAlB,CAAX;AACA;AACF,iBAAK/B,kBAAL;AACE6B,uBAAS,KAAKQ,YAAL,CAAkBb,QAAQO,GAAR,CAAlB,CAAT;AACA;AACF,iBAAK9B,mBAAL;AACE6B,wBAAU,KAAKO,YAAL,CAAkBb,QAAQO,GAAR,CAAlB,CAAV;AACA;AACF,iBAAK7B,qBAAL;AACE,kBAAIsB,QAAQO,GAAR,MAAiB,GAAjB,IAAwBP,QAAQO,GAAR,MAAiB,MAA7C,EAAqD;AACnDL,wBAAQA,QAAQ5B,UAAUwC,YAA1B;AACD;AACD;AACF,iBAAKnC,mBAAL;AACE;AACA;AACA;AACA;AACA,kBAAIqB,QAAQO,GAAR,MAAiB,GAArB,EAA0B;AACxBL,wBAAQA,QAAQ5B,UAAUwC,YAAlB,GAAiCxC,UAAUyC,UAAnD;AACD;AACD;AACF,iBAAKzC,UAAU0C,mBAAf;AACEb,wBAAU,KAAKU,YAAL,CAAkBb,QAAQO,GAAR,CAAlB,CAAV;AACA;AACF,iBAAKjC,UAAU2C,qBAAf;AACE,uDAA2BhB,OAA3B,EAAoC,KAAKY,YAAL,CAAkBb,QAAQO,GAAR,CAAlB,CAApC;AACA;AACF;AACE,kBAAIW,eAAMC,UAAN,CAAiBP,QAAjB,EAA2B,KAAK5B,cAAhC,CAAJ,EAAqD;AACnD,oBAAIoC,mBAAmBb,IAAIc,SAAJ,CAAc,KAAKrC,cAAL,CAAoBsC,MAAlC,CAAvB;AACArB,wBAAQmB,gBAAR,IAA4B,KAAKP,YAAL,CAAkBb,QAAQO,GAAR,CAAlB,CAA5B;AACD;AAlCL;AAoCD;AACF;;AAED,UACE,CAAC,KAAKgB,gBAAL,CAAsBjB,OAAtB,CAAD,IACA,CAAC,KAAKiB,gBAAL,CAAsBlB,MAAtB,CADD,IAEA,CAAC,KAAKkB,gBAAL,CAAsBnB,QAAtB,CAHH,EAIE;AACA;AACA;AACA;AACAE,kBAAUD,SAASD,WAAW,EAA9B;AACA,aAAKhB,QAAL,CAAcoC,cAAd,CAA6BC,SAA7B,CAAuC,CAAvC;AACD;;AAED,aAAOC,uBAAYC,aAAZ,CAA0BrB,OAA1B,EAAmCD,MAAnC,EAA2CD,QAA3C,EAAqDF,KAArD,EAA4DD,OAA5D,EAAqEE,OAArE,CAAP;AACD;;;2BAEMyB,W,EAA0B5B,O,EAAoB;AACnDA,cAAQvB,mBAAR,IAA+BmD,YAAYC,UAA3C;AACA,UAAID,YAAYE,WAAhB,EAA6B;AAC3B9B,gBAAQzB,wBAAR,IAAoCqD,YAAYE,WAAhD;AACD;AACD9B,cAAQxB,kBAAR,IAA8BoD,YAAYG,SAA1C;;AAEA,UAAIH,YAAYI,OAAZ,EAAJ,EAA2B;AACzBhC,gBAAQrB,mBAAR,IAA+B,GAA/B;AACD,OAFD,MAEO;AACL;AACA;AACA;AACAqB,gBAAQtB,qBAAR,IAAiCkD,YAAYK,SAAZ,KAA0B,GAA1B,GAAgC,GAAjE;AACD;;AAED,UAAIhC,UAAU2B,YAAY3B,OAA1B;AACA,WAAK,IAAIM,GAAT,IAAgBN,OAAhB,EAAyB;AACvB,YAAIO,OAAOC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCV,OAArC,EAA8CM,GAA9C,CAAJ,EAAwD;AACtD,cAAIf,QAAQ,KAAK0C,YAAL,CAAkBN,YAAY3B,OAAZ,CAAoBM,GAApB,CAAlB,CAAZ;AACAP,uBAAW,KAAKhB,cAAhB,GAAiCuB,GAAjC,IAA0Cf,KAA1C;AACD;AACF;AACF;;;;;;kBA1JkBZ,oB", "file": "zipkin_b3_text_map_codec.js", "sourcesContent": ["// @flow\n// Copyright (c) 2017 The Jaeger Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport * as constants from '../constants.js';\nimport Metrics from '../metrics/metrics.js';\nimport NoopMetricFactory from '../metrics/noop/metric_factory';\nimport SpanContext from '../span_context.js';\nimport Utils from '../util.js';\nimport { parseCommaSeparatedBaggage } from '../propagators/baggage';\n\nconst ZIPKIN_PARENTSPAN_HEADER = 'x-b3-parentspanid';\nconst ZIPKIN_SPAN_HEADER = 'x-b3-spanid';\nconst ZIPKIN_TRACE_HEADER = 'x-b3-traceid';\nconst ZIPKIN_SAMPLED_HEADER = 'x-b3-sampled';\nconst ZIPKIN_FLAGS_HEADER = 'x-b3-flags';\n\nexport default class ZipkinB3TextMapCodec {\n  _urlEncoding: boolean;\n  _baggagePrefix: string;\n  _metrics: any;\n\n  constructor(options: any = {}) {\n    this._urlEncoding = !!options.urlEncoding;\n    this._baggagePrefix = options.baggagePrefix || constants.TRACER_BAGGAGE_HEADER_PREFIX;\n    this._baggagePrefix = this._baggagePrefix.toLowerCase();\n    this._metrics = options.metrics || new Metrics(new NoopMetricFactory());\n  }\n\n  _encodeValue(value: string): string {\n    if (this._urlEncoding) {\n      return encodeURIComponent(value);\n    }\n\n    return value;\n  }\n\n  _decodeValue(value: string): string {\n    // only use url-decoding if there are meta-characters '%'\n    if (this._urlEncoding && value.indexOf('%') > -1) {\n      return this._decodeURIValue(value);\n    }\n\n    return value;\n  }\n\n  _isValidZipkinId(value: string): boolean {\n    // Validates a zipkin trace/spanID by attempting to parse it as a\n    // string of hex digits. This \"validation\" is not entirely rigorous,\n    // but equivalent to what is performed in the TextMapCodec.\n    //\n    // Note: due to the way parseInt works, this does not guarantee that\n    // the string is composed *entirely* of hex digits.\n    //\n    // > If parseInt encounters a character that is not a numeral in the\n    // > specified radix, it ignores it and all succeeding characters and\n    // > returns the integer value parsed up to that point.\n    //\n    // Note: The Number type in JS cannot represent the full range of 64bit\n    // unsigned ints, so using parseInt() on strings representing 64bit hex\n    // numbers only returns an approximation of the actual value.\n    // Fortunately, we do not depend on the returned value, we are only\n    // using it to validate that the string is a valid hex number (which is\n    // faster than doing it manually).  We cannot use\n    // Int64(numberValue).toBuffer() because it throws exceptions on bad\n    // strings.\n    if (!value) {\n      return true;\n    }\n\n    return !isNaN(parseInt(value, 16));\n  }\n\n  _decodeURIValue(value: string): string {\n    // unfortunately, decodeURIComponent() can throw 'URIError: URI malformed' on bad strings\n    try {\n      return decodeURIComponent(value);\n    } catch (e) {\n      return value;\n    }\n  }\n\n  extract(carrier: any): ?SpanContext {\n    let baggage = {};\n    let flags = 0;\n    let debugId = '';\n    let parentId = '';\n    let spanId = '';\n    let traceId = '';\n\n    for (let key in carrier) {\n      if (Object.prototype.hasOwnProperty.call(carrier, key)) {\n        let lowerKey = key.toLowerCase();\n\n        switch (lowerKey) {\n          case ZIPKIN_PARENTSPAN_HEADER:\n            parentId = this._decodeValue(carrier[key]);\n            break;\n          case ZIPKIN_SPAN_HEADER:\n            spanId = this._decodeValue(carrier[key]);\n            break;\n          case ZIPKIN_TRACE_HEADER:\n            traceId = this._decodeValue(carrier[key]);\n            break;\n          case ZIPKIN_SAMPLED_HEADER:\n            if (carrier[key] === '1' || carrier[key] === 'true') {\n              flags = flags | constants.SAMPLED_MASK;\n            }\n            break;\n          case ZIPKIN_FLAGS_HEADER:\n            // Per https://github.com/openzipkin/b3-propagation\n            //   \"Debug is encoded as X-B3-Flags: 1\"\n            // and\n            //   \"Debug implies Sampled.\"\n            if (carrier[key] === '1') {\n              flags = flags | constants.SAMPLED_MASK | constants.DEBUG_MASK;\n            }\n            break;\n          case constants.JAEGER_DEBUG_HEADER:\n            debugId = this._decodeValue(carrier[key]);\n            break;\n          case constants.JAEGER_BAGGAGE_HEADER:\n            parseCommaSeparatedBaggage(baggage, this._decodeValue(carrier[key]));\n            break;\n          default:\n            if (Utils.startsWith(lowerKey, this._baggagePrefix)) {\n              let keyWithoutPrefix = key.substring(this._baggagePrefix.length);\n              baggage[keyWithoutPrefix] = this._decodeValue(carrier[key]);\n            }\n        }\n      }\n    }\n\n    if (\n      !this._isValidZipkinId(traceId) ||\n      !this._isValidZipkinId(spanId) ||\n      !this._isValidZipkinId(parentId)\n    ) {\n      // Use a context devoid of trace/span/parentSpan IDs (to be\n      // consistent with the default codec behavior), and increment a\n      // metric\n      traceId = spanId = parentId = '';\n      this._metrics.decodingErrors.increment(1);\n    }\n\n    return SpanContext.withStringIds(traceId, spanId, parentId, flags, baggage, debugId);\n  }\n\n  inject(spanContext: SpanContext, carrier: any): void {\n    carrier[ZIPKIN_TRACE_HEADER] = spanContext.traceIdStr;\n    if (spanContext.parentIdStr) {\n      carrier[ZIPKIN_PARENTSPAN_HEADER] = spanContext.parentIdStr;\n    }\n    carrier[ZIPKIN_SPAN_HEADER] = spanContext.spanIdStr;\n\n    if (spanContext.isDebug()) {\n      carrier[ZIPKIN_FLAGS_HEADER] = '1';\n    } else {\n      // Only set the zipkin sampled header if we're NOT using debug.\n      // Per https://github.com/openzipkin/b3-propagation\n      //   \"Since Debug implies Sampled, so don't also send \"X-B3-Sampled: 1\"\n      carrier[ZIPKIN_SAMPLED_HEADER] = spanContext.isSampled() ? '1' : '0';\n    }\n\n    let baggage = spanContext.baggage;\n    for (let key in baggage) {\n      if (Object.prototype.hasOwnProperty.call(baggage, key)) {\n        let value = this._encodeValue(spanContext.baggage[key]);\n        carrier[`${this._baggagePrefix}${key}`] = value;\n      }\n    }\n  }\n}\n"]}