{"version": 3, "sources": ["../../../src/propagators/baggage.js"], "names": ["parseCommaSeparatedBaggage", "baggage", "values", "split", "for<PERSON>ach", "splitKeyVal", "keyVal", "trim", "length"], "mappings": ";;;;;QAwBgBA,0B,GAAAA,0B;;AAvBhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;AAWO,SAASA,0BAAT,CAAoCC,OAApC,EAAkDC,MAAlD,EAAwE;AAC7EA,SAAOC,KAAP,CAAa,GAAb,EAAkBC,OAAlB,CAA0B,kBAAU;AAClC,QAAIC,cAA6BC,OAAOC,IAAP,GAAcJ,KAAd,CAAoB,GAApB,CAAjC;AACA,QAAIE,YAAYG,MAAZ,IAAsB,CAA1B,EAA6B;AAC3BP,cAAQI,YAAY,CAAZ,CAAR,IAA0BA,YAAY,CAAZ,CAA1B;AACD;AACF,GALD;AAMD", "file": "baggage.js", "sourcesContent": ["// @flow\n// Copyright (c) 2017 The Jaeger Authors\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\n/**\n * Parses a comma separated key=value pair list and add the results to `baggage`.\n * E.g. \"key1=value1, key2=value2, key3=value3\"\n * is converted to map[string]string { \"key1\" : \"value1\",\n *                                     \"key2\" : \"value2\",\n *                                     \"key3\" : \"value3\" }\n *\n * @param {any} baggage- the object container to accept the parsed key=value pairs\n * @param {string} values - the string value containing any key=value pairs\n * to parse\n */\nexport function parseCommaSeparatedBaggage(baggage: any, values: string): void {\n  values.split(',').forEach(keyVal => {\n    let splitKeyVal: Array<string> = keyVal.trim().split('=');\n    if (splitKeyVal.length == 2) {\n      baggage[splitKeyVal[0]] = splitKeyVal[1];\n    }\n  });\n}\n"]}