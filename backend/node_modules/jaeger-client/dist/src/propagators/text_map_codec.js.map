{"version": 3, "sources": ["../../../src/propagators/text_map_codec.js"], "names": ["constants", "TextMapCodec", "options", "_urlEncoding", "urlEncoding", "_context<PERSON>ey", "<PERSON><PERSON>ey", "TRACER_STATE_HEADER_NAME", "toLowerCase", "_baggagePrefix", "baggagePrefix", "TRACER_BAGGAGE_HEADER_PREFIX", "_metrics", "metrics", "Metrics", "NoopMetricFactory", "value", "encodeURIComponent", "indexOf", "_decodeURIValue", "decodeURIComponent", "e", "carrier", "spanContext", "SpanContext", "baggage", "debugId", "key", "Object", "prototype", "hasOwnProperty", "call", "lowerKey", "decodedContext", "fromString", "_decodeValue", "decodingErrors", "increment", "JAEGER_DEBUG_HEADER", "JAEGER_BAGGAGE_HEADER", "Utils", "startsWith", "keyWithoutPrefix", "substring", "length", "stringSpanContext", "toString", "_encodeValue"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;IAAYA,S;;AACZ;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;;;;;IAEqBC,Y;AAMnB,0BAA+B;AAAA,QAAnBC,OAAmB,uEAAJ,EAAI;;AAAA;;AAC7B,SAAKC,YAAL,GAAoB,CAAC,CAACD,QAAQE,WAA9B;AACA,SAAKC,WAAL,GAAmBH,QAAQI,UAAR,IAAsBN,UAAUO,wBAAnD;AACA,SAAKF,WAAL,GAAmB,KAAKA,WAAL,CAAiBG,WAAjB,EAAnB;AACA,SAAKC,cAAL,GAAsBP,QAAQQ,aAAR,IAAyBV,UAAUW,4BAAzD;AACA,SAAKF,cAAL,GAAsB,KAAKA,cAAL,CAAoBD,WAApB,EAAtB;AACA,SAAKI,QAAL,GAAgBV,QAAQW,OAAR,IAAmB,IAAIC,iBAAJ,CAAY,IAAIC,wBAAJ,EAAZ,CAAnC;AACD;;;;iCAEYC,K,EAAuB;AAClC,UAAI,KAAKb,YAAT,EAAuB;AACrB,eAAOc,mBAAmBD,KAAnB,CAAP;AACD;;AAED,aAAOA,KAAP;AACD;;;iCAEYA,K,EAAuB;AAClC;AACA,UAAI,KAAKb,YAAL,IAAqBa,MAAME,OAAN,CAAc,GAAd,IAAqB,CAAC,CAA/C,EAAkD;AAChD,eAAO,KAAKC,eAAL,CAAqBH,KAArB,CAAP;AACD;;AAED,aAAOA,KAAP;AACD;;;oCAEeA,K,EAAuB;AACrC;AACA,UAAI;AACF,eAAOI,mBAAmBJ,KAAnB,CAAP;AACD,OAFD,CAEE,OAAOK,CAAP,EAAU;AACV,eAAOL,KAAP;AACD;AACF;;;4BAEOM,O,EAA4B;AAClC,UAAIC,cAAc,IAAIC,sBAAJ,EAAlB;AACA,UAAIC,UAAU,EAAd;AACA,UAAIC,UAAU,EAAd;;AAEA,WAAK,IAAIC,GAAT,IAAgBL,OAAhB,EAAyB;AACvB,YAAIM,OAAOC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCT,OAArC,EAA8CK,GAA9C,CAAJ,EAAwD;AACtD,cAAIK,WAAWL,IAAInB,WAAJ,EAAf;AACA,cAAIwB,aAAa,KAAK3B,WAAtB,EAAmC;AACjC,gBAAI4B,iBAAiBT,uBAAYU,UAAZ,CAAuB,KAAKC,YAAL,CAAkBb,QAAQK,GAAR,CAAlB,CAAvB,CAArB;AACA,gBAAIM,mBAAmB,IAAvB,EAA6B;AAC3B,mBAAKrB,QAAL,CAAcwB,cAAd,CAA6BC,SAA7B,CAAuC,CAAvC;AACD,aAFD,MAEO;AACLd,4BAAcU,cAAd;AACD;AACF,WAPD,MAOO,IAAID,aAAahC,UAAUsC,mBAA3B,EAAgD;AACrDZ,sBAAU,KAAKS,YAAL,CAAkBb,QAAQK,GAAR,CAAlB,CAAV;AACD,WAFM,MAEA,IAAIK,aAAahC,UAAUuC,qBAA3B,EAAkD;AACvD,qDAA2Bd,OAA3B,EAAoC,KAAKU,YAAL,CAAkBb,QAAQK,GAAR,CAAlB,CAApC;AACD,WAFM,MAEA,IAAIa,eAAMC,UAAN,CAAiBT,QAAjB,EAA2B,KAAKvB,cAAhC,CAAJ,EAAqD;AAC1D,gBAAIiC,mBAAmBf,IAAIgB,SAAJ,CAAc,KAAKlC,cAAL,CAAoBmC,MAAlC,CAAvB;AACAnB,oBAAQiB,gBAAR,IAA4B,KAAKP,YAAL,CAAkBb,QAAQK,GAAR,CAAlB,CAA5B;AACD;AACF;AACF;;AAEDJ,kBAAYG,OAAZ,GAAsBA,OAAtB;AACAH,kBAAYE,OAAZ,GAAsBA,OAAtB;AACA,aAAOF,WAAP;AACD;;;2BAEMA,W,EAA0BD,O,EAAoB;AACnD,UAAIuB,oBAAoBtB,YAAYuB,QAAZ,EAAxB;AACAxB,cAAQ,KAAKjB,WAAb,IAA4BwC,iBAA5B,CAFmD,CAEJ;;AAE/C,UAAIpB,UAAUF,YAAYE,OAA1B;AACA,WAAK,IAAIE,GAAT,IAAgBF,OAAhB,EAAyB;AACvB,YAAIG,OAAOC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCN,OAArC,EAA8CE,GAA9C,CAAJ,EAAwD;AACtD,cAAIX,QAAQ,KAAK+B,YAAL,CAAkBxB,YAAYE,OAAZ,CAAoBE,GAApB,CAAlB,CAAZ;AACAL,uBAAW,KAAKb,cAAhB,GAAiCkB,GAAjC,IAA0CX,KAA1C;AACD;AACF;AACF;;;;;;kBAnFkBf,Y", "file": "text_map_codec.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport * as constants from '../constants';\nimport Metrics from '../metrics/metrics';\nimport NoopMetricFactory from '../metrics/noop/metric_factory';\nimport SpanContext from '../span_context';\nimport Utils from '../util';\nimport { parseCommaSeparatedBaggage } from '../propagators/baggage';\n\nexport default class TextMapCodec {\n  _urlEncoding: boolean;\n  _contextKey: string;\n  _baggagePrefix: string;\n  _metrics: any;\n\n  constructor(options: any = {}) {\n    this._urlEncoding = !!options.urlEncoding;\n    this._contextKey = options.contextKey || constants.TRACER_STATE_HEADER_NAME;\n    this._contextKey = this._contextKey.toLowerCase();\n    this._baggagePrefix = options.baggagePrefix || constants.TRACER_BAGGAGE_HEADER_PREFIX;\n    this._baggagePrefix = this._baggagePrefix.toLowerCase();\n    this._metrics = options.metrics || new Metrics(new NoopMetricFactory());\n  }\n\n  _encodeValue(value: string): string {\n    if (this._urlEncoding) {\n      return encodeURIComponent(value);\n    }\n\n    return value;\n  }\n\n  _decodeValue(value: string): string {\n    // only use url-decoding if there are meta-characters '%'\n    if (this._urlEncoding && value.indexOf('%') > -1) {\n      return this._decodeURIValue(value);\n    }\n\n    return value;\n  }\n\n  _decodeURIValue(value: string): string {\n    // unfortunately, decodeURIComponent() can throw 'URIError: URI malformed' on bad strings\n    try {\n      return decodeURIComponent(value);\n    } catch (e) {\n      return value;\n    }\n  }\n\n  extract(carrier: any): ?SpanContext {\n    let spanContext = new SpanContext();\n    let baggage = {};\n    let debugId = '';\n\n    for (let key in carrier) {\n      if (Object.prototype.hasOwnProperty.call(carrier, key)) {\n        let lowerKey = key.toLowerCase();\n        if (lowerKey === this._contextKey) {\n          let decodedContext = SpanContext.fromString(this._decodeValue(carrier[key]));\n          if (decodedContext === null) {\n            this._metrics.decodingErrors.increment(1);\n          } else {\n            spanContext = decodedContext;\n          }\n        } else if (lowerKey === constants.JAEGER_DEBUG_HEADER) {\n          debugId = this._decodeValue(carrier[key]);\n        } else if (lowerKey === constants.JAEGER_BAGGAGE_HEADER) {\n          parseCommaSeparatedBaggage(baggage, this._decodeValue(carrier[key]));\n        } else if (Utils.startsWith(lowerKey, this._baggagePrefix)) {\n          let keyWithoutPrefix = key.substring(this._baggagePrefix.length);\n          baggage[keyWithoutPrefix] = this._decodeValue(carrier[key]);\n        }\n      }\n    }\n\n    spanContext.debugId = debugId;\n    spanContext.baggage = baggage;\n    return spanContext;\n  }\n\n  inject(spanContext: SpanContext, carrier: any): void {\n    let stringSpanContext = spanContext.toString();\n    carrier[this._contextKey] = stringSpanContext; // no need to encode this\n\n    let baggage = spanContext.baggage;\n    for (let key in baggage) {\n      if (Object.prototype.hasOwnProperty.call(baggage, key)) {\n        let value = this._encodeValue(spanContext.baggage[key]);\n        carrier[`${this._baggagePrefix}${key}`] = value;\n      }\n    }\n  }\n}\n"]}