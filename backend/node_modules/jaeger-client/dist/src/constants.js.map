{"version": 3, "sources": ["../../src/constants.js"], "names": ["SAMPLED_MASK", "DEBUG_MASK", "FIREHOSE_MASK", "JAEGER_CLIENT_VERSION_TAG_KEY", "TRACER_HOSTNAME_TAG_KEY", "TRACER_CLIENT_ID_TAG_KEY", "PROCESS_IP", "SAMPLER_TYPE_TAG_KEY", "SAMPLER_PARAM_TAG_KEY", "SAMPLER_TYPE_CONST", "SAMPLER_TYPE_PROBABILISTIC", "SAMPLER_TYPE_RATE_LIMITING", "SAMPLER_TYPE_LOWER_BOUND", "SAMPLER_TYPE_REMOTE", "JAEGER_DEBUG_HEADER", "JAEGER_BAGGAGE_HEADER", "TRACER_BAGGAGE_HEADER_PREFIX", "TRACER_STATE_HEADER_NAME"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMA,sCAAe,GAArB;;AAEP;AACO,IAAMC,kCAAa,GAAnB;;AAEP;;AAEA;AACO,IAAMC,wCAAgB,GAAtB;;AAEP;AACO,IAAMC,wEAAgC,gBAAtC;;AAEP;AACO,IAAMC,4DAA0B,UAAhC;;AAEP;AACO,IAAMC,8DAA2B,aAAjC;;AAEP;AACO,IAAMC,kCAAa,IAAnB;;AAEP;AACO,IAAMC,sDAAuB,cAA7B;;AAEP;AACO,IAAMC,wDAAwB,eAA9B;;AAEP;AACO,IAAMC,kDAAqB,OAA3B;;AAEP;AACA;AACO,IAAMC,kEAA6B,eAAnC;;AAEP;AACA;AACO,IAAMC,kEAA6B,cAAnC;;AAEP;AACA;AACO,IAAMC,8DAA2B,YAAjC;;AAEP;AACO,IAAMC,oDAAsB,QAA5B;;AAEP;AACA;AACA;AACA;AACO,IAAMC,oDAAsB,iBAA5B;;AAEP;AACA;AACA;AACO,IAAMC,wDAAwB,gBAA9B;;AAEP;AACO,IAAMC,sEAA+B,UAArC;;AAEP;AACO,IAAMC,8DAA2B,eAAjC", "file": "constants.js", "sourcesContent": ["// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\n// SAMPLED_MASK is the bit mask indicating that a span has been sampled.\nexport const SAMPLED_MASK = 0x1;\n\n// DEBUG_MASK is the bit mask indicating that a span has been marked for debug.\nexport const DEBUG_MASK = 0x2;\n\n// MASK = 0x4 is reserved for deferred sampling in the future.\n\n// FIREHOSE_MASK is the bit mask indicating a span is a firehose span.\nexport const FIREHOSE_MASK = 0x8;\n\n// JAEGER_CLIENT_VERSION_TAG_KEY is the name of the tag used to report client version.\nexport const JAEGER_CLIENT_VERSION_TAG_KEY = 'jaeger.version';\n\n// TRACER_HOSTNAME_TAG_KEY is used to report host name of the process.\nexport const TRACER_HOSTNAME_TAG_KEY = 'hostname';\n\n// TRACER_CLIENT_ID_TAG_KEY is used to report client ID of the process.\nexport const TRACER_CLIENT_ID_TAG_KEY = 'client-uuid';\n\n// PROCESS_IP used to report ip of the process.\nexport const PROCESS_IP = 'ip';\n\n// SAMPLER_TYPE_TAG_KEY reports which sampler was used on the root span.\nexport const SAMPLER_TYPE_TAG_KEY = 'sampler.type';\n\n// SAMPLER_PARAM_TAG_KEY reports which sampler was used on the root span.\nexport const SAMPLER_PARAM_TAG_KEY = 'sampler.param';\n\n// SAMPLER_TYPE_CONST is the type of the sampler that always makes the same decision.\nexport const SAMPLER_TYPE_CONST = 'const';\n\n// SAMPLER_TYPE_PROBABILISTIC is the type of sampler that samples traces\n// with a certain fixed probability.\nexport const SAMPLER_TYPE_PROBABILISTIC = 'probabilistic';\n\n// SAMPLER_TYPE_RATE_LIMITING is the type of sampler that samples\n// only up to a fixed number of traces per second.\nexport const SAMPLER_TYPE_RATE_LIMITING = 'ratelimiting';\n\n// SAMPLER_TYPE_LOWER_BOUND is the type of sampler that samples\n// only up to a fixed number of traces per second.\nexport const SAMPLER_TYPE_LOWER_BOUND = 'lowerbound';\n\n// SAMPLER_TYPE_REMOTE is the type of sampler that polls Jaeger agent for sampling strategy.\nexport const SAMPLER_TYPE_REMOTE = 'remote';\n\n// JAEGER_DEBUG_HEADER is the name of an HTTP header or a TextMap carrier key which,\n// if found in the carrier, forces the trace to be sampled as \"debug\" trace.\n// The value of the header is recorded as the tag on the root span, so that the\n// trace can be found in the UI using this value as a correlation ID.\nexport const JAEGER_DEBUG_HEADER = 'jaeger-debug-id';\n\n// JaegerBaggageHeader is the name of the HTTP header that is used to submit baggage.\n// It differs from TraceBaggageHeaderPrefix in that it can be used only in cases where\n// a root span does not exist.\nexport const JAEGER_BAGGAGE_HEADER = 'jaeger-baggage';\n\n// TRACER_BAGGAGE_HEADER_PREFIX is the default prefix used for saving baggage to a carrier.\nexport const TRACER_BAGGAGE_HEADER_PREFIX = 'uberctx-';\n\n// TRACER_STATE_HEADER_NAME is the header key used for a span's serialized context.\nexport const TRACER_STATE_HEADER_NAME = 'uber-trace-id';\n"]}