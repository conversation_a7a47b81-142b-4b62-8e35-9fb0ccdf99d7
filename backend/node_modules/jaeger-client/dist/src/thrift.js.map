{"version": 3, "sources": ["../../src/thrift.js"], "names": ["opentracing", "ThriftUtils", "fs", "readFileSync", "path", "join", "__dirname", "initialTags", "thriftTags", "i", "length", "tag", "key", "vLong", "emptyBuffer", "vBinary", "vBool", "vDouble", "vStr", "vType", "valueType", "value", "_thrift", "TagType", "DOUBLE", "BOOL", "<PERSON><PERSON><PERSON>", "BINARY", "STRING", "JSON", "stringify", "String", "push", "logs", "thriftLogs", "log", "timestamp", "Utils", "encodeInt64", "fields", "getThriftTags", "refs", "thriftRefs", "refEnum", "ref", "context", "referencedContext", "type", "REFERENCE_CHILD_OF", "SpanRefType", "CHILD_OF", "REFERENCE_FOLLOWS_FROM", "FOLLOWS_FROM", "refType", "traceIdLow", "getTraceIdLow", "traceId", "traceIdHigh", "getTraceIdHigh", "spanId", "slice", "span", "tags", "_tags", "getThriftLogs", "_logs", "unsigned", "_spanContext", "parentSpanId", "parentId", "operationName", "_operationName", "references", "spanRefsToThriftRefs", "_references", "flags", "startTime", "_startTime", "duration", "_duration", "Thrift", "source", "loadJaegerThriftDefinition", "allowOptionalArguments", "new<PERSON>uffer"], "mappings": ";;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;IAAYA,W;;AACZ;;;;AACA;;AACA;;;;;;;;;;IAEqBC,W;;;;;;;iDAOyB;AAC1C,aAAOC,aAAGC,YAAH,CAAgBC,eAAKC,IAAL,CAAUC,SAAV,EAAqB,mCAArB,CAAhB,EAA2E,OAA3E,CAAP;AACD;;;2CAEqC;AACpC,aAAOF,eAAKC,IAAL,CAAUC,SAAV,EAAqB,6BAArB,CAAP;AACD;;;kCAEoBC,W,EAAqC;AACxD,UAAIC,aAAa,EAAjB;AACA,WAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIF,YAAYG,MAAhC,EAAwCD,GAAxC,EAA6C;AAC3C,YAAIE,MAAMJ,YAAYE,CAAZ,CAAV;;AAEA,YAAIG,MAAcD,IAAIC,GAAtB;;AAEA,YAAIC,QAAgBZ,YAAYa,WAAhC;AACA,YAAIC,UAAkBd,YAAYa,WAAlC;AACA,YAAIE,QAAiB,KAArB;AACA,YAAIC,UAAkB,CAAtB;AACA,YAAIC,OAAe,EAAnB;;AAEA,YAAIC,QAAgB,EAApB;AACA,YAAIC,oBAAmBT,IAAIU,KAAvB,CAAJ;AACA,YAAID,cAAc,QAAlB,EAA4B;AAC1BD,kBAAQlB,YAAYqB,OAAZ,CAAoBC,OAApB,CAA4BC,MAApC;AACAP,oBAAUN,IAAIU,KAAd;AACD,SAHD,MAGO,IAAID,cAAc,SAAlB,EAA6B;AAClCD,kBAAQlB,YAAYqB,OAAZ,CAAoBC,OAApB,CAA4BE,IAApC;AACAT,kBAAQL,IAAIU,KAAZ;AACD,SAHM,MAGA,IAAIV,IAAIU,KAAJ,YAAqBK,MAAzB,EAAiC;AACtCP,kBAAQlB,YAAYqB,OAAZ,CAAoBC,OAApB,CAA4BI,MAApC;AACAZ,oBAAUJ,IAAIU,KAAd;AACD,SAHM,MAGA,IAAID,cAAc,QAAlB,EAA4B;AACjCD,kBAAQlB,YAAYqB,OAAZ,CAAoBC,OAApB,CAA4BK,MAApC;AACAV,iBAAOW,KAAKC,SAAL,CAAenB,IAAIU,KAAnB,CAAP;AACD,SAHM,MAGA;AACLF,kBAAQlB,YAAYqB,OAAZ,CAAoBC,OAApB,CAA4BK,MAApC;AACA,cAAIR,cAAc,QAAlB,EAA4B;AAC1BF,mBAAOP,IAAIU,KAAX;AACD,WAFD,MAEO;AACLH,mBAAOa,OAAOpB,IAAIU,KAAX,CAAP;AACD;AACF;;AAEDb,mBAAWwB,IAAX,CAAgB;AACdpB,eAAKA,GADS;AAEdO,iBAAOA,KAFO;AAGdD,gBAAMA,IAHQ;AAIdD,mBAASA,OAJK;AAKdD,iBAAOA,KALO;AAMdH,iBAAOA,KANO;AAOdE,mBAASA;AAPK,SAAhB;AASD;;AAED,aAAOP,UAAP;AACD;;;kCAEoByB,I,EAAkC;AACrD,UAAIC,aAAa,EAAjB;AACA,WAAK,IAAIzB,IAAI,CAAb,EAAgBA,IAAIwB,KAAKvB,MAAzB,EAAiCD,GAAjC,EAAsC;AACpC,YAAI0B,MAAMF,KAAKxB,CAAL,CAAV;AACAyB,mBAAWF,IAAX,CAAgB;AACdI,qBAAWC,eAAMC,WAAN,CAAkBH,IAAIC,SAAJ,GAAgB,IAAlC,CADG,EACsC;AACpDG,kBAAQtC,YAAYuC,aAAZ,CAA0BL,IAAII,MAA9B;AAFM,SAAhB;AAID;;AAED,aAAOL,UAAP;AACD;;;yCAE2BO,I,EAAoC;AAC9D,UAAIC,aAAa,EAAjB;AACA,WAAK,IAAIjC,IAAI,CAAb,EAAgBA,IAAIgC,KAAK/B,MAAzB,EAAiCD,GAAjC,EAAsC;AACpC,YAAIkC,gBAAJ;AACA,YAAIC,MAAMH,KAAKhC,CAAL,CAAV;AACA,YAAIoC,UAAUJ,KAAKhC,CAAL,EAAQqC,iBAAR,EAAd;;AAEA,YAAIF,IAAIG,IAAJ,OAAe/C,YAAYgD,kBAA/B,EAAmD;AACjDL,oBAAU1C,YAAYqB,OAAZ,CAAoB2B,WAApB,CAAgCC,QAA1C;AACD,SAFD,MAEO,IAAIN,IAAIG,IAAJ,OAAe/C,YAAYmD,sBAA/B,EAAuD;AAC5DR,oBAAU1C,YAAYqB,OAAZ,CAAoB2B,WAApB,CAAgCG,YAA1C;AACD,SAFM,MAEA;AACL;AACD;;AAEDV,mBAAWV,IAAX,CAAgB;AACdqB,mBAASV,OADK;AAEdW,sBAAYrD,YAAYsD,aAAZ,CAA0BV,QAAQW,OAAlC,CAFE;AAGdC,uBAAaxD,YAAYyD,cAAZ,CAA2Bb,QAAQW,OAAnC,CAHC;AAIdG,kBAAQd,QAAQc;AAJF,SAAhB;AAMD;;AAED,aAAOjB,UAAP;AACD;;;kCAEoBc,O,EAAiB;AACpC,UAAIA,WAAW,IAAf,EAAqB;AACnB,eAAOA,QAAQI,KAAR,CAAc,CAAC,CAAf,CAAP;AACD;;AAED,aAAO3D,YAAYa,WAAnB;AACD;;;mCAEqB0C,O,EAAiB;AACrC,UAAIA,WAAW,IAAX,IAAmBA,QAAQ9C,MAAR,GAAiB,CAAxC,EAA2C;AACzC,eAAO8C,QAAQI,KAAR,CAAc,CAAC,EAAf,EAAmB,CAAC,CAApB,CAAP;AACD;;AAED,aAAO3D,YAAYa,WAAnB;AACD;;;iCAEmB+C,I,EAAiB;AACnC,UAAIC,OAAO7D,YAAYuC,aAAZ,CAA0BqB,KAAKE,KAA/B,CAAX;AACA,UAAI9B,OAAOhC,YAAY+D,aAAZ,CAA0BH,KAAKI,KAA/B,CAAX;AACA,UAAIC,WAAW,IAAf;;AAEA,aAAO;AACLZ,oBAAYrD,YAAYsD,aAAZ,CAA0BM,KAAKM,YAAL,CAAkBX,OAA5C,CADP;AAELC,qBAAaxD,YAAYyD,cAAZ,CAA2BG,KAAKM,YAAL,CAAkBX,OAA7C,CAFR;AAGLG,gBAAQE,KAAKM,YAAL,CAAkBR,MAHrB;AAILS,sBAAcP,KAAKM,YAAL,CAAkBE,QAAlB,IAA8BpE,YAAYa,WAJnD;AAKLwD,uBAAeT,KAAKU,cALf;AAMLC,oBAAYvE,YAAYwE,oBAAZ,CAAiCZ,KAAKa,WAAtC,CANP;AAOLC,eAAOd,KAAKM,YAAL,CAAkBQ,KAPpB;AAQLC,mBAAWvC,eAAMC,WAAN,CAAkBuB,KAAKgB,UAAL,GAAkB,IAApC,CARN,EAQiD;AACtDC,kBAAUzC,eAAMC,WAAN,CAAkBuB,KAAKkB,SAAL,GAAiB,IAAnC,CATL,EAS+C;AACpDjB,cAAMA,IAVD;AAWL7B,cAAMA;AAXD,OAAP;AAaD;;;;;;AA1IkBhC,W,CACZqB,O,GAAU,IAAI0D,gBAAJ,CAAW;AAC1BC,UAAQhF,YAAYiF,0BAAZ,EADkB;AAE1BC,0BAAwB;AAFE,CAAX,C;AADElF,W,CAKZa,W,GAAsBuB,eAAM+C,SAAN,CAAgB,CAAhB,C;kBALVnF,W", "file": "thrift.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport fs from 'fs';\nimport * as opentracing from 'opentracing';\nimport path from 'path';\nimport { Thrift } from 'thriftrw';\nimport Utils from './util.js';\n\nexport default class ThriftUtils {\n  static _thrift = new Thrift({\n    source: ThriftUtils.loadJaegerThriftDefinition(),\n    allowOptionalArguments: true,\n  });\n  static emptyBuffer: Buffer = Utils.newBuffer(8);\n\n  static loadJaegerThriftDefinition(): string {\n    return fs.readFileSync(path.join(__dirname, './jaeger-idl/thrift/jaeger.thrift'), 'ascii');\n  }\n\n  static buildAgentThriftPath(): string {\n    return path.join(__dirname, './thriftrw-idl/agent.thrift');\n  }\n\n  static getThriftTags(initialTags: Array<Tag>): Array<any> {\n    let thriftTags = [];\n    for (let i = 0; i < initialTags.length; i++) {\n      let tag = initialTags[i];\n\n      let key: string = tag.key;\n\n      let vLong: Buffer = ThriftUtils.emptyBuffer;\n      let vBinary: Buffer = ThriftUtils.emptyBuffer;\n      let vBool: boolean = false;\n      let vDouble: number = 0;\n      let vStr: string = '';\n\n      let vType: string = '';\n      let valueType = typeof tag.value;\n      if (valueType === 'number') {\n        vType = ThriftUtils._thrift.TagType.DOUBLE;\n        vDouble = tag.value;\n      } else if (valueType === 'boolean') {\n        vType = ThriftUtils._thrift.TagType.BOOL;\n        vBool = tag.value;\n      } else if (tag.value instanceof Buffer) {\n        vType = ThriftUtils._thrift.TagType.BINARY;\n        vBinary = tag.value;\n      } else if (valueType === 'object') {\n        vType = ThriftUtils._thrift.TagType.STRING;\n        vStr = JSON.stringify(tag.value);\n      } else {\n        vType = ThriftUtils._thrift.TagType.STRING;\n        if (valueType === 'string') {\n          vStr = tag.value;\n        } else {\n          vStr = String(tag.value);\n        }\n      }\n\n      thriftTags.push({\n        key: key,\n        vType: vType,\n        vStr: vStr,\n        vDouble: vDouble,\n        vBool: vBool,\n        vLong: vLong,\n        vBinary: vBinary,\n      });\n    }\n\n    return thriftTags;\n  }\n\n  static getThriftLogs(logs: Array<LogData>): Array<any> {\n    let thriftLogs = [];\n    for (let i = 0; i < logs.length; i++) {\n      let log = logs[i];\n      thriftLogs.push({\n        timestamp: Utils.encodeInt64(log.timestamp * 1000), // to microseconds\n        fields: ThriftUtils.getThriftTags(log.fields),\n      });\n    }\n\n    return thriftLogs;\n  }\n\n  static spanRefsToThriftRefs(refs: Array<Reference>): Array<any> {\n    let thriftRefs = [];\n    for (let i = 0; i < refs.length; i++) {\n      let refEnum;\n      let ref = refs[i];\n      let context = refs[i].referencedContext();\n\n      if (ref.type() === opentracing.REFERENCE_CHILD_OF) {\n        refEnum = ThriftUtils._thrift.SpanRefType.CHILD_OF;\n      } else if (ref.type() === opentracing.REFERENCE_FOLLOWS_FROM) {\n        refEnum = ThriftUtils._thrift.SpanRefType.FOLLOWS_FROM;\n      } else {\n        continue;\n      }\n\n      thriftRefs.push({\n        refType: refEnum,\n        traceIdLow: ThriftUtils.getTraceIdLow(context.traceId),\n        traceIdHigh: ThriftUtils.getTraceIdHigh(context.traceId),\n        spanId: context.spanId,\n      });\n    }\n\n    return thriftRefs;\n  }\n\n  static getTraceIdLow(traceId: Buffer) {\n    if (traceId != null) {\n      return traceId.slice(-8);\n    }\n\n    return ThriftUtils.emptyBuffer;\n  }\n\n  static getTraceIdHigh(traceId: Buffer) {\n    if (traceId != null && traceId.length > 8) {\n      return traceId.slice(-16, -8);\n    }\n\n    return ThriftUtils.emptyBuffer;\n  }\n\n  static spanToThrift(span: Span): any {\n    let tags = ThriftUtils.getThriftTags(span._tags);\n    let logs = ThriftUtils.getThriftLogs(span._logs);\n    let unsigned = true;\n\n    return {\n      traceIdLow: ThriftUtils.getTraceIdLow(span._spanContext.traceId),\n      traceIdHigh: ThriftUtils.getTraceIdHigh(span._spanContext.traceId),\n      spanId: span._spanContext.spanId,\n      parentSpanId: span._spanContext.parentId || ThriftUtils.emptyBuffer,\n      operationName: span._operationName,\n      references: ThriftUtils.spanRefsToThriftRefs(span._references),\n      flags: span._spanContext.flags,\n      startTime: Utils.encodeInt64(span._startTime * 1000), // to microseconds\n      duration: Utils.encodeInt64(span._duration * 1000), // to microseconds\n      tags: tags,\n      logs: logs,\n    };\n  }\n}\n"]}