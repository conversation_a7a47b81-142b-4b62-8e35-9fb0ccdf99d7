{"version": 3, "sources": ["../../src/span_context.js"], "names": ["constants", "SpanContext", "traceId", "spanId", "parentId", "traceIdStr", "spanIdStr", "parentIdStr", "baggage", "debugId", "samplingState", "_traceId", "_spanId", "_parentId", "_traceIdStr", "_spanIdStr", "_parentIdStr", "_baggage", "_debugId", "_samplingState", "SamplingState", "_remote", "value", "setSampled", "setDebug", "setFirehose", "setFinal", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "isSampled", "isDebug", "isFirehose", "key", "newBaggage", "Utils", "clone", "childId", "idIsStr", "_childId", "_childIdStr", "String", "flags", "toString", "traceIdExactLength", "length", "newBufferFromHex", "padding", "safeTraceIdStr", "slice", "encodeInt64", "removeLeadingZeros", "setFlags", "isFinal", "serializedString", "headers", "split", "approxTraceId", "parseInt", "NaNDetected", "isNaN", "withStringIds", "ctx"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;IAAYA,S;;AACZ;;;;AACA;;;;AACA;;;;;;;;;;IAEqBC,W;AAUD;;AAElB,uBACEC,OADF,EAEEC,MAFF,EAGEC,QAHF,EAIEC,UAJF,EAKEC,SALF,EAMEC,WANF,EAUE;AAAA,QAHAC,OAGA,uEAHe,EAGf;AAAA,QAFAC,OAEA,uEAFmB,EAEnB;AAAA,QADAC,aACA;;AAAA;;AACA,SAAKC,QAAL,GAAgBT,OAAhB;AACA,SAAKU,OAAL,GAAeT,MAAf;AACA,SAAKU,SAAL,GAAiBT,YAAY,IAA7B;AACA,SAAKU,WAAL,GAAmBT,UAAnB;AACA,SAAKU,UAAL,GAAkBT,SAAlB;AACA,SAAKU,YAAL,GAAoBT,WAApB;AACA,SAAKU,QAAL,GAAgBT,OAAhB;AACA,SAAKU,QAAL,GAAgBT,OAAhB;AACA,SAAKU,cAAL,GAAsBT,iBAAiB,IAAIU,wBAAJ,CAAkB,KAAKd,SAAvB,CAAvC;AACA,SAAKe,OAAL,GAAe,KAAf;AACD;;;;gCA2FWC,K,EAAgB;AAC1B,WAAKH,cAAL,CAAoBI,UAApB,CAA+BD,KAA/B;AACD;;;8BAESA,K,EAAgB;AACxB,WAAKH,cAAL,CAAoBK,QAApB,CAA6BF,KAA7B;AACD;;;iCAEYA,K,EAAgB;AAC3B,WAAKH,cAAL,CAAoBM,WAApB,CAAgCH,KAAhC;AACD;;;+BAEUA,K,EAAgB;AACzB,WAAKD,OAAL,GAAeC,KAAf;AACD;;;uCAckB;AACjB,aAAO,KAAKH,cAAL,CAAoBO,QAApB,CAA6B,IAA7B,CAAP;AACD;;;+BAEmB;AAClB,aAAO,KAAKL,OAAZ;AACD;;;6CAEiC;AAChC,aAAO,CAAC,KAAKM,OAAN,IAAiBC,QAAQ,KAAKV,QAAb,CAAxB;AACD;;AAED;;;;;;gCAGqB;AACnB,aAAO,KAAKC,cAAL,CAAoBU,SAApB,EAAP;AACD;;AAED;;;;;;8BAGmB;AACjB,aAAO,KAAKV,cAAL,CAAoBW,OAApB,EAAP;AACD;;;iCAEqB;AACpB,aAAO,KAAKX,cAAL,CAAoBY,UAApB,EAAP;AACD;;;oCAEeC,G,EAAaV,K,EAA4B;AACvD,UAAIW,aAAaC,eAAMC,KAAN,CAAY,KAAKlB,QAAjB,CAAjB;AACAgB,iBAAWD,GAAX,IAAkBV,KAAlB;AACA,aAAO,IAAIrB,WAAJ,CACL,KAAKU,QADA,EAEL,KAAKC,OAFA,EAGL,KAAKC,SAHA,EAIL,KAAKC,WAJA,EAKL,KAAKC,UALA,EAML,KAAKC,YANA,EAOLiB,UAPK,EAQL,KAAKf,QARA,EASL,KAAKC,cATA,CAAP;AAWD;;;sCAEiBiB,O,EAAc;AAC9B,UAAMC,UAAU,OAAOD,OAAP,KAAmB,QAAnC;AACA,UAAME,WAAWD,UAAU,IAAV,GAAiBD,OAAlC;AACA,UAAMG,cAAcF,UAAUD,OAAV,GAAoB,IAAxC;AACA,aAAO,IAAInC,WAAJ,CACL,KAAKU,QADA,EACU;AACf2B,cAFK,EAEK;AACV,WAAK1B,OAHA,EAGS;AACd,WAAKE,WAJA,EAIa;AAClByB,iBALK,EAKQ;AACb,WAAKxB,UANA,EAMY;AACjB,WAAKE,QAPA,EAOU;AACf,WAAKC,QARA,EAQU;AACf,WAAKC,cATA,CASe;AATf,OAAP;AAWD;;AAED;;;;;;gCAGoB;AAClB,aAAO,KAAKd,UAAL,IAAmB,EAA1B;AACD;;AAED;;;;;;+BAGmB;AACjB,aAAO,KAAKC,SAAL,IAAkB,EAAzB;AACD;;AAED;;;;;;+BAGmB;AACjB,aAAUkC,OAAO,KAAKnC,UAAZ,CAAV,SAAqCmC,OAAO,KAAKlC,SAAZ,CAArC,SAA+DkC,OAC7D,KAAKjC,WAAL,IAAoB,CADyC,CAA/D,SAEK,KAAKY,cAAL,CAAoBsB,KAApB,GAA4BC,QAA5B,CAAqC,EAArC,CAFL;AAGD;;AAED;;;;;;;wBA3MmB;AACjB,UAAI,KAAK/B,QAAL,IAAiB,IAAjB,IAAyB,KAAKG,WAAL,IAAoB,IAAjD,EAAuD;AACrD;AACA;AACA;AACA;AACA;AACA,YAAM6B,qBAAqB,KAAK7B,WAAL,CAAiB8B,MAAjB,GAA0B,EAA1B,GAA+B,EAA/B,GAAoC,EAA/D;AACA,YAAI,KAAK9B,WAAL,CAAiB8B,MAAjB,KAA4BD,kBAAhC,EAAoD;AAClD,eAAKhC,QAAL,GAAgBuB,eAAMW,gBAAN,CAAuB,KAAK/B,WAA5B,CAAhB;AACD,SAFD,MAEO;AACL,cAAMgC,UAAUH,uBAAuB,EAAvB,GAA4B,kBAA5B,GAAiD,kCAAjE;AACA,cAAMI,iBAAiB,CAACD,UAAU,KAAKhC,WAAhB,EAA6BkC,KAA7B,CAAmC,CAACL,kBAApC,CAAvB;AACA,eAAKhC,QAAL,GAAgBuB,eAAMW,gBAAN,CAAuBE,cAAvB,CAAhB;AACD;AACF;AACD,aAAO,KAAKpC,QAAZ;AACD,K;sBAqDWT,O,EAAuB;AACjC,WAAKS,QAAL,GAAgBT,OAAhB;AACA,WAAKY,WAAL,GAAmB,IAAnB;AACD;;;wBAtDiB;AAChB,UAAI,KAAKF,OAAL,IAAgB,IAAhB,IAAwB,KAAKG,UAAL,IAAmB,IAA/C,EAAqD;AACnD,aAAKH,OAAL,GAAesB,eAAMe,WAAN,CAAkB,KAAKlC,UAAvB,CAAf;AACD;AACD,aAAO,KAAKH,OAAZ;AACD,K;sBAmDUT,M,EAAsB;AAC/B,WAAKS,OAAL,GAAeT,MAAf;AACA,WAAKY,UAAL,GAAkB,IAAlB;AACD;;;wBApDmB;AAClB,UAAI,KAAKF,SAAL,IAAkB,IAAlB,IAA0B,KAAKG,YAAL,IAAqB,IAAnD,EAAyD;AACvD,aAAKH,SAAL,GAAiBqB,eAAMe,WAAN,CAAkB,KAAKjC,YAAvB,CAAjB;AACD;AACD,aAAO,KAAKH,SAAZ;AACD,K;sBAiDYT,Q,EAA+B;AAC1C,WAAKS,SAAL,GAAiBT,QAAjB;AACA,WAAKY,YAAL,GAAoB,IAApB;AACD;;;wBAlDyB;AACxB,UAAI,KAAKF,WAAL,IAAoB,IAApB,IAA4B,KAAKH,QAAL,IAAiB,IAAjD,EAAuD;AACrD,aAAKG,WAAL,GAAmBoB,eAAMgB,kBAAN,CAAyB,KAAKvC,QAAL,CAAc+B,QAAd,CAAuB,KAAvB,CAAzB,CAAnB;AACD;AACD,aAAO,KAAK5B,WAAZ;AACD;;;wBAEwB;AACvB,UAAI,KAAKC,UAAL,IAAmB,IAAnB,IAA2B,KAAKH,OAAL,IAAgB,IAA/C,EAAqD;AACnD,aAAKG,UAAL,GAAkBmB,eAAMgB,kBAAN,CAAyB,KAAKtC,OAAL,CAAa8B,QAAb,CAAsB,KAAtB,CAAzB,CAAlB;AACD;AACD,aAAO,KAAK3B,UAAZ;AACD;;;wBAE0B;AACzB,UAAI,KAAKC,YAAL,IAAqB,IAArB,IAA6B,KAAKH,SAAL,IAAkB,IAAnD,EAAyD;AACvD,aAAKG,YAAL,GAAoBkB,eAAMgB,kBAAN,CAAyB,KAAKrC,SAAL,CAAe6B,QAAf,CAAwB,KAAxB,CAAzB,CAApB;AACD;AACD,aAAO,KAAK1B,YAAZ;AACD;;;wBAEmB;AAClB,aAAO,KAAKG,cAAL,CAAoBsB,KAApB,EAAP;AACD,K;sBA6BSA,K,EAAqB;AAC7B,WAAKtB,cAAL,CAAoBgC,QAApB,CAA6BV,KAA7B;AACD;;;wBA7BkB;AACjB,aAAO,KAAKxB,QAAZ;AACD,K;sBA6CWT,O,EAAoB;AAC9B,WAAKS,QAAL,GAAgBT,OAAhB;AACD;;;wBA7CsB;AACrB,aAAO,KAAKU,QAAZ;AACD,K;sBA6CWT,O,EAAwB;AAClC,WAAKS,QAAL,GAAgBT,OAAhB;AACD;;;wBA7CgC;AAC/B,aAAO,KAAKU,cAAL,CAAoBiC,OAApB,EAAP;AACD;;;wBA6CsB;AACrB,aAAOxB,QAAQ,CAAC,KAAKjB,QAAL,IAAiB,KAAKG,WAAvB,MAAwC,KAAKF,OAAL,IAAgB,KAAKG,UAA7D,CAAR,CAAP;AACD;;;+BA4FiBsC,gB,EAA+B;AAC/C,UAAIC,UAAeD,iBAAiBE,KAAjB,CAAuB,GAAvB,CAAnB;AACA,UAAID,QAAQV,MAAR,KAAmB,CAAvB,EAA0B;AACxB,eAAO,IAAP;AACD;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,UAAIY,gBAAgBC,SAASH,QAAQ,CAAR,CAAT,EAAqB,EAArB,CAApB;AACA,UAAII,cACFC,MAAMH,aAAN,KACAA,kBAAkB,CADlB,IAEAG,MAAMF,SAASH,QAAQ,CAAR,CAAT,EAAqB,EAArB,CAAN,CAFA,IAGAK,MAAMF,SAASH,QAAQ,CAAR,CAAT,EAAqB,EAArB,CAAN,CAHA,IAIAK,MAAMF,SAASH,QAAQ,CAAR,CAAT,EAAqB,EAArB,CAAN,CALF;;AAOA,UAAII,WAAJ,EAAiB;AACf,eAAO,IAAP;AACD;;AAED,UAAItD,WAAW,IAAf;AACA,UAAIkD,QAAQ,CAAR,MAAe,GAAnB,EAAwB;AACtBlD,mBAAWkD,QAAQ,CAAR,CAAX;AACD;;AAED,aAAOrD,YAAY2D,aAAZ,CAA0BN,QAAQ,CAAR,CAA1B,EAAsCA,QAAQ,CAAR,CAAtC,EAAkDlD,QAAlD,EAA4DqD,SAASH,QAAQ,CAAR,CAAT,EAAqB,EAArB,CAA5D,CAAP;AACD;;;kCAGCpD,O,EACAC,M,EACAC,Q,EACAqC,K,EAGa;AAAA,UAFbjC,OAEa,uEAFE,EAEF;AAAA,UADbC,OACa,uEADM,EACN;;AACb,UAAMoD,MAAM,IAAI5D,WAAJ,CACVC,OADU,EAEVC,MAFU,EAGVC,QAHU,EAIV,IAJU,EAIJ;AACN,UALU,EAKJ;AACN,UANU,EAMJ;AACNI,aAPU,EAQVC,OARU,CAAZ;AAUAoD,UAAIpB,KAAJ,GAAYA,KAAZ;AACA,aAAOoB,GAAP;AACD;;;kCAGCxD,U,EACAC,S,EACAC,W,EACAkC,K,EAGa;AAAA,UAFbjC,OAEa,uEAFE,EAEF;AAAA,UADbC,OACa,uEADM,EACN;;AACb,UAAMoD,MAAM,IAAI5D,WAAJ,CACV,IADU,EACJ;AACN,UAFU,EAEJ;AACN,UAHU,EAGJ;AACNI,gBAJU,EAKVC,SALU,EAMVC,WANU,EAOVC,OAPU,EAQVC,OARU,CAAZ;AAUAoD,UAAIpB,KAAJ,GAAYA,KAAZ;AACA,aAAOoB,GAAP;AACD;;;;;;kBA5TkB5D,W", "file": "span_context.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport * as constants from './constants';\nimport SamplingState from './samplers/v2/sampling_state';\nimport Utils from './util';\nimport Span from './span';\n\nexport default class SpanContext {\n  _traceId: any;\n  _spanId: any;\n  _parentId: any;\n  _traceIdStr: ?string;\n  _spanIdStr: ?string;\n  _parentIdStr: ?string;\n  _baggage: any;\n  _debugId: ?string;\n  _samplingState: SamplingState;\n  _remote: boolean; // indicates that span context represents a remote parent\n\n  constructor(\n    traceId: any,\n    spanId: any,\n    parentId: any,\n    traceIdStr: ?string,\n    spanIdStr: ?string,\n    parentIdStr: ?string,\n    baggage: any = {},\n    debugId: ?string = '',\n    samplingState: ?SamplingState\n  ) {\n    this._traceId = traceId;\n    this._spanId = spanId;\n    this._parentId = parentId || null;\n    this._traceIdStr = traceIdStr;\n    this._spanIdStr = spanIdStr;\n    this._parentIdStr = parentIdStr;\n    this._baggage = baggage;\n    this._debugId = debugId;\n    this._samplingState = samplingState || new SamplingState(this.spanIdStr);\n    this._remote = false;\n  }\n\n  get traceId(): any {\n    if (this._traceId == null && this._traceIdStr != null) {\n      // make sure that the string is 32 or 16 characters long to generate the\n      // corresponding 64 or 128 bits buffer by padding the start with zeros if necessary\n      // https://github.com/jaegertracing/jaeger/issues/1657\n      // At the same time this will enforce that the HEX has an even number of digits, node is expecting 2 HEX characters per byte\n      // https://github.com/nodejs/node/issues/21242\n      const traceIdExactLength = this._traceIdStr.length > 16 ? 32 : 16;\n      if (this._traceIdStr.length === traceIdExactLength) {\n        this._traceId = Utils.newBufferFromHex(this._traceIdStr);\n      } else {\n        const padding = traceIdExactLength === 16 ? '0000000000000000' : '00000000000000000000000000000000';\n        const safeTraceIdStr = (padding + this._traceIdStr).slice(-traceIdExactLength);\n        this._traceId = Utils.newBufferFromHex(safeTraceIdStr);\n      }\n    }\n    return this._traceId;\n  }\n\n  get spanId(): any {\n    if (this._spanId == null && this._spanIdStr != null) {\n      this._spanId = Utils.encodeInt64(this._spanIdStr);\n    }\n    return this._spanId;\n  }\n\n  get parentId(): any {\n    if (this._parentId == null && this._parentIdStr != null) {\n      this._parentId = Utils.encodeInt64(this._parentIdStr);\n    }\n    return this._parentId;\n  }\n\n  get traceIdStr(): ?string {\n    if (this._traceIdStr == null && this._traceId != null) {\n      this._traceIdStr = Utils.removeLeadingZeros(this._traceId.toString('hex'));\n    }\n    return this._traceIdStr;\n  }\n\n  get spanIdStr(): ?string {\n    if (this._spanIdStr == null && this._spanId != null) {\n      this._spanIdStr = Utils.removeLeadingZeros(this._spanId.toString('hex'));\n    }\n    return this._spanIdStr;\n  }\n  \n  get parentIdStr(): ?string {\n    if (this._parentIdStr == null && this._parentId != null) {\n      this._parentIdStr = Utils.removeLeadingZeros(this._parentId.toString('hex'));\n    }\n    return this._parentIdStr;\n  }\n\n  get flags(): number {\n    return this._samplingState.flags();\n  }\n\n  get baggage(): any {\n    return this._baggage;\n  }\n\n  get debugId(): ?string {\n    return this._debugId;\n  }\n\n  get samplingFinalized(): boolean {\n    return this._samplingState.isFinal();\n  }\n\n  set traceId(traceId: Buffer): void {\n    this._traceId = traceId;\n    this._traceIdStr = null;\n  }\n\n  set spanId(spanId: Buffer): void {\n    this._spanId = spanId;\n    this._spanIdStr = null;\n  }\n\n  set parentId(parentId: Buffer | null): void {\n    this._parentId = parentId;\n    this._parentIdStr = null;\n  }\n\n  set flags(flags: number): void {\n    this._samplingState.setFlags(flags);\n  }\n\n  _setSampled(value: boolean) {\n    this._samplingState.setSampled(value);\n  }\n\n  _setDebug(value: boolean) {\n    this._samplingState.setDebug(value);\n  }\n\n  _setFirehose(value: boolean) {\n    this._samplingState.setFirehose(value);\n  }\n\n  _setRemote(value: boolean) {\n    this._remote = value;\n  }\n\n  set baggage(baggage: any): void {\n    this._baggage = baggage;\n  }\n\n  set debugId(debugId: ?string): void {\n    this._debugId = debugId;\n  }\n\n  get isValid(): boolean {\n    return Boolean((this._traceId || this._traceIdStr) && (this._spanId || this._spanIdStr));\n  }\n\n  finalizeSampling() {\n    return this._samplingState.setFinal(true);\n  }\n\n  isRemote(): boolean {\n    return this._remote;\n  }\n\n  isDebugIDContainerOnly(): boolean {\n    return !this.isValid && Boolean(this._debugId);\n  }\n\n  /**\n   * @return {boolean} - returns whether or not this span context was sampled.\n   **/\n  isSampled(): boolean {\n    return this._samplingState.isSampled();\n  }\n\n  /**\n   * @return {boolean} - returns whether or not this span context has a debug flag set.\n   **/\n  isDebug(): boolean {\n    return this._samplingState.isDebug();\n  }\n\n  isFirehose(): boolean {\n    return this._samplingState.isFirehose();\n  }\n\n  withBaggageItem(key: string, value: string): SpanContext {\n    let newBaggage = Utils.clone(this._baggage);\n    newBaggage[key] = value;\n    return new SpanContext(\n      this._traceId,\n      this._spanId,\n      this._parentId,\n      this._traceIdStr,\n      this._spanIdStr,\n      this._parentIdStr,\n      newBaggage,\n      this._debugId,\n      this._samplingState\n    );\n  }\n\n  _makeChildContext(childId: any) {\n    const idIsStr = typeof childId === 'string';\n    const _childId = idIsStr ? null : childId;\n    const _childIdStr = idIsStr ? childId : null;\n    return new SpanContext(\n      this._traceId, // traceId\n      _childId, // spanId\n      this._spanId, // parentId\n      this._traceIdStr, // traceIdStr\n      _childIdStr, // spanIdStr\n      this._spanIdStr, // parentIdStr\n      this._baggage, // baggage\n      this._debugId, // debugID\n      this._samplingState // samplingState\n    );\n  }\n\n  /**\n   * @return {string} - returns trace ID as string or \"\" if null\n   */\n  toTraceId(): string {\n    return this.traceIdStr || \"\";\n  }\n\n  /**\n   * @return {string} - returns span ID as string or \"\" if null\n   */\n  toSpanId(): string {\n    return this.spanIdStr || \"\";\n  }\n\n  /**\n   * @return {string} - returns a string version of this span context.\n   **/\n  toString(): string {\n    return `${String(this.traceIdStr)}:${String(this.spanIdStr)}:${String(\n      this.parentIdStr || 0\n    )}:${this._samplingState.flags().toString(16)}`;\n  }\n\n  /**\n   * @param {string} serializedString - a serialized span context.\n   * @return {SpanContext} - returns a span context represented by the serializedString.\n   **/\n  static fromString(serializedString: string): any {\n    let headers: any = serializedString.split(':');\n    if (headers.length !== 4) {\n      return null;\n    }\n\n    // Note: Number type in JS cannot represent the full range of 64bit unsigned ints,\n    // so using parseInt() on strings representing 64bit hex numbers only returns\n    // an approximation of the actual value. Fortunately, we do not depend on the\n    // returned value, we are only using it to validate that the string is\n    // a valid hex number (which is faster than doing it manually).  We cannot use\n    // Int64(numberValue).toBuffer() because it throws exceptions on bad strings.\n    let approxTraceId = parseInt(headers[0], 16);\n    let NaNDetected =\n      isNaN(approxTraceId) ||\n      approxTraceId === 0 ||\n      isNaN(parseInt(headers[1], 16)) ||\n      isNaN(parseInt(headers[2], 16)) ||\n      isNaN(parseInt(headers[3], 16));\n\n    if (NaNDetected) {\n      return null;\n    }\n\n    let parentId = null;\n    if (headers[2] !== '0') {\n      parentId = headers[2];\n    }\n\n    return SpanContext.withStringIds(headers[0], headers[1], parentId, parseInt(headers[3], 16));\n  }\n\n  static withBinaryIds(\n    traceId: any,\n    spanId: any,\n    parentId: any,\n    flags: number,\n    baggage: any = {},\n    debugId: ?string = ''\n  ): SpanContext {\n    const ctx = new SpanContext(\n      traceId,\n      spanId,\n      parentId,\n      null, // traceIdStr: string,\n      null, // spanIdStr: string,\n      null, // parentIdStr: string,\n      baggage,\n      debugId\n    );\n    ctx.flags = flags;\n    return ctx;\n  }\n\n  static withStringIds(\n    traceIdStr: any,\n    spanIdStr: any,\n    parentIdStr: any,\n    flags: number,\n    baggage: any = {},\n    debugId: ?string = ''\n  ): SpanContext {\n    const ctx = new SpanContext(\n      null, // traceId,\n      null, // spanId,\n      null, // parentId,\n      traceIdStr,\n      spanIdStr,\n      parentIdStr,\n      baggage,\n      debugId\n    );\n    ctx.flags = flags;\n    return ctx;\n  }\n}\n"]}