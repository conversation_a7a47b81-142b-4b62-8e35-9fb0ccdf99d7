{"version": 3, "sources": ["../../src/configuration.js"], "names": ["opentracing", "constants", "jaegerSchema", "id", "type", "properties", "serviceName", "disable", "sampler", "param", "hostPort", "host", "port", "samplingPath", "refreshIntervalMs", "required", "additionalProperties", "reporter", "logSpans", "agentHost", "agentPort", "agentSocketType", "collectorEndpoint", "username", "password", "flushIntervalMs", "timeoutMs", "throttler", "Configuration", "config", "options", "Error", "SAMPLER_TYPE_PROBABILISTIC", "ProbabilisticSampler", "SAMPLER_TYPE_RATE_LIMITING", "RateLimitingSampler", "SAMPLER_TYPE_CONST", "ConstSampler", "SAMPLER_TYPE_REMOTE", "RemoteSampler", "refreshInterval", "metrics", "logger", "reporterConfig", "reporters", "isHTTPSender", "senderConfig", "push", "Logging<PERSON>ep<PERSON>er", "sender", "HTTPSender", "UDPSender", "remoteReporter", "RemoteReport<PERSON>", "length", "CompositeReporter", "throttlerOptions", "Utils", "clone", "RemoteThrottler", "Metrics", "Tracer", "_getSampler", "_getReporter", "_getThrottler", "info", "<PERSON><PERSON>ey", "baggagePrefix", "tags", "traceId128bit", "shareRpcSpan", "debugThrottler"], "mappings": ";;;;;;qjBAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;IAAYA,W;;AACZ;;IAAYC,S;;AACZ;;;;AACA;;;;;;;;;;AAEA,IAAIC,eAAe;AACjBC,MAAI,SADa;AAEjBC,QAAM,QAFW;AAGjBC,cAAY;AACVC,iBAAa,EAAEF,MAAM,QAAR,EADH;AAEVG,aAAS,EAAEH,MAAM,SAAR,EAFC;AAGVI,aAAS;AACPH,kBAAY;AACVD,cAAM,EAAEA,MAAM,QAAR,EADI;AAEVK,eAAO,EAAEL,MAAM,QAAR,EAFG;AAGVM,kBAAU,EAAEN,MAAM,QAAR,EAHA;AAIVO,cAAM,EAAEP,MAAM,QAAR,EAJI;AAKVQ,cAAM,EAAER,MAAM,QAAR,EALI;AAMVS,sBAAc,EAAET,MAAM,QAAR,EANJ;AAOVU,2BAAmB,EAAEV,MAAM,QAAR;AAPT,OADL;AAUPW,gBAAU,CAAC,MAAD,EAAS,OAAT,CAVH;AAWPC,4BAAsB;AAXf,KAHC;AAgBVC,cAAU;AACRZ,kBAAY;AACVa,kBAAU,EAAEd,MAAM,SAAR,EADA;AAEVe,mBAAW,EAAEf,MAAM,QAAR,EAFD;AAGVgB,mBAAW,EAAEhB,MAAM,QAAR,EAHD;AAIViB,yBAAiB,EAAEjB,MAAM,QAAR,EAJP;AAKVkB,2BAAmB,EAAElB,MAAM,QAAR,EALT;AAMVmB,kBAAU,EAAEnB,MAAM,QAAR,EANA;AAOVoB,kBAAU,EAAEpB,MAAM,QAAR,EAPA;AAQVqB,yBAAiB,EAAErB,MAAM,QAAR,EARP;AASVsB,mBAAW,EAAEtB,MAAM,QAAR;AATD,OADJ;AAYRY,4BAAsB;AAZd,KAhBA;AA8BVW,eAAW;AACTtB,kBAAY;AACVM,cAAM,EAAEP,MAAM,QAAR,EADI;AAEVQ,cAAM,EAAER,MAAM,QAAR,EAFI;AAGVU,2BAAmB,EAAEV,MAAM,QAAR;AAHT,OADH;AAMTY,4BAAsB;AANb;AA9BD;AAHK,CAAnB;;IA4CqBY,a;;;;;;;gCACAC,M,EAAQC,O,EAAS;AAClC,UAAI1B,OAAOyB,OAAOrB,OAAP,CAAeJ,IAA1B;AACA,UAAIK,QAAQoB,OAAOrB,OAAP,CAAeC,KAA3B;AACA,UAAIC,WAAWmB,OAAOrB,OAAP,CAAeE,QAA9B;AACA,UAAIC,OAAOkB,OAAOrB,OAAP,CAAeG,IAA1B;AACA,UAAIC,OAAOiB,OAAOrB,OAAP,CAAeI,IAA1B;AACA,UAAIC,eAAegB,OAAOrB,OAAP,CAAeK,YAAlC;AACA,UAAIC,oBAAoBe,OAAOrB,OAAP,CAAeM,iBAAvC;;AAEA,UAAI,OAAOL,KAAP,KAAiB,QAArB,EAA+B;AAC7B,cAAM,IAAIsB,KAAJ,uDAA8DtB,KAA9D,CAAN;AACD;;AAED,UAAID,gBAAJ;AACA,UAAIJ,SAASH,UAAU+B,0BAAvB,EAAmD;AACjDxB,kBAAU,IAAIyB,+BAAJ,CAAyBxB,KAAzB,CAAV;AACD;;AAED,UAAIL,SAASH,UAAUiC,0BAAvB,EAAmD;AACjD1B,kBAAU,IAAI2B,+BAAJ,CAAwB1B,KAAxB,CAAV;AACD;;AAED,UAAIL,SAASH,UAAUmC,kBAAvB,EAA2C;AACzC5B,kBAAU,IAAI6B,uBAAJ,CAAiB5B,UAAU,CAA3B,CAAV;AACD;;AAED,UAAIL,SAASH,UAAUqC,mBAAvB,EAA4C;AAC1C9B,kBAAU,IAAI+B,wBAAJ,CAAkBV,OAAOvB,WAAzB,EAAsC;AAC9CE,mBAAS,IAAIyB,+BAAJ,CAAyBxB,KAAzB,CADqC;AAE9CC,oBAAUA,QAFoC;AAG9CC,gBAAMA,IAHwC;AAI9CC,gBAAMA,IAJwC;AAK9CC,wBAAcA,YALgC;AAM9C2B,2BAAiB1B,iBAN6B;AAO9C2B,mBAASX,QAAQW,OAP6B;AAQ9CC,kBAAQZ,QAAQY;AAR8B,SAAtC,CAAV;AAUD;;AAED,aAAOlC,OAAP;AACD;;;iCAEmBqB,M,EAAQC,O,EAAS;AACnC,UAAIa,iBAAiB,EAArB;AACA,UAAIC,YAAY,EAAhB;AACA,UAAIC,eAAe,KAAnB;AACA,UAAIC,eAAe;AACjBJ,gBAAQZ,QAAQY;AADC,OAAnB;AAGA,UAAIb,OAAOZ,QAAX,EAAqB;AACnB,YAAIY,OAAOZ,QAAP,CAAgBC,QAApB,EAA8B;AAC5B0B,oBAAUG,IAAV,CAAe,IAAIC,0BAAJ,CAAoBlB,QAAQY,MAA5B,CAAf;AACD;;AAED,YAAIb,OAAOZ,QAAP,CAAgBQ,eAApB,EAAqC;AACnCkB,yBAAe,qBAAf,IAAwCd,OAAOZ,QAAP,CAAgBQ,eAAxD;AACD;;AAED,YAAII,OAAOZ,QAAP,CAAgBK,iBAApB,EAAuC;AACrCuB,yBAAe,IAAf;;AAEAC,uBAAa,UAAb,IAA2BjB,OAAOZ,QAAP,CAAgBK,iBAA3C;;AAEA,cAAIO,OAAOZ,QAAP,CAAgBM,QAApB,EAA8B;AAC5BuB,yBAAa,UAAb,IAA2BjB,OAAOZ,QAAP,CAAgBM,QAA3C;AACD;AACD,cAAIM,OAAOZ,QAAP,CAAgBO,QAApB,EAA8B;AAC5BsB,yBAAa,UAAb,IAA2BjB,OAAOZ,QAAP,CAAgBO,QAA3C;AACD;AACD,cAAIK,OAAOZ,QAAP,CAAgBS,SAApB,EAA+B;AAC7BoB,yBAAa,WAAb,IAA4BjB,OAAOZ,QAAP,CAAgBS,SAA5C;AACD;AACF;AACD,YAAIG,OAAOZ,QAAP,CAAgBE,SAApB,EAA+B;AAC7B2B,uBAAa,MAAb,IAAuBjB,OAAOZ,QAAP,CAAgBE,SAAvC;AACD;;AAED,YAAIU,OAAOZ,QAAP,CAAgBG,SAApB,EAA+B;AAC7B0B,uBAAa,MAAb,IAAuBjB,OAAOZ,QAAP,CAAgBG,SAAvC;AACD;;AAED,YAAIS,OAAOZ,QAAP,CAAgBI,eAApB,EAAqC;AACnCyB,uBAAa,YAAb,IAA6BjB,OAAOZ,QAAP,CAAgBI,eAA7C;AACD;AACF;AACDsB,qBAAe,SAAf,IAA4Bb,QAAQW,OAApC;AACAE,qBAAe,QAAf,IAA2Bb,QAAQY,MAAnC;AACA,UAAIO,SAASJ,eAAe,IAAIK,qBAAJ,CAAeJ,YAAf,CAAf,GAA8C,IAAIK,oBAAJ,CAAcL,YAAd,CAA3D;AACA,UAAIM,iBAAiB,IAAIC,yBAAJ,CAAmBJ,MAAnB,EAA2BN,cAA3B,CAArB;AACA,UAAIC,UAAUU,MAAV,IAAoB,CAAxB,EAA2B;AACzB,eAAOF,cAAP;AACD;AACDR,gBAAUG,IAAV,CAAeK,cAAf;AACA,aAAO,IAAIG,4BAAJ,CAAsBX,SAAtB,CAAP;AACD;;;kCAEoBf,M,EAAQC,O,EAAS;AACpC,UAAM0B,mBAAmBC,eAAMC,KAAN,CAAY7B,OAAOF,SAAnB,CAAzB;AACA,UAAIG,QAAQY,MAAZ,EAAoB;AAClBc,yBAAiBd,MAAjB,GAA0BZ,QAAQY,MAAlC;AACD;AACD,UAAIZ,QAAQW,OAAZ,EAAqB;AACnBe,yBAAiBf,OAAjB,GAA2BX,QAAQW,OAAnC;AACD;AACD,aAAO,IAAIkB,0BAAJ,CAAoB9B,OAAOvB,WAA3B,EAAwCkD,gBAAxC,CAAP;AACD;;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA6BkB3B,M,EAAsB;AAAA,UAAdC,OAAc,uEAAJ,EAAI;;AACtC,UAAIb,iBAAJ;AACA,UAAIT,gBAAJ;AACA,UAAImB,kBAAJ;AACA,UAAIG,QAAQW,OAAZ,EAAqB;AACnBX,gBAAQW,OAAR,GAAkB,IAAImB,iBAAJ,CAAY9B,QAAQW,OAApB,CAAlB;AACD;AACD,UAAIZ,OAAOtB,OAAX,EAAoB;AAClB,eAAO,IAAIP,YAAY6D,MAAhB,EAAP;AACD;AACD,UAAI,CAAChC,OAAOvB,WAAZ,EAAyB;AACvB,cAAM,IAAIyB,KAAJ,uCAAN;AACD;AACD,UAAIF,OAAOrB,OAAX,EAAoB;AAClBA,kBAAUoB,cAAckC,WAAd,CAA0BjC,MAA1B,EAAkCC,OAAlC,CAAV;AACD,OAFD,MAEO;AACLtB,kBAAU,IAAI+B,wBAAJ,CAAkBV,OAAOvB,WAAzB,EAAsCwB,OAAtC,CAAV;AACD;AACD,UAAI,CAACA,QAAQb,QAAb,EAAuB;AACrBA,mBAAWW,cAAcmC,YAAd,CAA2BlC,MAA3B,EAAmCC,OAAnC,CAAX;AACD,OAFD,MAEO;AACLb,mBAAWa,QAAQb,QAAnB;AACD;AACD,UAAI,CAACa,QAAQH,SAAb,EAAwB;AACtB,YAAIE,OAAOF,SAAX,EAAsB;AACpBA,sBAAYC,cAAcoC,aAAd,CAA4BnC,MAA5B,EAAoCC,OAApC,CAAZ;AACD;AACF,OAJD,MAIO;AACLH,oBAAYG,QAAQH,SAApB;AACD;;AAED,UAAIG,QAAQY,MAAZ,EAAoB;AAClBZ,gBAAQY,MAAR,CAAeuB,IAAf,sCAAuDhD,QAAvD,aAAuET,OAAvE;AACD;;AAED,aAAO,IAAIqD,gBAAJ,CAAWhC,OAAOvB,WAAlB,EAA+BW,QAA/B,EAAyCT,OAAzC,EAAkD;AACvD0D,oBAAYpC,QAAQoC,UADmC;AAEvDC,uBAAerC,QAAQqC,aAFgC;AAGvD1B,iBAASX,QAAQW,OAHsC;AAIvDC,gBAAQZ,QAAQY,MAJuC;AAKvD0B,cAAMtC,QAAQsC,IALyC;AAMvDC,uBAAevC,QAAQuC,aANgC;AAOvDC,sBAAcxC,QAAQwC,YAPiC;AAQvDC,wBAAgB5C;AARuC,OAAlD,CAAP;AAUD;;;;;;kBAtLkBC,a", "file": "configuration.js", "sourcesContent": ["// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport ConstSampler from './samplers/const_sampler';\nimport ProbabilisticSampler from './samplers/probabilistic_sampler';\nimport RateLimitingSampler from './samplers/rate_limiting_sampler';\nimport RemoteReporter from './reporters/remote_reporter';\nimport CompositeReporter from './reporters/composite_reporter';\nimport LoggingReporter from './reporters/logging_reporter';\nimport RemoteSampler from './samplers/remote_sampler';\nimport Metrics from './metrics/metrics';\nimport Tracer from './tracer';\nimport UDPSender from './reporters/udp_sender';\nimport HTTPSender from './reporters/http_sender';\nimport * as opentracing from 'opentracing';\nimport * as constants from './constants.js';\nimport RemoteThrottler from './throttler/remote_throttler';\nimport Utils from './util.js';\n\nlet jaegerSchema = {\n  id: '/jaeger',\n  type: 'object',\n  properties: {\n    serviceName: { type: 'string' },\n    disable: { type: 'boolean' },\n    sampler: {\n      properties: {\n        type: { type: 'string' },\n        param: { type: 'number' },\n        hostPort: { type: 'string' },\n        host: { type: 'string' },\n        port: { type: 'number' },\n        samplingPath: { type: 'string' },\n        refreshIntervalMs: { type: 'number' },\n      },\n      required: ['type', 'param'],\n      additionalProperties: false,\n    },\n    reporter: {\n      properties: {\n        logSpans: { type: 'boolean' },\n        agentHost: { type: 'string' },\n        agentPort: { type: 'number' },\n        agentSocketType: { type: 'string' },\n        collectorEndpoint: { type: 'string' },\n        username: { type: 'string' },\n        password: { type: 'string' },\n        flushIntervalMs: { type: 'number' },\n        timeoutMs: { type: 'number' },\n      },\n      additionalProperties: false,\n    },\n    throttler: {\n      properties: {\n        host: { type: 'string' },\n        port: { type: 'number' },\n        refreshIntervalMs: { type: 'number' },\n      },\n      additionalProperties: false,\n    },\n  },\n};\n\nexport default class Configuration {\n  static _getSampler(config, options) {\n    let type = config.sampler.type;\n    let param = config.sampler.param;\n    let hostPort = config.sampler.hostPort;\n    let host = config.sampler.host;\n    let port = config.sampler.port;\n    let samplingPath = config.sampler.samplingPath;\n    let refreshIntervalMs = config.sampler.refreshIntervalMs;\n\n    if (typeof param !== 'number') {\n      throw new Error(`Expecting sampler.param to be a number. Received ${param}`);\n    }\n\n    let sampler;\n    if (type === constants.SAMPLER_TYPE_PROBABILISTIC) {\n      sampler = new ProbabilisticSampler(param);\n    }\n\n    if (type === constants.SAMPLER_TYPE_RATE_LIMITING) {\n      sampler = new RateLimitingSampler(param);\n    }\n\n    if (type === constants.SAMPLER_TYPE_CONST) {\n      sampler = new ConstSampler(param === 1);\n    }\n\n    if (type === constants.SAMPLER_TYPE_REMOTE) {\n      sampler = new RemoteSampler(config.serviceName, {\n        sampler: new ProbabilisticSampler(param),\n        hostPort: hostPort,\n        host: host,\n        port: port,\n        samplingPath: samplingPath,\n        refreshInterval: refreshIntervalMs,\n        metrics: options.metrics,\n        logger: options.logger,\n      });\n    }\n\n    return sampler;\n  }\n\n  static _getReporter(config, options) {\n    let reporterConfig = {};\n    let reporters = [];\n    let isHTTPSender = false;\n    let senderConfig = {\n      logger: options.logger,\n    };\n    if (config.reporter) {\n      if (config.reporter.logSpans) {\n        reporters.push(new LoggingReporter(options.logger));\n      }\n\n      if (config.reporter.flushIntervalMs) {\n        reporterConfig['bufferFlushInterval'] = config.reporter.flushIntervalMs;\n      }\n\n      if (config.reporter.collectorEndpoint) {\n        isHTTPSender = true;\n\n        senderConfig['endpoint'] = config.reporter.collectorEndpoint;\n\n        if (config.reporter.username) {\n          senderConfig['username'] = config.reporter.username;\n        }\n        if (config.reporter.password) {\n          senderConfig['password'] = config.reporter.password;\n        }\n        if (config.reporter.timeoutMs) {\n          senderConfig['timeoutMs'] = config.reporter.timeoutMs;\n        }\n      }\n      if (config.reporter.agentHost) {\n        senderConfig['host'] = config.reporter.agentHost;\n      }\n\n      if (config.reporter.agentPort) {\n        senderConfig['port'] = config.reporter.agentPort;\n      }\n\n      if (config.reporter.agentSocketType) {\n        senderConfig['socketType'] = config.reporter.agentSocketType;\n      }\n    }\n    reporterConfig['metrics'] = options.metrics;\n    reporterConfig['logger'] = options.logger;\n    let sender = isHTTPSender ? new HTTPSender(senderConfig) : new UDPSender(senderConfig);\n    let remoteReporter = new RemoteReporter(sender, reporterConfig);\n    if (reporters.length == 0) {\n      return remoteReporter;\n    }\n    reporters.push(remoteReporter);\n    return new CompositeReporter(reporters);\n  }\n\n  static _getThrottler(config, options) {\n    const throttlerOptions = Utils.clone(config.throttler);\n    if (options.logger) {\n      throttlerOptions.logger = options.logger;\n    }\n    if (options.metrics) {\n      throttlerOptions.metrics = options.metrics;\n    }\n    return new RemoteThrottler(config.serviceName, throttlerOptions);\n  }\n\n  /**\n   * Initialize and return a new instance of Jaeger Tracer.\n   *\n   * The config dictionary is not validated for adherence to the schema above.\n   * Such validation can be performed like this:\n   *\n   *     import {Validator} from 'jsonschema';\n   *\n   *     let v = new Validator();\n   *     v.validate(config, jaegerSchema, {\n   *       throwError: true\n   *     });\n   *\n   * @param {Object} config - configuration matching the jaegerSchema definition.\n   * @param {Object} options - options\n   * @param {Object} [options.reporter] - if provided, this reporter will be used.\n   *        Otherwise a new reporter will be created according to the description\n   *        in the config.\n   * @param {Object} [options.throttler] - if provided, this throttler will be used.\n   *        Otherwise a new throttler will be created according to the description\n   *        in the config.\n   * @param {Object} [options.metrics] - a metrics factory (see ./_flow/metrics.js)\n   * @param {Object} [options.logger] - a logger (see ./_flow/logger.js)\n   * @param {Object} [options.tags] - set of key-value pairs which will be set\n   *        as process-level tags on the Tracer itself.\n   * @param {boolean} [options.traceId128bit] - generate root span with a 128bit traceId.\n   * @param {boolean} [options.shareRpcSpan] - Share the same span for rpc span_kind.\n   */\n\n  static initTracer(config, options = {}) {\n    let reporter;\n    let sampler;\n    let throttler;\n    if (options.metrics) {\n      options.metrics = new Metrics(options.metrics);\n    }\n    if (config.disable) {\n      return new opentracing.Tracer();\n    }\n    if (!config.serviceName) {\n      throw new Error(`config.serviceName must be provided`);\n    }\n    if (config.sampler) {\n      sampler = Configuration._getSampler(config, options);\n    } else {\n      sampler = new RemoteSampler(config.serviceName, options);\n    }\n    if (!options.reporter) {\n      reporter = Configuration._getReporter(config, options);\n    } else {\n      reporter = options.reporter;\n    }\n    if (!options.throttler) {\n      if (config.throttler) {\n        throttler = Configuration._getThrottler(config, options);\n      }\n    } else {\n      throttler = options.throttler;\n    }\n\n    if (options.logger) {\n      options.logger.info(`Initializing Jaeger Tracer with ${reporter} and ${sampler}`);\n    }\n\n    return new Tracer(config.serviceName, reporter, sampler, {\n      contextKey: options.contextKey,\n      baggagePrefix: options.baggagePrefix,\n      metrics: options.metrics,\n      logger: options.logger,\n      tags: options.tags,\n      traceId128bit: options.traceId128bit,\n      shareRpcSpan: options.shareRpcSpan,\n      debugThrottler: throttler,\n    });\n  }\n}\n"]}