{"version": 3, "sources": ["../../src/util.js"], "names": ["Utils", "text", "prefix", "indexOf", "suffix", "lastIndexOf", "length", "ran<PERSON>t", "xorshift", "randomint", "buf", "new<PERSON>uffer", "writeUInt32BE", "randint1", "randint2", "numberValue", "Int64", "<PERSON><PERSON><PERSON><PERSON>", "input", "counter", "i", "char<PERSON>t", "substring", "myIp", "ifaces", "os", "networkInterfaces", "keys", "Object", "loop1", "iface", "j", "family", "internal", "address", "obj", "newObj", "key", "prototype", "hasOwnProperty", "call", "dict", "tags", "value", "push", "host", "port", "path", "success", "error", "http", "get", "res", "setEncoding", "body", "on", "chunk", "err", "encoding", "<PERSON><PERSON><PERSON>", "from", "Uint8Array", "size", "alloc", "buffer", "fill", "limit", "callback", "count"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;AACA;;;;AACA;;;;;;;;IAEqBA,K;;;;;;;;AACnB;;;;;;;;+BAQkBC,I,EAAcC,M,EAAyB;AACvD,aAAOD,KAAKE,OAAL,CAAaD,MAAb,MAAyB,CAAhC;AACD;;AAED;;;;;;;;;;;6BAQgBD,I,EAAcG,M,EAAyB;AACrD,aAAOH,KAAKI,WAAL,CAAiBD,MAAjB,MAA6BH,KAAKK,MAAL,GAAcF,OAAOE,MAAzD;AACD;;AAED;;;;;;;;;kCAM6B;AAC3B,UAAIC,UAAUC,mBAASC,SAAT,EAAd;AACA,UAAIC,MAAM,KAAKC,SAAL,CAAe,CAAf,CAAV;AACAD,UAAIE,aAAJ,CAAkBL,QAAQ,CAAR,CAAlB,EAA8B,CAA9B;AACAG,UAAIE,aAAJ,CAAkBL,QAAQ,CAAR,CAAlB,EAA8B,CAA9B;AACA,aAAOG,GAAP;AACD;;AAED;;;;;;;;;mCAM8B;AAC5B,UAAIG,WAAWL,mBAASC,SAAT,EAAf;AACA,UAAIK,WAAWN,mBAASC,SAAT,EAAf;AACA,UAAIC,MAAM,KAAKC,SAAL,CAAe,EAAf,CAAV;AACAD,UAAIE,aAAJ,CAAkBC,SAAS,CAAT,CAAlB,EAA+B,CAA/B;AACAH,UAAIE,aAAJ,CAAkBC,SAAS,CAAT,CAAlB,EAA+B,CAA/B;AACAH,UAAIE,aAAJ,CAAkBE,SAAS,CAAT,CAAlB,EAA+B,CAA/B;AACAJ,UAAIE,aAAJ,CAAkBE,SAAS,CAAT,CAAlB,EAA+B,EAA/B;AACA,aAAOJ,GAAP;AACD;;AAED;;;;;;;;gCAKmBK,W,EAAuB;AACxC,aAAO,IAAIC,iBAAJ,CAAUD,WAAV,EAAuBE,QAAvB,EAAP;AACD;;AAED;;;;;;;uCAI0BC,K,EAAuB;AAC/C,UAAIC,UAAU,CAAd;AACA,UAAIb,SAASY,MAAMZ,MAAN,GAAe,CAA5B;AACA,WAAK,IAAIc,IAAI,CAAb,EAAgBA,IAAId,MAApB,EAA4Bc,GAA5B,EAAiC;AAC/B,YAAIF,MAAMG,MAAN,CAAaD,CAAb,MAAoB,GAAxB,EAA6B;AAC3BD;AACD,SAFD,MAEO;AACL;AACD;AACF;;AAED,aAAOD,MAAMI,SAAN,CAAgBH,OAAhB,CAAP;AACD;;;2BAEqB;AACpB,UAAII,OAAO,SAAX;AACA,UAAIC,SAASC,aAAGC,iBAAH,EAAb;AACA,UAAIC,OAAOC,OAAOD,IAAP,CAAYH,MAAZ,CAAX;AACAK,aAAO,KAAK,IAAIT,IAAI,CAAb,EAAgBA,IAAIO,KAAKrB,MAAzB,EAAiCc,GAAjC,EAAsC;AAC3C,YAAIU,QAAQN,OAAOG,KAAKP,CAAL,CAAP,CAAZ;AACA,aAAK,IAAIW,IAAI,CAAb,EAAgBA,IAAID,MAAMxB,MAA1B,EAAkCyB,GAAlC,EAAuC;AACrC,cAAID,MAAMC,CAAN,EAASC,MAAT,KAAoB,MAApB,IAA8B,CAACF,MAAMC,CAAN,EAASE,QAA5C,EAAsD;AACpDV,mBAAOO,MAAMC,CAAN,EAASG,OAAhB;AACA,kBAAML,KAAN;AACD;AACF;AACF;AACD,aAAON,IAAP;AACD;;;0BAEYY,G,EAAe;AAC1B,UAAIC,SAAS,EAAb;AACA,WAAK,IAAIC,GAAT,IAAgBF,GAAhB,EAAqB;AACnB,YAAIP,OAAOU,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCL,GAArC,EAA0CE,GAA1C,CAAJ,EAAoD;AAClDD,iBAAOC,GAAP,IAAcF,IAAIE,GAAJ,CAAd;AACD;AACF;;AAED,aAAOD,MAAP;AACD;;;wCAE0BK,I,EAAuB;AAChD,UAAIC,OAAmB,EAAvB;AACA,WAAK,IAAIL,GAAT,IAAgBI,IAAhB,EAAsB;AACpB,YAAIE,QAAQF,KAAKJ,GAAL,CAAZ;AACA,YAAIT,OAAOU,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCC,IAArC,EAA2CJ,GAA3C,CAAJ,EAAqD;AACnDK,eAAKE,IAAL,CAAU,EAAEP,KAAKA,GAAP,EAAYM,OAAOA,KAAnB,EAAV;AACD;AACF;;AAED,aAAOD,IAAP;AACD;;;4BAEcG,I,EAAcC,I,EAAcC,I,EAAcC,O,EAAmBC,K,EAAiB;AAC3FC,qBACGC,GADH,CAEI;AACEN,cAAMA,IADR;AAEEC,cAAMA,IAFR;AAGEC,cAAMA;AAHR,OAFJ,EAOI,eAAO;AACL;AACAK,YAAIC,WAAJ,CAAgB,MAAhB;;AAEA;AACA,YAAIC,OAAO,EAAX;AACAF,YAAIG,EAAJ,CAAO,MAAP,EAAe,iBAAS;AACtBD,kBAAQE,KAAR;AACD,SAFD;;AAIAJ,YAAIG,EAAJ,CAAO,KAAP,EAAc,YAAM;AAClBP,kBAAQM,IAAR;AACD,SAFD;AAGD,OApBL,EAsBGC,EAtBH,CAsBM,OAtBN,EAsBe,eAAO;AAClBN,cAAMQ,GAAN;AACD,OAxBH;AAyBD;;AAED;;;;;;;qCAIwBvC,K,EAAuB;AAC7C,UAAMwC,WAAW,KAAjB;AACA;AACA;AACA,UAAIC,OAAOC,IAAP,IAAeD,OAAOC,IAAP,KAAgBC,WAAWD,IAA9C,EAAoD;AAClD,eAAOD,OAAOC,IAAP,CAAY1C,KAAZ,EAAmBwC,QAAnB,CAAP;AACD;AACD,aAAO,IAAIC,MAAJ,CAAWzC,KAAX,EAAkBwC,QAAlB,CAAP;AACD;;AAED;;;;;;;8BAIiBI,I,EAAsB;AACrC,UAAIH,OAAOI,KAAX,EAAkB;AAChB,eAAOJ,OAAOI,KAAP,CAAaD,IAAb,CAAP;AACD;AACD,UAAME,SAAS,IAAIL,MAAJ,CAAWG,IAAX,CAAf;AACAE,aAAOC,IAAP,CAAY,CAAZ;AACA,aAAOD,MAAP;AACD;;AAED;;;;;;;;;sCAMyBE,K,EAAeC,Q,EAAmC;AACzE,UAAIC,QAAQ,CAAZ;AACA,aAAO,YAAM;AACXA;AACA,YAAIA,SAASF,KAAT,IAAkBC,QAAtB,EAAgC;AAC9BA;AACD;AACF,OALD;AAMD;;;;;;kBA/LkBnE,K", "file": "util.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport xorshift from 'xorshift';\nimport Int64 from 'node-int64';\nimport os from 'os';\nimport http from 'http';\n\nexport default class Utils {\n  /**\n   * Determines whether a string contains a given prefix.\n   *\n   * @param {string} text - the string for to search for a prefix\n   * @param {string} prefix - the prefix to search for in the text given.\n   * @return {boolean} - boolean representing whether or not the\n   * string contains the prefix.\n   **/\n  static startsWith(text: string, prefix: string): boolean {\n    return text.indexOf(prefix) === 0;\n  }\n\n  /**\n   * Determines whether a string contains a given suffix.\n   *\n   * @param {string} text - the string for to search for a suffix\n   * @param {string} suffix - the suffix to search for in the text given.\n   * @return {boolean} - boolean representing whether or not the\n   * string contains the suffix.\n   **/\n  static endsWith(text: string, suffix: string): boolean {\n    return text.lastIndexOf(suffix) === text.length - suffix.length;\n  }\n\n  /**\n   * Get a random buffer representing a random 64 bit.\n   *\n   * @return {Buffer}  - returns a buffer representing a random 64 bit\n   * number.\n   **/\n  static getRandom64(): Buffer {\n    let randint = xorshift.randomint();\n    let buf = this.newBuffer(8);\n    buf.writeUInt32BE(randint[0], 0);\n    buf.writeUInt32BE(randint[1], 4);\n    return buf;\n  }\n\n  /**\n   * Get a random buffer representing a random 128 bit.\n   *\n   * @return {Buffer}  - returns a buffer representing a random 128 bit\n   * number.\n   **/\n  static getRandom128(): Buffer {\n    let randint1 = xorshift.randomint();\n    let randint2 = xorshift.randomint();\n    let buf = this.newBuffer(16);\n    buf.writeUInt32BE(randint1[0], 0);\n    buf.writeUInt32BE(randint1[1], 4);\n    buf.writeUInt32BE(randint2[0], 8);\n    buf.writeUInt32BE(randint2[1], 12);\n    return buf;\n  }\n\n  /**\n   * @param {string|number} numberValue - a string or number to be encoded\n   * as a 64 bit byte array.\n   * @return {Buffer} - returns a buffer representing the encoded string, or number.\n   **/\n  static encodeInt64(numberValue: any): any {\n    return new Int64(numberValue).toBuffer();\n  }\n\n  /**\n   * @param {string} input - the input for which leading zeros should be removed.\n   * @return {string} - returns the input string without leading zeros.\n   **/\n  static removeLeadingZeros(input: string): string {\n    let counter = 0;\n    let length = input.length - 1;\n    for (let i = 0; i < length; i++) {\n      if (input.charAt(i) === '0') {\n        counter++;\n      } else {\n        break;\n      }\n    }\n\n    return input.substring(counter);\n  }\n\n  static myIp(): string {\n    let myIp = '0.0.0.0';\n    let ifaces = os.networkInterfaces();\n    let keys = Object.keys(ifaces);\n    loop1: for (let i = 0; i < keys.length; i++) {\n      let iface = ifaces[keys[i]];\n      for (let j = 0; j < iface.length; j++) {\n        if (iface[j].family === 'IPv4' && !iface[j].internal) {\n          myIp = iface[j].address;\n          break loop1;\n        }\n      }\n    }\n    return myIp;\n  }\n\n  static clone(obj: any): any {\n    let newObj = {};\n    for (let key in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, key)) {\n        newObj[key] = obj[key];\n      }\n    }\n\n    return newObj;\n  }\n\n  static convertObjectToTags(dict: any): Array<Tag> {\n    let tags: Array<Tag> = [];\n    for (let key in dict) {\n      let value = dict[key];\n      if (Object.prototype.hasOwnProperty.call(dict, key)) {\n        tags.push({ key: key, value: value });\n      }\n    }\n\n    return tags;\n  }\n\n  static httpGet(host: string, port: number, path: string, success: Function, error: Function) {\n    http\n      .get(\n        {\n          host: host,\n          port: port,\n          path: path,\n        },\n        res => {\n          // explicitly treat incoming data as utf8 (avoids issues with multi-byte chars)\n          res.setEncoding('utf8');\n\n          // incrementally capture the incoming response body\n          let body = '';\n          res.on('data', chunk => {\n            body += chunk;\n          });\n\n          res.on('end', () => {\n            success(body);\n          });\n        }\n      )\n      .on('error', err => {\n        error(err);\n      });\n  }\n\n  /**\n   * @param {string|number} input - a hex encoded string to store in the buffer.\n   * @return {Buffer} - returns a buffer representing the hex encoded string.\n   **/\n  static newBufferFromHex(input: string): Buffer {\n    const encoding = 'hex';\n    // check that 'Buffer.from' exists based on node's documentation\n    // https://nodejs.org/en/docs/guides/buffer-constructor-deprecation/#variant-3\n    if (Buffer.from && Buffer.from !== Uint8Array.from) {\n      return Buffer.from(input, encoding);\n    }\n    return new Buffer(input, encoding);\n  }\n\n  /**\n   * @param {number} input - a number of octets to allocate.\n   * @return {Buffer} - returns an empty buffer.\n   **/\n  static newBuffer(size: number): Buffer {\n    if (Buffer.alloc) {\n      return Buffer.alloc(size);\n    }\n    const buffer = new Buffer(size);\n    buffer.fill(0);\n    return buffer;\n  }\n\n  /**\n   * Creates a callback function that only delegates to passed <code>callback</code>\n   * after <code>limit</code> invocations. Useful in types like CompositeReporter that\n   * needs to invoke the top level callback only after all delegates' close() methods\n   * are called.\n   */\n  static countdownCallback(limit: number, callback: ?() => void): () => void {\n    let count = 0;\n    return () => {\n      count++;\n      if (count >= limit && callback) {\n        callback();\n      }\n    };\n  }\n}\n"]}