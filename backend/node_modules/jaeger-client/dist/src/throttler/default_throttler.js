"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

// Copyright (c) 2018 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software distributed under the License
// is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
// or implied. See the License for the specific language governing permissions and limitations under
// the License.

/**
 * DefaultThrottler either throttles everything or nothing.
 */
var DefaultThrottler = function () {
  function DefaultThrottler(throttleAll) {
    _classCallCheck(this, DefaultThrottler);

    this._throttleAll = throttleAll || false;
  }

  _createClass(DefaultThrottler, [{
    key: "isAllowed",
    value: function isAllowed(operation) {
      return !this._throttleAll;
    }
  }, {
    key: "setProcess",
    value: function setProcess(process) {
      // NOP
    }
  }, {
    key: "close",
    value: function close(callback) {
      if (callback) {
        callback();
      }
    }
  }]);

  return DefaultThrottler;
}();

exports.default = DefaultThrottler;
//# sourceMappingURL=default_throttler.js.map