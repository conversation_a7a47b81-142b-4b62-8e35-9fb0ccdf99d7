{"version": 3, "sources": ["../../../src/throttler/remote_throttler.js"], "names": ["DEFAULT_REFRESH_INTERVAL_MS", "DEFAULT_INITIAL_DELAY_MS", "DEFAULT_THROTTLER_HOST", "DEFAULT_THROTTLER_PORT", "UNIT_CREDIT", "RemoteThrottler", "serviceName", "options", "_serviceName", "_logger", "logger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_metrics", "metrics", "Metrics", "NoopMetricFactory", "_refreshIntervalMs", "refreshIntervalMs", "_host", "host", "_port", "port", "_credits", "_onCreditsUpdate", "onCreditsUpdate", "_initialDelayTimeoutHandle", "setTimeout", "_afterInitialDelay", "bind", "initialDelayMs", "_refreshCredits", "_refreshIntervalHandle", "setInterval", "process", "_uuid", "uuid", "operation", "_isAllowed", "throttledDebugSpans", "increment", "credits", "error", "keys", "Object", "length", "_fetchCredits", "creditResponses", "for<PERSON>ach", "r", "balance", "operations", "encodeURIComponent", "ops", "map", "join", "url", "success", "_parseCreditResponse", "body", "err", "throttlerUpdateFailure", "Utils", "httpGet", "JSON", "parse", "_incrementCredits", "balances", "throttlerUpdateSuccess", "callback", "clearTimeout", "clearInterval"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;AACA;;;;AACA;;;;;;;;AAEA,IAAMA,8BAA8B,IAApC;AACA,IAAMC,2BAA2B,IAAjC;AACA,IAAMC,yBAAyB,SAA/B;AACA,IAAMC,yBAAyB,IAA/B;;AAEA;AACA;AACA,IAAMC,cAAc,GAApB;;IAIqBC,e;;AAgBnB;;;;;;;;;;;;;;;AAeA,2BAAYC,WAAZ,EAAoD;AAAA,QAAnBC,OAAmB,uEAAJ,EAAI;;AAAA;;AAClD,SAAKC,YAAL,GAAoBF,WAApB;AACA,SAAKG,OAAL,GAAeF,QAAQG,MAAR,IAAkB,IAAIC,gBAAJ,EAAjC;AACA,SAAKC,QAAL,GAAgBL,QAAQM,OAAR,IAAmB,IAAIC,iBAAJ,CAAY,IAAIC,wBAAJ,EAAZ,CAAnC;AACA,SAAKC,kBAAL,GAA0BT,QAAQU,iBAAR,IAA6BjB,2BAAvD;AACA,SAAKkB,KAAL,GAAaX,QAAQY,IAAR,IAAgBjB,sBAA7B;AACA,SAAKkB,KAAL,GAAab,QAAQc,IAAR,IAAgBlB,sBAA7B;;AAEA,SAAKmB,QAAL,GAAgB,EAAhB;AACA,SAAKC,gBAAL,GAAwBhB,QAAQiB,eAAhC;;AAEA,SAAKC,0BAAL,GAAkCC,WAChC,KAAKC,kBAAL,CAAwBC,IAAxB,CAA6B,IAA7B,CADgC,EAEhCrB,QAAQsB,cAAR,IAA0B5B,wBAFM,CAAlC;AAID;;;;yCAE0B;AACzB,WAAK6B,eAAL;AACA,WAAKC,sBAAL,GAA8BC,YAAY,KAAKF,eAAL,CAAqBF,IAArB,CAA0B,IAA1B,CAAZ,EAA6C,KAAKZ,kBAAlD,CAA9B;AACA,WAAKS,0BAAL,GAAkC,IAAlC;AACD;;;+BAEUQ,O,EAAwB;AACjC,WAAKC,KAAL,GAAaD,QAAQE,IAAR,IAAgB,EAA7B;AACD;;;8BAESC,S,EAA4B;AACpC,UAAIA,aAAa,KAAKd,QAAtB,EAAgC;AAC9B,eAAO,KAAKe,UAAL,CAAgBD,SAAhB,CAAP;AACD;AACD;AACA,WAAKd,QAAL,CAAcc,SAAd,IAA2B,CAA3B;AACA,WAAKxB,QAAL,CAAc0B,mBAAd,CAAkCC,SAAlC,CAA4C,CAA5C;AACA,aAAO,KAAP;AACD;;;+BAEUH,S,EAA4B;AACrC,UAAMI,UAAU,KAAKlB,QAAL,CAAcc,SAAd,KAA4B,CAA5C;AACA,UAAII,UAAUpC,WAAd,EAA2B;AACzB,aAAKQ,QAAL,CAAc0B,mBAAd,CAAkCC,SAAlC,CAA4C,CAA5C;AACA,eAAO,KAAP;AACD;AACD,WAAKjB,QAAL,CAAcc,SAAd,IAA2BI,UAAUpC,WAArC;AACA,aAAO,IAAP;AACD;;;sCAEiB;AAChB,UAAI,CAAC,KAAK8B,KAAV,EAAiB;AACf,aAAKzB,OAAL,CAAagC,KAAb;AACA;AACD;AACD,UAAMC,OAAOC,OAAOD,IAAP,CAAY,KAAKpB,QAAjB,CAAb;AACA,UAAIoB,KAAKE,MAAL,KAAgB,CAApB,EAAuB;AACrB;AACA;AACD;AACD,WAAKC,aAAL,CAAmBH,IAAnB;AACD;;;sCAEiBI,e,EAAwC;AAAA;;AACxDA,sBAAgBC,OAAhB,CAAwB,aAAK;AAC3B,cAAKzB,QAAL,CAAc0B,EAAEZ,SAAhB,IAA6B,MAAKd,QAAL,CAAc0B,EAAEZ,SAAhB,IAA6BY,EAAEC,OAA5D;AACD,OAFD;AAGD;;;kCAEaC,U,EAAiB;AAAA;;AAC7B,UAAM5C,cAAsB6C,mBAAmB,KAAK3C,YAAxB,CAA5B;AACA,UAAM2B,OAAegB,mBAAmB,KAAKjB,KAAxB,CAArB;AACA,UAAMkB,MAAcF,WAAWG,GAAX,CAAeF,kBAAf,EAAmCG,IAAnC,CAAwC,cAAxC,CAApB;AACA,UAAMC,4BAAkCjD,WAAlC,cAAsD6B,IAAtD,oBAAyEiB,GAA/E;;AAEA,UAAMI,UAAoB,SAApBA,OAAoB,OAAQ;AAChC,eAAKC,oBAAL,CAA0BC,IAA1B;AACD,OAFD;AAGA,UAAMjB,QAAkB,SAAlBA,KAAkB,MAAO;AAC7B,eAAKhC,OAAL,CAAagC,KAAb,iCAAiDkB,GAAjD;AACA,eAAK/C,QAAL,CAAcgD,sBAAd,CAAqCrB,SAArC,CAA+C,CAA/C;AACD,OAHD;AAIAsB,qBAAMC,OAAN,CAAc,KAAK5C,KAAnB,EAA0B,KAAKE,KAA/B,EAAsCmC,GAAtC,EAA2CC,OAA3C,EAAoDf,KAApD;AACD;;;yCAEoBiB,I,EAAc;AACjC,UAAIZ,wBAAJ;AACA,UAAI;AACFA,0BAAkBiB,KAAKC,KAAL,CAAWN,IAAX,CAAlB;AACD,OAFD,CAEE,OAAOjB,KAAP,EAAc;AACd,aAAKhC,OAAL,CAAagC,KAAb,wCAAwDA,KAAxD;AACA,aAAK7B,QAAL,CAAcgD,sBAAd,CAAqCrB,SAArC,CAA+C,CAA/C;AACA;AACD;AACD,UAAI;AACF,aAAK0B,iBAAL,CAAuBnB,gBAAgBoB,QAAvC;AACA,aAAKtD,QAAL,CAAcuD,sBAAd,CAAqC5B,SAArC,CAA+C,CAA/C;AACD,OAHD,CAGE,OAAOE,KAAP,EAAc;AACd,aAAKhC,OAAL,CAAagC,KAAb,iCAAiDA,KAAjD;AACA,aAAK7B,QAAL,CAAcgD,sBAAd,CAAqCrB,SAArC,CAA+C,CAA/C;AACA;AACD;AACD,UAAI,KAAKhB,gBAAT,EAA2B;AACzB,aAAKA,gBAAL,CAAsB,IAAtB;AACD;AACF;;;0BAEK6C,Q,EAA2B;AAC/BC,mBAAa,KAAK5C,0BAAlB;AACA6C,oBAAc,KAAKvC,sBAAnB;;AAEA,UAAIqC,QAAJ,EAAc;AACZA;AACD;AACF;;;;;;kBA9IkB/D,e", "file": "remote_throttler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2018 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Metrics from '../metrics/metrics.js';\nimport NullLogger from '../logger.js';\nimport NoopMetricFactory from '../metrics/noop/metric_factory';\nimport Utils from '../util.js';\n\nconst DEFAULT_REFRESH_INTERVAL_MS = 5000;\nconst DEFAULT_INITIAL_DELAY_MS = 5000;\nconst DEFAULT_THROTTLER_HOST = '0.0.0.0';\nconst DEFAULT_THROTTLER_PORT = 5778;\n\n// UNIT_CREDIT is the minimum amount of credits necessary to not be throttled.\n// i.e. if currentCredits > UNIT_CREDIT, then the operation will not be throttled.\nconst UNIT_CREDIT = 1.0;\n\ntype CreditsPerOperation = { [key: string]: number };\n\nexport default class RemoteThrottler {\n  _serviceName: string;\n  _logger: Logger;\n  _metrics: Metrics;\n\n  _refreshIntervalMs: number;\n  _host: string;\n  _port: number;\n\n  _uuid: string;\n  _credits: CreditsPerOperation;\n\n  _initialDelayTimeoutHandle: any;\n  _refreshIntervalHandle: any;\n  _onCreditsUpdate: ?Function;\n\n  /**\n   * Creates a RemoteThrottler that fetches credits remotely from jaeger-agent.\n   *\n   * @param {string} [serviceName] - name of the current service / application, same as given to Tracer\n   * @param {object} [options] - optional settings\n   * @param {object} [options.logger] - optional logger, see _flow/logger.js\n   * @param {object} [options.metrics] - instance of Metrics object\n   * @param {number} [options.refreshIntervalMs] - interval in milliseconds that determines how often credits\n   * are fetched\n   * @param {number} [options.initialDelayMs] - interval in milliseconds that determines how soon after initialization\n   * credits are first fetched\n   * @param {string} [options.host] - host for jaeger-agent, defaults to 'localhost'\n   * @param {number} [options.port] - port for jaeger-agent for /credits endpoint\n   * @param {function} [options.onCreditsUpdate] - callback function once credits are updated. Used for testing.\n   */\n  constructor(serviceName: string, options: any = {}) {\n    this._serviceName = serviceName;\n    this._logger = options.logger || new NullLogger();\n    this._metrics = options.metrics || new Metrics(new NoopMetricFactory());\n    this._refreshIntervalMs = options.refreshIntervalMs || DEFAULT_REFRESH_INTERVAL_MS;\n    this._host = options.host || DEFAULT_THROTTLER_HOST;\n    this._port = options.port || DEFAULT_THROTTLER_PORT;\n\n    this._credits = {};\n    this._onCreditsUpdate = options.onCreditsUpdate;\n\n    this._initialDelayTimeoutHandle = setTimeout(\n      this._afterInitialDelay.bind(this),\n      options.initialDelayMs || DEFAULT_INITIAL_DELAY_MS\n    );\n  }\n\n  _afterInitialDelay(): void {\n    this._refreshCredits();\n    this._refreshIntervalHandle = setInterval(this._refreshCredits.bind(this), this._refreshIntervalMs);\n    this._initialDelayTimeoutHandle = null;\n  }\n\n  setProcess(process: Process): void {\n    this._uuid = process.uuid || '';\n  }\n\n  isAllowed(operation: string): boolean {\n    if (operation in this._credits) {\n      return this._isAllowed(operation);\n    }\n    // Credits for the operation will be asynchronously fetched\n    this._credits[operation] = 0;\n    this._metrics.throttledDebugSpans.increment(1);\n    return false;\n  }\n\n  _isAllowed(operation: string): boolean {\n    const credits = this._credits[operation] || 0;\n    if (credits < UNIT_CREDIT) {\n      this._metrics.throttledDebugSpans.increment(1);\n      return false;\n    }\n    this._credits[operation] = credits - UNIT_CREDIT;\n    return true;\n  }\n\n  _refreshCredits() {\n    if (!this._uuid) {\n      this._logger.error(`UUID must be set to fetch credits`);\n      return;\n    }\n    const keys = Object.keys(this._credits);\n    if (keys.length === 0) {\n      // No point fetching credits if there's no operations to fetch\n      return;\n    }\n    this._fetchCredits(keys);\n  }\n\n  _incrementCredits(creditResponses: Array<CreditResponse>) {\n    creditResponses.forEach(r => {\n      this._credits[r.operation] = this._credits[r.operation] + r.balance;\n    });\n  }\n\n  _fetchCredits(operations: any) {\n    const serviceName: string = encodeURIComponent(this._serviceName);\n    const uuid: string = encodeURIComponent(this._uuid);\n    const ops: string = operations.map(encodeURIComponent).join('&operations=');\n    const url: string = `/credits?service=${serviceName}&uuid=${uuid}&operations=${ops}`;\n\n    const success: Function = body => {\n      this._parseCreditResponse(body);\n    };\n    const error: Function = err => {\n      this._logger.error(`Error in fetching credits: ${err}.`);\n      this._metrics.throttlerUpdateFailure.increment(1);\n    };\n    Utils.httpGet(this._host, this._port, url, success, error);\n  }\n\n  _parseCreditResponse(body: string) {\n    let creditResponses;\n    try {\n      creditResponses = JSON.parse(body);\n    } catch (error) {\n      this._logger.error(`Error in parsing credit response: ${error}.`);\n      this._metrics.throttlerUpdateFailure.increment(1);\n      return;\n    }\n    try {\n      this._incrementCredits(creditResponses.balances);\n      this._metrics.throttlerUpdateSuccess.increment(1);\n    } catch (error) {\n      this._logger.error(`Error in updating credits: ${error}.`);\n      this._metrics.throttlerUpdateFailure.increment(1);\n      return;\n    }\n    if (this._onCreditsUpdate) {\n      this._onCreditsUpdate(this);\n    }\n  }\n\n  close(callback?: Function): void {\n    clearTimeout(this._initialDelayTimeoutHandle);\n    clearInterval(this._refreshIntervalHandle);\n\n    if (callback) {\n      callback();\n    }\n  }\n}\n"]}