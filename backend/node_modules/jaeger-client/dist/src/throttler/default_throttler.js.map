{"version": 3, "sources": ["../../../src/throttler/default_throttler.js"], "names": ["DefaultThrottler", "throttleAll", "_throttleAll", "operation", "process", "callback"], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;IAGqBA,gB;AAGnB,4BAAYC,WAAZ,EAAmC;AAAA;;AACjC,SAAKC,YAAL,GAAoBD,eAAe,KAAnC;AACD;;;;8BAESE,S,EAA4B;AACpC,aAAO,CAAC,KAAKD,YAAb;AACD;;;+BAEUE,O,EAAwB;AACjC;AACD;;;0BAEKC,Q,EAA2B;AAC/B,UAAIA,QAAJ,EAAc;AACZA;AACD;AACF;;;;;;kBAnBkBL,gB", "file": "default_throttler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2018 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\n/**\n * DefaultThrottler either throttles everything or nothing.\n */\nexport default class DefaultThrottler {\n  _throttleAll: boolean;\n\n  constructor(throttleAll?: boolean) {\n    this._throttleAll = throttleAll || false;\n  }\n\n  isAllowed(operation: string): boolean {\n    return !this._throttleAll;\n  }\n\n  setProcess(process: Process): void {\n    // NOP\n  }\n\n  close(callback?: Function): void {\n    if (callback) {\n      callback();\n    }\n  }\n}\n"]}