{"version": 3, "sources": ["../../../src/reporters/logging_reporter.js"], "names": ["Logging<PERSON>ep<PERSON>er", "logger", "_logger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "span", "info", "context", "toString", "name", "callback", "serviceName", "tags"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;;;;;IAEqBA,e;AAGnB,2BAAYC,MAAZ,EAA4B;AAAA;;AAC1B,SAAKC,OAAL,GAAeD,UAAU,IAAIE,gBAAJ,EAAzB;AACD;;;;2BAEMC,I,EAAkB;AACvB,WAAKF,OAAL,CAAaG,IAAb,qBAAoCD,KAAKE,OAAL,GAAeC,QAAf,EAApC;AACD;;;2BAEc;AACb,aAAO,iBAAP;AACD;;;+BAEkB;AACjB,aAAO,KAAKC,IAAL,EAAP;AACD;;;0BAEKC,Q,EAA6B;AACjC,UAAIA,QAAJ,EAAc;AACZA;AACD;AACF;;;+BAEUC,W,EAAqBC,I,EAAwB,CAAE;;;;;;kBAzBvCX,e", "file": "logging_reporter.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Span from '../span';\nimport NullLogger from '../logger';\n\nexport default class LoggingReporter implements Reporter {\n  _logger: Logger;\n\n  constructor(logger: Logger) {\n    this._logger = logger || new NullLogger();\n  }\n\n  report(span: Span): void {\n    this._logger.info(`Reporting span ${span.context().toString()}`);\n  }\n\n  name(): string {\n    return 'LoggingReporter';\n  }\n\n  toString(): string {\n    return this.name();\n  }\n\n  close(callback?: () => void): void {\n    if (callback) {\n      callback();\n    }\n  }\n\n  setProcess(serviceName: string, tags: Array<Tag>): void {}\n}\n"]}