{"version": 3, "sources": ["../../../src/reporters/sender_utils.js"], "names": ["Sender<PERSON><PERSON>s", "callback", "numSpans", "error", "t", "process", "tagMessages", "j", "tags", "length", "tag", "push", "Tag", "Process", "serviceName"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;IAEqBA,W;;;;;;;mCACGC,Q,EAA2BC,Q,EAAkBC,K,EAAgB;AACjF,UAAIF,QAAJ,EAAc;AACZA,iBAASC,QAAT,EAAmBC,KAAnB;AACD;AACF;;;2CAE6BC,C,EAAWC,O,EAAkB;AACzD,UAAMC,cAAc,EAApB;AACA,WAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIF,QAAQG,IAAR,CAAaC,MAAjC,EAAyCF,GAAzC,EAA8C;AAC5C,YAAMG,MAAML,QAAQG,IAAR,CAAaD,CAAb,CAAZ;AACAD,oBAAYK,IAAZ,CAAiB,IAAIP,EAAEQ,GAAN,CAAUF,GAAV,CAAjB;AACD;;AAED,aAAO,IAAIN,EAAES,OAAN,CAAc;AACnBC,qBAAaT,QAAQS,WADF;AAEnBN,cAAMF;AAFa,OAAd,CAAP;AAID;;;;;;kBAlBkBN,W", "file": "sender_utils.js", "sourcesContent": ["// @flow\n// Copyright (c) 2018 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport { Thrift } from 'thriftrw';\n\nexport default class SenderUtils {\n  static invokeCallback(callback?: SenderCallback, numSpans: number, error?: string) {\n    if (callback) {\n      callback(numSpans, error);\n    }\n  }\n\n  static convertProcessToThrift(t: Thrift, process: Process) {\n    const tagMessages = [];\n    for (let j = 0; j < process.tags.length; j++) {\n      const tag = process.tags[j];\n      tagMessages.push(new t.Tag(tag));\n    }\n\n    return new t.Process({\n      serviceName: process.serviceName,\n      tags: tagMessages,\n    });\n  }\n}\n"]}