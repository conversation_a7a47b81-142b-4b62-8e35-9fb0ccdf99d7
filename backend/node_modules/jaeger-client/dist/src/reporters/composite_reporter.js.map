{"version": 3, "sources": ["../../../src/reporters/composite_reporter.js"], "names": ["CompositeReporter", "reporters", "_reporters", "name", "map", "reporter", "toString", "join", "span", "for<PERSON>ach", "r", "report", "callback", "countdownCallback", "Utils", "length", "close", "serviceName", "tags", "setProcess"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;;;;;IAEqBA,iB;AAGnB,6BAAYC,SAAZ,EAAwC;AAAA;;AACtC,SAAKC,UAAL,GAAkBD,SAAlB;AACD;;;;2BAEc;AACb,aAAO,mBAAP;AACD;;;+BAEkB;AACjB,aAAU,KAAKE,IAAL,EAAV,SAAyB,KAAKD,UAAL,CAAgBE,GAAhB,CAAoB;AAAA,eAAYC,SAASC,QAAT,EAAZ;AAAA,OAApB,EAAqDC,IAArD,CAA0D,GAA1D,CAAzB;AACD;;;2BAEMC,I,EAAkB;AACvB,WAAKN,UAAL,CAAgBO,OAAhB,CAAwB,aAAK;AAC3BC,UAAEC,MAAF,CAASH,IAAT;AACD,OAFD;AAGD;;;0BAEKI,Q,EAA6B;AACjC,UAAMC,oBAAoBC,eAAMD,iBAAN,CAAwB,KAAKX,UAAL,CAAgBa,MAAxC,EAAgDH,QAAhD,CAA1B;AACA,WAAKV,UAAL,CAAgBO,OAAhB,CAAwB;AAAA,eAAKC,EAAEM,KAAF,CAAQH,iBAAR,CAAL;AAAA,OAAxB;AACD;;;+BAEUI,W,EAAqBC,I,EAAwB;AACtD,WAAKhB,UAAL,CAAgBO,OAAhB,CAAwB,aAAK;AAC3B,YAAIC,EAAES,UAAN,EAAkB;AAChBT,YAAES,UAAF,CAAaF,WAAb,EAA0BC,IAA1B;AACD;AACF,OAJD;AAKD;;;;;;kBAhCkBlB,iB", "file": "composite_reporter.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Span from '../span.js';\nimport Utils from '../util.js';\n\nexport default class CompositeReporter implements Reporter {\n  _reporters: Array<Reporter>;\n\n  constructor(reporters: Array<Reporter>) {\n    this._reporters = reporters;\n  }\n\n  name(): string {\n    return 'CompositeReporter';\n  }\n\n  toString(): string {\n    return `${this.name()}(${this._reporters.map(reporter => reporter.toString()).join(',')})`;\n  }\n\n  report(span: Span): void {\n    this._reporters.forEach(r => {\n      r.report(span);\n    });\n  }\n\n  close(callback?: () => void): void {\n    const countdownCallback = Utils.countdownCallback(this._reporters.length, callback);\n    this._reporters.forEach(r => r.close(countdownCallback));\n  }\n\n  setProcess(serviceName: string, tags: Array<Tag>): void {\n    this._reporters.forEach(r => {\n      if (r.setProcess) {\n        r.setProcess(serviceName, tags);\n      }\n    });\n  }\n}\n"]}