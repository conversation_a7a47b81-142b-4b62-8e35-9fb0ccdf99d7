{"version": 3, "sources": ["../../../src/reporters/udp_sender.js"], "names": ["HOST", "PORT", "SOCKET_TYPE", "UDP_PACKET_MAX_LENGTH", "UDPSender", "options", "_host", "host", "_port", "port", "_socketType", "socketType", "_maxPacketSize", "maxPacketSize", "_logger", "logger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_client", "dgram", "createSocket", "on", "error", "err", "_agentThrift", "Thrift", "entryPoint", "ThriftUtils", "buildAgentThriftPath", "allowOptionalArguments", "allowFilesystemAccess", "_jaegerThrift", "source", "loadJaegerThriftDefinition", "_totalSpanBytes", "batch", "Agent", "emitBatch", "argumentsMessageRW", "byteLength", "_convertBatchToThriftMessage", "length", "span", "Span", "rw", "process", "_process", "_batch", "spans", "_thriftProcessMessage", "Sender<PERSON><PERSON>s", "convertProcessToThrift", "_emitSpanBatchOverhead", "_calcBatchSize", "_maxSpanBytes", "callback", "_calcSpanSize", "invokeCallback", "spanSize", "push", "flush", "numSpans", "bufferLen", "thriftBuffer", "Utils", "new<PERSON>uffer", "writeResult", "writeInto", "_reset", "send", "sent", "offset", "spanMessages", "i", "ArgumentsMessage", "version", "id", "body", "<PERSON><PERSON>", "close"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;AACA;;;;AACA;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;;;;;AAEA,IAAMA,OAAO,WAAb;AACA,IAAMC,OAAO,IAAb;AACA,IAAMC,cAAc,MAApB;AACA,IAAMC,wBAAwB,KAA9B;;IAEqBC,S;AAcM;;AAEzB,uBAA+B;AAAA;;AAAA,QAAnBC,OAAmB,uEAAJ,EAAI;;AAAA;;AAC7B,SAAKC,KAAL,GAAaD,QAAQE,IAAR,IAAgBP,IAA7B;AACA,SAAKQ,KAAL,GAAaH,QAAQI,IAAR,IAAgBR,IAA7B;AACA,SAAKS,WAAL,GAAmBL,QAAQM,UAAR,IAAsBT,WAAzC;AACA,SAAKU,cAAL,GAAsBP,QAAQQ,aAAR,IAAyBV,qBAA/C;AACA,SAAKW,OAAL,GAAeT,QAAQU,MAAR,IAAkB,IAAIC,gBAAJ,EAAjC;AACA,SAAKC,OAAL,GAAeC,gBAAMC,YAAN,CAAmB,KAAKT,WAAxB,CAAf;AACA,SAAKO,OAAL,CAAaG,EAAb,CAAgB,OAAhB,EAAyB,eAAO;AAC9B,YAAKN,OAAL,CAAaO,KAAb,oCAAoDC,GAApD;AACD,KAFD;AAGA,SAAKC,YAAL,GAAoB,IAAIC,gBAAJ,CAAW;AAC7BC,kBAAYC,iBAAYC,oBAAZ,EADiB;AAE7BC,8BAAwB,IAFK;AAG7BC,6BAAuB;AAHM,KAAX,CAApB;AAKA,SAAKC,aAAL,GAAqB,IAAIN,gBAAJ,CAAW;AAC9BO,cAAQL,iBAAYM,0BAAZ,EADsB;AAE9BJ,8BAAwB;AAFM,KAAX,CAArB;AAIA,SAAKK,eAAL,GAAuB,CAAvB;AACD,G,CAvBsB;;;;;mCAyBRC,K,EAAc;AAC3B,aAAO,KAAKX,YAAL,CAAkBY,KAAlB,CAAwBC,SAAxB,CAAkCC,kBAAlC,CAAqDC,UAArD,CACL,KAAKC,4BAAL,EADK,EAELC,MAFF;AAGD;;;kCAEaC,I,EAAyB;AACrC,aAAO,KAAKX,aAAL,CAAmBY,IAAnB,CAAwBC,EAAxB,CAA2BL,UAA3B,CAAsC,IAAI,KAAKR,aAAL,CAAmBY,IAAvB,CAA4BD,IAA5B,CAAtC,CAAP;AACD;;;+BAEUG,O,EAAwB;AACjC;AACA;AACA;AACA,WAAKC,QAAL,GAAgBD,OAAhB;AACA,WAAKE,MAAL,GAAc;AACZF,iBAAS,KAAKC,QADF;AAEZE,eAAO;AAFK,OAAd;;AAKA,WAAKC,qBAAL,GAA6BC,uBAAYC,sBAAZ,CAAmC,KAAKpB,aAAxC,EAAuDc,OAAvD,CAA7B;AACA,WAAKO,sBAAL,GAA8B,KAAKC,cAAL,CAAoB,KAAKN,MAAzB,CAA9B;AACA,WAAKO,aAAL,GAAqB,KAAKzC,cAAL,GAAsB,KAAKuC,sBAAhD;AACD;;;2BAEMV,I,EAAWa,Q,EAAiC;AAAA;;AAAA,2BACzB,KAAKC,aAAL,CAAmBd,IAAnB,CADyB;AAAA,UACzCnB,GADyC,kBACzCA,GADyC;AAAA,UACpCkB,MADoC,kBACpCA,MADoC;;AAEjD,UAAIlB,GAAJ,EAAS;AACP2B,+BAAYO,cAAZ,CAA2BF,QAA3B,EAAqC,CAArC,wCAA4EhC,GAA5E;AACA;AACD;AACD,UAAMmC,WAAWjB,MAAjB;AACA,UAAIiB,WAAW,KAAKJ,aAApB,EAAmC;AACjCJ,+BAAYO,cAAZ,CACEF,QADF,EAEE,CAFF,iBAGeG,QAHf,oCAGsD,KAAKJ,aAH3D;AAKA;AACD;;AAED,UAAI,KAAKpB,eAAL,GAAuBwB,QAAvB,IAAmC,KAAKJ,aAA5C,EAA2D;AACzD,aAAKP,MAAL,CAAYC,KAAZ,CAAkBW,IAAlB,CAAuBjB,IAAvB;AACA,aAAKR,eAAL,IAAwBwB,QAAxB;AACA,YAAI,KAAKxB,eAAL,GAAuB,KAAKoB,aAAhC,EAA+C;AAC7C;AACAJ,iCAAYO,cAAZ,CAA2BF,QAA3B,EAAqC,CAArC;AACA;AACD;AACD;AACA,aAAKK,KAAL,CAAWL,QAAX;AACA;AACD;;AAED,WAAKK,KAAL,CAAW,UAACC,QAAD,EAAmBtC,GAAnB,EAAoC;AAC7C;AACA,eAAKwB,MAAL,CAAYC,KAAZ,CAAkBW,IAAlB,CAAuBjB,IAAvB;AACA,eAAKR,eAAL,IAAwBwB,QAAxB;AACAR,+BAAYO,cAAZ,CAA2BF,QAA3B,EAAqCM,QAArC,EAA+CtC,GAA/C;AACD,OALD;AAMD;;;0BAEKgC,Q,EAAiC;AACrC,UAAMM,WAAW,KAAKd,MAAL,CAAYC,KAAZ,CAAkBP,MAAnC;AACA,UAAI,CAACoB,QAAL,EAAe;AACbX,+BAAYO,cAAZ,CAA2BF,QAA3B,EAAqC,CAArC;AACA;AACD;;AAED,UAAMO,YAAY,KAAK5B,eAAL,GAAuB,KAAKkB,sBAA9C;AACA,UAAMW,eAAeC,eAAMC,SAAN,CAAgBH,SAAhB,CAArB;AACA,UAAMI,cAAc,KAAK1C,YAAL,CAAkBY,KAAlB,CAAwBC,SAAxB,CAAkCC,kBAAlC,CAAqD6B,SAArD,CAClB,KAAK3B,4BAAL,EADkB,EAElBuB,YAFkB,EAGlB,CAHkB,CAApB;AAKA,WAAKK,MAAL;;AAEA,UAAIF,YAAY3C,GAAhB,EAAqB;AACnB2B,+BAAYO,cAAZ,CAA2BF,QAA3B,EAAqCM,QAArC,oCAA+EK,YAAY3C,GAA3F;AACA;AACD;;AAED;AACA;AACA,WAAKL,OAAL,CAAamD,IAAb,CAAkBN,YAAlB,EAAgC,CAAhC,EAAmCA,aAAatB,MAAhD,EAAwD,KAAKhC,KAA7D,EAAoE,KAAKF,KAAzE,EAAgF,UAACgB,GAAD,EAAM+C,IAAN,EAAe;AAC7F,YAAI/C,GAAJ,EAAS;AACP,cAAMD,QACJC,0CACiCA,GADjC,uBACsD2C,YAAYK,MADlE,sBACyFD,IAF3F;AAGApB,iCAAYO,cAAZ,CAA2BF,QAA3B,EAAqCM,QAArC,EAA+CvC,KAA/C;AACD,SALD,MAKO;AACL4B,iCAAYO,cAAZ,CAA2BF,QAA3B,EAAqCM,QAArC;AACD;AACF,OATD;AAUD;;;mDAE8B;AAC7B,UAAMW,eAAe,EAArB;AACA,WAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAI,KAAK1B,MAAL,CAAYC,KAAZ,CAAkBP,MAAtC,EAA8CgC,GAA9C,EAAmD;AACjD,YAAM/B,OAAO,KAAKK,MAAL,CAAYC,KAAZ,CAAkByB,CAAlB,CAAb;AACAD,qBAAab,IAAb,CAAkB,IAAI,KAAK5B,aAAL,CAAmBY,IAAvB,CAA4BD,IAA5B,CAAlB;AACD;;AAED,aAAO,IAAI,KAAKlB,YAAL,CAAkBY,KAAlB,CAAwBC,SAAxB,CAAkCqC,gBAAtC,CAAuD;AAC5DC,iBAAS,CADmD;AAE5DC,YAAI,CAFwD;AAG5DC,cAAM;AACJ1C,iBAAO,IAAI,KAAKJ,aAAL,CAAmB+C,KAAvB,CAA6B;AAClCjC,qBAAS,KAAKI,qBADoB;AAElCD,mBAAOwB;AAF2B,WAA7B;AADH;AAHsD,OAAvD,CAAP;AAUD;;;6BAEQ;AACP,WAAKzB,MAAL,CAAYC,KAAZ,GAAoB,EAApB;AACA,WAAKd,eAAL,GAAuB,CAAvB;AACD;;;4BAEa;AACZ,WAAKhB,OAAL,CAAa6D,KAAb;AACD;;;;;;kBAjKkB1E,S", "file": "udp_sender.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport dgram from 'dgram';\nimport fs from 'fs';\nimport path from 'path';\nimport { Thrift } from 'thriftrw';\nimport NullLogger from '../logger';\nimport SenderUtils from './sender_utils';\nimport ThriftUtils from '../thrift';\nimport Utils from '../util';\n\nconst HOST = 'localhost';\nconst PORT = 6832;\nconst SOCKET_TYPE = 'udp4';\nconst UDP_PACKET_MAX_LENGTH = 65000;\n\nexport default class UDPSender {\n  _host: string;\n  _port: number;\n  _socketType: string;\n  _maxPacketSize: number;\n  _process: Process;\n  _emitSpanBatchOverhead: number;\n  _logger: Logger;\n  _client: dgram$Socket;\n  _agentThrift: Thrift;\n  _jaegerThrift: Thrift;\n  _batch: Batch;\n  _thriftProcessMessage: any;\n  _maxSpanBytes: number; // maxPacketSize - (batch + tags overhead)\n  _totalSpanBytes: number; // size of currently batched spans as Thrift bytes\n\n  constructor(options: any = {}) {\n    this._host = options.host || HOST;\n    this._port = options.port || PORT;\n    this._socketType = options.socketType || SOCKET_TYPE;\n    this._maxPacketSize = options.maxPacketSize || UDP_PACKET_MAX_LENGTH;\n    this._logger = options.logger || new NullLogger();\n    this._client = dgram.createSocket(this._socketType);\n    this._client.on('error', err => {\n      this._logger.error(`error sending spans over UDP: ${err}`);\n    });\n    this._agentThrift = new Thrift({\n      entryPoint: ThriftUtils.buildAgentThriftPath(),\n      allowOptionalArguments: true,\n      allowFilesystemAccess: true,\n    });\n    this._jaegerThrift = new Thrift({\n      source: ThriftUtils.loadJaegerThriftDefinition(),\n      allowOptionalArguments: true,\n    });\n    this._totalSpanBytes = 0;\n  }\n\n  _calcBatchSize(batch: Batch) {\n    return this._agentThrift.Agent.emitBatch.argumentsMessageRW.byteLength(\n      this._convertBatchToThriftMessage()\n    ).length;\n  }\n\n  _calcSpanSize(span: any): LengthResult {\n    return this._jaegerThrift.Span.rw.byteLength(new this._jaegerThrift.Span(span));\n  }\n\n  setProcess(process: Process): void {\n    // This function is only called once during reporter construction, and thus will\n    // give us the length of the batch before any spans have been added to the span\n    // list in batch.\n    this._process = process;\n    this._batch = {\n      process: this._process,\n      spans: [],\n    };\n\n    this._thriftProcessMessage = SenderUtils.convertProcessToThrift(this._jaegerThrift, process);\n    this._emitSpanBatchOverhead = this._calcBatchSize(this._batch);\n    this._maxSpanBytes = this._maxPacketSize - this._emitSpanBatchOverhead;\n  }\n\n  append(span: any, callback?: SenderCallback): void {\n    const { err, length } = this._calcSpanSize(span);\n    if (err) {\n      SenderUtils.invokeCallback(callback, 1, `error converting span to Thrift: ${err}`);\n      return;\n    }\n    const spanSize = length;\n    if (spanSize > this._maxSpanBytes) {\n      SenderUtils.invokeCallback(\n        callback,\n        1,\n        `span size ${spanSize} is larger than maxSpanSize ${this._maxSpanBytes}`\n      );\n      return;\n    }\n\n    if (this._totalSpanBytes + spanSize <= this._maxSpanBytes) {\n      this._batch.spans.push(span);\n      this._totalSpanBytes += spanSize;\n      if (this._totalSpanBytes < this._maxSpanBytes) {\n        // still have space in the buffer, don't flush it yet\n        SenderUtils.invokeCallback(callback, 0);\n        return;\n      }\n      // buffer size === this._maxSpanBytes\n      this.flush(callback);\n      return;\n    }\n\n    this.flush((numSpans: number, err?: string) => {\n      // TODO theoretically we can have buffer overflow here too, if many spans were appended during flush()\n      this._batch.spans.push(span);\n      this._totalSpanBytes += spanSize;\n      SenderUtils.invokeCallback(callback, numSpans, err);\n    });\n  }\n\n  flush(callback?: SenderCallback): void {\n    const numSpans = this._batch.spans.length;\n    if (!numSpans) {\n      SenderUtils.invokeCallback(callback, 0);\n      return;\n    }\n\n    const bufferLen = this._totalSpanBytes + this._emitSpanBatchOverhead;\n    const thriftBuffer = Utils.newBuffer(bufferLen);\n    const writeResult = this._agentThrift.Agent.emitBatch.argumentsMessageRW.writeInto(\n      this._convertBatchToThriftMessage(),\n      thriftBuffer,\n      0\n    );\n    this._reset();\n\n    if (writeResult.err) {\n      SenderUtils.invokeCallback(callback, numSpans, `error writing Thrift object: ${writeResult.err}`);\n      return;\n    }\n\n    // Having the error callback here does not prevent uncaught exception from being thrown,\n    // that's why in the constructor we also add a general on('error') handler.\n    this._client.send(thriftBuffer, 0, thriftBuffer.length, this._port, this._host, (err, sent) => {\n      if (err) {\n        const error: string =\n          err &&\n          `error sending spans over UDP: ${err}, packet size: ${writeResult.offset}, bytes sent: ${sent}`;\n        SenderUtils.invokeCallback(callback, numSpans, error);\n      } else {\n        SenderUtils.invokeCallback(callback, numSpans);\n      }\n    });\n  }\n\n  _convertBatchToThriftMessage() {\n    const spanMessages = [];\n    for (let i = 0; i < this._batch.spans.length; i++) {\n      const span = this._batch.spans[i];\n      spanMessages.push(new this._jaegerThrift.Span(span));\n    }\n\n    return new this._agentThrift.Agent.emitBatch.ArgumentsMessage({\n      version: 1,\n      id: 0,\n      body: {\n        batch: new this._jaegerThrift.Batch({\n          process: this._thriftProcessMessage,\n          spans: spanMessages,\n        }),\n      },\n    });\n  }\n\n  _reset() {\n    this._batch.spans = [];\n    this._totalSpanBytes = 0;\n  }\n\n  close(): void {\n    this._client.close();\n  }\n}\n"]}