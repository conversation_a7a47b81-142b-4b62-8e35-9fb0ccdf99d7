{"version": 3, "sources": ["../../../src/reporters/noop_reporter.js"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "span", "callback", "serviceName", "tags"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;IAEqBA,Y;;;;;;;2BACJ;AACb,aAAO,cAAP;AACD;;;+BAEkB;AACjB,aAAO,KAAKC,IAAL,EAAP;AACD;;;2BAEMC,I,EAAkB,CAAE;;;0BAErBC,Q,EAA6B;AACjC,UAAIA,QAAJ,EAAc;AACZA;AACD;AACF;;;+BAEUC,W,EAAqBC,I,EAAwB,CAAE;;;;;;kBAjBvCL,Y", "file": "noop_reporter.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Span from '../span';\n\nexport default class NoopReporter implements Reporter {\n  name(): string {\n    return 'NoopReporter';\n  }\n\n  toString(): string {\n    return this.name();\n  }\n\n  report(span: Span): void {}\n\n  close(callback?: () => void): void {\n    if (callback) {\n      callback();\n    }\n  }\n\n  setProcess(serviceName: string, tags: Array<Tag>): void {}\n}\n"]}