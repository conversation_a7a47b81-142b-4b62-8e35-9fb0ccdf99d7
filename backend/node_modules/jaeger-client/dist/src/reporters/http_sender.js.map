{"version": 3, "sources": ["../../../src/reporters/http_sender.js"], "names": ["URL", "DEFAULT_PATH", "DEFAULT_PORT", "DEFAULT_TIMEOUT_MS", "DEFAULT_MAX_SPAN_BATCH_SIZE", "HTTPSender", "options", "_url", "parse", "endpoint", "_username", "username", "_password", "password", "_timeoutMs", "timeoutMs", "timeoutMS", "_httpAgent", "protocol", "https", "Agent", "keepAlive", "http", "_maxSpanBatchSize", "maxSpanBatchSize", "_logger", "logger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_jaegerThrift", "Thrift", "source", "ThriftUtils", "loadJaegerThriftDefinition", "allowOptionalArguments", "_httpOptions", "hostname", "port", "path", "pathname", "method", "auth", "undefined", "headers", "Connection", "agent", "timeout", "process", "_batch", "<PERSON><PERSON>", "Sender<PERSON><PERSON>s", "convertProcessToThrift", "spans", "span", "callback", "push", "Span", "length", "flush", "invokeCallback", "numSpans", "result", "rw", "<PERSON><PERSON><PERSON><PERSON>", "_reset", "err", "requester", "request", "req", "resp", "resume", "error", "statusCode", "on", "write", "value", "end", "destroy"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;IAAYA,G;;AACZ;;AAEA;;;;AACA;;;;AACA;;;;;;;;;;AAEA,IAAMC,eAAe,aAArB;AACA,IAAMC,eAAe,KAArB;AACA,IAAMC,qBAAqB,IAA3B;AACA,IAAMC,8BAA8B,GAApC;;IAEqBC,U;AAenB,wBAA+B;AAAA,QAAnBC,OAAmB,uEAAJ,EAAI;;AAAA;;AAC7B,SAAKC,IAAL,GAAYP,IAAIQ,KAAJ,CAAUF,QAAQG,QAAlB,CAAZ;AACA,SAAKC,SAAL,GAAiBJ,QAAQK,QAAzB;AACA,SAAKC,SAAL,GAAiBN,QAAQO,QAAzB;AACA,SAAKC,UAAL,GAAkBR,QAAQS,SAAR,IAAqBT,QAAQU,SAA7B,IAA0Cb,kBAA5D;AACA,SAAKc,UAAL,GACE,KAAKV,IAAL,CAAUW,QAAV,KAAuB,QAAvB,GACI,IAAIC,gBAAMC,KAAV,CAAgB,EAAEC,WAAW,IAAb,EAAhB,CADJ,GAEI,IAAIC,eAAKF,KAAT,CAAe,EAAEC,WAAW,IAAb,EAAf,CAHN;;AAKA,SAAKE,iBAAL,GAAyBjB,QAAQkB,gBAAR,IAA4BpB,2BAArD;;AAEA,SAAKqB,OAAL,GAAenB,QAAQoB,MAAR,IAAkB,IAAIC,gBAAJ,EAAjC;AACA,SAAKC,aAAL,GAAqB,IAAIC,gBAAJ,CAAW;AAC9BC,cAAQC,iBAAYC,0BAAZ,EADsB;AAE9BC,8BAAwB;AAFM,KAAX,CAArB;;AAKA,SAAKC,YAAL,GAAoB;AAClBhB,gBAAU,KAAKX,IAAL,CAAUW,QADF;AAElBiB,gBAAU,KAAK5B,IAAL,CAAU4B,QAFF;AAGlBC,YAAM,KAAK7B,IAAL,CAAU6B,IAHE;AAIlBC,YAAM,KAAK9B,IAAL,CAAU+B,QAJE;AAKlBC,cAAQ,MALU;AAMlBC,YAAM,KAAK9B,SAAL,IAAkB,KAAKE,SAAvB,GAAsC,KAAKF,SAA3C,SAAwD,KAAKE,SAA7D,GAA2E6B,SAN/D;AAOlBC,eAAS;AACP,wBAAgB,sBADT;AAEPC,oBAAY;AAFL,OAPS;AAWlBC,aAAO,KAAK3B,UAXM;AAYlB4B,eAAS,KAAK/B;AAZI,KAApB;AAcD;;;;+BAEUgC,O,EAAwB;AACjC;AACA;AACA,WAAKC,MAAL,GAAc,IAAI,KAAKnB,aAAL,CAAmBoB,KAAvB,CAA6B;AACzCF,iBAASG,uBAAYC,sBAAZ,CAAmC,KAAKtB,aAAxC,EAAuDkB,OAAvD,CADgC;AAEzCK,eAAO;AAFkC,OAA7B,CAAd;AAID;;;2BAEMC,I,EAAWC,Q,EAAiC;AACjD,WAAKN,MAAL,CAAYI,KAAZ,CAAkBG,IAAlB,CAAuB,IAAI,KAAK1B,aAAL,CAAmB2B,IAAvB,CAA4BH,IAA5B,CAAvB;;AAEA,UAAI,KAAKL,MAAL,CAAYI,KAAZ,CAAkBK,MAAlB,IAA4B,KAAKjC,iBAArC,EAAwD;AACtD,aAAKkC,KAAL,CAAWJ,QAAX;AACA;AACD;AACDJ,6BAAYS,cAAZ,CAA2BL,QAA3B,EAAqC,CAArC;AACD;;;0BAEKA,Q,EAAiC;AAAA;;AACrC,UAAMM,WAAW,KAAKZ,MAAL,CAAYI,KAAZ,CAAkBK,MAAnC;AACA,UAAI,CAACG,QAAL,EAAe;AACbV,+BAAYS,cAAZ,CAA2BL,QAA3B,EAAqC,CAArC;AACA;AACD;;AAED,UAAMO,SAAS,KAAKhC,aAAL,CAAmBoB,KAAnB,CAAyBa,EAAzB,CAA4BC,QAA5B,CAAqC,KAAKf,MAA1C,CAAf;AACA,WAAKgB,MAAL,GARqC,CAQtB;;AAEf,UAAIH,OAAOI,GAAX,EAAgB;AACdf,+BAAYS,cAAZ,CAA2BL,QAA3B,EAAqCM,QAArC,oCAA+EC,OAAOI,GAAtF;AACA;AACD;;AAED,UAAMC,YAAY,KAAK1D,IAAL,CAAUW,QAAV,KAAuB,QAAvB,GAAkCC,gBAAM+C,OAAxC,GAAkD5C,eAAK4C,OAAzE;;AAEA,UAAMC,MAAMF,UAAU,KAAK/B,YAAf,EAA6B,gBAAQ;AAC/C;AACAkC,aAAKC,MAAL;;AAEA,YAAIC,cAAJ;AACA,YAAIF,KAAKG,UAAL,IAAmB,GAAvB,EAA4B;AAC1BD,iFAAqEF,KAAKG,UAA1E;AACA,gBAAK9C,OAAL,CAAa6C,KAAb,CAAmBA,KAAnB;AACD;;AAEDrB,+BAAYS,cAAZ,CAA2BL,QAA3B,EAAqCM,QAArC,EAA+CW,KAA/C;AACD,OAXW,CAAZ;;AAaAH,UAAIK,EAAJ,CAAO,OAAP,EAAgB,eAAO;AACrB,YAAMF,4CAAkDN,GAAxD;AACA,cAAKvC,OAAL,CAAa6C,KAAb,CAAmBA,KAAnB;AACArB,+BAAYS,cAAZ,CAA2BL,QAA3B,EAAqCM,QAArC,EAA+CW,KAA/C;AACD,OAJD;AAKAH,UAAIM,KAAJ,CAAUb,OAAOc,KAAjB;AACAP,UAAIQ,GAAJ;AACD;;;6BAEQ;AACP,WAAK5B,MAAL,CAAYI,KAAZ,GAAoB,EAApB;AACD;;;4BAEa;AACZ;AACA,UAAI,KAAKlC,UAAL,CAAgB2D,OAApB,EAA6B;AAC3B,aAAK3D,UAAL,CAAgB2D,OAAhB;AACD;AACF;;;;;;kBApHkBvE,U", "file": "http_sender.js", "sourcesContent": ["// @flow\n// Copyright (c) 2018 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport fs from 'fs';\nimport http from 'http';\nimport https from 'https';\nimport path from 'path';\nimport * as URL from 'url';\nimport { Thrift } from 'thriftrw';\n\nimport NullLogger from '../logger.js';\nimport SenderUtils from './sender_utils.js';\nimport ThriftUtils from '../thrift.js';\n\nconst DEFAULT_PATH = '/api/traces';\nconst DEFAULT_PORT = 14268;\nconst DEFAULT_TIMEOUT_MS = 5000;\nconst DEFAULT_MAX_SPAN_BATCH_SIZE = 100;\n\nexport default class HTTPSender {\n  _url: Object;\n  _username: string;\n  _password: string;\n  _emitSpanBatchOverhead: number;\n  _timeoutMs: number;\n  _httpAgent: http$Agent;\n  _logger: Logger;\n  _jaegerThrift: Thrift;\n  _process: Process;\n  _batch: Batch;\n  _thriftProcessMessage: any;\n  _maxSpanBatchSize: number;\n  _httpOptions: Object;\n\n  constructor(options: any = {}) {\n    this._url = URL.parse(options.endpoint);\n    this._username = options.username;\n    this._password = options.password;\n    this._timeoutMs = options.timeoutMs || options.timeoutMS || DEFAULT_TIMEOUT_MS;\n    this._httpAgent =\n      this._url.protocol === 'https:'\n        ? new https.Agent({ keepAlive: true })\n        : new http.Agent({ keepAlive: true });\n\n    this._maxSpanBatchSize = options.maxSpanBatchSize || DEFAULT_MAX_SPAN_BATCH_SIZE;\n\n    this._logger = options.logger || new NullLogger();\n    this._jaegerThrift = new Thrift({\n      source: ThriftUtils.loadJaegerThriftDefinition(),\n      allowOptionalArguments: true,\n    });\n\n    this._httpOptions = {\n      protocol: this._url.protocol,\n      hostname: this._url.hostname,\n      port: this._url.port,\n      path: this._url.pathname,\n      method: 'POST',\n      auth: this._username && this._password ? `${this._username}:${this._password}` : undefined,\n      headers: {\n        'Content-Type': 'application/x-thrift',\n        Connection: 'keep-alive',\n      },\n      agent: this._httpAgent,\n      timeout: this._timeoutMs,\n    };\n  }\n\n  setProcess(process: Process): void {\n    // Go ahead and initialize the Thrift batch that we will reuse for each\n    // flush.\n    this._batch = new this._jaegerThrift.Batch({\n      process: SenderUtils.convertProcessToThrift(this._jaegerThrift, process),\n      spans: [],\n    });\n  }\n\n  append(span: any, callback?: SenderCallback): void {\n    this._batch.spans.push(new this._jaegerThrift.Span(span));\n\n    if (this._batch.spans.length >= this._maxSpanBatchSize) {\n      this.flush(callback);\n      return;\n    }\n    SenderUtils.invokeCallback(callback, 0);\n  }\n\n  flush(callback?: SenderCallback): void {\n    const numSpans = this._batch.spans.length;\n    if (!numSpans) {\n      SenderUtils.invokeCallback(callback, 0);\n      return;\n    }\n\n    const result = this._jaegerThrift.Batch.rw.toBuffer(this._batch);\n    this._reset(); // clear buffer for new spans, even if Thrift conversion fails\n\n    if (result.err) {\n      SenderUtils.invokeCallback(callback, numSpans, `Error encoding Thrift batch: ${result.err}`);\n      return;\n    }\n\n    const requester = this._url.protocol === 'https:' ? https.request : http.request;\n\n    const req = requester(this._httpOptions, resp => {\n      // consume response data to free up memory\n      resp.resume();\n\n      let error;\n      if (resp.statusCode >= 400) {\n        error = `error sending spans over HTTP: server responded with HTTP ${resp.statusCode}`;\n        this._logger.error(error);\n      }\n\n      SenderUtils.invokeCallback(callback, numSpans, error);\n    });\n\n    req.on('error', err => {\n      const error: string = `error sending spans over HTTP: ${err}`;\n      this._logger.error(error);\n      SenderUtils.invokeCallback(callback, numSpans, error);\n    });\n    req.write(result.value);\n    req.end();\n  }\n\n  _reset() {\n    this._batch.spans = [];\n  }\n\n  close(): void {\n    // Older node versions don't have this.\n    if (this._httpAgent.destroy) {\n      this._httpAgent.destroy();\n    }\n  }\n}\n"]}