{"version": 3, "sources": ["../../../src/reporters/remote_reporter.js"], "names": ["DEFAULT_BUFFER_FLUSH_INTERVAL_MILLIS", "RemoteReport<PERSON>", "sender", "options", "_appendCallback", "numSpans", "err", "_logger", "error", "_metrics", "reporterDropped", "increment", "reporterSuccess", "Error", "_bufferFlushInterval", "bufferFlushInterval", "logger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_sender", "_intervalHandle", "setInterval", "flush", "metrics", "Metrics", "NoopMetricFactory", "name", "span", "thriftSpan", "ThriftUtils", "spanToThrift", "append", "callback", "_process", "undefined", "_invokeCallback", "reporterFailure", "clearInterval", "close", "serviceName", "tags", "getThriftTags", "setProcess"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;AACA;;;;AACA;;;;;;;;AAEA,IAAMA,uCAAuC,IAA7C;;IAEqBC,c;AAQnB,0BAAYC,MAAZ,EAA+C;AAAA;;AAAA,QAAnBC,OAAmB,uEAAJ,EAAI;;AAAA;;AAAA,SA2B/CC,eA3B+C,GA2B7B,UAACC,QAAD,EAAmBC,GAAnB,EAAoC;AACpD,UAAIA,GAAJ,EAAS;AACP,cAAKC,OAAL,CAAaC,KAAb,0CAA0DF,GAA1D;AACA,cAAKG,QAAL,CAAcC,eAAd,CAA8BC,SAA9B,CAAwCN,QAAxC;AACD,OAHD,MAGO;AACL,cAAKI,QAAL,CAAcG,eAAd,CAA8BD,SAA9B,CAAwCN,QAAxC;AACD;AACF,KAlC8C;;AAC7C,QAAI,CAACH,MAAL,EAAa;AACX,YAAM,IAAIW,KAAJ,CAAU,wCAAV,CAAN;AACD;;AAED,SAAKC,oBAAL,GAA4BX,QAAQY,mBAAR,IAA+Bf,oCAA3D;AACA,SAAKO,OAAL,GAAeJ,QAAQa,MAAR,IAAkB,IAAIC,gBAAJ,EAAjC;AACA,SAAKC,OAAL,GAAehB,MAAf;AACA,SAAKiB,eAAL,GAAuBC,YAAY,YAAM;AACvC,YAAKC,KAAL;AACD,KAFsB,EAEpB,KAAKP,oBAFe,CAAvB;AAGA,SAAKL,QAAL,GAAgBN,QAAQmB,OAAR,IAAmB,IAAIC,iBAAJ,CAAY,IAAIC,wBAAJ,EAAZ,CAAnC;AACD;;;;2BAEc;AACb,aAAO,gBAAP;AACD;;;+BAEkB;AACjB,aAAO,KAAKC,IAAL,EAAP;AACD;;;2BAEMC,I,EAAkB;AACvB,UAAMC,aAAaC,iBAAYC,YAAZ,CAAyBH,IAAzB,CAAnB;AACA,WAAKR,OAAL,CAAaY,MAAb,CAAoBH,UAApB,EAAgC,KAAKvB,eAArC;AACD;;;oCAWe2B,Q,EAA6B;AAC3C,UAAIA,QAAJ,EAAc;AACZA;AACD;AACF;;;0BAEKA,Q,EAA6B;AAAA;;AACjC,UAAI,KAAKC,QAAL,KAAkBC,SAAtB,EAAiC;AAC/B,aAAK1B,OAAL,CAAaC,KAAb,CAAmB,2CAAnB;AACA,aAAK0B,eAAL,CAAqBH,QAArB;AACA;AACD;AACD,WAAKb,OAAL,CAAaG,KAAb,CAAmB,UAAChB,QAAD,EAAmBC,GAAnB,EAAoC;AACrD,YAAIA,GAAJ,EAAS;AACP,iBAAKC,OAAL,CAAaC,KAAb,yCAAyDF,GAAzD;AACA,iBAAKG,QAAL,CAAc0B,eAAd,CAA8BxB,SAA9B,CAAwCN,QAAxC;AACD,SAHD,MAGO;AACL,iBAAKI,QAAL,CAAcG,eAAd,CAA8BD,SAA9B,CAAwCN,QAAxC;AACD;AACD,eAAK6B,eAAL,CAAqBH,QAArB;AACD,OARD;AASD;;;0BAEKA,Q,EAA6B;AAAA;;AACjCK,oBAAc,KAAKjB,eAAnB;AACA,WAAKE,KAAL,CAAW,YAAM;AACf,eAAKH,OAAL,CAAamB,KAAb;AACA,eAAKH,eAAL,CAAqBH,QAArB;AACD,OAHD;AAID;;;+BAEUO,W,EAAqBC,I,EAAwB;AACtD,WAAKP,QAAL,GAAgB;AACdM,qBAAaA,WADC;AAEdC,cAAMX,iBAAYY,aAAZ,CAA0BD,IAA1B;AAFQ,OAAhB;;AAKA,WAAKrB,OAAL,CAAauB,UAAb,CAAwB,KAAKT,QAA7B;AACD;;;;;;kBAlFkB/B,c", "file": "remote_reporter.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport NullLogger from '../logger.js';\nimport ThriftUtils from '../thrift.js';\nimport Metrics from '../metrics/metrics.js';\nimport NoopMetricFactory from '../metrics/noop/metric_factory';\n\nconst DEFAULT_BUFFER_FLUSH_INTERVAL_MILLIS = 1000;\n\nexport default class RemoteReporter implements Reporter {\n  _bufferFlushInterval: number;\n  _logger: Logger;\n  _sender: Sender;\n  _intervalHandle: any;\n  _process: Process;\n  _metrics: any;\n\n  constructor(sender: Sender, options: any = {}) {\n    if (!sender) {\n      throw new Error('RemoteReporter must be given a Sender.');\n    }\n\n    this._bufferFlushInterval = options.bufferFlushInterval || DEFAULT_BUFFER_FLUSH_INTERVAL_MILLIS;\n    this._logger = options.logger || new NullLogger();\n    this._sender = sender;\n    this._intervalHandle = setInterval(() => {\n      this.flush();\n    }, this._bufferFlushInterval);\n    this._metrics = options.metrics || new Metrics(new NoopMetricFactory());\n  }\n\n  name(): string {\n    return 'RemoteReporter';\n  }\n\n  toString(): string {\n    return this.name();\n  }\n\n  report(span: Span): void {\n    const thriftSpan = ThriftUtils.spanToThrift(span);\n    this._sender.append(thriftSpan, this._appendCallback);\n  }\n\n  _appendCallback = (numSpans: number, err?: string) => {\n    if (err) {\n      this._logger.error(`Failed to append spans in reporter: ${err}`);\n      this._metrics.reporterDropped.increment(numSpans);\n    } else {\n      this._metrics.reporterSuccess.increment(numSpans);\n    }\n  };\n\n  _invokeCallback(callback?: () => void): void {\n    if (callback) {\n      callback();\n    }\n  }\n\n  flush(callback?: () => void): void {\n    if (this._process === undefined) {\n      this._logger.error('Failed to flush since process is not set.');\n      this._invokeCallback(callback);\n      return;\n    }\n    this._sender.flush((numSpans: number, err?: string) => {\n      if (err) {\n        this._logger.error(`Failed to flush spans in reporter: ${err}`);\n        this._metrics.reporterFailure.increment(numSpans);\n      } else {\n        this._metrics.reporterSuccess.increment(numSpans);\n      }\n      this._invokeCallback(callback);\n    });\n  }\n\n  close(callback?: () => void): void {\n    clearInterval(this._intervalHandle);\n    this.flush(() => {\n      this._sender.close();\n      this._invokeCallback(callback);\n    });\n  }\n\n  setProcess(serviceName: string, tags: Array<Tag>): void {\n    this._process = {\n      serviceName: serviceName,\n      tags: ThriftUtils.getThriftTags(tags),\n    };\n\n    this._sender.setProcess(this._process);\n  }\n}\n"]}