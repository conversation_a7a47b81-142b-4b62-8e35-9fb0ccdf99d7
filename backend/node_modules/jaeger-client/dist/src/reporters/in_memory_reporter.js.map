{"version": 3, "sources": ["../../../src/reporters/in_memory_reporter.js"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_spans", "name", "span", "push", "callback", "serviceName", "tags", "_process", "ThriftUtils", "getThriftTags"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;;;;;IAEqBA,gB;AAInB,8BAAc;AAAA;;AACZ,SAAKC,MAAL,GAAc,EAAd;AACD;;;;2BAEc;AACb,aAAO,kBAAP;AACD;;;+BAEkB;AACjB,aAAO,KAAKC,IAAL,EAAP;AACD;;;2BAEMC,I,EAAkB;AACvB,WAAKF,MAAL,CAAYG,IAAZ,CAAiBD,IAAjB;AACD;;;4BAMa;AACZ,WAAKF,MAAL,GAAc,EAAd;AACD;;;0BAEKI,Q,EAA6B;AACjC,UAAIA,QAAJ,EAAc;AACZA;AACD;AACF;;;+BAEUC,W,EAAqBC,I,EAAwB;AACtD,WAAKC,QAAL,GAAgB;AACdF,qBAAaA,WADC;AAEdC,cAAME,iBAAYC,aAAZ,CAA0BH,IAA1B;AAFQ,OAAhB;AAID;;;wBAnBwB;AACvB,aAAO,KAAKN,MAAZ;AACD;;;;;;kBAtBkBD,gB", "file": "in_memory_reporter.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Span from '../span.js';\nimport ThriftUtils from '../thrift.js';\n\nexport default class InMemoryReporter implements Reporter {\n  _spans: Array<Span>;\n  _process: Process;\n\n  constructor() {\n    this._spans = [];\n  }\n\n  name(): string {\n    return 'InMemoryReporter';\n  }\n\n  toString(): string {\n    return this.name();\n  }\n\n  report(span: Span): void {\n    this._spans.push(span);\n  }\n\n  get spans(): Array<Span> {\n    return this._spans;\n  }\n\n  clear(): void {\n    this._spans = [];\n  }\n\n  close(callback?: () => void): void {\n    if (callback) {\n      callback();\n    }\n  }\n\n  setProcess(serviceName: string, tags: Array<Tag>): void {\n    this._process = {\n      serviceName: serviceName,\n      tags: ThriftUtils.getThriftTags(tags),\n    };\n  }\n}\n"]}