{"version": 3, "sources": ["../../src/index.js"], "names": ["opentracing", "module", "exports", "Configuration", "initTracer", "initTracerFromEnv", "ConfigurationEnv", "SpanContext", "Span", "Tracer", "ConstSampler", "ProbabilisticSampler", "RateLimitingSampler", "RemoteSampler", "experimental", "PrioritySampler", "TagEqualsSampler", "CompositeReporter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Logging<PERSON>ep<PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RemoteReport<PERSON>", "TextMapCodec", "ZipkinB3TextMapCodec", "TestUtils", "TChannelBridge", "PrometheusMetricsFactory"], "mappings": ";;AAYA;;;;AACA;;;;AAEA;;;;AACA;;;;AACA;;;;AAEA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAEA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAEA;;;;AACA;;;;AAEA;;;;AACA;;;;AAEA;;;;AAEA;;IAAYA,W;;;;;;AAxCZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAgCAC,OAAOC,OAAP,GAAiB;AACfC,wCADe;AAEfC,cAAYD,wBAAcC,UAFX;AAGfC,qBAAmBC,4BAAiBF,UAHrB;AAIfG,qCAJe;AAKfC,sBALe;AAMfC,0BANe;;AAQfC,uCARe;AASfC,uDATe;AAUfC,sDAVe;AAWfC,yCAXe;AAYfC,gBAAc;AACZC,+CADY;AAEZC;AAFY,GAZC;;AAiBfC,iDAjBe;AAkBfC,gDAlBe;AAmBfC,6CAnBe;AAoBfC,uCApBe;AAqBfC,2CArBe;;AAuBfC,wCAvBe;AAwBfC,0DAxBe;;AA0BfC,gCA1Be;AA2BfC,2CA3Be;AA4BfC,gDA5Be;AA6Bf1B;AA7Be,CAAjB", "file": "index.js", "sourcesContent": ["// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Configuration from './configuration';\nimport ConfigurationEnv from './configuration_env';\n\nimport SpanContext from './span_context';\nimport Span from './span';\nimport Tracer from './tracer';\n\nimport ConstSampler from './samplers/const_sampler';\nimport ProbabilisticSampler from './samplers/probabilistic_sampler';\nimport RateLimitingSampler from './samplers/rate_limiting_sampler';\nimport RemoteSampler from './samplers/remote_sampler';\nimport PrioritySampler from './samplers/experimental/priority_sampler';\nimport TagEqualsSampler from './samplers/experimental/tag_equals_sampler';\n\nimport CompositeReporter from './reporters/composite_reporter';\nimport InMemoryReporter from './reporters/in_memory_reporter';\nimport LoggingReporter from './reporters/logging_reporter';\nimport NoopReporter from './reporters/noop_reporter';\nimport RemoteReporter from './reporters/remote_reporter';\n\nimport TextMapCodec from './propagators/text_map_codec';\nimport ZipkinB3TextMapCodec from './propagators/zipkin_b3_text_map_codec';\n\nimport TestUtils from './test_util';\nimport TChannelBridge from './tchannel_bridge';\n\nimport PrometheusMetricsFactory from './metrics/prometheus';\n\nimport * as opentracing from 'opentracing';\n\nmodule.exports = {\n  Configuration,\n  initTracer: Configuration.initTracer,\n  initTracerFromEnv: ConfigurationEnv.initTracer,\n  SpanContext,\n  Span,\n  Tracer,\n\n  ConstSampler,\n  ProbabilisticSampler,\n  RateLimitingSampler,\n  RemoteSampler,\n  experimental: {\n    PrioritySampler,\n    TagEqualsSampler,\n  },\n\n  CompositeReporter,\n  InMemoryReporter,\n  LoggingReporter,\n  NoopReporter,\n  RemoteReporter,\n\n  TextMapCodec,\n  ZipkinB3TextMapCodec,\n\n  TestUtils,\n  TChannelBridge,\n  PrometheusMetricsFactory,\n  opentracing,\n};\n"]}