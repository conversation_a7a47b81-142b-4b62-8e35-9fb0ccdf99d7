{"version": 3, "sources": ["../../src/span.js"], "names": ["Span", "tracer", "operationName", "spanContext", "startTime", "references", "_tracer", "_operationName", "_spanContext", "_startTime", "_logger", "_references", "_baggageSetter", "_logs", "_tags", "key", "baggageHeaderCache", "_getBaggageHeaderCache", "normalizedKey", "replace", "toLowerCase", "Object", "keys", "length", "value", "_normalizeBaggageKey", "setBaggage", "baggage", "samplingFinalized", "isSampled", "decision", "_sampler", "onSetOperationName", "_applySamplingDecision", "retryable", "finalizeSampling", "sample", "_setSampled", "tags", "_appendTags", "finishTime", "_duration", "undefined", "spanInfo", "context", "toString", "error", "endTime", "now", "_report", "keyValuePairs", "hasOwnProperty", "prototype", "<PERSON><PERSON><PERSON>", "otTags", "SAMPLING_PRIORITY", "samplingPriority", "_setSamplingPriority", "_appendTag", "_isWriteable", "call", "onSetTag", "timestamp", "push", "fields", "Utils", "convertObjectToTags", "eventName", "payload", "log", "event", "priority", "isDebug", "_isDebugAllowed", "_setDebug", "_serviceName", "_baggageHeaderCache"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;AACA;;;;AACA;;;;AACA;;;;;;;;IAEqBA,I;AAanB,gBACEC,MADF,EAEEC,aAFF,EAGEC,WAHF,EAIEC,SAJF,EAKEC,UALF,EAME;AAAA;;AACA,SAAKC,OAAL,GAAeL,MAAf;AACA,SAAKM,cAAL,GAAsBL,aAAtB;AACA,SAAKM,YAAL,GAAoBL,WAApB;AACA,SAAKM,UAAL,GAAkBL,SAAlB;AACA,SAAKM,OAAL,GAAeT,OAAOS,OAAtB;AACA,SAAKC,WAAL,GAAmBN,cAAc,EAAjC;AACA,SAAKO,cAAL,GAAsBX,OAAOW,cAA7B;AACA,SAAKC,KAAL,GAAa,EAAb;AACA,SAAKC,KAAL,GAAa,EAAb;AACD;;;;;;AAkBD;;;;;;yCAMqBC,G,EAAa;AAChC,UAAIC,qBAAqBhB,KAAKiB,sBAAL,EAAzB;AACA,UAAIF,OAAOC,kBAAX,EAA+B;AAC7B,eAAOA,mBAAmBD,GAAnB,CAAP;AACD;;AAED,UAAIG,gBAAwBH,IAAII,OAAJ,CAAY,IAAZ,EAAkB,GAAlB,EAAuBC,WAAvB,EAA5B;;AAEA,UAAIC,OAAOC,IAAP,CAAYN,kBAAZ,EAAgCO,MAAhC,GAAyC,GAA7C,EAAkD;AAChDP,2BAAmBD,GAAnB,IAA0BG,aAA1B;AACD;;AAED,aAAOA,aAAP;AACD;;AAED;;;;;;;;;;;mCAQeH,G,EAAaS,K,EAAqB;AAC/C,UAAIN,gBAAgB,KAAKO,oBAAL,CAA0BV,GAA1B,CAApB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAKP,YAAL,GAAoB,KAAKI,cAAL,CAAoBc,UAApB,CAA+B,IAA/B,EAAqCR,aAArC,EAAoDM,KAApD,CAApB;AACA,aAAO,IAAP;AACD;;AAED;;;;;;;;;mCAMeT,G,EAAqB;AAClC,UAAIG,gBAAgB,KAAKO,oBAAL,CAA0BV,GAA1B,CAApB;AACA,aAAO,KAAKP,YAAL,CAAkBmB,OAAlB,CAA0BT,aAA1B,CAAP;AACD;;AAED;;;;;;;;8BAKuB;AACrB,aAAO,KAAKV,YAAZ;AACD;;AAED;;;;;;;;6BAKiB;AACf,aAAO,KAAKF,OAAZ;AACD;;AAED;;;;;;;;mCAKwB;AACtB,aAAO,CAAC,KAAKE,YAAL,CAAkBoB,iBAAnB,IAAwC,KAAKpB,YAAL,CAAkBqB,SAAlB,EAA/C;AACD;;AAED;;;;;;;;;qCAMiB3B,a,EAA6B;AAC5C,WAAKK,cAAL,GAAsBL,aAAtB;AACA;AACA,UAAI,CAAC,KAAKM,YAAL,CAAkBoB,iBAAvB,EAA0C;AACxC,YAAME,WAAW,KAAK7B,MAAL,GAAc8B,QAAd,CAAuBC,kBAAvB,CAA0C,IAA1C,EAAgD9B,aAAhD,CAAjB;AACA,aAAK+B,sBAAL,CAA4BH,QAA5B;AACD;AACD,aAAO,IAAP;AACD;;;2CAEsBA,Q,EAAkC;AACvD,UAAI,CAACA,SAASI,SAAd,EAAyB;AACvB,aAAK1B,YAAL,CAAkB2B,gBAAlB;AACD;AACD,UAAIL,SAASM,MAAb,EAAqB;AACnB,aAAK5B,YAAL,CAAkB6B,WAAlB,CAA8B,IAA9B;AACA,YAAIP,SAASQ,IAAb,EAAmB;AACjB,eAAKC,WAAL,CAAiBT,SAASQ,IAA1B;AACD;AACF;AACF;;AAED;;;;;;;;;;;;;;;;2BAaOE,U,EAA2B;AAChC,UAAI,KAAKC,SAAL,KAAmBC,SAAvB,EAAkC;AAChC,YAAIC,0BAAwB,KAAKzC,aAA7B,iBAAsD,KAAK0C,OAAL,GAAeC,QAAf,EAA1D;AACA,aAAK5C,MAAL,GAAcS,OAAd,CAAsBoC,KAAtB,CAA+BH,QAA/B;AACA;AACD;;AAED,UAAI,KAAKnC,YAAL,CAAkBqB,SAAlB,EAAJ,EAAmC;AACjC,YAAIkB,UAAUP,cAAc,KAAKlC,OAAL,CAAa0C,GAAb,EAA5B;AACA,aAAKP,SAAL,GAAiBM,UAAU,KAAKtC,UAAhC;AACA,aAAKH,OAAL,CAAa2C,OAAb,CAAqB,IAArB;AACD;AACF;;AAED;;;;;;;;;;4BAOQC,a,EAA0B;AAChC,UAAMC,iBAAiB9B,OAAO+B,SAAP,CAAiBD,cAAxC;AACA;AACA,UAAME,cAAcC,kBAAOC,iBAA3B;AACA,UAAMC,mBAAmBN,cAAcG,WAAd,CAAzB;AACA,UAAI,KAAKI,oBAAL,CAA0BD,gBAA1B,CAAJ,EAAiD;AAC/C,aAAKE,UAAL,CAAgBL,WAAhB,EAA6BG,gBAA7B;AACD;AACD,UAAI,KAAKG,YAAL,EAAJ,EAAyB;AACvB,aAAK,IAAI5C,GAAT,IAAgBmC,aAAhB,EAA+B;AAC7B,cAAIC,eAAeS,IAAf,CAAoBV,aAApB,EAAmCnC,GAAnC,CAAJ,EAA6C;AAC3C,gBAAIA,QAAQsC,WAAZ,EAAyB;AACvB,uBADuB,CACb;AACX;AACD,gBAAM7B,QAAQ0B,cAAcnC,GAAd,CAAd;AACA,iBAAK2C,UAAL,CAAgB3C,GAAhB,EAAqBS,KAArB;AACA,gBAAI,CAAC,KAAKhB,YAAL,CAAkBoB,iBAAvB,EAA0C;AACxC,kBAAME,WAAW,KAAKxB,OAAL,CAAayB,QAAb,CAAsB8B,QAAtB,CAA+B,IAA/B,EAAqC9C,GAArC,EAA0CS,KAA1C,CAAjB;AACA,mBAAKS,sBAAL,CAA4BH,QAA5B;AACD;AACF;AACF;AACF;AACD,aAAO,IAAP;AACD;;AAED;;;;;;;;;;;2BAQOf,G,EAAaS,K,EAAkB;AACpC,UAAIT,QAAQuC,kBAAOC,iBAAf,IAAoC,CAAC,KAAKE,oBAAL,CAA0BjC,KAA1B,CAAzC,EAA2E;AACzE,eAAO,IAAP;AACD;;AAED,UAAI,KAAKmC,YAAL,EAAJ,EAAyB;AACvB,aAAKD,UAAL,CAAgB3C,GAAhB,EAAqBS,KAArB;AACA,YAAI,CAAC,KAAKhB,YAAL,CAAkBoB,iBAAvB,EAA0C;AACxC,cAAME,WAAW,KAAKxB,OAAL,CAAayB,QAAb,CAAsB8B,QAAtB,CAA+B,IAA/B,EAAqC9C,GAArC,EAA0CS,KAA1C,CAAjB;AACA,eAAKS,sBAAL,CAA4BH,QAA5B;AACD;AACF;AACD,aAAO,IAAP;AACD;;AAED;;;;;;;;;;;;;;;;;;wBAeIoB,a,EAAoBY,S,EAA0B;AAChD,UAAI,KAAKH,YAAL,EAAJ,EAAyB;AACvB,aAAK9C,KAAL,CAAWkD,IAAX,CAAgB;AACdD,qBAAWA,aAAa,KAAKxD,OAAL,CAAa0C,GAAb,EADV;AAEdgB,kBAAQC,eAAMC,mBAAN,CAA0BhB,aAA1B;AAFM,SAAhB;AAID;AACD,aAAO,IAAP;AACD;;AAED;;;;;;;;;;6BAOSiB,S,EAAmBC,O,EAAoB;AAC9C,WAAKC,GAAL,CAAS;AACPC,eAAOH,SADA;AAEPC,iBAASA;AAFF,OAAT;AAID;;AAED;;;;;;;;;;yCAOqBG,Q,EAA4B;AAC/C,UAAIA,YAAY,IAAhB,EAAsB;AACpB,eAAO,KAAP;AACD;AACD,WAAK/D,YAAL,CAAkB2B,gBAAlB;AACA,UAAIoC,WAAW,CAAf,EAAkB;AAChB,YAAI,KAAK/D,YAAL,CAAkBgE,OAAlB,EAAJ,EAAiC;AAC/B;AACA,iBAAO,KAAP;AACD;AACD,YAAI,KAAKlE,OAAL,CAAamE,eAAb,CAA6B,KAAKlE,cAAlC,CAAJ,EAAuD;AACrD,eAAKC,YAAL,CAAkB6B,WAAlB,CAA8B,IAA9B;AACA,eAAK7B,YAAL,CAAkBkE,SAAlB,CAA4B,IAA5B;AACA,iBAAO,IAAP;AACD;AACD,eAAO,KAAP;AACD;AACD,WAAKlE,YAAL,CAAkB6B,WAAlB,CAA8B,KAA9B;AACA,WAAK7B,YAAL,CAAkBkE,SAAlB,CAA4B,KAA5B;AACA,aAAO,IAAP;AACD;;;+BAEU3D,G,EAAaS,K,EAAkB;AACxC,WAAKV,KAAL,CAAWiD,IAAX,CAAgB,EAAEhD,KAAKA,GAAP,EAAYS,OAAOA,KAAnB,EAAhB;AACD;;AAED;AACA;;;;gCACYc,I,EAAgB;AAC1B,UAAMa,iBAAiB9B,OAAO+B,SAAP,CAAiBD,cAAxC;AACA,WAAK,IAAIpC,GAAT,IAAgBuB,IAAhB,EAAsB;AACpB,YAAIa,eAAeS,IAAf,CAAoBtB,IAApB,EAA0BvB,GAA1B,CAAJ,EAAoC;AAClC,cAAMS,QAAQc,KAAKvB,GAAL,CAAd;AACA,eAAK2C,UAAL,CAAgB3C,GAAhB,EAAqBS,KAArB;AACD;AACF;AACF;;;wBAjS2B;AAC1B,aAAO,KAAKjB,cAAZ;AACD;;;wBAEyB;AACxB,aAAO,KAAKD,OAAL,CAAaqE,YAApB;AACD;;;6CAE+B;AAC9B,UAAI,CAAC3E,KAAK4E,mBAAV,EAA+B;AAC7B5E,aAAK4E,mBAAL,GAA2B,EAA3B;AACD;;AAED,aAAO5E,KAAK4E,mBAAZ;AACD;;;;;;kBA7CkB5E,I", "file": "span.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport BaggageSetter from './baggage/baggage_setter';\nimport { Tags as otTags } from 'opentracing';\nimport SpanContext from './span_context';\nimport Tracer from './tracer';\nimport Utils from './util';\n\nexport default class Span {\n  _tracer: Tracer;\n  _operationName: string;\n  _spanContext: SpanContext;\n  _startTime: number;\n  _logger: any;\n  _duration: number;\n  _logs: Array<LogData>;\n  _tags: Array<Tag>;\n  static _baggageHeaderCache: {};\n  _references: Array<Reference>;\n  _baggageSetter: BaggageSetter;\n\n  constructor(\n    tracer: Tracer,\n    operationName: string,\n    spanContext: SpanContext,\n    startTime: number,\n    references: ?Array<Reference>\n  ) {\n    this._tracer = tracer;\n    this._operationName = operationName;\n    this._spanContext = spanContext;\n    this._startTime = startTime;\n    this._logger = tracer._logger;\n    this._references = references || [];\n    this._baggageSetter = tracer._baggageSetter;\n    this._logs = [];\n    this._tags = [];\n  }\n\n  get operationName(): string {\n    return this._operationName;\n  }\n\n  get serviceName(): string {\n    return this._tracer._serviceName;\n  }\n\n  static _getBaggageHeaderCache() {\n    if (!Span._baggageHeaderCache) {\n      Span._baggageHeaderCache = {};\n    }\n\n    return Span._baggageHeaderCache;\n  }\n\n  /**\n   * Returns a normalize key.\n   *\n   * @param {string} key - The key to be normalized for a particular baggage value.\n   * @return {string} - The normalized key (lower cased and underscores replaced, along with dashes.)\n   **/\n  _normalizeBaggageKey(key: string) {\n    let baggageHeaderCache = Span._getBaggageHeaderCache();\n    if (key in baggageHeaderCache) {\n      return baggageHeaderCache[key];\n    }\n\n    let normalizedKey: string = key.replace(/_/g, '-').toLowerCase();\n\n    if (Object.keys(baggageHeaderCache).length < 100) {\n      baggageHeaderCache[key] = normalizedKey;\n    }\n\n    return normalizedKey;\n  }\n\n  /**\n   * Sets a baggage value with an associated key.\n   *\n   * @param {string} key - The baggage key.\n   * @param {string} value - The baggage value.\n   *\n   * @return {Span} - returns this span.\n   **/\n  setBaggageItem(key: string, value: string): Span {\n    let normalizedKey = this._normalizeBaggageKey(key);\n\n    // We create a new instance of the context here instead of just adding\n    // another entry to the baggage dictionary. By doing so we keep the\n    // baggage immutable so that it can be passed to children spans as is.\n    // If it was mutable, we would have to make a copy of the dictionary\n    // for every child span, which on average we expect to occur more\n    // frequently than items being added to the baggage.\n    this._spanContext = this._baggageSetter.setBaggage(this, normalizedKey, value);\n    return this;\n  }\n\n  /**\n   * Gets a baggage value with an associated key.\n   *\n   * @param {string} key - The baggage key.\n   * @return {string} value - The baggage value.\n   **/\n  getBaggageItem(key: string): string {\n    let normalizedKey = this._normalizeBaggageKey(key);\n    return this._spanContext.baggage[normalizedKey];\n  }\n\n  /**\n   * Returns the span context that represents this span.\n   *\n   * @return {SpanContext} - Returns this span's span context.\n   **/\n  context(): SpanContext {\n    return this._spanContext;\n  }\n\n  /**\n   *  Returns the tracer associated with this span.\n   *\n   * @return {Tracer} - returns the tracer associated with this span.\n   **/\n  tracer(): Tracer {\n    return this._tracer;\n  }\n\n  /**\n   * Checks whether or not a span can be written to.\n   *\n   * @return {boolean} - The decision about whether this span can be written to.\n   **/\n  _isWriteable(): boolean {\n    return !this._spanContext.samplingFinalized || this._spanContext.isSampled();\n  }\n\n  /**\n   * Sets the operation name on this given span.\n   *\n   * @param {string} name - The name to use for setting a span's operation name.\n   * @return {Span} - returns this span.\n   **/\n  setOperationName(operationName: string): Span {\n    this._operationName = operationName;\n    // We re-sample the span if it has not been finalized.\n    if (!this._spanContext.samplingFinalized) {\n      const decision = this.tracer()._sampler.onSetOperationName(this, operationName);\n      this._applySamplingDecision(decision);\n    }\n    return this;\n  }\n\n  _applySamplingDecision(decision: SamplingDecision): void {\n    if (!decision.retryable) {\n      this._spanContext.finalizeSampling();\n    }\n    if (decision.sample) {\n      this._spanContext._setSampled(true);\n      if (decision.tags) {\n        this._appendTags(decision.tags);\n      }\n    }\n  }\n\n  /**\n   * Sets the end timestamp and finalizes Span state.\n   *\n   * With the exception of calls to Span.context() (which are always allowed),\n   * finish() must be the last call made to any span instance, and to do\n   * otherwise leads to undefined behavior.\n   *\n   * @param  {number} finishTime\n   *         Optional finish time in milliseconds as a Unix timestamp. Decimal\n   *         values are supported for timestamps with sub-millisecond accuracy.\n   *         If not specified, the current time (as defined by the\n   *         implementation) will be used.\n   */\n  finish(finishTime: ?number): void {\n    if (this._duration !== undefined) {\n      let spanInfo = `operation=${this.operationName},context=${this.context().toString()}`;\n      this.tracer()._logger.error(`${spanInfo}#You can only call finish() on a span once.`);\n      return;\n    }\n\n    if (this._spanContext.isSampled()) {\n      let endTime = finishTime || this._tracer.now();\n      this._duration = endTime - this._startTime;\n      this._tracer._report(this);\n    }\n  }\n\n  /**\n   * Adds a set of tags to a span.\n   *\n   * @param {Object} keyValuePairs - An object with key value pairs\n   * that represent tags to be added to this span.\n   * @return {Span} - returns this span.\n   **/\n  addTags(keyValuePairs: any): Span {\n    const hasOwnProperty = Object.prototype.hasOwnProperty;\n    // handle sampling.priority tag first as it can make the span writable\n    const samplingKey = otTags.SAMPLING_PRIORITY;\n    const samplingPriority = keyValuePairs[samplingKey];\n    if (this._setSamplingPriority(samplingPriority)) {\n      this._appendTag(samplingKey, samplingPriority);\n    }\n    if (this._isWriteable()) {\n      for (let key in keyValuePairs) {\n        if (hasOwnProperty.call(keyValuePairs, key)) {\n          if (key === samplingKey) {\n            continue; // this tag has already been added above\n          }\n          const value = keyValuePairs[key];\n          this._appendTag(key, value);\n          if (!this._spanContext.samplingFinalized) {\n            const decision = this._tracer._sampler.onSetTag(this, key, value);\n            this._applySamplingDecision(decision);\n          }\n        }\n      }\n    }\n    return this;\n  }\n\n  /**\n   * Adds a single tag to a span\n   *\n   * @param {string} key - The key for the tag added to this span.\n   * @param {string} value - The value corresponding with the key\n   * for the tag added to this span.\n   * @return {Span} - returns this span.\n   * */\n  setTag(key: string, value: any): Span {\n    if (key === otTags.SAMPLING_PRIORITY && !this._setSamplingPriority(value)) {\n      return this;\n    }\n\n    if (this._isWriteable()) {\n      this._appendTag(key, value);\n      if (!this._spanContext.samplingFinalized) {\n        const decision = this._tracer._sampler.onSetTag(this, key, value);\n        this._applySamplingDecision(decision);\n      }\n    }\n    return this;\n  }\n\n  /**\n   * Adds a log event, or payload to a span.\n   *\n   * @param {object} keyValuePairs\n   *        An object mapping string keys to arbitrary value types. All\n   *        Tracer implementations should support bool, string, and numeric\n   *        value types, and some may also support Object values.\n   * @param {number} timestamp\n   *        An optional parameter specifying the timestamp in milliseconds\n   *        since the Unix epoch. Fractional values are allowed so that\n   *        timestamps with sub-millisecond accuracy can be represented. If\n   *        not specified, the implementation is expected to use its notion\n   *        of the current time of the call.\n   * @return {Span} - returns this span.\n   */\n  log(keyValuePairs: any, timestamp: ?number): Span {\n    if (this._isWriteable()) {\n      this._logs.push({\n        timestamp: timestamp || this._tracer.now(),\n        fields: Utils.convertObjectToTags(keyValuePairs),\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Logs a event with an optional payload.\n   *\n   * @param  {string} eventName - string associated with the log record\n   * @param  {object} [payload] - arbitrary payload object associated with the\n   *         log record.\n   */\n  logEvent(eventName: string, payload: any): void {\n    this.log({\n      event: eventName,\n      payload: payload,\n    });\n  }\n\n  /**\n   * Returns true if the flag was updated successfully, false otherwise\n   *\n   * @param priority - 0 to disable sampling, 1 to enable\n   * @returns {boolean} - true if the flag was updated successfully\n   * @private\n   */\n  _setSamplingPriority(priority: ?number): boolean {\n    if (priority == null) {\n      return false;\n    }\n    this._spanContext.finalizeSampling();\n    if (priority > 0) {\n      if (this._spanContext.isDebug()) {\n        // If the span is already in debug, no need to set it again\n        return false;\n      }\n      if (this._tracer._isDebugAllowed(this._operationName)) {\n        this._spanContext._setSampled(true);\n        this._spanContext._setDebug(true);\n        return true;\n      }\n      return false;\n    }\n    this._spanContext._setSampled(false);\n    this._spanContext._setDebug(false);\n    return true;\n  }\n\n  _appendTag(key: string, value: any): void {\n    this._tags.push({ key: key, value: value });\n  }\n\n  // Internal method that adds tags without verifying them.\n  // TODO: consider if we want to remove duplicates when sampling tags are added twice.\n  _appendTags(tags: {}): void {\n    const hasOwnProperty = Object.prototype.hasOwnProperty;\n    for (let key in tags) {\n      if (hasOwnProperty.call(tags, key)) {\n        const value = tags[key];\n        this._appendTag(key, value);\n      }\n    }\n  }\n}\n"]}