{"version": 3, "sources": ["../../../../src/metrics/noop/metric_factory.js"], "names": ["NoopMetricFactory", "name", "tags", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NoopTimer", "NoopGauge"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;AACA;;;;;;;;IAEqBA,iB;;;;;;;kCACLC,I,EAAcC,I,EAAoB;AAC9C,aAAO,IAAIC,iBAAJ,EAAP;AACD;;;gCAEWF,I,EAAcC,I,EAAkB;AAC1C,aAAO,IAAIE,eAAJ,EAAP;AACD;;;gCAEWH,I,EAAcC,I,EAAkB;AAC1C,aAAO,IAAIG,eAAJ,EAAP;AACD;;;;;;kBAXkBL,iB", "file": "metric_factory.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport NoopCounter from './counter';\nimport NoopTimer from './timer';\nimport NoopGauge from './gauge';\n\nexport default class NoopMetricFactory {\n  createCounter(name: string, tags: any): Counter {\n    return new NoopCounter();\n  }\n\n  createTimer(name: string, tags: any): Timer {\n    return new NoopTimer();\n  }\n\n  createGauge(name: string, tags: any): Gauge {\n    return new NoopGauge();\n  }\n}\n"]}