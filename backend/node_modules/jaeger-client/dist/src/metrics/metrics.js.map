{"version": 3, "sources": ["../../../src/metrics/metrics.js"], "names": ["Metrics", "factory", "_factory", "tracesStartedSampled", "createCounter", "state", "sampled", "tracesStartedNotSampled", "tracesJoinedSampled", "tracesJoinedNotSampled", "spansFinished", "spansStartedSampled", "spansStartedNotSampled", "decodingErrors", "reporterSuccess", "result", "reporterFailure", "reporterDropped", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createGauge", "samplerRetrieved", "samplerQueryFailure", "samplerUpdated", "samplerUpdateFailure", "baggageUpdateSuccess", "baggageUpdateFailure", "baggageTruncate", "throttledDebugSpans", "throttlerUpdateSuccess", "throttlerUpdateFailure"], "mappings": ";;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEqBA,O,GAyBnB,iBAAYC,OAAZ,EAAqC;AAAA;;AACnC,OAAKC,QAAL,GAAgBD,OAAhB;;AAEA,OAAKE,oBAAL,GAA4B,KAAKD,QAAL,CAAcE,aAAd,CAA4B,eAA5B,EAA6C;AACvEC,WAAO,SADgE;AAEvEC,aAAS;AAF8D,GAA7C,CAA5B;;AAKA,OAAKC,uBAAL,GAA+B,KAAKL,QAAL,CAAcE,aAAd,CAA4B,eAA5B,EAA6C;AAC1EC,WAAO,SADmE;AAE1EC,aAAS;AAFiE,GAA7C,CAA/B;;AAKA,OAAKE,mBAAL,GAA2B,KAAKN,QAAL,CAAcE,aAAd,CAA4B,eAA5B,EAA6C;AACtEC,WAAO,QAD+D;AAEtEC,aAAS;AAF6D,GAA7C,CAA3B;;AAKA,OAAKG,sBAAL,GAA8B,KAAKP,QAAL,CAAcE,aAAd,CAA4B,eAA5B,EAA6C;AACzEC,WAAO,QADkE;AAEzEC,aAAS;AAFgE,GAA7C,CAA9B;;AAKA,OAAKI,aAAL,GAAqB,KAAKR,QAAL,CAAcE,aAAd,CAA4B,uBAA5B,CAArB;;AAEA,OAAKO,mBAAL,GAA2B,KAAKT,QAAL,CAAcE,aAAd,CAA4B,sBAA5B,EAAoD;AAC7EE,aAAS;AADoE,GAApD,CAA3B;;AAIA,OAAKM,sBAAL,GAA8B,KAAKV,QAAL,CAAcE,aAAd,CAA4B,sBAA5B,EAAoD;AAChFE,aAAS;AADuE,GAApD,CAA9B;;AAIA,OAAKO,cAAL,GAAsB,KAAKX,QAAL,CAAcE,aAAd,CAA4B,qCAA5B,CAAtB;;AAEA,OAAKU,eAAL,GAAuB,KAAKZ,QAAL,CAAcE,aAAd,CAA4B,uBAA5B,EAAqD;AAC1EW,YAAQ;AADkE,GAArD,CAAvB;;AAIA,OAAKC,eAAL,GAAuB,KAAKd,QAAL,CAAcE,aAAd,CAA4B,uBAA5B,EAAqD;AAC1EW,YAAQ;AADkE,GAArD,CAAvB;;AAIA,OAAKE,eAAL,GAAuB,KAAKf,QAAL,CAAcE,aAAd,CAA4B,uBAA5B,EAAqD;AAC1EW,YAAQ;AADkE,GAArD,CAAvB;;AAIA,OAAKG,mBAAL,GAA2B,KAAKhB,QAAL,CAAciB,WAAd,CAA0B,8BAA1B,CAA3B;;AAEA,OAAKC,gBAAL,GAAwB,KAAKlB,QAAL,CAAcE,aAAd,CAA4B,wBAA5B,EAAsD;AAC5EW,YAAQ;AADoE,GAAtD,CAAxB;;AAIA,OAAKM,mBAAL,GAA2B,KAAKnB,QAAL,CAAcE,aAAd,CAA4B,wBAA5B,EAAsD;AAC/EW,YAAQ;AADuE,GAAtD,CAA3B;;AAIA,OAAKO,cAAL,GAAsB,KAAKpB,QAAL,CAAcE,aAAd,CAA4B,wBAA5B,EAAsD;AAC1EW,YAAQ;AADkE,GAAtD,CAAtB;;AAIA,OAAKQ,oBAAL,GAA4B,KAAKrB,QAAL,CAAcE,aAAd,CAA4B,wBAA5B,EAAsD;AAChFW,YAAQ;AADwE,GAAtD,CAA5B;;AAIA,OAAKS,oBAAL,GAA4B,KAAKtB,QAAL,CAAcE,aAAd,CAA4B,wBAA5B,EAAsD;AAChFW,YAAQ;AADwE,GAAtD,CAA5B;;AAIA,OAAKU,oBAAL,GAA4B,KAAKvB,QAAL,CAAcE,aAAd,CAA4B,wBAA5B,EAAsD;AAChFW,YAAQ;AADwE,GAAtD,CAA5B;;AAIA,OAAKW,eAAL,GAAuB,KAAKxB,QAAL,CAAcE,aAAd,CAA4B,4BAA5B,CAAvB;;AAEA,OAAKuB,mBAAL,GAA2B,KAAKzB,QAAL,CAAcE,aAAd,CAA4B,8BAA5B,CAA3B;;AAEA,OAAKwB,sBAAL,GAA8B,KAAK1B,QAAL,CAAcE,aAAd,CAA4B,0BAA5B,EAAwD;AACpFW,YAAQ;AAD4E,GAAxD,CAA9B;;AAIA,OAAKc,sBAAL,GAA8B,KAAK3B,QAAL,CAAcE,aAAd,CAA4B,0BAA5B,EAAwD;AACpFW,YAAQ;AAD4E,GAAxD,CAA9B;AAGD,C;;kBA7GkBf,O", "file": "metrics.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nexport default class Metrics {\n  _factory: MetricsFactory;\n  tracesStartedSampled: Counter;\n  tracesStartedNotSampled: Counter;\n  tracesJoinedSampled: Counter;\n  tracesJoinedNotSampled: Counter;\n  spansFinished: Counter;\n  spansStartedSampled: Counter;\n  spansStartedNotSampled: Counter;\n  decodingErrors: Counter;\n  reporterSuccess: Counter;\n  reporterFailure: Counter;\n  reporterDropped: Counter;\n  reporterQueueLength: Gauge;\n  samplerRetrieved: Counter;\n  samplerUpdated: Counter;\n  samplerQueryFailure: Counter;\n  samplerUpdateFailure: Counter;\n  baggageUpdateSuccess: Counter;\n  baggageUpdateFailure: Counter;\n  baggageTruncate: Counter;\n  throttledDebugSpans: Counter;\n  throttlerUpdateSuccess: Counter;\n  throttlerUpdateFailure: Counter;\n\n  constructor(factory: MetricsFactory) {\n    this._factory = factory;\n\n    this.tracesStartedSampled = this._factory.createCounter('jaeger:traces', {\n      state: 'started',\n      sampled: 'y',\n    });\n\n    this.tracesStartedNotSampled = this._factory.createCounter('jaeger:traces', {\n      state: 'started',\n      sampled: 'n',\n    });\n\n    this.tracesJoinedSampled = this._factory.createCounter('jaeger:traces', {\n      state: 'joined',\n      sampled: 'y',\n    });\n\n    this.tracesJoinedNotSampled = this._factory.createCounter('jaeger:traces', {\n      state: 'joined',\n      sampled: 'n',\n    });\n\n    this.spansFinished = this._factory.createCounter('jaeger:finished_spans');\n\n    this.spansStartedSampled = this._factory.createCounter('jaeger:started_spans', {\n      sampled: 'y',\n    });\n\n    this.spansStartedNotSampled = this._factory.createCounter('jaeger:started_spans', {\n      sampled: 'n',\n    });\n\n    this.decodingErrors = this._factory.createCounter('jaeger:span_context_decoding_errors');\n\n    this.reporterSuccess = this._factory.createCounter('jaeger:reporter_spans', {\n      result: 'ok',\n    });\n\n    this.reporterFailure = this._factory.createCounter('jaeger:reporter_spans', {\n      result: 'err',\n    });\n\n    this.reporterDropped = this._factory.createCounter('jaeger:reporter_spans', {\n      result: 'dropped',\n    });\n\n    this.reporterQueueLength = this._factory.createGauge('jaeger:reporter_queue_length');\n\n    this.samplerRetrieved = this._factory.createCounter('jaeger:sampler_queries', {\n      result: 'ok',\n    });\n\n    this.samplerQueryFailure = this._factory.createCounter('jaeger:sampler_queries', {\n      result: 'err',\n    });\n\n    this.samplerUpdated = this._factory.createCounter('jaeger:sampler_updates', {\n      result: 'ok',\n    });\n\n    this.samplerUpdateFailure = this._factory.createCounter('jaeger:sampler_updates', {\n      result: 'err',\n    });\n\n    this.baggageUpdateSuccess = this._factory.createCounter('jaeger:baggage_updates', {\n      result: 'ok',\n    });\n\n    this.baggageUpdateFailure = this._factory.createCounter('jaeger:baggage_updates', {\n      result: 'err',\n    });\n\n    this.baggageTruncate = this._factory.createCounter('jaeger:baggage_truncations');\n\n    this.throttledDebugSpans = this._factory.createCounter('jaeger:throttled_debug_spans');\n\n    this.throttlerUpdateSuccess = this._factory.createCounter('jaeger:throttler_updates', {\n      result: 'ok',\n    });\n\n    this.throttlerUpdateFailure = this._factory.createCounter('jaeger:throttler_updates', {\n      result: 'err',\n    });\n  }\n}\n"]}