{"version": 3, "sources": ["../../../src/metrics/prometheus.js"], "names": ["CounterPromWrapper", "counter", "_counter", "delta", "inc", "GaugePromWrapper", "gauge", "_gauge", "value", "set", "PrometheusMetricsFactory", "promClient", "namespace", "_cache", "Counter", "Gauge", "Error", "_promClient", "_namespace", "metric", "name", "labels", "labelNames", "labelValues", "key", "push", "toString", "help", "length", "tags", "_createMetric"], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEMA,kB;AAGJ,8BAAYC,OAAZ,EAA0B;AAAA;;AACxB,SAAKC,QAAL,GAAgBD,OAAhB;AACD;;;;8BAESE,K,EAAqB;AAC7B,WAAKD,QAAL,CAAcE,GAAd,CAAkBD,KAAlB;AACD;;;;;;IAGGE,gB;AAGJ,4BAAYC,KAAZ,EAAwB;AAAA;;AACtB,SAAKC,MAAL,GAAcD,KAAd;AACD;;;;2BAEME,K,EAAqB;AAC1B,WAAKD,MAAL,CAAYE,GAAZ,CAAgBD,KAAhB;AACD;;;;;;IAGkBE,wB;;AAKnB;;;;;;;;;;;;;;AAcA,oCAAYC,UAAZ,EAAgCC,SAAhC,EAAoD;AAAA;;AAAA,SAlBpDC,MAkBoD,GAlBtC,EAkBsC;;AAClD,QAAI,CAACF,UAAD,IAAe,CAACA,WAAWG,OAA3B,IAAsC,CAACH,WAAWI,KAAtD,EAA6D;AAC3D,YAAM,IAAIC,KAAJ,CAAU,8BAAV,CAAN;AACD;AACD,SAAKC,WAAL,GAAmBN,UAAnB;AACA,SAAKO,UAAL,GAAkBN,SAAlB;AACD;;;;kCAEaO,M,EAAaC,I,EAAcC,M,EAAiB;AAAA;;AACxD,UAAIC,aAAa,EAAjB;AACA,UAAIC,cAAc,EAAlB;AACA,WAAK,IAAIC,IAAT,IAAgBH,MAAhB,EAAwB;AACtBC,mBAAWG,IAAX,CAAgBD,IAAhB;AACAD,oBAAYE,IAAZ,CAAiBJ,OAAOG,IAAP,CAAjB;AACD;AACD,UAAIA,MAAMJ,OAAO,GAAP,GAAaE,WAAWI,QAAX,EAAvB;AACA,UAAIC,OAAOP,IAAX;AACA,UAAI,KAAKF,UAAT,EAAqB;AACnBE,eAAO,KAAKF,UAAL,GAAkB,GAAlB,GAAwBE,IAA/B;AACD;AACD,UAAI,EAAEI,OAAO,KAAKX,MAAd,CAAJ,EAA2B;AACzB,aAAKA,MAAL,CAAYW,GAAZ,IAAmB,IAAIL,MAAJ,CAAW,EAAEC,UAAF,EAAQO,UAAR,EAAcL,sBAAd,EAAX,CAAnB;AACD;AACD,aAAOC,YAAYK,MAAZ,GAAqB,CAArB,GAAyB,mBAAKf,MAAL,CAAYW,GAAZ,GAAiBH,MAAjB,mBAA2BE,WAA3B,CAAzB,GAAmE,KAAKV,MAAL,CAAYW,GAAZ,CAA1E;AACD;;AAED;;;;;;;;;kCAMcJ,I,EAAcS,I,EAAmB;AAC7C,aAAO,IAAI7B,kBAAJ,CAAuB,KAAK8B,aAAL,CAAmB,KAAKb,WAAL,CAAiBH,OAApC,EAA6CM,IAA7C,EAAmDS,IAAnD,CAAvB,CAAP;AACD;;AAED;;;;;;;;;gCAMYT,I,EAAcS,I,EAAiB;AACzC,aAAO,IAAIxB,gBAAJ,CAAqB,KAAKyB,aAAL,CAAmB,KAAKb,WAAL,CAAiBF,KAApC,EAA2CK,IAA3C,EAAiDS,IAAjD,CAArB,CAAP;AACD;;;;;;kBA/DkBnB,wB", "file": "prometheus.js", "sourcesContent": ["// @flow\n// Copyright (c) 2018 Jaeger Author.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nclass CounterPromWrapper {\n  _counter: any;\n\n  constructor(counter: any) {\n    this._counter = counter;\n  }\n\n  increment(delta: number): void {\n    this._counter.inc(delta);\n  }\n}\n\nclass GaugePromWrapper {\n  _gauge: any;\n\n  constructor(gauge: any) {\n    this._gauge = gauge;\n  }\n\n  update(value: number): void {\n    this._gauge.set(value);\n  }\n}\n\nexport default class PrometheusMetricsFactory {\n  _cache: any = {};\n  _namespace: ?string;\n  _promClient: any;\n\n  /**\n   * Construct metrics factory for Prometheus\n   *\n   * To instantiate, prom-client needs to be passed like this:\n   *\n   *    var PrometheusMetricsFactory = require('jaeger-client').PrometheusMetricsFactory;\n   *    var promClient = require('prom-client');\n   *\n   *    var namespace = 'your-namespace';\n   *    var metrics = new PrometheusMetricsFactory(promClient, namespace);\n   *\n   * @param {Object} promClient - prom-client object.\n   * @param {String} namespace - Optional a namespace that prepends to each metric name.\n   */\n  constructor(promClient: Object, namespace: ?string) {\n    if (!promClient || !promClient.Counter || !promClient.Gauge) {\n      throw new Error('prom-client must be provided');\n    }\n    this._promClient = promClient;\n    this._namespace = namespace;\n  }\n\n  _createMetric(metric: any, name: string, labels: {}): any {\n    let labelNames = [];\n    let labelValues = [];\n    for (let key in labels) {\n      labelNames.push(key);\n      labelValues.push(labels[key]);\n    }\n    let key = name + ',' + labelNames.toString();\n    let help = name;\n    if (this._namespace) {\n      name = this._namespace + '_' + name;\n    }\n    if (!(key in this._cache)) {\n      this._cache[key] = new metric({ name, help, labelNames });\n    }\n    return labelValues.length > 0 ? this._cache[key].labels(...labelValues) : this._cache[key];\n  }\n\n  /**\n   * Create a counter metric\n   * @param {string} name - metric name\n   * @param {any} tags - labels\n   * @returns {Counter} - created counter metric\n   */\n  createCounter(name: string, tags: {}): Counter {\n    return new CounterPromWrapper(this._createMetric(this._promClient.Counter, name, tags));\n  }\n\n  /**\n   * Create a gauge metric\n   * @param {string} name - metric name\n   * @param {any} tags - labels\n   * @returns {Gauge} - created gauge metric\n   */\n  createGauge(name: string, tags: {}): Gauge {\n    return new GaugePromWrapper(this._createMetric(this._promClient.Gauge, name, tags));\n  }\n}\n"]}