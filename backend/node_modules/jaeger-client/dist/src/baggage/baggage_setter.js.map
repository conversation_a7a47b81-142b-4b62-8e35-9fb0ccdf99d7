{"version": 3, "sources": ["../../../src/baggage/baggage_setter.js"], "names": ["BaggageSetter", "restrictionManager", "metrics", "_restrictionManager", "_metrics", "span", "key", "baggageValue", "value", "truncated", "prevItem", "restriction", "getRestriction", "serviceName", "keyAllowed", "_logFields", "baggageUpdateFailure", "increment", "context", "length", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "substring", "baggageTruncate", "getBaggageItem", "baggageUpdateSuccess", "withBaggageItem", "valid", "isSampled", "fields", "event", "override", "invalid", "log"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;AACA;;;;;;;;AAEA;;;;IAIqBA,a;AAInB,yBAAYC,kBAAZ,EAA2DC,OAA3D,EAA6E;AAAA;;AAC3E,SAAKC,mBAAL,GAA2BF,kBAA3B;AACA,SAAKG,QAAL,GAAgBF,OAAhB;AACD;;AAED;;;;;;;;;;;;;+BASWG,I,EAAYC,G,EAAaC,Y,EAAmC;AACrE,UAAIC,QAAQD,YAAZ;AACA,UAAIE,YAAY,KAAhB;AACA,UAAIC,WAAW,EAAf;AACA,UAAIC,cAAc,KAAKR,mBAAL,CAAyBS,cAAzB,CAAwCP,KAAKQ,WAA7C,EAA0DP,GAA1D,CAAlB;AACA,UAAI,CAACK,YAAYG,UAAjB,EAA6B;AAC3B,aAAKC,UAAL,CAAgBV,IAAhB,EAAsBC,GAAtB,EAA2BE,KAA3B,EAAkCE,QAAlC,EAA4CD,SAA5C,EAAuDE,YAAYG,UAAnE;AACA,aAAKV,QAAL,CAAcY,oBAAd,CAAmCC,SAAnC,CAA6C,CAA7C;AACA,eAAOZ,KAAKa,OAAL,EAAP;AACD;AACD,UAAIV,MAAMW,MAAN,GAAeR,YAAYS,cAA/B,EAA+C;AAC7CX,oBAAY,IAAZ;AACAD,gBAAQA,MAAMa,SAAN,CAAgB,CAAhB,EAAmBV,YAAYS,cAA/B,CAAR;AACA,aAAKhB,QAAL,CAAckB,eAAd,CAA8BL,SAA9B,CAAwC,CAAxC;AACD;AACDP,iBAAWL,KAAKkB,cAAL,CAAoBjB,GAApB,CAAX;AACA,WAAKS,UAAL,CAAgBV,IAAhB,EAAsBC,GAAtB,EAA2BE,KAA3B,EAAkCE,QAAlC,EAA4CD,SAA5C,EAAuDE,YAAYG,UAAnE;AACA,WAAKV,QAAL,CAAcoB,oBAAd,CAAmCP,SAAnC,CAA6C,CAA7C;AACA,aAAOZ,KAAKa,OAAL,GAAeO,eAAf,CAA+BnB,GAA/B,EAAoCE,KAApC,CAAP;AACD;;;+BAEUH,I,EAAYC,G,EAAaE,K,EAAeE,Q,EAAkBD,S,EAAoBiB,K,EAAgB;AACvG,UAAI,CAACrB,KAAKa,OAAL,GAAeS,SAAf,EAAL,EAAiC;AAC/B;AACD;AACD,UAAIC,SAAoC;AACtCC,eAAO,SAD+B;AAEtCvB,aAAKA,GAFiC;AAGtCE,eAAOA;AAH+B,OAAxC;AAKA,UAAIE,QAAJ,EAAc;AACZkB,eAAOE,QAAP,GAAkB,MAAlB;AACD;AACD,UAAIrB,SAAJ,EAAe;AACbmB,eAAOnB,SAAP,GAAmB,MAAnB;AACD;AACD,UAAI,CAACiB,KAAL,EAAY;AACVE,eAAOG,OAAP,GAAiB,MAAjB;AACD;AACD1B,WAAK2B,GAAL,CAASJ,MAAT;AACD;;;;;;kBA1DkB5B,a", "file": "baggage_setter.js", "sourcesContent": ["// @flow\n// Copyright (c) 2017 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Span from '../span';\nimport SpanContext from '../span_context';\nimport Metrics from '../metrics/metrics';\n\n/**\n * BaggageSetter is a class that sets a baggage key:value and the associated\n * logs on a Span.\n */\nexport default class BaggageSetter {\n  _restrictionManager: BaggageRestrictionManager;\n  _metrics: Metrics;\n\n  constructor(restrictionManager: BaggageRestrictionManager, metrics: Metrics) {\n    this._restrictionManager = restrictionManager;\n    this._metrics = metrics;\n  }\n\n  /**\n   * Sets the baggage key:value on the span and the corresponding logs.\n   * A SpanContext is returned with the new baggage key:value set.\n   *\n   * @param {Span} span - The span to set the baggage on.\n   * @param {string} key - The baggage key to set.\n   * @param {string} baggageValue - The baggage value to set.\n   * @return {SpanContext} - The SpanContext with the baggage set if applicable.\n   */\n  setBaggage(span: Span, key: string, baggageValue: string): SpanContext {\n    let value = baggageValue;\n    let truncated = false;\n    let prevItem = '';\n    let restriction = this._restrictionManager.getRestriction(span.serviceName, key);\n    if (!restriction.keyAllowed) {\n      this._logFields(span, key, value, prevItem, truncated, restriction.keyAllowed);\n      this._metrics.baggageUpdateFailure.increment(1);\n      return span.context();\n    }\n    if (value.length > restriction.maxValueLength) {\n      truncated = true;\n      value = value.substring(0, restriction.maxValueLength);\n      this._metrics.baggageTruncate.increment(1);\n    }\n    prevItem = span.getBaggageItem(key);\n    this._logFields(span, key, value, prevItem, truncated, restriction.keyAllowed);\n    this._metrics.baggageUpdateSuccess.increment(1);\n    return span.context().withBaggageItem(key, value);\n  }\n\n  _logFields(span: Span, key: string, value: string, prevItem: string, truncated: boolean, valid: boolean) {\n    if (!span.context().isSampled()) {\n      return;\n    }\n    let fields: { [key: string]: string } = {\n      event: 'baggage',\n      key: key,\n      value: value,\n    };\n    if (prevItem) {\n      fields.override = 'true';\n    }\n    if (truncated) {\n      fields.truncated = 'true';\n    }\n    if (!valid) {\n      fields.invalid = 'true';\n    }\n    span.log(fields);\n  }\n}\n"]}