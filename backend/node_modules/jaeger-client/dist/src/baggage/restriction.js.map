{"version": 3, "sources": ["../../../src/baggage/restriction.js"], "names": ["Restriction", "keyAllowed", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_keyAllowed", "_max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;IAIqBA,W;AAInB,uBAAYC,UAAZ,EAAiCC,cAAjC,EAAyD;AAAA;;AACvD,SAAKC,WAAL,GAAmBF,UAAnB;AACA,SAAKG,eAAL,GAAuBF,cAAvB;AACD;;;;wBAEyB;AACxB,aAAO,KAAKC,WAAZ;AACD;;;wBAE4B;AAC3B,aAAO,KAAKC,eAAZ;AACD;;;;;;kBAfkBJ,W", "file": "restriction.js", "sourcesContent": ["// @flow\n// Copyright (c) 2017 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\n/**\n * Restriction determines whether a baggage key is allowed and contains any\n * restrictions on the baggage value.\n */\nexport default class Restriction {\n  _keyAllowed: boolean;\n  _maxValueLength: number;\n\n  constructor(keyAllowed: boolean, maxValueLength: number) {\n    this._keyAllowed = keyAllowed;\n    this._maxValueLength = maxValueLength;\n  }\n\n  get keyAllowed(): boolean {\n    return this._keyAllowed;\n  }\n\n  get maxValueLength(): number {\n    return this._maxValueLength;\n  }\n}\n"]}