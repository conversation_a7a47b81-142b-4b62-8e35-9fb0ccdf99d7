{"version": 3, "sources": ["../../../src/baggage/default_baggage_restriction_manager.js"], "names": ["DEFAULT_MAX_VALUE_LENGTH", "DefaultBaggageRestrictionManager", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "_restriction", "Restriction", "service", "key"], "mappings": ";;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;AAEO,IAAMA,8DAA2B,IAAjC;;AAEP;;;;IAGqBC,gC;AAGnB,4CAAYC,cAAZ,EAAqC;AAAA;;AACnC,QAAIC,SAASD,kBAAkBF,wBAA/B;AACA,SAAKI,YAAL,GAAoB,IAAIC,qBAAJ,CAAgB,IAAhB,EAAsBF,MAAtB,CAApB;AACD;;;;mCAEcG,O,EAAiBC,G,EAA0B;AACxD,aAAO,KAAKH,YAAZ;AACD;;;;;;kBAVkBH,gC", "file": "default_baggage_restriction_manager.js", "sourcesContent": ["// @flow\n// Copyright (c) 2017 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Restriction from './restriction.js';\n\nexport const DEFAULT_MAX_VALUE_LENGTH = 2048;\n\n/**\n * Creates a BaggageRestrictionManager that allows any baggage key.\n */\nexport default class DefaultBaggageRestrictionManager {\n  _restriction: Restriction;\n\n  constructor(maxValueLength: ?number) {\n    let length = maxValueLength || DEFAULT_MAX_VALUE_LENGTH;\n    this._restriction = new Restriction(true, length);\n  }\n\n  getRestriction(service: string, key: string): Restriction {\n    return this._restriction;\n  }\n}\n"]}