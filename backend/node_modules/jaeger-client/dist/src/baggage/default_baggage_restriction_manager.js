'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DEFAULT_MAX_VALUE_LENGTH = undefined;

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
// Copyright (c) 2017 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
// in compliance with the License. You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software distributed under the License
// is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
// or implied. See the License for the specific language governing permissions and limitations under
// the License.

var _restriction = require('./restriction.js');

var _restriction2 = _interopRequireDefault(_restriction);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var DEFAULT_MAX_VALUE_LENGTH = exports.DEFAULT_MAX_VALUE_LENGTH = 2048;

/**
 * Creates a BaggageRestrictionManager that allows any baggage key.
 */

var DefaultBaggageRestrictionManager = function () {
  function DefaultBaggageRestrictionManager(maxValueLength) {
    _classCallCheck(this, DefaultBaggageRestrictionManager);

    var length = maxValueLength || DEFAULT_MAX_VALUE_LENGTH;
    this._restriction = new _restriction2.default(true, length);
  }

  _createClass(DefaultBaggageRestrictionManager, [{
    key: 'getRestriction',
    value: function getRestriction(service, key) {
      return this._restriction;
    }
  }]);

  return DefaultBaggageRestrictionManager;
}();

exports.default = DefaultBaggageRestrictionManager;
//# sourceMappingURL=default_baggage_restriction_manager.js.map