{"version": 3, "sources": ["../../../src/samplers/constants.js"], "names": ["SAMPLER_API_V2"], "mappings": ";;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO,IAAMA,0CAAiB,gBAAvB", "file": "constants.js", "sourcesContent": ["// @flow\n// Copyright (c) 2019 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nexport const SAMPLER_API_V2 = 'SAMPLER_API_V2';\n\nexport type SamplerApiVersion = typeof SAMPLER_API_V2;\n"]}