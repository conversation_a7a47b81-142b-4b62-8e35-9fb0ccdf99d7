{"version": 3, "sources": ["../../../../src/samplers/experimental/tag_equals_sampler.js"], "names": ["TagEqualsSampler", "<PERSON><PERSON><PERSON>", "matchers", "_tagKey", "_matchers", "for<PERSON>ach", "m", "tagValue", "_undecided", "sample", "retryable", "tags", "span", "match", "firehose", "_spanContext", "_setFire<PERSON>e", "_createOutTags", "operationName", "key", "value", "_decide", "strategy", "Object", "keys", "values", "push", "v", "Boolean", "BaseSamplerV2"], "mappings": ";;;;;;;;AAaA;;AACA;;;;AACA;;;;;;;;;;;AAdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAWqBA,gB;;;AAKnB,4BAAYC,MAAZ,EAA4BC,QAA5B,EAAsD;AAAA;;AAAA,oIAC9C,kBAD8C;;AAEpD,UAAKC,OAAL,GAAeF,MAAf;AACA,UAAKG,SAAL,GAAiB,EAAjB;AACAF,aAASG,OAAT,CAAiB,aAAK;AACpB,YAAKD,SAAL,CAAeE,EAAEC,QAAjB,IAA6BD,CAA7B;AACD,KAFD;AAGA,UAAKE,UAAL,GAAkB,EAAEC,QAAQ,KAAV,EAAiBC,WAAW,IAA5B,EAAkCC,MAAM,IAAxC,EAAlB;AAPoD;AAQrD;;AAED;;;;;;;;;;;;;;;;;;;;;mCA6BeJ,Q,EAAwC;AACrD,aAAO;AACL,wBAAgB,kBADX;AAEL,yBAAiBA;AAFZ,OAAP;AAID;;;4BAEOK,I,EAAYL,Q,EAAiC;AACnD,UAAMM,QAAkB,KAAKT,SAAL,CAAeG,QAAf,CAAxB;AACA,UAAIM,KAAJ,EAAW;AACT,YAAIA,MAAMC,QAAV,EAAoB;AAClBF,eAAKG,YAAL,CAAkBC,YAAlB,CAA+B,IAA/B;AACD;AACD,eAAO,EAAEP,QAAQ,IAAV,EAAgBC,WAAW,KAA3B,EAAkCC,MAAM,KAAKM,cAAL,CAAoBJ,MAAMN,QAA1B,CAAxC,EAAP;AACD;AACD,aAAO,KAAKC,UAAZ;AACD;;;iCAEYI,I,EAA8B;AACzC;AACA,aAAO,KAAKJ,UAAZ;AACD;;;uCAEkBI,I,EAAYM,a,EAAyC;AACtE;AACA,aAAO,KAAKV,UAAZ;AACD;;;6BAEQI,I,EAAYO,G,EAAaC,K,EAA8B;AAC9D,UAAID,QAAQ,KAAKhB,OAAjB,EAA0B;AACxB,eAAO,KAAKkB,OAAL,CAAaT,IAAb,EAAmBQ,KAAnB,CAAP;AACD;AACD,aAAO,KAAKZ,UAAZ;AACD;;;iCA7CmBc,Q,EAAiC;AACnD,UAAIH,MAAMG,SAASH,GAAnB;AACA,UAAIjB,WAA2B,EAA/B;AACAqB,aAAOC,IAAP,CAAYF,SAASG,MAArB,EAA6BpB,OAA7B,CAAqC,aAAK;AACxCH,iBAASwB,IAAT,CAAc;AACZnB,oBAAUoB,CADE;AAEZb,oBAAUc,QAAQN,SAASG,MAAT,CAAgBE,CAAhB,EAAmBb,QAA3B;AAFE,SAAd;AAID,OALD;AAMA,aAAO,IAAId,gBAAJ,CAAqBmB,GAArB,EAA0BjB,QAA1B,CAAP;AACD;;;;EA1C2C2B,c;;kBAAzB7B,gB", "file": "tag_equals_sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2019 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport { adaptSamplerOrThrow } from '../_adapt_sampler';\nimport BaseSamplerV2 from '../v2/base';\nimport Span from '../../span';\n\ndeclare type Matcher = {\n  tagValue: string,\n  firehose: boolean,\n};\n\nexport default class TagEqualsSampler extends BaseSamplerV2 {\n  _tagKey: string;\n  _matchers: { [string]: Matcher };\n  _undecided: SamplingDecision;\n\n  constructor(tagKey: string, matchers: Array<Matcher>) {\n    super('TagEqualsSampler');\n    this._tagKey = tagKey;\n    this._matchers = {};\n    matchers.forEach(m => {\n      this._matchers[m.tagValue] = m;\n    });\n    this._undecided = { sample: false, retryable: true, tags: null };\n  }\n\n  /**\n   * Creates the sampler from a JSON configuration of the following form:\n   * <code>\n   *   {\n   *     key: 'taKey',\n   *     values: {\n   *       'tagValue1': {\n   *         firehose: true,\n   *       },\n   *       'tagValue1: {\n   *         firehose: false,\n   *       },\n   *     },\n   *   }\n   * </code>\n   * @param {JSON} strategy\n   */\n  static fromStrategy(strategy: any): TagEqualsSampler {\n    let key = strategy.key;\n    let matchers: Array<Matcher> = [];\n    Object.keys(strategy.values).forEach(v => {\n      matchers.push({\n        tagValue: v,\n        firehose: Boolean(strategy.values[v].firehose),\n      });\n    });\n    return new TagEqualsSampler(key, matchers);\n  }\n\n  _createOutTags(tagValue: string): { [string]: string } {\n    return {\n      'sampler.type': 'TagEqualsSampler',\n      'sampler.param': tagValue,\n    };\n  }\n\n  _decide(span: Span, tagValue: any): SamplingDecision {\n    const match: ?Matcher = this._matchers[tagValue];\n    if (match) {\n      if (match.firehose) {\n        span._spanContext._setFirehose(true);\n      }\n      return { sample: true, retryable: false, tags: this._createOutTags(match.tagValue) };\n    }\n    return this._undecided;\n  }\n\n  onCreateSpan(span: Span): SamplingDecision {\n    // onCreateSpan is called on a brand new span that has no tags yet, so nothing to do here.\n    return this._undecided;\n  }\n\n  onSetOperationName(span: Span, operationName: string): SamplingDecision {\n    // this sampler is not sensitive to operationName, so nothing to do here.\n    return this._undecided;\n  }\n\n  onSetTag(span: Span, key: string, value: any): SamplingDecision {\n    if (key === this._tagKey) {\n      return this._decide(span, value);\n    }\n    return this._undecided;\n  }\n}\n"]}