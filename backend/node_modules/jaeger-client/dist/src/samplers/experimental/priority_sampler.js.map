{"version": 3, "sources": ["../../../../src/samplers/experimental/priority_sampler.js"], "names": ["PrioritySamplerState", "numDelegateSamplers", "samplerFired", "Array", "i", "PrioritySampler", "samplers", "_delegates", "map", "s", "span", "store", "context", "_samplingState", "extendedState", "stateKey", "uniqueName", "state", "length", "fn", "_getOrCreateState", "retryable", "d", "sample", "tags", "_trySampling", "delegate", "onCreateSpan", "operationName", "onSetOperationName", "key", "value", "onSetTag", "callback", "countdownCallback", "Utils", "for<PERSON>ach", "r", "close", "BaseSamplerV2"], "mappings": ";;;;;;;;;AAaA;;AACA;;;;AACA;;;;AACA;;;;;;;;;;;AAfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAOA;;;;;IAKaA,oB,WAAAA,oB,GAGX,8BAAYC,mBAAZ,EAAyC;AAAA;;AACvC,OAAKC,YAAL,GAAoBC,MAAMF,mBAAN,CAApB;AACA;AACA,OAAK,IAAIG,IAAI,CAAb,EAAgBA,IAAIH,mBAApB,EAAyCG,GAAzC,EAA8C;AAC5C,SAAKF,YAAL,CAAkBE,CAAlB,IAAuB,KAAvB;AACD;AACF,C;;AAGH;;;;;;;;;IAOqBC,e;;;AAGnB,2BAAYC,QAAZ,EAAwD;AAAA;;AAAA,kIAChD,iBADgD;;AAEtD,UAAKC,UAAL,GAAkBD,SAASE,GAAT,CAAa;AAAA,aAAK,wCAAoBC,CAApB,CAAL;AAAA,KAAb,CAAlB;AAFsD;AAGvD;;;;sCAEiBC,I,EAAkC;AAClD,UAAMC,QAAQD,KAAKE,OAAL,GAAeC,cAAf,CAA8BC,aAA9B,EAAd;AACA,UAAMC,WAAW,KAAKC,UAAL,EAAjB;AACA,UAAIC,QAA+BN,MAAMI,QAAN,CAAnC;AACA,UAAI,CAACE,KAAL,EAAY;AACVA,gBAAQ,IAAIjB,oBAAJ,CAAyB,KAAKO,UAAL,CAAgBW,MAAzC,CAAR;AACAP,cAAMI,QAAN,IAAkBE,KAAlB;AACD;AACD,aAAOA,KAAP;AACD;;;iCAEYP,I,EAAYS,E,EAAgC;AACvD,UAAMF,QAAQ,KAAKG,iBAAL,CAAuBV,IAAvB,CAAd;AACA,UAAIW,YAAY,KAAhB;AACA,WAAK,IAAIjB,IAAI,CAAb,EAAgBA,IAAI,KAAKG,UAAL,CAAgBW,MAApC,EAA4Cd,GAA5C,EAAiD;AAC/C,YAAIa,MAAMf,YAAN,CAAmBE,CAAnB,CAAJ,EAA2B;AACzB;AACD;AACD,YAAMkB,IAAIH,GAAG,KAAKZ,UAAL,CAAgBH,CAAhB,CAAH,CAAV;AACAiB,oBAAYA,aAAaC,EAAED,SAA3B;AACA,YAAI,CAACC,EAAED,SAAP,EAAkB;AAChBJ,gBAAMf,YAAN,CAAmBE,CAAnB,IAAwB,IAAxB;AACD;AACD,YAAIkB,EAAEC,MAAN,EAAc;AACZ,iBAAOD,CAAP,CADY,CACF;AACX;AACF;AACD,aAAO,EAAEC,QAAQ,KAAV,EAAiBF,WAAWA,SAA5B,EAAuCG,MAAM,IAA7C,EAAP;AACD;;;iCAEYd,I,EAA8B;AACzC,aAAO,KAAKe,YAAL,CAAkBf,IAAlB,EAAwB,UAASgB,QAAT,EAA8C;AAC3E,eAAOA,SAASC,YAAT,CAAsBjB,IAAtB,CAAP;AACD,OAFM,CAAP;AAGD;;;uCAEkBA,I,EAAYkB,a,EAAyC;AACtE,aAAO,KAAKH,YAAL,CAAkBf,IAAlB,EAAwB,UAASgB,QAAT,EAA8C;AAC3E,eAAOA,SAASG,kBAAT,CAA4BnB,IAA5B,EAAkCkB,aAAlC,CAAP;AACD,OAFM,CAAP;AAGD;;;6BAEQlB,I,EAAYoB,G,EAAaC,K,EAA8B;AAC9D,aAAO,KAAKN,YAAL,CAAkBf,IAAlB,EAAwB,UAASgB,QAAT,EAA8C;AAC3E,eAAOA,SAASM,QAAT,CAAkBtB,IAAlB,EAAwBoB,GAAxB,EAA6BC,KAA7B,CAAP;AACD,OAFM,CAAP;AAGD;;;0BAEKE,Q,EAA6B;AACjC,UAAMC,oBAAoBC,eAAMD,iBAAN,CAAwB,KAAK3B,UAAL,CAAgBW,MAAxC,EAAgDe,QAAhD,CAA1B;AACA,WAAK1B,UAAL,CAAgB6B,OAAhB,CAAwB;AAAA,eAAKC,EAAEC,KAAF,CAAQJ,iBAAR,CAAL;AAAA,OAAxB;AACD;;;;EA3D0CK,c;;kBAAxBlC,e", "file": "priority_sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2019 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport { adaptSamplerOrThrow } from '../_adapt_sampler';\nimport BaseSamplerV2 from '../v2/base';\nimport Span from '../../span';\nimport Utils from '../../util';\n\n/**\n * PrioritySamplerState keeps the state of all underlying samplers, specifically\n * whether each of them has previously returned retryable=false, in which case\n * those samplers are no longer invoked on future sampling calls.\n */\nexport class PrioritySamplerState {\n  samplerFired: Array<boolean>;\n\n  constructor(numDelegateSamplers: number) {\n    this.samplerFired = Array(numDelegateSamplers);\n    // TODO: for some reason <PERSON><PERSON> does not translate array.fill() to polyfil that works w/ Node 0.10\n    for (let i = 0; i < numDelegateSamplers; i++) {\n      this.samplerFired[i] = false;\n    }\n  }\n}\n\n/**\n * PrioritySampler contains a list of samplers that it interrogates in order.\n * Sampling methods return as soon as one of the samplers returns sample=true.\n * The retryable state for each underlying sampler is stored in the extended context\n * and once retryable=false is returned by one of the delegates it will never be\n * called against.\n */\nexport default class PrioritySampler extends BaseSamplerV2 {\n  _delegates: Array<Sampler>;\n\n  constructor(samplers: Array<Sampler | LegacySamplerV1>) {\n    super('PrioritySampler');\n    this._delegates = samplers.map(s => adaptSamplerOrThrow(s));\n  }\n\n  _getOrCreateState(span: Span): PrioritySamplerState {\n    const store = span.context()._samplingState.extendedState();\n    const stateKey = this.uniqueName();\n    let state: ?PrioritySamplerState = store[stateKey];\n    if (!state) {\n      state = new PrioritySamplerState(this._delegates.length);\n      store[stateKey] = state;\n    }\n    return state;\n  }\n\n  _trySampling(span: Span, fn: Function): SamplingDecision {\n    const state = this._getOrCreateState(span);\n    let retryable = false;\n    for (let i = 0; i < this._delegates.length; i++) {\n      if (state.samplerFired[i]) {\n        continue;\n      }\n      const d = fn(this._delegates[i]);\n      retryable = retryable || d.retryable;\n      if (!d.retryable) {\n        state.samplerFired[i] = true;\n      }\n      if (d.sample) {\n        return d; // TODO do we want to alter out tags?\n      }\n    }\n    return { sample: false, retryable: retryable, tags: null };\n  }\n\n  onCreateSpan(span: Span): SamplingDecision {\n    return this._trySampling(span, function(delegate: Sampler): SamplingDecision {\n      return delegate.onCreateSpan(span);\n    });\n  }\n\n  onSetOperationName(span: Span, operationName: string): SamplingDecision {\n    return this._trySampling(span, function(delegate: Sampler): SamplingDecision {\n      return delegate.onSetOperationName(span, operationName);\n    });\n  }\n\n  onSetTag(span: Span, key: string, value: any): SamplingDecision {\n    return this._trySampling(span, function(delegate: Sampler): SamplingDecision {\n      return delegate.onSetTag(span, key, value);\n    });\n  }\n\n  close(callback: ?() => void): void {\n    const countdownCallback = Utils.countdownCallback(this._delegates.length, callback);\n    this._delegates.forEach(r => r.close(countdownCallback));\n  }\n}\n"]}