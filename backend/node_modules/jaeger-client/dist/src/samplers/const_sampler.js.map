{"version": 3, "sources": ["../../../src/samplers/const_sampler.js"], "names": ["constants", "ConstSampler", "decision", "_decision", "name", "operation", "tags", "SAMPLER_TYPE_TAG_KEY", "SAMPLER_TYPE_CONST", "SAMPLER_PARAM_TAG_KEY", "other", "LegacySamplerV1Base"], "mappings": ";;;;;;;;AAaA;;IAAYA,S;;AACZ;;;;;;;;;;;;;AAbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAKqBC,Y;;;AAGnB,wBAAYC,QAAZ,EAA+B;AAAA;;AAAA,4HACvB,cADuB;;AAE7B,UAAKC,SAAL,GAAiBD,QAAjB;AAF6B;AAG9B;;;;2BAEc;AACb,aAAO,cAAP;AACD;;;+BAEkB;AACjB,aAAU,KAAKE,IAAL,EAAV,UAAyB,KAAKD,SAAL,GAAiB,QAAjB,GAA4B,OAArD;AACD;;;8BAMSE,S,EAAmBC,I,EAAoB;AAC/C,UAAI,KAAKH,SAAT,EAAoB;AAClBG,aAAKN,UAAUO,oBAAf,IAAuCP,UAAUQ,kBAAjD;AACAF,aAAKN,UAAUS,qBAAf,IAAwC,KAAKN,SAA7C;AACD;AACD,aAAO,KAAKA,SAAZ;AACD;;;0BAEKO,K,EAAiC;AACrC,UAAI,EAAEA,iBAAiBT,YAAnB,CAAJ,EAAsC;AACpC,eAAO,KAAP;AACD;;AAED,aAAO,KAAKC,QAAL,KAAkBQ,MAAMR,QAA/B;AACD;;;wBAlBuB;AACtB,aAAO,KAAKC,SAAZ;AACD;;;;EAlBuCQ,uB;;kBAArBV,Y", "file": "const_sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport * as constants from '../constants';\nimport LegacySamplerV1Base from './_adapt_sampler';\n\nexport default class ConstSampler extends LegacySamplerV1Base implements LegacySamplerV1 {\n  _decision: boolean;\n\n  constructor(decision: boolean) {\n    super('ConstSampler');\n    this._decision = decision;\n  }\n\n  name(): string {\n    return 'ConstSampler';\n  }\n\n  toString(): string {\n    return `${this.name()}(${this._decision ? 'always' : 'never'})`;\n  }\n\n  get decision(): boolean {\n    return this._decision;\n  }\n\n  isSampled(operation: string, tags: any): boolean {\n    if (this._decision) {\n      tags[constants.SAMPLER_TYPE_TAG_KEY] = constants.SAMPLER_TYPE_CONST;\n      tags[constants.SAMPLER_PARAM_TAG_KEY] = this._decision;\n    }\n    return this._decision;\n  }\n\n  equal(other: LegacySamplerV1): boolean {\n    if (!(other instanceof ConstSampler)) {\n      return false;\n    }\n\n    return this.decision === other.decision;\n  }\n}\n"]}