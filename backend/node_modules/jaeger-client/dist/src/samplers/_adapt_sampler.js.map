{"version": 3, "sources": ["../../../src/samplers/_adapt_sampler.js"], "names": ["adaptSampler", "adaptSamplerOrThrow", "sampler", "apiVersion", "SAMPLER_API_V2", "LegacySamplerV1Adapter", "s", "Error", "LegacySamplerV1Base", "name", "operationName", "outTags", "span", "isSampled", "sample", "retryable", "tags", "key", "value", "BaseSamplerV2", "delegate", "_delegate", "toString", "callback", "close"], "mappings": ";;;;;;;;QAiBgBA,Y,GAAAA,Y;QAeAC,mB,GAAAA,mB;;AAnBhB;;AACA;;;;AACA;;;;;;;;;;;AAdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMO,SAASD,YAAT,CAAsBE,OAAtB,EAA8C;AACnD,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,IAAP;AACD;AACD,MAAIA,QAAQC,UAAR,KAAuBC,yBAA3B,EAA2C;AACzC;AACA,WAAOF,OAAP;AACD;AACD,MAAI,CAACA,QAAQC,UAAb,EAAyB;AACvB;AACA,WAAO,IAAIE,sBAAJ,CAA2BH,OAA3B,CAAP;AACD;AACD,SAAO,IAAP;AACD;;AAEM,SAASD,mBAAT,CAA6BC,OAA7B,EAAoD;AACzD,MAAMI,IAAIN,aAAaE,OAAb,CAAV;AACA,MAAI,CAACI,CAAL,EAAQ;AACN,UAAM,IAAIC,KAAJ,4BAAmCL,OAAnC,CAAN;AACD;AACD,SAAOI,CAAP;AACD;;AAED;;;;;IAIqBE,mB;;;AACnB,+BAAYC,IAAZ,EAA0B;AAAA;;AAAA,qIAClBA,IADkB;AAEzB;;;;8BAESC,a,EAAuBC,O,EAAsB;AACrD,YAAM,IAAIJ,KAAJ,CAAU,oCAAV,CAAN;AACD;;;iCAEYK,I,EAA8B;AACzC,UAAMD,UAAU,EAAhB;AACA,UAAME,YAAY,KAAKA,SAAL,CAAeD,KAAKF,aAApB,EAAmCC,OAAnC,CAAlB;AACA,aAAO,EAAEG,QAAQD,SAAV,EAAqBE,WAAW,KAAhC,EAAuCC,MAAML,OAA7C,EAAP;AACD;;;uCAEkBC,I,EAAYF,a,EAAyC;AACtE,UAAMC,UAAU,EAAhB;AACA,UAAME,YAAY,KAAKA,SAAL,CAAeD,KAAKF,aAApB,EAAmCC,OAAnC,CAAlB;AACA,aAAO,EAAEG,QAAQD,SAAV,EAAqBE,WAAW,KAAhC,EAAuCC,MAAML,OAA7C,EAAP;AACD;;;6BAEQC,I,EAAYK,G,EAAaC,K,EAA8B;AAC9D,aAAO,EAAEJ,QAAQ,KAAV,EAAiBC,WAAW,IAA5B,EAAkCC,MAAM,IAAxC,EAAP;AACD;;;;EAvB8CG,c;;AA0BjD;;;;;;;;;;;;kBA1BqBX,mB;;IAoCfH,sB;;;AAIJ,kCAAYe,QAAZ,EAAuC;AAAA;;AAAA,iJAC/BA,SAASX,IAAT,EAD+B;;AAAA,WAHvCN,UAGuC,GAH1BC,yBAG0B;;AAErC,WAAKiB,SAAL,GAAiBD,QAAjB;AAFqC;AAGtC;;;;8BAESV,a,EAAuBC,O,EAAa;AAC5C,aAAO,KAAKU,SAAL,CAAeR,SAAf,CAAyBH,aAAzB,EAAwCC,OAAxC,CAAP;AACD;;;+BAEkB;AACjB,aAAO,KAAKU,SAAL,CAAeC,QAAf,EAAP;AACD;;;0BAEKC,Q,EAAqB;AACzB,WAAKF,SAAL,CAAeG,KAAf,CAAqBD,QAArB;AACD;;;;EAnBkCf,mB", "file": "_adapt_sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2019 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport { SAMPLER_API_V2 } from './constants';\nimport Span from '../span';\nimport BaseSamplerV2 from './v2/base';\n\nexport function adaptSampler(sampler: any): ?Sampler {\n  if (!sampler) {\n    return null;\n  }\n  if (sampler.apiVersion === SAMPLER_API_V2) {\n    // already v2 API compatible\n    return sampler;\n  }\n  if (!sampler.apiVersion) {\n    // v1 legacy sampler\n    return new LegacySamplerV1Adapter(sampler);\n  }\n  return null;\n}\n\nexport function adaptSamplerOrThrow(sampler: any): Sampler {\n  const s = adaptSampler(sampler);\n  if (!s) {\n    throw new Error(`Unrecognized sampler: ${sampler}`);\n  }\n  return s;\n}\n\n/**\n * Convenience base class for simple samplers that implement isSampled() function\n * that is not sensitive to its arguments.\n */\nexport default class LegacySamplerV1Base extends BaseSamplerV2 {\n  constructor(name: string) {\n    super(name);\n  }\n\n  isSampled(operationName: string, outTags: {}): boolean {\n    throw new Error('Subclass must override isSampled()');\n  }\n\n  onCreateSpan(span: Span): SamplingDecision {\n    const outTags = {};\n    const isSampled = this.isSampled(span.operationName, outTags);\n    return { sample: isSampled, retryable: false, tags: outTags };\n  }\n\n  onSetOperationName(span: Span, operationName: string): SamplingDecision {\n    const outTags = {};\n    const isSampled = this.isSampled(span.operationName, outTags);\n    return { sample: isSampled, retryable: false, tags: outTags };\n  }\n\n  onSetTag(span: Span, key: string, value: any): SamplingDecision {\n    return { sample: false, retryable: true, tags: null };\n  }\n}\n\n/**\n * Transforms legacy v1 sampler into V2.\n * Primarily intended for simple samplers that are not sensitive to\n * things like operation names or tags and make a decision only once.\n *\n * However, to keep compatible with existing behavior, onCreateSpan and onSetTag\n * return retryable decision, because previously that's how tracer was behaving,\n * where as onSetOperation() returns retryable=false, since that is what the tracer\n * used to do.\n */\nclass LegacySamplerV1Adapter extends LegacySamplerV1Base {\n  apiVersion = SAMPLER_API_V2;\n  _delegate: LegacySamplerV1;\n\n  constructor(delegate: LegacySamplerV1) {\n    super(delegate.name());\n    this._delegate = delegate;\n  }\n\n  isSampled(operationName: string, outTags: {}) {\n    return this._delegate.isSampled(operationName, outTags);\n  }\n\n  toString(): string {\n    return this._delegate.toString();\n  }\n\n  close(callback: ?Function) {\n    this._delegate.close(callback);\n  }\n}\n"]}