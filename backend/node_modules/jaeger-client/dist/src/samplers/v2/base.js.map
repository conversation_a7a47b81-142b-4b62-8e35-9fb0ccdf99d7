{"version": 3, "sources": ["../../../../src/samplers/v2/base.js"], "names": ["_instanceId", "BaseSamplerV2", "name", "apiVersion", "SAMPLER_API_V2", "_name", "_uniqueName", "_getInstanceId", "_cachedDecision", "sample", "retryable", "tags", "span", "Error", "operationName", "key", "value", "callback"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AACA;;;;;;;;AAEA,IAAIA,cAAc,CAAlB;;IAEqBC,a;AAMnB,yBAAYC,IAAZ,EAA0B;AAAA;;AAAA,SAL1BC,UAK0B,GALbC,yBAKa;;AACxB,SAAKC,KAAL,GAAaH,IAAb;AACA,SAAKI,WAAL,GAAmBL,cAAcM,cAAd,CAA6BL,IAA7B,CAAnB;AACA,SAAKM,eAAL,GAAuB,EAAEC,QAAQ,KAAV,EAAiBC,WAAW,KAA5B,EAAmCC,MAAM,IAAzC,EAAvB;AACD;;;;2BAMM;AACL,aAAO,KAAKN,KAAZ;AACD;;;iCAEY;AACX,aAAO,KAAKC,WAAZ;AACD;;;iCAEYM,I,EAA8B;AACzC,YAAM,IAAIC,KAAJ,CAAa,KAAKX,IAAL,EAAb,sCAAN;AACD;;;uCAEkBU,I,EAAYE,a,EAAyC;AACtE,aAAO,KAAKN,eAAZ;AACD;;;6BAEQI,I,EAAYG,G,EAAaC,K,EAA8B;AAC9D,aAAO,KAAKR,eAAZ;AACD;;;0BAEKS,Q,EAA2B;AAC/B,UAAIA,QAAJ,EAAc;AACZA;AACD;AACF;;;mCA5BqBf,I,EAAc;AAClC,aAAUA,IAAV,SAAkBF,aAAlB;AACD;;;;;;kBAdkBC,a", "file": "base.js", "sourcesContent": ["// @flow\n// Copyright (c) 2019 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport { SAMPLER_API_V2 } from '../constants.js';\nimport Span from '../../span';\n\nlet _instanceId = 0;\n\nexport default class BaseSamplerV2 implements Sampler {\n  apiVersion = SAMPLER_API_V2;\n  _name: string;\n  _uniqueName: string;\n  _cachedDecision: SamplingDecision;\n\n  constructor(name: string) {\n    this._name = name;\n    this._uniqueName = BaseSamplerV2._getInstanceId(name);\n    this._cachedDecision = { sample: false, retryable: false, tags: null };\n  }\n\n  static _getInstanceId(name: string) {\n    return `${name}[${_instanceId++}]`;\n  }\n\n  name() {\n    return this._name;\n  }\n\n  uniqueName() {\n    return this._uniqueName;\n  }\n\n  onCreateSpan(span: Span): SamplingDecision {\n    throw new Error(`${this.name()} does not implement onCreateSpan`);\n  }\n\n  onSetOperationName(span: Span, operationName: string): SamplingDecision {\n    return this._cachedDecision;\n  }\n\n  onSetTag(span: Span, key: string, value: any): SamplingDecision {\n    return this._cachedDecision;\n  }\n\n  close(callback: ?Function): void {\n    if (callback) {\n      callback();\n    }\n  }\n}\n"]}