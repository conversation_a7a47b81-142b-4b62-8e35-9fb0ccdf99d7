{"version": 3, "sources": ["../../../../src/samplers/v2/sampling_state.js"], "names": ["SamplingState", "localRootSpanIdStr", "_extendedState", "_flags", "_final", "_localRootSpanIdStr", "context", "spanIdStr", "value", "Boolean", "SAMPLED_MASK", "enable", "_toggleFlag", "DEBUG_MASK", "FIREHOSE_MASK", "flags", "mask"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AACA;;;;;;;;IAEqBA,a;;AAOnB;;;;;;;;;;;;;;;;;;;;AANA;AA8BA,yBAAYC,kBAAZ,EAAyC;AAAA;;AAAA,SA7BzCC,cA6ByC,GA7BL,EA6BK;AAAA,SA1BzCC,MA0ByC,GA1BxB,CA0BwB;AAAA,SALzCC,MAKyC,GALvB,KAKuB;;AACvC,SAAKC,mBAAL,GAA2BJ,kBAA3B;AACD;;AAED;;;AAPA;;;AAxBA;;;;;oCAgCgBK,O,EAAsB;AACpC,aAAO,KAAKD,mBAAL,KAA6BC,QAAQC,SAA5C;AACD;;;sCAE0B;AACzB,aAAO,KAAKF,mBAAZ;AACD;;;oCAEe;AACd,aAAO,KAAKH,cAAZ;AACD;;;8BAES;AACR,aAAO,KAAKE,MAAZ;AACD;;;6BAEQI,K,EAAgB;AACvB,WAAKJ,MAAL,GAAcI,KAAd;AACD;;;gCAEW;AACV,aAAOC,QAAQ,KAAKN,MAAL,GAAcO,uBAAtB,CAAP;AACD;;;+BAEUC,M,EAAiB;AAC1B,WAAKC,WAAL,CAAiBF,uBAAjB,EAA+BC,MAA/B;AACD;;;8BAES;AACR,aAAOF,QAAQ,KAAKN,MAAL,GAAcU,qBAAtB,CAAP;AACD;;;6BAEQF,M,EAAiB;AACxB,WAAKC,WAAL,CAAiBC,qBAAjB,EAA6BF,MAA7B;AACD;;;iCAEY;AACX,aAAOF,QAAQ,KAAKN,MAAL,GAAcW,wBAAtB,CAAP;AACD;;;gCAEWH,M,EAAiB;AAC3B,WAAKC,WAAL,CAAiBE,wBAAjB,EAAgCH,MAAhC;AACD;;;4BAEO;AACN,aAAO,KAAKR,MAAZ;AACD;;;6BAEQY,K,EAAe;AACtB,WAAKZ,MAAL,GAAcY,KAAd;AACD;;;gCAEWC,I,EAAcL,M,EAAiB;AACzC,UAAIA,MAAJ,EAAY;AACV,aAAKR,MAAL,IAAea,IAAf;AACD,OAFD,MAEO;AACL,aAAKb,MAAL,IAAe,CAACa,IAAhB;AACD;AACF;;;;;;kBA9FkBhB,a", "file": "sampling_state.js", "sourcesContent": ["// @flow\n// Copyright (c) 2019 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport { DEBUG_MASK, FIREHOSE_MASK, SAMPLED_MASK } from '../../constants';\nimport SpanContext from '../../span_context';\n\nexport default class SamplingState {\n  // samplers may store their individual states in this map\n  _extendedState: { [string]: any } = {};\n\n  // shared Flags from span context\n  _flags: number = 0;\n\n  /**\n   * When state is not final, sampling will be retried on other write operations,\n   * and spans will remain writable.\n   *\n   * This field exists to help distinguish between when a span can have a properly\n   * correlated operation name -> sampling rate mapping, and when it cannot.\n   * Adaptive sampling uses the operation name of a span to correlate it with\n   * a sampling rate.  If an operation name is set on a span after the span's creation\n   * then adaptive sampling cannot associate the operation name with the proper sampling rate.\n   * In order to correct this we allow a span to be written to, so that we can re-sample\n   * it in the case that an operation name is set after span creation. Situations\n   * where a span context's sampling decision is finalized include:\n   * - it has inherited the sampling decision from its parent\n   * - its debug flag is set via the sampling.priority tag\n   * - it is finish()-ed\n   * - setOperationName is called\n   * - it is used as a parent for another span\n   * - its context is serialized using injectors\n   */\n  _final: boolean = false;\n\n  // Span ID of the local root span, i.e. the first span in this process for this trace.\n  _localRootSpanIdStr: ?string;\n\n  constructor(localRootSpanIdStr: ?string) {\n    this._localRootSpanIdStr = localRootSpanIdStr;\n  }\n\n  // checks if another span context has the same Span ID as the local root span\n  isLocalRootSpan(context: SpanContext) {\n    return this._localRootSpanIdStr === context.spanIdStr;\n  }\n\n  localRootSpanId(): ?string {\n    return this._localRootSpanIdStr;\n  }\n\n  extendedState() {\n    return this._extendedState;\n  }\n\n  isFinal() {\n    return this._final;\n  }\n\n  setFinal(value: boolean) {\n    this._final = value;\n  }\n\n  isSampled() {\n    return Boolean(this._flags & SAMPLED_MASK);\n  }\n\n  setSampled(enable: boolean) {\n    this._toggleFlag(SAMPLED_MASK, enable);\n  }\n\n  isDebug() {\n    return Boolean(this._flags & DEBUG_MASK);\n  }\n\n  setDebug(enable: boolean) {\n    this._toggleFlag(DEBUG_MASK, enable);\n  }\n\n  isFirehose() {\n    return Boolean(this._flags & FIREHOSE_MASK);\n  }\n\n  setFirehose(enable: boolean) {\n    this._toggleFlag(FIREHOSE_MASK, enable);\n  }\n\n  flags() {\n    return this._flags;\n  }\n\n  setFlags(flags: number) {\n    this._flags = flags;\n  }\n\n  _toggleFlag(mask: number, enable: boolean) {\n    if (enable) {\n      this._flags |= mask;\n    } else {\n      this._flags &= ~mask;\n    }\n  }\n}\n"]}