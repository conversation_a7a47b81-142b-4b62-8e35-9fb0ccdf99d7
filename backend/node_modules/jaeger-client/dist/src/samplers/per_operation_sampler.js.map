{"version": 3, "sources": ["../../../src/samplers/per_operation_sampler.js"], "names": ["PerOperationSampler", "strategies", "maxOperations", "apiVersion", "SAMPLER_API_V2", "_maxOperations", "_samplersByOperation", "Object", "create", "update", "defaultLowerBoundTracesPerSecond", "defaultSamplingProbability", "updated", "_defaultLowerBound", "perOperationStrategies", "for<PERSON>ach", "operation", "strategy", "samplingRate", "probabilisticSampling", "sampler", "GuaranteedThroughputSampler", "defaultSamplingRate", "_defaultSampler", "ProbabilisticSampler", "name", "tags", "keys", "length", "isSampled", "span", "outTags", "context", "_samplingState", "isLocalRootSpan", "operationName", "sample", "retryable", "key", "value", "other", "callback"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;AACA;;;;AACA;;;;;;;;AAIA;AACA;AACA;AACA;AACA;IACqBA,mB;AAOnB,+BAAYC,UAAZ,EAAwDC,aAAxD,EAA+E;AAAA;;AAAA,SAN/EC,UAM+E,GANlEC,yBAMkE;;AAC7E,SAAKC,cAAL,GAAsBH,aAAtB;AACA,SAAKI,oBAAL,GAA4BC,OAAOC,MAAP,CAAc,IAAd,CAA5B;AACA,SAAKC,MAAL,CAAYR,UAAZ;AACD;;;;2BAEMA,U,EAAqD;AAAA;;AAC1D,4BACE,OAAOA,WAAWS,gCAAlB,KAAuD,QADzD,EAEE,mEAFF;AAIA,4BACE,OAAOT,WAAWU,0BAAlB,KAAiD,QADnD,EAEE,6DAFF;;AAKA,UAAIC,UAAmB,KAAKC,kBAAL,KAA4BZ,WAAWS,gCAA9D;AACA,WAAKG,kBAAL,GAA0BZ,WAAWS,gCAArC;AACAT,iBAAWa,sBAAX,CAAkCC,OAAlC,CAA0C,oBAAY;AACpD,YAAIC,YAAYC,SAASD,SAAzB;AACA,YAAIE,eAAeD,SAASE,qBAAT,CAA+BD,YAAlD;AACA,YAAIE,UAAU,MAAKd,oBAAL,CAA0BU,SAA1B,CAAd;AACA,YAAII,OAAJ,EAAa;AACX,cAAIA,QAAQX,MAAR,CAAe,MAAKI,kBAApB,EAAwCK,YAAxC,CAAJ,EAA2D;AACzDN,sBAAU,IAAV;AACD;AACF,SAJD,MAIO;AACLQ,oBAAU,IAAIC,uCAAJ,CAAgC,MAAKR,kBAArC,EAAyDK,YAAzD,CAAV;AACA,gBAAKZ,oBAAL,CAA0BU,SAA1B,IAAuCI,OAAvC;AACAR,oBAAU,IAAV;AACD;AACF,OAbD;AAcA,UAAIU,sBAAsBrB,WAAWU,0BAArC;AACA,UAAI,CAAC,KAAKY,eAAN,IAAyB,KAAKA,eAAL,CAAqBL,YAArB,IAAqCI,mBAAlE,EAAuF;AACrF,aAAKC,eAAL,GAAuB,IAAIC,+BAAJ,CAAyBF,mBAAzB,CAAvB;AACAV,kBAAU,IAAV;AACD;AACD,aAAOA,OAAP;AACD;;;2BAEc;AACb,aAAO,qBAAP;AACD;;;+BAEkB;AACjB,aAAU,KAAKa,IAAL,EAAV,uBAAuC,KAAKpB,cAA5C;AACD;;;8BAESW,S,EAAmBU,I,EAAoB;AAC/C,UAAIN,UAA2B,KAAKd,oBAAL,CAA0BU,SAA1B,CAA/B;AACA,UAAI,CAACI,OAAL,EAAc;AACZ,YAAIb,OAAOoB,IAAP,CAAY,KAAKrB,oBAAjB,EAAuCsB,MAAvC,IAAiD,KAAKvB,cAA1D,EAA0E;AACxE,iBAAO,KAAKkB,eAAL,CAAqBM,SAArB,CAA+Bb,SAA/B,EAA0CU,IAA1C,CAAP;AACD;AACDN,kBAAU,IAAIC,uCAAJ,CAAgC,KAAKR,kBAArC,EAAyD,KAAKU,eAAL,CAAqBL,YAA9E,CAAV;AACA,aAAKZ,oBAAL,CAA0BU,SAA1B,IAAuCI,OAAvC;AACD;AACD,aAAOA,QAAQS,SAAR,CAAkBb,SAAlB,EAA6BU,IAA7B,CAAP;AACD;;;iCAEYI,I,EAA8B;AACzC,UAAMC,UAAU,EAAhB;AACA,UAAIF,YAAY,KAAhB;AACA,UAAIC,KAAKE,OAAL,GAAeC,cAAf,CAA8BC,eAA9B,CAA8CJ,KAAKE,OAAL,EAA9C,CAAJ,EAAmE;AACjEH,oBAAY,KAAKA,SAAL,CAAeC,KAAKK,aAApB,EAAmCJ,OAAnC,CAAZ;AACD;AACD;AACA;AACA,aAAO,EAAEK,QAAQP,SAAV,EAAqBQ,WAAW,IAAhC,EAAsCX,MAAMK,OAA5C,EAAP;AACD;;;uCAEkBD,I,EAAYK,a,EAAyC;AACtE,UAAMJ,UAAU,EAAhB;AACA,UAAIF,YAAY,KAAhB;AACA,UAAIC,KAAKE,OAAL,GAAeC,cAAf,CAA8BC,eAA9B,CAA8CJ,KAAKE,OAAL,EAA9C,CAAJ,EAAmE;AACjEH,oBAAY,KAAKA,SAAL,CAAeC,KAAKK,aAApB,EAAmCJ,OAAnC,CAAZ;AACD;AACD,aAAO,EAAEK,QAAQP,SAAV,EAAqBQ,WAAW,KAAhC,EAAuCX,MAAMK,OAA7C,EAAP;AACD;;;6BAEQD,I,EAAYQ,G,EAAaC,K,EAA8B;AAC9D,aAAO,EAAEH,QAAQ,KAAV,EAAiBC,WAAW,IAA5B,EAAkCX,MAAM,IAAxC,EAAP;AACD;;;0BAEKc,K,EAAiC;AACrC,aAAO,KAAP,CADqC,CACvB;AACf;;;0BAEKC,Q,EAA2B;AAC/B;AACA,UAAIA,QAAJ,EAAc;AACZA;AACD;AACF;;;;;;kBApGkBzC,mB", "file": "per_operation_sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport assert from 'assert';\nimport { SAMPLER_API_V2 } from './constants';\nimport GuaranteedThroughputSampler from './guaranteed_throughput_sampler';\nimport ProbabilisticSampler from './probabilistic_sampler';\n\ntype SamplersByOperation = { [key: string]: GuaranteedThroughputSampler, __proto__: null };\n\n// PerOperationSampler keeps track of all operation names it is asked to sample\n// and uses GuaranteedThroughputSampler for each operation name to ensure\n// that all endpoints are represented in the sampled traces. If the number\n// of distinct operation names exceeds maxOperations, all other names are\n// sampled with a default probabilistic sampler.\nexport default class PerOperationSampler implements Sampler {\n  apiVersion = SAMPLER_API_V2;\n  _maxOperations: number;\n  _samplersByOperation: SamplersByOperation;\n  _defaultSampler: ProbabilisticSampler;\n  _defaultLowerBound: number;\n\n  constructor(strategies: PerOperationSamplingStrategies, maxOperations: number) {\n    this._maxOperations = maxOperations;\n    this._samplersByOperation = Object.create(null);\n    this.update(strategies);\n  }\n\n  update(strategies: PerOperationSamplingStrategies): boolean {\n    assert(\n      typeof strategies.defaultLowerBoundTracesPerSecond === 'number',\n      'expected strategies.defaultLowerBoundTracesPerSecond to be number'\n    );\n    assert(\n      typeof strategies.defaultSamplingProbability === 'number',\n      'expected strategies.defaultSamplingProbability to be number'\n    );\n\n    let updated: boolean = this._defaultLowerBound !== strategies.defaultLowerBoundTracesPerSecond;\n    this._defaultLowerBound = strategies.defaultLowerBoundTracesPerSecond;\n    strategies.perOperationStrategies.forEach(strategy => {\n      let operation = strategy.operation;\n      let samplingRate = strategy.probabilisticSampling.samplingRate;\n      let sampler = this._samplersByOperation[operation];\n      if (sampler) {\n        if (sampler.update(this._defaultLowerBound, samplingRate)) {\n          updated = true;\n        }\n      } else {\n        sampler = new GuaranteedThroughputSampler(this._defaultLowerBound, samplingRate);\n        this._samplersByOperation[operation] = sampler;\n        updated = true;\n      }\n    });\n    let defaultSamplingRate = strategies.defaultSamplingProbability;\n    if (!this._defaultSampler || this._defaultSampler.samplingRate != defaultSamplingRate) {\n      this._defaultSampler = new ProbabilisticSampler(defaultSamplingRate);\n      updated = true;\n    }\n    return updated;\n  }\n\n  name(): string {\n    return 'PerOperationSampler';\n  }\n\n  toString(): string {\n    return `${this.name()}(maxOperations=${this._maxOperations})`;\n  }\n\n  isSampled(operation: string, tags: any): boolean {\n    let sampler: LegacySamplerV1 = this._samplersByOperation[operation];\n    if (!sampler) {\n      if (Object.keys(this._samplersByOperation).length >= this._maxOperations) {\n        return this._defaultSampler.isSampled(operation, tags);\n      }\n      sampler = new GuaranteedThroughputSampler(this._defaultLowerBound, this._defaultSampler.samplingRate);\n      this._samplersByOperation[operation] = sampler;\n    }\n    return sampler.isSampled(operation, tags);\n  }\n\n  onCreateSpan(span: Span): SamplingDecision {\n    const outTags = {};\n    let isSampled = false;\n    if (span.context()._samplingState.isLocalRootSpan(span.context())) {\n      isSampled = this.isSampled(span.operationName, outTags);\n    }\n    // returning retryable=true since we can change the sampling decision\n    // after the first call to setOperationName()\n    return { sample: isSampled, retryable: true, tags: outTags };\n  }\n\n  onSetOperationName(span: Span, operationName: string): SamplingDecision {\n    const outTags = {};\n    let isSampled = false;\n    if (span.context()._samplingState.isLocalRootSpan(span.context())) {\n      isSampled = this.isSampled(span.operationName, outTags);\n    }\n    return { sample: isSampled, retryable: false, tags: outTags };\n  }\n\n  onSetTag(span: Span, key: string, value: any): SamplingDecision {\n    return { sample: false, retryable: true, tags: null };\n  }\n\n  equal(other: LegacySamplerV1): boolean {\n    return false; // TODO equal should be removed\n  }\n\n  close(callback: ?Function): void {\n    // all nested samplers are of simple types, so we do not need to Close them\n    if (callback) {\n      callback();\n    }\n  }\n}\n"]}