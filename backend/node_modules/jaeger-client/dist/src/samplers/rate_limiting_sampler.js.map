{"version": 3, "sources": ["../../../src/samplers/rate_limiting_sampler.js"], "names": ["constants", "RateLimitingSampler", "maxTracesPerSecond", "initBalance", "_init", "prevMaxTracesPerSecond", "_maxTracesPerSecond", "Error", "maxBalance", "_rateLimiter", "update", "RateLimiter", "name", "operation", "tags", "decision", "checkCredit", "SAMPLER_TYPE_TAG_KEY", "SAMPLER_TYPE_RATE_LIMITING", "SAMPLER_PARAM_TAG_KEY", "other", "LegacySamplerV1Base"], "mappings": ";;;;;;;;AAaA;;IAAYA,S;;AACZ;;;;AACA;;;;;;;;;;;;;AAdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAMqBC,mB;;;AAInB,+BAAYC,kBAAZ,EAAwCC,WAAxC,EAA8D;AAAA;;AAAA,0IACtD,qBADsD;;AAE5D,UAAKC,KAAL,CAAWF,kBAAX,EAA+BC,WAA/B;AAF4D;AAG7D;;;;2BAEMD,kB,EAAqC;AAC1C,UAAIG,yBAAyB,KAAKC,mBAAlC;AACA,WAAKF,KAAL,CAAWF,kBAAX;AACA,aAAO,KAAKI,mBAAL,KAA6BD,sBAApC;AACD;;;0BAEKH,kB,EAA4BC,W,EAAsB;AACtD,UAAID,qBAAqB,CAAzB,EAA4B;AAC1B,cAAM,IAAIK,KAAJ,6DAAoEL,kBAApE,CAAN;AACD;AACD,UAAIM,aAAaN,qBAAqB,GAArB,GAA2B,GAA3B,GAAiCA,kBAAlD;;AAEA,WAAKI,mBAAL,GAA2BJ,kBAA3B;AACA,UAAI,KAAKO,YAAT,EAAuB;AACrB,aAAKA,YAAL,CAAkBC,MAAlB,CAAyBR,kBAAzB,EAA6CM,UAA7C;AACD,OAFD,MAEO;AACL,aAAKC,YAAL,GAAoB,IAAIE,sBAAJ,CAAgBT,kBAAhB,EAAoCM,UAApC,EAAgDL,WAAhD,CAApB;AACD;AACF;;;2BAEc;AACb,aAAO,qBAAP;AACD;;;+BAEkB;AACjB,aAAU,KAAKS,IAAL,EAAV,4BAA4C,KAAKN,mBAAjD;AACD;;;8BAMSO,S,EAAmBC,I,EAAoB;AAC/C,UAAIC,WAAW,KAAKN,YAAL,CAAkBO,WAAlB,CAA8B,GAA9B,CAAf;AACA,UAAID,QAAJ,EAAc;AACZD,aAAKd,UAAUiB,oBAAf,IAAuCjB,UAAUkB,0BAAjD;AACAJ,aAAKd,UAAUmB,qBAAf,IAAwC,KAAKb,mBAA7C;AACD;AACD,aAAOS,QAAP;AACD;;;0BAEKK,K,EAAiC;AACrC,UAAI,EAAEA,iBAAiBnB,mBAAnB,CAAJ,EAA6C;AAC3C,eAAO,KAAP;AACD;;AAED,aAAO,KAAKC,kBAAL,KAA4BkB,MAAMlB,kBAAzC;AACD;;;wBAnBgC;AAC/B,aAAO,KAAKI,mBAAZ;AACD;;;;EAvC8Ce,uB;;kBAA5BpB,mB", "file": "rate_limiting_sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport * as constants from '../constants';\nimport LegacySamplerV1Base from './_adapt_sampler';\nimport RateLimiter from '../rate_limiter';\n\nexport default class RateLimitingSampler extends LegacySamplerV1Base implements LegacySamplerV1 {\n  _rateLimiter: RateLimiter;\n  _maxTracesPerSecond: number;\n\n  constructor(maxTracesPerSecond: number, initBalance: ?number) {\n    super('RateLimitingSampler');\n    this._init(maxTracesPerSecond, initBalance);\n  }\n\n  update(maxTracesPerSecond: number): boolean {\n    let prevMaxTracesPerSecond = this._maxTracesPerSecond;\n    this._init(maxTracesPerSecond);\n    return this._maxTracesPerSecond !== prevMaxTracesPerSecond;\n  }\n\n  _init(maxTracesPerSecond: number, initBalance: ?number) {\n    if (maxTracesPerSecond < 0) {\n      throw new Error(`maxTracesPerSecond must be greater than 0.0.  Received ${maxTracesPerSecond}`);\n    }\n    let maxBalance = maxTracesPerSecond < 1.0 ? 1.0 : maxTracesPerSecond;\n\n    this._maxTracesPerSecond = maxTracesPerSecond;\n    if (this._rateLimiter) {\n      this._rateLimiter.update(maxTracesPerSecond, maxBalance);\n    } else {\n      this._rateLimiter = new RateLimiter(maxTracesPerSecond, maxBalance, initBalance);\n    }\n  }\n\n  name(): string {\n    return 'RateLimitingSampler';\n  }\n\n  toString(): string {\n    return `${this.name()}(maxTracesPerSecond=${this._maxTracesPerSecond})`;\n  }\n\n  get maxTracesPerSecond(): number {\n    return this._maxTracesPerSecond;\n  }\n\n  isSampled(operation: string, tags: any): boolean {\n    let decision = this._rateLimiter.checkCredit(1.0);\n    if (decision) {\n      tags[constants.SAMPLER_TYPE_TAG_KEY] = constants.SAMPLER_TYPE_RATE_LIMITING;\n      tags[constants.SAMPLER_PARAM_TAG_KEY] = this._maxTracesPerSecond;\n    }\n    return decision;\n  }\n\n  equal(other: LegacySamplerV1): boolean {\n    if (!(other instanceof RateLimitingSampler)) {\n      return false;\n    }\n\n    return this.maxTracesPerSecond === other.maxTracesPerSecond;\n  }\n}\n"]}