{"version": 3, "sources": ["../../../src/samplers/probabilistic_sampler.js"], "names": ["constants", "ProbabilisticSampler", "samplingRate", "Error", "_samplingRate", "name", "operation", "tags", "decision", "random", "SAMPLER_TYPE_TAG_KEY", "SAMPLER_TYPE_PROBABILISTIC", "SAMPLER_PARAM_TAG_KEY", "Math", "other", "LegacySamplerV1Base"], "mappings": ";;;;;;;;AAaA;;IAAYA,S;;AACZ;;;;;;;;;;;;;AAbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAKqBC,oB;;;AAGnB,gCAAYC,YAAZ,EAAkC;AAAA;;AAAA,4IAC1B,sBAD0B;;AAEhC,QAAIA,eAAe,GAAf,IAAsBA,eAAe,GAAzC,EAA8C;AAC5C,YAAM,IAAIC,KAAJ,8DAAqED,YAArE,CAAN;AACD;;AAED,UAAKE,aAAL,GAAqBF,YAArB;AANgC;AAOjC;;;;2BAEMA,Y,EAA+B;AACpC,UAAI,KAAKE,aAAL,IAAsBF,YAA1B,EAAwC;AACtC,eAAO,KAAP;AACD;AACD,WAAKE,aAAL,GAAqBF,YAArB;AACA,aAAO,IAAP;AACD;;;2BAEc;AACb,aAAO,sBAAP;AACD;;;+BAEkB;AACjB,aAAU,KAAKG,IAAL,EAAV,sBAAsC,KAAKD,aAA3C;AACD;;;8BAMSE,S,EAAmBC,I,EAAoB;AAC/C,UAAIC,WAAW,KAAKC,MAAL,KAAgB,KAAKL,aAApC;AACA,UAAII,QAAJ,EAAc;AACZD,aAAKP,UAAUU,oBAAf,IAAuCV,UAAUW,0BAAjD;AACAJ,aAAKP,UAAUY,qBAAf,IAAwC,KAAKR,aAA7C;AACD;AACD,aAAOI,QAAP;AACD;;;6BAEgB;AACf,aAAOK,KAAKJ,MAAL,EAAP;AACD;;;0BAEKK,K,EAAiC;AACrC,UAAI,EAAEA,iBAAiBb,oBAAnB,CAAJ,EAA8C;AAC5C,eAAO,KAAP;AACD;;AAED,aAAO,KAAKC,YAAL,KAAsBY,MAAMZ,YAAnC;AACD;;;wBAvB0B;AACzB,aAAO,KAAKE,aAAZ;AACD;;;;EA9B+CW,uB;;kBAA7Bd,oB", "file": "probabilistic_sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport * as constants from '../constants';\nimport LegacySamplerV1Base from './_adapt_sampler';\n\nexport default class ProbabilisticSampler extends LegacySamplerV1Base implements LegacySamplerV1 {\n  _samplingRate: number;\n\n  constructor(samplingRate: number) {\n    super('ProbabilisticSampler');\n    if (samplingRate < 0.0 || samplingRate > 1.0) {\n      throw new Error(`The sampling rate must be between 0.0 and 1.0. Received ${samplingRate}`);\n    }\n\n    this._samplingRate = samplingRate;\n  }\n\n  update(samplingRate: number): boolean {\n    if (this._samplingRate == samplingRate) {\n      return false;\n    }\n    this._samplingRate = samplingRate;\n    return true;\n  }\n\n  name(): string {\n    return 'ProbabilisticSampler';\n  }\n\n  toString(): string {\n    return `${this.name()}(samplingRate=${this._samplingRate})`;\n  }\n\n  get samplingRate(): number {\n    return this._samplingRate;\n  }\n\n  isSampled(operation: string, tags: any): boolean {\n    let decision = this.random() < this._samplingRate;\n    if (decision) {\n      tags[constants.SAMPLER_TYPE_TAG_KEY] = constants.SAMPLER_TYPE_PROBABILISTIC;\n      tags[constants.SAMPLER_PARAM_TAG_KEY] = this._samplingRate;\n    }\n    return decision;\n  }\n\n  random(): number {\n    return Math.random();\n  }\n\n  equal(other: LegacySamplerV1): boolean {\n    if (!(other instanceof ProbabilisticSampler)) {\n      return false;\n    }\n\n    return this.samplingRate === other.samplingRate;\n  }\n}\n"]}