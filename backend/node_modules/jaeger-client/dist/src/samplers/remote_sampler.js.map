{"version": 3, "sources": ["../../../src/samplers/remote_sampler.js"], "names": ["DEFAULT_INITIAL_SAMPLING_RATE", "DEFAULT_REFRESH_INTERVAL", "DEFAULT_MAX_OPERATIONS", "DEFAULT_SAMPLING_HOST", "DEFAULT_SAMPLING_PORT", "DEFAULT_SAMPLING_PATH", "PROBABILISTIC_STRATEGY_TYPE", "RATE_LIMITING_STRATEGY_TYPE", "RemoteControlledSampler", "serviceName", "options", "apiVersion", "SAMPLER_API_V2", "_serviceName", "_sampler", "sampler", "ProbabilisticSampler", "_logger", "logger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_metrics", "metrics", "Metrics", "NoopMetricFactory", "_refreshInterval", "refreshInterval", "_maxOperations", "maxOperations", "hostPort", "_parseHostPort", "_host", "host", "_port", "port", "_samplingPath", "samplingPath", "_onSamplerUpdate", "onSamplerUpdate", "randomDelay", "Math", "random", "_initialDelayTimeoutHandle", "setTimeout", "_afterInitialDelay", "bind", "name", "test", "parsedUrl", "url", "parse", "hostname", "parseInt", "_refreshIntervalHandle", "setInterval", "_refreshSamplingStrategy", "encodeURIComponent", "success", "_handleSamplingServerResponse", "body", "error", "err", "samplerQueryFailure", "increment", "Utils", "httpGet", "samplerRetrieved", "strategy", "JSON", "samplerUpdateFailure", "_updateSampler", "samplerUpdated", "response", "operationSampling", "PerOperationSampler", "update", "strategyType", "probabilisticSampling", "samplingRate", "rateLimitingSampling", "maxTracesPerSecond", "RateLimitingSampler", "stringify", "span", "onCreateSpan", "operationName", "onSetOperationName", "key", "value", "onSetTag", "callback", "clearTimeout", "clearInterval"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;AACA;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;;;;;AAEA,IAAMA,gCAAgC,KAAtC;AACA,IAAMC,2BAA2B,KAAjC;AACA,IAAMC,yBAAyB,IAA/B;AACA,IAAMC,wBAAwB,SAA9B;AACA,IAAMC,wBAAwB,IAA9B;AACA,IAAMC,wBAAwB,WAA9B;AACA,IAAMC,8BAA8B,eAApC;AACA,IAAMC,8BAA8B,eAApC;;IAEqBC,uB;;AAkBnB;;;;;;;;;;;;;;;;AAgBA,mCAAYC,WAAZ,EAAoD;AAAA,QAAnBC,OAAmB,uEAAJ,EAAI;;AAAA;;AAAA,SAjCpDC,UAiCoD,GAjCvCC,yBAiCuC;;AAClD,SAAKC,YAAL,GAAoBJ,WAApB;AACA,SAAKK,QAAL,GAAgB,wCACdJ,QAAQK,OAAR,IAAmB,IAAIC,+BAAJ,CAAyBhB,6BAAzB,CADL,CAAhB;AAGA,SAAKiB,OAAL,GAAeP,QAAQQ,MAAR,IAAkB,IAAIC,gBAAJ,EAAjC;AACA,SAAKC,QAAL,GAAgBV,QAAQW,OAAR,IAAmB,IAAIC,iBAAJ,CAAY,IAAIC,wBAAJ,EAAZ,CAAnC;AACA,SAAKC,gBAAL,GAAwBd,QAAQe,eAAR,IAA2BxB,wBAAnD;AACA,SAAKyB,cAAL,GAAsBhB,QAAQiB,aAAR,IAAyBzB,sBAA/C;AACA,QAAIQ,QAAQkB,QAAZ,EAAsB;AACpB,WAAKC,cAAL,CAAoBnB,QAAQkB,QAA5B;AACD,KAFD,MAEO;AACL,WAAKE,KAAL,GAAapB,QAAQqB,IAAR,IAAgB5B,qBAA7B;AACA,WAAK6B,KAAL,GAAatB,QAAQuB,IAAR,IAAgB7B,qBAA7B;AACD;AACD,SAAK8B,aAAL,GAAqBxB,QAAQyB,YAAR,IAAwB9B,qBAA7C;AACA,SAAK+B,gBAAL,GAAwB1B,QAAQ2B,eAAhC;;AAEA,QAAI3B,QAAQe,eAAR,KAA4B,CAAhC,EAAmC;AACjC,UAAIa,cAAsBC,KAAKC,MAAL,KAAgB,KAAKhB,gBAA/C;AACA,WAAKiB,0BAAL,GAAkCC,WAAW,KAAKC,kBAAL,CAAwBC,IAAxB,CAA6B,IAA7B,CAAX,EAA+CN,WAA/C,CAAlC;AACD;AACF;;;;2BAEc;AACb,aAAO,eAAP;AACD;;;+BAEkB;AACjB,aAAU,KAAKO,IAAL,EAAV,qBAAqC,KAAKhC,YAA1C;AACD;;;mCAEce,Q,EAAkB;AAC/BA,iBAAW,QAAQkB,IAAR,CAAalB,QAAb,IAAyBA,QAAzB,eAA8CA,QAAzD;AACA,UAAMmB,YAAYC,cAAIC,KAAJ,CAAUrB,QAAV,CAAlB;;AAEA,WAAKE,KAAL,GAAaiB,UAAUG,QAAV,IAAsB/C,qBAAnC;AACA,WAAK6B,KAAL,GAAae,UAAUd,IAAV,GAAiBkB,SAASJ,UAAUd,IAAnB,CAAjB,GAA4C7B,qBAAzD;AACD;;;yCAE0B;AACzB,WAAKgD,sBAAL,GAA8BC,YAC5B,KAAKC,wBAAL,CAA8BV,IAA9B,CAAmC,IAAnC,CAD4B,EAE5B,KAAKpB,gBAFuB,CAA9B;AAIA,WAAKiB,0BAAL,GAAkC,IAAlC;AACD;;;+CAE0B;AAAA;;AACzB,UAAIhC,cAAsB8C,mBAAmB,KAAK1C,YAAxB,CAA1B;AACA,UAAM2C,UAAoB,SAApBA,OAAoB,OAAQ;AAChC,cAAKC,6BAAL,CAAmCC,IAAnC;AACD,OAFD;AAGA,UAAMC,QAAkB,SAAlBA,KAAkB,MAAO;AAC7B,cAAK1C,OAAL,CAAa0C,KAAb,2CAA2DC,GAA3D;AACA,cAAKxC,QAAL,CAAcyC,mBAAd,CAAkCC,SAAlC,CAA4C,CAA5C;AACD,OAHD;AAIAC,qBAAMC,OAAN,CAAc,KAAKlC,KAAnB,EAA0B,KAAKE,KAA/B,EAAyC,KAAKE,aAA9C,iBAAuEzB,WAAvE,EAAsF+C,OAAtF,EAA+FG,KAA/F;AACD;;;kDAE6BD,I,EAAc;AAC1C,WAAKtC,QAAL,CAAc6C,gBAAd,CAA+BH,SAA/B,CAAyC,CAAzC;AACA,UAAII,iBAAJ;AACA,UAAI;AACFA,mBAAWC,KAAKlB,KAAL,CAAWS,IAAX,CAAX;AACA,YAAI,CAACQ,QAAL,EAAe;AACb,gBAAM,yBAAyBR,IAA/B;AACD;AACF,OALD,CAKE,OAAOC,KAAP,EAAc;AACd,aAAK1C,OAAL,CAAa0C,KAAb,0CAA0DA,KAA1D;AACA,aAAKvC,QAAL,CAAcgD,oBAAd,CAAmCN,SAAnC,CAA6C,CAA7C;AACA;AACD;AACD,UAAI;AACF,YAAI,KAAKO,cAAL,CAAoBH,QAApB,CAAJ,EAAmC;AACjC,eAAK9C,QAAL,CAAckD,cAAd,CAA6BR,SAA7B,CAAuC,CAAvC;AACD;AACF,OAJD,CAIE,OAAOH,KAAP,EAAc;AACd,aAAK1C,OAAL,CAAa0C,KAAb,iCAAiDA,KAAjD;AACA,aAAKvC,QAAL,CAAcgD,oBAAd,CAAmCN,SAAnC,CAA6C,CAA7C;AACA;AACD;AACD,UAAI,KAAK1B,gBAAT,EAA2B;AACzB,aAAKA,gBAAL,CAAsB,KAAKtB,QAA3B;AACD;AACF;;;mCAEcyD,Q,EAA6C;AAC1D,UAAIA,SAASC,iBAAb,EAAgC;AAC9B,YAAI,KAAK1D,QAAL,YAAyB2D,+BAA7B,EAAkD;AAChD,cAAI1D,UAA+B,KAAKD,QAAxC;AACA,iBAAOC,QAAQ2D,MAAR,CAAeH,SAASC,iBAAxB,CAAP;AACD;AACD,aAAK1D,QAAL,GAAgB,IAAI2D,+BAAJ,CAAwBF,SAASC,iBAAjC,EAAoD,KAAK9C,cAAzD,CAAhB;AACA,eAAO,IAAP;AACD;AACD,UAAI6C,SAASI,YAAT,KAA0BrE,2BAA1B,IAAyDiE,SAASK,qBAAtE,EAA6F;AAC3F,YAAIC,eAAeN,SAASK,qBAAT,CAA+BC,YAAlD;AACA,YAAI,KAAK/D,QAAL,YAAyBE,+BAA7B,EAAmD;AACjD,iBAAO,KAAKF,QAAL,CAAc4D,MAAd,CAAqBG,YAArB,CAAP;AACD;AACD,aAAK/D,QAAL,GAAgB,IAAIE,+BAAJ,CAAyB6D,YAAzB,CAAhB;AACA,eAAO,IAAP;AACD;AACD,UAAIN,SAASI,YAAT,KAA0BpE,2BAA1B,IAAyDgE,SAASO,oBAAtE,EAA4F;AAC1F,YAAIC,qBAAqBR,SAASO,oBAAT,CAA8BC,kBAAvD;AACA,YAAI,KAAKjE,QAAL,YAAyBkE,+BAA7B,EAAkD;AAChD,cAAIjE,WAA+B,KAAKD,QAAxC;AACA,iBAAOC,SAAQ2D,MAAR,CAAeK,kBAAf,CAAP;AACD;AACD,aAAKjE,QAAL,GAAgB,IAAIkE,+BAAJ,CAAwBD,kBAAxB,CAAhB;AACA,eAAO,IAAP;AACD;;AAED,YAAM,yBAAyBZ,KAAKc,SAAL,CAAeV,QAAf,CAA/B;AACD;;;iCAEYW,I,EAA8B;AACzC,aAAO,KAAKpE,QAAL,CAAcqE,YAAd,CAA2BD,IAA3B,CAAP;AACD;;;uCAEkBA,I,EAAYE,a,EAAyC;AACtE,aAAO,KAAKtE,QAAL,CAAcuE,kBAAd,CAAiCH,IAAjC,EAAuCE,aAAvC,CAAP;AACD;;;6BAEQF,I,EAAYI,G,EAAaC,K,EAA8B;AAC9D,aAAO,KAAKzE,QAAL,CAAc0E,QAAd,CAAuBN,IAAvB,EAA6BI,GAA7B,EAAkCC,KAAlC,CAAP;AACD;;;0BAEKE,Q,EAA2B;AAC/BC,mBAAa,KAAKjD,0BAAlB;AACAkD,oBAAc,KAAKvC,sBAAnB;;AAEA,UAAIqC,QAAJ,EAAc;AACZA;AACD;AACF;;;;;;kBA1KkBjF,uB", "file": "remote_sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport url from 'url';\nimport { SAMPLER_API_V2 } from './constants';\nimport { adaptSamplerOrThrow } from './_adapt_sampler';\nimport ProbabilisticSampler from './probabilistic_sampler';\nimport RateLimitingSampler from './rate_limiting_sampler';\nimport PerOperationSampler from './per_operation_sampler';\nimport Metrics from '../metrics/metrics';\nimport NullLogger from '../logger';\nimport NoopMetricFactory from '../metrics/noop/metric_factory';\nimport Utils from '../util';\n\nconst DEFAULT_INITIAL_SAMPLING_RATE = 0.001;\nconst DEFAULT_REFRESH_INTERVAL = 60000;\nconst DEFAULT_MAX_OPERATIONS = 2000;\nconst DEFAULT_SAMPLING_HOST = '0.0.0.0';\nconst DEFAULT_SAMPLING_PORT = 5778;\nconst DEFAULT_SAMPLING_PATH = '/sampling';\nconst PROBABILISTIC_STRATEGY_TYPE = 'PROBABILISTIC';\nconst RATE_LIMITING_STRATEGY_TYPE = 'RATE_LIMITING';\n\nexport default class RemoteControlledSampler implements Sampler {\n  apiVersion = SAMPLER_API_V2;\n  _serviceName: string;\n  _sampler: Sampler;\n  _logger: Logger;\n  _metrics: Metrics;\n\n  _refreshInterval: number;\n  _host: string;\n  _port: number;\n  _samplingPath: string;\n  _maxOperations: number;\n\n  _onSamplerUpdate: ?Function;\n\n  _initialDelayTimeoutHandle: any;\n  _refreshIntervalHandle: any;\n\n  /**\n   * Creates a sampler remotely controlled by jaeger-agent.\n   *\n   * @param {string} [serviceName] - name of the current service / application, same as given to Tracer\n   * @param {object} [options] - optional settings\n   * @param {object} [options.sampler] - initial sampler to use prior to retrieving strategies from Agent\n   * @param {object} [options.logger] - optional logger, see _flow/logger.js\n   * @param {object} [options.metrics] - instance of Metrics object\n   * @param {number} [options.refreshInterval] - interval in milliseconds before sampling strategy refreshes (0 to not refresh)\n   * @param {string} [options.hostPort] - host and port for jaeger-agent, defaults to 'localhost:5778'\n   * @param {string} [options.host] - host for jaeger-agent, defaults to 'localhost'\n   * @param {number} [options.port] - port for jaeger-agent for SamplingManager endpoint\n   * @param {string} [options.samplingPath] - path on jaeger-agent for SamplingManager endpoint\n   * @param {number} [options.maxOperations] - max number of operations to track in PerOperationSampler\n   * @param {function} [options.onSamplerUpdate]\n   */\n  constructor(serviceName: string, options: any = {}) {\n    this._serviceName = serviceName;\n    this._sampler = adaptSamplerOrThrow(\n      options.sampler || new ProbabilisticSampler(DEFAULT_INITIAL_SAMPLING_RATE)\n    );\n    this._logger = options.logger || new NullLogger();\n    this._metrics = options.metrics || new Metrics(new NoopMetricFactory());\n    this._refreshInterval = options.refreshInterval || DEFAULT_REFRESH_INTERVAL;\n    this._maxOperations = options.maxOperations || DEFAULT_MAX_OPERATIONS;\n    if (options.hostPort) {\n      this._parseHostPort(options.hostPort);\n    } else {\n      this._host = options.host || DEFAULT_SAMPLING_HOST;\n      this._port = options.port || DEFAULT_SAMPLING_PORT;\n    }\n    this._samplingPath = options.samplingPath || DEFAULT_SAMPLING_PATH;\n    this._onSamplerUpdate = options.onSamplerUpdate;\n\n    if (options.refreshInterval !== 0) {\n      let randomDelay: number = Math.random() * this._refreshInterval;\n      this._initialDelayTimeoutHandle = setTimeout(this._afterInitialDelay.bind(this), randomDelay);\n    }\n  }\n\n  name(): string {\n    return 'RemoteSampler';\n  }\n\n  toString(): string {\n    return `${this.name()}(serviceName=${this._serviceName})`;\n  }\n\n  _parseHostPort(hostPort: string) {\n    hostPort = /^http/.test(hostPort) ? hostPort : `http://${hostPort}`;\n    const parsedUrl = url.parse(hostPort);\n\n    this._host = parsedUrl.hostname || DEFAULT_SAMPLING_HOST;\n    this._port = parsedUrl.port ? parseInt(parsedUrl.port) : DEFAULT_SAMPLING_PORT;\n  }\n\n  _afterInitialDelay(): void {\n    this._refreshIntervalHandle = setInterval(\n      this._refreshSamplingStrategy.bind(this),\n      this._refreshInterval\n    );\n    this._initialDelayTimeoutHandle = null;\n  }\n\n  _refreshSamplingStrategy() {\n    let serviceName: string = encodeURIComponent(this._serviceName);\n    const success: Function = body => {\n      this._handleSamplingServerResponse(body);\n    };\n    const error: Function = err => {\n      this._logger.error(`Error in fetching sampling strategy: ${err}.`);\n      this._metrics.samplerQueryFailure.increment(1);\n    };\n    Utils.httpGet(this._host, this._port, `${this._samplingPath}?service=${serviceName}`, success, error);\n  }\n\n  _handleSamplingServerResponse(body: string) {\n    this._metrics.samplerRetrieved.increment(1);\n    let strategy;\n    try {\n      strategy = JSON.parse(body);\n      if (!strategy) {\n        throw 'Malformed response: ' + body;\n      }\n    } catch (error) {\n      this._logger.error(`Error in parsing sampling strategy: ${error}.`);\n      this._metrics.samplerUpdateFailure.increment(1);\n      return;\n    }\n    try {\n      if (this._updateSampler(strategy)) {\n        this._metrics.samplerUpdated.increment(1);\n      }\n    } catch (error) {\n      this._logger.error(`Error in updating sampler: ${error}.`);\n      this._metrics.samplerUpdateFailure.increment(1);\n      return;\n    }\n    if (this._onSamplerUpdate) {\n      this._onSamplerUpdate(this._sampler);\n    }\n  }\n\n  _updateSampler(response: SamplingStrategyResponse): boolean {\n    if (response.operationSampling) {\n      if (this._sampler instanceof PerOperationSampler) {\n        let sampler: PerOperationSampler = this._sampler;\n        return sampler.update(response.operationSampling);\n      }\n      this._sampler = new PerOperationSampler(response.operationSampling, this._maxOperations);\n      return true;\n    }\n    if (response.strategyType === PROBABILISTIC_STRATEGY_TYPE && response.probabilisticSampling) {\n      let samplingRate = response.probabilisticSampling.samplingRate;\n      if (this._sampler instanceof ProbabilisticSampler) {\n        return this._sampler.update(samplingRate);\n      }\n      this._sampler = new ProbabilisticSampler(samplingRate);\n      return true;\n    }\n    if (response.strategyType === RATE_LIMITING_STRATEGY_TYPE && response.rateLimitingSampling) {\n      let maxTracesPerSecond = response.rateLimitingSampling.maxTracesPerSecond;\n      if (this._sampler instanceof RateLimitingSampler) {\n        let sampler: RateLimitingSampler = this._sampler;\n        return sampler.update(maxTracesPerSecond);\n      }\n      this._sampler = new RateLimitingSampler(maxTracesPerSecond);\n      return true;\n    }\n\n    throw 'Malformed response: ' + JSON.stringify(response);\n  }\n\n  onCreateSpan(span: Span): SamplingDecision {\n    return this._sampler.onCreateSpan(span);\n  }\n\n  onSetOperationName(span: Span, operationName: string): SamplingDecision {\n    return this._sampler.onSetOperationName(span, operationName);\n  }\n\n  onSetTag(span: Span, key: string, value: any): SamplingDecision {\n    return this._sampler.onSetTag(span, key, value);\n  }\n\n  close(callback: ?Function): void {\n    clearTimeout(this._initialDelayTimeoutHandle);\n    clearInterval(this._refreshIntervalHandle);\n\n    if (callback) {\n      callback();\n    }\n  }\n}\n"]}