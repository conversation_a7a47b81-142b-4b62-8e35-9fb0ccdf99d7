{"version": 3, "sources": ["../../../src/samplers/guaranteed_throughput_sampler.js"], "names": ["constants", "GuaranteedThroughputSampler", "lowerBound", "samplingRate", "_probabilisticSampler", "ProbabilisticSampler", "_lowerBoundSampler", "RateLimitingSampler", "_tagsPlaceholder", "name", "maxTracesPerSecond", "operation", "tags", "isSampled", "decision", "SAMPLER_TYPE_TAG_KEY", "SAMPLER_TYPE_LOWER_BOUND", "SAMPLER_PARAM_TAG_KEY", "other", "equal", "callback", "close", "updated", "update"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;IAAYA,S;;AACZ;;;;AACA;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;IACqBC,2B;AAKnB,uCAAYC,UAAZ,EAAgCC,YAAhC,EAAsD;AAAA;;AACpD,SAAKC,qBAAL,GAA6B,IAAIC,+BAAJ,CAAyBF,YAAzB,CAA7B;AACA,SAAKG,kBAAL,GAA0B,IAAIC,+BAAJ,CAAwBL,UAAxB,CAA1B;AACA;AACA;AACA,SAAKM,gBAAL,GAAwB,EAAxB;AACD;;;;2BAEc;AACb,aAAO,6BAAP;AACD;;;+BAEkB;AACjB,aAAU,KAAKC,IAAL,EAAV,sBAAsC,KAAKL,qBAAL,CAA2BD,YAAjE,qBACE,KAAKG,kBAAL,CAAwBI,kBAD1B;AAGD;;;8BAESC,S,EAAmBC,I,EAAoB;AAC/C,UAAI,KAAKR,qBAAL,CAA2BS,SAA3B,CAAqCF,SAArC,EAAgDC,IAAhD,CAAJ,EAA2D;AACzD;AACA,aAAKN,kBAAL,CAAwBO,SAAxB,CAAkCF,SAAlC,EAA6C,KAAKH,gBAAlD;AACA,eAAO,IAAP;AACD;AACD,UAAIM,WAAW,KAAKR,kBAAL,CAAwBO,SAAxB,CAAkCF,SAAlC,EAA6C,KAAKH,gBAAlD,CAAf;AACA,UAAIM,QAAJ,EAAc;AACZF,aAAKZ,UAAUe,oBAAf,IAAuCf,UAAUgB,wBAAjD;AACAJ,aAAKZ,UAAUiB,qBAAf,IAAwC,KAAKb,qBAAL,CAA2BD,YAAnE;AACD;AACD,aAAOW,QAAP;AACD;;;0BAEKI,K,EAAiC;AACrC,UAAI,EAAEA,iBAAiBjB,2BAAnB,CAAJ,EAAqD;AACnD,eAAO,KAAP;AACD;AACD,aACE,KAAKG,qBAAL,CAA2Be,KAA3B,CAAiCD,MAAMd,qBAAvC,KACA,KAAKE,kBAAL,CAAwBa,KAAxB,CAA8BD,MAAMZ,kBAApC,CAFF;AAID;;;0BAEKc,Q,EAA2B;AAC/B;AACA;AACA;AACA,WAAKhB,qBAAL,CAA2BiB,KAA3B,CAAiC,YAAM,CAAE,CAAzC;AACA,WAAKf,kBAAL,CAAwBe,KAAxB,CAA8B,YAAM,CAAE,CAAtC;AACA,UAAID,QAAJ,EAAc;AACZA;AACD;AACF;;;2BAEMlB,U,EAAoBC,Y,EAA+B;AACxD,UAAImB,UAAU,KAAd;AACA,UAAI,KAAKlB,qBAAL,CAA2BD,YAA3B,IAA2CA,YAA/C,EAA6D;AAC3D,aAAKC,qBAAL,GAA6B,IAAIC,+BAAJ,CAAyBF,YAAzB,CAA7B;AACAmB,kBAAU,IAAV;AACD;AACD,UAAI,KAAKhB,kBAAL,CAAwBI,kBAAxB,IAA8CR,UAAlD,EAA8D;AAC5DoB,kBAAU,KAAKhB,kBAAL,CAAwBiB,MAAxB,CAA+BrB,UAA/B,CAAV;AACD;AACD,aAAOoB,OAAP;AACD;;;;;;kBApEkBrB,2B", "file": "guaranteed_throughput_sampler.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport * as constants from '../constants';\nimport ProbabilisticSampler from './probabilistic_sampler';\nimport RateLimitingSampler from './rate_limiting_sampler';\n\n// GuaranteedThroughputProbabilisticSampler is a sampler that leverages both probabilisticSampler and\n// rateLimitingSampler. The rateLimitingSampler is used as a guaranteed lower bound sampler such that\n// every operation is sampled at least once in a time interval defined by the lowerBound. ie a lowerBound\n// of 1.0 / (60 * 10) will sample an operation at least once every 10 minutes.\n//\n// The probabilisticSampler is given higher priority when tags are emitted, ie. if IsSampled() for both\n// samplers return true, the tags for probabilisticSampler will be used.\nexport default class GuaranteedThroughputSampler implements LegacySamplerV1 {\n  _probabilisticSampler: ProbabilisticSampler;\n  _lowerBoundSampler: RateLimitingSampler;\n  _tagsPlaceholder: any;\n\n  constructor(lowerBound: number, samplingRate: number) {\n    this._probabilisticSampler = new ProbabilisticSampler(samplingRate);\n    this._lowerBoundSampler = new RateLimitingSampler(lowerBound);\n    // we never let the lowerBoundSampler return its real tags, so avoid allocations\n    // by reusing the same placeholder object\n    this._tagsPlaceholder = {};\n  }\n\n  name(): string {\n    return 'GuaranteedThroughputSampler';\n  }\n\n  toString(): string {\n    return `${this.name()}(samplingRate=${this._probabilisticSampler.samplingRate}, lowerBound=${\n      this._lowerBoundSampler.maxTracesPerSecond\n    })`;\n  }\n\n  isSampled(operation: string, tags: any): boolean {\n    if (this._probabilisticSampler.isSampled(operation, tags)) {\n      // make rate limiting sampler update its budget\n      this._lowerBoundSampler.isSampled(operation, this._tagsPlaceholder);\n      return true;\n    }\n    let decision = this._lowerBoundSampler.isSampled(operation, this._tagsPlaceholder);\n    if (decision) {\n      tags[constants.SAMPLER_TYPE_TAG_KEY] = constants.SAMPLER_TYPE_LOWER_BOUND;\n      tags[constants.SAMPLER_PARAM_TAG_KEY] = this._probabilisticSampler.samplingRate;\n    }\n    return decision;\n  }\n\n  equal(other: LegacySamplerV1): boolean {\n    if (!(other instanceof GuaranteedThroughputSampler)) {\n      return false;\n    }\n    return (\n      this._probabilisticSampler.equal(other._probabilisticSampler) &&\n      this._lowerBoundSampler.equal(other._lowerBoundSampler)\n    );\n  }\n\n  close(callback: ?Function): void {\n    // neither probabilistic nor rate limiting samplers allocate resources,\n    // so their close methods are effectively no-op. We do not need to\n    // pass the callback to them (if we did we'd need to wrap it).\n    this._probabilisticSampler.close(() => {});\n    this._lowerBoundSampler.close(() => {});\n    if (callback) {\n      callback();\n    }\n  }\n\n  update(lowerBound: number, samplingRate: number): boolean {\n    let updated = false;\n    if (this._probabilisticSampler.samplingRate != samplingRate) {\n      this._probabilisticSampler = new ProbabilisticSampler(samplingRate);\n      updated = true;\n    }\n    if (this._lowerBoundSampler.maxTracesPerSecond != lowerBound) {\n      updated = this._lowerBoundSampler.update(lowerBound);\n    }\n    return updated;\n  }\n}\n"]}