{"version": 3, "sources": ["../../src/test_util.js"], "names": ["opentracing", "TestUtils", "span", "expectedTags", "actualTags", "i", "_tags", "length", "key", "value", "tag", "Object", "prototype", "hasOwnProperty", "call", "console", "log", "keys", "filteredTags"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;IAAYA,W;;AACZ;;;;AACA;;;;;;;;;;IAEqBC,S;;;;;;;4BACJC,I,EAAYC,Y,EAA4B;AACrD;AACA,UAAIC,aAAa,EAAjB;AACA,WAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIH,KAAKI,KAAL,CAAWC,MAA/B,EAAuCF,GAAvC,EAA4C;AAC1C,YAAIG,MAAMN,KAAKI,KAAL,CAAWD,CAAX,EAAcG,GAAxB;AACAJ,mBAAWI,GAAX,IAAkBN,KAAKI,KAAL,CAAWD,CAAX,EAAcI,KAAhC;AACD;;AAED,WAAK,IAAIC,GAAT,IAAgBP,YAAhB,EAA8B;AAC5B,YACEQ,OAAOC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCX,YAArC,EAAmDO,GAAnD,KACAC,OAAOC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCV,UAArC,EAAiDM,GAAjD,CAFF,EAGE;AACA,cAAIN,WAAWM,GAAX,MAAoBP,aAAaO,GAAb,CAAxB,EAA2C;AACzCK,oBAAQC,GAAR,CAAY,eAAZ,EAA6Bb,aAAaO,GAAb,CAA7B,EAAgD,gBAAhD,EAAkEN,WAAWM,GAAX,CAAlE;AACA,mBAAO,KAAP;AACD;AACF,SARD,MAQO;AACL;AACA,iBAAO,KAAP;AACD;AACF;;AAED,aAAO,IAAP;AACD;;AAED;;;;;;;;;4BAMeR,I,EAAYe,I,EAA2B;AACpD,UAAIb,aAAa,EAAjB;AACA,WAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIH,KAAKI,KAAL,CAAWC,MAA/B,EAAuCF,GAAvC,EAA4C;AAC1C,YAAIG,MAAMN,KAAKI,KAAL,CAAWD,CAAX,EAAcG,GAAxB;AACAJ,mBAAWI,GAAX,IAAkBN,KAAKI,KAAL,CAAWD,CAAX,EAAcI,KAAhC;AACD;AACD,UAAIQ,IAAJ,EAAU;AACR,YAAIC,eAAe,EAAnB;AACA,aAAK,IAAIb,KAAI,CAAb,EAAgBA,KAAIY,KAAKV,MAAzB,EAAiCF,IAAjC,EAAsC;AACpC,cAAIG,OAAMS,KAAKZ,EAAL,CAAV;AACA,cAAID,WAAWS,cAAX,CAA0BL,IAA1B,CAAJ,EAAoC;AAClCU,yBAAaV,IAAb,IAAoBJ,WAAWI,IAAX,CAApB;AACD;AACF;AACD,eAAOU,YAAP;AACD;AACD,aAAOd,UAAP;AACD;;;;;;kBAlDkBH,S", "file": "test_util.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport * as opentracing from 'opentracing';\nimport Span from './span';\nimport Utils from './util';\n\nexport default class TestUtils {\n  static hasTags(span: Span, expectedTags: any): boolean {\n    // TODO(oibe) make this work for duplicate tags\n    let actualTags = {};\n    for (let i = 0; i < span._tags.length; i++) {\n      let key = span._tags[i].key;\n      actualTags[key] = span._tags[i].value;\n    }\n\n    for (let tag in expectedTags) {\n      if (\n        Object.prototype.hasOwnProperty.call(expectedTags, tag) &&\n        Object.prototype.hasOwnProperty.call(actualTags, tag)\n      ) {\n        if (actualTags[tag] !== expectedTags[tag]) {\n          console.log('expected tag:', expectedTags[tag], ', actual tag: ', actualTags[tag]);\n          return false;\n        }\n      } else {\n        // mismatch in tag keys\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  /**\n   * Returns tags stored in the span. If tags with the same key are present,\n   * only the last tag is returned.\n   * @param {Object} span - span from which to read the tags.\n   * @param {Array} [keys] - if specified, only tags with these keys are returned.\n   */\n  static getTags(span: Span, keys: ?Array<string>): any {\n    let actualTags = {};\n    for (let i = 0; i < span._tags.length; i++) {\n      let key = span._tags[i].key;\n      actualTags[key] = span._tags[i].value;\n    }\n    if (keys) {\n      let filteredTags = {};\n      for (let i = 0; i < keys.length; i++) {\n        let key = keys[i];\n        if (actualTags.hasOwnProperty(key)) {\n          filteredTags[key] = actualTags[key];\n        }\n      }\n      return filteredTags;\n    }\n    return actualTags;\n  }\n}\n"]}