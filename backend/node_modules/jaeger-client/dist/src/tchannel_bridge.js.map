{"version": 3, "sources": ["../../src/tchannel_bridge.js"], "names": ["constants", "opentracing", "TCHANNEL_TRACING_PREFIX", "TChannelBridge", "tracer", "options", "_tracer", "assert", "equal", "_codec", "TextMapCodec", "urlEncoding", "<PERSON><PERSON>ey", "TRACER_STATE_HEADER_NAME", "baggagePrefix", "TRACER_BAGGAGE_HEADER_PREFIX", "_contextFactory", "contextFactory", "DefaultContext", "_getSpan", "getSpan", "ctx", "_setSpan", "setSpan", "span", "callback", "err", "res", "setTag", "Tags", "ERROR", "log", "event", "message", "finish", "handlerFunc", "perProcessOptions", "request", "headers", "body", "context", "operationName", "arg1", "_extractSpan", "PEER_SERVICE", "callerName", "hostPort", "remoteAddr", "split", "length", "PEER_PORT", "parseInt", "as", "header<PERSON><PERSON><PERSON>", "Object", "keys", "i", "key", "prototype", "hasOwnProperty", "call", "Utils", "startsWith", "wrappingCallback", "_tchannelCallbackWrapper", "bind", "wrappedSend", "channel", "req", "endpoint", "childOf", "clientSpan", "startSpan", "serviceName", "SPAN_KIND", "SPAN_KIND_RPC_CLIENT", "inject", "wrappedRequestMethod", "requestOptions", "parent", "makeFakeTChannelParentSpan", "tchannelRequest", "spanContext", "send", "_wrapTChannelRequest", "_wrapTChannelSend", "traceContext", "extract", "tags", "SPAN_KIND_RPC_SERVER", "id", "traceid", "parentid", "flags"], "mappings": ";;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;IAAYA,S;;AACZ;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;IAAYC,W;;AACZ;;;;AACA;;;;;;;;;;AAEA,IAAIC,0BAA0B,WAA9B;;IAEqBC,c;;AAOnB;;;;;;;AAOA,0BAAYC,MAAZ,EAA+C;AAAA,QAAnBC,OAAmB,uEAAJ,EAAI;;AAAA;;AAC7C,SAAKC,OAAL,GAAeF,MAAf;AACAG,qBAAOC,KAAP,CAAa,QAAb,SAA8BH,OAA9B,yCAA8BA,OAA9B,GAAuC,2BAAvC;AACA,SAAKI,MAAL,GAAc,IAAIC,wBAAJ,CAAiB;AAC7BC,mBAAa,KADgB;AAE7BC,kBAAYV,0BAA0BF,UAAUa,wBAFnB;AAG7BC,qBAAeZ,0BAA0BF,UAAUe;AAHtB,KAAjB,CAAd;AAKA,SAAKC,eAAL,GACEX,QAAQY,cAAR,IACA,YAAW;AACT,aAAO,IAAIC,yBAAJ,EAAP;AACD,KAJH;AAKA,SAAKC,QAAL,GACEd,QAAQe,OAAR,IACA,UAASC,GAAT,EAAc;AACZ,aAAOA,IAAID,OAAJ,EAAP;AACD,KAJH;AAKA,SAAKE,QAAL,GACEjB,QAAQkB,OAAR,IACA,UAASF,GAAT,EAAcG,IAAd,EAAoB;AAClB,aAAOH,IAAIE,OAAJ,CAAYC,IAAZ,CAAP;AACD,KAJH;AAKD;;;;6CAEwBA,I,EAAYC,Q,EAAoBC,G,EAAUC,G,EAAU;AAC3E,UAAID,GAAJ,EAAS;AACPF,aAAKI,MAAL,CAAY3B,YAAY4B,IAAZ,CAAiBC,KAA7B,EAAoC,IAApC;AACAN,aAAKO,GAAL,CAAS;AACPC,iBAAO,OADA;AAEPC,mBAASP;AAFF,SAAT;AAID;;AAEDF,WAAKU,MAAL;AACA,aAAOT,SAASC,GAAT,EAAcC,GAAd,CAAP;AACD;;AAED;;;;;;;;;;;;kCAScQ,W,EAA+C;AAAA;;AAAA,UAA7B9B,OAA6B,uEAAd,EAAc;;AAC3D,aAAO,UAAC+B,iBAAD,EAAoBC,OAApB,EAA6BC,OAA7B,EAAsCC,IAAtC,EAA4Cd,QAA5C,EAAyD;AAC9D,YAAIe,UAAmB,MAAKxB,eAAL,EAAvB;AACA,YAAIyB,gBAAwBpC,QAAQoC,aAAR,IAAyBJ,QAAQK,IAA7D;AACA,YAAIlB,OAAa,MAAKmB,YAAL,CAAkBF,aAAlB,EAAiCH,OAAjC,CAAjB;;AAEA;AACAd,aAAKI,MAAL,CAAY3B,YAAY4B,IAAZ,CAAiBe,YAA7B,EAA2CP,QAAQQ,UAAnD;AACA,YAAIC,WAA0BT,QAAQU,UAAR,CAAmBC,KAAnB,CAAyB,GAAzB,CAA9B;AACA,YAAIF,SAASG,MAAT,IAAmB,CAAvB,EAA0B;AACxBzB,eAAKI,MAAL,CAAY3B,YAAY4B,IAAZ,CAAiBqB,SAA7B,EAAwCC,SAASL,SAAS,CAAT,CAAT,CAAxC;AACD;AACD,YAAIT,QAAQC,OAAR,IAAmBD,QAAQC,OAAR,CAAgBc,EAAvC,EAA2C;AACzC5B,eAAKI,MAAL,CAAY,IAAZ,EAAkBS,QAAQC,OAAR,CAAgBc,EAAlC;AACD;;AAED,cAAK9B,QAAL,CAAckB,OAAd,EAAuBhB,IAAvB;;AAEA;AACA,YAAI6B,aAA4BC,OAAOC,IAAP,CAAYjB,OAAZ,CAAhC;AACA,aAAK,IAAIkB,IAAI,CAAb,EAAgBA,IAAIH,WAAWJ,MAA/B,EAAuCO,GAAvC,EAA4C;AAC1C,cAAIC,MAAMJ,WAAWG,CAAX,CAAV;AACA,cACEF,OAAOI,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCtB,OAArC,EAA8CmB,GAA9C,KACAI,eAAMC,UAAN,CAAiBL,GAAjB,EAAsBvD,uBAAtB,CAFF,EAGE;AACA,mBAAOoC,QAAQmB,GAAR,CAAP;AACD;AACF;;AAED,YAAIM,mBAA6B,MAAKC,wBAAL,CAA8BC,IAA9B,CAAmC,IAAnC,EAAyCzC,IAAzC,EAA+CC,QAA/C,CAAjC;AACAY,gBAAQG,OAAR,GAAkBA,OAAlB;AACAL,oBAAYC,iBAAZ,EAA+BC,OAA/B,EAAwCC,OAAxC,EAAiDC,IAAjD,EAAuDwB,gBAAvD;AACD,OAhCD;AAiCD;;;sCAGCG,W,EACAC,O,EACAC,G,EACAC,Q,EACA/B,O,EACAC,I,EACAd,Q,EACA;AACAa,gBAAUA,WAAW,EAArB;AACA,UAAIE,UAAmB4B,IAAI5B,OAAJ,IAAe,KAAKxB,eAAL,EAAtC;AACA,UAAIsD,UAAgB,KAAKnD,QAAL,CAAcqB,OAAd,CAApB;AACA,UAAI+B,aAAa,KAAKjE,OAAL,CAAakE,SAAb,CAAuBH,QAAvB,EAAiC;AAChDC,iBAASA,OADuC,CAC9B;AAD8B,OAAjC,CAAjB;AAGAC,iBAAW3C,MAAX,CAAkB3B,YAAY4B,IAAZ,CAAiBe,YAAnC,EAAiDwB,IAAIK,WAArD;AACAF,iBAAW3C,MAAX,CAAkB3B,YAAY4B,IAAZ,CAAiB6C,SAAnC,EAA8CzE,YAAY4B,IAAZ,CAAiB8C,oBAA/D;AACA,WAAKC,MAAL,CAAYL,WAAW/B,OAAX,EAAZ,EAAkCF,OAAlC;;AAEA;AACA,UAAIyB,mBAA6B,KAAKC,wBAAL,CAA8BC,IAA9B,CAAmC,IAAnC,EAAyCM,UAAzC,EAAqD9C,QAArD,CAAjC;;AAEA,aAAOyC,YAAYN,IAAZ,CAAiBO,OAAjB,EAA0BC,GAA1B,EAA+BC,QAA/B,EAAyC/B,OAAzC,EAAkDC,IAAlD,EAAwDwB,gBAAxD,CAAP;AACD;;;yCAEoBI,O,EAAcU,oB,EAA2BC,c,EAAqB;AACjF;AACA;AACA;AACAA,qBAAeC,MAAf,GAAwB,EAAEvD,MAAMrB,eAAe6E,0BAAf,EAAR,EAAxB;;AAEA,UAAIC,kBAAuBJ,qBAAqBjB,IAArB,CAA0BO,OAA1B,EAAmCW,cAAnC,CAA3B;AACAG,sBAAgBzC,OAAhB,GAA0BsC,eAAetC,OAAzC;AACA,aAAOyC,eAAP;AACD;;AAED;;;;;;;;2BAKOC,W,EAAkB5C,O,EAAc;AACrC,WAAK7B,MAAL,CAAYmE,MAAZ,CAAmBM,WAAnB,EAAgC5C,OAAhC;AACD;;AAED;;;;;;;;;;kCAOc6B,O,EAAmB;AAC/B,UAAID,cAAcC,QAAQgB,IAA1B;AACA,UAAIN,uBAAuBV,QAAQA,OAAR,CAAgB9B,OAA3C;;AAEA;AACA8B,cAAQA,OAAR,CAAgB9B,OAAhB,GAA0B,KAAK+C,oBAAL,CAA0BnB,IAA1B,CAA+B,IAA/B,EAAqCE,QAAQA,OAA7C,EAAsDU,oBAAtD,CAA1B;;AAEAV,cAAQgB,IAAR,GAAe,KAAKE,iBAAL,CAAuBpB,IAAvB,CAA4B,IAA5B,EAAkCC,WAAlC,EAA+CC,OAA/C,CAAf;AACA,aAAOA,OAAP;AACD;;;iCAWY1B,a,EAAuBH,O,EAAoB;AACtD,UAAIgD,eAA6B,KAAK7E,MAAL,CAAY8E,OAAZ,CAAoBjD,OAApB,CAAjC;AACA,UAAIkD,OAAY,EAAhB;AACAA,WAAKvF,YAAY4B,IAAZ,CAAiB6C,SAAtB,IAAmCzE,YAAY4B,IAAZ,CAAiB4D,oBAApD;AACA,UAAIpF,UAAe;AACjBiE,iBAASgB,YADQ;AAEjBE,cAAMA;AAFW,OAAnB;AAIA,UAAIhE,OAAa,KAAKlB,OAAL,CAAakE,SAAb,CAAuB/B,aAAvB,EAAsCpC,OAAtC,CAAjB;AACA,aAAOmB,IAAP;AACD;;;iDAnBwC;AACvC,aAAO;AACLkE,YAAI,CAAC,CAAD,EAAI,CAAJ,CADC;AAELC,iBAAS,CAAC,CAAD,EAAI,CAAJ,CAFJ;AAGLC,kBAAU,CAAC,CAAD,EAAI,CAAJ,CAHL;AAILC,eAAO;AAJF,OAAP;AAMD;;;;;;kBAvKkB1F,c", "file": "tchannel_bridge.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport assert from 'assert';\nimport * as constants from './constants';\nimport DefaultContext from './default_context';\nimport Span from './span';\nimport SpanContext from './span_context';\nimport Utils from './util';\nimport * as opentracing from 'opentracing';\nimport Tracer from './tracer';\nimport TextMapCodec from './propagators/text_map_codec';\n\nlet TCHANNEL_TRACING_PREFIX = '$tracing$';\n\nexport default class TChannelBridge {\n  _tracer: Tracer;\n  _codec: TextMapCodec;\n  _contextFactory: Function;\n  _getSpan: Function;\n  _setSpan: Function;\n\n  /**\n   * @param {Object} [tracer] - Jaeger Tracer\n   * @param {Object} [options] - options\n   * @param {Function} [options.contextFactory] - function used to create new Context object instead of DefaultContext\n   * @param {Function} [options.getSpan] - function(ctx): Span - used to read Span from Context object; default is ctx.getSpan()\n   * @param {Function} [options.setSpan] - function(ctx, span): void - used to set Span on the Context object; default is ctx.setSpan(span)\n   */\n  constructor(tracer: Tracer, options: any = {}) {\n    this._tracer = tracer;\n    assert.equal('object', typeof options, 'options must be an object');\n    this._codec = new TextMapCodec({\n      urlEncoding: false,\n      contextKey: TCHANNEL_TRACING_PREFIX + constants.TRACER_STATE_HEADER_NAME,\n      baggagePrefix: TCHANNEL_TRACING_PREFIX + constants.TRACER_BAGGAGE_HEADER_PREFIX,\n    });\n    this._contextFactory =\n      options.contextFactory ||\n      function() {\n        return new DefaultContext();\n      };\n    this._getSpan =\n      options.getSpan ||\n      function(ctx) {\n        return ctx.getSpan();\n      };\n    this._setSpan =\n      options.setSpan ||\n      function(ctx, span) {\n        return ctx.setSpan(span);\n      };\n  }\n\n  _tchannelCallbackWrapper(span: Span, callback: Function, err: any, res: any) {\n    if (err) {\n      span.setTag(opentracing.Tags.ERROR, true);\n      span.log({\n        event: 'error',\n        message: err,\n      });\n    }\n\n    span.finish();\n    return callback(err, res);\n  }\n\n  /**\n   * Wraps a tchannel handler, and takes a context in order to populate the incoming context\n   * with a span.\n   *\n   * @param {Function} [handlerFunc] - a tchannel handler function that responds to an incoming request.\n   * @param {Object} [options] - options to be passed to a span on creation.\n   * @returns {Function} - a function that wrapps the handler in order to automatically populate\n   * a the handler's context with a span.\n   **/\n  tracedHandler(handlerFunc: any, options: any = {}): Function {\n    return (perProcessOptions, request, headers, body, callback) => {\n      let context: Context = this._contextFactory();\n      let operationName: string = options.operationName || request.arg1;\n      let span: Span = this._extractSpan(operationName, headers);\n\n      // set tags\n      span.setTag(opentracing.Tags.PEER_SERVICE, request.callerName);\n      let hostPort: Array<string> = request.remoteAddr.split(':');\n      if (hostPort.length == 2) {\n        span.setTag(opentracing.Tags.PEER_PORT, parseInt(hostPort[1]));\n      }\n      if (request.headers && request.headers.as) {\n        span.setTag('as', request.headers.as);\n      }\n\n      this._setSpan(context, span);\n\n      // remove headers prefixed with $tracing$\n      let headerKeys: Array<string> = Object.keys(headers);\n      for (let i = 0; i < headerKeys.length; i++) {\n        let key = headerKeys[i];\n        if (\n          Object.prototype.hasOwnProperty.call(headers, key) &&\n          Utils.startsWith(key, TCHANNEL_TRACING_PREFIX)\n        ) {\n          delete headers[key];\n        }\n      }\n\n      let wrappingCallback: Function = this._tchannelCallbackWrapper.bind(null, span, callback);\n      request.context = context;\n      handlerFunc(perProcessOptions, request, headers, body, wrappingCallback);\n    };\n  }\n\n  _wrapTChannelSend(\n    wrappedSend: Function,\n    channel: any,\n    req: any,\n    endpoint: string,\n    headers: any,\n    body: any,\n    callback: Function\n  ) {\n    headers = headers || {};\n    let context: Context = req.context || this._contextFactory();\n    let childOf: Span = this._getSpan(context);\n    let clientSpan = this._tracer.startSpan(endpoint, {\n      childOf: childOf, // ok if null, will start a new trace\n    });\n    clientSpan.setTag(opentracing.Tags.PEER_SERVICE, req.serviceName);\n    clientSpan.setTag(opentracing.Tags.SPAN_KIND, opentracing.Tags.SPAN_KIND_RPC_CLIENT);\n    this.inject(clientSpan.context(), headers);\n\n    // wrap callback so that span can be finished as soon as the response is received\n    let wrappingCallback: Function = this._tchannelCallbackWrapper.bind(null, clientSpan, callback);\n\n    return wrappedSend.call(channel, req, endpoint, headers, body, wrappingCallback);\n  }\n\n  _wrapTChannelRequest(channel: any, wrappedRequestMethod: any, requestOptions: any) {\n    // We set the parent to a span with trace_id zero, so that tchannel's\n    // outgoing tracing frame also has a trace id of zero.\n    // This forces other tchannel implementations to rely on the headers for the trace context.\n    requestOptions.parent = { span: TChannelBridge.makeFakeTChannelParentSpan() };\n\n    let tchannelRequest: any = wrappedRequestMethod.call(channel, requestOptions);\n    tchannelRequest.context = requestOptions.context;\n    return tchannelRequest;\n  }\n\n  /**\n   * Encode given span context as tchannel headers and store into the headers dictionary.\n   * @param {Object} spanContext - Jaeger SpanContext.\n   * @returns {Object} headers - a dictionary with TChannel application headers.\n   */\n  inject(spanContext: any, headers: any) {\n    this._codec.inject(spanContext, headers);\n  }\n\n  /**\n   * A function that wraps a json, or thrift encoded channel, in order to populate\n   * the outgoing headers with trace context, and baggage information.\n   *\n   * @param {Object} channel - the encoded channel to be wrapped for tracing.\n   * @returns {Object} channel - the trace wrapped channel.\n   * */\n  tracedChannel(channel: any): any {\n    let wrappedSend = channel.send;\n    let wrappedRequestMethod = channel.channel.request;\n\n    // We are patching the top level channel request method, not the encoded request method.\n    channel.channel.request = this._wrapTChannelRequest.bind(this, channel.channel, wrappedRequestMethod);\n\n    channel.send = this._wrapTChannelSend.bind(this, wrappedSend, channel);\n    return channel;\n  }\n\n  static makeFakeTChannelParentSpan(): any {\n    return {\n      id: [0, 0],\n      traceid: [0, 0],\n      parentid: [0, 0],\n      flags: 0,\n    };\n  }\n\n  _extractSpan(operationName: string, headers: any): Span {\n    let traceContext: ?SpanContext = this._codec.extract(headers);\n    let tags: any = {};\n    tags[opentracing.Tags.SPAN_KIND] = opentracing.Tags.SPAN_KIND_RPC_SERVER;\n    let options: any = {\n      childOf: traceContext,\n      tags: tags,\n    };\n    let span: Span = this._tracer.startSpan(operationName, options);\n    return span;\n  }\n}\n"]}