{"version": 3, "sources": ["../../src/configuration_env.js"], "names": ["deprecatedEnvVars", "JAEGER_SAMPLER_HOST", "JAEGER_SAMPLER_PORT", "JAEGER_REPORTER_ENDPOINT", "JAEGER_REPORTER_USER", "JAEGER_REPORTER_PASSWORD", "JAEGER_REPORTER_AGENT_HOST", "JAEGER_REPORTER_AGENT_PORT", "JAEGER_DISABLE", "ConfigurationEnv", "Object", "keys", "for<PERSON>ach", "process", "env", "console", "warn", "obj", "key", "defaultValue", "config", "samplerConfig", "value", "_getConfigValue", "sampler", "JAEGER_SAMPLER_TYPE", "type", "JAEGER_SAMPLER_PARAM", "isNaN", "param", "parseFloat", "JAEGER_SAMPLER_MANAGER_HOST_PORT", "hostPort", "host", "port", "parseInt", "JAEGER_SAMPLER_SAMPLING_PATH", "samplingPath", "JAEGER_SAMPLER_REFRESH_INTERVAL", "refreshIntervalMs", "reporterConfig", "reporter", "JAEGER_REPORTER_LOG_SPANS", "logSpans", "Boolean", "JAEGER_REPORTER_FLUSH_INTERVAL", "flushIntervalMs", "JAEGER_ENDPOINT", "collectorEndpoint", "JAEGER_USER", "username", "JAEGER_PASSWORD", "password", "JAEGER_AGENT_HOST", "agentHost", "JAEGER_REPORTER_TIMEOUT", "timeoutMs", "JAEGER_AGENT_PORT", "agentPort", "JAEGER_AGENT_SOCKET_TYPE", "agentSocketType", "options", "tags", "JAEGER_TAGS", "tagsList", "split", "len", "length", "idx", "kv", "k", "trim", "v", "Utils", "startsWith", "endsWith", "ed", "substring", "_validateEnv", "disable", "JAEGER_DISABLED", "serviceName", "JAEGER_SERVICE_NAME", "_parseTagsFromEnv", "_getSamplerFromEnv", "_getReporterFromEnv", "Configuration", "initTracer"], "mappings": ";;;;;;qjBAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AACA;;;;;;;;AAEA,IAAMA,oBAAoB;AACxBC,uBAAqB,kCADG;AAExBC,uBAAqB,kCAFG;AAGxBC,4BAA0B,iBAHF;AAIxBC,wBAAsB,aAJE;AAKxBC,4BAA0B,iBALF;AAMxBC,8BAA4B,mBANJ;AAOxBC,8BAA4B,mBAPJ;AAQxBC,kBAAgB;AARQ,CAA1B;;IAWqBC,gB;;;;;;;mCACG;AACpBC,aAAOC,IAAP,CAAYX,iBAAZ,EAA+BY,OAA/B,CAAuC,eAAO;AAC5C,YAAIC,QAAQC,GAAR,CAAYA,GAAZ,CAAJ,EAAsB;AACpBC,kBAAQC,IAAR,4CAC2CF,GAD3C,cAEId,kBAAkBc,GAAlB,CAFJ;AAKD;AACF,OARD;AASD;;;oCAEsBG,G,EAAKC,G,EAAKC,Y,EAAc;AAC7C,aAAOF,OAAOC,OAAOD,GAAd,GAAoBA,IAAIC,GAAJ,CAApB,GAA+BC,YAAtC;AACD;;;uCAEyBC,M,EAAQ;AAChC,UAAIC,gBAAgB,EAApB;AACA,UAAIC,QAAQb,iBAAiBc,eAAjB,CAAiCH,OAAOI,OAAxC,EAAiD,MAAjD,EAAyDX,QAAQC,GAAR,CAAYW,mBAArE,CAAZ;AACA,UAAIH,KAAJ,EAAW;AACTD,sBAAcK,IAAd,GAAqBJ,KAArB;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CAAiCH,OAAOI,OAAxC,EAAiD,OAAjD,EAA0DX,QAAQC,GAAR,CAAYa,oBAAtE,CAAR;AACA,UAAI,CAACC,MAAMN,KAAN,CAAL,EAAmB;AACjBD,sBAAcQ,KAAd,GAAsBC,WAAWR,KAAX,CAAtB;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOI,OADD,EAEN,UAFM,EAGNX,QAAQC,GAAR,CAAYiB,gCAHN,CAAR;AAKA,UAAIT,KAAJ,EAAW;AACTD,sBAAcW,QAAd,GAAyBV,KAAzB;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CAAiCH,OAAOI,OAAxC,EAAiD,MAAjD,EAAyDX,QAAQC,GAAR,CAAYb,mBAArE,CAAR;AACA,UAAIqB,KAAJ,EAAW;AACTD,sBAAcY,IAAd,GAAqBX,KAArB;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CAAiCH,OAAOI,OAAxC,EAAiD,MAAjD,EAAyDX,QAAQC,GAAR,CAAYZ,mBAArE,CAAR;AACA,UAAIoB,KAAJ,EAAW;AACTD,sBAAca,IAAd,GAAqBC,SAASb,KAAT,CAArB;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOI,OADD,EAEN,cAFM,EAGNX,QAAQC,GAAR,CAAYsB,4BAHN,CAAR;AAKA,UAAId,KAAJ,EAAW;AACTD,sBAAcgB,YAAd,GAA6Bf,KAA7B;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOI,OADD,EAEN,mBAFM,EAGNX,QAAQC,GAAR,CAAYwB,+BAHN,CAAR;AAKA,UAAIhB,KAAJ,EAAW;AACTD,sBAAckB,iBAAd,GAAkCJ,SAASb,KAAT,CAAlC;AACD;;AAED,aAAOD,aAAP;AACD;;;wCAE0BD,M,EAAQ;AACjC,UAAIoB,iBAAiB,EAArB;AACA,UAAIlB,QAAQb,iBAAiBc,eAAjB,CACVH,OAAOqB,QADG,EAEV,UAFU,EAGV5B,QAAQC,GAAR,CAAY4B,yBAHF,CAAZ;AAKA,UAAIpB,KAAJ,EAAW;AACTkB,uBAAeG,QAAf,GAA0BC,QAAQtB,KAAR,CAA1B;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOqB,QADD,EAEN,iBAFM,EAGN5B,QAAQC,GAAR,CAAY+B,8BAHN,CAAR;AAKA,UAAIvB,KAAJ,EAAW;AACTkB,uBAAeM,eAAf,GAAiCX,SAASb,KAAT,CAAjC;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOqB,QADD,EAEN,mBAFM,EAGN5B,QAAQC,GAAR,CAAYiC,eAAZ,IAA+BlC,QAAQC,GAAR,CAAYX,wBAHrC,CAAR;AAKA,UAAImB,KAAJ,EAAW;AACTkB,uBAAeQ,iBAAf,GAAmC1B,KAAnC;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOqB,QADD,EAEN,UAFM,EAGN5B,QAAQC,GAAR,CAAYmC,WAAZ,IAA2BpC,QAAQC,GAAR,CAAYV,oBAHjC,CAAR;AAKA,UAAIkB,KAAJ,EAAW;AACTkB,uBAAeU,QAAf,GAA0B5B,KAA1B;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOqB,QADD,EAEN,UAFM,EAGN5B,QAAQC,GAAR,CAAYqC,eAAZ,IAA+BtC,QAAQC,GAAR,CAAYT,wBAHrC,CAAR;AAKA,UAAIiB,KAAJ,EAAW;AACTkB,uBAAeY,QAAf,GAA0B9B,KAA1B;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOqB,QADD,EAEN,WAFM,EAGN5B,QAAQC,GAAR,CAAYuC,iBAAZ,IAAiCxC,QAAQC,GAAR,CAAYR,0BAHvC,CAAR;AAKA,UAAIgB,KAAJ,EAAW;AACTkB,uBAAec,SAAf,GAA2BhC,KAA3B;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOqB,QADD,EAEN,WAFM,EAGN5B,QAAQC,GAAR,CAAYyC,uBAHN,CAAR;AAKA,UAAIjC,KAAJ,EAAW;AACTkB,uBAAegB,SAAf,GAA2BrB,SAASb,KAAT,CAA3B;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOqB,QADD,EAEN,WAFM,EAGN5B,QAAQC,GAAR,CAAY2C,iBAAZ,IAAiC5C,QAAQC,GAAR,CAAYP,0BAHvC,CAAR;AAKA,UAAIe,KAAJ,EAAW;AACTkB,uBAAekB,SAAf,GAA2BvB,SAASb,KAAT,CAA3B;AACD;;AAEDA,cAAQb,iBAAiBc,eAAjB,CACNH,OAAOqB,QADD,EAEN,iBAFM,EAGN5B,QAAQC,GAAR,CAAY6C,wBAHN,CAAR;AAKA,UAAIrC,KAAJ,EAAW;AACTkB,uBAAeoB,eAAf,GAAiCtC,KAAjC;AACD;;AAED,aAAOkB,cAAP;AACD;;;sCAEwBqB,O,EAAS;AAChC,UAAIA,QAAQC,IAAZ,EAAkB;AAChB,eAAOD,QAAQC,IAAf;AACD;AACD,UAAIA,OAAO,EAAX;AACA,UAAIjD,QAAQC,GAAR,CAAYiD,WAAhB,EAA6B;AAC3B,YAAIC,WAAWnD,QAAQC,GAAR,CAAYiD,WAAZ,CAAwBE,KAAxB,CAA8B,GAA9B,CAAf;AACA,YAAIC,MAAMF,SAASG,MAAnB;AACA,YAAIC,MAAM,CAAV;AACA,eAAOA,MAAMF,GAAb,EAAkB;AAChB,cAAIG,KAAKL,SAASI,GAAT,EAAcH,KAAd,CAAoB,GAApB,CAAT;AACA,cAAIK,IAAID,GAAG,CAAH,EAAME,IAAN,EAAR;AACA,cAAIC,IAAIH,GAAG,CAAH,EAAME,IAAN,EAAR;AACA,cAAIE,eAAMC,UAAN,CAAiBF,CAAjB,EAAoB,IAApB,KAA6BC,eAAME,QAAN,CAAeH,CAAf,EAAkB,GAAlB,CAAjC,EAAyD;AACvD,gBAAII,KAAKJ,EAAEK,SAAF,CAAY,CAAZ,EAAeL,EAAEL,MAAF,GAAW,CAA1B,EAA6BF,KAA7B,CAAmC,GAAnC,CAAT;AACAO,gBAAI3D,QAAQC,GAAR,CAAY8D,GAAG,CAAH,CAAZ,CAAJ;AACA,gBAAI,CAACJ,CAAD,IAAMI,GAAG,CAAH,MAAU,EAApB,EAAwB;AACtBJ,kBAAII,GAAG,CAAH,CAAJ;AACD;AACF;AACDd,eAAKQ,CAAL,IAAUE,CAAV;AACAJ,iBAAO,CAAP;AACD;AACF;AACD,aAAON,IAAP;AACD;;AAED;;;;;;;;;;iCAO6C;AAAA,UAA3B1C,MAA2B,uEAAlB,EAAkB;AAAA,UAAdyC,OAAc,uEAAJ,EAAI;;AAC3CpD,uBAAiBqE,YAAjB;;AAEA1D,aAAO2D,OAAP,GACE3D,OAAO2D,OAAP,IAAkBlE,QAAQC,GAAR,CAAYkE,eAAZ,KAAgC,MAAlD,IAA4DnE,QAAQC,GAAR,CAAYN,cAAZ,KAA+B,MAD7F;AAEAY,aAAO6D,WAAP,GAAqB7D,OAAO6D,WAAP,IAAsBpE,QAAQC,GAAR,CAAYoE,mBAAvD;;AAEArB,cAAQC,IAAR,GAAerD,iBAAiB0E,iBAAjB,CAAmCtB,OAAnC,CAAf;AACA,UAAIxC,gBAAgBZ,iBAAiB2E,kBAAjB,CAAoChE,MAApC,CAApB;AACA,UAAIV,OAAOC,IAAP,CAAYU,aAAZ,EAA2B8C,MAA3B,GAAoC,CAAxC,EAA2C;AACzC/C,eAAOI,OAAP,GAAiBH,aAAjB;AACD;;AAED,UAAI,CAACwC,QAAQpB,QAAb,EAAuB;AACrB,YAAID,iBAAiB/B,iBAAiB4E,mBAAjB,CAAqCjE,MAArC,EAA6CyC,OAA7C,CAArB;AACA,YAAInD,OAAOC,IAAP,CAAY6B,cAAZ,EAA4B2B,MAA5B,GAAqC,CAAzC,EAA4C;AAC1C/C,iBAAOqB,QAAP,GAAkBD,cAAlB;AACD;AACF;AACD,aAAO8C,wBAAcC,UAAd,CAAyBnE,MAAzB,EAAiCyC,OAAjC,CAAP;AACD;;;;;;kBAjNkBpD,gB", "file": "configuration_env.js", "sourcesContent": ["// Copyright (c) 2018 Jaeger Author.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport Configuration from './configuration.js';\nimport Utils from './util.js';\n\nconst deprecatedEnvVars = {\n  JAEGER_SAMPLER_HOST: 'JAEGER_SAMPLER_MANAGER_HOST_PORT',\n  JAEGER_SAMPLER_PORT: 'JAEGER_SAMPLER_MANAGER_HOST_PORT',\n  JAEGER_REPORTER_ENDPOINT: 'JAEGER_ENDPOINT',\n  JAEGER_REPORTER_USER: 'JAEGER_USER',\n  JA<PERSON><PERSON>_REPORTER_PASSWORD: 'JAEGER_PASSWORD',\n  JA<PERSON>ER_REPORTER_AGENT_HOST: 'JAEGER_AGENT_HOST',\n  JAEGER_REPORTER_AGENT_PORT: 'JAEGER_AGENT_PORT',\n  JAEGER_DISABLE: 'JAEGER_DISABLED',\n};\n\nexport default class ConfigurationEnv {\n  static _validateEnv() {\n    Object.keys(deprecatedEnvVars).forEach(env => {\n      if (process.env[env]) {\n        console.warn(\n          `You are using deprecated env variable ${env}. Use ${\n            deprecatedEnvVars[env]\n          } instead. \\nDeprecated env variable will be removed in the next major release (4.x.x)`\n        );\n      }\n    });\n  }\n\n  static _getConfigValue(obj, key, defaultValue) {\n    return obj && key in obj ? obj[key] : defaultValue;\n  }\n\n  static _getSamplerFromEnv(config) {\n    let samplerConfig = {};\n    let value = ConfigurationEnv._getConfigValue(config.sampler, 'type', process.env.JAEGER_SAMPLER_TYPE);\n    if (value) {\n      samplerConfig.type = value;\n    }\n\n    value = ConfigurationEnv._getConfigValue(config.sampler, 'param', process.env.JAEGER_SAMPLER_PARAM);\n    if (!isNaN(value)) {\n      samplerConfig.param = parseFloat(value);\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.sampler,\n      'hostPort',\n      process.env.JAEGER_SAMPLER_MANAGER_HOST_PORT\n    );\n    if (value) {\n      samplerConfig.hostPort = value;\n    }\n\n    value = ConfigurationEnv._getConfigValue(config.sampler, 'host', process.env.JAEGER_SAMPLER_HOST);\n    if (value) {\n      samplerConfig.host = value;\n    }\n\n    value = ConfigurationEnv._getConfigValue(config.sampler, 'port', process.env.JAEGER_SAMPLER_PORT);\n    if (value) {\n      samplerConfig.port = parseInt(value);\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.sampler,\n      'samplingPath',\n      process.env.JAEGER_SAMPLER_SAMPLING_PATH\n    );\n    if (value) {\n      samplerConfig.samplingPath = value;\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.sampler,\n      'refreshIntervalMs',\n      process.env.JAEGER_SAMPLER_REFRESH_INTERVAL\n    );\n    if (value) {\n      samplerConfig.refreshIntervalMs = parseInt(value);\n    }\n\n    return samplerConfig;\n  }\n\n  static _getReporterFromEnv(config) {\n    let reporterConfig = {};\n    let value = ConfigurationEnv._getConfigValue(\n      config.reporter,\n      'logSpans',\n      process.env.JAEGER_REPORTER_LOG_SPANS\n    );\n    if (value) {\n      reporterConfig.logSpans = Boolean(value);\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.reporter,\n      'flushIntervalMs',\n      process.env.JAEGER_REPORTER_FLUSH_INTERVAL\n    );\n    if (value) {\n      reporterConfig.flushIntervalMs = parseInt(value);\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.reporter,\n      'collectorEndpoint',\n      process.env.JAEGER_ENDPOINT || process.env.JAEGER_REPORTER_ENDPOINT\n    );\n    if (value) {\n      reporterConfig.collectorEndpoint = value;\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.reporter,\n      'username',\n      process.env.JAEGER_USER || process.env.JAEGER_REPORTER_USER\n    );\n    if (value) {\n      reporterConfig.username = value;\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.reporter,\n      'password',\n      process.env.JAEGER_PASSWORD || process.env.JAEGER_REPORTER_PASSWORD\n    );\n    if (value) {\n      reporterConfig.password = value;\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.reporter,\n      'agentHost',\n      process.env.JAEGER_AGENT_HOST || process.env.JAEGER_REPORTER_AGENT_HOST\n    );\n    if (value) {\n      reporterConfig.agentHost = value;\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.reporter,\n      'timeoutMs',\n      process.env.JAEGER_REPORTER_TIMEOUT\n    );\n    if (value) {\n      reporterConfig.timeoutMs = parseInt(value);\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.reporter,\n      'agentPort',\n      process.env.JAEGER_AGENT_PORT || process.env.JAEGER_REPORTER_AGENT_PORT\n    );\n    if (value) {\n      reporterConfig.agentPort = parseInt(value);\n    }\n\n    value = ConfigurationEnv._getConfigValue(\n      config.reporter,\n      'agentSocketType',\n      process.env.JAEGER_AGENT_SOCKET_TYPE\n    );\n    if (value) {\n      reporterConfig.agentSocketType = value;\n    }\n\n    return reporterConfig;\n  }\n\n  static _parseTagsFromEnv(options) {\n    if (options.tags) {\n      return options.tags;\n    }\n    let tags = {};\n    if (process.env.JAEGER_TAGS) {\n      let tagsList = process.env.JAEGER_TAGS.split(',');\n      let len = tagsList.length;\n      let idx = 0;\n      while (idx < len) {\n        let kv = tagsList[idx].split('=');\n        let k = kv[0].trim();\n        let v = kv[1].trim();\n        if (Utils.startsWith(v, '${') && Utils.endsWith(v, '}')) {\n          let ed = v.substring(2, v.length - 1).split(':');\n          v = process.env[ed[0]];\n          if (!v && ed[1] !== '') {\n            v = ed[1];\n          }\n        }\n        tags[k] = v;\n        idx += 1;\n      }\n    }\n    return tags;\n  }\n\n  /**\n   * Initialize and return a new instance of Jaeger Tracer from environment variables.\n   * config or options can be passed to override environment variables.\n   *\n   * @param {Object} config - configuration, see Configuration.initTracer\n   * @param {Object} options - options, see Configuration.initTracer\n   */\n  static initTracer(config = {}, options = {}) {\n    ConfigurationEnv._validateEnv();\n\n    config.disable =\n      config.disable || process.env.JAEGER_DISABLED === 'true' || process.env.JAEGER_DISABLE === 'true';\n    config.serviceName = config.serviceName || process.env.JAEGER_SERVICE_NAME;\n\n    options.tags = ConfigurationEnv._parseTagsFromEnv(options);\n    let samplerConfig = ConfigurationEnv._getSamplerFromEnv(config);\n    if (Object.keys(samplerConfig).length > 0) {\n      config.sampler = samplerConfig;\n    }\n\n    if (!options.reporter) {\n      let reporterConfig = ConfigurationEnv._getReporterFromEnv(config, options);\n      if (Object.keys(reporterConfig).length > 0) {\n        config.reporter = reporterConfig;\n      }\n    }\n    return Configuration.initTracer(config, options);\n  }\n}\n"]}