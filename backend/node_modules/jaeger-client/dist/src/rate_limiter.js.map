{"version": 3, "sources": ["../../src/rate_limiter.js"], "names": ["RateLimiter", "creditsPerSecond", "maxBalance", "initBalance", "_creditsPerSecond", "_balance", "Math", "random", "_maxBalance", "_lastTick", "Date", "getTime", "_updateBalance", "itemCost", "currentTime", "elapsedTime"], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEqBA,W;AAMnB,uBAAYC,gBAAZ,EAAsCC,UAAtC,EAA0DC,WAA1D,EAAgF;AAAA;;AAC9E,SAAKC,iBAAL,GAAyBH,gBAAzB;AACA,SAAKI,QAAL,GAAgBF,eAAeG,KAAKC,MAAL,KAAgBL,UAA/C;AACA,SAAKM,WAAL,GAAmBN,UAAnB;AACA,SAAKO,SAAL,GAAiB,IAAIC,IAAJ,GAAWC,OAAX,EAAjB;AACD;;;;2BAEMV,gB,EAA0BC,U,EAAoB;AACnD,WAAKU,cAAL;AACA,WAAKR,iBAAL,GAAyBH,gBAAzB;AACA;AACA,WAAKI,QAAL,GAAgBH,aAAa,KAAKG,QAAlB,GAA6B,KAAKG,WAAlD;AACA,WAAKA,WAAL,GAAmBN,UAAnB;AACD;;;gCAEWW,Q,EAA2B;AACrC,WAAKD,cAAL;AACA,UAAI,KAAKP,QAAL,IAAiBQ,QAArB,EAA+B;AAC7B,aAAKR,QAAL,IAAiBQ,QAAjB;AACA,eAAO,IAAP;AACD;AACD,aAAO,KAAP;AACD;;;qCAEgB;AACf,UAAIC,cAAsB,IAAIJ,IAAJ,GAAWC,OAAX,EAA1B;AACA,UAAII,cAAsB,CAACD,cAAc,KAAKL,SAApB,IAAiC,IAA3D;AACA,WAAKA,SAAL,GAAiBK,WAAjB;;AAEA,WAAKT,QAAL,IAAiBU,cAAc,KAAKX,iBAApC;AACA,UAAI,KAAKC,QAAL,GAAgB,KAAKG,WAAzB,EAAsC;AACpC,aAAKH,QAAL,GAAgB,KAAKG,WAArB;AACD;AACF;;;;;;kBAvCkBR,W", "file": "rate_limiter.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nexport default class RateLimiter {\n  _creditsPerSecond: number;\n  _balance: number;\n  _maxBalance: number;\n  _lastTick: number;\n\n  constructor(creditsPerSecond: number, maxBalance: number, initBalance: ?number) {\n    this._creditsPerSecond = creditsPerSecond;\n    this._balance = initBalance || Math.random() * maxBalance;\n    this._maxBalance = maxBalance;\n    this._lastTick = new Date().getTime();\n  }\n\n  update(creditsPerSecond: number, maxBalance: number) {\n    this._updateBalance();\n    this._creditsPerSecond = creditsPerSecond;\n    // The new balance should be proportional to the old balance.\n    this._balance = maxBalance * this._balance / this._maxBalance;\n    this._maxBalance = maxBalance;\n  }\n\n  checkCredit(itemCost: number): boolean {\n    this._updateBalance();\n    if (this._balance >= itemCost) {\n      this._balance -= itemCost;\n      return true;\n    }\n    return false;\n  }\n\n  _updateBalance() {\n    let currentTime: number = new Date().getTime();\n    let elapsedTime: number = (currentTime - this._lastTick) / 1000;\n    this._lastTick = currentTime;\n\n    this._balance += elapsedTime * this._creditsPerSecond;\n    if (this._balance > this._maxBalance) {\n      this._balance = this._maxBalance;\n    }\n  }\n}\n"]}