{"version": 3, "sources": ["../../src/tracer.js"], "names": ["opentracing", "constants", "Tracer", "serviceName", "reporter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sampler", "ConstSampler", "options", "_tags", "tags", "Utils", "clone", "JAEGER_CLIENT_VERSION_TAG_KEY", "version", "TRACER_HOSTNAME_TAG_KEY", "os", "hostname", "PROCESS_IP", "myIp", "_metrics", "metrics", "Metrics", "NoopMetricFactory", "_serviceName", "_reporter", "_sampler", "_logger", "logger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_baggageSetter", "BaggageSetter", "baggageRestrictionManager", "DefaultBaggageRestrictionManager", "_debugThrottler", "debugThrottler", "DefaultThrottler", "_injectors", "_extractors", "codecOptions", "<PERSON><PERSON>ey", "baggagePrefix", "urlEncoding", "textCodec", "TextMapCodec", "registerInjector", "FORMAT_TEXT_MAP", "registerExtractor", "httpCodec", "FORMAT_HTTP_HEADERS", "binaryCodec", "BinaryCodec", "FORMAT_BINARY", "uuid", "TRACER_CLIENT_ID_TAG_KEY", "_process", "convertObjectToTags", "setProcess", "_traceId128bit", "traceId128bit", "_shareRpcSpan", "shareRpcSpan", "spanContext", "operationName", "startTime", "userTags", "internalTags", "references", "had<PERSON>arent", "isRpcServer", "span", "Span", "_spanContext", "samplingFinalized", "decision", "onCreateSpan", "_applySamplingDecision", "addTags", "_appendTags", "context", "isSampled", "spansStartedSampled", "increment", "tracesStartedSampled", "tracesJoinedSampled", "spansStartedNotSampled", "tracesStartedNotSampled", "tracesJoinedNotSampled", "spansFinished", "report", "format", "injector", "extractor", "now", "followsFromIsParent", "parent", "childOf", "i", "length", "ref", "type", "REFERENCE_CHILD_OF", "referencedContext", "REFERENCE_FOLLOWS_FROM", "ctx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Boolean", "otTags", "SPAN_KIND", "SPAN_KIND_RPC_SERVER", "<PERSON><PERSON><PERSON><PERSON>", "randomId", "getRandom128", "SpanContext", "slice", "getRandom64", "isDebugIDContainerOnly", "_isDebugAllowed", "_setSampled", "_setDebug", "JAEGER_DEBUG_HEADER", "debugId", "baggage", "spanId", "_makeChildContext", "parentId", "isRemote", "finalizeSampling", "_startInternalSpan", "carrier", "Error", "_context", "inject", "extract", "_setRemote", "callback", "close", "Date", "operation", "isAllowed"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;IAAYA,W;;AAEZ;;;;AACA;;AACA;;;;AACA;;;;AACA;;IAAYC,S;;AACZ;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;;;;;;;IAEqBC,M;;AAenB;;;;;;;;;;;;;;;;AAgBA,kBACEC,WADF,EAKE;AAAA,QAHAC,QAGA,uEAHqB,IAAIC,uBAAJ,EAGrB;AAAA,QAFAC,OAEA,uEAFqC,IAAIC,uBAAJ,CAAiB,KAAjB,CAErC;AAAA,QADAC,OACA,uEADe,EACf;;AAAA;;AACA,SAAKC,KAAL,GAAaD,QAAQE,IAAR,GAAeC,eAAMC,KAAN,CAAYJ,QAAQE,IAApB,CAAf,GAA2C,EAAxD;AACA,SAAKD,KAAL,CAAWR,UAAUY,6BAArB,cAA8DC,iBAA9D;AACA,SAAKL,KAAL,CAAWR,UAAUc,uBAArB,IACE,KAAKN,KAAL,CAAWR,UAAUc,uBAArB,KAAiDC,aAAGC,QAAH,EADnD;AAEA,SAAKR,KAAL,CAAWR,UAAUiB,UAArB,IAAmC,KAAKT,KAAL,CAAWR,UAAUiB,UAArB,KAAoCP,eAAMQ,IAAN,EAAvE;;AAEA,SAAKC,QAAL,GAAgBZ,QAAQa,OAAR,IAAmB,IAAIC,iBAAJ,CAAY,IAAIC,wBAAJ,EAAZ,CAAnC;;AAEA,SAAKC,YAAL,GAAoBrB,WAApB;AACA,SAAKsB,SAAL,GAAiBrB,QAAjB;AACA;AACA,SAAKsB,QAAL,GAAgB,wCAAoBpB,OAApB,CAAhB;AACA,SAAKqB,OAAL,GAAenB,QAAQoB,MAAR,IAAkB,IAAIC,gBAAJ,EAAjC;AACA,SAAKC,cAAL,GAAsB,IAAIC,wBAAJ,CACpBvB,QAAQwB,yBAAR,IAAqC,IAAIC,6CAAJ,EADjB,EAEpB,KAAKb,QAFe,CAAtB;AAIA,SAAKc,eAAL,GAAuB1B,QAAQ2B,cAAR,IAA0B,IAAIC,2BAAJ,CAAqB,KAArB,CAAjD;AACA,SAAKC,UAAL,GAAkB,EAAlB;AACA,SAAKC,WAAL,GAAmB,EAAnB;;AAEA,QAAIC,eAAe;AACjBC,kBAAYhC,QAAQgC,UAAR,IAAsB,IADjB;AAEjBC,qBAAejC,QAAQiC,aAAR,IAAyB,IAFvB;AAGjBC,mBAAa,KAHI;AAIjBrB,eAAS,KAAKD;AAJG,KAAnB;;AAOA,QAAIuB,YAAY,IAAIC,wBAAJ,CAAiBL,YAAjB,CAAhB;AACA,SAAKM,gBAAL,CAAsB7C,YAAY8C,eAAlC,EAAmDH,SAAnD;AACA,SAAKI,iBAAL,CAAuB/C,YAAY8C,eAAnC,EAAoDH,SAApD;;AAEAJ,iBAAaG,WAAb,GAA2B,IAA3B;;AAEA,QAAIM,YAAY,IAAIJ,wBAAJ,CAAiBL,YAAjB,CAAhB;AACA,SAAKM,gBAAL,CAAsB7C,YAAYiD,mBAAlC,EAAuDD,SAAvD;AACA,SAAKD,iBAAL,CAAuB/C,YAAYiD,mBAAnC,EAAwDD,SAAxD;;AAEA,QAAIE,cAAc,IAAIC,sBAAJ,EAAlB;AACA,SAAKN,gBAAL,CAAsB7C,YAAYoD,aAAlC,EAAiDF,WAAjD;AACA,SAAKH,iBAAL,CAAuB/C,YAAYoD,aAAnC,EAAkDF,WAAlD;;AAEA,QAAMG,OAAO,eAAb;AACA,SAAK5C,KAAL,CAAWR,UAAUqD,wBAArB,IAAiDD,IAAjD;AACA,SAAKE,QAAL,GAAgB;AACdpD,mBAAaA,WADC;AAEdO,YAAMC,eAAM6C,mBAAN,CAA0B,KAAK/C,KAA/B,CAFQ;AAGd4C,YAAMA;AAHQ,KAAhB;AAKA,SAAKnB,eAAL,CAAqBuB,UAArB,CAAgC,KAAKF,QAArC;AACA;AACA,SAAK9B,SAAL,CAAegC,UAAf,CAA0B,KAAKF,QAAL,CAAcpD,WAAxC,EAAqD,KAAKoD,QAAL,CAAc7C,IAAnE;;AAEA,SAAKgD,cAAL,GAAsBlD,QAAQmD,aAA9B;AACA,SAAKC,aAAL,GAAqBpD,QAAQqD,YAA7B;AACD;;;;uCAGCC,W,EACAC,a,EACAC,S,EACAC,Q,EACAC,Y,EACAC,U,EACAC,S,EACAC,W,EACM;AACN,UAAMC,OAAO,IAAIC,cAAJ,CAAS,IAAT,EAAeR,aAAf,EAA8BD,WAA9B,EAA2CE,SAA3C,EAAsDG,UAAtD,CAAb;AACA,UAAI,CAACG,KAAKE,YAAL,CAAkBC,iBAAvB,EAA0C;AACxC,YAAMC,WAAW,KAAKhD,QAAL,CAAciD,YAAd,CAA2BL,IAA3B,CAAjB;AACAA,aAAKM,sBAAL,CAA4BF,QAA5B;AACD;;AAED,UAAIT,QAAJ,EAAc;AACZK,aAAKO,OAAL,CAAaZ,QAAb;AACD;AACD,UAAIC,YAAJ,EAAkB;AAChBI,aAAKQ,WAAL,CAAiBZ,YAAjB,EADgB,CACgB;AACjC;;AAED;AACA,UAAII,KAAKS,OAAL,GAAeC,SAAf,EAAJ,EAAgC;AAC9B,aAAK5D,QAAL,CAAc6D,mBAAd,CAAkCC,SAAlC,CAA4C,CAA5C;AACA,YAAI,CAACd,SAAL,EAAgB;AACd,eAAKhD,QAAL,CAAc+D,oBAAd,CAAmCD,SAAnC,CAA6C,CAA7C;AACD,SAFD,MAEO,IAAIb,WAAJ,EAAiB;AACtB,eAAKjD,QAAL,CAAcgE,mBAAd,CAAkCF,SAAlC,CAA4C,CAA5C;AACD;AACF,OAPD,MAOO;AACL,aAAK9D,QAAL,CAAciE,sBAAd,CAAqCH,SAArC,CAA+C,CAA/C;AACA,YAAI,CAACd,SAAL,EAAgB;AACd,eAAKhD,QAAL,CAAckE,uBAAd,CAAsCJ,SAAtC,CAAgD,CAAhD;AACD,SAFD,MAEO,IAAIb,WAAJ,EAAiB;AACtB,eAAKjD,QAAL,CAAcmE,sBAAd,CAAqCL,SAArC,CAA+C,CAA/C;AACD;AACF;;AAED,aAAOZ,IAAP;AACD;;;4BAEOA,I,EAAkB;AACxB,WAAKlD,QAAL,CAAcoE,aAAd,CAA4BN,SAA5B,CAAsC,CAAtC;AACA,WAAKzD,SAAL,CAAegE,MAAf,CAAsBnB,IAAtB;AACD;;;qCAEgBoB,M,EAAgBC,Q,EAA0B;AACzD,WAAKtD,UAAL,CAAgBqD,MAAhB,IAA0BC,QAA1B;AACD;;;sCAEiBD,M,EAAgBE,S,EAA4B;AAC5D,WAAKtD,WAAL,CAAiBoD,MAAjB,IAA2BE,SAA3B;AACD;;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAwBU7B,a,EAAuBvD,O,EAAkC;AACjEA,gBAAUA,WAAW,EAArB;AACA,UAAI2D,aAAa3D,QAAQ2D,UAAR,IAAsB,EAAvC;;AAEA,UAAIF,WAAWzD,QAAQE,IAAvB;AACA,UAAIsD,YAAYxD,QAAQwD,SAAR,IAAqB,KAAK6B,GAAL,EAArC;;AAEA;AACA;AACA,UAAIC,sBAAsB,KAA1B;AACA,UAAIC,SAAuBvF,QAAQwF,OAAR,YAA2BzB,cAA3B,GAAkC/D,QAAQwF,OAAR,CAAgBjB,OAAhB,EAAlC,GAA8DvE,QAAQwF,OAAjG;AACA;AACA,WAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAI9B,WAAW+B,MAA/B,EAAuCD,GAAvC,EAA4C;AAC1C,YAAIE,MAAiBhC,WAAW8B,CAAX,CAArB;AACA,YAAIE,IAAIC,IAAJ,OAAepG,YAAYqG,kBAA/B,EAAmD;AACjD,cAAI,CAACN,MAAD,IAAWD,mBAAf,EAAoC;AAClCC,qBAASI,IAAIG,iBAAJ,EAAT;AACA;AACD;AACF,SALD,MAKO,IAAIH,IAAIC,IAAJ,OAAepG,YAAYuG,sBAA/B,EAAuD;AAC5D,cAAI,CAACR,MAAL,EAAa;AACXA,qBAASI,IAAIG,iBAAJ,EAAT;AACAR,kCAAsB,IAAtB;AACD;AACF;AACF;;AAED,UAAIU,YAAJ;AACA,UAAItC,eAAoB,EAAxB;AACA,UAAIuC,iBAAiB,KAArB;AACA,UAAMpC,cAAcqC,QAAQzC,YAAYA,SAAS0C,kBAAOC,SAAhB,MAA+BD,kBAAOE,oBAA1D,CAApB;AACA,UAAI,CAACd,MAAD,IAAW,CAACA,OAAOe,OAAvB,EAAgC;AAC9B,YAAI,KAAKpD,cAAT,EAAyB;AACvB,cAAIqD,WAAWpG,eAAMqG,YAAN,EAAf;AACAR,gBAAM,IAAIS,sBAAJ,CAAgBF,QAAhB,EAA0BA,SAASG,KAAT,CAAe,CAAC,CAAhB,CAA1B,CAAN;AACD,SAHD,MAGO;AACL,cAAIH,YAAWpG,eAAMwG,WAAN,EAAf;AACAX,gBAAM,IAAIS,sBAAJ,CAAgBF,SAAhB,EAA0BA,SAA1B,CAAN;AACD;AACD,YAAIhB,MAAJ,EAAY;AACV;AACA,cAAIA,OAAOqB,sBAAP,MAAmC,KAAKC,eAAL,CAAqBtD,aAArB,CAAvC,EAA4E;AAC1EyC,gBAAIc,WAAJ,CAAgB,IAAhB;AACAd,gBAAIe,SAAJ,CAAc,IAAd;AACArD,yBAAajE,UAAUuH,mBAAvB,IAA8CzB,OAAO0B,OAArD;AACD;AACD;AACAjB,cAAIkB,OAAJ,GAAc3B,OAAO2B,OAArB;AACD;AACF,OAlBD,MAkBO;AACLjB,yBAAiB,IAAjB;AACA,YAAIkB,SAAS,KAAK/D,aAAL,IAAsBS,WAAtB,GAAoC0B,OAAO4B,MAA3C,GAAoDhH,eAAMwG,WAAN,EAAjE;AACAX,cAAMT,OAAO6B,iBAAP,CAAyBD,MAAzB,CAAN;AACA,YAAI,KAAK/D,aAAL,IAAsBS,WAA1B,EAAuC;AACrCmC,cAAIqB,QAAJ,GAAe9B,OAAO8B,QAAtB;AACD;AACD,YAAI9B,OAAO+B,QAAP,EAAJ,EAAuB;AACrBtB,cAAIuB,gBAAJ,GADqB,CACG;AACzB;AACF;;AAED,aAAO,KAAKC,kBAAL,CACLxB,GADK,EAELzC,aAFK,EAGLC,SAHK,EAILC,QAJK,EAKLC,YALK,EAMLC,UANK,EAOLsC,cAPK,EAQLpC,WARK,CAAP;AAUD;;AAED;;;;;;;;;;;;;;2BAWOP,W,EAAiC4B,M,EAAgBuC,O,EAAoB;AAC1E,UAAI,CAACnE,WAAL,EAAkB;AAChB;AACD;AACD,UAAM6B,WAAW,KAAKtD,UAAL,CAAgBqD,MAAhB,CAAjB;AACA,UAAI,CAACC,QAAL,EAAe;AACb,cAAM,IAAIuC,KAAJ,0BAAiCxC,MAAjC,CAAN;AACD;AACD,UAAMyC,WAAWrE,uBAAuBS,cAAvB,GAA8BT,YAAYiB,OAAZ,EAA9B,GAAsDjB,WAAvE;AACA6B,eAASyC,MAAT,CAAgBD,QAAhB,EAA0BF,OAA1B;AACD;;AAED;;;;;;;;;;;;;4BAUQvC,M,EAAgBuC,O,EAA2B;AACjD,UAAIrC,YAAY,KAAKtD,WAAL,CAAiBoD,MAAjB,CAAhB;AACA,UAAI,CAACE,SAAL,EAAgB;AACd,cAAM,IAAIsC,KAAJ,0BAAiCxC,MAAjC,CAAN;AACD;AACD,UAAM5B,cAAc8B,UAAUyC,OAAV,CAAkBJ,OAAlB,CAApB;AACA,UAAInE,WAAJ,EAAiB;AACfA,oBAAYwE,UAAZ,CAAuB,IAAvB;AACD;AACD,aAAOxE,WAAP;AACD;;AAED;;;;;;;;0BAKMyE,Q,EAA0B;AAAA;;AAC9B,UAAInI,WAAW,KAAKqB,SAApB;AACA,WAAKA,SAAL,GAAiB,IAAIpB,uBAAJ,EAAjB;AACAD,eAASoI,KAAT,CAAe,YAAM;AACnB,cAAK9G,QAAL,CAAc8G,KAAd,CAAoBD,QAApB;AACD,OAFD;AAGA,WAAKrG,eAAL,CAAqBsG,KAArB;AACD;;AAED;;;;;;;;0BAKc;AACZ;AACA;AACA,aAAOC,KAAK5C,GAAL,EAAP;AACD;;;oCAEe6C,S,EAA4B;AAC1C,aAAO,KAAKxG,eAAL,CAAqByG,SAArB,CAA+BD,SAA/B,CAAP;AACD;;;;;;kBA/TkBxI,M", "file": "tracer.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nimport * as opentracing from 'opentracing';\nimport { Tags as otTags } from 'opentracing';\nimport os from 'os';\nimport { v4 as uuidv4 } from 'uuid';\nimport BaggageSetter from './baggage/baggage_setter';\nimport DefaultBaggageRestrictionManager from './baggage/default_baggage_restriction_manager';\nimport * as constants from './constants';\nimport NullLogger from './logger';\nimport Metrics from './metrics/metrics';\nimport NoopMetricFactory from './metrics/noop/metric_factory';\nimport BinaryCodec from './propagators/binary_codec';\nimport TextMapCodec from './propagators/text_map_codec';\nimport NoopReporter from './reporters/noop_reporter';\nimport ConstSampler from './samplers/const_sampler';\nimport { adaptSamplerOrThrow } from './samplers/_adapt_sampler';\nimport Span from './span';\nimport SpanContext from './span_context';\nimport DefaultThrottler from './throttler/default_throttler';\nimport Utils from './util';\nimport version from './version';\n\nexport default class Tracer {\n  _serviceName: string;\n  _reporter: Reporter;\n  _sampler: Sampler;\n  _logger: NullLogger;\n  _tags: any;\n  _injectors: any;\n  _extractors: any;\n  _metrics: any;\n  _baggageSetter: BaggageSetter;\n  _debugThrottler: Throttler & ProcessSetter;\n  _process: Process;\n  _traceId128bit: boolean;\n  _shareRpcSpan: boolean;\n\n  /**\n   * @param {String} [serviceName] - name of the current service or application.\n   * @param {Object} [reporter] - reporter used to submit finished spans to Jaeger backend.\n   * @param {Object} [sampler] - sampler used to decide if trace should be sampled when starting a new one.\n   * @param {Object} [options] - the fields to set on the newly created span.\n   * @param {Object} [options.tags] - set of key-value pairs which will be set\n   *        as process-level tags on the Tracer itself.\n   * @param {Object} [options.metrics] - instance of the Metrics class from ./metrics/metrics.js.\n   * @param {Object} [options.logger] - a logger matching NullLogger API from ./logger.js.\n   * @param {Object} [options.baggageRestrictionManager] - a baggageRestrictionManager matching\n   * @param {Object} [options.contextKey] - a name of the key to extract/inject context from headers\n   * @param {Object} [options.baggagePrefix] - a name of the context baggage key prefix\n   * @param {boolean} [options.traceId128bit] - generate root span with a 128bit traceId.\n   * @param {boolean} [options.shareRpcSpan] - share spanIds for rpc servers.\n   * BaggageRestrictionManager API from ./baggage.js.\n   */\n  constructor(\n    serviceName: string,\n    reporter: Reporter = new NoopReporter(),\n    sampler: LegacySamplerV1 | Sampler = new ConstSampler(false),\n    options: any = {}\n  ) {\n    this._tags = options.tags ? Utils.clone(options.tags) : {};\n    this._tags[constants.JAEGER_CLIENT_VERSION_TAG_KEY] = `Node-${version}`;\n    this._tags[constants.TRACER_HOSTNAME_TAG_KEY] =\n      this._tags[constants.TRACER_HOSTNAME_TAG_KEY] || os.hostname();\n    this._tags[constants.PROCESS_IP] = this._tags[constants.PROCESS_IP] || Utils.myIp();\n\n    this._metrics = options.metrics || new Metrics(new NoopMetricFactory());\n\n    this._serviceName = serviceName;\n    this._reporter = reporter;\n    // TODO(joe): verify we want to throw if the sampler is invalid\n    this._sampler = adaptSamplerOrThrow(sampler);\n    this._logger = options.logger || new NullLogger();\n    this._baggageSetter = new BaggageSetter(\n      options.baggageRestrictionManager || new DefaultBaggageRestrictionManager(),\n      this._metrics\n    );\n    this._debugThrottler = options.debugThrottler || new DefaultThrottler(false);\n    this._injectors = {};\n    this._extractors = {};\n\n    let codecOptions = {\n      contextKey: options.contextKey || null,\n      baggagePrefix: options.baggagePrefix || null,\n      urlEncoding: false,\n      metrics: this._metrics,\n    };\n\n    let textCodec = new TextMapCodec(codecOptions);\n    this.registerInjector(opentracing.FORMAT_TEXT_MAP, textCodec);\n    this.registerExtractor(opentracing.FORMAT_TEXT_MAP, textCodec);\n\n    codecOptions.urlEncoding = true;\n\n    let httpCodec = new TextMapCodec(codecOptions);\n    this.registerInjector(opentracing.FORMAT_HTTP_HEADERS, httpCodec);\n    this.registerExtractor(opentracing.FORMAT_HTTP_HEADERS, httpCodec);\n\n    let binaryCodec = new BinaryCodec();\n    this.registerInjector(opentracing.FORMAT_BINARY, binaryCodec);\n    this.registerExtractor(opentracing.FORMAT_BINARY, binaryCodec);\n\n    const uuid = uuidv4();\n    this._tags[constants.TRACER_CLIENT_ID_TAG_KEY] = uuid;\n    this._process = {\n      serviceName: serviceName,\n      tags: Utils.convertObjectToTags(this._tags),\n      uuid: uuid,\n    };\n    this._debugThrottler.setProcess(this._process);\n    // TODO update reporter to implement ProcessSetter\n    this._reporter.setProcess(this._process.serviceName, this._process.tags);\n\n    this._traceId128bit = options.traceId128bit;\n    this._shareRpcSpan = options.shareRpcSpan;\n  }\n\n  _startInternalSpan(\n    spanContext: SpanContext,\n    operationName: string,\n    startTime: number,\n    userTags: ?{},\n    internalTags: ?{},\n    references: Array<Reference>,\n    hadParent: boolean,\n    isRpcServer: boolean\n  ): Span {\n    const span = new Span(this, operationName, spanContext, startTime, references);\n    if (!span._spanContext.samplingFinalized) {\n      const decision = this._sampler.onCreateSpan(span);\n      span._applySamplingDecision(decision);\n    }\n\n    if (userTags) {\n      span.addTags(userTags);\n    }\n    if (internalTags) {\n      span._appendTags(internalTags); // no need to run internal tags through sampler\n    }\n\n    // emit metrics\n    if (span.context().isSampled()) {\n      this._metrics.spansStartedSampled.increment(1);\n      if (!hadParent) {\n        this._metrics.tracesStartedSampled.increment(1);\n      } else if (isRpcServer) {\n        this._metrics.tracesJoinedSampled.increment(1);\n      }\n    } else {\n      this._metrics.spansStartedNotSampled.increment(1);\n      if (!hadParent) {\n        this._metrics.tracesStartedNotSampled.increment(1);\n      } else if (isRpcServer) {\n        this._metrics.tracesJoinedNotSampled.increment(1);\n      }\n    }\n\n    return span;\n  }\n\n  _report(span: Span): void {\n    this._metrics.spansFinished.increment(1);\n    this._reporter.report(span);\n  }\n\n  registerInjector(format: string, injector: Injector): void {\n    this._injectors[format] = injector;\n  }\n\n  registerExtractor(format: string, extractor: Extractor): void {\n    this._extractors[format] = extractor;\n  }\n\n  /**\n   * The method for creating a root or child span.\n   *\n   * @param {string} operationName - the name of the operation.\n   * @param {object} [options] - the fields to set on the newly created span.\n   * @param {string} options.operationName - the name to use for the newly\n   *        created span. Required if called with a single argument.\n   * @param {SpanContext} [options.childOf] - a parent SpanContext (or Span,\n   *        for convenience) that the newly-started span will be the child of\n   *        (per REFERENCE_CHILD_OF). If specified, `fields.references` must\n   *        be unspecified.\n   * @param {array} [options.references] - an array of Reference instances,\n   *        each pointing to a causal parent SpanContext. If specified,\n   *        `fields.childOf` must be unspecified.\n   * @param {object} [options.tags] - set of key-value pairs which will be set\n   *        as tags on the newly created Span. Ownership of the object is\n   *        passed to the created span for efficiency reasons (the caller\n   *        should not modify this object after calling startSpan).\n   * @param {number} [options.startTime] - a manually specified start time for\n   *        the created Span object. The time should be specified in\n   *        milliseconds as Unix timestamp. Decimal value are supported\n   *        to represent time values with sub-millisecond accuracy.\n   * @return {Span} - a new Span object.\n   **/\n  startSpan(operationName: string, options: ?startSpanOptions): Span {\n    options = options || {};\n    let references = options.references || [];\n\n    let userTags = options.tags;\n    let startTime = options.startTime || this.now();\n\n    // followsFromIsParent is used to ensure that CHILD_OF reference is preferred\n    // as a parent even if it comes after FOLLOWS_FROM reference.\n    let followsFromIsParent = false;\n    let parent: ?SpanContext = options.childOf instanceof Span ? options.childOf.context() : options.childOf;\n    // If there is no childOf in options, then search list of references\n    for (let i = 0; i < references.length; i++) {\n      let ref: Reference = references[i];\n      if (ref.type() === opentracing.REFERENCE_CHILD_OF) {\n        if (!parent || followsFromIsParent) {\n          parent = ref.referencedContext();\n          break;\n        }\n      } else if (ref.type() === opentracing.REFERENCE_FOLLOWS_FROM) {\n        if (!parent) {\n          parent = ref.referencedContext();\n          followsFromIsParent = true;\n        }\n      }\n    }\n\n    let ctx: SpanContext;\n    let internalTags: any = {};\n    let hasValidParent = false;\n    const isRpcServer = Boolean(userTags && userTags[otTags.SPAN_KIND] === otTags.SPAN_KIND_RPC_SERVER);\n    if (!parent || !parent.isValid) {\n      if (this._traceId128bit) {\n        let randomId = Utils.getRandom128();\n        ctx = new SpanContext(randomId, randomId.slice(-8));\n      } else {\n        let randomId = Utils.getRandom64();\n        ctx = new SpanContext(randomId, randomId);\n      }\n      if (parent) {\n        // fake parent, doesn't contain a parent trace-id, but may contain debug-id/baggage\n        if (parent.isDebugIDContainerOnly() && this._isDebugAllowed(operationName)) {\n          ctx._setSampled(true);\n          ctx._setDebug(true);\n          internalTags[constants.JAEGER_DEBUG_HEADER] = parent.debugId;\n        }\n        // baggage that could have been passed via `jaeger-baggage` header\n        ctx.baggage = parent.baggage;\n      }\n    } else {\n      hasValidParent = true;\n      let spanId = this._shareRpcSpan && isRpcServer ? parent.spanId : Utils.getRandom64();\n      ctx = parent._makeChildContext(spanId);\n      if (this._shareRpcSpan && isRpcServer) {\n        ctx.parentId = parent.parentId;\n      }\n      if (parent.isRemote()) {\n        ctx.finalizeSampling(); // will finalize sampling for all spans sharing this traceId\n      }\n    }\n\n    return this._startInternalSpan(\n      ctx,\n      operationName,\n      startTime,\n      userTags,\n      internalTags,\n      references,\n      hasValidParent,\n      isRpcServer\n    );\n  }\n\n  /**\n   * Saves the span context into the carrier object for various formats, and encoders.\n   *\n   * @param  {SpanContext} spanContext - the SpanContext to inject into the\n   *         carrier object. As a convenience, a Span instance may be passed\n   *         in instead (in which case its .context() is used for the\n   *         inject()).\n   * @param  {string} format - the format of the carrier.\n   * @param  {any} carrier - see the documentation for the chosen `format`\n   *         for a description of the carrier object.\n   **/\n  inject(spanContext: SpanContext | Span, format: string, carrier: any): void {\n    if (!spanContext) {\n      return;\n    }\n    const injector = this._injectors[format];\n    if (!injector) {\n      throw new Error(`Unsupported format: ${format}`);\n    }\n    const _context = spanContext instanceof Span ? spanContext.context() : spanContext;\n    injector.inject(_context, carrier);\n  }\n\n  /**\n   * Responsible for extracting a span context from various serialized formats.\n   *\n   * @param  {string} format - the format of the carrier.\n   * @param  {any} carrier - the type of the carrier object is determined by\n   *         the format.\n   * @return {SpanContext}\n   *         The extracted SpanContext, or null if no such SpanContext could\n   *         be found in `carrier`\n   */\n  extract(format: string, carrier: any): SpanContext {\n    let extractor = this._extractors[format];\n    if (!extractor) {\n      throw new Error(`Unsupported format: ${format}`);\n    }\n    const spanContext = extractor.extract(carrier);\n    if (spanContext) {\n      spanContext._setRemote(true);\n    }\n    return spanContext;\n  }\n\n  /**\n   * Closes the tracer, flushes spans, and executes any callbacks if necessary.\n   *\n   * @param {Function} [callback] - a callback that runs after the tracer has been closed.\n   **/\n  close(callback: Function): void {\n    let reporter = this._reporter;\n    this._reporter = new NoopReporter();\n    reporter.close(() => {\n      this._sampler.close(callback);\n    });\n    this._debugThrottler.close();\n  }\n\n  /**\n   * Returns the current timestamp in milliseconds since the Unix epoch.\n   * Fractional values are allowed so that timestamps with sub-millisecond\n   * accuracy can be represented.\n   */\n  now(): number {\n    // TODO investigate process.hrtime; verify it is available in all Node versions.\n    // http://stackoverflow.com/questions/11725691/how-to-get-a-microtime-in-node-js\n    return Date.now();\n  }\n\n  _isDebugAllowed(operation: string): boolean {\n    return this._debugThrottler.isAllowed(operation);\n  }\n}\n"]}