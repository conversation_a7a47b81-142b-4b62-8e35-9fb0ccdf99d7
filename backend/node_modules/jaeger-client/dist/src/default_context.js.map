{"version": 3, "sources": ["../../src/default_context.js"], "names": ["DefaultContext", "span", "_span"], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEqBA,c;;;;;;;4BAGXC,I,EAAkB;AACxB,WAAKC,KAAL,GAAaD,IAAb;AACD;;;8BAEe;AACd,aAAO,KAAKC,KAAZ;AACD;;;;;;kBATkBF,c", "file": "default_context.js", "sourcesContent": ["// @flow\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nexport default class DefaultContext {\n  _span: Span;\n\n  setSpan(span: Span): void {\n    this._span = span;\n  }\n\n  getSpan(): Span {\n    return this._span;\n  }\n}\n"]}