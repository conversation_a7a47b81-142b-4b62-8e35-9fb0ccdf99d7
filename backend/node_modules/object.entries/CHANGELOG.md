# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.9](https://github.com/ljharb/Object.entries/compare/v1.1.8...v1.1.9) - 2025-03-14

### Commits

- [actions] split out node 10-20, and 20+ [`5ea5516`](https://github.com/ljharb/Object.entries/commit/5ea55165f812ad2c547af70bac86431a368fc556)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `array.prototype.map`, `auto-changelog`, `has-strict-mode`, `tape` [`d2f5f11`](https://github.com/ljharb/Object.entries/commit/d2f5f116b33401af53ffea7eab03c5ea60654b72)
- [Deps] update `call-bind`, `es-object-atoms` [`9e50a4b`](https://github.com/ljharb/Object.entries/commit/9e50a4b8a72a85ff10db99eb3ba960526c409a86)
- [Refactor] use `call-bound` directly [`b44af77`](https://github.com/ljharb/Object.entries/commit/b44af773a2bbb6ac74e752289e0da0bd2a583715)
- [Tests] replace `aud` with `npm audit` [`615e995`](https://github.com/ljharb/Object.entries/commit/615e99538b1d0cdc9fc55c63633d0e0ff5bbe304)
- [Dev Deps] add missing peer dep [`b750b1a`](https://github.com/ljharb/Object.entries/commit/b750b1acc727c2c95a7a406ae30b54681578fda9)

## [v1.1.8](https://github.com/ljharb/Object.entries/compare/v1.1.7...v1.1.8) - 2024-03-18

### Commits

- [actions] remove redundant finisher [`14a9f46`](https://github.com/ljharb/Object.entries/commit/14a9f46a8068b5d676e6babf417ed4731b779b87)
- [Dev Deps] update `array.prototype.map`, `aud`, `npmignore`, `tape` [`eee9379`](https://github.com/ljharb/Object.entries/commit/eee9379437fee86a75f542717ce2dec0f813469b)
- [Deps] update `call-bind`, `define-properties` [`02bbe0d`](https://github.com/ljharb/Object.entries/commit/02bbe0d4c860ec04ef993b149a90f61650c128d1)
- [Refactor] use `es-object-atoms` instead of `es-abstract` [`dac98c9`](https://github.com/ljharb/Object.entries/commit/dac98c90921712f2b055cdb77c5318bc7eb5a424)

## [v1.1.7](https://github.com/ljharb/Object.entries/compare/v1.1.6...v1.1.7) - 2023-08-27

### Commits

- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`f2edb13`](https://github.com/ljharb/Object.entries/commit/f2edb1340e8ff5a07f3997b7f3b6f23a048162a1)
- [Deps] update `define-properties`, `es-abstract` [`7b9daec`](https://github.com/ljharb/Object.entries/commit/7b9daec059051d2a1900500d9b5eff5846bd8536)

## [v1.1.6](https://github.com/ljharb/Object.entries/compare/v1.1.5...v1.1.6) - 2022-11-05

### Commits

- [actions] reuse common workflows [`e6dc177`](https://github.com/ljharb/Object.entries/commit/e6dc177fef3434a3eeb430d1bef2ca4db3b473e0)
- [meta] use `npmignore` to autogenerate an npmignore file [`76b4136`](https://github.com/ljharb/Object.entries/commit/76b4136ccddca82ee7ed07da53f1856b6509b79b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`2ab7fcd`](https://github.com/ljharb/Object.entries/commit/2ab7fcd693cff6a39dfbc2b4854d2366eafb4d88)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `array.prototype.map`, `aud`, `auto-changelog`, `functions-have-names`, `tape` [`61a205e`](https://github.com/ljharb/Object.entries/commit/61a205eb569e1d2956c010c639199b6f33649e75)
- [actions] update rebase action to use reusable workflow [`3b886b0`](https://github.com/ljharb/Object.entries/commit/3b886b01481a99cd9108a98bfc8f4538fcf1fd5d)
- [actions] update codecov uploader [`6db30f8`](https://github.com/ljharb/Object.entries/commit/6db30f8663954b35b5498818a401be654d5678cd)
- [Deps] update `define-properties`, `es-abstract` [`fa0a9c9`](https://github.com/ljharb/Object.entries/commit/fa0a9c920eb69474631f26956b199600a4cc4c90)

## [v1.1.5](https://github.com/ljharb/Object.entries/compare/v1.1.4...v1.1.5) - 2021-10-03

### Commits

- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `array.prototype.map`, `tape` [`3fa8e24`](https://github.com/ljharb/Object.entries/commit/3fa8e24026bcef1ea84ad421ef138255068123b0)
- [Deps] update `es-abstract` [`9288cc0`](https://github.com/ljharb/Object.entries/commit/9288cc0b2b2282c6a41b1d4499bf41945639c18f)
- [Robustness] use call-bound `Array.prototype.push` [`e495b27`](https://github.com/ljharb/Object.entries/commit/e495b2737b16124a936002a0e204bda0a1608f12)
- [meta] npmignore coverage output [`d16eb7d`](https://github.com/ljharb/Object.entries/commit/d16eb7d64e44baf37beb3f08f405fb206bc0fe50)

## [v1.1.4](https://github.com/ljharb/Object.entries/compare/v1.1.3...v1.1.4) - 2021-05-26

### Commits

- [actions] use `node/install` instead of `node/run`; use `codecov` action [`502a2d7`](https://github.com/ljharb/Object.entries/commit/502a2d729a36dbe92f03a2416be5d9cf3f3cd5fa)
- [meta] do not publish github action workflow files [`f38243c`](https://github.com/ljharb/Object.entries/commit/f38243c9c9e4b0478219d9313316e6d7af433496)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `functions-have-names`, `has-strict-mode`, `tape` [`28b8c53`](https://github.com/ljharb/Object.entries/commit/28b8c53aa2f3671e8d500d332d4b658afd27ced7)
- [readme] fix repo URLs; remove travis badge [`01eb2bc`](https://github.com/ljharb/Object.entries/commit/01eb2bc33977dba3e25f9e8ce4341a8eac24662f)
- [readme] add actions and codecov badges [`e9455ce`](https://github.com/ljharb/Object.entries/commit/e9455ce300168157a448f695b1be983bda974a8a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog` [`3f26a81`](https://github.com/ljharb/Object.entries/commit/3f26a81ba0dff813c5828e1d818bf02330c1ddc7)
- [actions] update workflows [`2447f74`](https://github.com/ljharb/Object.entries/commit/2447f740f3b963221f7525588879f628c1a4538b)
- [Refactor] `propertyIsEnumerable` checks own-ness; remove `has` [`a65ae8d`](https://github.com/ljharb/Object.entries/commit/a65ae8d8349464d7d9fb2621c5e2ee02533cd9e3)
- [actions] update workflows [`2465bef`](https://github.com/ljharb/Object.entries/commit/2465befa1132bf327853ed134e9640b6d7cb7d90)
- [Tests] swap `array-map` for `array.prototype.map` [`37d5157`](https://github.com/ljharb/Object.entries/commit/37d51574118272e90cbbe6356ecca505e399e7ed)
- [Dev Deps] update `eslint`, `tape` [`3878db8`](https://github.com/ljharb/Object.entries/commit/3878db8f4b120e35e7e2ab9c3906983a6a3c98a9)
- [Deps] update `call-bind`, `es-abstract` [`9ff20ec`](https://github.com/ljharb/Object.entries/commit/9ff20ec9bb0b079e652eaabb5f4e15b785d6abd2)
- [meta] use `prepublishOnly` script for npm 7+ [`052d1ca`](https://github.com/ljharb/Object.entries/commit/052d1caf5f9b56be1948fa68ddd47a6ee36bdb0f)
- [Tests] increase coverage [`1e84c9c`](https://github.com/ljharb/Object.entries/commit/1e84c9ce0eb2dd5178ccea5e659384c70f29e758)
- [Deps] update `es-abstract` [`65af70d`](https://github.com/ljharb/Object.entries/commit/65af70d1c31bce6eb630ffa100dde99a0cb53529)
- [Deps] update `es-abstract` [`2a633ce`](https://github.com/ljharb/Object.entries/commit/2a633ce6ec8b363e865a41783758c1b5ab55f6e8)
- [meta] gitignore coverage output [`5f4a0c1`](https://github.com/ljharb/Object.entries/commit/5f4a0c10918a492ce09fbab6ea104bc9f8567fa7)

## [v1.1.3](https://github.com/ljharb/Object.entries/compare/v1.1.2...v1.1.3) - 2020-11-26

### Commits

- [Tests] migrate tests to Github Actions [`f9641aa`](https://github.com/ljharb/Object.entries/commit/f9641aa552ca26fe787474f342d0868f65c306b3)
- [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner [`7cd4184`](https://github.com/ljharb/Object.entries/commit/7cd418456d3de86030959fc7f8e790dba41698d9)
- [Tests] run `nyc` on all tests [`de597c9`](https://github.com/ljharb/Object.entries/commit/de597c9b1feeb48c3a8c90e3e19c20b13885e6ed)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`f453127`](https://github.com/ljharb/Object.entries/commit/f4531276dd22be8dfa3d693155f095fc2e3d09ab)
- [actions] add "Allow Edits" workflow [`5b65ef6`](https://github.com/ljharb/Object.entries/commit/5b65ef6626632d17a9f601246cadfec1e658c94b)
- [Deps] update `es-abstract`; use `call-bind` where applicable [`339136d`](https://github.com/ljharb/Object.entries/commit/339136db2e06c06b718fb13528fb8a8b21ecb3db)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`ef2df4d`](https://github.com/ljharb/Object.entries/commit/ef2df4d65c2a2faeb1d0cd1964ecf9b4d7723fa9)

## [v1.1.2](https://github.com/ljharb/Object.entries/compare/v1.1.1...v1.1.2) - 2020-05-20

### Commits

- [meta] add `auto-changelog` [`29e2771`](https://github.com/ljharb/Object.entries/commit/29e2771f01d3cc14c9372584ca8c966ccd10a294)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`, `functions-have-names`; add `safe-publish-latest` [`f30dde0`](https://github.com/ljharb/Object.entries/commit/f30dde0d2061c6f17078c45db925d536b3986327)
- [Refactor] use `callBound` instead of `function-bind` [`db94733`](https://github.com/ljharb/Object.entries/commit/db94733a409286914f0b0067ced2a05e0af34064)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `aud` [`124d3bb`](https://github.com/ljharb/Object.entries/commit/124d3bb9f14c7f117aec7310fd0a461e90d7255b)
- [Fix] do not mutate the native function when present [`fffb674`](https://github.com/ljharb/Object.entries/commit/fffb6746635276a921eb6c893d230448d89d8436)
- [Dev Deps] update `@ljharb/eslint-config`, `aud` [`5552db4`](https://github.com/ljharb/Object.entries/commit/5552db46d67f9d9b2de2952d44b96a883b96007d)
- [Deps] update `es-abstract` [`bcf93d0`](https://github.com/ljharb/Object.entries/commit/bcf93d0dc0a5b1a3cd0620ad75876d09602524bd)
- [meta] only run `aud` on prod deps [`13c35c9`](https://github.com/ljharb/Object.entries/commit/13c35c9d8e0b1e30be0ea9e5307ea552d8b31143)
- [Deps] update `es-abstract` [`9374fed`](https://github.com/ljharb/Object.entries/commit/9374fed15ca58ba6af9494d1bc61634e9a6b9d8b)

## [v1.1.1](https://github.com/ljharb/Object.entries/compare/v1.1.0...v1.1.1) - 2019-12-12

### Commits

- [Tests] use shared travis-ci configs [`576c8a7`](https://github.com/ljharb/Object.entries/commit/576c8a7db358c79ba8135a0c87c91e6e3f31c91a)
- [Tests] up to `node` `v12.7`, `v11.15`, `v10.16`, `v8.16`, `v6.17`; use `nvm install-latest-npm` [`26c5a45`](https://github.com/ljharb/Object.entries/commit/26c5a4570bb7c4a535921e5e1a51e1f22f2fbd80)
- [actions] add automatic rebasing / merge commit blocking [`3253bdf`](https://github.com/ljharb/Object.entries/commit/3253bdf6b24d83414db15b4175eaf2fa0fdd1b5f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `functions-have-names` [`bf480ef`](https://github.com/ljharb/Object.entries/commit/bf480efed7cdd20bd99d2d441bf99472fc061057)
- [Refactor] use split-up `es-abstract` (85% bundle size decrease) [`3fd42b9`](https://github.com/ljharb/Object.entries/commit/3fd42b97b91a2192b20f3b6f04c7e658ba841955)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`9bf7f7f`](https://github.com/ljharb/Object.entries/commit/9bf7f7feaab5b4d1ffbd19e63a91db619fc3c15d)
- [meta] clean up scripts [`908f4fd`](https://github.com/ljharb/Object.entries/commit/908f4fdc3c7b7bb4fd595b8136715254e1462d01)
- [Dev Deps] update `eslint`, `tape` [`213436c`](https://github.com/ljharb/Object.entries/commit/213436c385c49dd9f50aacfeb77a85a44c276de6)
- [Tests] use `functions-have-names` [`8cd5de5`](https://github.com/ljharb/Object.entries/commit/8cd5de572695fcf0ffda03714cd0a0754a296edb)
- [Deps] update `es-abstract` [`3a2ca03`](https://github.com/ljharb/Object.entries/commit/3a2ca0383cc511c05f196820a264e81e3dad0a54)

## [v1.1.0](https://github.com/ljharb/Object.entries/compare/v1.0.4...v1.1.0) - 2019-01-01

### Fixed

- Exclude test.html from the npm package. [`#12`](https://github.com/ljharb/Object.entries/issues/12)

### Commits

- [Tests] remove `jscs` [`052aed1`](https://github.com/ljharb/Object.entries/commit/052aed1bba3d9b1fc177d8570110291740ffa3d0)
- [Tests] up to `node` `v11.1`, `v10.13`, `v9.11`, `v8.12`, `v7.10`, `v6.14`, `v4.9`; use `nvm install-latest-npm` [`0015678`](https://github.com/ljharb/Object.entries/commit/001567848e2c81be9df50f3e7e16cdfbe168f815)
- [Tests] up to `node` `v7.4`, `v4.7`; improve test matrix [`6f19e66`](https://github.com/ljharb/Object.entries/commit/6f19e66bbbf6d38628589ad3654fd3cb3299cc47)
- [Tests] up to `node` `v11.6`, `v10.15`, `v8.15`, `v6.16` [`89415ba`](https://github.com/ljharb/Object.entries/commit/89415ba61eb7260b7f16ad7df95c40a3f175fa5a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `nsp`, `tape` [`91f9ee6`](https://github.com/ljharb/Object.entries/commit/91f9ee65a28021d42fe53fb3b0341160c691f876)
- [New] add `auto` entry point` [`9799c0d`](https://github.com/ljharb/Object.entries/commit/9799c0d8634cfecb68038a1770fcb35e184ebe94)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape` [`074677d`](https://github.com/ljharb/Object.entries/commit/074677d53f2226fc1f09ed18275ce0963cf60998)
- [Deps] update `define-properties`, `es-abstract`, `function-bind`, `has` [`db5d97e`](https://github.com/ljharb/Object.entries/commit/db5d97ed6ff82757df50bc776afa9aa8260bdb3b)
- [Tests] use `npm audit` instead of `nsp` [`bc3e6a7`](https://github.com/ljharb/Object.entries/commit/bc3e6a74aa961adee9e66bb46c02ca5aa5eba8fd)
- Only apps should have lockfiles [`d192ab7`](https://github.com/ljharb/Object.entries/commit/d192ab7e6bfd9e2c4260e82097ab5d81915cc30f)

## [v1.0.4](https://github.com/ljharb/Object.entries/compare/v1.0.3...v1.0.4) - 2016-12-04

### Commits

- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `@es-shims/api` [`cf4a9cf`](https://github.com/ljharb/Object.entries/commit/cf4a9cf9f0397a5ff385801879df86316b90cc00)
- [Tests] up to `node` `v7.2`, `v6.9`, `v4.6`; improve test matrix. [`281ccbd`](https://github.com/ljharb/Object.entries/commit/281ccbd6fe3b3bf6f214bd628679963bbe34ed7e)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`33fb890`](https://github.com/ljharb/Object.entries/commit/33fb8903265ac8e5ec64bde093c1b2a047e9e709)
- [Dev Deps] update `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`7c1ea3f`](https://github.com/ljharb/Object.entries/commit/7c1ea3f748fce028d95b4f4e87104725a3659aaa)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`f9473c7`](https://github.com/ljharb/Object.entries/commit/f9473c7f49d894c5e6de116652aa39420889f549)
- [Dev Deps] update `jscs`, `nsp`, `eslint`, `@es-shims/api` [`1368a76`](https://github.com/ljharb/Object.entries/commit/1368a7680f0bb37063e006d81879623a6a8021cc)
- [Tests] up to `v5.6`, `v4.3` [`7ad9976`](https://github.com/ljharb/Object.entries/commit/7ad9976601f953997142d7a23fd7714345474f52)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`6bf18dc`](https://github.com/ljharb/Object.entries/commit/6bf18dc431fbff454b4a3a0531450250aadbbc49)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`a45c239`](https://github.com/ljharb/Object.entries/commit/a45c239369a29305ea854e5a99ebaef3199e3dfa)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config` [`13f7d10`](https://github.com/ljharb/Object.entries/commit/13f7d10ddf5947d2b3c853edfd2a450498dd3694)
- [Tests] up to `node` `v5.9`, `v4.4` [`261533c`](https://github.com/ljharb/Object.entries/commit/261533c95bde27255ff86f9f20d4de958d48e860)
- [Dev Deps] update `jscs` [`8dd7b5f`](https://github.com/ljharb/Object.entries/commit/8dd7b5f169295af19b363ccccd39eeb8c25bb688)
- [Docs] update to reflect ES2017 inclusion. [`7662206`](https://github.com/ljharb/Object.entries/commit/7662206b05bd30f4851fd94eb6c27051601387eb)
- [Tests] use pretest/posttest for linting/security [`87d62ad`](https://github.com/ljharb/Object.entries/commit/87d62ad0fd1212bbb827a8be9a121906f4498835)
- [Tests] fix npm upgrades on older nodes [`67cabde`](https://github.com/ljharb/Object.entries/commit/67cabdeb4860c94a96c2075581260c5862dd3564)
- [Tests] up to `node` `v6.2` [`c50c154`](https://github.com/ljharb/Object.entries/commit/c50c154edb3acdc4741ef55df9e2d2cdae9a11d3)
- [Dev Deps] update `jscs`, `eslint`, `@ljharb/eslint-config` [`ff17ab8`](https://github.com/ljharb/Object.entries/commit/ff17ab8b5f27535b4f048afe6d7340319bb051cd)
- [Deps] update `es-abstract` [`b8be50a`](https://github.com/ljharb/Object.entries/commit/b8be50ab1beab650243dc4066d2e90ec755f79e9)
- [Deps] update `es-abstract` [`fb3a7e1`](https://github.com/ljharb/Object.entries/commit/fb3a7e192617e8247730818b6d6ff977304184b5)
- [Tests] on `node` `v5.12` [`ba624ca`](https://github.com/ljharb/Object.entries/commit/ba624caff1a2a722d17c495bd35d4b0994f4cc42)
- [Tests] on `node` `v5.10` [`e513ca5`](https://github.com/ljharb/Object.entries/commit/e513ca566d2ca3e39f0638e15cb1f126f82d14ca)
- [Deps] update `function-bind` [`6e25d29`](https://github.com/ljharb/Object.entries/commit/6e25d2992bf73908d6e544e8563997c81053f9e2)
- [Deps] update `es-abstract` [`bf680a4`](https://github.com/ljharb/Object.entries/commit/bf680a49bd7dccd98b58ab35b994306b199c3c94)
- [Deps] update `es-abstract` [`fbb209b`](https://github.com/ljharb/Object.entries/commit/fbb209b786bdd23d169b38c9dda1d2b38fae5e73)
- [Deps] update `define-properties` [`77d4ead`](https://github.com/ljharb/Object.entries/commit/77d4ead3e5f5f2877f5e3cba99cdd0e293089811)
- [Tests] on `node` `v4.2` [`d946594`](https://github.com/ljharb/Object.entries/commit/d946594e73f99a3896f89ce5e517effd7f9c8487)
- [Tests] on `node` `v5.0` [`80d7d16`](https://github.com/ljharb/Object.entries/commit/80d7d16f766d22bd9134d8bc5d796ef2e67d2239)

## [v1.0.3](https://github.com/ljharb/Object.entries/compare/v1.0.2...v1.0.3) - 2015-10-06

### Commits

- Add test case to cover non-enumerable keys made enumerable by a previous getter. [`5b53808`](https://github.com/ljharb/Object.entries/commit/5b53808de5c7af8070b398a7d23fcfa60713ba70)
- [Dev Deps] update `tape`, `eslint`, `@ljharb/eslint-config` [`1c3ddff`](https://github.com/ljharb/Object.entries/commit/1c3ddff43a0ad435ebd23cb09ade6ceec3e38f02)
- [Dev Deps] update `jscs`, `eslint` [`0a0f1be`](https://github.com/ljharb/Object.entries/commit/0a0f1be8d4cb060689e5e7e8a16a8de5a358b599)
- [Deps] update `es-abstract` [`8cacfdc`](https://github.com/ljharb/Object.entries/commit/8cacfdc380db1f73f7f4331c934119c8323f39a3)

## [v1.0.2](https://github.com/ljharb/Object.entries/compare/v1.0.1...v1.0.2) - 2015-09-25

### Fixed

- Not-yet-visited keys deleted on a [[Get]] must not show up in the output [`#1`](https://github.com/ljharb/Object.entries/issues/1)

## [v1.0.1](https://github.com/ljharb/Object.entries/compare/v1.0.0...v1.0.1) - 2015-09-21

### Commits

- [Tests] on `iojs` `v3.3`, up to `node` `v4.1` [`181f888`](https://github.com/ljharb/Object.entries/commit/181f888a24cc89e2e3fd5cf2b93abbbda204242f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`e93536e`](https://github.com/ljharb/Object.entries/commit/e93536e3313967445764e27cd146800c18133060)
- Add es-shim API keyword [`51080c2`](https://github.com/ljharb/Object.entries/commit/51080c2c41f19567f1bda40d5dd00e7d8f177d64)
- [Docs] update version badge URL [`398a7e4`](https://github.com/ljharb/Object.entries/commit/398a7e4550eb37adf880013a735875a8986181da)

## v1.0.0 - 2015-09-02

### Commits

- Dotfiles [`3a59351`](https://github.com/ljharb/Object.entries/commit/3a59351792c3b06ffbb6f515198a35955a0a1124)
- Tests [`bd1ceae`](https://github.com/ljharb/Object.entries/commit/bd1ceae7b6e16381da6065513ce4373e0f15e0ad)
- package.json [`301832d`](https://github.com/ljharb/Object.entries/commit/301832d1fb1a688f879f7d06451062d2fea7671f)
- Read me [`d92e775`](https://github.com/ljharb/Object.entries/commit/d92e7756a80e0e3e2a32a1b6525b8d48ecd73391)
- Initial commit [`212a7ce`](https://github.com/ljharb/Object.entries/commit/212a7ce24ef0edbcc8c84d5d3338fe3a60ffa963)
- Implementation. [`8fd8aae`](https://github.com/ljharb/Object.entries/commit/8fd8aae302b1d25db118e5f127ff887e04f966c7)
- Clarifying tests that only Symbol *properties* are omitted. [`1b5cb92`](https://github.com/ljharb/Object.entries/commit/1b5cb92ae7edde17fb17971f961d157bef714c63)
