{"name": "socket.io-redis", "version": "6.1.1", "description": "", "license": "MIT", "repository": {"type": "git", "url": "**************:socketio/socket.io-redis.git"}, "files": ["dist/"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"test": "npm run format:check && tsc && nyc mocha --require ts-node/register test/index.ts", "format:check": "prettier --parser typescript --check 'lib/**/*.ts' 'test/**/*.ts'", "format:fix": "prettier --parser typescript --write 'lib/**/*.ts' 'test/**/*.ts'", "prepack": "tsc"}, "dependencies": {"debug": "~4.3.1", "notepack.io": "~2.2.0", "redis": "^3.0.0", "socket.io-adapter": "~2.2.0", "uid2": "0.0.3"}, "devDependencies": {"@types/expect.js": "^0.3.29", "@types/mocha": "^8.2.1", "@types/node": "^14.14.7", "expect.js": "0.3.1", "ioredis": "^4.0.0", "mocha": "^3.4.2", "nyc": "^15.1.0", "prettier": "^2.1.2", "socket.io": "^4.0.0", "socket.io-client": "^4.0.0", "ts-node": "^9.1.1", "typescript": "^4.0.5"}, "engines": {"node": ">=10.0.0"}}