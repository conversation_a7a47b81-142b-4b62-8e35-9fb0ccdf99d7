{"name": "redis", "version": "3.1.2", "description": "A high performance Redis client.", "keywords": ["database", "redis", "transaction", "pipelining", "performance", "queue", "nodejs", "pubsub", "backpressure"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON> (Salakar)", "url": "https://github.com/salakar"}, {"name": "<PERSON><PERSON> (BridgeAR)", "url": "https://github.com/BridgeAR"}], "license": "MIT", "main": "./index.js", "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "coverage": "nyc report --reporter=html", "benchmark": "node benchmarks/multi_bench.js", "test": "nyc --cache mocha ./test/*.spec.js ./test/commands/*.spec.js --timeout=8000 && npm run coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:report": "eslint --output-file=eslint-report.json --format=json  .", "compare": "node benchmarks/diff_multi_bench_output.js beforeBench.txt afterBench.txt"}, "dependencies": {"denque": "^1.5.0", "redis-commands": "^1.7.0", "redis-errors": "^1.2.0", "redis-parser": "^3.0.0"}, "engines": {"node": ">=10"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.1.0", "cross-spawn": "^7.0.3", "eslint": "^7.21.0", "intercept-stdout": "~0.1.2", "metrics": "^0.1.21", "mocha": "^8.3.0", "nyc": "^15.1.0", "prettier": "^2.2.1", "tcp-port-used": "^1.0.1", "uuid": "^8.3.2"}, "repository": {"type": "git", "url": "git://github.com/NodeRedis/node-redis.git"}, "bugs": {"url": "https://github.com/NodeRedis/node-redis/issues"}, "homepage": "https://github.com/NodeRedis/node-redis", "directories": {"example": "examples", "test": "test"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-redis"}}