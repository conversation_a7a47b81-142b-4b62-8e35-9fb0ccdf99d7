
# socket.io-adapter

Default socket.io in-memory adapter class.

Compatibility table:

| Adapter version | Socket.IO server version |
|-----------------| ------------------------ |
| 1.x.x           | 1.x.x / 2.x.x            |
| 2.x.x           | 3.x.x                    |

## How to use

This module is not intended for end-user usage, but can be used as an
interface to inherit from other adapters you might want to build.

As an example of an adapter that builds on top of this, please take a look
at [socket.io-redis](https://github.com/learnboost/socket.io-redis).

## License

MIT
