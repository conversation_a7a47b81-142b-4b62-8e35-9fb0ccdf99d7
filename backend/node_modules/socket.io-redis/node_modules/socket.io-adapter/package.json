{"name": "socket.io-adapter", "version": "2.2.0", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/socketio/socket.io-adapter.git"}, "files": ["dist/"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "description": "default socket.io in-memory adapter", "devDependencies": {"@types/node": "^14.11.2", "expect.js": "^0.3.1", "mocha": "^8.1.3", "nyc": "^15.1.0", "prettier": "^1.19.1", "typescript": "^4.0.3"}, "scripts": {"test": "npm run format:check && tsc && nyc mocha test/index.js", "format:check": "prettier --parser typescript --check 'lib/**/*.ts' 'test/**/*.js'", "format:fix": "prettier --parser typescript --write 'lib/**/*.ts' 'test/**/*.js'", "prepack": "tsc"}}