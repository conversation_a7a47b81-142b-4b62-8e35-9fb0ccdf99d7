{"name": "@opentelemetry/instrumentation-pg", "version": "0.37.2", "description": "OpenTelemetry postgres automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-pg --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test-all-versions": "cross-env IN_TAV=true tav", "test-all-versions:local": "cross-env IN_TAV=true RUN_POSTGRES_TESTS_LOCAL=true npm run test-all-versions", "test:debug": "ts-mocha --inspect-brk --no-timeouts -p tsconfig.json 'test/**/*.test.ts'", "test:local": "cross-env RUN_POSTGRES_TESTS_LOCAL=true npm run test", "version:update": "node ../../../scripts/version-update.js", "watch": "tsc -w"}, "keywords": ["instrumentation", "nodejs", "opentelemetry", "pg", "plugin", "postgres", "postgresql", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.35.1", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "@types/sinon": "10.0.18", "cross-env": "7.0.3", "mocha": "7.2.0", "nyc": "15.1.0", "pg": "8.7.1", "pg-pool": "3.4.1", "rimraf": "5.0.5", "safe-stable-stringify": "^2.4.1", "sinon": "15.2.0", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@opentelemetry/sql-common": "^0.40.0", "@types/pg": "8.6.1", "@types/pg-pool": "2.0.4"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-pg#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}