{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAQ4B;AAC5B,2DAAwD;AACxD,8EAG6C;AAU7C,yBAAuC;AACvC,oEAAwE;AAExE;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,gBAAgB,CAC9B,MAA0B,EAC1B,WAA8C;IAE9C,wEAAwE;IACxE,wEAAwE;IACxE,2DAA2D;IAC3D,IAAI,CAAC,WAAW;QAAE,OAAO,oBAAiB,CAAC,cAAc,CAAC;IAE1D,iEAAiE;IACjE,2DAA2D;IAC3D,MAAM,OAAO,GACX,OAAO,WAAW,CAAC,IAAI,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAI;QACtD,CAAC,CAAC,WAAW,CAAC,IAAI;QAClB,CAAC,CAAC,4BAA4B,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAErD,OAAO,GAAG,oBAAiB,CAAC,cAAc,IAAI,OAAO,GACnD,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,CAAC,CAAC,EAC1B,EAAE,CAAC;AACL,CAAC;AAnBD,4CAmBC;AAED,SAAS,4BAA4B,CAAC,SAAiB;IACrD,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAEzD,oFAAoF;IACpF,OAAO,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;AACzE,CAAC;AAED,SAAgB,mBAAmB,CAAC,MAAgC;IAClE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,WAAW,CAAC;IACxC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC;IACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;IACvC,OAAO,gBAAgB,IAAI,IAAI,IAAI,IAAI,QAAQ,EAAE,CAAC;AACpD,CAAC;AALD,kDAKC;AAED,SAAgB,mCAAmC,CACjD,MAAgC;IAEhC,OAAO;QACL,CAAC,yCAAkB,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,QAAQ;QAC7C,CAAC,yCAAkB,CAAC,oBAAoB,CAAC,EAAE,mBAAmB,CAAC,MAAM,CAAC;QACtE,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;QAC/C,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;QAC/C,CAAC,yCAAkB,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,IAAI;KAC1C,CAAC;AACJ,CAAC;AAVD,kFAUC;AAED,SAAgB,yBAAyB,CACvC,qBAA8C;IAE9C,OAAO,CACL,qBAAqB,CAAC,iBAAiB,KAAK,IAAI;QAChD,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,CAC9C,CAAC;AACJ,CAAC;AAPD,8DAOC;AAED,wDAAwD;AACxD,uEAAuE;AACvE,SAAgB,iBAAiB,CAE/B,MAAc,EACd,qBAA8C,EAC9C,WAAgE;IAEhE,qBAAqB;IACrB,MAAM,EAAE,oBAAoB,EAAE,GAAG,IAAI,CAAC;IACtC,MAAM,MAAM,GAAG,oBAAoB,CAAC,QAAQ,CAAC;IAE7C,MAAM,QAAQ,GAAG,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACvD,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;QACtC,IAAI,EAAE,cAAQ,CAAC,MAAM;QACrB,UAAU,kBACR,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,UAAU,IACtD,mCAAmC,CAAC,oBAAoB,CAAC,CAC7D;KACF,CAAC,CAAC;IAEH,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,IAAI,CAAC;KACb;IAED,iBAAiB;IACjB,IAAI,WAAW,CAAC,IAAI,EAAE;QACpB,IAAI,CAAC,YAAY,CAAC,yCAAkB,CAAC,YAAY,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;KACtE;IAED,IACE,qBAAqB,CAAC,yBAAyB;QAC/C,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,EACjC;QACA,IAAI;YACF,MAAM,eAAe,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACrD,IAAI,KAAK,IAAI,IAAI,EAAE;oBACjB,OAAO,MAAM,CAAC;iBACf;qBAAM,IAAI,KAAK,YAAY,MAAM,EAAE;oBAClC,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;iBACzB;qBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBACpC,IAAI,OAAO,KAAK,CAAC,UAAU,KAAK,UAAU,EAAE;wBAC1C,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;qBAC3B;oBACD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;iBAC9B;qBAAM;oBACL,gBAAgB;oBAChB,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;iBACzB;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,YAAY,CAAC,+BAAc,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;SAC9D;QAAC,OAAO,CAAC,EAAE;YACV,UAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAC3D;KACF;IAED,sCAAsC;IACtC,IAAI,OAAO,WAAW,CAAC,IAAI,KAAK,QAAQ,EAAE;QACxC,IAAI,CAAC,YAAY,CAAC,+BAAc,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;KAC7D;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AA5DD,8CA4DC;AAED,SAAgB,qBAAqB,CACnC,MAA+B,EAC/B,IAAU,EACV,QAAkE;IAElE,IAAI,OAAO,MAAM,CAAC,YAAY,KAAK,UAAU,EAAE;QAC7C,IAAA,wCAAsB,EACpB,GAAG,EAAE;YACH,MAAM,CAAC,YAAa,CAAC,IAAI,EAAE;gBACzB,IAAI,EAAE,QAA0D;aACjE,CAAC,CAAC;QACL,CAAC,EACD,GAAG,CAAC,EAAE;YACJ,IAAI,GAAG,EAAE;gBACP,UAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;aAChD;QACH,CAAC,EACD,IAAI,CACL,CAAC;KACH;AACH,CAAC;AApBD,sDAoBC;AAED,SAAgB,aAAa,CAC3B,qBAA8C,EAC9C,IAAU,EACV,EAAoB;IAEpB,OAAO,SAAS,eAAe,CAE7B,GAAU,EACV,GAAW;QAEX,IAAI,GAAG,EAAE;YACP,6BAA6B;YAC7B,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;aAAM;YACL,qBAAqB,CAAC,qBAAqB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC;AAvBD,sCAuBC;AAED,SAAgB,mBAAmB,CACjC,IAAU,EACV,EAAkB;IAElB,OAAO,SAAS,eAAe,CAE7B,GAAU,EACV,GAAW,EACX,IAAS;QAET,IAAI,GAAG,EAAE;YACP,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC,CAAC;AACJ,CAAC;AAnBD,kDAmBC;AAED,SAAgB,0BAA0B,CAAC,IAAU,EAAE,EAAY;IACjE,OAAO,SAAS,4BAA4B,CAE1C,GAAU;QAEV,IAAI,GAAG,EAAE;YACP,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC5B,CAAC,CAAC;AACJ,CAAC;AAdD,gEAcC;AAED;;;;GAIG;AACH,SAAgB,eAAe,CAAC,CAAU;IACxC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,IAAI,SAAS,IAAI,CAAC;QAC1D,CAAC,CAAC,MAAM,CAAE,CAA2B,CAAC,OAAO,CAAC;QAC9C,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC;AAJD,0CAIC;AAED,SAAgB,sBAAsB,CAAC,EAAW;;IAChD,OAAO,CACL,OAAO,EAAE,KAAK,QAAQ;QACtB,OAAO,CAAA,MAAC,EAAgC,0CAAE,IAAI,CAAA,KAAK,QAAQ,CAC5D,CAAC;AACJ,CAAC;AALD,wDAKC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context,\n  trace,\n  Span,\n  SpanStatusCode,\n  Tracer,\n  SpanKind,\n  diag,\n} from '@opentelemetry/api';\nimport { AttributeNames } from './enums/AttributeNames';\nimport {\n  SemanticAttributes,\n  DbSystemValues,\n} from '@opentelemetry/semantic-conventions';\nimport {\n  PgClientExtended,\n  PostgresCallback,\n  PgPoolCallback,\n  PgPoolExtended,\n  PgParsedConnectionParams,\n} from './internal-types';\nimport { PgInstrumentationConfig } from './types';\nimport type * as pgTypes from 'pg';\nimport { PgInstrumentation } from './';\nimport { safeExecuteInTheMiddle } from '@opentelemetry/instrumentation';\n\n/**\n * Helper function to get a low cardinality span name from whatever info we have\n * about the query.\n *\n * This is tricky, because we don't have most of the information (table name,\n * operation name, etc) the spec recommends using to build a low-cardinality\n * value w/o parsing. So, we use db.name and assume that, if the query's a named\n * prepared statement, those `name` values will be low cardinality. If we don't\n * have a named prepared statement, we try to parse an operation (despite the\n * spec's warnings).\n *\n * @params dbName The name of the db against which this query is being issued,\n *   which could be missing if no db name was given at the time that the\n *   connection was established.\n * @params queryConfig Information we have about the query being issued, typed\n *   to reflect only the validation we've actually done on the args to\n *   `client.query()`. This will be undefined if `client.query()` was called\n *   with invalid arguments.\n */\nexport function getQuerySpanName(\n  dbName: string | undefined,\n  queryConfig?: { text: string; name?: unknown }\n) {\n  // NB: when the query config is invalid, we omit the dbName too, so that\n  // someone (or some tool) reading the span name doesn't misinterpret the\n  // dbName as being a prepared statement or sql commit name.\n  if (!queryConfig) return PgInstrumentation.BASE_SPAN_NAME;\n\n  // Either the name of a prepared statement; or an attempted parse\n  // of the SQL command, normalized to uppercase; or unknown.\n  const command =\n    typeof queryConfig.name === 'string' && queryConfig.name\n      ? queryConfig.name\n      : parseNormalizedOperationName(queryConfig.text);\n\n  return `${PgInstrumentation.BASE_SPAN_NAME}:${command}${\n    dbName ? ` ${dbName}` : ''\n  }`;\n}\n\nfunction parseNormalizedOperationName(queryText: string) {\n  const sqlCommand = queryText.split(' ')[0].toUpperCase();\n\n  // Handle query text being \"COMMIT;\", which has an extra semicolon before the space.\n  return sqlCommand.endsWith(';') ? sqlCommand.slice(0, -1) : sqlCommand;\n}\n\nexport function getConnectionString(params: PgParsedConnectionParams) {\n  const host = params.host || 'localhost';\n  const port = params.port || 5432;\n  const database = params.database || '';\n  return `postgresql://${host}:${port}/${database}`;\n}\n\nexport function getSemanticAttributesFromConnection(\n  params: PgParsedConnectionParams\n) {\n  return {\n    [SemanticAttributes.DB_NAME]: params.database, // required\n    [SemanticAttributes.DB_CONNECTION_STRING]: getConnectionString(params), // required\n    [SemanticAttributes.NET_PEER_NAME]: params.host, // required\n    [SemanticAttributes.NET_PEER_PORT]: params.port,\n    [SemanticAttributes.DB_USER]: params.user,\n  };\n}\n\nexport function shouldSkipInstrumentation(\n  instrumentationConfig: PgInstrumentationConfig\n) {\n  return (\n    instrumentationConfig.requireParentSpan === true &&\n    trace.getSpan(context.active()) === undefined\n  );\n}\n\n// Create a span from our normalized queryConfig object,\n// or return a basic span if no queryConfig was given/could be created.\nexport function handleConfigQuery(\n  this: PgClientExtended,\n  tracer: Tracer,\n  instrumentationConfig: PgInstrumentationConfig,\n  queryConfig?: { text: string; values?: unknown; name?: unknown }\n) {\n  // Create child span.\n  const { connectionParameters } = this;\n  const dbName = connectionParameters.database;\n\n  const spanName = getQuerySpanName(dbName, queryConfig);\n  const span = tracer.startSpan(spanName, {\n    kind: SpanKind.CLIENT,\n    attributes: {\n      [SemanticAttributes.DB_SYSTEM]: DbSystemValues.POSTGRESQL, // required\n      ...getSemanticAttributesFromConnection(connectionParameters),\n    },\n  });\n\n  if (!queryConfig) {\n    return span;\n  }\n\n  // Set attributes\n  if (queryConfig.text) {\n    span.setAttribute(SemanticAttributes.DB_STATEMENT, queryConfig.text);\n  }\n\n  if (\n    instrumentationConfig.enhancedDatabaseReporting &&\n    Array.isArray(queryConfig.values)\n  ) {\n    try {\n      const convertedValues = queryConfig.values.map(value => {\n        if (value == null) {\n          return 'null';\n        } else if (value instanceof Buffer) {\n          return value.toString();\n        } else if (typeof value === 'object') {\n          if (typeof value.toPostgres === 'function') {\n            return value.toPostgres();\n          }\n          return JSON.stringify(value);\n        } else {\n          //string, number\n          return value.toString();\n        }\n      });\n      span.setAttribute(AttributeNames.PG_VALUES, convertedValues);\n    } catch (e) {\n      diag.error('failed to stringify ', queryConfig.values, e);\n    }\n  }\n\n  // Set plan name attribute, if present\n  if (typeof queryConfig.name === 'string') {\n    span.setAttribute(AttributeNames.PG_PLAN, queryConfig.name);\n  }\n\n  return span;\n}\n\nexport function handleExecutionResult(\n  config: PgInstrumentationConfig,\n  span: Span,\n  pgResult: pgTypes.QueryResult | pgTypes.QueryArrayResult | unknown\n) {\n  if (typeof config.responseHook === 'function') {\n    safeExecuteInTheMiddle(\n      () => {\n        config.responseHook!(span, {\n          data: pgResult as pgTypes.QueryResult | pgTypes.QueryArrayResult,\n        });\n      },\n      err => {\n        if (err) {\n          diag.error('Error running response hook', err);\n        }\n      },\n      true\n    );\n  }\n}\n\nexport function patchCallback(\n  instrumentationConfig: PgInstrumentationConfig,\n  span: Span,\n  cb: PostgresCallback\n): PostgresCallback {\n  return function patchedCallback(\n    this: PgClientExtended,\n    err: Error,\n    res: object\n  ) {\n    if (err) {\n      // span.recordException(err);\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      });\n    } else {\n      handleExecutionResult(instrumentationConfig, span, res);\n    }\n\n    span.end();\n    cb.call(this, err, res);\n  };\n}\n\nexport function patchCallbackPGPool(\n  span: Span,\n  cb: PgPoolCallback\n): PgPoolCallback {\n  return function patchedCallback(\n    this: PgPoolExtended,\n    err: Error,\n    res: object,\n    done: any\n  ) {\n    if (err) {\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      });\n    }\n    span.end();\n    cb.call(this, err, res, done);\n  };\n}\n\nexport function patchClientConnectCallback(span: Span, cb: Function): Function {\n  return function patchedClientConnectCallback(\n    this: pgTypes.Client,\n    err: Error\n  ) {\n    if (err) {\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      });\n    }\n    span.end();\n    cb.apply(this, arguments);\n  };\n}\n\n/**\n * Attempt to get a message string from a thrown value, while being quite\n * defensive, to recognize the fact that, in JS, any kind of value (even\n * primitives) can be thrown.\n */\nexport function getErrorMessage(e: unknown) {\n  return typeof e === 'object' && e !== null && 'message' in e\n    ? String((e as { message?: unknown }).message)\n    : undefined;\n}\n\nexport function isObjectWithTextString(it: unknown): it is ObjectWithText {\n  return (\n    typeof it === 'object' &&\n    typeof (it as null | { text?: unknown })?.text === 'string'\n  );\n}\n\nexport type ObjectWithText = {\n  text: string;\n  [k: string]: unknown;\n};\n"]}