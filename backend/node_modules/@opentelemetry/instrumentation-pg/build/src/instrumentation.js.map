{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,oEAKwC;AAExC,4CAM4B;AAW5B,iCAAiC;AACjC,2DAAwD;AACxD,8EAG6C;AAC7C,0DAAmE;AACnE,uCAAoC;AAEpC,MAAM,iBAAiB,GAAG,SAAS,CAAC;AAEpC,MAAa,iBAAkB,SAAQ,qCAAmB;IAKxD,YAAY,SAAkC,EAAE;QAC9C,KAAK,CACH,mCAAmC,EACnC,iBAAO,EACP,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAC1B,CAAC;IACJ,CAAC;IAES,IAAI;QACZ,MAAM,QAAQ,GAAG,IAAI,qDAAmC,CACtD,IAAI,EACJ,CAAC,KAAK,CAAC,EACP,CAAC,MAAW,EAAE,aAAa,EAAE,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,yBAAyB,aAAa,EAAE,CAAC,CAAC;YAC3D,MAAM,aAAa,GACjB,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ;gBACrC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;gBACvB,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW;YACzB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;aACvD;YAED,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;gBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aACzD;YAED,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,OAAO,EACP,IAAI,CAAC,oBAAoB,EAAS,CACnC,CAAC;YAEF,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,SAAS,EACT,IAAI,CAAC,sBAAsB,EAAS,CACrC,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,MAAW,EAAE,aAAa,EAAE,EAAE;YAC7B,MAAM,aAAa,GACjB,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ;gBACrC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;gBACvB,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW;YACzB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,yBAAyB,aAAa,EAAE,CAAC,CAAC;YAC3D,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;aACvD;QACH,CAAC,CACF,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,qDAAmC,CAG1D,SAAS,EACT,CAAC,KAAK,EAAE,KAAK,CAAC,EACd,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;YAChE,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;gBAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aAClD;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,EACvB,SAAS,EACT,IAAI,CAAC,oBAAoB,EAAS,CACnC,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;YAChE,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;gBAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aAClD;QACH,CAAC,CACF,CAAC;QAEF,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IAClC,CAAC;IAEQ,SAAS,CAAC,SAAkC,EAAE;QACrD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,OAAkC,CAAC;IACjD,CAAC;IAEO,sBAAsB;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,QAAyB,EAAE,EAAE;YACnC,OAAO,SAAS,OAAO,CAAuB,QAAmB;gBAC/D,IAAI,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE;oBACvD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;iBACtC;gBAED,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAClC,GAAG,iBAAiB,CAAC,SAAS,UAAU,EACxC;oBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,kBACR,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,UAAU,IACtD,KAAK,CAAC,mCAAmC,CAAC,IAAI,CAAC,CACnD;iBACF,CACF,CAAC;gBAEF,IAAI,QAAQ,EAAE;oBACZ,MAAM,UAAU,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;oBACnD,QAAQ,GAAG,KAAK,CAAC,0BAA0B,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC5D,IAAI,UAAU,EAAE;wBACd,QAAQ,GAAG,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;qBACrD;iBACF;gBAED,MAAM,aAAa,GAAY,aAAO,CAAC,IAAI,CACzC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EACrC,GAAG,EAAE;oBACH,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACvC,CAAC,CACF,CAAC;gBAEF,OAAO,mBAAmB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAClD,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,QAA+C,EAAE,EAAE;YACzD,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,YAAY,iBAAiB,CAAC,SAAS,yBAAyB,CACjE,CAAC;YACF,OAAO,SAAS,KAAK,CAAyB,GAAG,IAAe;gBAC9D,IAAI,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE;oBACvD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAa,CAAC,CAAC;iBAC5C;gBAED,gEAAgE;gBAChE,sEAAsE;gBACtE,uEAAuE;gBACvE,oEAAoE;gBACpE,wEAAwE;gBACxE,wEAAwE;gBACxE,kEAAkE;gBAClE,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM,gBAAgB,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC;gBAClD,MAAM,6BAA6B,GACjC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;gBAErC,yEAAyE;gBACzE,yEAAyE;gBACzE,kDAAkD;gBAClD,MAAM,WAAW,GAAG,gBAAgB;oBAClC,CAAC,CAAC;wBACE,IAAI,EAAE,IAAc;wBACpB,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;qBACrD;oBACH,CAAC,CAAC,6BAA6B;wBAC/B,CAAC,CAAE,IAA6B;wBAChC,CAAC,CAAC,SAAS,CAAC;gBAEd,MAAM,qBAAqB,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;gBAEjD,MAAM,IAAI,GAAG,KAAK,CAAC,iBAAiB,CAAC,IAAI,CACvC,IAAI,EACJ,MAAM,CAAC,MAAM,EACb,qBAAqB,EACrB,WAAW,CACZ,CAAC;gBAEF,sEAAsE;gBACtE,+DAA+D;gBAC/D,IAAI,qBAAqB,CAAC,+BAA+B,EAAE;oBACzD,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB;wBACxB,CAAC,CAAC,IAAA,mCAAsB,EAAC,IAAI,EAAE,IAAI,CAAC;wBACpC,CAAC,CAAC,6BAA6B;4BAC/B,CAAC,iCACM,IAAI,KACP,IAAI,EAAE,IAAA,mCAAsB,EAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAEjD,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACb;gBAED,iDAAiD;gBACjD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBACnB,MAAM,UAAU,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;oBACnD,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;wBAC/C,gCAAgC;wBAChC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,aAAa,CACzC,qBAAqB,EACrB,IAAI,EACJ,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAqB,CAAC,qBAAqB;yBAChE,CAAC;wBAEF,6CAA6C;wBAC7C,IAAI,UAAU,EAAE;4BACd,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,aAAO,CAAC,IAAI,CAClC,aAAO,CAAC,MAAM,EAAE,EAChB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CACtB,CAAC;yBACH;qBACF;yBAAM,IAAI,OAAO,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ,CAAA,KAAK,UAAU,EAAE;wBACtD,6BAA6B;wBAC7B,IAAI,QAAQ,GAAG,KAAK,CAAC,aAAa,CAChC,MAAM,CAAC,SAAS,EAAE,EAClB,IAAI,EACJ,WAAW,CAAC,QAA4B,CAAC,qBAAqB;yBAC/D,CAAC;wBAEF,8CAA8C;wBAC9C,IAAI,UAAU,EAAE;4BACd,QAAQ,GAAG,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;yBACrD;wBAEA,IAAI,CAAC,CAAC,CAAqC,CAAC,QAAQ,GAAG,QAAQ,CAAC;qBAClE;iBACF;gBAED,IACE,OAAO,qBAAqB,CAAC,WAAW,KAAK,UAAU;oBACvD,WAAW,EACX;oBACA,IAAA,wCAAsB,EACpB,GAAG,EAAE;wBACH,kEAAkE;wBAClE,uCAAuC;wBACvC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC;wBACjE,MAAM,UAAU,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;wBAElD,qBAAqB,CAAC,WAAY,CAAC,IAAI,EAAE;4BACvC,UAAU;4BACV,KAAK,EAAE;gCACL,IAAI,EAAE,WAAW,CAAC,IAAI;gCACtB,yDAAyD;gCACzD,6DAA6D;gCAC7D,8DAA8D;gCAC9D,yDAAyD;gCACzD,4DAA4D;gCAC5D,2DAA2D;gCAC3D,8DAA8D;gCAC9D,0DAA0D;gCAC1D,2DAA2D;gCAC3D,0DAA0D;gCAC1D,6DAA6D;gCAC7D,4DAA4D;gCAC5D,6DAA6D;gCAC7D,8DAA8D;gCAC9D,kEAAkE;gCAClE,MAAM,EAAE,WAAW,CAAC,MAAmB;gCACvC,IAAI,EAAE,WAAW,CAAC,IAA0B;6BAC7C;yBACF,CAAC,CAAC;oBACL,CAAC,EACD,GAAG,CAAC,EAAE;wBACJ,IAAI,GAAG,EAAE;4BACP,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;yBACrD;oBACH,CAAC,EACD,IAAI,CACL,CAAC;iBACH;gBAED,IAAI,MAAe,CAAC;gBACpB,IAAI;oBACF,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAa,CAAC,CAAC;iBAC9C;gBAAC,OAAO,CAAU,EAAE;oBACnB,2BAA2B;oBAC3B,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;wBAC1B,OAAO,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;qBAClC,CAAC,CAAC;oBACH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,MAAM,CAAC,CAAC;iBACT;gBAED,+CAA+C;gBAC/C,IAAI,MAAM,YAAY,OAAO,EAAE;oBAC7B,OAAO,MAAM;yBACV,IAAI,CAAC,CAAC,MAAe,EAAE,EAAE;wBACxB,yFAAyF;wBACzF,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;4BAC3B,KAAK,CAAC,qBAAqB,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;4BAC9D,IAAI,CAAC,GAAG,EAAE,CAAC;4BACX,OAAO,CAAC,MAAM,CAAC,CAAC;wBAClB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;wBACtB,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;4BAC/B,IAAI,CAAC,SAAS,CAAC;gCACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gCAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;6BACvB,CAAC,CAAC;4BACH,IAAI,CAAC,GAAG,EAAE,CAAC;4BACX,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;iBACN;gBAED,oBAAoB;gBACpB,OAAO,MAAM,CAAC,CAAC,OAAO;YACxB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,eAAqD,EAAE,EAAE;YAC/D,OAAO,SAAS,OAAO,CAAuB,QAAyB;gBACrE,IAAI,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE;oBACvD,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAe,CAAC,CAAC;iBACpD;gBAED,aAAa;gBACb,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,iBAAiB,UAAU,EAAE;oBACnE,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,gCACR,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,UAAU,IACtD,KAAK,CAAC,mCAAmC,CAAC,IAAI,CAAC,OAAO,CAAC,KAC1D,CAAC,+BAAc,CAAC,mBAAmB,CAAC,EAClC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAChC,CAAC,+BAAc,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GACpD;iBACF,CAAC,CAAC;gBAEH,IAAI,QAAQ,EAAE;oBACZ,MAAM,UAAU,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;oBACnD,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAClC,IAAI,EACJ,QAAQ,CACS,CAAC;oBACpB,6CAA6C;oBAC7C,IAAI,UAAU,EAAE;wBACd,QAAQ,GAAG,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;qBACrD;iBACF;gBAED,MAAM,aAAa,GAAY,aAAO,CAAC,IAAI,CACzC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EACrC,GAAG,EAAE;oBACH,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAe,CAAC,CAAC;gBACrD,CAAC,CACF,CAAC;gBAEF,OAAO,mBAAmB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAClD,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;;AAhWH,8CAiWC;AAhWiB,2BAAS,GAAG,IAAI,CAAC;AAEjB,gCAAc,GAAG,iBAAiB,CAAC,SAAS,GAAG,QAAQ,CAAC;AAgW1E,SAAS,mBAAmB,CAAC,IAAU,EAAE,aAAsB;IAC7D,IAAI,CAAC,CAAC,aAAa,YAAY,OAAO,CAAC,EAAE;QACvC,OAAO,aAAa,CAAC;KACtB;IAED,MAAM,oBAAoB,GAAG,aAAiC,CAAC;IAC/D,OAAO,aAAO,CAAC,IAAI,CACjB,aAAO,CAAC,MAAM,EAAE,EAChB,oBAAoB;SACjB,IAAI,CAAC,MAAM,CAAC,EAAE;QACb,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAc,EAAE,EAAE;QACxB,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,oBAAc,CAAC,KAAK;YAC1B,OAAO,EAAE,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC;SACtC,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC,CAAC,CACL,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  isWrapped,\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\n\nimport {\n  context,\n  trace,\n  Span,\n  SpanStatusCode,\n  SpanKind,\n} from '@opentelemetry/api';\nimport type * as pgTypes from 'pg';\nimport type * as pgPoolTypes from 'pg-pool';\nimport {\n  PgClientConnect,\n  PgClientExtended,\n  PostgresCallback,\n  PgPoolExtended,\n  PgPoolCallback,\n} from './internal-types';\nimport { PgInstrumentationConfig } from './types';\nimport * as utils from './utils';\nimport { AttributeNames } from './enums/AttributeNames';\nimport {\n  SemanticAttributes,\n  DbSystemValues,\n} from '@opentelemetry/semantic-conventions';\nimport { addSqlCommenterComment } from '@opentelemetry/sql-common';\nimport { VERSION } from './version';\n\nconst PG_POOL_COMPONENT = 'pg-pool';\n\nexport class PgInstrumentation extends InstrumentationBase {\n  static readonly COMPONENT = 'pg';\n\n  static readonly BASE_SPAN_NAME = PgInstrumentation.COMPONENT + '.query';\n\n  constructor(config: PgInstrumentationConfig = {}) {\n    super(\n      '@opentelemetry/instrumentation-pg',\n      VERSION,\n      Object.assign({}, config)\n    );\n  }\n\n  protected init() {\n    const modulePG = new InstrumentationNodeModuleDefinition<typeof pgTypes>(\n      'pg',\n      ['8.*'],\n      (module: any, moduleVersion) => {\n        this._diag.debug(`Applying patch for pg@${moduleVersion}`);\n        const moduleExports: typeof pgTypes =\n          module[Symbol.toStringTag] === 'Module'\n            ? module.default // ESM\n            : module; // CommonJS\n        if (isWrapped(moduleExports.Client.prototype.query)) {\n          this._unwrap(moduleExports.Client.prototype, 'query');\n        }\n\n        if (isWrapped(moduleExports.Client.prototype.connect)) {\n          this._unwrap(moduleExports.Client.prototype, 'connect');\n        }\n\n        this._wrap(\n          moduleExports.Client.prototype,\n          'query',\n          this._getClientQueryPatch() as any\n        );\n\n        this._wrap(\n          moduleExports.Client.prototype,\n          'connect',\n          this._getClientConnectPatch() as any\n        );\n\n        return module;\n      },\n      (module: any, moduleVersion) => {\n        const moduleExports: typeof pgTypes =\n          module[Symbol.toStringTag] === 'Module'\n            ? module.default // ESM\n            : module; // CommonJS\n        this._diag.debug(`Removing patch for pg@${moduleVersion}`);\n        if (isWrapped(moduleExports.Client.prototype.query)) {\n          this._unwrap(moduleExports.Client.prototype, 'query');\n        }\n      }\n    );\n\n    const modulePGPool = new InstrumentationNodeModuleDefinition<\n      typeof pgPoolTypes\n    >(\n      'pg-pool',\n      ['2.*', '3.*'],\n      (moduleExports, moduleVersion) => {\n        this._diag.debug(`Applying patch for pg-pool@${moduleVersion}`);\n        if (isWrapped(moduleExports.prototype.connect)) {\n          this._unwrap(moduleExports.prototype, 'connect');\n        }\n        this._wrap(\n          moduleExports.prototype,\n          'connect',\n          this._getPoolConnectPatch() as any\n        );\n        return moduleExports;\n      },\n      (moduleExports, moduleVersion) => {\n        this._diag.debug(`Removing patch for pg-pool@${moduleVersion}`);\n        if (isWrapped(moduleExports.prototype.connect)) {\n          this._unwrap(moduleExports.prototype, 'connect');\n        }\n      }\n    );\n\n    return [modulePG, modulePGPool];\n  }\n\n  override setConfig(config: PgInstrumentationConfig = {}) {\n    this._config = Object.assign({}, config);\n  }\n\n  override getConfig(): PgInstrumentationConfig {\n    return this._config as PgInstrumentationConfig;\n  }\n\n  private _getClientConnectPatch() {\n    const plugin = this;\n    return (original: PgClientConnect) => {\n      return function connect(this: pgTypes.Client, callback?: Function) {\n        if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n          return original.call(this, callback);\n        }\n\n        const span = plugin.tracer.startSpan(\n          `${PgInstrumentation.COMPONENT}.connect`,\n          {\n            kind: SpanKind.CLIENT,\n            attributes: {\n              [SemanticAttributes.DB_SYSTEM]: DbSystemValues.POSTGRESQL,\n              ...utils.getSemanticAttributesFromConnection(this),\n            },\n          }\n        );\n\n        if (callback) {\n          const parentSpan = trace.getSpan(context.active());\n          callback = utils.patchClientConnectCallback(span, callback);\n          if (parentSpan) {\n            callback = context.bind(context.active(), callback);\n          }\n        }\n\n        const connectResult: unknown = context.with(\n          trace.setSpan(context.active(), span),\n          () => {\n            return original.call(this, callback);\n          }\n        );\n\n        return handleConnectResult(span, connectResult);\n      };\n    };\n  }\n\n  private _getClientQueryPatch() {\n    const plugin = this;\n    return (original: typeof pgTypes.Client.prototype.query) => {\n      this._diag.debug(\n        `Patching ${PgInstrumentation.COMPONENT}.Client.prototype.query`\n      );\n      return function query(this: PgClientExtended, ...args: unknown[]) {\n        if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n          return original.apply(this, args as never);\n        }\n\n        // client.query(text, cb?), client.query(text, values, cb?), and\n        // client.query(configObj, cb?) are all valid signatures. We construct\n        // a queryConfig obj from all (valid) signatures to build the span in a\n        // unified way. We verify that we at least have query text, and code\n        // defensively when dealing with `queryConfig` after that (to handle all\n        // the other invalid cases, like a non-array for values being provided).\n        // The type casts here reflect only what we've actually validated.\n        const arg0 = args[0];\n        const firstArgIsString = typeof arg0 === 'string';\n        const firstArgIsQueryObjectWithText =\n          utils.isObjectWithTextString(arg0);\n\n        // TODO: remove the `as ...` casts below when the TS version is upgraded.\n        // Newer TS versions will use the result of firstArgIsQueryObjectWithText\n        // to properly narrow arg0, but TS 4.3.5 does not.\n        const queryConfig = firstArgIsString\n          ? {\n              text: arg0 as string,\n              values: Array.isArray(args[1]) ? args[1] : undefined,\n            }\n          : firstArgIsQueryObjectWithText\n          ? (arg0 as utils.ObjectWithText)\n          : undefined;\n\n        const instrumentationConfig = plugin.getConfig();\n\n        const span = utils.handleConfigQuery.call(\n          this,\n          plugin.tracer,\n          instrumentationConfig,\n          queryConfig\n        );\n\n        // Modify query text w/ a tracing comment before invoking original for\n        // tracing, but only if args[0] has one of our expected shapes.\n        if (instrumentationConfig.addSqlCommenterCommentToQueries) {\n          args[0] = firstArgIsString\n            ? addSqlCommenterComment(span, arg0)\n            : firstArgIsQueryObjectWithText\n            ? {\n                ...arg0,\n                text: addSqlCommenterComment(span, arg0.text),\n              }\n            : args[0];\n        }\n\n        // Bind callback (if any) to parent span (if any)\n        if (args.length > 0) {\n          const parentSpan = trace.getSpan(context.active());\n          if (typeof args[args.length - 1] === 'function') {\n            // Patch ParameterQuery callback\n            args[args.length - 1] = utils.patchCallback(\n              instrumentationConfig,\n              span,\n              args[args.length - 1] as PostgresCallback // nb: not type safe.\n            );\n\n            // If a parent span exists, bind the callback\n            if (parentSpan) {\n              args[args.length - 1] = context.bind(\n                context.active(),\n                args[args.length - 1]\n              );\n            }\n          } else if (typeof queryConfig?.callback === 'function') {\n            // Patch ConfigQuery callback\n            let callback = utils.patchCallback(\n              plugin.getConfig(),\n              span,\n              queryConfig.callback as PostgresCallback // nb: not type safe.\n            );\n\n            // If a parent span existed, bind the callback\n            if (parentSpan) {\n              callback = context.bind(context.active(), callback);\n            }\n\n            (args[0] as { callback?: PostgresCallback }).callback = callback;\n          }\n        }\n\n        if (\n          typeof instrumentationConfig.requestHook === 'function' &&\n          queryConfig\n        ) {\n          safeExecuteInTheMiddle(\n            () => {\n              // pick keys to expose explicitly, so we're not leaking pg package\n              // internals that are subject to change\n              const { database, host, port, user } = this.connectionParameters;\n              const connection = { database, host, port, user };\n\n              instrumentationConfig.requestHook!(span, {\n                connection,\n                query: {\n                  text: queryConfig.text,\n                  // nb: if `client.query` is called with illegal arguments\n                  // (e.g., if `queryConfig.values` is passed explicitly, but a\n                  // non-array is given), then the type casts will be wrong. But\n                  // we leave it up to the queryHook to handle that, and we\n                  // catch and swallow any errors it throws. The other options\n                  // are all worse. E.g., we could leave `queryConfig.values`\n                  // and `queryConfig.name` as `unknown`, but then the hook body\n                  // would be forced to validate (or cast) them before using\n                  // them, which seems incredibly cumbersome given that these\n                  // casts will be correct 99.9% of the time -- and pg.query\n                  // will immediately throw during development in the other .1%\n                  // of cases. Alternatively, we could simply skip calling the\n                  // hook when `values` or `name` don't have the expected type,\n                  // but that would add unnecessary validation overhead to every\n                  // hook invocation and possibly be even more confusing/unexpected.\n                  values: queryConfig.values as unknown[],\n                  name: queryConfig.name as string | undefined,\n                },\n              });\n            },\n            err => {\n              if (err) {\n                plugin._diag.error('Error running query hook', err);\n              }\n            },\n            true\n          );\n        }\n\n        let result: unknown;\n        try {\n          result = original.apply(this, args as never);\n        } catch (e: unknown) {\n          // span.recordException(e);\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: utils.getErrorMessage(e),\n          });\n          span.end();\n          throw e;\n        }\n\n        // Bind promise to parent span and end the span\n        if (result instanceof Promise) {\n          return result\n            .then((result: unknown) => {\n              // Return a pass-along promise which ends the span and then goes to user's orig resolvers\n              return new Promise(resolve => {\n                utils.handleExecutionResult(plugin.getConfig(), span, result);\n                span.end();\n                resolve(result);\n              });\n            })\n            .catch((error: Error) => {\n              return new Promise((_, reject) => {\n                span.setStatus({\n                  code: SpanStatusCode.ERROR,\n                  message: error.message,\n                });\n                span.end();\n                reject(error);\n              });\n            });\n        }\n\n        // else returns void\n        return result; // void\n      };\n    };\n  }\n\n  private _getPoolConnectPatch() {\n    const plugin = this;\n    return (originalConnect: typeof pgPoolTypes.prototype.connect) => {\n      return function connect(this: PgPoolExtended, callback?: PgPoolCallback) {\n        if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n          return originalConnect.call(this, callback as any);\n        }\n\n        // setup span\n        const span = plugin.tracer.startSpan(`${PG_POOL_COMPONENT}.connect`, {\n          kind: SpanKind.CLIENT,\n          attributes: {\n            [SemanticAttributes.DB_SYSTEM]: DbSystemValues.POSTGRESQL,\n            ...utils.getSemanticAttributesFromConnection(this.options),\n            [AttributeNames.IDLE_TIMEOUT_MILLIS]:\n              this.options.idleTimeoutMillis,\n            [AttributeNames.MAX_CLIENT]: this.options.maxClient,\n          },\n        });\n\n        if (callback) {\n          const parentSpan = trace.getSpan(context.active());\n          callback = utils.patchCallbackPGPool(\n            span,\n            callback\n          ) as PgPoolCallback;\n          // If a parent span exists, bind the callback\n          if (parentSpan) {\n            callback = context.bind(context.active(), callback);\n          }\n        }\n\n        const connectResult: unknown = context.with(\n          trace.setSpan(context.active(), span),\n          () => {\n            return originalConnect.call(this, callback as any);\n          }\n        );\n\n        return handleConnectResult(span, connectResult);\n      };\n    };\n  }\n}\n\nfunction handleConnectResult(span: Span, connectResult: unknown) {\n  if (!(connectResult instanceof Promise)) {\n    return connectResult;\n  }\n\n  const connectResultPromise = connectResult as Promise<unknown>;\n  return context.bind(\n    context.active(),\n    connectResultPromise\n      .then(result => {\n        span.end();\n        return result;\n      })\n      .catch((error: unknown) => {\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: utils.getErrorMessage(error),\n        });\n        span.end();\n        return Promise.reject(error);\n      })\n  );\n}\n"]}