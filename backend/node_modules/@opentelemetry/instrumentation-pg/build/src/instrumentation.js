"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PgInstrumentation = void 0;
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const instrumentation_1 = require("@opentelemetry/instrumentation");
const api_1 = require("@opentelemetry/api");
const utils = require("./utils");
const AttributeNames_1 = require("./enums/AttributeNames");
const semantic_conventions_1 = require("@opentelemetry/semantic-conventions");
const sql_common_1 = require("@opentelemetry/sql-common");
const version_1 = require("./version");
const PG_POOL_COMPONENT = 'pg-pool';
class PgInstrumentation extends instrumentation_1.InstrumentationBase {
    constructor(config = {}) {
        super('@opentelemetry/instrumentation-pg', version_1.VERSION, Object.assign({}, config));
    }
    init() {
        const modulePG = new instrumentation_1.InstrumentationNodeModuleDefinition('pg', ['8.*'], (module, moduleVersion) => {
            this._diag.debug(`Applying patch for pg@${moduleVersion}`);
            const moduleExports = module[Symbol.toStringTag] === 'Module'
                ? module.default // ESM
                : module; // CommonJS
            if ((0, instrumentation_1.isWrapped)(moduleExports.Client.prototype.query)) {
                this._unwrap(moduleExports.Client.prototype, 'query');
            }
            if ((0, instrumentation_1.isWrapped)(moduleExports.Client.prototype.connect)) {
                this._unwrap(moduleExports.Client.prototype, 'connect');
            }
            this._wrap(moduleExports.Client.prototype, 'query', this._getClientQueryPatch());
            this._wrap(moduleExports.Client.prototype, 'connect', this._getClientConnectPatch());
            return module;
        }, (module, moduleVersion) => {
            const moduleExports = module[Symbol.toStringTag] === 'Module'
                ? module.default // ESM
                : module; // CommonJS
            this._diag.debug(`Removing patch for pg@${moduleVersion}`);
            if ((0, instrumentation_1.isWrapped)(moduleExports.Client.prototype.query)) {
                this._unwrap(moduleExports.Client.prototype, 'query');
            }
        });
        const modulePGPool = new instrumentation_1.InstrumentationNodeModuleDefinition('pg-pool', ['2.*', '3.*'], (moduleExports, moduleVersion) => {
            this._diag.debug(`Applying patch for pg-pool@${moduleVersion}`);
            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {
                this._unwrap(moduleExports.prototype, 'connect');
            }
            this._wrap(moduleExports.prototype, 'connect', this._getPoolConnectPatch());
            return moduleExports;
        }, (moduleExports, moduleVersion) => {
            this._diag.debug(`Removing patch for pg-pool@${moduleVersion}`);
            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {
                this._unwrap(moduleExports.prototype, 'connect');
            }
        });
        return [modulePG, modulePGPool];
    }
    setConfig(config = {}) {
        this._config = Object.assign({}, config);
    }
    getConfig() {
        return this._config;
    }
    _getClientConnectPatch() {
        const plugin = this;
        return (original) => {
            return function connect(callback) {
                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {
                    return original.call(this, callback);
                }
                const span = plugin.tracer.startSpan(`${PgInstrumentation.COMPONENT}.connect`, {
                    kind: api_1.SpanKind.CLIENT,
                    attributes: Object.assign({ [semantic_conventions_1.SemanticAttributes.DB_SYSTEM]: semantic_conventions_1.DbSystemValues.POSTGRESQL }, utils.getSemanticAttributesFromConnection(this)),
                });
                if (callback) {
                    const parentSpan = api_1.trace.getSpan(api_1.context.active());
                    callback = utils.patchClientConnectCallback(span, callback);
                    if (parentSpan) {
                        callback = api_1.context.bind(api_1.context.active(), callback);
                    }
                }
                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {
                    return original.call(this, callback);
                });
                return handleConnectResult(span, connectResult);
            };
        };
    }
    _getClientQueryPatch() {
        const plugin = this;
        return (original) => {
            this._diag.debug(`Patching ${PgInstrumentation.COMPONENT}.Client.prototype.query`);
            return function query(...args) {
                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {
                    return original.apply(this, args);
                }
                // client.query(text, cb?), client.query(text, values, cb?), and
                // client.query(configObj, cb?) are all valid signatures. We construct
                // a queryConfig obj from all (valid) signatures to build the span in a
                // unified way. We verify that we at least have query text, and code
                // defensively when dealing with `queryConfig` after that (to handle all
                // the other invalid cases, like a non-array for values being provided).
                // The type casts here reflect only what we've actually validated.
                const arg0 = args[0];
                const firstArgIsString = typeof arg0 === 'string';
                const firstArgIsQueryObjectWithText = utils.isObjectWithTextString(arg0);
                // TODO: remove the `as ...` casts below when the TS version is upgraded.
                // Newer TS versions will use the result of firstArgIsQueryObjectWithText
                // to properly narrow arg0, but TS 4.3.5 does not.
                const queryConfig = firstArgIsString
                    ? {
                        text: arg0,
                        values: Array.isArray(args[1]) ? args[1] : undefined,
                    }
                    : firstArgIsQueryObjectWithText
                        ? arg0
                        : undefined;
                const instrumentationConfig = plugin.getConfig();
                const span = utils.handleConfigQuery.call(this, plugin.tracer, instrumentationConfig, queryConfig);
                // Modify query text w/ a tracing comment before invoking original for
                // tracing, but only if args[0] has one of our expected shapes.
                if (instrumentationConfig.addSqlCommenterCommentToQueries) {
                    args[0] = firstArgIsString
                        ? (0, sql_common_1.addSqlCommenterComment)(span, arg0)
                        : firstArgIsQueryObjectWithText
                            ? Object.assign(Object.assign({}, arg0), { text: (0, sql_common_1.addSqlCommenterComment)(span, arg0.text) }) : args[0];
                }
                // Bind callback (if any) to parent span (if any)
                if (args.length > 0) {
                    const parentSpan = api_1.trace.getSpan(api_1.context.active());
                    if (typeof args[args.length - 1] === 'function') {
                        // Patch ParameterQuery callback
                        args[args.length - 1] = utils.patchCallback(instrumentationConfig, span, args[args.length - 1] // nb: not type safe.
                        );
                        // If a parent span exists, bind the callback
                        if (parentSpan) {
                            args[args.length - 1] = api_1.context.bind(api_1.context.active(), args[args.length - 1]);
                        }
                    }
                    else if (typeof (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.callback) === 'function') {
                        // Patch ConfigQuery callback
                        let callback = utils.patchCallback(plugin.getConfig(), span, queryConfig.callback // nb: not type safe.
                        );
                        // If a parent span existed, bind the callback
                        if (parentSpan) {
                            callback = api_1.context.bind(api_1.context.active(), callback);
                        }
                        args[0].callback = callback;
                    }
                }
                if (typeof instrumentationConfig.requestHook === 'function' &&
                    queryConfig) {
                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => {
                        // pick keys to expose explicitly, so we're not leaking pg package
                        // internals that are subject to change
                        const { database, host, port, user } = this.connectionParameters;
                        const connection = { database, host, port, user };
                        instrumentationConfig.requestHook(span, {
                            connection,
                            query: {
                                text: queryConfig.text,
                                // nb: if `client.query` is called with illegal arguments
                                // (e.g., if `queryConfig.values` is passed explicitly, but a
                                // non-array is given), then the type casts will be wrong. But
                                // we leave it up to the queryHook to handle that, and we
                                // catch and swallow any errors it throws. The other options
                                // are all worse. E.g., we could leave `queryConfig.values`
                                // and `queryConfig.name` as `unknown`, but then the hook body
                                // would be forced to validate (or cast) them before using
                                // them, which seems incredibly cumbersome given that these
                                // casts will be correct 99.9% of the time -- and pg.query
                                // will immediately throw during development in the other .1%
                                // of cases. Alternatively, we could simply skip calling the
                                // hook when `values` or `name` don't have the expected type,
                                // but that would add unnecessary validation overhead to every
                                // hook invocation and possibly be even more confusing/unexpected.
                                values: queryConfig.values,
                                name: queryConfig.name,
                            },
                        });
                    }, err => {
                        if (err) {
                            plugin._diag.error('Error running query hook', err);
                        }
                    }, true);
                }
                let result;
                try {
                    result = original.apply(this, args);
                }
                catch (e) {
                    // span.recordException(e);
                    span.setStatus({
                        code: api_1.SpanStatusCode.ERROR,
                        message: utils.getErrorMessage(e),
                    });
                    span.end();
                    throw e;
                }
                // Bind promise to parent span and end the span
                if (result instanceof Promise) {
                    return result
                        .then((result) => {
                        // Return a pass-along promise which ends the span and then goes to user's orig resolvers
                        return new Promise(resolve => {
                            utils.handleExecutionResult(plugin.getConfig(), span, result);
                            span.end();
                            resolve(result);
                        });
                    })
                        .catch((error) => {
                        return new Promise((_, reject) => {
                            span.setStatus({
                                code: api_1.SpanStatusCode.ERROR,
                                message: error.message,
                            });
                            span.end();
                            reject(error);
                        });
                    });
                }
                // else returns void
                return result; // void
            };
        };
    }
    _getPoolConnectPatch() {
        const plugin = this;
        return (originalConnect) => {
            return function connect(callback) {
                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {
                    return originalConnect.call(this, callback);
                }
                // setup span
                const span = plugin.tracer.startSpan(`${PG_POOL_COMPONENT}.connect`, {
                    kind: api_1.SpanKind.CLIENT,
                    attributes: Object.assign(Object.assign({ [semantic_conventions_1.SemanticAttributes.DB_SYSTEM]: semantic_conventions_1.DbSystemValues.POSTGRESQL }, utils.getSemanticAttributesFromConnection(this.options)), { [AttributeNames_1.AttributeNames.IDLE_TIMEOUT_MILLIS]: this.options.idleTimeoutMillis, [AttributeNames_1.AttributeNames.MAX_CLIENT]: this.options.maxClient }),
                });
                if (callback) {
                    const parentSpan = api_1.trace.getSpan(api_1.context.active());
                    callback = utils.patchCallbackPGPool(span, callback);
                    // If a parent span exists, bind the callback
                    if (parentSpan) {
                        callback = api_1.context.bind(api_1.context.active(), callback);
                    }
                }
                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {
                    return originalConnect.call(this, callback);
                });
                return handleConnectResult(span, connectResult);
            };
        };
    }
}
exports.PgInstrumentation = PgInstrumentation;
PgInstrumentation.COMPONENT = 'pg';
PgInstrumentation.BASE_SPAN_NAME = PgInstrumentation.COMPONENT + '.query';
function handleConnectResult(span, connectResult) {
    if (!(connectResult instanceof Promise)) {
        return connectResult;
    }
    const connectResultPromise = connectResult;
    return api_1.context.bind(api_1.context.active(), connectResultPromise
        .then(result => {
        span.end();
        return result;
    })
        .catch((error) => {
        span.setStatus({
            code: api_1.SpanStatusCode.ERROR,
            message: utils.getErrorMessage(error),
        });
        span.end();
        return Promise.reject(error);
    }));
}
//# sourceMappingURL=instrumentation.js.map