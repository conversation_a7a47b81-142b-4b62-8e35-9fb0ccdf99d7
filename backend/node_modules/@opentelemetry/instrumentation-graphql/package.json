{"name": "@opentelemetry/instrumentation-graphql", "version": "0.36.1", "description": "OpenTelemetry @opentelemetry/instrumentation-graphql automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-graphql --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test-all-versions": "tav", "tdd": "npm run test -- --watch-extensions ts --watch", "version:update": "node ../../../scripts/version-update.js", "watch": "tsc -w"}, "keywords": ["graphql", "metrics", "nodejs", "opentelemetry", "stats", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/semantic-conventions": "^1.3.1", "@types/mocha": "8.2.3", "@types/node": "18.6.5", "graphql": "^16.5.0", "mocha": "7.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.46.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-graphql#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}