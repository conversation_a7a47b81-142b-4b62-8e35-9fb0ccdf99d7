export declare enum AttributeNames {
    SOURCE = "graphql.source",
    FIELD_NAME = "graphql.field.name",
    FIELD_PATH = "graphql.field.path",
    FIELD_TYPE = "graphql.field.type",
    OPERATION_TYPE = "graphql.operation.type",
    OPERATION_NAME = "graphql.operation.name",
    VARIABLES = "graphql.variables.",
    ERROR_VALIDATION_NAME = "graphql.validation.error"
}
//# sourceMappingURL=AttributeNames.d.ts.map