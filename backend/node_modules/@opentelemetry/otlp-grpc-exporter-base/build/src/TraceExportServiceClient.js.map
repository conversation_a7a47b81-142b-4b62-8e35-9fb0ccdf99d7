{"version": 3, "file": "TraceExportServiceClient.js", "sourceRoot": "", "sources": ["../../src/TraceExportServiceClient.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,yCAAyC;AACzC,sCAAsC;AAOtC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;KAC7D,0BAAqE,CAAC;AAEzE,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;KAC5D,yBAAmE,CAAC;AAEvE,MAAM,sBAAsB,GAAG;IAC7B,MAAM,EAAE;QACN,IAAI,EAAE,6DAA6D;QACnE,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,KAAK;QACrB,gBAAgB,EAAE,CAAC,GAA+B,EAAE,EAAE;YACpD,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QACvD,CAAC;QACD,kBAAkB,EAAE,CAAC,GAAW,EAAE,EAAE;YAClC,OAAO,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC;QACD,iBAAiB,EAAE,CAAC,GAAgC,EAAE,EAAE;YACtD,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,CAAC;QACD,mBAAmB,EAAE,CAAC,GAAW,EAAE,EAAE;YACnC,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC;KACF;CACF,CAAC;AAEF,4EAA4E;AAC/D,QAAA,wBAAwB,GACnC,IAAI,CAAC,4BAA4B,CAC/B,sBAAsB,EACtB,oBAAoB,CACrB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as root from './generated/root';\nimport * as grpc from '@grpc/grpc-js';\nimport {\n  IExportTraceServiceRequest,\n  IExportTraceServiceResponse,\n} from '@opentelemetry/otlp-transformer';\nimport { ExportType } from './internal-types';\n\nconst responseType = root.opentelemetry.proto.collector.trace.v1\n  .ExportTraceServiceResponse as ExportType<IExportTraceServiceResponse>;\n\nconst requestType = root.opentelemetry.proto.collector.trace.v1\n  .ExportTraceServiceRequest as ExportType<IExportTraceServiceRequest>;\n\nconst traceServiceDefinition = {\n  export: {\n    path: '/opentelemetry.proto.collector.trace.v1.TraceService/Export',\n    requestStream: false,\n    responseStream: false,\n    requestSerialize: (arg: IExportTraceServiceRequest) => {\n      return Buffer.from(requestType.encode(arg).finish());\n    },\n    requestDeserialize: (arg: Buffer) => {\n      return requestType.decode(arg);\n    },\n    responseSerialize: (arg: IExportTraceServiceResponse) => {\n      return Buffer.from(responseType.encode(arg).finish());\n    },\n    responseDeserialize: (arg: Buffer) => {\n      return responseType.decode(arg);\n    },\n  },\n};\n\n// Creates a new instance of a gRPC service client for exporting OTLP traces\nexport const TraceExportServiceClient: grpc.ServiceClientConstructor =\n  grpc.makeGenericClientConstructor(\n    traceServiceDefinition,\n    'TraceExportService'\n  );\n"]}