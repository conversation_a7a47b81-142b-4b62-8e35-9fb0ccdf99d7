{"version": 3, "file": "OTLPGRPCExporterNodeBase.js", "sourceRoot": "", "sources": ["../../src/OTLPGRPCExporterNodeBase.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAC1C,2CAAyC;AAOzC,8CAA2D;AAC3D,iCAAwE;AACxE,0EAG2C;AAE3C;;GAEG;AACH,MAAsB,wBAGpB,SAAQ,qCAIT;IAOC,YAAY,SAAqC,EAAE;QACjD,KAAK,CAAC,MAAM,CAAC,CAAC;QAPhB,cAAS,GAAgC,EAAE,CAAC;QAE5C,kBAAa,GAAmB,SAAS,CAAC;QAMxC,IAAI,MAAM,CAAC,OAAO,EAAE;YAClB,UAAI,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;SACpD;QACD,MAAM,OAAO,GAAG,mBAAY,CAAC,uBAAuB,CAClD,IAAA,aAAM,GAAE,CAAC,0BAA0B,CACpC,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAI,kBAAQ,EAAE,CAAC;QAClD,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC5C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACzB;QACD,IAAI,CAAC,WAAW,GAAG,IAAA,2BAAoB,EAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAC9D,CAAC;IAEO,YAAY,CAClB,OAAqB,EACrB,SAAqB,EACrB,OAA2C;QAE3C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACpD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,MAAkC;QACvC,mEAAmE;QACnE,gDAAgD;QAChD,YAAY,CAAC,GAAG,EAAE;YAChB,8DAA8D;YAC9D,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,CACF,OAAqB,EACrB,SAAqB,EACrB,OAA2C;QAE3C,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,UAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,mEAAmE;YACnE,gDAAgD;YAChD,YAAY,CAAC,GAAG,EAAE;gBAChB,8DAA8D;gBAC9D,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBAElB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SAChD;IACH,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;SAC5B;IACH,CAAC;CAKF;AAzFD,4DAyFC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { Metadata } from '@grpc/grpc-js';\nimport {\n  OTLPGRPCExporterConfigNode,\n  GRPCQueueItem,\n  ServiceClientType,\n} from './types';\nimport { ServiceClient } from './types';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport { configureCompression, GrpcCompressionAlgorithm } from './util';\nimport {\n  OTLPExporterBase,\n  OTLPExporterError,\n} from '@opentelemetry/otlp-exporter-base';\n\n/**\n * OTLP Exporter abstract base class\n */\nexport abstract class OTLPGRPCExporterNodeBase<\n  ExportItem,\n  ServiceRequest,\n> extends OTLPExporterBase<\n  OTLPGRPCExporterConfigNode,\n  ExportItem,\n  ServiceRequest\n> {\n  grpcQueue: GRPCQueueItem<ExportItem>[] = [];\n  metadata?: Metadata;\n  serviceClient?: ServiceClient = undefined;\n  private _send!: Function;\n  compression: GrpcCompressionAlgorithm;\n\n  constructor(config: OTLPGRPCExporterConfigNode = {}) {\n    super(config);\n    if (config.headers) {\n      diag.warn('Headers cannot be set when using grpc');\n    }\n    const headers = baggageUtils.parseKeyPairsIntoRecord(\n      getEnv().OTEL_EXPORTER_OTLP_HEADERS\n    );\n    this.metadata = config.metadata || new Metadata();\n    for (const [k, v] of Object.entries(headers)) {\n      this.metadata.set(k, v);\n    }\n    this.compression = configureCompression(config.compression);\n  }\n\n  private _sendPromise(\n    objects: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: OTLPExporterError) => void\n  ): void {\n    const promise = new Promise<void>((resolve, reject) => {\n      this._send(this, objects, resolve, reject);\n    }).then(onSuccess, onError);\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n\n  onInit(config: OTLPGRPCExporterConfigNode): void {\n    // defer to next tick and lazy load to avoid loading grpc too early\n    // and making this impossible to be instrumented\n    setImmediate(() => {\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      const { onInit } = require('./util');\n      onInit(this, config);\n    });\n  }\n\n  send(\n    objects: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: OTLPExporterError) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      diag.debug('Shutdown already started. Cannot send objects');\n      return;\n    }\n    if (!this._send) {\n      // defer to next tick and lazy load to avoid loading grpc too early\n      // and making this impossible to be instrumented\n      setImmediate(() => {\n        // eslint-disable-next-line @typescript-eslint/no-var-requires\n        const { send } = require('./util');\n        this._send = send;\n\n        this._sendPromise(objects, onSuccess, onError);\n      });\n    } else {\n      this._sendPromise(objects, onSuccess, onError);\n    }\n  }\n\n  onShutdown(): void {\n    if (this.serviceClient) {\n      this.serviceClient.close();\n    }\n  }\n\n  abstract getServiceProtoPath(): string;\n  abstract getServiceClientType(): ServiceClientType;\n  abstract getUrlFromConfig(config: OTLPGRPCExporterConfigNode): string;\n}\n"]}