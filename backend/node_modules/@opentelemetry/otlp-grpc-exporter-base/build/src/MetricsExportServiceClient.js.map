{"version": 3, "file": "MetricsExportServiceClient.js", "sourceRoot": "", "sources": ["../../src/MetricsExportServiceClient.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,yCAAyC;AACzC,sCAAsC;AAOtC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;KAC/D,4BAAyE,CAAC;AAE7E,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;KAC9D,2BAAuE,CAAC;AAE3E,MAAM,wBAAwB,GAAG;IAC/B,MAAM,EAAE;QACN,IAAI,EAAE,iEAAiE;QACvE,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,KAAK;QACrB,gBAAgB,EAAE,CAAC,GAAiC,EAAE,EAAE;YACtD,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QACvD,CAAC;QACD,kBAAkB,EAAE,CAAC,GAAW,EAAE,EAAE;YAClC,OAAO,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC;QACD,iBAAiB,EAAE,CAAC,GAAkC,EAAE,EAAE;YACxD,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,CAAC;QACD,mBAAmB,EAAE,CAAC,GAAW,EAAE,EAAE;YACnC,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC;KACF;CACF,CAAC;AAEF,mEAAmE;AACtD,QAAA,yBAAyB,GACpC,IAAI,CAAC,4BAA4B,CAC/B,wBAAwB,EACxB,sBAAsB,CACvB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as root from './generated/root';\nimport * as grpc from '@grpc/grpc-js';\nimport {\n  IExportMetricsServiceRequest,\n  IExportMetricsServiceResponse,\n} from '@opentelemetry/otlp-transformer';\nimport { ExportType } from './internal-types';\n\nconst responseType = root.opentelemetry.proto.collector.metrics.v1\n  .ExportMetricsServiceResponse as ExportType<IExportMetricsServiceResponse>;\n\nconst requestType = root.opentelemetry.proto.collector.metrics.v1\n  .ExportMetricsServiceRequest as ExportType<IExportMetricsServiceRequest>;\n\nconst metricsServiceDefinition = {\n  export: {\n    path: '/opentelemetry.proto.collector.metrics.v1.MetricsService/Export',\n    requestStream: false,\n    responseStream: false,\n    requestSerialize: (arg: IExportMetricsServiceRequest) => {\n      return Buffer.from(requestType.encode(arg).finish());\n    },\n    requestDeserialize: (arg: Buffer) => {\n      return requestType.decode(arg);\n    },\n    responseSerialize: (arg: IExportMetricsServiceResponse) => {\n      return Buffer.from(responseType.encode(arg).finish());\n    },\n    responseDeserialize: (arg: Buffer) => {\n      return responseType.decode(arg);\n    },\n  },\n};\n\n// Creates a new instance of a gRPC service client for OTLP metrics\nexport const MetricExportServiceClient: grpc.ServiceClientConstructor =\n  grpc.makeGenericClientConstructor(\n    metricsServiceDefinition,\n    'MetricsExportService'\n  );\n"]}