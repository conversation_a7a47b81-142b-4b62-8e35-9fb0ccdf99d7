{"name": "@opentelemetry/otlp-grpc-exporter-base", "version": "0.45.1", "description": "OpenTelemetry OTLP-gRPC Exporter base (for internal use only)", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "compile": "npm run protos && tsc --build", "clean": "tsc --build --clean", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "protos": "npm run submodule && npm run protos:generate", "protos:generate": "node ../../../scripts/generate-protos.js", "submodule": "git submodule sync --recursive && git submodule update --init --recursive", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "version": "node ../../../scripts/version-update.js", "watch": "npm run protos && tsc -w", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile"}, "keywords": ["opentelemetry", "nodejs", "grpc", "tracing", "profiling", "metrics", "stats"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@babel/core": "7.22.20", "@opentelemetry/api": "1.7.0", "@opentelemetry/otlp-transformer": "0.45.1", "@opentelemetry/resources": "1.18.1", "@opentelemetry/sdk-trace-base": "1.18.1", "@types/mocha": "10.0.3", "@types/node": "18.6.5", "@types/sinon": "10.0.20", "codecov": "3.8.3", "cpx": "1.5.0", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "protobufjs-cli": "1.1.2", "sinon": "15.1.2", "ts-loader": "8.4.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}, "dependencies": {"@grpc/grpc-js": "^1.7.1", "@opentelemetry/core": "1.18.1", "@opentelemetry/otlp-exporter-base": "0.45.1", "protobufjs": "^7.2.3"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/otlp-grpc-exporter-base", "sideEffects": false, "gitHead": "f665499096189390e691cf1a772e677fa67812d7"}