{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/common/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Exception } from '@opentelemetry/api';\n\n/**\n * This interface defines a fallback to read a timeOrigin when it is not available on performance.timeOrigin,\n * this happens for example on Safari Mac\n * then the timeOrigin is taken from fetchStart - which is the closest to timeOrigin\n */\nexport interface TimeOriginLegacy {\n  timing: {\n    fetchStart: number;\n  };\n}\n\n/**\n * This interface defines the params that are be added to the wrapped function\n * using the \"shimmer.wrap\"\n */\nexport interface ShimWrapped extends Function {\n  __wrapped: boolean;\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  __unwrap: Function;\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  __original: Function;\n}\n\n/**\n * An instrumentation library consists of the name and optional version\n * used to obtain a tracer or meter from a provider. This metadata is made\n * available on ReadableSpan and MetricRecord for use by the export pipeline.\n * @deprecated Use {@link InstrumentationScope} instead.\n */\nexport interface InstrumentationLibrary {\n  readonly name: string;\n  readonly version?: string;\n  readonly schemaUrl?: string;\n}\n\n/**\n * An instrumentation scope consists of the name and optional version\n * used to obtain a tracer or meter from a provider. This metadata is made\n * available on ReadableSpan and MetricRecord for use by the export pipeline.\n */\nexport interface InstrumentationScope {\n  readonly name: string;\n  readonly version?: string;\n  readonly schemaUrl?: string;\n}\n\n/** Defines an error handler function */\nexport type ErrorHandler = (ex: Exception) => void;\n"]}