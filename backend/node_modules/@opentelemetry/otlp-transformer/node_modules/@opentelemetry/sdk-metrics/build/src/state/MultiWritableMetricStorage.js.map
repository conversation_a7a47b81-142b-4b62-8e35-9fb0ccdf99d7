{"version": 3, "file": "MultiWritableMetricStorage.js", "sourceRoot": "", "sources": ["../../../src/state/MultiWritableMetricStorage.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH;;GAEG;AACH,MAAa,kBAAkB;IAC7B,YAA6B,gBAAyC;QAAzC,qBAAgB,GAAhB,gBAAgB,CAAyB;IAAG,CAAC;IAE1E,MAAM,CACJ,KAAa,EACb,UAA4B,EAC5B,OAAgB,EAChB,UAAkB;QAElB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACjC,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAbD,gDAaC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { WritableMetricStorage } from './WritableMetricStorage';\n\n/**\n * Internal interface.\n */\nexport class MultiMetricStorage implements WritableMetricStorage {\n  constructor(private readonly _backingStorages: WritableMetricStorage[]) {}\n\n  record(\n    value: number,\n    attributes: MetricAttributes,\n    context: Context,\n    recordTime: HrTime\n  ) {\n    this._backingStorages.forEach(it => {\n      it.record(value, attributes, context, recordTime);\n    });\n  }\n}\n"]}