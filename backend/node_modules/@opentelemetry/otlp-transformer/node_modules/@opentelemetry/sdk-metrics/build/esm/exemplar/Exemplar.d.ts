import { HrTime, MetricAttributes } from '@opentelemetry/api';
/**
 * A representation of an exemplar, which is a sample input measurement.
 * Exemplars also hold information about the environment when the measurement
 * was recorded, for example the span and trace ID of the active span when the
 * exemplar was recorded.
 */
export declare type Exemplar = {
    filteredAttributes: MetricAttributes;
    value: number;
    timestamp: HrTime;
    spanId?: string;
    traceId?: string;
};
//# sourceMappingURL=Exemplar.d.ts.map