{"version": 3, "file": "Predicate.js", "sourceRoot": "", "sources": ["../../../src/view/Predicate.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAC1C,oCAAoC;AACpC,yDAAyD;AACzD,MAAM,MAAM,GAAG,qBAAqB,CAAC;AAMrC;;GAEG;AACH,MAAa,gBAAgB;IAI3B,YAAY,OAAe;QACzB,IAAI,OAAO,KAAK,GAAG,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;SACpE;IACH,CAAC;IAED,KAAK,CAAC,GAAW;QACf,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC;SACb;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAAe;QAClC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC;IACnE,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAe;QAChC,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;CACF;AA7BD,4CA6BC;AAED,MAAa,cAAc;IAIzB,YAAY,OAAgB;QAC1B,IAAI,CAAC,SAAS,GAAG,OAAO,KAAK,SAAS,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,GAAW;QACf,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE;YACzB,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAlBD,wCAkBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// https://tc39.es/proposal-regex-escaping\n// escape ^ $ \\ .  + ? ( ) [ ] { } |\n// do not need to escape * as we interpret it as wildcard\nconst ESCAPE = /[\\^$\\\\.+?()[\\]{}|]/g;\n\nexport interface Predicate {\n  match(str: string): boolean;\n}\n\n/**\n * Wildcard pattern predicate, supports patterns like `*`, `foo*`, `*bar`.\n */\nexport class PatternPredicate implements Predicate {\n  private _matchAll: boolean;\n  private _regexp: RegExp;\n\n  constructor(pattern: string) {\n    if (pattern === '*') {\n      this._matchAll = true;\n      this._regexp = /.*/;\n    } else {\n      this._matchAll = false;\n      this._regexp = new RegExp(PatternPredicate.escapePattern(pattern));\n    }\n  }\n\n  match(str: string): boolean {\n    if (this._matchAll) {\n      return true;\n    }\n\n    return this._regexp.test(str);\n  }\n\n  static escapePattern(pattern: string): string {\n    return `^${pattern.replace(ESCAPE, '\\\\$&').replace('*', '.*')}$`;\n  }\n\n  static hasWildcard(pattern: string): boolean {\n    return pattern.includes('*');\n  }\n}\n\nexport class ExactPredicate implements Predicate {\n  private _matchAll: boolean;\n  private _pattern?: string;\n\n  constructor(pattern?: string) {\n    this._matchAll = pattern === undefined;\n    this._pattern = pattern;\n  }\n\n  match(str: string): boolean {\n    if (this._matchAll) {\n      return true;\n    }\n    if (str === this._pattern) {\n      return true;\n    }\n    return false;\n  }\n}\n"]}