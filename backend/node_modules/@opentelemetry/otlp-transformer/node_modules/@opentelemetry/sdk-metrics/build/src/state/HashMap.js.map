{"version": 3, "file": "HashMap.js", "sourceRoot": "", "sources": ["../../../src/state/HashMap.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,oCAA0C;AAM1C,MAAa,OAAO;IAIlB,YAAoB,KAAkC;QAAlC,UAAK,GAAL,KAAK,CAA6B;QAH9C,cAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;QAC/C,YAAO,GAAG,IAAI,GAAG,EAAyB,CAAC;IAEM,CAAC;IAE1D,GAAG,CAAC,GAAY,EAAE,QAAuB;QACvC,QAAQ,aAAR,QAAQ,cAAR,QAAQ,IAAR,QAAQ,GAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAC;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,YAAY,CAAC,GAAY,EAAE,cAA+B;QACxD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACjC;QACD,MAAM,GAAG,GAAG,cAAc,EAAE,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,GAAG,CAAC,GAAY,EAAE,KAAgB,EAAE,QAAuB;QACzD,QAAQ,aAAR,QAAQ,cAAR,QAAQ,IAAR,QAAQ,GAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED,GAAG,CAAC,GAAY,EAAE,QAAuB;QACvC,QAAQ,aAAR,QAAQ,cAAR,QAAQ,IAAR,QAAQ,GAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAC;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,CAAC,IAAI;QACH,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3C,IAAI,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YACzB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;SAC3B;IACH,CAAC;IAED,CAAC,OAAO;QACN,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC/C,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YACzB,0CAA0C;YAC1C,oEAAoE;YACpE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;SAC7B;IACH,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC7B,CAAC;CACF;AA5DD,0BA4DC;AAED,MAAa,gBAA4B,SAAQ,OAIhD;IACC;QACE,KAAK,CAAC,sBAAc,CAAC,CAAC;IACxB,CAAC;CACF;AARD,4CAQC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricAttributes } from '@opentelemetry/api';\nimport { hashAttributes } from '../utils';\n\nexport interface Hash<ValueType, HashCodeType> {\n  (value: ValueType): HashCodeType;\n}\n\nexport class HashMap<KeyType, ValueType, HashCodeType> {\n  private _valueMap = new Map<HashCodeType, ValueType>();\n  private _keyMap = new Map<HashCodeType, KeyType>();\n\n  constructor(private _hash: Hash<KeyType, HashCodeType>) {}\n\n  get(key: KeyType, hashCode?: HashCodeType) {\n    hashCode ??= this._hash(key);\n    return this._valueMap.get(hashCode);\n  }\n\n  getOrDefault(key: KeyType, defaultFactory: () => ValueType) {\n    const hash = this._hash(key);\n    if (this._valueMap.has(hash)) {\n      return this._valueMap.get(hash);\n    }\n    const val = defaultFactory();\n    if (!this._keyMap.has(hash)) {\n      this._keyMap.set(hash, key);\n    }\n    this._valueMap.set(hash, val);\n    return val;\n  }\n\n  set(key: KeyType, value: ValueType, hashCode?: HashCodeType) {\n    hashCode ??= this._hash(key);\n    if (!this._keyMap.has(hashCode)) {\n      this._keyMap.set(hashCode, key);\n    }\n    this._valueMap.set(hashCode, value);\n  }\n\n  has(key: KeyType, hashCode?: HashCodeType) {\n    hashCode ??= this._hash(key);\n    return this._valueMap.has(hashCode);\n  }\n\n  *keys(): IterableIterator<[KeyType, HashCodeType]> {\n    const keyIterator = this._keyMap.entries();\n    let next = keyIterator.next();\n    while (next.done !== true) {\n      yield [next.value[1], next.value[0]];\n      next = keyIterator.next();\n    }\n  }\n\n  *entries(): IterableIterator<[KeyType, ValueType, HashCodeType]> {\n    const valueIterator = this._valueMap.entries();\n    let next = valueIterator.next();\n    while (next.done !== true) {\n      // next.value[0] here can not be undefined\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      yield [this._keyMap.get(next.value[0])!, next.value[1], next.value[0]];\n      next = valueIterator.next();\n    }\n  }\n\n  get size() {\n    return this._valueMap.size;\n  }\n}\n\nexport class AttributeHashMap<ValueType> extends HashMap<\n  MetricAttributes,\n  ValueType,\n  string\n> {\n  constructor() {\n    super(hashAttributes);\n  }\n}\n"]}