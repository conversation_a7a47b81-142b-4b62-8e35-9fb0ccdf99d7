{"version": 3, "file": "Aggregation.js", "sourceRoot": "", "sources": ["../../../src/view/Aggregation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;AAEH,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAEL,aAAa,EACb,cAAc,EACd,mBAAmB,EACnB,mBAAmB,EACnB,8BAA8B,GAC/B,MAAM,eAAe,CAAC;AAEvB,OAAO,EAAwB,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAG/E;;;;GAIG;AACH;IAAA;IA4BA,CAAC;IAvBQ,gBAAI,GAAX;QACE,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEM,eAAG,GAAV;QACE,OAAO,eAAe,CAAC;IACzB,CAAC;IAEM,qBAAS,GAAhB;QACE,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAEM,qBAAS,GAAhB;QACE,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAEM,gCAAoB,GAA3B;QACE,OAAO,iCAAiC,CAAC;IAC3C,CAAC;IAEM,mBAAO,GAAd;QACE,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IACH,kBAAC;AAAD,CAAC,AA5BD,IA4BC;;AAED;;GAEG;AACH;IAAqC,mCAAW;IAAhD;;IAKA,CAAC;IAHC,0CAAgB,GAAhB,UAAiB,WAAiC;QAChD,OAAO,eAAe,CAAC,gBAAgB,CAAC;IAC1C,CAAC;IAHc,gCAAgB,GAAG,IAAI,cAAc,EAAE,CAAC;IAIzD,sBAAC;CAAA,AALD,CAAqC,WAAW,GAK/C;SALY,eAAe;AAO5B;;GAEG;AACH;IAAoC,kCAAW;IAA/C;;IAeA,CAAC;IAZC,yCAAgB,GAAhB,UAAiB,UAAgC;QAC/C,QAAQ,UAAU,CAAC,IAAI,EAAE;YACvB,KAAK,cAAc,CAAC,OAAO,CAAC;YAC5B,KAAK,cAAc,CAAC,kBAAkB,CAAC;YACvC,KAAK,cAAc,CAAC,SAAS,CAAC,CAAC;gBAC7B,OAAO,cAAc,CAAC,kBAAkB,CAAC;aAC1C;YACD,OAAO,CAAC,CAAC;gBACP,OAAO,cAAc,CAAC,sBAAsB,CAAC;aAC9C;SACF;IACH,CAAC;IAbc,iCAAkB,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;IAC7C,qCAAsB,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;IAanE,qBAAC;CAAA,AAfD,CAAoC,WAAW,GAe9C;SAfY,cAAc;AAiB3B;;GAEG;AACH;IAA0C,wCAAW;IAArD;;IAKA,CAAC;IAHC,+CAAgB,GAAhB,UAAiB,WAAiC;QAChD,OAAO,oBAAoB,CAAC,gBAAgB,CAAC;IAC/C,CAAC;IAHc,qCAAgB,GAAG,IAAI,mBAAmB,EAAE,CAAC;IAI9D,2BAAC;CAAA,AALD,CAA0C,WAAW,GAKpD;SALY,oBAAoB;AAOjC;;GAEG;AACH;IAA0C,wCAAW;IAArD;;IAQA,CAAC;IAHC,+CAAgB,GAAhB,UAAiB,WAAiC;QAChD,OAAO,oBAAoB,CAAC,gBAAgB,CAAC;IAC/C,CAAC;IANc,qCAAgB,GAAG,IAAI,mBAAmB,CACvD,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,EACzE,IAAI,CACL,CAAC;IAIJ,2BAAC;CAAA,AARD,CAA0C,WAAW,GAQpD;SARY,oBAAoB;AAUjC;;GAEG;AACH;IAAwD,sDAAW;IAGjE;;;OAGG;IACH,4CACE,UAAoB,EACH,aAAoB;QAApB,8BAAA,EAAA,oBAAoB;QAFvC,YAIE,iBAAO,SAgBR;QAlBkB,mBAAa,GAAb,aAAa,CAAO;QAGrC,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACvD,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;SAC3E;QACD,8CAA8C;QAC9C,UAAU,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QACjC,2EAA2E;QAC3E,iDAAiD;QACjD,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,CAAC,CAAC;QAC9C,2CAA2C;QAC3C,IAAM,kBAAkB,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC7D,IAAI,aAAa,GAAuB,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrE,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;YACxB,aAAa,GAAG,SAAS,CAAC;SAC3B;QACD,KAAI,CAAC,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,EAAE,aAAa,CAAC,CAAC;;IAC7E,CAAC;IAED,6DAAgB,GAAhB,UAAiB,WAAiC;QAChD,OAAO,IAAI,mBAAmB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACvE,CAAC;IACH,yCAAC;AAAD,CAAC,AAhCD,CAAwD,WAAW,GAgClE;;AAED;IAAqD,mDAAW;IAC9D,yCACmB,QAAsB,EACtB,aAAoB;QADpB,yBAAA,EAAA,cAAsB;QACtB,8BAAA,EAAA,oBAAoB;QAFvC,YAIE,iBAAO,SACR;QAJkB,cAAQ,GAAR,QAAQ,CAAc;QACtB,mBAAa,GAAb,aAAa,CAAO;;IAGvC,CAAC;IACD,0DAAgB,GAAhB,UAAiB,WAAiC;QAChD,OAAO,IAAI,8BAA8B,CACvC,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IACH,sCAAC;AAAD,CAAC,AAbD,CAAqD,WAAW,GAa/D;;AAED;;GAEG;AACH;IAAwC,sCAAW;IAAnD;;IA+BA,CAAC;IA9BS,qCAAQ,GAAhB,UAAiB,UAAgC;QAC/C,uEAAuE;QACvE,QAAQ,UAAU,CAAC,IAAe,EAAE;YAClC,KAAK,cAAc,CAAC,OAAO,CAAC;YAC5B,KAAK,cAAc,CAAC,eAAe,CAAC;YACpC,KAAK,cAAc,CAAC,kBAAkB,CAAC;YACvC,KAAK,cAAc,CAAC,0BAA0B,CAAC,CAAC;gBAC9C,OAAO,eAAe,CAAC;aACxB;YACD,KAAK,cAAc,CAAC,gBAAgB,CAAC,CAAC;gBACpC,OAAO,sBAAsB,CAAC;aAC/B;YACD,KAAK,cAAc,CAAC,SAAS,CAAC,CAAC;gBAC7B,IAAI,UAAU,CAAC,MAAM,CAAC,wBAAwB,EAAE;oBAC9C,OAAO,IAAI,kCAAkC,CAC3C,UAAU,CAAC,MAAM,CAAC,wBAAwB,CAC3C,CAAC;iBACH;gBACD,OAAO,qBAAqB,CAAC;aAC9B;SACF;QACD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,0CAAwC,UAAU,CAAC,IAAM,CAAC,CAAC;QACzE,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,6CAAgB,GAAhB,UACE,UAAgC;QAEhC,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC;IACH,yBAAC;AAAD,CAAC,AA/BD,CAAwC,WAAW,GA+BlD;;AAED,IAAM,gBAAgB,GAAG,IAAI,eAAe,EAAE,CAAC;AAC/C,IAAM,eAAe,GAAG,IAAI,cAAc,EAAE,CAAC;AAC7C,IAAM,sBAAsB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAC1D,IAAM,qBAAqB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AACzD,IAAM,iCAAiC,GAAG,IAAI,+BAA+B,EAAE,CAAC;AAChF,IAAM,mBAAmB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  Aggregator,\n  SumAggregator,\n  DropAggregator,\n  LastValueAggregator,\n  HistogramAggregator,\n  ExponentialHistogramAggregator,\n} from '../aggregator';\nimport { Accumulation } from '../aggregator/types';\nimport { InstrumentDescriptor, InstrumentType } from '../InstrumentDescriptor';\nimport { Maybe } from '../utils';\n\n/**\n * Configures how measurements are combined into metrics for views.\n *\n * Aggregation provides a set of built-in aggregations via static methods.\n */\nexport abstract class Aggregation {\n  abstract createAggregator(\n    instrument: InstrumentDescriptor\n  ): Aggregator<Maybe<Accumulation>>;\n\n  static Drop(): Aggregation {\n    return DROP_AGGREGATION;\n  }\n\n  static Sum(): Aggregation {\n    return SUM_AGGREGATION;\n  }\n\n  static LastValue(): Aggregation {\n    return LAST_VALUE_AGGREGATION;\n  }\n\n  static Histogram(): Aggregation {\n    return HISTOGRAM_AGGREGATION;\n  }\n\n  static ExponentialHistogram(): Aggregation {\n    return EXPONENTIAL_HISTOGRAM_AGGREGATION;\n  }\n\n  static Default(): Aggregation {\n    return DEFAULT_AGGREGATION;\n  }\n}\n\n/**\n * The default drop aggregation.\n */\nexport class DropAggregation extends Aggregation {\n  private static DEFAULT_INSTANCE = new DropAggregator();\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return DropAggregation.DEFAULT_INSTANCE;\n  }\n}\n\n/**\n * The default sum aggregation.\n */\nexport class SumAggregation extends Aggregation {\n  private static MONOTONIC_INSTANCE = new SumAggregator(true);\n  private static NON_MONOTONIC_INSTANCE = new SumAggregator(false);\n  createAggregator(instrument: InstrumentDescriptor) {\n    switch (instrument.type) {\n      case InstrumentType.COUNTER:\n      case InstrumentType.OBSERVABLE_COUNTER:\n      case InstrumentType.HISTOGRAM: {\n        return SumAggregation.MONOTONIC_INSTANCE;\n      }\n      default: {\n        return SumAggregation.NON_MONOTONIC_INSTANCE;\n      }\n    }\n  }\n}\n\n/**\n * The default last value aggregation.\n */\nexport class LastValueAggregation extends Aggregation {\n  private static DEFAULT_INSTANCE = new LastValueAggregator();\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return LastValueAggregation.DEFAULT_INSTANCE;\n  }\n}\n\n/**\n * The default histogram aggregation.\n */\nexport class HistogramAggregation extends Aggregation {\n  private static DEFAULT_INSTANCE = new HistogramAggregator(\n    [0, 5, 10, 25, 50, 75, 100, 250, 500, 750, 1000, 2500, 5000, 7500, 10000],\n    true\n  );\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return HistogramAggregation.DEFAULT_INSTANCE;\n  }\n}\n\n/**\n * The explicit bucket histogram aggregation.\n */\nexport class ExplicitBucketHistogramAggregation extends Aggregation {\n  private _boundaries: number[];\n\n  /**\n   * @param boundaries the bucket boundaries of the histogram aggregation\n   * @param _recordMinMax If set to true, min and max will be recorded. Otherwise, min and max will not be recorded.\n   */\n  constructor(\n    boundaries: number[],\n    private readonly _recordMinMax = true\n  ) {\n    super();\n    if (boundaries === undefined || boundaries.length === 0) {\n      throw new Error('HistogramAggregator should be created with boundaries.');\n    }\n    // Copy the boundaries array for modification.\n    boundaries = boundaries.concat();\n    // We need to an ordered set to be able to correctly compute count for each\n    // boundary since we'll iterate on each in order.\n    boundaries = boundaries.sort((a, b) => a - b);\n    // Remove all Infinity from the boundaries.\n    const minusInfinityIndex = boundaries.lastIndexOf(-Infinity);\n    let infinityIndex: number | undefined = boundaries.indexOf(Infinity);\n    if (infinityIndex === -1) {\n      infinityIndex = undefined;\n    }\n    this._boundaries = boundaries.slice(minusInfinityIndex + 1, infinityIndex);\n  }\n\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return new HistogramAggregator(this._boundaries, this._recordMinMax);\n  }\n}\n\nexport class ExponentialHistogramAggregation extends Aggregation {\n  constructor(\n    private readonly _maxSize: number = 160,\n    private readonly _recordMinMax = true\n  ) {\n    super();\n  }\n  createAggregator(_instrument: InstrumentDescriptor) {\n    return new ExponentialHistogramAggregator(\n      this._maxSize,\n      this._recordMinMax\n    );\n  }\n}\n\n/**\n * The default aggregation.\n */\nexport class DefaultAggregation extends Aggregation {\n  private _resolve(instrument: InstrumentDescriptor): Aggregation {\n    // cast to unknown to disable complaints on the (unreachable) fallback.\n    switch (instrument.type as unknown) {\n      case InstrumentType.COUNTER:\n      case InstrumentType.UP_DOWN_COUNTER:\n      case InstrumentType.OBSERVABLE_COUNTER:\n      case InstrumentType.OBSERVABLE_UP_DOWN_COUNTER: {\n        return SUM_AGGREGATION;\n      }\n      case InstrumentType.OBSERVABLE_GAUGE: {\n        return LAST_VALUE_AGGREGATION;\n      }\n      case InstrumentType.HISTOGRAM: {\n        if (instrument.advice.explicitBucketBoundaries) {\n          return new ExplicitBucketHistogramAggregation(\n            instrument.advice.explicitBucketBoundaries\n          );\n        }\n        return HISTOGRAM_AGGREGATION;\n      }\n    }\n    api.diag.warn(`Unable to recognize instrument type: ${instrument.type}`);\n    return DROP_AGGREGATION;\n  }\n\n  createAggregator(\n    instrument: InstrumentDescriptor\n  ): Aggregator<Maybe<Accumulation>> {\n    return this._resolve(instrument).createAggregator(instrument);\n  }\n}\n\nconst DROP_AGGREGATION = new DropAggregation();\nconst SUM_AGGREGATION = new SumAggregation();\nconst LAST_VALUE_AGGREGATION = new LastValueAggregation();\nconst HISTOGRAM_AGGREGATION = new HistogramAggregation();\nconst EXPONENTIAL_HISTOGRAM_AGGREGATION = new ExponentialHistogramAggregation();\nconst DEFAULT_AGGREGATION = new DefaultAggregation();\n"]}