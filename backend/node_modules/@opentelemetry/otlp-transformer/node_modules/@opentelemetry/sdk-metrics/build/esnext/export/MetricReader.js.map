{"version": 3, "file": "MetricReader.js", "sourceRoot": "", "sources": ["../../../src/export/MetricReader.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAI1C,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAQpD,OAAO,EAGL,4BAA4B,EAC5B,wCAAwC,GACzC,MAAM,uBAAuB,CAAC;AAuB/B;;;GAGG;AACH,MAAM,OAAgB,YAAY;IAWhC,YAAY,OAA6B;;QAVzC,6BAA6B;QAC7B,yFAAyF;QACjF,cAAS,GAAG,KAAK,CAAC;QASxB,IAAI,CAAC,oBAAoB;YACvB,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,mBAAmB,mCAAI,4BAA4B,CAAC;QAC/D,IAAI,CAAC,+BAA+B;YAClC,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,8BAA8B,mCACvC,wCAAwC,CAAC;QAC3C,IAAI,CAAC,gBAAgB,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,eAAe,mCAAI,EAAE,CAAC;IACzD,CAAC;IAED;;;;;;;;;OASG;IACH,iBAAiB,CAAC,cAA8B;QAC9C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,MAAM,IAAI,KAAK,CACb,yDAAyD,CAC1D,CAAC;SACH;QACD,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;QACzC,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,iBAAiB,CAAC,cAA8B;QAC9C,OAAO,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG;IACH,4BAA4B,CAC1B,cAA8B;QAE9B,OAAO,IAAI,CAAC,+BAA+B,CAAC,cAAc,CAAC,CAAC;IAC9D,CAAC;IAED;;;OAGG;IACO,aAAa;QACrB,mCAAmC;IACrC,CAAC;IAmBD;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,OAA2B;QACvC,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SAClE;QAED,sGAAsG;QACtG,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC7C;QAED,MAAM,CAAC,oBAAoB,EAAE,GAAG,2BAA2B,CAAC,GAC1D,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC9B,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;aACtC,CAAC;YACF,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CACtC,QAAQ,CAAC,OAAO,CAAC;gBACf,aAAa,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa;aACtC,CAAC,CACH;SACF,CAAC,CAAC;QAEL,gDAAgD;QAChD,MAAM,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAC/C,OAAO,CAAC,2BAA2B,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAC9D,CAAC;QACF,MAAM,QAAQ,GAAG,oBAAoB,CAAC,eAAe,CAAC,QAAQ,CAAC;QAC/D,MAAM,YAAY,GAChB,oBAAoB,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,CACtD,OAAO,CACL,2BAA2B,EAC3B,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAC9C,CACF,CAAC;QACJ,OAAO;YACL,eAAe,EAAE;gBACf,QAAQ;gBACR,YAAY;aACb;YACD,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,QAAQ,CAAC,OAAyB;QACtC,4DAA4D;QAC5D,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAC9C,OAAO;SACR;QAED,oDAAoD;QACpD,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,KAAI,IAAI,EAAE;YAClC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;SACzB;aAAM;YACL,MAAM,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;SACjE;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,UAAU,CAAC,OAA2B;QAC1C,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACrE,OAAO;SACR;QAED,oDAAoD;QACpD,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,KAAI,IAAI,EAAE;YAClC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC1B,OAAO;SACR;QAED,MAAM,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;IACpE,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { MetricProducer } from './MetricProducer';\nimport { CollectionResult } from './MetricData';\nimport { FlatMap, callWithTimeout } from '../utils';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport {\n  CollectionOptions,\n  ForceFlushOptions,\n  ShutdownOptions,\n} from '../types';\nimport { Aggregation } from '../view/Aggregation';\nimport {\n  AggregationSelector,\n  AggregationTemporalitySelector,\n  DEFAULT_AGGREGATION_SELECTOR,\n  DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR,\n} from './AggregationSelector';\n\nexport interface MetricReaderOptions {\n  /**\n   * Aggregation selector based on metric instrument types. If no views are\n   * configured for a metric instrument, a per-metric-reader aggregation is\n   * selected with this selector.\n   */\n  aggregationSelector?: AggregationSelector;\n  /**\n   * Aggregation temporality selector based on metric instrument types. If\n   * not configured, cumulative is used for all instruments.\n   */\n  aggregationTemporalitySelector?: AggregationTemporalitySelector;\n  /**\n   * **Note, this option is experimental**. Additional MetricProducers to use as a source of\n   * aggregated metric data in addition to the SDK's metric data. The resource returned by\n   * these MetricProducers is ignored; the SDK's resource will be used instead.\n   * @experimental\n   */\n  metricProducers?: MetricProducer[];\n}\n\n/**\n * A registered reader of metrics that, when linked to a {@link MetricProducer}, offers global\n * control over metrics.\n */\nexport abstract class MetricReader {\n  // Tracks the shutdown state.\n  // TODO: use BindOncePromise here once a new version of @opentelemetry/core is available.\n  private _shutdown = false;\n  // Additional MetricProducers which will be combined with the SDK's output\n  private _metricProducers: MetricProducer[];\n  // MetricProducer used by this instance which produces metrics from the SDK\n  private _sdkMetricProducer?: MetricProducer;\n  private readonly _aggregationTemporalitySelector: AggregationTemporalitySelector;\n  private readonly _aggregationSelector: AggregationSelector;\n\n  constructor(options?: MetricReaderOptions) {\n    this._aggregationSelector =\n      options?.aggregationSelector ?? DEFAULT_AGGREGATION_SELECTOR;\n    this._aggregationTemporalitySelector =\n      options?.aggregationTemporalitySelector ??\n      DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR;\n    this._metricProducers = options?.metricProducers ?? [];\n  }\n\n  /**\n   * Set the {@link MetricProducer} used by this instance. **This should only be called by the\n   * SDK and should be considered internal.**\n   *\n   * To add additional {@link MetricProducer}s to a {@link MetricReader}, pass them to the\n   * constructor as {@link MetricReaderOptions.metricProducers}.\n   *\n   * @internal\n   * @param metricProducer\n   */\n  setMetricProducer(metricProducer: MetricProducer) {\n    if (this._sdkMetricProducer) {\n      throw new Error(\n        'MetricReader can not be bound to a MeterProvider again.'\n      );\n    }\n    this._sdkMetricProducer = metricProducer;\n    this.onInitialized();\n  }\n\n  /**\n   * Select the {@link Aggregation} for the given {@link InstrumentType} for this\n   * reader.\n   */\n  selectAggregation(instrumentType: InstrumentType): Aggregation {\n    return this._aggregationSelector(instrumentType);\n  }\n\n  /**\n   * Select the {@link AggregationTemporality} for the given\n   * {@link InstrumentType} for this reader.\n   */\n  selectAggregationTemporality(\n    instrumentType: InstrumentType\n  ): AggregationTemporality {\n    return this._aggregationTemporalitySelector(instrumentType);\n  }\n\n  /**\n   * Handle once the SDK has initialized this {@link MetricReader}\n   * Overriding this method is optional.\n   */\n  protected onInitialized(): void {\n    // Default implementation is empty.\n  }\n\n  /**\n   * Handle a shutdown signal by the SDK.\n   *\n   * <p> For push exporters, this should shut down any intervals and close any open connections.\n   * @protected\n   */\n  protected abstract onShutdown(): Promise<void>;\n\n  /**\n   * Handle a force flush signal by the SDK.\n   *\n   * <p> In all scenarios metrics should be collected via {@link collect()}.\n   * <p> For push exporters, this should collect and report metrics.\n   * @protected\n   */\n  protected abstract onForceFlush(): Promise<void>;\n\n  /**\n   * Collect all metrics from the associated {@link MetricProducer}\n   */\n  async collect(options?: CollectionOptions): Promise<CollectionResult> {\n    if (this._sdkMetricProducer === undefined) {\n      throw new Error('MetricReader is not bound to a MetricProducer');\n    }\n\n    // Subsequent invocations to collect are not allowed. SDKs SHOULD return some failure for these calls.\n    if (this._shutdown) {\n      throw new Error('MetricReader is shutdown');\n    }\n\n    const [sdkCollectionResults, ...additionalCollectionResults] =\n      await Promise.all([\n        this._sdkMetricProducer.collect({\n          timeoutMillis: options?.timeoutMillis,\n        }),\n        ...this._metricProducers.map(producer =>\n          producer.collect({\n            timeoutMillis: options?.timeoutMillis,\n          })\n        ),\n      ]);\n\n    // Merge the results, keeping the SDK's Resource\n    const errors = sdkCollectionResults.errors.concat(\n      FlatMap(additionalCollectionResults, result => result.errors)\n    );\n    const resource = sdkCollectionResults.resourceMetrics.resource;\n    const scopeMetrics =\n      sdkCollectionResults.resourceMetrics.scopeMetrics.concat(\n        FlatMap(\n          additionalCollectionResults,\n          result => result.resourceMetrics.scopeMetrics\n        )\n      );\n    return {\n      resourceMetrics: {\n        resource,\n        scopeMetrics,\n      },\n      errors,\n    };\n  }\n\n  /**\n   * Shuts down the metric reader, the promise will reject after the optional timeout or resolve after completion.\n   *\n   * <p> NOTE: this operation will continue even after the promise rejects due to a timeout.\n   * @param options options with timeout.\n   */\n  async shutdown(options?: ShutdownOptions): Promise<void> {\n    // Do not call shutdown again if it has already been called.\n    if (this._shutdown) {\n      api.diag.error('Cannot call shutdown twice.');\n      return;\n    }\n\n    // No timeout if timeoutMillis is undefined or null.\n    if (options?.timeoutMillis == null) {\n      await this.onShutdown();\n    } else {\n      await callWithTimeout(this.onShutdown(), options.timeoutMillis);\n    }\n\n    this._shutdown = true;\n  }\n\n  /**\n   * Flushes metrics read by this reader, the promise will reject after the optional timeout or resolve after completion.\n   *\n   * <p> NOTE: this operation will continue even after the promise rejects due to a timeout.\n   * @param options options with timeout.\n   */\n  async forceFlush(options?: ForceFlushOptions): Promise<void> {\n    if (this._shutdown) {\n      api.diag.warn('Cannot forceFlush on already shutdown MetricReader.');\n      return;\n    }\n\n    // No timeout if timeoutMillis is undefined or null.\n    if (options?.timeoutMillis == null) {\n      await this.onForceFlush();\n      return;\n    }\n\n    await callWithTimeout(this.onForceFlush(), options.timeoutMillis);\n  }\n}\n"]}