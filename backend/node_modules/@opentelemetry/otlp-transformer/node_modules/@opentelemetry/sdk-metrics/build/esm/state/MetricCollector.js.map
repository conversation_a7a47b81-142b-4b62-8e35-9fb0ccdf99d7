{"version": 3, "file": "MetricCollector.js", "sourceRoot": "", "sources": ["../../../src/state/MetricCollector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AASrD;;;;GAIG;AACH;IACE,yBACU,YAAsC,EACtC,aAA2B;QAD3B,iBAAY,GAAZ,YAAY,CAA0B;QACtC,kBAAa,GAAb,aAAa,CAAc;IAClC,CAAC;IAEE,iCAAO,GAAb,UAAc,OAA8B;;;;;;;wBACpC,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;wBAC5C,YAAY,GAAmB,EAAE,CAAC;wBAClC,MAAM,GAAc,EAAE,CAAC;wBAEvB,uBAAuB,GAAG,KAAK,CAAC,IAAI,CACxC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAC7C,CAAC,GAAG,CAAC,UAAM,gBAAgB;;;;4CACV,qBAAM,gBAAgB,CAAC,OAAO,CAC5C,IAAI,EACJ,cAAc,EACd,OAAO,CACR,EAAA;;wCAJK,OAAO,GAAG,SAIf;wCAED,sCAAsC;wCACtC,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,KAAI,IAAI,EAAE;4CACjC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;yCACzC;wCAED,+BAA+B;wCAC/B,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAAI,IAAI,EAAE;4CAC3B,MAAM,CAAC,IAAI,OAAX,MAAM,2BAAS,OAAO,CAAC,MAAM,WAAE;yCAChC;;;;6BACF,CAAC,CAAC;wBACH,qBAAM,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAA;;wBAA1C,SAA0C,CAAC;wBAE3C,sBAAO;gCACL,eAAe,EAAE;oCACf,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ;oCACpC,YAAY,EAAE,YAAY;iCAC3B;gCACD,MAAM,EAAE,MAAM;6BACf,EAAC;;;;KACH;IAED;;OAEG;IACG,oCAAU,GAAhB,UAAiB,OAA2B;;;;4BAC1C,qBAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,EAAA;;wBAA5C,SAA4C,CAAC;;;;;KAC9C;IAED;;OAEG;IACG,kCAAQ,GAAd,UAAe,OAAyB;;;;4BACtC,qBAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAA;;wBAA1C,SAA0C,CAAC;;;;;KAC5C;IAED,sDAA4B,GAA5B,UAA6B,cAA8B;QACzD,OAAO,IAAI,CAAC,aAAa,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAAC;IACzE,CAAC;IAED,2CAAiB,GAAjB,UAAkB,cAA8B;QAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAC9D,CAAC;IACH,sBAAC;AAAD,CAAC,AA9DD,IA8DC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { millisToHrTime } from '@opentelemetry/core';\nimport { AggregationTemporalitySelector } from '../export/AggregationSelector';\nimport { CollectionResult, ScopeMetrics } from '../export/MetricData';\nimport { MetricProducer, MetricCollectOptions } from '../export/MetricProducer';\nimport { MetricReader } from '../export/MetricReader';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { ForceFlushOptions, ShutdownOptions } from '../types';\nimport { MeterProviderSharedState } from './MeterProviderSharedState';\n\n/**\n * An internal opaque interface that the MetricReader receives as\n * MetricProducer. It acts as the storage key to the internal metric stream\n * state for each MetricReader.\n */\nexport class MetricCollector implements MetricProducer {\n  constructor(\n    private _sharedState: MeterProviderSharedState,\n    private _metricReader: MetricReader\n  ) {}\n\n  async collect(options?: MetricCollectOptions): Promise<CollectionResult> {\n    const collectionTime = millisToHrTime(Date.now());\n    const scopeMetrics: ScopeMetrics[] = [];\n    const errors: unknown[] = [];\n\n    const meterCollectionPromises = Array.from(\n      this._sharedState.meterSharedStates.values()\n    ).map(async meterSharedState => {\n      const current = await meterSharedState.collect(\n        this,\n        collectionTime,\n        options\n      );\n\n      // only add scope metrics if available\n      if (current?.scopeMetrics != null) {\n        scopeMetrics.push(current.scopeMetrics);\n      }\n\n      // only add errors if available\n      if (current?.errors != null) {\n        errors.push(...current.errors);\n      }\n    });\n    await Promise.all(meterCollectionPromises);\n\n    return {\n      resourceMetrics: {\n        resource: this._sharedState.resource,\n        scopeMetrics: scopeMetrics,\n      },\n      errors: errors,\n    };\n  }\n\n  /**\n   * Delegates for MetricReader.forceFlush.\n   */\n  async forceFlush(options?: ForceFlushOptions): Promise<void> {\n    await this._metricReader.forceFlush(options);\n  }\n\n  /**\n   * Delegates for MetricReader.shutdown.\n   */\n  async shutdown(options?: ShutdownOptions): Promise<void> {\n    await this._metricReader.shutdown(options);\n  }\n\n  selectAggregationTemporality(instrumentType: InstrumentType) {\n    return this._metricReader.selectAggregationTemporality(instrumentType);\n  }\n\n  selectAggregation(instrumentType: InstrumentType) {\n    return this._metricReader.selectAggregation(instrumentType);\n  }\n}\n\n/**\n * An internal interface for MetricCollector. Exposes the necessary\n * information for metric collection.\n */\nexport interface MetricCollectorHandle {\n  selectAggregationTemporality: AggregationTemporalitySelector;\n}\n"]}