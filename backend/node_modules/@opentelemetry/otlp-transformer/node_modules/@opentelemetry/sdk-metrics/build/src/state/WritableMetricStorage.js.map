{"version": 3, "file": "WritableMetricStorage.js", "sourceRoot": "", "sources": ["../../../src/state/WritableMetricStorage.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { AttributeHashMap } from './HashMap';\n\n/**\n * Internal interface. Stores measurements and allows synchronous writes of\n * measurements.\n *\n * An interface representing SyncMetricStorage with type parameters removed.\n */\nexport interface WritableMetricStorage {\n  /** Records a measurement. */\n  record(\n    value: number,\n    attributes: MetricAttributes,\n    context: Context,\n    recordTime: HrTime\n  ): void;\n}\n\n/**\n * Internal interface. Stores measurements and allows asynchronous writes of\n * measurements.\n *\n * An interface representing AsyncMetricStorage with type parameters removed.\n */\nexport interface AsyncWritableMetricStorage {\n  /** Records a batch of measurements. */\n  record(measurements: AttributeHashMap<number>, observationTime: HrTime): void;\n}\n"]}