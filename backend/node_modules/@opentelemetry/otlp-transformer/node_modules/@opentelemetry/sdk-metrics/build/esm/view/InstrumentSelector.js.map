{"version": 3, "file": "InstrumentSelector.js", "sourceRoot": "", "sources": ["../../../src/view/InstrumentSelector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAa,MAAM,aAAa,CAAC;AAQ1E;IAKE,4BAAY,QAAqC;;QAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,gBAAgB,CAAC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,mCAAI,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC;QAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC,CAAC;IACxD,CAAC;IAED,oCAAO,GAAP;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,0CAAa,GAAb;QACE,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,0CAAa,GAAb;QACE,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IACH,yBAAC;AAAD,CAAC,AAtBD,IAsBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { ExactPredicate, PatternPredicate, Predicate } from './Predicate';\n\nexport interface InstrumentSelectorCriteria {\n  name?: string;\n  type?: InstrumentType;\n  unit?: string;\n}\n\nexport class InstrumentSelector {\n  private _nameFilter: Predicate;\n  private _type?: InstrumentType;\n  private _unitFilter: Predicate;\n\n  constructor(criteria?: InstrumentSelectorCriteria) {\n    this._nameFilter = new PatternPredicate(criteria?.name ?? '*');\n    this._type = criteria?.type;\n    this._unitFilter = new ExactPredicate(criteria?.unit);\n  }\n\n  getType() {\n    return this._type;\n  }\n\n  getNameFilter() {\n    return this._nameFilter;\n  }\n\n  getUnitFilter() {\n    return this._unitFilter;\n  }\n}\n"]}