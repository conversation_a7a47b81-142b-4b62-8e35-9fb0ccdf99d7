{"version": 3, "file": "NeverSampleExemplarFilter.js", "sourceRoot": "", "sources": ["../../../src/exemplar/NeverSampleExemplarFilter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH,MAAM,OAAO,yBAAyB;IACpC,YAAY,CACV,MAAc,EACd,UAAkB,EAClB,WAA6B,EAC7B,IAAa;QAEb,OAAO,KAAK,CAAC;IACf,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { ExemplarFilter } from './ExemplarFilter';\n\nexport class NeverSampleExemplarFilter implements ExemplarFilter {\n  shouldSample(\n    _value: number,\n    _timestamp: HrTime,\n    _attributes: MetricAttributes,\n    _ctx: Context\n  ): boolean {\n    return false;\n  }\n}\n"]}