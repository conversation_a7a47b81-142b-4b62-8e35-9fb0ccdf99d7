{"version": 3, "file": "ProcessDetector.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/ProcessDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAE5D;;;GAGG;AACH,MAAM,eAAe;IACnB,MAAM,CAAC,MAAgC;QACrC,OAAO,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7D,CAAC;CACF;AAED,MAAM,CAAC,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector } from '../../types';\nimport { ResourceDetectionConfig } from '../../config';\nimport { IResource } from '../../IResource';\nimport { processDetectorSync } from './ProcessDetectorSync';\n\n/**\n * ProcessDetector will be used to detect the resources related current process running\n * and being instrumented from the NodeJS Process module.\n */\nclass ProcessDetector implements Detector {\n  detect(config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(processDetectorSync.detect(config));\n  }\n}\n\nexport const processDetector = new ProcessDetector();\n"]}