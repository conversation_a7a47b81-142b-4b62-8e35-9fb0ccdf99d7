"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.osDetectorSync = void 0;
const semantic_conventions_1 = require("@opentelemetry/semantic-conventions");
const Resource_1 = require("../../Resource");
const os_1 = require("os");
const utils_1 = require("./utils");
/**
 * OSDetectorSync detects the resources related to the operating system (OS) on
 * which the process represented by this resource is running.
 */
class OSDetectorSync {
    detect(_config) {
        const attributes = {
            [semantic_conventions_1.SemanticResourceAttributes.OS_TYPE]: (0, utils_1.normalizeType)((0, os_1.platform)()),
            [semantic_conventions_1.SemanticResourceAttributes.OS_VERSION]: (0, os_1.release)(),
        };
        return new Resource_1.Resource(attributes);
    }
}
exports.osDetectorSync = new OSDetectorSync();
//# sourceMappingURL=OSDetectorSync.js.map