{"version": 3, "file": "hex-to-base64.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/hex-to-base64.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,WAAW,CAAC,MAAc;IACxC,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;IAChC,IAAI,gBAAgB,GAAG,EAAE,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE;QACrC,IAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3C,IAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACrC,gBAAgB,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;KACjD;IACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAChC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport function hexToBase64(hexStr: string): string {\n  const hexStrLen = hexStr.length;\n  let hexAsciiCharsStr = '';\n  for (let i = 0; i < hexStrLen; i += 2) {\n    const hexPair = hexStr.substring(i, i + 2);\n    const hexVal = parseInt(hexPair, 16);\n    hexAsciiCharsStr += String.fromCharCode(hexVal);\n  }\n  return btoa(hexAsciiCharsStr);\n}\n"]}