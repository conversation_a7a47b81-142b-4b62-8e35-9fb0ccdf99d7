{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/baggage/utils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAGL,8BAA8B,GAC/B,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACL,uBAAuB,EACvB,4BAA4B,EAC5B,0BAA0B,EAC1B,wBAAwB,GACzB,MAAM,aAAa,CAAC;AAQrB,MAAM,UAAU,iBAAiB,CAAC,QAAkB;IAClD,OAAO,QAAQ,CAAC,MAAM,CAAC,UAAC,MAAc,EAAE,OAAe;QACrD,IAAM,KAAK,GAAG,KAAG,MAAM,IACrB,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,IAC3C,OAAS,CAAC;QACb,OAAO,KAAK,CAAC,MAAM,GAAG,wBAAwB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;IAClE,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,OAAgB;IAC1C,OAAO,OAAO,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,UAAC,EAAY;YAAZ,KAAA,aAAY,EAAX,GAAG,QAAA,EAAE,KAAK,QAAA;QAC7C,IAAI,KAAK,GAAM,kBAAkB,CAAC,GAAG,CAAC,SAAI,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAG,CAAC;QAE5E,sCAAsC;QACtC,kHAAkH;QAClH,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE;YAChC,KAAK,IAAI,4BAA4B,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;SACnE;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,iBAAiB,CAC/B,KAAa;IAEb,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAC7D,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC;QAAE,OAAO;IACnC,IAAM,WAAW,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;IACvC,IAAI,CAAC,WAAW;QAAE,OAAO;IACzB,IAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;IACvE,IAAI,cAAc,IAAI,CAAC;QAAE,OAAO;IAChC,IAAM,GAAG,GAAG,kBAAkB,CAC5B,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,CAChD,CAAC;IACF,IAAM,KAAK,GAAG,kBAAkB,CAC9B,WAAW,CAAC,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CACjD,CAAC;IACF,IAAI,QAAQ,CAAC;IACb,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;QACzB,QAAQ,GAAG,8BAA8B,CACvC,UAAU,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAC9C,CAAC;KACH;IACD,OAAO,EAAE,GAAG,KAAA,EAAE,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAE,CAAC;AAClC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,uBAAuB,CACrC,KAAc;IAEd,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAC/D,OAAO,KAAK;SACT,KAAK,CAAC,uBAAuB,CAAC;SAC9B,GAAG,CAAC,UAAA,KAAK;QACR,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC,CAAC;SACD,MAAM,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAjD,CAAiD,CAAC;SACpE,MAAM,CAAyB,UAAC,OAAO,EAAE,OAAO;QAC/C,oEAAoE;QACpE,OAAO,CAAC,OAAQ,CAAC,GAAG,CAAC,GAAG,OAAQ,CAAC,KAAK,CAAC;QACvC,OAAO,OAAO,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;AACX,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  Baggage,\n  BaggageEntryMetadata,\n  baggageEntryMetadataFromString,\n} from '@opentelemetry/api';\nimport {\n  BAGGAGE_ITEMS_SEPARATOR,\n  BAGGAGE_PROPERTIES_SEPARATOR,\n  BAGGAGE_KEY_PAIR_SEPARATOR,\n  BAGGAGE_MAX_TOTAL_LENGTH,\n} from './constants';\n\ntype ParsedBaggageKeyValue = {\n  key: string;\n  value: string;\n  metadata: BaggageEntryMetadata | undefined;\n};\n\nexport function serializeKeyPairs(keyPairs: string[]): string {\n  return keyPairs.reduce((hValue: string, current: string) => {\n    const value = `${hValue}${\n      hValue !== '' ? BAGGAGE_ITEMS_SEPARATOR : ''\n    }${current}`;\n    return value.length > BAGGAGE_MAX_TOTAL_LENGTH ? hValue : value;\n  }, '');\n}\n\nexport function getKeyPairs(baggage: Baggage): string[] {\n  return baggage.getAllEntries().map(([key, value]) => {\n    let entry = `${encodeURIComponent(key)}=${encodeURIComponent(value.value)}`;\n\n    // include opaque metadata if provided\n    // NOTE: we intentionally don't URI-encode the metadata - that responsibility falls on the metadata implementation\n    if (value.metadata !== undefined) {\n      entry += BAGGAGE_PROPERTIES_SEPARATOR + value.metadata.toString();\n    }\n\n    return entry;\n  });\n}\n\nexport function parsePairKeyValue(\n  entry: string\n): ParsedBaggageKeyValue | undefined {\n  const valueProps = entry.split(BAGGAGE_PROPERTIES_SEPARATOR);\n  if (valueProps.length <= 0) return;\n  const keyPairPart = valueProps.shift();\n  if (!keyPairPart) return;\n  const separatorIndex = keyPairPart.indexOf(BAGGAGE_KEY_PAIR_SEPARATOR);\n  if (separatorIndex <= 0) return;\n  const key = decodeURIComponent(\n    keyPairPart.substring(0, separatorIndex).trim()\n  );\n  const value = decodeURIComponent(\n    keyPairPart.substring(separatorIndex + 1).trim()\n  );\n  let metadata;\n  if (valueProps.length > 0) {\n    metadata = baggageEntryMetadataFromString(\n      valueProps.join(BAGGAGE_PROPERTIES_SEPARATOR)\n    );\n  }\n  return { key, value, metadata };\n}\n\n/**\n * Parse a string serialized in the baggage HTTP Format (without metadata):\n * https://github.com/w3c/baggage/blob/master/baggage/HTTP_HEADER_FORMAT.md\n */\nexport function parseKeyPairsIntoRecord(\n  value?: string\n): Record<string, string> {\n  if (typeof value !== 'string' || value.length === 0) return {};\n  return value\n    .split(BAGGAGE_ITEMS_SEPARATOR)\n    .map(entry => {\n      return parsePairKeyValue(entry);\n    })\n    .filter(keyPair => keyPair !== undefined && keyPair.value.length > 0)\n    .reduce<Record<string, string>>((headers, keyPair) => {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      headers[keyPair!.key] = keyPair!.value;\n      return headers;\n    }, {});\n}\n"]}