{"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["../../../src/api/logs.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,mCAAmC,EACnC,mBAAmB,EACnB,OAAO,EACP,UAAU,GACX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAI7D;IAGE;IAAuB,CAAC;IAEV,mBAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,yCAAuB,GAA9B,UAA+B,QAAwB;QACrD,IAAI,OAAO,CAAC,mBAAmB,CAAC,EAAE;YAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;QAED,OAAO,CAAC,mBAAmB,CAAC,GAAG,UAAU,CACvC,mCAAmC,EACnC,QAAQ,EACR,oBAAoB,CACrB,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,mCAAiB,GAAxB;;QACE,OAAO,CACL,MAAA,MAAA,OAAO,CAAC,mBAAmB,CAAC,+CAA5B,OAAO,EAAwB,mCAAmC,CAAC,mCACnE,oBAAoB,CACrB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,2BAAS,GAAhB,UACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAEvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,wCAAwC;IACjC,yBAAO,GAAd;QACE,OAAO,OAAO,CAAC,mBAAmB,CAAC,CAAC;IACtC,CAAC;IACH,cAAC;AAAD,CAAC,AAxDD,IAwDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  API_BACKWARDS_COMPATIBILITY_VERSION,\n  GLOBAL_LOGS_API_KEY,\n  _global,\n  makeGetter,\n} from '../internal/global-utils';\nimport { LoggerProvider } from '../types/LoggerProvider';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { Logger } from '../types/Logger';\nimport { LoggerOptions } from '../types/LoggerOptions';\n\nexport class LogsAPI {\n  private static _instance?: LogsAPI;\n\n  private constructor() {}\n\n  public static getInstance(): LogsAPI {\n    if (!this._instance) {\n      this._instance = new LogsAPI();\n    }\n\n    return this._instance;\n  }\n\n  public setGlobalLoggerProvider(provider: LoggerProvider): LoggerProvider {\n    if (_global[GLOBAL_LOGS_API_KEY]) {\n      return this.getLoggerProvider();\n    }\n\n    _global[GLOBAL_LOGS_API_KEY] = makeGetter<LoggerProvider>(\n      API_BACKWARDS_COMPATIBILITY_VERSION,\n      provider,\n      NOOP_LOGGER_PROVIDER\n    );\n\n    return provider;\n  }\n\n  /**\n   * Returns the global logger provider.\n   *\n   * @returns LoggerProvider\n   */\n  public getLoggerProvider(): LoggerProvider {\n    return (\n      _global[GLOBAL_LOGS_API_KEY]?.(API_BACKWARDS_COMPATIBILITY_VERSION) ??\n      NOOP_LOGGER_PROVIDER\n    );\n  }\n\n  /**\n   * Returns a logger from the global logger provider.\n   *\n   * @returns Logger\n   */\n  public getLogger(\n    name: string,\n    version?: string,\n    options?: LoggerOptions\n  ): Logger {\n    return this.getLoggerProvider().getLogger(name, version, options);\n  }\n\n  /** Remove the global logger provider */\n  public disable(): void {\n    delete _global[GLOBAL_LOGS_API_KEY];\n  }\n}\n"]}