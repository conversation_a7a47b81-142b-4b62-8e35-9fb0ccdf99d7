{"version": 3, "file": "MultiSpanProcessor.js", "sourceRoot": "", "sources": ["../../src/MultiSpanProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8CAAyD;AAKzD;;;GAGG;AACH,MAAa,kBAAkB;IAC7B,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAEjE,UAAU;QACR,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE;YAChD,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;SAC3C;QACD,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAClB,IAAI,CAAC,GAAG,EAAE;gBACT,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,IAAA,yBAAkB,EAChB,KAAK,IAAI,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAC5D,CAAC;gBACF,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CAAC,IAAU,EAAE,OAAgB;QAClC,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE;YAChD,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACtC;IACH,CAAC;IAED,KAAK,CAAC,IAAkB;QACtB,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE;YAChD,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC3B;IACH,CAAC;IAED,QAAQ;QACN,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE;YAChD,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;SACzC;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC9B,OAAO,EAAE,CAAC;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA/CD,gDA+CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\nimport { globalErrorHandler } from '@opentelemetry/core';\nimport { ReadableSpan } from './export/ReadableSpan';\nimport { Span } from './Span';\nimport { SpanProcessor } from './SpanProcessor';\n\n/**\n * Implementation of the {@link SpanProcessor} that simply forwards all\n * received events to a list of {@link SpanProcessor}s.\n */\nexport class MultiSpanProcessor implements SpanProcessor {\n  constructor(private readonly _spanProcessors: SpanProcessor[]) {}\n\n  forceFlush(): Promise<void> {\n    const promises: Promise<void>[] = [];\n\n    for (const spanProcessor of this._spanProcessors) {\n      promises.push(spanProcessor.forceFlush());\n    }\n    return new Promise(resolve => {\n      Promise.all(promises)\n        .then(() => {\n          resolve();\n        })\n        .catch(error => {\n          globalErrorHandler(\n            error || new Error('MultiSpanProcessor: forceFlush failed')\n          );\n          resolve();\n        });\n    });\n  }\n\n  onStart(span: Span, context: Context): void {\n    for (const spanProcessor of this._spanProcessors) {\n      spanProcessor.onStart(span, context);\n    }\n  }\n\n  onEnd(span: ReadableSpan): void {\n    for (const spanProcessor of this._spanProcessors) {\n      spanProcessor.onEnd(span);\n    }\n  }\n\n  shutdown(): Promise<void> {\n    const promises: Promise<void>[] = [];\n\n    for (const spanProcessor of this._spanProcessors) {\n      promises.push(spanProcessor.shutdown());\n    }\n    return new Promise((resolve, reject) => {\n      Promise.all(promises).then(() => {\n        resolve();\n      }, reject);\n    });\n  }\n}\n"]}