import type { <PERSON> } from '@opentelemetry/api';
import type { ReadableSpan, TimedEvent } from '@opentelemetry/sdk-trace-base';
import type { Encoder } from '../common';
import { IEvent, ILink, ISpan } from './types';
export declare function sdkSpanToOtlpSpan(span: ReadableSpan, encoder: Encoder): ISpan;
export declare function toOtlpLink(link: Link, encoder: Encoder): ILink;
export declare function toOtlpSpanEvent(timedEvent: TimedEvent, encoder: Encoder): IEvent;
//# sourceMappingURL=internal.d.ts.map