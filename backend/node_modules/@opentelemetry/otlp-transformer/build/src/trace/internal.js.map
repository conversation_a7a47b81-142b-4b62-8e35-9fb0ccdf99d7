{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/trace/internal.ts"], "names": [], "mappings": ";;;AAkBA,iDAAkD;AAGlD,SAAgB,iBAAiB,CAAC,IAAkB,EAAE,OAAgB;;IACpE,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,OAAO;QACL,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;QAC/C,MAAM,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC;QAC7C,YAAY,EAAE,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC;QAClE,UAAU,EAAE,MAAA,GAAG,CAAC,UAAU,0CAAE,SAAS,EAAE;QACvC,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,6EAA6E;QAC7E,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;QAC3C,iBAAiB,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;QACvD,eAAe,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;QACnD,UAAU,EAAE,IAAA,uBAAY,EAAC,IAAI,CAAC,UAAU,CAAC;QACzC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;QACnD,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACjE,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;QAC3C,MAAM,EAAE;YACN,4CAA4C;YAC5C,IAAI,EAAE,MAAM,CAAC,IAA8B;YAC3C,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB;QACD,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACxD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;KAC1C,CAAC;AACJ,CAAC;AAzBD,8CAyBC;AAED,SAAgB,UAAU,CAAC,IAAU,EAAE,OAAgB;;IACrD,OAAO;QACL,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAA,uBAAY,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAChE,MAAM,EAAE,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACtD,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACxD,UAAU,EAAE,MAAA,IAAI,CAAC,OAAO,CAAC,UAAU,0CAAE,SAAS,EAAE;QAChD,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,IAAI,CAAC;KACzD,CAAC;AACJ,CAAC;AARD,gCAQC;AAED,SAAgB,eAAe,CAC7B,UAAsB,EACtB,OAAgB;IAEhB,OAAO;QACL,UAAU,EAAE,UAAU,CAAC,UAAU;YAC/B,CAAC,CAAC,IAAA,uBAAY,EAAC,UAAU,CAAC,UAAU,CAAC;YACrC,CAAC,CAAC,EAAE;QACN,IAAI,EAAE,UAAU,CAAC,IAAI;QACrB,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC;QACnD,sBAAsB,EAAE,UAAU,CAAC,sBAAsB,IAAI,CAAC;KAC/D,CAAC;AACJ,CAAC;AAZD,0CAYC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { Link } from '@opentelemetry/api';\nimport type { ReadableSpan, TimedEvent } from '@opentelemetry/sdk-trace-base';\nimport type { Encoder } from '../common';\nimport { toAttributes } from '../common/internal';\nimport { EStatusCode, IEvent, ILink, ISpan } from './types';\n\nexport function sdkSpanToOtlpSpan(span: ReadableSpan, encoder: Encoder): ISpan {\n  const ctx = span.spanContext();\n  const status = span.status;\n  return {\n    traceId: encoder.encodeSpanContext(ctx.traceId),\n    spanId: encoder.encodeSpanContext(ctx.spanId),\n    parentSpanId: encoder.encodeOptionalSpanContext(span.parentSpanId),\n    traceState: ctx.traceState?.serialize(),\n    name: span.name,\n    // Span kind is offset by 1 because the API does not define a value for unset\n    kind: span.kind == null ? 0 : span.kind + 1,\n    startTimeUnixNano: encoder.encodeHrTime(span.startTime),\n    endTimeUnixNano: encoder.encodeHrTime(span.endTime),\n    attributes: toAttributes(span.attributes),\n    droppedAttributesCount: span.droppedAttributesCount,\n    events: span.events.map(event => toOtlpSpanEvent(event, encoder)),\n    droppedEventsCount: span.droppedEventsCount,\n    status: {\n      // API and proto enums share the same values\n      code: status.code as unknown as EStatusCode,\n      message: status.message,\n    },\n    links: span.links.map(link => toOtlpLink(link, encoder)),\n    droppedLinksCount: span.droppedLinksCount,\n  };\n}\n\nexport function toOtlpLink(link: Link, encoder: Encoder): ILink {\n  return {\n    attributes: link.attributes ? toAttributes(link.attributes) : [],\n    spanId: encoder.encodeSpanContext(link.context.spanId),\n    traceId: encoder.encodeSpanContext(link.context.traceId),\n    traceState: link.context.traceState?.serialize(),\n    droppedAttributesCount: link.droppedAttributesCount || 0,\n  };\n}\n\nexport function toOtlpSpanEvent(\n  timedEvent: TimedEvent,\n  encoder: Encoder\n): IEvent {\n  return {\n    attributes: timedEvent.attributes\n      ? toAttributes(timedEvent.attributes)\n      : [],\n    name: timedEvent.name,\n    timeUnixNano: encoder.encodeHrTime(timedEvent.time),\n    droppedAttributesCount: timedEvent.droppedAttributesCount || 0,\n  };\n}\n"]}