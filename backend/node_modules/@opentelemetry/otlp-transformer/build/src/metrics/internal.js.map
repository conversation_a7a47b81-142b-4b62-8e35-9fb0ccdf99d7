{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/metrics/internal.ts"], "names": [], "mappings": ";;;AAgBA,4CAA+C;AAC/C,4DASoC;AACpC,iDAAkD;AAUlD,sCAAoD;AAEpD,SAAgB,iBAAiB,CAC/B,eAAgC,EAChC,OAA6B;IAE7B,MAAM,OAAO,GAAG,IAAA,uBAAc,EAAC,OAAO,CAAC,CAAC;IACxC,OAAO;QACL,QAAQ,EAAE;YACR,UAAU,EAAE,IAAA,uBAAY,EAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC7D,sBAAsB,EAAE,CAAC;SAC1B;QACD,SAAS,EAAE,SAAS;QACpB,YAAY,EAAE,cAAc,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC;KACpE,CAAC;AACJ,CAAC;AAbD,8CAaC;AAED,SAAgB,cAAc,CAC5B,YAA4B,EAC5B,OAAgB;IAEhB,OAAO,KAAK,CAAC,IAAI,CACf,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC3B,KAAK,EAAE;YACL,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI;YACxB,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO;SAC/B;QACD,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACzE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,SAAS;KACnC,CAAC,CAAC,CACJ,CAAC;AACJ,CAAC;AAdD,wCAcC;AAED,SAAgB,QAAQ,CAAC,UAAsB,EAAE,OAAgB;IAC/D,MAAM,GAAG,GAAY;QACnB,IAAI,EAAE,UAAU,CAAC,UAAU,CAAC,IAAI;QAChC,WAAW,EAAE,UAAU,CAAC,UAAU,CAAC,WAAW;QAC9C,IAAI,EAAE,UAAU,CAAC,UAAU,CAAC,IAAI;KACjC,CAAC;IAEF,MAAM,sBAAsB,GAAG,wBAAwB,CACrD,UAAU,CAAC,sBAAsB,CAClC,CAAC;IAEF,QAAQ,UAAU,CAAC,aAAa,EAAE;QAChC,KAAK,2BAAa,CAAC,GAAG;YACpB,GAAG,CAAC,GAAG,GAAG;gBACR,sBAAsB;gBACtB,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,UAAU,EAAE,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC;aACtD,CAAC;YACF,MAAM;QACR,KAAK,2BAAa,CAAC,KAAK;YACtB,GAAG,CAAC,KAAK,GAAG;gBACV,UAAU,EAAE,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC;aACtD,CAAC;YACF,MAAM;QACR,KAAK,2BAAa,CAAC,SAAS;YAC1B,GAAG,CAAC,SAAS,GAAG;gBACd,sBAAsB;gBACtB,UAAU,EAAE,qBAAqB,CAAC,UAAU,EAAE,OAAO,CAAC;aACvD,CAAC;YACF,MAAM;QACR,KAAK,2BAAa,CAAC,qBAAqB;YACtC,GAAG,CAAC,oBAAoB,GAAG;gBACzB,sBAAsB;gBACtB,UAAU,EAAE,gCAAgC,CAAC,UAAU,EAAE,OAAO,CAAC;aAClE,CAAC;YACF,MAAM;KACT;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAvCD,4BAuCC;AAED,SAAS,mBAAmB,CAC1B,SAGmC,EACnC,SAAoB,EACpB,OAAgB;IAEhB,MAAM,GAAG,GAAqB;QAC5B,UAAU,EAAE,IAAA,uBAAY,EAAC,SAAS,CAAC,UAAU,CAAC;QAC9C,iBAAiB,EAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC;QAC5D,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC;KACtD,CAAC;IAEF,QAAQ,SAAS,EAAE;QACjB,KAAK,eAAS,CAAC,GAAG;YAChB,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,KAAe,CAAC;YACtC,MAAM;QACR,KAAK,eAAS,CAAC,MAAM;YACnB,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAe,CAAC;YACzC,MAAM;KACT;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,oBAAoB,CAC3B,UAAsB,EACtB,OAAgB;IAEhB,OAAO,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;QAC3C,OAAO,mBAAmB,CACxB,SAAS,EACT,UAAU,CAAC,UAAU,CAAC,SAAS,EAC/B,OAAO,CACR,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAsB,EACtB,OAAgB;IAEhB,OAAO,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;QAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,KAAkB,CAAC;QAC/C,OAAO;YACL,UAAU,EAAE,IAAA,uBAAY,EAAC,SAAS,CAAC,UAAU,CAAC;YAC9C,YAAY,EAAE,SAAS,CAAC,OAAO,CAAC,MAAM;YACtC,cAAc,EAAE,SAAS,CAAC,OAAO,CAAC,UAAU;YAC5C,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,GAAG,EAAE,SAAS,CAAC,GAAG;YAClB,GAAG,EAAE,SAAS,CAAC,GAAG;YAClB,GAAG,EAAE,SAAS,CAAC,GAAG;YAClB,iBAAiB,EAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC;YAC5D,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC;SACtD,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,gCAAgC,CACvC,UAAsB,EACtB,OAAgB;IAEhB,OAAO,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;QAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,KAA6B,CAAC;QAC1D,OAAO;YACL,UAAU,EAAE,IAAA,uBAAY,EAAC,SAAS,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,GAAG,EAAE,SAAS,CAAC,GAAG;YAClB,GAAG,EAAE,SAAS,CAAC,GAAG;YAClB,GAAG,EAAE,SAAS,CAAC,GAAG;YAClB,QAAQ,EAAE;gBACR,MAAM,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM;gBACjC,YAAY,EAAE,SAAS,CAAC,QAAQ,CAAC,YAAY;aAC9C;YACD,QAAQ,EAAE;gBACR,MAAM,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM;gBACjC,YAAY,EAAE,SAAS,CAAC,QAAQ,CAAC,YAAY;aAC9C;YACD,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,iBAAiB,EAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC;YAC5D,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC;SACtD,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,wBAAwB,CAC/B,WAAmC;IAEnC,QAAQ,WAAW,EAAE;QACnB,KAAK,oCAAsB,CAAC,KAAK;YAC/B,6CAA6D;QAC/D,KAAK,oCAAsB,CAAC,UAAU;YACpC,kDAAkE;KACrE;AACH,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { OtlpEncodingOptions } from '../common/types';\nimport { ValueType } from '@opentelemetry/api';\nimport {\n  AggregationTemporality,\n  DataPoint,\n  DataPointType,\n  ExponentialHistogram,\n  Histogram,\n  MetricData,\n  ResourceMetrics,\n  ScopeMetrics,\n} from '@opentelemetry/sdk-metrics';\nimport { toAttributes } from '../common/internal';\nimport {\n  EAggregationTemporality,\n  IExponentialHistogramDataPoint,\n  IHistogramDataPoint,\n  IMetric,\n  INumberDataPoint,\n  IResourceMetrics,\n  IScopeMetrics,\n} from './types';\nimport { Encoder, getOtlpEncoder } from '../common';\n\nexport function toResourceMetrics(\n  resourceMetrics: ResourceMetrics,\n  options?: OtlpEncodingOptions\n): IResourceMetrics {\n  const encoder = getOtlpEncoder(options);\n  return {\n    resource: {\n      attributes: toAttributes(resourceMetrics.resource.attributes),\n      droppedAttributesCount: 0,\n    },\n    schemaUrl: undefined,\n    scopeMetrics: toScopeMetrics(resourceMetrics.scopeMetrics, encoder),\n  };\n}\n\nexport function toScopeMetrics(\n  scopeMetrics: ScopeMetrics[],\n  encoder: Encoder\n): IScopeMetrics[] {\n  return Array.from(\n    scopeMetrics.map(metrics => ({\n      scope: {\n        name: metrics.scope.name,\n        version: metrics.scope.version,\n      },\n      metrics: metrics.metrics.map(metricData => toMetric(metricData, encoder)),\n      schemaUrl: metrics.scope.schemaUrl,\n    }))\n  );\n}\n\nexport function toMetric(metricData: MetricData, encoder: Encoder): IMetric {\n  const out: IMetric = {\n    name: metricData.descriptor.name,\n    description: metricData.descriptor.description,\n    unit: metricData.descriptor.unit,\n  };\n\n  const aggregationTemporality = toAggregationTemporality(\n    metricData.aggregationTemporality\n  );\n\n  switch (metricData.dataPointType) {\n    case DataPointType.SUM:\n      out.sum = {\n        aggregationTemporality,\n        isMonotonic: metricData.isMonotonic,\n        dataPoints: toSingularDataPoints(metricData, encoder),\n      };\n      break;\n    case DataPointType.GAUGE:\n      out.gauge = {\n        dataPoints: toSingularDataPoints(metricData, encoder),\n      };\n      break;\n    case DataPointType.HISTOGRAM:\n      out.histogram = {\n        aggregationTemporality,\n        dataPoints: toHistogramDataPoints(metricData, encoder),\n      };\n      break;\n    case DataPointType.EXPONENTIAL_HISTOGRAM:\n      out.exponentialHistogram = {\n        aggregationTemporality,\n        dataPoints: toExponentialHistogramDataPoints(metricData, encoder),\n      };\n      break;\n  }\n\n  return out;\n}\n\nfunction toSingularDataPoint(\n  dataPoint:\n    | DataPoint<number>\n    | DataPoint<Histogram>\n    | DataPoint<ExponentialHistogram>,\n  valueType: ValueType,\n  encoder: Encoder\n) {\n  const out: INumberDataPoint = {\n    attributes: toAttributes(dataPoint.attributes),\n    startTimeUnixNano: encoder.encodeHrTime(dataPoint.startTime),\n    timeUnixNano: encoder.encodeHrTime(dataPoint.endTime),\n  };\n\n  switch (valueType) {\n    case ValueType.INT:\n      out.asInt = dataPoint.value as number;\n      break;\n    case ValueType.DOUBLE:\n      out.asDouble = dataPoint.value as number;\n      break;\n  }\n\n  return out;\n}\n\nfunction toSingularDataPoints(\n  metricData: MetricData,\n  encoder: Encoder\n): INumberDataPoint[] {\n  return metricData.dataPoints.map(dataPoint => {\n    return toSingularDataPoint(\n      dataPoint,\n      metricData.descriptor.valueType,\n      encoder\n    );\n  });\n}\n\nfunction toHistogramDataPoints(\n  metricData: MetricData,\n  encoder: Encoder\n): IHistogramDataPoint[] {\n  return metricData.dataPoints.map(dataPoint => {\n    const histogram = dataPoint.value as Histogram;\n    return {\n      attributes: toAttributes(dataPoint.attributes),\n      bucketCounts: histogram.buckets.counts,\n      explicitBounds: histogram.buckets.boundaries,\n      count: histogram.count,\n      sum: histogram.sum,\n      min: histogram.min,\n      max: histogram.max,\n      startTimeUnixNano: encoder.encodeHrTime(dataPoint.startTime),\n      timeUnixNano: encoder.encodeHrTime(dataPoint.endTime),\n    };\n  });\n}\n\nfunction toExponentialHistogramDataPoints(\n  metricData: MetricData,\n  encoder: Encoder\n): IExponentialHistogramDataPoint[] {\n  return metricData.dataPoints.map(dataPoint => {\n    const histogram = dataPoint.value as ExponentialHistogram;\n    return {\n      attributes: toAttributes(dataPoint.attributes),\n      count: histogram.count,\n      min: histogram.min,\n      max: histogram.max,\n      sum: histogram.sum,\n      positive: {\n        offset: histogram.positive.offset,\n        bucketCounts: histogram.positive.bucketCounts,\n      },\n      negative: {\n        offset: histogram.negative.offset,\n        bucketCounts: histogram.negative.bucketCounts,\n      },\n      scale: histogram.scale,\n      zeroCount: histogram.zeroCount,\n      startTimeUnixNano: encoder.encodeHrTime(dataPoint.startTime),\n      timeUnixNano: encoder.encodeHrTime(dataPoint.endTime),\n    };\n  });\n}\n\nfunction toAggregationTemporality(\n  temporality: AggregationTemporality\n): EAggregationTemporality {\n  switch (temporality) {\n    case AggregationTemporality.DELTA:\n      return EAggregationTemporality.AGGREGATION_TEMPORALITY_DELTA;\n    case AggregationTemporality.CUMULATIVE:\n      return EAggregationTemporality.AGGREGATION_TEMPORALITY_CUMULATIVE;\n  }\n}\n"]}