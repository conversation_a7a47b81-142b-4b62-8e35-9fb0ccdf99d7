{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/metrics/index.ts"], "names": [], "mappings": "AAkBA,OAAO,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAC;AAE/C,MAAM,UAAU,iCAAiC,CAC/C,eAAkC,EAClC,OAA6B;IAE7B,OAAO;QACL,eAAe,EAAE,eAAe,CAAC,GAAG,CAAC,UAAA,OAAO;YAC1C,OAAA,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC;QAAnC,CAAmC,CACpC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { ResourceMetrics } from '@opentelemetry/sdk-metrics';\nimport type { IExportMetricsServiceRequest } from './types';\nimport type { OtlpEncodingOptions } from '../common/types';\nimport { toResourceMetrics } from './internal';\n\nexport function createExportMetricsServiceRequest(\n  resourceMetrics: ResourceMetrics[],\n  options?: OtlpEncodingOptions\n): IExportMetricsServiceRequest {\n  return {\n    resourceMetrics: resourceMetrics.map(metrics =>\n      toResourceMetrics(metrics, options)\n    ),\n  };\n}\n"]}