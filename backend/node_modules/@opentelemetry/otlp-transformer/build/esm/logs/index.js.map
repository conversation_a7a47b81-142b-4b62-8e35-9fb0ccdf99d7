{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/logs/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUH,OAAO,EAAW,cAAc,EAAE,MAAM,WAAW,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAK1E,MAAM,UAAU,8BAA8B,CAC5C,UAA+B,EAC/B,OAA6B;IAE7B,IAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;IACxC,OAAO;QACL,YAAY,EAAE,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC;KAC5D,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACxB,UAA+B;;IAE/B,IAAM,WAAW,GAGb,IAAI,GAAG,EAAE,CAAC;;QAEd,KAAqB,IAAA,eAAA,SAAA,UAAU,CAAA,sCAAA,8DAAE;YAA5B,IAAM,MAAM,uBAAA;YAEb,IAAA,QAAQ,GAEN,MAAM,SAFA,EACR,KACE,MAAM,qBADoD,EAApC,MAAI,UAAA,EAAE,eAAY,EAAZ,OAAO,mBAAG,EAAE,KAAA,EAAE,iBAAc,EAAd,SAAS,mBAAG,EAAE,KAAE,CACnD;YAEX,IAAI,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;gBACnB,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;aACnC;YAED,IAAM,MAAM,GAAM,MAAI,SAAI,OAAO,SAAI,SAAW,CAAC;YACjD,IAAI,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACjC,IAAI,CAAC,OAAO,EAAE;gBACZ,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAC7B;YACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACtB;;;;;;;;;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAA+B,EAC/B,OAAgB;IAEhB,IAAM,WAAW,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAClD,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,UAAC,EAAkB;YAAlB,KAAA,aAAkB,EAAjB,QAAQ,QAAA,EAAE,MAAM,QAAA;QAAM,OAAA,CAAC;YACtD,QAAQ,EAAE;gBACR,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC7C,sBAAsB,EAAE,CAAC;aAC1B;YACD,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,UAAC,EAAa;oBAAb,KAAA,aAAa,EAAV,SAAS,QAAA;gBAEvC,IAAA,KACE,SAAS,CAAC,CAAC,CAAC,qBADoC,EAA1B,IAAI,UAAA,EAAE,OAAO,aAAA,EAAE,SAAS,eAAE,CACnC;gBACjB,OAAO;oBACL,KAAK,EAAE,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE;oBACxB,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,EAAzB,CAAyB,CAAC;oBAC3D,SAAS,WAAA;iBACV,CAAC;YACJ,CAAC,CAAC;YACF,SAAS,EAAE,SAAS;SACrB,CAAC;IAhBqD,CAgBrD,CAAC,CAAC;AACN,CAAC;AAED,SAAS,WAAW,CAAC,GAAsB,EAAE,OAAgB;;IAC3D,OAAO;QACL,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;QAC9C,oBAAoB,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC;QAC9D,cAAc,EAAE,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC;QACpD,YAAY,EAAE,GAAG,CAAC,YAAY;QAC9B,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC;QAC1B,UAAU,EAAE,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC;QAC3C,sBAAsB,EAAE,CAAC;QACzB,KAAK,EAAE,MAAA,GAAG,CAAC,WAAW,0CAAE,UAAU;QAClC,OAAO,EAAE,OAAO,CAAC,yBAAyB,CAAC,MAAA,GAAG,CAAC,WAAW,0CAAE,OAAO,CAAC;QACpE,MAAM,EAAE,OAAO,CAAC,yBAAyB,CAAC,MAAA,GAAG,CAAC,WAAW,0CAAE,MAAM,CAAC;KACnE,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CACvB,cAA0C;IAE1C,OAAO,cAAmE,CAAC;AAC7E,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,UAAyB;IACvD,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAhC,CAAgC,CAAC,CAAC;AAC9E,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ReadableLogRecord } from '@opentelemetry/sdk-logs';\nimport {\n  ESeverityNumber,\n  IExportLogsServiceRequest,\n  ILogRecord,\n  IResourceLogs,\n} from './types';\nimport { IResource } from '@opentelemetry/resources';\nimport { Encoder, getOtlpEncoder } from '../common';\nimport { toAnyValue, toAttributes, toKeyValue } from '../common/internal';\nimport { SeverityNumber } from '@opentelemetry/api-logs';\nimport { OtlpEncodingOptions, IKeyValue } from '../common/types';\nimport { LogAttributes } from '@opentelemetry/api-logs';\n\nexport function createExportLogsServiceRequest(\n  logRecords: ReadableLogRecord[],\n  options?: OtlpEncodingOptions\n): IExportLogsServiceRequest {\n  const encoder = getOtlpEncoder(options);\n  return {\n    resourceLogs: logRecordsToResourceLogs(logRecords, encoder),\n  };\n}\n\nfunction createResourceMap(\n  logRecords: ReadableLogRecord[]\n): Map<IResource, Map<string, ReadableLogRecord[]>> {\n  const resourceMap: Map<\n    IResource,\n    Map<string, ReadableLogRecord[]>\n  > = new Map();\n\n  for (const record of logRecords) {\n    const {\n      resource,\n      instrumentationScope: { name, version = '', schemaUrl = '' },\n    } = record;\n\n    let ismMap = resourceMap.get(resource);\n    if (!ismMap) {\n      ismMap = new Map();\n      resourceMap.set(resource, ismMap);\n    }\n\n    const ismKey = `${name}@${version}:${schemaUrl}`;\n    let records = ismMap.get(ismKey);\n    if (!records) {\n      records = [];\n      ismMap.set(ismKey, records);\n    }\n    records.push(record);\n  }\n  return resourceMap;\n}\n\nfunction logRecordsToResourceLogs(\n  logRecords: ReadableLogRecord[],\n  encoder: Encoder\n): IResourceLogs[] {\n  const resourceMap = createResourceMap(logRecords);\n  return Array.from(resourceMap, ([resource, ismMap]) => ({\n    resource: {\n      attributes: toAttributes(resource.attributes),\n      droppedAttributesCount: 0,\n    },\n    scopeLogs: Array.from(ismMap, ([, scopeLogs]) => {\n      const {\n        instrumentationScope: { name, version, schemaUrl },\n      } = scopeLogs[0];\n      return {\n        scope: { name, version },\n        logRecords: scopeLogs.map(log => toLogRecord(log, encoder)),\n        schemaUrl,\n      };\n    }),\n    schemaUrl: undefined,\n  }));\n}\n\nfunction toLogRecord(log: ReadableLogRecord, encoder: Encoder): ILogRecord {\n  return {\n    timeUnixNano: encoder.encodeHrTime(log.hrTime),\n    observedTimeUnixNano: encoder.encodeHrTime(log.hrTimeObserved),\n    severityNumber: toSeverityNumber(log.severityNumber),\n    severityText: log.severityText,\n    body: toAnyValue(log.body),\n    attributes: toLogAttributes(log.attributes),\n    droppedAttributesCount: 0,\n    flags: log.spanContext?.traceFlags,\n    traceId: encoder.encodeOptionalSpanContext(log.spanContext?.traceId),\n    spanId: encoder.encodeOptionalSpanContext(log.spanContext?.spanId),\n  };\n}\n\nfunction toSeverityNumber(\n  severityNumber: SeverityNumber | undefined\n): ESeverityNumber | undefined {\n  return severityNumber as number | undefined as ESeverityNumber | undefined;\n}\n\nexport function toLogAttributes(attributes: LogAttributes): IKeyValue[] {\n  return Object.keys(attributes).map(key => toKeyValue(key, attributes[key]));\n}\n"]}