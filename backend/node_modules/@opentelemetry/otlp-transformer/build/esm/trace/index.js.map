{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/trace/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAC;AAM/C,OAAO,EAAW,cAAc,EAAE,MAAM,WAAW,CAAC;AAEpD,MAAM,UAAU,+BAA+B,CAC7C,KAAqB,EACrB,OAA6B;IAE7B,IAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;IACxC,OAAO;QACL,aAAa,EAAE,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC;KAC1D,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,aAA6B;;IACtD,IAAM,WAAW,GAAgD,IAAI,GAAG,EAAE,CAAC;;QAC3E,KAAqB,IAAA,kBAAA,SAAA,aAAa,CAAA,4CAAA,uEAAE;YAA/B,IAAM,MAAM,0BAAA;YACf,IAAI,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAE9C,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;gBACnB,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;aAC1C;YAED,kFAAkF;YAClF,IAAM,yBAAyB,GAAM,MAAM,CAAC,sBAAsB,CAAC,IAAI,UACrE,MAAM,CAAC,sBAAsB,CAAC,OAAO,IAAI,EAAE,WACzC,MAAM,CAAC,sBAAsB,CAAC,SAAS,IAAI,EAAE,CAAE,CAAC;YACpD,IAAI,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YAEpD,IAAI,CAAC,OAAO,EAAE;gBACZ,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,CAAC,GAAG,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;aAChD;YAED,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACtB;;;;;;;;;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,0BAA0B,CACjC,aAA6B,EAC7B,OAAgB;IAEhB,IAAM,WAAW,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC;IACrD,IAAM,GAAG,GAAqB,EAAE,CAAC;IAEjC,IAAM,aAAa,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;IAC5C,IAAI,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;IACjC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE;QACZ,IAAA,KAAA,OAAqB,KAAK,CAAC,KAAK,IAAA,EAA/B,QAAQ,QAAA,EAAE,MAAM,QAAe,CAAC;QACvC,IAAM,kBAAkB,GAAkB,EAAE,CAAC;QAC7C,IAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QACpC,IAAI,QAAQ,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;QAClC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;YACrB,IAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC;YAClC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnB,IAAA,KACJ,UAAU,CAAC,CAAC,CAAC,CAAC,sBAAsB,EAD9B,MAAI,UAAA,EAAE,OAAO,aAAA,EAAE,SAAS,eACM,CAAC;gBACvC,IAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,UAAA,YAAY;oBACvC,OAAA,iBAAiB,CAAC,YAAY,EAAE,OAAO,CAAC;gBAAxC,CAAwC,CACzC,CAAC;gBAEF,kBAAkB,CAAC,IAAI,CAAC;oBACtB,KAAK,EAAE,EAAE,IAAI,QAAA,EAAE,OAAO,SAAA,EAAE;oBACxB,KAAK,EAAE,KAAK;oBACZ,SAAS,EAAE,SAAS;iBACrB,CAAC,CAAC;aACJ;YACD,QAAQ,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;SAC/B;QACD,gEAAgE;QAChE,IAAM,gBAAgB,GAAmB;YACvC,QAAQ,EAAE;gBACR,UAAU,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC7C,sBAAsB,EAAE,CAAC;aAC1B;YACD,UAAU,EAAE,kBAAkB;YAC9B,SAAS,EAAE,SAAS;SACrB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3B,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;KAC9B;IAED,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { IResource } from '@opentelemetry/resources';\nimport type { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport type { OtlpEncodingOptions } from '../common/types';\nimport { toAttributes } from '../common/internal';\nimport { sdkSpanToOtlpSpan } from './internal';\nimport {\n  IExportTraceServiceRequest,\n  IResourceSpans,\n  IScopeSpans,\n} from './types';\nimport { Encoder, getOtlpEncoder } from '../common';\n\nexport function createExportTraceServiceRequest(\n  spans: ReadableSpan[],\n  options?: OtlpEncodingOptions\n): IExportTraceServiceRequest {\n  const encoder = getOtlpEncoder(options);\n  return {\n    resourceSpans: spanRecordsToResourceSpans(spans, encoder),\n  };\n}\n\nfunction createResourceMap(readableSpans: ReadableSpan[]) {\n  const resourceMap: Map<IResource, Map<string, ReadableSpan[]>> = new Map();\n  for (const record of readableSpans) {\n    let ilmMap = resourceMap.get(record.resource);\n\n    if (!ilmMap) {\n      ilmMap = new Map();\n      resourceMap.set(record.resource, ilmMap);\n    }\n\n    // TODO this is duplicated in basic tracer. Consolidate on a common helper in core\n    const instrumentationLibraryKey = `${record.instrumentationLibrary.name}@${\n      record.instrumentationLibrary.version || ''\n    }:${record.instrumentationLibrary.schemaUrl || ''}`;\n    let records = ilmMap.get(instrumentationLibraryKey);\n\n    if (!records) {\n      records = [];\n      ilmMap.set(instrumentationLibraryKey, records);\n    }\n\n    records.push(record);\n  }\n\n  return resourceMap;\n}\n\nfunction spanRecordsToResourceSpans(\n  readableSpans: ReadableSpan[],\n  encoder: Encoder\n): IResourceSpans[] {\n  const resourceMap = createResourceMap(readableSpans);\n  const out: IResourceSpans[] = [];\n\n  const entryIterator = resourceMap.entries();\n  let entry = entryIterator.next();\n  while (!entry.done) {\n    const [resource, ilmMap] = entry.value;\n    const scopeResourceSpans: IScopeSpans[] = [];\n    const ilmIterator = ilmMap.values();\n    let ilmEntry = ilmIterator.next();\n    while (!ilmEntry.done) {\n      const scopeSpans = ilmEntry.value;\n      if (scopeSpans.length > 0) {\n        const { name, version, schemaUrl } =\n          scopeSpans[0].instrumentationLibrary;\n        const spans = scopeSpans.map(readableSpan =>\n          sdkSpanToOtlpSpan(readableSpan, encoder)\n        );\n\n        scopeResourceSpans.push({\n          scope: { name, version },\n          spans: spans,\n          schemaUrl: schemaUrl,\n        });\n      }\n      ilmEntry = ilmIterator.next();\n    }\n    // TODO SDK types don't provide resource schema URL at this time\n    const transformedSpans: IResourceSpans = {\n      resource: {\n        attributes: toAttributes(resource.attributes),\n        droppedAttributesCount: 0,\n      },\n      scopeSpans: scopeResourceSpans,\n      schemaUrl: undefined,\n    };\n\n    out.push(transformedSpans);\n    entry = entryIterator.next();\n  }\n\n  return out;\n}\n"]}