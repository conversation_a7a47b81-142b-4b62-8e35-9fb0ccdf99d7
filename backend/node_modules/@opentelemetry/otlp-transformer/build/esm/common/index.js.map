{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/common/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAIH,OAAO,EAAE,WAAW,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAEvE,IAAM,WAAW,GAAG,MAAM,CAAC,UAAa,CAAC,CAAC;AAE1C,MAAM,UAAU,aAAa,CAAC,MAAc;IAC1C,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,KAAa;IACtC,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;IAC9C,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7D,OAAO,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,CAAC;AACvB,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,MAAc;IAC7C,IAAM,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IACpC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,MAAc;IAC3C,IAAM,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IACpC,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC1B,CAAC;AAED,IAAM,eAAe,GACnB,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,mBAAmB,CAAC;AAcvE,SAAS,QAAQ,CAAI,KAAQ;IAC3B,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAuB;IAClD,IAAI,GAAG,KAAK,SAAS;QAAE,OAAO,SAAS,CAAC;IACxC,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC;AAED,IAAM,eAAe,GAAY;IAC/B,YAAY,EAAE,gBAAgB;IAC9B,iBAAiB,EAAE,WAAW;IAC9B,yBAAyB,EAAE,mBAAmB;CAC/C,CAAC;AAEF,MAAM,UAAU,cAAc,CAAC,OAA6B;;IAC1D,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,OAAO,eAAe,CAAC;KACxB;IAED,IAAM,WAAW,GAAG,MAAA,OAAO,CAAC,WAAW,mCAAI,IAAI,CAAC;IAChD,IAAM,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,KAAK,CAAC;IACvC,OAAO;QACL,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe;QAC9D,iBAAiB,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW;QAClD,yBAAyB,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,mBAAmB;KACnE,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { OtlpEncodingOptions, Fixed64, LongBits } from './types';\nimport { HrTime } from '@opentelemetry/api';\nimport { hexToBase64, hrTimeToNanoseconds } from '@opentelemetry/core';\n\nconst NANOSECONDS = BigInt(1_000_000_000);\n\nexport function hrTimeToNanos(hrTime: HrTime): bigint {\n  return BigInt(hrTime[0]) * NANOSECONDS + BigInt(hrTime[1]);\n}\n\nexport function toLongBits(value: bigint): LongBits {\n  const low = Number(BigInt.asUintN(32, value));\n  const high = Number(BigInt.asUintN(32, value >> BigInt(32)));\n  return { low, high };\n}\n\nexport function encodeAsLongBits(hrTime: HrTime): LongBits {\n  const nanos = hrTimeToNanos(hrTime);\n  return toLongBits(nanos);\n}\n\nexport function encodeAsString(hrTime: HrTime): string {\n  const nanos = hrTimeToNanos(hrTime);\n  return nanos.toString();\n}\n\nconst encodeTimestamp =\n  typeof BigInt !== 'undefined' ? encodeAsString : hrTimeToNanoseconds;\n\nexport type HrTimeEncodeFunction = (hrTime: HrTime) => Fixed64;\nexport type SpanContextEncodeFunction = (spanContext: string) => string;\nexport type OptionalSpanContextEncodeFunction = (\n  spanContext: string | undefined\n) => string | undefined;\n\nexport interface Encoder {\n  encodeHrTime: HrTimeEncodeFunction;\n  encodeSpanContext: SpanContextEncodeFunction;\n  encodeOptionalSpanContext: OptionalSpanContextEncodeFunction;\n}\n\nfunction identity<T>(value: T): T {\n  return value;\n}\n\nfunction optionalHexToBase64(str: string | undefined): string | undefined {\n  if (str === undefined) return undefined;\n  return hexToBase64(str);\n}\n\nconst DEFAULT_ENCODER: Encoder = {\n  encodeHrTime: encodeAsLongBits,\n  encodeSpanContext: hexToBase64,\n  encodeOptionalSpanContext: optionalHexToBase64,\n};\n\nexport function getOtlpEncoder(options?: OtlpEncodingOptions): Encoder {\n  if (options === undefined) {\n    return DEFAULT_ENCODER;\n  }\n\n  const useLongBits = options.useLongBits ?? true;\n  const useHex = options.useHex ?? false;\n  return {\n    encodeHrTime: useLongBits ? encodeAsLongBits : encodeTimestamp,\n    encodeSpanContext: useHex ? identity : hexToBase64,\n    encodeOptionalSpanContext: useHex ? identity : optionalHexToBase64,\n  };\n}\n"]}