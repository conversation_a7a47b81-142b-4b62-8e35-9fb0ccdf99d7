{"name": "@opentelemetry/instrumentation-nestjs-core", "version": "0.33.4", "description": "OpenTelemetry NestJS automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "compile:watch": "tsc -w", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-nestjs-core --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha --timeout 5000 -p tsconfig.json 'test/**/*.test.ts'", "test-all-versions": "tav", "version:update": "node ../../../scripts/version-update.js"}, "keywords": ["instrumentation", "<PERSON><PERSON><PERSON>", "nestjs-core", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@nestjs/common": "9.4.3", "@nestjs/core": "9.4.3", "@nestjs/microservices": "9.4.3", "@nestjs/platform-express": "9.4.3", "@nestjs/websockets": "9.4.3", "@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "@types/semver": "7.5.3", "cross-env": "7.0.3", "mocha": "7.2.0", "nyc": "15.1.0", "reflect-metadata": "0.1.13", "rimraf": "5.0.5", "rxjs": "7.8.1", "rxjs-compat": "6.6.7", "semver": "7.5.4", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-nestjs-core#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}