{"version": 3, "file": "AttributeNames.js", "sourceRoot": "", "sources": ["../../../src/enums/AttributeNames.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,IAAY,cASX;AATD,WAAY,cAAc;IACxB,4CAA0B,CAAA;IAC1B,sCAAoB,CAAA;IACpB,0CAAwB,CAAA;IACxB,kDAAgC,CAAA;IAChC,8CAA4B,CAAA;IAC5B,wCAAsB,CAAA;IACtB,sDAAoC,CAAA;IACpC,0CAAwB,CAAA;AAC1B,CAAC,EATW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QASzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum AttributeNames {\n  VERSION = 'nestjs.version',\n  TYPE = 'nestjs.type',\n  MODULE = 'nestjs.module',\n  CONTROLLER = 'nestjs.controller',\n  CALLBACK = 'nestjs.callback',\n  PIPES = 'nestjs.pipes',\n  INTERCEPTORS = 'nestjs.interceptors',\n  GUARDS = 'nestjs.guards',\n}\n"]}