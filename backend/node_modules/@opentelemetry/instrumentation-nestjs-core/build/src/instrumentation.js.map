{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAC1C,oEAMwC;AAIxC,8EAAyE;AACzE,uCAAoC;AACpC,mCAAmD;AAEnD,MAAa,eAAgB,SAAQ,qCAAwB;IAM3D,YAAY,SAAgC,EAAE;QAC5C,KAAK,CAAC,4CAA4C,EAAE,iBAAO,CAAC,CAAC;IAC/D,CAAC;IAED,IAAI;QACF,MAAM,MAAM,GAAG,IAAI,qDAAmC,CACpD,eAAe,CAAC,SAAS,EACzB,CAAC,SAAS,CAAC,EACX,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,YAAY,eAAe,CAAC,SAAS,IAAI,aAAa,EAAE,CACzD,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,cAAc,eAAe,CAAC,SAAS,IAAI,aAAa,EAAE,CAC3D,CAAC;YACF,IAAI,aAAa,KAAK,SAAS;gBAAE,OAAO;QAC1C,CAAC,CACF,CAAC;QAEF,MAAM,CAAC,KAAK,CAAC,IAAI,CACf,IAAI,CAAC,iCAAiC,CAAC,CAAC,SAAS,CAAC,CAAC,EACnD,IAAI,CAAC,4CAA4C,CAAC,CAAC,SAAS,CAAC,CAAC,CAC/D,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,iCAAiC,CAAC,QAAkB;QAClD,OAAO,IAAI,+CAA6B,CACtC,8BAA8B,EAC9B,QAAQ,EACR,CAAC,iBAAsB,EAAE,aAAsB,EAAE,EAAE;YACjD,IAAI,CAAC,aAAa,CAChB,aAAa,EACb,iBAAiB,CAAC,iBAAiB,CAAC,SAAS,EAC7C,QAAQ,EACR,2BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CACxD,CAAC;YACF,OAAO,iBAAiB,CAAC;QAC3B,CAAC,EACD,CAAC,iBAAsB,EAAE,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACxE,CAAC,CACF,CAAC;IACJ,CAAC;IAED,4CAA4C,CAAC,QAAkB;QAC7D,OAAO,IAAI,+CAA6B,CACtC,iDAAiD,EACjD,QAAQ,EACR,CAAC,sBAA2B,EAAE,aAAsB,EAAE,EAAE;YACtD,IAAI,CAAC,aAAa,CAChB,aAAa,EACb,sBAAsB,CAAC,sBAAsB,CAAC,SAAS,EACvD,QAAQ,EACR,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CACpD,CAAC;YACF,OAAO,sBAAsB,CAAC;QAChC,CAAC,EACD,CAAC,sBAA2B,EAAE,EAAE;YAC9B,IAAI,CAAC,OAAO,CACV,sBAAsB,CAAC,sBAAsB,CAAC,SAAS,EACvD,QAAQ,CACT,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,aAAa,CACnB,aAAiC,EACjC,GAAQ,EACR,UAAkB,EAClB,OAA+B;QAE/B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,YAAY,UAAU,cAAc,eAAe,CAAC,SAAS,IAAI,aAAa,EAAE,CACjF,CAAC;QACF,IAAI,IAAA,2BAAS,EAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;SAC/B;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;;AA1FH,0CA2FC;AA1FiB,yBAAS,GAAG,cAAc,CAAC;AAC3B,iCAAiB,GAAG;IAClC,SAAS,EAAE,eAAe,CAAC,SAAS;CACrC,CAAC;AAyFJ,SAAS,2BAA2B,CAClC,MAAkB,EAClB,aAAsB;IAEtB,OAAO,SAAS,UAAU,CAAC,QAAmC;QAC5D,OAAO,SAAS,eAAe,CAE7B,UAAe;QACf,qBAAqB;;YAErB,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE;gBAC/C,UAAU,kCACL,eAAe,CAAC,iBAAiB,KACpC,CAAC,sBAAc,CAAC,IAAI,CAAC,EAAE,gBAAQ,CAAC,YAAY,EAC5C,CAAC,sBAAc,CAAC,OAAO,CAAC,EAAE,aAAa,EACvC,CAAC,sBAAc,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,IAAI,GACzC;aACF,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;YAElE,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;gBAC9C,IAAI;oBACF,OAAO,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAgB,CAAC,CAAC;iBACrD;gBAAC,OAAO,CAAM,EAAE;oBACf,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;iBACzB;wBAAS;oBACR,IAAI,CAAC,GAAG,EAAE,CAAC;iBACZ;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAAC,MAAkB,EAAE,aAAsB;IACzE,OAAO,SAAS,iBAAiB,CAC/B,QAA0C;QAE1C,OAAO,SAAS,sBAAsB,CAEpC,QAAoB,EACpB,QAAqC;YAErC,SAAS,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAClE,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAgB,CAAC,CAAC;YACvD,OAAO,UAEL,GAAQ,EACR,GAAQ,EACR,IAAiC;;gBAEjC,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACnC,MAAM,YAAY,GAChB,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI;oBAC/C,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI;oBAC3B,CAAC,CAAC,iBAAiB,CAAC;gBACxB,MAAM,QAAQ,GAAG,YAAY;oBAC3B,CAAC,CAAC,GAAG,YAAY,IAAI,YAAY,EAAE;oBACnC,CAAC,CAAC,YAAY,CAAC;gBAEjB,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;oBACtC,UAAU,kCACL,eAAe,CAAC,iBAAiB,KACpC,CAAC,sBAAc,CAAC,OAAO,CAAC,EAAE,aAAa,EACvC,CAAC,sBAAc,CAAC,IAAI,CAAC,EAAE,gBAAQ,CAAC,eAAe,EAC/C,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC,MAAM,EAC5C,CAAC,yCAAkB,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG,EACzD,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAC7B,CAAA,MAAA,GAAG,CAAC,KAAK,0CAAE,IAAI,MAAI,MAAA,GAAG,CAAC,YAAY,0CAAE,GAAG,CAAA,IAAI,GAAG,CAAC,UAAU,EAC5D,CAAC,sBAAc,CAAC,UAAU,CAAC,EAAE,YAAY,EACzC,CAAC,sBAAc,CAAC,QAAQ,CAAC,EAAE,YAAY,GACxC;iBACF,CAAC,CAAC;gBACH,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;gBAElE,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;oBAC9C,IAAI;wBACF,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAgB,CAAC,CAAC;qBACpD;oBAAC,OAAO,CAAM,EAAE;wBACf,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;qBACzB;4BAAS;wBACR,IAAI,CAAC,GAAG,EAAE,CAAC;qBACZ;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACxB,MAAkB,EAClB,aAAiC,EACjC,OAAiB;IAEjB,MAAM,cAAc,GAAG;QACrB,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,IAAI,wBAAwB,EAAE;YACtE,UAAU,kCACL,eAAe,CAAC,iBAAiB,KACpC,CAAC,sBAAc,CAAC,OAAO,CAAC,EAAE,aAAa,EACvC,CAAC,sBAAc,CAAC,IAAI,CAAC,EAAE,gBAAQ,CAAC,eAAe,EAC/C,CAAC,sBAAc,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,IAAI,GACxC;SACF,CAAC,CAAC;QACH,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;QAElE,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;YAC9C,IAAI;gBACF,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;aAC7C;YAAC,OAAO,CAAM,EAAE;gBACf,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACzB;oBAAS;gBACR,IAAI,CAAC,GAAG,EAAE,CAAC;aACZ;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,OAAO,CAAC,IAAI,EAAE;QAChB,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;KACxE;IAED,wHAAwH;IACxH,uDAAuD;IACvD,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;QACrD,OAAO,CAAC,cAAc,CACpB,WAAW,EACX,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,OAAO,CAAC,EACzC,cAAc,CACf,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,MAAM,QAAQ,GAAG,CAAC,IAAc,EAAE,KAAY,EAAE,EAAE;IAChD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IAC3E,OAAO,KAAK,CAAC;AACf,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationConfig,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n} from '@opentelemetry/instrumentation';\nimport type { NestFactory } from '@nestjs/core/nest-factory.js';\nimport type { RouterExecutionContext } from '@nestjs/core/router/router-execution-context.js';\nimport type { Controller } from '@nestjs/common/interfaces';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\nimport { VERSION } from './version';\nimport { AttributeNames, NestType } from './enums';\n\nexport class Instrumentation extends InstrumentationBase<any> {\n  static readonly COMPONENT = '@nestjs/core';\n  static readonly COMMON_ATTRIBUTES = {\n    component: Instrumentation.COMPONENT,\n  };\n\n  constructor(config: InstrumentationConfig = {}) {\n    super('@opentelemetry/instrumentation-nestjs-core', VERSION);\n  }\n\n  init() {\n    const module = new InstrumentationNodeModuleDefinition<any>(\n      Instrumentation.COMPONENT,\n      ['>=4.0.0'],\n      (moduleExports, moduleVersion) => {\n        this._diag.debug(\n          `Patching ${Instrumentation.COMPONENT}@${moduleVersion}`\n        );\n        return moduleExports;\n      },\n      (moduleExports, moduleVersion) => {\n        this._diag.debug(\n          `Unpatching ${Instrumentation.COMPONENT}@${moduleVersion}`\n        );\n        if (moduleExports === undefined) return;\n      }\n    );\n\n    module.files.push(\n      this.getNestFactoryFileInstrumentation(['>=4.0.0']),\n      this.getRouterExecutionContextFileInstrumentation(['>=4.0.0'])\n    );\n\n    return module;\n  }\n\n  getNestFactoryFileInstrumentation(versions: string[]) {\n    return new InstrumentationNodeModuleFile<any>(\n      '@nestjs/core/nest-factory.js',\n      versions,\n      (NestFactoryStatic: any, moduleVersion?: string) => {\n        this.ensureWrapped(\n          moduleVersion,\n          NestFactoryStatic.NestFactoryStatic.prototype,\n          'create',\n          createWrapNestFactoryCreate(this.tracer, moduleVersion)\n        );\n        return NestFactoryStatic;\n      },\n      (NestFactoryStatic: any) => {\n        this._unwrap(NestFactoryStatic.NestFactoryStatic.prototype, 'create');\n      }\n    );\n  }\n\n  getRouterExecutionContextFileInstrumentation(versions: string[]) {\n    return new InstrumentationNodeModuleFile<any>(\n      '@nestjs/core/router/router-execution-context.js',\n      versions,\n      (RouterExecutionContext: any, moduleVersion?: string) => {\n        this.ensureWrapped(\n          moduleVersion,\n          RouterExecutionContext.RouterExecutionContext.prototype,\n          'create',\n          createWrapCreateHandler(this.tracer, moduleVersion)\n        );\n        return RouterExecutionContext;\n      },\n      (RouterExecutionContext: any) => {\n        this._unwrap(\n          RouterExecutionContext.RouterExecutionContext.prototype,\n          'create'\n        );\n      }\n    );\n  }\n\n  private ensureWrapped(\n    moduleVersion: string | undefined,\n    obj: any,\n    methodName: string,\n    wrapper: (original: any) => any\n  ) {\n    this._diag.debug(\n      `Applying ${methodName} patch for ${Instrumentation.COMPONENT}@${moduleVersion}`\n    );\n    if (isWrapped(obj[methodName])) {\n      this._unwrap(obj, methodName);\n    }\n    this._wrap(obj, methodName, wrapper);\n  }\n}\n\nfunction createWrapNestFactoryCreate(\n  tracer: api.Tracer,\n  moduleVersion?: string\n) {\n  return function wrapCreate(original: typeof NestFactory.create) {\n    return function createWithTrace(\n      this: typeof NestFactory,\n      nestModule: any\n      /* serverOrOptions */\n    ) {\n      const span = tracer.startSpan('Create Nest App', {\n        attributes: {\n          ...Instrumentation.COMMON_ATTRIBUTES,\n          [AttributeNames.TYPE]: NestType.APP_CREATION,\n          [AttributeNames.VERSION]: moduleVersion,\n          [AttributeNames.MODULE]: nestModule.name,\n        },\n      });\n      const spanContext = api.trace.setSpan(api.context.active(), span);\n\n      return api.context.with(spanContext, async () => {\n        try {\n          return await original.apply(this, arguments as any);\n        } catch (e: any) {\n          throw addError(span, e);\n        } finally {\n          span.end();\n        }\n      });\n    };\n  };\n}\n\nfunction createWrapCreateHandler(tracer: api.Tracer, moduleVersion?: string) {\n  return function wrapCreateHandler(\n    original: RouterExecutionContext['create']\n  ) {\n    return function createHandlerWithTrace(\n      this: RouterExecutionContext,\n      instance: Controller,\n      callback: (...args: any[]) => unknown\n    ) {\n      arguments[1] = createWrapHandler(tracer, moduleVersion, callback);\n      const handler = original.apply(this, arguments as any);\n      return function (\n        this: any,\n        req: any,\n        res: any,\n        next: (...args: any[]) => unknown\n      ) {\n        const callbackName = callback.name;\n        const instanceName =\n          instance.constructor && instance.constructor.name\n            ? instance.constructor.name\n            : 'UnnamedInstance';\n        const spanName = callbackName\n          ? `${instanceName}.${callbackName}`\n          : instanceName;\n\n        const span = tracer.startSpan(spanName, {\n          attributes: {\n            ...Instrumentation.COMMON_ATTRIBUTES,\n            [AttributeNames.VERSION]: moduleVersion,\n            [AttributeNames.TYPE]: NestType.REQUEST_CONTEXT,\n            [SemanticAttributes.HTTP_METHOD]: req.method,\n            [SemanticAttributes.HTTP_URL]: req.originalUrl || req.url,\n            [SemanticAttributes.HTTP_ROUTE]:\n              req.route?.path || req.routeOptions?.url || req.routerPath,\n            [AttributeNames.CONTROLLER]: instanceName,\n            [AttributeNames.CALLBACK]: callbackName,\n          },\n        });\n        const spanContext = api.trace.setSpan(api.context.active(), span);\n\n        return api.context.with(spanContext, async () => {\n          try {\n            return await handler.apply(this, arguments as any);\n          } catch (e: any) {\n            throw addError(span, e);\n          } finally {\n            span.end();\n          }\n        });\n      };\n    };\n  };\n}\n\nfunction createWrapHandler(\n  tracer: api.Tracer,\n  moduleVersion: string | undefined,\n  handler: Function\n) {\n  const wrappedHandler = function (this: RouterExecutionContext) {\n    const span = tracer.startSpan(handler.name || 'anonymous nest handler', {\n      attributes: {\n        ...Instrumentation.COMMON_ATTRIBUTES,\n        [AttributeNames.VERSION]: moduleVersion,\n        [AttributeNames.TYPE]: NestType.REQUEST_HANDLER,\n        [AttributeNames.CALLBACK]: handler.name,\n      },\n    });\n    const spanContext = api.trace.setSpan(api.context.active(), span);\n\n    return api.context.with(spanContext, async () => {\n      try {\n        return await handler.apply(this, arguments);\n      } catch (e: any) {\n        throw addError(span, e);\n      } finally {\n        span.end();\n      }\n    });\n  };\n\n  if (handler.name) {\n    Object.defineProperty(wrappedHandler, 'name', { value: handler.name });\n  }\n\n  // Get the current metadata and set onto the wrapper to ensure other decorators ( ie: NestJS EventPattern / RolesGuard )\n  // won't be affected by the use of this instrumentation\n  Reflect.getMetadataKeys(handler).forEach(metadataKey => {\n    Reflect.defineMetadata(\n      metadataKey,\n      Reflect.getMetadata(metadataKey, handler),\n      wrappedHandler\n    );\n  });\n  return wrappedHandler;\n}\n\nconst addError = (span: api.Span, error: Error) => {\n  span.recordException(error);\n  span.setStatus({ code: api.SpanStatusCode.ERROR, message: error.message });\n  return error;\n};\n"]}