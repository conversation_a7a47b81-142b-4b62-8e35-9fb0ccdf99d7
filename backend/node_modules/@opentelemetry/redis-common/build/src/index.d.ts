/// <reference types="node" />
export declare type DbStatementSerializer = (cmdName: string, cmdArgs: Array<string | Buffer | number | any[]>) => string;
/**
 * Given the redis command name and arguments, return a combination of the
 * command name + the allowed arguments according to `serializationSubsets`.
 * @param cmdName The redis command name
 * @param cmdArgs The redis command arguments
 * @returns a combination of the command name + args according to `serializationSubsets`.
 */
export declare const defaultDbStatementSerializer: DbStatementSerializer;
//# sourceMappingURL=index.d.ts.map