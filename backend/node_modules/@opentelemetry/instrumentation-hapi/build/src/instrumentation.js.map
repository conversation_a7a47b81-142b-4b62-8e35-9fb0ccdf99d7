{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAC1C,8CAA8D;AAC9D,oEAKwC;AAIxC,uCAAoC;AACpC,qDAU0B;AAC1B,mCAQiB;AAEjB,6CAA6C;AAC7C,MAAa,mBAAoB,SAAQ,qCAAmB;IAC1D,YAAY,MAA8B;QACxC,KAAK,CAAC,qCAAqC,EAAE,iBAAO,EAAE,MAAM,CAAC,CAAC;IAChE,CAAC;IAES,IAAI;QACZ,OAAO,IAAI,qDAAmC,CAC5C,kCAAiB,EACjB,CAAC,UAAU,CAAC,EACZ,aAAa,CAAC,EAAE;YACd,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,EAAE;gBACpC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBACvC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aACtE;YAED,8EAA8E;YAC9E,sFAAsF;YACtF,6EAA6E;YAC7E,sFAAsF;YACtF,mFAAmF;YACnF,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,EAAE;gBACpC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBACvC,IAAI,CAAC,KAAK;gBACR,8DAA8D;gBAC9D,aAAoB,EACpB,QAAQ,EACR,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC;aACH;YACD,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;YACd,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAClC,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC1D,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACK,eAAe,CACrB,QAAuD;QAEvD,MAAM,eAAe,GAAwB,IAAI,CAAC;QAClD,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,SAAS,MAAM,CAAoB,IAAyB;YACjE,MAAM,SAAS,GAAgB,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;YAE5D,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,EAAE;gBAC9C,OAAO,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAC/D,cAAc,CACf,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,6EAA6E;YAC7E,wEAAwE;YACxE,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,EAAE,kBAAkB,CAAC,EAAE;gBAChD,OAAO,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC;gBAC7D,8DAA8D;gBAC9D,kBAAyB,CAC1B,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,yFAAyF;YACzF,oFAAoF;YACpF,IAAI,CAAC,KAAK,CACR,SAAS,EACT,UAAU;YACV,8DAA8D;YAC9D,eAAe,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAQ,CACrE,CAAC;YACF,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACK,uBAAuB,CAC7B,QAA6B;QAE7B,MAAM,eAAe,GAAwB,IAAI,CAAC;QAClD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;QACzD,OAAO,SAAS,QAAQ,CAEtB,WAA+B,EAC/B,OAAoC;;YAEpC,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBAC9B,KAAK,MAAM,SAAS,IAAI,WAAW,EAAE;oBACnC,eAAe,CAAC,oBAAoB,CAClC,MAAA,MAAA,MAAA,SAAS,CAAC,MAAM,0CAAE,MAAM,mCAAI,SAAS,CAAC,MAAM,mCAAI,SAAS,CAC1D,CAAC;iBACH;aACF;iBAAM;gBACL,eAAe,CAAC,oBAAoB,CAClC,MAAA,MAAA,MAAA,WAAW,CAAC,MAAM,0CAAE,MAAM,mCAAI,WAAW,CAAC,MAAM,mCAAI,WAAW,CAChE,CAAC;aACH;YACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACK,kBAAkB,CACxB,QAAyC,EACzC,UAAmB;QAEnB,MAAM,eAAe,GAAwB,IAAI,CAAC;QAClD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAEpD,OAAO,SAAS,GAAG,CAEjB,GAAG,IAAiC;YAEpC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC1B,MAAM,UAAU,GAE0B,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC1C,MAAM,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;oBAC/B,IAAI,IAAA,0BAAkB,EAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;wBACrC,MAAM,iBAAiB,GACrB,QAA6C,CAAC;wBAChD,MAAM,OAAO,GAAG,eAAe,CAAC,eAAe,CAC7C,iBAAiB,CAAC,MAAM,EACxB,QAAQ,CAAC,IAAI,EACb,UAAU,CACX,CAAC;wBACF,iBAAiB,CAAC,MAAM,GAAG,OAAO,CAAC;wBACnC,UAAU,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC;qBACnC;iBACF;gBACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACnC;iBAAM,IAAI,IAAA,wBAAgB,EAAC,IAAI,CAAC,EAAE;gBACjC,MAAM,QAAQ,GAAyB,IAAI,CAAC;gBAC5C,MAAM,MAAM,GAAuB,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC/C,MAAM,OAAO,GAAG,eAAe,CAAC,eAAe,CAC7C,MAAM,EACN,QAAQ,CAAC,CAAC,CAAC,EACX,UAAU,CACX,CAAC;gBACF,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAClE;iBAAM,IAAI,IAAA,8BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC1C,MAAM,iBAAiB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,OAAO,GAAG,eAAe,CAAC,eAAe,CAC7C,iBAAiB,CAAC,MAAM,EACxB,iBAAiB,CAAC,IAAI,EACtB,UAAU,CACX,CAAC;gBACF,iBAAiB,CAAC,MAAM,GAAG,OAAO,CAAC;gBACnC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;aAC/C;YACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACK,oBAAoB,CAC1B,QAAoC,EACpC,UAAmB;QAEnB,MAAM,eAAe,GAAwB,IAAI,CAAC;QAClD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACtD,OAAO,SAAS,KAAK,CAEnB,KAA2B;YAE3B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACrC,MAAM,QAAQ,GAAG,eAAe,CAAC,iBAAiB,CAAC,IAAI,CACrD,eAAe,EACf,KAAK,CAAC,CAAC,CAAC,EACR,UAAU,CACX,CAAC;oBACF,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;iBACrB;aACF;iBAAM;gBACL,KAAK,GAAG,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAC5C,eAAe,EACf,KAAK,EACL,UAAU,CACX,CAAC;aACH;YACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACK,oBAAoB,CAAI,MAAsB;QACpD,MAAM,eAAe,GAAwB,IAAI,CAAC;QAClD,MAAM,UAAU,GAAG,IAAA,qBAAa,EAAC,MAAM,CAAC,CAAC;QACzC,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,MAAM,kBAAkB,GAAG,UAAU,MAAmB,EAAE,OAAU;YAClE,MAAM,CAAC,KAAK,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE;gBACrC,OAAO,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAC/D,QAAQ,EACR,UAAU,CACX,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,6EAA6E;YAC7E,wEAAwE;YACxE,sBAAsB;YACtB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,kBAAkB,CAAC,EAAE;gBAC7C,OAAO,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC;gBAC7D,8DAA8D;gBAC9D,kBAAyB,EACzB,UAAU,CACX,CAAC;YACJ,CAAC,CAAC,CAAC;YACH,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC;QACF,MAAM,CAAC,QAAQ,GAAG,kBAAkB,CAAC;IACvC,CAAC;IAED;;;;;;;;;;OAUG;IACK,eAAe,CACrB,MAAS,EACT,QAAmC,EACnC,UAAmB;QAEnB,MAAM,eAAe,GAAwB,IAAI,CAAC;QAElD,IAAI,MAAM,YAAY,KAAK,EAAE;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,MAAM,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,eAAe,CACzC,MAAM,CAAC,CAAC,CAAC,EACT,QAAQ,CACa,CAAC;aACzB;YACD,OAAO,MAAM,CAAC;SACf;aAAM,IAAI,IAAA,4BAAoB,EAAC,MAAM,CAAC,EAAE;YACvC,IAAI,MAAM,CAAC,+BAAc,CAAC,KAAK,IAAI;gBAAE,OAAO,MAAM,CAAC;YACnD,MAAM,CAAC,+BAAc,CAAC,GAAG,IAAI,CAAC;YAE9B,MAAM,UAAU,GAAuB,KAAK,WAC1C,GAAG,MAAyC;gBAE5C,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,EAAE;oBACzD,OAAO,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;iBACzC;gBACD,MAAM,QAAQ,GAAG,IAAA,sBAAc,EAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBACtD,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE;oBAC3D,UAAU,EAAE,QAAQ,CAAC,UAAU;iBAChC,CAAC,CAAC;gBACH,IAAI;oBACF,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,CAI3B,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAC7C,MAAM,EACN,SAAS,EACT,GAAG,MAAM,CACV,CAAC;iBACH;gBAAC,OAAO,GAAQ,EAAE;oBACjB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;oBAC1B,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;wBAC9B,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;oBACH,MAAM,GAAG,CAAC;iBACX;wBAAS;oBACR,IAAI,CAAC,GAAG,EAAE,CAAC;iBACZ;YACH,CAAC,CAAC;YACF,OAAO,UAAe,CAAC;SACxB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACK,iBAAiB,CACvB,KAA2B,EAC3B,UAAmB;;QAEnB,MAAM,eAAe,GAAwB,IAAI,CAAC;QAClD,IAAI,KAAK,CAAC,+BAAc,CAAC,KAAK,IAAI;YAAE,OAAO,KAAK,CAAC;QACjD,KAAK,CAAC,+BAAc,CAAC,GAAG,IAAI,CAAC;QAC7B,MAAM,UAAU,GAAG,MAAA,MAAA,KAAK,CAAC,OAAO,0CAAE,OAAO,mCAAI,KAAK,CAAC,OAAO,CAAC;QAC3D,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;YACpC,MAAM,UAAU,GAA0B,KAAK,WAC7C,GAAG,MAAyC;gBAE5C,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,EAAE;oBACzD,OAAO,MAAM,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC;iBACpC;gBACD,MAAM,WAAW,GAAG,IAAA,qBAAc,EAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACzD,IAAI,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,MAAK,cAAO,CAAC,IAAI,EAAE;oBACtC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;iBAChC;gBACD,MAAM,QAAQ,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACrD,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE;oBAC3D,UAAU,EAAE,QAAQ,CAAC,UAAU;iBAChC,CAAC,CAAC;gBACH,IAAI;oBACF,OAAO,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,CAC3B,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAC7C,GAAG,EAAE,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,CAC5B,CAAC;iBACH;gBAAC,OAAO,GAAQ,EAAE;oBACjB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;oBAC1B,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;wBAC9B,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;oBACH,MAAM,GAAG,CAAC;iBACX;wBAAS;oBACR,IAAI,CAAC,GAAG,EAAE,CAAC;iBACZ;YACH,CAAC,CAAC;YACF,IAAI,MAAA,KAAK,CAAC,OAAO,0CAAE,OAAO,EAAE;gBAC1B,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC;aACpC;iBAAM;gBACL,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC;aAC5B;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAlXD,kDAkXC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { getRPCMetadata, RPCType } from '@opentelemetry/core';\nimport {\n  InstrumentationBase,\n  InstrumentationConfig,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n} from '@opentelemetry/instrumentation';\n\n// types for @hapi/hapi are published under @types/hapi__hapi\nimport type * as Hapi from 'hapi__hapi';\nimport { VERSION } from './version';\nimport {\n  HapiComponentName,\n  HapiServerRouteInput,\n  handlerPatched,\n  PatchableServerRoute,\n  HapiServerRouteInputMethod,\n  HapiPluginInput,\n  RegisterFunction,\n  PatchableExtMethod,\n  ServerExtDirectInput,\n} from './internal-types';\nimport {\n  getRouteMetadata,\n  getPluginName,\n  isLifecycleExtType,\n  isLifecycleExtEventObj,\n  getExtMetadata,\n  isDirectExtInput,\n  isPatchableExtMethod,\n} from './utils';\n\n/** Hapi instrumentation for OpenTelemetry */\nexport class HapiInstrumentation extends InstrumentationBase {\n  constructor(config?: InstrumentationConfig) {\n    super('@opentelemetry/instrumentation-hapi', VERSION, config);\n  }\n\n  protected init() {\n    return new InstrumentationNodeModuleDefinition<typeof Hapi>(\n      HapiComponentName,\n      ['>=17 <21'],\n      moduleExports => {\n        if (!isWrapped(moduleExports.server)) {\n          api.diag.debug('Patching Hapi.server');\n          this._wrap(moduleExports, 'server', this._getServerPatch.bind(this));\n        }\n\n        // Casting as any is necessary here due to an issue with the @types/hapi__hapi\n        // type definition for Hapi.Server. Hapi.Server (note the uppercase) can also function\n        // as a factory function, similarly to Hapi.server (lowercase), and so should\n        // also be supported and instrumented. This is an issue with the DefinitelyTyped repo.\n        // Function is defined at: https://github.com/hapijs/hapi/blob/main/lib/index.js#L9\n        if (!isWrapped(moduleExports.Server)) {\n          api.diag.debug('Patching Hapi.Server');\n          this._wrap(\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            moduleExports as any,\n            'Server',\n            this._getServerPatch.bind(this)\n          );\n        }\n        return moduleExports;\n      },\n      moduleExports => {\n        api.diag.debug('Unpatching Hapi');\n        this._massUnwrap([moduleExports], ['server', 'Server']);\n      }\n    );\n  }\n\n  /**\n   * Patches the Hapi.server and Hapi.Server functions in order to instrument\n   * the server.route, server.ext, and server.register functions via calls to the\n   * @function _getServerRoutePatch, @function _getServerExtPatch, and\n   * @function _getServerRegisterPatch functions\n   * @param original - the original Hapi Server creation function\n   */\n  private _getServerPatch(\n    original: (options?: Hapi.ServerOptions) => Hapi.Server\n  ) {\n    const instrumentation: HapiInstrumentation = this;\n    const self = this;\n    return function server(this: Hapi.Server, opts?: Hapi.ServerOptions) {\n      const newServer: Hapi.Server = original.apply(this, [opts]);\n\n      self._wrap(newServer, 'route', originalRouter => {\n        return instrumentation._getServerRoutePatch.bind(instrumentation)(\n          originalRouter\n        );\n      });\n\n      // Casting as any is necessary here due to multiple overloads on the Hapi.ext\n      // function, which requires supporting a variety of different parameters\n      // as extension inputs\n      self._wrap(newServer, 'ext', originalExtHandler => {\n        return instrumentation._getServerExtPatch.bind(instrumentation)(\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          originalExtHandler as any\n        );\n      });\n\n      // Casting as any is necessary here due to multiple overloads on the Hapi.Server.register\n      // function, which requires supporting a variety of different types of Plugin inputs\n      self._wrap(\n        newServer,\n        'register',\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        instrumentation._getServerRegisterPatch.bind(instrumentation) as any\n      );\n      return newServer;\n    };\n  }\n\n  /**\n   * Patches the plugin register function used by the Hapi Server. This function\n   * goes through each plugin that is being registered and adds instrumentation\n   * via a call to the @function _wrapRegisterHandler function.\n   * @param {RegisterFunction<T>} original - the original register function which\n   * registers each plugin on the server\n   */\n  private _getServerRegisterPatch<T>(\n    original: RegisterFunction<T>\n  ): RegisterFunction<T> {\n    const instrumentation: HapiInstrumentation = this;\n    api.diag.debug('Patching Hapi.Server register function');\n    return function register(\n      this: Hapi.Server,\n      pluginInput: HapiPluginInput<T>,\n      options?: Hapi.ServerRegisterOptions\n    ) {\n      if (Array.isArray(pluginInput)) {\n        for (const pluginObj of pluginInput) {\n          instrumentation._wrapRegisterHandler(\n            pluginObj.plugin?.plugin ?? pluginObj.plugin ?? pluginObj\n          );\n        }\n      } else {\n        instrumentation._wrapRegisterHandler(\n          pluginInput.plugin?.plugin ?? pluginInput.plugin ?? pluginInput\n        );\n      }\n      return original.apply(this, [pluginInput, options]);\n    };\n  }\n\n  /**\n   * Patches the Server.ext function which adds extension methods to the specified\n   * point along the request lifecycle. This function accepts the full range of\n   * accepted input into the standard Hapi `server.ext` function. For each extension,\n   * it adds instrumentation to the handler via a call to the @function _wrapExtMethods\n   * function.\n   * @param original - the original ext function which adds the extension method to the server\n   * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n   * for adding this server extension. Else, signifies that the extension was added directly\n   */\n  private _getServerExtPatch(\n    original: (...args: unknown[]) => unknown,\n    pluginName?: string\n  ) {\n    const instrumentation: HapiInstrumentation = this;\n    api.diag.debug('Patching Hapi.Server ext function');\n\n    return function ext(\n      this: ThisParameterType<typeof original>,\n      ...args: Parameters<typeof original>\n    ) {\n      if (Array.isArray(args[0])) {\n        const eventsList:\n          | Hapi.ServerExtEventsObject[]\n          | Hapi.ServerExtEventsRequestObject[] = args[0];\n        for (let i = 0; i < eventsList.length; i++) {\n          const eventObj = eventsList[i];\n          if (isLifecycleExtType(eventObj.type)) {\n            const lifecycleEventObj =\n              eventObj as Hapi.ServerExtEventsRequestObject;\n            const handler = instrumentation._wrapExtMethods(\n              lifecycleEventObj.method,\n              eventObj.type,\n              pluginName\n            );\n            lifecycleEventObj.method = handler;\n            eventsList[i] = lifecycleEventObj;\n          }\n        }\n        return original.apply(this, args);\n      } else if (isDirectExtInput(args)) {\n        const extInput: ServerExtDirectInput = args;\n        const method: PatchableExtMethod = extInput[1];\n        const handler = instrumentation._wrapExtMethods(\n          method,\n          extInput[0],\n          pluginName\n        );\n        return original.apply(this, [extInput[0], handler, extInput[2]]);\n      } else if (isLifecycleExtEventObj(args[0])) {\n        const lifecycleEventObj = args[0];\n        const handler = instrumentation._wrapExtMethods(\n          lifecycleEventObj.method,\n          lifecycleEventObj.type,\n          pluginName\n        );\n        lifecycleEventObj.method = handler;\n        return original.call(this, lifecycleEventObj);\n      }\n      return original.apply(this, args);\n    };\n  }\n\n  /**\n   * Patches the Server.route function. This function accepts either one or an array\n   * of Hapi.ServerRoute objects and adds instrumentation on each route via a call to\n   * the @function _wrapRouteHandler function.\n   * @param {HapiServerRouteInputMethod} original - the original route function which adds\n   * the route to the server\n   * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n   * for adding this server route. Else, signifies that the route was added directly\n   */\n  private _getServerRoutePatch(\n    original: HapiServerRouteInputMethod,\n    pluginName?: string\n  ) {\n    const instrumentation: HapiInstrumentation = this;\n    api.diag.debug('Patching Hapi.Server route function');\n    return function route(\n      this: Hapi.Server,\n      route: HapiServerRouteInput\n    ): void {\n      if (Array.isArray(route)) {\n        for (let i = 0; i < route.length; i++) {\n          const newRoute = instrumentation._wrapRouteHandler.call(\n            instrumentation,\n            route[i],\n            pluginName\n          );\n          route[i] = newRoute;\n        }\n      } else {\n        route = instrumentation._wrapRouteHandler.call(\n          instrumentation,\n          route,\n          pluginName\n        );\n      }\n      return original.apply(this, [route]);\n    };\n  }\n\n  /**\n   * Wraps newly registered plugins to add instrumentation to the plugin's clone of\n   * the original server. Specifically, wraps the server.route and server.ext functions\n   * via calls to @function _getServerRoutePatch and @function _getServerExtPatch\n   * @param {Hapi.Plugin<T>} plugin - the new plugin which is being instrumented\n   */\n  private _wrapRegisterHandler<T>(plugin: Hapi.Plugin<T>): void {\n    const instrumentation: HapiInstrumentation = this;\n    const pluginName = getPluginName(plugin);\n    const oldHandler = plugin.register;\n    const self = this;\n    const newRegisterHandler = function (server: Hapi.Server, options: T) {\n      server.route;\n      self._wrap(server, 'route', original => {\n        return instrumentation._getServerRoutePatch.bind(instrumentation)(\n          original,\n          pluginName\n        );\n      });\n\n      // Casting as any is necessary here due to multiple overloads on the Hapi.ext\n      // function, which requires supporting a variety of different parameters\n      // as extension inputs\n      self._wrap(server, 'ext', originalExtHandler => {\n        return instrumentation._getServerExtPatch.bind(instrumentation)(\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          originalExtHandler as any,\n          pluginName\n        );\n      });\n      return oldHandler(server, options);\n    };\n    plugin.register = newRegisterHandler;\n  }\n\n  /**\n   * Wraps request extension methods to add instrumentation to each new extension handler.\n   * Patches each individual extension in order to create the\n   * span and propagate context. It does not create spans when there is no parent span.\n   * @param {PatchableExtMethod | PatchableExtMethod[]} method - the request extension\n   * handler which is being instrumented\n   * @param {Hapi.ServerRequestExtType} extPoint - the point in the Hapi request lifecycle\n   * which this extension targets\n   * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n   * for adding this server route. Else, signifies that the route was added directly\n   */\n  private _wrapExtMethods<T extends PatchableExtMethod | PatchableExtMethod[]>(\n    method: T,\n    extPoint: Hapi.ServerRequestExtType,\n    pluginName?: string\n  ): T {\n    const instrumentation: HapiInstrumentation = this;\n\n    if (method instanceof Array) {\n      for (let i = 0; i < method.length; i++) {\n        method[i] = instrumentation._wrapExtMethods(\n          method[i],\n          extPoint\n        ) as PatchableExtMethod;\n      }\n      return method;\n    } else if (isPatchableExtMethod(method)) {\n      if (method[handlerPatched] === true) return method;\n      method[handlerPatched] = true;\n\n      const newHandler: PatchableExtMethod = async function (\n        ...params: Parameters<Hapi.Lifecycle.Method>\n      ) {\n        if (api.trace.getSpan(api.context.active()) === undefined) {\n          return await method.apply(this, params);\n        }\n        const metadata = getExtMetadata(extPoint, pluginName);\n        const span = instrumentation.tracer.startSpan(metadata.name, {\n          attributes: metadata.attributes,\n        });\n        try {\n          return await api.context.with<\n            Parameters<Hapi.Lifecycle.Method>,\n            Hapi.Lifecycle.Method\n          >(\n            api.trace.setSpan(api.context.active(), span),\n            method,\n            undefined,\n            ...params\n          );\n        } catch (err: any) {\n          span.recordException(err);\n          span.setStatus({\n            code: api.SpanStatusCode.ERROR,\n            message: err.message,\n          });\n          throw err;\n        } finally {\n          span.end();\n        }\n      };\n      return newHandler as T;\n    }\n    return method;\n  }\n\n  /**\n   * Patches each individual route handler method in order to create the\n   * span and propagate context. It does not create spans when there is no parent span.\n   * @param {PatchableServerRoute} route - the route handler which is being instrumented\n   * @param {string} [pluginName] - if present, represents the name of the plugin responsible\n   * for adding this server route. Else, signifies that the route was added directly\n   */\n  private _wrapRouteHandler(\n    route: PatchableServerRoute,\n    pluginName?: string\n  ): PatchableServerRoute {\n    const instrumentation: HapiInstrumentation = this;\n    if (route[handlerPatched] === true) return route;\n    route[handlerPatched] = true;\n    const oldHandler = route.options?.handler ?? route.handler;\n    if (typeof oldHandler === 'function') {\n      const newHandler: Hapi.Lifecycle.Method = async function (\n        ...params: Parameters<Hapi.Lifecycle.Method>\n      ) {\n        if (api.trace.getSpan(api.context.active()) === undefined) {\n          return await oldHandler(...params);\n        }\n        const rpcMetadata = getRPCMetadata(api.context.active());\n        if (rpcMetadata?.type === RPCType.HTTP) {\n          rpcMetadata.route = route.path;\n        }\n        const metadata = getRouteMetadata(route, pluginName);\n        const span = instrumentation.tracer.startSpan(metadata.name, {\n          attributes: metadata.attributes,\n        });\n        try {\n          return await api.context.with(\n            api.trace.setSpan(api.context.active(), span),\n            () => oldHandler(...params)\n          );\n        } catch (err: any) {\n          span.recordException(err);\n          span.setStatus({\n            code: api.SpanStatusCode.ERROR,\n            message: err.message,\n          });\n          throw err;\n        } finally {\n          span.end();\n        }\n      };\n      if (route.options?.handler) {\n        route.options.handler = newHandler;\n      } else {\n        route.handler = newHandler;\n      }\n    }\n    return route;\n  }\n}\n"]}