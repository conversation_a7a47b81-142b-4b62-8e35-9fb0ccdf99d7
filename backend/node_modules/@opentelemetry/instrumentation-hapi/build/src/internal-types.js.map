{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKU,QAAA,iBAAiB,GAAG,YAAY,CAAC;AAE9C;;;;;GAKG;AACU,QAAA,cAAc,GAAkB,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAsC/D,QAAA,aAAa,GAAG;IAC3B,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;IAChB,GAAG,EAAE,YAAY;CAClB,CAAC;AAEW,QAAA,wBAAwB,GAAG,IAAI,GAAG,CAAC;IAC9C,WAAW;IACX,eAAe;IACf,YAAY;IACZ,cAAc;IACd,eAAe;IACf,eAAe;IACf,WAAW;CACZ,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// types for @hapi/hapi are published under @types/hapi__hapi\nimport type * as Hapi from 'hapi__hapi';\n\nexport const HapiComponentName = '@hapi/hapi';\n\n/**\n * This symbol is used to mark a Hapi route handler or server extension handler as\n * already patched, since its possible to use these handlers multiple times\n * i.e. when allowing multiple versions of one plugin, or when registering a plugin\n * multiple times on different servers.\n */\nexport const handlerPatched: unique symbol = Symbol('hapi-handler-patched');\n\nexport type HapiServerRouteInputMethod = (route: HapiServerRouteInput) => void;\n\nexport type HapiServerRouteInput =\n  | PatchableServerRoute\n  | PatchableServerRoute[];\n\nexport type PatchableServerRoute = Hapi.ServerRoute<any> & {\n  [handlerPatched]?: boolean;\n  options?: {\n    handler?: Hapi.Lifecycle.Method;\n  };\n};\n\nexport type HapiPluginObject<T> = Hapi.ServerRegisterPluginObject<T> & {\n  plugin: Hapi.ServerRegisterPluginObject<T>;\n};\n\nexport type HapiPluginInput<T> =\n  | HapiPluginObject<T>\n  | Array<HapiPluginObject<T>>;\n\nexport type RegisterFunction<T> = (\n  plugin: HapiPluginInput<T>,\n  options?: Hapi.ServerRegisterOptions\n) => Promise<void>;\n\nexport type PatchableExtMethod = Hapi.Lifecycle.Method & {\n  [handlerPatched]?: boolean;\n};\n\nexport type ServerExtDirectInput = [\n  Hapi.ServerRequestExtType,\n  Hapi.Lifecycle.Method,\n  (Hapi.ServerExtOptions | undefined)?\n];\n\nexport const HapiLayerType = {\n  ROUTER: 'router',\n  PLUGIN: 'plugin',\n  EXT: 'server.ext',\n};\n\nexport const HapiLifecycleMethodNames = new Set([\n  'onPreAuth',\n  'onCredentials',\n  'onPostAuth',\n  'onPreHandler',\n  'onPostHandler',\n  'onPreResponse',\n  'onRequest',\n]);\n"]}