"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.unrefTimer = exports.otperformance = exports.hexToBase64 = exports.getEnvWithoutDefaults = exports.getEnv = exports._globalThis = exports.SDK_INFO = exports.RandomIdGenerator = void 0;
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var node_1 = require("./node");
Object.defineProperty(exports, "RandomIdGenerator", { enumerable: true, get: function () { return node_1.RandomIdGenerator; } });
Object.defineProperty(exports, "SDK_INFO", { enumerable: true, get: function () { return node_1.SDK_INFO; } });
Object.defineProperty(exports, "_globalThis", { enumerable: true, get: function () { return node_1._globalThis; } });
Object.defineProperty(exports, "getEnv", { enumerable: true, get: function () { return node_1.getEnv; } });
Object.defineProperty(exports, "getEnvWithoutDefaults", { enumerable: true, get: function () { return node_1.getEnvWithoutDefaults; } });
Object.defineProperty(exports, "hexToBase64", { enumerable: true, get: function () { return node_1.hexToBase64; } });
Object.defineProperty(exports, "otperformance", { enumerable: true, get: function () { return node_1.otperformance; } });
Object.defineProperty(exports, "unrefTimer", { enumerable: true, get: function () { return node_1.unrefTimer; } });
//# sourceMappingURL=index.js.map