{"name": "@opentelemetry/exporter-jaeger", "version": "1.30.1", "description": "OpenTelemetry Exporter Jaeger allows user to send collected traces to Jaeger", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build", "clean": "tsc --build --clean", "test": "nyc mocha 'test/**/*.test.ts'", "tdd": "npm run test -- --watch-extensions ts --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../scripts/version-update.js", "watch": "tsc --build --watch", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../scripts/peer-api-check.js", "align-api-deps": "node ../../scripts/align-api-deps.js"}, "keywords": ["opentelemetry", "nodejs", "tracing", "profiling", "jaeger"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": "^1.0.0", "@opentelemetry/resources": "1.30.1", "@types/mocha": "10.0.10", "@types/node": "18.6.5", "@types/sinon": "17.0.3", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "10.8.2", "nock": "13.3.8", "nyc": "15.1.0", "sinon": "15.1.2", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}, "dependencies": {"@opentelemetry/core": "1.30.1", "@opentelemetry/sdk-trace-base": "1.30.1", "@opentelemetry/semantic-conventions": "1.28.0", "jaeger-client": "^3.15.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/packages/opentelemetry-exporter-jaeger", "sideEffects": false, "gitHead": "cbc912d67bda462ca00449d7ce7b80052c20a4fc"}