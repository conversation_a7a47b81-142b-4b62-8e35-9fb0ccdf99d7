{"version": 3, "file": "jaeger.js", "sourceRoot": "", "sources": ["../../src/jaeger.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAC1C,8CAK6B;AAE7B,iCAA+B;AAC/B,8EAA+E;AAC/E,2CAA2C;AAC3C,uCAAuC;AAEvC;;;;;;;;;;;;GAYG;AACH,MAAa,cAAc;IAOzB,YAAY,MAAmC;QAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,uBAAuB;YAC1B,OAAO,WAAW,CAAC,YAAY,KAAK,QAAQ;gBAC1C,CAAC,CAAC,WAAW,CAAC,YAAY;gBAC1B,CAAC,CAAC,IAAI,CAAC;QAEX,4EAA4E;QAC5E,sHAAsH;QACtH,kIAAkI;QAClI,kJAAkJ;QAClJ,+IAA+I;QAE/I,MAAM,GAAG,GAAG,IAAA,aAAM,GAAE,CAAC;QACrB,WAAW,CAAC,QAAQ;YAClB,WAAW,CAAC,QAAQ,IAAI,GAAG,CAAC,6BAA6B,CAAC;QAC5D,WAAW,CAAC,QAAQ;YAClB,WAAW,CAAC,QAAQ,IAAI,GAAG,CAAC,yBAAyB,CAAC;QACxD,WAAW,CAAC,QAAQ;YAClB,WAAW,CAAC,QAAQ,IAAI,GAAG,CAAC,6BAA6B,CAAC;QAC5D,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,IAAI,GAAG,CAAC,+BAA+B,CAAC;QAC3E,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,IAAI,GAAG,CAAC,+BAA+B,CAAC;QAE3E,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAEhC,IAAI,CAAC,aAAa,GAAG,IAAI,qBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAED,yCAAyC;IACzC,MAAM,CACJ,KAAqB,EACrB,cAA8C;QAE9C,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QACD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,cAAc,CAAC,EAAE,IAAI,EAAE,uBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;SAC3D;QACD,UAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACnD,OAAO,cAAc,CAAC,EAAE,IAAI,EAAE,uBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IACzB,QAAQ;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;IACvB,CAAC;IAEO,SAAS;QACf,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,IAAI,OAAO,CAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;gBACrC,UAAU,CACR,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,EACxC,IAAI,CAAC,uBAAuB,CAC7B,CAAC;YACJ,CAAC,CAAC;YACF,IAAI,CAAC,MAAM,EAAE;SACd,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;;YACd,MAAA,IAAI,CAAC,OAAO,0CAAE,KAAK,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mDAAmD;IAC3C,KAAK,CAAC,UAAU,CACtB,KAAqB,EACrB,IAAqC;QAErC,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAA,wBAAY,EAAC,IAAI,CAAC,CAAC,CAAC;QACzD,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE;YAC7B,IAAI;gBACF,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC1B;YAAC,OAAO,KAAK,EAAE;gBACd,iEAAiE;gBACjE,IAAI,IAAI;oBAAE,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,uBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;aACjE;SACF;QACD,UAAI,CAAC,KAAK,CAAC,2BAA2B,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;QAE3D,gEAAgE;QAChE,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QAEpB,IAAI,IAAI;YAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,uBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9E,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,IAA4B;QAChD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,KAAa,EAAE,GAAY,EAAE,EAAE;gBACjE,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC/B;gBACD,OAAO,CAAC,KAAK,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,UAAU,CAChB,IAA4B;QAE5B,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,IAAI,CAAC,OAAO,CAAC;SACrB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ;YACvC,CAAC,CAAC,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC;YAC/C,CAAC,CAAC,IAAI,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEjD,IAAI,MAAM,CAAC,OAAO,YAAY,cAAM,EAAE;YACpC,8DAA8D;YAC9D,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;SACxB;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CACnC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,+CAAwB,CACxC,CAAC;QACF,MAAM,WAAW,GAAG,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,IAAI,KAAI,iBAAiB,CAAC;QAE9D,MAAM,CAAC,UAAU,CAAC;YAChB,WAAW;YACX,IAAI,EAAE,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,CAAC;SAC1E,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,MAAM;QAClB,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,OAAO,OAAO,EAAE,CAAC;aAClB;YAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAc,EAAE,GAAY,EAAE,EAAE;gBAClD,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC/B;gBACD,UAAI,CAAC,KAAK,CAAC,wBAAwB,MAAM,QAAQ,CAAC,CAAC;gBACnD,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA5JD,wCA4JC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  BindOnceFuture,\n  ExportResult,\n  ExportResultCode,\n  getEnv,\n} from '@opentelemetry/core';\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport { Socket } from 'dgram';\nimport { SEMRESATTRS_SERVICE_NAME } from '@opentelemetry/semantic-conventions';\nimport { spanToThrift } from './transform';\nimport * as jaegerTypes from './types';\n\n/**\n * Format and sends span information to Jaeger Exporter.\n *\n * @deprecated <PERSON><PERSON><PERSON> supports the OpenTelemetry protocol natively\n * (see https://www.jaegertracing.io/docs/1.41/apis/#opentelemetry-protocol-stable).\n * This exporter will not be required by the OpenTelemetry specification starting July 2023, and\n * will not receive any security fixes past March 2024.\n *\n * Please migrate to any of the following packages:\n * - `@opentelemetry/exporter-trace-otlp-proto`\n * - `@opentelemetry/exporter-trace-otlp-grpc`\n * - `@opentelemetry/exporter-trace-otlp-http`\n */\nexport class JaegerExporter implements SpanExporter {\n  private readonly _onShutdownFlushTimeout: number;\n  private readonly _localConfig: jaegerTypes.ExporterConfig;\n  private _shutdownOnce: BindOnceFuture<void>;\n\n  private _sender?: typeof jaegerTypes.UDPSender;\n\n  constructor(config?: jaegerTypes.ExporterConfig) {\n    const localConfig = Object.assign({}, config);\n    this._onShutdownFlushTimeout =\n      typeof localConfig.flushTimeout === 'number'\n        ? localConfig.flushTimeout\n        : 2000;\n\n    // https://github.com/jaegertracing/jaeger-client-node#environment-variables\n    // By default, the client sends traces via UDP to the agent at localhost:6832. Use OTEL_EXPORTER_JAEGER_AGENT_HOST and\n    // JAEGER_AGENT_PORT to send UDP traces to a different host:port. If OTEL_EXPORTER_JAEGER_ENDPOINT is set, the client sends traces\n    // to the endpoint via HTTP, making the OTEL_EXPORTER_JAEGER_AGENT_HOST and JAEGER_AGENT_PORT unused. If OTEL_EXPORTER_JAEGER_ENDPOINT is secured,\n    // HTTP basic authentication can be performed by setting the OTEL_EXPORTER_JAEGER_USER and OTEL_EXPORTER_JAEGER_PASSWORD environment variables.\n\n    const env = getEnv();\n    localConfig.endpoint =\n      localConfig.endpoint || env.OTEL_EXPORTER_JAEGER_ENDPOINT;\n    localConfig.username =\n      localConfig.username || env.OTEL_EXPORTER_JAEGER_USER;\n    localConfig.password =\n      localConfig.password || env.OTEL_EXPORTER_JAEGER_PASSWORD;\n    localConfig.host = localConfig.host || env.OTEL_EXPORTER_JAEGER_AGENT_HOST;\n    localConfig.port = localConfig.port || env.OTEL_EXPORTER_JAEGER_AGENT_PORT;\n\n    this._localConfig = localConfig;\n\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n  }\n\n  /** Exports a list of spans to Jaeger. */\n  export(\n    spans: ReadableSpan[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n    if (spans.length === 0) {\n      return resultCallback({ code: ExportResultCode.SUCCESS });\n    }\n    diag.debug('Jaeger exporter export');\n    this._sendSpans(spans, resultCallback).catch(error => {\n      return resultCallback({ code: ExportResultCode.FAILED, error });\n    });\n  }\n\n  /** Shutdown exporter. */\n  shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  /**\n   * Exports any pending spans in exporter\n   */\n  forceFlush(): Promise<void> {\n    return this._flush();\n  }\n\n  private _shutdown(): Promise<void> {\n    return Promise.race([\n      new Promise<void>((_resolve, reject) => {\n        setTimeout(\n          () => reject(new Error('Flush timeout')),\n          this._onShutdownFlushTimeout\n        );\n      }),\n      this._flush(),\n    ]).finally(() => {\n      this._sender?.close();\n    });\n  }\n\n  /** Transform spans and sends to Jaeger service. */\n  private async _sendSpans(\n    spans: ReadableSpan[],\n    done?: (result: ExportResult) => void\n  ) {\n    const thriftSpan = spans.map(span => spanToThrift(span));\n    for (const span of thriftSpan) {\n      try {\n        await this._append(span);\n      } catch (error) {\n        // TODO right now we break out on first error, is that desirable?\n        if (done) return done({ code: ExportResultCode.FAILED, error });\n      }\n    }\n    diag.debug(`successful append for : ${thriftSpan.length}`);\n\n    // Flush all spans on each export. No-op if span buffer is empty\n    await this._flush();\n\n    if (done) return process.nextTick(done, { code: ExportResultCode.SUCCESS });\n  }\n\n  private async _append(span: jaegerTypes.ThriftSpan): Promise<number> {\n    return new Promise((resolve, reject) => {\n      this._getSender(span).append(span, (count: number, err?: string) => {\n        if (err) {\n          return reject(new Error(err));\n        }\n        resolve(count);\n      });\n    });\n  }\n\n  private _getSender(\n    span: jaegerTypes.ThriftSpan\n  ): typeof jaegerTypes.UDPSender {\n    if (this._sender) {\n      return this._sender;\n    }\n\n    const sender = this._localConfig.endpoint\n      ? new jaegerTypes.HTTPSender(this._localConfig)\n      : new jaegerTypes.UDPSender(this._localConfig);\n\n    if (sender._client instanceof Socket) {\n      // unref socket to prevent it from keeping the process running\n      sender._client.unref();\n    }\n\n    const serviceNameTag = span.tags.find(\n      t => t.key === SEMRESATTRS_SERVICE_NAME\n    );\n    const serviceName = serviceNameTag?.vStr || 'unknown_service';\n\n    sender.setProcess({\n      serviceName,\n      tags: jaegerTypes.ThriftUtils.getThriftTags(this._localConfig.tags || []),\n    });\n\n    this._sender = sender;\n    return sender;\n  }\n\n  private async _flush(): Promise<void> {\n    await new Promise<void>((resolve, reject) => {\n      if (!this._sender) {\n        return resolve();\n      }\n\n      this._sender.flush((_count: number, err?: string) => {\n        if (err) {\n          return reject(new Error(err));\n        }\n        diag.debug(`successful flush for ${_count} spans`);\n        resolve();\n      });\n    });\n  }\n}\n"]}