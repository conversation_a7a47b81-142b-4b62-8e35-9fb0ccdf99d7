{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAqBH,6EAA6E;AAC7E,iCAAiC;AAEjC,uDAAuD;AAC1C,QAAA,SAAS,GACpB,OAAO,CAAC,6CAA6C,CAAC,CAAC,OAAO,CAAC;AACpD,QAAA,KAAK,GAAG,OAAO,CAAC,6BAA6B,CAAC,CAAC,OAAO,CAAC;AACvD,QAAA,WAAW,GAAG,OAAO,CAAC,+BAA+B,CAAC,CAAC,OAAO,CAAC;AAE/D,QAAA,UAAU,GACrB,OAAO,CAAC,8CAA8C,CAAC,CAAC,OAAO,CAAC;AAmClE,IAAY,mBAGX;AAHD,WAAY,mBAAmB;IAC7B,4CAAqB,CAAA;IACrB,oDAA6B,CAAA;AAC/B,CAAC,EAHW,mBAAmB,GAAnB,2BAAmB,KAAnB,2BAAmB,QAG9B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Options for Jaeger configuration\n */\nexport interface ExporterConfig {\n  tags?: Tag[];\n  host?: string; // default: 'localhost'\n  port?: number; // default: 6832\n  maxPacketSize?: number; // default: 65000\n  /** Time to wait for an onShutdown flush to finish before closing the sender */\n  flushTimeout?: number; // default: 2000\n  //The HTTP endpoint for sending spans directly to a collector, i.e. http://jaeger-collector:14268/api/traces\n  //If set, will override host and port\n  endpoint?: string;\n  //Username to send as part of \"Basic\" authentication to the collector endpoint\n  username?: string;\n  //Password to send as part of \"Basic\" authentication to the collector endpoint\n  password?: string;\n}\n\n// Below require is needed as jaeger-client types does not expose the thrift,\n// udp_sender, util etc. modules.\n\n/* eslint-disable @typescript-eslint/no-var-requires */\nexport const UDPSender =\n  require('jaeger-client/dist/src/reporters/udp_sender').default;\nexport const Utils = require('jaeger-client/dist/src/util').default;\nexport const ThriftUtils = require('jaeger-client/dist/src/thrift').default;\n\nexport const HTTPSender =\n  require('jaeger-client/dist/src/reporters/http_sender').default;\n/* eslint-enable @typescript-eslint/no-var-requires */\n\nexport type TagValue = string | number | boolean;\n\nexport interface Tag {\n  key: string;\n  value: TagValue;\n}\n\nexport interface Log {\n  timestamp: number;\n  fields: Tag[];\n}\n\nexport type SenderCallback = (numSpans: number, err?: string) => void;\n\nexport interface ThriftProcess {\n  serviceName: string;\n  tags: ThriftTag[];\n}\n\nexport interface ThriftTag {\n  key: string;\n  vType: string;\n  vStr: string;\n  vDouble: number;\n  vBool: boolean;\n}\n\nexport interface ThriftLog {\n  timestamp: number;\n  fields: ThriftTag[];\n}\n\nexport enum ThriftReferenceType {\n  CHILD_OF = 'CHILD_OF',\n  FOLLOWS_FROM = 'FOLLOWS_FROM',\n}\n\nexport interface ThriftReference {\n  traceIdLow: Buffer;\n  traceIdHigh: Buffer;\n  spanId: Buffer;\n  refType: ThriftReferenceType;\n}\n\nexport interface ThriftSpan {\n  traceIdLow: Buffer;\n  traceIdHigh: Buffer;\n  spanId: Buffer;\n  parentSpanId: string | Buffer;\n  operationName: string;\n  references: ThriftReference[];\n  flags: number;\n  startTime: number; // milliseconds\n  duration: number; // milliseconds\n  tags: ThriftTag[];\n  logs: ThriftLog[];\n}\n"]}