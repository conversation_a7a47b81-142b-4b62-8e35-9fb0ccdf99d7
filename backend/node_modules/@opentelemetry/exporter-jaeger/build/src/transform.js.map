{"version": 3, "file": "transform.js", "sourceRoot": "", "sources": ["../../src/transform.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAoE;AAEpE,8CAG6B;AAC7B,mCAWiB;AAEjB,MAAM,aAAa,GAAG,GAAG,CAAC;AAE1B;;;GAGG;AACH,SAAgB,YAAY,CAAC,IAAkB;IAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IAC7D,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACzC,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACrC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY;QAClC,CAAC,CAAC,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;QACtC,CAAC,CAAC,mBAAW,CAAC,WAAW,CAAC;IAE5B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAC3C,CAAC,IAAI,EAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CACzE,CAAC;IACF,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAc,CAAC,KAAK,EAAE;QAC7C,IAAI,CAAC,IAAI,CAAC;YACR,GAAG,EAAE,kBAAkB;YACvB,KAAK,EAAE,oBAAc,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;SACxC,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,yBAAyB,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;SAC3E;KACF;IACD,8EAA8E;IAC9E,eAAe;IACf,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAc,CAAC,KAAK,EAAE;QAC7C,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;KAC1C;IAED,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,cAAQ,CAAC,QAAQ,EAAE;QAC9D,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,cAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;KAC3E;IACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CACnD,IAAI,CAAC,IAAI,CAAC;QACR,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KAClD,CAAC,CACH,CAAC;IAEF,IAAI,IAAI,CAAC,sBAAsB,EAAE;QAC/B,IAAI,CAAC,IAAI,CAAC;YACR,GAAG,EAAE,mBAAmB;YACxB,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;SACpD,CAAC,CAAC;QACH,IAAI,CAAC,IAAI,CAAC;YACR,GAAG,EAAE,sBAAsB;YAC3B,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;SACvD,CAAC,CAAC;KACJ;IAED,yCAAyC;IACzC,IAAI,IAAI,CAAC,sBAAsB,EAAE;QAC/B,IAAI,CAAC,IAAI,CAAC;YACR,GAAG,EAAE,+BAA+B;YACpC,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC;SAC/C,CAAC,CAAC;KACJ;IAED,qCAAqC;IACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE;QAC3B,IAAI,CAAC,IAAI,CAAC;YACR,GAAG,EAAE,2BAA2B;YAChC,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC;SAC3C,CAAC,CAAC;KACJ;IAED,oCAAoC;IACpC,IAAI,IAAI,CAAC,iBAAiB,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC;YACR,GAAG,EAAE,0BAA0B;YAC/B,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC;SAC1C,CAAC,CAAC;KACJ;IAED,MAAM,QAAQ,GAAgB,mBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAE9D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAO,EAAE;QAC1C,MAAM,MAAM,GAAU,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;QAC/B,IAAI,KAAK,EAAE;YACT,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAChC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAC3D,CAAC;SACH;QACD,IAAI,KAAK,CAAC,sBAAsB,EAAE;YAChC,MAAM,CAAC,IAAI,CAAC;gBACV,GAAG,EAAE,qCAAqC;gBAC1C,KAAK,EAAE,KAAK,CAAC,sBAAsB;aACpC,CAAC,CAAC;SACJ;QACD,OAAO,EAAE,SAAS,EAAE,IAAA,2BAAoB,EAAC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;IACjE,CAAC,CAAC,CAAC;IACH,MAAM,QAAQ,GAAgB,mBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAE9D,OAAO;QACL,UAAU,EAAE,aAAK,CAAC,WAAW,CAAC,UAAU,CAAC;QACzC,WAAW,EAAE,aAAK,CAAC,WAAW,CAAC,WAAW,CAAC;QAC3C,MAAM,EAAE,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC;QACpD,YAAY,EAAE,UAAU;QACxB,aAAa,EAAE,IAAI,CAAC,IAAI;QACxB,UAAU,EAAE,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC;QAC7C,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,IAAI,aAAa;QACrD,SAAS,EAAE,aAAK,CAAC,WAAW,CAAC,IAAA,2BAAoB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAClE,QAAQ,EAAE,aAAK,CAAC,WAAW,CAAC,IAAA,2BAAoB,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChE,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,QAAQ;KACf,CAAC;AACJ,CAAC;AAxGD,oCAwGC;AAED,uEAAuE;AACvE,SAAS,qBAAqB,CAAC,KAAa;IAC1C,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAmB,EAAE;QACzC,MAAM,OAAO,GAAG,2BAAmB,CAAC,YAAY,CAAC;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACrC,MAAM,WAAW,GAAG,aAAK,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC5D,MAAM,UAAU,GAAG,aAAK,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QACxD,MAAM,MAAM,GAAG,aAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACtD,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;IACtD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,kEAAkE;AAClE,SAAS,UAAU,CAAC,KAAc;IAChC,MAAM,SAAS,GAAG,OAAO,KAAK,CAAC;IAC/B,IAAI,SAAS,KAAK,SAAS,EAAE;QAC3B,OAAO,KAAgB,CAAC;KACzB;SAAM,IAAI,SAAS,KAAK,QAAQ,EAAE;QACjC,OAAO,KAAe,CAAC;KACxB;IACD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Link, SpanStatusCode, SpanKind } from '@opentelemetry/api';\nimport { ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport {\n  hrTimeToMilliseconds,\n  hrTimeToMicroseconds,\n} from '@opentelemetry/core';\nimport {\n  ThriftSpan,\n  Tag,\n  Log,\n  ThriftTag,\n  ThriftLog,\n  ThriftUtils,\n  Utils,\n  ThriftReference,\n  TagValue,\n  ThriftReferenceType,\n} from './types';\n\nconst DEFAULT_FLAGS = 0x1;\n\n/**\n * Translate OpenTelemetry ReadableSpan to Jaeger Thrift Span\n * @param span Span to be translated\n */\nexport function spanToThrift(span: ReadableSpan): ThriftSpan {\n  const traceId = span.spanContext().traceId.padStart(32, '0');\n  const traceIdHigh = traceId.slice(0, 16);\n  const traceIdLow = traceId.slice(16);\n  const parentSpan = span.parentSpanId\n    ? Utils.encodeInt64(span.parentSpanId)\n    : ThriftUtils.emptyBuffer;\n\n  const tags = Object.keys(span.attributes).map(\n    (name): Tag => ({ key: name, value: toTagValue(span.attributes[name]) })\n  );\n  if (span.status.code !== SpanStatusCode.UNSET) {\n    tags.push({\n      key: 'otel.status_code',\n      value: SpanStatusCode[span.status.code],\n    });\n    if (span.status.message) {\n      tags.push({ key: 'otel.status_description', value: span.status.message });\n    }\n  }\n  // Ensure that if SpanStatus.Code is ERROR, that we set the \"error\" tag on the\n  // Jaeger span.\n  if (span.status.code === SpanStatusCode.ERROR) {\n    tags.push({ key: 'error', value: true });\n  }\n\n  if (span.kind !== undefined && span.kind !== SpanKind.INTERNAL) {\n    tags.push({ key: 'span.kind', value: SpanKind[span.kind].toLowerCase() });\n  }\n  Object.keys(span.resource.attributes).forEach(name =>\n    tags.push({\n      key: name,\n      value: toTagValue(span.resource.attributes[name]),\n    })\n  );\n\n  if (span.instrumentationLibrary) {\n    tags.push({\n      key: 'otel.library.name',\n      value: toTagValue(span.instrumentationLibrary.name),\n    });\n    tags.push({\n      key: 'otel.library.version',\n      value: toTagValue(span.instrumentationLibrary.version),\n    });\n  }\n\n  /* Add droppedAttributesCount as a tag */\n  if (span.droppedAttributesCount) {\n    tags.push({\n      key: 'otel.dropped_attributes_count',\n      value: toTagValue(span.droppedAttributesCount),\n    });\n  }\n\n  /* Add droppedEventsCount as a tag */\n  if (span.droppedEventsCount) {\n    tags.push({\n      key: 'otel.dropped_events_count',\n      value: toTagValue(span.droppedEventsCount),\n    });\n  }\n\n  /* Add droppedLinksCount as a tag */\n  if (span.droppedLinksCount) {\n    tags.push({\n      key: 'otel.dropped_links_count',\n      value: toTagValue(span.droppedLinksCount),\n    });\n  }\n\n  const spanTags: ThriftTag[] = ThriftUtils.getThriftTags(tags);\n\n  const logs = span.events.map((event): Log => {\n    const fields: Tag[] = [{ key: 'event', value: event.name }];\n    const attrs = event.attributes;\n    if (attrs) {\n      Object.keys(attrs).forEach(attr =>\n        fields.push({ key: attr, value: toTagValue(attrs[attr]) })\n      );\n    }\n    if (event.droppedAttributesCount) {\n      fields.push({\n        key: 'otel.event.dropped_attributes_count',\n        value: event.droppedAttributesCount,\n      });\n    }\n    return { timestamp: hrTimeToMilliseconds(event.time), fields };\n  });\n  const spanLogs: ThriftLog[] = ThriftUtils.getThriftLogs(logs);\n\n  return {\n    traceIdLow: Utils.encodeInt64(traceIdLow),\n    traceIdHigh: Utils.encodeInt64(traceIdHigh),\n    spanId: Utils.encodeInt64(span.spanContext().spanId),\n    parentSpanId: parentSpan,\n    operationName: span.name,\n    references: spanLinksToThriftRefs(span.links),\n    flags: span.spanContext().traceFlags || DEFAULT_FLAGS,\n    startTime: Utils.encodeInt64(hrTimeToMicroseconds(span.startTime)),\n    duration: Utils.encodeInt64(hrTimeToMicroseconds(span.duration)),\n    tags: spanTags,\n    logs: spanLogs,\n  };\n}\n\n/** Translate OpenTelemetry {@link Link}s to Jaeger ThriftReference. */\nfunction spanLinksToThriftRefs(links: Link[]): ThriftReference[] {\n  return links.map((link): ThriftReference => {\n    const refType = ThriftReferenceType.FOLLOWS_FROM;\n    const traceId = link.context.traceId;\n    const traceIdHigh = Utils.encodeInt64(traceId.slice(0, 16));\n    const traceIdLow = Utils.encodeInt64(traceId.slice(16));\n    const spanId = Utils.encodeInt64(link.context.spanId);\n    return { traceIdLow, traceIdHigh, spanId, refType };\n  });\n}\n\n/** Translate OpenTelemetry attribute value to Jaeger TagValue. */\nfunction toTagValue(value: unknown): TagValue {\n  const valueType = typeof value;\n  if (valueType === 'boolean') {\n    return value as boolean;\n  } else if (valueType === 'number') {\n    return value as number;\n  }\n  return String(value);\n}\n"]}