{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAoE;AACpE,oEAKwC;AACxC,8EAAyE;AAWzE,mCAAwE;AACxE,uCAAoC;AAEpC,MAAM,KAAK,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,CAAU,CAAC;AACtE,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAU,CAAC;AAKjD,MAAa,uBAAwB,SAAQ,qCAAmB;IAG9D,YAAY,SAAwC,EAAE;QACpD,KAAK,CAAC,yCAAyC,EAAE,iBAAO,EAAE,MAAM,CAAC,CAAC;IACpE,CAAC;IAED,IAAI;QACF,OAAO;YACL,IAAI,qDAAmC,CACrC,oBAAoB,EACpB,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,EAC/B,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;gBAC/B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,yCAAyC,aAAa,EAAE,CACzD,CAAC;gBACF,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;gBAC5B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE;wBAClC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;qBACnC;oBACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC5D,CAAC,CAAC,CAAC;gBACH,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE;wBAClC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;qBACnC;oBACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC5D,CAAC,CAAC,CAAC;gBACH,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;gBAC/B,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,yCAAyC,aAAa,EAAE,CACzD,CAAC;gBACF,CAAC,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC;YACL,CAAC,EACD;gBACE,IAAI,+CAA6B,CAG/B,oDAAoD,EACpD,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,EAC/B,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;oBAC/B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,yEAAyE,aAAa,EAAE,CACzF,CAAC;oBACF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;wBAClD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;wBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;wBACzD,IAAI,YAAY,IAAI,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE;4BACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;yBAC7D;qBACF;oBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,KAAK,EACL,IAAI,CAAC,oBAAoB,EAAE,CAC5B,CAAC;oBACF,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,SAAS,EACT,IAAI,CAAC,wBAAwB,EAAE,CAChC,CAAC;oBACF,IAAI,YAAY,IAAI,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE;wBACnD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,YAAY,EACZ,IAAI,CAAC,2BAA2B,EAAE,CACnC,CAAC;qBACH;oBACD,OAAO,aAAa,CAAC;gBACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;oBAC/B,IAAI,aAAa,KAAK,SAAS;wBAAE,OAAO;oBACxC,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,yEAAyE,aAAa,EAAE,CACzF,CAAC;oBACF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;oBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;oBACzD,IAAI,YAAY,IAAI,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE;wBACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;qBAC7D;gBACH,CAAC,CACF;aACF,CACF;SACF,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,OAAO,CAAC,IAA6B;QAClD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,IAAU,EAAE,KAAU;;QAClD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,oBAAc,CAAC,KAAK;YAC1B,OAAO,EAAE,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,mCAAI,KAAK;SACjC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB,CACzB,IAAU,EACV,MAAqC,EACrC,OAAgB;QAEhB,4DAA4D;QAC5D,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEzB,IAAI,CAAC,YAAY,CAAC,sBAAc,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACtD,IACE;YACE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS;YAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS;YAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM;SAC1B,CAAC,QAAQ,CAAC,MAAM,CAAC,EAClB;YACA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,OAAO,IAAI,MAAM;aAC3B,CAAC,CAAC;SACJ;IACH,CAAC;IAEO,oBAAoB;QAC1B,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAU,QAA+B;YAC9C,OAAO,KAAK,WAAiC,GAAG,IAAI;;gBAClD,MAAM,eAAe,GAAG,IAAI,CAC1B,iBAAiB,CACoB,CAAC;gBACxC,MAAM,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC;gBACpC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAoB,CAAC;gBACjD,MAAM,QAAQ,GAAG,MAAA,OAAO,CAAC,QAAQ,CAAC,IAAI,CACpC,IAAI,CAAC,EAAE,WAAC,OAAA,CAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,0CAAE,EAAE,MAAK,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA,EAAA,CACpD,0CAAE,QAA6B,CAAC;gBAEjC,OAAO,eAAe,CAAC,MAAM,CAAC,eAAe,CAC3C,YAAY,OAAO,CAAC,IAAI,eAAe,MAAM,CAAC,IAAI,EAAE,EACpD;oBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,EAAE;wBACV,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,eAAe,CAAC,GAAG;wBACvD,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;wBACxD,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,IAAI;wBACjD,CAAC,yCAAkB,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,IAAI;wBACjD,CAAC,sBAAc,CAAC,YAAY,CAAC,EAAE,uBAAuB,CAAC,OAAO,CAC5D,OAAO,CAAC,IAAI,CACb;wBACD,CAAC,sBAAc,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAC,QAAQ;wBACnD,CAAC,sBAAc,CAAC,mBAAmB,CAAC,EAAE,OAAO,CAAC,WAAW;wBACzD,CAAC,sBAAc,CAAC,aAAa,CAAC,EAAE,uBAAuB,CAAC,OAAO,CAC7D,QAAQ,CAAC,IAAI,CACd;wBACD,CAAC,sBAAc,CAAC,oBAAoB,CAAC,EAAE,QAAQ,CAAC,WAAW;qBAC5D;iBACF,EACD,KAAK,EAAC,IAAI,EAAC,EAAE;oBACX,IAAI;wBACF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;wBAChD,eAAe,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;wBAClD,OAAO,MAAM,CAAC;qBACf;oBAAC,OAAO,KAAU,EAAE;wBACnB,uBAAuB,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;wBACpD,MAAM,KAAK,CAAC;qBACb;4BAAS;wBACR,IAAI,CAAC,GAAG,EAAE,CAAC;qBACZ;gBACH,CAAC,CACF,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,wBAAwB;QAC9B,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UACL,QAAmC;YAEnC,OAAO,KAAK,WAEV,GAAG,IAAI;gBAEP,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBAC1B,OAAO,eAAe,CAAC,MAAM,CAAC,eAAe,CAC3C,UAAU,CAAC,IAAI,EACf;oBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,EAAE;wBACV,CAAC,sBAAc,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,IAAI;qBAC5C;iBACF,EACD,KAAK,EAAC,IAAI,EAAC,EAAE;oBACX,IAAI;wBACF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;wBAChD,eAAe,CAAC,mBAAmB,CACjC,IAAI,EACJ,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,OAAO,CACf,CAAC;wBACF,OAAO,MAAM,CAAC;qBACf;oBAAC,OAAO,KAAK,EAAE;wBACd,uBAAuB,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;wBACpD,MAAM,KAAK,CAAC;qBACb;4BAAS;wBACR,IAAI,CAAC,GAAG,EAAE,CAAC;qBACZ;gBACH,CAAC,CACF,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,2BAA2B;QACjC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UACL,QAAsC;YAEtC,OAAO,KAAK,WAAiC,GAAG,IAAI;gBAClD,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;gBACvB,OAAO,eAAe,CAAC,MAAM,CAAC,eAAe,CAC3C,YAAY,OAAO,EAAE,EACrB;oBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,EAAE,EAAE;iBACf,EACD,KAAK,EAAC,IAAI,EAAC,EAAE;oBACX,IAAI;wBACF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;wBAChD,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBAC9C,eAAe,CAAC,mBAAmB,CACjC,IAAI,EACJ,WAAW,CAAC,MAAM,EAClB,WAAW,CAAC,OAAO,CACpB,CAAC;wBACF,OAAO,MAAM,CAAC;qBACf;oBAAC,OAAO,KAAK,EAAE;wBACd,uBAAuB,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;wBACpD,MAAM,KAAK,CAAC;qBACb;4BAAS;wBACR,IAAI,CAAC,GAAG,EAAE,CAAC;qBACZ;gBACH,CAAC,CACF,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,aAAa,CAAiB,IAAO;QAC3C,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAU,QAAqB;YACpC,OAAO,UAEL,aAA4D,EAC5D,IAAe;gBAEf,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;oBACvC,IAAI,GAAG,aAAa,CAAC;oBACrB,aAAa,GAAG,EAAE,CAAC;iBACpB;gBAED,SAAS,aAAa,CAEpB,GAAoC;oBAEpC,mEAAmE;oBACnE,oDAAoD;oBACpD,yCAAyC;oBACzC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;wBAAE,OAAO,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;oBAE/D,OAAO,eAAe,CAAC,MAAM,CAAC,eAAe,CAC3C,IAAI,EACJ;wBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;qBACtB,EACD,KAAK,EAAC,IAAI,EAAC,EAAE;;wBACX,IAAI;4BACF,OAAO,MAAM,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA,CAAC;yBACpC;wBAAC,OAAO,KAAU,EAAE;4BACnB,MAAA,IAAI,CAAC,MAAM,+CAAX,IAAI,EAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;4BAClD,uBAAuB,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BACpD,MAAM,KAAK,CAAC;yBACb;gCAAS;4BACR,IAAI,CAAC,GAAG,EAAE,CAAC;yBACZ;oBACH,CAAC,CACF,CAAC;gBACJ,CAAC;gBACD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,aAAoB,EAAE,aAAoB,CAAC,CAAC;YACzE,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,aAAa,CAAiB,IAAO;QAC3C,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAU,QAAqB;YACpC,OAAO,UAEL,OAA0B,EAC1B,OAAsC,EACtC,IAAe;gBAEf,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;oBACjC,IAAI,GAAG,OAAO,CAAC;oBACf,OAAO,GAAG,EAAE,CAAC;iBACd;gBAED,SAAS,aAAa,CAAwB,GAAG,IAAW;oBAC1D,mEAAmE;oBACnE,oDAAoD;oBACpD,yCAAyC;oBACzC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;wBAAE,OAAO,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAEjE,OAAO,eAAe,CAAC,MAAM,CAAC,eAAe,CAC3C,GAAG,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE,GAAG,EAChC;wBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;wBACrB,mDAAmD;wBACnD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAClC,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,iCAClB,KAAK,KACR,CAAC,GAAG,sBAAc,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,EACvC,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,GAAG,aAAY,QAAQ;gCAC1B,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;gCAC3B,CAAC,CAAC,GAAG,IACT,EACF,EAAE,CACH;qBACF,EACD,KAAK,EAAC,IAAI,EAAC,EAAE;;wBACX,IAAI;4BACF,OAAO,MAAM,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,CAAC;yBACtC;wBAAC,OAAO,KAAU,EAAE;4BACnB,MAAA,IAAI,CAAC,MAAM,+CAAX,IAAI,EAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;4BAClD,uBAAuB,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;4BACpD,MAAM,KAAK,CAAC;yBACb;gCAAS;4BACR,IAAI,CAAC,GAAG,EAAE,CAAC;yBACZ;oBACH,CAAC,CACF,CAAC;gBACJ,CAAC;gBACD,2EAA2E;gBAC3E,4CAA4C;gBAC5C,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,QAAQ,EAAE;oBAC7C,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM;iBACpB,CAAC,CAAC;gBACH,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,aAAoB,CAAC,CAAC;YACrE,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;CACF;AApWD,0DAoWC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Span, SpanKind, SpanStatusCode } from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n} from '@opentelemetry/instrumentation';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\n\nimport type * as cucumber from '@cucumber/cucumber';\nimport type * as messages from '@cucumber/messages';\nimport type TestCaseRunner from '@cucumber/cucumber/lib/runtime/test_case_runner';\nimport type {\n  DefineStepPattern,\n  IDefineStepOptions,\n  IDefineTestRunHookOptions,\n} from '@cucumber/cucumber/lib/support_code_library_builder/types';\n\nimport { AttributeNames, CucumberInstrumentationConfig } from './types';\nimport { VERSION } from './version';\n\nconst hooks = ['Before', 'BeforeStep', 'AfterStep', 'After'] as const;\nconst steps = ['Given', 'When', 'Then'] as const;\ntype Cucumber = typeof cucumber;\ntype Hook = (typeof hooks)[number];\ntype Step = (typeof steps)[number];\n\nexport class CucumberInstrumentation extends InstrumentationBase {\n  private module: Cucumber | undefined;\n\n  constructor(config: CucumberInstrumentationConfig = {}) {\n    super('@opentelemetry/instrumentation-cucumber', VERSION, config);\n  }\n\n  init(): InstrumentationNodeModuleDefinition<any>[] {\n    return [\n      new InstrumentationNodeModuleDefinition<Cucumber>(\n        '@cucumber/cucumber',\n        ['^8.0.0', '^9.0.0', '^10.0.0'],\n        (moduleExports, moduleVersion) => {\n          this._diag.debug(\n            `Applying patch for @cucumber/cucumber@${moduleVersion}`\n          );\n          this.module = moduleExports;\n          steps.forEach(step => {\n            if (isWrapped(moduleExports[step])) {\n              this._unwrap(moduleExports, step);\n            }\n            this._wrap(moduleExports, step, this._getStepPatch(step));\n          });\n          hooks.forEach(hook => {\n            if (isWrapped(moduleExports[hook])) {\n              this._unwrap(moduleExports, hook);\n            }\n            this._wrap(moduleExports, hook, this._getHookPatch(hook));\n          });\n          return moduleExports;\n        },\n        (moduleExports, moduleVersion) => {\n          if (moduleExports === undefined) return;\n          this._diag.debug(\n            `Removing patch for @cucumber/cucumber@${moduleVersion}`\n          );\n          [...hooks, ...steps].forEach(method => {\n            this._unwrap(moduleExports, method);\n          });\n        },\n        [\n          new InstrumentationNodeModuleFile<{\n            default: { new (): TestCaseRunner; prototype: TestCaseRunner };\n          }>(\n            '@cucumber/cucumber/lib/runtime/test_case_runner.js',\n            ['^8.0.0', '^9.0.0', '^10.0.0'],\n            (moduleExports, moduleVersion) => {\n              this._diag.debug(\n                `Applying patch for @cucumber/cucumber/lib/runtime/test_case_runner.js@${moduleVersion}`\n              );\n              if (isWrapped(moduleExports.default.prototype.run)) {\n                this._unwrap(moduleExports.default.prototype, 'run');\n                this._unwrap(moduleExports.default.prototype, 'runStep');\n                if ('runAttempt' in moduleExports.default.prototype) {\n                  this._unwrap(moduleExports.default.prototype, 'runAttempt');\n                }\n              }\n              this._wrap(\n                moduleExports.default.prototype,\n                'run',\n                this._getTestCaseRunPatch()\n              );\n              this._wrap(\n                moduleExports.default.prototype,\n                'runStep',\n                this._getTestCaseRunStepPatch()\n              );\n              if ('runAttempt' in moduleExports.default.prototype) {\n                this._wrap(\n                  moduleExports.default.prototype,\n                  'runAttempt',\n                  this._getTestCaseRunAttemptPatch()\n                );\n              }\n              return moduleExports;\n            },\n            (moduleExports, moduleVersion) => {\n              if (moduleExports === undefined) return;\n              this._diag.debug(\n                `Removing patch for @cucumber/cucumber/lib/runtime/test_case_runner.js@${moduleVersion}`\n              );\n              this._unwrap(moduleExports.default.prototype, 'run');\n              this._unwrap(moduleExports.default.prototype, 'runStep');\n              if ('runAttempt' in moduleExports.default.prototype) {\n                this._unwrap(moduleExports.default.prototype, 'runAttempt');\n              }\n            }\n          ),\n        ]\n      ),\n    ];\n  }\n\n  private static mapTags(tags: readonly messages.Tag[]): string[] {\n    return tags.map(tag => tag.name);\n  }\n\n  private static setSpanToError(span: Span, error: any) {\n    span.recordException(error);\n    span.setStatus({\n      code: SpanStatusCode.ERROR,\n      message: error?.message ?? error,\n    });\n  }\n\n  private setSpanToStepStatus(\n    span: Span,\n    status: messages.TestStepResultStatus,\n    context?: string\n  ) {\n    // if the telemetry is enabled, the module should be defined\n    if (!this.module) return;\n\n    span.setAttribute(AttributeNames.STEP_STATUS, status);\n    if (\n      [\n        this.module.Status.UNDEFINED,\n        this.module.Status.AMBIGUOUS,\n        this.module.Status.FAILED,\n      ].includes(status)\n    ) {\n      span.recordException(status);\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: context || status,\n      });\n    }\n  }\n\n  private _getTestCaseRunPatch() {\n    const instrumentation = this;\n    return function (original: TestCaseRunner['run']): TestCaseRunner['run'] {\n      return async function (this: TestCaseRunner, ...args) {\n        const gherkinDocument = this[\n          'gherkinDocument'\n        ] as Required<messages.GherkinDocument>;\n        const { feature } = gherkinDocument;\n        const pickle = this['pickle'] as messages.Pickle;\n        const scenario = feature.children.find(\n          node => node?.scenario?.id === pickle.astNodeIds[0]\n        )?.scenario as messages.Scenario;\n\n        return instrumentation.tracer.startActiveSpan(\n          `Feature: ${feature.name}. Scenario: ${pickle.name}`,\n          {\n            kind: SpanKind.CLIENT,\n            attributes: {\n              [SemanticAttributes.CODE_FILEPATH]: gherkinDocument.uri,\n              [SemanticAttributes.CODE_LINENO]: scenario.location.line,\n              [SemanticAttributes.CODE_FUNCTION]: scenario.name,\n              [SemanticAttributes.CODE_NAMESPACE]: feature.name,\n              [AttributeNames.FEATURE_TAGS]: CucumberInstrumentation.mapTags(\n                feature.tags\n              ),\n              [AttributeNames.FEATURE_LANGUAGE]: feature.language,\n              [AttributeNames.FEATURE_DESCRIPTION]: feature.description,\n              [AttributeNames.SCENARIO_TAGS]: CucumberInstrumentation.mapTags(\n                scenario.tags\n              ),\n              [AttributeNames.SCENARIO_DESCRIPTION]: scenario.description,\n            },\n          },\n          async span => {\n            try {\n              const status = await original.apply(this, args);\n              instrumentation.setSpanToStepStatus(span, status);\n              return status;\n            } catch (error: any) {\n              CucumberInstrumentation.setSpanToError(span, error);\n              throw error;\n            } finally {\n              span.end();\n            }\n          }\n        );\n      };\n    };\n  }\n\n  private _getTestCaseRunStepPatch() {\n    const instrumentation = this;\n    return function (\n      original: TestCaseRunner['runStep']\n    ): TestCaseRunner['runStep'] {\n      return async function (\n        this: TestCaseRunner,\n        ...args\n      ): Promise<messages.TestStepResult> {\n        const [pickleStep] = args;\n        return instrumentation.tracer.startActiveSpan(\n          pickleStep.text,\n          {\n            kind: SpanKind.CLIENT,\n            attributes: {\n              [AttributeNames.STEP_TYPE]: pickleStep.type,\n            },\n          },\n          async span => {\n            try {\n              const result = await original.apply(this, args);\n              instrumentation.setSpanToStepStatus(\n                span,\n                result.status,\n                result.message\n              );\n              return result;\n            } catch (error) {\n              CucumberInstrumentation.setSpanToError(span, error);\n              throw error;\n            } finally {\n              span.end();\n            }\n          }\n        );\n      };\n    };\n  }\n\n  private _getTestCaseRunAttemptPatch() {\n    const instrumentation = this;\n    return function (\n      original: TestCaseRunner['runAttempt']\n    ): TestCaseRunner['runAttempt'] {\n      return async function (this: TestCaseRunner, ...args): Promise<boolean> {\n        const [attempt] = args;\n        return instrumentation.tracer.startActiveSpan(\n          `Attempt #${attempt}`,\n          {\n            kind: SpanKind.CLIENT,\n            attributes: {},\n          },\n          async span => {\n            try {\n              const result = await original.apply(this, args);\n              const worstResult = this.getWorstStepResult();\n              instrumentation.setSpanToStepStatus(\n                span,\n                worstResult.status,\n                worstResult.message\n              );\n              return result;\n            } catch (error) {\n              CucumberInstrumentation.setSpanToError(span, error);\n              throw error;\n            } finally {\n              span.end();\n            }\n          }\n        );\n      };\n    };\n  }\n\n  private _getHookPatch<H extends Hook>(name: H) {\n    const instrumentation = this;\n    return function (original: Cucumber[H]): Cucumber[H] {\n      return function (\n        this: {},\n        tagsOrOptions: string | IDefineTestRunHookOptions | Function,\n        code?: Function\n      ) {\n        if (typeof tagsOrOptions === 'function') {\n          code = tagsOrOptions;\n          tagsOrOptions = {};\n        }\n\n        function traceableCode(\n          this: cucumber.IWorld,\n          arg: cucumber.ITestCaseHookParameter\n        ) {\n          // because we're wrapping the function that was passed to the hook,\n          // it will stay wrapped in cucumber's internal state\n          // even if we disable the instrumentation\n          if (!instrumentation.isEnabled()) return code?.call(this, arg);\n\n          return instrumentation.tracer.startActiveSpan(\n            name,\n            {\n              kind: SpanKind.CLIENT,\n            },\n            async span => {\n              try {\n                return await code?.call(this, arg);\n              } catch (error: any) {\n                this.attach?.(JSON.stringify(span.spanContext()));\n                CucumberInstrumentation.setSpanToError(span, error);\n                throw error;\n              } finally {\n                span.end();\n              }\n            }\n          );\n        }\n        return original.call(this, tagsOrOptions as any, traceableCode as any);\n      };\n    };\n  }\n\n  private _getStepPatch<S extends Step>(name: S) {\n    const instrumentation = this;\n    return function (original: Cucumber[S]): Cucumber[S] {\n      return function (\n        this: {},\n        pattern: DefineStepPattern,\n        options: IDefineStepOptions | Function,\n        code?: Function\n      ): void {\n        if (typeof options === 'function') {\n          code = options;\n          options = {};\n        }\n\n        function traceableCode(this: cucumber.IWorld, ...args: any[]) {\n          // because we're wrapping the function that was passed to the hook,\n          // it will stay wrapped in cucumber's internal state\n          // even if we disable the instrumentation\n          if (!instrumentation.isEnabled()) return code?.apply(this, args);\n\n          return instrumentation.tracer.startActiveSpan(\n            `${name}(${pattern.toString()})`,\n            {\n              kind: SpanKind.CLIENT,\n              // ignore the last argument because it's a callback\n              attributes: args.slice(0, -1).reduce(\n                (attrs, arg, index) => ({\n                  ...attrs,\n                  [`${AttributeNames.STEP_ARGS}[${index}]`]:\n                    arg?.raw instanceof Function\n                      ? JSON.stringify(arg.raw())\n                      : arg,\n                }),\n                {}\n              ),\n            },\n            async span => {\n              try {\n                return await code?.apply(this, args);\n              } catch (error: any) {\n                this.attach?.(JSON.stringify(span.spanContext()));\n                CucumberInstrumentation.setSpanToError(span, error);\n                throw error;\n              } finally {\n                span.end();\n              }\n            }\n          );\n        }\n        // cucumber asks for the number of arguments to match the specified pattern\n        // copy the value from the original function\n        Object.defineProperty(traceableCode, 'length', {\n          value: code?.length,\n        });\n        return original.call(this, pattern, options, traceableCode as any);\n      };\n    };\n  }\n}\n"]}