"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttributeNames = void 0;
var AttributeNames;
(function (AttributeNames) {
    AttributeNames["FEATURE_TAGS"] = "cucumber.feature.tags";
    AttributeNames["FEATURE_LANGUAGE"] = "cucumber.feature.language";
    AttributeNames["FEATURE_DESCRIPTION"] = "cucumber.feature.description";
    AttributeNames["SCENARIO_TAGS"] = "cucumber.scenario.tags";
    AttributeNames["SCENARIO_DESCRIPTION"] = "cucumber.scenario.description";
    AttributeNames["STEP_TYPE"] = "cucumber.step.type";
    AttributeNames["STEP_STATUS"] = "cucumber.step.status";
    AttributeNames["STEP_ARGS"] = "cucumber.step.args";
})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));
//# sourceMappingURL=types.js.map