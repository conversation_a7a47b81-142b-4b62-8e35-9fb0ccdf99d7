{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,IAAY,cASX;AATD,WAAY,cAAc;IACxB,wDAAsC,CAAA;IACtC,gEAA8C,CAAA;IAC9C,sEAAoD,CAAA;IACpD,0DAAwC,CAAA;IACxC,wEAAsD,CAAA;IACtD,kDAAgC,CAAA;IAChC,sDAAoC,CAAA;IACpC,kDAAgC,CAAA;AAClC,CAAC,EATW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QASzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport type CucumberInstrumentationConfig = InstrumentationConfig;\n\nexport enum AttributeNames {\n  FEATURE_TAGS = 'cucumber.feature.tags',\n  FEATURE_LANGUAGE = 'cucumber.feature.language',\n  FEATURE_DESCRIPTION = 'cucumber.feature.description',\n  SCENARIO_TAGS = 'cucumber.scenario.tags',\n  SCENARIO_DESCRIPTION = 'cucumber.scenario.description',\n  STEP_TYPE = 'cucumber.step.type',\n  STEP_STATUS = 'cucumber.step.status',\n  STEP_ARGS = 'cucumber.step.args',\n}\n"]}