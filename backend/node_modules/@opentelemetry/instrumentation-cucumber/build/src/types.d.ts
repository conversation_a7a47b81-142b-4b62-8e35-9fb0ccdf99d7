import { InstrumentationConfig } from '@opentelemetry/instrumentation';
export declare type CucumberInstrumentationConfig = InstrumentationConfig;
export declare enum AttributeNames {
    FEATURE_TAGS = "cucumber.feature.tags",
    FEATURE_LANGUAGE = "cucumber.feature.language",
    FEATURE_DESCRIPTION = "cucumber.feature.description",
    SCENARIO_TAGS = "cucumber.scenario.tags",
    SCENARIO_DESCRIPTION = "cucumber.scenario.description",
    STEP_TYPE = "cucumber.step.type",
    STEP_STATUS = "cucumber.step.status",
    STEP_ARGS = "cucumber.step.args"
}
//# sourceMappingURL=types.d.ts.map