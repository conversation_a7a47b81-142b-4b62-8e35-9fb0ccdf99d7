{"name": "@opentelemetry/instrumentation-cucumber", "version": "0.2.1", "description": "OpenTelemetry cucumber automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test-all-versions": "tav", "tdd": "npm run test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-cucumber --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "version:update": "node ../../../scripts/version-update.js", "compile": "tsc -p ."}, "keywords": ["cucumber", "instrumentation", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}, "devDependencies": {"@cucumber/cucumber": "^9.0.0", "@opentelemetry/api": "^1.0.0", "@opentelemetry/core": "^1.3.1", "@opentelemetry/sdk-trace-base": "^1.3.1", "@opentelemetry/sdk-trace-node": "^1.3.1", "@types/mocha": "7.0.2", "@types/semver": "7.5.3", "@types/shimmer": "1.0.3", "@types/sinon": "10.0.18", "mocha": "7.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "semver": "7.5.4", "sinon": "15.2.0", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/instrumentation-cucumber#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}