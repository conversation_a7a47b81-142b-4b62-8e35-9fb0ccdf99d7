{"version": 3, "file": "B3SinglePropagator.js", "sourceRoot": "", "sources": ["../../src/B3SinglePropagator.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAEL,kBAAkB,EAClB,aAAa,EACb,cAAc,EAId,KAAK,EACL,UAAU,GACX,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAC7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAEhD,MAAM,gBAAgB,GACpB,qFAAqF,CAAC;AACxF,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC/B,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3C,MAAM,WAAW,GAAG,GAAG,CAAC;AAExB,SAAS,mBAAmB,CAAC,OAAe;IAC1C,OAAO,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC;AAClE,CAAC;AAED,SAAS,mBAAmB,CAAC,aAAiC;IAC5D,IAAI,aAAa,IAAI,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;QACtD,OAAO,UAAU,CAAC,OAAO,CAAC;KAC3B;IACD,OAAO,UAAU,CAAC,IAAI,CAAC;AACzB,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,kBAAkB;IAC7B,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IACE,CAAC,WAAW;YACZ,CAAC,kBAAkB,CAAC,WAAW,CAAC;YAChC,mBAAmB,CAAC,OAAO,CAAC;YAE5B,OAAO;QAET,MAAM,aAAa,GACjB,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC;QACtE,MAAM,KAAK,GAAG,GAAG,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,MAAM,IAAI,aAAa,EAAE,CAAC;QAC9E,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QACtD,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7D,IAAI,OAAO,SAAS,KAAK,QAAQ;YAAE,OAAO,OAAO,CAAC;QAElD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK;YAAE,OAAO,OAAO,CAAC;QAE3B,MAAM,CAAC,EAAE,gBAAgB,EAAE,MAAM,EAAE,aAAa,CAAC,GAAG,KAAK,CAAC;QAC1D,MAAM,OAAO,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAEtD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAAE,OAAO,OAAO,CAAC;QAEvE,MAAM,UAAU,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;QAEtD,IAAI,aAAa,KAAK,WAAW,EAAE;YACjC,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;SAC9D;QAED,OAAO,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE;YACnC,OAAO;YACP,MAAM;YACN,QAAQ,EAAE,IAAI;YACd,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED,MAAM;QACJ,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAC7B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  isValidSpanId,\n  isValidTraceId,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n  trace,\n  TraceFlags,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport { B3_DEBUG_FLAG_KEY } from './common';\nimport { B3_CONTEXT_HEADER } from './constants';\n\nconst B3_CONTEXT_REGEX =\n  /((?:[0-9a-f]{16}){1,2})-([0-9a-f]{16})(?:-([01d](?![0-9a-f])))?(?:-([0-9a-f]{16}))?/;\nconst PADDING = '0'.repeat(16);\nconst SAMPLED_VALUES = new Set(['d', '1']);\nconst DEBUG_STATE = 'd';\n\nfunction convertToTraceId128(traceId: string): string {\n  return traceId.length === 32 ? traceId : `${PADDING}${traceId}`;\n}\n\nfunction convertToTraceFlags(samplingState: string | undefined): TraceFlags {\n  if (samplingState && SAMPLED_VALUES.has(samplingState)) {\n    return TraceFlags.SAMPLED;\n  }\n  return TraceFlags.NONE;\n}\n\n/**\n * Propagator for the B3 single-header HTTP format.\n * Based on: https://github.com/openzipkin/b3-propagation\n */\nexport class B3SinglePropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const spanContext = trace.getSpanContext(context);\n    if (\n      !spanContext ||\n      !isSpanContextValid(spanContext) ||\n      isTracingSuppressed(context)\n    )\n      return;\n\n    const samplingState =\n      context.getValue(B3_DEBUG_FLAG_KEY) || spanContext.traceFlags & 0x1;\n    const value = `${spanContext.traceId}-${spanContext.spanId}-${samplingState}`;\n    setter.set(carrier, B3_CONTEXT_HEADER, value);\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const header = getter.get(carrier, B3_CONTEXT_HEADER);\n    const b3Context = Array.isArray(header) ? header[0] : header;\n    if (typeof b3Context !== 'string') return context;\n\n    const match = b3Context.match(B3_CONTEXT_REGEX);\n    if (!match) return context;\n\n    const [, extractedTraceId, spanId, samplingState] = match;\n    const traceId = convertToTraceId128(extractedTraceId);\n\n    if (!isValidTraceId(traceId) || !isValidSpanId(spanId)) return context;\n\n    const traceFlags = convertToTraceFlags(samplingState);\n\n    if (samplingState === DEBUG_STATE) {\n      context = context.setValue(B3_DEBUG_FLAG_KEY, samplingState);\n    }\n\n    return trace.setSpanContext(context, {\n      traceId,\n      spanId,\n      isRemote: true,\n      traceFlags,\n    });\n  }\n\n  fields(): string[] {\n    return [B3_CONTEXT_HEADER];\n  }\n}\n"]}