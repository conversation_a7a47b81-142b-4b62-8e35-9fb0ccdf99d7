{"version": 3, "file": "B3MultiPropagator.js", "sourceRoot": "", "sources": ["../../src/B3MultiPropagator.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAU4B;AAC5B,8CAA0D;AAC1D,qCAA6C;AAC7C,2CAMqB;AAErB,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACrE,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAE1E,SAAS,mBAAmB,CAAC,OAA+B;IAC1D,OAAO,OAAO,KAAK,gBAAU,CAAC,OAAO,IAAI,OAAO,KAAK,gBAAU,CAAC,IAAI,CAAC;AACvE,CAAC;AAED,SAAS,WAAW,CAAC,MAAe;IAClC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACpD,CAAC;AAED,SAAS,cAAc,CAAC,OAAgB,EAAE,MAAqB,EAAE,GAAW;IAC1E,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACxC,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,UAAU,CAAC,OAAgB,EAAE,MAAqB;IACzD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,yBAAa,CAAC,CAAC;IAC/D,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;KAClC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,SAAS,CAAC,OAAgB,EAAE,MAAqB;IACxD,MAAM,MAAM,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,wBAAY,CAAC,CAAC;IAC7D,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO,MAAM,CAAC;KACf;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,QAAQ,CAAC,OAAgB,EAAE,MAAqB;IACvD,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,sBAAU,CAAC,CAAC;IAC1D,OAAO,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AACzC,CAAC;AAED,SAAS,aAAa,CACpB,OAAgB,EAChB,MAAqB;IAErB,MAAM,UAAU,GAAG,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,wBAAY,CAAC,CAAC;IACjE,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACxC,IAAI,KAAK,KAAK,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;QACzD,OAAO,gBAAU,CAAC,OAAO,CAAC;KAC3B;IACD,IAAI,UAAU,KAAK,SAAS,IAAI,sBAAsB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;QACtE,OAAO,gBAAU,CAAC,IAAI,CAAC;KACxB;IACD,+DAA+D;IAC/D,OAAO;AACT,CAAC;AAED;;;GAGG;AACH,MAAa,iBAAiB;IAC5B,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,MAAM,WAAW,GAAG,WAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IACE,CAAC,WAAW;YACZ,CAAC,IAAA,wBAAkB,EAAC,WAAW,CAAC;YAChC,IAAA,0BAAmB,EAAC,OAAO,CAAC;YAE5B,OAAO;QAET,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,0BAAiB,CAAC,CAAC;QAClD,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,yBAAa,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;QACxD,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,wBAAY,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;QACtD,sDAAsD;QACtD,oDAAoD;QACpD,IAAI,KAAK,KAAK,GAAG,EAAE;YACjB,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,sBAAU,EAAE,KAAK,CAAC,CAAC;SACxC;aAAM,IAAI,WAAW,CAAC,UAAU,KAAK,SAAS,EAAE;YAC/C,oEAAoE;YACpE,uCAAuC;YACvC,MAAM,CAAC,GAAG,CACR,OAAO,EACP,wBAAY,EACZ,CAAC,gBAAU,CAAC,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,gBAAU,CAAC,OAAO;gBAClE,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,GAAG,CACR,CAAC;SACH;IACH,CAAC;IAED,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC5C,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,EAAE,MAAM,CAAe,CAAC;QAChE,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAExC,IACE,IAAA,oBAAc,EAAC,OAAO,CAAC;YACvB,IAAA,mBAAa,EAAC,MAAM,CAAC;YACrB,mBAAmB,CAAC,UAAU,CAAC,EAC/B;YACA,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,0BAAiB,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,WAAK,CAAC,cAAc,CAAC,OAAO,EAAE;gBACnC,OAAO;gBACP,MAAM;gBACN,QAAQ,EAAE,IAAI;gBACd,UAAU;aACX,CAAC,CAAC;SACJ;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM;QACJ,OAAO;YACL,yBAAa;YACb,wBAAY;YACZ,sBAAU;YACV,wBAAY;YACZ,+BAAmB;SACpB,CAAC;IACJ,CAAC;CACF;AA7DD,8CA6DC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  isValidSpanId,\n  isValidTraceId,\n  trace,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n  TraceFlags,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport { B3_DEBUG_FLAG_KEY } from './common';\nimport {\n  X_B3_FLAGS,\n  X_B3_PARENT_SPAN_ID,\n  X_B3_SAMPLED,\n  X_B3_SPAN_ID,\n  X_B3_TRACE_ID,\n} from './constants';\n\nconst VALID_SAMPLED_VALUES = new Set([true, 'true', 'True', '1', 1]);\nconst VALID_UNSAMPLED_VALUES = new Set([false, 'false', 'False', '0', 0]);\n\nfunction isValidSampledValue(sampled: TraceFlags | undefined): boolean {\n  return sampled === TraceFlags.SAMPLED || sampled === TraceFlags.NONE;\n}\n\nfunction parseHeader(header: unknown) {\n  return Array.isArray(header) ? header[0] : header;\n}\n\nfunction getHeaderValue(carrier: unknown, getter: TextMapGetter, key: string) {\n  const header = getter.get(carrier, key);\n  return parseHeader(header);\n}\n\nfunction getTraceId(carrier: unknown, getter: TextMapGetter): string {\n  const traceId = getHeaderValue(carrier, getter, X_B3_TRACE_ID);\n  if (typeof traceId === 'string') {\n    return traceId.padStart(32, '0');\n  }\n  return '';\n}\n\nfunction getSpanId(carrier: unknown, getter: TextMapGetter): string {\n  const spanId = getHeaderValue(carrier, getter, X_B3_SPAN_ID);\n  if (typeof spanId === 'string') {\n    return spanId;\n  }\n  return '';\n}\n\nfunction getDebug(carrier: unknown, getter: TextMapGetter): string | undefined {\n  const debug = getHeaderValue(carrier, getter, X_B3_FLAGS);\n  return debug === '1' ? '1' : undefined;\n}\n\nfunction getTraceFlags(\n  carrier: unknown,\n  getter: TextMapGetter\n): TraceFlags | undefined {\n  const traceFlags = getHeaderValue(carrier, getter, X_B3_SAMPLED);\n  const debug = getDebug(carrier, getter);\n  if (debug === '1' || VALID_SAMPLED_VALUES.has(traceFlags)) {\n    return TraceFlags.SAMPLED;\n  }\n  if (traceFlags === undefined || VALID_UNSAMPLED_VALUES.has(traceFlags)) {\n    return TraceFlags.NONE;\n  }\n  // This indicates to isValidSampledValue that this is not valid\n  return;\n}\n\n/**\n * Propagator for the B3 multiple-header HTTP format.\n * Based on: https://github.com/openzipkin/b3-propagation\n */\nexport class B3MultiPropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const spanContext = trace.getSpanContext(context);\n    if (\n      !spanContext ||\n      !isSpanContextValid(spanContext) ||\n      isTracingSuppressed(context)\n    )\n      return;\n\n    const debug = context.getValue(B3_DEBUG_FLAG_KEY);\n    setter.set(carrier, X_B3_TRACE_ID, spanContext.traceId);\n    setter.set(carrier, X_B3_SPAN_ID, spanContext.spanId);\n    // According to the B3 spec, if the debug flag is set,\n    // the sampled flag shouldn't be propagated as well.\n    if (debug === '1') {\n      setter.set(carrier, X_B3_FLAGS, debug);\n    } else if (spanContext.traceFlags !== undefined) {\n      // We set the header only if there is an existing sampling decision.\n      // Otherwise we will omit it => Absent.\n      setter.set(\n        carrier,\n        X_B3_SAMPLED,\n        (TraceFlags.SAMPLED & spanContext.traceFlags) === TraceFlags.SAMPLED\n          ? '1'\n          : '0'\n      );\n    }\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const traceId = getTraceId(carrier, getter);\n    const spanId = getSpanId(carrier, getter);\n    const traceFlags = getTraceFlags(carrier, getter) as TraceFlags;\n    const debug = getDebug(carrier, getter);\n\n    if (\n      isValidTraceId(traceId) &&\n      isValidSpanId(spanId) &&\n      isValidSampledValue(traceFlags)\n    ) {\n      context = context.setValue(B3_DEBUG_FLAG_KEY, debug);\n      return trace.setSpanContext(context, {\n        traceId,\n        spanId,\n        isRemote: true,\n        traceFlags,\n      });\n    }\n    return context;\n  }\n\n  fields(): string[] {\n    return [\n      X_B3_TRACE_ID,\n      X_B3_SPAN_ID,\n      X_B3_FLAGS,\n      X_B3_SAMPLED,\n      X_B3_PARENT_SPAN_ID,\n    ];\n  }\n}\n"]}