{"version": 3, "file": "B3Propagator.js", "sourceRoot": "", "sources": ["../../src/B3Propagator.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,8CAA0D;AAC1D,2DAAwD;AACxD,6DAA0D;AAC1D,2CAAgD;AAChD,mCAA+D;AAE/D;;;;;;GAMG;AACH,MAAa,YAAY;IAYvB,YAAY,SAA6B,EAAE;QAX1B,uBAAkB,GACjC,IAAI,qCAAiB,EAAE,CAAC;QACT,wBAAmB,GAClC,IAAI,uCAAkB,EAAE,CAAC;QASzB,IAAI,MAAM,CAAC,cAAc,KAAK,wBAAgB,CAAC,YAAY,EAAE;YAC3D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC9C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;SACjD;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;SAClD;IACH,CAAC;IAED,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,IAAI,IAAA,0BAAmB,EAAC,OAAO,CAAC,EAAE;YAChC,OAAO;SACR;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACzC,CAAC;IAED,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,6BAAiB,CAAC,CAAC;QACtD,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAE7D,IAAI,SAAS,EAAE;YACb,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SACnE;aAAM;YACL,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SAClE;IACH,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;CACF;AA3CD,oCA2CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from '@opentelemetry/core';\nimport { B3MultiPropagator } from './B3MultiPropagator';\nimport { B3SinglePropagator } from './B3SinglePropagator';\nimport { B3_CONTEXT_HEADER } from './constants';\nimport { B3InjectEncoding, B3PropagatorConfig } from './types';\n\n/**\n * Propagator that extracts B3 context in both single and multi-header variants,\n * with configurable injection format defaulting to B3 single-header. Due to\n * the asymmetry in injection and extraction formats this is not suitable to\n * be implemented as a composite propagator.\n * Based on: https://github.com/openzipkin/b3-propagation\n */\nexport class B3Propagator implements TextMapPropagator {\n  private readonly _b3MultiPropagator: B3MultiPropagator =\n    new B3MultiPropagator();\n  private readonly _b3SinglePropagator: B3SinglePropagator =\n    new B3SinglePropagator();\n  private readonly _inject: (\n    context: Context,\n    carrier: unknown,\n    setter: TextMapSetter\n  ) => void;\n  public readonly _fields: string[];\n\n  constructor(config: B3PropagatorConfig = {}) {\n    if (config.injectEncoding === B3InjectEncoding.MULTI_HEADER) {\n      this._inject = this._b3MultiPropagator.inject;\n      this._fields = this._b3MultiPropagator.fields();\n    } else {\n      this._inject = this._b3SinglePropagator.inject;\n      this._fields = this._b3SinglePropagator.fields();\n    }\n  }\n\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    if (isTracingSuppressed(context)) {\n      return;\n    }\n    this._inject(context, carrier, setter);\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const header = getter.get(carrier, B3_CONTEXT_HEADER);\n    const b3Context = Array.isArray(header) ? header[0] : header;\n\n    if (b3Context) {\n      return this._b3SinglePropagator.extract(context, carrier, getter);\n    } else {\n      return this._b3MultiPropagator.extract(context, carrier, getter);\n    }\n  }\n\n  fields(): string[] {\n    return this._fields;\n  }\n}\n"]}