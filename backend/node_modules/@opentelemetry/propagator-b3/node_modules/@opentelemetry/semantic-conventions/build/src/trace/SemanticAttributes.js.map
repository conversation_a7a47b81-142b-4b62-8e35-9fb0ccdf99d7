{"version": 3, "file": "SemanticAttributes.js", "sourceRoot": "", "sources": ["../../../src/trace/SemanticAttributes.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,iHAAiH;AACpG,QAAA,kBAAkB,GAAG;IAChC;;;;OAIG;IACH,sBAAsB,EAAE,wBAAwB;IAEhD;;OAEG;IACH,SAAS,EAAE,WAAW;IAEtB;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,OAAO,EAAE,SAAS;IAElB;;OAEG;IACH,wBAAwB,EAAE,0BAA0B;IAEpD;;;;OAIG;IACH,OAAO,EAAE,SAAS;IAElB;;;;OAIG;IACH,YAAY,EAAE,cAAc;IAE5B;;;;OAIG;IACH,YAAY,EAAE,cAAc;IAE5B;;;;OAIG;IACH,sBAAsB,EAAE,wBAAwB;IAEhD;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;OAEG;IACH,sBAAsB,EAAE,wBAAwB;IAEhD;;OAEG;IACH,8BAA8B,EAAE,gCAAgC;IAEhE;;;;OAIG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,wBAAwB,EAAE,0BAA0B;IAEpD;;OAEG;IACH,wCAAwC,EACtC,0CAA0C;IAE5C;;OAEG;IACH,2BAA2B,EAAE,6BAA6B;IAE1D;;OAEG;IACH,2BAA2B,EAAE,6BAA6B;IAE1D;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;;;OAIG;IACH,YAAY,EAAE,cAAc;IAE5B;;OAEG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;OAEG;IACH,iBAAiB,EAAE,mBAAmB;IAEtC;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;;;;;;;;;;;;;;;;;;MAmBE;IACF,iBAAiB,EAAE,mBAAmB;IAEtC;;OAEG;IACH,YAAY,EAAE,cAAc;IAE5B;;OAEG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;OAEG;IACH,wBAAwB,EAAE,0BAA0B;IAEpD;;OAEG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,SAAS,EAAE,WAAW;IAEtB;;OAEG;IACH,SAAS,EAAE,WAAW;IAEtB;;OAEG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;;;OAIG;IACH,iBAAiB,EAAE,mBAAmB;IAEtC;;;;OAIG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;;;OAIG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,wBAAwB,EAAE,0BAA0B;IAEpD;;OAEG;IACH,2BAA2B,EAAE,6BAA6B;IAE1D;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,YAAY,EAAE,cAAc;IAE5B;;OAEG;IACH,UAAU,EAAE,YAAY;IAExB;;OAEG;IACH,YAAY,EAAE,cAAc;IAE5B;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,SAAS,EAAE,WAAW;IAEtB;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,cAAc,EAAE,gBAAgB;IAEhC;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;;;OAIG;IACH,QAAQ,EAAE,UAAU;IAEpB;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;;;OAIG;IACH,SAAS,EAAE,WAAW;IAEtB;;OAEG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,gBAAgB,EAAE,kBAAkB;IAEpC;;;;OAIG;IACH,WAAW,EAAE,aAAa;IAE1B;;OAEG;IACH,eAAe,EAAE,iBAAiB;IAElC;;OAEG;IACH,2BAA2B,EAAE,6BAA6B;IAE1D;;OAEG;IACH,wCAAwC,EACtC,0CAA0C;IAE5C;;OAEG;IACH,4BAA4B,EAAE,8BAA8B;IAE5D;;OAEG;IACH,yCAAyC,EACvC,2CAA2C;IAE7C;;;;OAIG;IACH,gBAAgB,EAAE,kBAAkB;IAEpC;;OAEG;IACH,UAAU,EAAE,YAAY;IAExB;;;;;;;;;;;;;;MAcE;IACF,cAAc,EAAE,gBAAgB;IAEhC;;OAEG;IACH,wBAAwB,EAAE,0BAA0B;IAEpD;;OAEG;IACH,8BAA8B,EAAE,gCAAgC;IAEhE;;OAEG;IACH,oCAAoC,EAAE,sCAAsC;IAE5E;;OAEG;IACH,sCAAsC,EACpC,wCAAwC;IAE1C;;OAEG;IACH,uCAAuC,EACrC,yCAAyC;IAE3C;;OAEG;IACH,4BAA4B,EAAE,8BAA8B;IAE5D;;OAEG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,8BAA8B,EAAE,gCAAgC;IAEhE;;OAEG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,qCAAqC,EACnC,uCAAuC;IAEzC;;OAEG;IACH,oCAAoC,EAAE,sCAAsC;IAE5E;;OAEG;IACH,kCAAkC,EAAE,oCAAoC;IAExE;;OAEG;IACH,wBAAwB,EAAE,0BAA0B;IAEpD;;OAEG;IACH,yBAAyB,EAAE,2BAA2B;IAEtD;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,2BAA2B,EAAE,6BAA6B;IAE1D;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,0BAA0B,EAAE,4BAA4B;IAExD;;OAEG;IACH,kCAAkC,EAAE,oCAAoC;IAExE;;OAEG;IACH,2CAA2C,EACzC,6CAA6C;IAE/C;;OAEG;IACH,gBAAgB,EAAE,kBAAkB;IAEpC;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;OAEG;IACH,0BAA0B,EAAE,4BAA4B;IAExD;;OAEG;IACH,0BAA0B,EAAE,4BAA4B;IAExD;;OAEG;IACH,kBAAkB,EAAE,oBAAoB;IAExC;;OAEG;IACH,0BAA0B,EAAE,4BAA4B;IAExD;;OAEG;IACH,aAAa,EAAE,eAAe;IAE9B;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,yBAAyB,EAAE,2BAA2B;IAEtD;;OAEG;IACH,oCAAoC,EAAE,sCAAsC;IAE5E;;OAEG;IACH,+CAA+C,EAC7C,iDAAiD;IAEnD;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,qBAAqB,EAAE,uBAAuB;IAE9C;;OAEG;IACH,8BAA8B,EAAE,gCAAgC;IAEhE;;;;OAIG;IACH,2BAA2B,EAAE,6BAA6B;IAE1D;;OAEG;IACH,8BAA8B,EAAE,gCAAgC;IAEhE;;OAEG;IACH,yBAAyB,EAAE,2BAA2B;IAEtD;;OAEG;IACH,yBAAyB,EAAE,2BAA2B;IAEtD;;OAEG;IACH,yBAAyB,EAAE,2BAA2B;IAEtD;;OAEG;IACH,UAAU,EAAE,YAAY;IAExB;;;;OAIG;IACH,WAAW,EAAE,aAAa;IAE1B;;;;OAIG;IACH,UAAU,EAAE,YAAY;IAExB;;OAEG;IACH,oBAAoB,EAAE,sBAAsB;IAE5C;;OAEG;IACH,mBAAmB,EAAE,qBAAqB;IAE1C;;OAEG;IACH,sBAAsB,EAAE,wBAAwB;IAEhD;;OAEG;IACH,sBAAsB,EAAE,wBAAwB;IAEhD;;OAEG;IACH,yBAAyB,EAAE,2BAA2B;IAEtD;;OAEG;IACH,YAAY,EAAE,cAAc;IAE5B;;;;OAIG;IACH,UAAU,EAAE,YAAY;IAExB;;OAEG;IACH,uBAAuB,EAAE,yBAAyB;IAElD;;OAEG;IACH,yBAAyB,EAAE,2BAA2B;CACvD,CAAC;AAEW,QAAA,cAAc,GAAG;IAC5B,yDAAyD;IACzD,SAAS,EAAE,WAAW;IACtB,4BAA4B;IAC5B,KAAK,EAAE,OAAO;IACd,aAAa;IACb,KAAK,EAAE,OAAO;IACd,uBAAuB;IACvB,MAAM,EAAE,QAAQ;IAChB,eAAe;IACf,GAAG,EAAE,KAAK;IACV,kBAAkB;IAClB,UAAU,EAAE,YAAY;IACxB,uBAAuB;IACvB,QAAQ,EAAE,UAAU;IACpB,mBAAmB;IACnB,IAAI,EAAE,MAAM;IACZ,kBAAkB;IAClB,UAAU,EAAE,YAAY;IACxB,yBAAyB;IACzB,MAAM,EAAE,QAAQ;IAChB,yBAAyB;IACzB,QAAQ,EAAE,UAAU;IACpB,iBAAiB;IACjB,KAAK,EAAE,OAAO;IACd,gBAAgB;IAChB,MAAM,EAAE,QAAQ;IAChB,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,gBAAgB;IAChB,QAAQ,EAAE,UAAU;IACpB,oBAAoB;IACpB,GAAG,EAAE,KAAK;IACV,0BAA0B;IAC1B,KAAK,EAAE,OAAO;IACd,0CAA0C;IAC1C,MAAM,EAAE,QAAQ;IAChB,gBAAgB;IAChB,QAAQ,EAAE,UAAU;IACpB,oBAAoB;IACpB,KAAK,EAAE,OAAO;IACd,iBAAiB;IACjB,SAAS,EAAE,WAAW;IACtB,gBAAgB;IAChB,QAAQ,EAAE,UAAU;IACpB,iBAAiB;IACjB,SAAS,EAAE,WAAW;IACtB,iBAAiB;IACjB,SAAS,EAAE,WAAW;IACtB,eAAe;IACf,OAAO,EAAE,SAAS;IAClB,eAAe;IACf,OAAO,EAAE,SAAS;IAClB,sBAAsB;IACtB,SAAS,EAAE,WAAW;IACtB,iBAAiB;IACjB,SAAS,EAAE,WAAW;IACtB,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,gBAAgB;IAChB,QAAQ,EAAE,UAAU;IACpB,eAAe;IACf,OAAO,EAAE,SAAS;IAClB,UAAU;IACV,EAAE,EAAE,IAAI;IACR,sBAAsB;IACtB,UAAU,EAAE,YAAY;IACxB,wBAAwB;IACxB,SAAS,EAAE,WAAW;IACtB,oBAAoB;IACpB,KAAK,EAAE,OAAO;IACd,eAAe;IACf,OAAO,EAAE,SAAS;IAClB,aAAa;IACb,KAAK,EAAE,OAAO;IACd,iBAAiB;IACjB,SAAS,EAAE,WAAW;IACtB,eAAe;IACf,OAAO,EAAE,SAAS;IAClB,iCAAiC;IACjC,QAAQ,EAAE,UAAU;IACpB,uBAAuB;IACvB,QAAQ,EAAE,UAAU;IACpB,aAAa;IACb,KAAK,EAAE,OAAO;IACd,oBAAoB;IACpB,KAAK,EAAE,OAAO;IACd,qBAAqB;IACrB,aAAa,EAAE,eAAe;IAC9B,iBAAiB;IACjB,SAAS,EAAE,WAAW;IACtB,mBAAmB;IACnB,WAAW,EAAE,aAAa;CAClB,CAAC;AAIE,QAAA,iCAAiC,GAAG;IAC/C,WAAW;IACX,GAAG,EAAE,KAAK;IACV,mBAAmB;IACnB,WAAW,EAAE,aAAa;IAC1B,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,oBAAoB;IACpB,YAAY,EAAE,cAAc;IAC5B,WAAW;IACX,GAAG,EAAE,KAAK;IACV,WAAW;IACX,GAAG,EAAE,KAAK;IACV,aAAa;IACb,KAAK,EAAE,OAAO;IACd,iBAAiB;IACjB,SAAS,EAAE,WAAW;IACtB,WAAW;IACX,GAAG,EAAE,KAAK;IACV,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,oBAAoB;IACpB,YAAY,EAAE,cAAc;CACpB,CAAC;AAIE,QAAA,iBAAiB,GAAG;IAC/B,4FAA4F;IAC5F,UAAU,EAAE,YAAY;IACxB,uDAAuD;IACvD,IAAI,EAAE,MAAM;IACZ,qFAAqF;IACrF,MAAM,EAAE,QAAQ;IAChB,wDAAwD;IACxD,KAAK,EAAE,OAAO;IACd,mCAAmC;IACnC,KAAK,EAAE,OAAO;CACN,CAAC;AAIE,QAAA,2BAA2B,GAAG;IACzC,oCAAoC;IACpC,MAAM,EAAE,QAAQ;IAChB,kCAAkC;IAClC,IAAI,EAAE,MAAM;IACZ,iCAAiC;IACjC,MAAM,EAAE,QAAQ;CACR,CAAC;AAIE,QAAA,yBAAyB,GAAG;IACvC,qBAAqB;IACrB,aAAa,EAAE,eAAe;IAC9B,2BAA2B;IAC3B,GAAG,EAAE,KAAK;IACV,uBAAuB;IACvB,KAAK,EAAE,OAAO;IACd,6BAA6B;IAC7B,GAAG,EAAE,KAAK;CACF,CAAC;AAIE,QAAA,kBAAkB,GAAG;IAChC,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,cAAc;IACd,MAAM,EAAE,QAAQ;IAChB,iCAAiC;IACjC,EAAE,EAAE,IAAI;IACR,qCAAqC;IACrC,IAAI,EAAE,MAAM;IACZ,+CAA+C;IAC/C,IAAI,EAAE,MAAM;IACZ,gCAAgC;IAChC,MAAM,EAAE,QAAQ;IAChB,qCAAqC;IACrC,KAAK,EAAE,OAAO;CACN,CAAC;AAIE,QAAA,2BAA2B,GAAG;IACzC,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,aAAa;IACb,KAAK,EAAE,OAAO;IACd,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,mBAAmB;IACnB,WAAW,EAAE,aAAa;IAC1B,eAAe;IACf,OAAO,EAAE,SAAS;CACV,CAAC;AAIE,QAAA,8BAA8B,GAAG;IAC5C,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,mBAAmB;IACnB,MAAM,EAAE,QAAQ;IAChB,mBAAmB;IACnB,MAAM,EAAE,QAAQ;IAChB,sBAAsB;IACtB,cAAc,EAAE,gBAAgB;IAChC,aAAa;IACb,KAAK,EAAE,OAAO;IACd,aAAa;IACb,KAAK,EAAE,OAAO;IACd,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,mBAAmB;IACnB,MAAM,EAAE,QAAQ;IAChB,WAAW;IACX,GAAG,EAAE,KAAK;IACV,aAAa;IACb,KAAK,EAAE,OAAO;IACd,aAAa;IACb,KAAK,EAAE,OAAO;IACd,WAAW;IACX,GAAG,EAAE,KAAK;IACV,gBAAgB;IAChB,QAAQ,EAAE,UAAU;IACpB,aAAa;IACb,KAAK,EAAE,OAAO;IACd,yBAAyB;IACzB,EAAE,EAAE,IAAI;IACR,2CAA2C;IAC3C,KAAK,EAAE,OAAO;IACd,cAAc;IACd,MAAM,EAAE,QAAQ;CACR,CAAC;AAIE,QAAA,gBAAgB,GAAG;IAC9B,gBAAgB;IAChB,QAAQ,EAAE,KAAK;IACf,gBAAgB;IAChB,QAAQ,EAAE,KAAK;IACf,cAAc;IACd,QAAQ,EAAE,KAAK;IACf,qBAAqB;IACrB,IAAI,EAAE,MAAM;IACZ,qBAAqB;IACrB,IAAI,EAAE,MAAM;CACJ,CAAC;AAIE,QAAA,8BAA8B,GAAG;IAC5C,iCAAiC;IACjC,KAAK,EAAE,OAAO;IACd,iCAAiC;IACjC,KAAK,EAAE,OAAO;CACN,CAAC;AAIE,QAAA,wBAAwB,GAAG;IACtC,eAAe;IACf,OAAO,EAAE,SAAS;IAClB,eAAe;IACf,OAAO,EAAE,SAAS;CACV,CAAC;AAIE,QAAA,uBAAuB,GAAG;IACrC,UAAU;IACV,EAAE,EAAE,CAAC;IACL,iBAAiB;IACjB,SAAS,EAAE,CAAC;IACZ,eAAe;IACf,OAAO,EAAE,CAAC;IACV,wBAAwB;IACxB,gBAAgB,EAAE,CAAC;IACnB,yBAAyB;IACzB,iBAAiB,EAAE,CAAC;IACpB,iBAAiB;IACjB,SAAS,EAAE,CAAC;IACZ,sBAAsB;IACtB,cAAc,EAAE,CAAC;IACjB,yBAAyB;IACzB,iBAAiB,EAAE,CAAC;IACpB,0BAA0B;IAC1B,kBAAkB,EAAE,CAAC;IACrB,2BAA2B;IAC3B,mBAAmB,EAAE,CAAC;IACtB,eAAe;IACf,OAAO,EAAE,EAAE;IACX,oBAAoB;IACpB,YAAY,EAAE,EAAE;IAChB,qBAAqB;IACrB,aAAa,EAAE,EAAE;IACjB,gBAAgB;IAChB,QAAQ,EAAE,EAAE;IACZ,mBAAmB;IACnB,WAAW,EAAE,EAAE;IACf,iBAAiB;IACjB,SAAS,EAAE,EAAE;IACb,uBAAuB;IACvB,eAAe,EAAE,EAAE;CACX,CAAC;AAIE,QAAA,iBAAiB,GAAG;IAC/B,YAAY;IACZ,IAAI,EAAE,MAAM;IACZ,gBAAgB;IAChB,QAAQ,EAAE,UAAU;CACZ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\nexport const SemanticAttributes = {\n  /**\n   * The full invoked ARN as provided on the `Context` passed to the function (`Lambda-Runtime-Invoked-Function-Arn` header on the `/runtime/invocation/next` applicable).\n   *\n   * Note: This may be different from `faas.id` if an alias is involved.\n   */\n  AWS_LAMBDA_INVOKED_ARN: 'aws.lambda.invoked_arn',\n\n  /**\n   * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.\n   */\n  DB_SYSTEM: 'db.system',\n\n  /**\n   * The connection string used to connect to the database. It is recommended to remove embedded credentials.\n   */\n  DB_CONNECTION_STRING: 'db.connection_string',\n\n  /**\n   * Username for accessing the database.\n   */\n  DB_USER: 'db.user',\n\n  /**\n   * The fully-qualified class name of the [Java Database Connectivity (JDBC)](https://docs.oracle.com/javase/8/docs/technotes/guides/jdbc/) driver used to connect.\n   */\n  DB_JDBC_DRIVER_CLASSNAME: 'db.jdbc.driver_classname',\n\n  /**\n   * If no [tech-specific attribute](#call-level-attributes-for-specific-technologies) is defined, this attribute is used to report the name of the database being accessed. For commands that switch the database, this should be set to the target database (even if the command fails).\n   *\n   * Note: In some SQL databases, the database name to be used is called &#34;schema name&#34;.\n   */\n  DB_NAME: 'db.name',\n\n  /**\n   * The database statement being executed.\n   *\n   * Note: The value may be sanitized to exclude sensitive information.\n   */\n  DB_STATEMENT: 'db.statement',\n\n  /**\n   * The name of the operation being executed, e.g. the [MongoDB command name](https://docs.mongodb.com/manual/reference/command/#database-operations) such as `findAndModify`, or the SQL keyword.\n   *\n   * Note: When setting this to an SQL keyword, it is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if the operation name is provided by the library being instrumented. If the SQL statement has an ambiguous operation, or performs more than one operation, this value may be omitted.\n   */\n  DB_OPERATION: 'db.operation',\n\n  /**\n   * The Microsoft SQL Server [instance name](https://docs.microsoft.com/en-us/sql/connect/jdbc/building-the-connection-url?view=sql-server-ver15) connecting to. This name is used to determine the port of a named instance.\n   *\n   * Note: If setting a `db.mssql.instance_name`, `net.peer.port` is no longer required (but still recommended if non-standard).\n   */\n  DB_MSSQL_INSTANCE_NAME: 'db.mssql.instance_name',\n\n  /**\n   * The name of the keyspace being accessed. To be used instead of the generic `db.name` attribute.\n   */\n  DB_CASSANDRA_KEYSPACE: 'db.cassandra.keyspace',\n\n  /**\n   * The fetch size used for paging, i.e. how many rows will be returned at once.\n   */\n  DB_CASSANDRA_PAGE_SIZE: 'db.cassandra.page_size',\n\n  /**\n   * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n   */\n  DB_CASSANDRA_CONSISTENCY_LEVEL: 'db.cassandra.consistency_level',\n\n  /**\n   * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n   *\n   * Note: This mirrors the db.sql.table attribute but references cassandra rather than sql. It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n   */\n  DB_CASSANDRA_TABLE: 'db.cassandra.table',\n\n  /**\n   * Whether or not the query is idempotent.\n   */\n  DB_CASSANDRA_IDEMPOTENCE: 'db.cassandra.idempotence',\n\n  /**\n   * The number of times a query was speculatively executed. Not set or `0` if the query was not executed speculatively.\n   */\n  DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT:\n    'db.cassandra.speculative_execution_count',\n\n  /**\n   * The ID of the coordinating node for a query.\n   */\n  DB_CASSANDRA_COORDINATOR_ID: 'db.cassandra.coordinator.id',\n\n  /**\n   * The data center of the coordinating node for a query.\n   */\n  DB_CASSANDRA_COORDINATOR_DC: 'db.cassandra.coordinator.dc',\n\n  /**\n   * The [HBase namespace](https://hbase.apache.org/book.html#_namespace) being accessed. To be used instead of the generic `db.name` attribute.\n   */\n  DB_HBASE_NAMESPACE: 'db.hbase.namespace',\n\n  /**\n   * The index of the database being accessed as used in the [`SELECT` command](https://redis.io/commands/select), provided as an integer. To be used instead of the generic `db.name` attribute.\n   */\n  DB_REDIS_DATABASE_INDEX: 'db.redis.database_index',\n\n  /**\n   * The collection being accessed within the database stated in `db.name`.\n   */\n  DB_MONGODB_COLLECTION: 'db.mongodb.collection',\n\n  /**\n   * The name of the primary table that the operation is acting upon, including the schema name (if applicable).\n   *\n   * Note: It is not recommended to attempt any client-side parsing of `db.statement` just to get this property, but it should be set if it is provided by the library being instrumented. If the operation is acting upon an anonymous table, or more than one table, this value MUST NOT be set.\n   */\n  DB_SQL_TABLE: 'db.sql.table',\n\n  /**\n   * The type of the exception (its fully-qualified class name, if applicable). The dynamic type of the exception should be preferred over the static type in languages that support it.\n   */\n  EXCEPTION_TYPE: 'exception.type',\n\n  /**\n   * The exception message.\n   */\n  EXCEPTION_MESSAGE: 'exception.message',\n\n  /**\n   * A stacktrace as a string in the natural representation for the language runtime. The representation is to be determined and documented by each language SIG.\n   */\n  EXCEPTION_STACKTRACE: 'exception.stacktrace',\n\n  /**\n  * SHOULD be set to true if the exception event is recorded at a point where it is known that the exception is escaping the scope of the span.\n  *\n  * Note: An exception is considered to have escaped (or left) the scope of a span,\nif that span is ended while the exception is still logically &#34;in flight&#34;.\nThis may be actually &#34;in flight&#34; in some languages (e.g. if the exception\nis passed to a Context manager&#39;s `__exit__` method in Python) but will\nusually be caught at the point of recording the exception in most languages.\n\nIt is usually not possible to determine at the point where an exception is thrown\nwhether it will escape the scope of a span.\nHowever, it is trivial to know that an exception\nwill escape, if one checks for an active exception just before ending the span,\nas done in the [example above](#exception-end-example).\n\nIt follows that an exception may still escape the scope of the span\neven if the `exception.escaped` attribute was not set or set to false,\nsince the event might have been recorded at a time where it was not\nclear whether the exception will escape.\n  */\n  EXCEPTION_ESCAPED: 'exception.escaped',\n\n  /**\n   * Type of the trigger on which the function is executed.\n   */\n  FAAS_TRIGGER: 'faas.trigger',\n\n  /**\n   * The execution ID of the current function execution.\n   */\n  FAAS_EXECUTION: 'faas.execution',\n\n  /**\n   * The name of the source on which the triggering operation was performed. For example, in Cloud Storage or S3 corresponds to the bucket name, and in Cosmos DB to the database name.\n   */\n  FAAS_DOCUMENT_COLLECTION: 'faas.document.collection',\n\n  /**\n   * Describes the type of the operation that was performed on the data.\n   */\n  FAAS_DOCUMENT_OPERATION: 'faas.document.operation',\n\n  /**\n   * A string containing the time when the data was accessed in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n   */\n  FAAS_DOCUMENT_TIME: 'faas.document.time',\n\n  /**\n   * The document name/table subjected to the operation. For example, in Cloud Storage or S3 is the name of the file, and in Cosmos DB the table name.\n   */\n  FAAS_DOCUMENT_NAME: 'faas.document.name',\n\n  /**\n   * A string containing the function invocation time in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n   */\n  FAAS_TIME: 'faas.time',\n\n  /**\n   * A string containing the schedule period as [Cron Expression](https://docs.oracle.com/cd/E12058_01/doc/doc.1014/e12030/cron_expressions.htm).\n   */\n  FAAS_CRON: 'faas.cron',\n\n  /**\n   * A boolean that is true if the serverless function is executed for the first time (aka cold-start).\n   */\n  FAAS_COLDSTART: 'faas.coldstart',\n\n  /**\n   * The name of the invoked function.\n   *\n   * Note: SHOULD be equal to the `faas.name` resource attribute of the invoked function.\n   */\n  FAAS_INVOKED_NAME: 'faas.invoked_name',\n\n  /**\n   * The cloud provider of the invoked function.\n   *\n   * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.\n   */\n  FAAS_INVOKED_PROVIDER: 'faas.invoked_provider',\n\n  /**\n   * The cloud region of the invoked function.\n   *\n   * Note: SHOULD be equal to the `cloud.region` resource attribute of the invoked function.\n   */\n  FAAS_INVOKED_REGION: 'faas.invoked_region',\n\n  /**\n   * Transport protocol used. See note below.\n   */\n  NET_TRANSPORT: 'net.transport',\n\n  /**\n   * Remote address of the peer (dotted decimal for IPv4 or [RFC5952](https://tools.ietf.org/html/rfc5952) for IPv6).\n   */\n  NET_PEER_IP: 'net.peer.ip',\n\n  /**\n   * Remote port number.\n   */\n  NET_PEER_PORT: 'net.peer.port',\n\n  /**\n   * Remote hostname or similar, see note below.\n   */\n  NET_PEER_NAME: 'net.peer.name',\n\n  /**\n   * Like `net.peer.ip` but for the host IP. Useful in case of a multi-IP host.\n   */\n  NET_HOST_IP: 'net.host.ip',\n\n  /**\n   * Like `net.peer.port` but for the host port.\n   */\n  NET_HOST_PORT: 'net.host.port',\n\n  /**\n   * Local hostname or similar, see note below.\n   */\n  NET_HOST_NAME: 'net.host.name',\n\n  /**\n   * The internet connection type currently being used by the host.\n   */\n  NET_HOST_CONNECTION_TYPE: 'net.host.connection.type',\n\n  /**\n   * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n   */\n  NET_HOST_CONNECTION_SUBTYPE: 'net.host.connection.subtype',\n\n  /**\n   * The name of the mobile carrier.\n   */\n  NET_HOST_CARRIER_NAME: 'net.host.carrier.name',\n\n  /**\n   * The mobile carrier country code.\n   */\n  NET_HOST_CARRIER_MCC: 'net.host.carrier.mcc',\n\n  /**\n   * The mobile carrier network code.\n   */\n  NET_HOST_CARRIER_MNC: 'net.host.carrier.mnc',\n\n  /**\n   * The ISO 3166-1 alpha-2 2-character country code associated with the mobile carrier network.\n   */\n  NET_HOST_CARRIER_ICC: 'net.host.carrier.icc',\n\n  /**\n   * The [`service.name`](../../resource/semantic_conventions/README.md#service) of the remote service. SHOULD be equal to the actual `service.name` resource attribute of the remote service if any.\n   */\n  PEER_SERVICE: 'peer.service',\n\n  /**\n   * Username or client_id extracted from the access token or [Authorization](https://tools.ietf.org/html/rfc7235#section-4.2) header in the inbound request from outside the system.\n   */\n  ENDUSER_ID: 'enduser.id',\n\n  /**\n   * Actual/assumed role the client is making the request under extracted from token or application security context.\n   */\n  ENDUSER_ROLE: 'enduser.role',\n\n  /**\n   * Scopes or granted authorities the client currently possesses extracted from token or application security context. The value would come from the scope associated with an [OAuth 2.0 Access Token](https://tools.ietf.org/html/rfc6749#section-3.3) or an attribute value in a [SAML 2.0 Assertion](http://docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html).\n   */\n  ENDUSER_SCOPE: 'enduser.scope',\n\n  /**\n   * Current &#34;managed&#34; thread ID (as opposed to OS thread ID).\n   */\n  THREAD_ID: 'thread.id',\n\n  /**\n   * Current thread name.\n   */\n  THREAD_NAME: 'thread.name',\n\n  /**\n   * The method or function name, or equivalent (usually rightmost part of the code unit&#39;s name).\n   */\n  CODE_FUNCTION: 'code.function',\n\n  /**\n   * The &#34;namespace&#34; within which `code.function` is defined. Usually the qualified class or module name, such that `code.namespace` + some separator + `code.function` form a unique identifier for the code unit.\n   */\n  CODE_NAMESPACE: 'code.namespace',\n\n  /**\n   * The source code file name that identifies the code unit as uniquely as possible (preferably an absolute file path).\n   */\n  CODE_FILEPATH: 'code.filepath',\n\n  /**\n   * The line number in `code.filepath` best representing the operation. It SHOULD point within the code unit named in `code.function`.\n   */\n  CODE_LINENO: 'code.lineno',\n\n  /**\n   * HTTP request method.\n   */\n  HTTP_METHOD: 'http.method',\n\n  /**\n   * Full HTTP request URL in the form `scheme://host[:port]/path?query[#fragment]`. Usually the fragment is not transmitted over HTTP, but if it is known, it should be included nevertheless.\n   *\n   * Note: `http.url` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case the attribute&#39;s value should be `https://www.example.com/`.\n   */\n  HTTP_URL: 'http.url',\n\n  /**\n   * The full request target as passed in a HTTP request line or equivalent.\n   */\n  HTTP_TARGET: 'http.target',\n\n  /**\n   * The value of the [HTTP host header](https://tools.ietf.org/html/rfc7230#section-5.4). An empty Host header should also be reported, see note.\n   *\n   * Note: When the header is present but empty the attribute SHOULD be set to the empty string. Note that this is a valid situation that is expected in certain cases, according the aforementioned [section of RFC 7230](https://tools.ietf.org/html/rfc7230#section-5.4). When the header is not set the attribute MUST NOT be set.\n   */\n  HTTP_HOST: 'http.host',\n\n  /**\n   * The URI scheme identifying the used protocol.\n   */\n  HTTP_SCHEME: 'http.scheme',\n\n  /**\n   * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n   */\n  HTTP_STATUS_CODE: 'http.status_code',\n\n  /**\n   * Kind of HTTP protocol used.\n   *\n   * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.\n   */\n  HTTP_FLAVOR: 'http.flavor',\n\n  /**\n   * Value of the [HTTP User-Agent](https://tools.ietf.org/html/rfc7231#section-5.5.3) header sent by the client.\n   */\n  HTTP_USER_AGENT: 'http.user_agent',\n\n  /**\n   * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n   */\n  HTTP_REQUEST_CONTENT_LENGTH: 'http.request_content_length',\n\n  /**\n   * The size of the uncompressed request payload body after transport decoding. Not set if transport encoding not used.\n   */\n  HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED:\n    'http.request_content_length_uncompressed',\n\n  /**\n   * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://tools.ietf.org/html/rfc7230#section-3.3.2) header. For requests using transport encoding, this should be the compressed size.\n   */\n  HTTP_RESPONSE_CONTENT_LENGTH: 'http.response_content_length',\n\n  /**\n   * The size of the uncompressed response payload body after transport decoding. Not set if transport encoding not used.\n   */\n  HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED:\n    'http.response_content_length_uncompressed',\n\n  /**\n   * The primary server name of the matched virtual host. This should be obtained via configuration. If no such configuration can be obtained, this attribute MUST NOT be set ( `net.host.name` should be used instead).\n   *\n   * Note: `http.url` is usually not readily available on the server side but would have to be assembled in a cumbersome and sometimes lossy process from other information (see e.g. open-telemetry/opentelemetry-python/pull/148). It is thus preferred to supply the raw data that is available.\n   */\n  HTTP_SERVER_NAME: 'http.server_name',\n\n  /**\n   * The matched route (path template).\n   */\n  HTTP_ROUTE: 'http.route',\n\n  /**\n  * The IP address of the original client behind all proxies, if known (e.g. from [X-Forwarded-For](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Forwarded-For)).\n  *\n  * Note: This is not necessarily the same as `net.peer.ip`, which would\nidentify the network-level peer, which may be a proxy.\n\nThis attribute should be set when a source of information different\nfrom the one used for `net.peer.ip`, is available even if that other\nsource just confirms the same value as `net.peer.ip`.\nRationale: For `net.peer.ip`, one typically does not know if it\ncomes from a proxy, reverse proxy, or the actual client. Setting\n`http.client_ip` when it&#39;s the same as `net.peer.ip` means that\none is at least somewhat confident that the address is not that of\nthe closest proxy.\n  */\n  HTTP_CLIENT_IP: 'http.client_ip',\n\n  /**\n   * The keys in the `RequestItems` object field.\n   */\n  AWS_DYNAMODB_TABLE_NAMES: 'aws.dynamodb.table_names',\n\n  /**\n   * The JSON-serialized value of each item in the `ConsumedCapacity` response field.\n   */\n  AWS_DYNAMODB_CONSUMED_CAPACITY: 'aws.dynamodb.consumed_capacity',\n\n  /**\n   * The JSON-serialized value of the `ItemCollectionMetrics` response field.\n   */\n  AWS_DYNAMODB_ITEM_COLLECTION_METRICS: 'aws.dynamodb.item_collection_metrics',\n\n  /**\n   * The value of the `ProvisionedThroughput.ReadCapacityUnits` request parameter.\n   */\n  AWS_DYNAMODB_PROVISIONED_READ_CAPACITY:\n    'aws.dynamodb.provisioned_read_capacity',\n\n  /**\n   * The value of the `ProvisionedThroughput.WriteCapacityUnits` request parameter.\n   */\n  AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY:\n    'aws.dynamodb.provisioned_write_capacity',\n\n  /**\n   * The value of the `ConsistentRead` request parameter.\n   */\n  AWS_DYNAMODB_CONSISTENT_READ: 'aws.dynamodb.consistent_read',\n\n  /**\n   * The value of the `ProjectionExpression` request parameter.\n   */\n  AWS_DYNAMODB_PROJECTION: 'aws.dynamodb.projection',\n\n  /**\n   * The value of the `Limit` request parameter.\n   */\n  AWS_DYNAMODB_LIMIT: 'aws.dynamodb.limit',\n\n  /**\n   * The value of the `AttributesToGet` request parameter.\n   */\n  AWS_DYNAMODB_ATTRIBUTES_TO_GET: 'aws.dynamodb.attributes_to_get',\n\n  /**\n   * The value of the `IndexName` request parameter.\n   */\n  AWS_DYNAMODB_INDEX_NAME: 'aws.dynamodb.index_name',\n\n  /**\n   * The value of the `Select` request parameter.\n   */\n  AWS_DYNAMODB_SELECT: 'aws.dynamodb.select',\n\n  /**\n   * The JSON-serialized value of each item of the `GlobalSecondaryIndexes` request field.\n   */\n  AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES:\n    'aws.dynamodb.global_secondary_indexes',\n\n  /**\n   * The JSON-serialized value of each item of the `LocalSecondaryIndexes` request field.\n   */\n  AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES: 'aws.dynamodb.local_secondary_indexes',\n\n  /**\n   * The value of the `ExclusiveStartTableName` request parameter.\n   */\n  AWS_DYNAMODB_EXCLUSIVE_START_TABLE: 'aws.dynamodb.exclusive_start_table',\n\n  /**\n   * The the number of items in the `TableNames` response parameter.\n   */\n  AWS_DYNAMODB_TABLE_COUNT: 'aws.dynamodb.table_count',\n\n  /**\n   * The value of the `ScanIndexForward` request parameter.\n   */\n  AWS_DYNAMODB_SCAN_FORWARD: 'aws.dynamodb.scan_forward',\n\n  /**\n   * The value of the `Segment` request parameter.\n   */\n  AWS_DYNAMODB_SEGMENT: 'aws.dynamodb.segment',\n\n  /**\n   * The value of the `TotalSegments` request parameter.\n   */\n  AWS_DYNAMODB_TOTAL_SEGMENTS: 'aws.dynamodb.total_segments',\n\n  /**\n   * The value of the `Count` response parameter.\n   */\n  AWS_DYNAMODB_COUNT: 'aws.dynamodb.count',\n\n  /**\n   * The value of the `ScannedCount` response parameter.\n   */\n  AWS_DYNAMODB_SCANNED_COUNT: 'aws.dynamodb.scanned_count',\n\n  /**\n   * The JSON-serialized value of each item in the `AttributeDefinitions` request field.\n   */\n  AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS: 'aws.dynamodb.attribute_definitions',\n\n  /**\n   * The JSON-serialized value of each item in the the `GlobalSecondaryIndexUpdates` request field.\n   */\n  AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES:\n    'aws.dynamodb.global_secondary_index_updates',\n\n  /**\n   * A string identifying the messaging system.\n   */\n  MESSAGING_SYSTEM: 'messaging.system',\n\n  /**\n   * The message destination name. This might be equal to the span name but is required nevertheless.\n   */\n  MESSAGING_DESTINATION: 'messaging.destination',\n\n  /**\n   * The kind of message destination.\n   */\n  MESSAGING_DESTINATION_KIND: 'messaging.destination_kind',\n\n  /**\n   * A boolean that is true if the message destination is temporary.\n   */\n  MESSAGING_TEMP_DESTINATION: 'messaging.temp_destination',\n\n  /**\n   * The name of the transport protocol.\n   */\n  MESSAGING_PROTOCOL: 'messaging.protocol',\n\n  /**\n   * The version of the transport protocol.\n   */\n  MESSAGING_PROTOCOL_VERSION: 'messaging.protocol_version',\n\n  /**\n   * Connection string.\n   */\n  MESSAGING_URL: 'messaging.url',\n\n  /**\n   * A value used by the messaging system as an identifier for the message, represented as a string.\n   */\n  MESSAGING_MESSAGE_ID: 'messaging.message_id',\n\n  /**\n   * The [conversation ID](#conversations) identifying the conversation to which the message belongs, represented as a string. Sometimes called &#34;Correlation ID&#34;.\n   */\n  MESSAGING_CONVERSATION_ID: 'messaging.conversation_id',\n\n  /**\n   * The (uncompressed) size of the message payload in bytes. Also use this attribute if it is unknown whether the compressed or uncompressed payload size is reported.\n   */\n  MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES: 'messaging.message_payload_size_bytes',\n\n  /**\n   * The compressed size of the message payload in bytes.\n   */\n  MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES:\n    'messaging.message_payload_compressed_size_bytes',\n\n  /**\n   * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.\n   */\n  MESSAGING_OPERATION: 'messaging.operation',\n\n  /**\n   * The identifier for the consumer receiving a message. For Kafka, set it to `{messaging.kafka.consumer_group} - {messaging.kafka.client_id}`, if both are present, or only `messaging.kafka.consumer_group`. For brokers, such as RabbitMQ and Artemis, set it to the `client_id` of the client consuming the message.\n   */\n  MESSAGING_CONSUMER_ID: 'messaging.consumer_id',\n\n  /**\n   * RabbitMQ message routing key.\n   */\n  MESSAGING_RABBITMQ_ROUTING_KEY: 'messaging.rabbitmq.routing_key',\n\n  /**\n   * Message keys in Kafka are used for grouping alike messages to ensure they&#39;re processed on the same partition. They differ from `messaging.message_id` in that they&#39;re not unique. If the key is `null`, the attribute MUST NOT be set.\n   *\n   * Note: If the key type is not string, it&#39;s string representation has to be supplied for the attribute. If the key has no unambiguous, canonical string form, don&#39;t include its value.\n   */\n  MESSAGING_KAFKA_MESSAGE_KEY: 'messaging.kafka.message_key',\n\n  /**\n   * Name of the Kafka Consumer Group that is handling the message. Only applies to consumers, not producers.\n   */\n  MESSAGING_KAFKA_CONSUMER_GROUP: 'messaging.kafka.consumer_group',\n\n  /**\n   * Client Id for the Consumer or Producer that is handling the message.\n   */\n  MESSAGING_KAFKA_CLIENT_ID: 'messaging.kafka.client_id',\n\n  /**\n   * Partition the message is sent to.\n   */\n  MESSAGING_KAFKA_PARTITION: 'messaging.kafka.partition',\n\n  /**\n   * A boolean that is true if the message is a tombstone.\n   */\n  MESSAGING_KAFKA_TOMBSTONE: 'messaging.kafka.tombstone',\n\n  /**\n   * A string identifying the remoting system.\n   */\n  RPC_SYSTEM: 'rpc.system',\n\n  /**\n   * The full (logical) name of the service being called, including its package name, if applicable.\n   *\n   * Note: This is the logical name of the service from the RPC interface perspective, which can be different from the name of any implementing class. The `code.namespace` attribute may be used to store the latter (despite the attribute name, it may include a class name; e.g., class with method actually executing the call on the server side, RPC client stub class on the client side).\n   */\n  RPC_SERVICE: 'rpc.service',\n\n  /**\n   * The name of the (logical) method being called, must be equal to the $method part in the span name.\n   *\n   * Note: This is the logical name of the method from the RPC interface perspective, which can be different from the name of any implementing method/function. The `code.function` attribute may be used to store the latter (e.g., method actually executing the call on the server side, RPC client stub method on the client side).\n   */\n  RPC_METHOD: 'rpc.method',\n\n  /**\n   * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n   */\n  RPC_GRPC_STATUS_CODE: 'rpc.grpc.status_code',\n\n  /**\n   * Protocol version as in `jsonrpc` property of request/response. Since JSON-RPC 1.0 does not specify this, the value can be omitted.\n   */\n  RPC_JSONRPC_VERSION: 'rpc.jsonrpc.version',\n\n  /**\n   * `id` property of request or response. Since protocol allows id to be int, string, `null` or missing (for notifications), value is expected to be cast to string for simplicity. Use empty string in case of `null` value. Omit entirely if this is a notification.\n   */\n  RPC_JSONRPC_REQUEST_ID: 'rpc.jsonrpc.request_id',\n\n  /**\n   * `error.code` property of response if it is an error response.\n   */\n  RPC_JSONRPC_ERROR_CODE: 'rpc.jsonrpc.error_code',\n\n  /**\n   * `error.message` property of response if it is an error response.\n   */\n  RPC_JSONRPC_ERROR_MESSAGE: 'rpc.jsonrpc.error_message',\n\n  /**\n   * Whether this is a received or sent message.\n   */\n  MESSAGE_TYPE: 'message.type',\n\n  /**\n   * MUST be calculated as two different counters starting from `1` one for sent messages and one for received message.\n   *\n   * Note: This way we guarantee that the values will be consistent between different implementations.\n   */\n  MESSAGE_ID: 'message.id',\n\n  /**\n   * Compressed size of the message in bytes.\n   */\n  MESSAGE_COMPRESSED_SIZE: 'message.compressed_size',\n\n  /**\n   * Uncompressed size of the message in bytes.\n   */\n  MESSAGE_UNCOMPRESSED_SIZE: 'message.uncompressed_size',\n};\n\nexport const DbSystemValues = {\n  /** Some other SQL database. Fallback only. See notes. */\n  OTHER_SQL: 'other_sql',\n  /** Microsoft SQL Server. */\n  MSSQL: 'mssql',\n  /** MySQL. */\n  MYSQL: 'mysql',\n  /** Oracle Database. */\n  ORACLE: 'oracle',\n  /** IBM Db2. */\n  DB2: 'db2',\n  /** PostgreSQL. */\n  POSTGRESQL: 'postgresql',\n  /** Amazon Redshift. */\n  REDSHIFT: 'redshift',\n  /** Apache Hive. */\n  HIVE: 'hive',\n  /** Cloudscape. */\n  CLOUDSCAPE: 'cloudscape',\n  /** HyperSQL DataBase. */\n  HSQLDB: 'hsqldb',\n  /** Progress Database. */\n  PROGRESS: 'progress',\n  /** SAP MaxDB. */\n  MAXDB: 'maxdb',\n  /** SAP HANA. */\n  HANADB: 'hanadb',\n  /** Ingres. */\n  INGRES: 'ingres',\n  /** FirstSQL. */\n  FIRSTSQL: 'firstsql',\n  /** EnterpriseDB. */\n  EDB: 'edb',\n  /** InterSystems Caché. */\n  CACHE: 'cache',\n  /** Adabas (Adaptable Database System). */\n  ADABAS: 'adabas',\n  /** Firebird. */\n  FIREBIRD: 'firebird',\n  /** Apache Derby. */\n  DERBY: 'derby',\n  /** FileMaker. */\n  FILEMAKER: 'filemaker',\n  /** Informix. */\n  INFORMIX: 'informix',\n  /** InstantDB. */\n  INSTANTDB: 'instantdb',\n  /** InterBase. */\n  INTERBASE: 'interbase',\n  /** MariaDB. */\n  MARIADB: 'mariadb',\n  /** Netezza. */\n  NETEZZA: 'netezza',\n  /** Pervasive PSQL. */\n  PERVASIVE: 'pervasive',\n  /** PointBase. */\n  POINTBASE: 'pointbase',\n  /** SQLite. */\n  SQLITE: 'sqlite',\n  /** Sybase. */\n  SYBASE: 'sybase',\n  /** Teradata. */\n  TERADATA: 'teradata',\n  /** Vertica. */\n  VERTICA: 'vertica',\n  /** H2. */\n  H2: 'h2',\n  /** ColdFusion IMQ. */\n  COLDFUSION: 'coldfusion',\n  /** Apache Cassandra. */\n  CASSANDRA: 'cassandra',\n  /** Apache HBase. */\n  HBASE: 'hbase',\n  /** MongoDB. */\n  MONGODB: 'mongodb',\n  /** Redis. */\n  REDIS: 'redis',\n  /** Couchbase. */\n  COUCHBASE: 'couchbase',\n  /** CouchDB. */\n  COUCHDB: 'couchdb',\n  /** Microsoft Azure Cosmos DB. */\n  COSMOSDB: 'cosmosdb',\n  /** Amazon DynamoDB. */\n  DYNAMODB: 'dynamodb',\n  /** Neo4j. */\n  NEO4J: 'neo4j',\n  /** Apache Geode. */\n  GEODE: 'geode',\n  /** Elasticsearch. */\n  ELASTICSEARCH: 'elasticsearch',\n  /** Memcached. */\n  MEMCACHED: 'memcached',\n  /** CockroachDB. */\n  COCKROACHDB: 'cockroachdb',\n} as const;\nexport type DbSystemValues =\n  (typeof DbSystemValues)[keyof typeof DbSystemValues];\n\nexport const DbCassandraConsistencyLevelValues = {\n  /** all. */\n  ALL: 'all',\n  /** each_quorum. */\n  EACH_QUORUM: 'each_quorum',\n  /** quorum. */\n  QUORUM: 'quorum',\n  /** local_quorum. */\n  LOCAL_QUORUM: 'local_quorum',\n  /** one. */\n  ONE: 'one',\n  /** two. */\n  TWO: 'two',\n  /** three. */\n  THREE: 'three',\n  /** local_one. */\n  LOCAL_ONE: 'local_one',\n  /** any. */\n  ANY: 'any',\n  /** serial. */\n  SERIAL: 'serial',\n  /** local_serial. */\n  LOCAL_SERIAL: 'local_serial',\n} as const;\nexport type DbCassandraConsistencyLevelValues =\n  (typeof DbCassandraConsistencyLevelValues)[keyof typeof DbCassandraConsistencyLevelValues];\n\nexport const FaasTriggerValues = {\n  /** A response to some data source operation such as a database or filesystem read/write. */\n  DATASOURCE: 'datasource',\n  /** To provide an answer to an inbound HTTP request. */\n  HTTP: 'http',\n  /** A function is set to be executed when messages are sent to a messaging system. */\n  PUBSUB: 'pubsub',\n  /** A function is scheduled to be executed regularly. */\n  TIMER: 'timer',\n  /** If none of the others apply. */\n  OTHER: 'other',\n} as const;\nexport type FaasTriggerValues =\n  (typeof FaasTriggerValues)[keyof typeof FaasTriggerValues];\n\nexport const FaasDocumentOperationValues = {\n  /** When a new object is created. */\n  INSERT: 'insert',\n  /** When an object is modified. */\n  EDIT: 'edit',\n  /** When an object is deleted. */\n  DELETE: 'delete',\n} as const;\nexport type FaasDocumentOperationValues =\n  (typeof FaasDocumentOperationValues)[keyof typeof FaasDocumentOperationValues];\n\nexport const FaasInvokedProviderValues = {\n  /** Alibaba Cloud. */\n  ALIBABA_CLOUD: 'alibaba_cloud',\n  /** Amazon Web Services. */\n  AWS: 'aws',\n  /** Microsoft Azure. */\n  AZURE: 'azure',\n  /** Google Cloud Platform. */\n  GCP: 'gcp',\n} as const;\nexport type FaasInvokedProviderValues =\n  (typeof FaasInvokedProviderValues)[keyof typeof FaasInvokedProviderValues];\n\nexport const NetTransportValues = {\n  /** ip_tcp. */\n  IP_TCP: 'ip_tcp',\n  /** ip_udp. */\n  IP_UDP: 'ip_udp',\n  /** Another IP-based protocol. */\n  IP: 'ip',\n  /** Unix Domain socket. See below. */\n  UNIX: 'unix',\n  /** Named or anonymous pipe. See note below. */\n  PIPE: 'pipe',\n  /** In-process communication. */\n  INPROC: 'inproc',\n  /** Something else (non IP-based). */\n  OTHER: 'other',\n} as const;\nexport type NetTransportValues =\n  (typeof NetTransportValues)[keyof typeof NetTransportValues];\n\nexport const NetHostConnectionTypeValues = {\n  /** wifi. */\n  WIFI: 'wifi',\n  /** wired. */\n  WIRED: 'wired',\n  /** cell. */\n  CELL: 'cell',\n  /** unavailable. */\n  UNAVAILABLE: 'unavailable',\n  /** unknown. */\n  UNKNOWN: 'unknown',\n} as const;\nexport type NetHostConnectionTypeValues =\n  (typeof NetHostConnectionTypeValues)[keyof typeof NetHostConnectionTypeValues];\n\nexport const NetHostConnectionSubtypeValues = {\n  /** GPRS. */\n  GPRS: 'gprs',\n  /** EDGE. */\n  EDGE: 'edge',\n  /** UMTS. */\n  UMTS: 'umts',\n  /** CDMA. */\n  CDMA: 'cdma',\n  /** EVDO Rel. 0. */\n  EVDO_0: 'evdo_0',\n  /** EVDO Rev. A. */\n  EVDO_A: 'evdo_a',\n  /** CDMA2000 1XRTT. */\n  CDMA2000_1XRTT: 'cdma2000_1xrtt',\n  /** HSDPA. */\n  HSDPA: 'hsdpa',\n  /** HSUPA. */\n  HSUPA: 'hsupa',\n  /** HSPA. */\n  HSPA: 'hspa',\n  /** IDEN. */\n  IDEN: 'iden',\n  /** EVDO Rev. B. */\n  EVDO_B: 'evdo_b',\n  /** LTE. */\n  LTE: 'lte',\n  /** EHRPD. */\n  EHRPD: 'ehrpd',\n  /** HSPAP. */\n  HSPAP: 'hspap',\n  /** GSM. */\n  GSM: 'gsm',\n  /** TD-SCDMA. */\n  TD_SCDMA: 'td_scdma',\n  /** IWLAN. */\n  IWLAN: 'iwlan',\n  /** 5G NR (New Radio). */\n  NR: 'nr',\n  /** 5G NRNSA (New Radio Non-Standalone). */\n  NRNSA: 'nrnsa',\n  /** LTE CA. */\n  LTE_CA: 'lte_ca',\n} as const;\nexport type NetHostConnectionSubtypeValues =\n  (typeof NetHostConnectionSubtypeValues)[keyof typeof NetHostConnectionSubtypeValues];\n\nexport const HttpFlavorValues = {\n  /** HTTP 1.0. */\n  HTTP_1_0: '1.0',\n  /** HTTP 1.1. */\n  HTTP_1_1: '1.1',\n  /** HTTP 2. */\n  HTTP_2_0: '2.0',\n  /** SPDY protocol. */\n  SPDY: 'SPDY',\n  /** QUIC protocol. */\n  QUIC: 'QUIC',\n} as const;\nexport type HttpFlavorValues =\n  (typeof HttpFlavorValues)[keyof typeof HttpFlavorValues];\n\nexport const MessagingDestinationKindValues = {\n  /** A message sent to a queue. */\n  QUEUE: 'queue',\n  /** A message sent to a topic. */\n  TOPIC: 'topic',\n} as const;\nexport type MessagingDestinationKindValues =\n  (typeof MessagingDestinationKindValues)[keyof typeof MessagingDestinationKindValues];\n\nexport const MessagingOperationValues = {\n  /** receive. */\n  RECEIVE: 'receive',\n  /** process. */\n  PROCESS: 'process',\n} as const;\nexport type MessagingOperationValues =\n  (typeof MessagingOperationValues)[keyof typeof MessagingOperationValues];\n\nexport const RpcGrpcStatusCodeValues = {\n  /** OK. */\n  OK: 0,\n  /** CANCELLED. */\n  CANCELLED: 1,\n  /** UNKNOWN. */\n  UNKNOWN: 2,\n  /** INVALID_ARGUMENT. */\n  INVALID_ARGUMENT: 3,\n  /** DEADLINE_EXCEEDED. */\n  DEADLINE_EXCEEDED: 4,\n  /** NOT_FOUND. */\n  NOT_FOUND: 5,\n  /** ALREADY_EXISTS. */\n  ALREADY_EXISTS: 6,\n  /** PERMISSION_DENIED. */\n  PERMISSION_DENIED: 7,\n  /** RESOURCE_EXHAUSTED. */\n  RESOURCE_EXHAUSTED: 8,\n  /** FAILED_PRECONDITION. */\n  FAILED_PRECONDITION: 9,\n  /** ABORTED. */\n  ABORTED: 10,\n  /** OUT_OF_RANGE. */\n  OUT_OF_RANGE: 11,\n  /** UNIMPLEMENTED. */\n  UNIMPLEMENTED: 12,\n  /** INTERNAL. */\n  INTERNAL: 13,\n  /** UNAVAILABLE. */\n  UNAVAILABLE: 14,\n  /** DATA_LOSS. */\n  DATA_LOSS: 15,\n  /** UNAUTHENTICATED. */\n  UNAUTHENTICATED: 16,\n} as const;\nexport type RpcGrpcStatusCodeValues =\n  (typeof RpcGrpcStatusCodeValues)[keyof typeof RpcGrpcStatusCodeValues];\n\nexport const MessageTypeValues = {\n  /** sent. */\n  SENT: 'SENT',\n  /** received. */\n  RECEIVED: 'RECEIVED',\n} as const;\nexport type MessageTypeValues =\n  (typeof MessageTypeValues)[keyof typeof MessageTypeValues];\n"]}