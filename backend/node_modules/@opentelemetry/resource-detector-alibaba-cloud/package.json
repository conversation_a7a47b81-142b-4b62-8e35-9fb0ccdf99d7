{"name": "@opentelemetry/resource-detector-alibaba-cloud", "version": "0.28.10", "description": "OpenTelemetry resource detector for Alibaba Cloud", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/resource-detector-alibaba-cloud --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "tdd": "npm run test -- --watch-extensions ts --watch", "watch": "tsc -w"}, "keywords": ["opentelemetry", "nodejs", "resources", "stats", "alibaba-cloud", "profiling"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": "^1.0.0", "@opentelemetry/contrib-test-utils": "^0.40.0", "@types/mocha": "8.2.3", "@types/node": "18.6.5", "@types/sinon": "10.0.18", "mocha": "7.2.0", "nock": "13.3.3", "nyc": "15.1.0", "rimraf": "5.0.5", "sinon": "15.2.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}, "dependencies": {"@opentelemetry/resources": "^1.0.0", "@opentelemetry/semantic-conventions": "^1.22.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/detectors/node/opentelemetry-resource-detector-alibaba-cloud#readme", "gitHead": "0af1b70f7c3c9763c85ac51fa5e334c1e1512020"}