{"version": 3, "file": "AlibabaCloudEcsDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/AlibabaCloudEcsDetector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,wDAIkC;AAClC,8EAW6C;AAC7C,6BAA6B;AAE7B;;;;GAIG;AACH,MAAM,uBAAuB;IAA7B;QACE;;;WAGG;QACM,gCAA2B,GAAG,iBAAiB,CAAC;QAChD,kDAA6C,GACpD,4CAA4C,CAAC;QACtC,8CAAyC,GAChD,4BAA4B,CAAC;QACtB,0BAAqB,GAAG,IAAI,CAAC;IA8FxC,CAAC;IA5FC;;;;;;OAMG;IACH,KAAK,CAAC,MAAM,CAAC,OAAiC;QAC5C,MAAM,EACJ,kBAAkB,EAAE,SAAS,EAC7B,aAAa,EAAE,UAAU,EACzB,eAAe,EAAE,YAAY,EAC7B,WAAW,EAAE,MAAM,EACnB,SAAS,EAAE,gBAAgB,GAC5B,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAEzC,OAAO,IAAI,oBAAQ,CAAC;YAClB,CAAC,iDAA0B,CAAC,EAAE,wDAAiC;YAC/D,CAAC,iDAA0B,CAAC,EAAE,4DAAqC;YACnE,CAAC,mDAA4B,CAAC,EAAE,SAAS;YACzC,CAAC,+CAAwB,CAAC,EAAE,MAAM;YAClC,CAAC,0DAAmC,CAAC,EAAE,gBAAgB;YACvD,CAAC,0CAAmB,CAAC,EAAE,UAAU;YACjC,CAAC,4CAAqB,CAAC,EAAE,YAAY;YACrC,CAAC,4CAAqB,CAAC,EAAE,QAAQ;SAClC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,cAAc;QAC1B,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,IAAI,CAAC,2BAA2B;YACtC,IAAI,EAAE,IAAI,CAAC,6CAA6C;YACxD,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,IAAI,CAAC,qBAAqB;SACpC,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,UAAU;QACtB,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,IAAI,CAAC,2BAA2B;YACtC,IAAI,EAAE,IAAI,CAAC,yCAAyC;YACpD,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,IAAI,CAAC,qBAAqB;SACpC,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,OAA4B;QACrD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC,CAAC;YAChE,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAE/B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;gBACtC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;gBAC3B,IACE,OAAO,UAAU,KAAK,QAAQ;oBAC9B,CAAC,CAAC,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,CAAC,EACxC;oBACA,GAAG,CAAC,OAAO,EAAE,CAAC;oBACd,OAAO,MAAM,CACX,IAAI,KAAK,CAAC,qCAAqC,UAAU,EAAE,CAAC,CAC7D,CAAC;iBACH;gBAED,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;gBACxB,IAAI,OAAO,GAAG,EAAE,CAAC;gBACjB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC;gBAC5C,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;oBACpB,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC;gBACH,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACjB,OAAO,CAAC,OAAO,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;gBACpB,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,GAAG,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAEY,QAAA,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Detector,\n  Resource,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport {\n  CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,\n  CLOUDPROVIDERVALUES_ALIBABA_CLOUD,\n  SEMRESATTRS_CLOUD_ACCOUNT_ID,\n  SEMRESATTRS_CLOUD_AVAILABILITY_ZONE,\n  SEMRESATTRS_CLOUD_PLATFORM,\n  SEMRESATTRS_CLOUD_PROVIDER,\n  SEMRESATTRS_CLOUD_REGION,\n  SEMRESATTRS_HOST_ID,\n  SEMRESATTRS_HOST_NAME,\n  SEMRESATTRS_HOST_TYPE,\n} from '@opentelemetry/semantic-conventions';\nimport * as http from 'http';\n\n/**\n * The AlibabaCloudEcsDetector can be used to detect if a process is running in\n * AlibabaCloud ECS and return a {@link Resource} populated with metadata about\n * the ECS instance. Returns an empty Resource if detection fails.\n */\nclass AlibabaCloudEcsDetector implements Detector {\n  /**\n   * See https://www.alibabacloud.com/help/doc-detail/67254.htm for\n   * documentation about the AlibabaCloud instance identity document.\n   */\n  readonly ALIBABA_CLOUD_IDMS_ENDPOINT = '***************';\n  readonly ALIBABA_CLOUD_INSTANCE_IDENTITY_DOCUMENT_PATH =\n    '/latest/dynamic/instance-identity/document';\n  readonly ALIBABA_CLOUD_INSTANCE_HOST_DOCUMENT_PATH =\n    '/latest/meta-data/hostname';\n  readonly MILLISECONDS_TIME_OUT = 1000;\n\n  /**\n   * Attempts to connect and obtain an AlibabaCloud instance Identity document.\n   * If the connection is successful it returns a promise containing a\n   * {@link Resource} populated with instance metadata.\n   *\n   * @param config (unused) The resource detection config\n   */\n  async detect(_config?: ResourceDetectionConfig): Promise<Resource> {\n    const {\n      'owner-account-id': accountId,\n      'instance-id': instanceId,\n      'instance-type': instanceType,\n      'region-id': region,\n      'zone-id': availabilityZone,\n    } = await this._fetchIdentity();\n    const hostname = await this._fetchHost();\n\n    return new Resource({\n      [SEMRESATTRS_CLOUD_PROVIDER]: CLOUDPROVIDERVALUES_ALIBABA_CLOUD,\n      [SEMRESATTRS_CLOUD_PLATFORM]: CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS,\n      [SEMRESATTRS_CLOUD_ACCOUNT_ID]: accountId,\n      [SEMRESATTRS_CLOUD_REGION]: region,\n      [SEMRESATTRS_CLOUD_AVAILABILITY_ZONE]: availabilityZone,\n      [SEMRESATTRS_HOST_ID]: instanceId,\n      [SEMRESATTRS_HOST_TYPE]: instanceType,\n      [SEMRESATTRS_HOST_NAME]: hostname,\n    });\n  }\n\n  /**\n   * Fetch AlibabaCloud instance document url with http requests. If the\n   * application is running on an ECS instance, we should be able to get back a\n   * valid JSON document. Parses that document and stores the identity\n   * properties in a local map.\n   */\n  private async _fetchIdentity(): Promise<any> {\n    const options = {\n      host: this.ALIBABA_CLOUD_IDMS_ENDPOINT,\n      path: this.ALIBABA_CLOUD_INSTANCE_IDENTITY_DOCUMENT_PATH,\n      method: 'GET',\n      timeout: this.MILLISECONDS_TIME_OUT,\n    };\n    const identity = await this._fetchString(options);\n    return JSON.parse(identity);\n  }\n\n  private async _fetchHost(): Promise<string> {\n    const options = {\n      host: this.ALIBABA_CLOUD_IDMS_ENDPOINT,\n      path: this.ALIBABA_CLOUD_INSTANCE_HOST_DOCUMENT_PATH,\n      method: 'GET',\n      timeout: this.MILLISECONDS_TIME_OUT,\n    };\n    return await this._fetchString(options);\n  }\n\n  private async _fetchString(options: http.RequestOptions): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const timeoutId = setTimeout(() => {\n        req.destroy(new Error('ECS metadata api request timed out.'));\n      }, this.MILLISECONDS_TIME_OUT);\n\n      const req = http.request(options, res => {\n        clearTimeout(timeoutId);\n        const { statusCode } = res;\n        if (\n          typeof statusCode !== 'number' ||\n          !(statusCode >= 200 && statusCode < 300)\n        ) {\n          res.destroy();\n          return reject(\n            new Error(`Failed to load page, status code: ${statusCode}`)\n          );\n        }\n\n        res.setEncoding('utf8');\n        let rawData = '';\n        res.on('data', chunk => (rawData += chunk));\n        res.on('error', err => {\n          reject(err);\n        });\n        res.on('end', () => {\n          resolve(rawData);\n        });\n      });\n      req.on('error', err => {\n        clearTimeout(timeoutId);\n        reject(err);\n      });\n      req.end();\n    });\n  }\n}\n\nexport const alibabaCloudEcsDetector = new AlibabaCloudEcsDetector();\n"]}