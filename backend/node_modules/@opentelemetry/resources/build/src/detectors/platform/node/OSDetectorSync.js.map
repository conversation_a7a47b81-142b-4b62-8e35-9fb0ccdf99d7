{"version": 3, "file": "OSDetectorSync.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/OSDetectorSync.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,8EAG6C;AAC7C,gDAA6C;AAG7C,2BAAuC;AACvC,mCAAwC;AAExC;;;GAGG;AACH,MAAM,cAAc;IAClB,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAuB;YACrC,CAAC,0CAAmB,CAAC,EAAE,IAAA,qBAAa,EAAC,IAAA,aAAQ,GAAE,CAAC;YAChD,CAAC,6CAAsB,CAAC,EAAE,IAAA,YAAO,GAAE;SACpC,CAAC;QACF,OAAO,IAAI,mBAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;CACF;AAEY,QAAA,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  SEMRESATTRS_OS_TYPE,\n  SEMRESATTRS_OS_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { platform, release } from 'os';\nimport { normalizeType } from './utils';\n\n/**\n * OSDetectorSync detects the resources related to the operating system (OS) on\n * which the process represented by this resource is running.\n */\nclass OSDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): Resource {\n    const attributes: ResourceAttributes = {\n      [SEMRESATTRS_OS_TYPE]: normalizeType(platform()),\n      [SEMRESATTRS_OS_VERSION]: release(),\n    };\n    return new Resource(attributes);\n  }\n}\n\nexport const osDetectorSync = new OSDetectorSync();\n"]}