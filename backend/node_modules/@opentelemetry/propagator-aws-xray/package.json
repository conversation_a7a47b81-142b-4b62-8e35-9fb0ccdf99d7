{"name": "@opentelemetry/propagator-aws-xray", "version": "1.26.2", "description": "OpenTelemetry AWS Xray propagator provides context propagation for systems that are using AWS X-Ray format.", "main": "build/src/index.js", "module": "build/esm/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "test": "nyc mocha test/**/*.test.ts", "test:browser": "nyc karma start --single-run", "tdd": "npm run tdd:node", "tdd:node": "npm run test -- --watch-extensions ts --watch", "tdd:browser": "karma start", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "precompile": "lerna run version --scope @opentelemetry/propagator-aws-xray --include-dependencies", "prewatch": "npm run precompile"}, "keywords": ["opentelemetry", "nodejs", "tracing", "profiling", "awsxray"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}, "devDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0", "@opentelemetry/core": "^1.0.0", "@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/sinon": "10.0.20", "@types/webpack-env": "1.16.3", "babel-loader": "8.3.0", "babel-plugin-istanbul": "7.0.0", "karma": "6.4.4", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-spec-reporter": "0.0.36", "karma-webpack": "5.0.1", "lerna": "6.6.2", "nyc": "15.1.0", "ts-loader": "9.5.2", "typescript": "4.4.4", "webpack": "5.95.0", "webpack-cli": "5.1.4", "webpack-merge": "5.10.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/propagators/propagator-aws-xray#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}