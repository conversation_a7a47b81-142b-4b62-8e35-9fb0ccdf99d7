{"version": 3, "file": "AWSXRayPropagator.js", "sourceRoot": "", "sources": ["../../src/AWSXRayPropagator.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,KAAK,EAIL,UAAU,EAGV,kBAAkB,EAClB,aAAa,EACb,cAAc,EACd,eAAe,EACf,cAAc,EACd,oBAAoB,GACrB,MAAM,oBAAoB,CAAC;AAE5B,MAAM,CAAC,MAAM,uBAAuB,GAAG,iBAAiB,CAAC;AAEzD,MAAM,sBAAsB,GAAG,GAAG,CAAC;AACnC,MAAM,YAAY,GAAG,GAAG,CAAC;AAEzB,MAAM,YAAY,GAAG,MAAM,CAAC;AAC5B,MAAM,eAAe,GAAG,EAAE,CAAC;AAC3B,MAAM,gBAAgB,GAAG,GAAG,CAAC;AAC7B,MAAM,kBAAkB,GAAG,GAAG,CAAC;AAC/B,MAAM,0BAA0B,GAAG,CAAC,CAAC;AACrC,MAAM,0BAA0B,GAAG,EAAE,CAAC;AACtC,MAAM,0BAA0B,GAAG,CAAC,CAAC;AAErC,MAAM,aAAa,GAAG,QAAQ,CAAC;AAE/B,MAAM,gBAAgB,GAAG,SAAS,CAAC;AACnC,MAAM,UAAU,GAAG,GAAG,CAAC;AACvB,MAAM,WAAW,GAAG,GAAG,CAAC;AAExB;;;;;;;GAOG;AACH,MAAM,OAAO,iBAAiB;IAC5B,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;;QAC9D,MAAM,WAAW,GAAG,MAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,0CAAE,WAAW,EAAE,CAAC;QAC1D,IAAI,CAAC,WAAW,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC;YAAE,OAAO;QAE7D,MAAM,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC;QACtC,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,SAAS,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAErE,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC;QACpC,MAAM,YAAY,GAChB,CAAC,UAAU,CAAC,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,UAAU,CAAC,OAAO;YAClE,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,WAAW,CAAC;QAClB,qDAAqD;QAErD,MAAM,WAAW,GAAG,UAAU,SAAS,IAAI,YAAY,WAAW,QAAQ,YAAY,YAAY,EAAE,CAAC;QACrG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC;IAC5D,CAAC;IAED,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACnE,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC;YAAE,OAAO,OAAO,CAAC;QAErD,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,MAAM;QACJ,OAAO,CAAC,uBAAuB,CAAC,CAAC;IACnC,CAAC;IAEO,wBAAwB,CAC9B,OAAgB,EAChB,MAAqB;QAErB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,iBAAiB,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YAC5C,OAAO,CAAC,CAAC,WAAW,EAAE,KAAK,uBAAuB,CAAC;QACrD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,EAAE;YACtB,OAAO,oBAAoB,CAAC;SAC7B;QACD,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC;YAC/C,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,cAAc,CAAC;QAEnB,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnD,OAAO,oBAAoB,CAAC;SAC7B;QAED,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,WAAmB,CAAC;QACxB,IAAI,aAAa,GAAG,eAAe,CAAC;QACpC,IAAI,YAAY,GAAG,cAAc,CAAC;QAClC,IAAI,gBAAgB,GAAG,IAAI,CAAC;QAC5B,OAAO,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE;YAC/B,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC;YACxE,IAAI,cAAc,IAAI,CAAC,EAAE;gBACvB,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,CAAC;gBAChE,GAAG,GAAG,cAAc,GAAG,CAAC,CAAC;aAC1B;iBAAM;gBACL,WAAW;gBACX,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;gBAChD,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC;aAC1B;YACD,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAEtD,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YAErD,IAAI,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;gBACxC,aAAa,GAAG,iBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACxD;iBAAM,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;gBAChD,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;aACtD;iBAAM,IAAI,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;gBACnD,gBAAgB,GAAG,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aAC7D;SACF;QACD,IAAI,gBAAgB,KAAK,IAAI,EAAE;YAC7B,OAAO,oBAAoB,CAAC;SAC7B;QACD,MAAM,iBAAiB,GAAgB;YACrC,OAAO,EAAE,aAAa;YACtB,MAAM,EAAE,YAAY;YACpB,UAAU,EAAE,gBAAgB;YAC5B,QAAQ,EAAE,IAAI;SACf,CAAC;QACF,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,EAAE;YAC1C,OAAO,oBAAoB,CAAC;SAC7B;QACD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,WAAmB;QAC9C,2BAA2B;QAC3B,IAAI,WAAW,CAAC,MAAM,KAAK,eAAe,EAAE;YAC1C,OAAO,eAAe,CAAC;SACxB;QAED,iCAAiC;QACjC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;YAC7C,OAAO,eAAe,CAAC;SACxB;QAED,mBAAmB;QACnB,IACE,WAAW,CAAC,MAAM,CAAC,0BAA0B,CAAC,KAAK,kBAAkB;YACrE,WAAW,CAAC,MAAM,CAAC,0BAA0B,CAAC,KAAK,kBAAkB,EACrE;YACA,OAAO,eAAe,CAAC;SACxB;QAED,MAAM,SAAS,GAAG,WAAW,CAAC,SAAS,CACrC,0BAA0B,GAAG,CAAC,EAC9B,0BAA0B,CAC3B,CAAC;QACF,MAAM,UAAU,GAAG,WAAW,CAAC,SAAS,CACtC,0BAA0B,GAAG,CAAC,EAC9B,eAAe,CAChB,CAAC;QACF,MAAM,UAAU,GAAG,SAAS,GAAG,UAAU,CAAC;QAE1C,gCAAgC;QAChC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;YAC/B,OAAO,eAAe,CAAC;SACxB;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,YAAoB;QAC9C,OAAO,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC;IACrE,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,eAAuB;QACpD,IAAI,eAAe,KAAK,WAAW,EAAE;YACnC,OAAO,UAAU,CAAC,IAAI,CAAC;SACxB;QACD,IAAI,eAAe,KAAK,UAAU,EAAE;YAClC,OAAO,UAAU,CAAC,OAAO,CAAC;SAC3B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  trace,\n  Context,\n  TextMapPropagator,\n  SpanContext,\n  TraceFlags,\n  TextMapSetter,\n  TextMapGetter,\n  isSpanContextValid,\n  isValidSpanId,\n  isValidTraceId,\n  INVALID_TRACEID,\n  INVALID_SPANID,\n  INVALID_SPAN_CONTEXT,\n} from '@opentelemetry/api';\n\nexport const AWSXRAY_TRACE_ID_HEADER = 'x-amzn-trace-id';\n\nconst TRACE_HEADER_DELIMITER = ';';\nconst KV_DELIMITER = '=';\n\nconst TRACE_ID_KEY = 'Root';\nconst TRACE_ID_LENGTH = 35;\nconst TRACE_ID_VERSION = '1';\nconst TRACE_ID_DELIMITER = '-';\nconst TRACE_ID_DELIMITER_INDEX_1 = 1;\nconst TRACE_ID_DELIMITER_INDEX_2 = 10;\nconst TRACE_ID_FIRST_PART_LENGTH = 8;\n\nconst PARENT_ID_KEY = 'Parent';\n\nconst SAMPLED_FLAG_KEY = 'Sampled';\nconst IS_SAMPLED = '1';\nconst NOT_SAMPLED = '0';\n\n/**\n * Implementation of the AWS X-Ray Trace Header propagation protocol. See <a href=\n * https://https://docs.aws.amazon.com/xray/latest/devguide/xray-concepts.html#xray-concepts-tracingheader>AWS\n * Tracing header spec</a>\n *\n * An example AWS Xray Tracing Header is shown below:\n * X-Amzn-Trace-Id: Root=1-5759e988-bd862e3fe1be46a994272793;Parent=53995c3f42cd8ad8;Sampled=1\n */\nexport class AWSXRayPropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter) {\n    const spanContext = trace.getSpan(context)?.spanContext();\n    if (!spanContext || !isSpanContextValid(spanContext)) return;\n\n    const otTraceId = spanContext.traceId;\n    const timestamp = otTraceId.substring(0, TRACE_ID_FIRST_PART_LENGTH);\n    const randomNumber = otTraceId.substring(TRACE_ID_FIRST_PART_LENGTH);\n\n    const parentId = spanContext.spanId;\n    const samplingFlag =\n      (TraceFlags.SAMPLED & spanContext.traceFlags) === TraceFlags.SAMPLED\n        ? IS_SAMPLED\n        : NOT_SAMPLED;\n    // TODO: Add OT trace state to the X-Ray trace header\n\n    const traceHeader = `Root=1-${timestamp}-${randomNumber};Parent=${parentId};Sampled=${samplingFlag}`;\n    setter.set(carrier, AWSXRAY_TRACE_ID_HEADER, traceHeader);\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const spanContext = this.getSpanContextFromHeader(carrier, getter);\n    if (!isSpanContextValid(spanContext)) return context;\n\n    return trace.setSpan(context, trace.wrapSpanContext(spanContext));\n  }\n\n  fields(): string[] {\n    return [AWSXRAY_TRACE_ID_HEADER];\n  }\n\n  private getSpanContextFromHeader(\n    carrier: unknown,\n    getter: TextMapGetter\n  ): SpanContext {\n    const headerKeys = getter.keys(carrier);\n    const relevantHeaderKey = headerKeys.find(e => {\n      return e.toLowerCase() === AWSXRAY_TRACE_ID_HEADER;\n    });\n    if (!relevantHeaderKey) {\n      return INVALID_SPAN_CONTEXT;\n    }\n    const rawTraceHeader = getter.get(carrier, relevantHeaderKey);\n    const traceHeader = Array.isArray(rawTraceHeader)\n      ? rawTraceHeader[0]\n      : rawTraceHeader;\n\n    if (!traceHeader || typeof traceHeader !== 'string') {\n      return INVALID_SPAN_CONTEXT;\n    }\n\n    let pos = 0;\n    let trimmedPart: string;\n    let parsedTraceId = INVALID_TRACEID;\n    let parsedSpanId = INVALID_SPANID;\n    let parsedTraceFlags = null;\n    while (pos < traceHeader.length) {\n      const delimiterIndex = traceHeader.indexOf(TRACE_HEADER_DELIMITER, pos);\n      if (delimiterIndex >= 0) {\n        trimmedPart = traceHeader.substring(pos, delimiterIndex).trim();\n        pos = delimiterIndex + 1;\n      } else {\n        //last part\n        trimmedPart = traceHeader.substring(pos).trim();\n        pos = traceHeader.length;\n      }\n      const equalsIndex = trimmedPart.indexOf(KV_DELIMITER);\n\n      const value = trimmedPart.substring(equalsIndex + 1);\n\n      if (trimmedPart.startsWith(TRACE_ID_KEY)) {\n        parsedTraceId = AWSXRayPropagator._parseTraceId(value);\n      } else if (trimmedPart.startsWith(PARENT_ID_KEY)) {\n        parsedSpanId = AWSXRayPropagator._parseSpanId(value);\n      } else if (trimmedPart.startsWith(SAMPLED_FLAG_KEY)) {\n        parsedTraceFlags = AWSXRayPropagator._parseTraceFlag(value);\n      }\n    }\n    if (parsedTraceFlags === null) {\n      return INVALID_SPAN_CONTEXT;\n    }\n    const resultSpanContext: SpanContext = {\n      traceId: parsedTraceId,\n      spanId: parsedSpanId,\n      traceFlags: parsedTraceFlags,\n      isRemote: true,\n    };\n    if (!isSpanContextValid(resultSpanContext)) {\n      return INVALID_SPAN_CONTEXT;\n    }\n    return resultSpanContext;\n  }\n\n  private static _parseTraceId(xrayTraceId: string): string {\n    // Check length of trace id\n    if (xrayTraceId.length !== TRACE_ID_LENGTH) {\n      return INVALID_TRACEID;\n    }\n\n    // Check version trace id version\n    if (!xrayTraceId.startsWith(TRACE_ID_VERSION)) {\n      return INVALID_TRACEID;\n    }\n\n    // Check delimiters\n    if (\n      xrayTraceId.charAt(TRACE_ID_DELIMITER_INDEX_1) !== TRACE_ID_DELIMITER ||\n      xrayTraceId.charAt(TRACE_ID_DELIMITER_INDEX_2) !== TRACE_ID_DELIMITER\n    ) {\n      return INVALID_TRACEID;\n    }\n\n    const epochPart = xrayTraceId.substring(\n      TRACE_ID_DELIMITER_INDEX_1 + 1,\n      TRACE_ID_DELIMITER_INDEX_2\n    );\n    const uniquePart = xrayTraceId.substring(\n      TRACE_ID_DELIMITER_INDEX_2 + 1,\n      TRACE_ID_LENGTH\n    );\n    const resTraceId = epochPart + uniquePart;\n\n    // Check the content of trace id\n    if (!isValidTraceId(resTraceId)) {\n      return INVALID_TRACEID;\n    }\n\n    return resTraceId;\n  }\n\n  private static _parseSpanId(xrayParentId: string): string {\n    return isValidSpanId(xrayParentId) ? xrayParentId : INVALID_SPANID;\n  }\n\n  private static _parseTraceFlag(xraySampledFlag: string): TraceFlags | null {\n    if (xraySampledFlag === NOT_SAMPLED) {\n      return TraceFlags.NONE;\n    }\n    if (xraySampledFlag === IS_SAMPLED) {\n      return TraceFlags.SAMPLED;\n    }\n    return null;\n  }\n}\n"]}