{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAM4B;AAC5B,oEAIwC;AAExC,uCAAoC;AAEpC,MAAM,YAAY,GAAG,CAAC,aAAa,CAAC,CAAC;AAErC,MAAa,mBAAoB,SAAQ,qCAAmB;IAC1D,YAAY,SAAoC,EAAE;QAChD,KAAK,CAAC,qCAAqC,EAAE,iBAAO,EAAE,MAAM,CAAC,CAAC;IAChE,CAAC;IAES,IAAI;QACZ,OAAO;YACL,IAAI,qDAAmC,CACrC,MAAM,EACN,YAAY,EACZ,CAAC,MAAM,EAAE,aAAsB,EAAE,EAAE;gBACjC,UAAI,CAAC,KAAK,CAAC,2BAA2B,aAAa,EAAE,CAAC,CAAC;gBACvD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC;gBACtD,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;gBACtD,MAAM,eAAe,GAAG,IAAI,CAAC;gBAC7B,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAe,EAAE,EAAE;oBACvD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrB,OAAO,aAAa,CAAC;4BACnB,KAAK,EAAE,eAAe,CAAC,iBAAiB,EAAE;yBAC3C,CAAC,CAAC;qBACJ;oBAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrB,8DAA8D;wBAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAQ,CAAC;wBACpC,IACE,OAAO,YAAY,KAAK,QAAQ;4BAChC,OAAO,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,KAAK,CAAA,KAAK,UAAU,EACzC;4BACA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;gCAChB,KAAK,EAAE,eAAe,CAAC,iBAAiB,EAAE;6BAC3C,CAAC,CAAC;4BACH,OAAO,aAAa,CAAC,GAAG,IAAI,CAAC,CAAC;yBAC/B;qBACF;oBAED,IAAI,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEnD,OAAO,aAAa,CAAC,GAAG,IAAI,CAAC,CAAC;gBAChC,CAAC,EAAE,aAAa,CAAC,CAAC;gBAElB,IAAI,OAAO,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE;oBAC1C,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC;iBAChC;gBACD,IAAI,OAAO,WAAW,CAAC,OAAO,KAAK,UAAU,EAAE;oBAC7C,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC;iBACnC;gBACD,IAAI,KAAK,EAAE;oBACT,IAAI,MAAM,CAAC,IAAI,EAAE;wBACf,0EAA0E;wBAC1E,MAAM,CAAC,IAAI,GAAG,WAAW,CAAC;qBAC3B;oBACD,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC;iBAC9B;gBAED,OAAO,WAAW,CAAC;YACrB,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,SAAS,CAAC,MAAiC;QAClD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAEO,SAAS,CAAC,IAAU,EAAE,MAA8B,EAAE,KAAa;QACzE,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC;QAEtC,IAAI,CAAC,IAAI,EAAE;YACT,OAAO;SACR;QAED,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,EAC/B,GAAG,CAAC,EAAE;YACJ,IAAI,GAAG,EAAE;gBACP,UAAI,CAAC,KAAK,CAAC,6CAA6C,EAAE,GAAG,CAAC,CAAC;aAChE;QACH,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,SAAS,CAAC,QAAgB,EAAE,KAAa;YACvD,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,EAAE;gBAChC,OAAO,EAAE,CAAC;aACX;YAED,MAAM,IAAI,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAE7C,IAAI,CAAC,IAAI,EAAE;gBACT,OAAO,EAAE,CAAC;aACX;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAEvC,IAAI,CAAC,IAAA,wBAAkB,EAAC,WAAW,CAAC,EAAE;gBACpC,OAAO,EAAE,CAAC;aACX;YAED,MAAM,MAAM,GAAG;gBACb,QAAQ,EAAE,WAAW,CAAC,OAAO;gBAC7B,OAAO,EAAE,WAAW,CAAC,MAAM;gBAC3B,WAAW,EAAE,IAAI,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;aACvD,CAAC;YAEF,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAE/C,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,OAAa;QACnC,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;SAC5C;QAED,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzC,OAAO,OAAO,CAAC;SAChB;QAED,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE3C,OAAO,CAAC,KAAK,GAAG,CAAC,OAAe,EAAE,KAAa,EAAE,EAAE;YACjD,OAAO,MAAM,CAAC,MAAM,CAClB,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,EACzB,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAC9B,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA5ID,kDA4IC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context,\n  diag,\n  trace,\n  isSpanContextValid,\n  Span,\n} from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { PinoInstrumentationConfig } from './types';\nimport { VERSION } from './version';\n\nconst pinoVersions = ['>=5.14.0 <9'];\n\nexport class PinoInstrumentation extends InstrumentationBase {\n  constructor(config: PinoInstrumentationConfig = {}) {\n    super('@opentelemetry/instrumentation-pino', VERSION, config);\n  }\n\n  protected init() {\n    return [\n      new InstrumentationNodeModuleDefinition<any>(\n        'pino',\n        pinoVersions,\n        (module, moduleVersion?: string) => {\n          diag.debug(`Applying patch for pino@${moduleVersion}`);\n          const isESM = module[Symbol.toStringTag] === 'Module';\n          const moduleExports = isESM ? module.default : module;\n          const instrumentation = this;\n          const patchedPino = Object.assign((...args: unknown[]) => {\n            if (args.length === 0) {\n              return moduleExports({\n                mixin: instrumentation._getMixinFunction(),\n              });\n            }\n\n            if (args.length === 1) {\n              // eslint-disable-next-line @typescript-eslint/no-explicit-any\n              const optsOrStream = args[0] as any;\n              if (\n                typeof optsOrStream === 'string' ||\n                typeof optsOrStream?.write === 'function'\n              ) {\n                args.splice(0, 0, {\n                  mixin: instrumentation._getMixinFunction(),\n                });\n                return moduleExports(...args);\n              }\n            }\n\n            args[0] = instrumentation._combineOptions(args[0]);\n\n            return moduleExports(...args);\n          }, moduleExports);\n\n          if (typeof patchedPino.pino === 'function') {\n            patchedPino.pino = patchedPino;\n          }\n          if (typeof patchedPino.default === 'function') {\n            patchedPino.default = patchedPino;\n          }\n          if (isESM) {\n            if (module.pino) {\n              // This was added in pino@6.8.0 (https://github.com/pinojs/pino/pull/936).\n              module.pino = patchedPino;\n            }\n            module.default = patchedPino;\n          }\n\n          return patchedPino;\n        }\n      ),\n    ];\n  }\n\n  override getConfig(): PinoInstrumentationConfig {\n    return this._config;\n  }\n\n  override setConfig(config: PinoInstrumentationConfig) {\n    this._config = config;\n  }\n\n  private _callHook(span: Span, record: Record<string, string>, level: number) {\n    const hook = this.getConfig().logHook;\n\n    if (!hook) {\n      return;\n    }\n\n    safeExecuteInTheMiddle(\n      () => hook(span, record, level),\n      err => {\n        if (err) {\n          diag.error('pino instrumentation: error calling logHook', err);\n        }\n      },\n      true\n    );\n  }\n\n  private _getMixinFunction() {\n    const instrumentation = this;\n    return function otelMixin(_context: object, level: number) {\n      if (!instrumentation.isEnabled()) {\n        return {};\n      }\n\n      const span = trace.getSpan(context.active());\n\n      if (!span) {\n        return {};\n      }\n\n      const spanContext = span.spanContext();\n\n      if (!isSpanContextValid(spanContext)) {\n        return {};\n      }\n\n      const record = {\n        trace_id: spanContext.traceId,\n        span_id: spanContext.spanId,\n        trace_flags: `0${spanContext.traceFlags.toString(16)}`,\n      };\n\n      instrumentation._callHook(span, record, level);\n\n      return record;\n    };\n  }\n\n  private _combineOptions(options?: any) {\n    if (options === undefined) {\n      return { mixin: this._getMixinFunction() };\n    }\n\n    if (options.mixin === undefined) {\n      options.mixin = this._getMixinFunction();\n      return options;\n    }\n\n    const originalMixin = options.mixin;\n    const otelMixin = this._getMixinFunction();\n\n    options.mixin = (context: object, level: number) => {\n      return Object.assign(\n        otelMixin(context, level),\n        originalMixin(context, level)\n      );\n    };\n\n    return options;\n  }\n}\n"]}