{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { Command, Redis } from 'ioredis';\nimport type * as LegacyIORedis from 'ioredis4';\n\ninterface LegacyIORedisCommand {\n  reject: (err: Error) => void;\n  resolve: (result: {}) => void;\n  promise: Promise<{}>;\n  args: Array<string | Buffer | number>;\n  callback: LegacyIORedis.CallbackFunction<unknown>;\n  name: string;\n}\n\nexport type IORedisCommand = Command | LegacyIORedisCommand;\nexport type RedisInterface = Redis | LegacyIORedis.Redis;\n"]}