{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAoE;AACpE,oEAIwC;AAGxC,8EAG6C;AAC7C,oEAAwE;AACxE,mCAAkC;AAClC,8DAA2E;AAC3E,uCAAoC;AAEpC,MAAM,cAAc,GAAiC;IACnD,iBAAiB,EAAE,IAAI;CACxB,CAAC;AAEF,MAAa,sBAAuB,SAAQ,qCAAwB;IAClE,YAAY,UAAwC,EAAE;QACpD,KAAK,CACH,wCAAwC,EACxC,iBAAO,EACP,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,EAAE,OAAO,CAAC,CAC3C,CAAC;IACJ,CAAC;IAED,IAAI;QACF,OAAO;YACL,IAAI,qDAAmC,CACrC,SAAS,EACT,CAAC,IAAI,EAAE,IAAI,CAAC,EACZ,CAAC,MAAM,EAAE,aAAsB,EAAE,EAAE;gBACjC,MAAM,aAAa,GACjB,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ;oBACrC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;oBACvB,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW;gBACzB,UAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBACzC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;oBAClD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;iBACtD;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,EACvB,aAAa,EACb,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CACtC,CAAC;gBACF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;oBAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;iBAClD;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,EACvB,SAAS,EACT,IAAI,CAAC,gBAAgB,EAAE,CACxB,CAAC;gBACF,OAAO,MAAM,CAAC;YAChB,CAAC,EACD,MAAM,CAAC,EAAE;gBACP,IAAI,MAAM,KAAK,SAAS;oBAAE,OAAO;gBACjC,MAAM,aAAa,GACjB,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ;oBACrC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;oBACvB,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW;gBACzB,UAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBACzC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;gBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACnD,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,aAAsB;QAC9C,OAAO,CAAC,QAAkB,EAAE,EAAE;YAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QACzD,CAAC,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,OAAO,CAAC,QAAkB,EAAE,EAAE;YAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,QAAkB,EAAE,aAAsB;QAClE,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAgC,GAAoB;YACzD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBACnD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;aACxC;YACD,MAAM,MAAM,GACV,eAAe,CAAC,SAAS,EAAkC,CAAC;YAC9D,MAAM,qBAAqB,GACzB,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,qBAAqB,KAAI,2CAA4B,CAAC;YAEhE,MAAM,eAAe,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,CAAC;YACtE,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,iBAAiB,MAAK,IAAI,IAAI,eAAe,EAAE;gBACzD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;aACxC;YAED,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE;gBACtD,IAAI,EAAE,cAAQ,CAAC,MAAM;gBACrB,UAAU,EAAE;oBACV,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,KAAK;oBACpD,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAAE,qBAAqB,CACtD,GAAG,CAAC,IAAI,EACR,GAAG,CAAC,IAAI,CACT;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,EAAE;gBACvB,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAE,IAAI,EAAE;oBACzB,aAAa;oBACb,OAAO,EAAE,GAAG,CAAC,IAAI;oBACjB,OAAO,EAAE,GAAG,CAAC,IAAI;iBAClB,CAAC,EACJ,CAAC,CAAC,EAAE;oBACF,IAAI,CAAC,EAAE;wBACL,UAAI,CAAC,KAAK,CAAC,8CAA8C,EAAE,CAAC,CAAC,CAAC;qBAC/D;gBACH,CAAC,EACD,IAAI,CACL,CAAC;aACH;YAED,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YAEpC,IAAI,CAAC,aAAa,CAAC;gBACjB,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC,CAAC,yCAAkB,CAAC,oBAAoB,CAAC,EAAE,WAAW,IAAI,IAAI,IAAI,EAAE;aACrE,CAAC,CAAC;YAEH,IAAI;gBACF,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAE/C,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC;gBAChC,uDAAuD;gBACvD,GAAG,CAAC,OAAO,GAAG,UAAU,MAAW;oBACjC,IAAA,wCAAsB,EACpB,GAAG,EAAE,WAAC,OAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,YAAY,+CAApB,MAAM,EAAiB,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA,EAAA,EAC9D,CAAC,CAAC,EAAE;wBACF,IAAI,CAAC,EAAE;4BACL,UAAI,CAAC,KAAK,CAAC,+CAA+C,EAAE,CAAC,CAAC,CAAC;yBAChE;oBACH,CAAC,EACD,IAAI,CACL,CAAC;oBAEF,IAAA,eAAO,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBACpB,WAAW,CAAC,MAAM,CAAC,CAAC;gBACtB,CAAC,CAAC;gBAEF,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC;gBAC9B,GAAG,CAAC,MAAM,GAAG,UAAU,GAAU;oBAC/B,IAAA,eAAO,EAAC,IAAI,EAAE,GAAG,CAAC,CAAC;oBACnB,UAAU,CAAC,GAAG,CAAC,CAAC;gBAClB,CAAC,CAAC;gBAEF,OAAO,MAAM,CAAC;aACf;YAAC,OAAO,KAAU,EAAE;gBACnB,IAAA,eAAO,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACrB,MAAM,KAAK,CAAC;aACb;QACH,CAAC,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,QAAkB;QACzC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO;YACL,MAAM,MAAM,GACV,eAAe,CAAC,SAAS,EAAkC,CAAC;YAC9D,MAAM,eAAe,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,CAAC;YACtE,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,iBAAiB,MAAK,IAAI,IAAI,eAAe,EAAE;gBACzD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;aACxC;YAED,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE;gBACvD,IAAI,EAAE,cAAQ,CAAC,MAAM;gBACrB,UAAU,EAAE;oBACV,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,KAAK;oBACpD,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAAE,SAAS;iBAC7C;aACF,CAAC,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;YAEpC,IAAI,CAAC,aAAa,CAAC;gBACjB,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC,CAAC,yCAAkB,CAAC,oBAAoB,CAAC,EAAE,WAAW,IAAI,IAAI,IAAI,EAAE;aACrE,CAAC,CAAC;YACH,IAAI;gBACF,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC/C,IAAA,eAAO,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACpB,OAAO,MAAM,CAAC;aACf;YAAC,OAAO,KAAU,EAAE;gBACnB,IAAA,eAAO,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACrB,MAAM,KAAK,CAAC;aACb;QACH,CAAC,CAAC;IACJ,CAAC;CACF;AA3LD,wDA2LC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag, trace, context, SpanKind } from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n} from '@opentelemetry/instrumentation';\nimport { IORedisInstrumentationConfig } from './types';\nimport { IORedisCommand, RedisInterface } from './internal-types';\nimport {\n  DbSystemValues,\n  SemanticAttributes,\n} from '@opentelemetry/semantic-conventions';\nimport { safeExecuteInTheMiddle } from '@opentelemetry/instrumentation';\nimport { endSpan } from './utils';\nimport { defaultDbStatementSerializer } from '@opentelemetry/redis-common';\nimport { VERSION } from './version';\n\nconst DEFAULT_CONFIG: IORedisInstrumentationConfig = {\n  requireParentSpan: true,\n};\n\nexport class IORedisInstrumentation extends InstrumentationBase<any> {\n  constructor(_config: IORedisInstrumentationConfig = {}) {\n    super(\n      '@opentelemetry/instrumentation-ioredis',\n      VERSION,\n      Object.assign({}, DEFAULT_CONFIG, _config)\n    );\n  }\n\n  init(): InstrumentationNodeModuleDefinition<any>[] {\n    return [\n      new InstrumentationNodeModuleDefinition<any>(\n        'ioredis',\n        ['>1', '<6'],\n        (module, moduleVersion?: string) => {\n          const moduleExports =\n            module[Symbol.toStringTag] === 'Module'\n              ? module.default // ESM\n              : module; // CommonJS\n          diag.debug('Applying patch for ioredis');\n          if (isWrapped(moduleExports.prototype.sendCommand)) {\n            this._unwrap(moduleExports.prototype, 'sendCommand');\n          }\n          this._wrap(\n            moduleExports.prototype,\n            'sendCommand',\n            this._patchSendCommand(moduleVersion)\n          );\n          if (isWrapped(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n          }\n          this._wrap(\n            moduleExports.prototype,\n            'connect',\n            this._patchConnection()\n          );\n          return module;\n        },\n        module => {\n          if (module === undefined) return;\n          const moduleExports =\n            module[Symbol.toStringTag] === 'Module'\n              ? module.default // ESM\n              : module; // CommonJS\n          diag.debug('Removing patch for ioredis');\n          this._unwrap(moduleExports.prototype, 'sendCommand');\n          this._unwrap(moduleExports.prototype, 'connect');\n        }\n      ),\n    ];\n  }\n\n  /**\n   * Patch send command internal to trace requests\n   */\n  private _patchSendCommand(moduleVersion?: string) {\n    return (original: Function) => {\n      return this._traceSendCommand(original, moduleVersion);\n    };\n  }\n\n  private _patchConnection() {\n    return (original: Function) => {\n      return this._traceConnection(original);\n    };\n  }\n\n  private _traceSendCommand(original: Function, moduleVersion?: string) {\n    const instrumentation = this;\n    return function (this: RedisInterface, cmd?: IORedisCommand) {\n      if (arguments.length < 1 || typeof cmd !== 'object') {\n        return original.apply(this, arguments);\n      }\n      const config =\n        instrumentation.getConfig() as IORedisInstrumentationConfig;\n      const dbStatementSerializer =\n        config?.dbStatementSerializer || defaultDbStatementSerializer;\n\n      const hasNoParentSpan = trace.getSpan(context.active()) === undefined;\n      if (config?.requireParentSpan === true && hasNoParentSpan) {\n        return original.apply(this, arguments);\n      }\n\n      const span = instrumentation.tracer.startSpan(cmd.name, {\n        kind: SpanKind.CLIENT,\n        attributes: {\n          [SemanticAttributes.DB_SYSTEM]: DbSystemValues.REDIS,\n          [SemanticAttributes.DB_STATEMENT]: dbStatementSerializer(\n            cmd.name,\n            cmd.args\n          ),\n        },\n      });\n\n      if (config?.requestHook) {\n        safeExecuteInTheMiddle(\n          () =>\n            config?.requestHook!(span, {\n              moduleVersion,\n              cmdName: cmd.name,\n              cmdArgs: cmd.args,\n            }),\n          e => {\n            if (e) {\n              diag.error('ioredis instrumentation: request hook failed', e);\n            }\n          },\n          true\n        );\n      }\n\n      const { host, port } = this.options;\n\n      span.setAttributes({\n        [SemanticAttributes.NET_PEER_NAME]: host,\n        [SemanticAttributes.NET_PEER_PORT]: port,\n        [SemanticAttributes.DB_CONNECTION_STRING]: `redis://${host}:${port}`,\n      });\n\n      try {\n        const result = original.apply(this, arguments);\n\n        const origResolve = cmd.resolve;\n        /* eslint-disable @typescript-eslint/no-explicit-any */\n        cmd.resolve = function (result: any) {\n          safeExecuteInTheMiddle(\n            () => config?.responseHook?.(span, cmd.name, cmd.args, result),\n            e => {\n              if (e) {\n                diag.error('ioredis instrumentation: response hook failed', e);\n              }\n            },\n            true\n          );\n\n          endSpan(span, null);\n          origResolve(result);\n        };\n\n        const origReject = cmd.reject;\n        cmd.reject = function (err: Error) {\n          endSpan(span, err);\n          origReject(err);\n        };\n\n        return result;\n      } catch (error: any) {\n        endSpan(span, error);\n        throw error;\n      }\n    };\n  }\n\n  private _traceConnection(original: Function) {\n    const instrumentation = this;\n    return function (this: RedisInterface) {\n      const config =\n        instrumentation.getConfig() as IORedisInstrumentationConfig;\n      const hasNoParentSpan = trace.getSpan(context.active()) === undefined;\n      if (config?.requireParentSpan === true && hasNoParentSpan) {\n        return original.apply(this, arguments);\n      }\n\n      const span = instrumentation.tracer.startSpan('connect', {\n        kind: SpanKind.CLIENT,\n        attributes: {\n          [SemanticAttributes.DB_SYSTEM]: DbSystemValues.REDIS,\n          [SemanticAttributes.DB_STATEMENT]: 'connect',\n        },\n      });\n      const { host, port } = this.options;\n\n      span.setAttributes({\n        [SemanticAttributes.NET_PEER_NAME]: host,\n        [SemanticAttributes.NET_PEER_PORT]: port,\n        [SemanticAttributes.DB_CONNECTION_STRING]: `redis://${host}:${port}`,\n      });\n      try {\n        const client = original.apply(this, arguments);\n        endSpan(span, null);\n        return client;\n      } catch (error: any) {\n        endSpan(span, error);\n        throw error;\n      }\n    };\n  }\n}\n"]}