import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import { IORedisInstrumentationConfig } from './types';
export declare class IORedisInstrumentation extends InstrumentationBase<any> {
    constructor(_config?: IORedisInstrumentationConfig);
    init(): InstrumentationNodeModuleDefinition<any>[];
    /**
     * Patch send command internal to trace requests
     */
    private _patchSendCommand;
    private _patchConnection;
    private _traceSendCommand;
    private _traceConnection;
}
//# sourceMappingURL=instrumentation.d.ts.map