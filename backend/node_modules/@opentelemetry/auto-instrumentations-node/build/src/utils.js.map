{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAG1C,oFAAgF;AAChF,0FAAqF;AACrF,oFAA4E;AAC5E,kFAA8E;AAC9E,sGAAiG;AACjG,oFAAgF;AAChF,sFAAkF;AAClF,0FAAsF;AACtF,4EAAwE;AACxE,oFAAgF;AAChF,oFAAgF;AAChF,0EAAsE;AACtE,8FAAyF;AACzF,oFAAgF;AAChF,8EAA0E;AAC1E,8EAA0E;AAC1E,8EAA0E;AAC1E,oFAAgF;AAChF,8EAA0E;AAC1E,4EAAwE;AACxE,8FAAyF;AACzF,wFAAoF;AACpF,oFAAgF;AAChF,sFAAkF;AAClF,kFAA8E;AAC9E,gFAA4E;AAC5E,4FAAiF;AACjF,4EAAwE;AACxE,0EAAsE;AACtE,8EAA0E;AAC1E,gFAAsG;AACtG,oFAAwG;AACxG,oFAAgF;AAChF,kFAA8E;AAC9E,wFAAmF;AACnF,oFAAgF;AAChF,oFAAgF;AAEhF,oGAAyF;AACzF,gFAM8C;AAC9C,4FAA+E;AAC/E,gFAAmE;AACnE,wDAOkC;AAElC,MAAM,2BAA2B,GAAG,WAAW,CAAC;AAChD,MAAM,6BAA6B,GAAG,KAAK,CAAC;AAC5C,MAAM,sBAAsB,GAAG,MAAM,CAAC;AACtC,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAClC,MAAM,yBAAyB,GAAG,SAAS,CAAC;AAC5C,MAAM,yBAAyB,GAAG,SAAS,CAAC;AAC5C,MAAM,qBAAqB,GAAG,KAAK,CAAC;AACpC,MAAM,qBAAqB,GAAG,KAAK,CAAC;AAEpC,MAAM,kBAAkB,GAAG;IACzB,wCAAwC,EAAE,gDAAsB;IAChE,2CAA2C,EAAE,qDAAwB;IACrE,wCAAwC,EAAE,4CAAkB;IAC5D,uCAAuC,EAAE,8CAAqB;IAC9D,iDAAiD,EAC/C,iEAA8B;IAChC,wCAAwC,EAAE,gDAAsB;IAChE,yCAAyC,EAAE,kDAAuB;IAClE,2CAA2C,EAAE,sDAAyB;IACtE,oCAAoC,EAAE,wCAAkB;IACxD,wCAAwC,EAAE,gDAAsB;IAChE,wCAAwC,EAAE,gDAAsB;IAChE,mCAAmC,EAAE,sCAAiB;IACtD,6CAA6C,EAAE,yDAA0B;IACzE,wCAAwC,EAAE,gDAAsB;IAChE,qCAAqC,EAAE,0CAAmB;IAC1D,qCAAqC,EAAE,0CAAmB;IAC1D,qCAAqC,EAAE,0CAAmB;IAC1D,wCAAwC,EAAE,gDAAsB;IAChE,qCAAqC,EAAE,0CAAmB;IAC1D,oCAAoC,EAAE,wCAAkB;IACxD,6CAA6C,EAAE,yDAA0B;IACzE,0CAA0C,EAAE,oDAAwB;IACpE,wCAAwC,EAAE,gDAAsB;IAChE,yCAAyC,EAAE,kDAAuB;IAClE,uCAAuC,EAAE,8CAAqB;IAC9D,sCAAsC,EAAE,4CAAoB;IAC5D,4CAA4C,EAAE,iDAAmB;IACjE,oCAAoC,EAAE,wCAAkB;IACxD,mCAAmC,EAAE,sCAAiB;IACtD,qCAAqC,EAAE,0CAAmB;IAC1D,sCAAsC,EAAE,4CAAsB;IAC9D,wCAAwC,EAAE,8CAAsB;IAChE,wCAAwC,EAAE,gDAAsB;IAChE,uCAAuC,EAAE,8CAAqB;IAC9D,0CAA0C,EAAE,mDAAuB;IACnE,wCAAwC,EAAE,gDAAsB;IAChE,wCAAwC,EAAE,gDAAsB;CACjE,CAAC;AAUF,SAAgB,2BAA2B,CACzC,eAAyC,EAAE;;IAE3C,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;QAC5C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,EAAE;YACnE,UAAI,CAAC,KAAK,CAAC,kCAAkC,IAAI,aAAa,CAAC,CAAC;YAChE,SAAS;SACV;KACF;IAED,MAAM,gBAAgB,GAAsB,EAAE,CAAC;IAE/C,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAEhD,EAAE;QACD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC1C,qDAAqD;QACrD,MAAM,UAAU,GAAQ,MAAA,YAAY,CAAC,IAAI,CAAC,mCAAI,EAAE,CAAC;QAEjD,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;YAChC,UAAI,CAAC,KAAK,CAAC,iCAAiC,IAAI,EAAE,CAAC,CAAC;YACpD,SAAS;SACV;QAED,IAAI;YACF,UAAI,CAAC,KAAK,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;YAClD,gBAAgB,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;SACjD;QAAC,OAAO,CAAM,EAAE;YACf,UAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACf;KACF;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAjCD,kEAiCC;AAED,SAAgB,2BAA2B;;IACzC,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAG/B;QACA,CAAC,2BAA2B,EAAE,+CAAiB,CAAC;QAChD,CAAC,6BAA6B,EAAE,2BAAe,CAAC;QAChD,CAAC,sBAAsB,EAAE,4BAAgB,CAAC;QAC1C,CAAC,oBAAoB,EAAE,0BAAc,CAAC;QACtC,CAAC,yBAAyB,EAAE,+BAAmB,CAAC;QAChD,CAAC,yBAAyB,EAAE,yDAAuB,CAAC;QACpD,CAAC,qBAAqB,EAAE,mCAAW,CAAC;QACpC;YACE,qBAAqB;YACrB;gBACE,sCAAc;gBACd,sCAAc;gBACd,sCAAc;gBACd,4CAAoB;gBACpB,yCAAiB;aAClB;SACF;KACF,CAAC,CAAC;IAEH,MAAM,wBAAwB,GAC5B,MAAA,MAAA,OAAO,CAAC,GAAG,CAAC,4BAA4B,0CAAE,KAAK,CAAC,GAAG,CAAC,mCAAI,CAAC,KAAK,CAAC,CAAC;IAElE,IAAI,wBAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC5C,OAAO,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;KAC/C;IAED,IAAI,wBAAwB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC7C,OAAO,EAAE,CAAC;KACX;IAED,OAAO,wBAAwB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QACjD,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB,EAAE;YACrB,UAAI,CAAC,KAAK,CACR,8BAA8B,QAAQ,sEAAsE,CAC7G,CAAC;SACH;QACD,OAAO,gBAAgB,IAAI,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC;AA5CD,kEA4CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { Instrumentation } from '@opentelemetry/instrumentation';\n\nimport { AmqplibInstrumentation } from '@opentelemetry/instrumentation-amqplib';\nimport { AwsLambdaInstrumentation } from '@opentelemetry/instrumentation-aws-lambda';\nimport { AwsInstrumentation } from '@opentelemetry/instrumentation-aws-sdk';\nimport { BunyanInstrumentation } from '@opentelemetry/instrumentation-bunyan';\nimport { CassandraDriverInstrumentation } from '@opentelemetry/instrumentation-cassandra-driver';\nimport { ConnectInstrumentation } from '@opentelemetry/instrumentation-connect';\nimport { CucumberInstrumentation } from '@opentelemetry/instrumentation-cucumber';\nimport { DataloaderInstrumentation } from '@opentelemetry/instrumentation-dataloader';\nimport { DnsInstrumentation } from '@opentelemetry/instrumentation-dns';\nimport { ExpressInstrumentation } from '@opentelemetry/instrumentation-express';\nimport { FastifyInstrumentation } from '@opentelemetry/instrumentation-fastify';\nimport { FsInstrumentation } from '@opentelemetry/instrumentation-fs';\nimport { GenericPoolInstrumentation } from '@opentelemetry/instrumentation-generic-pool';\nimport { GraphQLInstrumentation } from '@opentelemetry/instrumentation-graphql';\nimport { GrpcInstrumentation } from '@opentelemetry/instrumentation-grpc';\nimport { HapiInstrumentation } from '@opentelemetry/instrumentation-hapi';\nimport { HttpInstrumentation } from '@opentelemetry/instrumentation-http';\nimport { IORedisInstrumentation } from '@opentelemetry/instrumentation-ioredis';\nimport { KnexInstrumentation } from '@opentelemetry/instrumentation-knex';\nimport { KoaInstrumentation } from '@opentelemetry/instrumentation-koa';\nimport { LruMemoizerInstrumentation } from '@opentelemetry/instrumentation-lru-memoizer';\nimport { MemcachedInstrumentation } from '@opentelemetry/instrumentation-memcached';\nimport { MongoDBInstrumentation } from '@opentelemetry/instrumentation-mongodb';\nimport { MongooseInstrumentation } from '@opentelemetry/instrumentation-mongoose';\nimport { MySQL2Instrumentation } from '@opentelemetry/instrumentation-mysql2';\nimport { MySQLInstrumentation } from '@opentelemetry/instrumentation-mysql';\nimport { NestInstrumentation } from '@opentelemetry/instrumentation-nestjs-core';\nimport { NetInstrumentation } from '@opentelemetry/instrumentation-net';\nimport { PgInstrumentation } from '@opentelemetry/instrumentation-pg';\nimport { PinoInstrumentation } from '@opentelemetry/instrumentation-pino';\nimport { RedisInstrumentation as RedisInstrumentationV2 } from '@opentelemetry/instrumentation-redis';\nimport { RedisInstrumentation as RedisInstrumentationV4 } from '@opentelemetry/instrumentation-redis-4';\nimport { RestifyInstrumentation } from '@opentelemetry/instrumentation-restify';\nimport { RouterInstrumentation } from '@opentelemetry/instrumentation-router';\nimport { SocketIoInstrumentation } from '@opentelemetry/instrumentation-socket.io';\nimport { TediousInstrumentation } from '@opentelemetry/instrumentation-tedious';\nimport { WinstonInstrumentation } from '@opentelemetry/instrumentation-winston';\n\nimport { alibabaCloudEcsDetector } from '@opentelemetry/resource-detector-alibaba-cloud';\nimport {\n  awsBeanstalkDetector,\n  awsEc2Detector,\n  awsEcsDetector,\n  awsEksDetector,\n  awsLambdaDetector,\n} from '@opentelemetry/resource-detector-aws';\nimport { containerDetector } from '@opentelemetry/resource-detector-container';\nimport { gcpDetector } from '@opentelemetry/resource-detector-gcp';\nimport {\n  Detector,\n  DetectorSync,\n  envDetectorSync,\n  hostDetectorSync,\n  osDetectorSync,\n  processDetectorSync,\n} from '@opentelemetry/resources';\n\nconst RESOURCE_DETECTOR_CONTAINER = 'container';\nconst RESOURCE_DETECTOR_ENVIRONMENT = 'env';\nconst RESOURCE_DETECTOR_HOST = 'host';\nconst RESOURCE_DETECTOR_OS = 'os';\nconst RESOURCE_DETECTOR_PROCESS = 'process';\nconst RESOURCE_DETECTOR_ALIBABA = 'alibaba';\nconst RESOURCE_DETECTOR_AWS = 'aws';\nconst RESOURCE_DETECTOR_GCP = 'gcp';\n\nconst InstrumentationMap = {\n  '@opentelemetry/instrumentation-amqplib': AmqplibInstrumentation,\n  '@opentelemetry/instrumentation-aws-lambda': AwsLambdaInstrumentation,\n  '@opentelemetry/instrumentation-aws-sdk': AwsInstrumentation,\n  '@opentelemetry/instrumentation-bunyan': BunyanInstrumentation,\n  '@opentelemetry/instrumentation-cassandra-driver':\n    CassandraDriverInstrumentation,\n  '@opentelemetry/instrumentation-connect': ConnectInstrumentation,\n  '@opentelemetry/instrumentation-cucumber': CucumberInstrumentation,\n  '@opentelemetry/instrumentation-dataloader': DataloaderInstrumentation,\n  '@opentelemetry/instrumentation-dns': DnsInstrumentation,\n  '@opentelemetry/instrumentation-express': ExpressInstrumentation,\n  '@opentelemetry/instrumentation-fastify': FastifyInstrumentation,\n  '@opentelemetry/instrumentation-fs': FsInstrumentation,\n  '@opentelemetry/instrumentation-generic-pool': GenericPoolInstrumentation,\n  '@opentelemetry/instrumentation-graphql': GraphQLInstrumentation,\n  '@opentelemetry/instrumentation-grpc': GrpcInstrumentation,\n  '@opentelemetry/instrumentation-hapi': HapiInstrumentation,\n  '@opentelemetry/instrumentation-http': HttpInstrumentation,\n  '@opentelemetry/instrumentation-ioredis': IORedisInstrumentation,\n  '@opentelemetry/instrumentation-knex': KnexInstrumentation,\n  '@opentelemetry/instrumentation-koa': KoaInstrumentation,\n  '@opentelemetry/instrumentation-lru-memoizer': LruMemoizerInstrumentation,\n  '@opentelemetry/instrumentation-memcached': MemcachedInstrumentation,\n  '@opentelemetry/instrumentation-mongodb': MongoDBInstrumentation,\n  '@opentelemetry/instrumentation-mongoose': MongooseInstrumentation,\n  '@opentelemetry/instrumentation-mysql2': MySQL2Instrumentation,\n  '@opentelemetry/instrumentation-mysql': MySQLInstrumentation,\n  '@opentelemetry/instrumentation-nestjs-core': NestInstrumentation,\n  '@opentelemetry/instrumentation-net': NetInstrumentation,\n  '@opentelemetry/instrumentation-pg': PgInstrumentation,\n  '@opentelemetry/instrumentation-pino': PinoInstrumentation,\n  '@opentelemetry/instrumentation-redis': RedisInstrumentationV2,\n  '@opentelemetry/instrumentation-redis-4': RedisInstrumentationV4,\n  '@opentelemetry/instrumentation-restify': RestifyInstrumentation,\n  '@opentelemetry/instrumentation-router': RouterInstrumentation,\n  '@opentelemetry/instrumentation-socket.io': SocketIoInstrumentation,\n  '@opentelemetry/instrumentation-tedious': TediousInstrumentation,\n  '@opentelemetry/instrumentation-winston': WinstonInstrumentation,\n};\n\n// Config types inferred automatically from the first argument of the constructor\ntype ConfigArg<T> = T extends new (...args: infer U) => unknown ? U[0] : never;\nexport type InstrumentationConfigMap = {\n  [Name in keyof typeof InstrumentationMap]?: ConfigArg<\n    (typeof InstrumentationMap)[Name]\n  >;\n};\n\nexport function getNodeAutoInstrumentations(\n  inputConfigs: InstrumentationConfigMap = {}\n): Instrumentation[] {\n  for (const name of Object.keys(inputConfigs)) {\n    if (!Object.prototype.hasOwnProperty.call(InstrumentationMap, name)) {\n      diag.error(`Provided instrumentation name \"${name}\" not found`);\n      continue;\n    }\n  }\n\n  const instrumentations: Instrumentation[] = [];\n\n  for (const name of Object.keys(InstrumentationMap) as Array<\n    keyof typeof InstrumentationMap\n  >) {\n    const Instance = InstrumentationMap[name];\n    // Defaults are defined by the instrumentation itself\n    const userConfig: any = inputConfigs[name] ?? {};\n\n    if (userConfig.enabled === false) {\n      diag.debug(`Disabling instrumentation for ${name}`);\n      continue;\n    }\n\n    try {\n      diag.debug(`Loading instrumentation for ${name}`);\n      instrumentations.push(new Instance(userConfig));\n    } catch (e: any) {\n      diag.error(e);\n    }\n  }\n\n  return instrumentations;\n}\n\nexport function getResourceDetectorsFromEnv(): Array<Detector | DetectorSync> {\n  const resourceDetectors = new Map<\n    string,\n    Detector | DetectorSync | Detector[]\n  >([\n    [RESOURCE_DETECTOR_CONTAINER, containerDetector],\n    [RESOURCE_DETECTOR_ENVIRONMENT, envDetectorSync],\n    [RESOURCE_DETECTOR_HOST, hostDetectorSync],\n    [RESOURCE_DETECTOR_OS, osDetectorSync],\n    [RESOURCE_DETECTOR_PROCESS, processDetectorSync],\n    [RESOURCE_DETECTOR_ALIBABA, alibabaCloudEcsDetector],\n    [RESOURCE_DETECTOR_GCP, gcpDetector],\n    [\n      RESOURCE_DETECTOR_AWS,\n      [\n        awsEc2Detector,\n        awsEcsDetector,\n        awsEksDetector,\n        awsBeanstalkDetector,\n        awsLambdaDetector,\n      ],\n    ],\n  ]);\n\n  const resourceDetectorsFromEnv =\n    process.env.OTEL_NODE_RESOURCE_DETECTORS?.split(',') ?? ['all'];\n\n  if (resourceDetectorsFromEnv.includes('all')) {\n    return [...resourceDetectors.values()].flat();\n  }\n\n  if (resourceDetectorsFromEnv.includes('none')) {\n    return [];\n  }\n\n  return resourceDetectorsFromEnv.flatMap(detector => {\n    const resourceDetector = resourceDetectors.get(detector);\n    if (!resourceDetector) {\n      diag.error(\n        `Invalid resource detector \"${detector}\" specified in the environment variable OTEL_NODE_RESOURCE_DETECTORS`\n      );\n    }\n    return resourceDetector || [];\n  });\n}\n"]}