{"version": 3, "file": "register.js", "sourceRoot": "", "sources": ["../../src/register.ts"], "names": [], "mappings": ";;AAAA;;;;;;;;;;;;;;GAcG;AACH,yDAAyD;AACzD,4CAA6D;AAC7D,mCAGiB;AAEjB,UAAI,CAAC,SAAS,CACZ,IAAI,uBAAiB,EAAE,EACvB,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,cAAc,CAC3C,CAAC;AAEF,MAAM,GAAG,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC;IACpC,gBAAgB,EAAE,IAAA,mCAA2B,GAAE;IAC/C,iBAAiB,EAAE,IAAA,mCAA2B,GAAE;CACjD,CAAC,CAAC;AAEH,IAAI;IACF,GAAG,CAAC,KAAK,EAAE,CAAC;IACZ,UAAI,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;CAC3E;AAAC,OAAO,KAAK,EAAE;IACd,UAAI,CAAC,KAAK,CACR,2GAA2G,EAC3G,KAAK,CACN,CAAC;CACH;AAED,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;IACzB,GAAG;SACA,QAAQ,EAAE;SACV,IAAI,CAAC,GAAG,EAAE,CAAC,UAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACtD,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,UAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC9E,CAAC,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as opentelemetry from '@opentelemetry/sdk-node';\nimport { diag, DiagConsoleLogger } from '@opentelemetry/api';\nimport {\n  getNodeAutoInstrumentations,\n  getResourceDetectorsFromEnv,\n} from './utils';\n\ndiag.setLogger(\n  new DiagConsoleLogger(),\n  opentelemetry.core.getEnv().OTEL_LOG_LEVEL\n);\n\nconst sdk = new opentelemetry.NodeSDK({\n  instrumentations: getNodeAutoInstrumentations(),\n  resourceDetectors: getResourceDetectorsFromEnv(),\n});\n\ntry {\n  sdk.start();\n  diag.info('OpenTelemetry automatic instrumentation started successfully');\n} catch (error) {\n  diag.error(\n    'Error initializing OpenTelemetry SDK. Your application is not instrumented and will not produce telemetry',\n    error\n  );\n}\n\nprocess.on('SIGTERM', () => {\n  sdk\n    .shutdown()\n    .then(() => diag.debug('OpenTelemetry SDK terminated'))\n    .catch(error => diag.error('Error terminating OpenTelemetry SDK', error));\n});\n"]}