import { Instrumentation } from '@opentelemetry/instrumentation';
import { AmqplibInstrumentation } from '@opentelemetry/instrumentation-amqplib';
import { AwsLambdaInstrumentation } from '@opentelemetry/instrumentation-aws-lambda';
import { AwsInstrumentation } from '@opentelemetry/instrumentation-aws-sdk';
import { BunyanInstrumentation } from '@opentelemetry/instrumentation-bunyan';
import { CassandraDriverInstrumentation } from '@opentelemetry/instrumentation-cassandra-driver';
import { ConnectInstrumentation } from '@opentelemetry/instrumentation-connect';
import { CucumberInstrumentation } from '@opentelemetry/instrumentation-cucumber';
import { DataloaderInstrumentation } from '@opentelemetry/instrumentation-dataloader';
import { DnsInstrumentation } from '@opentelemetry/instrumentation-dns';
import { ExpressInstrumentation } from '@opentelemetry/instrumentation-express';
import { FastifyInstrumentation } from '@opentelemetry/instrumentation-fastify';
import { FsInstrumentation } from '@opentelemetry/instrumentation-fs';
import { GenericPoolInstrumentation } from '@opentelemetry/instrumentation-generic-pool';
import { GraphQLInstrumentation } from '@opentelemetry/instrumentation-graphql';
import { GrpcInstrumentation } from '@opentelemetry/instrumentation-grpc';
import { HapiInstrumentation } from '@opentelemetry/instrumentation-hapi';
import { HttpInstrumentation } from '@opentelemetry/instrumentation-http';
import { IORedisInstrumentation } from '@opentelemetry/instrumentation-ioredis';
import { KnexInstrumentation } from '@opentelemetry/instrumentation-knex';
import { KoaInstrumentation } from '@opentelemetry/instrumentation-koa';
import { LruMemoizerInstrumentation } from '@opentelemetry/instrumentation-lru-memoizer';
import { MemcachedInstrumentation } from '@opentelemetry/instrumentation-memcached';
import { MongoDBInstrumentation } from '@opentelemetry/instrumentation-mongodb';
import { MongooseInstrumentation } from '@opentelemetry/instrumentation-mongoose';
import { MySQL2Instrumentation } from '@opentelemetry/instrumentation-mysql2';
import { MySQLInstrumentation } from '@opentelemetry/instrumentation-mysql';
import { NestInstrumentation } from '@opentelemetry/instrumentation-nestjs-core';
import { NetInstrumentation } from '@opentelemetry/instrumentation-net';
import { PgInstrumentation } from '@opentelemetry/instrumentation-pg';
import { PinoInstrumentation } from '@opentelemetry/instrumentation-pino';
import { RedisInstrumentation as RedisInstrumentationV2 } from '@opentelemetry/instrumentation-redis';
import { RedisInstrumentation as RedisInstrumentationV4 } from '@opentelemetry/instrumentation-redis-4';
import { RestifyInstrumentation } from '@opentelemetry/instrumentation-restify';
import { RouterInstrumentation } from '@opentelemetry/instrumentation-router';
import { SocketIoInstrumentation } from '@opentelemetry/instrumentation-socket.io';
import { TediousInstrumentation } from '@opentelemetry/instrumentation-tedious';
import { WinstonInstrumentation } from '@opentelemetry/instrumentation-winston';
import { Detector, DetectorSync } from '@opentelemetry/resources';
declare const InstrumentationMap: {
    '@opentelemetry/instrumentation-amqplib': typeof AmqplibInstrumentation;
    '@opentelemetry/instrumentation-aws-lambda': typeof AwsLambdaInstrumentation;
    '@opentelemetry/instrumentation-aws-sdk': typeof AwsInstrumentation;
    '@opentelemetry/instrumentation-bunyan': typeof BunyanInstrumentation;
    '@opentelemetry/instrumentation-cassandra-driver': typeof CassandraDriverInstrumentation;
    '@opentelemetry/instrumentation-connect': typeof ConnectInstrumentation;
    '@opentelemetry/instrumentation-cucumber': typeof CucumberInstrumentation;
    '@opentelemetry/instrumentation-dataloader': typeof DataloaderInstrumentation;
    '@opentelemetry/instrumentation-dns': typeof DnsInstrumentation;
    '@opentelemetry/instrumentation-express': typeof ExpressInstrumentation;
    '@opentelemetry/instrumentation-fastify': typeof FastifyInstrumentation;
    '@opentelemetry/instrumentation-fs': typeof FsInstrumentation;
    '@opentelemetry/instrumentation-generic-pool': typeof GenericPoolInstrumentation;
    '@opentelemetry/instrumentation-graphql': typeof GraphQLInstrumentation;
    '@opentelemetry/instrumentation-grpc': typeof GrpcInstrumentation;
    '@opentelemetry/instrumentation-hapi': typeof HapiInstrumentation;
    '@opentelemetry/instrumentation-http': typeof HttpInstrumentation;
    '@opentelemetry/instrumentation-ioredis': typeof IORedisInstrumentation;
    '@opentelemetry/instrumentation-knex': typeof KnexInstrumentation;
    '@opentelemetry/instrumentation-koa': typeof KoaInstrumentation;
    '@opentelemetry/instrumentation-lru-memoizer': typeof LruMemoizerInstrumentation;
    '@opentelemetry/instrumentation-memcached': typeof MemcachedInstrumentation;
    '@opentelemetry/instrumentation-mongodb': typeof MongoDBInstrumentation;
    '@opentelemetry/instrumentation-mongoose': typeof MongooseInstrumentation;
    '@opentelemetry/instrumentation-mysql2': typeof MySQL2Instrumentation;
    '@opentelemetry/instrumentation-mysql': typeof MySQLInstrumentation;
    '@opentelemetry/instrumentation-nestjs-core': typeof NestInstrumentation;
    '@opentelemetry/instrumentation-net': typeof NetInstrumentation;
    '@opentelemetry/instrumentation-pg': typeof PgInstrumentation;
    '@opentelemetry/instrumentation-pino': typeof PinoInstrumentation;
    '@opentelemetry/instrumentation-redis': typeof RedisInstrumentationV2;
    '@opentelemetry/instrumentation-redis-4': typeof RedisInstrumentationV4;
    '@opentelemetry/instrumentation-restify': typeof RestifyInstrumentation;
    '@opentelemetry/instrumentation-router': typeof RouterInstrumentation;
    '@opentelemetry/instrumentation-socket.io': typeof SocketIoInstrumentation;
    '@opentelemetry/instrumentation-tedious': typeof TediousInstrumentation;
    '@opentelemetry/instrumentation-winston': typeof WinstonInstrumentation;
};
declare type ConfigArg<T> = T extends new (...args: infer U) => unknown ? U[0] : never;
export declare type InstrumentationConfigMap = {
    [Name in keyof typeof InstrumentationMap]?: ConfigArg<(typeof InstrumentationMap)[Name]>;
};
export declare function getNodeAutoInstrumentations(inputConfigs?: InstrumentationConfigMap): Instrumentation[];
export declare function getResourceDetectorsFromEnv(): Array<Detector | DetectorSync>;
export {};
//# sourceMappingURL=utils.d.ts.map