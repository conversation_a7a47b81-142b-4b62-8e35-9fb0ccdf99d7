{"name": "@opentelemetry/auto-instrumentations-node", "version": "0.40.3", "description": "Metapackage which bundles opentelemetry node core and contrib instrumentations", "author": "OpenTelemetry Authors", "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/metapackages/auto-instrumentations-node#readme", "license": "Apache-2.0", "engines": {"node": ">=14"}, "publishConfig": {"access": "public"}, "main": "build/src/index.js", "types": "build/src/index.d.ts", "exports": {".": "./build/src/index.js", "./register": "./build/src/register.js"}, "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/auto-instrumentations-node --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "yarn test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.ts'", "watch": "tsc -w"}, "bugs": {"url": "https://github.com/open-telemetry/opentelemetry-js-contrib/issues"}, "peerDependencies": {"@opentelemetry/api": "^1.4.1"}, "devDependencies": {"@opentelemetry/api": "^1.4.1", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "@types/sinon": "10.0.18", "mocha": "7.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "sinon": "15.2.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/instrumentation-amqplib": "^0.33.5", "@opentelemetry/instrumentation-aws-lambda": "^0.37.4", "@opentelemetry/instrumentation-aws-sdk": "^0.37.2", "@opentelemetry/instrumentation-bunyan": "^0.34.1", "@opentelemetry/instrumentation-cassandra-driver": "^0.34.2", "@opentelemetry/instrumentation-connect": "^0.32.4", "@opentelemetry/instrumentation-cucumber": "^0.2.1", "@opentelemetry/instrumentation-dataloader": "^0.5.4", "@opentelemetry/instrumentation-dns": "^0.32.5", "@opentelemetry/instrumentation-express": "^0.34.1", "@opentelemetry/instrumentation-fastify": "^0.32.6", "@opentelemetry/instrumentation-fs": "^0.8.4", "@opentelemetry/instrumentation-generic-pool": "^0.32.5", "@opentelemetry/instrumentation-graphql": "^0.36.1", "@opentelemetry/instrumentation-grpc": "^0.46.0", "@opentelemetry/instrumentation-hapi": "^0.33.3", "@opentelemetry/instrumentation-http": "^0.46.0", "@opentelemetry/instrumentation-ioredis": "^0.36.1", "@opentelemetry/instrumentation-knex": "^0.32.4", "@opentelemetry/instrumentation-koa": "^0.36.4", "@opentelemetry/instrumentation-lru-memoizer": "^0.33.5", "@opentelemetry/instrumentation-memcached": "^0.32.5", "@opentelemetry/instrumentation-mongodb": "^0.38.1", "@opentelemetry/instrumentation-mongoose": "^0.34.0", "@opentelemetry/instrumentation-mysql": "^0.34.5", "@opentelemetry/instrumentation-mysql2": "^0.34.5", "@opentelemetry/instrumentation-nestjs-core": "^0.33.4", "@opentelemetry/instrumentation-net": "^0.32.5", "@opentelemetry/instrumentation-pg": "^0.37.2", "@opentelemetry/instrumentation-pino": "^0.34.5", "@opentelemetry/instrumentation-redis": "^0.35.5", "@opentelemetry/instrumentation-redis-4": "^0.35.6", "@opentelemetry/instrumentation-restify": "^0.34.3", "@opentelemetry/instrumentation-router": "^0.33.4", "@opentelemetry/instrumentation-socket.io": "^0.35.0", "@opentelemetry/instrumentation-tedious": "^0.6.5", "@opentelemetry/instrumentation-winston": "^0.33.1", "@opentelemetry/resource-detector-alibaba-cloud": "^0.28.5", "@opentelemetry/resource-detector-aws": "^1.3.5", "@opentelemetry/resource-detector-container": "^0.3.5", "@opentelemetry/resource-detector-gcp": "^0.29.5", "@opentelemetry/resources": "^1.12.0", "@opentelemetry/sdk-node": "^0.46.0"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}