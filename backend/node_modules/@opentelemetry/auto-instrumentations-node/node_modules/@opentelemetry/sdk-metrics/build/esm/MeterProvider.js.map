{"version": 3, "file": "MeterProvider.js", "sourceRoot": "", "sources": ["../../src/MeterProvider.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EACL,IAAI,EAIJ,eAAe,GAChB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAa,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAE/D,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAa1D;;GAEG;AACH;IAIE,uBAAY,OAA8B;;;QAFlC,cAAS,GAAG,KAAK,CAAC;QAGxB,IAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CACvC,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAI,QAAQ,CAAC,KAAK,EAAE,CACtC,CAAC;QACF,IAAI,CAAC,YAAY,GAAG,IAAI,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,KAAI,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;;gBACtD,KAAmB,IAAA,KAAA,SAAA,OAAO,CAAC,KAAK,CAAA,gBAAA,4BAAE;oBAA7B,IAAM,IAAI,WAAA;oBACb,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBAC9C;;;;;;;;;SACF;IACH,CAAC;IAED;;OAEG;IACH,gCAAQ,GAAR,UAAS,IAAY,EAAE,OAAY,EAAE,OAA0B;QAAxC,wBAAA,EAAA,YAAY;QAAE,wBAAA,EAAA,YAA0B;QAC7D,sHAAsH;QACtH,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC7D,OAAO,eAAe,EAAE,CAAC;SAC1B;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC;YAC3C,IAAI,MAAA;YACJ,OAAO,SAAA;YACP,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC,KAAK,CAAC;IACX,CAAC;IAED;;;;;OAKG;IACH,uCAAe,GAAf,UAAgB,YAA0B;QACxC,IAAM,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QACvE,YAAY,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG;IACG,gCAAQ,GAAd,UAAe,OAAyB;;;;;wBACtC,IAAI,IAAI,CAAC,SAAS,EAAE;4BAClB,IAAI,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;4BAChE,sBAAO;yBACR;wBAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;wBAEtB,qBAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAA,SAAS;gCAC9C,OAAO,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;4BACrC,CAAC,CAAC,CACH,EAAA;;wBAJD,SAIC,CAAC;;;;;KACH;IAED;;;;OAIG;IACG,kCAAU,GAAhB,UAAiB,OAA2B;;;;;wBAC1C,8BAA8B;wBAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;4BAClB,IAAI,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;4BACzE,sBAAO;yBACR;wBAED,qBAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAA,SAAS;gCAC9C,OAAO,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;4BACvC,CAAC,CAAC,CACH,EAAA;;wBAJD,SAIC,CAAC;;;;;KACH;IACH,oBAAC;AAAD,CAAC,AApFD,IAoFC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  MeterProvider as IMeterProvider,\n  Meter as IMeter,\n  MeterOptions,\n  createNoopMeter,\n} from '@opentelemetry/api';\nimport { IResource, Resource } from '@opentelemetry/resources';\nimport { MetricReader } from './export/MetricReader';\nimport { MeterProviderSharedState } from './state/MeterProviderSharedState';\nimport { MetricCollector } from './state/MetricCollector';\nimport { ForceFlushOptions, ShutdownOptions } from './types';\nimport { View } from './view/View';\n\n/**\n * MeterProviderOptions provides an interface for configuring a MeterProvider.\n */\nexport interface MeterProviderOptions {\n  /** Resource associated with metric telemetry  */\n  resource?: IResource;\n  views?: View[];\n}\n\n/**\n * This class implements the {@link MeterProvider} interface.\n */\nexport class MeterProvider implements IMeterProvider {\n  private _sharedState: MeterProviderSharedState;\n  private _shutdown = false;\n\n  constructor(options?: MeterProviderOptions) {\n    const resource = Resource.default().merge(\n      options?.resource ?? Resource.empty()\n    );\n    this._sharedState = new MeterProviderSharedState(resource);\n    if (options?.views != null && options.views.length > 0) {\n      for (const view of options.views) {\n        this._sharedState.viewRegistry.addView(view);\n      }\n    }\n  }\n\n  /**\n   * Get a meter with the configuration of the MeterProvider.\n   */\n  getMeter(name: string, version = '', options: MeterOptions = {}): IMeter {\n    // https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/metrics/sdk.md#meter-creation\n    if (this._shutdown) {\n      diag.warn('A shutdown MeterProvider cannot provide a Meter');\n      return createNoopMeter();\n    }\n\n    return this._sharedState.getMeterSharedState({\n      name,\n      version,\n      schemaUrl: options.schemaUrl,\n    }).meter;\n  }\n\n  /**\n   * Register a {@link MetricReader} to the meter provider. After the\n   * registration, the MetricReader can start metrics collection.\n   *\n   * @param metricReader the metric reader to be registered.\n   */\n  addMetricReader(metricReader: MetricReader) {\n    const collector = new MetricCollector(this._sharedState, metricReader);\n    metricReader.setMetricProducer(collector);\n    this._sharedState.metricCollectors.push(collector);\n  }\n\n  /**\n   * Flush all buffered data and shut down the MeterProvider and all registered\n   * MetricReaders.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  async shutdown(options?: ShutdownOptions): Promise<void> {\n    if (this._shutdown) {\n      diag.warn('shutdown may only be called once per MeterProvider');\n      return;\n    }\n\n    this._shutdown = true;\n\n    await Promise.all(\n      this._sharedState.metricCollectors.map(collector => {\n        return collector.shutdown(options);\n      })\n    );\n  }\n\n  /**\n   * Notifies all registered MetricReaders to flush any buffered data.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  async forceFlush(options?: ForceFlushOptions): Promise<void> {\n    // do not flush after shutdown\n    if (this._shutdown) {\n      diag.warn('invalid attempt to force flush after MeterProvider shutdown');\n      return;\n    }\n\n    await Promise.all(\n      this._sharedState.metricCollectors.map(collector => {\n        return collector.forceFlush(options);\n      })\n    );\n  }\n}\n"]}