{"version": 3, "file": "MetricStorage.js", "sourceRoot": "", "sources": ["../../../src/state/MetricStorage.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAMH,OAAO,EACL,0BAA0B,GAE3B,MAAM,yBAAyB,CAAC;AAEjC;;;;GAIG;AACH;IACE,uBAAsB,qBAA2C;QAA3C,0BAAqB,GAArB,qBAAqB,CAAsB;IAAG,CAAC;IAarE,+CAAuB,GAAvB;QACE,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAED,yCAAiB,GAAjB,UAAkB,WAAmB;QACnC,IAAI,CAAC,qBAAqB,GAAG,0BAA0B,CACrD,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAC/B,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAC/B;YACE,WAAW,EAAE,WAAW;YACxB,SAAS,EAAE,IAAI,CAAC,qBAAqB,CAAC,SAAS;YAC/C,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI;YACrC,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM;SAC1C,CACF,CAAC;IACJ,CAAC;IACH,oBAAC;AAAD,CAAC,AA9BD,IA8BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport { MetricData } from '../export/MetricData';\nimport { Maybe } from '../utils';\nimport { MetricCollectorHandle } from './MetricCollector';\nimport {\n  createInstrumentDescriptor,\n  InstrumentDescriptor,\n} from '../InstrumentDescriptor';\n\n/**\n * Internal interface.\n *\n * Represents a storage from which we can collect metrics.\n */\nexport abstract class MetricStorage {\n  constructor(protected _instrumentDescriptor: InstrumentDescriptor) {}\n\n  /**\n   * Collects the metrics from this storage.\n   *\n   * Note: This is a stateful operation and may reset any interval-related\n   * state for the MetricCollector.\n   */\n  abstract collect(\n    collector: MetricCollectorHandle,\n    collectionTime: HrTime\n  ): Maybe<MetricData>;\n\n  getInstrumentDescriptor(): Readonly<InstrumentDescriptor> {\n    return this._instrumentDescriptor;\n  }\n\n  updateDescription(description: string): void {\n    this._instrumentDescriptor = createInstrumentDescriptor(\n      this._instrumentDescriptor.name,\n      this._instrumentDescriptor.type,\n      {\n        description: description,\n        valueType: this._instrumentDescriptor.valueType,\n        unit: this._instrumentDescriptor.unit,\n        advice: this._instrumentDescriptor.advice,\n      }\n    );\n  }\n}\n"]}