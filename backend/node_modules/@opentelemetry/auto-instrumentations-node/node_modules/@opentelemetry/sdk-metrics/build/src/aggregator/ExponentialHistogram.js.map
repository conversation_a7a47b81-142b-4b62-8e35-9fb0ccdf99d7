{"version": 3, "file": "ExponentialHistogram.js", "sourceRoot": "", "sources": ["../../../src/aggregator/ExponentialHistogram.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,mCAMiB;AACjB,qDAI8B;AAC9B,4CAAkD;AAClD,kEAAyD;AAGzD,6DAA0D;AAC1D,2EAAwE;AAExE,uDAAiE;AAcjE,mEAAmE;AACnE,0CAA0C;AAC1C,MAAM,OAAO;IAIX,YACS,GAAW,EACX,IAAY;QADZ,QAAG,GAAH,GAAG,CAAQ;QACX,SAAI,GAAJ,IAAI,CAAQ;IAClB,CAAC;IANJ,MAAM,CAAC,OAAO,CAAC,EAAW,EAAE,EAAW;QACrC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC;CAKF;AAED,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,MAAM,gBAAgB,GAAG,GAAG,CAAC;AAC7B,MAAM,YAAY,GAAG,CAAC,CAAC;AAEvB,MAAa,gCAAgC;IAC3C,YACS,YAAoB,SAAS,EAC5B,WAAW,gBAAgB,EAC3B,gBAAgB,IAAI,EACpB,OAAO,CAAC,EACR,SAAS,CAAC,EACV,aAAa,CAAC,EACd,OAAO,MAAM,CAAC,iBAAiB,EAC/B,OAAO,MAAM,CAAC,iBAAiB,EAC/B,YAAY,IAAI,iBAAO,EAAE,EACzB,YAAY,IAAI,iBAAO,EAAE,EACzB,WAAoB,IAAA,uBAAU,EAAC,SAAS,CAAC;QAV1C,cAAS,GAAT,SAAS,CAAoB;QAC5B,aAAQ,GAAR,QAAQ,CAAmB;QAC3B,kBAAa,GAAb,aAAa,CAAO;QACpB,SAAI,GAAJ,IAAI,CAAI;QACR,WAAM,GAAN,MAAM,CAAI;QACV,eAAU,GAAV,UAAU,CAAI;QACd,SAAI,GAAJ,IAAI,CAA2B;QAC/B,SAAI,GAAJ,IAAI,CAA2B;QAC/B,cAAS,GAAT,SAAS,CAAgB;QACzB,cAAS,GAAT,SAAS,CAAgB;QACzB,aAAQ,GAAR,QAAQ,CAAiC;QAEjD,IAAI,IAAI,CAAC,QAAQ,GAAG,YAAY,EAAE;YAChC,UAAI,CAAC,IAAI,CAAC,yCAAyC,IAAI,CAAC,QAAQ;mDACnB,YAAY,EAAE,CAAC,CAAC;YAC7D,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;SAC9B;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAa;QAClB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,SAAiB;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,YAAY;QACV,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,aAAa;YAC7B,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,QAAQ,EAAE;gBACR,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC5B,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;aACrC;YACD,QAAQ,EAAE;gBACR,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC5B,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;aACrC;YACD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE;YACnC,4CAA4C;YAC5C,OAAO,CAAC,CAAC;SACV;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACH,iBAAiB,CAAC,KAAa,EAAE,SAAiB;QAChD,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE;YACrB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;SACnB;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE;YACrB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;SACnB;QAED,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;QAEzB,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC;YAC7B,OAAO;SACR;QAED,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,SAAS,CAAC;QAE/B,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;SACvD;aAAM;YACL,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;SACxD;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,QAA0C;QAC9C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC;YACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC;SAC1B;aAAM,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,EAAE;YAC/B,IAAI,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;gBAC3B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC;aAC1B;YACD,IAAI,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;gBAC3B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC;aAC1B;SACF;QAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC;QAC1B,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,CAAC;QAEtC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE1C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;QAEvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACzE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG;IACH,IAAI,CAAC,KAAuC;QAC1C,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC;QACtB,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC;QACvB,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS,CAAC;QAEnC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEvC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;QAEvC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED;;;OAGG;IACH,KAAK;QACH,OAAO,IAAI,gCAAgC,CACzC,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EACrB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EACrB,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACK,cAAc,CAAC,OAAgB,EAAE,KAAa,EAAE,SAAiB;QACvE,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAE5C,gCAAgC;QAChC,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,GAAG,GAAG,CAAC,CAAC;QAEZ,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;YAC3B,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC;YACtC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC;SACxC;aAAM,IACL,KAAK,GAAG,OAAO,CAAC,UAAU;YAC1B,OAAO,CAAC,QAAQ,GAAG,KAAK,IAAI,IAAI,CAAC,QAAQ,EACzC;YACA,eAAe,GAAG,IAAI,CAAC;YACvB,GAAG,GAAG,KAAK,CAAC;YACZ,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC;SACzB;aAAM,IACL,KAAK,GAAG,OAAO,CAAC,QAAQ;YACxB,KAAK,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,EAC3C;YACA,eAAe,GAAG,IAAI,CAAC;YACvB,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC;YACzB,IAAI,GAAG,KAAK,CAAC;SACd;QAED,yCAAyC;QACzC,IAAI,eAAe,EAAE;YACnB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACxB,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;;OAKG;IACK,iBAAiB,CACvB,OAAgB,EAChB,KAAa,EACb,SAAiB;QAEjB,IAAI,SAAS,KAAK,CAAC,EAAE;YACnB,0EAA0E;YAC1E,OAAO;SACR;QAED,IAAI,KAAK,GAAG,OAAO,CAAC,UAAU,EAAE;YAC9B,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtC,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;aAC/B;YACD,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;SAC5B;aAAM,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE;YACnC,MAAM,IAAI,GAAG,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC;YACxC,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;aAC/B;YACD,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC1B;QAED,IAAI,WAAW,GAAG,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC;QAC5C,IAAI,WAAW,GAAG,CAAC,EAAE;YACnB,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;SACvC;QACD,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,OAAgB,EAAE,MAAc;QAC5C,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC;QACpD,MAAM,gBAAgB,GAAG,IAAI,GAAG,IAAI,CAAC;QACrC,IAAI,OAAO,GAAG,IAAA,wBAAiB,EAAC,MAAM,CAAC,CAAC;QACxC,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC3B,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;SACzB;QACD,MAAM,gBAAgB,GAAG,OAAO,GAAG,IAAI,CAAC;QACxC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;IACtE,CAAC;IAED;;;OAGG;IACK,YAAY,CAAC,IAAY,EAAE,GAAW;QAC5C,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,OAAO,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClC,IAAI,KAAK,CAAC,CAAC;YACX,GAAG,KAAK,CAAC,CAAC;YACV,MAAM,EAAE,CAAC;SACV;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,MAAc;QAC/B,IAAI,MAAM,KAAK,CAAC,EAAE;YAChB,OAAO;SACR;QACD,IAAI,MAAM,GAAG,CAAC,EAAE;YACd,+DAA+D;YAC/D,wCAAwC;YACxC,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;SAC9D;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC;QAE9C,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEjC,IAAI,CAAC,QAAQ,GAAG,IAAA,uBAAU,EAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,KAAuC;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAEnD,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAChC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EACzD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAC5D,CAAC;QAEF,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAChC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EACzD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAC5D,CAAC;QAEF,OAAO,IAAI,CAAC,GAAG,CACb,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,EAC7D,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,CAC9D,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CACrB,OAAgB,EAChB,YAAoB,EACpB,QAAgB;QAEhB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC3B;QACD,MAAM,KAAK,GAAG,YAAY,GAAG,QAAQ,CAAC;QACtC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACK,aAAa,CACnB,IAAa,EACb,KAAuC,EACvC,MAAe,EACf,KAAa;QAEb,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,CAAC,iBAAiB,CACpB,IAAI,EACJ,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,WAAW,EAChC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CACb,CAAC;SACH;IACH,CAAC;IAED;;;OAGG;IACK,YAAY,CAClB,IAAa,EACb,KAAuC,EACvC,MAAe,EACf,KAAa;QAEb,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,QAAQ,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,WAAW,CAAC;YAClD,IAAI,WAAW,GAAG,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAC5C,IAAI,WAAW,GAAG,CAAC,EAAE;gBACnB,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;aACpC;YACD,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACjD;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;CACF;AApbD,4EAobC;AAED;;GAEG;AACH,MAAa,8BAA8B;IAMzC;;;;;OAKG;IACH,YACW,QAAgB,EACR,aAAsB;QAD9B,aAAQ,GAAR,QAAQ,CAAQ;QACR,kBAAa,GAAb,aAAa,CAAS;QAXlC,SAAI,GACT,sBAAc,CAAC,qBAAqB,CAAC;IAWpC,CAAC;IAEJ,kBAAkB,CAAC,SAAiB;QAClC,OAAO,IAAI,gCAAgC,CACzC,SAAS,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CACH,QAA0C,EAC1C,KAAuC;QAEvC,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QAC7B,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEvB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAI,CACF,QAA0C,EAC1C,OAAyC;QAEzC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAC/B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEtB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,YAAY,CACV,UAA4B,EAC5B,sBAA8C,EAC9C,wBAAgF,EAChF,OAAe;QAEf,OAAO;YACL,UAAU;YACV,sBAAsB;YACtB,aAAa,EAAE,0BAAa,CAAC,qBAAqB;YAClD,UAAU,EAAE,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,EAAE;gBACtE,MAAM,UAAU,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;gBAE/C,kDAAkD;gBAClD,MAAM,oBAAoB,GACxB,UAAU,CAAC,IAAI,KAAK,qCAAc,CAAC,eAAe;oBAClD,UAAU,CAAC,IAAI,KAAK,qCAAc,CAAC,gBAAgB;oBACnD,UAAU,CAAC,IAAI,KAAK,qCAAc,CAAC,0BAA0B,CAAC;gBAEhE,OAAO;oBACL,UAAU;oBACV,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,OAAO;oBACP,KAAK,EAAE;wBACL,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACtD,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACtD,GAAG,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACvD,QAAQ,EAAE;4BACR,MAAM,EAAE,UAAU,CAAC,QAAQ,CAAC,MAAM;4BAClC,YAAY,EAAE,UAAU,CAAC,QAAQ,CAAC,YAAY;yBAC/C;wBACD,QAAQ,EAAE;4BACR,MAAM,EAAE,UAAU,CAAC,QAAQ,CAAC,MAAM;4BAClC,YAAY,EAAE,UAAU,CAAC,QAAQ,CAAC,YAAY;yBAC/C;wBACD,KAAK,EAAE,UAAU,CAAC,KAAK;wBACvB,KAAK,EAAE,UAAU,CAAC,KAAK;wBACvB,SAAS,EAAE,UAAU,CAAC,SAAS;qBAChC;iBACF,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;CACF;AA9FD,wEA8FC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Accumulation,\n  AccumulationRecord,\n  Aggregator,\n  AggregatorKind,\n  ExponentialHistogram,\n} from './types';\nimport {\n  DataPointType,\n  ExponentialHistogramMetricData,\n  MetricDescriptor,\n} from '../export/MetricData';\nimport { diag, HrTime } from '@opentelemetry/api';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { Maybe } from '../utils';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\nimport { Buckets } from './exponential-histogram/Buckets';\nimport { getMapping } from './exponential-histogram/mapping/getMapping';\nimport { Mapping } from './exponential-histogram/mapping/types';\nimport { nextGreaterSquare } from './exponential-histogram/util';\n\n/**\n * Internal value type for ExponentialHistogramAggregation.\n * Differs from the exported type as undefined sum/min/max complicate arithmetic\n * performed by this aggregation, but are required to be undefined in the exported types.\n */\ninterface InternalHistogram extends ExponentialHistogram {\n  hasMinMax: boolean;\n  min: number;\n  max: number;\n  sum: number;\n}\n\n// HighLow is a utility class used for computing a common scale for\n// two exponential histogram accumulations\nclass HighLow {\n  static combine(h1: HighLow, h2: HighLow): HighLow {\n    return new HighLow(Math.min(h1.low, h2.low), Math.max(h1.high, h2.high));\n  }\n  constructor(\n    public low: number,\n    public high: number\n  ) {}\n}\n\nconst MAX_SCALE = 20;\nconst DEFAULT_MAX_SIZE = 160;\nconst MIN_MAX_SIZE = 2;\n\nexport class ExponentialHistogramAccumulation implements Accumulation {\n  constructor(\n    public startTime: HrTime = startTime,\n    private _maxSize = DEFAULT_MAX_SIZE,\n    private _recordMinMax = true,\n    private _sum = 0,\n    private _count = 0,\n    private _zeroCount = 0,\n    private _min = Number.POSITIVE_INFINITY,\n    private _max = Number.NEGATIVE_INFINITY,\n    private _positive = new Buckets(),\n    private _negative = new Buckets(),\n    private _mapping: Mapping = getMapping(MAX_SCALE)\n  ) {\n    if (this._maxSize < MIN_MAX_SIZE) {\n      diag.warn(`Exponential Histogram Max Size set to ${this._maxSize}, \\\n                changing to the minimum size of: ${MIN_MAX_SIZE}`);\n      this._maxSize = MIN_MAX_SIZE;\n    }\n  }\n\n  /**\n   * record updates a histogram with a single count\n   * @param {Number} value\n   */\n  record(value: number) {\n    this.updateByIncrement(value, 1);\n  }\n\n  /**\n   * Sets the start time for this accumulation\n   * @param {HrTime} startTime\n   */\n  setStartTime(startTime: HrTime): void {\n    this.startTime = startTime;\n  }\n\n  /**\n   * Returns the datapoint representation of this accumulation\n   * @param {HrTime} startTime\n   */\n  toPointValue(): InternalHistogram {\n    return {\n      hasMinMax: this._recordMinMax,\n      min: this.min,\n      max: this.max,\n      sum: this.sum,\n      positive: {\n        offset: this.positive.offset,\n        bucketCounts: this.positive.counts(),\n      },\n      negative: {\n        offset: this.negative.offset,\n        bucketCounts: this.negative.counts(),\n      },\n      count: this.count,\n      scale: this.scale,\n      zeroCount: this.zeroCount,\n    };\n  }\n\n  /**\n   * @returns {Number} The sum of values recorded by this accumulation\n   */\n  get sum(): number {\n    return this._sum;\n  }\n\n  /**\n   * @returns {Number} The minimum value recorded by this accumulation\n   */\n  get min(): number {\n    return this._min;\n  }\n\n  /**\n   * @returns {Number} The maximum value recorded by this accumulation\n   */\n  get max(): number {\n    return this._max;\n  }\n\n  /**\n   * @returns {Number} The count of values recorded by this accumulation\n   */\n  get count(): number {\n    return this._count;\n  }\n\n  /**\n   * @returns {Number} The number of 0 values recorded by this accumulation\n   */\n  get zeroCount(): number {\n    return this._zeroCount;\n  }\n\n  /**\n   * @returns {Number} The scale used by thie accumulation\n   */\n  get scale(): number {\n    if (this._count === this._zeroCount) {\n      // all zeros! scale doesn't matter, use zero\n      return 0;\n    }\n    return this._mapping.scale;\n  }\n\n  /**\n   * positive holds the postive values\n   * @returns {Buckets}\n   */\n  get positive(): Buckets {\n    return this._positive;\n  }\n\n  /**\n   * negative holds the negative values by their absolute value\n   * @returns {Buckets}\n   */\n  get negative(): Buckets {\n    return this._negative;\n  }\n\n  /**\n   * uppdateByIncr supports updating a histogram with a non-negative\n   * increment.\n   * @param value\n   * @param increment\n   */\n  updateByIncrement(value: number, increment: number) {\n    if (value > this._max) {\n      this._max = value;\n    }\n    if (value < this._min) {\n      this._min = value;\n    }\n\n    this._count += increment;\n\n    if (value === 0) {\n      this._zeroCount += increment;\n      return;\n    }\n\n    this._sum += value * increment;\n\n    if (value > 0) {\n      this._updateBuckets(this._positive, value, increment);\n    } else {\n      this._updateBuckets(this._negative, -value, increment);\n    }\n  }\n\n  /**\n   * merge combines data from previous value into self\n   * @param {ExponentialHistogramAccumulation} previous\n   */\n  merge(previous: ExponentialHistogramAccumulation) {\n    if (this._count === 0) {\n      this._min = previous.min;\n      this._max = previous.max;\n    } else if (previous.count !== 0) {\n      if (previous.min < this.min) {\n        this._min = previous.min;\n      }\n      if (previous.max > this.max) {\n        this._max = previous.max;\n      }\n    }\n\n    this.startTime = previous.startTime;\n    this._sum += previous.sum;\n    this._count += previous.count;\n    this._zeroCount += previous.zeroCount;\n\n    const minScale = this._minScale(previous);\n\n    this._downscale(this.scale - minScale);\n\n    this._mergeBuckets(this.positive, previous, previous.positive, minScale);\n    this._mergeBuckets(this.negative, previous, previous.negative, minScale);\n  }\n\n  /**\n   * diff substracts other from self\n   * @param {ExponentialHistogramAccumulation} other\n   */\n  diff(other: ExponentialHistogramAccumulation) {\n    this._min = Infinity;\n    this._max = -Infinity;\n    this._sum -= other.sum;\n    this._count -= other.count;\n    this._zeroCount -= other.zeroCount;\n\n    const minScale = this._minScale(other);\n\n    this._downscale(this.scale - minScale);\n\n    this._diffBuckets(this.positive, other, other.positive, minScale);\n    this._diffBuckets(this.negative, other, other.negative, minScale);\n  }\n\n  /**\n   * clone returns a deep copy of self\n   * @returns {ExponentialHistogramAccumulation}\n   */\n  clone(): ExponentialHistogramAccumulation {\n    return new ExponentialHistogramAccumulation(\n      this.startTime,\n      this._maxSize,\n      this._recordMinMax,\n      this._sum,\n      this._count,\n      this._zeroCount,\n      this._min,\n      this._max,\n      this.positive.clone(),\n      this.negative.clone(),\n      this._mapping\n    );\n  }\n\n  /**\n   * _updateBuckets maps the incoming value to a bucket index for the current\n   * scale. If the bucket index is outside of the range of the backing array,\n   * it will rescale the backing array and update the mapping for the new scale.\n   */\n  private _updateBuckets(buckets: Buckets, value: number, increment: number) {\n    let index = this._mapping.mapToIndex(value);\n\n    // rescale the mapping if needed\n    let rescalingNeeded = false;\n    let high = 0;\n    let low = 0;\n\n    if (buckets.length === 0) {\n      buckets.indexStart = index;\n      buckets.indexEnd = buckets.indexStart;\n      buckets.indexBase = buckets.indexStart;\n    } else if (\n      index < buckets.indexStart &&\n      buckets.indexEnd - index >= this._maxSize\n    ) {\n      rescalingNeeded = true;\n      low = index;\n      high = buckets.indexEnd;\n    } else if (\n      index > buckets.indexEnd &&\n      index - buckets.indexStart >= this._maxSize\n    ) {\n      rescalingNeeded = true;\n      low = buckets.indexStart;\n      high = index;\n    }\n\n    // rescale and compute index at new scale\n    if (rescalingNeeded) {\n      const change = this._changeScale(high, low);\n      this._downscale(change);\n      index = this._mapping.mapToIndex(value);\n    }\n\n    this._incrementIndexBy(buckets, index, increment);\n  }\n\n  /**\n   * _incrementIndexBy increments the count of the bucket specified by `index`.\n   * If the index is outside of the range [buckets.indexStart, buckets.indexEnd]\n   * the boundaries of the backing array will be adjusted and more buckets will\n   * be added if needed.\n   */\n  private _incrementIndexBy(\n    buckets: Buckets,\n    index: number,\n    increment: number\n  ) {\n    if (increment === 0) {\n      // nothing to do for a zero increment, can happen during a merge operation\n      return;\n    }\n\n    if (index < buckets.indexStart) {\n      const span = buckets.indexEnd - index;\n      if (span >= buckets.backing.length) {\n        this._grow(buckets, span + 1);\n      }\n      buckets.indexStart = index;\n    } else if (index > buckets.indexEnd) {\n      const span = index - buckets.indexStart;\n      if (span >= buckets.backing.length) {\n        this._grow(buckets, span + 1);\n      }\n      buckets.indexEnd = index;\n    }\n\n    let bucketIndex = index - buckets.indexBase;\n    if (bucketIndex < 0) {\n      bucketIndex += buckets.backing.length;\n    }\n    buckets.incrementBucket(bucketIndex, increment);\n  }\n\n  /**\n   * grow resizes the backing array by doubling in size up to maxSize.\n   * This extends the array with a bunch of zeros and copies the\n   * existing counts to the same position.\n   */\n  private _grow(buckets: Buckets, needed: number) {\n    const size = buckets.backing.length;\n    const bias = buckets.indexBase - buckets.indexStart;\n    const oldPositiveLimit = size - bias;\n    let newSize = nextGreaterSquare(needed);\n    if (newSize > this._maxSize) {\n      newSize = this._maxSize;\n    }\n    const newPositiveLimit = newSize - bias;\n    buckets.backing.growTo(newSize, oldPositiveLimit, newPositiveLimit);\n  }\n\n  /**\n   * _changeScale computes how much downscaling is needed by shifting the\n   * high and low values until they are separated by no more than size.\n   */\n  private _changeScale(high: number, low: number): number {\n    let change = 0;\n    while (high - low >= this._maxSize) {\n      high >>= 1;\n      low >>= 1;\n      change++;\n    }\n    return change;\n  }\n\n  /**\n   * _downscale subtracts `change` from the current mapping scale.\n   */\n  private _downscale(change: number) {\n    if (change === 0) {\n      return;\n    }\n    if (change < 0) {\n      // Note: this should be impossible. If we get here it's because\n      // there is a bug in the implementation.\n      throw new Error(`impossible change of scale: ${this.scale}`);\n    }\n    const newScale = this._mapping.scale - change;\n\n    this._positive.downscale(change);\n    this._negative.downscale(change);\n\n    this._mapping = getMapping(newScale);\n  }\n\n  /**\n   * _minScale is used by diff and merge to compute an ideal combined scale\n   */\n  private _minScale(other: ExponentialHistogramAccumulation): number {\n    const minScale = Math.min(this.scale, other.scale);\n\n    const highLowPos = HighLow.combine(\n      this._highLowAtScale(this.positive, this.scale, minScale),\n      this._highLowAtScale(other.positive, other.scale, minScale)\n    );\n\n    const highLowNeg = HighLow.combine(\n      this._highLowAtScale(this.negative, this.scale, minScale),\n      this._highLowAtScale(other.negative, other.scale, minScale)\n    );\n\n    return Math.min(\n      minScale - this._changeScale(highLowPos.high, highLowPos.low),\n      minScale - this._changeScale(highLowNeg.high, highLowNeg.low)\n    );\n  }\n\n  /**\n   * _highLowAtScale is used by diff and merge to compute an ideal combined scale.\n   */\n  private _highLowAtScale(\n    buckets: Buckets,\n    currentScale: number,\n    newScale: number\n  ): HighLow {\n    if (buckets.length === 0) {\n      return new HighLow(0, -1);\n    }\n    const shift = currentScale - newScale;\n    return new HighLow(buckets.indexStart >> shift, buckets.indexEnd >> shift);\n  }\n\n  /**\n   * _mergeBuckets translates index values from another histogram and\n   * adds the values into the corresponding buckets of this histogram.\n   */\n  private _mergeBuckets(\n    ours: Buckets,\n    other: ExponentialHistogramAccumulation,\n    theirs: Buckets,\n    scale: number\n  ) {\n    const theirOffset = theirs.offset;\n    const theirChange = other.scale - scale;\n\n    for (let i = 0; i < theirs.length; i++) {\n      this._incrementIndexBy(\n        ours,\n        (theirOffset + i) >> theirChange,\n        theirs.at(i)\n      );\n    }\n  }\n\n  /**\n   * _diffBuckets translates index values from another histogram and\n   * subtracts the values in the corresponding buckets of this histogram.\n   */\n  private _diffBuckets(\n    ours: Buckets,\n    other: ExponentialHistogramAccumulation,\n    theirs: Buckets,\n    scale: number\n  ) {\n    const theirOffset = theirs.offset;\n    const theirChange = other.scale - scale;\n\n    for (let i = 0; i < theirs.length; i++) {\n      const ourIndex = (theirOffset + i) >> theirChange;\n      let bucketIndex = ourIndex - ours.indexBase;\n      if (bucketIndex < 0) {\n        bucketIndex += ours.backing.length;\n      }\n      ours.decrementBucket(bucketIndex, theirs.at(i));\n    }\n\n    ours.trim();\n  }\n}\n\n/**\n * Aggregator for ExponentialHistogramAccumlations\n */\nexport class ExponentialHistogramAggregator\n  implements Aggregator<ExponentialHistogramAccumulation>\n{\n  public kind: AggregatorKind.EXPONENTIAL_HISTOGRAM =\n    AggregatorKind.EXPONENTIAL_HISTOGRAM;\n\n  /**\n   * @param _maxSize Maximum number of buckets for each of the positive\n   *    and negative ranges, exclusive of the zero-bucket.\n   * @param _recordMinMax If set to true, min and max will be recorded.\n   *    Otherwise, min and max will not be recorded.\n   */\n  constructor(\n    readonly _maxSize: number,\n    private readonly _recordMinMax: boolean\n  ) {}\n\n  createAccumulation(startTime: HrTime) {\n    return new ExponentialHistogramAccumulation(\n      startTime,\n      this._maxSize,\n      this._recordMinMax\n    );\n  }\n\n  /**\n   * Return the result of the merge of two exponential histogram accumulations.\n   */\n  merge(\n    previous: ExponentialHistogramAccumulation,\n    delta: ExponentialHistogramAccumulation\n  ): ExponentialHistogramAccumulation {\n    const result = delta.clone();\n    result.merge(previous);\n\n    return result;\n  }\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   */\n  diff(\n    previous: ExponentialHistogramAccumulation,\n    current: ExponentialHistogramAccumulation\n  ): ExponentialHistogramAccumulation {\n    const result = current.clone();\n    result.diff(previous);\n\n    return result;\n  }\n\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<ExponentialHistogramAccumulation>[],\n    endTime: HrTime\n  ): Maybe<ExponentialHistogramMetricData> {\n    return {\n      descriptor,\n      aggregationTemporality,\n      dataPointType: DataPointType.EXPONENTIAL_HISTOGRAM,\n      dataPoints: accumulationByAttributes.map(([attributes, accumulation]) => {\n        const pointValue = accumulation.toPointValue();\n\n        // determine if instrument allows negative values.\n        const allowsNegativeValues =\n          descriptor.type === InstrumentType.UP_DOWN_COUNTER ||\n          descriptor.type === InstrumentType.OBSERVABLE_GAUGE ||\n          descriptor.type === InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;\n\n        return {\n          attributes,\n          startTime: accumulation.startTime,\n          endTime,\n          value: {\n            min: pointValue.hasMinMax ? pointValue.min : undefined,\n            max: pointValue.hasMinMax ? pointValue.max : undefined,\n            sum: !allowsNegativeValues ? pointValue.sum : undefined,\n            positive: {\n              offset: pointValue.positive.offset,\n              bucketCounts: pointValue.positive.bucketCounts,\n            },\n            negative: {\n              offset: pointValue.negative.offset,\n              bucketCounts: pointValue.negative.bucketCounts,\n            },\n            count: pointValue.count,\n            scale: pointValue.scale,\n            zeroCount: pointValue.zeroCount,\n          },\n        };\n      }),\n    };\n  }\n}\n"]}