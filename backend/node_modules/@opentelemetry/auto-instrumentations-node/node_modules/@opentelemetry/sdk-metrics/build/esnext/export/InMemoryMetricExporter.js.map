{"version": 3, "file": "InMemoryMetricExporter.js", "sourceRoot": "", "sources": ["../../../src/export/InMemoryMetricExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAOvD;;;;GAIG;AACH,MAAM,OAAO,sBAAsB;IAKjC,YAAY,sBAA8C;QAJhD,cAAS,GAAG,KAAK,CAAC;QAEpB,aAAQ,GAAsB,EAAE,CAAC;QAGvC,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,MAAM,CACJ,OAAwB,EACxB,cAA8C;QAE9C,kDAAkD;QAClD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,UAAU,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvE,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,UAAU,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,UAAU;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK;QACH,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,4BAA4B,CAC1B,eAA+B;QAE/B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExportResultCode } from '@opentelemetry/core';\nimport { ExportResult } from '@opentelemetry/core';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { ResourceMetrics } from './MetricData';\nimport { PushMetricExporter } from './MetricExporter';\n\n/**\n * In-memory Metrics Exporter is a Push Metric Exporter\n * which accumulates metrics data in the local memory and\n * allows to inspect it (useful for e.g. unit tests).\n */\nexport class InMemoryMetricExporter implements PushMetricExporter {\n  protected _shutdown = false;\n  protected _aggregationTemporality: AggregationTemporality;\n  private _metrics: ResourceMetrics[] = [];\n\n  constructor(aggregationTemporality: AggregationTemporality) {\n    this._aggregationTemporality = aggregationTemporality;\n  }\n\n  /**\n   * @inheritedDoc\n   */\n  export(\n    metrics: ResourceMetrics,\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    // Avoid storing metrics when exporter is shutdown\n    if (this._shutdown) {\n      setTimeout(() => resultCallback({ code: ExportResultCode.FAILED }), 0);\n      return;\n    }\n\n    this._metrics.push(metrics);\n    setTimeout(() => resultCallback({ code: ExportResultCode.SUCCESS }), 0);\n  }\n\n  /**\n   * Returns all the collected resource metrics\n   * @returns ResourceMetrics[]\n   */\n  public getMetrics(): ResourceMetrics[] {\n    return this._metrics;\n  }\n\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  reset() {\n    this._metrics = [];\n  }\n\n  selectAggregationTemporality(\n    _instrumentType: InstrumentType\n  ): AggregationTemporality {\n    return this._aggregationTemporality;\n  }\n\n  shutdown(): Promise<void> {\n    this._shutdown = true;\n    return Promise.resolve();\n  }\n}\n"]}