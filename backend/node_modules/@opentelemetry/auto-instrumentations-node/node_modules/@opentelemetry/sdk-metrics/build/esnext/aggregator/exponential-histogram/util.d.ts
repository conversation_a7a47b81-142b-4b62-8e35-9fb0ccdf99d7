/**
 * Note: other languages provide this as a built in function. This is
 * a naive, but functionally correct implementation. This is used sparingly,
 * when creating a new mapping in a running application.
 *
 * ldexp returns frac × 2**exp. With the following special cases:
 *   ldexp(±0, exp) = ±0
 *   ldexp(±Inf, exp) = ±Inf
 *   ldexp(NaN, exp) = NaN
 * @param frac
 * @param exp
 * @returns {number}
 */
export declare function ldexp(frac: number, exp: number): number;
/**
 * Computes the next power of two that is greater than or equal to v.
 * This implementation more efficient than, but functionally equivalent
 * to Math.pow(2, Math.ceil(Math.log(x)/Math.log(2))).
 * @param v
 * @returns {number}
 */
export declare function nextGreaterSquare(v: number): number;
//# sourceMappingURL=util.d.ts.map