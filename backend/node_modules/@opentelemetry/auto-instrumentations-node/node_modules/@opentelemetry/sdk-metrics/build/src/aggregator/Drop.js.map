{"version": 3, "file": "Drop.js", "sourceRoot": "", "sources": ["../../../src/aggregator/Drop.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,mCAAyE;AAEzE,+DAA+D;AAC/D,MAAa,cAAc;IAA3B;QACE,SAAI,GAAwB,sBAAc,CAAC,IAAI,CAAC;IAsBlD,CAAC;IApBC,kBAAkB;QAChB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,SAAoB,EAAE,MAAiB;QAC3C,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,CAAC,SAAoB,EAAE,QAAmB;QAC5C,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,YAAY,CACV,WAA6B,EAC7B,uBAA+C,EAC/C,yBAA0D,EAC1D,QAAgB;QAEhB,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAvBD,wCAuBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\nimport { MetricData, MetricDescriptor } from '../export/MetricData';\nimport { Maybe } from '../utils';\nimport { AggregatorKind, Aggregator, AccumulationRecord } from './types';\n\n/** Basic aggregator for None which keeps no recorded value. */\nexport class DropAggregator implements Aggregator<undefined> {\n  kind: AggregatorKind.DROP = AggregatorKind.DROP;\n\n  createAccumulation() {\n    return undefined;\n  }\n\n  merge(_previous: undefined, _delta: undefined) {\n    return undefined;\n  }\n\n  diff(_previous: undefined, _current: undefined) {\n    return undefined;\n  }\n\n  toMetricData(\n    _descriptor: MetricDescriptor,\n    _aggregationTemporality: AggregationTemporality,\n    _accumulationByAttributes: AccumulationRecord<undefined>[],\n    _endTime: HrTime\n  ): Maybe<MetricData> {\n    return undefined;\n  }\n}\n"]}