{"version": 3, "file": "AsyncHooksContextManager.js", "sourceRoot": "", "sources": ["../../src/AsyncHooksContextManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA2D;AAC3D,0CAA0C;AAC1C,yFAAsF;AAEtF,MAAa,wBAAyB,SAAQ,mEAAgC;IAK5E;QACE,KAAK,EAAE,CAAC;QAJF,cAAS,GAAyB,IAAI,GAAG,EAAE,CAAC;QAC5C,WAAM,GAA+B,EAAE,CAAC;QAI9C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;YACtC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YAC/B,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;YAC7B,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YACjC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;SACzC,CAAC,CAAC;IACL,CAAC;IAED,MAAM;;QACJ,OAAO,MAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,mCAAI,kBAAY,CAAC;IAC7D,CAAC;IAED,IAAI,CACF,OAAgB,EAChB,EAAK,EACL,OAA8B,EAC9B,GAAG,IAAO;QAEV,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC5B,IAAI;YACF,OAAO,EAAE,CAAC,IAAI,CAAC,OAAQ,EAAE,GAAG,IAAI,CAAC,CAAC;SACnC;gBAAS;YACR,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;IACH,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO;QACL,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,GAAW,EAAE,IAAY;QACrC,8EAA8E;QAC9E,mEAAmE;QACnE,gFAAgF;QAChF,WAAW;QACX,IAAI,IAAI,KAAK,WAAW;YAAE,OAAO;QAEjC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpD,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;SAClC;IACH,CAAC;IAED;;;;OAIG;IACK,QAAQ,CAAC,GAAW;QAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACK,OAAO,CAAC,GAAW;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC7B;IACH,CAAC;IAED;;OAEG;IACK,MAAM;QACZ,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,OAAgB;QACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;CACF;AAzGD,4DAyGC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, ROOT_CONTEXT } from '@opentelemetry/api';\nimport * as asyncHooks from 'async_hooks';\nimport { AbstractAsyncHooksContextManager } from './AbstractAsyncHooksContextManager';\n\nexport class AsyncHooksContextManager extends AbstractAsyncHooksContextManager {\n  private _asyncHook: asyncHooks.AsyncHook;\n  private _contexts: Map<number, Context> = new Map();\n  private _stack: Array<Context | undefined> = [];\n\n  constructor() {\n    super();\n    this._asyncHook = asyncHooks.createHook({\n      init: this._init.bind(this),\n      before: this._before.bind(this),\n      after: this._after.bind(this),\n      destroy: this._destroy.bind(this),\n      promiseResolve: this._destroy.bind(this),\n    });\n  }\n\n  active(): Context {\n    return this._stack[this._stack.length - 1] ?? ROOT_CONTEXT;\n  }\n\n  with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F> {\n    this._enterContext(context);\n    try {\n      return fn.call(thisArg!, ...args);\n    } finally {\n      this._exitContext();\n    }\n  }\n\n  enable(): this {\n    this._asyncHook.enable();\n    return this;\n  }\n\n  disable(): this {\n    this._asyncHook.disable();\n    this._contexts.clear();\n    this._stack = [];\n    return this;\n  }\n\n  /**\n   * Init hook will be called when userland create a async context, setting the\n   * context as the current one if it exist.\n   * @param uid id of the async context\n   * @param type the resource type\n   */\n  private _init(uid: number, type: string) {\n    // ignore TIMERWRAP as they combine timers with same timeout which can lead to\n    // false context propagation. TIMERWRAP has been removed in node 11\n    // every timer has it's own `Timeout` resource anyway which is used to propagete\n    // context.\n    if (type === 'TIMERWRAP') return;\n\n    const context = this._stack[this._stack.length - 1];\n    if (context !== undefined) {\n      this._contexts.set(uid, context);\n    }\n  }\n\n  /**\n   * Destroy hook will be called when a given context is no longer used so we can\n   * remove its attached context.\n   * @param uid uid of the async context\n   */\n  private _destroy(uid: number) {\n    this._contexts.delete(uid);\n  }\n\n  /**\n   * Before hook is called just before executing a async context.\n   * @param uid uid of the async context\n   */\n  private _before(uid: number) {\n    const context = this._contexts.get(uid);\n    if (context !== undefined) {\n      this._enterContext(context);\n    }\n  }\n\n  /**\n   * After hook is called just after completing the execution of a async context.\n   */\n  private _after() {\n    this._exitContext();\n  }\n\n  /**\n   * Set the given context as active\n   */\n  private _enterContext(context: Context) {\n    this._stack.push(context);\n  }\n\n  /**\n   * Remove the context at the root of the stack\n   */\n  private _exitContext() {\n    this._stack.pop();\n  }\n}\n"]}