import { Context } from '@opentelemetry/api';
import { AbstractAsyncHooksContextManager } from './AbstractAsyncHooksContextManager';
export declare class AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager {
    private _asyncLocalStorage;
    constructor();
    active(): Context;
    with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(context: Context, fn: F, thisArg?: ThisParameterType<F>, ...args: A): ReturnType<F>;
    enable(): this;
    disable(): this;
}
//# sourceMappingURL=AsyncLocalStorageContextManager.d.ts.map