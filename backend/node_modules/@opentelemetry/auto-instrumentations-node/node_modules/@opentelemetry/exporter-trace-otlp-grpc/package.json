{"name": "@opentelemetry/exporter-trace-otlp-grpc", "version": "0.46.0", "description": "OpenTelemetry Collector Exporter allows user to send collected traces to the OpenTelemetry Collector", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build", "clean": "tsc --build --clean", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "version": "node ../../../scripts/version-update.js", "watch": "tsc --watch --build", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../../scripts/peer-api-check.js", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../"}, "keywords": ["opentelemetry", "nodejs", "grpc", "tracing", "profiling", "metrics", "stats"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "build/protos/**/*.proto", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@babel/core": "7.23.6", "@grpc/proto-loader": "^0.7.10", "@opentelemetry/api": "1.7.0", "@opentelemetry/otlp-exporter-base": "0.46.0", "@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/sinon": "10.0.20", "codecov": "3.8.3", "cpx": "1.5.0", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "sinon": "15.1.2", "ts-loader": "8.4.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}, "dependencies": {"@grpc/grpc-js": "^1.7.1", "@opentelemetry/core": "1.19.0", "@opentelemetry/otlp-grpc-exporter-base": "0.46.0", "@opentelemetry/otlp-transformer": "0.46.0", "@opentelemetry/resources": "1.19.0", "@opentelemetry/sdk-trace-base": "1.19.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/exporter-trace-otlp-grpc", "sideEffects": false, "gitHead": "d3c311aec24137084dc820805a2597e120335672"}