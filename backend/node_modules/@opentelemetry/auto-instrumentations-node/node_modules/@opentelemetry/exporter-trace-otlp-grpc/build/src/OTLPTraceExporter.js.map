{"version": 3, "file": "OTLPTraceExporter.js", "sourceRoot": "", "sources": ["../../src/OTLPTraceExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8CAA2D;AAC3D,2CAAyC;AACzC,oFAMgD;AAChD,sEAGyC;AACzC,uCAAoC;AAEpC,MAAM,UAAU,GAAG;IACjB,YAAY,EAAE,iCAAiC,iBAAO,EAAE;CACzD,CAAC;AAEF;;GAEG;AACH,MAAa,iBACX,SAAQ,kDAAkE;IAG1E,YAAY,SAAqC,EAAE;QACjD,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,MAAM,OAAO,mCACR,UAAU,GACV,mBAAY,CAAC,uBAAuB,CACrC,IAAA,aAAM,GAAE,CAAC,iCAAiC,CAC3C,CACF,CAAC;QACF,IAAI,CAAC,QAAQ,KAAb,IAAI,CAAC,QAAQ,GAAK,IAAI,kBAAQ,EAAE,EAAC;QACjC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC5C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACzB;IACH,CAAC;IAED,OAAO,CAAC,KAAqB;QAC3B,OAAO,IAAA,kDAA+B,EAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAAC,MAAkC;QAC9C,OAAO,IAAA,iDAAuB,EAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,oBAAoB;QAClB,OAAO,2CAAiB,CAAC,KAAK,CAAC;IACjC,CAAC;IAED,mBAAmB;QACjB,OAAO,4DAA4D,CAAC;IACtE,CAAC;IAED,gBAAgB,CAAC,MAAkC;QACjD,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE;YAClC,OAAO,MAAM,CAAC,GAAG,CAAC;SACnB;QAED,OAAO,CACL,IAAA,aAAM,GAAE,CAAC,kCAAkC;YAC3C,IAAA,aAAM,GAAE,CAAC,2BAA2B;YACpC,+CAAqB,CACtB,CAAC;IACJ,CAAC;CACF;AA7CD,8CA6CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport { baggageUtils, getEnv } from '@opentelemetry/core';\nimport { Metadata } from '@grpc/grpc-js';\nimport {\n  OTLPGRPCExporterConfigNode,\n  OTLPGRPCExporterNodeBase,\n  ServiceClientType,\n  validateAndNormalizeUrl,\n  DEFAULT_COLLECTOR_URL,\n} from '@opentelemetry/otlp-grpc-exporter-base';\nimport {\n  createExportTraceServiceRequest,\n  IExportTraceServiceRequest,\n} from '@opentelemetry/otlp-transformer';\nimport { VERSION } from './version';\n\nconst USER_AGENT = {\n  'User-Agent': `OTel-OTLP-Exporter-JavaScript/${VERSION}`,\n};\n\n/**\n * OTLP Trace Exporter for Node\n */\nexport class OTLPTraceExporter\n  extends OTLPGRPCExporterNodeBase<ReadableSpan, IExportTraceServiceRequest>\n  implements SpanExporter\n{\n  constructor(config: OTLPGRPCExporterConfigNode = {}) {\n    super(config);\n    const headers = {\n      ...USER_AGENT,\n      ...baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_TRACES_HEADERS\n      ),\n    };\n    this.metadata ||= new Metadata();\n    for (const [k, v] of Object.entries(headers)) {\n      this.metadata.set(k, v);\n    }\n  }\n\n  convert(spans: ReadableSpan[]): IExportTraceServiceRequest {\n    return createExportTraceServiceRequest(spans);\n  }\n\n  getDefaultUrl(config: OTLPGRPCExporterConfigNode) {\n    return validateAndNormalizeUrl(this.getUrlFromConfig(config));\n  }\n\n  getServiceClientType() {\n    return ServiceClientType.SPANS;\n  }\n\n  getServiceProtoPath(): string {\n    return 'opentelemetry/proto/collector/trace/v1/trace_service.proto';\n  }\n\n  getUrlFromConfig(config: OTLPGRPCExporterConfigNode): string {\n    if (typeof config.url === 'string') {\n      return config.url;\n    }\n\n    return (\n      getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT ||\n      getEnv().OTEL_EXPORTER_OTLP_ENDPOINT ||\n      DEFAULT_COLLECTOR_URL\n    );\n  }\n}\n"]}