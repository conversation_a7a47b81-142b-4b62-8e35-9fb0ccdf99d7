{"version": 3, "file": "BatchSpanProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/platform/browser/export/BatchSpanProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,sBAAsB,EAAE,MAAM,wCAAwC,CAAC;AAIhF,MAAM,OAAO,kBAAmB,SAAQ,sBAAuD;IAI7F,YACE,SAAuB,EACvB,MAAwC;QAExC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAEO,MAAM,CAAC,MAAwC;QACrD,IACE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,8BAA8B,MAAK,IAAI;YAC/C,OAAO,QAAQ,KAAK,WAAW,EAC/B;YACA,IAAI,CAAC,yBAAyB,GAAG,GAAG,EAAE;gBACpC,IAAI,QAAQ,CAAC,eAAe,KAAK,QAAQ,EAAE;oBACzC,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;iBACxB;YACH,CAAC,CAAC;YACF,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAE;gBAC5B,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;YACzB,CAAC,CAAC;YACF,QAAQ,CAAC,gBAAgB,CACvB,kBAAkB,EAClB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;YAEF,oGAAoG;YACpG,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC/D;IACH,CAAC;IAES,UAAU;QAClB,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBAClC,QAAQ,CAAC,mBAAmB,CAC1B,kBAAkB,EAClB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;aACH;YACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAClE;SACF;IACH,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { BatchSpanProcessorBase } from '../../../export/BatchSpanProcessorBase';\nimport { SpanExporter } from '../../../export/SpanExporter';\nimport { BatchSpanProcessorBrowserConfig } from '../../../types';\n\nexport class BatchSpanProcessor extends BatchSpanProcessorBase<BatchSpanProcessorBrowserConfig> {\n  private _visibilityChangeListener?: () => void;\n  private _pageHideListener?: () => void;\n\n  constructor(\n    _exporter: SpanExporter,\n    config?: BatchSpanProcessorBrowserConfig\n  ) {\n    super(_exporter, config);\n    this.onInit(config);\n  }\n\n  private onInit(config?: BatchSpanProcessorBrowserConfig): void {\n    if (\n      config?.disableAutoFlushOnDocumentHide !== true &&\n      typeof document !== 'undefined'\n    ) {\n      this._visibilityChangeListener = () => {\n        if (document.visibilityState === 'hidden') {\n          void this.forceFlush();\n        }\n      };\n      this._pageHideListener = () => {\n        void this.forceFlush();\n      };\n      document.addEventListener(\n        'visibilitychange',\n        this._visibilityChangeListener\n      );\n\n      // use 'pagehide' event as a fallback for Safari; see https://bugs.webkit.org/show_bug.cgi?id=116769\n      document.addEventListener('pagehide', this._pageHideListener);\n    }\n  }\n\n  protected onShutdown(): void {\n    if (typeof document !== 'undefined') {\n      if (this._visibilityChangeListener) {\n        document.removeEventListener(\n          'visibilitychange',\n          this._visibilityChangeListener\n        );\n      }\n      if (this._pageHideListener) {\n        document.removeEventListener('pagehide', this._pageHideListener);\n      }\n    }\n  }\n}\n"]}