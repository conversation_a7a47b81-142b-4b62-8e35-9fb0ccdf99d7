{"version": 3, "file": "ConsoleSpanExporter.js", "sourceRoot": "", "sources": ["../../../src/export/ConsoleSpanExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;AAIH,OAAO,EAEL,gBAAgB,EAChB,oBAAoB,GACrB,MAAM,qBAAqB,CAAC;AAE7B;;;GAGG;AAEH,+BAA+B;AAC/B;IAAA;IAiEA,CAAC;IAhEC;;;;OAIG;IACH,oCAAM,GAAN,UACE,KAAqB,EACrB,cAA8C;QAE9C,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,sCAAQ,GAAR;QACE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACpB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,wCAAU,GAAV;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACK,yCAAW,GAAnB,UAAoB,IAAkB;;QACpC,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO;YACnC,QAAQ,EAAE,IAAI,CAAC,YAAY;YAC3B,UAAU,EAAE,MAAA,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,0CAAE,SAAS,EAAE;YACtD,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,QAAQ,EAAE,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC7C,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACK,wCAAU,GAAlB,UACE,KAAqB,EACrB,IAAqC;;;YAErC,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAArB,IAAM,IAAI,kBAAA;gBACb,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;aACnD;;;;;;;;;QACD,IAAI,IAAI,EAAE;YACR,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;SACjD;IACH,CAAC;IACH,0BAAC;AAAD,CAAC,AAjED,IAiEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanExporter } from './SpanExporter';\nimport { ReadableSpan } from './ReadableSpan';\nimport {\n  ExportResult,\n  ExportResultCode,\n  hrTimeToMicroseconds,\n} from '@opentelemetry/core';\n\n/**\n * This is implementation of {@link SpanExporter} that prints spans to the\n * console. This class can be used for diagnostic purposes.\n */\n\n/* eslint-disable no-console */\nexport class ConsoleSpanExporter implements SpanExporter {\n  /**\n   * Export spans.\n   * @param spans\n   * @param resultCallback\n   */\n  export(\n    spans: ReadableSpan[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    return this._sendSpans(spans, resultCallback);\n  }\n\n  /**\n   * Shutdown the exporter.\n   */\n  shutdown(): Promise<void> {\n    this._sendSpans([]);\n    return this.forceFlush();\n  }\n\n  /**\n   * Exports any pending spans in exporter\n   */\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  /**\n   * converts span info into more readable format\n   * @param span\n   */\n  private _exportInfo(span: ReadableSpan) {\n    return {\n      traceId: span.spanContext().traceId,\n      parentId: span.parentSpanId,\n      traceState: span.spanContext().traceState?.serialize(),\n      name: span.name,\n      id: span.spanContext().spanId,\n      kind: span.kind,\n      timestamp: hrTimeToMicroseconds(span.startTime),\n      duration: hrTimeToMicroseconds(span.duration),\n      attributes: span.attributes,\n      status: span.status,\n      events: span.events,\n      links: span.links,\n    };\n  }\n\n  /**\n   * Showing spans in console\n   * @param spans\n   * @param done\n   */\n  private _sendSpans(\n    spans: ReadableSpan[],\n    done?: (result: ExportResult) => void\n  ): void {\n    for (const span of spans) {\n      console.dir(this._exportInfo(span), { depth: 3 });\n    }\n    if (done) {\n      return done({ code: ExportResultCode.SUCCESS });\n    }\n  }\n}\n"]}