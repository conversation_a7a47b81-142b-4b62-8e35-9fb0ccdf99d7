{"version": 3, "file": "LogsExportServiceClient.js", "sourceRoot": "", "sources": ["../../src/LogsExportServiceClient.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,yCAAyC;AACzC,sCAAsC;AAOtC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;KAC5D,yBAAmE,CAAC;AAEvE,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;KAC3D,wBAAiE,CAAC;AAErE,MAAM,qBAAqB,GAAG;IAC5B,MAAM,EAAE;QACN,IAAI,EAAE,2DAA2D;QACjE,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,KAAK;QACrB,gBAAgB,EAAE,CAAC,GAA8B,EAAE,EAAE;YACnD,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QACvD,CAAC;QACD,kBAAkB,EAAE,CAAC,GAAW,EAAE,EAAE;YAClC,OAAO,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC;QACD,iBAAiB,EAAE,CAAC,GAA+B,EAAE,EAAE;YACrD,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QACxD,CAAC;QACD,mBAAmB,EAAE,CAAC,GAAW,EAAE,EAAE;YACnC,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC;KACF;CACF,CAAC;AAEF,gEAAgE;AACnD,QAAA,uBAAuB,GAClC,IAAI,CAAC,4BAA4B,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as root from './generated/root';\nimport * as grpc from '@grpc/grpc-js';\nimport {\n  IExportLogsServiceRequest,\n  IExportLogsServiceResponse,\n} from '@opentelemetry/otlp-transformer';\nimport { ExportType } from './internal-types';\n\nconst responseType = root.opentelemetry.proto.collector.logs.v1\n  .ExportLogsServiceResponse as ExportType<IExportLogsServiceResponse>;\n\nconst requestType = root.opentelemetry.proto.collector.logs.v1\n  .ExportLogsServiceRequest as ExportType<IExportLogsServiceRequest>;\n\nconst logsServiceDefinition = {\n  export: {\n    path: '/opentelemetry.proto.collector.logs.v1.LogsService/Export',\n    requestStream: false,\n    responseStream: false,\n    requestSerialize: (arg: IExportLogsServiceRequest) => {\n      return Buffer.from(requestType.encode(arg).finish());\n    },\n    requestDeserialize: (arg: Buffer) => {\n      return requestType.decode(arg);\n    },\n    responseSerialize: (arg: IExportLogsServiceResponse) => {\n      return Buffer.from(responseType.encode(arg).finish());\n    },\n    responseDeserialize: (arg: Buffer) => {\n      return responseType.decode(arg);\n    },\n  },\n};\n\n// Creates a new instance of a gRPC service client for OTLP logs\nexport const LogsExportServiceClient: grpc.ServiceClientConstructor =\n  grpc.makeGenericClientConstructor(logsServiceDefinition, 'LogsExportService');\n"]}