{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,sCAAsC;AACtC,4CAA0C;AAC1C,8CAAiE;AACjE,6BAA6B;AAE7B,6BAA0B;AAC1B,yBAAyB;AACzB,mCAIiB;AACjB,0EAI2C;AAE3C,6EAAyE;AACzE,yEAAsE;AACtE,uEAAoE;AAEvD,QAAA,qBAAqB,GAAG,uBAAuB,CAAC;AAE7D,SAAgB,MAAM,CACpB,SAA+D,EAC/D,MAAkC;IAElC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;IAEzB,MAAM,WAAW,GAA4B,iBAAiB,CAC5D,MAAM,CAAC,WAAW,EAClB,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CACnC,CAAC;IAEF,IAAI;QACF,IAAI,SAAS,CAAC,oBAAoB,EAAE,KAAK,yBAAiB,CAAC,KAAK,EAAE;YAChE,MAAM,MAAM,GAAG,IAAI,mDAAwB,CAAC,SAAS,CAAC,GAAG,EAAE,WAAW,EAAE;gBACtE,oCAAoC,EAAE,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE;aACtE,CAAC,CAAC;YAEH,6DAA6D;YAC7D,aAAa;YACb,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAClC;aAAM,IAAI,SAAS,CAAC,oBAAoB,EAAE,KAAK,yBAAiB,CAAC,OAAO,EAAE;YACzE,MAAM,MAAM,GAAG,IAAI,sDAAyB,CAAC,SAAS,CAAC,GAAG,EAAE,WAAW,EAAE;gBACvE,oCAAoC,EAAE,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE;aACtE,CAAC,CAAC;YAEH,6DAA6D;YAC7D,aAAa;YACb,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAClC;aAAM,IAAI,SAAS,CAAC,oBAAoB,EAAE,KAAK,yBAAiB,CAAC,IAAI,EAAE;YACtE,MAAM,MAAM,GAAG,IAAI,iDAAuB,CAAC,SAAS,CAAC,GAAG,EAAE,WAAW,EAAE;gBACrE,oCAAoC,EAAE,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE;aACtE,CAAC,CAAC;YAEH,6DAA6D;YAC7D,aAAa;YACb,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAClC;KACF;IAAC,OAAO,GAAG,EAAE;QACZ,IAAA,yBAAkB,EAAC,GAAG,CAAC,CAAC;KACzB;IACD,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;QAClC,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5C,KAAK,CAAC,OAAO,CAAC,CAAC,IAA+B,EAAE,EAAE;YAChD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;KACJ;AACH,CAAC;AA9CD,wBA8CC;AAED,SAAgB,IAAI,CAClB,SAA+D,EAC/D,OAAqB,EACrB,SAAqB,EACrB,OAA2C;IAE3C,IAAI,SAAS,CAAC,aAAa,EAAE;QAC3B,MAAM,cAAc,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC;QAEtD,SAAS,CAAC,aAAa,CAAC,MAAM,CAC5B,cAAc,EACd,SAAS,CAAC,QAAQ,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,EACzC,EAAE,QAAQ,EAAE,QAAQ,EAAE,EACtB,CAAC,GAAuB,EAAE,EAAE;YAC1B,IAAI,GAAG,EAAE;gBACP,UAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;gBAC9C,OAAO,CAAC,GAAG,CAAC,CAAC;aACd;iBAAM;gBACL,UAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAC3B,SAAS,EAAE,CAAC;aACb;QACH,CAAC,CACF,CAAC;KACH;SAAM;QACL,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS;YACT,OAAO;SACR,CAAC,CAAC;KACJ;AACH,CAAC;AA/BD,oBA+BC;AAED,SAAgB,uBAAuB,CAAC,GAAW;;IACjD,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACnD,IAAI,CAAC,WAAW,EAAE;QAChB,GAAG,GAAG,WAAW,GAAG,EAAE,CAAC;KACxB;IACD,MAAM,MAAM,GAAG,IAAI,SAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAI,MAAM,CAAC,QAAQ,KAAK,OAAO,EAAE;QAC/B,OAAO,GAAG,CAAC;KACZ;IACD,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,EAAE;QAC9C,UAAI,CAAC,IAAI,CACP,uFAAuF,CACxF,CAAC;KACH;IACD,IAAI,MAAM,CAAC,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAA,MAAA,MAAM,CAAC,QAAQ,0CAAE,KAAK,CAAC,aAAa,CAAC,CAAA,EAAE;QACpE,UAAI,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;KAChE;IACD,OAAO,MAAM,CAAC,IAAI,CAAC;AACrB,CAAC;AAlBD,0DAkBC;AAED,SAAgB,iBAAiB,CAC/B,WAAgD,EAChD,QAAgB;IAEhB,IAAI,QAAiB,CAAC;IAEtB,IAAI,WAAW,EAAE;QACf,OAAO,WAAW,CAAC;KACpB;SAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;QAC1C,QAAQ,GAAG,KAAK,CAAC;KAClB;SAAM,IACL,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC;QAC9B,QAAQ,KAAK,6BAAqB,EAClC;QACA,QAAQ,GAAG,IAAI,CAAC;KACjB;SAAM;QACL,QAAQ,GAAG,kBAAkB,EAAE,CAAC;KACjC;IAED,IAAI,QAAQ,EAAE;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;KAC1C;SAAM;QACL,OAAO,mBAAmB,EAAE,CAAC;KAC9B;AACH,CAAC;AAxBD,8CAwBC;AAED,SAAS,kBAAkB;IACzB,MAAM,eAAe,GACnB,IAAA,aAAM,GAAE,CAAC,kCAAkC;QAC3C,IAAA,aAAM,GAAE,CAAC,2BAA2B,CAAC;IAEvC,IAAI,eAAe,EAAE;QACnB,OAAO,eAAe,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;KACjD;SAAM;QACL,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,SAAgB,mBAAmB;IACjC,MAAM,YAAY,GAAG,gBAAgB,EAAE,CAAC;IACxC,MAAM,cAAc,GAAG,kBAAkB,EAAE,CAAC;IAC5C,MAAM,aAAa,GAAG,iBAAiB,EAAE,CAAC;IAE1C,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAC/B,YAAY,EACZ,cAAc,EACd,aAAa,CACd,CAAC;AACJ,CAAC;AAVD,kDAUC;AAED,SAAS,gBAAgB;IACvB,MAAM,eAAe,GACnB,IAAA,aAAM,GAAE,CAAC,qCAAqC;QAC9C,IAAA,aAAM,GAAE,CAAC,8BAA8B,CAAC;IAE1C,IAAI,eAAe,EAAE;QACnB,IAAI;YACF,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,eAAe,CAAC,CAAC,CAAC;SACtE;QAAC,WAAM;YACN,UAAI,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAClD,OAAO,SAAS,CAAC;SAClB;KACF;SAAM;QACL,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,kBAAkB;IACzB,MAAM,SAAS,GACb,IAAA,aAAM,GAAE,CAAC,oCAAoC;QAC7C,IAAA,aAAM,GAAE,CAAC,6BAA6B,CAAC;IAEzC,IAAI,SAAS,EAAE;QACb,IAAI;YACF,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC;SAChE;QAAC,WAAM;YACN,UAAI,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAChE,OAAO,SAAS,CAAC;SAClB;KACF;SAAM;QACL,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,iBAAiB;IACxB,MAAM,WAAW,GACf,IAAA,aAAM,GAAE,CAAC,4CAA4C;QACrD,IAAA,aAAM,GAAE,CAAC,qCAAqC,CAAC;IAEjD,IAAI,WAAW,EAAE;QACf,IAAI;YACF,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;SAClE;QAAC,WAAM;YACN,UAAI,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC1D,OAAO,SAAS,CAAC;SAClB;KACF;SAAM;QACL,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,iBAAiB,CACxB,WAAiC;IAEjC,IAAI,WAAW,KAAK,yCAAoB,CAAC,IAAI;QAC3C,OAAO,wBAAwB,CAAC,IAAI,CAAC;SAClC,IAAI,WAAW,KAAK,yCAAoB,CAAC,IAAI;QAChD,OAAO,wBAAwB,CAAC,IAAI,CAAC;IACvC,OAAO,wBAAwB,CAAC,IAAI,CAAC;AACvC,CAAC;AAED;;GAEG;AACH,IAAY,wBAGX;AAHD,WAAY,wBAAwB;IAClC,uEAAQ,CAAA;IACR,uEAAQ,CAAA;AACV,CAAC,EAHW,wBAAwB,GAAxB,gCAAwB,KAAxB,gCAAwB,QAGnC;AAED,SAAgB,oBAAoB,CAClC,WAA6C;IAE7C,IAAI,WAAW,EAAE;QACf,OAAO,iBAAiB,CAAC,WAAW,CAAC,CAAC;KACvC;SAAM;QACL,MAAM,kBAAkB,GACtB,IAAA,aAAM,GAAE,CAAC,qCAAqC;YAC9C,IAAA,aAAM,GAAE,CAAC,8BAA8B,CAAC;QAE1C,OAAO,kBAAkB,KAAK,MAAM;YAClC,CAAC,CAAC,wBAAwB,CAAC,IAAI;YAC/B,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC;KACnC;AACH,CAAC;AAdD,oDAcC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as grpc from '@grpc/grpc-js';\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, globalErrorHandler } from '@opentelemetry/core';\nimport * as path from 'path';\nimport { OTLPGRPCExporterNodeBase } from './OTLPGRPCExporterNodeBase';\nimport { URL } from 'url';\nimport * as fs from 'fs';\nimport {\n  GRPCQueueItem,\n  OTLPGRPCExporterConfigNode,\n  ServiceClientType,\n} from './types';\nimport {\n  ExportServiceError,\n  OTLPExporterError,\n  CompressionAlgorithm,\n} from '@opentelemetry/otlp-exporter-base';\n\nimport { MetricExportServiceClient } from './MetricsExportServiceClient';\nimport { TraceExportServiceClient } from './TraceExportServiceClient';\nimport { LogsExportServiceClient } from './LogsExportServiceClient';\n\nexport const DEFAULT_COLLECTOR_URL = 'http://localhost:4317';\n\nexport function onInit<ExportItem, ServiceRequest>(\n  collector: OTLPGRPCExporterNodeBase<ExportItem, ServiceRequest>,\n  config: OTLPGRPCExporterConfigNode\n): void {\n  collector.grpcQueue = [];\n\n  const credentials: grpc.ChannelCredentials = configureSecurity(\n    config.credentials,\n    collector.getUrlFromConfig(config)\n  );\n\n  try {\n    if (collector.getServiceClientType() === ServiceClientType.SPANS) {\n      const client = new TraceExportServiceClient(collector.url, credentials, {\n        'grpc.default_compression_algorithm': collector.compression.valueOf(),\n      });\n\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      collector.serviceClient = client;\n    } else if (collector.getServiceClientType() === ServiceClientType.METRICS) {\n      const client = new MetricExportServiceClient(collector.url, credentials, {\n        'grpc.default_compression_algorithm': collector.compression.valueOf(),\n      });\n\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      collector.serviceClient = client;\n    } else if (collector.getServiceClientType() === ServiceClientType.LOGS) {\n      const client = new LogsExportServiceClient(collector.url, credentials, {\n        'grpc.default_compression_algorithm': collector.compression.valueOf(),\n      });\n\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      collector.serviceClient = client;\n    }\n  } catch (err) {\n    globalErrorHandler(err);\n  }\n  if (collector.grpcQueue.length > 0) {\n    const queue = collector.grpcQueue.splice(0);\n    queue.forEach((item: GRPCQueueItem<ExportItem>) => {\n      collector.send(item.objects, item.onSuccess, item.onError);\n    });\n  }\n}\n\nexport function send<ExportItem, ServiceRequest>(\n  collector: OTLPGRPCExporterNodeBase<ExportItem, ServiceRequest>,\n  objects: ExportItem[],\n  onSuccess: () => void,\n  onError: (error: OTLPExporterError) => void\n): void {\n  if (collector.serviceClient) {\n    const serviceRequest = collector.convert(objects);\n    const deadline = Date.now() + collector.timeoutMillis;\n\n    collector.serviceClient.export(\n      serviceRequest,\n      collector.metadata || new grpc.Metadata(),\n      { deadline: deadline },\n      (err: ExportServiceError) => {\n        if (err) {\n          diag.error('Service request', serviceRequest);\n          onError(err);\n        } else {\n          diag.debug('Objects sent');\n          onSuccess();\n        }\n      }\n    );\n  } else {\n    collector.grpcQueue.push({\n      objects,\n      onSuccess,\n      onError,\n    });\n  }\n}\n\nexport function validateAndNormalizeUrl(url: string): string {\n  const hasProtocol = url.match(/^([\\w]{1,8}):\\/\\//);\n  if (!hasProtocol) {\n    url = `https://${url}`;\n  }\n  const target = new URL(url);\n  if (target.protocol === 'unix:') {\n    return url;\n  }\n  if (target.pathname && target.pathname !== '/') {\n    diag.warn(\n      'URL path should not be set when using grpc, the path part of the URL will be ignored.'\n    );\n  }\n  if (target.protocol !== '' && !target.protocol?.match(/^(http)s?:$/)) {\n    diag.warn('URL protocol should be http(s)://. Using http://.');\n  }\n  return target.host;\n}\n\nexport function configureSecurity(\n  credentials: grpc.ChannelCredentials | undefined,\n  endpoint: string\n): grpc.ChannelCredentials {\n  let insecure: boolean;\n\n  if (credentials) {\n    return credentials;\n  } else if (endpoint.startsWith('https://')) {\n    insecure = false;\n  } else if (\n    endpoint.startsWith('http://') ||\n    endpoint === DEFAULT_COLLECTOR_URL\n  ) {\n    insecure = true;\n  } else {\n    insecure = getSecurityFromEnv();\n  }\n\n  if (insecure) {\n    return grpc.credentials.createInsecure();\n  } else {\n    return useSecureConnection();\n  }\n}\n\nfunction getSecurityFromEnv(): boolean {\n  const definedInsecure =\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_INSECURE ||\n    getEnv().OTEL_EXPORTER_OTLP_INSECURE;\n\n  if (definedInsecure) {\n    return definedInsecure.toLowerCase() === 'true';\n  } else {\n    return false;\n  }\n}\n\nexport function useSecureConnection(): grpc.ChannelCredentials {\n  const rootCertPath = retrieveRootCert();\n  const privateKeyPath = retrievePrivateKey();\n  const certChainPath = retrieveCertChain();\n\n  return grpc.credentials.createSsl(\n    rootCertPath,\n    privateKeyPath,\n    certChainPath\n  );\n}\n\nfunction retrieveRootCert(): Buffer | undefined {\n  const rootCertificate =\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE ||\n    getEnv().OTEL_EXPORTER_OTLP_CERTIFICATE;\n\n  if (rootCertificate) {\n    try {\n      return fs.readFileSync(path.resolve(process.cwd(), rootCertificate));\n    } catch {\n      diag.warn('Failed to read root certificate file');\n      return undefined;\n    }\n  } else {\n    return undefined;\n  }\n}\n\nfunction retrievePrivateKey(): Buffer | undefined {\n  const clientKey =\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY ||\n    getEnv().OTEL_EXPORTER_OTLP_CLIENT_KEY;\n\n  if (clientKey) {\n    try {\n      return fs.readFileSync(path.resolve(process.cwd(), clientKey));\n    } catch {\n      diag.warn('Failed to read client certificate private key file');\n      return undefined;\n    }\n  } else {\n    return undefined;\n  }\n}\n\nfunction retrieveCertChain(): Buffer | undefined {\n  const clientChain =\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE ||\n    getEnv().OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE;\n\n  if (clientChain) {\n    try {\n      return fs.readFileSync(path.resolve(process.cwd(), clientChain));\n    } catch {\n      diag.warn('Failed to read client certificate chain file');\n      return undefined;\n    }\n  } else {\n    return undefined;\n  }\n}\n\nfunction toGrpcCompression(\n  compression: CompressionAlgorithm\n): GrpcCompressionAlgorithm {\n  if (compression === CompressionAlgorithm.NONE)\n    return GrpcCompressionAlgorithm.NONE;\n  else if (compression === CompressionAlgorithm.GZIP)\n    return GrpcCompressionAlgorithm.GZIP;\n  return GrpcCompressionAlgorithm.NONE;\n}\n\n/**\n * These values are defined by grpc client\n */\nexport enum GrpcCompressionAlgorithm {\n  NONE = 0,\n  GZIP = 2,\n}\n\nexport function configureCompression(\n  compression: CompressionAlgorithm | undefined\n): GrpcCompressionAlgorithm {\n  if (compression) {\n    return toGrpcCompression(compression);\n  } else {\n    const definedCompression =\n      getEnv().OTEL_EXPORTER_OTLP_TRACES_COMPRESSION ||\n      getEnv().OTEL_EXPORTER_OTLP_COMPRESSION;\n\n    return definedCompression === 'gzip'\n      ? GrpcCompressionAlgorithm.GZIP\n      : GrpcCompressionAlgorithm.NONE;\n  }\n}\n"]}