{"version": 3, "file": "OTLPProtoExporterBrowserBase.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/OTLPProtoExporterBrowserBase.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAE1C,0EAK2C;AAE3C,kCAAgD;AAEhD;;GAEG;AACH,MAAsB,4BAGpB,SAAQ,4CAAgD;IACxD,YAAY,SAAiC,EAAE;QAC7C,KAAK,CAAC,MAAM,CAAC,CAAC;IAChB,CAAC;IAEQ,IAAI,CACX,OAAqB,EACrB,SAAqB,EACrB,OAA2C;QAE3C,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,UAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO;SACR;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,iBAAiB,GAAG,IAAA,4BAAqB,EAC7C,IAAI,CAAC,oBAAoB,EAAE,CAC5B,CAAC;QACF,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAEzD,IAAI,OAAO,EAAE;YACX,MAAM,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;YACxD,IAAI,IAAI,EAAE;gBACR,IAAA,gCAAW,EACT,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC,EACpD,IAAI,CAAC,GAAG,kCAEH,IAAI,CAAC,QAAQ,KAChB,cAAc,EAAE,wBAAwB,EACxC,MAAM,EAAE,wBAAwB,KAElC,IAAI,CAAC,aAAa,EAClB,SAAS,EACT,OAAO,CACR,CAAC;aACH;SACF;aAAM;YACL,OAAO,CAAC,IAAI,sCAAiB,CAAC,UAAU,CAAC,CAAC,CAAC;SAC5C;IACH,CAAC;CAGF;AA9CD,oEA8CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { ServiceClientType } from '../types';\nimport {\n  OTLPExporterBrowserBase as OTLPExporterBaseMain,\n  OTLPExporterError,\n  OTLPExporterConfigBase,\n  sendWithXhr,\n} from '@opentelemetry/otlp-exporter-base';\n\nimport { getExportRequestProto } from '../util';\n\n/**\n * Collector Exporter abstract base class\n */\nexport abstract class OTLPProtoExporterBrowserBase<\n  ExportItem,\n  ServiceRequest,\n> extends OTLPExporterBaseMain<ExportItem, ServiceRequest> {\n  constructor(config: OTLPExporterConfigBase = {}) {\n    super(config);\n  }\n\n  override send(\n    objects: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: OTLPExporterError) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      diag.debug('Shutdown already started. Cannot send objects');\n      return;\n    }\n\n    const serviceRequest = this.convert(objects);\n    const exportRequestType = getExportRequestProto(\n      this.getServiceClientType()\n    );\n    const message = exportRequestType.create(serviceRequest);\n\n    if (message) {\n      const body = exportRequestType.encode(message).finish();\n      if (body) {\n        sendWithXhr(\n          new Blob([body], { type: 'application/x-protobuf' }),\n          this.url,\n          {\n            ...this._headers,\n            'Content-Type': 'application/x-protobuf',\n            Accept: 'application/x-protobuf',\n          },\n          this.timeoutMillis,\n          onSuccess,\n          onError\n        );\n      }\n    } else {\n      onError(new OTLPExporterError('No proto'));\n    }\n  }\n\n  abstract getServiceClientType(): ServiceClientType;\n}\n"]}