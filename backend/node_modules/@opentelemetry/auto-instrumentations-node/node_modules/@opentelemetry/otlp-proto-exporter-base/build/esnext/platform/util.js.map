{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/platform/util.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,KAAK,IAAI,MAAM,mBAAmB,CAAC;AAC1C,OAAO,EAAE,iBAAiB,EAAE,MAAM,SAAS,CAAC;AAS5C,MAAM,UAAU,qBAAqB,CACnC,UAA6B;IAE7B,IAAI,UAAU,KAAK,iBAAiB,CAAC,KAAK,EAAE;QAC1C,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;aAC/C,yBAAyE,CAAC;KAC9E;SAAM,IAAI,UAAU,KAAK,iBAAiB,CAAC,IAAI,EAAE;QAChD,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;aAC9C,wBAAwE,CAAC;KAC7E;SAAM;QACL,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;aACjD,2BAA2E,CAAC;KAChF;AACH,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as root from '../generated/root';\nimport { ServiceClientType } from './types';\nimport type * as protobuf from 'protobufjs';\n\nexport interface ExportRequestType<T, R = T & { toJSON: () => unknown }> {\n  create(properties?: T): R;\n  encode(message: T, writer?: protobuf.Writer): protobuf.Writer;\n  decode(reader: protobuf.Reader | Uint8Array, length?: number): R;\n}\n\nexport function getExportRequestProto<ServiceRequest>(\n  clientType: ServiceClientType\n): ExportRequestType<ServiceRequest> {\n  if (clientType === ServiceClientType.SPANS) {\n    return root.opentelemetry.proto.collector.trace.v1\n      .ExportTraceServiceRequest as unknown as ExportRequestType<ServiceRequest>;\n  } else if (clientType === ServiceClientType.LOGS) {\n    return root.opentelemetry.proto.collector.logs.v1\n      .ExportLogsServiceRequest as unknown as ExportRequestType<ServiceRequest>;\n  } else {\n    return root.opentelemetry.proto.collector.metrics.v1\n      .ExportMetricsServiceRequest as unknown as ExportRequestType<ServiceRequest>;\n  }\n}\n"]}