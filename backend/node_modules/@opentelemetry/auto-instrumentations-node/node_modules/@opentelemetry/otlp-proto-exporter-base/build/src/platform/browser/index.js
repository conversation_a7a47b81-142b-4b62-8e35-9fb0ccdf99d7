"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceClientType = exports.OTLPProtoExporterBrowserBase = void 0;
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var OTLPProtoExporterBrowserBase_1 = require("./OTLPProtoExporterBrowserBase");
Object.defineProperty(exports, "OTLPProtoExporterBrowserBase", { enumerable: true, get: function () { return OTLPProtoExporterBrowserBase_1.OTLPProtoExporterBrowserBase; } });
var types_1 = require("../types");
Object.defineProperty(exports, "ServiceClientType", { enumerable: true, get: function () { return types_1.ServiceClientType; } });
//# sourceMappingURL=index.js.map