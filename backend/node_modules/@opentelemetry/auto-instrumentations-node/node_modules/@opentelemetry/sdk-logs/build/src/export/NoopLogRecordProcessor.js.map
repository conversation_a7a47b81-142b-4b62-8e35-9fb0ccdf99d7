{"version": 3, "file": "NoopLogRecordProcessor.js", "sourceRoot": "", "sources": ["../../../src/export/NoopLogRecordProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,MAAa,sBAAsB;IACjC,UAAU;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,UAA6B,EAAE,QAAiB,IAAS,CAAC;IAEjE,QAAQ;QACN,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;CACF;AAVD,wDAUC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\nimport { LogRecordProcessor } from '../LogRecordProcessor';\nimport { ReadableLogRecord } from './ReadableLogRecord';\n\nexport class NoopLogRecordProcessor implements LogRecordProcessor {\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  onEmit(_logRecord: ReadableLogRecord, _context: Context): void {}\n\n  shutdown(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n"]}