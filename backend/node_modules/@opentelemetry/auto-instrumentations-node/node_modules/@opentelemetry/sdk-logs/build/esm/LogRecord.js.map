{"version": 3, "file": "LogRecord.js", "sourceRoot": "", "sources": ["../../src/LogRecord.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAkB,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1D,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,iBAAiB,EACjB,gBAAgB,GAEjB,MAAM,qBAAqB,CAAC;AAQ7B;IAiDE,mBACE,YAAuC,EACvC,oBAA0C,EAC1C,SAA4B;QA9CrB,eAAU,GAA0B,EAAE,CAAC;QAIxC,yBAAoB,GAAW,CAAC,CAAC;QAEjC,gBAAW,GAAY,KAAK,CAAC;QA2CjC,IAAA,SAAS,GAOP,SAAS,UAPF,EACT,iBAAiB,GAMf,SAAS,kBANM,EACjB,cAAc,GAKZ,SAAS,eALG,EACd,YAAY,GAIV,SAAS,aAJC,EACZ,IAAI,GAGF,SAAS,KAHP,EACJ,KAEE,SAAS,WAFI,EAAf,UAAU,mBAAG,EAAE,KAAA,EACf,OAAO,GACL,SAAS,QADJ,CACK;QAEd,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,iBAAiB,CAAC,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC,iBAAiB,aAAjB,iBAAiB,cAAjB,iBAAiB,GAAI,GAAG,CAAC,CAAC;QAElE,IAAI,OAAO,EAAE;YACX,IAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,WAAW,IAAI,GAAG,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE;gBACtD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;aAChC;SACF;QACD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;QACtC,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC,eAAe,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;IAlED,sBAAI,mCAAY;aAMhB;YACE,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;aARD,UAAiB,YAAgC;YAC/C,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBAC/B,OAAO;aACR;YACD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QACpC,CAAC;;;OAAA;IAKD,sBAAI,qCAAc;aAMlB;YACE,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;aARD,UAAmB,cAAkD;YACnE,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBAC/B,OAAO;aACR;YACD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACxC,CAAC;;;OAAA;IAKD,sBAAI,2BAAI;aAMR;YACE,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;aARD,UAAS,IAAwB;YAC/B,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBAC/B,OAAO;aACR;YACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QACpB,CAAC;;;OAAA;IAKD,sBAAI,6CAAsB;aAA1B;YACE,OAAO,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;QACzE,CAAC;;;OAAA;IAoCM,gCAAY,GAAnB,UAAoB,GAAW,EAAE,KAAsC;QACrE,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;YAC/B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA0B,GAAK,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC;SACb;QACD,IACE,CAAC,gBAAgB,CAAC,KAAK,CAAC;YACxB,CAAC,CACC,OAAO,KAAK,KAAK,QAAQ;gBACzB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;gBACrB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAC9B,EACD;YACA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,0CAAwC,GAAK,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAC;QAC/B,IACE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YACjC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;YAC3C,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAC3D;YACA,OAAO,IAAI,CAAC;SACb;QACD,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SACpD;aAAM;YACL,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SAC9B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,iCAAa,GAApB,UAAqB,UAAyB;;;YAC5C,KAAqB,IAAA,KAAA,SAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA,gBAAA,4BAAE;gBAAtC,IAAA,KAAA,mBAAM,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA;gBACd,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACzB;;;;;;;;;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,2BAAO,GAAd,UAAe,IAAY;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,qCAAiB,GAAxB,UAAyB,cAAsC;QAC7D,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,mCAAe,GAAtB,UAAuB,YAAoB;QACzC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,iCAAa,GAAb;QACE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAEO,mCAAe,GAAvB,UAAwB,KAAqB;QAA7C,iBAuBC;QAtBC,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC;QAC9D,cAAc;QACd,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,kDAAkD;YAClD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,iDAA+C,KAAO,CAAC,CAAC;YACtE,OAAO,KAAK,CAAC;SACd;QAED,SAAS;QACT,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAChD;QAED,mBAAmB;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,OAAQ,KAAY,CAAC,GAAG,CAAC,UAAA,GAAG;gBAC1B,OAAA,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;YAArE,CAAqE,CACtE,CAAC;SACH;QAED,mDAAmD;QACnD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,wCAAoB,GAA5B,UAA6B,KAAa,EAAE,KAAa;QACvD,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,EAAE;YACzB,OAAO,KAAK,CAAC;SACd;QACD,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;IAEO,wCAAoB,GAA5B;QACE,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;SAClE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IACH,gBAAC;AAAD,CAAC,AA9LD,IA8LC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AttributeValue, diag } from '@opentelemetry/api';\nimport type * as logsAPI from '@opentelemetry/api-logs';\nimport * as api from '@opentelemetry/api';\nimport {\n  timeInputToHrTime,\n  isAttributeValue,\n  InstrumentationScope,\n} from '@opentelemetry/core';\nimport type { IResource } from '@opentelemetry/resources';\n\nimport type { ReadableLogRecord } from './export/ReadableLogRecord';\nimport type { LogRecordLimits } from './types';\nimport { LogAttributes } from '@opentelemetry/api-logs';\nimport { LoggerProviderSharedState } from './internal/LoggerProviderSharedState';\n\nexport class LogRecord implements ReadableLogRecord {\n  readonly hrTime: api.HrTime;\n  readonly hrTimeObserved: api.HrTime;\n  readonly spanContext?: api.SpanContext;\n  readonly resource: IResource;\n  readonly instrumentationScope: InstrumentationScope;\n  readonly attributes: logsAPI.LogAttributes = {};\n  private _severityText?: string;\n  private _severityNumber?: logsAPI.SeverityNumber;\n  private _body?: string;\n  private totalAttributesCount: number = 0;\n\n  private _isReadonly: boolean = false;\n  private readonly _logRecordLimits: Required<LogRecordLimits>;\n\n  set severityText(severityText: string | undefined) {\n    if (this._isLogRecordReadonly()) {\n      return;\n    }\n    this._severityText = severityText;\n  }\n  get severityText(): string | undefined {\n    return this._severityText;\n  }\n\n  set severityNumber(severityNumber: logsAPI.SeverityNumber | undefined) {\n    if (this._isLogRecordReadonly()) {\n      return;\n    }\n    this._severityNumber = severityNumber;\n  }\n  get severityNumber(): logsAPI.SeverityNumber | undefined {\n    return this._severityNumber;\n  }\n\n  set body(body: string | undefined) {\n    if (this._isLogRecordReadonly()) {\n      return;\n    }\n    this._body = body;\n  }\n  get body(): string | undefined {\n    return this._body;\n  }\n\n  get droppedAttributesCount(): number {\n    return this.totalAttributesCount - Object.keys(this.attributes).length;\n  }\n\n  constructor(\n    _sharedState: LoggerProviderSharedState,\n    instrumentationScope: InstrumentationScope,\n    logRecord: logsAPI.LogRecord\n  ) {\n    const {\n      timestamp,\n      observedTimestamp,\n      severityNumber,\n      severityText,\n      body,\n      attributes = {},\n      context,\n    } = logRecord;\n\n    const now = Date.now();\n    this.hrTime = timeInputToHrTime(timestamp ?? now);\n    this.hrTimeObserved = timeInputToHrTime(observedTimestamp ?? now);\n\n    if (context) {\n      const spanContext = api.trace.getSpanContext(context);\n      if (spanContext && api.isSpanContextValid(spanContext)) {\n        this.spanContext = spanContext;\n      }\n    }\n    this.severityNumber = severityNumber;\n    this.severityText = severityText;\n    this.body = body;\n    this.resource = _sharedState.resource;\n    this.instrumentationScope = instrumentationScope;\n    this._logRecordLimits = _sharedState.logRecordLimits;\n    this.setAttributes(attributes);\n  }\n\n  public setAttribute(key: string, value?: LogAttributes | AttributeValue) {\n    if (this._isLogRecordReadonly()) {\n      return this;\n    }\n    if (value === null) {\n      return this;\n    }\n    if (key.length === 0) {\n      api.diag.warn(`Invalid attribute key: ${key}`);\n      return this;\n    }\n    if (\n      !isAttributeValue(value) &&\n      !(\n        typeof value === 'object' &&\n        !Array.isArray(value) &&\n        Object.keys(value).length > 0\n      )\n    ) {\n      api.diag.warn(`Invalid attribute value set for key: ${key}`);\n      return this;\n    }\n    this.totalAttributesCount += 1;\n    if (\n      Object.keys(this.attributes).length >=\n        this._logRecordLimits.attributeCountLimit &&\n      !Object.prototype.hasOwnProperty.call(this.attributes, key)\n    ) {\n      return this;\n    }\n    if (isAttributeValue(value)) {\n      this.attributes[key] = this._truncateToSize(value);\n    } else {\n      this.attributes[key] = value;\n    }\n    return this;\n  }\n\n  public setAttributes(attributes: LogAttributes) {\n    for (const [k, v] of Object.entries(attributes)) {\n      this.setAttribute(k, v);\n    }\n    return this;\n  }\n\n  public setBody(body: string) {\n    this.body = body;\n    return this;\n  }\n\n  public setSeverityNumber(severityNumber: logsAPI.SeverityNumber) {\n    this.severityNumber = severityNumber;\n    return this;\n  }\n\n  public setSeverityText(severityText: string) {\n    this.severityText = severityText;\n    return this;\n  }\n\n  /**\n   * @internal\n   * A LogRecordProcessor may freely modify logRecord for the duration of the OnEmit call.\n   * If logRecord is needed after OnEmit returns (i.e. for asynchronous processing) only reads are permitted.\n   */\n  _makeReadonly() {\n    this._isReadonly = true;\n  }\n\n  private _truncateToSize(value: AttributeValue): AttributeValue {\n    const limit = this._logRecordLimits.attributeValueLengthLimit;\n    // Check limit\n    if (limit <= 0) {\n      // Negative values are invalid, so do not truncate\n      api.diag.warn(`Attribute value limit must be positive, got ${limit}`);\n      return value;\n    }\n\n    // String\n    if (typeof value === 'string') {\n      return this._truncateToLimitUtil(value, limit);\n    }\n\n    // Array of strings\n    if (Array.isArray(value)) {\n      return (value as []).map(val =>\n        typeof val === 'string' ? this._truncateToLimitUtil(val, limit) : val\n      );\n    }\n\n    // Other types, no need to apply value length limit\n    return value;\n  }\n\n  private _truncateToLimitUtil(value: string, limit: number): string {\n    if (value.length <= limit) {\n      return value;\n    }\n    return value.substring(0, limit);\n  }\n\n  private _isLogRecordReadonly(): boolean {\n    if (this._isReadonly) {\n      diag.warn('Can not execute the operation on emitted log record');\n    }\n    return this._isReadonly;\n  }\n}\n"]}