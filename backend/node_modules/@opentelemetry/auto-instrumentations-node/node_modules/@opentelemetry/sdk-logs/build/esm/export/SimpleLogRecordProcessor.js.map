{"version": 3, "file": "SimpleLogRecordProcessor.js", "sourceRoot": "", "sources": ["../../../src/export/SimpleLogRecordProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EACL,cAAc,EACd,gBAAgB,EAChB,kBAAkB,GACnB,MAAM,qBAAqB,CAAC;AAM7B;IAGE,kCAA6B,SAA4B;QAA5B,cAAS,GAAT,SAAS,CAAmB;QACvD,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;IAEM,yCAAM,GAAb,UAAc,SAAoB;QAChC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QAED,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,EAAE,UAAC,GAAiB;;YACnD,IAAI,GAAG,CAAC,IAAI,KAAK,gBAAgB,CAAC,OAAO,EAAE;gBACzC,kBAAkB,CAChB,MAAA,GAAG,CAAC,KAAK,mCACP,IAAI,KAAK,CACP,gEAA8D,GAAG,MAAG,CACrE,CACJ,CAAC;gBACF,OAAO;aACR;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,6CAAU,GAAjB;QACE,mEAAmE;QACnE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAEM,2CAAQ,GAAf;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,4CAAS,GAAjB;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;IACH,+BAAC;AAAD,CAAC,AArCD,IAqCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ExportResult } from '@opentelemetry/core';\nimport {\n  BindOnceFuture,\n  ExportResultCode,\n  globalErrorHandler,\n} from '@opentelemetry/core';\n\nimport type { LogRecordExporter } from './LogRecordExporter';\nimport type { LogRecordProcessor } from '../LogRecordProcessor';\nimport type { LogRecord } from './../LogRecord';\n\nexport class SimpleLogRecordProcessor implements LogRecordProcessor {\n  private _shutdownOnce: BindOnceFuture<void>;\n\n  constructor(private readonly _exporter: LogRecordExporter) {\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n  }\n\n  public onEmit(logRecord: LogRecord): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n\n    this._exporter.export([logRecord], (res: ExportResult) => {\n      if (res.code !== ExportResultCode.SUCCESS) {\n        globalErrorHandler(\n          res.error ??\n            new Error(\n              `SimpleLogRecordProcessor: log record export failed (status ${res})`\n            )\n        );\n        return;\n      }\n    });\n  }\n\n  public forceFlush(): Promise<void> {\n    // do nothing as all log records are being exported without waiting\n    return Promise.resolve();\n  }\n\n  public shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private _shutdown(): Promise<void> {\n    return this._exporter.shutdown();\n  }\n}\n"]}