"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchLogRecordProcessor = exports.InMemoryLogRecordExporter = exports.SimpleLogRecordProcessor = exports.ConsoleLogRecordExporter = exports.NoopLogRecordProcessor = exports.LogRecord = exports.LoggerProvider = void 0;
var LoggerProvider_1 = require("./LoggerProvider");
Object.defineProperty(exports, "LoggerProvider", { enumerable: true, get: function () { return LoggerProvider_1.LoggerProvider; } });
var LogRecord_1 = require("./LogRecord");
Object.defineProperty(exports, "LogRecord", { enumerable: true, get: function () { return LogRecord_1.LogRecord; } });
var NoopLogRecordProcessor_1 = require("./export/NoopLogRecordProcessor");
Object.defineProperty(exports, "NoopLogRecordProcessor", { enumerable: true, get: function () { return NoopLogRecordProcessor_1.NoopLogRecordProcessor; } });
var ConsoleLogRecordExporter_1 = require("./export/ConsoleLogRecordExporter");
Object.defineProperty(exports, "ConsoleLogRecordExporter", { enumerable: true, get: function () { return ConsoleLogRecordExporter_1.ConsoleLogRecordExporter; } });
var SimpleLogRecordProcessor_1 = require("./export/SimpleLogRecordProcessor");
Object.defineProperty(exports, "SimpleLogRecordProcessor", { enumerable: true, get: function () { return SimpleLogRecordProcessor_1.SimpleLogRecordProcessor; } });
var InMemoryLogRecordExporter_1 = require("./export/InMemoryLogRecordExporter");
Object.defineProperty(exports, "InMemoryLogRecordExporter", { enumerable: true, get: function () { return InMemoryLogRecordExporter_1.InMemoryLogRecordExporter; } });
var platform_1 = require("./platform");
Object.defineProperty(exports, "BatchLogRecordProcessor", { enumerable: true, get: function () { return platform_1.BatchLogRecordProcessor; } });
//# sourceMappingURL=index.js.map