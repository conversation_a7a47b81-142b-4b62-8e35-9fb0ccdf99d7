{"version": 3, "file": "LogRecordProcessor.js", "sourceRoot": "", "sources": ["../../src/LogRecordProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\n\nimport { LogRecord } from './LogRecord';\n\nexport interface LogRecordProcessor {\n  /**\n   * Forces to export all finished log records\n   */\n  forceFlush(): Promise<void>;\n\n  /**\n   * Called when a {@link LogRecord} is emit\n   * @param logRecord the ReadWriteLogRecord that just emitted.\n   * @param context the current Context, or an empty Context if the Logger was obtained with include_trace_context=false\n   */\n  onEmit(logRecord: LogRecord, context?: Context): void;\n\n  /**\n   * Shuts down the processor. Called when SDK is shut down. This is an\n   * opportunity for processor to do any cleanup required.\n   */\n  shutdown(): Promise<void>;\n}\n"]}