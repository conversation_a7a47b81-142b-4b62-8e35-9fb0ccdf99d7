{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/config.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,6BAA6B,EAC7B,oCAAoC,EACpC,MAAM,EACN,qBAAqB,GACtB,MAAM,qBAAqB,CAAC;AAG7B,MAAM,UAAU,iBAAiB;IAC/B,OAAO;QACL,uBAAuB,EAAE,KAAK;QAC9B,eAAe,EAAE;YACf,yBAAyB,EACvB,MAAM,EAAE,CAAC,2CAA2C;YACtD,mBAAmB,EAAE,MAAM,EAAE,CAAC,oCAAoC;SACnE;QACD,mBAAmB,EAAE,IAAI;KAC1B,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAC/B,eAAgC;;IAEhC,IAAM,eAAe,GAAG,qBAAqB,EAAE,CAAC;IAEhD,OAAO;QACL;;WAEG;QACH,mBAAmB,EACjB,MAAA,MAAA,MAAA,eAAe,CAAC,mBAAmB,mCACnC,eAAe,CAAC,oCAAoC,mCACpD,eAAe,CAAC,0BAA0B,mCAC1C,6BAA6B;QAC/B;;WAEG;QACH,yBAAyB,EACvB,MAAA,MAAA,MAAA,eAAe,CAAC,yBAAyB,mCACzC,eAAe,CAAC,2CAA2C,mCAC3D,eAAe,CAAC,iCAAiC,mCACjD,oCAAoC;KACvC,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  getEnv,\n  getEnvWithoutDefaults,\n} from '@opentelemetry/core';\nimport { LogRecordLimits } from './types';\n\nexport function loadDefaultConfig() {\n  return {\n    forceFlushTimeoutMillis: 30000,\n    logRecordLimits: {\n      attributeValueLengthLimit:\n        getEnv().OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n      attributeCountLimit: getEnv().OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT,\n    },\n    includeTraceContext: true,\n  };\n}\n\n/**\n * When general limits are provided and model specific limits are not,\n * configures the model specific limits by using the values from the general ones.\n * @param logRecordLimits User provided limits configuration\n */\nexport function reconfigureLimits(\n  logRecordLimits: LogRecordLimits\n): Required<LogRecordLimits> {\n  const parsedEnvConfig = getEnvWithoutDefaults();\n\n  return {\n    /**\n     * Reassign log record attribute count limit to use first non null value defined by user or use default value\n     */\n    attributeCountLimit:\n      logRecordLimits.attributeCountLimit ??\n      parsedEnvConfig.OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT ??\n      parsedEnvConfig.OTEL_ATTRIBUTE_COUNT_LIMIT ??\n      DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n    /**\n     * Reassign log record attribute value length limit to use first non null value defined by user or use default value\n     */\n    attributeValueLengthLimit:\n      logRecordLimits.attributeValueLengthLimit ??\n      parsedEnvConfig.OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT ??\n      parsedEnvConfig.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT ??\n      DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  };\n}\n"]}