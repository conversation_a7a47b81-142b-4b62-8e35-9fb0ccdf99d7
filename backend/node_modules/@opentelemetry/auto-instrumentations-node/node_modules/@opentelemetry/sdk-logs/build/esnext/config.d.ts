import { LogRecordLimits } from './types';
export declare function loadDefaultConfig(): {
    forceFlushTimeoutMillis: number;
    logRecordLimits: {
        attributeValueLengthLimit: number;
        attributeCountLimit: number;
    };
    includeTraceContext: boolean;
};
/**
 * When general limits are provided and model specific limits are not,
 * configures the model specific limits by using the values from the general ones.
 * @param logRecordLimits User provided limits configuration
 */
export declare function reconfigureLimits(logRecordLimits: LogRecordLimits): Required<LogRecordLimits>;
//# sourceMappingURL=config.d.ts.map