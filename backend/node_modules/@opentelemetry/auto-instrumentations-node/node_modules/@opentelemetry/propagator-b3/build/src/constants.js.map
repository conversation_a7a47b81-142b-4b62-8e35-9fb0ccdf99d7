{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/constants.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,2BAA2B;AACd,QAAA,iBAAiB,GAAG,IAAI,CAAC;AAEtC,0BAA0B;AACb,QAAA,aAAa,GAAG,cAAc,CAAC;AAC/B,QAAA,YAAY,GAAG,aAAa,CAAC;AAC7B,QAAA,YAAY,GAAG,cAAc,CAAC;AAC9B,QAAA,mBAAmB,GAAG,mBAAmB,CAAC;AAC1C,QAAA,UAAU,GAAG,YAAY,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** B3 single-header key */\nexport const B3_CONTEXT_HEADER = 'b3';\n\n/* b3 multi-header keys */\nexport const X_B3_TRACE_ID = 'x-b3-traceid';\nexport const X_B3_SPAN_ID = 'x-b3-spanid';\nexport const X_B3_SAMPLED = 'x-b3-sampled';\nexport const X_B3_PARENT_SPAN_ID = 'x-b3-parentspanid';\nexport const X_B3_FLAGS = 'x-b3-flags';\n"]}