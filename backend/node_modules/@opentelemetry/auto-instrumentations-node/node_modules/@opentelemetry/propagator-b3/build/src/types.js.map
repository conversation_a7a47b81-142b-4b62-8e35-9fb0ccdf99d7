{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,yCAAyC;AACzC,IAAY,gBAGX;AAHD,WAAY,gBAAgB;IAC1B,yEAAa,CAAA;IACb,uEAAY,CAAA;AACd,CAAC,EAHW,gBAAgB,GAAhB,wBAAgB,KAAhB,wBAAgB,QAG3B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Enumeration of B3 inject encodings */\nexport enum B3InjectEncoding {\n  SINGLE_HEADER,\n  MULTI_HEADER,\n}\n\n/** Configuration for the B3Propagator */\nexport interface B3PropagatorConfig {\n  injectEncoding?: B3InjectEncoding;\n}\n"]}