{"version": 3, "file": "OTLPTraceExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/OTLPTraceExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8CAA2D;AAC3D,0EAI2C;AAC3C,sFAGiD;AACjD,sEAGyC;AAEzC,MAAM,+BAA+B,GAAG,WAAW,CAAC;AACpD,MAAM,qBAAqB,GAAG,yBAAyB,+BAA+B,EAAE,CAAC;AAEzF;;GAEG;AACH,MAAa,iBACX,SAAQ,uDAAsE;IAG9E,YAAY,SAAiC,EAAE;QAC7C,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAC3B,IAAI,CAAC,QAAQ,EACb,mBAAY,CAAC,uBAAuB,CAClC,IAAA,aAAM,GAAE,CAAC,iCAAiC,CAC3C,CACF,CAAC;IACJ,CAAC;IACD,OAAO,CAAC,KAAqB;QAC3B,OAAO,IAAA,kDAA+B,EAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAAC,MAA8B;QAC1C,OAAO,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ;YACnC,CAAC,CAAC,MAAM,CAAC,GAAG;YACZ,CAAC,CAAC,IAAA,aAAM,GAAE,CAAC,kCAAkC,CAAC,MAAM,GAAG,CAAC;gBACxD,CAAC,CAAC,IAAA,gDAA2B,EAAC,IAAA,aAAM,GAAE,CAAC,kCAAkC,CAAC;gBAC1E,CAAC,CAAC,IAAA,aAAM,GAAE,CAAC,2BAA2B,CAAC,MAAM,GAAG,CAAC;oBACjD,CAAC,CAAC,IAAA,4CAAuB,EACrB,IAAA,aAAM,GAAE,CAAC,2BAA2B,EACpC,+BAA+B,CAChC;oBACH,CAAC,CAAC,qBAAqB,CAAC;IAC5B,CAAC;IAED,oBAAoB;QAClB,OAAO,4CAAiB,CAAC,KAAK,CAAC;IACjC,CAAC;CACF;AAjCD,8CAiCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport {\n  OTLPExporterConfigBase,\n  appendResourcePathToUrl,\n  appendRootPathToUrlIfNeeded,\n} from '@opentelemetry/otlp-exporter-base';\nimport {\n  OTLPProtoExporterBrowserBase,\n  ServiceClientType,\n} from '@opentelemetry/otlp-proto-exporter-base';\nimport {\n  createExportTraceServiceRequest,\n  IExportTraceServiceRequest,\n} from '@opentelemetry/otlp-transformer';\n\nconst DEFAULT_COLLECTOR_RESOURCE_PATH = 'v1/traces';\nconst DEFAULT_COLLECTOR_URL = `http://localhost:4318/${DEFAULT_COLLECTOR_RESOURCE_PATH}`;\n\n/**\n * Collector Trace Exporter for Web\n */\nexport class OTLPTraceExporter\n  extends OTLPProtoExporterBrowserBase<ReadableSpan, IExportTraceServiceRequest>\n  implements SpanExporter\n{\n  constructor(config: OTLPExporterConfigBase = {}) {\n    super(config);\n    this._headers = Object.assign(\n      this._headers,\n      baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_TRACES_HEADERS\n      )\n    );\n  }\n  convert(spans: ReadableSpan[]): IExportTraceServiceRequest {\n    return createExportTraceServiceRequest(spans);\n  }\n\n  getDefaultUrl(config: OTLPExporterConfigBase): string {\n    return typeof config.url === 'string'\n      ? config.url\n      : getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT.length > 0\n      ? appendRootPathToUrlIfNeeded(getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT)\n      : getEnv().OTEL_EXPORTER_OTLP_ENDPOINT.length > 0\n      ? appendResourcePathToUrl(\n          getEnv().OTEL_EXPORTER_OTLP_ENDPOINT,\n          DEFAULT_COLLECTOR_RESOURCE_PATH\n        )\n      : DEFAULT_COLLECTOR_URL;\n  }\n\n  getServiceClientType() {\n    return ServiceClientType.SPANS;\n  }\n}\n"]}