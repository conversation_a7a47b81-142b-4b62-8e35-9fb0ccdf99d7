{"version": 3, "file": "zipkin.js", "sourceRoot": "", "sources": ["../../src/zipkin.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAC1C,8CAA6E;AAE7E,4CAA+C;AAE/C,2CAIqB;AACrB,8EAAiF;AACjF,mCAA4C;AAE5C;;GAEG;AACH,MAAa,cAAc;IAWzB,YAAY,SAAqC,EAAE;QAVlC,yBAAoB,GAAG,uBAAuB,CAAC;QAQxD,qBAAgB,GAAuB,EAAE,CAAC;QAGhD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,IAAI,IAAA,aAAM,GAAE,CAAC,6BAA6B,CAAC;QACpE,IAAI,CAAC,KAAK,GAAG,IAAA,mBAAW,EAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,kBAAkB;YACrB,MAAM,CAAC,iBAAiB,IAAI,oCAAwB,CAAC;QACvD,IAAI,CAAC,yBAAyB;YAC5B,MAAM,CAAC,wBAAwB,IAAI,qCAAyB,CAAC;QAC/D,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,OAAO,MAAM,CAAC,uBAAuB,KAAK,UAAU,EAAE;YACxD,IAAI,CAAC,WAAW,GAAG,IAAA,yBAAiB,EAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;SACtE;aAAM;YACL,OAAO;YACP,IAAI,CAAC,WAAW,GAAG,cAAa,CAAC,CAAC;SACnC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CACJ,KAAqB,EACrB,cAA8C;QAE9C,MAAM,WAAW,GAAG,MAAM,CACxB,IAAI,CAAC,YAAY;YACf,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,iDAA0B,CAAC,YAAY,CAAC;YACrE,IAAI,CAAC,oBAAoB,CAC5B,CAAC;QAEF,UAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,UAAU,CAAC,GAAG,EAAE,CACd,cAAc,CAAC;gBACb,IAAI,EAAE,uBAAgB,CAAC,MAAM;gBAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,4BAA4B,CAAC;aAC/C,CAAC,CACH,CAAC;YACF,OAAO;SACR;QACD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,OAAO,CAAC,EAAE;YAC1C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,MAAM,CAAC,EAAE;gBAC3C,OAAO,EAAE,CAAC;gBACV,cAAc,CAAC,MAAM,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,UAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC3C,OAAO,EAAE,CAAC;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,WAAW;QACjB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,KAAK,GAAG,IAAA,mBAAW,EAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;SAC5D;IACH,CAAC;IAED;;OAEG;IACK,UAAU,CAChB,KAAqB,EACrB,WAAmB,EACnB,IAAqC;QAErC,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACnC,IAAA,wBAAY,EACV,IAAI,EACJ,MAAM,CACJ,IAAI,CAAC,UAAU,CAAC,iDAA0B,CAAC,YAAY,CAAC;YACtD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,iDAA0B,CAAC,YAAY,CAAC;YACjE,WAAW,CACd,EACD,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,yBAAyB,CAC/B,CACF,CAAC;QACF,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,MAAoB,EAAE,EAAE;YACtD,IAAI,IAAI,EAAE;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;aACrB;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA7HD,wCA6HC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { ExportResult, ExportResultCode, getEnv } from '@opentelemetry/core';\nimport { SpanExporter, ReadableSpan } from '@opentelemetry/sdk-trace-base';\nimport { prepareSend } from './platform/index';\nimport * as zipkinTypes from './types';\nimport {\n  toZipkinSpan,\n  defaultStatusCodeTagName,\n  defaultStatusErrorTagName,\n} from './transform';\nimport { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';\nimport { prepareGetHeaders } from './utils';\n\n/**\n * Zipkin Exporter\n */\nexport class ZipkinExporter implements SpanExporter {\n  private readonly DEFAULT_SERVICE_NAME = 'OpenTelemetry Service';\n  private readonly _statusCodeTagName: string;\n  private readonly _statusDescriptionTagName: string;\n  private _urlStr: string;\n  private _send: zipkinTypes.SendFunction;\n  private _getHeaders: zipkinTypes.GetHeaders | undefined;\n  private _serviceName?: string;\n  private _isShutdown: boolean;\n  private _sendingPromises: Promise<unknown>[] = [];\n\n  constructor(config: zipkinTypes.ExporterConfig = {}) {\n    this._urlStr = config.url || getEnv().OTEL_EXPORTER_ZIPKIN_ENDPOINT;\n    this._send = prepareSend(this._urlStr, config.headers);\n    this._serviceName = config.serviceName;\n    this._statusCodeTagName =\n      config.statusCodeTagName || defaultStatusCodeTagName;\n    this._statusDescriptionTagName =\n      config.statusDescriptionTagName || defaultStatusErrorTagName;\n    this._isShutdown = false;\n    if (typeof config.getExportRequestHeaders === 'function') {\n      this._getHeaders = prepareGetHeaders(config.getExportRequestHeaders);\n    } else {\n      // noop\n      this._beforeSend = function () {};\n    }\n  }\n\n  /**\n   * Export spans.\n   */\n  export(\n    spans: ReadableSpan[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    const serviceName = String(\n      this._serviceName ||\n        spans[0].resource.attributes[SemanticResourceAttributes.SERVICE_NAME] ||\n        this.DEFAULT_SERVICE_NAME\n    );\n\n    diag.debug('Zipkin exporter export');\n    if (this._isShutdown) {\n      setTimeout(() =>\n        resultCallback({\n          code: ExportResultCode.FAILED,\n          error: new Error('Exporter has been shutdown'),\n        })\n      );\n      return;\n    }\n    const promise = new Promise<void>(resolve => {\n      this._sendSpans(spans, serviceName, result => {\n        resolve();\n        resultCallback(result);\n      });\n    });\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n\n  /**\n   * Shutdown exporter. Noop operation in this exporter.\n   */\n  shutdown(): Promise<void> {\n    diag.debug('Zipkin exporter shutdown');\n    this._isShutdown = true;\n    return this.forceFlush();\n  }\n\n  /**\n   * Exports any pending spans in exporter\n   */\n  forceFlush(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      Promise.all(this._sendingPromises).then(() => {\n        resolve();\n      }, reject);\n    });\n  }\n\n  /**\n   * if user defines getExportRequestHeaders in config then this will be called\n   * everytime before send, otherwise it will be replaced with noop in\n   * constructor\n   * @default noop\n   */\n  private _beforeSend() {\n    if (this._getHeaders) {\n      this._send = prepareSend(this._urlStr, this._getHeaders());\n    }\n  }\n\n  /**\n   * Transform spans and sends to Zipkin service.\n   */\n  private _sendSpans(\n    spans: ReadableSpan[],\n    serviceName: string,\n    done?: (result: ExportResult) => void\n  ) {\n    const zipkinSpans = spans.map(span =>\n      toZipkinSpan(\n        span,\n        String(\n          span.attributes[SemanticResourceAttributes.SERVICE_NAME] ||\n            span.resource.attributes[SemanticResourceAttributes.SERVICE_NAME] ||\n            serviceName\n        ),\n        this._statusCodeTagName,\n        this._statusDescriptionTagName\n      )\n    );\n    this._beforeSend();\n    return this._send(zipkinSpans, (result: ExportResult) => {\n      if (done) {\n        return done(result);\n      }\n    });\n  }\n}\n"]}