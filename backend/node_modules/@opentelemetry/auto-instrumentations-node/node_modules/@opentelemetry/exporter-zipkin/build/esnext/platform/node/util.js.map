{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/util.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAgB,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACrE,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAG3B;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CACzB,MAAc,EACd,OAAgC;IAEhC,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAElC,MAAM,OAAO,GAAwB,MAAM,CAAC,MAAM,CAChD;QACE,MAAM,EAAE,MAAM;QACd,OAAO,kBACL,cAAc,EAAE,kBAAkB,IAC/B,OAAO,CACX;KACF,EACD,OAAO,CACR,CAAC;IAEF;;OAEG;IACH,OAAO,SAAS,IAAI,CAClB,WAA+B,EAC/B,IAAoC;QAEpC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;SACjD;QAED,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAChE,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,GAAyB,EAAE,EAAE;YACzD,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;gBACrB,OAAO,IAAI,KAAK,CAAC;YACnB,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACjB,MAAM,UAAU,GAAG,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC;gBACvC,IAAI,CAAC,KAAK,CACR,gCAAgC,UAAU,WAAW,OAAO,EAAE,CAC/D,CAAC;gBAEF,mCAAmC;gBACnC,IAAI,UAAU,GAAG,GAAG,EAAE;oBACpB,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;oBAChD,wCAAwC;iBACzC;qBAAM;oBACL,OAAO,IAAI,CAAC;wBACV,IAAI,EAAE,gBAAgB,CAAC,MAAM;wBAC7B,KAAK,EAAE,IAAI,KAAK,CACd,2CAA2C,UAAU,EAAE,CACxD;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;YACtB,OAAO,IAAI,CAAC;gBACV,IAAI,EAAE,gBAAgB,CAAC,MAAM;gBAC7B,KAAK;aACN,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,kCAAkC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,2BAA2B,OAAO,EAAE,CAAC,CAAC;QACjD,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3B,GAAG,CAAC,GAAG,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { ExportResult, ExportResultCode } from '@opentelemetry/core';\nimport * as http from 'http';\nimport * as https from 'https';\nimport * as url from 'url';\nimport * as zipkinTypes from '../../types';\n\n/**\n * Prepares send function that will send spans to the remote Zipkin service.\n * @param urlStr - url to send spans\n * @param headers - headers\n * send\n */\nexport function prepareSend(\n  urlStr: string,\n  headers?: Record<string, string>\n): zipkinTypes.SendFn {\n  const urlOpts = url.parse(urlStr);\n\n  const reqOpts: http.RequestOptions = Object.assign(\n    {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n    },\n    urlOpts\n  );\n\n  /**\n   * Send spans to the remote Zipkin service.\n   */\n  return function send(\n    zipkinSpans: zipkinTypes.Span[],\n    done: (result: ExportResult) => void\n  ) {\n    if (zipkinSpans.length === 0) {\n      diag.debug('Zipkin send with empty spans');\n      return done({ code: ExportResultCode.SUCCESS });\n    }\n\n    const { request } = reqOpts.protocol === 'http:' ? http : https;\n    const req = request(reqOpts, (res: http.IncomingMessage) => {\n      let rawData = '';\n      res.on('data', chunk => {\n        rawData += chunk;\n      });\n      res.on('end', () => {\n        const statusCode = res.statusCode || 0;\n        diag.debug(\n          `Zipkin response status code: ${statusCode}, body: ${rawData}`\n        );\n\n        // Consider 2xx and 3xx as success.\n        if (statusCode < 400) {\n          return done({ code: ExportResultCode.SUCCESS });\n          // Consider 4xx as failed non-retriable.\n        } else {\n          return done({\n            code: ExportResultCode.FAILED,\n            error: new Error(\n              `Got unexpected status code from zipkin: ${statusCode}`\n            ),\n          });\n        }\n      });\n    });\n\n    req.on('error', error => {\n      return done({\n        code: ExportResultCode.FAILED,\n        error,\n      });\n    });\n\n    // Issue request to remote service\n    const payload = JSON.stringify(zipkinSpans);\n    diag.debug(`Zipkin request payload: ${payload}`);\n    req.write(payload, 'utf8');\n    req.end();\n  };\n}\n"]}