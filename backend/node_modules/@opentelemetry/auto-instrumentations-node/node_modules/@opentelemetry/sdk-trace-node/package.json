{"name": "@opentelemetry/sdk-trace-node", "version": "1.19.0", "description": "OpenTelemetry Node SDK provides automatic telemetry (tracing, metrics, etc) for Node.js applications", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build", "clean": "tsc --build --clean", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "tdd": "npm run test -- --watch-extensions ts --watch", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../scripts/version-update.js", "watch": "tsc --build --watch", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../scripts/peer-api-check.js"}, "keywords": ["opentelemetry", "nodejs", "tracing", "profiling", "metrics", "stats"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": ">=1.0.0 <1.8.0", "@opentelemetry/resources": "1.19.0", "@opentelemetry/semantic-conventions": "1.19.0", "@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/semver": "7.5.6", "@types/sinon": "10.0.20", "codecov": "3.8.3", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "sinon": "15.1.2", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.8.0"}, "dependencies": {"@opentelemetry/context-async-hooks": "1.19.0", "@opentelemetry/core": "1.19.0", "@opentelemetry/propagator-b3": "1.19.0", "@opentelemetry/propagator-jaeger": "1.19.0", "@opentelemetry/sdk-trace-base": "1.19.0", "semver": "^7.5.2"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/packages/opentelemetry-sdk-trace-node", "sideEffects": false, "gitHead": "d3c311aec24137084dc820805a2597e120335672"}