/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { OTLPExporterBase } from '../../OTLPExporterBase';
import { parseHeaders } from '../../util';
import { sendWithBeacon, sendWithXhr } from './util';
import { diag } from '@opentelemetry/api';
import { getEnv, baggageUtils, _globalThis } from '@opentelemetry/core';
/**
 * Collector Metric Exporter abstract base class
 */
export class OTLPExporterBrowserBase extends OTLPExporterBase {
    /**
     * @param config
     */
    constructor(config = {}) {
        super(config);
        this._useXHR = false;
        this._useXHR =
            !!config.headers || typeof navigator.sendBeacon !== 'function';
        if (this._useXHR) {
            this._headers = Object.assign({}, parseHeaders(config.headers), baggageUtils.parseKeyPairsIntoRecord(getEnv().OTEL_EXPORTER_OTLP_HEADERS));
        }
        else {
            this._headers = {};
        }
    }
    onInit() {
        _globalThis.addEventListener('unload', this.shutdown);
    }
    onShutdown() {
        _globalThis.removeEventListener('unload', this.shutdown);
    }
    send(items, onSuccess, onError) {
        if (this._shutdownOnce.isCalled) {
            diag.debug('Shutdown already started. Cannot send objects');
            return;
        }
        const serviceRequest = this.convert(items);
        const body = JSON.stringify(serviceRequest);
        const promise = new Promise((resolve, reject) => {
            if (this._useXHR) {
                sendWithXhr(body, this.url, this._headers, this.timeoutMillis, resolve, reject);
            }
            else {
                sendWithBeacon(body, this.url, { type: 'application/json' }, resolve, reject);
            }
        }).then(onSuccess, onError);
        this._sendingPromises.push(promise);
        const popPromise = () => {
            const index = this._sendingPromises.indexOf(promise);
            this._sendingPromises.splice(index, 1);
        };
        promise.then(popPromise, popPromise);
    }
}
//# sourceMappingURL=OTLPExporterBrowserBase.js.map