{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/util.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAC3B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAGlC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,oBAAoB,EAAE,MAAM,SAAS,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EACL,2BAA2B,EAC3B,8BAA8B,EAC9B,iCAAiC,EACjC,0BAA0B,EAC1B,iBAAiB,EACjB,sBAAsB,GACvB,MAAM,YAAY,CAAC;AAEpB;;;;;;;GAOG;AACH,MAAM,UAAU,YAAY,CAC1B,SAA2D,EAC3D,IAAqB,EACrB,WAAmB,EACnB,SAAqB,EACrB,OAA2C;IAE3C,MAAM,eAAe,GAAG,SAAS,CAAC,aAAa,CAAC;IAChD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,IAAI,UAAyC,CAAC;IAC9C,IAAI,GAAuB,CAAC;IAC5B,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;QACpC,YAAY,CAAC,UAAU,CAAC,CAAC;QACzB,cAAc,GAAG,IAAI,CAAC;QAEtB,IAAI,GAAG,CAAC,SAAS,EAAE;YACjB,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,CAAC;SACd;aAAM;YACL,uCAAuC;YACvC,WAAW,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;SACjD;IACH,CAAC,EAAE,eAAe,CAAC,CAAC;IAEpB,MAAM,OAAO,GAA+C;QAC1D,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;QACpB,IAAI,EAAE,SAAS,CAAC,QAAQ;QACxB,MAAM,EAAE,MAAM;QACd,OAAO,kBACL,cAAc,EAAE,WAAW,IACxB,SAAS,CAAC,OAAO,CACrB;QACD,KAAK,EAAE,SAAS,CAAC,KAAK;KACvB,CAAC;IAEF,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;IAE9E,MAAM,aAAa,GAAG,CACpB,OAAO,GAAG,2BAA2B,EACrC,QAAQ,GAAG,8BAA8B,EACzC,EAAE;QACF,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,GAAyB,EAAE,EAAE;YACnD,IAAI,YAAY,GAAG,EAAE,CAAC;YACtB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC;YAEjD,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;gBACrB,IAAI,cAAc,EAAE;oBAClB,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;oBACrD,OAAO,CAAC,GAAG,CAAC,CAAC;iBACd;YACH,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACjB,IAAI,cAAc,KAAK,KAAK,EAAE;oBAC5B,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,EAAE;wBAC1C,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,UAAU,EAAE,EAAE,YAAY,CAAC,CAAC;wBAC1D,SAAS,EAAE,CAAC;wBACZ,wEAAwE;wBACxE,YAAY,CAAC,aAAa,CAAC,CAAC;wBAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;qBAC1B;yBAAM,IACL,GAAG,CAAC,UAAU;wBACd,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC;wBACjC,OAAO,GAAG,CAAC,EACX;wBACA,IAAI,SAAiB,CAAC;wBACtB,QAAQ,GAAG,iCAAiC,GAAG,QAAQ,CAAC;wBAExD,uDAAuD;wBACvD,IAAI,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;4BAC9B,SAAS,GAAG,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAE,CAAC,CAAC;yBACjE;6BAAM;4BACL,kCAAkC;4BAClC,SAAS,GAAG,IAAI,CAAC,KAAK,CACpB,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,0BAA0B,GAAG,QAAQ,CAAC;gCACrD,QAAQ,CACX,CAAC;yBACH;wBAED,UAAU,GAAG,UAAU,CAAC,GAAG,EAAE;4BAC3B,aAAa,CAAC,OAAO,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;wBACvC,CAAC,EAAE,SAAS,CAAC,CAAC;qBACf;yBAAM;wBACL,MAAM,KAAK,GAAG,IAAI,iBAAiB,CACjC,GAAG,CAAC,aAAa,EACjB,GAAG,CAAC,UAAU,EACd,YAAY,CACb,CAAC;wBACF,OAAO,CAAC,KAAK,CAAC,CAAC;wBACf,wEAAwE;wBACxE,YAAY,CAAC,aAAa,CAAC,CAAC;wBAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;qBAC1B;iBACF;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAkB,EAAE,EAAE;YACrC,IAAI,cAAc,EAAE;gBAClB,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjE,OAAO,CAAC,GAAG,CAAC,CAAC;aACd;iBAAM;gBACL,OAAO,CAAC,KAAK,CAAC,CAAC;aAChB;YACD,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACnB,IAAI,cAAc,EAAE;gBAClB,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;gBACrD,OAAO,CAAC,GAAG,CAAC,CAAC;aACd;YACD,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,QAAQ,SAAS,CAAC,WAAW,EAAE;YAC7B,KAAK,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBAC9B,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;gBAC1C,MAAM,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC5C,UAAU;qBACP,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;qBACpB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;qBACvB,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;qBACpB,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEb,MAAM;aACP;YACD;gBACE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACd,MAAM;SACT;IACH,CAAC,CAAC;IACF,aAAa,EAAE,CAAC;AAClB,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAqB;IAC/C,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAChC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEpB,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,eAAe,CAC7B,MAAkC;IAElC,IAAI,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,KAAK,KAAK,EAAE;QACzD,IAAI,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAClE,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,MAAM,CAAC,SAAS,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG;QAAE,OAAO,SAAS,CAAC;IAEhE,IAAI;QACF,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAa,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;QACxE,OAAO,IAAI,KAAK,iBAAG,SAAS,EAAE,IAAI,IAAK,MAAM,CAAC,gBAAgB,EAAG,CAAC;KACnE;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,CAAC,KAAK,CACR,wDAAwD,GAAG,CAAC,OAAO,EAAE,CACtE,CAAC;QACF,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,WAA6C;IAE7C,IAAI,WAAW,EAAE;QACf,OAAO,WAAW,CAAC;KACpB;SAAM;QACL,MAAM,kBAAkB,GACtB,MAAM,EAAE,CAAC,qCAAqC;YAC9C,MAAM,EAAE,CAAC,8BAA8B,CAAC;QAC1C,OAAO,kBAAkB,KAAK,oBAAoB,CAAC,IAAI;YACrD,CAAC,CAAC,oBAAoB,CAAC,IAAI;YAC3B,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC;KAC/B;AACH,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as url from 'url';\nimport * as http from 'http';\nimport * as https from 'https';\nimport * as zlib from 'zlib';\nimport { Readable } from 'stream';\nimport { OTLPExporterNodeBase } from './OTLPExporterNodeBase';\nimport { OTLPExporterNodeConfigBase } from '.';\nimport { diag } from '@opentelemetry/api';\nimport { CompressionAlgorithm } from './types';\nimport { getEnv } from '@opentelemetry/core';\nimport { OTLPExporterError } from '../../types';\nimport {\n  DEFAULT_EXPORT_MAX_ATTEMPTS,\n  DEFAULT_EXPORT_INITIAL_BACKOFF,\n  DEFAULT_EXPORT_BACKOFF_MULTIPLIER,\n  DEFAULT_EXPORT_MAX_BACKOFF,\n  isExportRetryable,\n  parseRetryAfterToMills,\n} from '../../util';\n\n/**\n * Sends data using http\n * @param collector\n * @param data\n * @param contentType\n * @param onSuccess\n * @param onError\n */\nexport function sendWithHttp<ExportItem, ServiceRequest>(\n  collector: OTLPExporterNodeBase<ExportItem, ServiceRequest>,\n  data: string | Buffer,\n  contentType: string,\n  onSuccess: () => void,\n  onError: (error: OTLPExporterError) => void\n): void {\n  const exporterTimeout = collector.timeoutMillis;\n  const parsedUrl = new url.URL(collector.url);\n  const nodeVersion = Number(process.versions.node.split('.')[0]);\n  let retryTimer: ReturnType<typeof setTimeout>;\n  let req: http.ClientRequest;\n  let reqIsDestroyed = false;\n\n  const exporterTimer = setTimeout(() => {\n    clearTimeout(retryTimer);\n    reqIsDestroyed = true;\n\n    if (req.destroyed) {\n      const err = new OTLPExporterError('Request Timeout');\n      onError(err);\n    } else {\n      // req.abort() was deprecated since v14\n      nodeVersion >= 14 ? req.destroy() : req.abort();\n    }\n  }, exporterTimeout);\n\n  const options: http.RequestOptions | https.RequestOptions = {\n    hostname: parsedUrl.hostname,\n    port: parsedUrl.port,\n    path: parsedUrl.pathname,\n    method: 'POST',\n    headers: {\n      'Content-Type': contentType,\n      ...collector.headers,\n    },\n    agent: collector.agent,\n  };\n\n  const request = parsedUrl.protocol === 'http:' ? http.request : https.request;\n\n  const sendWithRetry = (\n    retries = DEFAULT_EXPORT_MAX_ATTEMPTS,\n    minDelay = DEFAULT_EXPORT_INITIAL_BACKOFF\n  ) => {\n    req = request(options, (res: http.IncomingMessage) => {\n      let responseData = '';\n      res.on('data', chunk => (responseData += chunk));\n\n      res.on('aborted', () => {\n        if (reqIsDestroyed) {\n          const err = new OTLPExporterError('Request Timeout');\n          onError(err);\n        }\n      });\n\n      res.on('end', () => {\n        if (reqIsDestroyed === false) {\n          if (res.statusCode && res.statusCode < 299) {\n            diag.debug(`statusCode: ${res.statusCode}`, responseData);\n            onSuccess();\n            // clear all timers since request was completed and promise was resolved\n            clearTimeout(exporterTimer);\n            clearTimeout(retryTimer);\n          } else if (\n            res.statusCode &&\n            isExportRetryable(res.statusCode) &&\n            retries > 0\n          ) {\n            let retryTime: number;\n            minDelay = DEFAULT_EXPORT_BACKOFF_MULTIPLIER * minDelay;\n\n            // retry after interval specified in Retry-After header\n            if (res.headers['retry-after']) {\n              retryTime = parseRetryAfterToMills(res.headers['retry-after']!);\n            } else {\n              // exponential backoff with jitter\n              retryTime = Math.round(\n                Math.random() * (DEFAULT_EXPORT_MAX_BACKOFF - minDelay) +\n                  minDelay\n              );\n            }\n\n            retryTimer = setTimeout(() => {\n              sendWithRetry(retries - 1, minDelay);\n            }, retryTime);\n          } else {\n            const error = new OTLPExporterError(\n              res.statusMessage,\n              res.statusCode,\n              responseData\n            );\n            onError(error);\n            // clear all timers since request was completed and promise was resolved\n            clearTimeout(exporterTimer);\n            clearTimeout(retryTimer);\n          }\n        }\n      });\n    });\n\n    req.on('error', (error: Error | any) => {\n      if (reqIsDestroyed) {\n        const err = new OTLPExporterError('Request Timeout', error.code);\n        onError(err);\n      } else {\n        onError(error);\n      }\n      clearTimeout(exporterTimer);\n      clearTimeout(retryTimer);\n    });\n\n    req.on('abort', () => {\n      if (reqIsDestroyed) {\n        const err = new OTLPExporterError('Request Timeout');\n        onError(err);\n      }\n      clearTimeout(exporterTimer);\n      clearTimeout(retryTimer);\n    });\n\n    switch (collector.compression) {\n      case CompressionAlgorithm.GZIP: {\n        req.setHeader('Content-Encoding', 'gzip');\n        const dataStream = readableFromBuffer(data);\n        dataStream\n          .on('error', onError)\n          .pipe(zlib.createGzip())\n          .on('error', onError)\n          .pipe(req);\n\n        break;\n      }\n      default:\n        req.end(data);\n        break;\n    }\n  };\n  sendWithRetry();\n}\n\nfunction readableFromBuffer(buff: string | Buffer): Readable {\n  const readable = new Readable();\n  readable.push(buff);\n  readable.push(null);\n\n  return readable;\n}\n\nexport function createHttpAgent(\n  config: OTLPExporterNodeConfigBase\n): http.Agent | https.Agent | undefined {\n  if (config.httpAgentOptions && config.keepAlive === false) {\n    diag.warn('httpAgentOptions is used only when keepAlive is true');\n    return undefined;\n  }\n\n  if (config.keepAlive === false || !config.url) return undefined;\n\n  try {\n    const parsedUrl = new url.URL(config.url as string);\n    const Agent = parsedUrl.protocol === 'http:' ? http.Agent : https.Agent;\n    return new Agent({ keepAlive: true, ...config.httpAgentOptions });\n  } catch (err) {\n    diag.error(\n      `collector exporter failed to create http agent. err: ${err.message}`\n    );\n    return undefined;\n  }\n}\n\nexport function configureCompression(\n  compression: CompressionAlgorithm | undefined\n): CompressionAlgorithm {\n  if (compression) {\n    return compression;\n  } else {\n    const definedCompression =\n      getEnv().OTEL_EXPORTER_OTLP_TRACES_COMPRESSION ||\n      getEnv().OTEL_EXPORTER_OTLP_COMPRESSION;\n    return definedCompression === CompressionAlgorithm.GZIP\n      ? CompressionAlgorithm.GZIP\n      : CompressionAlgorithm.NONE;\n  }\n}\n"]}