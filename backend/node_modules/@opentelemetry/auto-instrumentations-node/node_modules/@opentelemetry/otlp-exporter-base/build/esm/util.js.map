{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/util.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,IAAM,qBAAqB,GAAG,KAAK,CAAC;AACpC,MAAM,CAAC,IAAM,2BAA2B,GAAG,CAAC,CAAC;AAC7C,MAAM,CAAC,IAAM,8BAA8B,GAAG,IAAI,CAAC;AACnD,MAAM,CAAC,IAAM,0BAA0B,GAAG,IAAI,CAAC;AAC/C,MAAM,CAAC,IAAM,iCAAiC,GAAG,GAAG,CAAC;AAErD;;;GAGG;AACH,MAAM,UAAU,YAAY,CAC1B,cAAqD;IAArD,+BAAA,EAAA,mBAAqD;IAErD,IAAM,OAAO,GAA2B,EAAE,CAAC;IAC3C,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAC,EAAY;YAAZ,KAAA,aAAY,EAAX,GAAG,QAAA,EAAE,KAAK,QAAA;QACjD,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAChC,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;SAC9B;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,cAAW,GAAG,2CAAuC,CAAC,CAAC;SAClE;IACH,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,uBAAuB,CAAC,GAAW,EAAE,IAAY;IAC/D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;KACjB;IACD,OAAO,GAAG,GAAG,IAAI,CAAC;AACpB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,2BAA2B,CAAC,GAAW;IACrD,IAAI;QACF,IAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,SAAS,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC7B,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC;SAC/C;QACD,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;KAC7B;IAAC,WAAM;QACN,IAAI,CAAC,IAAI,CAAC,kCAAgC,GAAG,MAAG,CAAC,CAAC;QAClD,OAAO,GAAG,CAAC;KACZ;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,wBAAwB,CACtC,aAAiC;IAEjC,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;QACrC,IAAI,aAAa,IAAI,CAAC,EAAE;YACtB,oEAAoE;YACpE,OAAO,cAAc,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;SAC7D;QACD,OAAO,aAAa,CAAC;KACtB;SAAM;QACL,OAAO,yBAAyB,EAAE,CAAC;KACpC;AACH,CAAC;AAED,SAAS,yBAAyB;;IAChC,IAAM,cAAc,GAAG,MAAM,CAC3B,MAAA,MAAM,EAAE,CAAC,iCAAiC,mCACxC,MAAM,EAAE,CAAC,0BAA0B,CACtC,CAAC;IAEF,IAAI,cAAc,IAAI,CAAC,EAAE;QACvB,oEAAoE;QACpE,OAAO,cAAc,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC;KAC9D;SAAM;QACL,OAAO,cAAc,CAAC;KACvB;AACH,CAAC;AAED,oEAAoE;AACpE,MAAM,UAAU,cAAc,CAC5B,OAAe,EACf,cAAsB;IAEtB,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,OAAO,CAAC,CAAC;IAErD,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,UAAkB;IAClD,IAAM,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAExC,OAAO,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AACzC,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAC,UAA0B;IAC/D,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,OAAO,CAAC,CAAC,CAAC;KACX;IACD,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAChD,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;QAC7B,OAAO,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1C;IACD,mFAAmF;IACnF,IAAM,KAAK,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE1D,IAAI,KAAK,IAAI,CAAC,EAAE;QACd,OAAO,KAAK,CAAC;KACd;IACD,OAAO,CAAC,CAAC;AACX,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { getEnv } from '@opentelemetry/core';\n\nconst DEFAULT_TRACE_TIMEOUT = 10000;\nexport const DEFAULT_EXPORT_MAX_ATTEMPTS = 5;\nexport const DEFAULT_EXPORT_INITIAL_BACKOFF = 1000;\nexport const DEFAULT_EXPORT_MAX_BACKOFF = 5000;\nexport const DEFAULT_EXPORT_BACKOFF_MULTIPLIER = 1.5;\n\n/**\n * Parses headers from config leaving only those that have defined values\n * @param partialHeaders\n */\nexport function parseHeaders(\n  partialHeaders: Partial<Record<string, unknown>> = {}\n): Record<string, string> {\n  const headers: Record<string, string> = {};\n  Object.entries(partialHeaders).forEach(([key, value]) => {\n    if (typeof value !== 'undefined') {\n      headers[key] = String(value);\n    } else {\n      diag.warn(`Header \"${key}\" has wrong value and will be ignored`);\n    }\n  });\n  return headers;\n}\n\n/**\n * Adds path (version + signal) to a no per-signal endpoint\n * @param url\n * @param path\n * @returns url + path\n */\nexport function appendResourcePathToUrl(url: string, path: string): string {\n  if (!url.endsWith('/')) {\n    url = url + '/';\n  }\n  return url + path;\n}\n\n/**\n * Adds root path to signal specific endpoint when endpoint contains no path part and no root path\n * @param url\n * @returns url\n */\nexport function appendRootPathToUrlIfNeeded(url: string): string {\n  try {\n    const parsedUrl = new URL(url);\n    if (parsedUrl.pathname === '') {\n      parsedUrl.pathname = parsedUrl.pathname + '/';\n    }\n    return parsedUrl.toString();\n  } catch {\n    diag.warn(`Could not parse export URL: '${url}'`);\n    return url;\n  }\n}\n\n/**\n * Configure exporter trace timeout value from passed in value or environment variables\n * @param timeoutMillis\n * @returns timeout value in milliseconds\n */\nexport function configureExporterTimeout(\n  timeoutMillis: number | undefined\n): number {\n  if (typeof timeoutMillis === 'number') {\n    if (timeoutMillis <= 0) {\n      // OTLP exporter configured timeout - using default value of 10000ms\n      return invalidTimeout(timeoutMillis, DEFAULT_TRACE_TIMEOUT);\n    }\n    return timeoutMillis;\n  } else {\n    return getExporterTimeoutFromEnv();\n  }\n}\n\nfunction getExporterTimeoutFromEnv(): number {\n  const definedTimeout = Number(\n    getEnv().OTEL_EXPORTER_OTLP_TRACES_TIMEOUT ??\n      getEnv().OTEL_EXPORTER_OTLP_TIMEOUT\n  );\n\n  if (definedTimeout <= 0) {\n    // OTLP exporter configured timeout - using default value of 10000ms\n    return invalidTimeout(definedTimeout, DEFAULT_TRACE_TIMEOUT);\n  } else {\n    return definedTimeout;\n  }\n}\n\n// OTLP exporter configured timeout - using default value of 10000ms\nexport function invalidTimeout(\n  timeout: number,\n  defaultTimeout: number\n): number {\n  diag.warn('Timeout must be greater than 0', timeout);\n\n  return defaultTimeout;\n}\n\nexport function isExportRetryable(statusCode: number): boolean {\n  const retryCodes = [429, 502, 503, 504];\n\n  return retryCodes.includes(statusCode);\n}\n\nexport function parseRetryAfterToMills(retryAfter?: string | null): number {\n  if (retryAfter == null) {\n    return -1;\n  }\n  const seconds = Number.parseInt(retryAfter, 10);\n  if (Number.isInteger(seconds)) {\n    return seconds > 0 ? seconds * 1000 : -1;\n  }\n  // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After#directives\n  const delay = new Date(retryAfter).getTime() - Date.now();\n\n  if (delay >= 0) {\n    return delay;\n  }\n  return 0;\n}\n"]}