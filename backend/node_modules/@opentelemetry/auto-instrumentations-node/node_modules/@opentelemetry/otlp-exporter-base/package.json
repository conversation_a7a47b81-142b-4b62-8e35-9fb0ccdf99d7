{"name": "@opentelemetry/otlp-exporter-base", "version": "0.46.0", "description": "OpenTelemetry OTLP Exporter base (for internal use only)", "main": "build/src/index.js", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "browser": {"./src/platform/index.ts": "./src/platform/browser/index.ts", "./build/esm/platform/index.js": "./build/esm/platform/browser/index.js", "./build/esnext/platform/index.js": "./build/esnext/platform/browser/index.js", "./build/src/platform/index.js": "./build/src/platform/browser/index.js"}, "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "codecov:browser": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "tdd": "npm run test -- --watch-extensions ts --watch", "tdd:browser": "karma start", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts' --exclude 'test/browser/**/*.ts'", "test:browser": "karma start --single-run", "version": "node ../../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile"}, "keywords": ["opentelemetry", "nodejs", "tracing", "profiling", "metrics", "stats", "monitoring"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "dependencies": {"@opentelemetry/core": "1.19.0"}, "devDependencies": {"@babel/core": "7.23.6", "@opentelemetry/api": "1.7.0", "@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/sinon": "10.0.20", "babel-plugin-istanbul": "6.1.1", "codecov": "3.8.3", "cross-var": "1.1.0", "karma": "6.4.2", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-spec-reporter": "0.0.36", "karma-webpack": "4.0.2", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "sinon": "15.1.2", "ts-loader": "8.4.0", "ts-mocha": "10.0.0", "typescript": "4.4.4", "webpack": "5.89.0", "webpack-cli": "5.1.4", "webpack-merge": "5.10.0"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/otlp-exporter-base", "sideEffects": false, "gitHead": "d3c311aec24137084dc820805a2597e120335672"}