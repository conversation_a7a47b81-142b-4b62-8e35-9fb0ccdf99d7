{"version": 3, "file": "sdk-info.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/sdk-info.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EACL,0BAA0B,EAC1B,0BAA0B,GAC3B,MAAM,qCAAqC,CAAC;AAE7C,0CAA0C;AAC1C,MAAM,CAAC,IAAM,QAAQ;IACnB,GAAC,0BAA0B,CAAC,kBAAkB,IAAG,eAAe;IAChE,GAAC,0BAA0B,CAAC,oBAAoB,IAAG,MAAM;IACzD,GAAC,0BAA0B,CAAC,sBAAsB,IAChD,0BAA0B,CAAC,MAAM;IACnC,GAAC,0BAA0B,CAAC,qBAAqB,IAAG,OAAO;OAC5D,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VERSION } from '../../version';\nimport {\n  TelemetrySdkLanguageValues,\n  SemanticResourceAttributes,\n} from '@opentelemetry/semantic-conventions';\n\n/** Constants describing the SDK in use */\nexport const SDK_INFO = {\n  [SemanticResourceAttributes.TELEMETRY_SDK_NAME]: 'opentelemetry',\n  [SemanticResourceAttributes.PROCESS_RUNTIME_NAME]: 'node',\n  [SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]:\n    TelemetrySdkLanguageValues.NODEJS,\n  [SemanticResourceAttributes.TELEMETRY_SDK_VERSION]: VERSION,\n};\n"]}