{"version": 3, "file": "RandomIdGenerator.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/RandomIdGenerator.ts"], "names": [], "mappings": "AAiBA,IAAM,aAAa,GAAG,CAAC,CAAC;AACxB,IAAM,cAAc,GAAG,EAAE,CAAC;AAE1B;;GAEG;AACH;IAAA;QACE;;;WAGG;QACH,oBAAe,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;QAEjD;;;WAGG;QACH,mBAAc,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAAD,wBAAC;AAAD,CAAC,AAZD,IAYC;;AAED,IAAM,uBAAuB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;AAC1C,SAAS,cAAc,CAAC,KAAa;IACnC,OAAO,SAAS,UAAU;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,uBAAuB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;YACjE,qDAAqD;YACrD,IAAI,uBAAuB,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE;gBACpC,uBAAuB,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aAClC;SACF;QACD,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,CAC9B,IAAI,EACJ,uBAAuB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAC5C,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { IdGenerator } from '../../trace/IdGenerator';\n\nconst SPAN_ID_BYTES = 8;\nconst TRACE_ID_BYTES = 16;\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n */\nexport class RandomIdGenerator implements IdGenerator {\n  /**\n   * Returns a random 16-byte trace ID formatted/encoded as a 32 lowercase hex\n   * characters corresponding to 128 bits.\n   */\n  generateTraceId = getIdGenerator(TRACE_ID_BYTES);\n\n  /**\n   * Returns a random 8-byte span ID formatted/encoded as a 16 lowercase hex\n   * characters corresponding to 64 bits.\n   */\n  generateSpanId = getIdGenerator(SPAN_ID_BYTES);\n}\n\nconst SHARED_CHAR_CODES_ARRAY = Array(32);\nfunction getIdGenerator(bytes: number): () => string {\n  return function generateId() {\n    for (let i = 0; i < bytes * 2; i++) {\n      SHARED_CHAR_CODES_ARRAY[i] = Math.floor(Math.random() * 16) + 48;\n      // valid hex characters in the range 48-57 and 97-102\n      if (SHARED_CHAR_CODES_ARRAY[i] >= 58) {\n        SHARED_CHAR_CODES_ARRAY[i] += 39;\n      }\n    }\n    return String.fromCharCode.apply(\n      null,\n      SHARED_CHAR_CODES_ARRAY.slice(0, bytes * 2)\n    );\n  };\n}\n"]}