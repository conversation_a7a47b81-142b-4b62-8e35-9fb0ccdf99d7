{"name": "@opentelemetry/sdk-node", "version": "0.46.0", "description": "OpenTelemetry SDK for Node.js", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build", "clean": "tsc --build --clean", "test": "nyc ts-mocha -p tsconfig.json test/**/*.test.ts", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../../scripts/version-update.js", "watch": "tsc --build --watch", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../../scripts/peer-api-check.js"}, "keywords": ["opentelemetry", "nodejs", "tracing", "profiling", "metrics", "stats", "monitoring"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "dependencies": {"@opentelemetry/api-logs": "0.46.0", "@opentelemetry/core": "1.19.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.46.0", "@opentelemetry/exporter-trace-otlp-http": "0.46.0", "@opentelemetry/exporter-trace-otlp-proto": "0.46.0", "@opentelemetry/exporter-zipkin": "1.19.0", "@opentelemetry/instrumentation": "0.46.0", "@opentelemetry/resources": "1.19.0", "@opentelemetry/sdk-logs": "0.46.0", "@opentelemetry/sdk-metrics": "1.19.0", "@opentelemetry/sdk-trace-base": "1.19.0", "@opentelemetry/sdk-trace-node": "1.19.0", "@opentelemetry/semantic-conventions": "1.19.0"}, "peerDependencies": {"@opentelemetry/api": ">=1.3.0 <1.8.0"}, "devDependencies": {"@opentelemetry/api": "1.7.0", "@opentelemetry/context-async-hooks": "1.19.0", "@opentelemetry/exporter-jaeger": "1.19.0", "@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/semver": "7.5.6", "@types/sinon": "10.0.20", "codecov": "3.8.3", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "semver": "7.5.4", "sinon": "15.1.2", "ts-loader": "8.4.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/opentelemetry-sdk-node", "sideEffects": false, "gitHead": "d3c311aec24137084dc820805a2597e120335672"}