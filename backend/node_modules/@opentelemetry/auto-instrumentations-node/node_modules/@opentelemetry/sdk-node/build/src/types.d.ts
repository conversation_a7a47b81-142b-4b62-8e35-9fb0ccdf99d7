import type { ContextManager } from '@opentelemetry/api';
import { TextMapPropagator } from '@opentelemetry/api';
import { InstrumentationOption } from '@opentelemetry/instrumentation';
import { Detector, DetectorSync, IResource } from '@opentelemetry/resources';
import { LogRecordProcessor } from '@opentelemetry/sdk-logs';
import { MetricReader, View } from '@opentelemetry/sdk-metrics';
import { Sampler, SpanExporter, SpanLimits, SpanProcessor, IdGenerator } from '@opentelemetry/sdk-trace-base';
export interface NodeSDKConfiguration {
    autoDetectResources: boolean;
    contextManager: ContextManager;
    textMapPropagator: TextMapPropagator;
    logRecordProcessor: LogRecordProcessor;
    metricReader: MetricReader;
    views: View[];
    instrumentations: InstrumentationOption[];
    resource: IResource;
    resourceDetectors: Array<Detector | DetectorSync>;
    sampler: Sam<PERSON>;
    serviceName?: string;
    spanProcessor: SpanProcessor;
    traceExporter: SpanExporter;
    spanLimits: SpanLimits;
    idGenerator: IdGenerator;
}
//# sourceMappingURL=types.d.ts.map