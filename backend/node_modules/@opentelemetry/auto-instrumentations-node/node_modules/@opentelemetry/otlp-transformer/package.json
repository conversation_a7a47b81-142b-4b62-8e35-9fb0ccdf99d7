{"name": "@opentelemetry/otlp-transformer", "private": false, "publishConfig": {"access": "public"}, "version": "0.46.0", "description": "Transform OpenTelemetry SDK data into OTLP", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "main": "build/src/index.js", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test:browser": "karma start --single-run", "test:bench": "node test/performance/benchmark/index.js | tee .benchmark-results.txt", "prewatch": "node ../../../scripts/version-update.js", "watch": "tsc --build -w tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "peer-api-check": "node ../../../scripts/peer-api-check.js", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../"}, "keywords": ["opentelemetry", "nodejs", "grpc", "protobuf", "otlp", "tracing", "metrics"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "LICENSE", "README.md"], "peerDependencies": {"@opentelemetry/api": ">=1.3.0 <1.8.0"}, "devDependencies": {"@opentelemetry/api": "1.7.0", "@types/mocha": "10.0.6", "@types/webpack-env": "1.16.3", "babel-plugin-istanbul": "6.1.1", "codecov": "3.8.3", "cross-var": "1.1.0", "karma": "6.4.2", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-spec-reporter": "0.0.36", "karma-webpack": "4.0.2", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "ts-loader": "8.4.0", "ts-mocha": "10.0.0", "typescript": "4.4.4", "webpack": "5.89.0"}, "dependencies": {"@opentelemetry/api-logs": "0.46.0", "@opentelemetry/core": "1.19.0", "@opentelemetry/resources": "1.19.0", "@opentelemetry/sdk-logs": "0.46.0", "@opentelemetry/sdk-metrics": "1.19.0", "@opentelemetry/sdk-trace-base": "1.19.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/otlp-transformer", "sideEffects": false, "gitHead": "d3c311aec24137084dc820805a2597e120335672"}