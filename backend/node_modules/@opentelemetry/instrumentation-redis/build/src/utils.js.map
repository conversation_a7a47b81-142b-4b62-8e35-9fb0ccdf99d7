{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,4CAQ4B;AAG5B,yBAA0C;AAC1C,8EAG6C;AAC7C,oEAAwE;AAExE,8DAA2E;AAE3E,MAAM,OAAO,GAAG,CAAC,IAAU,EAAE,GAAkB,EAAE,EAAE;IACjD,IAAI,GAAG,EAAE;QACP,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,oBAAc,CAAC,KAAK;YAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC;KACJ;IACD,IAAI,CAAC,GAAG,EAAE,CAAC;AACb,CAAC,CAAC;AAEK,MAAM,qBAAqB,GAAG,CAAC,MAAc,EAAE,QAAkB,EAAE,EAAE;IAC1E,OAAO,SAAS,iBAAiB;QAC/B,MAAM,MAAM,GAA2B,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACvE,OAAO,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC,CAAC;AACJ,CAAC,CAAC;AALW,QAAA,qBAAqB,yBAKhC;AAEK,MAAM,0BAA0B,GAAG,CACxC,MAAc,EACd,QAAkB,EAClB,EAAE;IACF,OAAO,SAAS,mBAAmB;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;YACzD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE;gBACpC,GAAG;oBACD,OAAO,IAAI,CAAC,qBAAqB,CAAC;gBACpC,CAAC;gBACD,GAAG,CAAC,GAAiB;oBACnB,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;oBACpC,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAC;gBACnC,CAAC;aACF,CAAC,CAAC;SACJ;QACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACzC,CAAC,CAAC;AACJ,CAAC,CAAC;AAlBW,QAAA,0BAA0B,8BAkBrC;AAEK,MAAM,4BAA4B,GAAG,CAC1C,MAAc,EACd,QAAkB,EAClB,MAAmC,EACnC,EAAE;IACF,OAAO,SAAS,2BAA2B,CAEzC,GAAkB;QAElB,2DAA2D;QAC3D,6BAA6B;QAC7B,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACrD,mEAAmE;YACnE,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SACxC;QAED,MAAM,eAAe,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,CAAC;QACtE,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,iBAAiB,MAAK,IAAI,IAAI,eAAe,EAAE;YACzD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SACxC;QAED,MAAM,qBAAqB,GACzB,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,qBAAqB,KAAI,2CAA4B,CAAC;QAChE,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAC3B,GAAG,uBAAoB,CAAC,SAAS,IAAI,GAAG,CAAC,OAAO,EAAE,EAClD;YACE,IAAI,EAAE,cAAQ,CAAC,MAAM;YACrB,UAAU,EAAE;gBACV,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,KAAK;gBACpD,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAAE,qBAAqB,CACtD,GAAG,CAAC,OAAO,EACX,GAAG,CAAC,IAAI,CACT;aACF;SACF,CACF,CAAC;QAEF,iEAAiE;QACjE,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,aAAa,CAAC;gBACjB,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;gBACrD,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;aACtD,CAAC,CAAC;SACJ;QACD,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,oBAAoB,EACvC,WAAW,IAAI,CAAC,OAAO,EAAE,CAC1B,CAAC;SACH;QAED,MAAM,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC/C,IAAI,gBAAgB,EAAE;YACpB,MAAM,eAAe,GAAG,aAAO,CAAC,MAAM,EAAE,CAAC;YACxC,SAAS,CAAC,CAAC,CAAkB,CAAC,QAAQ,GAAG,SAAS,QAAQ,CAEzD,GAAiB,EACjB,KAAQ;gBAER,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,YAAY,EAAE;oBACxB,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;oBACzC,IAAA,wCAAsB,EACpB,GAAG,EAAE;wBACH,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBACnD,CAAC,EACD,GAAG,CAAC,EAAE;wBACJ,IAAI,GAAG,EAAE;4BACP,UAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC;yBACjD;oBACH,CAAC,EACD,IAAI,CACL,CAAC;iBACH;gBAED,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBACnB,OAAO,aAAO,CAAC,IAAI,CACjB,eAAe,EACf,gBAAgB,EAChB,IAAI,EACJ,GAAG,SAAS,CACb,CAAC;YACJ,CAAC,CAAC;SACH;QACD,IAAI;YACF,iCAAiC;YACjC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SACxC;QAAC,OAAO,OAAY,EAAE;YACrB,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACvB,MAAM,OAAO,CAAC,CAAC,4BAA4B;SAC5C;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AA3FW,QAAA,4BAA4B,gCA2FvC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as redisTypes from 'redis';\nimport {\n  context,\n  Tracer,\n  SpanKind,\n  Span,\n  SpanStatusCode,\n  trace,\n  diag,\n} from '@opentelemetry/api';\nimport { RedisCommand, RedisInstrumentationConfig } from './types';\nimport { EventEmitter } from 'events';\nimport { RedisInstrumentation } from './';\nimport {\n  DbSystemValues,\n  SemanticAttributes,\n} from '@opentelemetry/semantic-conventions';\nimport { safeExecuteInTheMiddle } from '@opentelemetry/instrumentation';\nimport { RedisPluginClientTypes } from './internal-types';\nimport { defaultDbStatementSerializer } from '@opentelemetry/redis-common';\n\nconst endSpan = (span: Span, err?: Error | null) => {\n  if (err) {\n    span.setStatus({\n      code: SpanStatusCode.ERROR,\n      message: err.message,\n    });\n  }\n  span.end();\n};\n\nexport const getTracedCreateClient = (tracer: Tracer, original: Function) => {\n  return function createClientTrace(this: redisTypes.RedisClient) {\n    const client: redisTypes.RedisClient = original.apply(this, arguments);\n    return context.bind(context.active(), client);\n  };\n};\n\nexport const getTracedCreateStreamTrace = (\n  tracer: Tracer,\n  original: Function\n) => {\n  return function create_stream_trace(this: redisTypes.RedisClient) {\n    if (!Object.prototype.hasOwnProperty.call(this, 'stream')) {\n      Object.defineProperty(this, 'stream', {\n        get() {\n          return this._patched_redis_stream;\n        },\n        set(val: EventEmitter) {\n          context.bind(context.active(), val);\n          this._patched_redis_stream = val;\n        },\n      });\n    }\n    return original.apply(this, arguments);\n  };\n};\n\nexport const getTracedInternalSendCommand = (\n  tracer: Tracer,\n  original: Function,\n  config?: RedisInstrumentationConfig\n) => {\n  return function internal_send_command_trace(\n    this: RedisPluginClientTypes,\n    cmd?: RedisCommand\n  ) {\n    // New versions of redis (2.4+) use a single options object\n    // instead of named arguments\n    if (arguments.length !== 1 || typeof cmd !== 'object') {\n      // We don't know how to trace this call, so don't start/stop a span\n      return original.apply(this, arguments);\n    }\n\n    const hasNoParentSpan = trace.getSpan(context.active()) === undefined;\n    if (config?.requireParentSpan === true && hasNoParentSpan) {\n      return original.apply(this, arguments);\n    }\n\n    const dbStatementSerializer =\n      config?.dbStatementSerializer || defaultDbStatementSerializer;\n    const span = tracer.startSpan(\n      `${RedisInstrumentation.COMPONENT}-${cmd.command}`,\n      {\n        kind: SpanKind.CLIENT,\n        attributes: {\n          [SemanticAttributes.DB_SYSTEM]: DbSystemValues.REDIS,\n          [SemanticAttributes.DB_STATEMENT]: dbStatementSerializer(\n            cmd.command,\n            cmd.args\n          ),\n        },\n      }\n    );\n\n    // Set attributes for not explicitly typed RedisPluginClientTypes\n    if (this.options) {\n      span.setAttributes({\n        [SemanticAttributes.NET_PEER_NAME]: this.options.host,\n        [SemanticAttributes.NET_PEER_PORT]: this.options.port,\n      });\n    }\n    if (this.address) {\n      span.setAttribute(\n        SemanticAttributes.DB_CONNECTION_STRING,\n        `redis://${this.address}`\n      );\n    }\n\n    const originalCallback = arguments[0].callback;\n    if (originalCallback) {\n      const originalContext = context.active();\n      (arguments[0] as RedisCommand).callback = function callback<T>(\n        this: unknown,\n        err: Error | null,\n        reply: T\n      ) {\n        if (config?.responseHook) {\n          const responseHook = config.responseHook;\n          safeExecuteInTheMiddle(\n            () => {\n              responseHook(span, cmd.command, cmd.args, reply);\n            },\n            err => {\n              if (err) {\n                diag.error('Error executing responseHook', err);\n              }\n            },\n            true\n          );\n        }\n\n        endSpan(span, err);\n        return context.with(\n          originalContext,\n          originalCallback,\n          this,\n          ...arguments\n        );\n      };\n    }\n    try {\n      // Span will be ended in callback\n      return original.apply(this, arguments);\n    } catch (rethrow: any) {\n      endSpan(span, rethrow);\n      throw rethrow; // rethrow after ending span\n    }\n  };\n};\n"]}