{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAC1C,oEAIwC;AACxC,mCAIiB;AAEjB,uCAAoC;AAEpC,MAAM,cAAc,GAA+B;IACjD,iBAAiB,EAAE,KAAK;CACzB,CAAC;AAEF,MAAa,oBAAqB,SAAQ,qCAAmB;IAG3D,YAA+B,UAAsC,EAAE;QACrE,KAAK,CAAC,sCAAsC,EAAE,iBAAO,EAAE,OAAO,CAAC,CAAC;QADnC,YAAO,GAAP,OAAO,CAAiC;IAEvE,CAAC;IAEQ,SAAS,CAAC,SAAqC,EAAE;QACxD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAES,IAAI;QACZ,OAAO;YACL,IAAI,qDAAmC,CACrC,OAAO,EACP,CAAC,QAAQ,EAAE,QAAQ,CAAC,EACpB,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;gBAC/B,UAAI,CAAC,KAAK,CAAC,kBAAkB,aAAa,EAAE,CAAC,CAAC;gBAC9C,UAAI,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;gBAC/D,IACE,IAAA,2BAAS,EACP,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAC7D,EACD;oBACA,IAAI,CAAC,OAAO,CACV,aAAa,CAAC,WAAW,CAAC,SAAS,EACnC,uBAAuB,CACxB,CAAC;iBACH;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,WAAW,CAAC,SAAS,EACnC,uBAAuB,EACvB,IAAI,CAAC,4BAA4B,EAAE,CACpC,CAAC;gBAEF,UAAI,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBACvD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE;oBACnE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;iBACpE;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,WAAW,CAAC,SAAS,EACnC,eAAe,EACf,IAAI,CAAC,qBAAqB,EAAE,CAC7B,CAAC;gBAEF,UAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBAC1C,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,YAAY,CAAC,EAAE;oBACzC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;iBAC7C;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,cAAc,EACd,IAAI,CAAC,qBAAqB,EAAE,CAC7B,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,aAAa,CAAC,EAAE;gBACd,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,IAAI,CAAC,OAAO,CACV,aAAa,CAAC,WAAW,CAAC,SAAS,EACnC,uBAAuB,CACxB,CAAC;gBACF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;gBACnE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;YAC9C,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IACD;;OAEG;IACK,4BAA4B;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,OAAO,SAAS,qBAAqB,CAAC,QAAkB;YACtD,OAAO,IAAA,oCAA4B,EAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAChE,CAAC,CAAC;IACJ,CAAC;IAEO,qBAAqB;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,OAAO,SAAS,YAAY,CAAC,QAAkB;YAC7C,OAAO,IAAA,6BAAqB,EAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACjD,CAAC,CAAC;IACJ,CAAC;IAEO,qBAAqB;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,OAAO,SAAS,gBAAgB,CAAC,QAAkB;YACjD,OAAO,IAAA,kCAA0B,EAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACtD,CAAC,CAAC;IACJ,CAAC;;AA3FH,oDA4FC;AA3FiB,8BAAS,GAAG,OAAO,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  isWrapped,\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n} from '@opentelemetry/instrumentation';\nimport {\n  getTracedCreateClient,\n  getTracedCreateStreamTrace,\n  getTracedInternalSendCommand,\n} from './utils';\nimport { RedisInstrumentationConfig } from './types';\nimport { VERSION } from './version';\n\nconst DEFAULT_CONFIG: RedisInstrumentationConfig = {\n  requireParentSpan: false,\n};\n\nexport class RedisInstrumentation extends InstrumentationBase {\n  static readonly COMPONENT = 'redis';\n\n  constructor(protected override _config: RedisInstrumentationConfig = {}) {\n    super('@opentelemetry/instrumentation-redis', VERSION, _config);\n  }\n\n  override setConfig(config: RedisInstrumentationConfig = {}) {\n    this._config = Object.assign({}, DEFAULT_CONFIG, config);\n  }\n\n  protected init() {\n    return [\n      new InstrumentationNodeModuleDefinition<any>(\n        'redis',\n        ['^2.6.0', '^3.0.0'],\n        (moduleExports, moduleVersion) => {\n          diag.debug(`Patching redis@${moduleVersion}`);\n          diag.debug('Patching redis.RedisClient.internal_send_command');\n          if (\n            isWrapped(\n              moduleExports.RedisClient.prototype['internal_send_command']\n            )\n          ) {\n            this._unwrap(\n              moduleExports.RedisClient.prototype,\n              'internal_send_command'\n            );\n          }\n          this._wrap(\n            moduleExports.RedisClient.prototype,\n            'internal_send_command',\n            this._getPatchInternalSendCommand()\n          );\n\n          diag.debug('patching redis.RedisClient.create_stream');\n          if (isWrapped(moduleExports.RedisClient.prototype['create_stream'])) {\n            this._unwrap(moduleExports.RedisClient.prototype, 'create_stream');\n          }\n          this._wrap(\n            moduleExports.RedisClient.prototype,\n            'create_stream',\n            this._getPatchCreateStream()\n          );\n\n          diag.debug('patching redis.createClient');\n          if (isWrapped(moduleExports.createClient)) {\n            this._unwrap(moduleExports, 'createClient');\n          }\n          this._wrap(\n            moduleExports,\n            'createClient',\n            this._getPatchCreateClient()\n          );\n          return moduleExports;\n        },\n        moduleExports => {\n          if (moduleExports === undefined) return;\n          this._unwrap(\n            moduleExports.RedisClient.prototype,\n            'internal_send_command'\n          );\n          this._unwrap(moduleExports.RedisClient.prototype, 'create_stream');\n          this._unwrap(moduleExports, 'createClient');\n        }\n      ),\n    ];\n  }\n  /**\n   * Patch internal_send_command(...) to trace requests\n   */\n  private _getPatchInternalSendCommand() {\n    const tracer = this.tracer;\n    const config = this._config;\n    return function internal_send_command(original: Function) {\n      return getTracedInternalSendCommand(tracer, original, config);\n    };\n  }\n\n  private _getPatchCreateClient() {\n    const tracer = this.tracer;\n    return function createClient(original: Function) {\n      return getTracedCreateClient(tracer, original);\n    };\n  }\n\n  private _getPatchCreateStream() {\n    const tracer = this.tracer;\n    return function createReadStream(original: Function) {\n      return getTracedCreateStreamTrace(tracer, original);\n    };\n  }\n}\n"]}