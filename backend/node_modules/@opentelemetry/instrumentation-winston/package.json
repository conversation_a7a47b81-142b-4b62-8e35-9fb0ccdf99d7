{"name": "@opentelemetry/instrumentation-winston", "version": "0.33.1", "description": "OpenTelemetry instrumentation for winston", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test-all-versions": "tav", "tdd": "npm run test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-winston --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "version:update": "node ../../../scripts/version-update.js", "compile": "tsc -p ."}, "keywords": ["instrumentation", "logging", "nodejs", "opentelemetry", "profiling", "tracing", "winston"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "@types/sinon": "10.0.18", "@types/triple-beam": "^1.3.2", "mocha": "7.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "sinon": "15.2.0", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4", "winston": "3.3.3", "winston2": "npm:winston@2.4.7"}, "dependencies": {"@opentelemetry/instrumentation": "^0.46.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-winston#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}