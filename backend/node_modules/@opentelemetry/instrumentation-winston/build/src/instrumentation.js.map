{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAM4B;AAC5B,oEAMwC;AAQxC,uCAAoC;AAEpC,MAAM,gBAAgB,GAAG,CAAC,QAAQ,CAAC,CAAC;AACpC,MAAM,mBAAmB,GAAG,CAAC,QAAQ,CAAC,CAAC;AAEvC,MAAa,sBAAuB,SAAQ,qCAAmB;IAC7D,YAAY,SAAuC,EAAE;QACnD,KAAK,CAAC,wCAAwC,EAAE,iBAAO,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAES,IAAI;QACZ,OAAO;YACL,IAAI,qDAAmC,CACrC,SAAS,EACT,gBAAgB,EAChB,aAAa,CAAC,EAAE,CAAC,aAAa,EAC9B,GAAG,EAAE,GAAE,CAAC,EACR;gBACE,IAAI,+CAA6B,CAC/B,+BAA+B,EAC/B,gBAAgB,EAChB,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;oBACxB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;oBAChE,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE;wBACxC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;qBACzC;oBAED,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;oBAC/D,OAAO,MAAM,CAAC;gBAChB,CAAC,EACD,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;oBACxB,IAAI,MAAM,KAAK,SAAS;wBAAE,OAAO;oBACjC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;oBAChE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAC1C,CAAC,CACF;aACF,CACF;YACD,IAAI,qDAAmC,CACrC,SAAS,EACT,mBAAmB,EACnB,aAAa,CAAC,EAAE,CAAC,aAAa,EAC9B,GAAG,EAAE,GAAE,CAAC,EACR;gBACE,IAAI,+CAA6B,CAC/B,+BAA+B,EAC/B,mBAAmB,EACnB,CAAC,WAAW,EAAE,aAAa,EAAE,EAAE;oBAC7B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;oBAChE,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;oBAE3C,IAAI,IAAA,2BAAS,EAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACxB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;qBAC5B;oBAED,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;oBAEhD,OAAO,WAAW,CAAC;gBACrB,CAAC,EACD,CAAC,WAAW,EAAE,aAAa,EAAE,EAAE;oBAC7B,IAAI,WAAW,KAAK,SAAS;wBAAE,OAAO;oBACtC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;oBAChE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBACpD,CAAC,CACF;aACF,CACF;SACF,CAAC;IACJ,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,SAAS,CAAC,MAAoC;QACrD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAEO,SAAS,CAAC,IAAU,EAAE,MAA8B;QAC1D,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC;QAEtC,IAAI,CAAC,IAAI,EAAE;YACT,OAAO;SACR;QAED,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EACxB,GAAG,CAAC,EAAE;YACJ,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;aAChD;QACH,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,OAAO,CAAC,QAA2B,EAAE,EAAE;YACrC,MAAM,eAAe,GAAG,IAAI,CAAC;YAC7B,OAAO,SAAS,YAAY,CAE1B,GAAG,IAAiC;gBAEpC,MAAM,IAAI,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAE7C,IAAI,CAAC,IAAI,EAAE;oBACT,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBACnC;gBAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBAEvC,IAAI,CAAC,IAAA,wBAAkB,EAAC,WAAW,CAAC,EAAE;oBACpC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBACnC;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACvB,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;gBAClC,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAExC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,cAAc;QACpB,OAAO,CAAC,QAA2B,EAAE,EAAE;YACrC,MAAM,eAAe,GAAG,IAAI,CAAC;YAC7B,OAAO,SAAS,UAAU,CAExB,GAAG,IAAiC;gBAEpC,MAAM,IAAI,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAE7C,IAAI,CAAC,IAAI,EAAE;oBACT,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBACnC;gBAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBAEvC,IAAI,CAAC,IAAA,wBAAkB,EAAC,WAAW,CAAC,EAAE;oBACpC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBACnC;gBAED,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBACzC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;wBAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBACvB,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;wBAClC,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;wBACxC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;qBACnC;iBACF;gBAED,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;gBAEzC,MAAM,QAAQ,GACZ,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU;oBACzC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;oBACjB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;gBAElB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;gBACjC,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAExC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;CACF;AAjKD,wDAiKC;AAED,SAAS,YAAY,CACnB,WAAwB,EACxB,MAA+B;IAE/B,MAAM,MAAM,GAAG;QACb,QAAQ,EAAE,WAAW,CAAC,OAAO;QAC7B,OAAO,EAAE,WAAW,CAAC,MAAM;QAC3B,WAAW,EAAE,IAAI,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;KACvD,CAAC;IAEF,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,MAAM,CAAC;KACf;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACvC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context,\n  trace,\n  isSpanContextValid,\n  Span,\n  SpanContext,\n} from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport type { WinstonInstrumentationConfig } from './types';\nimport type {\n  Winston2LogMethod,\n  Winston2LoggerModule,\n  Winston3LogMethod,\n  Winston3Logger,\n} from './internal-types';\nimport { VERSION } from './version';\n\nconst winston3Versions = ['>=3 <4'];\nconst winstonPre3Versions = ['>=1 <3'];\n\nexport class WinstonInstrumentation extends InstrumentationBase {\n  constructor(config: WinstonInstrumentationConfig = {}) {\n    super('@opentelemetry/instrumentation-winston', VERSION, config);\n  }\n\n  protected init() {\n    return [\n      new InstrumentationNodeModuleDefinition<{}>(\n        'winston',\n        winston3Versions,\n        moduleExports => moduleExports,\n        () => {},\n        [\n          new InstrumentationNodeModuleFile<Winston3Logger>(\n            'winston/lib/winston/logger.js',\n            winston3Versions,\n            (logger, moduleVersion) => {\n              this._diag.debug(`Applying patch for winston@${moduleVersion}`);\n              if (isWrapped(logger.prototype['write'])) {\n                this._unwrap(logger.prototype, 'write');\n              }\n\n              this._wrap(logger.prototype, 'write', this._getPatchedWrite());\n              return logger;\n            },\n            (logger, moduleVersion) => {\n              if (logger === undefined) return;\n              this._diag.debug(`Removing patch for winston@${moduleVersion}`);\n              this._unwrap(logger.prototype, 'write');\n            }\n          ),\n        ]\n      ),\n      new InstrumentationNodeModuleDefinition<{}>(\n        'winston',\n        winstonPre3Versions,\n        moduleExports => moduleExports,\n        () => {},\n        [\n          new InstrumentationNodeModuleFile<Winston2LoggerModule>(\n            'winston/lib/winston/logger.js',\n            winstonPre3Versions,\n            (fileExports, moduleVersion) => {\n              this._diag.debug(`Applying patch for winston@${moduleVersion}`);\n              const proto = fileExports.Logger.prototype;\n\n              if (isWrapped(proto.log)) {\n                this._unwrap(proto, 'log');\n              }\n\n              this._wrap(proto, 'log', this._getPatchedLog());\n\n              return fileExports;\n            },\n            (fileExports, moduleVersion) => {\n              if (fileExports === undefined) return;\n              this._diag.debug(`Removing patch for winston@${moduleVersion}`);\n              this._unwrap(fileExports.Logger.prototype, 'log');\n            }\n          ),\n        ]\n      ),\n    ];\n  }\n\n  override getConfig(): WinstonInstrumentationConfig {\n    return this._config;\n  }\n\n  override setConfig(config: WinstonInstrumentationConfig) {\n    this._config = config;\n  }\n\n  private _callHook(span: Span, record: Record<string, string>) {\n    const hook = this.getConfig().logHook;\n\n    if (!hook) {\n      return;\n    }\n\n    safeExecuteInTheMiddle(\n      () => hook(span, record),\n      err => {\n        if (err) {\n          this._diag.error('error calling logHook', err);\n        }\n      },\n      true\n    );\n  }\n\n  private _getPatchedWrite() {\n    return (original: Winston3LogMethod) => {\n      const instrumentation = this;\n      return function patchedWrite(\n        this: never,\n        ...args: Parameters<typeof original>\n      ) {\n        const span = trace.getSpan(context.active());\n\n        if (!span) {\n          return original.apply(this, args);\n        }\n\n        const spanContext = span.spanContext();\n\n        if (!isSpanContextValid(spanContext)) {\n          return original.apply(this, args);\n        }\n\n        const record = args[0];\n        injectRecord(spanContext, record);\n        instrumentation._callHook(span, record);\n\n        return original.apply(this, args);\n      };\n    };\n  }\n\n  private _getPatchedLog() {\n    return (original: Winston2LogMethod) => {\n      const instrumentation = this;\n      return function patchedLog(\n        this: unknown,\n        ...args: Parameters<typeof original>\n      ) {\n        const span = trace.getSpan(context.active());\n\n        if (!span) {\n          return original.apply(this, args);\n        }\n\n        const spanContext = span.spanContext();\n\n        if (!isSpanContextValid(spanContext)) {\n          return original.apply(this, args);\n        }\n\n        for (let i = args.length - 1; i >= 0; i--) {\n          if (typeof args[i] === 'object') {\n            const record = args[i];\n            injectRecord(spanContext, record);\n            instrumentation._callHook(span, record);\n            return original.apply(this, args);\n          }\n        }\n\n        const record = injectRecord(spanContext);\n\n        const insertAt =\n          typeof args[args.length - 1] === 'function'\n            ? args.length - 1\n            : args.length;\n\n        args.splice(insertAt, 0, record);\n        instrumentation._callHook(span, record);\n\n        return original.apply(this, args);\n      };\n    };\n  }\n}\n\nfunction injectRecord(\n  spanContext: SpanContext,\n  record?: Record<string, string>\n) {\n  const fields = {\n    trace_id: spanContext.traceId,\n    span_id: spanContext.spanId,\n    trace_flags: `0${spanContext.traceFlags.toString(16)}`,\n  };\n\n  if (!record) {\n    return fields;\n  }\n\n  return Object.assign(record, fields);\n}\n"]}