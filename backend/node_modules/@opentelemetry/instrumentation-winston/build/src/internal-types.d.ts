import type { <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'winston';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gMethod as <PERSON>2<PERSON><PERSON>Method } from 'winston2';
export declare type Winston3LogMethod = Winston3Logger['write'];
export type { <PERSON>3Logger };
export type { <PERSON>2LogMethod };
export declare type Winston2LoggerModule = {
    Logger: <PERSON>2Logger & {
        prototype: {
            log: <PERSON>2LogMethod;
        };
    };
};
export type { <PERSON>2Logger };
//# sourceMappingURL=internal-types.d.ts.map