"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WinstonInstrumentation = void 0;
const api_1 = require("@opentelemetry/api");
const instrumentation_1 = require("@opentelemetry/instrumentation");
const version_1 = require("./version");
const winston3Versions = ['>=3 <4'];
const winstonPre3Versions = ['>=1 <3'];
class WinstonInstrumentation extends instrumentation_1.InstrumentationBase {
    constructor(config = {}) {
        super('@opentelemetry/instrumentation-winston', version_1.VERSION, config);
    }
    init() {
        return [
            new instrumentation_1.InstrumentationNodeModuleDefinition('winston', winston3Versions, moduleExports => moduleExports, () => { }, [
                new instrumentation_1.InstrumentationNodeModuleFile('winston/lib/winston/logger.js', winston3Versions, (logger, moduleVersion) => {
                    this._diag.debug(`Applying patch for winston@${moduleVersion}`);
                    if ((0, instrumentation_1.isWrapped)(logger.prototype['write'])) {
                        this._unwrap(logger.prototype, 'write');
                    }
                    this._wrap(logger.prototype, 'write', this._getPatchedWrite());
                    return logger;
                }, (logger, moduleVersion) => {
                    if (logger === undefined)
                        return;
                    this._diag.debug(`Removing patch for winston@${moduleVersion}`);
                    this._unwrap(logger.prototype, 'write');
                }),
            ]),
            new instrumentation_1.InstrumentationNodeModuleDefinition('winston', winstonPre3Versions, moduleExports => moduleExports, () => { }, [
                new instrumentation_1.InstrumentationNodeModuleFile('winston/lib/winston/logger.js', winstonPre3Versions, (fileExports, moduleVersion) => {
                    this._diag.debug(`Applying patch for winston@${moduleVersion}`);
                    const proto = fileExports.Logger.prototype;
                    if ((0, instrumentation_1.isWrapped)(proto.log)) {
                        this._unwrap(proto, 'log');
                    }
                    this._wrap(proto, 'log', this._getPatchedLog());
                    return fileExports;
                }, (fileExports, moduleVersion) => {
                    if (fileExports === undefined)
                        return;
                    this._diag.debug(`Removing patch for winston@${moduleVersion}`);
                    this._unwrap(fileExports.Logger.prototype, 'log');
                }),
            ]),
        ];
    }
    getConfig() {
        return this._config;
    }
    setConfig(config) {
        this._config = config;
    }
    _callHook(span, record) {
        const hook = this.getConfig().logHook;
        if (!hook) {
            return;
        }
        (0, instrumentation_1.safeExecuteInTheMiddle)(() => hook(span, record), err => {
            if (err) {
                this._diag.error('error calling logHook', err);
            }
        }, true);
    }
    _getPatchedWrite() {
        return (original) => {
            const instrumentation = this;
            return function patchedWrite(...args) {
                const span = api_1.trace.getSpan(api_1.context.active());
                if (!span) {
                    return original.apply(this, args);
                }
                const spanContext = span.spanContext();
                if (!(0, api_1.isSpanContextValid)(spanContext)) {
                    return original.apply(this, args);
                }
                const record = args[0];
                injectRecord(spanContext, record);
                instrumentation._callHook(span, record);
                return original.apply(this, args);
            };
        };
    }
    _getPatchedLog() {
        return (original) => {
            const instrumentation = this;
            return function patchedLog(...args) {
                const span = api_1.trace.getSpan(api_1.context.active());
                if (!span) {
                    return original.apply(this, args);
                }
                const spanContext = span.spanContext();
                if (!(0, api_1.isSpanContextValid)(spanContext)) {
                    return original.apply(this, args);
                }
                for (let i = args.length - 1; i >= 0; i--) {
                    if (typeof args[i] === 'object') {
                        const record = args[i];
                        injectRecord(spanContext, record);
                        instrumentation._callHook(span, record);
                        return original.apply(this, args);
                    }
                }
                const record = injectRecord(spanContext);
                const insertAt = typeof args[args.length - 1] === 'function'
                    ? args.length - 1
                    : args.length;
                args.splice(insertAt, 0, record);
                instrumentation._callHook(span, record);
                return original.apply(this, args);
            };
        };
    }
}
exports.WinstonInstrumentation = WinstonInstrumentation;
function injectRecord(spanContext, record) {
    const fields = {
        trace_id: spanContext.traceId,
        span_id: spanContext.spanId,
        trace_flags: `0${spanContext.traceFlags.toString(16)}`,
    };
    if (!record) {
        return fields;
    }
    return Object.assign(record, fields);
}
//# sourceMappingURL=instrumentation.js.map