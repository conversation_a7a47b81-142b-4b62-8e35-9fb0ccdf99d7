{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Logger as <PERSON>3Logger } from 'winston';\nimport type {\n  LoggerInstance as Winston2Logger,\n  LogMethod as Winston2LogMethod,\n} from 'winston2';\nexport type Winston3LogMethod = Winston3Logger['write'];\nexport type { <PERSON>3Logger };\n\nexport type { Winston2LogMethod };\nexport type Winston2LoggerModule = {\n  Logger: Winston2Logger & { prototype: { log: Winston2LogMethod } };\n};\nexport type { <PERSON>2Logger };\n"]}