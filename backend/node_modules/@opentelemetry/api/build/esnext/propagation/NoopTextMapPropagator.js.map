{"version": 3, "file": "NoopTextMapPropagator.js", "sourceRoot": "", "sources": ["../../../src/propagation/NoopTextMapPropagator.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH;;GAEG;AACH,MAAM,OAAO,qBAAqB;IAChC,wCAAwC;IACxC,MAAM,CAAC,QAAiB,EAAE,QAAiB,IAAS,CAAC;IACrD,uEAAuE;IACvE,OAAO,CAAC,OAAgB,EAAE,QAAiB;QACzC,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,MAAM;QACJ,OAAO,EAAE,CAAC;IACZ,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\nimport { TextMapPropagator } from './TextMapPropagator';\n\n/**\n * No-op implementations of {@link TextMapPropagator}.\n */\nexport class NoopTextMapPropagator implements TextMapPropagator {\n  /** Noop inject function does nothing */\n  inject(_context: Context, _carrier: unknown): void {}\n  /** Noop extract function does nothing and returns the input context */\n  extract(context: Context, _carrier: unknown): Context {\n    return context;\n  }\n  fields(): string[] {\n    return [];\n  }\n}\n"]}