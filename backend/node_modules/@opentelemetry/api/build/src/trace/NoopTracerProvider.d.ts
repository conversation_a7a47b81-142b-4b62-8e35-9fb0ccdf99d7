import { Tracer } from './tracer';
import { TracerOptions } from './tracer_options';
import { TracerProvider } from './tracer_provider';
/**
 * An implementation of the {@link TracerProvider} which returns an impotent
 * Tracer for all calls to `getTracer`.
 *
 * All operations are no-op.
 */
export declare class NoopTracerProvider implements TracerProvider {
    getTracer(_name?: string, _version?: string, _options?: TracerOptions): Tracer;
}
//# sourceMappingURL=NoopTracerProvider.d.ts.map