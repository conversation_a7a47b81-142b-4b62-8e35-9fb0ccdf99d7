{"version": 3, "file": "context.js", "sourceRoot": "", "sources": ["../../../src/context/context.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,qDAAqD;AACrD,SAAgB,gBAAgB,CAAC,WAAmB;IAClD,0EAA0E;IAC1E,2EAA2E;IAC3E,wEAAwE;IACxE,mDAAmD;IACnD,EAAE;IACF,8EAA8E;IAC9E,OAAO,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACjC,CAAC;AARD,4CAQC;AAED,MAAM,WAAW;IAGf;;;;OAIG;IACH,YAAY,aAAoC;QAC9C,mBAAmB;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;QAE1E,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE/D,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAW,EAAE,KAAc,EAAW,EAAE;YACvD,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtD,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACxC,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC;QAEF,IAAI,CAAC,WAAW,GAAG,CAAC,GAAW,EAAW,EAAE;YAC1C,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtD,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACpC,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC;IACJ,CAAC;CAyBF;AAED,6FAA6F;AAChF,QAAA,YAAY,GAAY,IAAI,WAAW,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from './types';\n\n/** Get a key to uniquely identify a context value */\nexport function createContextKey(description: string) {\n  // The specification states that for the same input, multiple calls should\n  // return different keys. Due to the nature of the JS dependency management\n  // system, this creates problems where multiple versions of some package\n  // could hold different keys for the same property.\n  //\n  // Therefore, we use Symbol.for which returns the same key for the same input.\n  return Symbol.for(description);\n}\n\nclass BaseContext implements Context {\n  private _currentContext!: Map<symbol, unknown>;\n\n  /**\n   * Construct a new context which inherits values from an optional parent context.\n   *\n   * @param parentContext a context from which to inherit values\n   */\n  constructor(parentContext?: Map<symbol, unknown>) {\n    // for minification\n    const self = this;\n\n    self._currentContext = parentContext ? new Map(parentContext) : new Map();\n\n    self.getValue = (key: symbol) => self._currentContext.get(key);\n\n    self.setValue = (key: symbol, value: unknown): Context => {\n      const context = new BaseContext(self._currentContext);\n      context._currentContext.set(key, value);\n      return context;\n    };\n\n    self.deleteValue = (key: symbol): Context => {\n      const context = new BaseContext(self._currentContext);\n      context._currentContext.delete(key);\n      return context;\n    };\n  }\n\n  /**\n   * Get a value from the context.\n   *\n   * @param key key which identifies a context value\n   */\n  public getValue!: (key: symbol) => unknown;\n\n  /**\n   * Create a new context which inherits from this context and has\n   * the given key set to the given value.\n   *\n   * @param key context key for which to set the value\n   * @param value value to set for the given key\n   */\n  public setValue!: (key: symbol, value: unknown) => Context;\n\n  /**\n   * Return a new context which inherits from this context but does\n   * not contain a value for the given key.\n   *\n   * @param key context key for which to clear a value\n   */\n  public deleteValue!: (key: symbol) => Context;\n}\n\n/** The root context is used as the default parent context when there is no active context */\nexport const ROOT_CONTEXT: Context = new BaseContext();\n"]}