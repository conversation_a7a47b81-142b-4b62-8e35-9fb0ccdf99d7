{"version": 3, "file": "NoopMeterProvider.js", "sourceRoot": "", "sources": ["../../../src/metrics/NoopMeterProvider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,2CAAyC;AAEzC;;;GAGG;AACH,MAAa,iBAAiB;IAC5B,QAAQ,CAAC,KAAa,EAAE,QAAiB,EAAE,QAAuB;QAChE,OAAO,sBAAU,CAAC;IACpB,CAAC;CACF;AAJD,8CAIC;AAEY,QAAA,mBAAmB,GAAG,IAAI,iBAAiB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Meter, MeterOptions } from './Meter';\nimport { MeterProvider } from './MeterProvider';\nimport { NOOP_METER } from './NoopMeter';\n\n/**\n * An implementation of the {@link MeterProvider} which returns an impotent Meter\n * for all calls to `getMeter`\n */\nexport class NoopMeterProvider implements MeterProvider {\n  getMeter(_name: string, _version?: string, _options?: MeterOptions): Meter {\n    return NOOP_METER;\n  }\n}\n\nexport const NOOP_METER_PROVIDER = new NoopMeterProvider();\n"]}