import { Context<PERSON>anager, TextMapPropagator } from '@opentelemetry/api';
import { IResource } from '@opentelemetry/resources';
import { LogRecordProcessor } from '@opentelemetry/sdk-logs';
import { MetricReader, View } from '@opentelemetry/sdk-metrics';
import { SpanProcessor } from '@opentelemetry/sdk-trace-base';
import { NodeTracerConfig } from '@opentelemetry/sdk-trace-node';
import { NodeSDKConfiguration } from './types';
/** This class represents everything needed to register a fully configured OpenTelemetry Node.js SDK */
export declare type MeterProviderConfig = {
    /**
     * Reference to the MetricReader instance by the NodeSDK
     */
    reader?: MetricReader;
    /**
     * List of {@link View}s that should be passed to the MeterProvider
     */
    views?: View[];
};
export declare type LoggerProviderConfig = {
    /**
     * Reference to the LoggerRecordProcessor instance by the NodeSDK
     */
    logRecordProcessor: LogRecordProcessor;
};
export declare class NodeSDK {
    private _tracerProviderConfig?;
    private _loggerProviderConfig?;
    private _meterProviderConfig?;
    private _instrumentations;
    private _resource;
    private _resourceDetectors;
    private _autoDetectResources;
    private _tracerProvider?;
    private _loggerProvider?;
    private _meterProvider?;
    private _serviceName?;
    private _disabled?;
    /**
     * Create a new NodeJS SDK instance
     */
    constructor(configuration?: Partial<NodeSDKConfiguration>);
    /**
     *
     * @deprecated Please pass {@code sampler}, {@code generalLimits}, {@code spanLimits}, {@code resource},
     * {@code IdGenerator}, {@code spanProcessor}, {@code contextManager} and {@code textMapPropagator},
     * to the constructor options instead.
     *
     * Set configurations needed to register a TracerProvider
     */
    configureTracerProvider(tracerConfig: NodeTracerConfig, spanProcessor: SpanProcessor, contextManager?: ContextManager, textMapPropagator?: TextMapPropagator): void;
    /**
     * @deprecated Please pass {@code logRecordProcessor} to the constructor options instead.
     *
     * Set configurations needed to register a LoggerProvider
     */
    configureLoggerProvider(config: LoggerProviderConfig): void;
    /**
     * @deprecated Please pass {@code views} and {@code reader} to the constructor options instead.
     *
     * Set configurations needed to register a MeterProvider
     */
    configureMeterProvider(config: MeterProviderConfig): void;
    /**
     * @deprecated Resources are detected automatically on {@link NodeSDK.start()}, when the {@code autoDetectResources}
     * constructor option is set to {@code true} or left {@code undefined}.
     *
     * Detect resource attributes
     */
    detectResources(): void;
    /**
     * @deprecated Please pre-merge resources and pass them to the constructor
     *
     * Manually add a Resource
     * @param resource
     */
    addResource(resource: IResource): void;
    /**
     * Call this method to construct SDK components and register them with the OpenTelemetry API.
     */
    start(): void;
    shutdown(): Promise<void>;
}
//# sourceMappingURL=sdk.d.ts.map