{"version": 3, "file": "sdk.js", "sourceRoot": "", "sources": ["../../src/sdk.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAM4B;AAC5B,sDAA+C;AAC/C,oEAGwC;AACxC,wDASkC;AAClC,sDAA6E;AAC7E,4DAA+E;AAC/E,kEAGuC;AACvC,kEAGuC;AACvC,8EAAiF;AAEjF,mFAAiF;AACjF,8CAAoE;AACpE,mCAAsD;AAsBtD,MAAa,OAAO;IAuBlB;;OAEG;IACH,YAAmB,gBAA+C,EAAE;;QAClE,MAAM,GAAG,GAAG,IAAA,aAAM,GAAE,CAAC;QACrB,MAAM,kBAAkB,GAAG,IAAA,4BAAqB,GAAE,CAAC;QAEnD,IAAI,GAAG,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,+CAA+C;YAC/C,kCAAkC;SACnC;QAED,6DAA6D;QAC7D,uDAAuD;QACvD,IAAI,kBAAkB,CAAC,cAAc,EAAE;YACrC,UAAI,CAAC,SAAS,CAAC,IAAI,uBAAiB,EAAE,EAAE;gBACtC,QAAQ,EAAE,kBAAkB,CAAC,cAAc;aAC5C,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,SAAS,GAAG,MAAA,aAAa,CAAC,QAAQ,mCAAI,IAAI,oBAAQ,CAAC,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,kBAAkB,GAAG,MAAA,aAAa,CAAC,iBAAiB,mCAAI;YAC3D,uBAAW;YACX,2BAAe;SAChB,CAAC;QAEF,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,WAAW,CAAC;QAE9C,IAAI,CAAC,oBAAoB,GAAG,MAAA,aAAa,CAAC,mBAAmB,mCAAI,IAAI,CAAC;QAEtE,IAAI,aAAa,CAAC,aAAa,IAAI,aAAa,CAAC,aAAa,EAAE;YAC9D,MAAM,oBAAoB,GAAqB,EAAE,CAAC;YAElD,IAAI,aAAa,CAAC,OAAO,EAAE;gBACzB,oBAAoB,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;aACtD;YACD,IAAI,aAAa,CAAC,UAAU,EAAE;gBAC5B,oBAAoB,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;aAC5D;YACD,IAAI,aAAa,CAAC,WAAW,EAAE;gBAC7B,oBAAoB,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;aAC9D;YAED,MAAM,aAAa,GACjB,MAAA,aAAa,CAAC,aAAa,mCAC3B,IAAI,mCAAkB,CAAC,aAAa,CAAC,aAAc,CAAC,CAAC;YAEvD,IAAI,CAAC,uBAAuB,CAC1B,oBAAoB,EACpB,aAAa,EACb,aAAa,CAAC,cAAc,EAC5B,aAAa,CAAC,iBAAiB,CAChC,CAAC;SACH;QAED,IAAI,aAAa,CAAC,kBAAkB,EAAE;YACpC,MAAM,oBAAoB,GAAyB;gBACjD,kBAAkB,EAAE,aAAa,CAAC,kBAAkB;aACrD,CAAC;YACF,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;SACpD;QAED,IAAI,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,KAAK,EAAE;YACrD,MAAM,mBAAmB,GAAwB,EAAE,CAAC;YACpD,IAAI,aAAa,CAAC,YAAY,EAAE;gBAC9B,mBAAmB,CAAC,MAAM,GAAG,aAAa,CAAC,YAAY,CAAC;aACzD;YAED,IAAI,aAAa,CAAC,KAAK,EAAE;gBACvB,mBAAmB,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;aACjD;YAED,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;SAClD;QAED,IAAI,gBAAgB,GAA4B,EAAE,CAAC;QACnD,IAAI,aAAa,CAAC,gBAAgB,EAAE;YAClC,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,CAAC;SACnD;QACD,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IAC5C,CAAC;IAED;;;;;;;OAOG;IACI,uBAAuB,CAC5B,YAA8B,EAC9B,aAA4B,EAC5B,cAA+B,EAC/B,iBAAqC;QAErC,IAAI,CAAC,qBAAqB,GAAG;YAC3B,YAAY;YACZ,aAAa;YACb,cAAc;YACd,iBAAiB;SAClB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,MAA4B;QACzD,wDAAwD;QACxD,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,EAAE;YACtC,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC;YACpC,OAAO;SACR;QAED,2FAA2F;QAC3F,IACE,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,IAAI,IAAI;YACrD,MAAM,CAAC,kBAAkB,IAAI,IAAI,EACjC;YACA,MAAM,IAAI,KAAK,CACb,+EAA+E,CAChF,CAAC;SACH;QAED,6GAA6G;QAC7G,IAAI,MAAM,CAAC,kBAAkB,IAAI,IAAI,EAAE;YACrC,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;SAC3E;IACH,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,MAA2B;QACvD,oDAAoD;QACpD,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,EAAE;YACrC,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC;YACnC,OAAO;SACR;QAED,gEAAgE;QAChE,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,IAAI,IAAI,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;YACnE,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;SACzE;QAED,kFAAkF;QAClF,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;YACxB,IAAI,CAAC,oBAAoB,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;SAChD;QAED,oEAAoE;QACpE,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;YACrE,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;SACH;QAED,oFAAoF;QACpF,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;YACzB,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SAClD;IACH,CAAC;IAED;;;;;OAKG;IACI,eAAe;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QAED,MAAM,cAAc,GAA4B;YAC9C,SAAS,EAAE,IAAI,CAAC,kBAAkB;SACnC,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,IAAA,+BAAmB,EAAC,cAAc,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,QAAmB;QACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,KAAK;;QACV,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QAED,IAAA,0CAAwB,EAAC;YACvB,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;SACzC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,YAAY,KAAK,SAAS;gBAC7B,CAAC,CAAC,IAAI,CAAC,SAAS;gBAChB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAClB,IAAI,oBAAQ,CAAC;oBACX,CAAC,iDAA0B,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY;iBAC7D,CAAC,CACH,CAAC;QAER,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB;YACzC,CAAC,CAAC,mCAAkB;YACpB,CAAC,CAAC,8DAA8B,CAAC;QAEnC,MAAM,cAAc,GAAG,IAAI,QAAQ,iCAC9B,MAAA,IAAI,CAAC,qBAAqB,0CAAE,YAAY,KAC3C,QAAQ,EAAE,IAAI,CAAC,SAAS,IACxB,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QAEtC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;SAC3E;QAED,cAAc,CAAC,QAAQ,CAAC;YACtB,cAAc,EAAE,MAAA,IAAI,CAAC,qBAAqB,0CAAE,cAAc;YAC1D,UAAU,EAAE,MAAA,IAAI,CAAC,qBAAqB,0CAAE,iBAAiB;SAC1D,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,MAAM,cAAc,GAAG,IAAI,yBAAc,CAAC;gBACxC,QAAQ,EAAE,IAAI,CAAC,SAAS;aACzB,CAAC,CAAC;YACH,cAAc,CAAC,qBAAqB,CAClC,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAC9C,CAAC;YAEF,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;YAEtC,eAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,aAAa,GAAG,IAAI,2BAAa,CAAC;gBACtC,QAAQ,EAAE,IAAI,CAAC,SAAS;gBACxB,KAAK,EAAE,MAAA,MAAA,IAAI,CAAC,oBAAoB,0CAAE,KAAK,mCAAI,EAAE;aAC9C,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;gBACpC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;aACjE;YAED,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;YAEpC,aAAO,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAE9C,mGAAmG;YACnG,4GAA4G;YAC5G,4GAA4G;YAC5G,KAAK,MAAM,eAAe,IAAI,IAAA,mCAA2B,EACvD,IAAI,CAAC,iBAAiB,CACvB,EAAE;gBACD,eAAe,CAAC,gBAAgB,CAAC,aAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;aAC9D;SACF;IACH,CAAC;IAEM,QAAQ;QACb,MAAM,QAAQ,GAAuB,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC/C;QAED,OAAO,CACL,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;YACnB,oDAAoD;aACnD,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAClB,CAAC;IACJ,CAAC;CACF;AAhUD,0BAgUC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ContextManager,\n  TextMapPropagator,\n  metrics,\n  diag,\n  DiagConsoleLogger,\n} from '@opentelemetry/api';\nimport { logs } from '@opentelemetry/api-logs';\nimport {\n  InstrumentationOption,\n  registerInstrumentations,\n} from '@opentelemetry/instrumentation';\nimport {\n  Detector,\n  DetectorSync,\n  detectResourcesSync,\n  envDetector,\n  IResource,\n  processDetector,\n  Resource,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport { LogRecordProcessor, LoggerProvider } from '@opentelemetry/sdk-logs';\nimport { MeterProvider, MetricReader, View } from '@opentelemetry/sdk-metrics';\nimport {\n  BatchSpanProcessor,\n  SpanProcessor,\n} from '@opentelemetry/sdk-trace-base';\nimport {\n  NodeTracerConfig,\n  NodeTracerProvider,\n} from '@opentelemetry/sdk-trace-node';\nimport { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';\nimport { NodeSDKConfiguration } from './types';\nimport { TracerProviderWithEnvExporters } from './TracerProviderWithEnvExporter';\nimport { getEnv, getEnvWithoutDefaults } from '@opentelemetry/core';\nimport { parseInstrumentationOptions } from './utils';\n\n/** This class represents everything needed to register a fully configured OpenTelemetry Node.js SDK */\n\nexport type MeterProviderConfig = {\n  /**\n   * Reference to the MetricReader instance by the NodeSDK\n   */\n  reader?: MetricReader;\n  /**\n   * List of {@link View}s that should be passed to the MeterProvider\n   */\n  views?: View[];\n};\n\nexport type LoggerProviderConfig = {\n  /**\n   * Reference to the LoggerRecordProcessor instance by the NodeSDK\n   */\n  logRecordProcessor: LogRecordProcessor;\n};\n\nexport class NodeSDK {\n  private _tracerProviderConfig?: {\n    tracerConfig: NodeTracerConfig;\n    spanProcessor: SpanProcessor;\n    contextManager?: ContextManager;\n    textMapPropagator?: TextMapPropagator;\n  };\n  private _loggerProviderConfig?: LoggerProviderConfig;\n  private _meterProviderConfig?: MeterProviderConfig;\n  private _instrumentations: InstrumentationOption[];\n\n  private _resource: IResource;\n  private _resourceDetectors: Array<Detector | DetectorSync>;\n\n  private _autoDetectResources: boolean;\n\n  private _tracerProvider?: NodeTracerProvider | TracerProviderWithEnvExporters;\n  private _loggerProvider?: LoggerProvider;\n  private _meterProvider?: MeterProvider;\n  private _serviceName?: string;\n\n  private _disabled?: boolean;\n\n  /**\n   * Create a new NodeJS SDK instance\n   */\n  public constructor(configuration: Partial<NodeSDKConfiguration> = {}) {\n    const env = getEnv();\n    const envWithoutDefaults = getEnvWithoutDefaults();\n\n    if (env.OTEL_SDK_DISABLED) {\n      this._disabled = true;\n      // Functions with possible side-effects are set\n      // to no-op via the _disabled flag\n    }\n\n    // Default is INFO, use environment without defaults to check\n    // if the user originally set the environment variable.\n    if (envWithoutDefaults.OTEL_LOG_LEVEL) {\n      diag.setLogger(new DiagConsoleLogger(), {\n        logLevel: envWithoutDefaults.OTEL_LOG_LEVEL,\n      });\n    }\n\n    this._resource = configuration.resource ?? new Resource({});\n    this._resourceDetectors = configuration.resourceDetectors ?? [\n      envDetector,\n      processDetector,\n    ];\n\n    this._serviceName = configuration.serviceName;\n\n    this._autoDetectResources = configuration.autoDetectResources ?? true;\n\n    if (configuration.spanProcessor || configuration.traceExporter) {\n      const tracerProviderConfig: NodeTracerConfig = {};\n\n      if (configuration.sampler) {\n        tracerProviderConfig.sampler = configuration.sampler;\n      }\n      if (configuration.spanLimits) {\n        tracerProviderConfig.spanLimits = configuration.spanLimits;\n      }\n      if (configuration.idGenerator) {\n        tracerProviderConfig.idGenerator = configuration.idGenerator;\n      }\n\n      const spanProcessor =\n        configuration.spanProcessor ??\n        new BatchSpanProcessor(configuration.traceExporter!);\n\n      this.configureTracerProvider(\n        tracerProviderConfig,\n        spanProcessor,\n        configuration.contextManager,\n        configuration.textMapPropagator\n      );\n    }\n\n    if (configuration.logRecordProcessor) {\n      const loggerProviderConfig: LoggerProviderConfig = {\n        logRecordProcessor: configuration.logRecordProcessor,\n      };\n      this.configureLoggerProvider(loggerProviderConfig);\n    }\n\n    if (configuration.metricReader || configuration.views) {\n      const meterProviderConfig: MeterProviderConfig = {};\n      if (configuration.metricReader) {\n        meterProviderConfig.reader = configuration.metricReader;\n      }\n\n      if (configuration.views) {\n        meterProviderConfig.views = configuration.views;\n      }\n\n      this.configureMeterProvider(meterProviderConfig);\n    }\n\n    let instrumentations: InstrumentationOption[] = [];\n    if (configuration.instrumentations) {\n      instrumentations = configuration.instrumentations;\n    }\n    this._instrumentations = instrumentations;\n  }\n\n  /**\n   *\n   * @deprecated Please pass {@code sampler}, {@code generalLimits}, {@code spanLimits}, {@code resource},\n   * {@code IdGenerator}, {@code spanProcessor}, {@code contextManager} and {@code textMapPropagator},\n   * to the constructor options instead.\n   *\n   * Set configurations needed to register a TracerProvider\n   */\n  public configureTracerProvider(\n    tracerConfig: NodeTracerConfig,\n    spanProcessor: SpanProcessor,\n    contextManager?: ContextManager,\n    textMapPropagator?: TextMapPropagator\n  ): void {\n    this._tracerProviderConfig = {\n      tracerConfig,\n      spanProcessor,\n      contextManager,\n      textMapPropagator,\n    };\n  }\n\n  /**\n   * @deprecated Please pass {@code logRecordProcessor} to the constructor options instead.\n   *\n   * Set configurations needed to register a LoggerProvider\n   */\n  public configureLoggerProvider(config: LoggerProviderConfig): void {\n    // nothing is set yet, we can set config and then return\n    if (this._loggerProviderConfig == null) {\n      this._loggerProviderConfig = config;\n      return;\n    }\n\n    // make sure we do not override existing logRecordProcessor with other logRecordProcessors.\n    if (\n      this._loggerProviderConfig.logRecordProcessor != null &&\n      config.logRecordProcessor != null\n    ) {\n      throw new Error(\n        'LogRecordProcessor passed but LogRecordProcessor has already been configured.'\n      );\n    }\n\n    // set logRecordProcessor, but make sure we do not override existing logRecordProcessors with null/undefined.\n    if (config.logRecordProcessor != null) {\n      this._loggerProviderConfig.logRecordProcessor = config.logRecordProcessor;\n    }\n  }\n\n  /**\n   * @deprecated Please pass {@code views} and {@code reader} to the constructor options instead.\n   *\n   * Set configurations needed to register a MeterProvider\n   */\n  public configureMeterProvider(config: MeterProviderConfig): void {\n    // nothing is set yet, we can set config and return.\n    if (this._meterProviderConfig == null) {\n      this._meterProviderConfig = config;\n      return;\n    }\n\n    // make sure we do not override existing views with other views.\n    if (this._meterProviderConfig.views != null && config.views != null) {\n      throw new Error('Views passed but Views have already been configured.');\n    }\n\n    // set views, but make sure we do not override existing views with null/undefined.\n    if (config.views != null) {\n      this._meterProviderConfig.views = config.views;\n    }\n\n    // make sure we do not override existing reader with another reader.\n    if (this._meterProviderConfig.reader != null && config.reader != null) {\n      throw new Error(\n        'MetricReader passed but MetricReader has already been configured.'\n      );\n    }\n\n    // set reader, but make sure we do not override existing reader with null/undefined.\n    if (config.reader != null) {\n      this._meterProviderConfig.reader = config.reader;\n    }\n  }\n\n  /**\n   * @deprecated Resources are detected automatically on {@link NodeSDK.start()}, when the {@code autoDetectResources}\n   * constructor option is set to {@code true} or left {@code undefined}.\n   *\n   * Detect resource attributes\n   */\n  public detectResources(): void {\n    if (this._disabled) {\n      return;\n    }\n\n    const internalConfig: ResourceDetectionConfig = {\n      detectors: this._resourceDetectors,\n    };\n\n    this.addResource(detectResourcesSync(internalConfig));\n  }\n\n  /**\n   * @deprecated Please pre-merge resources and pass them to the constructor\n   *\n   * Manually add a Resource\n   * @param resource\n   */\n  public addResource(resource: IResource): void {\n    this._resource = this._resource.merge(resource);\n  }\n\n  /**\n   * Call this method to construct SDK components and register them with the OpenTelemetry API.\n   */\n  public start(): void {\n    if (this._disabled) {\n      return;\n    }\n\n    registerInstrumentations({\n      instrumentations: this._instrumentations,\n    });\n\n    if (this._autoDetectResources) {\n      this.detectResources();\n    }\n\n    this._resource =\n      this._serviceName === undefined\n        ? this._resource\n        : this._resource.merge(\n            new Resource({\n              [SemanticResourceAttributes.SERVICE_NAME]: this._serviceName,\n            })\n          );\n\n    const Provider = this._tracerProviderConfig\n      ? NodeTracerProvider\n      : TracerProviderWithEnvExporters;\n\n    const tracerProvider = new Provider({\n      ...this._tracerProviderConfig?.tracerConfig,\n      resource: this._resource,\n    });\n\n    this._tracerProvider = tracerProvider;\n\n    if (this._tracerProviderConfig) {\n      tracerProvider.addSpanProcessor(this._tracerProviderConfig.spanProcessor);\n    }\n\n    tracerProvider.register({\n      contextManager: this._tracerProviderConfig?.contextManager,\n      propagator: this._tracerProviderConfig?.textMapPropagator,\n    });\n\n    if (this._loggerProviderConfig) {\n      const loggerProvider = new LoggerProvider({\n        resource: this._resource,\n      });\n      loggerProvider.addLogRecordProcessor(\n        this._loggerProviderConfig.logRecordProcessor\n      );\n\n      this._loggerProvider = loggerProvider;\n\n      logs.setGlobalLoggerProvider(loggerProvider);\n    }\n\n    if (this._meterProviderConfig) {\n      const meterProvider = new MeterProvider({\n        resource: this._resource,\n        views: this._meterProviderConfig?.views ?? [],\n      });\n\n      if (this._meterProviderConfig.reader) {\n        meterProvider.addMetricReader(this._meterProviderConfig.reader);\n      }\n\n      this._meterProvider = meterProvider;\n\n      metrics.setGlobalMeterProvider(meterProvider);\n\n      // TODO: This is a workaround to fix https://github.com/open-telemetry/opentelemetry-js/issues/3609\n      // If the MeterProvider is not yet registered when instrumentations are registered, all metrics are dropped.\n      // This code is obsolete once https://github.com/open-telemetry/opentelemetry-js/issues/3622 is implemented.\n      for (const instrumentation of parseInstrumentationOptions(\n        this._instrumentations\n      )) {\n        instrumentation.setMeterProvider(metrics.getMeterProvider());\n      }\n    }\n  }\n\n  public shutdown(): Promise<void> {\n    const promises: Promise<unknown>[] = [];\n    if (this._tracerProvider) {\n      promises.push(this._tracerProvider.shutdown());\n    }\n    if (this._loggerProvider) {\n      promises.push(this._loggerProvider.shutdown());\n    }\n    if (this._meterProvider) {\n      promises.push(this._meterProvider.shutdown());\n    }\n\n    return (\n      Promise.all(promises)\n        // return void instead of the array from Promise.all\n        .then(() => {})\n    );\n  }\n}\n"]}