"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeSDK = void 0;
const api_1 = require("@opentelemetry/api");
const api_logs_1 = require("@opentelemetry/api-logs");
const instrumentation_1 = require("@opentelemetry/instrumentation");
const resources_1 = require("@opentelemetry/resources");
const sdk_logs_1 = require("@opentelemetry/sdk-logs");
const sdk_metrics_1 = require("@opentelemetry/sdk-metrics");
const sdk_trace_base_1 = require("@opentelemetry/sdk-trace-base");
const sdk_trace_node_1 = require("@opentelemetry/sdk-trace-node");
const semantic_conventions_1 = require("@opentelemetry/semantic-conventions");
const TracerProviderWithEnvExporter_1 = require("./TracerProviderWithEnvExporter");
const core_1 = require("@opentelemetry/core");
const utils_1 = require("./utils");
class NodeSDK {
    /**
     * Create a new NodeJS SDK instance
     */
    constructor(configuration = {}) {
        var _a, _b, _c, _d;
        const env = (0, core_1.getEnv)();
        const envWithoutDefaults = (0, core_1.getEnvWithoutDefaults)();
        if (env.OTEL_SDK_DISABLED) {
            this._disabled = true;
            // Functions with possible side-effects are set
            // to no-op via the _disabled flag
        }
        // Default is INFO, use environment without defaults to check
        // if the user originally set the environment variable.
        if (envWithoutDefaults.OTEL_LOG_LEVEL) {
            api_1.diag.setLogger(new api_1.DiagConsoleLogger(), {
                logLevel: envWithoutDefaults.OTEL_LOG_LEVEL,
            });
        }
        this._resource = (_a = configuration.resource) !== null && _a !== void 0 ? _a : new resources_1.Resource({});
        this._resourceDetectors = (_b = configuration.resourceDetectors) !== null && _b !== void 0 ? _b : [
            resources_1.envDetector,
            resources_1.processDetector,
        ];
        this._serviceName = configuration.serviceName;
        this._autoDetectResources = (_c = configuration.autoDetectResources) !== null && _c !== void 0 ? _c : true;
        if (configuration.spanProcessor || configuration.traceExporter) {
            const tracerProviderConfig = {};
            if (configuration.sampler) {
                tracerProviderConfig.sampler = configuration.sampler;
            }
            if (configuration.spanLimits) {
                tracerProviderConfig.spanLimits = configuration.spanLimits;
            }
            if (configuration.idGenerator) {
                tracerProviderConfig.idGenerator = configuration.idGenerator;
            }
            const spanProcessor = (_d = configuration.spanProcessor) !== null && _d !== void 0 ? _d : new sdk_trace_base_1.BatchSpanProcessor(configuration.traceExporter);
            this.configureTracerProvider(tracerProviderConfig, spanProcessor, configuration.contextManager, configuration.textMapPropagator);
        }
        if (configuration.logRecordProcessor) {
            const loggerProviderConfig = {
                logRecordProcessor: configuration.logRecordProcessor,
            };
            this.configureLoggerProvider(loggerProviderConfig);
        }
        if (configuration.metricReader || configuration.views) {
            const meterProviderConfig = {};
            if (configuration.metricReader) {
                meterProviderConfig.reader = configuration.metricReader;
            }
            if (configuration.views) {
                meterProviderConfig.views = configuration.views;
            }
            this.configureMeterProvider(meterProviderConfig);
        }
        let instrumentations = [];
        if (configuration.instrumentations) {
            instrumentations = configuration.instrumentations;
        }
        this._instrumentations = instrumentations;
    }
    /**
     *
     * @deprecated Please pass {@code sampler}, {@code generalLimits}, {@code spanLimits}, {@code resource},
     * {@code IdGenerator}, {@code spanProcessor}, {@code contextManager} and {@code textMapPropagator},
     * to the constructor options instead.
     *
     * Set configurations needed to register a TracerProvider
     */
    configureTracerProvider(tracerConfig, spanProcessor, contextManager, textMapPropagator) {
        this._tracerProviderConfig = {
            tracerConfig,
            spanProcessor,
            contextManager,
            textMapPropagator,
        };
    }
    /**
     * @deprecated Please pass {@code logRecordProcessor} to the constructor options instead.
     *
     * Set configurations needed to register a LoggerProvider
     */
    configureLoggerProvider(config) {
        // nothing is set yet, we can set config and then return
        if (this._loggerProviderConfig == null) {
            this._loggerProviderConfig = config;
            return;
        }
        // make sure we do not override existing logRecordProcessor with other logRecordProcessors.
        if (this._loggerProviderConfig.logRecordProcessor != null &&
            config.logRecordProcessor != null) {
            throw new Error('LogRecordProcessor passed but LogRecordProcessor has already been configured.');
        }
        // set logRecordProcessor, but make sure we do not override existing logRecordProcessors with null/undefined.
        if (config.logRecordProcessor != null) {
            this._loggerProviderConfig.logRecordProcessor = config.logRecordProcessor;
        }
    }
    /**
     * @deprecated Please pass {@code views} and {@code reader} to the constructor options instead.
     *
     * Set configurations needed to register a MeterProvider
     */
    configureMeterProvider(config) {
        // nothing is set yet, we can set config and return.
        if (this._meterProviderConfig == null) {
            this._meterProviderConfig = config;
            return;
        }
        // make sure we do not override existing views with other views.
        if (this._meterProviderConfig.views != null && config.views != null) {
            throw new Error('Views passed but Views have already been configured.');
        }
        // set views, but make sure we do not override existing views with null/undefined.
        if (config.views != null) {
            this._meterProviderConfig.views = config.views;
        }
        // make sure we do not override existing reader with another reader.
        if (this._meterProviderConfig.reader != null && config.reader != null) {
            throw new Error('MetricReader passed but MetricReader has already been configured.');
        }
        // set reader, but make sure we do not override existing reader with null/undefined.
        if (config.reader != null) {
            this._meterProviderConfig.reader = config.reader;
        }
    }
    /**
     * @deprecated Resources are detected automatically on {@link NodeSDK.start()}, when the {@code autoDetectResources}
     * constructor option is set to {@code true} or left {@code undefined}.
     *
     * Detect resource attributes
     */
    detectResources() {
        if (this._disabled) {
            return;
        }
        const internalConfig = {
            detectors: this._resourceDetectors,
        };
        this.addResource((0, resources_1.detectResourcesSync)(internalConfig));
    }
    /**
     * @deprecated Please pre-merge resources and pass them to the constructor
     *
     * Manually add a Resource
     * @param resource
     */
    addResource(resource) {
        this._resource = this._resource.merge(resource);
    }
    /**
     * Call this method to construct SDK components and register them with the OpenTelemetry API.
     */
    start() {
        var _a, _b, _c, _d, _e;
        if (this._disabled) {
            return;
        }
        (0, instrumentation_1.registerInstrumentations)({
            instrumentations: this._instrumentations,
        });
        if (this._autoDetectResources) {
            this.detectResources();
        }
        this._resource =
            this._serviceName === undefined
                ? this._resource
                : this._resource.merge(new resources_1.Resource({
                    [semantic_conventions_1.SemanticResourceAttributes.SERVICE_NAME]: this._serviceName,
                }));
        const Provider = this._tracerProviderConfig
            ? sdk_trace_node_1.NodeTracerProvider
            : TracerProviderWithEnvExporter_1.TracerProviderWithEnvExporters;
        const tracerProvider = new Provider(Object.assign(Object.assign({}, (_a = this._tracerProviderConfig) === null || _a === void 0 ? void 0 : _a.tracerConfig), { resource: this._resource }));
        this._tracerProvider = tracerProvider;
        if (this._tracerProviderConfig) {
            tracerProvider.addSpanProcessor(this._tracerProviderConfig.spanProcessor);
        }
        tracerProvider.register({
            contextManager: (_b = this._tracerProviderConfig) === null || _b === void 0 ? void 0 : _b.contextManager,
            propagator: (_c = this._tracerProviderConfig) === null || _c === void 0 ? void 0 : _c.textMapPropagator,
        });
        if (this._loggerProviderConfig) {
            const loggerProvider = new sdk_logs_1.LoggerProvider({
                resource: this._resource,
            });
            loggerProvider.addLogRecordProcessor(this._loggerProviderConfig.logRecordProcessor);
            this._loggerProvider = loggerProvider;
            api_logs_1.logs.setGlobalLoggerProvider(loggerProvider);
        }
        if (this._meterProviderConfig) {
            const meterProvider = new sdk_metrics_1.MeterProvider({
                resource: this._resource,
                views: (_e = (_d = this._meterProviderConfig) === null || _d === void 0 ? void 0 : _d.views) !== null && _e !== void 0 ? _e : [],
            });
            if (this._meterProviderConfig.reader) {
                meterProvider.addMetricReader(this._meterProviderConfig.reader);
            }
            this._meterProvider = meterProvider;
            api_1.metrics.setGlobalMeterProvider(meterProvider);
            // TODO: This is a workaround to fix https://github.com/open-telemetry/opentelemetry-js/issues/3609
            // If the MeterProvider is not yet registered when instrumentations are registered, all metrics are dropped.
            // This code is obsolete once https://github.com/open-telemetry/opentelemetry-js/issues/3622 is implemented.
            for (const instrumentation of (0, utils_1.parseInstrumentationOptions)(this._instrumentations)) {
                instrumentation.setMeterProvider(api_1.metrics.getMeterProvider());
            }
        }
    }
    shutdown() {
        const promises = [];
        if (this._tracerProvider) {
            promises.push(this._tracerProvider.shutdown());
        }
        if (this._loggerProvider) {
            promises.push(this._loggerProvider.shutdown());
        }
        if (this._meterProvider) {
            promises.push(this._meterProvider.shutdown());
        }
        return (Promise.all(promises)
            // return void instead of the array from Promise.all
            .then(() => { }));
    }
}
exports.NodeSDK = NodeSDK;
//# sourceMappingURL=sdk.js.map