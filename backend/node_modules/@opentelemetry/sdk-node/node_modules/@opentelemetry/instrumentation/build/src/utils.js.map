{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH;;;;GAIG;AACH,SAAgB,sBAAsB,CACpC,OAAgB,EAChB,QAA+D,EAC/D,oBAA8B;IAE9B,IAAI,KAAwB,CAAC;IAC7B,IAAI,MAAqB,CAAC;IAC1B,IAAI;QACF,MAAM,GAAG,OAAO,EAAE,CAAC;KACpB;IAAC,OAAO,CAAC,EAAE;QACV,KAAK,GAAG,CAAC,CAAC;KACX;YAAS;QACR,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACxB,IAAI,KAAK,IAAI,CAAC,oBAAoB,EAAE;YAClC,6CAA6C;YAC7C,MAAM,KAAK,CAAC;SACb;QACD,6CAA6C;QAC7C,OAAO,MAAW,CAAC;KACpB;AACH,CAAC;AApBD,wDAoBC;AAED;;;;GAIG;AACI,KAAK,UAAU,2BAA2B,CAC/C,OAAgB,EAChB,QAA+D,EAC/D,oBAA8B;IAE9B,IAAI,KAAwB,CAAC;IAC7B,IAAI,MAAqB,CAAC;IAC1B,IAAI;QACF,MAAM,GAAG,MAAM,OAAO,EAAE,CAAC;KAC1B;IAAC,OAAO,CAAC,EAAE;QACV,KAAK,GAAG,CAAC,CAAC;KACX;YAAS;QACR,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACxB,IAAI,KAAK,IAAI,CAAC,oBAAoB,EAAE;YAClC,6CAA6C;YAC7C,MAAM,KAAK,CAAC;SACb;QACD,6CAA6C;QAC7C,OAAO,MAAW,CAAC;KACpB;AACH,CAAC;AApBD,kEAoBC;AACD;;;GAGG;AACH,SAAgB,SAAS,CAAC,IAAa;IACrC,OAAO,CACL,OAAO,IAAI,KAAK,UAAU;QAC1B,OAAQ,IAAoB,CAAC,UAAU,KAAK,UAAU;QACtD,OAAQ,IAAoB,CAAC,QAAQ,KAAK,UAAU;QACnD,IAAoB,CAAC,SAAS,KAAK,IAAI,CACzC,CAAC;AACJ,CAAC;AAPD,8BAOC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ShimWrapped } from './types';\n\n/**\n * function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nexport function safeExecuteInTheMiddle<T>(\n  execute: () => T,\n  onFinish: (e: Error | undefined, result: T | undefined) => void,\n  preventThrowingError?: boolean\n): T {\n  let error: Error | undefined;\n  let result: T | undefined;\n  try {\n    result = execute();\n  } catch (e) {\n    error = e;\n  } finally {\n    onFinish(error, result);\n    if (error && !preventThrowingError) {\n      // eslint-disable-next-line no-unsafe-finally\n      throw error;\n    }\n    // eslint-disable-next-line no-unsafe-finally\n    return result as T;\n  }\n}\n\n/**\n * Async function to execute patched function and being able to catch errors\n * @param execute - function to be executed\n * @param onFinish - callback to run when execute finishes\n */\nexport async function safeExecuteInTheMiddleAsync<T>(\n  execute: () => T,\n  onFinish: (e: Error | undefined, result: T | undefined) => void,\n  preventThrowingError?: boolean\n): Promise<T> {\n  let error: Error | undefined;\n  let result: T | undefined;\n  try {\n    result = await execute();\n  } catch (e) {\n    error = e;\n  } finally {\n    onFinish(error, result);\n    if (error && !preventThrowingError) {\n      // eslint-disable-next-line no-unsafe-finally\n      throw error;\n    }\n    // eslint-disable-next-line no-unsafe-finally\n    return result as T;\n  }\n}\n/**\n * Checks if certain function has been already wrapped\n * @param func\n */\nexport function isWrapped(func: unknown): func is ShimWrapped {\n  return (\n    typeof func === 'function' &&\n    typeof (func as ShimWrapped).__original === 'function' &&\n    typeof (func as ShimWrapped).__unwrap === 'function' &&\n    (func as ShimWrapped).__wrapped === true\n  );\n}\n"]}