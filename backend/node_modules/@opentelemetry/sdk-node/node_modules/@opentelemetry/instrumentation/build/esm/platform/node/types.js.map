{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface InstrumentationModuleFile<T> {\n  /** Name of file to be patched with relative path */\n  name: string;\n\n  moduleExports?: T;\n\n  /** Supported version this file */\n  supportedVersions: string[];\n\n  /** Method to patch the instrumentation  */\n  patch(moduleExports: T, moduleVersion?: string): T;\n\n  /** Method to patch the instrumentation  */\n\n  /** Method to unpatch the instrumentation  */\n  unpatch(moduleExports?: T, moduleVersion?: string): void;\n}\n\nexport interface InstrumentationModuleDefinition<T> {\n  /** Module name or path  */\n  name: string;\n\n  moduleExports?: T;\n\n  /** Instrumented module version */\n  moduleVersion?: string;\n\n  /** Supported version of module  */\n  supportedVersions: string[];\n\n  /** Module internal files to be patched  */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  files: InstrumentationModuleFile<any>[];\n\n  /** If set to true, the includePrerelease check will be included when calling semver.satisfies */\n  includePrerelease?: boolean;\n\n  /** Method to patch the instrumentation  */\n  patch?: (moduleExports: T, moduleVersion?: string) => T;\n\n  /** Method to unpatch the instrumentation  */\n  unpatch?: (moduleExports: T, moduleVersion?: string) => void;\n}\n"]}