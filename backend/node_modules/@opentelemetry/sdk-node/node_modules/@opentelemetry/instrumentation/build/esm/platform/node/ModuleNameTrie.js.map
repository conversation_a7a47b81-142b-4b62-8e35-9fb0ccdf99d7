{"version": 3, "file": "ModuleNameTrie.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/ModuleNameTrie.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,MAAM,CAAC,IAAM,mBAAmB,GAAG,GAAG,CAAC;AAEvC;;GAEG;AACH;IAAA;QACE,UAAK,GAAgD,EAAE,CAAC;QACxD,aAAQ,GAAoC,IAAI,GAAG,EAAE,CAAC;IACxD,CAAC;IAAD,yBAAC;AAAD,CAAC,AAHD,IAGC;AAaD;;GAEG;AACH;IAAA;QACU,UAAK,GAAuB,IAAI,kBAAkB,EAAE,CAAC;QACrD,aAAQ,GAAW,CAAC,CAAC;IAgE/B,CAAC;IA9DC;;;;OAIG;IACH,+BAAM,GAAN,UAAO,IAAY;;QACjB,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;;YAE1B,KAA6B,IAAA,KAAA,SAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA,gBAAA,4BAAE;gBAApE,IAAM,cAAc,WAAA;gBACvB,IAAI,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBACrD,IAAI,CAAC,QAAQ,EAAE;oBACb,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;oBACpC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;iBACjD;gBACD,QAAQ,GAAG,QAAQ,CAAC;aACrB;;;;;;;;;QACD,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;OAOG;IACH,+BAAM,GAAN,UACE,UAAkB,EAClB,EAAsE;;YAAtE,qBAAoE,EAAE,KAAA,EAApE,sBAAsB,4BAAA,EAAE,QAAQ,cAAA;QAElC,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC1B,IAAM,OAAO,GAAgC,EAAE,CAAC;QAChD,IAAI,SAAS,GAAG,IAAI,CAAC;;YAErB,KAA6B,IAAA,KAAA,SAAA,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA,gBAAA,4BAAE;gBAA/D,IAAM,cAAc,WAAA;gBACvB,IAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBACvD,IAAI,CAAC,QAAQ,EAAE;oBACb,SAAS,GAAG,KAAK,CAAC;oBAClB,MAAM;iBACP;gBACD,IAAI,CAAC,QAAQ,EAAE;oBACb,OAAO,CAAC,IAAI,OAAZ,OAAO,2BAAS,QAAQ,CAAC,KAAK,WAAE;iBACjC;gBACD,QAAQ,GAAG,QAAQ,CAAC;aACrB;;;;;;;;;QAED,IAAI,QAAQ,IAAI,SAAS,EAAE;YACzB,OAAO,CAAC,IAAI,OAAZ,OAAO,2BAAS,QAAQ,CAAC,KAAK,WAAE;SACjC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,EAAE,CAAC;SACX;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SAC1B;QACD,IAAI,sBAAsB,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,EAA3B,CAA2B,CAAC,CAAC;SACrD;QACD,OAAO,OAAO,CAAC,GAAG,CAAC,UAAC,EAAQ;gBAAN,IAAI,UAAA;YAAO,OAAA,IAAI;QAAJ,CAAI,CAAC,CAAC;IACzC,CAAC;IACH,qBAAC;AAAD,CAAC,AAlED,IAkEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Hooked } from './RequireInTheMiddleSingleton';\n\nexport const ModuleNameSeparator = '/';\n\n/**\n * Node in a `ModuleNameTrie`\n */\nclass ModuleNameTrieNode {\n  hooks: Array<{ hook: Hooked; insertedId: number }> = [];\n  children: Map<string, ModuleNameTrieNode> = new Map();\n}\n\ntype ModuleNameTrieSearchOptions = {\n  /**\n   * Whether to return the results in insertion order\n   */\n  maintainInsertionOrder?: boolean;\n  /**\n   * Whether to return only full matches\n   */\n  fullOnly?: boolean;\n};\n\n/**\n * Trie containing nodes that represent a part of a module name (i.e. the parts separated by forward slash)\n */\nexport class ModuleNameTrie {\n  private _trie: ModuleNameTrieNode = new ModuleNameTrieNode();\n  private _counter: number = 0;\n\n  /**\n   * Insert a module hook into the trie\n   *\n   * @param {Hooked} hook Hook\n   */\n  insert(hook: Hooked) {\n    let trieNode = this._trie;\n\n    for (const moduleNamePart of hook.moduleName.split(ModuleNameSeparator)) {\n      let nextNode = trieNode.children.get(moduleNamePart);\n      if (!nextNode) {\n        nextNode = new ModuleNameTrieNode();\n        trieNode.children.set(moduleNamePart, nextNode);\n      }\n      trieNode = nextNode;\n    }\n    trieNode.hooks.push({ hook, insertedId: this._counter++ });\n  }\n\n  /**\n   * Search for matching hooks in the trie\n   *\n   * @param {string} moduleName Module name\n   * @param {boolean} maintainInsertionOrder Whether to return the results in insertion order\n   * @param {boolean} fullOnly Whether to return only full matches\n   * @returns {Hooked[]} Matching hooks\n   */\n  search(\n    moduleName: string,\n    { maintainInsertionOrder, fullOnly }: ModuleNameTrieSearchOptions = {}\n  ): Hooked[] {\n    let trieNode = this._trie;\n    const results: ModuleNameTrieNode['hooks'] = [];\n    let foundFull = true;\n\n    for (const moduleNamePart of moduleName.split(ModuleNameSeparator)) {\n      const nextNode = trieNode.children.get(moduleNamePart);\n      if (!nextNode) {\n        foundFull = false;\n        break;\n      }\n      if (!fullOnly) {\n        results.push(...nextNode.hooks);\n      }\n      trieNode = nextNode;\n    }\n\n    if (fullOnly && foundFull) {\n      results.push(...trieNode.hooks);\n    }\n\n    if (results.length === 0) {\n      return [];\n    }\n    if (results.length === 1) {\n      return [results[0].hook];\n    }\n    if (maintainInsertionOrder) {\n      results.sort((a, b) => a.insertedId - b.insertedId);\n    }\n    return results.map(({ hook }) => hook);\n  }\n}\n"]}