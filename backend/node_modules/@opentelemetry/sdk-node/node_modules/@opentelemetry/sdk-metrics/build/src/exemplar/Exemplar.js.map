{"version": 3, "file": "Exemplar.js", "sourceRoot": "", "sources": ["../../../src/exemplar/Exemplar.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime, MetricAttributes } from '@opentelemetry/api';\n\n/**\n * A representation of an exemplar, which is a sample input measurement.\n * Exemplars also hold information about the environment when the measurement\n * was recorded, for example the span and trace ID of the active span when the\n * exemplar was recorded.\n */\nexport type Exemplar = {\n  // The set of key/value pairs that were filtered out by the aggregator, but\n  // recorded alongside the original measurement. Only key/value pairs that were\n  // filtered out by the aggregator should be included\n  filteredAttributes: MetricAttributes;\n\n  // The value of the measurement that was recorded.\n  value: number;\n\n  // timestamp is the exact time when this exemplar was recorded\n  timestamp: HrTime;\n\n  // (Optional) Span ID of the exemplar trace.\n  // span_id may be missing if the measurement is not recorded inside a trace\n  // or if the trace is not sampled.\n  spanId?: string;\n\n  // (Optional) Trace ID of the exemplar trace.\n  // trace_id may be missing if the measurement is not recorded inside a trace\n  // or if the trace is not sampled.\n  traceId?: string;\n};\n"]}