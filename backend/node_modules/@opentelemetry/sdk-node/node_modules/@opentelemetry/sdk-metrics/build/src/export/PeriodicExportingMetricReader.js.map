{"version": 3, "file": "PeriodicExportingMetricReader.js", "sourceRoot": "", "sources": ["../../../src/export/PeriodicExportingMetricReader.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAC1C,8CAK6B;AAC7B,iDAA8C;AAE9C,oCAAyD;AACzD,4CAA0C;AA0B1C;;;GAGG;AACH,MAAa,6BAA8B,SAAQ,2BAAY;IAM7D,YAAY,OAA6C;;QACvD,KAAK,CAAC;YACJ,mBAAmB,EAAE,MAAA,OAAO,CAAC,QAAQ,CAAC,iBAAiB,0CAAE,IAAI,CAC3D,OAAO,CAAC,QAAQ,CACjB;YACD,8BAA8B,EAC5B,MAAA,OAAO,CAAC,QAAQ,CAAC,4BAA4B,0CAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;YACvE,eAAe,EAAE,OAAO,CAAC,eAAe;SACzC,CAAC,CAAC;QAEH,IACE,OAAO,CAAC,oBAAoB,KAAK,SAAS;YAC1C,OAAO,CAAC,oBAAoB,IAAI,CAAC,EACjC;YACA,MAAM,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAC5D;QAED,IACE,OAAO,CAAC,mBAAmB,KAAK,SAAS;YACzC,OAAO,CAAC,mBAAmB,IAAI,CAAC,EAChC;YACA,MAAM,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC3D;QAED,IACE,OAAO,CAAC,mBAAmB,KAAK,SAAS;YACzC,OAAO,CAAC,oBAAoB,KAAK,SAAS;YAC1C,OAAO,CAAC,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,EAC1D;YACA,MAAM,KAAK,CACT,2EAA2E,CAC5E,CAAC;SACH;QAED,IAAI,CAAC,eAAe,GAAG,MAAA,OAAO,CAAC,oBAAoB,mCAAI,KAAK,CAAC;QAC7D,IAAI,CAAC,cAAc,GAAG,MAAA,OAAO,CAAC,mBAAmB,mCAAI,KAAK,CAAC;QAC3D,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;IACpC,CAAC;IAEO,KAAK,CAAC,QAAQ;QACpB,IAAI;YACF,MAAM,IAAA,uBAAe,EAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;SAC3D;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,GAAG,YAAY,oBAAY,EAAE;gBAC/B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,wDAAwD,EACxD,IAAI,CAAC,cAAc,CACpB,CAAC;gBACF,OAAO;aACR;YAED,IAAA,yBAAkB,EAAC,GAAG,CAAC,CAAC;SACzB;IACH,CAAC;IAEO,KAAK,CAAC,MAAM;;QAClB,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;YACrD,aAAa,EAAE,IAAI,CAAC,cAAc;SACnC,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,0DAA0D,EAC1D,GAAG,MAAM,CACV,CAAC;SACH;QAED,MAAM,QAAQ,GAAG,KAAK,IAAI,EAAE;YAC1B,MAAM,MAAM,GAAG,MAAM,eAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;YACvE,IAAI,MAAM,CAAC,IAAI,KAAK,uBAAgB,CAAC,OAAO,EAAE;gBAC5C,MAAM,IAAI,KAAK,CACb,+DAA+D,MAAM,CAAC,KAAK,GAAG,CAC/E,CAAC;aACH;QACH,CAAC,CAAC;QAEF,sFAAsF;QACtF,IAAI,eAAe,CAAC,QAAQ,CAAC,sBAAsB,EAAE;YACnD,MAAA,MAAA,eAAe,CAAC,QAAQ,EACrB,sBAAsB,mDACtB,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CACpB,UAAI,CAAC,KAAK,CAAC,mDAAmD,EAAE,GAAG,CAAC,CACrE,CAAC;SACL;aAAM;YACL,MAAM,QAAQ,EAAE,CAAC;SAClB;IACH,CAAC;IAEkB,aAAa;QAC9B,iGAAiG;QACjG,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,wGAAwG;YACxG,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvB,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACzB,IAAA,iBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IAES,KAAK,CAAC,YAAY;QAC1B,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;IACpC,CAAC;IAES,KAAK,CAAC,UAAU;QACxB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC/B;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;CACF;AAnHD,sEAmHC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  internal,\n  ExportResultCode,\n  globalErrorHandler,\n  unrefTimer,\n} from '@opentelemetry/core';\nimport { MetricReader } from './MetricReader';\nimport { PushMetricExporter } from './MetricExporter';\nimport { callWithTimeout, TimeoutError } from '../utils';\nimport { diag } from '@opentelemetry/api';\nimport { MetricProducer } from './MetricProducer';\n\nexport type PeriodicExportingMetricReaderOptions = {\n  /**\n   * The backing exporter for the metric reader.\n   */\n  exporter: PushMetricExporter;\n  /**\n   * An internal milliseconds for the metric reader to initiate metric\n   * collection.\n   */\n  exportIntervalMillis?: number;\n  /**\n   * Milliseconds for the async observable callback to timeout.\n   */\n  exportTimeoutMillis?: number;\n  /**\n   * **Note, this option is experimental**. Additional MetricProducers to use as a source of\n   * aggregated metric data in addition to the SDK's metric data. The resource returned by\n   * these MetricProducers is ignored; the SDK's resource will be used instead.\n   * @experimental\n   */\n  metricProducers?: MetricProducer[];\n};\n\n/**\n * {@link MetricReader} which collects metrics based on a user-configurable time interval, and passes the metrics to\n * the configured {@link PushMetricExporter}\n */\nexport class PeriodicExportingMetricReader extends MetricReader {\n  private _interval?: ReturnType<typeof setInterval>;\n  private _exporter: PushMetricExporter;\n  private readonly _exportInterval: number;\n  private readonly _exportTimeout: number;\n\n  constructor(options: PeriodicExportingMetricReaderOptions) {\n    super({\n      aggregationSelector: options.exporter.selectAggregation?.bind(\n        options.exporter\n      ),\n      aggregationTemporalitySelector:\n        options.exporter.selectAggregationTemporality?.bind(options.exporter),\n      metricProducers: options.metricProducers,\n    });\n\n    if (\n      options.exportIntervalMillis !== undefined &&\n      options.exportIntervalMillis <= 0\n    ) {\n      throw Error('exportIntervalMillis must be greater than 0');\n    }\n\n    if (\n      options.exportTimeoutMillis !== undefined &&\n      options.exportTimeoutMillis <= 0\n    ) {\n      throw Error('exportTimeoutMillis must be greater than 0');\n    }\n\n    if (\n      options.exportTimeoutMillis !== undefined &&\n      options.exportIntervalMillis !== undefined &&\n      options.exportIntervalMillis < options.exportTimeoutMillis\n    ) {\n      throw Error(\n        'exportIntervalMillis must be greater than or equal to exportTimeoutMillis'\n      );\n    }\n\n    this._exportInterval = options.exportIntervalMillis ?? 60000;\n    this._exportTimeout = options.exportTimeoutMillis ?? 30000;\n    this._exporter = options.exporter;\n  }\n\n  private async _runOnce(): Promise<void> {\n    try {\n      await callWithTimeout(this._doRun(), this._exportTimeout);\n    } catch (err) {\n      if (err instanceof TimeoutError) {\n        api.diag.error(\n          'Export took longer than %s milliseconds and timed out.',\n          this._exportTimeout\n        );\n        return;\n      }\n\n      globalErrorHandler(err);\n    }\n  }\n\n  private async _doRun(): Promise<void> {\n    const { resourceMetrics, errors } = await this.collect({\n      timeoutMillis: this._exportTimeout,\n    });\n\n    if (errors.length > 0) {\n      api.diag.error(\n        'PeriodicExportingMetricReader: metrics collection errors',\n        ...errors\n      );\n    }\n\n    const doExport = async () => {\n      const result = await internal._export(this._exporter, resourceMetrics);\n      if (result.code !== ExportResultCode.SUCCESS) {\n        throw new Error(\n          `PeriodicExportingMetricReader: metrics export failed (error ${result.error})`\n        );\n      }\n    };\n\n    // Avoid scheduling a promise to make the behavior more predictable and easier to test\n    if (resourceMetrics.resource.asyncAttributesPending) {\n      resourceMetrics.resource\n        .waitForAsyncAttributes?.()\n        .then(doExport, err =>\n          diag.debug('Error while resolving async portion of resource: ', err)\n        );\n    } else {\n      await doExport();\n    }\n  }\n\n  protected override onInitialized(): void {\n    // start running the interval as soon as this reader is initialized and keep handle for shutdown.\n    this._interval = setInterval(() => {\n      // this._runOnce never rejects. Using void operator to suppress @typescript-eslint/no-floating-promises.\n      void this._runOnce();\n    }, this._exportInterval);\n    unrefTimer(this._interval);\n  }\n\n  protected async onForceFlush(): Promise<void> {\n    await this._runOnce();\n    await this._exporter.forceFlush();\n  }\n\n  protected async onShutdown(): Promise<void> {\n    if (this._interval) {\n      clearInterval(this._interval);\n    }\n\n    await this._exporter.shutdown();\n  }\n}\n"]}