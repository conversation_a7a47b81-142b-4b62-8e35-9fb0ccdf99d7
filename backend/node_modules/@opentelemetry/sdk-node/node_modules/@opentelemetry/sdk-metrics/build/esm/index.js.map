{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAgBH,OAAO,EAAE,sBAAsB,EAAE,MAAM,iCAAiC,CAAC;AAEzE,OAAO,EAEL,aAAa,GAUd,MAAM,qBAAqB,CAAC;AAI7B,OAAO,EAAE,YAAY,EAAuB,MAAM,uBAAuB,CAAC;AAE1E,OAAO,EACL,6BAA6B,GAE9B,MAAM,wCAAwC,CAAC;AAEhD,OAAO,EAAE,sBAAsB,EAAE,MAAM,iCAAiC,CAAC;AAEzE,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AAIvE,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAMxD,OAAO,EAAE,aAAa,EAAwB,MAAM,iBAAiB,CAAC;AAEtE,OAAO,EACL,kBAAkB,EAClB,kCAAkC,EAClC,+BAA+B,EAC/B,eAAe,EACf,oBAAoB,EACpB,oBAAoB,EACpB,cAAc,EACd,WAAW,GACZ,MAAM,oBAAoB,CAAC;AAE5B,OAAO,EAAE,IAAI,EAAe,MAAM,aAAa,CAAC;AAEhD,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricDescriptor } from './export/MetricData';\n\nexport {\n  Sum,\n  LastValue,\n  Histogram,\n  ExponentialHistogram,\n} from './aggregator/types';\n\nexport {\n  AggregationSelector,\n  AggregationTemporalitySelector,\n} from './export/AggregationSelector';\n\nexport { AggregationTemporality } from './export/AggregationTemporality';\n\nexport {\n  DataPoint,\n  DataPointType,\n  SumMetricData,\n  GaugeMetricData,\n  HistogramMetricData,\n  ExponentialHistogramMetricData,\n  ResourceMetrics,\n  ScopeMetrics,\n  MetricData,\n  MetricDescriptor,\n  CollectionResult,\n} from './export/MetricData';\n\nexport { PushMetricExporter } from './export/MetricExporter';\n\nexport { MetricReader, MetricReaderOptions } from './export/MetricReader';\n\nexport {\n  PeriodicExportingMetricReader,\n  PeriodicExportingMetricReaderOptions,\n} from './export/PeriodicExportingMetricReader';\n\nexport { InMemoryMetricExporter } from './export/InMemoryMetricExporter';\n\nexport { ConsoleMetricExporter } from './export/ConsoleMetricExporter';\n\nexport { MetricCollectOptions, MetricProducer } from './export/MetricProducer';\n\nexport { InstrumentType } from './InstrumentDescriptor';\n/**\n * @deprecated Use {@link MetricDescriptor} instead.\n */\nexport type InstrumentDescriptor = MetricDescriptor;\n\nexport { MeterProvider, MeterProviderOptions } from './MeterProvider';\n\nexport {\n  DefaultAggregation,\n  ExplicitBucketHistogramAggregation,\n  ExponentialHistogramAggregation,\n  DropAggregation,\n  HistogramAggregation,\n  LastValueAggregation,\n  SumAggregation,\n  Aggregation,\n} from './view/Aggregation';\n\nexport { View, ViewOptions } from './view/View';\n\nexport { TimeoutError } from './utils';\n"]}