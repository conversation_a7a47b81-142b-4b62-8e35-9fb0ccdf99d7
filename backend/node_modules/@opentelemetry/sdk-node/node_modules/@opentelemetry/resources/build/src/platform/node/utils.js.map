{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/utils.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACI,MAAM,aAAa,GAAG,CAAC,cAAsB,EAAU,EAAE;IAC9D,0EAA0E;IAC1E,8HAA8H;IAC9H,QAAQ,cAAc,EAAE;QACtB,KAAK,KAAK;YACR,OAAO,OAAO,CAAC;QACjB,KAAK,KAAK;YACR,OAAO,OAAO,CAAC;QACjB,KAAK,KAAK;YACR,OAAO,OAAO,CAAC;QACjB;YACE,OAAO,cAAc,CAAC;KACzB;AACH,CAAC,CAAC;AAbW,QAAA,aAAa,iBAaxB;AAEK,MAAM,aAAa,GAAG,CAAC,YAAoB,EAAU,EAAE;IAC5D,8EAA8E;IAC9E,4HAA4H;IAC5H,QAAQ,YAAY,EAAE;QACpB,KAAK,OAAO;YACV,OAAO,SAAS,CAAC;QACnB,KAAK,OAAO;YACV,OAAO,SAAS,CAAC;QACnB;YACE,OAAO,YAAY,CAAC;KACvB;AACH,CAAC,CAAC;AAXW,QAAA,aAAa,iBAWxB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport const normalizeArch = (nodeArchString: string): string => {\n  // Maps from https://nodejs.org/api/os.html#osarch to arch values in spec:\n  // https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/resource/semantic_conventions/host.md\n  switch (nodeArchString) {\n    case 'arm':\n      return 'arm32';\n    case 'ppc':\n      return 'ppc32';\n    case 'x64':\n      return 'amd64';\n    default:\n      return nodeArchString;\n  }\n};\n\nexport const normalizeType = (nodePlatform: string): string => {\n  // Maps from https://nodejs.org/api/os.html#osplatform to arch values in spec:\n  // https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/resource/semantic_conventions/os.md\n  switch (nodePlatform) {\n    case 'sunos':\n      return 'solaris';\n    case 'win32':\n      return 'windows';\n    default:\n      return nodePlatform;\n  }\n};\n"]}