/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export * from './baggage/propagation/W3CBaggagePropagator';
export * from './common/anchored-clock';
export * from './common/attributes';
export * from './common/global-error-handler';
export * from './common/logging-error-handler';
export * from './common/time';
export * from './common/types';
export * from './ExportResult';
export * as baggageUtils from './baggage/utils';
export * from './platform';
export * from './propagation/composite';
export * from './trace/W3CTraceContextPropagator';
export * from './trace/IdGenerator';
export * from './trace/rpc-metadata';
export * from './trace/sampler/AlwaysOffSampler';
export * from './trace/sampler/AlwaysOnSampler';
export * from './trace/sampler/ParentBasedSampler';
export * from './trace/sampler/TraceIdRatioBasedSampler';
export * from './trace/suppress-tracing';
export * from './trace/TraceState';
export * from './utils/environment';
export * from './utils/merge';
export * from './utils/sampling';
export * from './utils/timeout';
export * from './utils/url';
export * from './utils/wrap';
export * from './utils/callback';
export * from './version';
import { _export } from './internal/exporter';
export const internal = {
    _export,
};
//# sourceMappingURL=index.js.map