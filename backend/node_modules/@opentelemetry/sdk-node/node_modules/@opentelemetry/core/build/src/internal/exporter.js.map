{"version": 3, "file": "exporter.js", "sourceRoot": "", "sources": ["../../../src/internal/exporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA6C;AAE7C,gEAA4D;AAM5D;;;GAGG;AACH,SAAgB,OAAO,CACrB,QAAqB,EACrB,GAAM;IAEN,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAC3B,0DAA0D;QAC1D,aAAO,CAAC,IAAI,CAAC,IAAA,kCAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE;YACnD,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,MAAoB,EAAE,EAAE;gBAC5C,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAZD,0BAYC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context } from '@opentelemetry/api';\nimport { ExportResult } from '../ExportResult';\nimport { suppressTracing } from '../trace/suppress-tracing';\n\nexport interface Exporter<T> {\n  export(arg: T, resultCallback: (result: ExportResult) => void): void;\n}\n\n/**\n * @internal\n * Shared functionality used by Exporters while exporting data, including suppresion of Traces.\n */\nexport function _export<T>(\n  exporter: Exporter<T>,\n  arg: T\n): Promise<ExportResult> {\n  return new Promise(resolve => {\n    // prevent downstream exporter calls from generating spans\n    context.with(suppressTracing(context.active()), () => {\n      exporter.export(arg, (result: ExportResult) => {\n        resolve(result);\n      });\n    });\n  });\n}\n"]}