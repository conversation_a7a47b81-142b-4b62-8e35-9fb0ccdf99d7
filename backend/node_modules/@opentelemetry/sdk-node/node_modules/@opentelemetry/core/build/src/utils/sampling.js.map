{"version": 3, "file": "sampling.js", "sourceRoot": "", "sources": ["../../../src/utils/sampling.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,IAAY,mBAOX;AAPD,WAAY,mBAAmB;IAC7B,+CAAwB,CAAA;IACxB,6CAAsB,CAAA;IACtB,sEAA+C,CAAA;IAC/C,oEAA6C,CAAA;IAC7C,2EAAoD,CAAA;IACpD,oDAA6B,CAAA;AAC/B,CAAC,EAPW,mBAAmB,GAAnB,2BAAmB,KAAnB,2BAAmB,QAO9B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum TracesSamplerValues {\n  AlwaysOff = 'always_off',\n  AlwaysOn = 'always_on',\n  ParentBasedAlwaysOff = 'parentbased_always_off',\n  ParentBasedAlwaysOn = 'parentbased_always_on',\n  ParentBasedTraceIdRatio = 'parentbased_traceidratio',\n  TraceIdRatio = 'traceidratio',\n}\n"]}