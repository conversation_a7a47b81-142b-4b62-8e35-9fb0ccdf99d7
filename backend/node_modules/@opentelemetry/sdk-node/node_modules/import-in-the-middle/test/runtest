#!/usr/bin/env node

// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.
//
// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.

const { spawn } = require('child_process')
const path = require('path')
const url = require('url')

process.env.NODE_NO_WARNINGS = 1

const filename = process.argv[2]
const args = [
  '--unhandled-rejections=strict',
  ...process.argv.slice(2)
]

const [processMajor] = process.versions.node.split('.').map(Number)

const match = filename.match(/v([0-9]+)/)

const versionRequirement = match ? match[1] : 0;

if (processMajor < versionRequirement) {
  console.log(`skipping ${filename} as this is Node.js v${processMajor} and test wants v${versionRequirement}`);
  process.exit(0);
}

if (!filename.includes('disabled')) {
  const isTypescript = path.extname(filename).slice(-2) === 'ts'
  const loaderPath = isTypescript
    ? path.join(__dirname, 'typescript', 'iitm-ts-node-loader.mjs')
    : path.join(__dirname, '..', 'hook.mjs')

  args.unshift(`--experimental-loader=${url.pathToFileURL(loaderPath)}`)
}

spawn('node', args, { stdio: 'inherit' }).on('close', code => {
  process.exitCode = code
})
