{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,+BAAgC;AAChC,4CAA8E;AAC9E,oEAIwC;AAExC,uCAAoC;AACpC,2EAAwE;AAGxE,MAAM,cAAc,GAAgC;IAClD,iBAAiB,EAAE,KAAK;IACxB,qBAAqB,EAAE,KAAK;CAC7B,CAAC;AAEF,MAAa,qBAAsB,SAAQ,qCAE1C;IACC,YAAY,SAAsC,EAAE;QAClD,KAAK,CACH,uCAAuC,EACvC,iBAAO,EACP,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,CAC1C,CAAC;IACJ,CAAC;IAES,IAAI;QACZ,OAAO;YACL,IAAI,qDAAmC,CACrC,QAAQ,EACR,CAAC,MAAM,CAAC,EACR,CAAC,MAAW,EAAE,aAAa,EAAE,EAAE;gBAC7B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,6BAA6B,aAAa,EAAE,CAAC,CAAC;gBAC/D,MAAM,eAAe,GAAG,IAAI,CAAC;gBAC7B,MAAM,MAAM,GACV,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ;oBACrC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;oBACvB,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW;gBAEzB,IAAI,CAAC,KAAK,CACR,MAAM,CAAC,SAAS,EAChB,OAAO;gBACP,8DAA8D;gBAC9D,IAAI,CAAC,eAAe,EAAS,CAC9B,CAAC;gBAEF,SAAS,YAAY,CAAY,GAAG,IAAe;oBACjD,IAAI,IAAI,CAAC;oBACT,IAAI,MAAM,GAAG,SAAS,CAAC;oBACvB,IAAI,IAAI,YAAY,YAAY,EAAE;wBAChC,6BAA6B;wBAC7B,IAAI,GAAG,IAAI,CAAC;wBACZ,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;qBAC1B;yBAAM;wBACL,uBAAuB;wBACvB,IAAI,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;wBACvB,MAAM,GAAG,IAAI,CAAC;qBACf;oBACD,+DAA+D;oBAC/D,8CAA8C;oBAC9C,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,mBAAmB,KAAK,SAAS,EAAE;wBAC7C,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;qBAClC;oBACD,OAAO,MAAM,CAAC;gBAChB,CAAC;gBACD,4DAA4D;gBAC5D,mDAAmD;gBACnD,uDAAuD;gBACvD,IAAA,eAAQ,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAE/B,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAE3D,IAAI,CAAC,KAAK,CACR,cAAc,EACd,cAAc;gBACd,8DAA8D;gBAC9D,IAAI,CAAC,uBAAuB,EAAS,CACtC,CAAC;gBAEF,OAAO,cAAc,CAAC;YACxB,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,SAAS,CAAC,MAAmC;QACpD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAEO,eAAe;QACrB,OAAO,CAAC,QAAsC,EAAE,EAAE;YAChD,MAAM,eAAe,GAAG,IAAI,CAAC;YAC7B,OAAO,SAAS,WAAW,CAAqB,GAAG,IAAe;gBAChE,MAAM,MAAM,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;gBAC3C,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,MAAM,CAAC,qBAAqB,EAAE;oBAChE,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBACnC;gBAED,MAAM,IAAI,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC7C,IAAI,CAAC,IAAI,EAAE;oBACT,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBACnC;gBAED,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBACvC,IAAI,CAAC,IAAA,wBAAkB,EAAC,WAAW,CAAC,EAAE;oBACpC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBACnC;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAA2B,CAAC;gBACjD,MAAM,CAAC,UAAU,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;gBACzC,MAAM,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;gBACvC,MAAM,CAAC,aAAa,CAAC,GAAG,IAAI,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;gBAElE,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAExC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,uBAAuB;QAC7B,OAAO,CAAC,QAAsC,EAAE,EAAE;YAChD,MAAM,eAAe,GAAG,IAAI,CAAC;YAC7B,OAAO,SAAS,mBAAmB,CAAC,GAAG,IAAe;gBACpD,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;gBACjC,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACnC,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,UAAU,CAAC,MAAW;QAC5B,MAAM,MAAM,GAAgC,IAAI,CAAC,SAAS,EAAE,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,MAAM,CAAC,iBAAiB,EAAE;YACjD,OAAO;SACR;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAC/D,MAAM,CAAC,SAAS,CAAC;YACf,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,IAAI,qDAAyB,EAAE;YACvC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,IAAU,EAAE,MAA8B;QAC1D,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC;QAEtC,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;YAC9B,OAAO;SACR;QAED,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EACxB,GAAG,CAAC,EAAE;YACJ,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;aAChD;QACH,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;CACF;AAtJD,sDAsJC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { inherits } from 'util';\nimport { context, trace, isSpanContextValid, Span } from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { BunyanInstrumentationConfig } from './types';\nimport { VERSION } from './version';\nimport { OpenTelemetryBunyanStream } from './OpenTelemetryBunyanStream';\nimport type * as BunyanLogger from 'bunyan';\n\nconst DEFAULT_CONFIG: BunyanInstrumentationConfig = {\n  disableLogSending: false,\n  disableLogCorrelation: false,\n};\n\nexport class BunyanInstrumentation extends InstrumentationBase<\n  typeof BunyanLogger\n> {\n  constructor(config: BunyanInstrumentationConfig = {}) {\n    super(\n      '@opentelemetry/instrumentation-bunyan',\n      VERSION,\n      Object.assign({}, DEFAULT_CONFIG, config)\n    );\n  }\n\n  protected init() {\n    return [\n      new InstrumentationNodeModuleDefinition<typeof BunyanLogger>(\n        'bunyan',\n        ['<2.0'],\n        (module: any, moduleVersion) => {\n          this._diag.debug(`Applying patch for bunyan@${moduleVersion}`);\n          const instrumentation = this;\n          const Logger =\n            module[Symbol.toStringTag] === 'Module'\n              ? module.default // ESM\n              : module; // CommonJS\n\n          this._wrap(\n            Logger.prototype,\n            '_emit',\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this._getPatchedEmit() as any\n          );\n\n          function LoggerTraced(this: any, ...args: unknown[]) {\n            let inst;\n            let retval = undefined;\n            if (this instanceof LoggerTraced) {\n              // called with `new Logger()`\n              inst = this;\n              Logger.apply(this, args);\n            } else {\n              // called without `new`\n              inst = Logger(...args);\n              retval = inst;\n            }\n            // If `_childOptions` is defined, this is a `Logger#child(...)`\n            // call. We must not add an OTel stream again.\n            if (args[1] /* _childOptions */ === undefined) {\n              instrumentation._addStream(inst);\n            }\n            return retval;\n          }\n          // Must use the deprecated `inherits` to support this style:\n          //    const log = require('bunyan')({name: 'foo'});\n          // i.e. calling the constructor function without `new`.\n          inherits(LoggerTraced, Logger);\n\n          const patchedExports = Object.assign(LoggerTraced, Logger);\n\n          this._wrap(\n            patchedExports,\n            'createLogger',\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this._getPatchedCreateLogger() as any\n          );\n\n          return patchedExports;\n        }\n      ),\n    ];\n  }\n\n  override getConfig(): BunyanInstrumentationConfig {\n    return this._config;\n  }\n\n  override setConfig(config: BunyanInstrumentationConfig) {\n    this._config = Object.assign({}, DEFAULT_CONFIG, config);\n  }\n\n  private _getPatchedEmit() {\n    return (original: (...args: unknown[]) => void) => {\n      const instrumentation = this;\n      return function patchedEmit(this: BunyanLogger, ...args: unknown[]) {\n        const config = instrumentation.getConfig();\n        if (!instrumentation.isEnabled() || config.disableLogCorrelation) {\n          return original.apply(this, args);\n        }\n\n        const span = trace.getSpan(context.active());\n        if (!span) {\n          return original.apply(this, args);\n        }\n\n        const spanContext = span.spanContext();\n        if (!isSpanContextValid(spanContext)) {\n          return original.apply(this, args);\n        }\n\n        const record = args[0] as Record<string, string>;\n        record['trace_id'] = spanContext.traceId;\n        record['span_id'] = spanContext.spanId;\n        record['trace_flags'] = `0${spanContext.traceFlags.toString(16)}`;\n\n        instrumentation._callHook(span, record);\n\n        return original.apply(this, args);\n      };\n    };\n  }\n\n  private _getPatchedCreateLogger() {\n    return (original: (...args: unknown[]) => void) => {\n      const instrumentation = this;\n      return function patchedCreateLogger(...args: unknown[]) {\n        const logger = original(...args);\n        instrumentation._addStream(logger);\n        return logger;\n      };\n    };\n  }\n\n  private _addStream(logger: any) {\n    const config: BunyanInstrumentationConfig = this.getConfig();\n    if (!this.isEnabled() || config.disableLogSending) {\n      return;\n    }\n    this._diag.debug('Adding OpenTelemetryBunyanStream to logger');\n    logger.addStream({\n      type: 'raw',\n      stream: new OpenTelemetryBunyanStream(),\n      level: logger.level(),\n    });\n  }\n\n  private _callHook(span: Span, record: Record<string, string>) {\n    const hook = this.getConfig().logHook;\n\n    if (typeof hook !== 'function') {\n      return;\n    }\n\n    safeExecuteInTheMiddle(\n      () => hook(span, record),\n      err => {\n        if (err) {\n          this._diag.error('error calling logHook', err);\n        }\n      },\n      true\n    );\n  }\n}\n"]}