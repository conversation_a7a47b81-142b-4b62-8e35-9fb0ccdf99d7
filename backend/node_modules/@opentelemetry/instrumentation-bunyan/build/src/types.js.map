{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type LogHookFunction = (span: Span, record: Record<string, any>) => void;\n\nexport interface BunyanInstrumentationConfig extends InstrumentationConfig {\n  /**\n   * Whether to disable the automatic sending of log records to the\n   * OpenTelemetry Logs SDK.\n   * @default false\n   */\n  disableLogSending?: boolean;\n\n  /**\n   * Whether to disable the injection trace-context fields, and possibly other\n   * fields from `logHook()`, into log records for log correlation.\n   * @default false\n   */\n  disableLogCorrelation?: boolean;\n\n  /**\n   * A function that allows injecting additional fields in log records. It is\n   * called, as `logHook(span, record)`, for each log record emitted in a valid\n   * span context. It requires `disableLogCorrelation` to be false.\n   */\n  logHook?: LogHookFunction;\n}\n"]}