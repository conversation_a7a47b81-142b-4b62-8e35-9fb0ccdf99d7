import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import { BunyanInstrumentationConfig } from './types';
import type * as BunyanLogger from 'bunyan';
export declare class BunyanInstrumentation extends InstrumentationBase<typeof BunyanLogger> {
    constructor(config?: BunyanInstrumentationConfig);
    protected init(): InstrumentationNodeModuleDefinition<typeof BunyanLogger>[];
    getConfig(): BunyanInstrumentationConfig;
    setConfig(config: BunyanInstrumentationConfig): void;
    private _getPatchedEmit;
    private _getPatchedCreateLogger;
    private _addStream;
    private _callHook;
}
//# sourceMappingURL=instrumentation.d.ts.map