{"version": 3, "file": "socket.io.js", "sourceRoot": "", "sources": ["../../src/socket.io.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAM4B;AAC5B,oEAMwC;AACxC,8EAI6C;AAE7C,qDAAqE;AACrE,uCAAoC;AACpC,mCAIiB;AAEjB,MAAM,cAAc,GAAG;IACrB,SAAS;IACT,eAAe;IACf,YAAY;IACZ,eAAe;IACf,aAAa;IACb,gBAAgB;CACjB,CAAC;AAEF,MAAa,uBAAwB,SAAQ,qCAAwB;IAGnE,YAAY,SAAwC,EAAE;QACpD,KAAK,CACH,0CAA0C,EAC1C,iBAAO,EACP,IAAA,uBAAe,EAAC,MAAM,CAAC,CACxB,CAAC;IACJ,CAAC;IAES,IAAI;QACZ,MAAM,qBAAqB,GAAG,IAAI,+CAA6B,CAC7D,0BAA0B,EAC1B,CAAC,QAAQ,CAAC,EACV,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;;YAC/B,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;gBACzD,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC/B,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,+BAA+B,aAAa,SAAS,CAAC,CAAC;YACxE,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,0CAAE,SAAS,0CAAE,EAAE,CAAC,EAAE;gBACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;aACpD;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,IAAI,EACJ,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAC7B,CAAC;YACF,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,0CAAE,SAAS,0CAAE,IAAI,CAAC,EAAE;gBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACtD;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,MAAM,EACN,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAC/B,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;;YACd,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,0CAAE,SAAS,0CAAE,EAAE,CAAC,EAAE;gBACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;aACpD;YACD,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,0CAAE,SAAS,0CAAE,IAAI,CAAC,EAAE;gBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACtD;YACD,OAAO,aAAa,CAAC;QACvB,CAAC,CACF,CAAC;QAEF,MAAM,gCAAgC,GACpC,IAAI,+CAA6B,CAC/B,sCAAsC,EACtC,CAAC,QAAQ,CAAC,EACV,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;;YAC/B,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;gBACzD,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC/B,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,+BAA+B,aAAa,qBAAqB,CAClE,CAAC;YACF,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,iBAAiB,0CAAE,SAAS,0CAAE,IAAI,CAAC,EAAE;gBAChE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACjE;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,iBAAiB,CAAC,SAAS,EACzC,MAAM,EACN,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAC/B,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;;YACd,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,iBAAiB,0CAAE,SAAS,0CAAE,IAAI,CAAC,EAAE;gBAChE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACjE;YACD,OAAO,aAAa,CAAC;QACvB,CAAC,CACF,CAAC;QACJ,MAAM,wBAAwB,GAAG,IAAI,+CAA6B,CAChE,6BAA6B,EAC7B,CAAC,IAAI,CAAC,EACN,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;;YAC/B,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;gBACzD,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC/B,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,+BAA+B,aAAa,YAAY,CACzD,CAAC;YACF,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,SAAS,0CAAE,SAAS,0CAAE,IAAI,CAAC,EAAE;gBACxD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACzD;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,CAAC,SAAS,EACjC,MAAM,EACN,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAC/B,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;;YACd,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,SAAS,0CAAE,SAAS,0CAAE,IAAI,CAAC,EAAE;gBACxD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACzD;QACH,CAAC,CACF,CAAC;QACF,MAAM,2BAA2B,GAAG,IAAI,+CAA6B,CACnE,yBAAyB,EACzB,CAAC,GAAG,CAAC,EACL,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;;YAC/B,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;gBACzD,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC/B,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,+BAA+B,aAAa,SAAS,CAAC,CAAC;YACxE,IAAI,IAAA,2BAAS,EAAC,MAAA,aAAa,CAAC,SAAS,0CAAE,EAAE,CAAC,EAAE;gBAC1C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;aAC7C;YACD,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;YACxE,IAAI,IAAA,2BAAS,EAAC,MAAA,aAAa,CAAC,SAAS,0CAAE,IAAI,CAAC,EAAE;gBAC5C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aAC/C;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,EACvB,MAAM,EACN,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAC/B,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;;YACd,IAAI,IAAA,2BAAS,EAAC,MAAA,aAAa,CAAC,SAAS,0CAAE,EAAE,CAAC,EAAE;gBAC1C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;aAC7C;YACD,IAAI,IAAA,2BAAS,EAAC,MAAA,aAAa,CAAC,SAAS,0CAAE,IAAI,CAAC,EAAE;gBAC5C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aAC/C;YACD,OAAO,aAAa,CAAC;QACvB,CAAC,CACF,CAAC;QACF,MAAM,8BAA8B,GAClC,IAAI,+CAA6B,CAC/B,4BAA4B,EAC5B,CAAC,GAAG,CAAC,EACL,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;;YAC/B,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;gBACzD,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC/B,OAAO,aAAa,CAAC;aACtB;YACD,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,+BAA+B,aAAa,YAAY,CACzD,CAAC;YACF,IAAI,IAAA,2BAAS,EAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,SAAS,0CAAE,IAAI,CAAC,EAAE;gBAC7C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aAC/C;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,EACvB,MAAM,EACN,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAC/B,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;;YACd,IAAI,IAAA,2BAAS,EAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,SAAS,0CAAE,IAAI,CAAC,EAAE;gBAC7C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aAC/C;QACH,CAAC,CACF,CAAC;QAEJ,OAAO;YACL,IAAI,qDAAmC,CACrC,WAAW,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;;gBAC/B,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;oBACzD,OAAO,aAAa,CAAC;iBACtB;gBACD,IAAI,aAAa,KAAK,SAAS,EAAE;oBAC/B,OAAO,aAAa,CAAC;iBACtB;gBACD,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,+BAA+B,aAAa,SAAS,CACtD,CAAC;gBACF,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,0CAAE,SAAS,0CAAE,EAAE,CAAC,EAAE;oBACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;iBACpD;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,IAAI,EACJ,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAC7B,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;;gBAC/B,IAAI,IAAA,2BAAS,EAAC,MAAA,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,0CAAE,SAAS,0CAAE,EAAE,CAAC,EAAE;oBACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;iBACpD;gBACD,OAAO,aAAa,CAAC;YACvB,CAAC,EACD;gBACE,gCAAgC;gBAChC,wBAAwB;gBACxB,qBAAqB;aACtB,CACF;YACD,IAAI,qDAAmC,CACrC,WAAW,EACX,CAAC,GAAG,CAAC,EACL,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;;gBAC/B,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;oBACzD,OAAO,aAAa,CAAC;iBACtB;gBACD,IAAI,aAAa,KAAK,SAAS,EAAE;oBAC/B,OAAO,aAAa,CAAC;iBACtB;gBACD,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,+BAA+B,aAAa,SAAS,CACtD,CAAC;gBACF,IAAI,IAAA,2BAAS,EAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,SAAS,0CAAE,EAAE,CAAC,EAAE;oBAC3C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;iBAC7C;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,EACvB,IAAI,EACJ,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAC7B,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;;gBAC/B,IAAI,IAAA,2BAAS,EAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,SAAS,0CAAE,EAAE,CAAC,EAAE;oBAC3C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;iBAC7C;gBACD,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,CAAC,8BAA8B,EAAE,2BAA2B,CAAC,CAC9D;SACF,CAAC;IACJ,CAAC;IAEQ,SAAS,CAAC,SAAwC,EAAE;QAC3D,OAAO,KAAK,CAAC,SAAS,CAAC,IAAA,uBAAe,EAAC,MAAM,CAAC,CAAC,CAAC;IAClD,CAAC;IAEO,QAAQ,CAAC,aAAqB;QACpC,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,CAAC,QAAkB,EAAE,EAAE;YAC5B,OAAO,UAAqB,EAAO,EAAE,gBAA0B;;gBAC7D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;oBAC9D,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACxC;gBACD,IAAI,MAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,0CAAE,QAAQ,CAAC,EAAE,CAAC,EAAE;oBAChD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACxC;gBACD,MAAM,eAAe,GAAG,UAAqB,GAAG,IAAW;;oBACzD,MAAM,SAAS,GAAG,EAAE,CAAC;oBACrB,MAAM,gBAAgB,GAAG,GAAG,CAAC;oBAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,KAAI,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,GAAG,0CAAE,IAAI,CAAA,CAAC;oBACvD,MAAM,WAAW,GACf,SAAS,KAAK,gBAAgB;wBAC5B,CAAC,CAAC,SAAS;wBACX,CAAC,CAAC,GAAG,SAAS,IAAI,SAAS,EAAE,CAAC;oBAClC,MAAM,IAAI,GAAS,IAAI,CAAC,MAAM,CAAC,SAAS,CACtC,GAAG,WAAW,IAAI,+CAAwB,CAAC,OAAO,EAAE,EACpD;wBACE,IAAI,EAAE,cAAQ,CAAC,QAAQ;wBACvB,UAAU,EAAE;4BACV,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,EAAE,WAAW;4BAClD,CAAC,yCAAkB,CAAC,qBAAqB,CAAC,EAAE,SAAS;4BACrD,CAAC,yCAAkB,CAAC,mBAAmB,CAAC,EACtC,+CAAwB,CAAC,OAAO;4BAClC,CAAC,kDAAiC,CAAC,oBAAoB,CAAC,EACtD,SAAS;yBACZ;qBACF,CACF,CAAC;oBAEF,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;wBACvB,IAAA,wCAAsB,EACpB,GAAG,EAAE,eACH,OAAA,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,mDAAG,IAAI,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA,EAAA,EAChE,CAAC,CAAC,EAAE;4BACF,IAAI,CAAC;gCAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;wBAC7C,CAAC,EACD,IAAI,CACL,CAAC;qBACH;oBACD,OAAO,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,CAC9D,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,CAClE,CAAC;gBACJ,CAAC,CAAC;gBACF,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,OAAO,CAAC,MAAgC,EAAE,IAAU;QAC1D,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC;YACxB,IAAI,IAAA,iBAAS,EAAC,MAAM,CAAC,EAAE;gBACrB,OAAO,MAAM,CAAC,IAAI,CAChB,KAAK,CAAC,EAAE;oBACN,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,OAAO,KAAK,CAAC;gBACf,CAAC,EACD,GAAG,CAAC,EAAE;oBACJ,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;oBAC1B,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;wBAC1B,OAAO,EAAE,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,OAAO;qBACtB,CAAC,CAAC;oBACH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,MAAM,GAAG,CAAC;gBACZ,CAAC,CACF,CAAC;aACH;iBAAM;gBACL,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,OAAO,MAAM,CAAC;aACf;SACF;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,oBAAc,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,CAAC,CAAC;YACxE,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,UAAU,CAAC,aAAqB;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,CAAC,QAAkB,EAAE,EAAE;YAC5B,OAAO,UAAqB,EAAO,EAAE,GAAG,IAAW;;gBACjD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;oBAC9D,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACxC;gBACD,IAAI,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,mBAAmB,0CAAE,QAAQ,CAAC,EAAE,CAAC,EAAE;oBACnD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACxC;gBACD,MAAM,eAAe,GAAG,WAAW,CAAC;gBACpC,MAAM,SAAS,GAAG,EAAE,CAAC;gBACrB,MAAM,UAAU,GAAQ;oBACtB,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,EAAE,eAAe;oBACtD,CAAC,yCAAkB,CAAC,0BAA0B,CAAC,EAC7C,qDAA8B,CAAC,KAAK;oBACtC,CAAC,kDAAiC,CAAC,oBAAoB,CAAC,EAAE,SAAS;iBACpE,CAAC;gBAEF,MAAM,KAAK,GAAG,IAAA,kCAA0B,EAAC,IAAI,CAAC,CAAC;gBAC/C,IAAI,KAAK,CAAC,MAAM,EAAE;oBAChB,UAAU,CAAC,kDAAiC,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC;iBACvE;gBACD,MAAM,SAAS,GACb,IAAI,CAAC,IAAI,KAAI,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,GAAG,0CAAE,IAAI,CAAA,KAAI,MAAA,IAAI,CAAC,OAAO,0CAAE,IAAI,CAAA,CAAC;gBAC7D,IAAI,SAAS,EAAE;oBACb,UAAU,CAAC,kDAAiC,CAAC,mBAAmB,CAAC;wBAC/D,SAAS,CAAC;oBACZ,UAAU,CAAC,yCAAkB,CAAC,qBAAqB,CAAC,GAAG,SAAS,CAAC;iBAClE;gBACD,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAS,OAAO,EAAE;oBAClE,IAAI,EAAE,cAAQ,CAAC,QAAQ;oBACvB,UAAU;iBACX,CAAC,CAAC;gBAEH,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;oBACzB,IAAA,wCAAsB,EACpB,GAAG,EAAE,eACH,OAAA,MAAA,MAAA,IAAI,CAAC,OAAO,EAAC,QAAQ,mDAAG,IAAI,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA,EAAA,EACjE,CAAC,CAAC,EAAE;wBACF,IAAI,CAAC;4BAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;oBAC/C,CAAC,EACD,IAAI,CACL,CAAC;iBACH;gBACD,IAAI;oBACF,OAAO,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,CAC9D,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAChC,CAAC;iBACH;gBAAC,OAAO,KAAU,EAAE;oBACnB,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;wBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;qBACvB,CAAC,CAAC;oBACH,MAAM,KAAK,CAAC;iBACb;wBAAS;oBACR,IAAI,CAAC,GAAG,EAAE,CAAC;iBACZ;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;CACF;AA7YD,0DA6YC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  context,\n  trace,\n  Span,\n  SpanKind,\n  SpanStatusCode,\n} from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleFile,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  SemanticAttributes,\n  MessagingOperationValues,\n  MessagingDestinationKindValues,\n} from '@opentelemetry/semantic-conventions';\nimport { SocketIoInstrumentationConfig } from './types';\nimport { SocketIoInstrumentationAttributes } from './AttributeNames';\nimport { VERSION } from './version';\nimport {\n  extractRoomsAttributeValue,\n  isPromise,\n  normalizeConfig,\n} from './utils';\n\nconst reservedEvents = [\n  'connect',\n  'connect_error',\n  'disconnect',\n  'disconnecting',\n  'newListener',\n  'removeListener',\n];\n\nexport class SocketIoInstrumentation extends InstrumentationBase<any> {\n  protected override _config!: SocketIoInstrumentationConfig;\n\n  constructor(config: SocketIoInstrumentationConfig = {}) {\n    super(\n      '@opentelemetry/instrumentation-socket.io',\n      VERSION,\n      normalizeConfig(config)\n    );\n  }\n\n  protected init() {\n    const socketInstrumentation = new InstrumentationNodeModuleFile<any>(\n      'socket.io/dist/socket.js',\n      ['>=3 <5'],\n      (moduleExports, moduleVersion) => {\n        if (moduleExports === undefined || moduleExports === null) {\n          return moduleExports;\n        }\n        if (moduleVersion === undefined) {\n          return moduleExports;\n        }\n        this._diag.debug(`applying patch to socket.io@${moduleVersion} Socket`);\n        if (isWrapped(moduleExports?.Socket?.prototype?.on)) {\n          this._unwrap(moduleExports.Socket.prototype, 'on');\n        }\n        this._wrap(\n          moduleExports.Socket.prototype,\n          'on',\n          this._patchOn(moduleVersion)\n        );\n        if (isWrapped(moduleExports?.Socket?.prototype?.emit)) {\n          this._unwrap(moduleExports.Socket.prototype, 'emit');\n        }\n        this._wrap(\n          moduleExports.Socket.prototype,\n          'emit',\n          this._patchEmit(moduleVersion)\n        );\n        return moduleExports;\n      },\n      moduleExports => {\n        if (isWrapped(moduleExports?.Socket?.prototype?.on)) {\n          this._unwrap(moduleExports.Socket.prototype, 'on');\n        }\n        if (isWrapped(moduleExports?.Socket?.prototype?.emit)) {\n          this._unwrap(moduleExports.Socket.prototype, 'emit');\n        }\n        return moduleExports;\n      }\n    );\n\n    const broadcastOperatorInstrumentation =\n      new InstrumentationNodeModuleFile<any>(\n        'socket.io/dist/broadcast-operator.js',\n        ['>=4 <5'],\n        (moduleExports, moduleVersion) => {\n          if (moduleExports === undefined || moduleExports === null) {\n            return moduleExports;\n          }\n          if (moduleVersion === undefined) {\n            return moduleExports;\n          }\n          this._diag.debug(\n            `applying patch to socket.io@${moduleVersion} StrictEventEmitter`\n          );\n          if (isWrapped(moduleExports?.BroadcastOperator?.prototype?.emit)) {\n            this._unwrap(moduleExports.BroadcastOperator.prototype, 'emit');\n          }\n          this._wrap(\n            moduleExports.BroadcastOperator.prototype,\n            'emit',\n            this._patchEmit(moduleVersion)\n          );\n          return moduleExports;\n        },\n        moduleExports => {\n          if (isWrapped(moduleExports?.BroadcastOperator?.prototype?.emit)) {\n            this._unwrap(moduleExports.BroadcastOperator.prototype, 'emit');\n          }\n          return moduleExports;\n        }\n      );\n    const namespaceInstrumentation = new InstrumentationNodeModuleFile<any>(\n      'socket.io/dist/namespace.js',\n      ['<4'],\n      (moduleExports, moduleVersion) => {\n        if (moduleExports === undefined || moduleExports === null) {\n          return moduleExports;\n        }\n        if (moduleVersion === undefined) {\n          return moduleExports;\n        }\n        this._diag.debug(\n          `applying patch to socket.io@${moduleVersion} Namespace`\n        );\n        if (isWrapped(moduleExports?.Namespace?.prototype?.emit)) {\n          this._unwrap(moduleExports.Namespace.prototype, 'emit');\n        }\n        this._wrap(\n          moduleExports.Namespace.prototype,\n          'emit',\n          this._patchEmit(moduleVersion)\n        );\n        return moduleExports;\n      },\n      moduleExports => {\n        if (isWrapped(moduleExports?.Namespace?.prototype?.emit)) {\n          this._unwrap(moduleExports.Namespace.prototype, 'emit');\n        }\n      }\n    );\n    const socketInstrumentationLegacy = new InstrumentationNodeModuleFile<any>(\n      'socket.io/lib/socket.js',\n      ['2'],\n      (moduleExports, moduleVersion) => {\n        if (moduleExports === undefined || moduleExports === null) {\n          return moduleExports;\n        }\n        if (moduleVersion === undefined) {\n          return moduleExports;\n        }\n        this._diag.debug(`applying patch to socket.io@${moduleVersion} Socket`);\n        if (isWrapped(moduleExports.prototype?.on)) {\n          this._unwrap(moduleExports.prototype, 'on');\n        }\n        this._wrap(moduleExports.prototype, 'on', this._patchOn(moduleVersion));\n        if (isWrapped(moduleExports.prototype?.emit)) {\n          this._unwrap(moduleExports.prototype, 'emit');\n        }\n        this._wrap(\n          moduleExports.prototype,\n          'emit',\n          this._patchEmit(moduleVersion)\n        );\n        return moduleExports;\n      },\n      moduleExports => {\n        if (isWrapped(moduleExports.prototype?.on)) {\n          this._unwrap(moduleExports.prototype, 'on');\n        }\n        if (isWrapped(moduleExports.prototype?.emit)) {\n          this._unwrap(moduleExports.prototype, 'emit');\n        }\n        return moduleExports;\n      }\n    );\n    const namespaceInstrumentationLegacy =\n      new InstrumentationNodeModuleFile<any>(\n        'socket.io/lib/namespace.js',\n        ['2'],\n        (moduleExports, moduleVersion) => {\n          if (moduleExports === undefined || moduleExports === null) {\n            return moduleExports;\n          }\n          if (moduleVersion === undefined) {\n            return moduleExports;\n          }\n          this._diag.debug(\n            `applying patch to socket.io@${moduleVersion} Namespace`\n          );\n          if (isWrapped(moduleExports?.prototype?.emit)) {\n            this._unwrap(moduleExports.prototype, 'emit');\n          }\n          this._wrap(\n            moduleExports.prototype,\n            'emit',\n            this._patchEmit(moduleVersion)\n          );\n          return moduleExports;\n        },\n        moduleExports => {\n          if (isWrapped(moduleExports?.prototype?.emit)) {\n            this._unwrap(moduleExports.prototype, 'emit');\n          }\n        }\n      );\n\n    return [\n      new InstrumentationNodeModuleDefinition<any>(\n        'socket.io',\n        ['>=3 <5'],\n        (moduleExports, moduleVersion) => {\n          if (moduleExports === undefined || moduleExports === null) {\n            return moduleExports;\n          }\n          if (moduleVersion === undefined) {\n            return moduleExports;\n          }\n          this._diag.debug(\n            `applying patch to socket.io@${moduleVersion} Server`\n          );\n          if (isWrapped(moduleExports?.Server?.prototype?.on)) {\n            this._unwrap(moduleExports.Server.prototype, 'on');\n          }\n          this._wrap(\n            moduleExports.Server.prototype,\n            'on',\n            this._patchOn(moduleVersion)\n          );\n          return moduleExports;\n        },\n        (moduleExports, moduleVersion) => {\n          if (isWrapped(moduleExports?.Server?.prototype?.on)) {\n            this._unwrap(moduleExports.Server.prototype, 'on');\n          }\n          return moduleExports;\n        },\n        [\n          broadcastOperatorInstrumentation,\n          namespaceInstrumentation,\n          socketInstrumentation,\n        ]\n      ),\n      new InstrumentationNodeModuleDefinition<any>(\n        'socket.io',\n        ['2'],\n        (moduleExports, moduleVersion) => {\n          if (moduleExports === undefined || moduleExports === null) {\n            return moduleExports;\n          }\n          if (moduleVersion === undefined) {\n            return moduleExports;\n          }\n          this._diag.debug(\n            `applying patch to socket.io@${moduleVersion} Server`\n          );\n          if (isWrapped(moduleExports?.prototype?.on)) {\n            this._unwrap(moduleExports.prototype, 'on');\n          }\n          this._wrap(\n            moduleExports.prototype,\n            'on',\n            this._patchOn(moduleVersion)\n          );\n          return moduleExports;\n        },\n        (moduleExports, moduleVersion) => {\n          if (isWrapped(moduleExports?.prototype?.on)) {\n            this._unwrap(moduleExports.prototype, 'on');\n          }\n          return moduleExports;\n        },\n        [namespaceInstrumentationLegacy, socketInstrumentationLegacy]\n      ),\n    ];\n  }\n\n  override setConfig(config: SocketIoInstrumentationConfig = {}) {\n    return super.setConfig(normalizeConfig(config));\n  }\n\n  private _patchOn(moduleVersion: string) {\n    const self = this;\n    return (original: Function) => {\n      return function (this: any, ev: any, originalListener: Function) {\n        if (!self._config.traceReserved && reservedEvents.includes(ev)) {\n          return original.apply(this, arguments);\n        }\n        if (self._config.onIgnoreEventList?.includes(ev)) {\n          return original.apply(this, arguments);\n        }\n        const wrappedListener = function (this: any, ...args: any[]) {\n          const eventName = ev;\n          const defaultNamespace = '/';\n          const namespace = this.name || this.adapter?.nsp?.name;\n          const destination =\n            namespace === defaultNamespace\n              ? eventName\n              : `${namespace} ${eventName}`;\n          const span: Span = self.tracer.startSpan(\n            `${destination} ${MessagingOperationValues.RECEIVE}`,\n            {\n              kind: SpanKind.CONSUMER,\n              attributes: {\n                [SemanticAttributes.MESSAGING_SYSTEM]: 'socket.io',\n                [SemanticAttributes.MESSAGING_DESTINATION]: namespace,\n                [SemanticAttributes.MESSAGING_OPERATION]:\n                  MessagingOperationValues.RECEIVE,\n                [SocketIoInstrumentationAttributes.SOCKET_IO_EVENT_NAME]:\n                  eventName,\n              },\n            }\n          );\n\n          if (self._config.onHook) {\n            safeExecuteInTheMiddle(\n              () =>\n                self._config?.onHook?.(span, { moduleVersion, payload: args }),\n              e => {\n                if (e) self._diag.error('onHook error', e);\n              },\n              true\n            );\n          }\n          return context.with(trace.setSpan(context.active(), span), () =>\n            self.endSpan(() => originalListener.apply(this, arguments), span)\n          );\n        };\n        return original.apply(this, [ev, wrappedListener]);\n      };\n    };\n  }\n\n  private endSpan(traced: () => any | Promise<any>, span: Span) {\n    try {\n      const result = traced();\n      if (isPromise(result)) {\n        return result.then(\n          value => {\n            span.end();\n            return value;\n          },\n          err => {\n            span.recordException(err);\n            span.setStatus({\n              code: SpanStatusCode.ERROR,\n              message: err?.message,\n            });\n            span.end();\n            throw err;\n          }\n        );\n      } else {\n        span.end();\n        return result;\n      }\n    } catch (error: any) {\n      span.recordException(error);\n      span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message });\n      span.end();\n      throw error;\n    }\n  }\n\n  private _patchEmit(moduleVersion: string) {\n    const self = this;\n    return (original: Function) => {\n      return function (this: any, ev: any, ...args: any[]) {\n        if (!self._config.traceReserved && reservedEvents.includes(ev)) {\n          return original.apply(this, arguments);\n        }\n        if (self._config?.emitIgnoreEventList?.includes(ev)) {\n          return original.apply(this, arguments);\n        }\n        const messagingSystem = 'socket.io';\n        const eventName = ev;\n        const attributes: any = {\n          [SemanticAttributes.MESSAGING_SYSTEM]: messagingSystem,\n          [SemanticAttributes.MESSAGING_DESTINATION_KIND]:\n            MessagingDestinationKindValues.TOPIC,\n          [SocketIoInstrumentationAttributes.SOCKET_IO_EVENT_NAME]: eventName,\n        };\n\n        const rooms = extractRoomsAttributeValue(this);\n        if (rooms.length) {\n          attributes[SocketIoInstrumentationAttributes.SOCKET_IO_ROOMS] = rooms;\n        }\n        const namespace =\n          this.name || this.adapter?.nsp?.name || this.sockets?.name;\n        if (namespace) {\n          attributes[SocketIoInstrumentationAttributes.SOCKET_IO_NAMESPACE] =\n            namespace;\n          attributes[SemanticAttributes.MESSAGING_DESTINATION] = namespace;\n        }\n        const spanRooms = rooms.length ? `[${rooms.join()}]` : '';\n        const span = self.tracer.startSpan(`${namespace}${spanRooms} send`, {\n          kind: SpanKind.PRODUCER,\n          attributes,\n        });\n\n        if (self._config.emitHook) {\n          safeExecuteInTheMiddle(\n            () =>\n              self._config.emitHook?.(span, { moduleVersion, payload: args }),\n            e => {\n              if (e) self._diag.error('emitHook error', e);\n            },\n            true\n          );\n        }\n        try {\n          return context.with(trace.setSpan(context.active(), span), () =>\n            original.apply(this, arguments)\n          );\n        } catch (error: any) {\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: error.message,\n          });\n          throw error;\n        } finally {\n          span.end();\n        }\n      };\n    };\n  }\n}\n"]}