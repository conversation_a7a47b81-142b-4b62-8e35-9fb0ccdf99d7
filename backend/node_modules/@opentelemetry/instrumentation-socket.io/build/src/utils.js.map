{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";;;AAiBO,MAAM,SAAS,GAAG,CAAC,KAAU,EAA6B,EAAE;IACjE,OAAO,OAAO,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,CAAA,KAAK,UAAU,CAAC;AAC3C,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEK,MAAM,eAAe,GAAG,CAAC,MAAsC,EAAE,EAAE;IACxE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE;QAC9C,MAAM,CAAC,mBAAmB,GAAG,EAAE,CAAC;KACjC;IACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;QAC5C,MAAM,CAAC,iBAAiB,GAAG,EAAE,CAAC;KAC/B;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AATW,QAAA,eAAe,mBAS1B;AAEK,MAAM,0BAA0B,GAAG,CAAC,IAAS,EAAS,EAAE;;IAC7D,IAAI,KAAK,GACP,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,MAAM;SACX,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,CAAA;SACpB,MAAA,IAAI,CAAC,OAAO,0CAAE,KAAK,CAAA;QACnB,EAAE,CAAC;IACL,4DAA4D;IAC5D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACzB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAS,KAAK,CAAC,CAAC;KACnC;IACD,+GAA+G;IAC/G,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE;QACjC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACrB;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAhBW,QAAA,0BAA0B,8BAgBrC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { SocketIoInstrumentationConfig } from './types';\n\nexport const isPromise = (value: any): value is Promise<unknown> => {\n  return typeof value?.then === 'function';\n};\n\nexport const normalizeConfig = (config?: SocketIoInstrumentationConfig) => {\n  config = Object.assign({}, config);\n  if (!Array.isArray(config.emitIgnoreEventList)) {\n    config.emitIgnoreEventList = [];\n  }\n  if (!Array.isArray(config.onIgnoreEventList)) {\n    config.onIgnoreEventList = [];\n  }\n  return config;\n};\n\nexport const extractRoomsAttributeValue = (self: any): any[] => {\n  let rooms =\n    self.rooms ||\n    self._rooms ||\n    self.sockets?._rooms ||\n    self.sockets?.rooms ||\n    [];\n  // Some of the attributes above are of Set type. Convert it.\n  if (!Array.isArray(rooms)) {\n    rooms = Array.from<string>(rooms);\n  }\n  // only for v2: this.id is only set for v2. That's to mimic later versions which have this.id in the rooms Set.\n  if (rooms.length === 0 && self.id) {\n    rooms.push(self.id);\n  }\n  return rooms;\n};\n"]}