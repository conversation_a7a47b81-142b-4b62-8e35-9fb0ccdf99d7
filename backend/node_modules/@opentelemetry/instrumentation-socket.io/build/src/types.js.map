{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";;;AAkBa,QAAA,mBAAmB,GAAG,aAAa,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport const defaultSocketIoPath = '/socket.io/';\n\nexport interface SocketIoHookInfo {\n  moduleVersion?: string;\n  payload: any[];\n}\nexport interface SocketIoHookFunction {\n  (span: Span, hookInfo: SocketIoHookInfo): void;\n}\n\nexport interface SocketIoInstrumentationConfig extends InstrumentationConfig {\n  /** Hook for adding custom attributes before socket.io emits the event */\n  emitHook?: SocketIoHookFunction;\n  /** list of events to ignore tracing on for socket.io emits */\n  emitIgnoreEventList?: string[];\n  /** Hook for adding custom attributes before the event listener (callback) is invoked */\n  onHook?: SocketIoHookFunction;\n  /** list of events to ignore tracing on for socket.io listeners */\n  onIgnoreEventList?: string[];\n  /** Set to `true` if you want to trace socket.io reserved events (see https://socket.io/docs/v4/emit-cheatsheet/#Reserved-events) */\n  traceReserved?: boolean;\n}\n"]}