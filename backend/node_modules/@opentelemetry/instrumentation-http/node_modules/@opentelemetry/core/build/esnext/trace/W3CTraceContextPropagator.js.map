{"version": 3, "file": "W3CTraceContextPropagator.js", "sourceRoot": "", "sources": ["../../../src/trace/W3CTraceContextPropagator.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAEL,kBAAkB,EAKlB,KAAK,EACL,UAAU,GACX,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,MAAM,CAAC,MAAM,mBAAmB,GAAG,aAAa,CAAC;AACjD,MAAM,CAAC,MAAM,kBAAkB,GAAG,YAAY,CAAC;AAE/C,MAAM,OAAO,GAAG,IAAI,CAAC;AACrB,MAAM,YAAY,GAAG,mBAAmB,CAAC;AACzC,MAAM,aAAa,GAAG,yBAAyB,CAAC;AAChD,MAAM,cAAc,GAAG,yBAAyB,CAAC;AACjD,MAAM,UAAU,GAAG,aAAa,CAAC;AACjC,MAAM,kBAAkB,GAAG,IAAI,MAAM,CACnC,SAAS,YAAY,MAAM,aAAa,MAAM,cAAc,MAAM,UAAU,cAAc,CAC3F,CAAC;AAEF;;;;;;;;;GASG;AACH,MAAM,UAAU,gBAAgB,CAAC,WAAmB;IAClD,MAAM,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACnD,IAAI,CAAC,KAAK;QAAE,OAAO,IAAI,CAAC;IAExB,yEAAyE;IACzE,4FAA4F;IAC5F,qEAAqE;IACrE,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC;QAAE,OAAO,IAAI,CAAC;IAE/C,OAAO;QACL,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QACjB,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;QAChB,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;KACnC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,MAAM,OAAO,yBAAyB;IACpC,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IACE,CAAC,WAAW;YACZ,mBAAmB,CAAC,OAAO,CAAC;YAC5B,CAAC,kBAAkB,CAAC,WAAW,CAAC;YAEhC,OAAO;QAET,MAAM,WAAW,GAAG,GAAG,OAAO,IAAI,WAAW,CAAC,OAAO,IACnD,WAAW,CAAC,MACd,KAAK,MAAM,CAAC,WAAW,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;QAEtE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,mBAAmB,EAAE,WAAW,CAAC,CAAC;QACtD,IAAI,WAAW,CAAC,UAAU,EAAE;YAC1B,MAAM,CAAC,GAAG,CACR,OAAO,EACP,kBAAkB,EAClB,WAAW,CAAC,UAAU,CAAC,SAAS,EAAE,CACnC,CAAC;SACH;IACH,CAAC;IAED,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;QACnE,IAAI,CAAC,iBAAiB;YAAE,OAAO,OAAO,CAAC;QACvC,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAClD,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,iBAAiB,CAAC;QACtB,IAAI,OAAO,WAAW,KAAK,QAAQ;YAAE,OAAO,OAAO,CAAC;QACpD,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW;YAAE,OAAO,OAAO,CAAC;QAEjC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC;QAE5B,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;QACjE,IAAI,gBAAgB,EAAE;YACpB,sEAAsE;YACtE,iBAAiB;YACjB,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;gBAC3C,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC5B,CAAC,CAAC,gBAAgB,CAAC;YACrB,WAAW,CAAC,UAAU,GAAG,IAAI,UAAU,CACrC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAC9C,CAAC;SACH;QACD,OAAO,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IACpD,CAAC;IAED,MAAM;QACJ,OAAO,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;IACnD,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  SpanContext,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n  trace,\n  TraceFlags,\n} from '@opentelemetry/api';\nimport { isTracingSuppressed } from './suppress-tracing';\nimport { TraceState } from './TraceState';\n\nexport const TRACE_PARENT_HEADER = 'traceparent';\nexport const TRACE_STATE_HEADER = 'tracestate';\n\nconst VERSION = '00';\nconst VERSION_PART = '(?!ff)[\\\\da-f]{2}';\nconst TRACE_ID_PART = '(?![0]{32})[\\\\da-f]{32}';\nconst PARENT_ID_PART = '(?![0]{16})[\\\\da-f]{16}';\nconst FLAGS_PART = '[\\\\da-f]{2}';\nconst TRACE_PARENT_REGEX = new RegExp(\n  `^\\\\s?(${VERSION_PART})-(${TRACE_ID_PART})-(${PARENT_ID_PART})-(${FLAGS_PART})(-.*)?\\\\s?$`\n);\n\n/**\n * Parses information from the [traceparent] span tag and converts it into {@link SpanContext}\n * @param traceParent - A meta property that comes from server.\n *     It should be dynamically generated server side to have the server's request trace Id,\n *     a parent span Id that was set on the server's request span,\n *     and the trace flags to indicate the server's sampling decision\n *     (01 = sampled, 00 = not sampled).\n *     for example: '{version}-{traceId}-{spanId}-{sampleDecision}'\n *     For more information see {@link https://www.w3.org/TR/trace-context/}\n */\nexport function parseTraceParent(traceParent: string): SpanContext | null {\n  const match = TRACE_PARENT_REGEX.exec(traceParent);\n  if (!match) return null;\n\n  // According to the specification the implementation should be compatible\n  // with future versions. If there are more parts, we only reject it if it's using version 00\n  // See https://www.w3.org/TR/trace-context/#versioning-of-traceparent\n  if (match[1] === '00' && match[5]) return null;\n\n  return {\n    traceId: match[2],\n    spanId: match[3],\n    traceFlags: parseInt(match[4], 16),\n  };\n}\n\n/**\n * Propagates {@link SpanContext} through Trace Context format propagation.\n *\n * Based on the Trace Context specification:\n * https://www.w3.org/TR/trace-context/\n */\nexport class W3CTraceContextPropagator implements TextMapPropagator {\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    const spanContext = trace.getSpanContext(context);\n    if (\n      !spanContext ||\n      isTracingSuppressed(context) ||\n      !isSpanContextValid(spanContext)\n    )\n      return;\n\n    const traceParent = `${VERSION}-${spanContext.traceId}-${\n      spanContext.spanId\n    }-0${Number(spanContext.traceFlags || TraceFlags.NONE).toString(16)}`;\n\n    setter.set(carrier, TRACE_PARENT_HEADER, traceParent);\n    if (spanContext.traceState) {\n      setter.set(\n        carrier,\n        TRACE_STATE_HEADER,\n        spanContext.traceState.serialize()\n      );\n    }\n  }\n\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    const traceParentHeader = getter.get(carrier, TRACE_PARENT_HEADER);\n    if (!traceParentHeader) return context;\n    const traceParent = Array.isArray(traceParentHeader)\n      ? traceParentHeader[0]\n      : traceParentHeader;\n    if (typeof traceParent !== 'string') return context;\n    const spanContext = parseTraceParent(traceParent);\n    if (!spanContext) return context;\n\n    spanContext.isRemote = true;\n\n    const traceStateHeader = getter.get(carrier, TRACE_STATE_HEADER);\n    if (traceStateHeader) {\n      // If more than one `tracestate` header is found, we merge them into a\n      // single header.\n      const state = Array.isArray(traceStateHeader)\n        ? traceStateHeader.join(',')\n        : traceStateHeader;\n      spanContext.traceState = new TraceState(\n        typeof state === 'string' ? state : undefined\n      );\n    }\n    return trace.setSpanContext(context, spanContext);\n  }\n\n  fields(): string[] {\n    return [TRACE_PARENT_HEADER, TRACE_STATE_HEADER];\n  }\n}\n"]}