{"version": 3, "file": "AttributeNames.js", "sourceRoot": "", "sources": ["../../../src/enums/AttributeNames.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH;;GAEG;AACH,IAAY,cAIX;AAJD,WAAY,cAAc;IACxB,qDAAmC,CAAA;IACnC,2DAAyC,CAAA;IACzC,uDAAqC,CAAA;AACvC,CAAC,EAJW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAIzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * https://github.com/open-telemetry/opentelemetry-specification/blob/master/specification/trace/semantic_conventions/http.md\n */\nexport enum AttributeNames {\n  HTTP_ERROR_NAME = 'http.error_name',\n  HTTP_ERROR_MESSAGE = 'http.error_message',\n  HTTP_STATUS_TEXT = 'http.status_text',\n}\n"]}