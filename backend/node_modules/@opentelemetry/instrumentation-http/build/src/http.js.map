{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../../src/http.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAe4B;AAC5B,8CAK6B;AAI7B,iCAAiC;AACjC,2BAA2B;AAS3B,iCAAiC;AACjC,uCAAoC;AACpC,oEAKwC;AACxC,8CAA2E;AAC3E,mCAAsC;AACtC,8EAAyE;AAEzE;;GAEG;AACH,MAAa,mBAAoB,SAAQ,qCAAyB;IAOhE,YAAY,MAAkC;QAC5C,KAAK,CAAC,qCAAqC,EAAE,iBAAO,EAAE,MAAM,CAAC,CAAC;QAPhE,oCAAoC;QACnB,kBAAa,GAAkB,IAAI,OAAO,EAAQ,CAAC;QAOlE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACpD,CAAC;IAEkB,wBAAwB;QACzC,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAC5D,sBAAsB,EACtB;YACE,WAAW,EAAE,iDAAiD;YAC9D,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,eAAS,CAAC,MAAM;SAC5B,CACF,CAAC;QACF,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAC5D,sBAAsB,EACtB;YACE,WAAW,EAAE,kDAAkD;YAC/D,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,eAAS,CAAC,MAAM;SAC5B,CACF,CAAC;IACJ,CAAC;IAEO,UAAU;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,SAAS,CAAC,MAAkC;QACnD,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACpD,CAAC;IAED,IAAI;QAIF,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;IAC3E,CAAC;IAEO,uBAAuB;QAC7B,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QACtC,OAAO,IAAI,qDAAmC,CAC5C,MAAM,EACN,CAAC,GAAG,CAAC,EACL,aAAa,CAAC,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,2BAA2B,OAAO,EAAE,CAAC,CAAC;YACvD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;gBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,SAAS,EACT,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAC9C,CAAC;YACF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,GAAG,CAAC,EAAE;gBAChC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;aACpC;YACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,KAAK,EACL,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,OAAO,CAAC,CACzD,CAAC;YACF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBAClD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACtD;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,MAAM,EACN,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAC9C,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;YACd,IAAI,aAAa,KAAK,SAAS;gBAAE,OAAO;YACxC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,2BAA2B,OAAO,EAAE,CAAC,CAAC;YAEvD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,wBAAwB;QAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QACtC,OAAO,IAAI,qDAAmC,CAC5C,OAAO,EACP,CAAC,GAAG,CAAC,EACL,aAAa,CAAC,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,4BAA4B,OAAO,EAAE,CAAC,CAAC;YACxD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;gBACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,SAAS,EACT,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CACpD,CAAC;YACF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,GAAG,CAAC,EAAE;gBAChC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;aACpC;YACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,KAAK,EACL,IAAI,CAAC,iCAAiC,CAAC,aAAa,CAAC,OAAO,CAAC,CAC9D,CAAC;YACF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBAClD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACtD;YACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,MAAM,EACN,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAC/C,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,aAAa,CAAC,EAAE;YACd,IAAI,aAAa,KAAK,SAAS;gBAAE,OAAO;YACxC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,4BAA4B,OAAO,EAAE,CAAC,CAAC;YAExD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,gCAAgC,CAAC,SAA2B;QACpE,OAAO,CACL,QAAwD,EACS,EAAE;YACnE,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACO,gCAAgC,CAAC,SAA2B;QACpE,OAAO,CAAC,QAAkC,EAA4B,EAAE;YACtE,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC;IACJ,CAAC;IAES,4BAA4B,CACpC,aAGuB;QAEvB,OAAO,CAAC,SAAmC,EAA4B,EAAE;YACvE,iEAAiE;YACjE,kEAAkE;YAClE,yEAAyE;YACzE,kEAAkE;YAClE,uEAAuE;YACvE,sEAAsE;YACtE,sEAAsE;YACtE,iCAAiC;YACjC,mFAAmF;YACnF,iHAAiH;YACjH,OAAO,SAAS,kBAAkB,CAEhC,OAAU,EAAE,GAAG,IAAqB;gBACpC,MAAM,GAAG,GAAG,aAAa,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC5C,GAAG,CAAC,GAAG,EAAE,CAAC;gBACV,OAAO,GAAG,CAAC;YACb,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,sCAAsC;IAC9B,qCAAqC,CAAC,SAA2B;QACvE,OAAO,CAAC,QAAkC,EAA4B,EAAE;YACtE,MAAM,eAAe,GAAG,IAAI,CAAC;YAC7B,OAAO,SAAS,oBAAoB;YAClC,sEAAsE;YACtE,OAA4C,EAC5C,GAAG,IAAqB;;gBAExB,wDAAwD;gBACxD,IACE,SAAS,KAAK,OAAO;oBACrB,OAAO,OAAO,KAAK,QAAQ;oBAC3B,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,0CAAE,IAAI,MAAK,KAAK,EACpC;oBACA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;oBACrC,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;iBAC7C;gBACD,OAAO,eAAe,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAChE,QAAQ,CACT,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YACtB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,OAA6B;QACtD,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC;QAChD,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,GAAG,CAAC;IACrC,CAAC;IAED,0CAA0C;IAClC,iCAAiC,CACvC,aAIuB;QAEvB,OAAO,CAAC,QAAkC,EAA4B,EAAE;YACtE,MAAM,eAAe,GAAG,IAAI,CAAC;YAC7B,OAAO,SAAS,oBAAoB;YAClC,sEAAsE;YACtE,OAA4C,EAC5C,GAAG,IAAqB;gBAExB,OAAO,eAAe,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAChE,QAAQ,CACT,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;YACtB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACK,mBAAmB,CACzB,OAA2B,EAC3B,IAAU,EACV,SAAiB,EACjB,gBAAkC;QAElC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE;YACjC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACtC;QAED;;WAEG;QACH,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAE7B;;;;WAIG;QACH,OAAO,CAAC,eAAe,CACrB,UAAU,EACV,CAAC,QAAsD,EAAE,EAAE;YACzD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAClD,IAAI,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC1C,QAAQ,CAAC,MAAM,EAAE,CAAC;aACnB;YACD,MAAM,kBAAkB,GACtB,KAAK,CAAC,sCAAsC,CAAC,QAAQ,CAAC,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;YACvC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAC9B,gBAAgB,EAChB,KAAK,CAAC,4CAA4C,CAAC,kBAAkB,CAAC,CACvE,CAAC;YAEF,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE;gBAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACxC;YAED,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAC9D,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAC1B,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,sBAAsB,CAC/C,IAAI,EACJ,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CACnC,CAAC;YAEF,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;YAEzC,MAAM,UAAU,GAAG,GAAG,EAAE;gBACtB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC7C,IAAI,gBAAgB,EAAE;oBACpB,OAAO;iBACR;gBACD,gBAAgB,GAAG,IAAI,CAAC;gBACxB,IAAI,MAAkB,CAAC;gBAEvB,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAC1C,MAAM,GAAG,EAAE,IAAI,EAAE,oBAAc,CAAC,KAAK,EAAE,CAAC;iBACzC;qBAAM;oBACL,MAAM,GAAG;wBACP,IAAI,EAAE,KAAK,CAAC,mBAAmB,CAC7B,cAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,UAAU,CACpB;qBACF,CAAC;iBACH;gBAED,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAEvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,2BAA2B,EAAE;oBACjD,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,IAAI,CAAC,UAAU,EAAE,CAAC,2BAA4B,CAC5C,IAAI,EACJ,OAAO,EACP,QAAQ,CACT,EACH,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;iBACH;gBAED,IAAI,CAAC,cAAc,CACjB,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,gBAAgB,CACjB,CAAC;YACJ,CAAC,CAAC;YAEF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAC/B,2FAA2F;YAC3F,IAAI,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;gBACxC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;aAClC;YACD,QAAQ,CAAC,EAAE,CAAC,qBAAY,EAAE,CAAC,KAAU,EAAE,EAAE;gBACvC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBACtD,IAAI,gBAAgB,EAAE;oBACpB,OAAO;iBACR;gBACD,gBAAgB,GAAG,IAAI,CAAC;gBACxB,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACpC,IAAI,CAAC,SAAS,CAAC;oBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;oBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB,CAAC,CAAC;gBACH,IAAI,CAAC,cAAc,CACjB,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,gBAAgB,CACjB,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CACF,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACvB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACvD,IAAI,OAAO,CAAC,OAAO,IAAI,gBAAgB,EAAE;gBACvC,OAAO;aACR;YACD,gBAAgB,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,cAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAY,EAAE,CAAC,KAAU,EAAE,EAAE;YACtC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,gBAAgB,EAAE;gBACpB,OAAO;aACR;YACD,gBAAgB,GAAG,IAAI,CAAC;YACxB,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,cAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACtD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,wBAAwB,CAC9B,SAA2B,EAC3B,QAAwD;QAExD,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,eAAe,CAE7B,KAAa,EACb,GAAG,IAAe;YAElB,6BAA6B;YAC7B,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;aAC/C;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAyB,CAAC;YAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAA6C,CAAC;YACrE,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG;gBAC1B,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,IAAI,GAAG;gBACxC,CAAC,CAAC,GAAG,CAAC;YACR,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC;YAEvC,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,GAAG,SAAS,kCAAkC,CAC/C,CAAC;YAEF,IACE,KAAK,CAAC,SAAS,CACb,QAAQ,EACR,eAAe,CAAC,UAAU,EAAE,CAAC,mBAAmB,EAChD,CAAC,CAAU,EAAE,EAAE,CACb,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,oCAAoC,EAAE,CAAC,CAAC,CACvE;gBACD,IAAA,wCAAsB,EACpB,GAAG,EAAE,eACH,OAAA,MAAA,MAAA,eAAe,CAAC,UAAU,EAAE,EAAC,yBAAyB,mDAAG,OAAO,CAAC,CAAA,EAAA,EACnE,CAAC,CAAU,EAAE,EAAE;oBACb,IAAI,CAAC,IAAI,IAAI,EAAE;wBACb,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,0CAA0C,EAC1C,CAAC,CACF,CAAC;qBACH;gBACH,CAAC,EACD,IAAI,CACL,EACD;gBACA,OAAO,aAAO,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE;oBAC1D,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;oBACxC,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;oBACzC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;aACJ;YAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAEhC,MAAM,cAAc,GAAG,KAAK,CAAC,4BAA4B,CAAC,OAAO,EAAE;gBACjE,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,eAAe,CAAC,UAAU,EAAE,CAAC,UAAU;gBACnD,cAAc,EAAE,eAAe,CAAC,kBAAkB,CAChD,OAAO,EACP,eAAe,CAAC,UAAU,EAAE,CAAC,qBAAqB,CACnD;aACF,CAAC,CAAC;YAEH,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,cAAQ,CAAC,MAAM;gBACrB,UAAU,EAAE,cAAc;aAC3B,CAAC;YAEF,MAAM,SAAS,GAAG,IAAA,aAAM,GAAE,CAAC;YAC3B,MAAM,gBAAgB,GACpB,KAAK,CAAC,kCAAkC,CAAC,cAAc,CAAC,CAAC;YAE3D,MAAM,GAAG,GAAG,iBAAW,CAAC,OAAO,CAAC,kBAAY,EAAE,OAAO,CAAC,CAAC;YACvD,MAAM,IAAI,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;YACtE,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,cAAO,CAAC,IAAI;gBAClB,IAAI;aACL,CAAC;YAEF,OAAO,aAAO,CAAC,IAAI,CACjB,IAAA,qBAAc,EAAC,WAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,WAAW,CAAC,EACrD,GAAG,EAAE;gBACH,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;gBACxC,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAEzC,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE;oBAC5C,eAAe,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBACjD;gBACD,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE;oBAC7C,eAAe,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;iBACnD;gBAED,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,qBAAqB,CACzD,IAAI,EACJ,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAClC,CAAC;gBAEF,yEAAyE;gBACzE,IAAI,QAAQ,GAAG,KAAK,CAAC;gBACrB,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBACxB,IAAI,QAAQ,EAAE;wBACZ,OAAO;qBACR;oBACD,eAAe,CAAC,uBAAuB,CACrC,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,gBAAgB,EAChB,SAAS,CACV,CAAC;gBACJ,CAAC,CAAC,CAAC;gBACH,QAAQ,CAAC,EAAE,CAAC,qBAAY,EAAE,CAAC,GAAQ,EAAE,EAAE;oBACrC,QAAQ,GAAG,IAAI,CAAC;oBAChB,eAAe,CAAC,sBAAsB,CACpC,IAAI,EACJ,gBAAgB,EAChB,SAAS,EACT,GAAG,CACJ,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,OAAO,IAAA,wCAAsB,EAC3B,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,EAC5C,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,EAAE;wBACT,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;wBACpC,eAAe,CAAC,cAAc,CAC5B,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,gBAAgB,CACjB,CAAC;wBACF,MAAM,KAAK,CAAC;qBACb;gBACH,CAAC,CACF,CAAC;YACJ,CAAC,CACF,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAC9B,SAA2B,EAC3B,QAAkC;QAElC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,eAAe,CAE7B,OAA+C,EAC/C,GAAG,IAAe;YAElB,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;gBACtC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;aACjD;YACD,MAAM,YAAY,GAChB,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ;gBAC3B,CAAC,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,YAAY,GAAG,CAAC,GAAG,CAAC;gBACzD,CAAC,CAAE,IAAI,CAAC,KAAK,EAA0B;gBACvC,CAAC,CAAC,SAAS,CAAC;YAChB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC,cAAc,CACtE,OAAO,EACP,YAAY,CACb,CAAC;YACF;;;;eAIG;YACH,IACE,SAAS,KAAK,MAAM;gBACpB,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;gBACnC,aAAa,CAAC,QAAQ,KAAK,QAAQ,EACnC;gBACA,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;aACvD;YAED,IACE,KAAK,CAAC,SAAS,CACb,MAAM,GAAG,QAAQ,EACjB,eAAe,CAAC,UAAU,EAAE,CAAC,kBAAkB,EAC/C,CAAC,CAAU,EAAE,EAAE,CACb,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,mCAAmC,EAAE,CAAC,CAAC,CACtE;gBACD,IAAA,wCAAsB,EACpB,GAAG,EAAE;;oBACH,OAAA,MAAA,MAAA,eAAe;yBACZ,UAAU,EAAE,EACZ,yBAAyB,mDAAG,aAAa,CAAC,CAAA;iBAAA,EAC/C,CAAC,CAAU,EAAE,EAAE;oBACb,IAAI,CAAC,IAAI,IAAI,EAAE;wBACb,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,0CAA0C,EAC1C,CAAC,CACF,CAAC;qBACH;gBACH,CAAC,EACD,IAAI,CACL,EACD;gBACA,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;aACvD;YAED,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAEvE,MAAM,UAAU,GAAG,KAAK,CAAC,4BAA4B,CAAC,aAAa,EAAE;gBACnE,SAAS;gBACT,IAAI;gBACJ,QAAQ;gBACR,cAAc,EAAE,eAAe,CAAC,kBAAkB,CAChD,aAAa,EACb,eAAe,CAAC,UAAU,EAAE,CAAC,qBAAqB,CACnD;aACF,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAA,aAAM,GAAE,CAAC;YAC3B,MAAM,gBAAgB,GACpB,KAAK,CAAC,kCAAkC,CAAC,UAAU,CAAC,CAAC;YAEvD,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,cAAQ,CAAC,MAAM;gBACrB,UAAU;aACX,CAAC;YACF,MAAM,IAAI,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAEjE,MAAM,aAAa,GAAG,aAAO,CAAC,MAAM,EAAE,CAAC;YACvC,MAAM,cAAc,GAAG,WAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAE1D,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;gBAC1B,aAAa,CAAC,OAAO,GAAG,EAAE,CAAC;aAC5B;iBAAM;gBACL,oEAAoE;gBACpE,oCAAoC;gBACpC,aAAa,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;aAClE;YACD,iBAAW,CAAC,MAAM,CAAC,cAAc,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;YAE1D,OAAO,aAAO,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE;gBACvC;;;mBAGG;gBACH,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACjC,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;oBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,aAAO,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;iBACzD;gBAED,MAAM,OAAO,GAAuB,IAAA,wCAAsB,EACxD,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,CAAC,EACpD,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,EAAE;wBACT,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;wBACpC,eAAe,CAAC,cAAc,CAC5B,IAAI,EACJ,cAAQ,CAAC,MAAM,EACf,SAAS,EACT,gBAAgB,CACjB,CAAC;wBACF,MAAM,KAAK,CAAC;qBACb;gBACH,CAAC,CACF,CAAC;gBAEF,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,GAAG,SAAS,kCAAkC,CAC/C,CAAC;gBACF,aAAO,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;gBACrC,OAAO,eAAe,CAAC,mBAAmB,CACxC,OAAO,EACP,IAAI,EACJ,SAAS,EACT,gBAAgB,CACjB,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAC7B,OAA6B,EAC7B,QAA6B,EAC7B,IAAU,EACV,gBAAkC,EAClC,SAAiB;QAEjB,MAAM,UAAU,GAAG,KAAK,CAAC,sCAAsC,CAC7D,OAAO,EACP,QAAQ,CACT,CAAC;QACF,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAC9B,gBAAgB,EAChB,KAAK,CAAC,4CAA4C,CAAC,UAAU,CAAC,CAC/D,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAC/D,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAC3B,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC;YACvC,IAAI,EAAE,KAAK,CAAC,mBAAmB,CAAC,cAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;SACtE,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,yCAAkB,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,MAAM,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,CAAC;SACxD;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,2BAA2B,EAAE;YACjD,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,IAAI,CAAC,UAAU,EAAE,CAAC,2BAA4B,CAC5C,IAAI,EACJ,OAAO,EACP,QAAQ,CACT,EACH,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;SACH;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,cAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;IAC1E,CAAC;IAEO,sBAAsB,CAC5B,IAAU,EACV,gBAAkC,EAClC,SAAiB,EACjB,KAAU;QAEV,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,cAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;IAC1E,CAAC;IAEO,cAAc,CACpB,IAAY,EACZ,OAAoB,EACpB,GAAG,GAAG,aAAO,CAAC,MAAM,EAAE;QAEtB;;;WAGG;QACH,MAAM,aAAa,GACjB,OAAO,CAAC,IAAI,KAAK,cAAQ,CAAC,MAAM;YAC9B,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,6BAA6B;YACjD,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,6BAA6B,CAAC;QAEtD,IAAI,IAAU,CAAC;QACf,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEvC,IAAI,aAAa,KAAK,IAAI,IAAI,WAAW,KAAK,SAAS,EAAE;YACvD,IAAI,GAAG,WAAK,CAAC,eAAe,CAAC,0BAAoB,CAAC,CAAC;SACpD;aAAM,IAAI,aAAa,KAAK,IAAI,KAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,WAAW,GAAG,QAAQ,CAAA,EAAE;YACxE,IAAI,GAAG,WAAW,CAAC;SACpB;aAAM;YACL,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;SAClD;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,cAAc,CACpB,IAAU,EACV,QAAkB,EAClB,SAAiB,EACjB,gBAAkC;QAElC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACjC,OAAO;SACR;QAED,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEhC,iBAAiB;QACjB,MAAM,QAAQ,GAAG,IAAA,2BAAoB,EAAC,IAAA,qBAAc,EAAC,SAAS,EAAE,IAAA,aAAM,GAAE,CAAC,CAAC,CAAC;QAC3E,IAAI,QAAQ,KAAK,cAAQ,CAAC,MAAM,EAAE;YAChC,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;SACtE;aAAM,IAAI,QAAQ,KAAK,cAAQ,CAAC,MAAM,EAAE;YACvC,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;SACtE;IACH,CAAC;IAEO,iBAAiB,CACvB,IAAU,EACV,QAAoD;QAEpD,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,YAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,EACrD,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,gBAAgB,CACtB,IAAU,EACV,OAAkD;QAElD,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,WAAY,CAAC,IAAI,EAAE,OAAO,CAAC,EACnD,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,kBAAkB,CACxB,OAAmD,EACnD,QAA8B;QAE9B,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,OAAO,IAAA,wCAAsB,EAC3B,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EACvB,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACL,CAAC;SACH;IACH,CAAC;IAEO,oBAAoB;;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAEjC,OAAO;YACL,MAAM,EAAE;gBACN,qBAAqB,EAAE,KAAK,CAAC,aAAa,CACxC,SAAS,EACT,MAAA,MAAA,MAAA,MAAM,CAAC,uBAAuB,0CAAE,MAAM,0CAAE,cAAc,mCAAI,EAAE,CAC7D;gBACD,sBAAsB,EAAE,KAAK,CAAC,aAAa,CACzC,UAAU,EACV,MAAA,MAAA,MAAA,MAAM,CAAC,uBAAuB,0CAAE,MAAM,0CAAE,eAAe,mCAAI,EAAE,CAC9D;aACF;YACD,MAAM,EAAE;gBACN,qBAAqB,EAAE,KAAK,CAAC,aAAa,CACxC,SAAS,EACT,MAAA,MAAA,MAAA,MAAM,CAAC,uBAAuB,0CAAE,MAAM,0CAAE,cAAc,mCAAI,EAAE,CAC7D;gBACD,sBAAsB,EAAE,KAAK,CAAC,aAAa,CACzC,UAAU,EACV,MAAA,MAAA,MAAA,MAAM,CAAC,uBAAuB,0CAAE,MAAM,0CAAE,eAAe,mCAAI,EAAE,CAC9D;aACF;SACF,CAAC;IACJ,CAAC;CACF;AAzzBD,kDAyzBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  context,\n  HrTime,\n  INVALID_SPAN_CONTEXT,\n  propagation,\n  ROOT_CONTEXT,\n  Span,\n  SpanKind,\n  SpanOptions,\n  SpanStatus,\n  SpanStatusCode,\n  trace,\n  Histogram,\n  MetricAttributes,\n  ValueType,\n} from '@opentelemetry/api';\nimport {\n  hrTime,\n  hrTimeDuration,\n  hrTimeToMilliseconds,\n  suppressTracing,\n} from '@opentelemetry/core';\nimport type * as http from 'http';\nimport type * as https from 'https';\nimport { Socket } from 'net';\nimport * as semver from 'semver';\nimport * as url from 'url';\nimport {\n  Err,\n  Func,\n  Http,\n  HttpInstrumentationConfig,\n  HttpRequestArgs,\n  Https,\n} from './types';\nimport * as utils from './utils';\nimport { VERSION } from './version';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { RPCMetadata, RPCType, setRPCMetadata } from '@opentelemetry/core';\nimport { errorMonitor } from 'events';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\n\n/**\n * Http instrumentation instrumentation for Opentelemetry\n */\nexport class HttpInstrumentation extends InstrumentationBase<Http> {\n  /** keep track on spans not ended */\n  private readonly _spanNotEnded: WeakSet<Span> = new WeakSet<Span>();\n  private _headerCapture;\n  private _httpServerDurationHistogram!: Histogram;\n  private _httpClientDurationHistogram!: Histogram;\n\n  constructor(config?: HttpInstrumentationConfig) {\n    super('@opentelemetry/instrumentation-http', VERSION, config);\n    this._headerCapture = this._createHeaderCapture();\n  }\n\n  protected override _updateMetricInstruments() {\n    this._httpServerDurationHistogram = this.meter.createHistogram(\n      'http.server.duration',\n      {\n        description: 'Measures the duration of inbound HTTP requests.',\n        unit: 'ms',\n        valueType: ValueType.DOUBLE,\n      }\n    );\n    this._httpClientDurationHistogram = this.meter.createHistogram(\n      'http.client.duration',\n      {\n        description: 'Measures the duration of outbound HTTP requests.',\n        unit: 'ms',\n        valueType: ValueType.DOUBLE,\n      }\n    );\n  }\n\n  private _getConfig(): HttpInstrumentationConfig {\n    return this._config;\n  }\n\n  override setConfig(config?: HttpInstrumentationConfig): void {\n    super.setConfig(config);\n    this._headerCapture = this._createHeaderCapture();\n  }\n\n  init(): [\n    InstrumentationNodeModuleDefinition<Https>,\n    InstrumentationNodeModuleDefinition<Http>,\n  ] {\n    return [this._getHttpsInstrumentation(), this._getHttpInstrumentation()];\n  }\n\n  private _getHttpInstrumentation() {\n    const version = process.versions.node;\n    return new InstrumentationNodeModuleDefinition<Http>(\n      'http',\n      ['*'],\n      moduleExports => {\n        this._diag.debug(`Applying patch for http@${version}`);\n        if (isWrapped(moduleExports.request)) {\n          this._unwrap(moduleExports, 'request');\n        }\n        this._wrap(\n          moduleExports,\n          'request',\n          this._getPatchOutgoingRequestFunction('http')\n        );\n        if (isWrapped(moduleExports.get)) {\n          this._unwrap(moduleExports, 'get');\n        }\n        this._wrap(\n          moduleExports,\n          'get',\n          this._getPatchOutgoingGetFunction(moduleExports.request)\n        );\n        if (isWrapped(moduleExports.Server.prototype.emit)) {\n          this._unwrap(moduleExports.Server.prototype, 'emit');\n        }\n        this._wrap(\n          moduleExports.Server.prototype,\n          'emit',\n          this._getPatchIncomingRequestFunction('http')\n        );\n        return moduleExports;\n      },\n      moduleExports => {\n        if (moduleExports === undefined) return;\n        this._diag.debug(`Removing patch for http@${version}`);\n\n        this._unwrap(moduleExports, 'request');\n        this._unwrap(moduleExports, 'get');\n        this._unwrap(moduleExports.Server.prototype, 'emit');\n      }\n    );\n  }\n\n  private _getHttpsInstrumentation() {\n    const version = process.versions.node;\n    return new InstrumentationNodeModuleDefinition<Https>(\n      'https',\n      ['*'],\n      moduleExports => {\n        this._diag.debug(`Applying patch for https@${version}`);\n        if (isWrapped(moduleExports.request)) {\n          this._unwrap(moduleExports, 'request');\n        }\n        this._wrap(\n          moduleExports,\n          'request',\n          this._getPatchHttpsOutgoingRequestFunction('https')\n        );\n        if (isWrapped(moduleExports.get)) {\n          this._unwrap(moduleExports, 'get');\n        }\n        this._wrap(\n          moduleExports,\n          'get',\n          this._getPatchHttpsOutgoingGetFunction(moduleExports.request)\n        );\n        if (isWrapped(moduleExports.Server.prototype.emit)) {\n          this._unwrap(moduleExports.Server.prototype, 'emit');\n        }\n        this._wrap(\n          moduleExports.Server.prototype,\n          'emit',\n          this._getPatchIncomingRequestFunction('https')\n        );\n        return moduleExports;\n      },\n      moduleExports => {\n        if (moduleExports === undefined) return;\n        this._diag.debug(`Removing patch for https@${version}`);\n\n        this._unwrap(moduleExports, 'request');\n        this._unwrap(moduleExports, 'get');\n        this._unwrap(moduleExports.Server.prototype, 'emit');\n      }\n    );\n  }\n\n  /**\n   * Creates spans for incoming requests, restoring spans' context if applied.\n   */\n  protected _getPatchIncomingRequestFunction(component: 'http' | 'https') {\n    return (\n      original: (event: string, ...args: unknown[]) => boolean\n    ): ((this: unknown, event: string, ...args: unknown[]) => boolean) => {\n      return this._incomingRequestFunction(component, original);\n    };\n  }\n\n  /**\n   * Creates spans for outgoing requests, sending spans' context for distributed\n   * tracing.\n   */\n  protected _getPatchOutgoingRequestFunction(component: 'http' | 'https') {\n    return (original: Func<http.ClientRequest>): Func<http.ClientRequest> => {\n      return this._outgoingRequestFunction(component, original);\n    };\n  }\n\n  protected _getPatchOutgoingGetFunction(\n    clientRequest: (\n      options: http.RequestOptions | string | url.URL,\n      ...args: HttpRequestArgs\n    ) => http.ClientRequest\n  ) {\n    return (_original: Func<http.ClientRequest>): Func<http.ClientRequest> => {\n      // Re-implement http.get. This needs to be done (instead of using\n      // getPatchOutgoingRequestFunction to patch it) because we need to\n      // set the trace context header before the returned http.ClientRequest is\n      // ended. The Node.js docs state that the only differences between\n      // request and get are that (1) get defaults to the HTTP GET method and\n      // (2) the returned request object is ended immediately. The former is\n      // already true (at least in supported Node versions up to v10), so we\n      // simply follow the latter. Ref:\n      // https://nodejs.org/dist/latest/docs/api/http.html#http_http_get_options_callback\n      // https://github.com/googleapis/cloud-trace-nodejs/blob/master/src/instrumentations/instrumentation-http.ts#L198\n      return function outgoingGetRequest<\n        T extends http.RequestOptions | string | url.URL,\n      >(options: T, ...args: HttpRequestArgs): http.ClientRequest {\n        const req = clientRequest(options, ...args);\n        req.end();\n        return req;\n      };\n    };\n  }\n\n  /** Patches HTTPS outgoing requests */\n  private _getPatchHttpsOutgoingRequestFunction(component: 'http' | 'https') {\n    return (original: Func<http.ClientRequest>): Func<http.ClientRequest> => {\n      const instrumentation = this;\n      return function httpsOutgoingRequest(\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        options: https.RequestOptions | string | URL,\n        ...args: HttpRequestArgs\n      ): http.ClientRequest {\n        // Makes sure options will have default HTTPS parameters\n        if (\n          component === 'https' &&\n          typeof options === 'object' &&\n          options?.constructor?.name !== 'URL'\n        ) {\n          options = Object.assign({}, options);\n          instrumentation._setDefaultOptions(options);\n        }\n        return instrumentation._getPatchOutgoingRequestFunction(component)(\n          original\n        )(options, ...args);\n      };\n    };\n  }\n\n  private _setDefaultOptions(options: https.RequestOptions) {\n    options.protocol = options.protocol || 'https:';\n    options.port = options.port || 443;\n  }\n\n  /** Patches HTTPS outgoing get requests */\n  private _getPatchHttpsOutgoingGetFunction(\n    clientRequest: (\n      // eslint-disable-next-line node/no-unsupported-features/node-builtins\n      options: http.RequestOptions | string | URL,\n      ...args: HttpRequestArgs\n    ) => http.ClientRequest\n  ) {\n    return (original: Func<http.ClientRequest>): Func<http.ClientRequest> => {\n      const instrumentation = this;\n      return function httpsOutgoingRequest(\n        // eslint-disable-next-line node/no-unsupported-features/node-builtins\n        options: https.RequestOptions | string | URL,\n        ...args: HttpRequestArgs\n      ): http.ClientRequest {\n        return instrumentation._getPatchOutgoingGetFunction(clientRequest)(\n          original\n        )(options, ...args);\n      };\n    };\n  }\n\n  /**\n   * Attach event listeners to a client request to end span and add span attributes.\n   *\n   * @param request The original request object.\n   * @param span representing the current operation\n   * @param startTime representing the start time of the request to calculate duration in Metric\n   * @param metricAttributes metric attributes\n   */\n  private _traceClientRequest(\n    request: http.ClientRequest,\n    span: Span,\n    startTime: HrTime,\n    metricAttributes: MetricAttributes\n  ): http.ClientRequest {\n    if (this._getConfig().requestHook) {\n      this._callRequestHook(span, request);\n    }\n\n    /**\n     * Determines if the request has errored or the response has ended/errored.\n     */\n    let responseFinished = false;\n\n    /*\n     * User 'response' event listeners can be added before our listener,\n     * force our listener to be the first, so response emitter is bound\n     * before any user listeners are added to it.\n     */\n    request.prependListener(\n      'response',\n      (response: http.IncomingMessage & { aborted?: boolean }) => {\n        this._diag.debug('outgoingRequest on response()');\n        if (request.listenerCount('response') <= 1) {\n          response.resume();\n        }\n        const responseAttributes =\n          utils.getOutgoingRequestAttributesOnResponse(response);\n        span.setAttributes(responseAttributes);\n        metricAttributes = Object.assign(\n          metricAttributes,\n          utils.getOutgoingRequestMetricAttributesOnResponse(responseAttributes)\n        );\n\n        if (this._getConfig().responseHook) {\n          this._callResponseHook(span, response);\n        }\n\n        this._headerCapture.client.captureRequestHeaders(span, header =>\n          request.getHeader(header)\n        );\n        this._headerCapture.client.captureResponseHeaders(\n          span,\n          header => response.headers[header]\n        );\n\n        context.bind(context.active(), response);\n\n        const endHandler = () => {\n          this._diag.debug('outgoingRequest on end()');\n          if (responseFinished) {\n            return;\n          }\n          responseFinished = true;\n          let status: SpanStatus;\n\n          if (response.aborted && !response.complete) {\n            status = { code: SpanStatusCode.ERROR };\n          } else {\n            status = {\n              code: utils.parseResponseStatus(\n                SpanKind.CLIENT,\n                response.statusCode\n              ),\n            };\n          }\n\n          span.setStatus(status);\n\n          if (this._getConfig().applyCustomAttributesOnSpan) {\n            safeExecuteInTheMiddle(\n              () =>\n                this._getConfig().applyCustomAttributesOnSpan!(\n                  span,\n                  request,\n                  response\n                ),\n              () => {},\n              true\n            );\n          }\n\n          this._closeHttpSpan(\n            span,\n            SpanKind.CLIENT,\n            startTime,\n            metricAttributes\n          );\n        };\n\n        response.on('end', endHandler);\n        // See https://github.com/open-telemetry/opentelemetry-js/pull/3625#issuecomment-1475673533\n        if (semver.lt(process.version, '16.0.0')) {\n          response.on('close', endHandler);\n        }\n        response.on(errorMonitor, (error: Err) => {\n          this._diag.debug('outgoingRequest on error()', error);\n          if (responseFinished) {\n            return;\n          }\n          responseFinished = true;\n          utils.setSpanWithError(span, error);\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: error.message,\n          });\n          this._closeHttpSpan(\n            span,\n            SpanKind.CLIENT,\n            startTime,\n            metricAttributes\n          );\n        });\n      }\n    );\n    request.on('close', () => {\n      this._diag.debug('outgoingRequest on request close()');\n      if (request.aborted || responseFinished) {\n        return;\n      }\n      responseFinished = true;\n      this._closeHttpSpan(span, SpanKind.CLIENT, startTime, metricAttributes);\n    });\n    request.on(errorMonitor, (error: Err) => {\n      this._diag.debug('outgoingRequest on request error()', error);\n      if (responseFinished) {\n        return;\n      }\n      responseFinished = true;\n      utils.setSpanWithError(span, error);\n      this._closeHttpSpan(span, SpanKind.CLIENT, startTime, metricAttributes);\n    });\n\n    this._diag.debug('http.ClientRequest return request');\n    return request;\n  }\n\n  private _incomingRequestFunction(\n    component: 'http' | 'https',\n    original: (event: string, ...args: unknown[]) => boolean\n  ) {\n    const instrumentation = this;\n    return function incomingRequest(\n      this: unknown,\n      event: string,\n      ...args: unknown[]\n    ): boolean {\n      // Only traces request events\n      if (event !== 'request') {\n        return original.apply(this, [event, ...args]);\n      }\n\n      const request = args[0] as http.IncomingMessage;\n      const response = args[1] as http.ServerResponse & { socket: Socket };\n      const pathname = request.url\n        ? url.parse(request.url).pathname || '/'\n        : '/';\n      const method = request.method || 'GET';\n\n      instrumentation._diag.debug(\n        `${component} instrumentation incomingRequest`\n      );\n\n      if (\n        utils.isIgnored(\n          pathname,\n          instrumentation._getConfig().ignoreIncomingPaths,\n          (e: unknown) =>\n            instrumentation._diag.error('caught ignoreIncomingPaths error: ', e)\n        ) ||\n        safeExecuteInTheMiddle(\n          () =>\n            instrumentation._getConfig().ignoreIncomingRequestHook?.(request),\n          (e: unknown) => {\n            if (e != null) {\n              instrumentation._diag.error(\n                'caught ignoreIncomingRequestHook error: ',\n                e\n              );\n            }\n          },\n          true\n        )\n      ) {\n        return context.with(suppressTracing(context.active()), () => {\n          context.bind(context.active(), request);\n          context.bind(context.active(), response);\n          return original.apply(this, [event, ...args]);\n        });\n      }\n\n      const headers = request.headers;\n\n      const spanAttributes = utils.getIncomingRequestAttributes(request, {\n        component: component,\n        serverName: instrumentation._getConfig().serverName,\n        hookAttributes: instrumentation._callStartSpanHook(\n          request,\n          instrumentation._getConfig().startIncomingSpanHook\n        ),\n      });\n\n      const spanOptions: SpanOptions = {\n        kind: SpanKind.SERVER,\n        attributes: spanAttributes,\n      };\n\n      const startTime = hrTime();\n      const metricAttributes =\n        utils.getIncomingRequestMetricAttributes(spanAttributes);\n\n      const ctx = propagation.extract(ROOT_CONTEXT, headers);\n      const span = instrumentation._startHttpSpan(method, spanOptions, ctx);\n      const rpcMetadata: RPCMetadata = {\n        type: RPCType.HTTP,\n        span,\n      };\n\n      return context.with(\n        setRPCMetadata(trace.setSpan(ctx, span), rpcMetadata),\n        () => {\n          context.bind(context.active(), request);\n          context.bind(context.active(), response);\n\n          if (instrumentation._getConfig().requestHook) {\n            instrumentation._callRequestHook(span, request);\n          }\n          if (instrumentation._getConfig().responseHook) {\n            instrumentation._callResponseHook(span, response);\n          }\n\n          instrumentation._headerCapture.server.captureRequestHeaders(\n            span,\n            header => request.headers[header]\n          );\n\n          // After 'error', no further events other than 'close' should be emitted.\n          let hasError = false;\n          response.on('close', () => {\n            if (hasError) {\n              return;\n            }\n            instrumentation._onServerResponseFinish(\n              request,\n              response,\n              span,\n              metricAttributes,\n              startTime\n            );\n          });\n          response.on(errorMonitor, (err: Err) => {\n            hasError = true;\n            instrumentation._onServerResponseError(\n              span,\n              metricAttributes,\n              startTime,\n              err\n            );\n          });\n\n          return safeExecuteInTheMiddle(\n            () => original.apply(this, [event, ...args]),\n            error => {\n              if (error) {\n                utils.setSpanWithError(span, error);\n                instrumentation._closeHttpSpan(\n                  span,\n                  SpanKind.SERVER,\n                  startTime,\n                  metricAttributes\n                );\n                throw error;\n              }\n            }\n          );\n        }\n      );\n    };\n  }\n\n  private _outgoingRequestFunction(\n    component: 'http' | 'https',\n    original: Func<http.ClientRequest>\n  ): Func<http.ClientRequest> {\n    const instrumentation = this;\n    return function outgoingRequest(\n      this: unknown,\n      options: url.URL | http.RequestOptions | string,\n      ...args: unknown[]\n    ): http.ClientRequest {\n      if (!utils.isValidOptionsType(options)) {\n        return original.apply(this, [options, ...args]);\n      }\n      const extraOptions =\n        typeof args[0] === 'object' &&\n        (typeof options === 'string' || options instanceof url.URL)\n          ? (args.shift() as http.RequestOptions)\n          : undefined;\n      const { origin, pathname, method, optionsParsed } = utils.getRequestInfo(\n        options,\n        extraOptions\n      );\n      /**\n       * Node 8's https module directly call the http one so to avoid creating\n       * 2 span for the same request we need to check that the protocol is correct\n       * See: https://github.com/nodejs/node/blob/v8.17.0/lib/https.js#L245\n       */\n      if (\n        component === 'http' &&\n        semver.lt(process.version, '9.0.0') &&\n        optionsParsed.protocol === 'https:'\n      ) {\n        return original.apply(this, [optionsParsed, ...args]);\n      }\n\n      if (\n        utils.isIgnored(\n          origin + pathname,\n          instrumentation._getConfig().ignoreOutgoingUrls,\n          (e: unknown) =>\n            instrumentation._diag.error('caught ignoreOutgoingUrls error: ', e)\n        ) ||\n        safeExecuteInTheMiddle(\n          () =>\n            instrumentation\n              ._getConfig()\n              .ignoreOutgoingRequestHook?.(optionsParsed),\n          (e: unknown) => {\n            if (e != null) {\n              instrumentation._diag.error(\n                'caught ignoreOutgoingRequestHook error: ',\n                e\n              );\n            }\n          },\n          true\n        )\n      ) {\n        return original.apply(this, [optionsParsed, ...args]);\n      }\n\n      const { hostname, port } = utils.extractHostnameAndPort(optionsParsed);\n\n      const attributes = utils.getOutgoingRequestAttributes(optionsParsed, {\n        component,\n        port,\n        hostname,\n        hookAttributes: instrumentation._callStartSpanHook(\n          optionsParsed,\n          instrumentation._getConfig().startOutgoingSpanHook\n        ),\n      });\n\n      const startTime = hrTime();\n      const metricAttributes: MetricAttributes =\n        utils.getOutgoingRequestMetricAttributes(attributes);\n\n      const spanOptions: SpanOptions = {\n        kind: SpanKind.CLIENT,\n        attributes,\n      };\n      const span = instrumentation._startHttpSpan(method, spanOptions);\n\n      const parentContext = context.active();\n      const requestContext = trace.setSpan(parentContext, span);\n\n      if (!optionsParsed.headers) {\n        optionsParsed.headers = {};\n      } else {\n        // Make a copy of the headers object to avoid mutating an object the\n        // caller might have a reference to.\n        optionsParsed.headers = Object.assign({}, optionsParsed.headers);\n      }\n      propagation.inject(requestContext, optionsParsed.headers);\n\n      return context.with(requestContext, () => {\n        /*\n         * The response callback is registered before ClientRequest is bound,\n         * thus it is needed to bind it before the function call.\n         */\n        const cb = args[args.length - 1];\n        if (typeof cb === 'function') {\n          args[args.length - 1] = context.bind(parentContext, cb);\n        }\n\n        const request: http.ClientRequest = safeExecuteInTheMiddle(\n          () => original.apply(this, [optionsParsed, ...args]),\n          error => {\n            if (error) {\n              utils.setSpanWithError(span, error);\n              instrumentation._closeHttpSpan(\n                span,\n                SpanKind.CLIENT,\n                startTime,\n                metricAttributes\n              );\n              throw error;\n            }\n          }\n        );\n\n        instrumentation._diag.debug(\n          `${component} instrumentation outgoingRequest`\n        );\n        context.bind(parentContext, request);\n        return instrumentation._traceClientRequest(\n          request,\n          span,\n          startTime,\n          metricAttributes\n        );\n      });\n    };\n  }\n\n  private _onServerResponseFinish(\n    request: http.IncomingMessage,\n    response: http.ServerResponse,\n    span: Span,\n    metricAttributes: MetricAttributes,\n    startTime: HrTime\n  ) {\n    const attributes = utils.getIncomingRequestAttributesOnResponse(\n      request,\n      response\n    );\n    metricAttributes = Object.assign(\n      metricAttributes,\n      utils.getIncomingRequestMetricAttributesOnResponse(attributes)\n    );\n\n    this._headerCapture.server.captureResponseHeaders(span, header =>\n      response.getHeader(header)\n    );\n\n    span.setAttributes(attributes).setStatus({\n      code: utils.parseResponseStatus(SpanKind.SERVER, response.statusCode),\n    });\n\n    const route = attributes[SemanticAttributes.HTTP_ROUTE];\n    if (route) {\n      span.updateName(`${request.method || 'GET'} ${route}`);\n    }\n\n    if (this._getConfig().applyCustomAttributesOnSpan) {\n      safeExecuteInTheMiddle(\n        () =>\n          this._getConfig().applyCustomAttributesOnSpan!(\n            span,\n            request,\n            response\n          ),\n        () => {},\n        true\n      );\n    }\n\n    this._closeHttpSpan(span, SpanKind.SERVER, startTime, metricAttributes);\n  }\n\n  private _onServerResponseError(\n    span: Span,\n    metricAttributes: MetricAttributes,\n    startTime: HrTime,\n    error: Err\n  ) {\n    utils.setSpanWithError(span, error);\n    this._closeHttpSpan(span, SpanKind.SERVER, startTime, metricAttributes);\n  }\n\n  private _startHttpSpan(\n    name: string,\n    options: SpanOptions,\n    ctx = context.active()\n  ) {\n    /*\n     * If a parent is required but not present, we use a `NoopSpan` to still\n     * propagate context without recording it.\n     */\n    const requireParent =\n      options.kind === SpanKind.CLIENT\n        ? this._getConfig().requireParentforOutgoingSpans\n        : this._getConfig().requireParentforIncomingSpans;\n\n    let span: Span;\n    const currentSpan = trace.getSpan(ctx);\n\n    if (requireParent === true && currentSpan === undefined) {\n      span = trace.wrapSpanContext(INVALID_SPAN_CONTEXT);\n    } else if (requireParent === true && currentSpan?.spanContext().isRemote) {\n      span = currentSpan;\n    } else {\n      span = this.tracer.startSpan(name, options, ctx);\n    }\n    this._spanNotEnded.add(span);\n    return span;\n  }\n\n  private _closeHttpSpan(\n    span: Span,\n    spanKind: SpanKind,\n    startTime: HrTime,\n    metricAttributes: MetricAttributes\n  ) {\n    if (!this._spanNotEnded.has(span)) {\n      return;\n    }\n\n    span.end();\n    this._spanNotEnded.delete(span);\n\n    // Record metrics\n    const duration = hrTimeToMilliseconds(hrTimeDuration(startTime, hrTime()));\n    if (spanKind === SpanKind.SERVER) {\n      this._httpServerDurationHistogram.record(duration, metricAttributes);\n    } else if (spanKind === SpanKind.CLIENT) {\n      this._httpClientDurationHistogram.record(duration, metricAttributes);\n    }\n  }\n\n  private _callResponseHook(\n    span: Span,\n    response: http.IncomingMessage | http.ServerResponse\n  ) {\n    safeExecuteInTheMiddle(\n      () => this._getConfig().responseHook!(span, response),\n      () => {},\n      true\n    );\n  }\n\n  private _callRequestHook(\n    span: Span,\n    request: http.ClientRequest | http.IncomingMessage\n  ) {\n    safeExecuteInTheMiddle(\n      () => this._getConfig().requestHook!(span, request),\n      () => {},\n      true\n    );\n  }\n\n  private _callStartSpanHook(\n    request: http.IncomingMessage | http.RequestOptions,\n    hookFunc: Function | undefined\n  ) {\n    if (typeof hookFunc === 'function') {\n      return safeExecuteInTheMiddle(\n        () => hookFunc(request),\n        () => {},\n        true\n      );\n    }\n  }\n\n  private _createHeaderCapture() {\n    const config = this._getConfig();\n\n    return {\n      client: {\n        captureRequestHeaders: utils.headerCapture(\n          'request',\n          config.headersToSpanAttributes?.client?.requestHeaders ?? []\n        ),\n        captureResponseHeaders: utils.headerCapture(\n          'response',\n          config.headersToSpanAttributes?.client?.responseHeaders ?? []\n        ),\n      },\n      server: {\n        captureRequestHeaders: utils.headerCapture(\n          'request',\n          config.headersToSpanAttributes?.server?.requestHeaders ?? []\n        ),\n        captureResponseHeaders: utils.headerCapture(\n          'response',\n          config.headersToSpanAttributes?.server?.responseHeaders ?? []\n        ),\n      },\n    };\n  }\n}\n"]}