{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAO4B;AAC5B,8EAG6C;AAQ7C,8CAA8D;AAC9D,2BAA2B;AAC3B,2DAAwD;AAGxD;;GAEG;AACI,MAAM,cAAc,GAAG,CAC5B,UAAuC,EACvC,OAAkD,EAClD,gBAAgB,GAAG,OAAO,EAClB,EAAE;IACV,MAAM,YAAY,GAAG,UAAU,IAAI,EAAE,CAAC;IACtC,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,IAAI,gBAAgB,CAAC;IAC3D,MAAM,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;IAClD,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,IAAI,GAAG,CAAC;IACtC,IAAI,IAAI,GACN,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,QAAQ,IAAI,OAAO,CAAC,IAAI,IAAI,WAAW,CAAC;IAE5E,kDAAkD;IAClD,gEAAgE;IAChE,IACG,IAAe,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI;QACJ,IAAI,KAAK,IAAI;QACb,IAAI,KAAK,KAAK,EACd;QACA,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;KACpB;IAED,OAAO,GAAG,QAAQ,KAAK,IAAI,GAAG,IAAI,EAAE,CAAC;AACvC,CAAC,CAAC;AAxBW,QAAA,cAAc,kBAwBzB;AAEF;;GAEG;AACI,MAAM,mBAAmB,GAAG,CACjC,IAAc,EACd,UAAmB,EACH,EAAE;IAClB,MAAM,UAAU,GAAG,IAAI,KAAK,cAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACxD,4CAA4C;IAC5C,sBAAsB;IACtB,IAAI,UAAU,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,UAAU,EAAE;QAC9D,OAAO,oBAAc,CAAC,KAAK,CAAC;KAC7B;IAED,4BAA4B;IAC5B,OAAO,oBAAc,CAAC,KAAK,CAAC;AAC9B,CAAC,CAAC;AAbW,QAAA,mBAAmB,uBAa9B;AAEF;;;;GAIG;AACI,MAAM,gBAAgB,GAAG,CAC9B,QAAgB,EAChB,OAAsB,EACb,EAAE;IACX,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO,OAAO,KAAK,QAAQ,CAAC;KAC7B;SAAM,IAAI,OAAO,YAAY,MAAM,EAAE;QACpC,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B;SAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;QACxC,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC1B;SAAM;QACL,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;KAC3D;AACH,CAAC,CAAC;AAbW,QAAA,gBAAgB,oBAa3B;AAEF;;;;;;;GAOG;AACI,MAAM,SAAS,GAAG,CACvB,QAAgB,EAChB,IAAsB,EACtB,WAAsC,EAC7B,EAAE;IACX,IAAI,CAAC,IAAI,EAAE;QACT,qCAAqC;QACrC,OAAO,KAAK,CAAC;KACd;IACD,8CAA8C;IAC9C,IAAI;QACF,KAAK,MAAM,OAAO,IAAI,IAAI,EAAE;YAC1B,IAAI,IAAA,wBAAgB,EAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;gBACvC,OAAO,IAAI,CAAC;aACb;SACF;KACF;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,CAAC,CAAC,CAAC;SAChB;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAvBW,QAAA,SAAS,aAuBpB;AAEF;;;;GAIG;AACI,MAAM,gBAAgB,GAAG,CAAC,IAAU,EAAE,KAAU,EAAQ,EAAE;IAC/D,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAE9B,IAAI,CAAC,aAAa,CAAC;QACjB,CAAC,+BAAc,CAAC,eAAe,CAAC,EAAE,KAAK,CAAC,IAAI;QAC5C,CAAC,+BAAc,CAAC,kBAAkB,CAAC,EAAE,OAAO;KAC7C,CAAC,CAAC;IAEH,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,oBAAc,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IACxD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC,CAAC;AAVW,QAAA,gBAAgB,oBAU3B;AAEF;;;;GAIG;AACI,MAAM,gCAAgC,GAAG,CAC9C,OAAwB,EACxB,UAA0B,EACpB,EAAE;IACR,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACjD,IAAI,MAAM,KAAK,IAAI;QAAE,OAAO;IAE5B,IAAI,IAAA,oBAAY,EAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QACjC,UAAU,CAAC,yCAAkB,CAAC,2BAA2B,CAAC,GAAG,MAAM,CAAC;KACrE;SAAM;QACL,UAAU,CAAC,yCAAkB,CAAC,wCAAwC,CAAC;YACrE,MAAM,CAAC;KACV;AACH,CAAC,CAAC;AAbW,QAAA,gCAAgC,oCAa3C;AAEF;;;;GAIG;AACI,MAAM,iCAAiC,GAAG,CAC/C,QAAyB,EACzB,UAA0B,EACpB,EAAE;IACR,MAAM,MAAM,GAAG,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAClD,IAAI,MAAM,KAAK,IAAI;QAAE,OAAO;IAE5B,IAAI,IAAA,oBAAY,EAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAClC,UAAU,CAAC,yCAAkB,CAAC,4BAA4B,CAAC,GAAG,MAAM,CAAC;KACtE;SAAM;QACL,UAAU,CAAC,yCAAkB,CAAC,yCAAyC,CAAC;YACtE,MAAM,CAAC;KACV;AACH,CAAC,CAAC;AAbW,QAAA,iCAAiC,qCAa5C;AAEF,SAAS,gBAAgB,CACvB,OAAkD;IAElD,MAAM,mBAAmB,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACtD,IAAI,mBAAmB,KAAK,SAAS;QAAE,OAAO,IAAI,CAAC;IAEnD,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAA6B,EAAE,EAAE,CAAC,CAAC;IAClE,IAAI,KAAK,CAAC,aAAa,CAAC;QAAE,OAAO,IAAI,CAAC;IAEtC,OAAO,aAAa,CAAC;AACvB,CAAC;AAEM,MAAM,YAAY,GAAG,CAC1B,OAAkD,EACzC,EAAE;IACX,MAAM,QAAQ,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAE7C,OAAO,CAAC,CAAC,QAAQ,IAAI,QAAQ,KAAK,UAAU,CAAC;AAC/C,CAAC,CAAC;AANW,QAAA,YAAY,gBAMvB;AAEF;;;;;GAKG;AACI,MAAM,cAAc,GAAG,CAC5B,OAA0C,EAC1C,YAA6B,EAM7B,EAAE;IACF,IAAI,QAAQ,GAAG,GAAG,CAAC;IACnB,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,aAA6B,CAAC;IAClC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,aAAa,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACnC,QAAQ,GAAI,aAAwC,CAAC,QAAQ,IAAI,GAAG,CAAC;QACrE,MAAM,GAAG,GAAG,aAAa,CAAC,QAAQ,IAAI,OAAO,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC;QACvE,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SAC5C;KACF;SAAM,IAAI,OAAO,YAAY,GAAG,CAAC,GAAG,EAAE;QACrC,aAAa,GAAG;YACd,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,QAAQ,EACN,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;gBACtE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/B,CAAC,CAAC,OAAO,CAAC,QAAQ;YACtB,IAAI,EAAE,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE;SACzD,CAAC;QACF,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,aAAa,CAAC,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC3C;QACD,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,EAAE;YACxC,aAAa,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;SAChE;QACD,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC5B,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACxB,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SAC5C;KACF;SAAM;QACL,aAAa,GAAG,MAAM,CAAC,MAAM,CAC3B,EAAE,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,EAAE,EAChD,OAAO,CACR,CAAC;QACF,QAAQ,GAAI,OAAmB,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,IAAI,EAAE;YACnC,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,GAAG,CAAC;SAC1D;QACD,MAAM,QAAQ,GACZ,aAAa,CAAC,IAAI;YAClB,CAAC,aAAa,CAAC,IAAI,IAAI,IAAI;gBACzB,CAAC,CAAC,GAAG,aAAa,CAAC,QAAQ,GAAG,aAAa,CAAC,IAAI,EAAE;gBAClD,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,aAAa,CAAC,QAAQ,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;KAC9D;IAED,6CAA6C;IAC7C,mCAAmC;IACnC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM;QACjC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE;QACpC,CAAC,CAAC,KAAK,CAAC;IAEV,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;AACrD,CAAC,CAAC;AA/DW,QAAA,cAAc,kBA+DzB;AAEF;;;GAGG;AACI,MAAM,kBAAkB,GAAG,CAAC,OAAgB,EAAW,EAAE;IAC9D,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,KAAK,CAAC;KACd;IAED,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC;IAC5B,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7E,CAAC,CAAC;AAPW,QAAA,kBAAkB,sBAO7B;AAEK,MAAM,sBAAsB,GAAG,CACpC,cAGC,EAC4C,EAAE;;IAC/C,IAAI,cAAc,CAAC,QAAQ,IAAI,cAAc,CAAC,IAAI,EAAE;QAClD,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE,cAAc,CAAC,IAAI,EAAE,CAAC;KACzE;IACD,MAAM,OAAO,GAAG,CAAA,MAAA,cAAc,CAAC,IAAI,0CAAE,KAAK,CAAC,uBAAuB,CAAC,KAAI,IAAI,CAAC;IAC5E,MAAM,QAAQ,GACZ,cAAc,CAAC,QAAQ,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,IAAI,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;IAC/B,IAAI,CAAC,IAAI,EAAE;QACT,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;YACzB,6EAA6E;YAC7E,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SAChC;aAAM;YACL,IAAI,GAAG,cAAc,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;SAC5D;KACF;IACD,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC5B,CAAC,CAAC;AAtBW,QAAA,sBAAsB,0BAsBjC;AAEF;;;;GAIG;AACI,MAAM,4BAA4B,GAAG,CAC1C,cAAoC,EACpC,OAKC,EACe,EAAE;;IAClB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAClC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC;IAC5C,MAAM,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IACnE,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,IAAI,EAAE,CAAC;IAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;IACxC,MAAM,UAAU,GAAmB;QACjC,CAAC,yCAAkB,CAAC,QAAQ,CAAC,EAAE,IAAA,sBAAc,EAC3C,cAAc,EACd,OAAO,EACP,GAAG,OAAO,CAAC,SAAS,GAAG,CACxB;QACD,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,MAAM;QACxC,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,cAAc,CAAC,IAAI,IAAI,GAAG;QAC5D,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,QAAQ;QAC5C,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAC5B,MAAA,MAAA,cAAc,CAAC,OAAO,0CAAE,IAAI,mCAAI,GAAG,QAAQ,IAAI,IAAI,EAAE;KACxD,CAAC;IAEF,IAAI,SAAS,KAAK,SAAS,EAAE;QAC3B,UAAU,CAAC,yCAAkB,CAAC,eAAe,CAAC,GAAG,SAAS,CAAC;KAC5D;IACD,OAAO,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;AAC3D,CAAC,CAAC;AAhCW,QAAA,4BAA4B,gCAgCvC;AAEF;;;GAGG;AACI,MAAM,kCAAkC,GAAG,CAChD,cAA8B,EACZ,EAAE;IACpB,MAAM,gBAAgB,GAAqB,EAAE,CAAC;IAC9C,gBAAgB,CAAC,yCAAkB,CAAC,WAAW,CAAC;QAC9C,cAAc,CAAC,yCAAkB,CAAC,WAAW,CAAC,CAAC;IACjD,gBAAgB,CAAC,yCAAkB,CAAC,aAAa,CAAC;QAChD,cAAc,CAAC,yCAAkB,CAAC,aAAa,CAAC,CAAC;IACnD,0FAA0F;IAC1F,OAAO,gBAAgB,CAAC;AAC1B,CAAC,CAAC;AAVW,QAAA,kCAAkC,sCAU7C;AAEF;;;GAGG;AACI,MAAM,yBAAyB,GAAG,CAAC,IAAa,EAAkB,EAAE;IACzE,MAAM,UAAU,GAAmB,EAAE,CAAC;IACtC,IAAI,IAAI,EAAE;QACR,UAAU,CAAC,yCAAkB,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QAClD,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;YACjC,UAAU,CAAC,yCAAkB,CAAC,aAAa,CAAC,GAAG,yCAAkB,CAAC,MAAM,CAAC;SAC1E;aAAM;YACL,UAAU,CAAC,yCAAkB,CAAC,aAAa,CAAC,GAAG,yCAAkB,CAAC,MAAM,CAAC;SAC1E;KACF;IACD,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AAXW,QAAA,yBAAyB,6BAWpC;AAEF;;;;GAIG;AACI,MAAM,sCAAsC,GAAG,CACpD,QAAyB,EACT,EAAE;IAClB,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IACpE,MAAM,UAAU,GAAmB,EAAE,CAAC;IACtC,IAAI,MAAM,EAAE;QACV,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC7C,UAAU,CAAC,yCAAkB,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;QAC3D,UAAU,CAAC,yCAAkB,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC;KAC3D;IACD,IAAA,yCAAiC,EAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAExD,IAAI,UAAU,EAAE;QACd,UAAU,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAC;QAC7D,UAAU,CAAC,+BAAc,CAAC,gBAAgB,CAAC,GAAG,CAC5C,aAAa,IAAI,EAAE,CACpB,CAAC,WAAW,EAAE,CAAC;KACjB;IAED,MAAM,kBAAkB,GAAG,IAAA,iCAAyB,EAAC,WAAW,CAAC,CAAC;IAClE,OAAO,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;AACvD,CAAC,CAAC;AArBW,QAAA,sCAAsC,0CAqBjD;AAEF;;;GAGG;AACI,MAAM,4CAA4C,GAAG,CAC1D,cAA8B,EACZ,EAAE;IACpB,MAAM,gBAAgB,GAAqB,EAAE,CAAC;IAC9C,gBAAgB,CAAC,yCAAkB,CAAC,aAAa,CAAC;QAChD,cAAc,CAAC,yCAAkB,CAAC,aAAa,CAAC,CAAC;IACnD,gBAAgB,CAAC,yCAAkB,CAAC,gBAAgB,CAAC;QACnD,cAAc,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,CAAC;IACtD,gBAAgB,CAAC,yCAAkB,CAAC,WAAW,CAAC;QAC9C,cAAc,CAAC,yCAAkB,CAAC,WAAW,CAAC,CAAC;IACjD,OAAO,gBAAgB,CAAC;AAC1B,CAAC,CAAC;AAXW,QAAA,4CAA4C,gDAWvD;AAEF;;;;GAIG;AACI,MAAM,4BAA4B,GAAG,CAC1C,OAAwB,EACxB,OAIC,EACe,EAAE;IAClB,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAChC,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;IACxC,MAAM,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACvC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC;IACvC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;IACxC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/D,MAAM,IAAI,GAAG,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,KAAI,OAAO,CAAC,IAAI,CAAC;IAC9C,MAAM,QAAQ,GACZ,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ;SACpB,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAA;QACzC,WAAW,CAAC;IACd,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IACtC,MAAM,UAAU,GAAmB;QACjC,CAAC,yCAAkB,CAAC,QAAQ,CAAC,EAAE,IAAA,sBAAc,EAC3C,UAAU,EACV,OAAO,EACP,GAAG,OAAO,CAAC,SAAS,GAAG,CACxB;QACD,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,IAAI;QACpC,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,QAAQ;QAC5C,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,MAAM;QACxC,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,SAAS;KACpD,CAAC;IAEF,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,UAAU,CAAC,yCAAkB,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACnE;IAED,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;QAClC,UAAU,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAC;KAC9D;IAED,IAAI,UAAU,EAAE;QACd,UAAU,CAAC,yCAAkB,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC,IAAI,IAAI,GAAG,CAAC;KACrE;IAED,IAAI,SAAS,KAAK,SAAS,EAAE;QAC3B,UAAU,CAAC,yCAAkB,CAAC,eAAe,CAAC,GAAG,SAAS,CAAC;KAC5D;IACD,IAAA,wCAAgC,EAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAEtD,MAAM,kBAAkB,GAAG,IAAA,iCAAyB,EAAC,WAAW,CAAC,CAAC;IAClE,OAAO,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,kBAAkB,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;AAC/E,CAAC,CAAC;AAnDW,QAAA,4BAA4B,gCAmDvC;AAEF;;;;GAIG;AACI,MAAM,kCAAkC,GAAG,CAChD,cAA8B,EACZ,EAAE;IACpB,MAAM,gBAAgB,GAAqB,EAAE,CAAC;IAC9C,gBAAgB,CAAC,yCAAkB,CAAC,WAAW,CAAC;QAC9C,cAAc,CAAC,yCAAkB,CAAC,WAAW,CAAC,CAAC;IACjD,gBAAgB,CAAC,yCAAkB,CAAC,WAAW,CAAC;QAC9C,cAAc,CAAC,yCAAkB,CAAC,WAAW,CAAC,CAAC;IACjD,gBAAgB,CAAC,yCAAkB,CAAC,aAAa,CAAC;QAChD,cAAc,CAAC,yCAAkB,CAAC,aAAa,CAAC,CAAC;IACnD,gBAAgB,CAAC,yCAAkB,CAAC,WAAW,CAAC;QAC9C,cAAc,CAAC,yCAAkB,CAAC,WAAW,CAAC,CAAC;IACjD,6FAA6F;IAC7F,OAAO,gBAAgB,CAAC;AAC1B,CAAC,CAAC;AAdW,QAAA,kCAAkC,sCAc7C;AAEF;;;GAGG;AACI,MAAM,sCAAsC,GAAG,CACpD,OAAwB,EACxB,QAAwB,EACR,EAAE;IAClB,gCAAgC;IAChC,uEAAuE;IACvE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAC3B,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,QAAQ,CAAC;IAE/C,MAAM,WAAW,GAAG,IAAA,qBAAc,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IACrD,MAAM,UAAU,GAAmB,EAAE,CAAC;IACtC,IAAI,MAAM,EAAE;QACV,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QACtE,UAAU,CAAC,yCAAkB,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;QAC1D,UAAU,CAAC,yCAAkB,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;QACzD,UAAU,CAAC,yCAAkB,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;QAC3D,UAAU,CAAC,yCAAkB,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC;KAC3D;IACD,UAAU,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAC;IAC7D,UAAU,CAAC,+BAAc,CAAC,gBAAgB,CAAC,GAAG,CAC5C,aAAa,IAAI,EAAE,CACpB,CAAC,WAAW,EAAE,CAAC;IAEhB,IAAI,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,MAAK,cAAO,CAAC,IAAI,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,EAAE;QACzE,UAAU,CAAC,yCAAkB,CAAC,UAAU,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;KAC/D;IACD,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AA3BW,QAAA,sCAAsC,0CA2BjD;AAEF;;;GAGG;AACI,MAAM,4CAA4C,GAAG,CAC1D,cAA8B,EACZ,EAAE;IACpB,MAAM,gBAAgB,GAAqB,EAAE,CAAC;IAC9C,gBAAgB,CAAC,yCAAkB,CAAC,gBAAgB,CAAC;QACnD,cAAc,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,CAAC;IACtD,gBAAgB,CAAC,yCAAkB,CAAC,aAAa,CAAC;QAChD,cAAc,CAAC,yCAAkB,CAAC,aAAa,CAAC,CAAC;IACnD,IAAI,cAAc,CAAC,yCAAkB,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;QAC/D,gBAAgB,CAAC,yCAAkB,CAAC,UAAU,CAAC;YAC7C,cAAc,CAAC,yCAAkB,CAAC,UAAU,CAAC,CAAC;KACjD;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC,CAAC;AAbW,QAAA,4CAA4C,gDAavD;AAEF,SAAgB,aAAa,CAAC,IAA4B,EAAE,OAAiB;IAC3E,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAC/B,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QACpB,MAAM,CAAC,WAAW,EAAE;QACpB,MAAM,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;KACxC,CAAC,CACH,CAAC;IAEF,OAAO,CACL,IAAU,EACV,SAAkE,EAClE,EAAE;QACF,KAAK,MAAM,CAAC,cAAc,EAAE,gBAAgB,CAAC,IAAI,iBAAiB,EAAE;YAClE,MAAM,KAAK,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC;YAExC,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,SAAS;aACV;YAED,MAAM,GAAG,GAAG,QAAQ,IAAI,WAAW,gBAAgB,EAAE,CAAC;YAEtD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;aACjC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aAC/B;iBAAM;gBACL,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;aACjC;SACF;IACH,CAAC,CAAC;AACJ,CAAC;AA9BD,sCA8BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  MetricAttributes,\n  SpanAttributes,\n  SpanStatusCode,\n  Span,\n  context,\n  SpanKind,\n} from '@opentelemetry/api';\nimport {\n  NetTransportValues,\n  SemanticAttributes,\n} from '@opentelemetry/semantic-conventions';\nimport {\n  IncomingHttpHeaders,\n  IncomingMessage,\n  OutgoingHttpHeaders,\n  RequestOptions,\n  ServerResponse,\n} from 'http';\nimport { getRPCMetadata, RPCType } from '@opentelemetry/core';\nimport * as url from 'url';\nimport { AttributeNames } from './enums/AttributeNames';\nimport { Err, IgnoreMatcher, ParsedRequestOptions } from './types';\n\n/**\n * Get an absolute url\n */\nexport const getAbsoluteUrl = (\n  requestUrl: ParsedRequestOptions | null,\n  headers: IncomingHttpHeaders | OutgoingHttpHeaders,\n  fallbackProtocol = 'http:'\n): string => {\n  const reqUrlObject = requestUrl || {};\n  const protocol = reqUrlObject.protocol || fallbackProtocol;\n  const port = (reqUrlObject.port || '').toString();\n  const path = reqUrlObject.path || '/';\n  let host =\n    reqUrlObject.host || reqUrlObject.hostname || headers.host || 'localhost';\n\n  // if there is no port in host and there is a port\n  // it should be displayed if it's not 80 and 443 (default ports)\n  if (\n    (host as string).indexOf(':') === -1 &&\n    port &&\n    port !== '80' &&\n    port !== '443'\n  ) {\n    host += `:${port}`;\n  }\n\n  return `${protocol}//${host}${path}`;\n};\n\n/**\n * Parse status code from HTTP response. [More details](https://github.com/open-telemetry/opentelemetry-specification/blob/master/specification/data-http.md#status)\n */\nexport const parseResponseStatus = (\n  kind: SpanKind,\n  statusCode?: number\n): SpanStatusCode => {\n  const upperBound = kind === SpanKind.CLIENT ? 400 : 500;\n  // 1xx, 2xx, 3xx are OK on client and server\n  // 4xx is OK on server\n  if (statusCode && statusCode >= 100 && statusCode < upperBound) {\n    return SpanStatusCode.UNSET;\n  }\n\n  // All other codes are error\n  return SpanStatusCode.ERROR;\n};\n\n/**\n * Check whether the given obj match pattern\n * @param constant e.g URL of request\n * @param pattern Match pattern\n */\nexport const satisfiesPattern = (\n  constant: string,\n  pattern: IgnoreMatcher\n): boolean => {\n  if (typeof pattern === 'string') {\n    return pattern === constant;\n  } else if (pattern instanceof RegExp) {\n    return pattern.test(constant);\n  } else if (typeof pattern === 'function') {\n    return pattern(constant);\n  } else {\n    throw new TypeError('Pattern is in unsupported datatype');\n  }\n};\n\n/**\n * Check whether the given request is ignored by configuration\n * It will not re-throw exceptions from `list` provided by the client\n * @param constant e.g URL of request\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nexport const isIgnored = (\n  constant: string,\n  list?: IgnoreMatcher[],\n  onException?: (error: unknown) => void\n): boolean => {\n  if (!list) {\n    // No ignored urls - trace everything\n    return false;\n  }\n  // Try/catch outside the loop for failing fast\n  try {\n    for (const pattern of list) {\n      if (satisfiesPattern(constant, pattern)) {\n        return true;\n      }\n    }\n  } catch (e) {\n    if (onException) {\n      onException(e);\n    }\n  }\n\n  return false;\n};\n\n/**\n * Sets the span with the error passed in params\n * @param {Span} span the span that need to be set\n * @param {Error} error error that will be set to span\n */\nexport const setSpanWithError = (span: Span, error: Err): void => {\n  const message = error.message;\n\n  span.setAttributes({\n    [AttributeNames.HTTP_ERROR_NAME]: error.name,\n    [AttributeNames.HTTP_ERROR_MESSAGE]: message,\n  });\n\n  span.setStatus({ code: SpanStatusCode.ERROR, message });\n  span.recordException(error);\n};\n\n/**\n * Adds attributes for request content-length and content-encoding HTTP headers\n * @param { IncomingMessage } Request object whose headers will be analyzed\n * @param { SpanAttributes } SpanAttributes object to be modified\n */\nexport const setRequestContentLengthAttribute = (\n  request: IncomingMessage,\n  attributes: SpanAttributes\n): void => {\n  const length = getContentLength(request.headers);\n  if (length === null) return;\n\n  if (isCompressed(request.headers)) {\n    attributes[SemanticAttributes.HTTP_REQUEST_CONTENT_LENGTH] = length;\n  } else {\n    attributes[SemanticAttributes.HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED] =\n      length;\n  }\n};\n\n/**\n * Adds attributes for response content-length and content-encoding HTTP headers\n * @param { IncomingMessage } Response object whose headers will be analyzed\n * @param { SpanAttributes } SpanAttributes object to be modified\n */\nexport const setResponseContentLengthAttribute = (\n  response: IncomingMessage,\n  attributes: SpanAttributes\n): void => {\n  const length = getContentLength(response.headers);\n  if (length === null) return;\n\n  if (isCompressed(response.headers)) {\n    attributes[SemanticAttributes.HTTP_RESPONSE_CONTENT_LENGTH] = length;\n  } else {\n    attributes[SemanticAttributes.HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED] =\n      length;\n  }\n};\n\nfunction getContentLength(\n  headers: OutgoingHttpHeaders | IncomingHttpHeaders\n): number | null {\n  const contentLengthHeader = headers['content-length'];\n  if (contentLengthHeader === undefined) return null;\n\n  const contentLength = parseInt(contentLengthHeader as string, 10);\n  if (isNaN(contentLength)) return null;\n\n  return contentLength;\n}\n\nexport const isCompressed = (\n  headers: OutgoingHttpHeaders | IncomingHttpHeaders\n): boolean => {\n  const encoding = headers['content-encoding'];\n\n  return !!encoding && encoding !== 'identity';\n};\n\n/**\n * Makes sure options is an url object\n * return an object with default value and parsed options\n * @param options original options for the request\n * @param [extraOptions] additional options for the request\n */\nexport const getRequestInfo = (\n  options: url.URL | RequestOptions | string,\n  extraOptions?: RequestOptions\n): {\n  origin: string;\n  pathname: string;\n  method: string;\n  optionsParsed: RequestOptions;\n} => {\n  let pathname = '/';\n  let origin = '';\n  let optionsParsed: RequestOptions;\n  if (typeof options === 'string') {\n    optionsParsed = url.parse(options);\n    pathname = (optionsParsed as url.UrlWithStringQuery).pathname || '/';\n    origin = `${optionsParsed.protocol || 'http:'}//${optionsParsed.host}`;\n    if (extraOptions !== undefined) {\n      Object.assign(optionsParsed, extraOptions);\n    }\n  } else if (options instanceof url.URL) {\n    optionsParsed = {\n      protocol: options.protocol,\n      hostname:\n        typeof options.hostname === 'string' && options.hostname.startsWith('[')\n          ? options.hostname.slice(1, -1)\n          : options.hostname,\n      path: `${options.pathname || ''}${options.search || ''}`,\n    };\n    if (options.port !== '') {\n      optionsParsed.port = Number(options.port);\n    }\n    if (options.username || options.password) {\n      optionsParsed.auth = `${options.username}:${options.password}`;\n    }\n    pathname = options.pathname;\n    origin = options.origin;\n    if (extraOptions !== undefined) {\n      Object.assign(optionsParsed, extraOptions);\n    }\n  } else {\n    optionsParsed = Object.assign(\n      { protocol: options.host ? 'http:' : undefined },\n      options\n    );\n    pathname = (options as url.URL).pathname;\n    if (!pathname && optionsParsed.path) {\n      pathname = url.parse(optionsParsed.path).pathname || '/';\n    }\n    const hostname =\n      optionsParsed.host ||\n      (optionsParsed.port != null\n        ? `${optionsParsed.hostname}${optionsParsed.port}`\n        : optionsParsed.hostname);\n    origin = `${optionsParsed.protocol || 'http:'}//${hostname}`;\n  }\n\n  // some packages return method in lowercase..\n  // ensure upperCase for consistency\n  const method = optionsParsed.method\n    ? optionsParsed.method.toUpperCase()\n    : 'GET';\n\n  return { origin, pathname, method, optionsParsed };\n};\n\n/**\n * Makes sure options is of type string or object\n * @param options for the request\n */\nexport const isValidOptionsType = (options: unknown): boolean => {\n  if (!options) {\n    return false;\n  }\n\n  const type = typeof options;\n  return type === 'string' || (type === 'object' && !Array.isArray(options));\n};\n\nexport const extractHostnameAndPort = (\n  requestOptions: Pick<\n    ParsedRequestOptions,\n    'hostname' | 'host' | 'port' | 'protocol'\n  >\n): { hostname: string; port: number | string } => {\n  if (requestOptions.hostname && requestOptions.port) {\n    return { hostname: requestOptions.hostname, port: requestOptions.port };\n  }\n  const matches = requestOptions.host?.match(/^([^:/ ]+)(:\\d{1,5})?/) || null;\n  const hostname =\n    requestOptions.hostname || (matches === null ? 'localhost' : matches[1]);\n  let port = requestOptions.port;\n  if (!port) {\n    if (matches && matches[2]) {\n      // remove the leading \":\". The extracted port would be something like \":8080\"\n      port = matches[2].substring(1);\n    } else {\n      port = requestOptions.protocol === 'https:' ? '443' : '80';\n    }\n  }\n  return { hostname, port };\n};\n\n/**\n * Returns outgoing request attributes scoped to the options passed to the request\n * @param {ParsedRequestOptions} requestOptions the same options used to make the request\n * @param {{ component: string, hostname: string, hookAttributes?: SpanAttributes }} options used to pass data needed to create attributes\n */\nexport const getOutgoingRequestAttributes = (\n  requestOptions: ParsedRequestOptions,\n  options: {\n    component: string;\n    hostname: string;\n    port: string | number;\n    hookAttributes?: SpanAttributes;\n  }\n): SpanAttributes => {\n  const hostname = options.hostname;\n  const port = options.port;\n  const requestMethod = requestOptions.method;\n  const method = requestMethod ? requestMethod.toUpperCase() : 'GET';\n  const headers = requestOptions.headers || {};\n  const userAgent = headers['user-agent'];\n  const attributes: SpanAttributes = {\n    [SemanticAttributes.HTTP_URL]: getAbsoluteUrl(\n      requestOptions,\n      headers,\n      `${options.component}:`\n    ),\n    [SemanticAttributes.HTTP_METHOD]: method,\n    [SemanticAttributes.HTTP_TARGET]: requestOptions.path || '/',\n    [SemanticAttributes.NET_PEER_NAME]: hostname,\n    [SemanticAttributes.HTTP_HOST]:\n      requestOptions.headers?.host ?? `${hostname}:${port}`,\n  };\n\n  if (userAgent !== undefined) {\n    attributes[SemanticAttributes.HTTP_USER_AGENT] = userAgent;\n  }\n  return Object.assign(attributes, options.hookAttributes);\n};\n\n/**\n * Returns outgoing request Metric attributes scoped to the request data\n * @param {SpanAttributes} spanAttributes the span attributes\n */\nexport const getOutgoingRequestMetricAttributes = (\n  spanAttributes: SpanAttributes\n): MetricAttributes => {\n  const metricAttributes: MetricAttributes = {};\n  metricAttributes[SemanticAttributes.HTTP_METHOD] =\n    spanAttributes[SemanticAttributes.HTTP_METHOD];\n  metricAttributes[SemanticAttributes.NET_PEER_NAME] =\n    spanAttributes[SemanticAttributes.NET_PEER_NAME];\n  //TODO: http.url attribute, it should susbtitute any parameters to avoid high cardinality.\n  return metricAttributes;\n};\n\n/**\n * Returns attributes related to the kind of HTTP protocol used\n * @param {string} [kind] Kind of HTTP protocol used: \"1.0\", \"1.1\", \"2\", \"SPDY\" or \"QUIC\".\n */\nexport const getAttributesFromHttpKind = (kind?: string): SpanAttributes => {\n  const attributes: SpanAttributes = {};\n  if (kind) {\n    attributes[SemanticAttributes.HTTP_FLAVOR] = kind;\n    if (kind.toUpperCase() !== 'QUIC') {\n      attributes[SemanticAttributes.NET_TRANSPORT] = NetTransportValues.IP_TCP;\n    } else {\n      attributes[SemanticAttributes.NET_TRANSPORT] = NetTransportValues.IP_UDP;\n    }\n  }\n  return attributes;\n};\n\n/**\n * Returns outgoing request attributes scoped to the response data\n * @param {IncomingMessage} response the response object\n * @param {{ hostname: string }} options used to pass data needed to create attributes\n */\nexport const getOutgoingRequestAttributesOnResponse = (\n  response: IncomingMessage\n): SpanAttributes => {\n  const { statusCode, statusMessage, httpVersion, socket } = response;\n  const attributes: SpanAttributes = {};\n  if (socket) {\n    const { remoteAddress, remotePort } = socket;\n    attributes[SemanticAttributes.NET_PEER_IP] = remoteAddress;\n    attributes[SemanticAttributes.NET_PEER_PORT] = remotePort;\n  }\n  setResponseContentLengthAttribute(response, attributes);\n\n  if (statusCode) {\n    attributes[SemanticAttributes.HTTP_STATUS_CODE] = statusCode;\n    attributes[AttributeNames.HTTP_STATUS_TEXT] = (\n      statusMessage || ''\n    ).toUpperCase();\n  }\n\n  const httpKindAttributes = getAttributesFromHttpKind(httpVersion);\n  return Object.assign(attributes, httpKindAttributes);\n};\n\n/**\n * Returns outgoing request Metric attributes scoped to the response data\n * @param {SpanAttributes} spanAttributes the span attributes\n */\nexport const getOutgoingRequestMetricAttributesOnResponse = (\n  spanAttributes: SpanAttributes\n): MetricAttributes => {\n  const metricAttributes: MetricAttributes = {};\n  metricAttributes[SemanticAttributes.NET_PEER_PORT] =\n    spanAttributes[SemanticAttributes.NET_PEER_PORT];\n  metricAttributes[SemanticAttributes.HTTP_STATUS_CODE] =\n    spanAttributes[SemanticAttributes.HTTP_STATUS_CODE];\n  metricAttributes[SemanticAttributes.HTTP_FLAVOR] =\n    spanAttributes[SemanticAttributes.HTTP_FLAVOR];\n  return metricAttributes;\n};\n\n/**\n * Returns incoming request attributes scoped to the request data\n * @param {IncomingMessage} request the request object\n * @param {{ component: string, serverName?: string, hookAttributes?: SpanAttributes }} options used to pass data needed to create attributes\n */\nexport const getIncomingRequestAttributes = (\n  request: IncomingMessage,\n  options: {\n    component: string;\n    serverName?: string;\n    hookAttributes?: SpanAttributes;\n  }\n): SpanAttributes => {\n  const headers = request.headers;\n  const userAgent = headers['user-agent'];\n  const ips = headers['x-forwarded-for'];\n  const method = request.method || 'GET';\n  const httpVersion = request.httpVersion;\n  const requestUrl = request.url ? url.parse(request.url) : null;\n  const host = requestUrl?.host || headers.host;\n  const hostname =\n    requestUrl?.hostname ||\n    host?.replace(/^(.*)(:[0-9]{1,5})/, '$1') ||\n    'localhost';\n  const serverName = options.serverName;\n  const attributes: SpanAttributes = {\n    [SemanticAttributes.HTTP_URL]: getAbsoluteUrl(\n      requestUrl,\n      headers,\n      `${options.component}:`\n    ),\n    [SemanticAttributes.HTTP_HOST]: host,\n    [SemanticAttributes.NET_HOST_NAME]: hostname,\n    [SemanticAttributes.HTTP_METHOD]: method,\n    [SemanticAttributes.HTTP_SCHEME]: options.component,\n  };\n\n  if (typeof ips === 'string') {\n    attributes[SemanticAttributes.HTTP_CLIENT_IP] = ips.split(',')[0];\n  }\n\n  if (typeof serverName === 'string') {\n    attributes[SemanticAttributes.HTTP_SERVER_NAME] = serverName;\n  }\n\n  if (requestUrl) {\n    attributes[SemanticAttributes.HTTP_TARGET] = requestUrl.path || '/';\n  }\n\n  if (userAgent !== undefined) {\n    attributes[SemanticAttributes.HTTP_USER_AGENT] = userAgent;\n  }\n  setRequestContentLengthAttribute(request, attributes);\n\n  const httpKindAttributes = getAttributesFromHttpKind(httpVersion);\n  return Object.assign(attributes, httpKindAttributes, options.hookAttributes);\n};\n\n/**\n * Returns incoming request Metric attributes scoped to the request data\n * @param {SpanAttributes} spanAttributes the span attributes\n * @param {{ component: string }} options used to pass data needed to create attributes\n */\nexport const getIncomingRequestMetricAttributes = (\n  spanAttributes: SpanAttributes\n): MetricAttributes => {\n  const metricAttributes: MetricAttributes = {};\n  metricAttributes[SemanticAttributes.HTTP_SCHEME] =\n    spanAttributes[SemanticAttributes.HTTP_SCHEME];\n  metricAttributes[SemanticAttributes.HTTP_METHOD] =\n    spanAttributes[SemanticAttributes.HTTP_METHOD];\n  metricAttributes[SemanticAttributes.NET_HOST_NAME] =\n    spanAttributes[SemanticAttributes.NET_HOST_NAME];\n  metricAttributes[SemanticAttributes.HTTP_FLAVOR] =\n    spanAttributes[SemanticAttributes.HTTP_FLAVOR];\n  //TODO: http.target attribute, it should susbtitute any parameters to avoid high cardinality.\n  return metricAttributes;\n};\n\n/**\n * Returns incoming request attributes scoped to the response data\n * @param {(ServerResponse & { socket: Socket; })} response the response object\n */\nexport const getIncomingRequestAttributesOnResponse = (\n  request: IncomingMessage,\n  response: ServerResponse\n): SpanAttributes => {\n  // take socket from the request,\n  // since it may be detached from the response object in keep-alive mode\n  const { socket } = request;\n  const { statusCode, statusMessage } = response;\n\n  const rpcMetadata = getRPCMetadata(context.active());\n  const attributes: SpanAttributes = {};\n  if (socket) {\n    const { localAddress, localPort, remoteAddress, remotePort } = socket;\n    attributes[SemanticAttributes.NET_HOST_IP] = localAddress;\n    attributes[SemanticAttributes.NET_HOST_PORT] = localPort;\n    attributes[SemanticAttributes.NET_PEER_IP] = remoteAddress;\n    attributes[SemanticAttributes.NET_PEER_PORT] = remotePort;\n  }\n  attributes[SemanticAttributes.HTTP_STATUS_CODE] = statusCode;\n  attributes[AttributeNames.HTTP_STATUS_TEXT] = (\n    statusMessage || ''\n  ).toUpperCase();\n\n  if (rpcMetadata?.type === RPCType.HTTP && rpcMetadata.route !== undefined) {\n    attributes[SemanticAttributes.HTTP_ROUTE] = rpcMetadata.route;\n  }\n  return attributes;\n};\n\n/**\n * Returns incoming request Metric attributes scoped to the request data\n * @param {SpanAttributes} spanAttributes the span attributes\n */\nexport const getIncomingRequestMetricAttributesOnResponse = (\n  spanAttributes: SpanAttributes\n): MetricAttributes => {\n  const metricAttributes: MetricAttributes = {};\n  metricAttributes[SemanticAttributes.HTTP_STATUS_CODE] =\n    spanAttributes[SemanticAttributes.HTTP_STATUS_CODE];\n  metricAttributes[SemanticAttributes.NET_HOST_PORT] =\n    spanAttributes[SemanticAttributes.NET_HOST_PORT];\n  if (spanAttributes[SemanticAttributes.HTTP_ROUTE] !== undefined) {\n    metricAttributes[SemanticAttributes.HTTP_ROUTE] =\n      spanAttributes[SemanticAttributes.HTTP_ROUTE];\n  }\n  return metricAttributes;\n};\n\nexport function headerCapture(type: 'request' | 'response', headers: string[]) {\n  const normalizedHeaders = new Map(\n    headers.map(header => [\n      header.toLowerCase(),\n      header.toLowerCase().replace(/-/g, '_'),\n    ])\n  );\n\n  return (\n    span: Span,\n    getHeader: (key: string) => undefined | string | string[] | number\n  ) => {\n    for (const [capturedHeader, normalizedHeader] of normalizedHeaders) {\n      const value = getHeader(capturedHeader);\n\n      if (value === undefined) {\n        continue;\n      }\n\n      const key = `http.${type}.header.${normalizedHeader}`;\n\n      if (typeof value === 'string') {\n        span.setAttribute(key, [value]);\n      } else if (Array.isArray(value)) {\n        span.setAttribute(key, value);\n      } else {\n        span.setAttribute(key, [value]);\n      }\n    }\n  };\n}\n"]}