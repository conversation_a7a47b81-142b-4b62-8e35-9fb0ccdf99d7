{"version": 3, "file": "composite.js", "sourceRoot": "", "sources": ["../../../src/propagation/composite.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAM4B;AAY5B,8DAA8D;AAC9D,MAAa,mBAAmB;IAI9B;;;;OAIG;IACH,YAAY,SAAoC,EAAE;;QAChD,IAAI,CAAC,YAAY,GAAG,MAAA,MAAM,CAAC,WAAW,mCAAI,EAAE,CAAC;QAE7C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CACvB,IAAI,GAAG,CACL,IAAI,CAAC,YAAY;YACf,wEAAwE;aACvE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aAC5D,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACrC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC9D,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE;YAC1C,IAAI;gBACF,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;aAC7C;YAAC,OAAO,GAAG,EAAE;gBACZ,UAAI,CAAC,IAAI,CACP,yBAAyB,UAAU,CAAC,WAAW,CAAC,IAAI,UAAU,GAAG,CAAC,OAAO,EAAE,CAC5E,CAAC;aACH;SACF;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,OAAO,CAAC,OAAgB,EAAE,OAAgB,EAAE,MAAqB;QAC/D,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;YAClD,IAAI;gBACF,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;aACjD;YAAC,OAAO,GAAG,EAAE;gBACZ,UAAI,CAAC,IAAI,CACP,yBAAyB,UAAU,CAAC,WAAW,CAAC,IAAI,UAAU,GAAG,CAAC,OAAO,EAAE,CAC5E,CAAC;aACH;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED,MAAM;QACJ,sDAAsD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;CACF;AArED,kDAqEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  TextMapGetter,\n  TextMapPropagator,\n  diag,\n  TextMapSetter,\n} from '@opentelemetry/api';\n\n/** Configuration object for composite propagator */\nexport interface CompositePropagatorConfig {\n  /**\n   * List of propagators to run. Propagators run in the\n   * list order. If a propagator later in the list writes the same context\n   * key as a propagator earlier in the list, the later on will \"win\".\n   */\n  propagators?: TextMapPropagator[];\n}\n\n/** Combines multiple propagators into a single propagator. */\nexport class CompositePropagator implements TextMapPropagator {\n  private readonly _propagators: TextMapPropagator[];\n  private readonly _fields: string[];\n\n  /**\n   * Construct a composite propagator from a list of propagators.\n   *\n   * @param [config] Configuration object for composite propagator\n   */\n  constructor(config: CompositePropagatorConfig = {}) {\n    this._propagators = config.propagators ?? [];\n\n    this._fields = Array.from(\n      new Set(\n        this._propagators\n          // older propagators may not have fields function, null check to be sure\n          .map(p => (typeof p.fields === 'function' ? p.fields() : []))\n          .reduce((x, y) => x.concat(y), [])\n      )\n    );\n  }\n\n  /**\n   * Run each of the configured propagators with the given context and carrier.\n   * Propagators are run in the order they are configured, so if multiple\n   * propagators write the same carrier key, the propagator later in the list\n   * will \"win\".\n   *\n   * @param context Context to inject\n   * @param carrier Carrier into which context will be injected\n   */\n  inject(context: Context, carrier: unknown, setter: TextMapSetter): void {\n    for (const propagator of this._propagators) {\n      try {\n        propagator.inject(context, carrier, setter);\n      } catch (err) {\n        diag.warn(\n          `Failed to inject with ${propagator.constructor.name}. Err: ${err.message}`\n        );\n      }\n    }\n  }\n\n  /**\n   * Run each of the configured propagators with the given context and carrier.\n   * Propagators are run in the order they are configured, so if multiple\n   * propagators write the same context key, the propagator later in the list\n   * will \"win\".\n   *\n   * @param context Context to add values to\n   * @param carrier Carrier from which to extract context\n   */\n  extract(context: Context, carrier: unknown, getter: TextMapGetter): Context {\n    return this._propagators.reduce((ctx, propagator) => {\n      try {\n        return propagator.extract(ctx, carrier, getter);\n      } catch (err) {\n        diag.warn(\n          `Failed to inject with ${propagator.constructor.name}. Err: ${err.message}`\n        );\n      }\n      return ctx;\n    }, context);\n  }\n\n  fields(): string[] {\n    // return a new array so our fields cannot be modified\n    return this._fields.slice();\n  }\n}\n"]}