{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/util.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,0EAI2C;AAE3C,kCAAgD;AAEhD,SAAgB,IAAI,CAClB,SAAgE,EAChE,OAAqB,EACrB,WAAiC,EACjC,SAAqB,EACrB,OAA2C;IAE3C,MAAM,cAAc,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAElD,MAAM,iBAAiB,GAAG,IAAA,4BAAqB,EAC7C,SAAS,CAAC,oBAAoB,EAAE,CACjC,CAAC;IACF,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACzD,IAAI,OAAO,EAAE;QACX,MAAM,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;QACxD,IAAI,IAAI,EAAE;YACR,IAAA,iCAAY,EACV,SAAS,EACT,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EACjB,wBAAwB,EACxB,SAAS,EACT,OAAO,CACR,CAAC;SACH;KACF;SAAM;QACL,OAAO,CAAC,IAAI,sCAAiB,CAAC,UAAU,CAAC,CAAC,CAAC;KAC5C;AACH,CAAC;AA3BD,oBA2BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OTLPProtoExporterNodeBase } from './OTLPProtoExporterNodeBase';\nimport {\n  CompressionAlgorithm,\n  OTLPExporterError,\n  sendWithHttp,\n} from '@opentelemetry/otlp-exporter-base';\n\nimport { getExportRequestProto } from '../util';\n\nexport function send<ExportItem, ServiceRequest>(\n  collector: OTLPProtoExporterNodeBase<ExportItem, ServiceRequest>,\n  objects: ExportItem[],\n  compression: CompressionAlgorithm,\n  onSuccess: () => void,\n  onError: (error: OTLPExporterError) => void\n): void {\n  const serviceRequest = collector.convert(objects);\n\n  const exportRequestType = getExportRequestProto<ServiceRequest>(\n    collector.getServiceClientType()\n  );\n  const message = exportRequestType.create(serviceRequest);\n  if (message) {\n    const body = exportRequestType.encode(message).finish();\n    if (body) {\n      sendWithHttp(\n        collector,\n        Buffer.from(body),\n        'application/x-protobuf',\n        onSuccess,\n        onError\n      );\n    }\n  } else {\n    onError(new OTLPExporterError('No proto'));\n  }\n}\n"]}