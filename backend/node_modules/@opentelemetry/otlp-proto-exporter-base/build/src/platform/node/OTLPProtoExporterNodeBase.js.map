{"version": 3, "file": "OTLPProtoExporterNodeBase.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/OTLPProtoExporterNodeBase.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAE1C,0EAK2C;AAU3C;;GAEG;AACH,MAAsB,yBAGpB,SAAQ,yCAAgD;IAGxD,YAAY,SAAqC,EAAE;QACjD,KAAK,CAAC,MAAM,CAAC,CAAC;IAChB,CAAC;IAEO,YAAY,CAClB,OAAqB,EACrB,SAAqB,EACrB,OAA2C;QAE3C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACpD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;IAEQ,IAAI,CACX,OAAqB,EACrB,SAAqB,EACrB,OAA2C;QAE3C,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,UAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,yEAAyE;YACzE,gDAAgD;YAChD,YAAY,CAAC,GAAG,EAAE;gBAChB,8DAA8D;gBAC9D,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBAClB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SAChD;IACH,CAAC;CAGF;AAnDD,8DAmDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { ServiceClientType } from '../types';\nimport {\n  OTLPExporterNodeBase as OTLPExporterBaseMain,\n  CompressionAlgorithm,\n  OTLPExporterError,\n  OTLPExporterNodeConfigBase,\n} from '@opentelemetry/otlp-exporter-base';\n\ntype SendFn = <ExportItem, ServiceRequest>(\n  collector: OTLPProtoExporterNodeBase<ExportItem, ServiceRequest>,\n  objects: ExportItem[],\n  compression: CompressionAlgorithm,\n  onSuccess: () => void,\n  onError: (error: OTLPExporterError) => void\n) => void;\n\n/**\n * Collector Exporter abstract base class\n */\nexport abstract class OTLPProtoExporterNodeBase<\n  ExportItem,\n  ServiceRequest,\n> extends OTLPExporterBaseMain<ExportItem, ServiceRequest> {\n  private _send!: SendFn;\n\n  constructor(config: OTLPExporterNodeConfigBase = {}) {\n    super(config);\n  }\n\n  private _sendPromise(\n    objects: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: OTLPExporterError) => void\n  ): void {\n    const promise = new Promise<void>((resolve, reject) => {\n      this._send(this, objects, this.compression, resolve, reject);\n    }).then(onSuccess, onError);\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n\n  override send(\n    objects: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: OTLPExporterError) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      diag.debug('Shutdown already started. Cannot send objects');\n      return;\n    }\n    if (!this._send) {\n      // defer to next tick and lazy load to avoid loading protobufjs too early\n      // and making this impossible to be instrumented\n      setImmediate(() => {\n        // eslint-disable-next-line @typescript-eslint/no-var-requires\n        const { send } = require('./util');\n        this._send = send;\n        this._sendPromise(objects, onSuccess, onError);\n      });\n    } else {\n      this._sendPromise(objects, onSuccess, onError);\n    }\n  }\n\n  abstract getServiceClientType(): ServiceClientType;\n}\n"]}