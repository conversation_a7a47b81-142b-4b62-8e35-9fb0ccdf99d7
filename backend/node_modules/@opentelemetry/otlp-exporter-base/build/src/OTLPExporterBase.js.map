{"version": 3, "file": "OTLPExporterBase.js", "sourceRoot": "", "sources": ["../../src/OTLPExporterBase.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAC1C,8CAI6B;AAM7B,iCAAkD;AAElD;;GAEG;AACH,MAAsB,gBAAgB;IAYpC;;OAEG;IACH,YAAY,SAAY,EAAO;QANrB,qBAAgB,GAAuB,EAAE,CAAC;QAOlD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACvC,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;SACjC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,IAAI,qBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,CAAC,iBAAiB;YACpB,OAAO,MAAM,CAAC,gBAAgB,KAAK,QAAQ;gBACzC,CAAC,CAAC,MAAM,CAAC,gBAAgB;gBACzB,CAAC,CAAC,QAAQ,CAAC;QAEf,IAAI,CAAC,aAAa,GAAG,IAAA,+BAAwB,EAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEpE,qBAAqB;QACrB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACH,MAAM,CACJ,KAAmB,EACnB,cAA8C;QAE9C,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,cAAc,CAAC;gBACb,IAAI,EAAE,uBAAgB,CAAC,MAAM;gBAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,4BAA4B,CAAC;aAC/C,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1D,cAAc,CAAC;gBACb,IAAI,EAAE,uBAAgB,CAAC,MAAM;gBAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,iCAAiC,CAAC;aACpD,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;aAChB,IAAI,CAAC,GAAG,EAAE;YACT,cAAc,CAAC,EAAE,IAAI,EAAE,uBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,KAAyB,EAAE,EAAE;YACnC,cAAc,CAAC,EAAE,IAAI,EAAE,uBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,OAAO,CAAC,KAAmB;QACjC,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,IAAI;gBACF,UAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBACtC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;aACnC;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,CAAC,CAAC,CAAC,CAAC;aACX;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YAClD,6BAA6B;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,SAAS;QACf,UAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;CAWF;AAlHD,4CAkHC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  ExportResult,\n  ExportResultCode,\n  BindOnceFuture,\n} from '@opentelemetry/core';\nimport {\n  OTLPExporterError,\n  OTLPExporterConfigBase,\n  ExportServiceError,\n} from './types';\nimport { configureExporterTimeout } from './util';\n\n/**\n * Collector Exporter abstract base class\n */\nexport abstract class OTLPExporterBase<\n  T extends OTLPExporterConfigBase,\n  ExportItem,\n  ServiceRequest,\n> {\n  public readonly url: string;\n  public readonly hostname: string | undefined;\n  public readonly timeoutMillis: number;\n  protected _concurrencyLimit: number;\n  protected _sendingPromises: Promise<unknown>[] = [];\n  protected _shutdownOnce: BindOnceFuture<void>;\n\n  /**\n   * @param config\n   */\n  constructor(config: T = {} as T) {\n    this.url = this.getDefaultUrl(config);\n    if (typeof config.hostname === 'string') {\n      this.hostname = config.hostname;\n    }\n\n    this.shutdown = this.shutdown.bind(this);\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n\n    this._concurrencyLimit =\n      typeof config.concurrencyLimit === 'number'\n        ? config.concurrencyLimit\n        : Infinity;\n\n    this.timeoutMillis = configureExporterTimeout(config.timeoutMillis);\n\n    // platform dependent\n    this.onInit(config);\n  }\n\n  /**\n   * Export items.\n   * @param items\n   * @param resultCallback\n   */\n  export(\n    items: ExportItem[],\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      resultCallback({\n        code: ExportResultCode.FAILED,\n        error: new Error('Exporter has been shutdown'),\n      });\n      return;\n    }\n\n    if (this._sendingPromises.length >= this._concurrencyLimit) {\n      resultCallback({\n        code: ExportResultCode.FAILED,\n        error: new Error('Concurrent export limit reached'),\n      });\n      return;\n    }\n\n    this._export(items)\n      .then(() => {\n        resultCallback({ code: ExportResultCode.SUCCESS });\n      })\n      .catch((error: ExportServiceError) => {\n        resultCallback({ code: ExportResultCode.FAILED, error });\n      });\n  }\n\n  private _export(items: ExportItem[]): Promise<unknown> {\n    return new Promise<void>((resolve, reject) => {\n      try {\n        diag.debug('items to be sent', items);\n        this.send(items, resolve, reject);\n      } catch (e) {\n        reject(e);\n      }\n    });\n  }\n\n  /**\n   * Shutdown the exporter.\n   */\n  shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  /**\n   * Exports any pending spans in the exporter\n   */\n  forceFlush(): Promise<void> {\n    return Promise.all(this._sendingPromises).then(() => {\n      /** ignore resolved values */\n    });\n  }\n\n  /**\n   * Called by _shutdownOnce with BindOnceFuture\n   */\n  private _shutdown(): Promise<void> {\n    diag.debug('shutdown started');\n    this.onShutdown();\n    return this.forceFlush();\n  }\n\n  abstract onShutdown(): void;\n  abstract onInit(config: T): void;\n  abstract send(\n    items: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: OTLPExporterError) => void\n  ): void;\n  abstract getDefaultUrl(config: T): string;\n  abstract convert(objects: ExportItem[]): ServiceRequest;\n}\n"]}