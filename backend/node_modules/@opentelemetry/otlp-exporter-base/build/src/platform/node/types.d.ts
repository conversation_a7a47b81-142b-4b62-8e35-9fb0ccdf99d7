/// <reference types="node" />
import type * as http from 'http';
import type * as https from 'https';
import { OTLPExporterConfigBase } from '../../types';
/**
 * Collector Exporter node base config
 */
export interface OTLPExporterNodeConfigBase extends OTLPExporterConfigBase {
    keepAlive?: boolean;
    compression?: CompressionAlgorithm;
    httpAgentOptions?: http.AgentOptions | https.AgentOptions;
}
export declare enum CompressionAlgorithm {
    NONE = "none",
    GZIP = "gzip"
}
//# sourceMappingURL=types.d.ts.map