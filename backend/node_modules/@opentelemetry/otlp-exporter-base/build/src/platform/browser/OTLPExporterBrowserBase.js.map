{"version": 3, "file": "OTLPExporterBrowserBase.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/OTLPExporterBrowserBase.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,6DAA0D;AAG1D,qCAA0C;AAC1C,iCAAqD;AACrD,4CAA0C;AAC1C,8CAAwE;AAExE;;GAEG;AACH,MAAsB,uBAGpB,SAAQ,mCAAoE;IAI5E;;OAEG;IACH,YAAY,SAAiC,EAAE;QAC7C,KAAK,CAAC,MAAM,CAAC,CAAC;QANR,YAAO,GAAY,KAAK,CAAC;QAO/B,IAAI,CAAC,OAAO;YACV,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO,SAAS,CAAC,UAAU,KAAK,UAAU,CAAC;QACjE,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAC3B,EAAE,EACF,IAAA,mBAAY,EAAC,MAAM,CAAC,OAAO,CAAC,EAC5B,mBAAY,CAAC,uBAAuB,CAClC,IAAA,aAAM,GAAE,CAAC,0BAA0B,CACpC,CACF,CAAC;SACH;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;SACpB;IACH,CAAC;IAED,MAAM;QACJ,kBAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED,UAAU;QACR,kBAAW,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,CACF,KAAmB,EACnB,SAAqB,EACrB,OAAqD;QAErD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,UAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO;SACR;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAE5C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACpD,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,IAAA,kBAAW,EACT,IAAI,EACJ,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,aAAa,EAClB,OAAO,EACP,MAAM,CACP,CAAC;aACH;iBAAM;gBACL,IAAA,qBAAc,EACZ,IAAI,EACJ,IAAI,CAAC,GAAG,EACR,EAAE,IAAI,EAAE,kBAAkB,EAAE,EAC5B,OAAO,EACP,MAAM,CACP,CAAC;aACH;QACH,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;CACF;AA3ED,0DA2EC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OTLPExporterBase } from '../../OTLPExporterBase';\nimport { OTLPExporterConfigBase } from '../../types';\nimport * as otlpTypes from '../../types';\nimport { parseHeaders } from '../../util';\nimport { sendWithBeacon, sendWithXhr } from './util';\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, baggageUtils, _globalThis } from '@opentelemetry/core';\n\n/**\n * Collector Metric Exporter abstract base class\n */\nexport abstract class OTLPExporterBrowserBase<\n  ExportItem,\n  ServiceRequest,\n> extends OTLPExporterBase<OTLPExporterConfigBase, ExportItem, ServiceRequest> {\n  protected _headers: Record<string, string>;\n  private _useXHR: boolean = false;\n\n  /**\n   * @param config\n   */\n  constructor(config: OTLPExporterConfigBase = {}) {\n    super(config);\n    this._useXHR =\n      !!config.headers || typeof navigator.sendBeacon !== 'function';\n    if (this._useXHR) {\n      this._headers = Object.assign(\n        {},\n        parseHeaders(config.headers),\n        baggageUtils.parseKeyPairsIntoRecord(\n          getEnv().OTEL_EXPORTER_OTLP_HEADERS\n        )\n      );\n    } else {\n      this._headers = {};\n    }\n  }\n\n  onInit(): void {\n    _globalThis.addEventListener('unload', this.shutdown);\n  }\n\n  onShutdown(): void {\n    _globalThis.removeEventListener('unload', this.shutdown);\n  }\n\n  send(\n    items: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: otlpTypes.OTLPExporterError) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      diag.debug('Shutdown already started. Cannot send objects');\n      return;\n    }\n    const serviceRequest = this.convert(items);\n    const body = JSON.stringify(serviceRequest);\n\n    const promise = new Promise<void>((resolve, reject) => {\n      if (this._useXHR) {\n        sendWithXhr(\n          body,\n          this.url,\n          this._headers,\n          this.timeoutMillis,\n          resolve,\n          reject\n        );\n      } else {\n        sendWithBeacon(\n          body,\n          this.url,\n          { type: 'application/json' },\n          resolve,\n          reject\n        );\n      }\n    }).then(onSuccess, onError);\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n}\n"]}