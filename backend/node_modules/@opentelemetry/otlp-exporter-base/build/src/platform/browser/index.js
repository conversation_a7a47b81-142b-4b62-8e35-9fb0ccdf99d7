"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendWithXhr = exports.OTLPExporterBrowserBase = void 0;
var OTLPExporterBrowserBase_1 = require("./OTLPExporterBrowserBase");
Object.defineProperty(exports, "OTLPExporterBrowserBase", { enumerable: true, get: function () { return OTLPExporterBrowserBase_1.OTLPExporterBrowserBase; } });
var util_1 = require("./util");
Object.defineProperty(exports, "sendWithXhr", { enumerable: true, get: function () { return util_1.sendWithXhr; } });
//# sourceMappingURL=index.js.map