{"version": 3, "file": "wrap.js", "sourceRoot": "", "sources": ["../../../src/utils/wrap.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH;;;GAGG;AACH,SAAgB,SAAS,CAAC,IAAa;IACrC,OAAO,CACL,OAAO,IAAI,KAAK,UAAU;QAC1B,OAAQ,IAAoB,CAAC,UAAU,KAAK,UAAU;QACtD,OAAQ,IAAoB,CAAC,QAAQ,KAAK,UAAU;QACnD,IAAoB,CAAC,SAAS,KAAK,IAAI,CACzC,CAAC;AACJ,CAAC;AAPD,8BAOC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ShimWrapped } from '../common/types';\n\n/**\n * Checks if certain function has been already wrapped\n * @param func\n */\nexport function isWrapped(func: unknown): func is ShimWrapped {\n  return (\n    typeof func === 'function' &&\n    typeof (func as ShimWrapped).__original === 'function' &&\n    typeof (func as ShimWrapped).__unwrap === 'function' &&\n    (func as ShimWrapped).__wrapped === true\n  );\n}\n"]}