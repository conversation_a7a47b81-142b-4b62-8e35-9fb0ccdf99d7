{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAEH,6EAA2D;AAC3D,0DAAwC;AACxC,sDAAoC;AACpC,gEAA8C;AAC9C,iEAA+C;AAC/C,gDAA8B;AAC9B,iDAA+B;AAC/B,iDAA+B;AAC/B,kDAAgD;AAChD,6CAA2B;AAC3B,0DAAwC;AACxC,oEAAkD;AAClD,sDAAoC;AACpC,uDAAqC;AACrC,mEAAiD;AACjD,kEAAgD;AAChD,qEAAmD;AACnD,2EAAyD;AACzD,2DAAyC;AACzC,qDAAmC;AACnC,sDAAoC;AACpC,gDAA8B;AAC9B,mDAAiC;AACjC,kDAAgC;AAChC,8CAA4B;AAC5B,+CAA6B;AAC7B,mDAAiC;AACjC,4CAA0B;AAC1B,kDAA8C;AACjC,QAAA,QAAQ,GAAG;IACtB,OAAO,EAAP,kBAAO;CACR,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport * from './baggage/propagation/W3CBaggagePropagator';\nexport * from './common/anchored-clock';\nexport * from './common/attributes';\nexport * from './common/global-error-handler';\nexport * from './common/logging-error-handler';\nexport * from './common/time';\nexport * from './common/types';\nexport * from './ExportResult';\nexport * as baggageUtils from './baggage/utils';\nexport * from './platform';\nexport * from './propagation/composite';\nexport * from './trace/W3CTraceContextPropagator';\nexport * from './trace/IdGenerator';\nexport * from './trace/rpc-metadata';\nexport * from './trace/sampler/AlwaysOffSampler';\nexport * from './trace/sampler/AlwaysOnSampler';\nexport * from './trace/sampler/ParentBasedSampler';\nexport * from './trace/sampler/TraceIdRatioBasedSampler';\nexport * from './trace/suppress-tracing';\nexport * from './trace/TraceState';\nexport * from './utils/environment';\nexport * from './utils/merge';\nexport * from './utils/sampling';\nexport * from './utils/timeout';\nexport * from './utils/url';\nexport * from './utils/wrap';\nexport * from './utils/callback';\nexport * from './version';\nimport { _export } from './internal/exporter';\nexport const internal = {\n  _export,\n};\n"]}