{"version": 3, "file": "pubsub-propagation.test.js", "sourceRoot": "", "sources": ["../../test/pubsub-propagation.test.ts"], "names": [], "mappings": ";;AAAA;;;;;;;;;;;;;;GAcG;AACH,kEAA8C;AAC9C,0EAI2C;AAC3C,4CAAyD;AACzD,mCAAgC;AAEhC,IAAA,2DAAsC,GAAE,CAAC;AAEzC,MAAM,MAAM,GAAG,WAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAEvC,SAAS,CAAC,GAAG,EAAE;IACb,IAAA,wCAAmB,GAAE,CAAC;AACxB,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;QACvE,MAAM,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QACtB,4BAAK,CAAC,qCAAqC,CAAC;YAC1C,QAAQ;YACR,MAAM;YACN,aAAa,EAAE,kBAAY;YAC3B,oBAAoB,EAAE,GAAG,EAAE,CAAC,CAAC;gBAC3B,IAAI,EAAE,MAAM;gBACZ,aAAa,EAAE,kBAAY;gBAC3B,UAAU,EAAE,EAAE;aACf,CAAC;SACH,CAAC,CAAC;QACH,4BAAK,CAAC,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,kBAAY,CAAC,CAAC;QAEhE,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEtC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAErB,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,MAAM,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QACtB,4BAAK,CAAC,qCAAqC,CAAC;YAC1C,QAAQ;YACR,MAAM;YACN,aAAa,EAAE,kBAAY;YAC3B,oBAAoB,EAAE,GAAG,EAAE,CAAC,CAAC;gBAC3B,IAAI,EAAE,MAAM;gBACZ,aAAa,EAAE,kBAAY;gBAC3B,UAAU,EAAE,EAAE;aACf,CAAC;SACH,CAAC,CAAC;QACH,4BAAK,CAAC,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,kBAAY,CAAC,CAAC;QAEhE,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEtC,IAAI,OAAiC,CAAC;QAEtC,QAAQ,CAAC,GAAG,CACV,GAAG,EAAE,CACH,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;YAChB,OAAO,GAAG,GAAG,CAAC;QAChB,CAAC,CAAC,CACL,CAAC;QAEF,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEtC,0EAA0E;QAC1E,OAAO,CAAC,SAAS,CAAC,CAAC;QAEnB,yDAAyD;QACzD,6BAA6B;QAC7B,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACtD,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;QACxC,MAAM,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QACtB,4BAAK,CAAC,qCAAqC,CAAC;YAC1C,QAAQ;YACR,MAAM;YACN,aAAa,EAAE,kBAAY;YAC3B,oBAAoB,EAAE,GAAG,EAAE,CAAC,CAAC;gBAC3B,IAAI,EAAE,MAAM;gBACZ,aAAa,EAAE,kBAAY;gBAC3B,UAAU,EAAE,EAAE;aACf,CAAC;SACH,CAAC,CAAC;QACH,4BAAK,CAAC,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,kBAAY,CAAC,CAAC;QAEhE,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEtC,IAAI,MAAgC,CAAC;QAErC,QAAQ,CAAC,GAAG,CACV,GAAG,EAAE,CACH,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;YACrB,MAAM,GAAG,GAAG,CAAC;QACf,CAAC,CAAC,CACL,CAAC;QAEF,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEtC,0EAA0E;QAC1E,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE5B,yDAAyD;QACzD,6BAA6B;QAC7B,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACtD,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,IAAA,eAAM,EAAC,IAAA,iCAAY,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport utils from '../src/pubsub-propagation';\nimport {\n  getTestSpans,\n  registerInstrumentationTestingProvider,\n  resetMemoryExporter,\n} from '@opentelemetry/contrib-test-utils';\nimport { ROOT_CONTEXT, trace } from '@opentelemetry/api';\nimport { expect } from 'expect';\n\nregisterInstrumentationTestingProvider();\n\nconst tracer = trace.getTracer('test');\n\nafterEach(() => {\n  resetMemoryExporter();\n});\n\ndescribe('Pubsub propagation', () => {\n  it('Span ends immediately when the function returns a non-promise', () => {\n    const messages = [{}];\n    utils.patchMessagesArrayToStartProcessSpans({\n      messages,\n      tracer,\n      parentContext: ROOT_CONTEXT,\n      messageToSpanDetails: () => ({\n        name: 'test',\n        parentContext: ROOT_CONTEXT,\n        attributes: {},\n      }),\n    });\n    utils.patchArrayForProcessSpans(messages, tracer, ROOT_CONTEXT);\n\n    expect(getTestSpans().length).toBe(0);\n\n    messages.map(x => x);\n\n    expect(getTestSpans().length).toBe(1);\n    expect(getTestSpans()[0]).toMatchObject({ name: 'test process' });\n  });\n\n  it('Span ends on promise-resolution', () => {\n    const messages = [{}];\n    utils.patchMessagesArrayToStartProcessSpans({\n      messages,\n      tracer,\n      parentContext: ROOT_CONTEXT,\n      messageToSpanDetails: () => ({\n        name: 'test',\n        parentContext: ROOT_CONTEXT,\n        attributes: {},\n      }),\n    });\n    utils.patchArrayForProcessSpans(messages, tracer, ROOT_CONTEXT);\n\n    expect(getTestSpans().length).toBe(0);\n\n    let resolve: (value: unknown) => void;\n\n    messages.map(\n      () =>\n        new Promise(res => {\n          resolve = res;\n        })\n    );\n\n    expect(getTestSpans().length).toBe(0);\n\n    // @ts-expect-error Typescript thinks this value is used before assignment\n    resolve(undefined);\n\n    // We use setTimeout here to make sure our assertions run\n    // after the promise resolves\n    return new Promise(res => setTimeout(res, 0)).then(() => {\n      expect(getTestSpans().length).toBe(1);\n      expect(getTestSpans()[0]).toMatchObject({ name: 'test process' });\n    });\n  });\n\n  it('Span ends on promise-rejection', () => {\n    const messages = [{}];\n    utils.patchMessagesArrayToStartProcessSpans({\n      messages,\n      tracer,\n      parentContext: ROOT_CONTEXT,\n      messageToSpanDetails: () => ({\n        name: 'test',\n        parentContext: ROOT_CONTEXT,\n        attributes: {},\n      }),\n    });\n    utils.patchArrayForProcessSpans(messages, tracer, ROOT_CONTEXT);\n\n    expect(getTestSpans().length).toBe(0);\n\n    let reject: (value: unknown) => void;\n\n    messages.map(\n      () =>\n        new Promise((_, rej) => {\n          reject = rej;\n        })\n    );\n\n    expect(getTestSpans().length).toBe(0);\n\n    // @ts-expect-error Typescript thinks this value is used before assignment\n    reject(new Error('Failed'));\n\n    // We use setTimeout here to make sure our assertions run\n    // after the promise resolves\n    return new Promise(res => setTimeout(res, 0)).then(() => {\n      expect(getTestSpans().length).toBe(1);\n      expect(getTestSpans()[0]).toMatchObject({ name: 'test process' });\n    });\n  });\n});\n"]}