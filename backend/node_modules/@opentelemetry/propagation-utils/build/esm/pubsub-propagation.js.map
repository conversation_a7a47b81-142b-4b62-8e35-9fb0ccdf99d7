{"version": 3, "file": "pubsub-propagation.js", "sourceRoot": "", "sources": ["../../src/pubsub-propagation.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAEL,QAAQ,EAIR,OAAO,EACP,KAAK,EACL,IAAI,GAEL,MAAM,oBAAoB,CAAC;AAE5B,IAAM,mBAAmB,GAAG,MAAM,CAChC,6CAA6C,CAC9C,CAAC;AACF,IAAM,iBAAiB,GAAG,MAAM,CAAC,2CAA2C,CAAC,CAAC;AAO9E,IAAM,gBAAgB,GAAG,UACvB,QAAmB,EACnB,MAAc,EACd,WAAoB;IAEpB,IAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;IACjC,IAAM,WAAW,GAAG;QAElB,cAAoC;aAApC,UAAoC,EAApC,qBAAoC,EAApC,IAAoC;YAApC,yBAAoC;;QAEpC,IAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QACzD,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;IAEF,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE;QACxC,UAAU,EAAE,KAAK;QACjB,KAAK,EAAE,WAAW;KACnB,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAAS,SAAS,CAAC,KAAc;;IAC/B,8DAA8D;IAC9D,OAAO,OAAO,CAAA,MAAC,KAAa,0CAAE,IAAI,CAAA,KAAK,UAAU,CAAC;AACpD,CAAC;AAED,IAAM,kBAAkB,GAAG,UACzB,QAAgC,EAChC,YAA+B,EAC/B,MAAc,EACd,WAAoB;IAEpB,IAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAwB,CAAC;IAC/D,IAAM,WAAW,GAAG;QAElB,qBAA2C;aAA3C,UAA2C,EAA3C,qBAA2C,EAA3C,IAA2C;YAA3C,gCAA2C;;QAE3C,IAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAChC,IAAM,eAAe,GAAG;YAAA,iBAwCvB;;YAtCC,sBAA4C;iBAA5C,UAA4C,EAA5C,qBAA4C,EAA5C,IAA4C;gBAA5C,iCAA4C;;YAE5C,IAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,IAAM,WAAW,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,mBAAmB,CAAC,+CAA9B,OAAO,CAA2B,CAAC;YACvD,IAAI,CAAC,WAAW;gBAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAE5D,IAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;;gBAChE,IAAI,MAAkC,CAAC;gBACvC,IAAI;oBACF,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAI,EAAE,YAAY,CAAC,CAAC;oBAC5C,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;wBACrB,IAAM,OAAO,GAAG,sBAAM,OAAA,MAAA,OAAO,CAAC,iBAAiB,CAAC,+CAA1B,OAAO,CAAuB,CAAA,EAAA,CAAC;wBACrD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;qBAC/B;oBACD,OAAO,MAAM,CAAC;iBACf;wBAAS;oBACR,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;wBACtB,MAAA,OAAO,CAAC,iBAAiB,CAAC,+CAA1B,OAAO,CAAuB,CAAC;qBAChC;iBACF;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBAC3B,IAAM,iBAAiB,GAAG,MAAM,CAAC,wBAAwB,CACvD,OAAO,EACP,mBAAmB,CACpB,CAAC;gBACF,iBAAiB;oBACf,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;gBAErE,IAAM,eAAe,GAAG,MAAM,CAAC,wBAAwB,CACrD,OAAO,EACP,iBAAiB,CAClB,CAAC;gBACF,eAAe;oBACb,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;aAClE;YACD,OAAO,GAAG,CAAC;QACb,CAAC,CAAC;QACF,WAAW,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;QACjC,IAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACrD,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC;YAC3B,yBAAyB,CAAC,UAAU,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAC7D,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;IAEF,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,YAAY,EAAE;QAC5C,UAAU,EAAE,KAAK;QACjB,KAAK,EAAE,WAAW;KACnB,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,IAAM,yBAAyB,GAAG,UAChC,QAAmB,EACnB,MAAc,EACd,WAAuC;IAAvC,4BAAA,EAAA,cAAuB,OAAO,CAAC,MAAM,EAAE;IAEvC,kBAAkB,CAChB,QAAkC,EAClC,SAAS,EACT,MAAM,EACN,WAAW,CACZ,CAAC;IACF,kBAAkB,CAChB,QAAkC,EAClC,KAAK,EACL,MAAM,EACN,WAAW,CACZ,CAAC;IACF,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AAClD,CAAC,CAAC;AAEF,IAAM,yBAAyB,GAAG,UAChC,OAAU,EACV,IAAY,EACZ,UAA0B,EAC1B,aAAsB,EACtB,iBAA0B,EAC1B,MAAc,EACd,WAA4B;;IAE5B,IAAM,KAAK,GAAW,EAAE,CAAC;IACzB,IAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;IAC5D,IAAI,WAAW,EAAE;QACf,KAAK,CAAC,IAAI,CAAC;YACT,OAAO,EAAE,WAAW;SACb,CAAC,CAAC;KACZ;IAED,IAAM,QAAQ,GAAM,IAAI,aAAU,CAAC;IACnC,IAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAClC,QAAQ,EACR;QACE,IAAI,EAAE,QAAQ,CAAC,QAAQ;QACvB,UAAU,wBACL,UAAU,gBACZ,qBAAqB,IAAG,SAAS,MACnC;QACD,KAAK,OAAA;KACN,EACD,aAAa,CACd,CAAC;IAEF,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,mBAAmB,EAAE;QAClD,UAAU,EAAE,KAAK;QACjB,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,cAAM,OAAA,WAAW,EAAX,CAAW;KACzB,CAAC,CAAC;IAEH,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,EAAE;QAChD,UAAU,EAAE,KAAK;QACjB,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE;YACL,WAAW,CAAC,GAAG,EAAE,CAAC;YAClB,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,iBAAiB,EAAE;gBAChD,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,cAAO,CAAC;aAChB,CAAC,CAAC;QACL,CAAC;KACF,CAAC,CAAC;IAEH,IAAI;QACF,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,WAAW,EAAE,OAAO,CAAC,CAAC;KACrC;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,CAAC,KAAK,CAAC,sDAAsD,EAAE,GAAG,CAAC,CAAC;KACzE;IAED,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAkBF,IAAM,qCAAqC,GAAG,UAAI,EAMnB;QAL7B,QAAQ,cAAA,EACR,MAAM,YAAA,EACN,aAAa,mBAAA,EACb,oBAAoB,0BAAA,EACpB,WAAW,iBAAA;IAEX,QAAQ,CAAC,OAAO,CAAC,UAAA,OAAO;QAChB,IAAA,KAIF,oBAAoB,CAAC,OAAO,CAAC,EAH/B,UAAU,gBAAA,EACV,IAAI,UAAA,EACW,iBAAiB,mBACD,CAAC;QAElC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,mBAAmB,EAAE;YAClD,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE;gBACL,OAAA,yBAAyB,CACvB,OAAO,EACP,IAAI,EACJ,UAAU,EACV,aAAa,EACb,iBAAiB,EACjB,MAAM,EACN,WAAW,CACZ;YARD,CAQC;SACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,eAAe;IACb,qCAAqC,uCAAA;IACrC,yBAAyB,2BAAA;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  Tracer,\n  SpanKind,\n  Span,\n  Context,\n  Link,\n  context,\n  trace,\n  diag,\n  SpanAttributes,\n} from '@opentelemetry/api';\n\nconst START_SPAN_FUNCTION = Symbol(\n  'opentelemetry.pubsub-propagation.start_span'\n);\nconst END_SPAN_FUNCTION = Symbol('opentelemetry.pubsub-propagation.end_span');\n\ninterface OtelProcessedMessage {\n  [START_SPAN_FUNCTION]?: () => Span;\n  [END_SPAN_FUNCTION]?: () => void;\n}\n\nconst patchArrayFilter = (\n  messages: unknown[],\n  tracer: Tracer,\n  loopContext: Context\n) => {\n  const origFunc = messages.filter;\n  const patchedFunc = function (\n    this: unknown,\n    ...args: Parameters<typeof origFunc>\n  ) {\n    const newArray = origFunc.apply(this, args);\n    patchArrayForProcessSpans(newArray, tracer, loopContext);\n    return newArray;\n  };\n\n  Object.defineProperty(messages, 'filter', {\n    enumerable: false,\n    value: patchedFunc,\n  });\n};\n\nfunction isPromise(value: unknown): value is Promise<unknown> {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return typeof (value as any)?.then === 'function';\n}\n\nconst patchArrayFunction = (\n  messages: OtelProcessedMessage[],\n  functionName: 'forEach' | 'map',\n  tracer: Tracer,\n  loopContext: Context\n) => {\n  const origFunc = messages[functionName] as typeof messages.map;\n  const patchedFunc = function (\n    this: unknown,\n    ...arrFuncArgs: Parameters<typeof origFunc>\n  ) {\n    const callback = arrFuncArgs[0];\n    const wrappedCallback = function (\n      this: unknown,\n      ...callbackArgs: Parameters<typeof callback>\n    ) {\n      const message = callbackArgs[0];\n      const messageSpan = message?.[START_SPAN_FUNCTION]?.();\n      if (!messageSpan) return callback.apply(this, callbackArgs);\n\n      const res = context.with(trace.setSpan(loopContext, messageSpan), () => {\n        let result: Promise<unknown> | unknown;\n        try {\n          result = callback.apply(this, callbackArgs);\n          if (isPromise(result)) {\n            const endSpan = () => message[END_SPAN_FUNCTION]?.();\n            result.then(endSpan, endSpan);\n          }\n          return result;\n        } finally {\n          if (!isPromise(result)) {\n            message[END_SPAN_FUNCTION]?.();\n          }\n        }\n      });\n\n      if (typeof res === 'object') {\n        const startSpanFunction = Object.getOwnPropertyDescriptor(\n          message,\n          START_SPAN_FUNCTION\n        );\n        startSpanFunction &&\n          Object.defineProperty(res, START_SPAN_FUNCTION, startSpanFunction);\n\n        const endSpanFunction = Object.getOwnPropertyDescriptor(\n          message,\n          END_SPAN_FUNCTION\n        );\n        endSpanFunction &&\n          Object.defineProperty(res, END_SPAN_FUNCTION, endSpanFunction);\n      }\n      return res;\n    };\n    arrFuncArgs[0] = wrappedCallback;\n    const funcResult = origFunc.apply(this, arrFuncArgs);\n    if (Array.isArray(funcResult))\n      patchArrayForProcessSpans(funcResult, tracer, loopContext);\n    return funcResult;\n  };\n\n  Object.defineProperty(messages, functionName, {\n    enumerable: false,\n    value: patchedFunc,\n  });\n};\n\nconst patchArrayForProcessSpans = (\n  messages: unknown[],\n  tracer: Tracer,\n  loopContext: Context = context.active()\n) => {\n  patchArrayFunction(\n    messages as OtelProcessedMessage[],\n    'forEach',\n    tracer,\n    loopContext\n  );\n  patchArrayFunction(\n    messages as OtelProcessedMessage[],\n    'map',\n    tracer,\n    loopContext\n  );\n  patchArrayFilter(messages, tracer, loopContext);\n};\n\nconst startMessagingProcessSpan = <T>(\n  message: T,\n  name: string,\n  attributes: SpanAttributes,\n  parentContext: Context,\n  propagatedContext: Context,\n  tracer: Tracer,\n  processHook?: ProcessHook<T>\n): Span => {\n  const links: Link[] = [];\n  const spanContext = trace.getSpanContext(propagatedContext);\n  if (spanContext) {\n    links.push({\n      context: spanContext,\n    } as Link);\n  }\n\n  const spanName = `${name} process`;\n  const processSpan = tracer.startSpan(\n    spanName,\n    {\n      kind: SpanKind.CONSUMER,\n      attributes: {\n        ...attributes,\n        ['messaging.operation']: 'process',\n      },\n      links,\n    },\n    parentContext\n  );\n\n  Object.defineProperty(message, START_SPAN_FUNCTION, {\n    enumerable: false,\n    writable: true,\n    value: () => processSpan,\n  });\n\n  Object.defineProperty(message, END_SPAN_FUNCTION, {\n    enumerable: false,\n    writable: true,\n    value: () => {\n      processSpan.end();\n      Object.defineProperty(message, END_SPAN_FUNCTION, {\n        enumerable: false,\n        writable: true,\n        value: () => {},\n      });\n    },\n  });\n\n  try {\n    processHook?.(processSpan, message);\n  } catch (err) {\n    diag.error('opentelemetry-pubsub-propagation: process hook error', err);\n  }\n\n  return processSpan;\n};\n\ninterface SpanDetails {\n  attributes: SpanAttributes;\n  parentContext: Context;\n  name: string;\n}\n\ntype ProcessHook<T> = (processSpan: Span, message: T) => void;\n\ninterface PatchForProcessingPayload<T> {\n  messages: T[];\n  tracer: Tracer;\n  parentContext: Context;\n  messageToSpanDetails: (message: T) => SpanDetails;\n  processHook?: ProcessHook<T>;\n}\n\nconst patchMessagesArrayToStartProcessSpans = <T>({\n  messages,\n  tracer,\n  parentContext,\n  messageToSpanDetails,\n  processHook,\n}: PatchForProcessingPayload<T>) => {\n  messages.forEach(message => {\n    const {\n      attributes,\n      name,\n      parentContext: propagatedContext,\n    } = messageToSpanDetails(message);\n\n    Object.defineProperty(message, START_SPAN_FUNCTION, {\n      enumerable: false,\n      writable: true,\n      value: () =>\n        startMessagingProcessSpan<T>(\n          message,\n          name,\n          attributes,\n          parentContext,\n          propagatedContext,\n          tracer,\n          processHook\n        ),\n    });\n  });\n};\n\nexport default {\n  patchMessagesArrayToStartProcessSpans,\n  patchArrayForProcessSpans,\n};\n"]}