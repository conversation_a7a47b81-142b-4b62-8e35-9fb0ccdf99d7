{"name": "@opentelemetry/instrumentation-tedious", "version": "0.6.5", "description": "OpenTelemetry instrumentation for `tedious`", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint:fix": "eslint . --ext .ts --fix", "lint": "eslint . --ext .ts", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-tedious --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test-all-versions": "tav", "version:update": "node ../../../scripts/version-update.js"}, "keywords": ["instrumentation", "microsoft", "mssql", "nodejs", "opentelemetry", "profiling", "sql server", "tds", "tedious", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.35.1", "@opentelemetry/sdk-trace-base": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "mocha": "7.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "tedious": "15.1.3", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/tedious": "^4.0.10"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/instrumentation-tedious#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}