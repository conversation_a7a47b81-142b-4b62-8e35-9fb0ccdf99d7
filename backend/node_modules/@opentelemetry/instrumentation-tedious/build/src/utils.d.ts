/**
 * The span name SHOULD be set to a low cardinality value
 * representing the statement executed on the database.
 *
 * @returns Operation executed on Tedious Connection. Does not map to SQL statement in any way.
 */
export declare function getSpanName(operation: string, db: string | undefined, sql: string | undefined, bulkLoadTable: string | undefined): string;
export declare const once: (fn: Function) => (...args: unknown[]) => any;
//# sourceMappingURL=utils.d.ts.map