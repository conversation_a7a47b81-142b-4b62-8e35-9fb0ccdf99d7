{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;AAEH,OAAO,EACL,IAAI,EAEJ,OAAO,EAGP,KAAK,GAGN,MAAM,oBAAoB,CAAC;AAC5B,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AAOnC;;GAEG;AACH;IASE,iCACkB,mBAA2B,EAC3B,sBAA8B,EAC9C,MAAkC;QAAlC,uBAAA,EAAA,WAAkC;QAFlB,wBAAmB,GAAnB,mBAAmB,CAAQ;QAC3B,2BAAsB,GAAtB,sBAAsB,CAAQ;QAkBhD,qCAAqC;QAC3B,UAAK,GAAG,OAAO,CAAC,IAAI,CAAC;QAC/B,wCAAwC;QAC9B,YAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QACnC,0CAA0C;QAChC,cAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;QACvC,6CAA6C;QACnC,gBAAW,GAAG,OAAO,CAAC,UAAU,CAAC;QAtBzC,IAAI,CAAC,OAAO,cACV,OAAO,EAAE,IAAI,IACV,MAAM,CACV,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACtC,SAAS,EAAE,mBAAmB;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;QAE5E,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;QAC5E,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAYD,sBAAc,0CAAK;QADnB,mBAAmB;aACnB;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAED;;;OAGG;IACI,kDAAgB,GAAvB,UAAwB,aAA4B;QAClD,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ,CAClC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,CAC5B,CAAC;QAEF,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACO,0DAAwB,GAAlC;QACE,OAAO;IACT,CAAC;IAED,mCAAmC;IAC5B,2CAAS,GAAhB;QACE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,2CAAS,GAAhB,UAAiB,MAAkC;QAAlC,uBAAA,EAAA,WAAkC;QACjD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,mDAAiB,GAAxB,UAAyB,cAA8B;QACrD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,SAAS,CACrC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,CAC5B,CAAC;IACJ,CAAC;IAGD,sBAAc,2CAAM;QADpB,oBAAoB;aACpB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IAgBH,8BAAC;AAAD,CAAC,AA1GD,IA0GC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  DiagLogger,\n  metrics,\n  Meter,\n  MeterProvider,\n  trace,\n  Tracer,\n  TracerProvider,\n} from '@opentelemetry/api';\nimport * as shimmer from 'shimmer';\nimport {\n  InstrumentationModuleDefinition,\n  Instrumentation,\n  InstrumentationConfig,\n} from './types';\n\n/**\n * Base abstract internal class for instrumenting node and web plugins\n */\nexport abstract class InstrumentationAbstract<T = any>\n  implements Instrumentation\n{\n  protected _config: InstrumentationConfig;\n\n  private _tracer: Tracer;\n  private _meter: Meter;\n  protected _diag: DiagLogger;\n\n  constructor(\n    public readonly instrumentationName: string,\n    public readonly instrumentationVersion: string,\n    config: InstrumentationConfig = {}\n  ) {\n    this._config = {\n      enabled: true,\n      ...config,\n    };\n\n    this._diag = diag.createComponentLogger({\n      namespace: instrumentationName,\n    });\n\n    this._tracer = trace.getTracer(instrumentationName, instrumentationVersion);\n\n    this._meter = metrics.getMeter(instrumentationName, instrumentationVersion);\n    this._updateMetricInstruments();\n  }\n\n  /* Api to wrap instrumented method */\n  protected _wrap = shimmer.wrap;\n  /* Api to unwrap instrumented methods */\n  protected _unwrap = shimmer.unwrap;\n  /* Api to mass wrap instrumented method */\n  protected _massWrap = shimmer.massWrap;\n  /* Api to mass unwrap instrumented methods */\n  protected _massUnwrap = shimmer.massUnwrap;\n\n  /* Returns meter */\n  protected get meter(): Meter {\n    return this._meter;\n  }\n\n  /**\n   * Sets MeterProvider to this plugin\n   * @param meterProvider\n   */\n  public setMeterProvider(meterProvider: MeterProvider): void {\n    this._meter = meterProvider.getMeter(\n      this.instrumentationName,\n      this.instrumentationVersion\n    );\n\n    this._updateMetricInstruments();\n  }\n\n  /**\n   * Sets the new metric instruments with the current Meter.\n   */\n  protected _updateMetricInstruments(): void {\n    return;\n  }\n\n  /* Returns InstrumentationConfig */\n  public getConfig(): InstrumentationConfig {\n    return this._config;\n  }\n\n  /**\n   * Sets InstrumentationConfig to this plugin\n   * @param InstrumentationConfig\n   */\n  public setConfig(config: InstrumentationConfig = {}): void {\n    this._config = Object.assign({}, config);\n  }\n\n  /**\n   * Sets TraceProvider to this plugin\n   * @param tracerProvider\n   */\n  public setTracerProvider(tracerProvider: TracerProvider): void {\n    this._tracer = tracerProvider.getTracer(\n      this.instrumentationName,\n      this.instrumentationVersion\n    );\n  }\n\n  /* Returns tracer */\n  protected get tracer(): Tracer {\n    return this._tracer;\n  }\n\n  /* Disable plugin */\n  public abstract enable(): void;\n\n  /* Enable plugin */\n  public abstract disable(): void;\n\n  /**\n   * Init method in which plugin should define _modules and patches for\n   * methods\n   */\n  protected abstract init():\n    | InstrumentationModuleDefinition<T>\n    | InstrumentationModuleDefinition<T>[]\n    | void;\n}\n"]}