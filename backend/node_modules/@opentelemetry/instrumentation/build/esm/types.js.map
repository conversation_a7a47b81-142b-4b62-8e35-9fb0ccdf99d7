{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TracerProvider, MeterProvider } from '@opentelemetry/api';\n\n/** Interface Instrumentation to apply patch. */\nexport interface Instrumentation {\n  /** Instrumentation Name  */\n  instrumentationName: string;\n\n  /** Instrumentation Version  */\n  instrumentationVersion: string;\n\n  /**\n   * Instrumentation Description - please describe all useful information\n   * as Instrumentation might patch different version of different modules,\n   * or support different browsers etc.\n   */\n  instrumentationDescription?: string;\n\n  /** Method to disable the instrumentation  */\n  disable(): void;\n\n  /** Method to enable the instrumentation  */\n  enable(): void;\n\n  /** Method to set tracer provider  */\n  setTracerProvider(tracerProvider: TracerProvider): void;\n\n  /** Method to set meter provider  */\n  setMeterProvider(meterProvider: MeterProvider): void;\n\n  /** Method to set instrumentation config  */\n  setConfig(config: InstrumentationConfig): void;\n\n  /** Method to get instrumentation config  */\n  getConfig(): InstrumentationConfig;\n\n  /**\n   * Contains all supported versions.\n   * All versions must be compatible with [semver](https://semver.org/spec/v2.0.0.html) format.\n   * If the version is not supported, we won't apply instrumentation patch (see `enable` method).\n   * If omitted, all versions of the module will be patched.\n   */\n  supportedVersions?: string[];\n}\n\nexport interface InstrumentationConfig {\n  /**\n   * Whether to enable the plugin.\n   * @default true\n   */\n  enabled?: boolean;\n}\n\n/**\n * This interface defines the params that are be added to the wrapped function\n * using the \"shimmer.wrap\"\n */\nexport interface ShimWrapped extends Function {\n  __wrapped: boolean;\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  __unwrap: Function;\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  __original: Function;\n}\n\nexport interface InstrumentationModuleFile<T> {\n  /** Name of file to be patched with relative path */\n  name: string;\n\n  moduleExports?: T;\n\n  /** Supported version this file */\n  supportedVersions: string[];\n\n  /** Method to patch the instrumentation  */\n  patch(moduleExports: T, moduleVersion?: string): T;\n\n  /** Method to patch the instrumentation  */\n\n  /** Method to unpatch the instrumentation  */\n  unpatch(moduleExports?: T, moduleVersion?: string): void;\n}\n\nexport interface InstrumentationModuleDefinition<T> {\n  /** Module name or path  */\n  name: string;\n\n  moduleExports?: T;\n\n  /** Instrumented module version */\n  moduleVersion?: string;\n\n  /** Supported version of module  */\n  supportedVersions: string[];\n\n  /** Module internal files to be patched  */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  files: InstrumentationModuleFile<any>[];\n\n  /** If set to true, the includePrerelease check will be included when calling semver.satisfies */\n  includePrerelease?: boolean;\n\n  /** Method to patch the instrumentation  */\n  patch?: (moduleExports: T, moduleVersion?: string) => T;\n\n  /** Method to unpatch the instrumentation  */\n  unpatch?: (moduleExports: T, moduleVersion?: string) => void;\n}\n"]}