{"version": 3, "file": "autoLoader.js", "sourceRoot": "", "sources": ["../../src/autoLoader.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAoD;AACpD,uDAI2B;AAG3B;;;;;GAKG;AACH,SAAgB,wBAAwB,CACtC,OAA0B;IAE1B,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAA,6CAA2B,EACtD,OAAO,CAAC,gBAAgB,CACzB,CAAC;IACF,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,WAAK,CAAC,iBAAiB,EAAE,CAAC;IAC3E,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,aAAO,CAAC,gBAAgB,EAAE,CAAC;IAE1E,IAAA,wCAAsB,EAAC,gBAAgB,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;IAExE,OAAO,GAAG,EAAE;QACV,IAAA,yCAAuB,EAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC,CAAC;AACJ,CAAC;AAdD,4DAcC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { trace, metrics } from '@opentelemetry/api';\nimport {\n  disableInstrumentations,\n  enableInstrumentations,\n  parseInstrumentationOptions,\n} from './autoLoaderUtils';\nimport { AutoLoaderOptions } from './types_internal';\n\n/**\n * It will register instrumentations and plugins\n * @param options\n * @return returns function to unload instrumentation and plugins that were\n *   registered\n */\nexport function registerInstrumentations(\n  options: AutoLoaderOptions\n): () => void {\n  const { instrumentations } = parseInstrumentationOptions(\n    options.instrumentations\n  );\n  const tracerProvider = options.tracerProvider || trace.getTracerProvider();\n  const meterProvider = options.meterProvider || metrics.getMeterProvider();\n\n  enableInstrumentations(instrumentations, tracerProvider, meterProvider);\n\n  return () => {\n    disableInstrumentations(instrumentations);\n  };\n}\n"]}