{"version": 3, "file": "ModuleNameTrie.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/ModuleNameTrie.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIU,QAAA,mBAAmB,GAAG,GAAG,CAAC;AAEvC;;GAEG;AACH,MAAM,kBAAkB;IAAxB;QACE,UAAK,GAAgD,EAAE,CAAC;QACxD,aAAQ,GAAoC,IAAI,GAAG,EAAE,CAAC;IACxD,CAAC;CAAA;AAaD;;GAEG;AACH,MAAa,cAAc;IAA3B;QACU,UAAK,GAAuB,IAAI,kBAAkB,EAAE,CAAC;QACrD,aAAQ,GAAW,CAAC,CAAC;IAgE/B,CAAC;IA9DC;;;;OAIG;IACH,MAAM,CAAC,IAAY;QACjB,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAE1B,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,2BAAmB,CAAC,EAAE;YACvE,IAAI,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YACrD,IAAI,CAAC,QAAQ,EAAE;gBACb,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;gBACpC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;aACjD;YACD,QAAQ,GAAG,QAAQ,CAAC;SACrB;QACD,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CACJ,UAAkB,EAClB,EAAE,sBAAsB,EAAE,QAAQ,KAAkC,EAAE;QAEtE,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC1B,MAAM,OAAO,GAAgC,EAAE,CAAC;QAChD,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,KAAK,MAAM,cAAc,IAAI,UAAU,CAAC,KAAK,CAAC,2BAAmB,CAAC,EAAE;YAClE,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,EAAE;gBACb,SAAS,GAAG,KAAK,CAAC;gBAClB,MAAM;aACP;YACD,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;aACjC;YACD,QAAQ,GAAG,QAAQ,CAAC;SACrB;QAED,IAAI,QAAQ,IAAI,SAAS,EAAE;YACzB,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;SACjC;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,EAAE,CAAC;SACX;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SAC1B;QACD,IAAI,sBAAsB,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;SACrD;QACD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;CACF;AAlED,wCAkEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Hooked } from './RequireInTheMiddleSingleton';\n\nexport const ModuleNameSeparator = '/';\n\n/**\n * Node in a `ModuleNameTrie`\n */\nclass ModuleNameTrieNode {\n  hooks: Array<{ hook: Hooked; insertedId: number }> = [];\n  children: Map<string, ModuleNameTrieNode> = new Map();\n}\n\ntype ModuleNameTrieSearchOptions = {\n  /**\n   * Whether to return the results in insertion order\n   */\n  maintainInsertionOrder?: boolean;\n  /**\n   * Whether to return only full matches\n   */\n  fullOnly?: boolean;\n};\n\n/**\n * Trie containing nodes that represent a part of a module name (i.e. the parts separated by forward slash)\n */\nexport class ModuleNameTrie {\n  private _trie: ModuleNameTrieNode = new ModuleNameTrieNode();\n  private _counter: number = 0;\n\n  /**\n   * Insert a module hook into the trie\n   *\n   * @param {Hooked} hook Hook\n   */\n  insert(hook: Hooked) {\n    let trieNode = this._trie;\n\n    for (const moduleNamePart of hook.moduleName.split(ModuleNameSeparator)) {\n      let nextNode = trieNode.children.get(moduleNamePart);\n      if (!nextNode) {\n        nextNode = new ModuleNameTrieNode();\n        trieNode.children.set(moduleNamePart, nextNode);\n      }\n      trieNode = nextNode;\n    }\n    trieNode.hooks.push({ hook, insertedId: this._counter++ });\n  }\n\n  /**\n   * Search for matching hooks in the trie\n   *\n   * @param {string} moduleName Module name\n   * @param {boolean} maintainInsertionOrder Whether to return the results in insertion order\n   * @param {boolean} fullOnly Whether to return only full matches\n   * @returns {Hooked[]} Matching hooks\n   */\n  search(\n    moduleName: string,\n    { maintainInsertionOrder, fullOnly }: ModuleNameTrieSearchOptions = {}\n  ): Hooked[] {\n    let trieNode = this._trie;\n    const results: ModuleNameTrieNode['hooks'] = [];\n    let foundFull = true;\n\n    for (const moduleNamePart of moduleName.split(ModuleNameSeparator)) {\n      const nextNode = trieNode.children.get(moduleNamePart);\n      if (!nextNode) {\n        foundFull = false;\n        break;\n      }\n      if (!fullOnly) {\n        results.push(...nextNode.hooks);\n      }\n      trieNode = nextNode;\n    }\n\n    if (fullOnly && foundFull) {\n      results.push(...trieNode.hooks);\n    }\n\n    if (results.length === 0) {\n      return [];\n    }\n    if (results.length === 1) {\n      return [results[0].hook];\n    }\n    if (maintainInsertionOrder) {\n      results.sort((a, b) => a.insertedId - b.insertedId);\n    }\n    return results.map(({ hook }) => hook);\n  }\n}\n"]}