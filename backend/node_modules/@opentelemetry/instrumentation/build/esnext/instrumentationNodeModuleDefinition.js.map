{"version": 3, "file": "instrumentationNodeModuleDefinition.js", "sourceRoot": "", "sources": ["../../src/instrumentationNodeModuleDefinition.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAOH,MAAM,OAAO,mCAAmC;IAI9C,YACS,IAAY,EACZ,iBAA2B,EAC3B,KAAiD,EACjD,OAAsD,EAC7D,KAAwC;QAJjC,SAAI,GAAJ,IAAI,CAAQ;QACZ,sBAAiB,GAAjB,iBAAiB,CAAU;QAC3B,UAAK,GAAL,KAAK,CAA4C;QACjD,YAAO,GAAP,OAAO,CAA+C;QAG7D,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;IAC3B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  InstrumentationModuleDefinition,\n  InstrumentationModuleFile,\n} from './types';\n\nexport class InstrumentationNodeModuleDefinition<T>\n  implements InstrumentationModuleDefinition<T>\n{\n  files: InstrumentationModuleFile<T>[];\n  constructor(\n    public name: string,\n    public supportedVersions: string[],\n    public patch?: (exports: T, moduleVersion?: string) => T,\n    public unpatch?: (exports: T, moduleVersion?: string) => void,\n    files?: InstrumentationModuleFile<any>[]\n  ) {\n    this.files = files || [];\n  }\n}\n"]}