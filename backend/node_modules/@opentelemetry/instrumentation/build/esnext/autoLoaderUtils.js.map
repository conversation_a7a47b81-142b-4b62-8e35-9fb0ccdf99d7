{"version": 3, "file": "autoLoaderUtils.js", "sourceRoot": "", "sources": ["../../src/autoLoaderUtils.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAMH;;;;GAIG;AACH,MAAM,UAAU,2BAA2B,CACzC,UAAmC,EAAE;IAErC,IAAI,gBAAgB,GAAsB,EAAE,CAAC;IAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC9C,8DAA8D;QAC9D,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAQ,CAAC;QACjC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,OAAO,GAAG,2BAA2B,CAAC,MAAM,CAAC,CAAC;YACpD,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;SACtE;aAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;YACvC,gBAAgB,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAAC,CAAC;SACrC;aAAM,IAAK,MAA0B,CAAC,mBAAmB,EAAE;YAC1D,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC/B;KACF;IAED,OAAO,EAAE,gBAAgB,EAAE,CAAC;AAC9B,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,sBAAsB,CACpC,gBAAmC,EACnC,cAA+B,EAC/B,aAA6B;IAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACvD,MAAM,eAAe,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,cAAc,EAAE;YAClB,eAAe,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;SACnD;QACD,IAAI,aAAa,EAAE;YACjB,eAAe,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;SACjD;QACD,6DAA6D;QAC7D,oEAAoE;QACpE,mEAAmE;QACnE,yCAAyC;QACzC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE;YACxC,eAAe,CAAC,MAAM,EAAE,CAAC;SAC1B;KACF;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,uBAAuB,CACrC,gBAAmC;IAEnC,gBAAgB,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;AACzE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TracerProvider, MeterProvider } from '@opentelemetry/api';\nimport { Instrumentation } from './types';\nimport { AutoLoaderResult, InstrumentationOption } from './types_internal';\n\n/**\n * Parses the options and returns instrumentations, node plugins and\n *   web plugins\n * @param options\n */\nexport function parseInstrumentationOptions(\n  options: InstrumentationOption[] = []\n): AutoLoaderResult {\n  let instrumentations: Instrumentation[] = [];\n  for (let i = 0, j = options.length; i < j; i++) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const option = options[i] as any;\n    if (Array.isArray(option)) {\n      const results = parseInstrumentationOptions(option);\n      instrumentations = instrumentations.concat(results.instrumentations);\n    } else if (typeof option === 'function') {\n      instrumentations.push(new option());\n    } else if ((option as Instrumentation).instrumentationName) {\n      instrumentations.push(option);\n    }\n  }\n\n  return { instrumentations };\n}\n\n/**\n * Enable instrumentations\n * @param instrumentations\n * @param tracerProvider\n * @param meterProvider\n */\nexport function enableInstrumentations(\n  instrumentations: Instrumentation[],\n  tracerProvider?: TracerProvider,\n  meterProvider?: MeterProvider\n): void {\n  for (let i = 0, j = instrumentations.length; i < j; i++) {\n    const instrumentation = instrumentations[i];\n    if (tracerProvider) {\n      instrumentation.setTracerProvider(tracerProvider);\n    }\n    if (meterProvider) {\n      instrumentation.setMeterProvider(meterProvider);\n    }\n    // instrumentations have been already enabled during creation\n    // so enable only if user prevented that by setting enabled to false\n    // this is to prevent double enabling but when calling register all\n    // instrumentations should be now enabled\n    if (!instrumentation.getConfig().enabled) {\n      instrumentation.enable();\n    }\n  }\n}\n\n/**\n * Disable instrumentations\n * @param instrumentations\n */\nexport function disableInstrumentations(\n  instrumentations: Instrumentation[]\n): void {\n  instrumentations.forEach(instrumentation => instrumentation.disable());\n}\n"]}