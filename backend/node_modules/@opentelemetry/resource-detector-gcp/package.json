{"name": "@opentelemetry/resource-detector-gcp", "version": "0.29.13", "description": "OpenTelemetry SDK resource detector for GCP", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/resource-detector-gcp --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "test": "nyc mocha 'test/**/*.test.ts'", "tdd": "npm run test -- --watch-extensions ts --watch", "watch": "tsc -w"}, "keywords": ["opentelemetry", "nodejs", "resources", "stats", "profiling"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": "^1.0.0", "@opentelemetry/contrib-test-utils": "^0.42.0", "@types/mocha": "8.2.3", "@types/node": "18.18.14", "@types/semver": "7.5.8", "nock": "13.3.3", "nyc": "15.1.0", "rimraf": "5.0.10", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}, "dependencies": {"@opentelemetry/core": "^1.0.0", "@opentelemetry/resources": "^1.10.0", "@opentelemetry/semantic-conventions": "^1.27.0", "gcp-metadata": "^6.0.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/detectors/node/opentelemetry-resource-detector-gcp#readme", "gitHead": "7633caee19a7f04c5cc5e191d1ae7745ac3739f1"}