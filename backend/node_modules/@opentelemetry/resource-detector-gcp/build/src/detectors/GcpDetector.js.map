{"version": 3, "file": "GcpDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/GcpDetector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA4C;AAC5C,4CAAmD;AACnD,8CAAsD;AACtD,wDAMkC;AAClC,8CAA6C;AAC7C,8EAW6C;AAE7C;;;;GAIG;AACH,MAAM,WAAW;IACf,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAG,aAAO,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CACtE,IAAI,CAAC,cAAc,EAAE,CACtB,CAAC;QACF,OAAO,IAAI,oBAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC,CAAC,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC,EAAE;YACtC,UAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;SACX;QAED,MAAM,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,GAC1D,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,YAAY,EAAE;SACpB,CAAC,CAAC;QAEL,MAAM,UAAU,GAAuB,EAAE,CAAC;QAC1C,UAAU,CAAC,mDAA4B,CAAC,GAAG,SAAS,CAAC;QACrD,UAAU,CAAC,0CAAmB,CAAC,GAAG,UAAU,CAAC;QAC7C,UAAU,CAAC,4CAAqB,CAAC,GAAG,QAAQ,CAAC;QAC7C,UAAU,CAAC,0DAAmC,CAAC,GAAG,MAAM,CAAC;QACzD,UAAU,CAAC,iDAA0B,CAAC,GAAG,8CAAuB,CAAC;QAEjE,IAAI,IAAA,aAAM,GAAE,CAAC,uBAAuB;YAClC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAElD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,sCAAsC;IAC9B,iBAAiB,CACvB,UAA8B,EAC9B,WAAmB;QAEnB,MAAM,GAAG,GAAG,IAAA,aAAM,GAAE,CAAC;QAErB,UAAU,CAAC,mDAA4B,CAAC,GAAG,WAAW,CAAC;QACvD,UAAU,CAAC,qDAA8B,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC;QAC3D,UAAU,CAAC,+CAAwB,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;QACpD,UAAU,CAAC,iDAA0B,CAAC,GAAG,GAAG,CAAC,cAAc,CAAC;IAC9D,CAAC;IAED,iDAAiD;IACzC,KAAK,CAAC,aAAa;QACzB,IAAI;YACF,OAAO,MAAM,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SAChD;QAAC,WAAM;YACN,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAED,mDAAmD;IAC3C,KAAK,CAAC,cAAc;QAC1B,IAAI;YACF,MAAM,EAAE,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC5C,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC;SACtB;QAAC,WAAM;YACN,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAED,4CAA4C;IACpC,KAAK,CAAC,QAAQ;QACpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAClD,IAAI,MAAM,EAAE;gBACV,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;aAChC;YACD,OAAO,EAAE,CAAC;SACX;QAAC,WAAM;YACN,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAED,oDAAoD;IAC5C,KAAK,CAAC,eAAe;QAC3B,IAAI;YACF,OAAO,MAAM,WAAW,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;SAC9D;QAAC,WAAM;YACN,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAED,gDAAgD;IACxC,KAAK,CAAC,YAAY;QACxB,IAAI;YACF,OAAO,MAAM,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAC/C;QAAC,WAAM;YACN,OAAO,EAAE,CAAC;SACX;IACH,CAAC;CACF;AAEY,QAAA,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as gcpMetadata from 'gcp-metadata';\nimport { context, diag } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport {\n  DetectorSync,\n  ResourceDetectionConfig,\n  Resource,\n  ResourceAttributes,\n  IResource,\n} from '@opentelemetry/resources';\nimport { getEnv } from '@opentelemetry/core';\nimport {\n  CLOUDPROVIDERVALUES_GCP,\n  SEMRESATTRS_CLOUD_ACCOUNT_ID,\n  SEMRESATTRS_CLOUD_AVAILABILITY_ZONE,\n  SEMRESATTRS_CLOUD_PROVIDER,\n  SEMRESATTRS_CONTAINER_NAME,\n  SEMRESATTRS_HOST_ID,\n  SEMRESATTRS_HOST_NAME,\n  SEMRESATTRS_K8S_CLUSTER_NAME,\n  SEMRESATTRS_K8S_NAMESPACE_NAME,\n  SEMRESATTRS_K8S_POD_NAME,\n} from '@opentelemetry/semantic-conventions';\n\n/**\n * The GcpDetector can be used to detect if a process is running in the Google\n * Cloud Platform and return a {@link Resource} populated with metadata about\n * the instance. Returns an empty Resource if detection fails.\n */\nclass GcpDetector implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes = context.with(suppressTracing(context.active()), () =>\n      this._getAttributes()\n    );\n    return new Resource({}, attributes);\n  }\n\n  /**\n   * Attempts to connect and obtain instance configuration data from the GCP metadata service.\n   * If the connection is successful it returns a promise containing a {@link ResourceAttributes}\n   * object with instance metadata. Returns a promise containing an\n   * empty {@link ResourceAttributes} if the connection or parsing of the metadata fails.\n   */\n  private async _getAttributes(): Promise<ResourceAttributes> {\n    if (!(await gcpMetadata.isAvailable())) {\n      diag.debug('GcpDetector failed: GCP Metadata unavailable.');\n      return {};\n    }\n\n    const [projectId, instanceId, zoneId, clusterName, hostname] =\n      await Promise.all([\n        this._getProjectId(),\n        this._getInstanceId(),\n        this._getZone(),\n        this._getClusterName(),\n        this._getHostname(),\n      ]);\n\n    const attributes: ResourceAttributes = {};\n    attributes[SEMRESATTRS_CLOUD_ACCOUNT_ID] = projectId;\n    attributes[SEMRESATTRS_HOST_ID] = instanceId;\n    attributes[SEMRESATTRS_HOST_NAME] = hostname;\n    attributes[SEMRESATTRS_CLOUD_AVAILABILITY_ZONE] = zoneId;\n    attributes[SEMRESATTRS_CLOUD_PROVIDER] = CLOUDPROVIDERVALUES_GCP;\n\n    if (getEnv().KUBERNETES_SERVICE_HOST)\n      this._addK8sAttributes(attributes, clusterName);\n\n    return attributes;\n  }\n\n  /** Add resource attributes for K8s */\n  private _addK8sAttributes(\n    attributes: ResourceAttributes,\n    clusterName: string\n  ): void {\n    const env = getEnv();\n\n    attributes[SEMRESATTRS_K8S_CLUSTER_NAME] = clusterName;\n    attributes[SEMRESATTRS_K8S_NAMESPACE_NAME] = env.NAMESPACE;\n    attributes[SEMRESATTRS_K8S_POD_NAME] = env.HOSTNAME;\n    attributes[SEMRESATTRS_CONTAINER_NAME] = env.CONTAINER_NAME;\n  }\n\n  /** Gets project id from GCP project metadata. */\n  private async _getProjectId(): Promise<string> {\n    try {\n      return await gcpMetadata.project('project-id');\n    } catch {\n      return '';\n    }\n  }\n\n  /** Gets instance id from GCP instance metadata. */\n  private async _getInstanceId(): Promise<string> {\n    try {\n      const id = await gcpMetadata.instance('id');\n      return id.toString();\n    } catch {\n      return '';\n    }\n  }\n\n  /** Gets zone from GCP instance metadata. */\n  private async _getZone(): Promise<string> {\n    try {\n      const zoneId = await gcpMetadata.instance('zone');\n      if (zoneId) {\n        return zoneId.split('/').pop();\n      }\n      return '';\n    } catch {\n      return '';\n    }\n  }\n\n  /** Gets cluster name from GCP instance metadata. */\n  private async _getClusterName(): Promise<string> {\n    try {\n      return await gcpMetadata.instance('attributes/cluster-name');\n    } catch {\n      return '';\n    }\n  }\n\n  /** Gets hostname from GCP instance metadata. */\n  private async _getHostname(): Promise<string> {\n    try {\n      return await gcpMetadata.instance('hostname');\n    } catch {\n      return '';\n    }\n  }\n}\n\nexport const gcpDetector = new GcpDetector();\n"]}