import type { HandleFunction, IncomingMessage, Server } from 'connect';
export declare const _LAYERS_STORE_PROPERTY: unique symbol;
export declare type UseArgs1 = [HandleFunction];
export declare type UseArgs2 = [string, HandleFunction];
export declare type UseArgs = UseArgs1 | UseArgs2;
export declare type Use = (...args: UseArgs) => Server;
export declare type PatchedRequest = {
    [_LAYERS_STORE_PROPERTY]: string[];
} & IncomingMessage;
//# sourceMappingURL=internal-types.d.ts.map