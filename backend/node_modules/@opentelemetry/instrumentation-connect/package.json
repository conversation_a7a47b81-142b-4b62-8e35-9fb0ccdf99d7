{"name": "@opentelemetry/instrumentation-connect", "version": "0.32.4", "description": "OpenTelemetry connect automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-connect --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "version:update": "node ../../../scripts/version-update.js", "watch": "tsc -w"}, "keywords": ["connect", "instrumentation", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "connect": "3.7.0", "mocha": "7.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/connect": "3.4.36"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-connect#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}