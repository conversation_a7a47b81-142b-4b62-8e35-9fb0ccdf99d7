import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import type { FastifyInstrumentationConfig } from './types';
export declare const ANONYMOUS_NAME = "anonymous";
/** Fastify instrumentation for OpenTelemetry */
export declare class FastifyInstrumentation extends InstrumentationBase {
    constructor(config?: FastifyInstrumentationConfig);
    setConfig(config?: FastifyInstrumentationConfig): void;
    getConfig(): FastifyInstrumentationConfig;
    init(): InstrumentationNodeModuleDefinition<any>[];
    private _hookOnRequest;
    private _wrapHandler;
    private _wrapAddHook;
    private _patchConstructor;
    private _patchSend;
    private _hookPreHandler;
}
//# sourceMappingURL=instrumentation.d.ts.map