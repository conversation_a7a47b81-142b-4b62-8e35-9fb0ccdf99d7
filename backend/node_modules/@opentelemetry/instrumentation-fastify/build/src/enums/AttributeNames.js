"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FastifyNames = exports.FastifyTypes = exports.AttributeNames = void 0;
var AttributeNames;
(function (AttributeNames) {
    AttributeNames["FASTIFY_NAME"] = "fastify.name";
    AttributeNames["FASTIFY_TYPE"] = "fastify.type";
    AttributeNames["HOOK_NAME"] = "hook.name";
    AttributeNames["PLUGIN_NAME"] = "plugin.name";
})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));
var FastifyTypes;
(function (FastifyTypes) {
    FastifyTypes["MIDDLEWARE"] = "middleware";
    FastifyTypes["REQUEST_HANDLER"] = "request_handler";
})(FastifyTypes = exports.FastifyTypes || (exports.FastifyTypes = {}));
var FastifyNames;
(function (FastifyNames) {
    FastifyNames["MIDDLEWARE"] = "middleware";
    FastifyNames["REQUEST_HANDLER"] = "request handler";
})(FastifyNames = exports.FastifyNames || (exports.FastifyNames = {}));
//# sourceMappingURL=AttributeNames.js.map