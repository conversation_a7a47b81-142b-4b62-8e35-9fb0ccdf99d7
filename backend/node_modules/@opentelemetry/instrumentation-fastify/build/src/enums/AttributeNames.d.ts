export declare enum AttributeNames {
    FASTIFY_NAME = "fastify.name",
    FASTIFY_TYPE = "fastify.type",
    HOOK_NAME = "hook.name",
    PLUGIN_NAME = "plugin.name"
}
export declare enum FastifyTypes {
    MIDDLEWARE = "middleware",
    REQUEST_HANDLER = "request_handler"
}
export declare enum FastifyNames {
    MIDDLEWARE = "middleware",
    REQUEST_HANDLER = "request handler"
}
//# sourceMappingURL=AttributeNames.d.ts.map