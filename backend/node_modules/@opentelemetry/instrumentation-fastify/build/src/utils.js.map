{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA8E;AAC9E,2CAAgD;AAIhD;;;;;;GAMG;AACH,SAAgB,SAAS,CACvB,KAAyB,EACzB,MAAc,EACd,QAAgB,EAChB,iBAA6B,EAAE;IAE/B,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,cAAc,EAAE,CAAC,CAAC;IAExE,MAAM,KAAK,GAAW,KAAK,CAAC,6BAAiB,CAAC,IAAI,EAAE,CAAC;IACrD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,mEAAmE;IACnE,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,6BAAiB,EAAE;QAC9C,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE,IAAI;QAClB,KAAK,EAAE,KAAK;KACb,CAAC,CAAC;IAEH,OAAO,IAAI,CAAC;AACd,CAAC;AAnBD,8BAmBC;AAED;;;;GAIG;AACH,SAAgB,OAAO,CAAC,KAAyB,EAAE,GAAS;IAC1D,MAAM,KAAK,GAAG,KAAK,CAAC,6BAAiB,CAAC,IAAI,EAAE,CAAC;IAC7C,mDAAmD;IACnD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACjB,OAAO;KACR;IACD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACnB,IAAI,GAAG,EAAE;YACP,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;SAC3B;QACD,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC,CAAC,CAAC;IACH,OAAO,KAAK,CAAC,6BAAiB,CAAC,CAAC;AAClC,CAAC;AAjBD,0BAiBC;AAwBD,SAAgB,kCAAkC,CAChD,OAA6B,EAC7B,QAA0C,EAC1C,oBAA8B;IAE9B,IAAI,KAAc,CAAC;IACnB,IAAI,MAAM,GAA+B,SAAS,CAAC;IACnD,IAAI;QACF,MAAM,GAAG,OAAO,EAAE,CAAC;QAEnB,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;YACrB,MAAM,CAAC,IAAI,CACT,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,EAC/B,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CACrB,CAAC;SACH;KACF;IAAC,OAAO,CAAC,EAAE;QACV,KAAK,GAAG,CAAC,CAAC;KACX;YAAS;QACR,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACtB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACxB,IAAI,KAAK,IAAI,CAAC,oBAAoB,EAAE;gBAClC,6CAA6C;gBAC7C,MAAM,KAAK,CAAC;aACb;SACF;QACD,6CAA6C;QAC7C,OAAO,MAAM,CAAC;KACf;AACH,CAAC;AA7BD,gFA6BC;AAED,SAAS,SAAS,CAAI,GAAmB;;IACvC,OAAO,CACL,CAAC,OAAO,GAAG,KAAK,QAAQ;QACtB,GAAG;QACH,OAAO,CAAA,MAAA,MAAM,CAAC,wBAAwB,CAAC,GAAG,EAAE,MAAM,CAAC,0CAAE,KAAK,CAAA;YACxD,UAAU,CAAC;QACf,KAAK,CACN,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes, Span, SpanStatusCode, Tracer } from '@opentelemetry/api';\nimport { spanRequestSymbol } from './constants';\n\nimport type { PluginFastifyReply } from './internal-types';\n\n/**\n * Starts Span\n * @param reply - reply function\n * @param tracer - tracer\n * @param spanName - span name\n * @param spanAttributes - span attributes\n */\nexport function startSpan(\n  reply: PluginFastifyReply,\n  tracer: Tracer,\n  spanName: string,\n  spanAttributes: Attributes = {}\n) {\n  const span = tracer.startSpan(spanName, { attributes: spanAttributes });\n\n  const spans: Span[] = reply[spanRequestSymbol] || [];\n  spans.push(span);\n\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  Object.defineProperty(reply, spanRequestSymbol, {\n    enumerable: false,\n    configurable: true,\n    value: spans,\n  });\n\n  return span;\n}\n\n/**\n * Ends span\n * @param reply - reply function\n * @param err - error\n */\nexport function endSpan(reply: PluginFastifyReply, err?: any) {\n  const spans = reply[spanRequestSymbol] || [];\n  // there is no active span, or it has already ended\n  if (!spans.length) {\n    return;\n  }\n  spans.forEach(span => {\n    if (err) {\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      });\n      span.recordException(err);\n    }\n    span.end();\n  });\n  delete reply[spanRequestSymbol];\n}\n\n// @TODO after approve add this to instrumentation package and replace usage\n// when it will be released\n\n/**\n * This function handles the missing case from instrumentation package when\n * execute can either return a promise or void. And using async is not an\n * option as it is producing unwanted side effects.\n * @param execute - function to be executed\n * @param onFinish - function called when function executed\n * @param preventThrowingError - prevent to throw error when execute\n * function fails\n */\nexport function safeExecuteInTheMiddleMaybePromise<T>(\n  execute: () => Promise<T>,\n  onFinish: (e: unknown, result?: T) => void,\n  preventThrowingError?: boolean\n): Promise<T>;\nexport function safeExecuteInTheMiddleMaybePromise<T>(\n  execute: () => T,\n  onFinish: (e: unknown, result?: T) => void,\n  preventThrowingError?: boolean\n): T;\nexport function safeExecuteInTheMiddleMaybePromise<T>(\n  execute: () => T | Promise<T>,\n  onFinish: (e: unknown, result?: T) => void,\n  preventThrowingError?: boolean\n): T | Promise<T> | undefined {\n  let error: unknown;\n  let result: T | Promise<T> | undefined = undefined;\n  try {\n    result = execute();\n\n    if (isPromise(result)) {\n      result.then(\n        res => onFinish(undefined, res),\n        err => onFinish(err)\n      );\n    }\n  } catch (e) {\n    error = e;\n  } finally {\n    if (!isPromise(result)) {\n      onFinish(error, result);\n      if (error && !preventThrowingError) {\n        // eslint-disable-next-line no-unsafe-finally\n        throw error;\n      }\n    }\n    // eslint-disable-next-line no-unsafe-finally\n    return result;\n  }\n}\n\nfunction isPromise<T>(val: T | Promise<T>): val is Promise<T> {\n  return (\n    (typeof val === 'object' &&\n      val &&\n      typeof Object.getOwnPropertyDescriptor(val, 'then')?.value ===\n        'function') ||\n    false\n  );\n}\n"]}