{"name": "@opentelemetry/instrumentation-fastify", "version": "0.32.6", "description": "OpenTelemetry fastify automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-fastify --include-dependencies", "prepublishOnly": "npm run compile", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test-all-versions": "tav", "version:update": "node ../../../scripts/version-update.js", "prewatch": "npm run precompile", "watch": "tsc -w"}, "keywords": ["fastify", "instrumentation", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@fastify/express": "^2.0.2", "@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.35.1", "@opentelemetry/instrumentation-http": "^0.46.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/express": "4.17.18", "@types/mocha": "7.0.2", "@types/node": "18.15.3", "@types/semver": "7.5.5", "fastify": "4.18.0", "mocha": "7.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "semver": "^7.5.4", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-fastify#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}