{"version": 3, "file": "mongoose.js", "sourceRoot": "", "sources": ["../../src/mongoose.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAM4B;AAC5B,8CAAsD;AAGtD,mCAIiB;AACjB,oEAIwC;AACxC,uCAAoC;AACpC,8EAAyE;AAEzE,MAAM,uBAAuB,GAAG;IAC9B,QAAQ;IACR,WAAW;IACX,YAAY;IACZ,MAAM;IACN,SAAS;IACT,wBAAwB;IACxB,gBAAgB;IAChB,OAAO;IACP,UAAU;IACV,OAAO;IACP,QAAQ;IACR,kBAAkB;IAClB,kBAAkB;IAClB,mBAAmB;IACnB,kBAAkB;CACnB,CAAC;AAEF,yEAAyE;AACzE,gFAAgF;AAChF,6EAA6E;AAChE,QAAA,mBAAmB,GAAkB,MAAM,CAAC,oBAAoB,CAAC,CAAC;AAE/E,MAAa,uBAAwB,SAAQ,qCAAwB;IAGnE,YAAY,SAAwC,EAAE;QACpD,KAAK,CACH,yCAAyC,EACzC,iBAAO,EACP,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAC1B,CAAC;IACJ,CAAC;IAEQ,SAAS,CAAC,SAAwC,EAAE;QAC3D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAES,IAAI;QACZ,MAAM,MAAM,GAAG,IAAI,qDAAmC,CACpD,UAAU,EACV,CAAC,YAAY,CAAC,EACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CACxB,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CACX,aAA8B,EAC9B,aAAiC;QAEjC,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,KAAK,CAAC,SAAS,EAC7B,MAAM,EACN,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,aAAa,CAAC,CAChD,CAAC;QACF,gDAAgD;QAChD,gDAAgD;QAChD,yDAAyD;QACzD,4EAA4E;QAC5E,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;QAEzE,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,KAAK,CAAC,SAAS,EAC7B,QAAQ,EACR,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAClD,CAAC;QACF,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,KAAK,CAAC,SAAS,EAC7B,MAAM,EACN,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CACnC,CAAC;QACF,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,SAAS,CAAC,SAAS,EACjC,MAAM,EACN,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CACvC,CAAC;QAEF,uBAAuB,CAAC,OAAO,CAAC,CAAC,QAAgB,EAAE,EAAE;YACnD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,KAAK,CAAC,SAAS,EAC7B,QAAe,EACf,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAC1C,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAEzE,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,OAAO,CAAC,aAA8B;QAC5C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACpD,+EAA+E;QAC/E,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;QACzE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAExD,uBAAuB,CAAC,OAAO,CAAC,CAAC,QAAgB,EAAE,EAAE;YACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,QAAe,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IACjD,CAAC;IAEO,kBAAkB,CAAC,aAAiC;QAC1D,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC7D,OAAO,CAAC,iBAA2B,EAAE,EAAE;YACrC,OAAO,SAAS,IAAI,CAAY,QAAmB;;gBACjD,IACE,IAAI,CAAC,OAAO,CAAC,iBAAiB;oBAC9B,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,EAC7C;oBACA,OAAO,iBAAiB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACjD;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,2BAAmB,CAAC,CAAC;gBAC7C,MAAM,UAAU,GAAmB,EAAE,CAAC;gBACtC,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBACtC,UAAU,CAAC,yCAAkB,CAAC,YAAY,CAAC;wBACzC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,WAAW,EAAE;4BAC9C,OAAO,EAAE,IAAI,CAAC,OAAO;4BACrB,iBAAiB,EAAE,IAAI,CAAC,SAAS;yBAClC,CAAC,CAAC;iBACN;gBAED,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAC1B,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,MAAA,IAAI,CAAC,MAAM,0CAAE,SAAS,EACtB,WAAW,EACX,UAAU,EACV,UAAU,CACX,CAAC;gBAEF,OAAO,IAAI,CAAC,eAAe,CACzB,IAAI,EACJ,iBAAiB,EACjB,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,aAAa,CACd,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,aAAiC;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;QACzD,OAAO,CAAC,YAAsB,EAAE,EAAE;YAChC,OAAO,SAAS,IAAI,CAAY,QAAmB;gBACjD,IACE,IAAI,CAAC,OAAO,CAAC,iBAAiB;oBAC9B,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,EAC7C;oBACA,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBAC5C;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,2BAAmB,CAAC,CAAC;gBAC7C,MAAM,UAAU,GAAmB,EAAE,CAAC;gBACtC,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBACtC,UAAU,CAAC,yCAAkB,CAAC,YAAY,CAAC;wBACzC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE;4BAC1C,SAAS,EAAE,IAAI,CAAC,WAAW;4BAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;4BACrB,OAAO,EAAE,IAAI,CAAC,OAAO;4BACrB,MAAM,EAAE,IAAI,CAAC,OAAO;yBACrB,CAAC,CAAC;iBACN;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAC1B,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,KAAK,CAAC,SAAS,EACpB,IAAI,CAAC,EAAE,EACP,UAAU,EACV,UAAU,CACX,CAAC;gBAEF,OAAO,IAAI,CAAC,eAAe,CACzB,IAAI,EACJ,YAAY,EACZ,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,aAAa,CACd,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,EAAU,EAAE,aAAiC;QACvE,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,4BAA4B,EAAE,aAAa,CAAC,CAAC;QAC9D,OAAO,CAAC,uBAAiC,EAAE,EAAE;YAC3C,OAAO,SAAS,MAAM,CAAY,OAAa,EAAE,QAAmB;gBAClE,IACE,IAAI,CAAC,OAAO,CAAC,iBAAiB;oBAC9B,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,EAC7C;oBACA,OAAO,uBAAuB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACvD;gBAED,MAAM,gBAAgB,GAAsB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;gBAC/D,IAAI,OAAO,IAAI,CAAC,CAAC,OAAO,YAAY,QAAQ,CAAC,EAAE;oBAC7C,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC;iBACpC;gBACD,MAAM,UAAU,GAAmB,EAAE,CAAC;gBACtC,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBACtC,UAAU,CAAC,yCAAkB,CAAC,YAAY,CAAC;wBACzC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;iBAC5D;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAC1B,IAAI,CAAC,WAAW,CAAC,UAAU,EAC3B,IAAI,CAAC,WAAW,CAAC,SAAS,EAC1B,EAAE,EACF,UAAU,CACX,CAAC;gBAEF,IAAI,OAAO,YAAY,QAAQ,EAAE;oBAC/B,QAAQ,GAAG,OAAO,CAAC;oBACnB,OAAO,GAAG,SAAS,CAAC;iBACrB;gBAED,OAAO,IAAI,CAAC,eAAe,CACzB,IAAI,EACJ,uBAAuB,EACvB,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,aAAa,CACd,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,wEAAwE;IACxE,qEAAqE;IACrE,iEAAiE;IACjE,sEAAsE;IAC9D,mBAAmB;QACzB,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC9D,OAAO,CAAC,QAAkB,EAAE,EAAE;YAC5B,OAAO,SAAS,kBAAkB;gBAChC,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpD,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,CAChD,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAChC,CAAC;gBACF,IAAI,SAAS;oBAAE,SAAS,CAAC,2BAAmB,CAAC,GAAG,WAAW,CAAC;gBAC5D,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,0BAA0B,CAAC,QAAgB;QACjD,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,2BAA2B,QAAQ,WAAW,CAAC,CAAC;QACjE,OAAO,CAAC,QAAkB,EAAE,EAAE;YAC5B,OAAO,SAAS,kBAAkB;gBAChC,IAAI,CAAC,2BAAmB,CAAC,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC5D,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,CACrC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAChC,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,UAAU,CAChB,UAA+B,EAC/B,SAAiB,EACjB,SAAiB,EACjB,UAA0B,EAC1B,UAAiB;QAEjB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAC1B,YAAY,SAAS,IAAI,SAAS,EAAE,EACpC;YACE,IAAI,EAAE,cAAQ,CAAC,MAAM;YACrB,UAAU,gDACL,UAAU,GACV,IAAA,mCAA2B,EAAC,UAAU,CAAC,KAC1C,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAAE,SAAS,EAC5C,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,UAAU,GAC3C;SACF,EACD,UAAU,CAAC,CAAC,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CACrE,CAAC;IACJ,CAAC;IAEO,eAAe,CACrB,IAAU,EACV,IAAc,EACd,YAAiB,EACjB,IAAgB,EAChB,QAAmB,EACnB,gBAAoC,SAAS;QAE7C,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,QAAQ,YAAY,QAAQ,EAAE;YAChC,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,CACrC,IAAA,8BAAsB,EACpB,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,IAAI,EACJ,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,aAAa,CACd,CACF,CAAC;SACH;aAAM;YACL,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,CAC/C,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,CAC/B,CAAC;YACF,OAAO,IAAA,6BAAqB,EAC1B,QAAQ,EACR,IAAI,EACJ,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,aAAa,CACd,CAAC;SACH;IACH,CAAC;IAEO,qBAAqB,CAAI,gBAAuC;;QACtE,IAAI,MAAA,IAAI,CAAC,OAAO,0CAAE,+BAA+B,EAAE;YACjD,OAAO,aAAO,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC;SAC1E;aAAM;YACL,OAAO,gBAAgB,EAAE,CAAC;SAC3B;IACH,CAAC;CACF;AAnTD,0DAmTC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  context,\n  Span,\n  trace,\n  SpanAttributes,\n  SpanKind,\n} from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport type * as mongoose from 'mongoose';\nimport { MongooseInstrumentationConfig, SerializerPayload } from './types';\nimport {\n  handleCallbackResponse,\n  handlePromiseResponse,\n  getAttributesFromCollection,\n} from './utils';\nimport {\n  InstrumentationBase,\n  InstrumentationModuleDefinition,\n  InstrumentationNodeModuleDefinition,\n} from '@opentelemetry/instrumentation';\nimport { VERSION } from './version';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\n\nconst contextCaptureFunctions = [\n  'remove',\n  'deleteOne',\n  'deleteMany',\n  'find',\n  'findOne',\n  'estimatedDocumentCount',\n  'countDocuments',\n  'count',\n  'distinct',\n  'where',\n  '$where',\n  'findOneAndUpdate',\n  'findOneAndDelete',\n  'findOneAndReplace',\n  'findOneAndRemove',\n];\n\n// when mongoose functions are called, we store the original call context\n// and then set it as the parent for the spans created by Query/Aggregate exec()\n// calls. this bypass the unlinked spans issue on thenables await operations.\nexport const _STORED_PARENT_SPAN: unique symbol = Symbol('stored-parent-span');\n\nexport class MongooseInstrumentation extends InstrumentationBase<any> {\n  protected override _config!: MongooseInstrumentationConfig;\n\n  constructor(config: MongooseInstrumentationConfig = {}) {\n    super(\n      '@opentelemetry/instrumentation-mongoose',\n      VERSION,\n      Object.assign({}, config)\n    );\n  }\n\n  override setConfig(config: MongooseInstrumentationConfig = {}) {\n    this._config = Object.assign({}, config);\n  }\n\n  protected init(): InstrumentationModuleDefinition<any> {\n    const module = new InstrumentationNodeModuleDefinition<any>(\n      'mongoose',\n      ['>=5.9.7 <7'],\n      this.patch.bind(this),\n      this.unpatch.bind(this)\n    );\n    return module;\n  }\n\n  private patch(\n    moduleExports: typeof mongoose,\n    moduleVersion: string | undefined\n  ) {\n    this._wrap(\n      moduleExports.Model.prototype,\n      'save',\n      this.patchOnModelMethods('save', moduleVersion)\n    );\n    // mongoose applies this code on moudle require:\n    // Model.prototype.$save = Model.prototype.save;\n    // which captures the save function before it is patched.\n    // so we need to apply the same logic after instrumenting the save function.\n    moduleExports.Model.prototype.$save = moduleExports.Model.prototype.save;\n\n    this._wrap(\n      moduleExports.Model.prototype,\n      'remove',\n      this.patchOnModelMethods('remove', moduleVersion)\n    );\n    this._wrap(\n      moduleExports.Query.prototype,\n      'exec',\n      this.patchQueryExec(moduleVersion)\n    );\n    this._wrap(\n      moduleExports.Aggregate.prototype,\n      'exec',\n      this.patchAggregateExec(moduleVersion)\n    );\n\n    contextCaptureFunctions.forEach((funcName: string) => {\n      this._wrap(\n        moduleExports.Query.prototype,\n        funcName as any,\n        this.patchAndCaptureSpanContext(funcName)\n      );\n    });\n    this._wrap(moduleExports.Model, 'aggregate', this.patchModelAggregate());\n\n    return moduleExports;\n  }\n\n  private unpatch(moduleExports: typeof mongoose): void {\n    this._diag.debug('mongoose instrumentation: unpatch mongoose');\n    this._unwrap(moduleExports.Model.prototype, 'save');\n    // revert the patch for $save which we applyed by aliasing it to patched `save`\n    moduleExports.Model.prototype.$save = moduleExports.Model.prototype.save;\n    this._unwrap(moduleExports.Model.prototype, 'remove');\n    this._unwrap(moduleExports.Query.prototype, 'exec');\n    this._unwrap(moduleExports.Aggregate.prototype, 'exec');\n\n    contextCaptureFunctions.forEach((funcName: string) => {\n      this._unwrap(moduleExports.Query.prototype, funcName as any);\n    });\n    this._unwrap(moduleExports.Model, 'aggregate');\n  }\n\n  private patchAggregateExec(moduleVersion: string | undefined) {\n    const self = this;\n    this._diag.debug('patched mongoose Aggregate exec function');\n    return (originalAggregate: Function) => {\n      return function exec(this: any, callback?: Function) {\n        if (\n          self._config.requireParentSpan &&\n          trace.getSpan(context.active()) === undefined\n        ) {\n          return originalAggregate.apply(this, arguments);\n        }\n\n        const parentSpan = this[_STORED_PARENT_SPAN];\n        const attributes: SpanAttributes = {};\n        if (self._config.dbStatementSerializer) {\n          attributes[SemanticAttributes.DB_STATEMENT] =\n            self._config.dbStatementSerializer('aggregate', {\n              options: this.options,\n              aggregatePipeline: this._pipeline,\n            });\n        }\n\n        const span = self._startSpan(\n          this._model.collection,\n          this._model?.modelName,\n          'aggregate',\n          attributes,\n          parentSpan\n        );\n\n        return self._handleResponse(\n          span,\n          originalAggregate,\n          this,\n          arguments,\n          callback,\n          moduleVersion\n        );\n      };\n    };\n  }\n\n  private patchQueryExec(moduleVersion: string | undefined) {\n    const self = this;\n    this._diag.debug('patched mongoose Query exec function');\n    return (originalExec: Function) => {\n      return function exec(this: any, callback?: Function) {\n        if (\n          self._config.requireParentSpan &&\n          trace.getSpan(context.active()) === undefined\n        ) {\n          return originalExec.apply(this, arguments);\n        }\n\n        const parentSpan = this[_STORED_PARENT_SPAN];\n        const attributes: SpanAttributes = {};\n        if (self._config.dbStatementSerializer) {\n          attributes[SemanticAttributes.DB_STATEMENT] =\n            self._config.dbStatementSerializer(this.op, {\n              condition: this._conditions,\n              updates: this._update,\n              options: this.options,\n              fields: this._fields,\n            });\n        }\n        const span = self._startSpan(\n          this.mongooseCollection,\n          this.model.modelName,\n          this.op,\n          attributes,\n          parentSpan\n        );\n\n        return self._handleResponse(\n          span,\n          originalExec,\n          this,\n          arguments,\n          callback,\n          moduleVersion\n        );\n      };\n    };\n  }\n\n  private patchOnModelMethods(op: string, moduleVersion: string | undefined) {\n    const self = this;\n    this._diag.debug(`patching mongoose Model '${op}' operation`);\n    return (originalOnModelFunction: Function) => {\n      return function method(this: any, options?: any, callback?: Function) {\n        if (\n          self._config.requireParentSpan &&\n          trace.getSpan(context.active()) === undefined\n        ) {\n          return originalOnModelFunction.apply(this, arguments);\n        }\n\n        const serializePayload: SerializerPayload = { document: this };\n        if (options && !(options instanceof Function)) {\n          serializePayload.options = options;\n        }\n        const attributes: SpanAttributes = {};\n        if (self._config.dbStatementSerializer) {\n          attributes[SemanticAttributes.DB_STATEMENT] =\n            self._config.dbStatementSerializer(op, serializePayload);\n        }\n        const span = self._startSpan(\n          this.constructor.collection,\n          this.constructor.modelName,\n          op,\n          attributes\n        );\n\n        if (options instanceof Function) {\n          callback = options;\n          options = undefined;\n        }\n\n        return self._handleResponse(\n          span,\n          originalOnModelFunction,\n          this,\n          arguments,\n          callback,\n          moduleVersion\n        );\n      };\n    };\n  }\n\n  // we want to capture the otel span on the object which is calling exec.\n  // in the special case of aggregate, we need have no function to path\n  // on the Aggregate object to capture the context on, so we patch\n  // the aggregate of Model, and set the context on the Aggregate object\n  private patchModelAggregate() {\n    const self = this;\n    this._diag.debug('patched mongoose model aggregate function');\n    return (original: Function) => {\n      return function captureSpanContext(this: any) {\n        const currentSpan = trace.getSpan(context.active());\n        const aggregate = self._callOriginalFunction(() =>\n          original.apply(this, arguments)\n        );\n        if (aggregate) aggregate[_STORED_PARENT_SPAN] = currentSpan;\n        return aggregate;\n      };\n    };\n  }\n\n  private patchAndCaptureSpanContext(funcName: string) {\n    const self = this;\n    this._diag.debug(`patching mongoose query ${funcName} function`);\n    return (original: Function) => {\n      return function captureSpanContext(this: any) {\n        this[_STORED_PARENT_SPAN] = trace.getSpan(context.active());\n        return self._callOriginalFunction(() =>\n          original.apply(this, arguments)\n        );\n      };\n    };\n  }\n\n  private _startSpan(\n    collection: mongoose.Collection,\n    modelName: string,\n    operation: string,\n    attributes: SpanAttributes,\n    parentSpan?: Span\n  ): Span {\n    return this.tracer.startSpan(\n      `mongoose.${modelName}.${operation}`,\n      {\n        kind: SpanKind.CLIENT,\n        attributes: {\n          ...attributes,\n          ...getAttributesFromCollection(collection),\n          [SemanticAttributes.DB_OPERATION]: operation,\n          [SemanticAttributes.DB_SYSTEM]: 'mongoose',\n        },\n      },\n      parentSpan ? trace.setSpan(context.active(), parentSpan) : undefined\n    );\n  }\n\n  private _handleResponse(\n    span: Span,\n    exec: Function,\n    originalThis: any,\n    args: IArguments,\n    callback?: Function,\n    moduleVersion: string | undefined = undefined\n  ) {\n    const self = this;\n    if (callback instanceof Function) {\n      return self._callOriginalFunction(() =>\n        handleCallbackResponse(\n          callback,\n          exec,\n          originalThis,\n          span,\n          self._config.responseHook,\n          moduleVersion\n        )\n      );\n    } else {\n      const response = self._callOriginalFunction(() =>\n        exec.apply(originalThis, args)\n      );\n      return handlePromiseResponse(\n        response,\n        span,\n        self._config.responseHook,\n        moduleVersion\n      );\n    }\n  }\n\n  private _callOriginalFunction<T>(originalFunction: (...args: any[]) => T): T {\n    if (this._config?.suppressInternalInstrumentation) {\n      return context.with(suppressTracing(context.active()), originalFunction);\n    } else {\n      return originalFunction();\n    }\n  }\n}\n"]}