{"name": "@opentelemetry/instrumentation-amqplib", "version": "0.33.5", "description": "OpenTelemetry automatic instrumentation for the `amqplib` package", "keywords": ["amqplib", "opentelemetry", "rabbitmq", "AMQP 0-9-1"], "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/instrumentation-amqplib#readme", "license": "Apache-2.0", "author": "OpenTelemetry Authors", "bugs": {"url": "https://github.com/open-telemetry/opentelemetry-js-contrib/issues"}, "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "npm run version:update && tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-amqplib --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json --require '@opentelemetry/contrib-test-utils' 'test/**/*.test.ts'", "test-all-versions": "tav", "version:update": "node ../../../scripts/version-update.js", "watch": "tsc -w", "test:docker:run": "docker run -d --hostname demo-amqplib-rabbit --name amqplib-unittests -p 22221:5672 --env RABBITMQ_DEFAULT_USER=username --env RABBITMQ_DEFAULT_PASS=password rabbitmq:3"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/contrib-test-utils": "^0.35.1", "@types/amqplib": "^0.5.17", "@types/lodash": "4.14.199", "@types/mocha": "8.2.3", "@types/node": "18.6.5", "@types/sinon": "10.0.18", "amqplib": "0.8.0", "expect": "29.2.0", "lodash": "4.17.21", "mocha": "7.2.0", "nyc": "15.1.0", "sinon": "15.2.0", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "engines": {"node": ">=14"}, "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}