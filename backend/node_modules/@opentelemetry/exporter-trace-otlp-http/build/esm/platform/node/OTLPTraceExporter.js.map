{"version": 3, "file": "OTLPTraceExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/OTLPTraceExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAEL,uBAAuB,EACvB,2BAA2B,GAC5B,MAAM,mCAAmC,CAAC;AAC3C,OAAO,EACL,+BAA+B,GAEhC,MAAM,iCAAiC,CAAC;AACzC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAExC,IAAM,+BAA+B,GAAG,WAAW,CAAC;AACpD,IAAM,qBAAqB,GAAG,2BAAyB,+BAAiC,CAAC;AACzF,IAAM,UAAU,GAAG;IACjB,YAAY,EAAE,mCAAiC,OAAS;CACzD,CAAC;AAEF;;GAEG;AACH;IACU,qCAA8D;IAGtE,2BAAY,MAAuC;QAAvC,uBAAA,EAAA,WAAuC;QAAnD,YACE,kBAAM,MAAM,CAAC,SAQd;QAPC,KAAI,CAAC,OAAO,kCACP,KAAI,CAAC,OAAO,GACZ,UAAU,GACV,YAAY,CAAC,uBAAuB,CACrC,MAAM,EAAE,CAAC,iCAAiC,CAC3C,CACF,CAAC;;IACJ,CAAC;IAED,mCAAO,GAAP,UAAQ,KAAqB;QAC3B,OAAO,+BAA+B,CAAC,KAAK,EAAE;YAC5C,MAAM,EAAE,IAAI;YACZ,WAAW,EAAE,KAAK;SACnB,CAAC,CAAC;IACL,CAAC;IAED,yCAAa,GAAb,UAAc,MAAkC;QAC9C,OAAO,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ;YACnC,CAAC,CAAC,MAAM,CAAC,GAAG;YACZ,CAAC,CAAC,MAAM,EAAE,CAAC,kCAAkC,CAAC,MAAM,GAAG,CAAC;gBACxD,CAAC,CAAC,2BAA2B,CAAC,MAAM,EAAE,CAAC,kCAAkC,CAAC;gBAC1E,CAAC,CAAC,MAAM,EAAE,CAAC,2BAA2B,CAAC,MAAM,GAAG,CAAC;oBACjD,CAAC,CAAC,uBAAuB,CACrB,MAAM,EAAE,CAAC,2BAA2B,EACpC,+BAA+B,CAChC;oBACH,CAAC,CAAC,qBAAqB,CAAC;IAC5B,CAAC;IACH,wBAAC;AAAD,CAAC,AAlCD,CACU,oBAAoB,GAiC7B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport { OTLPExporterNodeBase } from '@opentelemetry/otlp-exporter-base';\nimport {\n  OTLPExporterNodeConfigBase,\n  appendResourcePathToUrl,\n  appendRootPathToUrlIfNeeded,\n} from '@opentelemetry/otlp-exporter-base';\nimport {\n  createExportTraceServiceRequest,\n  IExportTraceServiceRequest,\n} from '@opentelemetry/otlp-transformer';\nimport { VERSION } from '../../version';\n\nconst DEFAULT_COLLECTOR_RESOURCE_PATH = 'v1/traces';\nconst DEFAULT_COLLECTOR_URL = `http://localhost:4318/${DEFAULT_COLLECTOR_RESOURCE_PATH}`;\nconst USER_AGENT = {\n  'User-Agent': `OTel-OTLP-Exporter-JavaScript/${VERSION}`,\n};\n\n/**\n * Collector Trace Exporter for Node\n */\nexport class OTLPTraceExporter\n  extends OTLPExporterNodeBase<ReadableSpan, IExportTraceServiceRequest>\n  implements SpanExporter\n{\n  constructor(config: OTLPExporterNodeConfigBase = {}) {\n    super(config);\n    this.headers = {\n      ...this.headers,\n      ...USER_AGENT,\n      ...baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_TRACES_HEADERS\n      ),\n    };\n  }\n\n  convert(spans: ReadableSpan[]): IExportTraceServiceRequest {\n    return createExportTraceServiceRequest(spans, {\n      useHex: true,\n      useLongBits: false,\n    });\n  }\n\n  getDefaultUrl(config: OTLPExporterNodeConfigBase): string {\n    return typeof config.url === 'string'\n      ? config.url\n      : getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT.length > 0\n      ? appendRootPathToUrlIfNeeded(getEnv().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT)\n      : getEnv().OTEL_EXPORTER_OTLP_ENDPOINT.length > 0\n      ? appendResourcePathToUrl(\n          getEnv().OTEL_EXPORTER_OTLP_ENDPOINT,\n          DEFAULT_COLLECTOR_RESOURCE_PATH\n        )\n      : DEFAULT_COLLECTOR_URL;\n  }\n}\n"]}