{"version": 3, "file": "callback.js", "sourceRoot": "", "sources": ["../../../src/utils/callback.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;AAErC;;GAEG;AACH,MAAM,OAAO,cAAc;IAOzB,YACU,SAAY,EACZ,KAAW;QADX,cAAS,GAAT,SAAS,CAAG;QACZ,UAAK,GAAL,KAAK,CAAM;QAJb,cAAS,GAAG,KAAK,CAAC;QAClB,cAAS,GAAG,IAAI,QAAQ,EAAK,CAAC;IAInC,CAAC;IAEJ,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED,IAAI,CAAC,GAAG,IAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI;gBACF,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAC5D,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,EAClC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAClC,CAAC;aACH;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aAC5B;SACF;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from './promise';\n\n/**\n * Bind the callback and only invoke the callback once regardless how many times `BindOnceFuture.call` is invoked.\n */\nexport class BindOnceFuture<\n  R,\n  This = unknown,\n  T extends (this: This, ...args: unknown[]) => R = () => R,\n> {\n  private _isCalled = false;\n  private _deferred = new Deferred<R>();\n  constructor(\n    private _callback: T,\n    private _that: This\n  ) {}\n\n  get isCalled() {\n    return this._isCalled;\n  }\n\n  get promise() {\n    return this._deferred.promise;\n  }\n\n  call(...args: Parameters<T>): Promise<R> {\n    if (!this._isCalled) {\n      this._isCalled = true;\n      try {\n        Promise.resolve(this._callback.call(this._that, ...args)).then(\n          val => this._deferred.resolve(val),\n          err => this._deferred.reject(err)\n        );\n      } catch (err) {\n        this._deferred.reject(err);\n      }\n    }\n    return this._deferred.promise;\n  }\n}\n"]}