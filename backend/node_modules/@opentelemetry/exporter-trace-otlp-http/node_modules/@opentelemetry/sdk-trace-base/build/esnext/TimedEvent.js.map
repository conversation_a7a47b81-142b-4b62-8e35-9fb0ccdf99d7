{"version": 3, "file": "TimedEvent.js", "sourceRoot": "", "sources": ["../../src/TimedEvent.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime, SpanAttributes } from '@opentelemetry/api';\n\n/**\n * Represents a timed event.\n * A timed event is an event with a timestamp.\n */\nexport interface TimedEvent {\n  time: HrTime;\n  /** The name of the event. */\n  name: string;\n  /** The attributes of the event. */\n  attributes?: SpanAttributes;\n  /** Count of attributes of the event that were dropped due to collection limits */\n  droppedAttributesCount?: number;\n}\n"]}