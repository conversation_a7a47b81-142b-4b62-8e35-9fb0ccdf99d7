import { Context, Exception, HrTime, Link, Span as APISpan, SpanAttributes, SpanAttributeValue, SpanContext, SpanKind, SpanStatus, TimeInput } from '@opentelemetry/api';
import { InstrumentationLibrary } from '@opentelemetry/core';
import { IResource } from '@opentelemetry/resources';
import { ReadableSpan } from './export/ReadableSpan';
import { TimedEvent } from './TimedEvent';
import { Tracer } from './Tracer';
/**
 * This class represents a span.
 */
export declare class Span implements APISpan, ReadableSpan {
    private readonly _spanContext;
    readonly kind: SpanKind;
    readonly parentSpanId?: string;
    readonly attributes: SpanAttributes;
    readonly links: Link[];
    readonly events: TimedEvent[];
    readonly startTime: HrTime;
    readonly resource: IResource;
    readonly instrumentationLibrary: InstrumentationLibrary;
    private _droppedAttributesCount;
    private _droppedEventsCount;
    private _droppedLinksCount;
    name: string;
    status: SpanStatus;
    endTime: HrTime;
    private _ended;
    private _duration;
    private readonly _spanProcessor;
    private readonly _spanLimits;
    private readonly _attributeValueLengthLimit;
    private readonly _performanceStartTime;
    private readonly _performanceOffset;
    private readonly _startTimeProvided;
    /**
     * Constructs a new Span instance.
     *
     * @deprecated calling Span constructor directly is not supported. Please use tracer.startSpan.
     * */
    constructor(parentTracer: Tracer, context: Context, spanName: string, spanContext: SpanContext, kind: SpanKind, parentSpanId?: string, links?: Link[], startTime?: TimeInput, _deprecatedClock?: unknown);
    spanContext(): SpanContext;
    setAttribute(key: string, value?: SpanAttributeValue): this;
    setAttributes(attributes: SpanAttributes): this;
    /**
     *
     * @param name Span Name
     * @param [attributesOrStartTime] Span attributes or start time
     *     if type is {@type TimeInput} and 3rd param is undefined
     * @param [timeStamp] Specified time stamp for the event
     */
    addEvent(name: string, attributesOrStartTime?: SpanAttributes | TimeInput, timeStamp?: TimeInput): this;
    setStatus(status: SpanStatus): this;
    updateName(name: string): this;
    end(endTime?: TimeInput): void;
    private _getTime;
    isRecording(): boolean;
    recordException(exception: Exception, time?: TimeInput): void;
    get duration(): HrTime;
    get ended(): boolean;
    get droppedAttributesCount(): number;
    get droppedEventsCount(): number;
    get droppedLinksCount(): number;
    private _isSpanEnded;
    private _truncateToLimitUtil;
    /**
     * If the given attribute value is of type string and has more characters than given {@code attributeValueLengthLimit} then
     * return string with trucated to {@code attributeValueLengthLimit} characters
     *
     * If the given attribute value is array of strings then
     * return new array of strings with each element truncated to {@code attributeValueLengthLimit} characters
     *
     * Otherwise return same Attribute {@code value}
     *
     * @param value Attribute value
     * @returns truncated attribute value if required, otherwise same value
     */
    private _truncateToSize;
}
//# sourceMappingURL=Span.d.ts.map