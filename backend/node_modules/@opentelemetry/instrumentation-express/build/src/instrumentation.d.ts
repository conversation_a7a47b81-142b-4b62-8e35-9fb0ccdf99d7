import type * as express from 'express';
import { ExpressInstrumentationConfig, ExpressRequestInfo } from './types';
import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
/** Express instrumentation for OpenTelemetry */
export declare class ExpressInstrumentation extends InstrumentationBase<typeof express> {
    constructor(config?: ExpressInstrumentationConfig);
    setConfig(config?: ExpressInstrumentationConfig): void;
    getConfig(): ExpressInstrumentationConfig;
    init(): InstrumentationNodeModuleDefinition<typeof express>[];
    /**
     * Get the patch for Router.route function
     */
    private _getRoutePatch;
    /**
     * Get the patch for Router.use function
     */
    private _getRouterUsePatch;
    /**
     * Get the patch for Application.use function
     */
    private _getAppUsePatch;
    /** Patch each express layer to create span and propagate context */
    private _applyPatch;
    _getSpanName(info: ExpressRequestInfo, defaultName: string): string;
}
//# sourceMappingURL=instrumentation.d.ts.map