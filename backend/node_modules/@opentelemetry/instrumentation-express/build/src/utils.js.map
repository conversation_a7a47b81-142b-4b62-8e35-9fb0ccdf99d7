{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,+DAA4D;AAC5D,2DAAwD;AACxD,qDAI0B;AAE1B;;;;GAIG;AACI,MAAM,cAAc,GAAG,CAAC,OAAuB,EAAE,KAAc,EAAE,EAAE;IACxE,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,uCAAsB,CAAC,CAAC,KAAK,KAAK,EAAE;QAC5D,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,uCAAsB,EAAE;YACrD,UAAU,EAAE,KAAK;YACjB,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;KACJ;IACD,IAAI,KAAK,KAAK,SAAS;QAAE,OAAO;IAC/B,OAAO,CAAC,uCAAsB,CAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC,CAAC;AATW,QAAA,cAAc,kBASzB;AAEF;;;;GAIG;AACI,MAAM,gBAAgB,GAAG,CAC9B,KAAmB,EACnB,SAAkB,EAIlB,EAAE;IACF,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC3B,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,SAAS;gBACxC,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,mCAAgB,CAAC,MAAM;aACvD;YACD,IAAI,EAAE,YAAY,SAAS,EAAE;SAC9B,CAAC;KACH;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB,EAAE;QAC1C,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,iBAAiB;gBAC7D,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,mCAAgB,CAAC,eAAe;aAChE;YACD,IAAI,EAAE,kBAAkB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;SAC9D,CAAC;KACH;SAAM;QACL,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,IAAI;gBACzC,CAAC,+BAAc,CAAC,YAAY,CAAC,EAAE,mCAAgB,CAAC,UAAU;aAC3D;YACD,IAAI,EAAE,gBAAgB,KAAK,CAAC,IAAI,EAAE;SACnC,CAAC;KACH;AACH,CAAC,CAAC;AAhCW,QAAA,gBAAgB,oBAgC3B;AAEF;;;;;GAKG;AACH,MAAM,gBAAgB,GAAG,CACvB,QAAgB,EAChB,OAAsB,EACb,EAAE;IACX,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO,OAAO,KAAK,QAAQ,CAAC;KAC7B;SAAM,IAAI,OAAO,YAAY,MAAM,EAAE;QACpC,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC/B;SAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;QACxC,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;KAC1B;SAAM;QACL,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;KAC3D;AACH,CAAC,CAAC;AAEF;;;;;;;GAOG;AACI,MAAM,cAAc,GAAG,CAC5B,IAAY,EACZ,IAAsB,EACtB,MAAqC,EAC5B,EAAE;;IACX,IACE,KAAK,CAAC,OAAO,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAC;SACvC,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,0CAAE,QAAQ,CAAC,IAAI,CAAC,CAAA,EACxC;QACA,OAAO,IAAI,CAAC;KACb;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,YAAY,CAAC,KAAK,KAAK;QAAE,OAAO,KAAK,CAAC;IAChE,IAAI;QACF,KAAK,MAAM,OAAO,IAAI,MAAO,CAAC,YAAa,EAAE;YAC3C,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;gBACnC,OAAO,IAAI,CAAC;aACb;SACF;KACF;IAAC,OAAO,CAAC,EAAE;QACV,gBAAgB;KACjB;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAvBW,QAAA,cAAc,kBAuBzB;AAEF;;;;;GAKG;AACI,MAAM,iBAAiB,GAAG,CAC/B,KAAc,EAC4B,EAAE,CAC5C,KAAK,YAAY,KAAK;IACpB,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC;IACxB,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AALxB,QAAA,iBAAiB,qBAKO", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes } from '@opentelemetry/api';\nimport { IgnoreMatcher, ExpressInstrumentationConfig } from './types';\nimport { ExpressLayerType } from './enums/ExpressLayerType';\nimport { AttributeNames } from './enums/AttributeNames';\nimport {\n  ExpressLayer,\n  PatchedRequest,\n  _LAYERS_STORE_PROPERTY,\n} from './internal-types';\n\n/**\n * Store layers path in the request to be able to construct route later\n * @param request The request where\n * @param [value] the value to push into the array\n */\nexport const storeLayerPath = (request: PatchedRequest, value?: string) => {\n  if (Array.isArray(request[_LAYERS_STORE_PROPERTY]) === false) {\n    Object.defineProperty(request, _LAYERS_STORE_PROPERTY, {\n      enumerable: false,\n      value: [],\n    });\n  }\n  if (value === undefined) return;\n  (request[_LAYERS_STORE_PROPERTY] as string[]).push(value);\n};\n\n/**\n * Parse express layer context to retrieve a name and attributes.\n * @param layer Express layer\n * @param [layerPath] if present, the path on which the layer has been mounted\n */\nexport const getLayerMetadata = (\n  layer: ExpressLayer,\n  layerPath?: string\n): {\n  attributes: Attributes;\n  name: string;\n} => {\n  if (layer.name === 'router') {\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: layerPath,\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.ROUTER,\n      },\n      name: `router - ${layerPath}`,\n    };\n  } else if (layer.name === 'bound dispatch') {\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: layerPath ?? 'request handler',\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.REQUEST_HANDLER,\n      },\n      name: `request handler${layer.path ? ` - ${layerPath}` : ''}`,\n    };\n  } else {\n    return {\n      attributes: {\n        [AttributeNames.EXPRESS_NAME]: layer.name,\n        [AttributeNames.EXPRESS_TYPE]: ExpressLayerType.MIDDLEWARE,\n      },\n      name: `middleware - ${layer.name}`,\n    };\n  }\n};\n\n/**\n * Check whether the given obj match pattern\n * @param constant e.g URL of request\n * @param obj obj to inspect\n * @param pattern Match pattern\n */\nconst satisfiesPattern = (\n  constant: string,\n  pattern: IgnoreMatcher\n): boolean => {\n  if (typeof pattern === 'string') {\n    return pattern === constant;\n  } else if (pattern instanceof RegExp) {\n    return pattern.test(constant);\n  } else if (typeof pattern === 'function') {\n    return pattern(constant);\n  } else {\n    throw new TypeError('Pattern is in unsupported datatype');\n  }\n};\n\n/**\n * Check whether the given request is ignored by configuration\n * It will not re-throw exceptions from `list` provided by the client\n * @param constant e.g URL of request\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nexport const isLayerIgnored = (\n  name: string,\n  type: ExpressLayerType,\n  config?: ExpressInstrumentationConfig\n): boolean => {\n  if (\n    Array.isArray(config?.ignoreLayersType) &&\n    config?.ignoreLayersType?.includes(type)\n  ) {\n    return true;\n  }\n  if (Array.isArray(config?.ignoreLayers) === false) return false;\n  try {\n    for (const pattern of config!.ignoreLayers!) {\n      if (satisfiesPattern(name, pattern)) {\n        return true;\n      }\n    }\n  } catch (e) {\n    /* catch block*/\n  }\n\n  return false;\n};\n\n/**\n * Converts a user-provided error value into an error and error message pair\n *\n * @param error - User-provided error value\n * @returns Both an Error or string representation of the value and an error message\n */\nexport const asErrorAndMessage = (\n  error: unknown\n): [error: string | Error, message: string] =>\n  error instanceof Error\n    ? [error, error.message]\n    : [String(error), String(error)];\n"]}