{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";;;AAoBA,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,iCAAiB,CAAA;IACjB,yCAAyB,CAAA;AAC3B,CAAC,EAHW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAGvB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { Middleware, ParameterizedContext, DefaultState } from 'koa';\nimport type { RouterParamContext } from '@koa/router';\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport enum KoaLayerType {\n  ROUTER = 'router',\n  MIDDLEWARE = 'middleware',\n}\n\nexport type KoaContext = ParameterizedContext<DefaultState, RouterParamContext>;\n\nexport type KoaRequestInfo = {\n  context: KoaContext;\n  middlewareLayer: Middleware<DefaultState, KoaContext>;\n  layerType: KoaLayerType;\n};\n\n/**\n * Function that can be used to add custom attributes to the current span\n * @param span - The Express middleware layer span.\n * @param context - The current KoaContext.\n */\nexport interface KoaRequestCustomAttributeFunction {\n  (span: Span, info: KoaRequestInfo): void;\n}\n\n/**\n * Options available for the Koa Instrumentation (see [documentation](https://github.com/open-telemetry/opentelemetry-js/tree/main/packages/opentelemetry-Instrumentation-koa#koa-Instrumentation-options))\n */\nexport interface KoaInstrumentationConfig extends InstrumentationConfig {\n  /** Ignore specific layers based on their type */\n  ignoreLayersType?: KoaLayerType[];\n  /** Function for adding custom attributes to each middleware layer span */\n  requestHook?: KoaRequestCustomAttributeFunction;\n}\n"]}