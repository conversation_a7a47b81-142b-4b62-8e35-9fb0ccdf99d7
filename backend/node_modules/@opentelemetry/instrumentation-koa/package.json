{"name": "@opentelemetry/instrumentation-koa", "version": "0.36.4", "description": "OpenTelemetry Koa automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"test": "nyc ts-mocha -p tsconfig.json 'test/**/*.ts'", "test-all-versions": "tav", "tdd": "yarn test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-koa --include-dependencies", "prewatch": "npm run precompile", "version:update": "node ../../../scripts/version-update.js", "compile": "tsc -p .", "compile:examples": "cd examples && npm run compile", "prepublishOnly": "npm run compile", "watch": "tsc -w"}, "keywords": ["instrumentation", "koa", "nodejs", "opentelemetry", "plugin", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@koa/router": "12.0.0", "@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.35.1", "@opentelemetry/instrumentation-http": "^0.46.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "@types/sinon": "10.0.18", "koa": "2.13.1", "mocha": "7.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "sinon": "15.2.0", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/koa": "2.13.9", "@types/koa__router": "12.0.3"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-koa#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}