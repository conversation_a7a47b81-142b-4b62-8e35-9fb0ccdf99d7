{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/constants.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEU,QAAA,WAAW,GAAG,MAAM,CAAC;AACrB,QAAA,kBAAkB,GAAG;IAChC,2FAA2F;IAC3F,UAAU;IACV,wBAAwB;IACxB,kBAAkB;IAClB,kBAAkB;IAClB,wBAAwB;IACxB,kBAAkB;CACnB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const MODULE_NAME = 'knex';\nexport const SUPPORTED_VERSIONS = [\n  // use \"lib/execution\" for runner.js, \"lib\" for client.js as basepath, latest tested 0.95.6\n  '>=0.22.0',\n  // use \"lib\" as basepath\n  '>=0.10.0 <0.18.0',\n  '>=0.19.0 <0.22.0',\n  // use \"src\" as basepath\n  '>=0.18.0 <0.19.0',\n];\n"]}