{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAC1C,uCAAoC;AACpC,yCAAyC;AACzC,oEAKwC;AACxC,8EAAyE;AACzE,iCAAiC;AAKjC,MAAM,aAAa,GAAG,MAAM,CAAC,4CAA4C,CAAC,CAAC;AAC3E,MAAM,cAAc,GAAoC;IACtD,cAAc,EAAE,IAAI;CACrB,CAAC;AAEF,MAAa,mBAAoB,SAAQ,qCAAwB;IAC/D,YAAY,SAA0C,EAAE;QACtD,KAAK,CACH,kCAAkC,SAAS,CAAC,WAAW,EAAE,EACzD,iBAAO,EACP,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,CAC1C,CAAC;IACJ,CAAC;IAED,IAAI;QACF,MAAM,MAAM,GAAG,IAAI,qDAAmC,CACpD,SAAS,CAAC,WAAW,EACrB,SAAS,CAAC,kBAAkB,CAC7B,CAAC;QAEF,MAAM,CAAC,KAAK,CAAC,IAAI,CACf,IAAI,CAAC,sCAAsC,CAAC,KAAK,CAAC,EAClD,IAAI,CAAC,sCAAsC,CAAC,KAAK,CAAC,EAClD,IAAI,CAAC,sCAAsC,CAAC,KAAK,CAAC,EAClD,IAAI,CAAC,sCAAsC,CAAC,KAAK,CAAC,EAClD,IAAI,CAAC,sCAAsC,CAAC,eAAe,CAAC,CAC7D,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,sCAAsC,CAAC,QAAgB;QAC7D,OAAO,IAAI,+CAA6B,CACtC,QAAQ,QAAQ,YAAY,EAC5B,SAAS,CAAC,kBAAkB,EAC5B,CAAC,MAAW,EAAE,aAAa,EAAE,EAAE;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,YAAY,QAAQ,wBAAwB,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CACrF,CAAC;YACF,IAAI,CAAC,aAAa,CAChB,aAAa,EACb,MAAM,CAAC,SAAS,EAChB,OAAO,EACP,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CACvC,CAAC;YACF,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,MAAW,EAAE,aAAa,EAAE,EAAE;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,YAAY,QAAQ,wBAAwB,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CACrF,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACxC,OAAO,MAAM,CAAC;QAChB,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,sCAAsC,CAAC,QAAgB;QAC7D,OAAO,IAAI,+CAA6B,CACtC,QAAQ,QAAQ,YAAY,EAC5B,SAAS,CAAC,kBAAkB,EAC5B,CAAC,MAAW,EAAE,aAAa,EAAE,EAAE;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,YAAY,QAAQ,wBAAwB,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CACrF,CAAC;YACF,IAAI,CAAC,aAAa,CAChB,aAAa,EACb,MAAM,CAAC,SAAS,EAChB,cAAc,EACd,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAC7B,CAAC;YACF,IAAI,CAAC,aAAa,CAChB,aAAa,EACb,MAAM,CAAC,SAAS,EAChB,eAAe,EACf,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAC7B,CAAC;YACF,IAAI,CAAC,aAAa,CAChB,aAAa,EACb,MAAM,CAAC,SAAS,EAChB,KAAK,EACL,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAC7B,CAAC;YACF,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,MAAW,EAAE,aAAa,EAAE,EAAE;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,YAAY,QAAQ,wBAAwB,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CACrF,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACtC,OAAO,MAAM,CAAC;QAChB,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,aAAsB;QAC/C,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,SAAS,CAAC,QAAmB;YAC3C,OAAO,SAAS,sBAAsB,CAAY,KAAU;;gBAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBAElC,MAAM,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACnD,4EAA4E;gBAC5E,mDAAmD;gBACnD,MAAM,SAAS,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAC;gBAChC,MAAM,IAAI,GACR,CAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,0CAAE,QAAQ,MAAI,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,0CAAE,QAAQ,CAAA,CAAC;gBAC/D,MAAM,MAAM,GACV,eAAe,CAAC,OACjB,CAAC,cAAe,CAAC;gBAElB,MAAM,UAAU,GAAuB;oBACrC,cAAc,EAAE,aAAa;oBAC7B,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;oBAC9D,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAAE,KAAK;oBACxC,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAAE,SAAS;oBAC5C,CAAC,yCAAkB,CAAC,OAAO,CAAC,EAAE,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,0CAAE,IAAI;oBACtD,CAAC,yCAAkB,CAAC,OAAO,CAAC,EAAE,IAAI;oBAClC,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,0CAAE,IAAI;oBAC5D,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,0CAAE,IAAI;oBAC5D,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAChC,CAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,0CAAE,QAAQ,MAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;iBACrE,CAAC;gBACF,IAAI,MAAM,KAAK,CAAC,EAAE;oBAChB,UAAU,CAAC,yCAAkB,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,WAAW,CAC7D,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,EACV,MAAM,CACP,CAAC;iBACH;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;gBAC3C,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAC3C,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,EACrC;oBACE,UAAU;iBACX,EACD,MAAM,CACP,CAAC;gBACF,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;gBAElE,OAAO,GAAG,CAAC,OAAO;qBACf,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;qBAC/C,IAAI,CAAC,CAAC,MAAe,EAAE,EAAE;oBACxB,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,OAAO,MAAM,CAAC;gBAChB,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE;oBAClB,mEAAmE;oBACnE,2DAA2D;oBAC3D,MAAM,SAAS,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;oBAC7D,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK,EAAE,EAAE,CAAC,CAAC;oBAC3D,MAAM,WAAW,GAAG,KAAK,CAAC,wBAAwB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;oBACjE,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;oBAClC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;oBAC5D,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,MAAM,GAAG,CAAC;gBACZ,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,QAAkB;QACrC,OAAO,SAAS,sBAAsB;YACpC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAChD,mFAAmF;YACnF,sEAAsE;YACtE,4EAA4E;YAC5E,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,EAAE;gBAC5C,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;aAC5B,CAAC,CAAC;YACH,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC;IACJ,CAAC;IAED,aAAa,CACX,aAAiC,EACjC,GAAQ,EACR,UAAkB,EAClB,OAA+B;QAE/B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,YAAY,UAAU,cAAc,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CAC7E,CAAC;QACF,IAAI,IAAA,2BAAS,EAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;SAC/B;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;CACF;AA1LD,kDA0LC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { VERSION } from './version';\nimport * as constants from './constants';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n} from '@opentelemetry/instrumentation';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\nimport * as utils from './utils';\nimport * as types from './types';\n\nimport type * as knex from 'knex';\n\nconst contextSymbol = Symbol('opentelemetry.instrumentation-knex.context');\nconst DEFAULT_CONFIG: types.KnexInstrumentationConfig = {\n  maxQueryLength: 1022,\n};\n\nexport class KnexInstrumentation extends InstrumentationBase<any> {\n  constructor(config: types.KnexInstrumentationConfig = {}) {\n    super(\n      `@opentelemetry/instrumentation-${constants.MODULE_NAME}`,\n      VERSION,\n      Object.assign({}, DEFAULT_CONFIG, config)\n    );\n  }\n\n  init() {\n    const module = new InstrumentationNodeModuleDefinition<any>(\n      constants.MODULE_NAME,\n      constants.SUPPORTED_VERSIONS\n    );\n\n    module.files.push(\n      this.getClientNodeModuleFileInstrumentation('src'),\n      this.getClientNodeModuleFileInstrumentation('lib'),\n      this.getRunnerNodeModuleFileInstrumentation('src'),\n      this.getRunnerNodeModuleFileInstrumentation('lib'),\n      this.getRunnerNodeModuleFileInstrumentation('lib/execution')\n    );\n\n    return module;\n  }\n\n  private getRunnerNodeModuleFileInstrumentation(basePath: string) {\n    return new InstrumentationNodeModuleFile<typeof knex>(\n      `knex/${basePath}/runner.js`,\n      constants.SUPPORTED_VERSIONS,\n      (Runner: any, moduleVersion) => {\n        api.diag.debug(\n          `Applying ${basePath}/runner.js patch for ${constants.MODULE_NAME}@${moduleVersion}`\n        );\n        this.ensureWrapped(\n          moduleVersion,\n          Runner.prototype,\n          'query',\n          this.createQueryWrapper(moduleVersion)\n        );\n        return Runner;\n      },\n      (Runner: any, moduleVersion) => {\n        api.diag.debug(\n          `Removing ${basePath}/runner.js patch for ${constants.MODULE_NAME}@${moduleVersion}`\n        );\n        this._unwrap(Runner.prototype, 'query');\n        return Runner;\n      }\n    );\n  }\n\n  private getClientNodeModuleFileInstrumentation(basePath: string) {\n    return new InstrumentationNodeModuleFile<typeof knex>(\n      `knex/${basePath}/client.js`,\n      constants.SUPPORTED_VERSIONS,\n      (Client: any, moduleVersion) => {\n        api.diag.debug(\n          `Applying ${basePath}/client.js patch for ${constants.MODULE_NAME}@${moduleVersion}`\n        );\n        this.ensureWrapped(\n          moduleVersion,\n          Client.prototype,\n          'queryBuilder',\n          this.storeContext.bind(this)\n        );\n        this.ensureWrapped(\n          moduleVersion,\n          Client.prototype,\n          'schemaBuilder',\n          this.storeContext.bind(this)\n        );\n        this.ensureWrapped(\n          moduleVersion,\n          Client.prototype,\n          'raw',\n          this.storeContext.bind(this)\n        );\n        return Client;\n      },\n      (Client: any, moduleVersion) => {\n        api.diag.debug(\n          `Removing ${basePath}/client.js patch for ${constants.MODULE_NAME}@${moduleVersion}`\n        );\n        this._unwrap(Client.prototype, 'queryBuilder');\n        this._unwrap(Client.prototype, 'schemaBuilder');\n        this._unwrap(Client.prototype, 'raw');\n        return Client;\n      }\n    );\n  }\n\n  private createQueryWrapper(moduleVersion?: string) {\n    const instrumentation = this;\n    return function wrapQuery(original: () => any) {\n      return function wrapped_logging_method(this: any, query: any) {\n        const config = this.client.config;\n\n        const table = utils.extractTableName(this.builder);\n        // `method` actually refers to the knex API method - Not exactly \"operation\"\n        // in the spec sense, but matches most of the time.\n        const operation = query?.method;\n        const name =\n          config?.connection?.filename || config?.connection?.database;\n        const maxLen = (\n          instrumentation._config as types.KnexInstrumentationConfig\n        ).maxQueryLength!;\n\n        const attributes: api.SpanAttributes = {\n          'knex.version': moduleVersion,\n          [SemanticAttributes.DB_SYSTEM]: utils.mapSystem(config.client),\n          [SemanticAttributes.DB_SQL_TABLE]: table,\n          [SemanticAttributes.DB_OPERATION]: operation,\n          [SemanticAttributes.DB_USER]: config?.connection?.user,\n          [SemanticAttributes.DB_NAME]: name,\n          [SemanticAttributes.NET_PEER_NAME]: config?.connection?.host,\n          [SemanticAttributes.NET_PEER_PORT]: config?.connection?.port,\n          [SemanticAttributes.NET_TRANSPORT]:\n            config?.connection?.filename === ':memory:' ? 'inproc' : undefined,\n        };\n        if (maxLen !== 0) {\n          attributes[SemanticAttributes.DB_STATEMENT] = utils.limitLength(\n            query?.sql,\n            maxLen\n          );\n        }\n\n        const parent = this.builder[contextSymbol];\n        const span = instrumentation.tracer.startSpan(\n          utils.getName(name, operation, table),\n          {\n            attributes,\n          },\n          parent\n        );\n        const spanContext = api.trace.setSpan(api.context.active(), span);\n\n        return api.context\n          .with(spanContext, original, this, ...arguments)\n          .then((result: unknown) => {\n            span.end();\n            return result;\n          })\n          .catch((err: any) => {\n            // knex adds full query with all the binding values to the message,\n            // we want to undo that without changing the original error\n            const formatter = utils.getFormatter(this);\n            const fullQuery = formatter(query.sql, query.bindings || []);\n            const message = err.message.replace(fullQuery + ' - ', '');\n            const clonedError = utils.cloneErrorWithNewMessage(err, message);\n            span.recordException(clonedError);\n            span.setStatus({ code: api.SpanStatusCode.ERROR, message });\n            span.end();\n            throw err;\n          });\n      };\n    };\n  }\n\n  private storeContext(original: Function) {\n    return function wrapped_logging_method(this: any) {\n      const builder = original.apply(this, arguments);\n      // Builder is a custom promise type and when awaited it fails to propagate context.\n      // We store the parent context at the moment of initiating the builder\n      // otherwise we'd have nothing to attach the span as a child for in `query`.\n      Object.defineProperty(builder, contextSymbol, {\n        value: api.context.active(),\n      });\n      return builder;\n    };\n  }\n\n  ensureWrapped(\n    moduleVersion: string | undefined,\n    obj: any,\n    methodName: string,\n    wrapper: (original: any) => any\n  ) {\n    api.diag.debug(\n      `Applying ${methodName} patch for ${constants.MODULE_NAME}@${moduleVersion}`\n    );\n    if (isWrapped(obj[methodName])) {\n      this._unwrap(obj, methodName);\n    }\n    this._wrap(obj, methodName, wrapper);\n  }\n}\n"]}