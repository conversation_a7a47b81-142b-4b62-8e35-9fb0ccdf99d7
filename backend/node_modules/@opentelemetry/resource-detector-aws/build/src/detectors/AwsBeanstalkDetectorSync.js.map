{"version": 3, "file": "AwsBeanstalkDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsBeanstalkDetectorSync.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAmD;AACnD,8CAAsD;AAEtD,wDAMkC;AAClC,8EAG6C;AAC7C,wCAOoB;AACpB,yBAAyB;AACzB,6BAA6B;AAE7B;;;;;;;GAOG;AAEH,MAAM,2BAA2B,GAC/B,6CAA6C,CAAC;AAChD,MAAM,0BAA0B,GAC9B,mDAAmD,CAAC;AAEtD,MAAa,wBAAwB;IAKnC;QACE,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;YAChC,IAAI,CAAC,mBAAmB,GAAG,0BAA0B,CAAC;SACvD;aAAM;YACL,IAAI,CAAC,mBAAmB,GAAG,2BAA2B,CAAC;SACxD;IACH,CAAC;IAED,MAAM,CAAC,MAAgC;QACrC,MAAM,UAAU,GAAG,aAAO,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CACtE,IAAI,CAAC,cAAc,EAAE,CACtB,CAAC;QACF,OAAO,IAAI,oBAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,cAAc,CAClB,OAAiC;QAEjC,IAAI;YACF,MAAM,wBAAwB,CAAC,eAAe,CAC5C,IAAI,CAAC,mBAAmB,EACxB,EAAE,CAAC,SAAS,CAAC,IAAI,CAClB,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,wBAAwB,CAAC,aAAa,CAC1D,IAAI,CAAC,mBAAmB,EACxB,MAAM,CACP,CAAC;YACF,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEvC,OAAO;gBACL,CAAC,6BAAmB,CAAC,EAAE,kCAAwB;gBAC/C,CAAC,6BAAmB,CAAC,EAAE,oDAA0C;gBACjE,CAAC,wCAAiB,CAAC,EAAE,oDAA0C;gBAC/D,CAAC,gCAAsB,CAAC,EAAE,UAAU,CAAC,gBAAgB;gBACrD,CAAC,2CAAoB,CAAC,EAAE,UAAU,CAAC,aAAa;gBAChD,CAAC,kCAAwB,CAAC,EAAE,UAAU,CAAC,aAAa;aACrD,CAAC;SACH;QAAC,OAAO,CAAM,EAAE;YACf,UAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;SACX;IACH,CAAC;;AAvDH,4DAwDC;AAtDgB,sCAAa,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC5C,wCAAe,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAuDhD,QAAA,wBAAwB,GAAG,IAAI,wBAAwB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context, diag } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\n\nimport {\n  DetectorSync,\n  IResource,\n  Resource,\n  ResourceAttributes,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport {\n  ATTR_SERVICE_NAME,\n  ATTR_SERVICE_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport {\n  ATTR_CLOUD_PROVIDER,\n  ATTR_CLOUD_PLATFORM,\n  ATTR_SERVICE_NAMESPACE,\n  ATTR_SERVICE_INSTANCE_ID,\n  CLOUD_PROVIDER_VALUE_AWS,\n  CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK,\n} from '../semconv';\nimport * as fs from 'fs';\nimport * as util from 'util';\n\n/**\n * The AwsBeanstalkDetector can be used to detect if a process is running in AWS Elastic\n * Beanstalk and return a {@link Resource} populated with data about the beanstalk\n * plugins of AWS X-Ray. Returns an empty Resource if detection fails.\n *\n * See https://docs.amazonaws.cn/en_us/xray/latest/devguide/xray-guide.pdf\n * for more details about detecting information of Elastic Beanstalk plugins\n */\n\nconst DEFAULT_BEANSTALK_CONF_PATH =\n  '/var/elasticbeanstalk/xray/environment.conf';\nconst WIN_OS_BEANSTALK_CONF_PATH =\n  'C:\\\\Program Files\\\\Amazon\\\\XRay\\\\environment.conf';\n\nexport class AwsBeanstalkDetectorSync implements DetectorSync {\n  BEANSTALK_CONF_PATH: string;\n  private static readFileAsync = util.promisify(fs.readFile);\n  private static fileAccessAsync = util.promisify(fs.access);\n\n  constructor() {\n    if (process.platform === 'win32') {\n      this.BEANSTALK_CONF_PATH = WIN_OS_BEANSTALK_CONF_PATH;\n    } else {\n      this.BEANSTALK_CONF_PATH = DEFAULT_BEANSTALK_CONF_PATH;\n    }\n  }\n\n  detect(config?: ResourceDetectionConfig): IResource {\n    const attributes = context.with(suppressTracing(context.active()), () =>\n      this._getAttributes()\n    );\n    return new Resource({}, attributes);\n  }\n\n  /**\n   * Attempts to obtain AWS Beanstalk configuration from the file\n   * system. If file is accesible and read succesfully it returns\n   * a promise containing a {@link ResourceAttributes}\n   * object with instance metadata. Returns a promise containing an\n   * empty {@link ResourceAttributes} if the file is not accesible or\n   * fails in the reading process.\n   */\n  async _getAttributes(\n    _config?: ResourceDetectionConfig\n  ): Promise<ResourceAttributes> {\n    try {\n      await AwsBeanstalkDetectorSync.fileAccessAsync(\n        this.BEANSTALK_CONF_PATH,\n        fs.constants.R_OK\n      );\n\n      const rawData = await AwsBeanstalkDetectorSync.readFileAsync(\n        this.BEANSTALK_CONF_PATH,\n        'utf8'\n      );\n      const parsedData = JSON.parse(rawData);\n\n      return {\n        [ATTR_CLOUD_PROVIDER]: CLOUD_PROVIDER_VALUE_AWS,\n        [ATTR_CLOUD_PLATFORM]: CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK,\n        [ATTR_SERVICE_NAME]: CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK,\n        [ATTR_SERVICE_NAMESPACE]: parsedData.environment_name,\n        [ATTR_SERVICE_VERSION]: parsedData.version_label,\n        [ATTR_SERVICE_INSTANCE_ID]: parsedData.deployment_id,\n      };\n    } catch (e: any) {\n      diag.debug(`AwsBeanstalkDetectorSync failed: ${e.message}`);\n      return {};\n    }\n  }\n}\n\nexport const awsBeanstalkDetectorSync = new AwsBeanstalkDetectorSync();\n"]}