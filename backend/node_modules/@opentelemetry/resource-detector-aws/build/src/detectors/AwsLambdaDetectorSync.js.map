{"version": 3, "file": "AwsLambdaDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsLambdaDetectorSync.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,wDAMkC;AAClC,wCAWoB;AAEpB;;;;GAIG;AACH,MAAa,qBAAqB;IAChC,MAAM,CAAC,OAAiC;QACtC,iDAAiD;QACjD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;QACnD,IAAI,CAAC,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,UAAU,CAAC,aAAa,CAAC,CAAA,EAAE;YAC5C,OAAO,oBAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;QAED,iFAAiF;QACjF,wGAAwG;QACxG,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;QACtC,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC;QAC1D,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;QAChE,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;QAE/D,8EAA8E;QAC9E,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;QAC3D,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;QAE7D,MAAM,UAAU,GAAuB;YACrC,CAAC,6BAAmB,CAAC,EAAE,kCAAwB;YAC/C,CAAC,6BAAmB,CAAC,EAAE,yCAA+B;YACtD,CAAC,2BAAiB,CAAC,EAAE,MAAM;YAC3B,CAAC,wBAAc,CAAC,EAAE,YAAY;YAC9B,CAAC,2BAAiB,CAAC,EAAE,eAAe;YACpC,CAAC,8BAAoB,CAAC,EAAE,QAAQ,CAAC,UAAW,CAAC,GAAG,IAAI,GAAG,IAAI;SAC5D,CAAC;QAEF,IAAI,YAAY,EAAE;YAChB,UAAU,CAAC,kCAAwB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;SACvD;QACD,IAAI,aAAa,EAAE;YACjB,UAAU,CAAC,4BAAkB,CAAC,GAAG,aAAa,CAAC;SAChD;QAED,OAAO,IAAI,oBAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;CACF;AArCD,sDAqCC;AAEY,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DetectorSync,\n  IResource,\n  Resource,\n  ResourceAttributes,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport {\n  ATTR_AWS_LOG_GROUP_NAMES,\n  ATTR_CLOUD_PLATFORM,\n  ATTR_CLOUD_PROVIDER,\n  ATTR_CLOUD_REGION,\n  ATTR_FAAS_INSTANCE,\n  ATTR_FAAS_NAME,\n  ATTR_FAAS_MAX_MEMORY,\n  ATTR_FAAS_VERSION,\n  CLOUD_PROVIDER_VALUE_AWS,\n  CLOUD_PLATFORM_VALUE_AWS_LAMBDA,\n} from '../semconv';\n\n/**\n * The AwsLambdaDetector can be used to detect if a process is running in AWS Lambda\n * and return a {@link Resource} populated with data about the environment.\n * Returns an empty Resource if detection fails.\n */\nexport class AwsLambdaDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): IResource {\n    // Check if running inside AWS Lambda environment\n    const executionEnv = process.env.AWS_EXECUTION_ENV;\n    if (!executionEnv?.startsWith('AWS_Lambda_')) {\n      return Resource.empty();\n    }\n\n    // These environment variables are guaranteed to be present in Lambda environment\n    // https://docs.aws.amazon.com/lambda/latest/dg/configuration-envvars.html#configuration-envvars-runtime\n    const region = process.env.AWS_REGION;\n    const functionName = process.env.AWS_LAMBDA_FUNCTION_NAME;\n    const functionVersion = process.env.AWS_LAMBDA_FUNCTION_VERSION;\n    const memorySize = process.env.AWS_LAMBDA_FUNCTION_MEMORY_SIZE;\n\n    // These environment variables are not available in Lambda SnapStart functions\n    const logGroupName = process.env.AWS_LAMBDA_LOG_GROUP_NAME;\n    const logStreamName = process.env.AWS_LAMBDA_LOG_STREAM_NAME;\n\n    const attributes: ResourceAttributes = {\n      [ATTR_CLOUD_PROVIDER]: CLOUD_PROVIDER_VALUE_AWS,\n      [ATTR_CLOUD_PLATFORM]: CLOUD_PLATFORM_VALUE_AWS_LAMBDA,\n      [ATTR_CLOUD_REGION]: region,\n      [ATTR_FAAS_NAME]: functionName,\n      [ATTR_FAAS_VERSION]: functionVersion,\n      [ATTR_FAAS_MAX_MEMORY]: parseInt(memorySize!) * 1024 * 1024,\n    };\n\n    if (logGroupName) {\n      attributes[ATTR_AWS_LOG_GROUP_NAMES] = [logGroupName];\n    }\n    if (logStreamName) {\n      attributes[ATTR_FAAS_INSTANCE] = logStreamName;\n    }\n\n    return new Resource(attributes);\n  }\n}\n\nexport const awsLambdaDetectorSync = new AwsLambdaDetectorSync();\n"]}