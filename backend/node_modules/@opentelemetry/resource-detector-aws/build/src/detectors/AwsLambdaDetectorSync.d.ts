import { DetectorSync, IResource, ResourceDetectionConfig } from '@opentelemetry/resources';
/**
 * The AwsLambdaDetector can be used to detect if a process is running in AWS Lambda
 * and return a {@link Resource} populated with data about the environment.
 * Returns an empty Resource if detection fails.
 */
export declare class AwsLambdaDetectorSync implements DetectorSync {
    detect(_config?: ResourceDetectionConfig): IResource;
}
export declare const awsLambdaDetectorSync: AwsLambdaDetectorSync;
//# sourceMappingURL=AwsLambdaDetectorSync.d.ts.map