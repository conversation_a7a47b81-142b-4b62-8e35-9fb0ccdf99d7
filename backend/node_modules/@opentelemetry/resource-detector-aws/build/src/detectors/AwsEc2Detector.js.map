{"version": 3, "file": "AwsEc2Detector.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsEc2Detector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,6DAA0D;AAE1D;;;;;;GAMG;AACH,MAAM,cAAc;IAClB,MAAM,CAAC,OAAiC;QACtC,OAAO,OAAO,CAAC,OAAO,CAAC,uCAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7D,CAAC;CACF;AAEY,QAAA,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Detector,\n  IResource,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\n\nimport { awsEc2DetectorSync } from './AwsEc2DetectorSync';\n\n/**\n * The AwsEc2Detector can be used to detect if a process is running in AWS EC2\n * and return a {@link Resource} populated with metadata about the EC2\n * instance.\n *\n * @deprecated Use {@link AwsEc2DetectorSync} class instead.\n */\nclass AwsEc2Detector implements Detector {\n  detect(_config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(awsEc2DetectorSync.detect(_config));\n  }\n}\n\nexport const awsEc2Detector = new AwsEc2Detector();\n"]}