{"version": 3, "file": "AwsEc2DetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsEc2DetectorSync.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA6C;AAC7C,8CAAsD;AACtD,wDAMkC;AAClC,wCAWoB;AACpB,6BAA6B;AAE7B;;;;GAIG;AACH,MAAM,kBAAkB;IAAxB;QACE;;;;WAIG;QACM,sBAAiB,GAAG,iBAAiB,CAAC;QACtC,qCAAgC,GAAG,mBAAmB,CAAC;QACvD,wCAAmC,GAC1C,4CAA4C,CAAC;QACtC,oCAA+B,GAAG,4BAA4B,CAAC;QAC/D,4BAAuB,GAAG,sCAAsC,CAAC;QACjE,8BAAyB,GAAG,0BAA0B,CAAC;QACvD,yBAAoB,GAAG,IAAI,CAAC;IAsHvC,CAAC;IApHC,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAG,aAAO,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CACtE,IAAI,CAAC,cAAc,EAAE,CACtB,CAAC;QACF,OAAO,IAAI,oBAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI;YACF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,gBAAgB,EAAE,GACrE,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAE9C,OAAO;gBACL,CAAC,6BAAmB,CAAC,EAAE,kCAAwB;gBAC/C,CAAC,6BAAmB,CAAC,EAAE,sCAA4B;gBACnD,CAAC,+BAAqB,CAAC,EAAE,SAAS;gBAClC,CAAC,2BAAiB,CAAC,EAAE,MAAM;gBAC3B,CAAC,sCAA4B,CAAC,EAAE,gBAAgB;gBAChD,CAAC,sBAAY,CAAC,EAAE,UAAU;gBAC1B,CAAC,wBAAc,CAAC,EAAE,YAAY;gBAC9B,CAAC,wBAAc,CAAC,EAAE,QAAQ;aAC3B,CAAC;SACH;QAAC,WAAM;YACN,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,IAAI,CAAC,iBAAiB;YAC5B,IAAI,EAAE,IAAI,CAAC,gCAAgC;YAC3C,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,IAAI,CAAC,oBAAoB;YAClC,OAAO,EAAE;gBACP,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE,IAAI;aACrC;SACF,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,KAAa;QACxC,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,IAAI,CAAC,iBAAiB;YAC5B,IAAI,EAAE,IAAI,CAAC,mCAAmC;YAC9C,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,IAAI,CAAC,oBAAoB;YAClC,OAAO,EAAE;gBACP,CAAC,IAAI,CAAC,yBAAyB,CAAC,EAAE,KAAK;aACxC;SACF,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,KAAa;QACpC,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,IAAI,CAAC,iBAAiB;YAC5B,IAAI,EAAE,IAAI,CAAC,+BAA+B;YAC1C,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,IAAI,CAAC,oBAAoB;YAClC,OAAO,EAAE;gBACP,CAAC,IAAI,CAAC,yBAAyB,CAAC,EAAE,KAAK;aACxC;SACF,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,YAAY,CAAC,OAA4B;QACrD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,GAAG,CAAC,KAAK,EAAE,CAAC;gBACZ,MAAM,CAAC,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC,CAAC;YAC3D,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAE9B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;gBACtC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;gBAC3B,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;gBACxB,IAAI,OAAO,GAAG,EAAE,CAAC;gBACjB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC;gBAC5C,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACjB,IAAI,UAAU,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE;wBACvD,IAAI;4BACF,OAAO,CAAC,OAAO,CAAC,CAAC;yBAClB;wBAAC,OAAO,CAAC,EAAE;4BACV,MAAM,CAAC,CAAC,CAAC,CAAC;yBACX;qBACF;yBAAM;wBACL,MAAM,CACJ,IAAI,KAAK,CAAC,oCAAoC,GAAG,UAAU,CAAC,CAC7D,CAAC;qBACH;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;gBACpB,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,GAAG,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAEY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport {\n  DetectorSync,\n  IResource,\n  Resource,\n  ResourceAttributes,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport {\n  ATTR_CLOUD_PROVIDER,\n  ATTR_CLOUD_PLATFORM,\n  ATTR_CLOUD_REGION,\n  ATTR_CLOUD_ACCOUNT_ID,\n  ATTR_CLOUD_AVAILABILITY_ZONE,\n  ATTR_HOST_ID,\n  ATTR_HOST_TYPE,\n  ATTR_HOST_NAME,\n  CLOUD_PROVIDER_VALUE_AWS,\n  CLOUD_PLATFORM_VALUE_AWS_EC2,\n} from '../semconv';\nimport * as http from 'http';\n\n/**\n * The AwsEc2DetectorSync can be used to detect if a process is running in AWS EC2\n * and return a {@link Resource} populated with metadata about the EC2\n * instance. Returns an empty Resource if detection fails.\n */\nclass AwsEc2DetectorSync implements DetectorSync {\n  /**\n   * See https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instance-identity-documents.html\n   * for documentation about the AWS instance identity document\n   * and standard of IMDSv2.\n   */\n  readonly AWS_IDMS_ENDPOINT = '***************';\n  readonly AWS_INSTANCE_TOKEN_DOCUMENT_PATH = '/latest/api/token';\n  readonly AWS_INSTANCE_IDENTITY_DOCUMENT_PATH =\n    '/latest/dynamic/instance-identity/document';\n  readonly AWS_INSTANCE_HOST_DOCUMENT_PATH = '/latest/meta-data/hostname';\n  readonly AWS_METADATA_TTL_HEADER = 'X-aws-ec2-metadata-token-ttl-seconds';\n  readonly AWS_METADATA_TOKEN_HEADER = 'X-aws-ec2-metadata-token';\n  readonly MILLISECOND_TIME_OUT = 5000;\n\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes = context.with(suppressTracing(context.active()), () =>\n      this._getAttributes()\n    );\n    return new Resource({}, attributes);\n  }\n\n  /**\n   * Attempts to connect and obtain an AWS instance Identity document. If the\n   * connection is successful it returns a promise containing a {@link ResourceAttributes}\n   * object with instance metadata. Returns a promise containing an\n   * empty {@link ResourceAttributes} if the connection or parsing of the identity\n   * document fails.\n   */\n  async _getAttributes(): Promise<ResourceAttributes> {\n    try {\n      const token = await this._fetchToken();\n      const { accountId, instanceId, instanceType, region, availabilityZone } =\n        await this._fetchIdentity(token);\n      const hostname = await this._fetchHost(token);\n\n      return {\n        [ATTR_CLOUD_PROVIDER]: CLOUD_PROVIDER_VALUE_AWS,\n        [ATTR_CLOUD_PLATFORM]: CLOUD_PLATFORM_VALUE_AWS_EC2,\n        [ATTR_CLOUD_ACCOUNT_ID]: accountId,\n        [ATTR_CLOUD_REGION]: region,\n        [ATTR_CLOUD_AVAILABILITY_ZONE]: availabilityZone,\n        [ATTR_HOST_ID]: instanceId,\n        [ATTR_HOST_TYPE]: instanceType,\n        [ATTR_HOST_NAME]: hostname,\n      };\n    } catch {\n      return {};\n    }\n  }\n\n  private async _fetchToken(): Promise<string> {\n    const options = {\n      host: this.AWS_IDMS_ENDPOINT,\n      path: this.AWS_INSTANCE_TOKEN_DOCUMENT_PATH,\n      method: 'PUT',\n      timeout: this.MILLISECOND_TIME_OUT,\n      headers: {\n        [this.AWS_METADATA_TTL_HEADER]: '60',\n      },\n    };\n    return await this._fetchString(options);\n  }\n\n  private async _fetchIdentity(token: string): Promise<any> {\n    const options = {\n      host: this.AWS_IDMS_ENDPOINT,\n      path: this.AWS_INSTANCE_IDENTITY_DOCUMENT_PATH,\n      method: 'GET',\n      timeout: this.MILLISECOND_TIME_OUT,\n      headers: {\n        [this.AWS_METADATA_TOKEN_HEADER]: token,\n      },\n    };\n    const identity = await this._fetchString(options);\n    return JSON.parse(identity);\n  }\n\n  private async _fetchHost(token: string): Promise<string> {\n    const options = {\n      host: this.AWS_IDMS_ENDPOINT,\n      path: this.AWS_INSTANCE_HOST_DOCUMENT_PATH,\n      method: 'GET',\n      timeout: this.MILLISECOND_TIME_OUT,\n      headers: {\n        [this.AWS_METADATA_TOKEN_HEADER]: token,\n      },\n    };\n    return await this._fetchString(options);\n  }\n\n  /**\n   * Establishes an HTTP connection to AWS instance document url.\n   * If the application is running on an EC2 instance, we should be able\n   * to get back a valid JSON document. Parses that document and stores\n   * the identity properties in a local map.\n   */\n  private async _fetchString(options: http.RequestOptions): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const timeoutId = setTimeout(() => {\n        req.abort();\n        reject(new Error('EC2 metadata api request timed out.'));\n      }, this.MILLISECOND_TIME_OUT);\n\n      const req = http.request(options, res => {\n        clearTimeout(timeoutId);\n        const { statusCode } = res;\n        res.setEncoding('utf8');\n        let rawData = '';\n        res.on('data', chunk => (rawData += chunk));\n        res.on('end', () => {\n          if (statusCode && statusCode >= 200 && statusCode < 300) {\n            try {\n              resolve(rawData);\n            } catch (e) {\n              reject(e);\n            }\n          } else {\n            reject(\n              new Error('Failed to load page, status code: ' + statusCode)\n            );\n          }\n        });\n      });\n      req.on('error', err => {\n        clearTimeout(timeoutId);\n        reject(err);\n      });\n      req.end();\n    });\n  }\n}\n\nexport const awsEc2DetectorSync = new AwsEc2DetectorSync();\n"]}