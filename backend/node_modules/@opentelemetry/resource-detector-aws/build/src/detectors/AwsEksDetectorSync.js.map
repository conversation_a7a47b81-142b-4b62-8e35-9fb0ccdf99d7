{"version": 3, "file": "AwsEksDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsEksDetectorSync.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA6C;AAC7C,8CAAsD;AACtD,wDAMkC;AAClC,wCAOoB;AACpB,+BAA+B;AAC/B,yBAAyB;AACzB,6BAA6B;AAC7B,4CAA0C;AAE1C;;;;;;;GAOG;AAEH,MAAa,kBAAkB;IAA/B;QACW,gBAAW,GAAG,wBAAwB,CAAC;QACvC,mBAAc,GACrB,qDAAqD,CAAC;QAC/C,kBAAa,GACpB,sDAAsD,CAAC;QAChD,wBAAmB,GAC1B,oDAAoD,CAAC;QAC9C,sBAAiB,GACxB,8DAA8D,CAAC;QACxD,wBAAmB,GAAG,EAAE,CAAC;QACzB,wBAAmB,GAAG,mBAAmB,CAAC;QAC1C,eAAU,GAAG,IAAI,CAAC;QAClB,iBAAY,GAAG,MAAM,CAAC;IAqLjC,CAAC;IAhLC,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAG,aAAO,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CACtE,IAAI,CAAC,cAAc,EAAE,CACtB,CAAC;QACF,OAAO,IAAI,oBAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI;YACF,MAAM,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9D,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,aAAa,CACpD,IAAI,CAAC,aAAa,CACnB,CAAC;YAEF,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE;gBACjC,OAAO,EAAE,CAAC;aACX;YAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YACjD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAExD,OAAO,CAAC,WAAW,IAAI,CAAC,WAAW;gBACjC,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC;oBACE,CAAC,6BAAmB,CAAC,EAAE,kCAAwB;oBAC/C,CAAC,6BAAmB,CAAC,EAAE,sCAA4B;oBACnD,CAAC,+BAAqB,CAAC,EAAE,WAAW,IAAI,EAAE;oBAC1C,CAAC,2BAAiB,CAAC,EAAE,WAAW,IAAI,EAAE;iBACvC,CAAC;SACP;QAAC,OAAO,CAAC,EAAE;YACV,UAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC,CAAC,CAAC;YAC/C,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,MAAM,CAAC,IAAY;QAC/B,MAAM,OAAO,GAAG;YACd,EAAE,EAAE,IAAI;YACR,OAAO,EAAE;gBACP,aAAa,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;aAC9C;YACD,QAAQ,EAAE,IAAI,CAAC,WAAW;YAC1B,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,IAAI,CAAC,mBAAmB;YAC9B,OAAO,EAAE,IAAI,CAAC,UAAU;SACzB,CAAC;QACF,OAAO,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,eAAe,CAAC,IAAY;QACxC,MAAM,OAAO,GAAG;YACd,EAAE,EAAE,IAAI;YACR,OAAO,EAAE;gBACP,aAAa,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;aAC9C;YACD,IAAI,EAAE,IAAI,CAAC,WAAW;YACtB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,IAAI,CAAC,iBAAiB;YAC5B,OAAO,EAAE,IAAI,CAAC,UAAU;SACzB,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI;YACF,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAClD;QAAC,OAAO,CAAC,EAAE;YACV,UAAI,CAAC,KAAK,CAAC,gCAAgC,EAAE,CAAC,CAAC,CAAC;SACjD;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IACD;;;OAGG;IACK,KAAK,CAAC,iBAAiB;QAC7B,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,aAAa,CACpD,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,YAAY,CAClB,CAAC;YACF,OAAO,SAAS,GAAG,OAAO,CAAC;SAC5B;QAAC,OAAO,CAAC,EAAE;YACV,UAAI,CAAC,KAAK,CAAC,yCAAyC,EAAE,CAAC,CAAC,CAAC;SAC1D;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACK,KAAK,CAAC,eAAe;QAC3B,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,aAAa,CACpD,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,YAAY,CAClB,CAAC;YACF,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7C,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;gBAC3B,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE;oBACzC,OAAO,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;iBAC7D;aACF;SACF;QAAC,OAAO,CAAM,EAAE;YACf,UAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;SACxE;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,YAAY,CAAC,OAA6B;QACtD,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,GAAG,CAAC,KAAK,EAAE,CAAC;gBACZ,MAAM,CAAC,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC,CAAC;YAC3D,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;gBACvC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;gBAC3B,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACnC,IAAI,OAAO,GAAG,EAAE,CAAC;gBACjB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC;gBAC5C,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACjB,IAAI,UAAU,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE;wBACvD,IAAI;4BACF,OAAO,CAAC,OAAO,CAAC,CAAC;yBAClB;wBAAC,OAAO,CAAC,EAAE;4BACV,MAAM,CAAC,CAAC,CAAC,CAAC;yBACX;qBACF;yBAAM;wBACL,MAAM,CACJ,IAAI,KAAK,CAAC,oCAAoC,GAAG,UAAU,CAAC,CAC7D,CAAC;qBACH;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;gBACpB,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,GAAG,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;;AAjMH,gDAkMC;AAnLgB,gCAAa,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC5C,kCAAe,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAoLhD,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport {\n  DetectorSync,\n  IResource,\n  Resource,\n  ResourceAttributes,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport {\n  ATTR_CLOUD_PROVIDER,\n  ATTR_CLOUD_PLATFORM,\n  ATTR_K8S_CLUSTER_NAME,\n  ATTR_CONTAINER_ID,\n  CLOUD_PROVIDER_VALUE_AWS,\n  CLOUD_PLATFORM_VALUE_AWS_EKS,\n} from '../semconv';\nimport * as https from 'https';\nimport * as fs from 'fs';\nimport * as util from 'util';\nimport { diag } from '@opentelemetry/api';\n\n/**\n * The AwsEksDetectorSync can be used to detect if a process is running in AWS Elastic\n * Kubernetes and return a {@link Resource} populated with data about the Kubernetes\n * plugins of AWS X-Ray. Returns an empty Resource if detection fails.\n *\n * See https://docs.amazonaws.cn/en_us/xray/latest/devguide/xray-guide.pdf\n * for more details about detecting information for Elastic Kubernetes plugins\n */\n\nexport class AwsEksDetectorSync implements DetectorSync {\n  readonly K8S_SVC_URL = 'kubernetes.default.svc';\n  readonly K8S_TOKEN_PATH =\n    '/var/run/secrets/kubernetes.io/serviceaccount/token';\n  readonly K8S_CERT_PATH =\n    '/var/run/secrets/kubernetes.io/serviceaccount/ca.crt';\n  readonly AUTH_CONFIGMAP_PATH =\n    '/api/v1/namespaces/kube-system/configmaps/aws-auth';\n  readonly CW_CONFIGMAP_PATH =\n    '/api/v1/namespaces/amazon-cloudwatch/configmaps/cluster-info';\n  readonly CONTAINER_ID_LENGTH = 64;\n  readonly DEFAULT_CGROUP_PATH = '/proc/self/cgroup';\n  readonly TIMEOUT_MS = 2000;\n  readonly UTF8_UNICODE = 'utf8';\n\n  private static readFileAsync = util.promisify(fs.readFile);\n  private static fileAccessAsync = util.promisify(fs.access);\n\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes = context.with(suppressTracing(context.active()), () =>\n      this._getAttributes()\n    );\n    return new Resource({}, attributes);\n  }\n\n  /**\n   * The AwsEksDetector can be used to detect if a process is running on Amazon\n   * Elastic Kubernetes and returns a promise containing a {@link ResourceAttributes}\n   * object with instance metadata. Returns a promise containing an\n   * empty {@link ResourceAttributes} if the connection to kubernetes process\n   * or aws config maps fails\n   */\n  private async _getAttributes(): Promise<ResourceAttributes> {\n    try {\n      await AwsEksDetectorSync.fileAccessAsync(this.K8S_TOKEN_PATH);\n      const k8scert = await AwsEksDetectorSync.readFileAsync(\n        this.K8S_CERT_PATH\n      );\n\n      if (!(await this._isEks(k8scert))) {\n        return {};\n      }\n\n      const containerId = await this._getContainerId();\n      const clusterName = await this._getClusterName(k8scert);\n\n      return !containerId && !clusterName\n        ? {}\n        : {\n            [ATTR_CLOUD_PROVIDER]: CLOUD_PROVIDER_VALUE_AWS,\n            [ATTR_CLOUD_PLATFORM]: CLOUD_PLATFORM_VALUE_AWS_EKS,\n            [ATTR_K8S_CLUSTER_NAME]: clusterName || '',\n            [ATTR_CONTAINER_ID]: containerId || '',\n          };\n    } catch (e) {\n      diag.debug('Process is not running on K8S', e);\n      return {};\n    }\n  }\n\n  /**\n   * Attempts to make a connection to AWS Config map which will\n   * determine whether the process is running on an EKS\n   * process if the config map is empty or not\n   */\n  private async _isEks(cert: Buffer): Promise<boolean> {\n    const options = {\n      ca: cert,\n      headers: {\n        Authorization: await this._getK8sCredHeader(),\n      },\n      hostname: this.K8S_SVC_URL,\n      method: 'GET',\n      path: this.AUTH_CONFIGMAP_PATH,\n      timeout: this.TIMEOUT_MS,\n    };\n    return !!(await this._fetchString(options));\n  }\n\n  /**\n   * Attempts to make a connection to Amazon Cloudwatch\n   * Config Maps to grab cluster name\n   */\n  private async _getClusterName(cert: Buffer): Promise<string | undefined> {\n    const options = {\n      ca: cert,\n      headers: {\n        Authorization: await this._getK8sCredHeader(),\n      },\n      host: this.K8S_SVC_URL,\n      method: 'GET',\n      path: this.CW_CONFIGMAP_PATH,\n      timeout: this.TIMEOUT_MS,\n    };\n    const response = await this._fetchString(options);\n    try {\n      return JSON.parse(response).data['cluster.name'];\n    } catch (e) {\n      diag.debug('Cannot get cluster name on EKS', e);\n    }\n    return '';\n  }\n  /**\n   * Reads the Kubernetes token path and returns kubernetes\n   * credential header\n   */\n  private async _getK8sCredHeader(): Promise<string> {\n    try {\n      const content = await AwsEksDetectorSync.readFileAsync(\n        this.K8S_TOKEN_PATH,\n        this.UTF8_UNICODE\n      );\n      return 'Bearer ' + content;\n    } catch (e) {\n      diag.debug('Unable to read Kubernetes client token.', e);\n    }\n    return '';\n  }\n\n  /**\n   * Read container ID from cgroup file generated from docker which lists the full\n   * untruncated docker container ID at the end of each line.\n   *\n   * The predefined structure of calling /proc/self/cgroup when in a docker container has the structure:\n   *\n   * #:xxxxxx:/\n   *\n   * or\n   *\n   * #:xxxxxx:/docker/64characterID\n   *\n   * This function takes advantage of that fact by just reading the 64-character ID from the end of the\n   * first line. In EKS, even if we fail to find target file or target file does\n   * not contain container ID we do not throw an error but throw warning message\n   * and then return null string\n   */\n  private async _getContainerId(): Promise<string | undefined> {\n    try {\n      const rawData = await AwsEksDetectorSync.readFileAsync(\n        this.DEFAULT_CGROUP_PATH,\n        this.UTF8_UNICODE\n      );\n      const splitData = rawData.trim().split('\\n');\n      for (const str of splitData) {\n        if (str.length > this.CONTAINER_ID_LENGTH) {\n          return str.substring(str.length - this.CONTAINER_ID_LENGTH);\n        }\n      }\n    } catch (e: any) {\n      diag.debug(`AwsEksDetector failed to read container ID: ${e.message}`);\n    }\n    return undefined;\n  }\n\n  /**\n   * Establishes an HTTP connection to AWS instance document url.\n   * If the application is running on an EKS instance, we should be able\n   * to get back a valid JSON document. Parses that document and stores\n   * the identity properties in a local map.\n   */\n  private async _fetchString(options: https.RequestOptions): Promise<string> {\n    return await new Promise((resolve, reject) => {\n      const timeoutId = setTimeout(() => {\n        req.abort();\n        reject(new Error('EKS metadata api request timed out.'));\n      }, 2000);\n\n      const req = https.request(options, res => {\n        clearTimeout(timeoutId);\n        const { statusCode } = res;\n        res.setEncoding(this.UTF8_UNICODE);\n        let rawData = '';\n        res.on('data', chunk => (rawData += chunk));\n        res.on('end', () => {\n          if (statusCode && statusCode >= 200 && statusCode < 300) {\n            try {\n              resolve(rawData);\n            } catch (e) {\n              reject(e);\n            }\n          } else {\n            reject(\n              new Error('Failed to load page, status code: ' + statusCode)\n            );\n          }\n        });\n      });\n      req.on('error', err => {\n        clearTimeout(timeoutId);\n        reject(err);\n      });\n      req.end();\n    });\n  }\n}\n\nexport const awsEksDetectorSync = new AwsEksDetectorSync();\n"]}