{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/detectors/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,+DAGgC;AAF9B,4HAAA,oBAAoB,OAAA;AACpB,4HAAA,oBAAoB,OAAA;AAEtB,uEAGoC;AAFlC,oIAAA,wBAAwB,OAAA;AACxB,oIAAA,wBAAwB,OAAA;AAE1B,mDAAkD;AAAzC,gHAAA,cAAc,OAAA;AACvB,2DAA0D;AAAjD,wHAAA,kBAAkB,OAAA;AAC3B,mDAAkE;AAAzD,gHAAA,cAAc,OAAA;AAAE,gHAAA,cAAc,OAAA;AACvC,2DAA8E;AAArE,wHAAA,kBAAkB,OAAA;AAAE,wHAAA,kBAAkB,OAAA;AAC/C,mDAAkE;AAAzD,gHAAA,cAAc,OAAA;AAAE,gHAAA,cAAc,OAAA;AACvC,2DAA8E;AAArE,wHAAA,kBAAkB,OAAA;AAAE,wHAAA,kBAAkB,OAAA;AAC/C,yDAA2E;AAAlE,sHAAA,iBAAiB,OAAA;AAAE,sHAAA,iBAAiB,OAAA;AAC7C,iEAGiC;AAF/B,8HAAA,qBAAqB,OAAA;AACrB,8HAAA,qBAAqB,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport {\n  AwsBeanstalkDetector,\n  awsBeanstalkDetector,\n} from './AwsBeanstalkDetector';\nexport {\n  AwsBeanstalkDetectorSync,\n  awsBeanstalkDetectorSync,\n} from './AwsBeanstalkDetectorSync';\nexport { awsEc2Detector } from './AwsEc2Detector';\nexport { awsEc2DetectorSync } from './AwsEc2DetectorSync';\nexport { AwsEcsDetector, awsEcsDetector } from './AwsEcsDetector';\nexport { AwsEcsDetectorSync, awsEcsDetectorSync } from './AwsEcsDetectorSync';\nexport { AwsEksDetector, awsEksDetector } from './AwsEksDetector';\nexport { AwsEksDetectorSync, awsEksDetectorSync } from './AwsEksDetectorSync';\nexport { AwsLambdaDetector, awsLambdaDetector } from './AwsLambdaDetector';\nexport {\n  AwsLambdaDetectorSync,\n  awsLambdaDetectorSync,\n} from './AwsLambdaDetectorSync';\n"]}