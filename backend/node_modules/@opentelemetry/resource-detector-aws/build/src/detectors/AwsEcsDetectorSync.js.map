{"version": 3, "file": "AwsEcsDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsEcsDetectorSync.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAmD;AACnD,8CAAsD;AACtD,wDAKkC;AAClC,wCAqBoB;AACpB,6BAA6B;AAC7B,6BAA6B;AAC7B,yBAAyB;AACzB,yBAAyB;AAEzB,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAQhC;;;;GAIG;AACH,MAAa,kBAAkB;IAM7B,MAAM;QACJ,MAAM,UAAU,GAAG,aAAO,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CACtE,IAAI,CAAC,cAAc,EAAE,CACtB,CAAC;QACF,OAAO,IAAI,oBAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,IACE,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B;YAC1C,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,EACvC;YACA,UAAI,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAC3D,OAAO,EAAE,CAAC;SACX;QAED,IAAI;YACF,IAAI,QAAQ,GAAG,IAAI,oBAAQ,CAAC;gBAC1B,CAAC,6BAAmB,CAAC,EAAE,kCAAwB;gBAC/C,CAAC,6BAAmB,CAAC,EAAE,sCAA4B;aACpD,CAAC,CAAC,KAAK,CAAC,MAAM,kBAAkB,CAAC,kCAAkC,EAAE,CAAC,CAAC;YAExE,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;YAC9D,IAAI,WAAW,EAAE;gBACf,MAAM,CAAC,iBAAiB,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;oBAC1D,kBAAkB,CAAC,aAAa,CAAC,WAAW,CAAC;oBAC7C,kBAAkB,CAAC,aAAa,CAAC,GAAG,WAAW,OAAO,CAAC;iBACxD,CAAC,CAAC;gBAEH,MAAM,kBAAkB,GACtB,MAAM,kBAAkB,CAAC,sBAAsB,CAC7C,iBAAiB,EACjB,YAAY,CACb,CAAC;gBACJ,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAAC,eAAe,CAC3D,iBAAiB,CAClB,CAAC;gBAEF,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;aACnE;YAED,OAAO,QAAQ,CAAC,UAAU,CAAC;SAC5B;QAAC,WAAM;YACN,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAED;;;;;;OAMG;IACK,MAAM,CAAC,KAAK,CAAC,kCAAkC;QACrD,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAE/B,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,aAAa,CACpD,kBAAkB,CAAC,mBAAmB,EACtC,MAAM,CACP,CAAC;YACF,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7C,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;gBAC3B,IAAI,GAAG,CAAC,MAAM,GAAG,kBAAkB,CAAC,mBAAmB,EAAE;oBACvD,WAAW,GAAG,GAAG,CAAC,SAAS,CACzB,GAAG,CAAC,MAAM,GAAG,kBAAkB,CAAC,mBAAmB,CACpD,CAAC;oBACF,MAAM;iBACP;aACF;SACF;QAAC,OAAO,CAAC,EAAE;YACV,UAAI,CAAC,KAAK,CAAC,4CAA4C,EAAE,CAAC,CAAC,CAAC;SAC7D;QAED,IAAI,QAAQ,IAAI,WAAW,EAAE;YAC3B,OAAO,IAAI,oBAAQ,CAAC;gBAClB,CAAC,6BAAmB,CAAC,EAAE,QAAQ,IAAI,EAAE;gBACrC,CAAC,2BAAiB,CAAC,EAAE,WAAW,IAAI,EAAE;aACvC,CAAC,CAAC;SACJ;QAED,OAAO,oBAAQ,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,sBAAsB,CACzC,iBAAsB,EACtB,YAAiB;QAEjB,MAAM,UAAU,GAAW,YAAY,CAAC,YAAY,CAAC,CAAC;QACtD,MAAM,OAAO,GAAW,YAAY,CAAC,SAAS,CAAC,CAAC;QAEhD,MAAM,OAAO,GAAW,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QACvE,MAAM,OAAO,GAAW,YAAY,CAAC,SAAS,CAAC,CAAC;QAEhD,MAAM,SAAS,GAAW,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACzE,MAAM,MAAM,GAAW,kBAAkB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACrE,MAAM,gBAAgB,GAAuB,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,gBAAgB,CAAC;QAE5E,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;YAC3C,CAAC,CAAC,OAAO;YACT,CAAC,CAAC,GAAG,OAAO,YAAY,OAAO,EAAE,CAAC;QAEpC,MAAM,YAAY,GAAW,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAE/D,6HAA6H;QAC7H,MAAM,UAAU,GAAuB;YACrC,CAAC,oCAA0B,CAAC,EAAE,YAAY;YAC1C,CAAC,kCAAwB,CAAC,EAAE,UAAU;YACtC,CAAC,iCAAuB,CAAC,EAAE,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,WAAW,EAAE;YACpD,CAAC,+BAAqB,CAAC,EAAE,OAAO;YAChC,CAAC,kCAAwB,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC;YAClD,CAAC,oCAA0B,CAAC,EAAE,YAAY,CAAC,UAAU,CAAC;YAEtD,CAAC,+BAAqB,CAAC,EAAE,SAAS;YAClC,CAAC,2BAAiB,CAAC,EAAE,MAAM;YAC3B,CAAC,gCAAsB,CAAC,EAAE,YAAY;SACvC,CAAC;QAEF,iEAAiE;QACjE,IAAI,gBAAgB,EAAE;YACpB,UAAU,CAAC,sCAA4B,CAAC,GAAG,gBAAgB,CAAC;SAC7D;QAED,OAAO,IAAI,oBAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,eAAe,CAClC,iBAAsB;QAEtB,IACE,iBAAiB,CAAC,WAAW,CAAC,KAAK,SAAS;YAC5C,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAChC;YACA,OAAO,oBAAQ,CAAC,KAAK,CAAC;SACvB;QAED,MAAM,YAAY,GAAG,iBAAiB,CAAC,cAAc,CAAE,CAAC;QACxD,MAAM,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAkB,CAAC;QAEpE,MAAM,UAAU,GACd,UAAU,CAAC,gBAAgB,CAAC;YAC5B,kBAAkB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAErD,MAAM,UAAU,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAEvE,MAAM,aAAa,GAAG,UAAU,CAAC,eAAe,CAAE,CAAC;QACnD,MAAM,YAAY,GAAG,gBAAgB,UAAU,IAAI,UAAU,cAAc,aAAa,EAAE,CAAC;QAC3F,MAAM,cAAc,GAAG,UAAU,CAAC,gBAAgB,CAAE,CAAC;QACrD,MAAM,aAAa,GAAG,gBAAgB,UAAU,IAAI,UAAU,cAAc,aAAa,eAAe,cAAc,EAAE,CAAC;QAEzH,OAAO,IAAI,oBAAQ,CAAC;YAClB,CAAC,kCAAwB,CAAC,EAAE,CAAC,aAAa,CAAC;YAC3C,CAAC,iCAAuB,CAAC,EAAE,CAAC,YAAY,CAAC;YACzC,CAAC,mCAAyB,CAAC,EAAE,CAAC,cAAc,CAAC;YAC7C,CAAC,kCAAwB,CAAC,EAAE,CAAC,aAAa,CAAC;SAC5C,CAAC,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,YAAoB;QACpD,MAAM,KAAK,GAAG,8BAA8B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAChE,OAAO,KAAM,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,YAAoB;QACnD,MAAM,KAAK,GAAG,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1D,OAAO,KAAM,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,GAAW;QACtC,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,QAA8B,EAAE,EAAE;gBAC/D,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE;oBACrD,MAAM,CACJ,IAAI,KAAK,CACP,eAAe,GAAG,wBAAwB,QAAQ,CAAC,UAAU,EAAE,CAChE,CACF,CAAC;iBACH;gBACD;;;mBAGG;gBACH,IAAI,YAAY,GAAG,EAAE,CAAC;gBACtB,QAAQ,CAAC,EAAE,CACT,MAAM,EACN,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,YAAY,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,CACtD,CAAC;gBACF,kDAAkD;gBAClD,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;gBAChD;;;;mBAIG;gBACH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,gDAAgD;YAChD,OAAO,CAAC,UAAU,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC1C,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC,CAAC,CAAC;YACH,wCAAwC;YACxC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC5B,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;IAC1D,CAAC;;AArNH,gDAsNC;AArNiB,sCAAmB,GAAG,EAAE,CAAC;AACzB,sCAAmB,GAAG,mBAAmB,CAAC;AAE3C,gCAAa,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AAoNhD,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context, diag } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport {\n  DetectorSync,\n  IResource,\n  Resource,\n  ResourceAttributes,\n} from '@opentelemetry/resources';\nimport {\n  ATTR_AWS_ECS_CLUSTER_ARN,\n  ATTR_AWS_ECS_CONTAINER_ARN,\n  ATTR_AWS_ECS_LAUNCHTYPE,\n  ATTR_AWS_ECS_TASK_ARN,\n  ATTR_AWS_ECS_TASK_FAMILY,\n  ATTR_AWS_ECS_TASK_REVISION,\n  ATTR_AWS_LOG_GROUP_ARNS,\n  ATTR_AWS_LOG_GROUP_NAMES,\n  ATTR_AWS_LOG_STREAM_ARNS,\n  ATTR_AWS_LOG_STREAM_NAMES,\n  ATTR_CLOUD_ACCOUNT_ID,\n  ATTR_CLOUD_AVAILABILITY_ZONE,\n  ATTR_CLOUD_PLATFORM,\n  ATTR_CLOUD_PROVIDER,\n  ATTR_CLOUD_REGION,\n  ATTR_CLOUD_RESOURCE_ID,\n  ATTR_CONTAINER_ID,\n  ATTR_CONTAINER_NAME,\n  CLOUD_PROVIDER_VALUE_AWS,\n  CLOUD_PLATFORM_VALUE_AWS_ECS,\n} from '../semconv';\nimport * as http from 'http';\nimport * as util from 'util';\nimport * as fs from 'fs';\nimport * as os from 'os';\n\nconst HTTP_TIMEOUT_IN_MS = 1000;\n\ninterface AwsLogOptions {\n  readonly 'awslogs-region'?: string;\n  readonly 'awslogs-group'?: string;\n  readonly 'awslogs-stream'?: string;\n}\n\n/**\n * The AwsEcsDetector can be used to detect if a process is running in AWS\n * ECS and return a {@link Resource} populated with data about the ECS\n * plugins of AWS X-Ray. Returns an empty Resource if detection fails.\n */\nexport class AwsEcsDetectorSync implements DetectorSync {\n  static readonly CONTAINER_ID_LENGTH = 64;\n  static readonly DEFAULT_CGROUP_PATH = '/proc/self/cgroup';\n\n  private static readFileAsync = util.promisify(fs.readFile);\n\n  detect(): IResource {\n    const attributes = context.with(suppressTracing(context.active()), () =>\n      this._getAttributes()\n    );\n    return new Resource({}, attributes);\n  }\n\n  private async _getAttributes(): Promise<ResourceAttributes> {\n    if (\n      !process.env.ECS_CONTAINER_METADATA_URI_V4 &&\n      !process.env.ECS_CONTAINER_METADATA_URI\n    ) {\n      diag.debug('AwsEcsDetector failed: Process is not on ECS');\n      return {};\n    }\n\n    try {\n      let resource = new Resource({\n        [ATTR_CLOUD_PROVIDER]: CLOUD_PROVIDER_VALUE_AWS,\n        [ATTR_CLOUD_PLATFORM]: CLOUD_PLATFORM_VALUE_AWS_ECS,\n      }).merge(await AwsEcsDetectorSync._getContainerIdAndHostnameResource());\n\n      const metadataUrl = process.env.ECS_CONTAINER_METADATA_URI_V4;\n      if (metadataUrl) {\n        const [containerMetadata, taskMetadata] = await Promise.all([\n          AwsEcsDetectorSync._getUrlAsJson(metadataUrl),\n          AwsEcsDetectorSync._getUrlAsJson(`${metadataUrl}/task`),\n        ]);\n\n        const metadatav4Resource =\n          await AwsEcsDetectorSync._getMetadataV4Resource(\n            containerMetadata,\n            taskMetadata\n          );\n        const logsResource = await AwsEcsDetectorSync._getLogResource(\n          containerMetadata\n        );\n\n        resource = resource.merge(metadatav4Resource).merge(logsResource);\n      }\n\n      return resource.attributes;\n    } catch {\n      return {};\n    }\n  }\n\n  /**\n   * Read container ID from cgroup file\n   * In ECS, even if we fail to find target file\n   * or target file does not contain container ID\n   * we do not throw an error but throw warning message\n   * and then return null string\n   */\n  private static async _getContainerIdAndHostnameResource(): Promise<Resource> {\n    const hostName = os.hostname();\n\n    let containerId = '';\n    try {\n      const rawData = await AwsEcsDetectorSync.readFileAsync(\n        AwsEcsDetectorSync.DEFAULT_CGROUP_PATH,\n        'utf8'\n      );\n      const splitData = rawData.trim().split('\\n');\n      for (const str of splitData) {\n        if (str.length > AwsEcsDetectorSync.CONTAINER_ID_LENGTH) {\n          containerId = str.substring(\n            str.length - AwsEcsDetectorSync.CONTAINER_ID_LENGTH\n          );\n          break;\n        }\n      }\n    } catch (e) {\n      diag.debug('AwsEcsDetector failed to read container ID', e);\n    }\n\n    if (hostName || containerId) {\n      return new Resource({\n        [ATTR_CONTAINER_NAME]: hostName || '',\n        [ATTR_CONTAINER_ID]: containerId || '',\n      });\n    }\n\n    return Resource.empty();\n  }\n\n  private static async _getMetadataV4Resource(\n    containerMetadata: any,\n    taskMetadata: any\n  ): Promise<Resource> {\n    const launchType: string = taskMetadata['LaunchType'];\n    const taskArn: string = taskMetadata['TaskARN'];\n\n    const baseArn: string = taskArn.substring(0, taskArn.lastIndexOf(':'));\n    const cluster: string = taskMetadata['Cluster'];\n\n    const accountId: string = AwsEcsDetectorSync._getAccountFromArn(taskArn);\n    const region: string = AwsEcsDetectorSync._getRegionFromArn(taskArn);\n    const availabilityZone: string | undefined = taskMetadata?.AvailabilityZone;\n\n    const clusterArn = cluster.startsWith('arn:')\n      ? cluster\n      : `${baseArn}:cluster/${cluster}`;\n\n    const containerArn: string = containerMetadata['ContainerARN'];\n\n    // https://github.com/open-telemetry/semantic-conventions/blob/main/semantic_conventions/resource/cloud_provider/aws/ecs.yaml\n    const attributes: ResourceAttributes = {\n      [ATTR_AWS_ECS_CONTAINER_ARN]: containerArn,\n      [ATTR_AWS_ECS_CLUSTER_ARN]: clusterArn,\n      [ATTR_AWS_ECS_LAUNCHTYPE]: launchType?.toLowerCase(),\n      [ATTR_AWS_ECS_TASK_ARN]: taskArn,\n      [ATTR_AWS_ECS_TASK_FAMILY]: taskMetadata['Family'],\n      [ATTR_AWS_ECS_TASK_REVISION]: taskMetadata['Revision'],\n\n      [ATTR_CLOUD_ACCOUNT_ID]: accountId,\n      [ATTR_CLOUD_REGION]: region,\n      [ATTR_CLOUD_RESOURCE_ID]: containerArn,\n    };\n\n    // The availability zone is not available in all Fargate runtimes\n    if (availabilityZone) {\n      attributes[ATTR_CLOUD_AVAILABILITY_ZONE] = availabilityZone;\n    }\n\n    return new Resource(attributes);\n  }\n\n  private static async _getLogResource(\n    containerMetadata: any\n  ): Promise<Resource> {\n    if (\n      containerMetadata['LogDriver'] !== 'awslogs' ||\n      !containerMetadata['LogOptions']\n    ) {\n      return Resource.EMPTY;\n    }\n\n    const containerArn = containerMetadata['ContainerARN']!;\n    const logOptions = containerMetadata['LogOptions'] as AwsLogOptions;\n\n    const logsRegion =\n      logOptions['awslogs-region'] ||\n      AwsEcsDetectorSync._getRegionFromArn(containerArn);\n\n    const awsAccount = AwsEcsDetectorSync._getAccountFromArn(containerArn);\n\n    const logsGroupName = logOptions['awslogs-group']!;\n    const logsGroupArn = `arn:aws:logs:${logsRegion}:${awsAccount}:log-group:${logsGroupName}`;\n    const logsStreamName = logOptions['awslogs-stream']!;\n    const logsStreamArn = `arn:aws:logs:${logsRegion}:${awsAccount}:log-group:${logsGroupName}:log-stream:${logsStreamName}`;\n\n    return new Resource({\n      [ATTR_AWS_LOG_GROUP_NAMES]: [logsGroupName],\n      [ATTR_AWS_LOG_GROUP_ARNS]: [logsGroupArn],\n      [ATTR_AWS_LOG_STREAM_NAMES]: [logsStreamName],\n      [ATTR_AWS_LOG_STREAM_ARNS]: [logsStreamArn],\n    });\n  }\n\n  private static _getAccountFromArn(containerArn: string): string {\n    const match = /arn:aws:ecs:[^:]+:([^:]+):.*/.exec(containerArn);\n    return match![1];\n  }\n\n  private static _getRegionFromArn(containerArn: string): string {\n    const match = /arn:aws:ecs:([^:]+):.*/.exec(containerArn);\n    return match![1];\n  }\n\n  private static _getUrlAsJson(url: string): Promise<any> {\n    return new Promise<string>((resolve, reject) => {\n      const request = http.get(url, (response: http.IncomingMessage) => {\n        if (response.statusCode && response.statusCode >= 400) {\n          reject(\n            new Error(\n              `Request to '${url}' failed with status ${response.statusCode}`\n            )\n          );\n        }\n        /*\n         * Concatenate the response out of chunks:\n         * https://nodejs.org/api/stream.html#stream_event_data\n         */\n        let responseBody = '';\n        response.on(\n          'data',\n          (chunk: Buffer) => (responseBody += chunk.toString())\n        );\n        // All the data has been read, resolve the Promise\n        response.on('end', () => resolve(responseBody));\n        /*\n         * https://nodejs.org/api/http.html#httprequesturl-options-callback, see the\n         * 'In the case of a premature connection close after the response is received'\n         * case\n         */\n        request.on('error', reject);\n      });\n\n      // Set an aggressive timeout to prevent lock-ups\n      request.setTimeout(HTTP_TIMEOUT_IN_MS, () => {\n        request.destroy();\n      });\n      // Connection error, disconnection, etc.\n      request.on('error', reject);\n      request.end();\n    }).then(responseBodyRaw => JSON.parse(responseBodyRaw));\n  }\n}\n\nexport const awsEcsDetectorSync = new AwsEcsDetectorSync();\n"]}