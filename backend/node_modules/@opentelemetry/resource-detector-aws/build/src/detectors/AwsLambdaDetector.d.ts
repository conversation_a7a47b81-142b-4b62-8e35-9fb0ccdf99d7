import { Detector, IResource, ResourceDetectionConfig } from '@opentelemetry/resources';
/**
 * The AwsLambdaDetector can be used to detect if a process is running in AWS Lambda
 * and return a {@link Resource} populated with data about the environment.
 *
 * @deprecated Use {@link AwsLambdaDetectorSync} class instead
 */
export declare class AwsLambdaDetector implements Detector {
    detect(_config?: ResourceDetectionConfig): Promise<IResource>;
}
export declare const awsLambdaDetector: AwsLambdaDetector;
//# sourceMappingURL=AwsLambdaDetector.d.ts.map