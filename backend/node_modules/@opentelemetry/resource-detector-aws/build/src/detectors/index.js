"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.awsLambdaDetectorSync = exports.AwsLambdaDetectorSync = exports.awsLambdaDetector = exports.AwsLambdaDetector = exports.awsEksDetectorSync = exports.AwsEksDetectorSync = exports.awsEksDetector = exports.AwsEksDetector = exports.awsEcsDetectorSync = exports.AwsEcsDetectorSync = exports.awsEcsDetector = exports.AwsEcsDetector = exports.awsEc2DetectorSync = exports.awsEc2Detector = exports.awsBeanstalkDetectorSync = exports.AwsBeanstalkDetectorSync = exports.awsBeanstalkDetector = exports.AwsBeanstalkDetector = void 0;
var AwsBeanstalkDetector_1 = require("./AwsBeanstalkDetector");
Object.defineProperty(exports, "AwsBeanstalkDetector", { enumerable: true, get: function () { return AwsBeanstalkDetector_1.AwsBeanstalkDetector; } });
Object.defineProperty(exports, "awsBeanstalkDetector", { enumerable: true, get: function () { return AwsBeanstalkDetector_1.awsBeanstalkDetector; } });
var AwsBeanstalkDetectorSync_1 = require("./AwsBeanstalkDetectorSync");
Object.defineProperty(exports, "AwsBeanstalkDetectorSync", { enumerable: true, get: function () { return AwsBeanstalkDetectorSync_1.AwsBeanstalkDetectorSync; } });
Object.defineProperty(exports, "awsBeanstalkDetectorSync", { enumerable: true, get: function () { return AwsBeanstalkDetectorSync_1.awsBeanstalkDetectorSync; } });
var AwsEc2Detector_1 = require("./AwsEc2Detector");
Object.defineProperty(exports, "awsEc2Detector", { enumerable: true, get: function () { return AwsEc2Detector_1.awsEc2Detector; } });
var AwsEc2DetectorSync_1 = require("./AwsEc2DetectorSync");
Object.defineProperty(exports, "awsEc2DetectorSync", { enumerable: true, get: function () { return AwsEc2DetectorSync_1.awsEc2DetectorSync; } });
var AwsEcsDetector_1 = require("./AwsEcsDetector");
Object.defineProperty(exports, "AwsEcsDetector", { enumerable: true, get: function () { return AwsEcsDetector_1.AwsEcsDetector; } });
Object.defineProperty(exports, "awsEcsDetector", { enumerable: true, get: function () { return AwsEcsDetector_1.awsEcsDetector; } });
var AwsEcsDetectorSync_1 = require("./AwsEcsDetectorSync");
Object.defineProperty(exports, "AwsEcsDetectorSync", { enumerable: true, get: function () { return AwsEcsDetectorSync_1.AwsEcsDetectorSync; } });
Object.defineProperty(exports, "awsEcsDetectorSync", { enumerable: true, get: function () { return AwsEcsDetectorSync_1.awsEcsDetectorSync; } });
var AwsEksDetector_1 = require("./AwsEksDetector");
Object.defineProperty(exports, "AwsEksDetector", { enumerable: true, get: function () { return AwsEksDetector_1.AwsEksDetector; } });
Object.defineProperty(exports, "awsEksDetector", { enumerable: true, get: function () { return AwsEksDetector_1.awsEksDetector; } });
var AwsEksDetectorSync_1 = require("./AwsEksDetectorSync");
Object.defineProperty(exports, "AwsEksDetectorSync", { enumerable: true, get: function () { return AwsEksDetectorSync_1.AwsEksDetectorSync; } });
Object.defineProperty(exports, "awsEksDetectorSync", { enumerable: true, get: function () { return AwsEksDetectorSync_1.awsEksDetectorSync; } });
var AwsLambdaDetector_1 = require("./AwsLambdaDetector");
Object.defineProperty(exports, "AwsLambdaDetector", { enumerable: true, get: function () { return AwsLambdaDetector_1.AwsLambdaDetector; } });
Object.defineProperty(exports, "awsLambdaDetector", { enumerable: true, get: function () { return AwsLambdaDetector_1.awsLambdaDetector; } });
var AwsLambdaDetectorSync_1 = require("./AwsLambdaDetectorSync");
Object.defineProperty(exports, "AwsLambdaDetectorSync", { enumerable: true, get: function () { return AwsLambdaDetectorSync_1.AwsLambdaDetectorSync; } });
Object.defineProperty(exports, "awsLambdaDetectorSync", { enumerable: true, get: function () { return AwsLambdaDetectorSync_1.awsLambdaDetectorSync; } });
//# sourceMappingURL=index.js.map