import { Detector, IResource, ResourceDetectionConfig } from '@opentelemetry/resources';
/**
 * The AwsEc2Detector can be used to detect if a process is running in AWS EC2
 * and return a {@link Resource} populated with metadata about the EC2
 * instance.
 *
 * @deprecated Use {@link AwsEc2DetectorSync} class instead.
 */
declare class AwsEc2Detector implements Detector {
    detect(_config?: ResourceDetectionConfig): Promise<IResource>;
}
export declare const awsEc2Detector: AwsEc2Detector;
export {};
//# sourceMappingURL=AwsEc2Detector.d.ts.map