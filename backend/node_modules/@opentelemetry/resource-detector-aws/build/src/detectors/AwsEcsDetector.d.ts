import { Detector, IResource } from '@opentelemetry/resources';
/**
 * The AwsEcsDetector can be used to detect if a process is running in AWS
 * ECS and return a {@link Resource} populated with data about the ECS
 * plugins of AWS X-Ray.
 *
 * @deprecated Use {@link AwsEcsDetectorSync} class instead.
 */
export declare class AwsEcsDetector implements Detector {
    detect(): Promise<IResource>;
}
export declare const awsEcsDetector: AwsEcsDetector;
//# sourceMappingURL=AwsEcsDetector.d.ts.map