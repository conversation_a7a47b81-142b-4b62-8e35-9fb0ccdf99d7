{"version": 3, "file": "AwsEksDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsEksDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAGL,QAAQ,GAGT,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,mBAAmB,EACnB,mBAAmB,EACnB,qBAAqB,EACrB,iBAAiB,EACjB,wBAAwB,EACxB,4BAA4B,GAC7B,MAAM,YAAY,CAAC;AACpB,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C;;;;;;;GAOG;AAEH;IAAA;QACW,gBAAW,GAAG,wBAAwB,CAAC;QACvC,mBAAc,GACrB,qDAAqD,CAAC;QAC/C,kBAAa,GACpB,sDAAsD,CAAC;QAChD,wBAAmB,GAC1B,oDAAoD,CAAC;QAC9C,sBAAiB,GACxB,8DAA8D,CAAC;QACxD,wBAAmB,GAAG,EAAE,CAAC;QACzB,wBAAmB,GAAG,mBAAmB,CAAC;QAC1C,eAAU,GAAG,IAAI,CAAC;QAClB,iBAAY,GAAG,MAAM,CAAC;IAqLjC,CAAC;IAhLC,mCAAM,GAAN,UAAO,OAAiC;QAAxC,iBAKC;QAJC,IAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;YACjE,OAAA,KAAI,CAAC,cAAc,EAAE;QAArB,CAAqB,CACtB,CAAC;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;OAMG;IACW,2CAAc,GAA5B;;;;;;;;wBAEI,qBAAM,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,EAAA;;wBAA7D,SAA6D,CAAC;wBAC9C,qBAAM,kBAAkB,CAAC,aAAa,CACpD,IAAI,CAAC,aAAa,CACnB,EAAA;;wBAFK,OAAO,GAAG,SAEf;wBAEK,qBAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAA;;wBAAhC,IAAI,CAAC,CAAC,SAA0B,CAAC,EAAE;4BACjC,sBAAO,EAAE,EAAC;yBACX;wBAEmB,qBAAM,IAAI,CAAC,eAAe,EAAE,EAAA;;wBAA1C,WAAW,GAAG,SAA4B;wBAC5B,qBAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAA;;wBAAjD,WAAW,GAAG,SAAmC;wBAEvD,sBAAO,CAAC,WAAW,IAAI,CAAC,WAAW;gCACjC,CAAC,CAAC,EAAE;gCACJ,CAAC;oCACG,GAAC,mBAAmB,IAAG,wBAAwB;oCAC/C,GAAC,mBAAmB,IAAG,4BAA4B;oCACnD,GAAC,qBAAqB,IAAG,WAAW,IAAI,EAAE;oCAC1C,GAAC,iBAAiB,IAAG,WAAW,IAAI,EAAE;uCACvC,EAAC;;;wBAEN,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,GAAC,CAAC,CAAC;wBAC/C,sBAAO,EAAE,EAAC;;;;;KAEb;IAED;;;;OAIG;IACW,mCAAM,GAApB,UAAqB,IAAY;;;;;;;;4BAE7B,EAAE,EAAE,IAAI;;;wBAES,qBAAM,IAAI,CAAC,iBAAiB,EAAE,EAAA;;wBAH3C,OAAO,IAEX,UAAO,IACL,gBAAa,GAAE,SAA8B;+BAC9C;4BACD,WAAQ,GAAE,IAAI,CAAC,WAAW;4BAC1B,SAAM,GAAE,KAAK;4BACb,OAAI,GAAE,IAAI,CAAC,mBAAmB;4BAC9B,UAAO,GAAE,IAAI,CAAC,UAAU;+BACzB;wBACS,qBAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAA;4BAA1C,sBAAO,CAAC,CAAC,CAAC,SAAgC,CAAC,EAAC;;;;KAC7C;IAED;;;OAGG;IACW,4CAAe,GAA7B,UAA8B,IAAY;;;;;;;;4BAEtC,EAAE,EAAE,IAAI;;;wBAES,qBAAM,IAAI,CAAC,iBAAiB,EAAE,EAAA;;wBAH3C,OAAO,IAEX,UAAO,IACL,gBAAa,GAAE,SAA8B;+BAC9C;4BACD,OAAI,GAAE,IAAI,CAAC,WAAW;4BACtB,SAAM,GAAE,KAAK;4BACb,OAAI,GAAE,IAAI,CAAC,iBAAiB;4BAC5B,UAAO,GAAE,IAAI,CAAC,UAAU;+BACzB;wBACgB,qBAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAA;;wBAA3C,QAAQ,GAAG,SAAgC;wBACjD,IAAI;4BACF,sBAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,EAAC;yBAClD;wBAAC,OAAO,CAAC,EAAE;4BACV,IAAI,CAAC,KAAK,CAAC,gCAAgC,EAAE,CAAC,CAAC,CAAC;yBACjD;wBACD,sBAAO,EAAE,EAAC;;;;KACX;IACD;;;OAGG;IACW,8CAAiB,GAA/B;;;;;;;wBAEoB,qBAAM,kBAAkB,CAAC,aAAa,CACpD,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,YAAY,CAClB,EAAA;;wBAHK,OAAO,GAAG,SAGf;wBACD,sBAAO,SAAS,GAAG,OAAO,EAAC;;;wBAE3B,IAAI,CAAC,KAAK,CAAC,yCAAyC,EAAE,GAAC,CAAC,CAAC;;4BAE3D,sBAAO,EAAE,EAAC;;;;KACX;IAED;;;;;;;;;;;;;;;;OAgBG;IACW,4CAAe,GAA7B;;;;;;;wBAEoB,qBAAM,kBAAkB,CAAC,aAAa,CACpD,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,YAAY,CAClB,EAAA;;wBAHK,OAAO,GAAG,SAGf;wBACK,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAC7C,WAA2B,EAAT,uBAAS,EAAT,uBAAS,EAAT,IAAS,EAAE;4BAAlB,GAAG;4BACZ,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE;gCACzC,sBAAO,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,EAAC;6BAC7D;yBACF;;;;wBAED,IAAI,CAAC,KAAK,CAAC,iDAA+C,GAAC,CAAC,OAAS,CAAC,CAAC;;4BAEzE,sBAAO,SAAS,EAAC;;;;KAClB;IAED;;;;;OAKG;IACW,yCAAY,GAA1B,UAA2B,OAA6B;;;;;4BAC/C,qBAAM,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;4BACvC,IAAM,SAAS,GAAG,UAAU,CAAC;gCAC3B,GAAG,CAAC,KAAK,EAAE,CAAC;gCACZ,MAAM,CAAC,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC,CAAC;4BAC3D,CAAC,EAAE,IAAI,CAAC,CAAC;4BAET,IAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,UAAA,GAAG;gCACpC,YAAY,CAAC,SAAS,CAAC,CAAC;gCAChB,IAAA,UAAU,GAAK,GAAG,WAAR,CAAS;gCAC3B,GAAG,CAAC,WAAW,CAAC,KAAI,CAAC,YAAY,CAAC,CAAC;gCACnC,IAAI,OAAO,GAAG,EAAE,CAAC;gCACjB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,UAAA,KAAK,IAAI,OAAA,CAAC,OAAO,IAAI,KAAK,CAAC,EAAlB,CAAkB,CAAC,CAAC;gCAC5C,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE;oCACZ,IAAI,UAAU,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE;wCACvD,IAAI;4CACF,OAAO,CAAC,OAAO,CAAC,CAAC;yCAClB;wCAAC,OAAO,CAAC,EAAE;4CACV,MAAM,CAAC,CAAC,CAAC,CAAC;yCACX;qCACF;yCAAM;wCACL,MAAM,CACJ,IAAI,KAAK,CAAC,oCAAoC,GAAG,UAAU,CAAC,CAC7D,CAAC;qCACH;gCACH,CAAC,CAAC,CAAC;4BACL,CAAC,CAAC,CAAC;4BACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,UAAA,GAAG;gCACjB,YAAY,CAAC,SAAS,CAAC,CAAC;gCACxB,MAAM,CAAC,GAAG,CAAC,CAAC;4BACd,CAAC,CAAC,CAAC;4BACH,GAAG,CAAC,GAAG,EAAE,CAAC;wBACZ,CAAC,CAAC,EAAA;4BA/BF,sBAAO,SA+BL,EAAC;;;;KACJ;IAlLc,gCAAa,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IAC5C,kCAAe,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;IAkL7D,yBAAC;CAAA,AAlMD,IAkMC;SAlMY,kBAAkB;AAoM/B,MAAM,CAAC,IAAM,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport {\n  DetectorSync,\n  IResource,\n  Resource,\n  ResourceAttributes,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport {\n  ATTR_CLOUD_PROVIDER,\n  ATTR_CLOUD_PLATFORM,\n  ATTR_K8S_CLUSTER_NAME,\n  ATTR_CONTAINER_ID,\n  CLOUD_PROVIDER_VALUE_AWS,\n  CLOUD_PLATFORM_VALUE_AWS_EKS,\n} from '../semconv';\nimport * as https from 'https';\nimport * as fs from 'fs';\nimport * as util from 'util';\nimport { diag } from '@opentelemetry/api';\n\n/**\n * The AwsEksDetectorSync can be used to detect if a process is running in AWS Elastic\n * Kubernetes and return a {@link Resource} populated with data about the Kubernetes\n * plugins of AWS X-Ray. Returns an empty Resource if detection fails.\n *\n * See https://docs.amazonaws.cn/en_us/xray/latest/devguide/xray-guide.pdf\n * for more details about detecting information for Elastic Kubernetes plugins\n */\n\nexport class AwsEksDetectorSync implements DetectorSync {\n  readonly K8S_SVC_URL = 'kubernetes.default.svc';\n  readonly K8S_TOKEN_PATH =\n    '/var/run/secrets/kubernetes.io/serviceaccount/token';\n  readonly K8S_CERT_PATH =\n    '/var/run/secrets/kubernetes.io/serviceaccount/ca.crt';\n  readonly AUTH_CONFIGMAP_PATH =\n    '/api/v1/namespaces/kube-system/configmaps/aws-auth';\n  readonly CW_CONFIGMAP_PATH =\n    '/api/v1/namespaces/amazon-cloudwatch/configmaps/cluster-info';\n  readonly CONTAINER_ID_LENGTH = 64;\n  readonly DEFAULT_CGROUP_PATH = '/proc/self/cgroup';\n  readonly TIMEOUT_MS = 2000;\n  readonly UTF8_UNICODE = 'utf8';\n\n  private static readFileAsync = util.promisify(fs.readFile);\n  private static fileAccessAsync = util.promisify(fs.access);\n\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes = context.with(suppressTracing(context.active()), () =>\n      this._getAttributes()\n    );\n    return new Resource({}, attributes);\n  }\n\n  /**\n   * The AwsEksDetector can be used to detect if a process is running on Amazon\n   * Elastic Kubernetes and returns a promise containing a {@link ResourceAttributes}\n   * object with instance metadata. Returns a promise containing an\n   * empty {@link ResourceAttributes} if the connection to kubernetes process\n   * or aws config maps fails\n   */\n  private async _getAttributes(): Promise<ResourceAttributes> {\n    try {\n      await AwsEksDetectorSync.fileAccessAsync(this.K8S_TOKEN_PATH);\n      const k8scert = await AwsEksDetectorSync.readFileAsync(\n        this.K8S_CERT_PATH\n      );\n\n      if (!(await this._isEks(k8scert))) {\n        return {};\n      }\n\n      const containerId = await this._getContainerId();\n      const clusterName = await this._getClusterName(k8scert);\n\n      return !containerId && !clusterName\n        ? {}\n        : {\n            [ATTR_CLOUD_PROVIDER]: CLOUD_PROVIDER_VALUE_AWS,\n            [ATTR_CLOUD_PLATFORM]: CLOUD_PLATFORM_VALUE_AWS_EKS,\n            [ATTR_K8S_CLUSTER_NAME]: clusterName || '',\n            [ATTR_CONTAINER_ID]: containerId || '',\n          };\n    } catch (e) {\n      diag.debug('Process is not running on K8S', e);\n      return {};\n    }\n  }\n\n  /**\n   * Attempts to make a connection to AWS Config map which will\n   * determine whether the process is running on an EKS\n   * process if the config map is empty or not\n   */\n  private async _isEks(cert: Buffer): Promise<boolean> {\n    const options = {\n      ca: cert,\n      headers: {\n        Authorization: await this._getK8sCredHeader(),\n      },\n      hostname: this.K8S_SVC_URL,\n      method: 'GET',\n      path: this.AUTH_CONFIGMAP_PATH,\n      timeout: this.TIMEOUT_MS,\n    };\n    return !!(await this._fetchString(options));\n  }\n\n  /**\n   * Attempts to make a connection to Amazon Cloudwatch\n   * Config Maps to grab cluster name\n   */\n  private async _getClusterName(cert: Buffer): Promise<string | undefined> {\n    const options = {\n      ca: cert,\n      headers: {\n        Authorization: await this._getK8sCredHeader(),\n      },\n      host: this.K8S_SVC_URL,\n      method: 'GET',\n      path: this.CW_CONFIGMAP_PATH,\n      timeout: this.TIMEOUT_MS,\n    };\n    const response = await this._fetchString(options);\n    try {\n      return JSON.parse(response).data['cluster.name'];\n    } catch (e) {\n      diag.debug('Cannot get cluster name on EKS', e);\n    }\n    return '';\n  }\n  /**\n   * Reads the Kubernetes token path and returns kubernetes\n   * credential header\n   */\n  private async _getK8sCredHeader(): Promise<string> {\n    try {\n      const content = await AwsEksDetectorSync.readFileAsync(\n        this.K8S_TOKEN_PATH,\n        this.UTF8_UNICODE\n      );\n      return 'Bearer ' + content;\n    } catch (e) {\n      diag.debug('Unable to read Kubernetes client token.', e);\n    }\n    return '';\n  }\n\n  /**\n   * Read container ID from cgroup file generated from docker which lists the full\n   * untruncated docker container ID at the end of each line.\n   *\n   * The predefined structure of calling /proc/self/cgroup when in a docker container has the structure:\n   *\n   * #:xxxxxx:/\n   *\n   * or\n   *\n   * #:xxxxxx:/docker/64characterID\n   *\n   * This function takes advantage of that fact by just reading the 64-character ID from the end of the\n   * first line. In EKS, even if we fail to find target file or target file does\n   * not contain container ID we do not throw an error but throw warning message\n   * and then return null string\n   */\n  private async _getContainerId(): Promise<string | undefined> {\n    try {\n      const rawData = await AwsEksDetectorSync.readFileAsync(\n        this.DEFAULT_CGROUP_PATH,\n        this.UTF8_UNICODE\n      );\n      const splitData = rawData.trim().split('\\n');\n      for (const str of splitData) {\n        if (str.length > this.CONTAINER_ID_LENGTH) {\n          return str.substring(str.length - this.CONTAINER_ID_LENGTH);\n        }\n      }\n    } catch (e: any) {\n      diag.debug(`AwsEksDetector failed to read container ID: ${e.message}`);\n    }\n    return undefined;\n  }\n\n  /**\n   * Establishes an HTTP connection to AWS instance document url.\n   * If the application is running on an EKS instance, we should be able\n   * to get back a valid JSON document. Parses that document and stores\n   * the identity properties in a local map.\n   */\n  private async _fetchString(options: https.RequestOptions): Promise<string> {\n    return await new Promise((resolve, reject) => {\n      const timeoutId = setTimeout(() => {\n        req.abort();\n        reject(new Error('EKS metadata api request timed out.'));\n      }, 2000);\n\n      const req = https.request(options, res => {\n        clearTimeout(timeoutId);\n        const { statusCode } = res;\n        res.setEncoding(this.UTF8_UNICODE);\n        let rawData = '';\n        res.on('data', chunk => (rawData += chunk));\n        res.on('end', () => {\n          if (statusCode && statusCode >= 200 && statusCode < 300) {\n            try {\n              resolve(rawData);\n            } catch (e) {\n              reject(e);\n            }\n          } else {\n            reject(\n              new Error('Failed to load page, status code: ' + statusCode)\n            );\n          }\n        });\n      });\n      req.on('error', err => {\n        clearTimeout(timeoutId);\n        reject(err);\n      });\n      req.end();\n    });\n  }\n}\n\nexport const awsEksDetectorSync = new AwsEksDetectorSync();\n"]}