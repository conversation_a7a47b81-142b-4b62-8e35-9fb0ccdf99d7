{"version": 3, "file": "AwsLambdaDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsLambdaDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAOH,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAEhE;;;;;GAKG;AACH;IAAA;IAIA,CAAC;IAHC,kCAAM,GAAN,UAAO,OAAiC;QACtC,OAAO,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IAChE,CAAC;IACH,wBAAC;AAAD,CAAC,AAJD,IAIC;;AAED,MAAM,CAAC,IAAM,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Detector,\n  IResource,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport { awsLambdaDetectorSync } from './AwsLambdaDetectorSync';\n\n/**\n * The AwsLambdaDetector can be used to detect if a process is running in AWS Lambda\n * and return a {@link Resource} populated with data about the environment.\n *\n * @deprecated Use {@link AwsLambdaDetectorSync} class instead\n */\nexport class AwsLambdaDetector implements Detector {\n  detect(_config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(awsLambdaDetectorSync.detect(_config));\n  }\n}\n\nexport const awsLambdaDetector = new AwsLambdaDetector();\n"]}