{"version": 3, "file": "AwsBeanstalkDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsBeanstalkDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAQH,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AAEtE;;;;;;;;;GASG;AAEH;IAAA;IAIA,CAAC;IAHC,qCAAM,GAAN,UAAO,MAAgC;QACrC,OAAO,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAClE,CAAC;IACH,2BAAC;AAAD,CAAC,AAJD,IAIC;;AAED,MAAM,CAAC,IAAM,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Detector,\n  IResource,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\n\nimport { awsBeanstalkDetectorSync } from './AwsBeanstalkDetectorSync';\n\n/**\n * The AwsBeanstalkDetector can be used to detect if a process is running in AWS Elastic\n * Beanstalk and return a {@link Resource} populated with data about the beanstalk\n * plugins of AWS X-Ray. Returns an empty Resource if detection fails.\n *\n * See https://docs.amazonaws.cn/en_us/xray/latest/devguide/xray-guide.pdf\n * for more details about detecting information of Elastic Beanstalk plugins\n *\n * @deprecated Use {@link AwsBeanstalkDetectorSync} class instead.\n */\n\nexport class AwsBeanstalkDetector implements Detector {\n  detect(config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(awsBeanstalkDetectorSync.detect(config));\n  }\n}\n\nexport const awsBeanstalkDetector = new AwsBeanstalkDetector();\n"]}