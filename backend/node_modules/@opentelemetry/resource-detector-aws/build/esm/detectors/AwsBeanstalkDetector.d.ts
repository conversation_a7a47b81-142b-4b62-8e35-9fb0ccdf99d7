import { Detector, IResource, ResourceDetectionConfig } from '@opentelemetry/resources';
/**
 * The AwsBeanstalkDetector can be used to detect if a process is running in AWS Elastic
 * Beanstalk and return a {@link Resource} populated with data about the beanstalk
 * plugins of AWS X-Ray. Returns an empty Resource if detection fails.
 *
 * See https://docs.amazonaws.cn/en_us/xray/latest/devguide/xray-guide.pdf
 * for more details about detecting information of Elastic Beanstalk plugins
 *
 * @deprecated Use {@link AwsBeanstalkDetectorSync} class instead.
 */
export declare class AwsBeanstalkDetector implements Detector {
    detect(config?: ResourceDetectionConfig): Promise<IResource>;
}
export declare const awsBeanstalkDetector: AwsBeanstalkDetector;
//# sourceMappingURL=AwsBeanstalkDetector.d.ts.map