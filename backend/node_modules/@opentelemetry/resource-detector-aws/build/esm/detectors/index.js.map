{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/detectors/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,oBAAoB,EACpB,oBAAoB,GACrB,MAAM,wBAAwB,CAAC;AAChC,OAAO,EACL,wBAAwB,EACxB,wBAAwB,GACzB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClE,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC9E,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClE,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC9E,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAC3E,OAAO,EACL,qBAAqB,EACrB,qBAAqB,GACtB,MAAM,yBAAyB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport {\n  AwsBeanstalkDetector,\n  awsBeanstalkDetector,\n} from './AwsBeanstalkDetector';\nexport {\n  AwsBeanstalkDetectorSync,\n  awsBeanstalkDetectorSync,\n} from './AwsBeanstalkDetectorSync';\nexport { awsEc2Detector } from './AwsEc2Detector';\nexport { awsEc2DetectorSync } from './AwsEc2DetectorSync';\nexport { AwsEcsDetector, awsEcsDetector } from './AwsEcsDetector';\nexport { AwsEcsDetectorSync, awsEcsDetectorSync } from './AwsEcsDetectorSync';\nexport { AwsEksDetector, awsEksDetector } from './AwsEksDetector';\nexport { AwsEksDetectorSync, awsEksDetectorSync } from './AwsEksDetectorSync';\nexport { AwsLambdaDetector, awsLambdaDetector } from './AwsLambdaDetector';\nexport {\n  AwsLambdaDetectorSync,\n  awsLambdaDetectorSync,\n} from './AwsLambdaDetectorSync';\n"]}