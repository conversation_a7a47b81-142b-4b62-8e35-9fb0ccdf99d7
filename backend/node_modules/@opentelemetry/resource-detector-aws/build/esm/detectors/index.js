/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export { AwsBeanstalkDetector, awsBeanstalkDetector, } from './AwsBeanstalkDetector';
export { AwsBeanstalkDetectorSync, awsBeanstalkDetectorSync, } from './AwsBeanstalkDetectorSync';
export { awsEc2Detector } from './AwsEc2Detector';
export { awsEc2DetectorSync } from './AwsEc2DetectorSync';
export { AwsEcsDetector, awsEcsDetector } from './AwsEcsDetector';
export { AwsEcsDetectorSync, awsEcsDetectorSync } from './AwsEcsDetectorSync';
export { AwsEksDetector, awsEksDetector } from './AwsEksDetector';
export { AwsEksDetectorSync, awsEksDetectorSync } from './AwsEksDetectorSync';
export { AwsLambdaDetector, awsLambdaDetector } from './AwsLambdaDetector';
export { AwsLambdaDetectorSync, awsLambdaDetectorSync, } from './AwsLambdaDetectorSync';
//# sourceMappingURL=index.js.map