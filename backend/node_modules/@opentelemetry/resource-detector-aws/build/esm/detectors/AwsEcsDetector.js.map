{"version": 3, "file": "AwsEcsDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsEcsDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAE1D;;;;;;GAMG;AACH;IAAA;IAIA,CAAC;IAHC,+BAAM,GAAN;QACE,OAAO,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAC;IACtD,CAAC;IACH,qBAAC;AAAD,CAAC,AAJD,IAIC;;AAED,MAAM,CAAC,IAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector, IResource } from '@opentelemetry/resources';\nimport { awsEcsDetectorSync } from './AwsEcsDetectorSync';\n\n/**\n * The AwsEcsDetector can be used to detect if a process is running in AWS\n * ECS and return a {@link Resource} populated with data about the ECS\n * plugins of AWS X-Ray.\n *\n * @deprecated Use {@link AwsEcsDetectorSync} class instead.\n */\nexport class AwsEcsDetector implements Detector {\n  detect(): Promise<IResource> {\n    return Promise.resolve(awsEcsDetectorSync.detect());\n  }\n}\n\nexport const awsEcsDetector = new AwsEcsDetector();\n"]}