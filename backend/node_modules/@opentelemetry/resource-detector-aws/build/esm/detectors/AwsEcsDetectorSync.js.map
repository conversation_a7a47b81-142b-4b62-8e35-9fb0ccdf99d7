{"version": 3, "file": "AwsEcsDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsEcsDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAGL,QAAQ,GAET,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,wBAAwB,EACxB,0BAA0B,EAC1B,uBAAuB,EACvB,qBAAqB,EACrB,wBAAwB,EACxB,0BAA0B,EAC1B,uBAAuB,EACvB,wBAAwB,EACxB,wBAAwB,EACxB,yBAAyB,EACzB,qBAAqB,EACrB,4BAA4B,EAC5B,mBAAmB,EACnB,mBAAmB,EACnB,iBAAiB,EACjB,sBAAsB,EACtB,iBAAiB,EACjB,mBAAmB,EACnB,wBAAwB,EACxB,4BAA4B,GAC7B,MAAM,YAAY,CAAC;AACpB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAEzB,IAAM,kBAAkB,GAAG,IAAI,CAAC;AAQhC;;;;GAIG;AACH;IAAA;IAsNA,CAAC;IAhNC,mCAAM,GAAN;QAAA,iBAKC;QAJC,IAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;YACjE,OAAA,KAAI,CAAC,cAAc,EAAE;QAArB,CAAqB,CACtB,CAAC;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAEa,2CAAc,GAA5B;;;;;;;wBACE,IACE,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B;4BAC1C,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,EACvC;4BACA,IAAI,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;4BAC3D,sBAAO,EAAE,EAAC;yBACX;;;;wBAGgB,KAAA,CAAA,KAAA,IAAI,QAAQ;4BACzB,GAAC,mBAAmB,IAAG,wBAAwB;4BAC/C,GAAC,mBAAmB,IAAG,4BAA4B;gCACnD,CAAA,CAAC,KAAK,CAAA;wBAAC,qBAAM,kBAAkB,CAAC,kCAAkC,EAAE,EAAA;;wBAHlE,QAAQ,GAAG,cAGN,SAA6D,EAAC;wBAEjE,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;6BAC1D,WAAW,EAAX,wBAAW;wBAC6B,qBAAM,OAAO,CAAC,GAAG,CAAC;gCAC1D,kBAAkB,CAAC,aAAa,CAAC,WAAW,CAAC;gCAC7C,kBAAkB,CAAC,aAAa,CAAI,WAAW,UAAO,CAAC;6BACxD,CAAC,EAAA;;wBAHI,KAAoC,SAGxC,EAHK,iBAAiB,QAAA,EAAE,YAAY,QAAA;wBAMpC,qBAAM,kBAAkB,CAAC,sBAAsB,CAC7C,iBAAiB,EACjB,YAAY,CACb,EAAA;;wBAJG,kBAAkB,GACtB,SAGC;wBACkB,qBAAM,kBAAkB,CAAC,eAAe,CAC3D,iBAAiB,CAClB,EAAA;;wBAFK,YAAY,GAAG,SAEpB;wBAED,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;;4BAGpE,sBAAO,QAAQ,CAAC,UAAU,EAAC;;;wBAE3B,sBAAO,EAAE,EAAC;;;;;KAEb;IAED;;;;;;OAMG;IACkB,qDAAkC,GAAvD;;;;;;;wBACQ,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;wBAE3B,WAAW,GAAG,EAAE,CAAC;;;;wBAEH,qBAAM,kBAAkB,CAAC,aAAa,CACpD,kBAAkB,CAAC,mBAAmB,EACtC,MAAM,CACP,EAAA;;wBAHK,OAAO,GAAG,SAGf;wBACK,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAC7C,WAA2B,EAAT,uBAAS,EAAT,uBAAS,EAAT,IAAS,EAAE;4BAAlB,GAAG;4BACZ,IAAI,GAAG,CAAC,MAAM,GAAG,kBAAkB,CAAC,mBAAmB,EAAE;gCACvD,WAAW,GAAG,GAAG,CAAC,SAAS,CACzB,GAAG,CAAC,MAAM,GAAG,kBAAkB,CAAC,mBAAmB,CACpD,CAAC;gCACF,MAAM;6BACP;yBACF;;;;wBAED,IAAI,CAAC,KAAK,CAAC,4CAA4C,EAAE,GAAC,CAAC,CAAC;;;wBAG9D,IAAI,QAAQ,IAAI,WAAW,EAAE;4BAC3B,sBAAO,IAAI,QAAQ;oCACjB,GAAC,mBAAmB,IAAG,QAAQ,IAAI,EAAE;oCACrC,GAAC,iBAAiB,IAAG,WAAW,IAAI,EAAE;wCACtC,EAAC;yBACJ;wBAED,sBAAO,QAAQ,CAAC,KAAK,EAAE,EAAC;;;;KACzB;IAEoB,yCAAsB,GAA3C,UACE,iBAAsB,EACtB,YAAiB;;;;;gBAEX,UAAU,GAAW,YAAY,CAAC,YAAY,CAAC,CAAC;gBAChD,OAAO,GAAW,YAAY,CAAC,SAAS,CAAC,CAAC;gBAE1C,OAAO,GAAW,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjE,OAAO,GAAW,YAAY,CAAC,SAAS,CAAC,CAAC;gBAE1C,SAAS,GAAW,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACnE,MAAM,GAAW,kBAAkB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBAC/D,gBAAgB,GAAuB,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,gBAAgB,CAAC;gBAEtE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;oBAC3C,CAAC,CAAC,OAAO;oBACT,CAAC,CAAI,OAAO,iBAAY,OAAS,CAAC;gBAE9B,YAAY,GAAW,iBAAiB,CAAC,cAAc,CAAC,CAAC;gBAGzD,UAAU;oBACd,GAAC,0BAA0B,IAAG,YAAY;oBAC1C,GAAC,wBAAwB,IAAG,UAAU;oBACtC,GAAC,uBAAuB,IAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,WAAW,EAAE;oBACpD,GAAC,qBAAqB,IAAG,OAAO;oBAChC,GAAC,wBAAwB,IAAG,YAAY,CAAC,QAAQ,CAAC;oBAClD,GAAC,0BAA0B,IAAG,YAAY,CAAC,UAAU,CAAC;oBAEtD,GAAC,qBAAqB,IAAG,SAAS;oBAClC,GAAC,iBAAiB,IAAG,MAAM;oBAC3B,GAAC,sBAAsB,IAAG,YAAY;uBACvC,CAAC;gBAEF,iEAAiE;gBACjE,IAAI,gBAAgB,EAAE;oBACpB,UAAU,CAAC,4BAA4B,CAAC,GAAG,gBAAgB,CAAC;iBAC7D;gBAED,sBAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAC;;;KACjC;IAEoB,kCAAe,GAApC,UACE,iBAAsB;;;;;gBAEtB,IACE,iBAAiB,CAAC,WAAW,CAAC,KAAK,SAAS;oBAC5C,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAChC;oBACA,sBAAO,QAAQ,CAAC,KAAK,EAAC;iBACvB;gBAEK,YAAY,GAAG,iBAAiB,CAAC,cAAc,CAAE,CAAC;gBAClD,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAkB,CAAC;gBAE9D,UAAU,GACd,UAAU,CAAC,gBAAgB,CAAC;oBAC5B,kBAAkB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAE/C,UAAU,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;gBAEjE,aAAa,GAAG,UAAU,CAAC,eAAe,CAAE,CAAC;gBAC7C,YAAY,GAAG,kBAAgB,UAAU,SAAI,UAAU,mBAAc,aAAe,CAAC;gBACrF,cAAc,GAAG,UAAU,CAAC,gBAAgB,CAAE,CAAC;gBAC/C,aAAa,GAAG,kBAAgB,UAAU,SAAI,UAAU,mBAAc,aAAa,oBAAe,cAAgB,CAAC;gBAEzH,sBAAO,IAAI,QAAQ;wBACjB,GAAC,wBAAwB,IAAG,CAAC,aAAa,CAAC;wBAC3C,GAAC,uBAAuB,IAAG,CAAC,YAAY,CAAC;wBACzC,GAAC,yBAAyB,IAAG,CAAC,cAAc,CAAC;wBAC7C,GAAC,wBAAwB,IAAG,CAAC,aAAa,CAAC;4BAC3C,EAAC;;;KACJ;IAEc,qCAAkB,GAAjC,UAAkC,YAAoB;QACpD,IAAM,KAAK,GAAG,8BAA8B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAChE,OAAO,KAAM,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAEc,oCAAiB,GAAhC,UAAiC,YAAoB;QACnD,IAAM,KAAK,GAAG,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1D,OAAO,KAAM,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAEc,gCAAa,GAA5B,UAA6B,GAAW;QACtC,OAAO,IAAI,OAAO,CAAS,UAAC,OAAO,EAAE,MAAM;YACzC,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAC,QAA8B;gBAC3D,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE;oBACrD,MAAM,CACJ,IAAI,KAAK,CACP,iBAAe,GAAG,6BAAwB,QAAQ,CAAC,UAAY,CAChE,CACF,CAAC;iBACH;gBACD;;;mBAGG;gBACH,IAAI,YAAY,GAAG,EAAE,CAAC;gBACtB,QAAQ,CAAC,EAAE,CACT,MAAM,EACN,UAAC,KAAa,IAAK,OAAA,CAAC,YAAY,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,EAAlC,CAAkC,CACtD,CAAC;gBACF,kDAAkD;gBAClD,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,cAAM,OAAA,OAAO,CAAC,YAAY,CAAC,EAArB,CAAqB,CAAC,CAAC;gBAChD;;;;mBAIG;gBACH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,gDAAgD;YAChD,OAAO,CAAC,UAAU,CAAC,kBAAkB,EAAE;gBACrC,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC,CAAC,CAAC;YACH,wCAAwC;YACxC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC5B,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAA,eAAe,IAAI,OAAA,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAA3B,CAA2B,CAAC,CAAC;IAC1D,CAAC;IApNe,sCAAmB,GAAG,EAAE,CAAC;IACzB,sCAAmB,GAAG,mBAAmB,CAAC;IAE3C,gCAAa,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IAkN7D,yBAAC;CAAA,AAtND,IAsNC;SAtNY,kBAAkB;AAwN/B,MAAM,CAAC,IAAM,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context, diag } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport {\n  DetectorSync,\n  IResource,\n  Resource,\n  ResourceAttributes,\n} from '@opentelemetry/resources';\nimport {\n  ATTR_AWS_ECS_CLUSTER_ARN,\n  ATTR_AWS_ECS_CONTAINER_ARN,\n  ATTR_AWS_ECS_LAUNCHTYPE,\n  ATTR_AWS_ECS_TASK_ARN,\n  ATTR_AWS_ECS_TASK_FAMILY,\n  ATTR_AWS_ECS_TASK_REVISION,\n  ATTR_AWS_LOG_GROUP_ARNS,\n  ATTR_AWS_LOG_GROUP_NAMES,\n  ATTR_AWS_LOG_STREAM_ARNS,\n  ATTR_AWS_LOG_STREAM_NAMES,\n  ATTR_CLOUD_ACCOUNT_ID,\n  ATTR_CLOUD_AVAILABILITY_ZONE,\n  ATTR_CLOUD_PLATFORM,\n  ATTR_CLOUD_PROVIDER,\n  ATTR_CLOUD_REGION,\n  ATTR_CLOUD_RESOURCE_ID,\n  ATTR_CONTAINER_ID,\n  ATTR_CONTAINER_NAME,\n  CLOUD_PROVIDER_VALUE_AWS,\n  CLOUD_PLATFORM_VALUE_AWS_ECS,\n} from '../semconv';\nimport * as http from 'http';\nimport * as util from 'util';\nimport * as fs from 'fs';\nimport * as os from 'os';\n\nconst HTTP_TIMEOUT_IN_MS = 1000;\n\ninterface AwsLogOptions {\n  readonly 'awslogs-region'?: string;\n  readonly 'awslogs-group'?: string;\n  readonly 'awslogs-stream'?: string;\n}\n\n/**\n * The AwsEcsDetector can be used to detect if a process is running in AWS\n * ECS and return a {@link Resource} populated with data about the ECS\n * plugins of AWS X-Ray. Returns an empty Resource if detection fails.\n */\nexport class AwsEcsDetectorSync implements DetectorSync {\n  static readonly CONTAINER_ID_LENGTH = 64;\n  static readonly DEFAULT_CGROUP_PATH = '/proc/self/cgroup';\n\n  private static readFileAsync = util.promisify(fs.readFile);\n\n  detect(): IResource {\n    const attributes = context.with(suppressTracing(context.active()), () =>\n      this._getAttributes()\n    );\n    return new Resource({}, attributes);\n  }\n\n  private async _getAttributes(): Promise<ResourceAttributes> {\n    if (\n      !process.env.ECS_CONTAINER_METADATA_URI_V4 &&\n      !process.env.ECS_CONTAINER_METADATA_URI\n    ) {\n      diag.debug('AwsEcsDetector failed: Process is not on ECS');\n      return {};\n    }\n\n    try {\n      let resource = new Resource({\n        [ATTR_CLOUD_PROVIDER]: CLOUD_PROVIDER_VALUE_AWS,\n        [ATTR_CLOUD_PLATFORM]: CLOUD_PLATFORM_VALUE_AWS_ECS,\n      }).merge(await AwsEcsDetectorSync._getContainerIdAndHostnameResource());\n\n      const metadataUrl = process.env.ECS_CONTAINER_METADATA_URI_V4;\n      if (metadataUrl) {\n        const [containerMetadata, taskMetadata] = await Promise.all([\n          AwsEcsDetectorSync._getUrlAsJson(metadataUrl),\n          AwsEcsDetectorSync._getUrlAsJson(`${metadataUrl}/task`),\n        ]);\n\n        const metadatav4Resource =\n          await AwsEcsDetectorSync._getMetadataV4Resource(\n            containerMetadata,\n            taskMetadata\n          );\n        const logsResource = await AwsEcsDetectorSync._getLogResource(\n          containerMetadata\n        );\n\n        resource = resource.merge(metadatav4Resource).merge(logsResource);\n      }\n\n      return resource.attributes;\n    } catch {\n      return {};\n    }\n  }\n\n  /**\n   * Read container ID from cgroup file\n   * In ECS, even if we fail to find target file\n   * or target file does not contain container ID\n   * we do not throw an error but throw warning message\n   * and then return null string\n   */\n  private static async _getContainerIdAndHostnameResource(): Promise<Resource> {\n    const hostName = os.hostname();\n\n    let containerId = '';\n    try {\n      const rawData = await AwsEcsDetectorSync.readFileAsync(\n        AwsEcsDetectorSync.DEFAULT_CGROUP_PATH,\n        'utf8'\n      );\n      const splitData = rawData.trim().split('\\n');\n      for (const str of splitData) {\n        if (str.length > AwsEcsDetectorSync.CONTAINER_ID_LENGTH) {\n          containerId = str.substring(\n            str.length - AwsEcsDetectorSync.CONTAINER_ID_LENGTH\n          );\n          break;\n        }\n      }\n    } catch (e) {\n      diag.debug('AwsEcsDetector failed to read container ID', e);\n    }\n\n    if (hostName || containerId) {\n      return new Resource({\n        [ATTR_CONTAINER_NAME]: hostName || '',\n        [ATTR_CONTAINER_ID]: containerId || '',\n      });\n    }\n\n    return Resource.empty();\n  }\n\n  private static async _getMetadataV4Resource(\n    containerMetadata: any,\n    taskMetadata: any\n  ): Promise<Resource> {\n    const launchType: string = taskMetadata['LaunchType'];\n    const taskArn: string = taskMetadata['TaskARN'];\n\n    const baseArn: string = taskArn.substring(0, taskArn.lastIndexOf(':'));\n    const cluster: string = taskMetadata['Cluster'];\n\n    const accountId: string = AwsEcsDetectorSync._getAccountFromArn(taskArn);\n    const region: string = AwsEcsDetectorSync._getRegionFromArn(taskArn);\n    const availabilityZone: string | undefined = taskMetadata?.AvailabilityZone;\n\n    const clusterArn = cluster.startsWith('arn:')\n      ? cluster\n      : `${baseArn}:cluster/${cluster}`;\n\n    const containerArn: string = containerMetadata['ContainerARN'];\n\n    // https://github.com/open-telemetry/semantic-conventions/blob/main/semantic_conventions/resource/cloud_provider/aws/ecs.yaml\n    const attributes: ResourceAttributes = {\n      [ATTR_AWS_ECS_CONTAINER_ARN]: containerArn,\n      [ATTR_AWS_ECS_CLUSTER_ARN]: clusterArn,\n      [ATTR_AWS_ECS_LAUNCHTYPE]: launchType?.toLowerCase(),\n      [ATTR_AWS_ECS_TASK_ARN]: taskArn,\n      [ATTR_AWS_ECS_TASK_FAMILY]: taskMetadata['Family'],\n      [ATTR_AWS_ECS_TASK_REVISION]: taskMetadata['Revision'],\n\n      [ATTR_CLOUD_ACCOUNT_ID]: accountId,\n      [ATTR_CLOUD_REGION]: region,\n      [ATTR_CLOUD_RESOURCE_ID]: containerArn,\n    };\n\n    // The availability zone is not available in all Fargate runtimes\n    if (availabilityZone) {\n      attributes[ATTR_CLOUD_AVAILABILITY_ZONE] = availabilityZone;\n    }\n\n    return new Resource(attributes);\n  }\n\n  private static async _getLogResource(\n    containerMetadata: any\n  ): Promise<Resource> {\n    if (\n      containerMetadata['LogDriver'] !== 'awslogs' ||\n      !containerMetadata['LogOptions']\n    ) {\n      return Resource.EMPTY;\n    }\n\n    const containerArn = containerMetadata['ContainerARN']!;\n    const logOptions = containerMetadata['LogOptions'] as AwsLogOptions;\n\n    const logsRegion =\n      logOptions['awslogs-region'] ||\n      AwsEcsDetectorSync._getRegionFromArn(containerArn);\n\n    const awsAccount = AwsEcsDetectorSync._getAccountFromArn(containerArn);\n\n    const logsGroupName = logOptions['awslogs-group']!;\n    const logsGroupArn = `arn:aws:logs:${logsRegion}:${awsAccount}:log-group:${logsGroupName}`;\n    const logsStreamName = logOptions['awslogs-stream']!;\n    const logsStreamArn = `arn:aws:logs:${logsRegion}:${awsAccount}:log-group:${logsGroupName}:log-stream:${logsStreamName}`;\n\n    return new Resource({\n      [ATTR_AWS_LOG_GROUP_NAMES]: [logsGroupName],\n      [ATTR_AWS_LOG_GROUP_ARNS]: [logsGroupArn],\n      [ATTR_AWS_LOG_STREAM_NAMES]: [logsStreamName],\n      [ATTR_AWS_LOG_STREAM_ARNS]: [logsStreamArn],\n    });\n  }\n\n  private static _getAccountFromArn(containerArn: string): string {\n    const match = /arn:aws:ecs:[^:]+:([^:]+):.*/.exec(containerArn);\n    return match![1];\n  }\n\n  private static _getRegionFromArn(containerArn: string): string {\n    const match = /arn:aws:ecs:([^:]+):.*/.exec(containerArn);\n    return match![1];\n  }\n\n  private static _getUrlAsJson(url: string): Promise<any> {\n    return new Promise<string>((resolve, reject) => {\n      const request = http.get(url, (response: http.IncomingMessage) => {\n        if (response.statusCode && response.statusCode >= 400) {\n          reject(\n            new Error(\n              `Request to '${url}' failed with status ${response.statusCode}`\n            )\n          );\n        }\n        /*\n         * Concatenate the response out of chunks:\n         * https://nodejs.org/api/stream.html#stream_event_data\n         */\n        let responseBody = '';\n        response.on(\n          'data',\n          (chunk: Buffer) => (responseBody += chunk.toString())\n        );\n        // All the data has been read, resolve the Promise\n        response.on('end', () => resolve(responseBody));\n        /*\n         * https://nodejs.org/api/http.html#httprequesturl-options-callback, see the\n         * 'In the case of a premature connection close after the response is received'\n         * case\n         */\n        request.on('error', reject);\n      });\n\n      // Set an aggressive timeout to prevent lock-ups\n      request.setTimeout(HTTP_TIMEOUT_IN_MS, () => {\n        request.destroy();\n      });\n      // Connection error, disconnection, etc.\n      request.on('error', reject);\n      request.end();\n    }).then(responseBodyRaw => JSON.parse(responseBodyRaw));\n  }\n}\n\nexport const awsEcsDetectorSync = new AwsEcsDetectorSync();\n"]}