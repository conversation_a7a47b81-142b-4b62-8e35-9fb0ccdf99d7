{"version": 3, "file": "AwsEksDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsEksDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAQH,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAE1D;;;;;;;;;GASG;AACH;IAAA;QACE,2DAA2D;QAClD,gBAAW,GAAG,wBAAwB,CAAC;QACvC,wBAAmB,GAC1B,oDAAoD,CAAC;QAC9C,sBAAiB,GACxB,8DAA8D,CAAC;QACxD,eAAU,GAAG,IAAI,CAAC;IAK7B,CAAC;IAHC,+BAAM,GAAN,UAAO,OAAiC;QACtC,OAAO,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAC;IACtD,CAAC;IACH,qBAAC;AAAD,CAAC,AAZD,IAYC;;AAED,MAAM,CAAC,IAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Detector,\n  IResource,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\n\nimport { awsEksDetectorSync } from './AwsEksDetectorSync';\n\n/**\n * The AwsEksDetector can be used to detect if a process is running in AWS Elastic\n * Kubernetes and return a {@link Resource} populated with data about the Kubernetes\n * plugins of AWS X-Ray. Returns an empty Resource if detection fails.\n *\n * See https://docs.amazonaws.cn/en_us/xray/latest/devguide/xray-guide.pdf\n * for more details about detecting information for Elastic Kubernetes plugins\n *\n * @deprecated Use the new {@link AwsEksDetectorSync} class instead.\n */\nexport class AwsEksDetector implements Detector {\n  // NOTE: these readonly props are kept for testing purposes\n  readonly K8S_SVC_URL = 'kubernetes.default.svc';\n  readonly AUTH_CONFIGMAP_PATH =\n    '/api/v1/namespaces/kube-system/configmaps/aws-auth';\n  readonly CW_CONFIGMAP_PATH =\n    '/api/v1/namespaces/amazon-cloudwatch/configmaps/cluster-info';\n  readonly TIMEOUT_MS = 2000;\n\n  detect(_config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(awsEksDetectorSync.detect());\n  }\n}\n\nexport const awsEksDetector = new AwsEksDetector();\n"]}