{"version": 3, "file": "AwsEc2DetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/AwsEc2DetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAGL,QAAQ,GAGT,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,mBAAmB,EACnB,mBAAmB,EACnB,iBAAiB,EACjB,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,EACZ,cAAc,EACd,cAAc,EACd,wBAAwB,EACxB,4BAA4B,GAC7B,MAAM,YAAY,CAAC;AACpB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAE7B;;;;GAIG;AACH;IAAA;QACE;;;;WAIG;QACM,sBAAiB,GAAG,iBAAiB,CAAC;QACtC,qCAAgC,GAAG,mBAAmB,CAAC;QACvD,wCAAmC,GAC1C,4CAA4C,CAAC;QACtC,oCAA+B,GAAG,4BAA4B,CAAC;QAC/D,4BAAuB,GAAG,sCAAsC,CAAC;QACjE,8BAAyB,GAAG,0BAA0B,CAAC;QACvD,yBAAoB,GAAG,IAAI,CAAC;IAsHvC,CAAC;IApHC,mCAAM,GAAN,UAAO,OAAiC;QAAxC,iBAKC;QAJC,IAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;YACjE,OAAA,KAAI,CAAC,cAAc,EAAE;QAArB,CAAqB,CACtB,CAAC;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;OAMG;IACG,2CAAc,GAApB;;;;;;;;wBAEkB,qBAAM,IAAI,CAAC,WAAW,EAAE,EAAA;;wBAAhC,KAAK,GAAG,SAAwB;wBAEpC,qBAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAA;;wBAD5B,KACJ,SAAgC,EAD1B,SAAS,eAAA,EAAE,UAAU,gBAAA,EAAE,YAAY,kBAAA,EAAE,MAAM,YAAA,EAAE,gBAAgB,sBAAA;wBAEpD,qBAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAA;;wBAAvC,QAAQ,GAAG,SAA4B;wBAE7C;gCACE,GAAC,mBAAmB,IAAG,wBAAwB;gCAC/C,GAAC,mBAAmB,IAAG,4BAA4B;gCACnD,GAAC,qBAAqB,IAAG,SAAS;gCAClC,GAAC,iBAAiB,IAAG,MAAM;gCAC3B,GAAC,4BAA4B,IAAG,gBAAgB;gCAChD,GAAC,YAAY,IAAG,UAAU;gCAC1B,GAAC,cAAc,IAAG,YAAY;gCAC9B,GAAC,cAAc,IAAG,QAAQ;qCAC1B;;;wBAEF,sBAAO,EAAE,EAAC;;;;;KAEb;IAEa,wCAAW,GAAzB;;;;;;;wBACQ,OAAO,GAAG;4BACd,IAAI,EAAE,IAAI,CAAC,iBAAiB;4BAC5B,IAAI,EAAE,IAAI,CAAC,gCAAgC;4BAC3C,MAAM,EAAE,KAAK;4BACb,OAAO,EAAE,IAAI,CAAC,oBAAoB;4BAClC,OAAO;gCACL,GAAC,IAAI,CAAC,uBAAuB,IAAG,IAAI;mCACrC;yBACF,CAAC;wBACK,qBAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAA;4BAAvC,sBAAO,SAAgC,EAAC;;;;KACzC;IAEa,2CAAc,GAA5B,UAA6B,KAAa;;;;;;;wBAClC,OAAO,GAAG;4BACd,IAAI,EAAE,IAAI,CAAC,iBAAiB;4BAC5B,IAAI,EAAE,IAAI,CAAC,mCAAmC;4BAC9C,MAAM,EAAE,KAAK;4BACb,OAAO,EAAE,IAAI,CAAC,oBAAoB;4BAClC,OAAO;gCACL,GAAC,IAAI,CAAC,yBAAyB,IAAG,KAAK;mCACxC;yBACF,CAAC;wBACe,qBAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAA;;wBAA3C,QAAQ,GAAG,SAAgC;wBACjD,sBAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAC;;;;KAC7B;IAEa,uCAAU,GAAxB,UAAyB,KAAa;;;;;;;wBAC9B,OAAO,GAAG;4BACd,IAAI,EAAE,IAAI,CAAC,iBAAiB;4BAC5B,IAAI,EAAE,IAAI,CAAC,+BAA+B;4BAC1C,MAAM,EAAE,KAAK;4BACb,OAAO,EAAE,IAAI,CAAC,oBAAoB;4BAClC,OAAO;gCACL,GAAC,IAAI,CAAC,yBAAyB,IAAG,KAAK;mCACxC;yBACF,CAAC;wBACK,qBAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAA;4BAAvC,sBAAO,SAAgC,EAAC;;;;KACzC;IAED;;;;;OAKG;IACW,yCAAY,GAA1B,UAA2B,OAA4B;;;;gBACrD,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;wBACjC,IAAM,SAAS,GAAG,UAAU,CAAC;4BAC3B,GAAG,CAAC,KAAK,EAAE,CAAC;4BACZ,MAAM,CAAC,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC,CAAC;wBAC3D,CAAC,EAAE,KAAI,CAAC,oBAAoB,CAAC,CAAC;wBAE9B,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAA,GAAG;4BACnC,YAAY,CAAC,SAAS,CAAC,CAAC;4BAChB,IAAA,UAAU,GAAK,GAAG,WAAR,CAAS;4BAC3B,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;4BACxB,IAAI,OAAO,GAAG,EAAE,CAAC;4BACjB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,UAAA,KAAK,IAAI,OAAA,CAAC,OAAO,IAAI,KAAK,CAAC,EAAlB,CAAkB,CAAC,CAAC;4BAC5C,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE;gCACZ,IAAI,UAAU,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE;oCACvD,IAAI;wCACF,OAAO,CAAC,OAAO,CAAC,CAAC;qCAClB;oCAAC,OAAO,CAAC,EAAE;wCACV,MAAM,CAAC,CAAC,CAAC,CAAC;qCACX;iCACF;qCAAM;oCACL,MAAM,CACJ,IAAI,KAAK,CAAC,oCAAoC,GAAG,UAAU,CAAC,CAC7D,CAAC;iCACH;4BACH,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;wBACH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,UAAA,GAAG;4BACjB,YAAY,CAAC,SAAS,CAAC,CAAC;4BACxB,MAAM,CAAC,GAAG,CAAC,CAAC;wBACd,CAAC,CAAC,CAAC;wBACH,GAAG,CAAC,GAAG,EAAE,CAAC;oBACZ,CAAC,CAAC,EAAC;;;KACJ;IACH,yBAAC;AAAD,CAAC,AAnID,IAmIC;AAED,MAAM,CAAC,IAAM,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context } from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport {\n  DetectorSync,\n  IResource,\n  Resource,\n  ResourceAttributes,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\nimport {\n  ATTR_CLOUD_PROVIDER,\n  ATTR_CLOUD_PLATFORM,\n  ATTR_CLOUD_REGION,\n  ATTR_CLOUD_ACCOUNT_ID,\n  ATTR_CLOUD_AVAILABILITY_ZONE,\n  ATTR_HOST_ID,\n  ATTR_HOST_TYPE,\n  ATTR_HOST_NAME,\n  CLOUD_PROVIDER_VALUE_AWS,\n  CLOUD_PLATFORM_VALUE_AWS_EC2,\n} from '../semconv';\nimport * as http from 'http';\n\n/**\n * The AwsEc2DetectorSync can be used to detect if a process is running in AWS EC2\n * and return a {@link Resource} populated with metadata about the EC2\n * instance. Returns an empty Resource if detection fails.\n */\nclass AwsEc2DetectorSync implements DetectorSync {\n  /**\n   * See https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instance-identity-documents.html\n   * for documentation about the AWS instance identity document\n   * and standard of IMDSv2.\n   */\n  readonly AWS_IDMS_ENDPOINT = '***************';\n  readonly AWS_INSTANCE_TOKEN_DOCUMENT_PATH = '/latest/api/token';\n  readonly AWS_INSTANCE_IDENTITY_DOCUMENT_PATH =\n    '/latest/dynamic/instance-identity/document';\n  readonly AWS_INSTANCE_HOST_DOCUMENT_PATH = '/latest/meta-data/hostname';\n  readonly AWS_METADATA_TTL_HEADER = 'X-aws-ec2-metadata-token-ttl-seconds';\n  readonly AWS_METADATA_TOKEN_HEADER = 'X-aws-ec2-metadata-token';\n  readonly MILLISECOND_TIME_OUT = 5000;\n\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes = context.with(suppressTracing(context.active()), () =>\n      this._getAttributes()\n    );\n    return new Resource({}, attributes);\n  }\n\n  /**\n   * Attempts to connect and obtain an AWS instance Identity document. If the\n   * connection is successful it returns a promise containing a {@link ResourceAttributes}\n   * object with instance metadata. Returns a promise containing an\n   * empty {@link ResourceAttributes} if the connection or parsing of the identity\n   * document fails.\n   */\n  async _getAttributes(): Promise<ResourceAttributes> {\n    try {\n      const token = await this._fetchToken();\n      const { accountId, instanceId, instanceType, region, availabilityZone } =\n        await this._fetchIdentity(token);\n      const hostname = await this._fetchHost(token);\n\n      return {\n        [ATTR_CLOUD_PROVIDER]: CLOUD_PROVIDER_VALUE_AWS,\n        [ATTR_CLOUD_PLATFORM]: CLOUD_PLATFORM_VALUE_AWS_EC2,\n        [ATTR_CLOUD_ACCOUNT_ID]: accountId,\n        [ATTR_CLOUD_REGION]: region,\n        [ATTR_CLOUD_AVAILABILITY_ZONE]: availabilityZone,\n        [ATTR_HOST_ID]: instanceId,\n        [ATTR_HOST_TYPE]: instanceType,\n        [ATTR_HOST_NAME]: hostname,\n      };\n    } catch {\n      return {};\n    }\n  }\n\n  private async _fetchToken(): Promise<string> {\n    const options = {\n      host: this.AWS_IDMS_ENDPOINT,\n      path: this.AWS_INSTANCE_TOKEN_DOCUMENT_PATH,\n      method: 'PUT',\n      timeout: this.MILLISECOND_TIME_OUT,\n      headers: {\n        [this.AWS_METADATA_TTL_HEADER]: '60',\n      },\n    };\n    return await this._fetchString(options);\n  }\n\n  private async _fetchIdentity(token: string): Promise<any> {\n    const options = {\n      host: this.AWS_IDMS_ENDPOINT,\n      path: this.AWS_INSTANCE_IDENTITY_DOCUMENT_PATH,\n      method: 'GET',\n      timeout: this.MILLISECOND_TIME_OUT,\n      headers: {\n        [this.AWS_METADATA_TOKEN_HEADER]: token,\n      },\n    };\n    const identity = await this._fetchString(options);\n    return JSON.parse(identity);\n  }\n\n  private async _fetchHost(token: string): Promise<string> {\n    const options = {\n      host: this.AWS_IDMS_ENDPOINT,\n      path: this.AWS_INSTANCE_HOST_DOCUMENT_PATH,\n      method: 'GET',\n      timeout: this.MILLISECOND_TIME_OUT,\n      headers: {\n        [this.AWS_METADATA_TOKEN_HEADER]: token,\n      },\n    };\n    return await this._fetchString(options);\n  }\n\n  /**\n   * Establishes an HTTP connection to AWS instance document url.\n   * If the application is running on an EC2 instance, we should be able\n   * to get back a valid JSON document. Parses that document and stores\n   * the identity properties in a local map.\n   */\n  private async _fetchString(options: http.RequestOptions): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const timeoutId = setTimeout(() => {\n        req.abort();\n        reject(new Error('EC2 metadata api request timed out.'));\n      }, this.MILLISECOND_TIME_OUT);\n\n      const req = http.request(options, res => {\n        clearTimeout(timeoutId);\n        const { statusCode } = res;\n        res.setEncoding('utf8');\n        let rawData = '';\n        res.on('data', chunk => (rawData += chunk));\n        res.on('end', () => {\n          if (statusCode && statusCode >= 200 && statusCode < 300) {\n            try {\n              resolve(rawData);\n            } catch (e) {\n              reject(e);\n            }\n          } else {\n            reject(\n              new Error('Failed to load page, status code: ' + statusCode)\n            );\n          }\n        });\n      });\n      req.on('error', err => {\n        clearTimeout(timeoutId);\n        reject(err);\n      });\n      req.end();\n    });\n  }\n}\n\nexport const awsEc2DetectorSync = new AwsEc2DetectorSync();\n"]}