{"version": 3, "file": "semconv.js", "sourceRoot": "", "sources": ["../../src/semconv.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH;;;;GAIG;AAEH;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,qBAAqB,CAAC;AAE9D;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,uBAAuB,CAAC;AAElE;;;;GAIG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,oBAAoB,CAAC;AAE5D;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,kBAAkB,CAAC;AAExD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,qBAAqB,CAAC;AAE9D;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,uBAAuB,CAAC;AAElE;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,oBAAoB,CAAC;AAE5D;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,qBAAqB,CAAC;AAE9D;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,qBAAqB,CAAC;AAE9D;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,sBAAsB,CAAC;AAEhE;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,kBAAkB,CAAC;AAExD;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,yBAAyB,CAAC;AAEtE;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,gBAAgB,CAAC;AAEpD;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,gBAAgB,CAAC;AAEpD;;;;;;;;;GASG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,cAAc,CAAC;AAEhD;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,mBAAmB,CAAC;AAE1D;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,cAAc,CAAC;AAEhD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,gBAAgB,CAAC;AAEpD;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,CAAC,IAAM,cAAc,GAAG,WAAW,CAAC;AAE1C;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,eAAe,CAAC;AAElD;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,iBAAiB,CAAC;AAEtD;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,cAAc,CAAC;AAEhD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,YAAY,GAAG,SAAS,CAAC;AAEtC;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,cAAc,GAAG,WAAW,CAAC;AAE1C;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,cAAc,GAAG,WAAW,CAAC;AAE1C;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,kBAAkB,CAAC;AAExD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,qBAAqB,CAAC;AAE9D;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,mBAAmB,CAAC;AAE1D;;GAEG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,SAAS,CAAC;AAEtD;;GAEG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,SAAS,CAAC;AAEtD;;GAEG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,SAAS,CAAC;AAEtD;;GAEG;AACH,MAAM,CAAC,IAAM,0CAA0C,GACrD,uBAAuB,CAAC;AAE1B;;GAEG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAAG,YAAY,CAAC;AAE5D;;GAEG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,KAAK,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This file contains a copy of unstable semantic convention definitions\n * used by this package.\n * @see https://github.com/open-telemetry/opentelemetry-js/tree/main/semantic-conventions#unstable-semconv\n */\n\n/**\n * The ARN of an [ECS cluster](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/clusters.html).\n *\n * @example arn:aws:ecs:us-west-2:************:cluster/my-cluster\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_CLUSTER_ARN = 'aws.ecs.cluster.arn';\n\n/**\n * The Amazon Resource Name (ARN) of an [ECS container instance](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ECS_instances.html).\n *\n * @example arn:aws:ecs:us-west-1:************:container/*************-4f0e-acae-1a75b14fe4d9\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_CONTAINER_ARN = 'aws.ecs.container.arn';\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_LAUNCHTYPE = 'aws.ecs.launchtype';\n\n/**\n * The ARN of a running [ECS task](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs-account-settings.html#ecs-resource-ids).\n *\n * @example arn:aws:ecs:us-west-1:************:task/10838bed-421f-43ef-870a-f43feacbbb5b\n * @example arn:aws:ecs:us-west-1:************:task/my-cluster/task-id/23ebb8ac-c18f-46c6-8bbe-d55d0e37cfbd\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_TASK_ARN = 'aws.ecs.task.arn';\n\n/**\n * The family name of the [ECS task definition](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definitions.html) used to create the ECS task.\n *\n * @example opentelemetry-family\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_TASK_FAMILY = 'aws.ecs.task.family';\n\n/**\n * The revision for the task definition used to create the ECS task.\n *\n * @example 8\n * @example 26\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_TASK_REVISION = 'aws.ecs.task.revision';\n\n/**\n * The Amazon Resource Name(s) (ARN) of the AWS log group(s).\n *\n * @example [\"arn:aws:logs:us-west-1:123456789012:log-group:/aws/my/group:*\"]\n *\n * @note See the [log group ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LOG_GROUP_ARNS = 'aws.log.group.arns';\n\n/**\n * The name(s) of the AWS log group(s) an application is writing to.\n *\n * @example [\"/aws/lambda/my-function\", \"opentelemetry-service\"]\n *\n * @note Multiple log groups must be supported for cases like multi-container applications, where a single application has sidecar containers, and each write to their own log group.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LOG_GROUP_NAMES = 'aws.log.group.names';\n\n/**\n * The ARN(s) of the AWS log stream(s).\n *\n * @example [\"arn:aws:logs:us-west-1:123456789012:log-group:/aws/my/group:log-stream:logs/main/10838bed-421f-43ef-870a-f43feacbbb5b\"]\n *\n * @note See the [log stream ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format). One log group can contain several log streams, so these ARNs necessarily identify both a log group and a log stream.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LOG_STREAM_ARNS = 'aws.log.stream.arns';\n\n/**\n * The name(s) of the AWS log stream(s) an application is writing to.\n *\n * @example [\"logs/main/10838bed-421f-43ef-870a-f43feacbbb5b\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LOG_STREAM_NAMES = 'aws.log.stream.names';\n\n/**\n * The cloud account ID the resource is assigned to.\n *\n * @example ************\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_ACCOUNT_ID = 'cloud.account.id';\n\n/**\n * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n *\n * @example us-east-1c\n *\n * @note Availability zones are called \"zones\" on Alibaba Cloud and Google Cloud.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone';\n\n/**\n * The cloud platform in use.\n *\n * @note The prefix of the service **SHOULD** match the one specified in `cloud.provider`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_PLATFORM = 'cloud.platform';\n\n/**\n * Name of the cloud provider.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_PROVIDER = 'cloud.provider';\n\n/**\n * The geographical region the resource is running.\n *\n * @example us-central1\n * @example us-east-1\n *\n * @note Refer to your provider's docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/global-infrastructure/geographies/), [Google Cloud regions](https://cloud.google.com/about/locations), or [Tencent Cloud regions](https://www.tencentcloud.com/document/product/213/6091).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_REGION = 'cloud.region';\n\n/**\n * Cloud provider-specific native identifier of the monitored cloud resource (e.g. an [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html) on AWS, a [fully qualified resource ID](https://learn.microsoft.com/rest/api/resources/resources/get-by-id) on Azure, a [full resource name](https://cloud.google.com/apis/design/resource_names#full_resource_name) on GCP)\n *\n * @example arn:aws:lambda:REGION:ACCOUNT_ID:function:my-function\n * @example //run.googleapis.com/projects/PROJECT_ID/locations/LOCATION_ID/services/SERVICE_ID\n * @example /subscriptions/<SUBSCRIPTION_GUID>/resourceGroups/<RG>/providers/Microsoft.Web/sites/<FUNCAPP>/functions/<FUNC>\n *\n * @note On some cloud providers, it may not be possible to determine the full ID at startup,\n * so it may be necessary to set `cloud.resource_id` as a span attribute instead.\n *\n * The exact value to use for `cloud.resource_id` depends on the cloud provider.\n * The following well-known definitions **MUST** be used if you set this attribute and they apply:\n *\n *   - **AWS Lambda:** The function [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html).\n *     Take care not to use the \"invoked ARN\" directly but replace any\n *     [alias suffix](https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html)\n *     with the resolved function version, as the same runtime instance may be invocable with\n *     multiple different aliases.\n *   - **GCP:** The [URI of the resource](https://cloud.google.com/iam/docs/full-resource-names)\n *   - **Azure:** The [Fully Qualified Resource ID](https://docs.microsoft.com/rest/api/resources/resources/get-by-id) of the invoked function,\n *     *not* the function app, having the form\n *     `/subscriptions/<SUBSCRIPTION_GUID>/resourceGroups/<RG>/providers/Microsoft.Web/sites/<FUNCAPP>/functions/<FUNC>`.\n *     This means that a span attribute **MUST** be used, as an Azure function app can host multiple functions that would usually share\n *     a TracerProvider.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_RESOURCE_ID = 'cloud.resource_id';\n\n/**\n * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/containers/run/#container-identification). The UUID might be abbreviated.\n *\n * @example a3bf90e006b2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_ID = 'container.id';\n\n/**\n * Container name used by container runtime.\n *\n * @example opentelemetry-autoconf\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_NAME = 'container.name';\n\n/**\n * The name of the single function that this runtime instance executes.\n *\n * @example my-function\n * @example myazurefunctionapp/some-function-name\n *\n * @note This is the name of the function as configured/deployed on the FaaS\n * platform and is usually different from the name of the callback\n * function (which may be stored in the\n * [`code.namespace`/`code.function`](/docs/general/attributes.md#source-code-attributes)\n * span attributes).\n *\n * For some cloud providers, the above definition is ambiguous. The following\n * definition of function name **MUST** be used for this attribute\n * (and consequently the span name) for the listed cloud providers/products:\n *\n *   - **Azure:**  The full name `<FUNCAPP>/<FUNC>`, i.e., function app name\n *     followed by a forward slash followed by the function name (this form\n *     can also be seen in the resource JSON for the function).\n *     This means that a span attribute **MUST** be used, as an Azure function\n *     app can host multiple functions that would usually share\n *     a TracerProvider (see also the `cloud.resource_id` attribute).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_NAME = 'faas.name';\n\n/**\n * The execution environment ID as a string, that will be potentially reused for other invocations to the same function/function version.\n *\n * @example 2021/06/28/[$LATEST]2f399eb14537447da05ab2a2e39309de\n *\n * @note * **AWS Lambda:** Use the (full) log stream name.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_INSTANCE = 'faas.instance';\n\n/**\n * The amount of memory available to the serverless function converted to Bytes.\n *\n * @example 134217728\n *\n * @note It's recommended to set this attribute since e.g. too little memory can easily stop a Java AWS Lambda function from working correctly. On AWS Lambda, the environment variable `AWS_LAMBDA_FUNCTION_MEMORY_SIZE` provides this information (which must be multiplied by 1,048,576).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_MAX_MEMORY = 'faas.max_memory';\n\n/**\n * The immutable version of the function being executed.\n *\n * @example 26\n * @example pinkfroid-00002\n *\n * @note Depending on the cloud provider and platform, use:\n *\n *   - **AWS Lambda:** The [function version](https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html)\n *     (an integer represented as a decimal string).\n *   - **Google Cloud Run (Services):** The [revision](https://cloud.google.com/run/docs/managing/revisions)\n *     (i.e., the function name plus the revision suffix).\n *   - **Google Cloud Functions:** The value of the\n *     [`K_REVISION` environment variable](https://cloud.google.com/functions/docs/env-var#runtime_environment_variables_set_automatically).\n *   - **Azure Functions:** Not applicable. Do not set this attribute.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_VERSION = 'faas.version';\n\n/**\n * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider. For non-containerized systems, this should be the `machine-id`. See the table below for the sources to use to determine the `machine-id` based on operating system.\n *\n * @example fdbf79e8af94cb7f9e8df36789187052\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_ID = 'host.id';\n\n/**\n * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n *\n * @example opentelemetry-test\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_NAME = 'host.name';\n\n/**\n * Type of host. For Cloud, this must be the machine type.\n *\n * @example n1-standard-1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_TYPE = 'host.type';\n\n/**\n * The name of the cluster.\n *\n * @example opentelemetry-cluster\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CLUSTER_NAME = 'k8s.cluster.name';\n\n/**\n * The string ID of the service instance.\n *\n * @example 627cc493-f310-47de-96bd-71410b7dec09\n *\n * @note **MUST** be unique for each instance of the same `service.namespace,service.name` pair (in other words\n * `service.namespace,service.name,service.instance.id` triplet **MUST** be globally unique). The ID helps to\n * distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled\n * service).\n *\n * Implementations, such as SDKs, are recommended to generate a random Version 1 or Version 4 [RFC\n * 4122](https://www.ietf.org/rfc/rfc4122.txt) UUID, but are free to use an inherent unique ID as the source of\n * this value if stability is desirable. In that case, the ID **SHOULD** be used as source of a UUID Version 5 and\n * **SHOULD** use the following UUID as the namespace: `4d63009a-8d0f-11ee-aad7-4c796ed8e320`.\n *\n * UUIDs are typically recommended, as only an opaque value for the purposes of identifying a service instance is\n * needed. Similar to what can be seen in the man page for the\n * [`/etc/machine-id`](https://www.freedesktop.org/software/systemd/man/machine-id.html) file, the underlying\n * data, such as pod name and namespace should be treated as confidential, being the user's choice to expose it\n * or not via another resource attribute.\n *\n * For applications running behind an application server (like unicorn), we do not recommend using one identifier\n * for all processes participating in the application. Instead, it's recommended each division (e.g. a worker\n * thread in unicorn) to have its own instance.id.\n *\n * It's not recommended for a Collector to set `service.instance.id` if it can't unambiguously determine the\n * service instance that is generating that telemetry. For instance, creating an UUID based on `pod.name` will\n * likely be wrong, as the Collector might not know from which container within that pod the telemetry originated.\n * However, Collectors can set the `service.instance.id` if they can unambiguously determine the service instance\n * for that telemetry. This is typically the case for scraping receivers, as they know the target address and\n * port.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SERVICE_INSTANCE_ID = 'service.instance.id';\n\n/**\n * A namespace for `service.name`.\n *\n * @example Shop\n *\n * @note A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SERVICE_NAMESPACE = 'service.namespace';\n\n/**\n * Enum value \"aws_ec2\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_EC2 = 'aws_ec2';\n\n/**\n * Enum value \"aws_ecs\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_ECS = 'aws_ecs';\n\n/**\n * Enum value \"aws_eks\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_EKS = 'aws_eks';\n\n/**\n * Enum value \"aws_elastic_beanstalk\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK =\n  'aws_elastic_beanstalk';\n\n/**\n * Enum value \"aws_lambda\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_LAMBDA = 'aws_lambda';\n\n/**\n * Enum value \"aws\" for attribute {@link ATTR_CLOUD_PROVIDER}.\n */\nexport const CLOUD_PROVIDER_VALUE_AWS = 'aws';\n"]}