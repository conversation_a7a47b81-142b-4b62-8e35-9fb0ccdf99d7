{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { Span } from '@opentelemetry/api';\n\nexport interface Row {\n  get(columnName: string | number): any;\n\n  keys(): string[];\n\n  forEach(callback: (row: Row) => void): void;\n\n  values(): any[];\n\n  [key: string]: any;\n}\n\n// https://github.com/datastax/nodejs-driver/blob/d42176e4baa1cfc3df79699cc3b5d575c86e3cec/lib/types/index.d.ts#L323\nexport interface ResultSet {\n  rows: Row[];\n}\n\nexport interface ResponseHookInfo {\n  response: ResultSet;\n}\n\nexport interface CassandraDriverResponseCustomAttributeFunction {\n  (span: Span, responseInfo: ResponseHookInfo): void;\n}\n\nexport interface CassandraDriverInstrumentationConfig\n  extends InstrumentationConfig {\n  /**\n   * Include database statements with spans.\n   * These can contain sensitive information when using unescaped queries.\n   * @default false\n   */\n  enhancedDatabaseReporting?: boolean;\n  /**\n   * Max recorded query length.\n   * @default 65536\n   */\n  maxQueryLength?: number;\n  /**\n   * Function for adding custom attributes before response is handled.\n   * @param span the current span\n   * @param responseInfo array of the resulting rows. This will only return the first page of results\n   */\n  responseHook?: CassandraDriverResponseCustomAttributeFunction;\n}\n"]}