{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAO4B;AAC5B,oEAMwC;AAExC,8EAG6C;AAC7C,uCAAoC;AAIpC,MAAM,iBAAiB,GAAG,CAAC,YAAY,CAAC,CAAC;AAEzC,MAAa,8BAA+B,SAAQ,qCAAmB;IAGrE,YAAY,SAA+C,EAAE;QAC3D,KAAK,CAAC,iDAAiD,EAAE,iBAAO,EAAE,MAAM,CAAC,CAAC;IAC5E,CAAC;IAES,IAAI;QACZ,OAAO,IAAI,qDAAmC,CAC5C,kBAAkB,EAClB,iBAAiB,EACjB,CAAC,YAAY,EAAE,aAAa,EAAE,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,uCAAuC,aAAa,EAAE,CACvD,CAAC;YACF,8DAA8D;YAC9D,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,SAAgB,CAAC;YAEpD,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE;gBACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;aAClC;YAED,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAC/B;YAED,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBAC5B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;aAChC;YAED,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YACrD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;YAEvD,OAAO,YAAY,CAAC;QACtB,CAAC,EACD,CAAC,YAAY,EAAE,aAAa,EAAE,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,uCAAuC,aAAa,EAAE,CACvD,CAAC;YACF,8DAA8D;YAC9D,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,SAAgB,CAAC;YAEpD,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE;gBACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;aAClC;YAED,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;aAC/B;YAED,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBAC5B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;aAChC;QACH,CAAC,EACD;YACE,IAAI,+CAA6B,CAC/B,2CAA2C,EAC3C,iBAAiB,EACjB,SAAS,CAAC,EAAE;gBACV,IAAI,IAAA,2BAAS,EAAC,SAAS,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,EAAE;oBACvD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;iBACxD;gBAED,IAAI,CAAC,KAAK,CACR,SAAS,CAAC,SAAS,EACnB,mBAAmB,EACnB,IAAI,CAAC,2BAA2B,EAAE,CACnC,CAAC;gBACF,OAAO,SAAS,CAAC;YACnB,CAAC,EACD,SAAS,CAAC,EAAE;gBACV,IAAI,SAAS,KAAK,SAAS;oBAAE,OAAO;gBACpC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;YACzD,CAAC,CACF;SACF,CACF,CAAC;IACJ,CAAC;IAEO,kBAAkB;;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAA0C,CAAC;QACxE,OAAO,MAAA,MAAM,CAAC,cAAc,mCAAI,KAAK,CAAC;IACxC,CAAC;IAEO,yBAAyB;;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAA0C,CAAC;QACxE,OAAO,MAAA,MAAM,CAAC,yBAAyB,mCAAI,KAAK,CAAC;IACnD,CAAC;IAEO,kBAAkB;QACxB,OAAO,CACL,QAA0E,EAC1E,EAAE;YACF,MAAM,MAAM,GAAG,IAAI,CAAC;YACpB,OAAO,SAAS,cAAc,CAE5B,GAAG,IAAe;gBAElB,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBAEvE,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;gBAC1D,MAAM,WAAW,GAAG,IAAA,wCAAsB,EACxC,GAAG,EAAE;oBACH,OAAO,aAAO,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;wBACpC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC;gBACL,CAAC,EACD,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,EAAE;wBACT,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;qBACvB;gBACH,CAAC,CACF,CAAC;gBAEF,MAAM,cAAc,GAAG,WAAW,CAChC,IAAI,EACJ,WAAW,EACX,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;oBACf,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACzC,CAAC,CACF,CAAC;gBAEF,OAAO,aAAO,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACnD,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,2BAA2B;QACjC,OAAO,CAAC,QAAyC,EAAE,EAAE;YACnD,8DAA8D;YAC9D,OAAO,SAAS,uBAAuB,CAAY,GAAG,IAAe;gBACnE,MAAM,IAAI,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;gBAEjC,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;oBAC5C,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBAErC,IAAI,CAAC,YAAY,CAAC,yCAAkB,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;oBAElE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wBAChB,IAAI,CAAC,YAAY,CAAC,yCAAkB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;qBAC3D;iBACF;gBAED,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,OAAO,CAAC,QAAyC,EAAE,EAAE;YACnD,MAAM,MAAM,GAAG,IAAI,CAAC;YACpB,OAAO,SAAS,YAAY,CAE1B,GAAG,IAAe;gBAElB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAC3B,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,CAAC,OAAO,CAAC,EAAE,EAC/C,IAAI,CACL,CAAC;gBAEF,MAAM,YAAY,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;gBAE3D,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;oBAC/C,MAAM,gBAAgB,GAAG,IAAI,CAC3B,IAAI,CAAC,MAAM,GAAG,CAAC,CACkD,CAAC;oBAEpE,MAAM,eAAe,GAAG,UAEtB,GAAG,MAA2C;wBAE9C,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;wBAExB,IAAI,KAAK,EAAE;4BACT,IAAI,CAAC,SAAS,CAAC;gCACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gCAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;6BACvB,CAAC,CAAC;4BACH,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;yBAC7B;wBAED,IAAI,CAAC,GAAG,EAAE,CAAC;wBAEX,OAAO,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oBAC9C,CAAC,CAAC;oBAEF,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC;oBAExC,OAAO,aAAO,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE;wBACrC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC;iBACJ;gBAED,MAAM,YAAY,GAAG,IAAA,wCAAsB,EACzC,GAAG,EAAE;oBACH,OAAO,QAAQ,CAAC,KAAK,CACnB,IAAI,EACJ,IAAI,CACuC,CAAC;gBAChD,CAAC,EACD,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,EAAE;wBACT,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;qBACvB;gBACH,CAAC,CACF,CAAC;gBAEF,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;gBAEvD,OAAO,aAAO,CAAC,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;YACpD,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,OAAO,CAAC,QAA8C,EAAE,EAAE;YACxD,MAAM,MAAM,GAAG,IAAI,CAAC;YACpB,OAAO,SAAS,aAAa,CAE3B,GAAG,IAAe;gBAElB,mFAAmF;gBACnF,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;gBAEtD,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAEzB,MAAM,OAAO,GAAG,CAAC,KAAY,EAAE,EAAE;oBAC/B,IAAI,KAAK,EAAE;wBACT,IAAI,CAAC,SAAS,CAAC;4BACb,IAAI,EAAE,oBAAc,CAAC,KAAK;4BAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;yBACvB,CAAC,CAAC;wBACH,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;qBAC7B;oBACD,IAAI,CAAC,GAAG,EAAE,CAAC;gBACb,CAAC,CAAC;gBAEF,IAAI,QAAQ,KAAK,SAAS,EAAE;oBAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;iBACnB;qBAAM,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;oBACzC,MAAM,eAAe,GAAG,UAAyB,GAAU;wBACzD,OAAO,CAAC,GAAG,CAAC,CAAC;wBACb,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;oBAClC,CAAC,CAAC;oBACF,IAAI,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;iBAC3B;gBAED,OAAO,IAAA,wCAAsB,EAC3B,GAAG,EAAE;oBACH,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACpC,CAAC,EACD,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,EAAE;wBACT,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;qBACvB;gBACH,CAAC,CACF,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,SAAS,CACf,EAAE,EAAE,EAAE,KAAK,EAAmC,EAC9C,MAA8B;;QAE9B,MAAM,UAAU,GAAmB;YACjC,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,SAAS;SACzD,CAAC;QAEF,IAAI,IAAI,CAAC,yBAAyB,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE;YAC3D,MAAM,SAAS,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAClE,UAAU,CAAC,yCAAkB,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC;SACzD;QAED,8DAA8D;QAC9D,MAAM,IAAI,GAAG,MAAA,MAAC,MAAc,CAAC,OAAO,0CAAE,WAAW,0CAAE,QAAQ,CAAC;QAE5D,IAAI,IAAI,EAAE;YACR,UAAU,CAAC,yCAAkB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;SAC/C;QAED,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,UAAU,CAAC,yCAAkB,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;SAC1D;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,EAAE,EAAE;YACrD,IAAI,EAAE,cAAQ,CAAC,MAAM;YACrB,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,IAAU,EAAE,QAAmB;QACvD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC9B,OAAO;SACR;QAED,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAa,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAC9D,CAAC,CAAC,EAAE;YACF,IAAI,CAAC,EAAE;gBACL,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;aAC3C;QACH,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;CACF;AArTD,wEAqTC;AAED,SAAS,QAAQ,CAAC,IAAU,EAAE,KAAY;IACxC,IAAI,CAAC,SAAS,CAAC;QACb,IAAI,EAAE,oBAAc,CAAC,KAAK;QAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;KACvB,CAAC,CAAC;IACH,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;AACb,CAAC;AAED,SAAS,cAAc,CAAC,OAA0C;IAChE,OAAO,OAAO;SACX,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SAC/D,IAAI,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC;AAED,SAAS,WAAW,CAClB,IAAU,EACV,OAAmB,EACnB,eAAiD;IAEjD,OAAO,OAAO;SACX,IAAI,CAAC,MAAM,CAAC,EAAE;QACb,OAAO,IAAI,OAAO,CAAI,OAAO,CAAC,EAAE;YAC9B,IAAI,eAAe,EAAE;gBACnB,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aAC/B;YACD,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;QACtB,OAAO,IAAI,OAAO,CAAI,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;YAClC,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,aAAa,CAAC,KAAc,EAAE,cAAsB;IAC3D,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;AACjD,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context,\n  trace,\n  Span,\n  SpanAttributes,\n  SpanKind,\n  SpanStatusCode,\n} from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { CassandraDriverInstrumentationConfig, ResultSet } from './types';\nimport {\n  SemanticAttributes,\n  DbSystemValues,\n} from '@opentelemetry/semantic-conventions';\nimport { VERSION } from './version';\nimport { EventEmitter } from 'events';\nimport type * as CassandraDriver from 'cassandra-driver';\n\nconst supportedVersions = ['>=4.4 <5.0'];\n\nexport class CassandraDriverInstrumentation extends InstrumentationBase {\n  protected override _config!: CassandraDriverInstrumentationConfig;\n\n  constructor(config: CassandraDriverInstrumentationConfig = {}) {\n    super('@opentelemetry/instrumentation-cassandra-driver', VERSION, config);\n  }\n\n  protected init() {\n    return new InstrumentationNodeModuleDefinition<any>(\n      'cassandra-driver',\n      supportedVersions,\n      (driverModule, moduleVersion) => {\n        this._diag.debug(\n          `Applying patch for cassandra-driver@${moduleVersion}`\n        );\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const Client = driverModule.Client.prototype as any;\n\n        if (isWrapped(Client['_execute'])) {\n          this._unwrap(Client, '_execute');\n        }\n\n        if (isWrapped(Client.batch)) {\n          this._unwrap(Client, 'batch');\n        }\n\n        if (isWrapped(Client.stream)) {\n          this._unwrap(Client, 'stream');\n        }\n\n        this._wrap(Client, '_execute', this._getPatchedExecute());\n        this._wrap(Client, 'batch', this._getPatchedBatch());\n        this._wrap(Client, 'stream', this._getPatchedStream());\n\n        return driverModule;\n      },\n      (driverModule, moduleVersion) => {\n        this._diag.debug(\n          `Removing patch for cassandra-driver@${moduleVersion}`\n        );\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const Client = driverModule.Client.prototype as any;\n\n        if (isWrapped(Client['_execute'])) {\n          this._unwrap(Client, '_execute');\n        }\n\n        if (isWrapped(Client.batch)) {\n          this._unwrap(Client, 'batch');\n        }\n\n        if (isWrapped(Client.stream)) {\n          this._unwrap(Client, 'stream');\n        }\n      },\n      [\n        new InstrumentationNodeModuleFile(\n          'cassandra-driver/lib/request-execution.js',\n          supportedVersions,\n          execution => {\n            if (isWrapped(execution.prototype['_sendOnConnection'])) {\n              this._unwrap(execution.prototype, '_sendOnConnection');\n            }\n\n            this._wrap(\n              execution.prototype,\n              '_sendOnConnection',\n              this._getPatchedSendOnConnection()\n            );\n            return execution;\n          },\n          execution => {\n            if (execution === undefined) return;\n            this._unwrap(execution.prototype, '_sendOnConnection');\n          }\n        ),\n      ]\n    );\n  }\n\n  private _getMaxQueryLength(): number {\n    const config = this.getConfig() as CassandraDriverInstrumentationConfig;\n    return config.maxQueryLength ?? 65536;\n  }\n\n  private _shouldIncludeDbStatement(): boolean {\n    const config = this.getConfig() as CassandraDriverInstrumentationConfig;\n    return config.enhancedDatabaseReporting ?? false;\n  }\n\n  private _getPatchedExecute() {\n    return (\n      original: (...args: unknown[]) => Promise<CassandraDriver.types.ResultSet>\n    ) => {\n      const plugin = this;\n      return function patchedExecute(\n        this: CassandraDriver.Client,\n        ...args: unknown[]\n      ) {\n        const span = plugin.startSpan({ op: 'execute', query: args[0] }, this);\n\n        const execContext = trace.setSpan(context.active(), span);\n        const execPromise = safeExecuteInTheMiddle(\n          () => {\n            return context.with(execContext, () => {\n              return original.apply(this, args);\n            });\n          },\n          error => {\n            if (error) {\n              failSpan(span, error);\n            }\n          }\n        );\n\n        const wrappedPromise = wrapPromise(\n          span,\n          execPromise,\n          (span, result) => {\n            plugin._callResponseHook(span, result);\n          }\n        );\n\n        return context.bind(execContext, wrappedPromise);\n      };\n    };\n  }\n\n  private _getPatchedSendOnConnection() {\n    return (original: (...args: unknown[]) => unknown) => {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return function patchedSendOnConnection(this: any, ...args: unknown[]) {\n        const span = trace.getSpan(context.active());\n        const conn = this['_connection'];\n\n        if (span !== undefined && conn !== undefined) {\n          const port = parseInt(conn.port, 10);\n\n          span.setAttribute(SemanticAttributes.NET_PEER_NAME, conn.address);\n\n          if (!isNaN(port)) {\n            span.setAttribute(SemanticAttributes.NET_PEER_PORT, port);\n          }\n        }\n\n        return original.apply(this, args);\n      };\n    };\n  }\n\n  private _getPatchedBatch() {\n    return (original: (...args: unknown[]) => unknown) => {\n      const plugin = this;\n      return function patchedBatch(\n        this: CassandraDriver.Client,\n        ...args: unknown[]\n      ) {\n        const queries = Array.isArray(args[0]) ? args[0] : [];\n        const span = plugin.startSpan(\n          { op: 'batch', query: combineQueries(queries) },\n          this\n        );\n\n        const batchContext = trace.setSpan(context.active(), span);\n\n        if (typeof args[args.length - 1] === 'function') {\n          const originalCallback = args[\n            args.length - 1\n          ] as CassandraDriver.ValueCallback<CassandraDriver.types.ResultSet>;\n\n          const patchedCallback = function (\n            this: unknown,\n            ...cbArgs: Parameters<typeof originalCallback>\n          ) {\n            const error = cbArgs[0];\n\n            if (error) {\n              span.setStatus({\n                code: SpanStatusCode.ERROR,\n                message: error.message,\n              });\n              span.recordException(error);\n            }\n\n            span.end();\n\n            return originalCallback.apply(this, cbArgs);\n          };\n\n          args[args.length - 1] = patchedCallback;\n\n          return context.with(batchContext, () => {\n            return original.apply(this, args);\n          });\n        }\n\n        const batchPromise = safeExecuteInTheMiddle(\n          () => {\n            return original.apply(\n              this,\n              args\n            ) as Promise<CassandraDriver.types.ResultSet>;\n          },\n          error => {\n            if (error) {\n              failSpan(span, error);\n            }\n          }\n        );\n\n        const wrappedPromise = wrapPromise(span, batchPromise);\n\n        return context.bind(batchContext, wrappedPromise);\n      };\n    };\n  }\n\n  private _getPatchedStream() {\n    return (original: (...args: unknown[]) => EventEmitter) => {\n      const plugin = this;\n      return function patchedStream(\n        this: CassandraDriver.Client,\n        ...args: unknown[]\n      ) {\n        // Since stream internally uses execute, there is no need to add DB_STATEMENT twice\n        const span = plugin.startSpan({ op: 'stream' }, this);\n\n        const callback = args[3];\n\n        const endSpan = (error: Error) => {\n          if (error) {\n            span.setStatus({\n              code: SpanStatusCode.ERROR,\n              message: error.message,\n            });\n            span.recordException(error);\n          }\n          span.end();\n        };\n\n        if (callback === undefined) {\n          args[3] = endSpan;\n        } else if (typeof callback === 'function') {\n          const wrappedCallback = function (this: unknown, err: Error) {\n            endSpan(err);\n            return callback.call(this, err);\n          };\n          args[3] = wrappedCallback;\n        }\n\n        return safeExecuteInTheMiddle(\n          () => {\n            return original.apply(this, args);\n          },\n          error => {\n            if (error) {\n              failSpan(span, error);\n            }\n          }\n        );\n      };\n    };\n  }\n\n  private startSpan(\n    { op, query }: { op: string; query?: unknown },\n    client: CassandraDriver.Client\n  ): Span {\n    const attributes: SpanAttributes = {\n      [SemanticAttributes.DB_SYSTEM]: DbSystemValues.CASSANDRA,\n    };\n\n    if (this._shouldIncludeDbStatement() && query !== undefined) {\n      const statement = truncateQuery(query, this._getMaxQueryLength());\n      attributes[SemanticAttributes.DB_STATEMENT] = statement;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const user = (client as any).options?.credentials?.username;\n\n    if (user) {\n      attributes[SemanticAttributes.DB_USER] = user;\n    }\n\n    if (client.keyspace) {\n      attributes[SemanticAttributes.DB_NAME] = client.keyspace;\n    }\n\n    return this.tracer.startSpan(`cassandra-driver.${op}`, {\n      kind: SpanKind.CLIENT,\n      attributes,\n    });\n  }\n\n  private _callResponseHook(span: Span, response: ResultSet) {\n    if (!this._config.responseHook) {\n      return;\n    }\n\n    safeExecuteInTheMiddle(\n      () => this._config.responseHook!(span, { response: response }),\n      e => {\n        if (e) {\n          this._diag.error('responseHook error', e);\n        }\n      },\n      true\n    );\n  }\n}\n\nfunction failSpan(span: Span, error: Error) {\n  span.setStatus({\n    code: SpanStatusCode.ERROR,\n    message: error.message,\n  });\n  span.recordException(error);\n  span.end();\n}\n\nfunction combineQueries(queries: Array<string | { query: string }>) {\n  return queries\n    .map(query => (typeof query === 'string' ? query : query.query))\n    .join('\\n');\n}\n\nfunction wrapPromise<T>(\n  span: Span,\n  promise: Promise<T>,\n  successCallback?: (span: Span, result: T) => void\n): Promise<T> {\n  return promise\n    .then(result => {\n      return new Promise<T>(resolve => {\n        if (successCallback) {\n          successCallback(span, result);\n        }\n        span.end();\n        resolve(result);\n      });\n    })\n    .catch((error: Error) => {\n      return new Promise<T>((_, reject) => {\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: error.message,\n        });\n        span.recordException(error);\n        span.end();\n        reject(error);\n      });\n    });\n}\n\nfunction truncateQuery(query: unknown, maxQueryLength: number) {\n  return String(query).substr(0, maxQueryLength);\n}\n"]}