{"version": 3, "file": "sdk-info.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/sdk-info.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,2CAAwC;AACxC,8EAG6C;AAE7C,0CAA0C;AAC7B,QAAA,QAAQ,GAAG;IACtB,CAAC,iDAA0B,CAAC,kBAAkB,CAAC,EAAE,eAAe;IAChE,CAAC,iDAA0B,CAAC,oBAAoB,CAAC,EAAE,SAAS;IAC5D,CAAC,iDAA0B,CAAC,sBAAsB,CAAC,EACjD,iDAA0B,CAAC,KAAK;IAClC,CAAC,iDAA0B,CAAC,qBAAqB,CAAC,EAAE,iBAAO;CAC5D,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VERSION } from '../../version';\nimport {\n  TelemetrySdkLanguageValues,\n  SemanticResourceAttributes,\n} from '@opentelemetry/semantic-conventions';\n\n/** Constants describing the SDK in use */\nexport const SDK_INFO = {\n  [SemanticResourceAttributes.TELEMETRY_SDK_NAME]: 'opentelemetry',\n  [SemanticResourceAttributes.PROCESS_RUNTIME_NAME]: 'browser',\n  [SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]:\n    TelemetrySdkLanguageValues.WEBJS,\n  [SemanticResourceAttributes.TELEMETRY_SDK_VERSION]: VERSION,\n};\n"]}