{"version": 3, "file": "RandomIdGenerator.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/RandomIdGenerator.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,MAAM,aAAa,GAAG,CAAC,CAAC;AACxB,MAAM,cAAc,GAAG,EAAE,CAAC;AAE1B;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAA9B;QACE;;;WAGG;QACH,oBAAe,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;QAEjD;;;WAGG;QACH,mBAAc,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;CAAA;AAED,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACzD,SAAS,cAAc,CAAC,KAAa;IACnC,OAAO,SAAS,UAAU;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,wDAAwD;YACxD,gIAAgI;YAChI,aAAa,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;SACrE;QAED,sFAAsF;QACtF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC9B,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBACxB,MAAM;aACP;iBAAM,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,EAAE;gBAC1B,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9B;SACF;QAED,OAAO,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { IdGenerator } from '../../trace/IdGenerator';\nconst SPAN_ID_BYTES = 8;\nconst TRACE_ID_BYTES = 16;\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n */\nexport class RandomIdGenerator implements IdGenerator {\n  /**\n   * Returns a random 16-byte trace ID formatted/encoded as a 32 lowercase hex\n   * characters corresponding to 128 bits.\n   */\n  generateTraceId = getIdGenerator(TRACE_ID_BYTES);\n\n  /**\n   * Returns a random 8-byte span ID formatted/encoded as a 16 lowercase hex\n   * characters corresponding to 64 bits.\n   */\n  generateSpanId = getIdGenerator(SPAN_ID_BYTES);\n}\n\nconst SHARED_BUFFER = Buffer.allocUnsafe(TRACE_ID_BYTES);\nfunction getIdGenerator(bytes: number): () => string {\n  return function generateId() {\n    for (let i = 0; i < bytes / 4; i++) {\n      // unsigned right shift drops decimal part of the number\n      // it is required because if a number between 2**32 and 2**32 - 1 is generated, an out of range error is thrown by writeUInt32BE\n      SHARED_BUFFER.writeUInt32BE((Math.random() * 2 ** 32) >>> 0, i * 4);\n    }\n\n    // If buffer is all 0, set the last byte to 1 to guarantee a valid w3c id is generated\n    for (let i = 0; i < bytes; i++) {\n      if (SHARED_BUFFER[i] > 0) {\n        break;\n      } else if (i === bytes - 1) {\n        SHARED_BUFFER[bytes - 1] = 1;\n      }\n    }\n\n    return SHARED_BUFFER.toString('hex', 0, bytes);\n  };\n}\n"]}