import { Resource } from '../../Resource';
import { DetectorSync } from '../../types';
import { ResourceDetectionConfig } from '../../config';
/**
 * HostDetectorSync detects the resources related to the host current process is
 * running on. Currently only non-cloud-based attributes are included.
 */
declare class HostDetectorSync implements DetectorSync {
    detect(_config?: ResourceDetectionConfig): Resource;
    private _getAsyncAttributes;
}
export declare const hostDetectorSync: HostDetectorSync;
export {};
//# sourceMappingURL=HostDetectorSync.d.ts.map