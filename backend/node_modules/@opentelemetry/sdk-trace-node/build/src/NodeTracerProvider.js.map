{"version": 3, "file": "NodeTracerProvider.js", "sourceRoot": "", "sources": ["../../src/NodeTracerProvider.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4EAG4C;AAC5C,gEAA8E;AAC9E,kEAIuC;AACvC,iCAAiC;AAEjC,wEAAoE;AAEpE;;;;;;GAMG;AACH,MAAa,kBAAmB,SAAQ,oCAAmB;IAkBzD,YAAY,SAA2B,EAAE;QACvC,KAAK,CAAC,MAAM,CAAC,CAAC;IAChB,CAAC;IAEQ,QAAQ,CAAC,SAAgC,EAAE;QAClD,IAAI,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE;YACvC,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;gBAC1D,CAAC,CAAC,qDAA+B;gBACjC,CAAC,CAAC,8CAAwB,CAAC;YAC7B,MAAM,CAAC,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;YAC7C,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;SAChC;QAED,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;;AAhCH,gDAiCC;AAhCoC,yCAAsB,GAAG,IAAI,GAAG,CAGjE;IACA,GAAG,oCAAmB,CAAC,sBAAsB;IAC7C;QACE,IAAI;QACJ,GAAG,EAAE,CACH,IAAI,4BAAY,CAAC,EAAE,cAAc,EAAE,gCAAgB,CAAC,aAAa,EAAE,CAAC;KACvE;IACD;QACE,SAAS;QACT,GAAG,EAAE,CAAC,IAAI,4BAAY,CAAC,EAAE,cAAc,EAAE,gCAAgB,CAAC,YAAY,EAAE,CAAC;KAC1E;IACD,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,oCAAgB,EAAE,CAAC;CACzC,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  AsyncHooksContextManager,\n  AsyncLocalStorageContextManager,\n} from '@opentelemetry/context-async-hooks';\nimport { B3Propagator, B3InjectEncoding } from '@opentelemetry/propagator-b3';\nimport {\n  BasicTracerProvider,\n  PROPAGATOR_FACTORY,\n  SDKRegistrationConfig,\n} from '@opentelemetry/sdk-trace-base';\nimport * as semver from 'semver';\nimport { NodeTracerConfig } from './config';\nimport { JaegerPropagator } from '@opentelemetry/propagator-jaeger';\n\n/**\n * Register this TracerProvider for use with the OpenTelemetry API.\n * Undefined values may be replaced with defaults, and\n * null values will be skipped.\n *\n * @param config Configuration object for SDK registration\n */\nexport class NodeTracerProvider extends BasicTracerProvider {\n  protected static override readonly _registeredPropagators = new Map<\n    string,\n    PROPAGATOR_FACTORY\n  >([\n    ...BasicTracerProvider._registeredPropagators,\n    [\n      'b3',\n      () =>\n        new B3Propagator({ injectEncoding: B3InjectEncoding.SINGLE_HEADER }),\n    ],\n    [\n      'b3multi',\n      () => new B3Propagator({ injectEncoding: B3InjectEncoding.MULTI_HEADER }),\n    ],\n    ['jaeger', () => new JaegerPropagator()],\n  ]);\n\n  constructor(config: NodeTracerConfig = {}) {\n    super(config);\n  }\n\n  override register(config: SDKRegistrationConfig = {}): void {\n    if (config.contextManager === undefined) {\n      const ContextManager = semver.gte(process.version, '14.8.0')\n        ? AsyncLocalStorageContextManager\n        : AsyncHooksContextManager;\n      config.contextManager = new ContextManager();\n      config.contextManager.enable();\n    }\n\n    super.register(config);\n  }\n}\n"]}