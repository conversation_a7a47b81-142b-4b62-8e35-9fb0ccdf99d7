export declare enum TLSAttributes {
    PROTOCOL = "tls.protocol",
    AUTHORIZED = "tls.authorized",
    CIPHER_NAME = "tls.cipher.name",
    CIPHER_VERSION = "tls.cipher.version",
    CERTIFICATE_FINGERPRINT = "tls.certificate.fingerprint",
    CERTIFICATE_SERIAL_NUMBER = "tls.certificate.serialNumber",
    CERTIFICATE_VALID_FROM = "tls.certificate.validFrom",
    CERTIFICATE_VALID_TO = "tls.certificate.validTo",
    ALPN_PROTOCOL = "tls.alpnProtocol"
}
//# sourceMappingURL=types.d.ts.map