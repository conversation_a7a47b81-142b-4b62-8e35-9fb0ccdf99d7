"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TLSAttributes = void 0;
/* The following attributes are not offical, see open-telemetry/opentelemetry-specification#1652 */
var TLSAttributes;
(function (TLSAttributes) {
    TLSAttributes["PROTOCOL"] = "tls.protocol";
    TLSAttributes["AUTHORIZED"] = "tls.authorized";
    TLSAttributes["CIPHER_NAME"] = "tls.cipher.name";
    TLSAttributes["CIPHER_VERSION"] = "tls.cipher.version";
    TLSAttributes["CERTIFICATE_FINGERPRINT"] = "tls.certificate.fingerprint";
    TLSAttributes["CERTIFICATE_SERIAL_NUMBER"] = "tls.certificate.serialNumber";
    TLSAttributes["CERTIFICATE_VALID_FROM"] = "tls.certificate.validFrom";
    TLSAttributes["CERTIFICATE_VALID_TO"] = "tls.certificate.validTo";
    TLSAttributes["ALPN_PROTOCOL"] = "tls.alpnProtocol";
})(TLSAttributes = exports.TLSAttributes || (exports.TLSAttributes = {}));
//# sourceMappingURL=types.js.map