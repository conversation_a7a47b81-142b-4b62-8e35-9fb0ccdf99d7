{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAgF;AAChF,oEAMwC;AACxC,8EAG6C;AAC7C,mCAAwC;AACxC,qDAAuE;AACvE,mCAA2D;AAC3D,uCAAoC;AAEpC,6BAAgC;AAEhC,MAAa,kBAAmB,SAAQ,qCAAwB;IAC9D,YAAY,OAA+B;QACzC,KAAK,CAAC,oCAAoC,EAAE,iBAAO,EAAE,OAAO,CAAC,CAAC;IAChE,CAAC;IAED,IAAI;QACF,OAAO;YACL,IAAI,qDAAmC,CACrC,KAAK,EACL,CAAC,GAAG,CAAC,EACL,aAAa,CAAC,EAAE;gBACd,UAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;gBAC5C,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;oBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;iBACzD;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,SAAS;gBACT,8DAA8D;gBAC9D,IAAI,CAAC,kBAAkB,EAAS,CACjC,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,aAAa,CAAC,EAAE;gBACd,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,UAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;gBAC7C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAC1D,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAEO,kBAAkB;QACxB,OAAO,CAAC,QAAsC,EAAE,EAAE;YAChD,MAAM,MAAM,GAAG,IAAI,CAAC;YACpB,OAAO,SAAS,cAAc,CAAe,GAAG,IAAe;gBAC7D,MAAM,OAAO,GAAG,IAAA,yBAAiB,EAAC,IAAI,CAAC,CAAC;gBAExC,MAAM,IAAI,GACR,IAAI,YAAY,eAAS;oBACvB,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC;oBACrC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAEvC,OAAO,IAAA,wCAAsB,EAC3B,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAChC,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,KAAK,SAAS,EAAE;wBACvB,IAAI,CAAC,SAAS,CAAC;4BACb,IAAI,EAAE,oBAAc,CAAC,KAAK;4BAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;yBACvB,CAAC,CAAC;wBACH,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;wBAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;qBACZ;gBACH,CAAC,CACF,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,UAAU,CAChB,OAA6C,EAC7C,MAAc;QAEd,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;SACvC;QACD,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAC5C;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;IAEO,aAAa,CACnB,OAA6C,EAC7C,MAAiB;QAEjB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAErD,MAAM,OAAO,GAAG,aAAO,CAAC,IAAI,CAC1B,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,EACxC,GAAG,EAAE;YACH,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1C,CAAC,CACF,CAAC;QAEF,MAAM,mBAAmB,GAAG,GAAG,EAAE;YAC/B,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,UAAU,GAAG;gBACjB,CAAC,qBAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC;gBAC1C,CAAC,qBAAa,CAAC,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;gBACrD,CAAC,qBAAa,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,IAAI;gBACxC,CAAC,qBAAa,CAAC,cAAc,CAAC,EAAE,MAAM,CAAC,OAAO;gBAC9C,CAAC,qBAAa,CAAC,uBAAuB,CAAC,EAAE,eAAe,CAAC,WAAW;gBACpE,CAAC,qBAAa,CAAC,yBAAyB,CAAC,EAAE,eAAe,CAAC,YAAY;gBACvE,CAAC,qBAAa,CAAC,sBAAsB,CAAC,EAAE,eAAe,CAAC,UAAU;gBAClE,CAAC,qBAAa,CAAC,oBAAoB,CAAC,EAAE,eAAe,CAAC,QAAQ;gBAC9D,CAAC,qBAAa,CAAC,aAAa,CAAC,EAAE,EAAE;aAClC,CAAC;YACF,IAAI,MAAM,CAAC,YAAY,EAAE;gBACvB,UAAU,CAAC,qBAAa,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC;aAC/D;YAED,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAClC,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC,CAAC;QAEF,MAAM,oBAAoB,GAAG,CAAC,CAAQ,EAAE,EAAE;YACxC,OAAO,CAAC,SAAS,CAAC;gBAChB,IAAI,EAAE,oBAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,CAAC,CAAC,OAAO;aACnB,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC,CAAC;QAEF,6EAA6E;QAC7E,MAAM,CAAC,mBAAmB,CAAC,4BAAW,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;QAC5E,MAAM,CAAC,IAAI,CAAC,4BAAW,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;QAErD,MAAM,sBAAsB,GAAG,GAAG,EAAE;YAClC,MAAM,CAAC,cAAc,CAAC,4BAAW,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;YACvE,MAAM,CAAC,cAAc,CAAC,4BAAW,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;YAC/D,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE;gBACjC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;aACtD;QACH,CAAC,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI;YAClB,4BAAW,CAAC,KAAK;YACjB,4BAAW,CAAC,KAAK;YACjB,4BAAW,CAAC,cAAc;SAC3B,EAAE;YACD,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;SAC5C;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,kFAAkF;IAC1E,iBAAiB,CAAC,MAAc;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE9C,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEhC,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,aAAa,CAAC,OAA0B,EAAE,MAAc;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE;YAChD,UAAU,EAAE;gBACV,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,qBAAa;gBACjD,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC,IAAI;aACjD;SACF,CAAC,CAAC;QAEH,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEhC,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,aAAa,CAAC,OAA0B,EAAE,MAAc;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE;YAChD,UAAU,EAAE;gBACV,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,yCAAkB,CAAC,MAAM;gBAC7D,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC,IAAI;gBAChD,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC,IAAI;aACjD;SACF,CAAC,CAAC;QAEH,iBAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA/KD,gDA+KC;AAED,MAAM,aAAa,GAAG;IACpB,4BAAW,CAAC,KAAK;IACjB,4BAAW,CAAC,OAAO;IACnB,4BAAW,CAAC,KAAK;CAClB,CAAC;AAEF,SAAS,cAAc,CAAC,IAAU;IAChC,OAAO,GAAG,EAAE;QACV,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAU;IAClC,OAAO,CAAC,CAAQ,EAAE,EAAE;QAClB,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,oBAAc,CAAC,KAAK;YAC1B,OAAO,EAAE,CAAC,CAAC,OAAO;SACnB,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CACxB,MAAc,EACd,IAAU,EACV,EAAE,cAAc,GAAG,KAAK,KAAmC,EAAE;IAE7D,MAAM,YAAY,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC5C,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;IAExC,MAAM,iBAAiB,GAAG,GAAG,EAAE;QAC7B,IAAI,CAAC,aAAa,CAAC;YACjB,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,aAAa;YACtD,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,YAAY;YACrD,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,SAAS;SACrD,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,CAAC,IAAI,CAAC,4BAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IAE7C,IAAI,cAAc,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,4BAAW,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;KACrD;IAED,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,MAAM,CAAC,cAAc,CAAC,4BAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QACvD,MAAM,CAAC,cAAc,CAAC,4BAAW,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QAC9D,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE;YACjC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;SAC/C;IACH,CAAC,CAAC;IAEF,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE;QACjC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAC/B,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;KACrC;AACH,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag, Span, SpanStatusCode, context, trace } from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationConfig,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  SemanticAttributes,\n  NetTransportValues,\n} from '@opentelemetry/semantic-conventions';\nimport { TLSAttributes } from './types';\nimport { Net, NormalizedOptions, SocketEvent } from './internal-types';\nimport { getNormalizedArgs, IPC_TRANSPORT } from './utils';\nimport { VERSION } from './version';\nimport { Socket } from 'net';\nimport { TLSSocket } from 'tls';\n\nexport class NetInstrumentation extends InstrumentationBase<Net> {\n  constructor(_config?: InstrumentationConfig) {\n    super('@opentelemetry/instrumentation-net', VERSION, _config);\n  }\n\n  init(): InstrumentationNodeModuleDefinition<Net>[] {\n    return [\n      new InstrumentationNodeModuleDefinition<Net>(\n        'net',\n        ['*'],\n        moduleExports => {\n          diag.debug('Applying patch for net module');\n          if (isWrapped(moduleExports.Socket.prototype.connect)) {\n            this._unwrap(moduleExports.Socket.prototype, 'connect');\n          }\n          this._wrap(\n            moduleExports.Socket.prototype,\n            'connect',\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this._getPatchedConnect() as any\n          );\n          return moduleExports;\n        },\n        moduleExports => {\n          if (moduleExports === undefined) return;\n          diag.debug('Removing patch from net module');\n          this._unwrap(moduleExports.Socket.prototype, 'connect');\n        }\n      ),\n    ];\n  }\n\n  private _getPatchedConnect() {\n    return (original: (...args: unknown[]) => void) => {\n      const plugin = this;\n      return function patchedConnect(this: Socket, ...args: unknown[]) {\n        const options = getNormalizedArgs(args);\n\n        const span =\n          this instanceof TLSSocket\n            ? plugin._startTLSSpan(options, this)\n            : plugin._startSpan(options, this);\n\n        return safeExecuteInTheMiddle(\n          () => original.apply(this, args),\n          error => {\n            if (error !== undefined) {\n              span.setStatus({\n                code: SpanStatusCode.ERROR,\n                message: error.message,\n              });\n              span.recordException(error);\n              span.end();\n            }\n          }\n        );\n      };\n    };\n  }\n\n  private _startSpan(\n    options: NormalizedOptions | undefined | null,\n    socket: Socket\n  ) {\n    if (!options) {\n      return this._startGenericSpan(socket);\n    }\n    if (options.path) {\n      return this._startIpcSpan(options, socket);\n    }\n    return this._startTcpSpan(options, socket);\n  }\n\n  private _startTLSSpan(\n    options: NormalizedOptions | undefined | null,\n    socket: TLSSocket\n  ) {\n    const tlsSpan = this.tracer.startSpan('tls.connect');\n\n    const netSpan = context.with(\n      trace.setSpan(context.active(), tlsSpan),\n      () => {\n        return this._startSpan(options, socket);\n      }\n    );\n\n    const otelTlsSpanListener = () => {\n      const peerCertificate = socket.getPeerCertificate(true);\n      const cipher = socket.getCipher();\n      const protocol = socket.getProtocol();\n      const attributes = {\n        [TLSAttributes.PROTOCOL]: String(protocol),\n        [TLSAttributes.AUTHORIZED]: String(socket.authorized),\n        [TLSAttributes.CIPHER_NAME]: cipher.name,\n        [TLSAttributes.CIPHER_VERSION]: cipher.version,\n        [TLSAttributes.CERTIFICATE_FINGERPRINT]: peerCertificate.fingerprint,\n        [TLSAttributes.CERTIFICATE_SERIAL_NUMBER]: peerCertificate.serialNumber,\n        [TLSAttributes.CERTIFICATE_VALID_FROM]: peerCertificate.valid_from,\n        [TLSAttributes.CERTIFICATE_VALID_TO]: peerCertificate.valid_to,\n        [TLSAttributes.ALPN_PROTOCOL]: '',\n      };\n      if (socket.alpnProtocol) {\n        attributes[TLSAttributes.ALPN_PROTOCOL] = socket.alpnProtocol;\n      }\n\n      tlsSpan.setAttributes(attributes);\n      tlsSpan.end();\n    };\n\n    const otelTlsErrorListener = (e: Error) => {\n      tlsSpan.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: e.message,\n      });\n      tlsSpan.end();\n    };\n\n    /* if we use once and tls.connect() uses a callback this is never executed */\n    socket.prependOnceListener(SocketEvent.SECURE_CONNECT, otelTlsSpanListener);\n    socket.once(SocketEvent.ERROR, otelTlsErrorListener);\n\n    const otelTlsRemoveListeners = () => {\n      socket.removeListener(SocketEvent.SECURE_CONNECT, otelTlsSpanListener);\n      socket.removeListener(SocketEvent.ERROR, otelTlsErrorListener);\n      for (const event of SOCKET_EVENTS) {\n        socket.removeListener(event, otelTlsRemoveListeners);\n      }\n    };\n\n    for (const event of [\n      SocketEvent.CLOSE,\n      SocketEvent.ERROR,\n      SocketEvent.SECURE_CONNECT,\n    ]) {\n      socket.once(event, otelTlsRemoveListeners);\n    }\n\n    return netSpan;\n  }\n\n  /* It might still be useful to pick up errors due to invalid connect arguments. */\n  private _startGenericSpan(socket: Socket) {\n    const span = this.tracer.startSpan('connect');\n\n    registerListeners(socket, span);\n\n    return span;\n  }\n\n  private _startIpcSpan(options: NormalizedOptions, socket: Socket) {\n    const span = this.tracer.startSpan('ipc.connect', {\n      attributes: {\n        [SemanticAttributes.NET_TRANSPORT]: IPC_TRANSPORT,\n        [SemanticAttributes.NET_PEER_NAME]: options.path,\n      },\n    });\n\n    registerListeners(socket, span);\n\n    return span;\n  }\n\n  private _startTcpSpan(options: NormalizedOptions, socket: Socket) {\n    const span = this.tracer.startSpan('tcp.connect', {\n      attributes: {\n        [SemanticAttributes.NET_TRANSPORT]: NetTransportValues.IP_TCP,\n        [SemanticAttributes.NET_PEER_NAME]: options.host,\n        [SemanticAttributes.NET_PEER_PORT]: options.port,\n      },\n    });\n\n    registerListeners(socket, span, { hostAttributes: true });\n\n    return span;\n  }\n}\n\nconst SOCKET_EVENTS = [\n  SocketEvent.CLOSE,\n  SocketEvent.CONNECT,\n  SocketEvent.ERROR,\n];\n\nfunction spanEndHandler(span: Span) {\n  return () => {\n    span.end();\n  };\n}\n\nfunction spanErrorHandler(span: Span) {\n  return (e: Error) => {\n    span.setStatus({\n      code: SpanStatusCode.ERROR,\n      message: e.message,\n    });\n  };\n}\n\nfunction registerListeners(\n  socket: Socket,\n  span: Span,\n  { hostAttributes = false }: { hostAttributes?: boolean } = {}\n) {\n  const setSpanError = spanErrorHandler(span);\n  const setSpanEnd = spanEndHandler(span);\n\n  const setHostAttributes = () => {\n    span.setAttributes({\n      [SemanticAttributes.NET_PEER_IP]: socket.remoteAddress,\n      [SemanticAttributes.NET_HOST_IP]: socket.localAddress,\n      [SemanticAttributes.NET_HOST_PORT]: socket.localPort,\n    });\n  };\n\n  socket.once(SocketEvent.ERROR, setSpanError);\n\n  if (hostAttributes) {\n    socket.once(SocketEvent.CONNECT, setHostAttributes);\n  }\n\n  const removeListeners = () => {\n    socket.removeListener(SocketEvent.ERROR, setSpanError);\n    socket.removeListener(SocketEvent.CONNECT, setHostAttributes);\n    for (const event of SOCKET_EVENTS) {\n      socket.removeListener(event, setSpanEnd);\n      socket.removeListener(event, removeListeners);\n    }\n  };\n\n  for (const event of SOCKET_EVENTS) {\n    socket.once(event, setSpanEnd);\n    socket.once(event, removeListeners);\n  }\n}\n"]}