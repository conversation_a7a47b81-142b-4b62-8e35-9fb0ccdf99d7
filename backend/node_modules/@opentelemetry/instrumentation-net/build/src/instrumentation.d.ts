import { InstrumentationBase, InstrumentationConfig, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import { Net } from './internal-types';
export declare class NetInstrumentation extends InstrumentationBase<Net> {
    constructor(_config?: InstrumentationConfig);
    init(): InstrumentationNodeModuleDefinition<Net>[];
    private _getPatchedConnect;
    private _startSpan;
    private _startTLSSpan;
    private _startGenericSpan;
    private _startIpcSpan;
    private _startTcpSpan;
}
//# sourceMappingURL=instrumentation.d.ts.map