{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as dns from 'dns';\nimport type * as dnsPromises from 'dns/promises';\n\nexport type Dns = typeof dns;\nexport type DnsPromises = typeof dnsPromises;\n\nexport type LookupFunction = ((\n  hostname: string,\n  family: number,\n  callback: LookupSimpleCallback\n) => void) &\n  ((\n    hostname: string,\n    options: dns.LookupOneOptions,\n    callback: LookupSimpleCallback\n  ) => void) &\n  ((\n    hostname: string,\n    options: dns.LookupAllOptions,\n    callback: (\n      err: NodeJS.ErrnoException | null,\n      addresses: dns.LookupAddress[]\n    ) => void\n  ) => void) &\n  ((\n    hostname: string,\n    options: dns.LookupOptions,\n    callback: (\n      err: NodeJS.ErrnoException | null,\n      address: string | dns.LookupAddress[],\n      family: number\n    ) => void\n  ) => void) &\n  ((hostname: string, callback: LookupSimpleCallback) => void);\n\nexport type LookupSimpleArgs = [number, LookupSimpleCallback];\nexport type LookupOneArgs = [dns.LookupOneOptions, LookupSimpleCallback];\nexport type LookupAllArgs = [\n  dns.LookupAllOptions,\n  (err: NodeJS.ErrnoException | null, addresses: dns.LookupAddress[]) => void\n];\nexport type LookupArgs = [\n  dns.LookupOptions,\n  (\n    err: NodeJS.ErrnoException | null,\n    address: string | dns.LookupAddress[],\n    family: number\n  ) => void\n];\nexport type LookupArgSignature = LookupSimpleArgs &\n  LookupSimpleCallback &\n  LookupOneArgs &\n  LookupAllArgs &\n  LookupArgs;\n\nexport type LookupFunctionSignature = (\n  hostname: string,\n  args: Array<LookupArgSignature>\n) => void;\nexport type LookupPromiseSignature = (\n  hostname: string,\n  ...args: unknown[]\n) => Promise<unknown>;\nexport type LookupSimpleCallback = (\n  err: NodeJS.ErrnoException | null,\n  address: string,\n  family: number\n) => void;\n\nexport type LookupCallbackSignature = LookupSimpleCallback &\n  ((\n    err: NodeJS.ErrnoException | null,\n    addresses: dns.LookupAddress[]\n  ) => void) &\n  ((\n    err: NodeJS.ErrnoException | null,\n    address: string | dns.LookupAddress[],\n    family: number\n  ) => void);\n"]}