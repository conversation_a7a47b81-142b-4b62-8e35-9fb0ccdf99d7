import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import { MySQL2InstrumentationConfig } from './types';
export declare class MySQL2Instrumentation extends InstrumentationBase<any> {
    static readonly COMMON_ATTRIBUTES: {
        [x: string]: "mysql";
    };
    constructor(config?: MySQL2InstrumentationConfig);
    protected init(): InstrumentationNodeModuleDefinition<any>[];
    private _patchQuery;
    private _patchCallbackQuery;
}
//# sourceMappingURL=instrumentation.d.ts.map