"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.once = exports.getSpanName = exports.getDbStatement = exports.getConnectionAttributes = void 0;
const semantic_conventions_1 = require("@opentelemetry/semantic-conventions");
/**
 * Get an SpanAttributes map from a mysql connection config object
 *
 * @param config ConnectionConfig
 */
function getConnectionAttributes(config) {
    const { host, port, database, user } = getConfig(config);
    return {
        [semantic_conventions_1.SemanticAttributes.NET_PEER_NAME]: host,
        [semantic_conventions_1.SemanticAttributes.NET_PEER_PORT]: port,
        [semantic_conventions_1.SemanticAttributes.DB_CONNECTION_STRING]: getJDBCString(host, port, database),
        [semantic_conventions_1.SemanticAttributes.DB_NAME]: database,
        [semantic_conventions_1.SemanticAttributes.DB_USER]: user,
    };
}
exports.getConnectionAttributes = getConnectionAttributes;
function getConfig(config) {
    const { host, port, database, user } = (config && config.connectionConfig) || config || {};
    return { host, port, database, user };
}
function getJDBCString(host, port, database) {
    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;
    if (typeof port === 'number') {
        jdbcString += `:${port}`;
    }
    if (typeof database === 'string') {
        jdbcString += `/${database}`;
    }
    return jdbcString;
}
/**
 * Conjures up the value for the db.statement attribute by formatting a SQL query.
 *
 * @returns the database statement being executed.
 */
function getDbStatement(query, format, values) {
    if (typeof query === 'string') {
        return values ? format(query, values) : query;
    }
    else {
        // According to https://github.com/mysqljs/mysql#performing-queries
        // The values argument will override the values in the option object.
        return values || query.values
            ? format(query.sql, values || query.values)
            : query.sql;
    }
}
exports.getDbStatement = getDbStatement;
/**
 * The span name SHOULD be set to a low cardinality value
 * representing the statement executed on the database.
 *
 * @returns SQL statement without variable arguments or SQL verb
 */
function getSpanName(query) {
    var _a;
    const rawQuery = typeof query === 'object' ? query.sql : query;
    // Extract the SQL verb
    return (_a = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.split(' ')) === null || _a === void 0 ? void 0 : _a[0];
}
exports.getSpanName = getSpanName;
const once = (fn) => {
    let called = false;
    return (...args) => {
        if (called)
            return;
        called = true;
        return fn(...args);
    };
};
exports.once = once;
//# sourceMappingURL=utils.js.map