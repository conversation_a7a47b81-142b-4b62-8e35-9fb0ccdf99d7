{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAC1C,oEAKwC;AACxC,8EAG6C;AAC7C,0DAAmE;AAGnE,mCAKiB;AACjB,uCAAoC;AAIpC,MAAa,qBAAsB,SAAQ,qCAAwB;IAKjE,YAAY,MAAoC;QAC9C,KAAK,CAAC,uCAAuC,EAAE,iBAAO,EAAE,MAAM,CAAC,CAAC;IAClE,CAAC;IAES,IAAI;QACZ,OAAO;YACL,IAAI,qDAAmC,CACrC,QAAQ,EACR,CAAC,gBAAgB,CAAC,EAClB,CAAC,aAAkB,EAAE,aAAa,EAAE,EAAE;gBACpC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,aAAa,EAAE,CAAC,CAAC;gBAEnD,MAAM,mBAAmB,GACvB,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC;gBACrC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;gBACtD,IAAI,IAAA,2BAAS,EAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;oBACxC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;iBAC5C;gBACD,IAAI,CAAC,KAAK,CACR,mBAAmB,EACnB,OAAO,EACP,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAQ,CACrD,CAAC;gBAEF,IAAI,IAAA,2BAAS,EAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;oBAC1C,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;iBAC9C;gBACD,IAAI,CAAC,KAAK,CACR,mBAAmB,EACnB,SAAS,EACT,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAQ,CACpD,CAAC;gBAEF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,CAAC,aAAkB,EAAE,EAAE;gBACrB,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,MAAM,mBAAmB,GACvB,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBAC3C,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;YAC/C,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,MAAkB,EAAE,UAAmB;QACzD,OAAO,CAAC,aAAuB,EAAY,EAAE;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;YAErE,OAAO,SAAS,KAAK,CAEnB,KAA0D,EAC1D,iBAAwC,EACxC,SAAoB;gBAEpB,MAAM,gBAAgB,GACpB,UAAU,CAAC,OAAO,CAAC;gBAErB,IAAI,MAAM,CAAC;gBACX,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;oBACpC,MAAM,GAAG,iBAAiB,CAAC;iBAC5B;qBAAM,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;oBACvB,MAAM,GAAG,CAAC,iBAAiB,CAAC,CAAC;iBAC9B;gBAED,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAA,mBAAW,EAAC,KAAK,CAAC,EAAE;oBAC3D,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,MAAM;oBACzB,UAAU,gDACL,qBAAqB,CAAC,iBAAiB,GACvC,IAAA,+BAAuB,EAAC,IAAI,CAAC,MAAM,CAAC,KACvC,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAAE,IAAA,sBAAc,EAC/C,KAAK,EACL,MAAM,EACN,MAAM,CACP,GACF;iBACF,CAAC,CAAC;gBAEH,IAAI,CAAC,UAAU,IAAI,gBAAgB,CAAC,+BAA+B,EAAE;oBACnE,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;wBAClB,OAAO,KAAK,KAAK,QAAQ;4BACvB,CAAC,CAAC,IAAA,mCAAsB,EAAC,IAAI,EAAE,KAAK,CAAC;4BACrC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;gCACnB,GAAG,EAAE,IAAA,mCAAsB,EAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;6BAC7C,CAAC,CAAC;iBACV;gBAED,MAAM,OAAO,GAAG,IAAA,YAAI,EAAC,CAAC,GAAS,EAAE,OAAa,EAAE,EAAE;oBAChD,IAAI,GAAG,EAAE;wBACP,IAAI,CAAC,SAAS,CAAC;4BACb,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;4BAC9B,OAAO,EAAE,GAAG,CAAC,OAAO;yBACrB,CAAC,CAAC;qBACJ;yBAAM;wBACL,IAAI,OAAO,gBAAgB,CAAC,YAAY,KAAK,UAAU,EAAE;4BACvD,IAAA,wCAAsB,EACpB,GAAG,EAAE;gCACH,gBAAgB,CAAC,YAAa,CAAC,IAAI,EAAE;oCACnC,YAAY,EAAE,OAAO;iCACtB,CAAC,CAAC;4BACL,CAAC,EACD,GAAG,CAAC,EAAE;gCACJ,IAAI,GAAG,EAAE;oCACP,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC;iCAC7D;4BACH,CAAC,EACD,IAAI,CACL,CAAC;yBACH;qBACF;oBAED,IAAI,CAAC,GAAG,EAAE,CAAC;gBACb,CAAC,CAAC,CAAC;gBAEH,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC1B,IAAI,OAAQ,KAAa,CAAC,QAAQ,KAAK,UAAU,EAAE;wBACjD,UAAU,CAAC,KAAK,CACd,KAAY,EACZ,UAAU,EACV,UAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,CACxC,CAAC;qBACH;oBAED,MAAM,eAAe,GAAqB,aAAa,CAAC,KAAK,CAC3D,IAAI,EACJ,SAAS,CACV,CAAC;oBAEF,0DAA0D;oBAC1D,eAAe;yBACZ,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;wBACnB,OAAO,CAAC,GAAG,CAAC,CAAC;oBACf,CAAC,CAAC;yBACD,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;wBACxB,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC;oBAEL,OAAO,eAAe,CAAC;iBACxB;gBAED,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;oBACtC,UAAU,CAAC,KAAK,CACd,SAAS,EACT,CAAC,EACD,UAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,CACxC,CAAC;iBACH;qBAAM,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;oBAC7C,UAAU,CAAC,KAAK,CACd,SAAS,EACT,CAAC,EACD,UAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,CACxC,CAAC;iBACH;gBAED,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC9C,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,OAAiB;QAC3C,OAAO,CAAC,gBAA0B,EAAE,EAAE;YACpC,OAAO,UACL,GAAiC,EACjC,OAAa,EACb,MAAiC;gBAEjC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBACtB,OAAO,gBAAgB,CAAC,GAAG,SAAS,CAAC,CAAC;YACxC,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;;AAjLH,sDAkLC;AAjLiB,uCAAiB,GAAG;IAClC,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,KAAK;CACrD,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  DbSystemValues,\n  SemanticAttributes,\n} from '@opentelemetry/semantic-conventions';\nimport { addSqlCommenterComment } from '@opentelemetry/sql-common';\nimport type * as mysqlTypes from 'mysql2';\nimport { MySQL2InstrumentationConfig } from './types';\nimport {\n  getConnectionAttributes,\n  getDbStatement,\n  getSpanName,\n  once,\n} from './utils';\nimport { VERSION } from './version';\n\ntype formatType = typeof mysqlTypes.format;\n\nexport class MySQL2Instrumentation extends InstrumentationBase<any> {\n  static readonly COMMON_ATTRIBUTES = {\n    [SemanticAttributes.DB_SYSTEM]: DbSystemValues.MYSQL,\n  };\n\n  constructor(config?: MySQL2InstrumentationConfig) {\n    super('@opentelemetry/instrumentation-mysql2', VERSION, config);\n  }\n\n  protected init() {\n    return [\n      new InstrumentationNodeModuleDefinition<any>(\n        'mysql2',\n        ['>= 1.4.2 < 4.0'],\n        (moduleExports: any, moduleVersion) => {\n          api.diag.debug(`Patching mysql2@${moduleVersion}`);\n\n          const ConnectionPrototype: mysqlTypes.Connection =\n            moduleExports.Connection.prototype;\n          api.diag.debug('Patching Connection.prototype.query');\n          if (isWrapped(ConnectionPrototype.query)) {\n            this._unwrap(ConnectionPrototype, 'query');\n          }\n          this._wrap(\n            ConnectionPrototype,\n            'query',\n            this._patchQuery(moduleExports.format, false) as any\n          );\n\n          if (isWrapped(ConnectionPrototype.execute)) {\n            this._unwrap(ConnectionPrototype, 'execute');\n          }\n          this._wrap(\n            ConnectionPrototype,\n            'execute',\n            this._patchQuery(moduleExports.format, true) as any\n          );\n\n          return moduleExports;\n        },\n        (moduleExports: any) => {\n          if (moduleExports === undefined) return;\n          const ConnectionPrototype: mysqlTypes.Connection =\n            moduleExports.Connection.prototype;\n          this._unwrap(ConnectionPrototype, 'query');\n          this._unwrap(ConnectionPrototype, 'execute');\n        }\n      ),\n    ];\n  }\n\n  private _patchQuery(format: formatType, isPrepared: boolean) {\n    return (originalQuery: Function): Function => {\n      const thisPlugin = this;\n      api.diag.debug('MySQL2Instrumentation: patched mysql query/execute');\n\n      return function query(\n        this: mysqlTypes.Connection,\n        query: string | mysqlTypes.Query | mysqlTypes.QueryOptions,\n        _valuesOrCallback?: unknown[] | Function,\n        _callback?: Function\n      ) {\n        const thisPluginConfig: MySQL2InstrumentationConfig =\n          thisPlugin._config;\n\n        let values;\n        if (Array.isArray(_valuesOrCallback)) {\n          values = _valuesOrCallback;\n        } else if (arguments[2]) {\n          values = [_valuesOrCallback];\n        }\n\n        const span = thisPlugin.tracer.startSpan(getSpanName(query), {\n          kind: api.SpanKind.CLIENT,\n          attributes: {\n            ...MySQL2Instrumentation.COMMON_ATTRIBUTES,\n            ...getConnectionAttributes(this.config),\n            [SemanticAttributes.DB_STATEMENT]: getDbStatement(\n              query,\n              format,\n              values\n            ),\n          },\n        });\n\n        if (!isPrepared && thisPluginConfig.addSqlCommenterCommentToQueries) {\n          arguments[0] = query =\n            typeof query === 'string'\n              ? addSqlCommenterComment(span, query)\n              : Object.assign(query, {\n                  sql: addSqlCommenterComment(span, query.sql),\n                });\n        }\n\n        const endSpan = once((err?: any, results?: any) => {\n          if (err) {\n            span.setStatus({\n              code: api.SpanStatusCode.ERROR,\n              message: err.message,\n            });\n          } else {\n            if (typeof thisPluginConfig.responseHook === 'function') {\n              safeExecuteInTheMiddle(\n                () => {\n                  thisPluginConfig.responseHook!(span, {\n                    queryResults: results,\n                  });\n                },\n                err => {\n                  if (err) {\n                    thisPlugin._diag.warn('Failed executing responseHook', err);\n                  }\n                },\n                true\n              );\n            }\n          }\n\n          span.end();\n        });\n\n        if (arguments.length === 1) {\n          if (typeof (query as any).onResult === 'function') {\n            thisPlugin._wrap(\n              query as any,\n              'onResult',\n              thisPlugin._patchCallbackQuery(endSpan)\n            );\n          }\n\n          const streamableQuery: mysqlTypes.Query = originalQuery.apply(\n            this,\n            arguments\n          );\n\n          // `end` in mysql behaves similarly to `result` in mysql2.\n          streamableQuery\n            .once('error', err => {\n              endSpan(err);\n            })\n            .once('result', results => {\n              endSpan(undefined, results);\n            });\n\n          return streamableQuery;\n        }\n\n        if (typeof arguments[1] === 'function') {\n          thisPlugin._wrap(\n            arguments,\n            1,\n            thisPlugin._patchCallbackQuery(endSpan)\n          );\n        } else if (typeof arguments[2] === 'function') {\n          thisPlugin._wrap(\n            arguments,\n            2,\n            thisPlugin._patchCallbackQuery(endSpan)\n          );\n        }\n\n        return originalQuery.apply(this, arguments);\n      };\n    };\n  }\n\n  private _patchCallbackQuery(endSpan: Function) {\n    return (originalCallback: Function) => {\n      return function (\n        err: mysqlTypes.QueryError | null,\n        results?: any,\n        fields?: mysqlTypes.FieldPacket[]\n      ) {\n        endSpan(err, results);\n        return originalCallback(...arguments);\n      };\n    };\n  }\n}\n"]}