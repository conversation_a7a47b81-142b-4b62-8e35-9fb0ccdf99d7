{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8EAAyE;AA0BzE;;;;GAIG;AACH,SAAgB,uBAAuB,CAAC,MAAc;IACpD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAEzD,OAAO;QACL,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI;QACxC,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI;QACxC,CAAC,yCAAkB,CAAC,oBAAoB,CAAC,EAAE,aAAa,CACtD,IAAI,EACJ,IAAI,EACJ,QAAQ,CACT;QACD,CAAC,yCAAkB,CAAC,OAAO,CAAC,EAAE,QAAQ;QACtC,CAAC,yCAAkB,CAAC,OAAO,CAAC,EAAE,IAAI;KACnC,CAAC;AACJ,CAAC;AAdD,0DAcC;AAED,SAAS,SAAS,CAAC,MAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAClC,CAAC,MAAM,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,MAAM,IAAI,EAAE,CAAC;IACtD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AACxC,CAAC;AAED,SAAS,aAAa,CACpB,IAAwB,EACxB,IAAwB,EACxB,QAA4B;IAE5B,IAAI,UAAU,GAAG,gBAAgB,IAAI,IAAI,WAAW,EAAE,CAAC;IAEvD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,UAAU,IAAI,IAAI,IAAI,EAAE,CAAC;KAC1B;IAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,UAAU,IAAI,IAAI,QAAQ,EAAE,CAAC;KAC9B;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAC5B,KAAoC,EACpC,MAKW,EACX,MAAc;IAEd,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;KAC/C;SAAM;QACL,mEAAmE;QACnE,qEAAqE;QACrE,OAAO,MAAM,IAAK,KAAsB,CAAC,MAAM;YAC7C,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,IAAK,KAAsB,CAAC,MAAM,CAAC;YAC7D,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;KACf;AACH,CAAC;AAnBD,wCAmBC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,KAAoC;;IAC9D,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/D,uBAAuB;IACvB,OAAO,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC,GAAG,CAAC,0CAAG,CAAC,CAAC,CAAC;AACnC,CAAC;AAJD,kCAIC;AAEM,MAAM,IAAI,GAAG,CAAC,EAAY,EAAE,EAAE;IACnC,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,OAAO,CAAC,GAAG,IAAe,EAAE,EAAE;QAC5B,IAAI,MAAM;YAAE,OAAO;QACnB,MAAM,GAAG,IAAI,CAAC;QACd,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IACrB,CAAC,CAAC;AACJ,CAAC,CAAC;AAPW,QAAA,IAAI,QAOf", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanAttributes } from '@opentelemetry/api';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\n\n/*\n  Following types declare an expectation on mysql2 types and define a subset we\n  use in the instrumentation of the types actually defined in mysql2 pacakge\n\n  We need to import them here so that the installing party of the instrumentation\n  doesn't have to absolutely install the mysql2 package as well - specially\n  important for auto-loaders and meta-pacakges.\n*/\ninterface QueryOptions {\n  sql: string;\n  values?: any | any[] | { [param: string]: any };\n}\n\ninterface Query {\n  sql: string;\n}\n\ninterface Config {\n  host?: string;\n  port?: number;\n  database?: string;\n  user?: string;\n  connectionConfig?: Config;\n}\n/**\n * Get an SpanAttributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nexport function getConnectionAttributes(config: Config): SpanAttributes {\n  const { host, port, database, user } = getConfig(config);\n\n  return {\n    [SemanticAttributes.NET_PEER_NAME]: host,\n    [SemanticAttributes.NET_PEER_PORT]: port,\n    [SemanticAttributes.DB_CONNECTION_STRING]: getJDBCString(\n      host,\n      port,\n      database\n    ),\n    [SemanticAttributes.DB_NAME]: database,\n    [SemanticAttributes.DB_USER]: user,\n  };\n}\n\nfunction getConfig(config: any) {\n  const { host, port, database, user } =\n    (config && config.connectionConfig) || config || {};\n  return { host, port, database, user };\n}\n\nfunction getJDBCString(\n  host: string | undefined,\n  port: number | undefined,\n  database: string | undefined\n) {\n  let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n\n  if (typeof port === 'number') {\n    jdbcString += `:${port}`;\n  }\n\n  if (typeof database === 'string') {\n    jdbcString += `/${database}`;\n  }\n\n  return jdbcString;\n}\n\n/**\n * Conjures up the value for the db.statement attribute by formatting a SQL query.\n *\n * @returns the database statement being executed.\n */\nexport function getDbStatement(\n  query: string | Query | QueryOptions,\n  format: (\n    sql: string,\n    values: any[],\n    stringifyObjects?: boolean,\n    timeZone?: string\n  ) => string,\n  values?: any[]\n): string {\n  if (typeof query === 'string') {\n    return values ? format(query, values) : query;\n  } else {\n    // According to https://github.com/mysqljs/mysql#performing-queries\n    // The values argument will override the values in the option object.\n    return values || (query as QueryOptions).values\n      ? format(query.sql, values || (query as QueryOptions).values)\n      : query.sql;\n  }\n}\n\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nexport function getSpanName(query: string | Query | QueryOptions): string {\n  const rawQuery = typeof query === 'object' ? query.sql : query;\n  // Extract the SQL verb\n  return rawQuery?.split(' ')?.[0];\n}\n\nexport const once = (fn: Function) => {\n  let called = false;\n  return (...args: unknown[]) => {\n    if (called) return;\n    called = true;\n    return fn(...args);\n  };\n};\n"]}