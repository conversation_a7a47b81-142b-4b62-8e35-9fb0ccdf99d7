{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport type { Span } from '@opentelemetry/api';\n\nexport interface MySQL2ResponseHookInformation {\n  queryResults: any;\n}\n\nexport interface MySQL2InstrumentationExecutionResponseHook {\n  (span: Span, responseHookInfo: MySQL2ResponseHookInformation): void;\n}\n\nexport interface MySQL2InstrumentationConfig extends InstrumentationConfig {\n  /**\n   * Hook that allows adding custom span attributes based on the data\n   * returned MySQL2 queries.\n   *\n   * @default undefined\n   */\n  responseHook?: MySQL2InstrumentationExecutionResponseHook;\n\n  /**\n   * If true, queries are modified to also include a comment with\n   * the tracing context, following the {@link https://github.com/open-telemetry/opentelemetry-sqlcommenter sqlcommenter} format\n   */\n  addSqlCommenterCommentToQueries?: boolean;\n}\n"]}