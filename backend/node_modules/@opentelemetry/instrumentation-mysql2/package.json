{"name": "@opentelemetry/instrumentation-mysql2", "version": "0.34.5", "description": "OpenTelemetry mysql2 automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint:fix": "eslint . --ext .ts --fix", "lint": "eslint . --ext .ts", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-mysql2 --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test-all-versions": "tav", "version:update": "node ../../../scripts/version-update.js"}, "keywords": ["instrumentation", "mysql", "mysql2", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.35.1", "@opentelemetry/sdk-trace-base": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "@types/semver": "7.5.3", "mocha": "7.2.0", "mysql2": "2.3.3", "nyc": "15.1.0", "rimraf": "5.0.5", "semver": "7.5.4", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@opentelemetry/sql-common": "^0.40.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-mysql2#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}