{"version": 3, "file": "lambda.js", "sourceRoot": "", "sources": ["../../../src/services/lambda.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAM4B;AAC5B,8EAAyE;AAOzE,4CAA0D;AAE1D,MAAM,cAAc;;AACK,qBAAM,GAAW,QAAQ,CAAC;AAGnD,MAAa,sBAAsB;IAAnC;QA+BE,wBAAmB,GAAG,CAAC,OAA0B,EAAE,EAAE;YACnD,QAAQ,OAAO,CAAC,WAAW,EAAE;gBAC3B,KAAK,cAAc,CAAC,MAAM;oBACxB;wBACE,IAAI,OAAO,CAAC,YAAY,EAAE;4BACxB,OAAO,CAAC,YAAY,CAAC,aAAa,GAAG,8BAA8B,CACjE,OAAO,CAAC,YAAY,CAAC,aAAa,CACnC,CAAC;yBACH;qBACF;oBACD,MAAM;aACT;QACH,CAAC,CAAC;QAoBF,wBAAmB,GAAG,CAAC,YAAiC,EAAU,EAAE;YAClE,OAAO,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,YAAY,CAAC;QACpC,CAAC,CAAC;IACJ,CAAC;IAjEC,kBAAkB,CAChB,OAA0B,EAC1B,OAAoC;QAEpC,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEpE,IAAI,cAAc,GAAmB,EAAE,CAAC;QACxC,IAAI,QAA4B,CAAC;QAEjC,QAAQ,OAAO,CAAC,WAAW,EAAE;YAC3B,KAAK,QAAQ;gBACX,cAAc,GAAG;oBACf,CAAC,yCAAkB,CAAC,iBAAiB,CAAC,EAAE,YAAY;oBACpD,CAAC,yCAAkB,CAAC,qBAAqB,CAAC,EAAE,KAAK;iBAClD,CAAC;gBACF,IAAI,OAAO,CAAC,MAAM,EAAE;oBAClB,cAAc,CAAC,yCAAkB,CAAC,mBAAmB,CAAC;wBACpD,OAAO,CAAC,MAAM,CAAC;iBAClB;gBACD,QAAQ,GAAG,GAAG,YAAY,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;gBACtD,MAAM;SACT;QACD,OAAO;YACL,UAAU,EAAE,KAAK;YACjB,cAAc;YACd,QAAQ,EAAE,cAAQ,CAAC,MAAM;YACzB,QAAQ;SACT,CAAC;IACJ,CAAC;IAgBD,YAAY,CACV,QAA4B,EAC5B,IAAU,EACV,MAAc,EACd,MAAmC;QAEnC,QAAQ,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE;YACpC,KAAK,cAAc,CAAC,MAAM;gBACxB;oBACE,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,cAAc,EACjC,QAAQ,CAAC,SAAS,CACnB,CAAC;iBACH;gBACD,MAAM;SACT;IACH,CAAC;CAKF;AAlED,wDAkEC;AAED,MAAM,8BAA8B,GAAG,CACrC,aAAiC,EACb,EAAE;IACtB,IAAI;QACF,MAAM,iBAAiB,GAAG,EAAE,CAAC;QAC7B,iBAAW,CAAC,MAAM,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,iBAAiB,CAAC,CAAC;QAExD,MAAM,mBAAmB,GAAG,aAAa;YACvC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACnE,CAAC,CAAC,EAAE,CAAC;QAEP,MAAM,oBAAoB,mCACrB,mBAAmB,KACtB,MAAM,kCACD,mBAAmB,CAAC,MAAM,GAC1B,iBAAiB,IAEvB,CAAC;QAEF,MAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CACtC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CACrC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAErB,8EAA8E;QAC9E,0FAA0F;QAC1F,IAAI,oBAAoB,CAAC,MAAM,GAAG,IAAI,EAAE;YACtC,UAAI,CAAC,IAAI,CACP,6HAA6H,CAC9H,CAAC;YACF,OAAO,aAAa,CAAC;SACtB;QAED,OAAO,oBAAoB,CAAC;KAC7B;IAAC,OAAO,CAAC,EAAE;QACV,UAAI,CAAC,KAAK,CACR,4EAA4E,EAC5E,CAAC,CACF,CAAC;QACF,OAAO,aAAa,CAAC;KACtB;AACH,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  Span,\n  SpanKind,\n  Tracer,\n  diag,\n  SpanAttributes,\n} from '@opentelemetry/api';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\nimport {\n  AwsSdkInstrumentationConfig,\n  NormalizedRequest,\n  NormalizedResponse,\n} from '../types';\nimport { RequestMetadata, ServiceExtension } from './ServiceExtension';\nimport { context, propagation } from '@opentelemetry/api';\n\nclass LambdaCommands {\n  public static readonly Invoke: string = 'Invoke';\n}\n\nexport class LambdaServiceExtension implements ServiceExtension {\n  requestPreSpanHook(\n    request: NormalizedRequest,\n    _config: AwsSdkInstrumentationConfig\n  ): RequestMetadata {\n    const functionName = this.extractFunctionName(request.commandInput);\n\n    let spanAttributes: SpanAttributes = {};\n    let spanName: string | undefined;\n\n    switch (request.commandName) {\n      case 'Invoke':\n        spanAttributes = {\n          [SemanticAttributes.FAAS_INVOKED_NAME]: functionName,\n          [SemanticAttributes.FAAS_INVOKED_PROVIDER]: 'aws',\n        };\n        if (request.region) {\n          spanAttributes[SemanticAttributes.FAAS_INVOKED_REGION] =\n            request.region;\n        }\n        spanName = `${functionName} ${LambdaCommands.Invoke}`;\n        break;\n    }\n    return {\n      isIncoming: false,\n      spanAttributes,\n      spanKind: SpanKind.CLIENT,\n      spanName,\n    };\n  }\n\n  requestPostSpanHook = (request: NormalizedRequest) => {\n    switch (request.commandName) {\n      case LambdaCommands.Invoke:\n        {\n          if (request.commandInput) {\n            request.commandInput.ClientContext = injectLambdaPropagationContext(\n              request.commandInput.ClientContext\n            );\n          }\n        }\n        break;\n    }\n  };\n\n  responseHook(\n    response: NormalizedResponse,\n    span: Span,\n    tracer: Tracer,\n    config: AwsSdkInstrumentationConfig\n  ) {\n    switch (response.request.commandName) {\n      case LambdaCommands.Invoke:\n        {\n          span.setAttribute(\n            SemanticAttributes.FAAS_EXECUTION,\n            response.requestId\n          );\n        }\n        break;\n    }\n  }\n\n  extractFunctionName = (commandInput: Record<string, any>): string => {\n    return commandInput?.FunctionName;\n  };\n}\n\nconst injectLambdaPropagationContext = (\n  clientContext: string | undefined\n): string | undefined => {\n  try {\n    const propagatedContext = {};\n    propagation.inject(context.active(), propagatedContext);\n\n    const parsedClientContext = clientContext\n      ? JSON.parse(Buffer.from(clientContext, 'base64').toString('utf8'))\n      : {};\n\n    const updatedClientContext = {\n      ...parsedClientContext,\n      Custom: {\n        ...parsedClientContext.Custom,\n        ...propagatedContext,\n      },\n    };\n\n    const encodedClientContext = Buffer.from(\n      JSON.stringify(updatedClientContext)\n    ).toString('base64');\n\n    // The length of client context is capped at 3583 bytes of base64 encoded data\n    // (https://docs.aws.amazon.com/lambda/latest/dg/API_Invoke.html#API_Invoke_RequestSyntax)\n    if (encodedClientContext.length > 3583) {\n      diag.warn(\n        'lambda instrumentation: cannot set context propagation on lambda invoke parameters due to ClientContext length limitations.'\n      );\n      return clientContext;\n    }\n\n    return encodedClientContext;\n  } catch (e) {\n    diag.debug(\n      'lambda instrumentation: failed to set context propagation on ClientContext',\n      e\n    );\n    return clientContext;\n  }\n};\n"]}