{"version": 3, "file": "ServicesExtensions.js", "sourceRoot": "", "sources": ["../../../src/services/ServicesExtensions.ts"], "names": [], "mappings": ";;;AAiBA,+BAA4C;AAM5C,yCAAsD;AACtD,+BAA4C;AAC5C,qCAAkD;AAElD,MAAa,kBAAkB;IAG7B;QAFA,aAAQ,GAAkC,IAAI,GAAG,EAAE,CAAC;QAGlD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,yBAAmB,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,yBAAmB,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,mCAAwB,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,+BAAsB,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED,kBAAkB,CAChB,OAA0B,EAC1B,MAAmC,EACnC,IAAgB;QAEhB,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAChE,IAAI,CAAC,gBAAgB;YACnB,OAAO;gBACL,UAAU,EAAE,KAAK;aAClB,CAAC;QACJ,OAAO,gBAAgB,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACpE,CAAC;IAED,mBAAmB,CAAC,OAA0B;QAC5C,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAChE,IAAI,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,mBAAmB,CAAA;YAAE,OAAO;QACnD,OAAO,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,YAAY,CACV,QAA4B,EAC5B,IAAU,EACV,MAAc,EACd,MAAmC;;QAEnC,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACzE,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,YAAY,+CAA9B,gBAAgB,EAAiB,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;CACF;AAtCD,gDAsCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Tracer, Span, DiagLogger } from '@opentelemetry/api';\nimport { ServiceExtension, RequestMetadata } from './ServiceExtension';\nimport { SqsServiceExtension } from './sqs';\nimport {\n  AwsSdkInstrumentationConfig,\n  NormalizedRequest,\n  NormalizedResponse,\n} from '../types';\nimport { DynamodbServiceExtension } from './dynamodb';\nimport { SnsServiceExtension } from './sns';\nimport { LambdaServiceExtension } from './lambda';\n\nexport class ServicesExtensions implements ServiceExtension {\n  services: Map<string, ServiceExtension> = new Map();\n\n  constructor() {\n    this.services.set('SQS', new SqsServiceExtension());\n    this.services.set('SNS', new SnsServiceExtension());\n    this.services.set('DynamoDB', new DynamodbServiceExtension());\n    this.services.set('Lambda', new LambdaServiceExtension());\n  }\n\n  requestPreSpanHook(\n    request: NormalizedRequest,\n    config: AwsSdkInstrumentationConfig,\n    diag: DiagLogger\n  ): RequestMetadata {\n    const serviceExtension = this.services.get(request.serviceName);\n    if (!serviceExtension)\n      return {\n        isIncoming: false,\n      };\n    return serviceExtension.requestPreSpanHook(request, config, diag);\n  }\n\n  requestPostSpanHook(request: NormalizedRequest) {\n    const serviceExtension = this.services.get(request.serviceName);\n    if (!serviceExtension?.requestPostSpanHook) return;\n    return serviceExtension.requestPostSpanHook(request);\n  }\n\n  responseHook(\n    response: NormalizedResponse,\n    span: Span,\n    tracer: Tracer,\n    config: AwsSdkInstrumentationConfig\n  ) {\n    const serviceExtension = this.services.get(response.request.serviceName);\n    serviceExtension?.responseHook?.(response, span, tracer, config);\n  }\n}\n"]}