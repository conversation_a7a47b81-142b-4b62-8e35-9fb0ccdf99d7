{"version": 3, "file": "sqs.js", "sourceRoot": "", "sources": ["../../../src/services/sqs.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAQ4B;AAC5B,wEAAqE;AAQrE,8EAI6C;AAC7C,2DAK6B;AAE7B,MAAa,mBAAmB;IAAhC;QAoDE,wBAAmB,GAAG,CAAC,OAA0B,EAAE,EAAE;;YACnD,QAAQ,OAAO,CAAC,WAAW,EAAE;gBAC3B,KAAK,aAAa;oBAChB;wBACE,MAAM,qBAAqB,GACzB,MAAA,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,mCAAI,EAAE,CAAC;wBAClD,IAAI,qBAAqB,EAAE;4BACzB,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC;gCACvC,IAAA,4CAAwB,EAAC,qBAAqB,CAAC,CAAC;yBACnD;qBACF;oBACD,MAAM;gBAER,KAAK,kBAAkB;oBACrB;wBACE,MAAA,MAAA,OAAO,CAAC,YAAY,0CAAE,OAAO,0CAAE,OAAO,CACpC,CAAC,aAA+C,EAAE,EAAE;;4BAClD,aAAa,CAAC,iBAAiB,GAAG,IAAA,4CAAwB,EACxD,MAAA,aAAa,CAAC,iBAAiB,mCAAI,EAAE,CACtC,CAAC;wBACJ,CAAC,CACF,CAAC;qBACH;oBACD,MAAM;aACT;QACH,CAAC,CAAC;QAEF,iBAAY,GAAG,CACb,QAA4B,EAC5B,IAAU,EACV,MAAc,EACd,MAAmC,EACnC,EAAE;;YACF,QAAQ,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE;gBACpC,KAAK,aAAa;oBAChB,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,oBAAoB,EACvC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,SAAS,CAC1B,CAAC;oBACF,MAAM;gBAER,KAAK,kBAAkB;oBACrB,oCAAoC;oBACpC,MAAM;gBAER,KAAK,gBAAgB,CAAC,CAAC;oBACrB,MAAM,QAAQ,GAAkB,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,0CAAE,QAAQ,CAAC;oBACzD,IAAI,QAAQ,EAAE;wBACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBACrE,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;wBAEzD,qCAAiB,CAAC,qCAAqC,CAAc;4BACnE,QAAQ;4BACR,aAAa,EAAE,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC;4BACpD,MAAM;4BACN,oBAAoB,EAAE,CAAC,OAAoB,EAAE,EAAE,CAAC,CAAC;gCAC/C,IAAI,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,SAAS;gCAC5B,aAAa,EAAE,iBAAW,CAAC,OAAO,CAChC,kBAAY,EACZ,IAAA,6CAAyB,EACvB,OAAO,EACP,MAAM,CAAC,uCAAuC,CAC/C,EACD,iCAAa,CACd;gCACD,UAAU,EAAE;oCACV,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,EAAE,SAAS;oCAChD,CAAC,yCAAkB,CAAC,qBAAqB,CAAC,EAAE,SAAS;oCACrD,CAAC,yCAAkB,CAAC,0BAA0B,CAAC,EAC7C,qDAA8B,CAAC,KAAK;oCACtC,CAAC,yCAAkB,CAAC,oBAAoB,CAAC,EAAE,OAAO,CAAC,SAAS;oCAC5D,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,QAAQ;oCAC5C,CAAC,yCAAkB,CAAC,mBAAmB,CAAC,EACtC,+CAAwB,CAAC,OAAO;iCACnC;6BACF,CAAC;4BACF,WAAW,EAAE,CAAC,IAAU,EAAE,OAAoB,EAAE,EAAE,WAChD,OAAA,MAAA,MAAM,CAAC,cAAc,+CAArB,MAAM,EAAkB,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,CAAA,EAAA;yBAC7C,CAAC,CAAC;wBAEH,qCAAiB,CAAC,yBAAyB,CACzC,QAAQ,EACR,MAAM,EACN,aAAO,CAAC,MAAM,EAAE,CACjB,CAAC;qBACH;oBACD,MAAM;iBACP;aACF;QACH,CAAC,CAAC;QAEF,oBAAe,GAAG,CAAC,YAAiC,EAAU,EAAE;YAC9D,OAAO,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,QAAQ,CAAC;QAChC,CAAC,CAAC;QAEF,4BAAuB,GAAG,CAAC,QAAgB,EAAsB,EAAE;YACjE,IAAI,CAAC,QAAQ;gBAAE,OAAO,SAAS,CAAC;YAEhC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACrC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,SAAS,CAAC;YAE5C,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC;IACJ,CAAC;IA1JC,kBAAkB,CAChB,OAA0B,EAC1B,OAAoC;QAEpC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC5D,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,QAAQ,GAAa,cAAQ,CAAC,MAAM,CAAC;QACzC,IAAI,QAA4B,CAAC;QAEjC,MAAM,cAAc,GAAG;YACrB,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,EAAE,SAAS;YAChD,CAAC,yCAAkB,CAAC,0BAA0B,CAAC,EAC7C,qDAA8B,CAAC,KAAK;YACtC,CAAC,yCAAkB,CAAC,qBAAqB,CAAC,EAAE,SAAS;YACrD,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,QAAQ;SAC7C,CAAC;QAEF,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,QAAQ,OAAO,CAAC,WAAW,EAAE;YAC3B,KAAK,gBAAgB;gBACnB;oBACE,UAAU,GAAG,IAAI,CAAC;oBAClB,QAAQ,GAAG,cAAQ,CAAC,QAAQ,CAAC;oBAC7B,QAAQ,GAAG,GAAG,SAAS,UAAU,CAAC;oBAClC,cAAc,CAAC,yCAAkB,CAAC,mBAAmB,CAAC;wBACpD,+CAAwB,CAAC,OAAO,CAAC;oBAEnC,OAAO,CAAC,YAAY,CAAC,qBAAqB;wBACxC,IAAA,wDAAoC,EAClC,OAAO,CAAC,YAAY,CAAC,qBAAqB,EAC1C,iBAAW,CAAC,MAAM,EAAE,CACrB,CAAC;iBACL;gBACD,MAAM;YAER,KAAK,aAAa,CAAC;YACnB,KAAK,kBAAkB;gBACrB,QAAQ,GAAG,cAAQ,CAAC,QAAQ,CAAC;gBAC7B,QAAQ,GAAG,GAAG,SAAS,OAAO,CAAC;gBAC/B,MAAM;SACT;QAED,OAAO;YACL,UAAU;YACV,cAAc;YACd,QAAQ;YACR,QAAQ;SACT,CAAC;IACJ,CAAC;CAyGF;AA3JD,kDA2JC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  Tracer,\n  SpanKind,\n  Span,\n  propagation,\n  trace,\n  context,\n  ROOT_CONTEXT,\n} from '@opentelemetry/api';\nimport { pubsubPropagation } from '@opentelemetry/propagation-utils';\nimport { RequestMetadata, ServiceExtension } from './ServiceExtension';\nimport type { SQS } from 'aws-sdk';\nimport {\n  AwsSdkInstrumentationConfig,\n  NormalizedRequest,\n  NormalizedResponse,\n} from '../types';\nimport {\n  MessagingDestinationKindValues,\n  MessagingOperationValues,\n  SemanticAttributes,\n} from '@opentelemetry/semantic-conventions';\nimport {\n  contextGetter,\n  extractPropagationContext,\n  injectPropagationContext,\n  addPropagationFieldsToAttributeNames,\n} from './MessageAttributes';\n\nexport class SqsServiceExtension implements ServiceExtension {\n  requestPreSpanHook(\n    request: NormalizedRequest,\n    _config: AwsSdkInstrumentationConfig\n  ): RequestMetadata {\n    const queueUrl = this.extractQueueUrl(request.commandInput);\n    const queueName = this.extractQueueNameFromUrl(queueUrl);\n    let spanKind: SpanKind = SpanKind.CLIENT;\n    let spanName: string | undefined;\n\n    const spanAttributes = {\n      [SemanticAttributes.MESSAGING_SYSTEM]: 'aws.sqs',\n      [SemanticAttributes.MESSAGING_DESTINATION_KIND]:\n        MessagingDestinationKindValues.QUEUE,\n      [SemanticAttributes.MESSAGING_DESTINATION]: queueName,\n      [SemanticAttributes.MESSAGING_URL]: queueUrl,\n    };\n\n    let isIncoming = false;\n\n    switch (request.commandName) {\n      case 'ReceiveMessage':\n        {\n          isIncoming = true;\n          spanKind = SpanKind.CONSUMER;\n          spanName = `${queueName} receive`;\n          spanAttributes[SemanticAttributes.MESSAGING_OPERATION] =\n            MessagingOperationValues.RECEIVE;\n\n          request.commandInput.MessageAttributeNames =\n            addPropagationFieldsToAttributeNames(\n              request.commandInput.MessageAttributeNames,\n              propagation.fields()\n            );\n        }\n        break;\n\n      case 'SendMessage':\n      case 'SendMessageBatch':\n        spanKind = SpanKind.PRODUCER;\n        spanName = `${queueName} send`;\n        break;\n    }\n\n    return {\n      isIncoming,\n      spanAttributes,\n      spanKind,\n      spanName,\n    };\n  }\n\n  requestPostSpanHook = (request: NormalizedRequest) => {\n    switch (request.commandName) {\n      case 'SendMessage':\n        {\n          const origMessageAttributes =\n            request.commandInput['MessageAttributes'] ?? {};\n          if (origMessageAttributes) {\n            request.commandInput['MessageAttributes'] =\n              injectPropagationContext(origMessageAttributes);\n          }\n        }\n        break;\n\n      case 'SendMessageBatch':\n        {\n          request.commandInput?.Entries?.forEach(\n            (messageParams: SQS.SendMessageBatchRequestEntry) => {\n              messageParams.MessageAttributes = injectPropagationContext(\n                messageParams.MessageAttributes ?? {}\n              );\n            }\n          );\n        }\n        break;\n    }\n  };\n\n  responseHook = (\n    response: NormalizedResponse,\n    span: Span,\n    tracer: Tracer,\n    config: AwsSdkInstrumentationConfig\n  ) => {\n    switch (response.request.commandName) {\n      case 'SendMessage':\n        span.setAttribute(\n          SemanticAttributes.MESSAGING_MESSAGE_ID,\n          response?.data?.MessageId\n        );\n        break;\n\n      case 'SendMessageBatch':\n        // TODO: How should this be handled?\n        break;\n\n      case 'ReceiveMessage': {\n        const messages: SQS.Message[] = response?.data?.Messages;\n        if (messages) {\n          const queueUrl = this.extractQueueUrl(response.request.commandInput);\n          const queueName = this.extractQueueNameFromUrl(queueUrl);\n\n          pubsubPropagation.patchMessagesArrayToStartProcessSpans<SQS.Message>({\n            messages,\n            parentContext: trace.setSpan(context.active(), span),\n            tracer,\n            messageToSpanDetails: (message: SQS.Message) => ({\n              name: queueName ?? 'unknown',\n              parentContext: propagation.extract(\n                ROOT_CONTEXT,\n                extractPropagationContext(\n                  message,\n                  config.sqsExtractContextPropagationFromPayload\n                ),\n                contextGetter\n              ),\n              attributes: {\n                [SemanticAttributes.MESSAGING_SYSTEM]: 'aws.sqs',\n                [SemanticAttributes.MESSAGING_DESTINATION]: queueName,\n                [SemanticAttributes.MESSAGING_DESTINATION_KIND]:\n                  MessagingDestinationKindValues.QUEUE,\n                [SemanticAttributes.MESSAGING_MESSAGE_ID]: message.MessageId,\n                [SemanticAttributes.MESSAGING_URL]: queueUrl,\n                [SemanticAttributes.MESSAGING_OPERATION]:\n                  MessagingOperationValues.PROCESS,\n              },\n            }),\n            processHook: (span: Span, message: SQS.Message) =>\n              config.sqsProcessHook?.(span, { message }),\n          });\n\n          pubsubPropagation.patchArrayForProcessSpans(\n            messages,\n            tracer,\n            context.active()\n          );\n        }\n        break;\n      }\n    }\n  };\n\n  extractQueueUrl = (commandInput: Record<string, any>): string => {\n    return commandInput?.QueueUrl;\n  };\n\n  extractQueueNameFromUrl = (queueUrl: string): string | undefined => {\n    if (!queueUrl) return undefined;\n\n    const segments = queueUrl.split('/');\n    if (segments.length === 0) return undefined;\n\n    return segments[segments.length - 1];\n  };\n}\n"]}