{"version": 3, "file": "ServiceExtension.js", "sourceRoot": "", "sources": ["../../../src/services/ServiceExtension.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  DiagLogger,\n  Span,\n  SpanAttributes,\n  SpanKind,\n  Tracer,\n} from '@opentelemetry/api';\nimport {\n  AwsSdkInstrumentationConfig,\n  NormalizedRequest,\n  NormalizedResponse,\n} from '../types';\n\nexport interface RequestMetadata {\n  // isIncoming - if true, then the operation callback / promise should be bind with the operation's span\n  isIncoming: boolean;\n  spanAttributes?: SpanAttributes;\n  spanKind?: SpanKind;\n  spanName?: string;\n}\n\nexport interface ServiceExtension {\n  // called before request is sent, and before span is started\n  requestPreSpanHook: (\n    request: NormalizedRequest,\n    config: AwsSdkInstrumentationConfig,\n    diag: DiagLogger\n  ) => RequestMetadata;\n\n  // called before request is sent, and after span is started\n  requestPostSpanHook?: (request: NormalizedRequest) => void;\n\n  responseHook?: (\n    response: NormalizedResponse,\n    span: Span,\n    tracer: Tracer,\n    config: AwsSdkInstrumentationConfig\n  ) => void;\n}\n"]}