{"version": 3, "file": "dynamodb.js", "sourceRoot": "", "sources": ["../../../src/services/dynamodb.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAAwE;AAExE,8EAG6C;AAO7C,MAAa,wBAAwB;IACnC,OAAO,CAAI,MAAe;QACxB,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IACnD,CAAC;IAED,kBAAkB,CAChB,iBAAoC,EACpC,MAAmC,EACnC,IAAgB;;QAEhB,MAAM,QAAQ,GAAa,cAAQ,CAAC,MAAM,CAAC;QAC3C,IAAI,QAA4B,CAAC;QACjC,MAAM,UAAU,GAAG,KAAK,CAAC;QACzB,MAAM,SAAS,GAAG,iBAAiB,CAAC,WAAW,CAAC;QAEhD,MAAM,cAAc,GAAG;YACrB,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,QAAQ;YACvD,CAAC,yCAAkB,CAAC,OAAO,CAAC,EAAE,MAAA,iBAAiB,CAAC,YAAY,0CAAE,SAAS;YACvE,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAAE,SAAS;SAC7C,CAAC;QAEF,IAAI,MAAM,CAAC,2BAA2B,EAAE;YACtC,IAAI;gBACF,MAAM,kBAAkB,GAAG,MAAM,CAAC,2BAA2B,CAC3D,SAAS,EACT,iBAAiB,CAAC,YAAY,CAC/B,CAAC;gBAEF,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE;oBAC1C,cAAc,CAAC,yCAAkB,CAAC,YAAY,CAAC,GAAG,kBAAkB,CAAC;iBACtE;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,uCAAuC,EAAE,GAAG,CAAC,CAAC;aAC1D;SACF;QAED,6FAA6F;QAC7F,0CAA0C;QAC1C,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,SAAS,EAAE;YAC7C,wHAAwH;YACxH,6DAA6D;YAC7D,cAAc,CAAC,yCAAkB,CAAC,wBAAwB,CAAC,GAAG;gBAC5D,iBAAiB,CAAC,YAAY,CAAC,SAAS;aACzC,CAAC;SACH;aAAM,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,YAAY,EAAE;YACvD,cAAc,CAAC,yCAAkB,CAAC,wBAAwB,CAAC,GAAG,MAAM,CAAC,IAAI,CACvE,iBAAiB,CAAC,YAAY,CAAC,YAAY,CAC5C,CAAC;SACH;QAED,IAAI,SAAS,KAAK,aAAa,IAAI,SAAS,KAAK,aAAa,EAAE;YAC9D,+GAA+G;YAC/G,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,qBAAqB,EAAE;gBACzD,cAAc,CACZ,yCAAkB,CAAC,sCAAsC,CAC1D;oBACC,iBAAiB,CAAC,YAAY,CAAC,qBAAqB,CAAC,iBAAiB,CAAC;gBACzE,cAAc,CACZ,yCAAkB,CAAC,uCAAuC,CAC3D;oBACC,iBAAiB,CAAC,YAAY,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;aAC3E;SACF;QAED,IACE,SAAS,KAAK,SAAS;YACvB,SAAS,KAAK,MAAM;YACpB,SAAS,KAAK,OAAO,EACrB;YACA,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,cAAc,EAAE;gBAClD,cAAc,CAAC,yCAAkB,CAAC,4BAA4B,CAAC;oBAC7D,iBAAiB,CAAC,YAAY,CAAC,cAAc,CAAC;aACjD;SACF;QAED,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,MAAM,EAAE;YACjD,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,oBAAoB,EAAE;gBACxD,cAAc,CAAC,yCAAkB,CAAC,uBAAuB,CAAC;oBACxD,iBAAiB,CAAC,YAAY,CAAC,oBAAoB,CAAC;aACvD;SACF;QAED,IAAI,SAAS,KAAK,aAAa,EAAE;YAC/B,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,sBAAsB,EAAE;gBAC1D,cAAc,CACZ,yCAAkB,CAAC,qCAAqC,CACzD,GAAG,IAAI,CAAC,OAAO,CACd,iBAAiB,CAAC,YAAY,CAAC,sBAAsB,CACtD,CAAC,GAAG,CAAC,CAAC,CAAmC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACnE;YAED,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,qBAAqB,EAAE;gBACzD,cAAc,CACZ,yCAAkB,CAAC,oCAAoC,CACxD,GAAG,IAAI,CAAC,OAAO,CACd,iBAAiB,CAAC,YAAY,CAAC,qBAAqB,CACrD,CAAC,GAAG,CAAC,CAAC,CAAmC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACnE;SACF;QAED,IACE,SAAS,KAAK,YAAY;YAC1B,SAAS,KAAK,OAAO;YACrB,SAAS,KAAK,MAAM,EACpB;YACA,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,KAAK,EAAE;gBACzC,cAAc,CAAC,yCAAkB,CAAC,kBAAkB,CAAC;oBACnD,iBAAiB,CAAC,YAAY,CAAC,KAAK,CAAC;aACxC;SACF;QAED,IAAI,SAAS,KAAK,YAAY,EAAE;YAC9B,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,uBAAuB,EAAE;gBAC3D,cAAc,CAAC,yCAAkB,CAAC,kCAAkC,CAAC;oBACnE,iBAAiB,CAAC,YAAY,CAAC,uBAAuB,CAAC;aAC1D;SACF;QAED,IAAI,SAAS,KAAK,OAAO,EAAE;YACzB,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,gBAAgB,EAAE;gBACpD,cAAc,CAAC,yCAAkB,CAAC,yBAAyB,CAAC;oBAC1D,iBAAiB,CAAC,YAAY,CAAC,gBAAgB,CAAC;aACnD;YAED,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,SAAS,EAAE;gBAC7C,cAAc,CAAC,yCAAkB,CAAC,uBAAuB,CAAC;oBACxD,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAC;aAC5C;YAED,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,MAAM,EAAE;gBAC1C,cAAc,CAAC,yCAAkB,CAAC,mBAAmB,CAAC;oBACpD,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;aACzC;SACF;QAED,IAAI,SAAS,KAAK,MAAM,EAAE;YACxB,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,OAAO,EAAE;gBAC3C,cAAc,CAAC,yCAAkB,CAAC,oBAAoB,CAAC;oBACrD,MAAA,iBAAiB,CAAC,YAAY,0CAAE,OAAO,CAAC;aAC3C;YAED,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,aAAa,EAAE;gBACjD,cAAc,CAAC,yCAAkB,CAAC,2BAA2B,CAAC;oBAC5D,MAAA,iBAAiB,CAAC,YAAY,0CAAE,aAAa,CAAC;aACjD;YAED,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,SAAS,EAAE;gBAC7C,cAAc,CAAC,yCAAkB,CAAC,uBAAuB,CAAC;oBACxD,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAC;aAC5C;YAED,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,MAAM,EAAE;gBAC1C,cAAc,CAAC,yCAAkB,CAAC,mBAAmB,CAAC;oBACpD,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;aACzC;SACF;QAED,IAAI,SAAS,KAAK,aAAa,EAAE;YAC/B,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,oBAAoB,EAAE;gBACxD,cAAc,CAAC,yCAAkB,CAAC,kCAAkC,CAAC;oBACnE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,GAAG,CACnE,CAAC,CAAmC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAC3D,CAAC;aACL;YAED,IAAI,MAAA,iBAAiB,CAAC,YAAY,0CAAE,2BAA2B,EAAE;gBAC/D,cAAc,CACZ,yCAAkB,CAAC,2CAA2C,CAC/D,GAAG,IAAI,CAAC,OAAO,CACd,iBAAiB,CAAC,YAAY,CAAC,2BAA2B,CAC3D,CAAC,GAAG,CAAC,CAAC,CAAmC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACnE;SACF;QAED,OAAO;YACL,UAAU;YACV,cAAc;YACd,QAAQ;YACR,QAAQ;SACT,CAAC;IACJ,CAAC;IAED,YAAY,CACV,QAA4B,EAC5B,IAAU,EACV,OAAe,EACf,OAAoC;;QAEpC,IAAI,MAAA,QAAQ,CAAC,IAAI,0CAAE,gBAAgB,EAAE;YACnC,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,8BAA8B,EACjD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CACzC,CAAC,CAAmC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAC3D,CACF,CAAC;SACH;QAED,IAAI,MAAA,QAAQ,CAAC,IAAI,0CAAE,qBAAqB,EAAE;YACxC,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,oCAAoC,EACvD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,CACnD,CAAC,CAAmC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAC3D,CACF,CAAC;SACH;QAED,IAAI,MAAA,QAAQ,CAAC,IAAI,0CAAE,UAAU,EAAE;YAC7B,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,wBAAwB,EAC3C,MAAA,QAAQ,CAAC,IAAI,0CAAE,UAAU,CAAC,MAAM,CACjC,CAAC;SACH;QAED,IAAI,MAAA,QAAQ,CAAC,IAAI,0CAAE,KAAK,EAAE;YACxB,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,kBAAkB,EACrC,MAAA,QAAQ,CAAC,IAAI,0CAAE,KAAK,CACrB,CAAC;SACH;QAED,IAAI,MAAA,QAAQ,CAAC,IAAI,0CAAE,YAAY,EAAE;YAC/B,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,0BAA0B,EAC7C,MAAA,QAAQ,CAAC,IAAI,0CAAE,YAAY,CAC5B,CAAC;SACH;IACH,CAAC;CACF;AAnOD,4DAmOC;AAED,SAAS,OAAO,CAAI,MAAe;IACjC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACnD,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DiagLogger, Span, SpanKind, Tracer } from '@opentelemetry/api';\nimport { RequestMetadata, ServiceExtension } from './ServiceExtension';\nimport {\n  DbSystemValues,\n  SemanticAttributes,\n} from '@opentelemetry/semantic-conventions';\nimport {\n  AwsSdkInstrumentationConfig,\n  NormalizedRequest,\n  NormalizedResponse,\n} from '../types';\n\nexport class DynamodbServiceExtension implements ServiceExtension {\n  toArray<T>(values: T | T[]): T[] {\n    return Array.isArray(values) ? values : [values];\n  }\n\n  requestPreSpanHook(\n    normalizedRequest: NormalizedRequest,\n    config: AwsSdkInstrumentationConfig,\n    diag: DiagLogger\n  ): RequestMetadata {\n    const spanKind: SpanKind = SpanKind.CLIENT;\n    let spanName: string | undefined;\n    const isIncoming = false;\n    const operation = normalizedRequest.commandName;\n\n    const spanAttributes = {\n      [SemanticAttributes.DB_SYSTEM]: DbSystemValues.DYNAMODB,\n      [SemanticAttributes.DB_NAME]: normalizedRequest.commandInput?.TableName,\n      [SemanticAttributes.DB_OPERATION]: operation,\n    };\n\n    if (config.dynamoDBStatementSerializer) {\n      try {\n        const sanitizedStatement = config.dynamoDBStatementSerializer(\n          operation,\n          normalizedRequest.commandInput\n        );\n\n        if (typeof sanitizedStatement === 'string') {\n          spanAttributes[SemanticAttributes.DB_STATEMENT] = sanitizedStatement;\n        }\n      } catch (err) {\n        diag.error('failed to sanitize DynamoDB statement', err);\n      }\n    }\n\n    // normalizedRequest.commandInput.RequestItems) is undefined when no table names are returned\n    // keys in this object are the table names\n    if (normalizedRequest.commandInput?.TableName) {\n      // Necessary for commands with only 1 table name (example: CreateTable). Attribute is TableName not keys of RequestItems\n      // single table name returned for operations like CreateTable\n      spanAttributes[SemanticAttributes.AWS_DYNAMODB_TABLE_NAMES] = [\n        normalizedRequest.commandInput.TableName,\n      ];\n    } else if (normalizedRequest.commandInput?.RequestItems) {\n      spanAttributes[SemanticAttributes.AWS_DYNAMODB_TABLE_NAMES] = Object.keys(\n        normalizedRequest.commandInput.RequestItems\n      );\n    }\n\n    if (operation === 'CreateTable' || operation === 'UpdateTable') {\n      // only check for ProvisionedThroughput since ReadCapacityUnits and WriteCapacity units are required attributes\n      if (normalizedRequest.commandInput?.ProvisionedThroughput) {\n        spanAttributes[\n          SemanticAttributes.AWS_DYNAMODB_PROVISIONED_READ_CAPACITY\n        ] =\n          normalizedRequest.commandInput.ProvisionedThroughput.ReadCapacityUnits;\n        spanAttributes[\n          SemanticAttributes.AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY\n        ] =\n          normalizedRequest.commandInput.ProvisionedThroughput.WriteCapacityUnits;\n      }\n    }\n\n    if (\n      operation === 'GetItem' ||\n      operation === 'Scan' ||\n      operation === 'Query'\n    ) {\n      if (normalizedRequest.commandInput?.ConsistentRead) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_CONSISTENT_READ] =\n          normalizedRequest.commandInput.ConsistentRead;\n      }\n    }\n\n    if (operation === 'Query' || operation === 'Scan') {\n      if (normalizedRequest.commandInput?.ProjectionExpression) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_PROJECTION] =\n          normalizedRequest.commandInput.ProjectionExpression;\n      }\n    }\n\n    if (operation === 'CreateTable') {\n      if (normalizedRequest.commandInput?.GlobalSecondaryIndexes) {\n        spanAttributes[\n          SemanticAttributes.AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES\n        ] = this.toArray(\n          normalizedRequest.commandInput.GlobalSecondaryIndexes\n        ).map((x: { [DictionaryKey: string]: any }) => JSON.stringify(x));\n      }\n\n      if (normalizedRequest.commandInput?.LocalSecondaryIndexes) {\n        spanAttributes[\n          SemanticAttributes.AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES\n        ] = this.toArray(\n          normalizedRequest.commandInput.LocalSecondaryIndexes\n        ).map((x: { [DictionaryKey: string]: any }) => JSON.stringify(x));\n      }\n    }\n\n    if (\n      operation === 'ListTables' ||\n      operation === 'Query' ||\n      operation === 'Scan'\n    ) {\n      if (normalizedRequest.commandInput?.Limit) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_LIMIT] =\n          normalizedRequest.commandInput.Limit;\n      }\n    }\n\n    if (operation === 'ListTables') {\n      if (normalizedRequest.commandInput?.ExclusiveStartTableName) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_EXCLUSIVE_START_TABLE] =\n          normalizedRequest.commandInput.ExclusiveStartTableName;\n      }\n    }\n\n    if (operation === 'Query') {\n      if (normalizedRequest.commandInput?.ScanIndexForward) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_SCAN_FORWARD] =\n          normalizedRequest.commandInput.ScanIndexForward;\n      }\n\n      if (normalizedRequest.commandInput?.IndexName) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_INDEX_NAME] =\n          normalizedRequest.commandInput.IndexName;\n      }\n\n      if (normalizedRequest.commandInput?.Select) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_SELECT] =\n          normalizedRequest.commandInput.Select;\n      }\n    }\n\n    if (operation === 'Scan') {\n      if (normalizedRequest.commandInput?.Segment) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_SEGMENT] =\n          normalizedRequest.commandInput?.Segment;\n      }\n\n      if (normalizedRequest.commandInput?.TotalSegments) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_TOTAL_SEGMENTS] =\n          normalizedRequest.commandInput?.TotalSegments;\n      }\n\n      if (normalizedRequest.commandInput?.IndexName) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_INDEX_NAME] =\n          normalizedRequest.commandInput.IndexName;\n      }\n\n      if (normalizedRequest.commandInput?.Select) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_SELECT] =\n          normalizedRequest.commandInput.Select;\n      }\n    }\n\n    if (operation === 'UpdateTable') {\n      if (normalizedRequest.commandInput?.AttributeDefinitions) {\n        spanAttributes[SemanticAttributes.AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS] =\n          this.toArray(normalizedRequest.commandInput.AttributeDefinitions).map(\n            (x: { [DictionaryKey: string]: any }) => JSON.stringify(x)\n          );\n      }\n\n      if (normalizedRequest.commandInput?.GlobalSecondaryIndexUpdates) {\n        spanAttributes[\n          SemanticAttributes.AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES\n        ] = this.toArray(\n          normalizedRequest.commandInput.GlobalSecondaryIndexUpdates\n        ).map((x: { [DictionaryKey: string]: any }) => JSON.stringify(x));\n      }\n    }\n\n    return {\n      isIncoming,\n      spanAttributes,\n      spanKind,\n      spanName,\n    };\n  }\n\n  responseHook(\n    response: NormalizedResponse,\n    span: Span,\n    _tracer: Tracer,\n    _config: AwsSdkInstrumentationConfig\n  ) {\n    if (response.data?.ConsumedCapacity) {\n      span.setAttribute(\n        SemanticAttributes.AWS_DYNAMODB_CONSUMED_CAPACITY,\n        toArray(response.data.ConsumedCapacity).map(\n          (x: { [DictionaryKey: string]: any }) => JSON.stringify(x)\n        )\n      );\n    }\n\n    if (response.data?.ItemCollectionMetrics) {\n      span.setAttribute(\n        SemanticAttributes.AWS_DYNAMODB_ITEM_COLLECTION_METRICS,\n        this.toArray(response.data.ItemCollectionMetrics).map(\n          (x: { [DictionaryKey: string]: any }) => JSON.stringify(x)\n        )\n      );\n    }\n\n    if (response.data?.TableNames) {\n      span.setAttribute(\n        SemanticAttributes.AWS_DYNAMODB_TABLE_COUNT,\n        response.data?.TableNames.length\n      );\n    }\n\n    if (response.data?.Count) {\n      span.setAttribute(\n        SemanticAttributes.AWS_DYNAMODB_COUNT,\n        response.data?.Count\n      );\n    }\n\n    if (response.data?.ScannedCount) {\n      span.setAttribute(\n        SemanticAttributes.AWS_DYNAMODB_SCANNED_COUNT,\n        response.data?.ScannedCount\n      );\n    }\n  }\n}\n\nfunction toArray<T>(values: T | T[]): T[] {\n  return Array.isArray(values) ? values : [values];\n}\n"]}