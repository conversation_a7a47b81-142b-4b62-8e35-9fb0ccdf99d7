{"version": 3, "file": "sns.js", "sourceRoot": "", "sources": ["../../../src/services/sns.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAA4D;AAC5D,8EAG6C;AAM7C,2DAA+D;AAG/D,MAAa,mBAAmB;IAC9B,kBAAkB,CAChB,OAA0B,EAC1B,OAAoC;QAEpC,IAAI,QAAQ,GAAa,cAAQ,CAAC,MAAM,CAAC;QACzC,IAAI,QAAQ,GAAG,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC;QAC5C,MAAM,cAAc,GAAG;YACrB,CAAC,yCAAkB,CAAC,gBAAgB,CAAC,EAAE,SAAS;SACjD,CAAC;QAEF,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YACrC,QAAQ,GAAG,cAAQ,CAAC,QAAQ,CAAC;YAE7B,cAAc,CAAC,yCAAkB,CAAC,0BAA0B,CAAC;gBAC3D,qDAA8B,CAAC,KAAK,CAAC;YACvC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,YAAY,CAAC;YAClE,cAAc,CAAC,yCAAkB,CAAC,qBAAqB,CAAC;gBACtD,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;YAChE,uEAAuE;YACvE,cAAc,CAAC,4BAA4B,CAAC;gBAC1C,QAAQ,IAAI,SAAS,IAAI,WAAW,IAAI,SAAS,CAAC;YAEpD,QAAQ,GAAG,GACT,WAAW;gBACT,CAAC,CAAC,cAAc;gBAChB,CAAC,CAAC,cAAc,CAAC,yCAAkB,CAAC,qBAAqB,CAC7D,OAAO,CAAC;SACT;QAED,OAAO;YACL,UAAU,EAAE,KAAK;YACjB,cAAc;YACd,QAAQ;YACR,QAAQ;SACT,CAAC;IACJ,CAAC;IAED,mBAAmB,CAAC,OAA0B;;QAC5C,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YACrC,MAAM,qBAAqB,GACzB,MAAA,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,mCAAI,EAAE,CAAC;YAClD,IAAI,qBAAqB,EAAE;gBACzB,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,IAAA,4CAAwB,EAClE,qBAAqB,CACtB,CAAC;aACH;SACF;IACH,CAAC;IAED,YAAY,CACV,QAA4B,EAC5B,IAAU,EACV,MAAc,EACd,MAAmC,IAC5B,CAAC;IAEV,sBAAsB,CACpB,QAAgB,EAChB,SAAiB,EACjB,WAAmB;QAEnB,IAAI,QAAQ,IAAI,SAAS,EAAE;YACzB,MAAM,GAAG,GAAG,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,SAAS,CAAC;YAClC,IAAI;gBACF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aAC7C;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,GAAG,CAAC;aACZ;SACF;aAAM,IAAI,WAAW,EAAE;YACtB,OAAO,WAAW,CAAC;SACpB;aAAM;YACL,OAAO,SAAS,CAAC;SAClB;IACH,CAAC;CACF;AA3ED,kDA2EC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Span, Tracer, SpanKind } from '@opentelemetry/api';\nimport {\n  MessagingDestinationKindValues,\n  SemanticAttributes,\n} from '@opentelemetry/semantic-conventions';\nimport {\n  NormalizedRequest,\n  NormalizedResponse,\n  AwsSdkInstrumentationConfig,\n} from '../types';\nimport { injectPropagationContext } from './MessageAttributes';\nimport { RequestMetadata, ServiceExtension } from './ServiceExtension';\n\nexport class SnsServiceExtension implements ServiceExtension {\n  requestPreSpanHook(\n    request: NormalizedRequest,\n    _config: AwsSdkInstrumentationConfig\n  ): RequestMetadata {\n    let spanKind: SpanKind = SpanKind.CLIENT;\n    let spanName = `SNS ${request.commandName}`;\n    const spanAttributes = {\n      [SemanticAttributes.MESSAGING_SYSTEM]: 'aws.sns',\n    };\n\n    if (request.commandName === 'Publish') {\n      spanKind = SpanKind.PRODUCER;\n\n      spanAttributes[SemanticAttributes.MESSAGING_DESTINATION_KIND] =\n        MessagingDestinationKindValues.TOPIC;\n      const { TopicArn, TargetArn, PhoneNumber } = request.commandInput;\n      spanAttributes[SemanticAttributes.MESSAGING_DESTINATION] =\n        this.extractDestinationName(TopicArn, TargetArn, PhoneNumber);\n      // ToDO: Use SpanAttributes.MESSAGING_DESTINATION_NAME when implemented\n      spanAttributes['messaging.destination.name'] =\n        TopicArn || TargetArn || PhoneNumber || 'unknown';\n\n      spanName = `${\n        PhoneNumber\n          ? 'phone_number'\n          : spanAttributes[SemanticAttributes.MESSAGING_DESTINATION]\n      } send`;\n    }\n\n    return {\n      isIncoming: false,\n      spanAttributes,\n      spanKind,\n      spanName,\n    };\n  }\n\n  requestPostSpanHook(request: NormalizedRequest): void {\n    if (request.commandName === 'Publish') {\n      const origMessageAttributes =\n        request.commandInput['MessageAttributes'] ?? {};\n      if (origMessageAttributes) {\n        request.commandInput['MessageAttributes'] = injectPropagationContext(\n          origMessageAttributes\n        );\n      }\n    }\n  }\n\n  responseHook(\n    response: NormalizedResponse,\n    span: Span,\n    tracer: Tracer,\n    config: AwsSdkInstrumentationConfig\n  ): void {}\n\n  extractDestinationName(\n    topicArn: string,\n    targetArn: string,\n    phoneNumber: string\n  ): string {\n    if (topicArn || targetArn) {\n      const arn = topicArn ?? targetArn;\n      try {\n        return arn.substr(arn.lastIndexOf(':') + 1);\n      } catch (err) {\n        return arn;\n      }\n    } else if (phoneNumber) {\n      return phoneNumber;\n    } else {\n      return 'unknown';\n    }\n  }\n}\n"]}