{"version": 3, "file": "MessageAttributes.js", "sourceRoot": "", "sources": ["../../../src/services/MessageAttributes.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAM4B;AAG5B,6FAA6F;AAChF,QAAA,sBAAsB,GAAG,EAAE,CAAC;AACzC,MAAM,aAAa;IAIjB,GAAG,CACD,OAA8D,EAC9D,GAAW,EACX,KAAa;QAEb,OAAO,CAAC,GAAG,CAAC,GAAG;YACb,QAAQ,EAAE,QAAQ;YAClB,WAAW,EAAE,KAAe;SAC7B,CAAC;IACJ,CAAC;CACF;AACY,QAAA,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;AASjD,MAAM,aAAa;IAIjB,IAAI,CACF,OAA8D;QAE9D,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED,GAAG,CACD,OAA4B,EAC5B,GAAW;;QAEX,OAAO,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,GAAG,CAAC,0CAAE,WAAW,MAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,GAAG,CAAC,0CAAE,KAAK,CAAA,CAAC;IAC9D,CAAC;CACF;AACY,QAAA,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;AAE1C,MAAM,wBAAwB,GAAG,CACtC,aAAqE,EACd,EAAE;IACzD,MAAM,UAAU,GAAG,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,EAAE,CAAC;IACvC,IACE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,iBAAW,CAAC,MAAM,EAAE,CAAC,MAAM;QAC5D,8BAAsB,EACtB;QACA,iBAAW,CAAC,MAAM,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,UAAU,EAAE,qBAAa,CAAC,CAAC;KACjE;SAAM;QACL,UAAI,CAAC,IAAI,CACP,uHAAuH,CACxH,CAAC;KACH;IACD,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AAfW,QAAA,wBAAwB,4BAenC;AAEK,MAAM,yBAAyB,GAAG,CACvC,OAAoB,EACpB,uCAA4D,EAC3B,EAAE;IACnC,MAAM,iBAAiB,GAAG,iBAAW,CAAC,MAAM,EAAE,CAAC;IAC/C,MAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CACtC,OAAO,CAAC,iBAAiB,IAAI,EAAE,CAChC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACjD,IAAI,oBAAoB,EAAE;QACxB,OAAO,OAAO,CAAC,iBAAiB,CAAC;KAClC;SAAM,IAAI,uCAAuC,IAAI,OAAO,CAAC,IAAI,EAAE;QAClE,IAAI;YACF,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACzC,OAAO,OAAO,CAAC,iBAAiB,CAAC;SAClC;QAAC,WAAM;YACN,UAAI,CAAC,KAAK,CACR,wFAAwF,CACzF,CAAC;SACH;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AArBW,QAAA,yBAAyB,6BAqBpC;AAEK,MAAM,oCAAoC,GAAG,CAClD,wBAAkC,EAAE,EACpC,iBAA2B,EAC3B,EAAE;IACF,OAAO,qBAAqB,CAAC,MAAM;QACjC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,qBAAqB,EAAE,GAAG,iBAAiB,CAAC,CAAC,CAAC;QACvE,CAAC,CAAC,iBAAiB,CAAC;AACxB,CAAC,CAAC;AAPW,QAAA,oCAAoC,wCAO/C", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  TextMapGetter,\n  TextMapSetter,\n  context,\n  propagation,\n  diag,\n} from '@opentelemetry/api';\nimport type { SQS, SNS } from '../aws-sdk.types';\n\n// https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-quotas.html\nexport const MAX_MESSAGE_ATTRIBUTES = 10;\nclass ContextSetter\n  implements\n    TextMapSetter<SQS.MessageBodyAttributeMap | SNS.MessageAttributeMap>\n{\n  set(\n    carrier: SQS.MessageBodyAttributeMap | SNS.MessageAttributeMap,\n    key: string,\n    value: string\n  ) {\n    carrier[key] = {\n      DataType: 'String',\n      StringValue: value as string,\n    };\n  }\n}\nexport const contextSetter = new ContextSetter();\n\nexport interface AwsSdkContextObject {\n  [key: string]: {\n    StringValue?: string;\n    Value?: string;\n  };\n}\n\nclass ContextGetter\n  implements\n    TextMapGetter<SQS.MessageBodyAttributeMap | SNS.MessageAttributeMap>\n{\n  keys(\n    carrier: SQS.MessageBodyAttributeMap | SNS.MessageAttributeMap\n  ): string[] {\n    return Object.keys(carrier);\n  }\n\n  get(\n    carrier: AwsSdkContextObject,\n    key: string\n  ): undefined | string | string[] {\n    return carrier?.[key]?.StringValue || carrier?.[key]?.Value;\n  }\n}\nexport const contextGetter = new ContextGetter();\n\nexport const injectPropagationContext = (\n  attributesMap?: SQS.MessageBodyAttributeMap | SNS.MessageAttributeMap\n): SQS.MessageBodyAttributeMap | SNS.MessageAttributeMap => {\n  const attributes = attributesMap ?? {};\n  if (\n    Object.keys(attributes).length + propagation.fields().length <=\n    MAX_MESSAGE_ATTRIBUTES\n  ) {\n    propagation.inject(context.active(), attributes, contextSetter);\n  } else {\n    diag.warn(\n      'aws-sdk instrumentation: cannot set context propagation on SQS/SNS message due to maximum amount of MessageAttributes'\n    );\n  }\n  return attributes;\n};\n\nexport const extractPropagationContext = (\n  message: SQS.Message,\n  sqsExtractContextPropagationFromPayload: boolean | undefined\n): AwsSdkContextObject | undefined => {\n  const propagationFields = propagation.fields();\n  const hasPropagationFields = Object.keys(\n    message.MessageAttributes || []\n  ).some(attr => propagationFields.includes(attr));\n  if (hasPropagationFields) {\n    return message.MessageAttributes;\n  } else if (sqsExtractContextPropagationFromPayload && message.Body) {\n    try {\n      const payload = JSON.parse(message.Body);\n      return payload.MessageAttributes;\n    } catch {\n      diag.debug(\n        'failed to parse SQS payload to extract context propagation, trace might be incomplete.'\n      );\n    }\n  }\n  return undefined;\n};\n\nexport const addPropagationFieldsToAttributeNames = (\n  messageAttributeNames: string[] = [],\n  propagationFields: string[]\n) => {\n  return messageAttributeNames.length\n    ? Array.from(new Set([...messageAttributeNames, ...propagationFields]))\n    : propagationFields;\n};\n"]}