{"version": 3, "file": "aws-sdk.js", "sourceRoot": "", "sources": ["../../src/aws-sdk.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAQ4B;AAC5B,8CAAsD;AAEtD,mCAAyC;AACzC,yCAAgD;AAQhD,uCAAoC;AACpC,oEAOwC;AAQxC,mCAMiB;AAEjB,8EAAyE;AAEzE,MAAM,oBAAoB,GAAG,MAAM,CACjC,qDAAqD,CACtD,CAAC;AAKF,MAAM,gBAAgB,GAAG,MAAM,CAAC,4CAA4C,CAAC,CAAC;AAK9E,MAAa,kBAAmB,SAAQ,qCAAwB;IAK9D,YAAY,SAAsC,EAAE;QAClD,KAAK,CACH,wCAAwC,EACxC,iBAAO,EACP,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAC1B,CAAC;QAPI,uBAAkB,GAAuB,IAAI,6BAAkB,EAAE,CAAC;IAQ1E,CAAC;IAEQ,SAAS,CAAC,SAAsC,EAAE;QACzD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAES,IAAI;QACZ,MAAM,gCAAgC,GAAG,IAAI,+CAA6B,CACxE,uDAAuD,EACvD,CAAC,iBAAiB,CAAC,EACnB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EACrC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CACxC,CAAC;QACF,MAAM,gCAAgC,GAAG,IAAI,+CAA6B,CACxE,uDAAuD,EACvD,CAAC,UAAU,CAAC,EACZ,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EACrC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CACxC,CAAC;QAEF,uFAAuF;QACvF,mDAAmD;QACnD,4EAA4E;QAC5E,MAAM,iBAAiB,GAAG,IAAI,qDAAmC,CAE/D,2BAA2B,EAAE,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE;YAC/D,gCAAgC;YAChC,gCAAgC;SACjC,CAAC,CAAC;QAEH,oEAAoE;QACpE,MAAM,2BAA2B,GAAG,IAAI,+CAA6B,CACnE,sDAAsD,EACtD,CAAC,SAAS,CAAC,EACX,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EACrC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CACxC,CAAC;QACF,MAAM,uBAAuB,GAAG,IAAI,qDAAmC,CACrE,0BAA0B,EAC1B,CAAC,SAAS,CAAC,EACX,SAAS,EACT,SAAS,EACT,CAAC,2BAA2B,CAAC,CAC9B,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,qDAAmC,CAC5D,wBAAwB,EACxB,CAAC,QAAQ,CAAC,EACV,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CACtC,CAAC;QAEF,qEAAqE;QACrE,MAAM,iBAAiB,GAAG,IAAI,qDAAmC,CAC/D,uBAAuB,EACvB,CAAC,SAAS,CAAC,EACX,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CACtC,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,+CAA6B,CACjD,qBAAqB,EACrB,CAAC,UAAU,CAAC,EACZ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAC1B,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,qDAAmC,CACtD,SAAS,EACT,CAAC,UAAU,CAAC,EACZ,SAAS,EACT,SAAS,EACT,CAAC,SAAS,CAAC,CACZ,CAAC;QAEF,OAAO;YACL,QAAQ;YACR,iBAAiB;YACjB,uBAAuB;YACvB,cAAc;YACd,iBAAiB;SAClB,CAAC;IACJ,CAAC;IAES,qBAAqB,CAAC,aAAkB,EAAE,aAAsB;QACxE,UAAI,CAAC,KAAK,CACR,sEAAsE,CACvE,CAAC;QACF,IAAI,CAAC,KAAK,CACR,aAAa,EACb,gBAAgB,EAChB,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CACzD,CAAC;QACF,OAAO,aAAa,CAAC;IACvB,CAAC;IAES,uBAAuB,CAAC,aAAkB;QAClD,UAAI,CAAC,KAAK,CACR,wEAAwE,CACzE,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAC9C,OAAO,aAAa,CAAC;IACvB,CAAC;IAES,mBAAmB,CAAC,aAAkB;QAC9C,UAAI,CAAC,KAAK,CACR,mEAAmE,CACpE,CAAC;QACF,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,MAAM,EACN,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5C,CAAC;QACF,OAAO,aAAa,CAAC;IACvB,CAAC;IAES,qBAAqB,CAAC,aAAkB;QAChD,UAAI,CAAC,KAAK,CACR,sEAAsE,CACvE,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QACrD,OAAO,aAAa,CAAC;IACvB,CAAC;IAES,OAAO,CAAC,aAAkB,EAAE,aAAsB;QAC1D,UAAI,CAAC,KAAK,CACR,8CAA8C,kBAAkB,CAAC,SAAS,EAAE,CAC7E,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC9B,IAAI,CAAC,KAAK,CACR,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,CAAC,SAAS,EAChC,MAAM,EACN,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CACpD,CAAC;QACF,IAAI,CAAC,KAAK,CACR,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,CAAC,SAAS,EAChC,SAAS,EACT,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CACvD,CAAC;QAEF,OAAO,aAAa,CAAC;IACvB,CAAC;IAES,SAAS,CAAC,aAAmB;QACrC,IAAI,IAAA,2BAAS,EAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YACpD,IAAI,CAAC,OAAO,CAAC,aAAc,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;SACxD;QACD,IAAI,IAAA,2BAAS,EAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACvD,IAAI,CAAC,OAAO,CAAC,aAAc,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SAC3D;IACH,CAAC;IAEO,eAAe,CACrB,iBAAoC,EACpC,QAAyB;;QAEzB,MAAM,IAAI,GACR,MAAA,QAAQ,CAAC,QAAQ,mCACjB,GAAG,iBAAiB,CAAC,WAAW,IAAI,iBAAiB,CAAC,WAAW,EAAE,CAAC;QACtE,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;YAC1C,IAAI,EAAE,MAAA,QAAQ,CAAC,QAAQ,mCAAI,cAAQ,CAAC,MAAM;YAC1C,UAAU,kCACL,IAAA,8CAAsC,EAAC,iBAAiB,CAAC,GACzD,QAAQ,CAAC,cAAc,CAC3B;SACF,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,eAAe,CACrB,OAA8B,EAC9B,QAAyB,EACzB,iBAAoC;;QAEpC,MAAM,SAAS,GAAI,OAAe,CAAC,SAAS,CAAC;QAC7C,MAAM,OAAO,GAAI,OAAe,CAAC,OAAO,CAAC;QACzC,MAAM,iBAAiB,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,CAAC;QACrD,MAAM,IAAI,GACR,MAAA,QAAQ,CAAC,QAAQ,mCACjB,GAAG,iBAAiB,CAAC,WAAW,IAAI,iBAAiB,CAAC,WAAW,EAAE,CAAC;QAEtE,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;YAC1C,IAAI,EAAE,MAAA,QAAQ,CAAC,QAAQ,mCAAI,cAAQ,CAAC,MAAM;YAC1C,UAAU,gCACR,CAAC,sBAAc,CAAC,aAAa,CAAC,EAAE,SAAS,EACzC,CAAC,sBAAc,CAAC,qBAAqB,CAAC,EACpC,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,0CAAE,gBAAgB,EACnC,CAAC,sBAAc,CAAC,eAAe,CAAC,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAE,SAAS,EACzD,CAAC,sBAAc,CAAC,sBAAsB,CAAC,EAAE,iBAAiB,EAC1D,CAAC,sBAAc,CAAC,gBAAgB,CAAC,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAE,YAAY,IAC1D,IAAA,8CAAsC,EAAC,iBAAiB,CAAC,GACzD,QAAQ,CAAC,cAAc,CAC3B;SACF,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,uBAAuB,CAC7B,IAAU,EACV,OAA0B,EAC1B,aAAiC;;QAEjC,IAAI,MAAA,IAAI,CAAC,OAAO,0CAAE,cAAc,EAAE;YAChC,MAAM,WAAW,GAAiC;gBAChD,aAAa;gBACb,OAAO;aACR,CAAC;YACF,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,cAAe,CAAC,IAAI,EAAE,WAAW,CAAC,EACrD,CAAC,CAAoB,EAAE,EAAE;gBACvB,IAAI,CAAC;oBACH,UAAI,CAAC,KAAK,CACR,GAAG,kBAAkB,CAAC,SAAS,wCAAwC,EACvE,CAAC,CACF,CAAC;YACN,CAAC,EACD,IAAI,CACL,CAAC;SACH;IACH,CAAC;IAEO,qBAAqB,CAAC,IAAU,EAAE,QAA4B;;QACpE,MAAM,YAAY,GAAG,MAAA,IAAI,CAAC,OAAO,0CAAE,YAAY,CAAC;QAChD,IAAI,CAAC,YAAY;YAAE,OAAO;QAE1B,MAAM,YAAY,GAAkC;YAClD,QAAQ;SACT,CAAC;QACF,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,EACtC,CAAC,CAAoB,EAAE,EAAE;YACvB,IAAI,CAAC;gBACH,UAAI,CAAC,KAAK,CACR,GAAG,kBAAkB,CAAC,SAAS,sCAAsC,EACrE,CAAC,CACF,CAAC;QACN,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAC/B,IAAU,EACV,SAA0B,EAC1B,iBAAoC,EACpC,qBAA8B;QAE9B,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;YAClC,0EAA0E;YAC1E,aAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,GAAG,EAAE;;gBACvC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE;oBAChC,OAAO;iBACR;gBACD,OAAO,SAAS,CAAC,gBAAgB,CAAC,CAAC;gBAEnC,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;gBACrC,MAAM,kBAAkB,GAAuB;oBAC7C,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,OAAO,EAAE,iBAAiB;oBAC1B,SAAS,EAAE,SAAS;iBACrB,CAAC;gBAEF,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;gBACrD,IAAI,QAAQ,CAAC,KAAK,EAAE;oBAClB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBACtC;qBAAM;oBACL,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAClC,kBAAkB,EAClB,IAAI,EACJ,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CACb,CAAC;iBACH;gBAED,IAAI,CAAC,YAAY,CAAC,sBAAc,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;gBAE5D,MAAM,cAAc,GAAG,MAAA,QAAQ,CAAC,YAAY,0CAAE,UAAU,CAAC;gBACzD,IAAI,cAAc,EAAE;oBAClB,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,gBAAgB,EACnC,cAAc,CACf,CAAC;iBACH;gBACD,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB,CAC/B,aAAiC,EACjC,QAA2D;QAE3D,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,SAAS,cAAc,CAE5B,GAAG,IAAe;YAElB,MAAM,KAAK,GAA8B,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpE,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;IACJ,CAAC;IAEO,2BAA2B,CACjC,QAA8C;QAE9C,OAAO,SAAS,IAAI,CAElB,OAAwB,EACxB,GAAG,IAAe;YAElB,OAAO,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;YAC5C,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAC5B,aAAiC,EACjC,sBAAiD;QAEjD,IAAI,CAAC,IAAA,2BAAS,EAAC,sBAAsB,CAAC,OAAO,CAAC,EAAE;YAC9C,IAAI,CAAC,KAAK,CACR,sBAAsB,EACtB,SAAS,EACT,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CACjE,CAAC;SACH;QAED,0FAA0F;QAC1F,yEAAyE;QACzE,IAAI,CAAC,KAAK,CACR,sBAAsB,EACtB,OAAO,EACP,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAC/D,CAAC;QACF,IAAI,CAAC,KAAK,CACR,sBAAsB,EACtB,QAAQ,EACR,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAC/D,CAAC;IACJ,CAAC;IAEO,+BAA+B,CACrC,aAAiC,EACjC,QAAuD;QAEvD,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,UAAqB,GAAG,IAAW;YACxC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YACrD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC;IACJ,CAAC;IAEO,iCAAiC,CACvC,aAAiC,EACjC,QAGqC;QAErC,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,UAEL,QAAa,EACb,mBAA4C;YAE5C,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,mBAAmB,CAAC,CAAC;YACvE,MAAM,cAAc,GAAG,UAErB,OAEC;;gBAED,MAAM,YAAY,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;gBACnD,MAAM,aAAa,GAAG,MAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,+CAApB,YAAY,CAAY,CAAC;gBAC/C,MAAM,WAAW,GACf,MAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,SAAS,mCACvB,IAAA,sCAA8B,EAC5B,mBAAmB,CAAC,UAAU,EAC9B,QAAQ,CACT,CAAC;gBACJ,MAAM,WAAW,GACf,MAAA,mBAAmB,CAAC,WAAW,mCAAI,MAAA,OAAO,CAAC,WAAW,0CAAE,IAAI,CAAC;gBAC/D,MAAM,iBAAiB,GAAG,IAAA,0BAAkB,EAC1C,WAAW,EACX,WAAW,EACX,OAAO,CAAC,KAAK,EACb,SAAS,CACV,CAAC;gBACF,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAChE,iBAAiB,EACjB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,KAAK,CACX,CAAC;gBACF,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;gBACtE,MAAM,qBAAqB,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;gBAEpE,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrD,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;yBAC3B,IAAI,CAAC,cAAc,CAAC,EAAE;wBACrB,iBAAiB,CAAC,MAAM,GAAG,cAAc,CAAC;wBAC1C,IAAI,CAAC,YAAY,CAAC,sBAAc,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;oBAC/D,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,CAAC,EAAE;wBACT,gDAAgD;wBAChD,qCAAqC;wBACrC,UAAI,CAAC,KAAK,CACR,GAAG,kBAAkB,CAAC,SAAS,gEAAgE,EAC/F,CAAC,CACF,CAAC;oBACJ,CAAC,CAAC;yBACD,OAAO,CAAC,GAAG,EAAE;wBACZ,IAAI,CAAC,uBAAuB,CAC1B,IAAI,EACJ,iBAAiB,EACjB,aAAa,CACd,CAAC;wBACF,MAAM,aAAa,GAAG,aAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,GAAG,EAAE;4BAC7D,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;4BAC/D,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,CACrC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAChC,CAAC;wBACJ,CAAC,CAAC,CAAC;wBACH,MAAM,wBAAwB,GAAG,aAAa;6BAC3C,IAAI,CAAC,QAAQ,CAAC,EAAE;;4BACf,MAAM,SAAS,GAAG,MAAA,MAAA,QAAQ,CAAC,MAAM,0CAAE,SAAS,0CAAE,SAAS,CAAC;4BACxD,IAAI,SAAS,EAAE;gCACb,IAAI,CAAC,YAAY,CAAC,sBAAc,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;6BAC7D;4BAED,MAAM,cAAc,GAClB,MAAA,MAAA,QAAQ,CAAC,MAAM,0CAAE,SAAS,0CAAE,cAAc,CAAC;4BAC7C,IAAI,cAAc,EAAE;gCAClB,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,gBAAgB,EACnC,cAAc,CACf,CAAC;6BACH;4BAED,MAAM,iBAAiB,GACrB,MAAA,MAAA,QAAQ,CAAC,MAAM,0CAAE,SAAS,0CAAE,iBAAiB,CAAC;4BAChD,IAAI,iBAAiB,EAAE;gCACrB,IAAI,CAAC,YAAY,CACf,sBAAc,CAAC,uBAAuB,EACtC,iBAAiB,CAClB,CAAC;6BACH;4BAED,MAAM,kBAAkB,GAAuB;gCAC7C,IAAI,EAAE,QAAQ,CAAC,MAAM;gCACrB,OAAO,EAAE,iBAAiB;gCAC1B,SAAS,EAAE,SAAS;6BACrB,CAAC;4BACF,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAClC,kBAAkB,EAClB,IAAI,EACJ,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,CACb,CAAC;4BACF,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;4BACrD,OAAO,QAAQ,CAAC;wBAClB,CAAC,CAAC;6BACD,KAAK,CAAC,GAAG,CAAC,EAAE;4BACX,MAAM,SAAS,GAAG,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,SAAS,CAAC;4BACjC,IAAI,SAAS,EAAE;gCACb,IAAI,CAAC,YAAY,CAAC,sBAAc,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;6BAC7D;4BACD,MAAM,iBAAiB,GAAG,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,iBAAiB,CAAC;4BACjD,IAAI,iBAAiB,EAAE;gCACrB,IAAI,CAAC,YAAY,CACf,sBAAc,CAAC,uBAAuB,EACtC,iBAAiB,CAClB,CAAC;6BACH;4BAED,IAAI,CAAC,SAAS,CAAC;gCACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gCAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;6BACrB,CAAC,CAAC;4BACH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;4BAC1B,MAAM,GAAG,CAAC;wBACZ,CAAC,CAAC;6BACD,OAAO,CAAC,GAAG,EAAE;4BACZ,IAAI,CAAC,GAAG,EAAE,CAAC;wBACb,CAAC,CAAC,CAAC;wBACL,wBAAwB;6BACrB,IAAI,CAAC,GAAG,CAAC,EAAE;4BACV,OAAO,CAAC,GAAG,CAAC,CAAC;wBACf,CAAC,CAAC;6BACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;gBAEH,OAAO,eAAe,CAAC,UAAU;oBAC/B,CAAC,CAAC,IAAA,mBAAW,EAAC,cAAc,EAAE,qBAAqB,EAAE,CAAC,CAAC;oBACvD,CAAC,CAAC,cAAc,CAAC;YACrB,CAAC,CAAC;YACF,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAC1B,aAAiC,EACjC,QAA4D;QAE5D,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,UAEL,QAAwC;YAExC;;;cAGE;YACF,IAAI,IAAI,CAAC,gBAAgB,CAAC,EAAE;gBAC1B,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACtC;YAED,MAAM,iBAAiB,GAAG,IAAA,0BAAkB,EAAC,IAAI,CAAC,CAAC;YACnD,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAChE,iBAAiB,EACjB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,KAAK,CACX,CAAC;YACF,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAC/B,IAAI,EACJ,eAAe,EACf,iBAAiB,CAClB,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;YAC9B,MAAM,qBAAqB,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;YACpE,MAAM,mBAAmB,GAAG,aAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YAE1E,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;YACrE,IAAI,CAAC,yBAAyB,CAC5B,IAAI,EACJ,IAAI,EACJ,iBAAiB,EACjB,qBAAqB,CACtB,CAAC;YAEF,OAAO,aAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,GAAG,EAAE;gBAC9C,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;gBAC/D,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,CACrC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC,CACzC,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAC7B,aAAiC,EACjC,QAA8C;QAE9C,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,UAAiC,GAAG,IAAe;YACxD,qGAAqG;YACrG,IAAI,IAAI,CAAC,gBAAgB,CAAC,EAAE;gBAC1B,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACnC;YAED,MAAM,iBAAiB,GAAG,IAAA,0BAAkB,EAAC,IAAI,CAAC,CAAC;YACnD,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAChE,iBAAiB,EACjB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,KAAK,CACX,CAAC;YACF,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAC/B,IAAI,EACJ,eAAe,EACf,iBAAiB,CAClB,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;YAE9B,MAAM,qBAAqB,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;YACpE,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;YACrE,IAAI,CAAC,yBAAyB,CAC5B,IAAI,EACJ,IAAI,EACJ,iBAAiB,EACjB,qBAAqB,CACtB,CAAC;YAEF,MAAM,WAAW,GAAiB,aAAO,CAAC,IAAI,CAC5C,qBAAqB,EACrB,GAAG,EAAE;gBACH,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;gBAC/D,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,CACrC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAC/B,CAAC;YACJ,CAAC,CACF,CAAC;YAEF,OAAO,eAAe,CAAC,UAAU;gBAC/B,CAAC,CAAC,IAAA,mBAAW,EAAC,WAAW,EAAE,qBAAqB,CAAC;gBACjD,CAAC,CAAC,WAAW,CAAC;QAClB,CAAC,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAI,gBAAuC;;QACtE,IAAI,MAAA,IAAI,CAAC,OAAO,0CAAE,+BAA+B,EAAE;YACjD,OAAO,aAAO,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC;SAC1E;aAAM;YACL,OAAO,gBAAgB,EAAE,CAAC;SAC3B;IACH,CAAC;;AA5mBH,gDA6mBC;AA5mBiB,4BAAS,GAAG,SAAS,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  Span,\n  SpanKind,\n  context,\n  trace,\n  Context,\n  diag,\n  SpanStatusCode,\n} from '@opentelemetry/api';\nimport { suppressTracing } from '@opentelemetry/core';\nimport type * as AWS from 'aws-sdk';\nimport { AttributeNames } from './enums';\nimport { ServicesExtensions } from './services';\nimport {\n  AwsSdkInstrumentationConfig,\n  AwsSdkRequestHookInformation,\n  AwsSdkResponseHookInformation,\n  NormalizedRequest,\n  NormalizedResponse,\n} from './types';\nimport { VERSION } from './version';\nimport {\n  InstrumentationBase,\n  InstrumentationModuleDefinition,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport type {\n  MiddlewareStack,\n  HandlerExecutionContext,\n  Command as AwsV3Command,\n  Handler as AwsV3MiddlewareHandler,\n  InitializeHandlerArguments,\n} from '@aws-sdk/types';\nimport {\n  bindPromise,\n  extractAttributesFromNormalizedRequest,\n  normalizeV2Request,\n  normalizeV3Request,\n  removeSuffixFromStringIfExists,\n} from './utils';\nimport { RequestMetadata } from './services/ServiceExtension';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\n\nconst V3_CLIENT_CONFIG_KEY = Symbol(\n  'opentelemetry.instrumentation.aws-sdk.client.config'\n);\ntype V3PluginCommand = AwsV3Command<any, any, any, any, any> & {\n  [V3_CLIENT_CONFIG_KEY]?: any;\n};\n\nconst REQUEST_SPAN_KEY = Symbol('opentelemetry.instrumentation.aws-sdk.span');\ntype V2PluginRequest = AWS.Request<any, any> & {\n  [REQUEST_SPAN_KEY]?: Span;\n};\n\nexport class AwsInstrumentation extends InstrumentationBase<any> {\n  static readonly component = 'aws-sdk';\n  protected override _config!: AwsSdkInstrumentationConfig;\n  private servicesExtensions: ServicesExtensions = new ServicesExtensions();\n\n  constructor(config: AwsSdkInstrumentationConfig = {}) {\n    super(\n      '@opentelemetry/instrumentation-aws-sdk',\n      VERSION,\n      Object.assign({}, config)\n    );\n  }\n\n  override setConfig(config: AwsSdkInstrumentationConfig = {}) {\n    this._config = Object.assign({}, config);\n  }\n\n  protected init(): InstrumentationModuleDefinition<any>[] {\n    const v3MiddlewareStackFileOldVersions = new InstrumentationNodeModuleFile(\n      '@aws-sdk/middleware-stack/dist/cjs/MiddlewareStack.js',\n      ['>=3.1.0 <3.35.0'],\n      this.patchV3ConstructStack.bind(this),\n      this.unpatchV3ConstructStack.bind(this)\n    );\n    const v3MiddlewareStackFileNewVersions = new InstrumentationNodeModuleFile(\n      '@aws-sdk/middleware-stack/dist-cjs/MiddlewareStack.js',\n      ['>=3.35.0'],\n      this.patchV3ConstructStack.bind(this),\n      this.unpatchV3ConstructStack.bind(this)\n    );\n\n    // as for aws-sdk v3.13.1, constructStack is exported from @aws-sdk/middleware-stack as\n    // getter instead of function, which fails shimmer.\n    // so we are patching the MiddlewareStack.js file directly to get around it.\n    const v3MiddlewareStack = new InstrumentationNodeModuleDefinition<\n      typeof AWS\n    >('@aws-sdk/middleware-stack', ['^3.1.0'], undefined, undefined, [\n      v3MiddlewareStackFileOldVersions,\n      v3MiddlewareStackFileNewVersions,\n    ]);\n\n    // patch for @smithy/middleware-stack for aws-sdk packages v3.363.0+\n    const v3SmithyMiddlewareStackFile = new InstrumentationNodeModuleFile(\n      '@smithy/middleware-stack/dist-cjs/MiddlewareStack.js',\n      ['>=1.0.1'],\n      this.patchV3ConstructStack.bind(this),\n      this.unpatchV3ConstructStack.bind(this)\n    );\n    const v3SmithyMiddlewareStack = new InstrumentationNodeModuleDefinition(\n      '@smithy/middleware-stack',\n      ['>=2.0.0'],\n      undefined,\n      undefined,\n      [v3SmithyMiddlewareStackFile]\n    );\n\n    const v3SmithyClient = new InstrumentationNodeModuleDefinition<typeof AWS>(\n      '@aws-sdk/smithy-client',\n      ['^3.1.0'],\n      this.patchV3SmithyClient.bind(this),\n      this.unpatchV3SmithyClient.bind(this)\n    );\n\n    // patch for new @smithy/smithy-client for aws-sdk packages v3.363.0+\n    const v3NewSmithyClient = new InstrumentationNodeModuleDefinition(\n      '@smithy/smithy-client',\n      ['>=1.0.3'],\n      this.patchV3SmithyClient.bind(this),\n      this.unpatchV3SmithyClient.bind(this)\n    );\n\n    const v2Request = new InstrumentationNodeModuleFile<typeof AWS>(\n      'aws-sdk/lib/core.js',\n      ['^2.308.0'],\n      this.patchV2.bind(this),\n      this.unpatchV2.bind(this)\n    );\n\n    const v2Module = new InstrumentationNodeModuleDefinition<typeof AWS>(\n      'aws-sdk',\n      ['^2.308.0'],\n      undefined,\n      undefined,\n      [v2Request]\n    );\n\n    return [\n      v2Module,\n      v3MiddlewareStack,\n      v3SmithyMiddlewareStack,\n      v3SmithyClient,\n      v3NewSmithyClient,\n    ];\n  }\n\n  protected patchV3ConstructStack(moduleExports: any, moduleVersion?: string) {\n    diag.debug(\n      'aws-sdk instrumentation: applying patch to aws-sdk v3 constructStack'\n    );\n    this._wrap(\n      moduleExports,\n      'constructStack',\n      this._getV3ConstructStackPatch.bind(this, moduleVersion)\n    );\n    return moduleExports;\n  }\n\n  protected unpatchV3ConstructStack(moduleExports: any) {\n    diag.debug(\n      'aws-sdk instrumentation: applying unpatch to aws-sdk v3 constructStack'\n    );\n    this._unwrap(moduleExports, 'constructStack');\n    return moduleExports;\n  }\n\n  protected patchV3SmithyClient(moduleExports: any) {\n    diag.debug(\n      'aws-sdk instrumentation: applying patch to aws-sdk v3 client send'\n    );\n    this._wrap(\n      moduleExports.Client.prototype,\n      'send',\n      this._getV3SmithyClientSendPatch.bind(this)\n    );\n    return moduleExports;\n  }\n\n  protected unpatchV3SmithyClient(moduleExports: any) {\n    diag.debug(\n      'aws-sdk instrumentation: applying patch to aws-sdk v3 constructStack'\n    );\n    this._unwrap(moduleExports.Client.prototype, 'send');\n    return moduleExports;\n  }\n\n  protected patchV2(moduleExports: any, moduleVersion?: string) {\n    diag.debug(\n      `aws-sdk instrumentation: applying patch to ${AwsInstrumentation.component}`\n    );\n    this.unpatchV2(moduleExports);\n    this._wrap(\n      moduleExports?.Request.prototype,\n      'send',\n      this._getRequestSendPatch.bind(this, moduleVersion)\n    );\n    this._wrap(\n      moduleExports?.Request.prototype,\n      'promise',\n      this._getRequestPromisePatch.bind(this, moduleVersion)\n    );\n\n    return moduleExports;\n  }\n\n  protected unpatchV2(moduleExports?: any) {\n    if (isWrapped(moduleExports?.Request.prototype.send)) {\n      this._unwrap(moduleExports!.Request.prototype, 'send');\n    }\n    if (isWrapped(moduleExports?.Request.prototype.promise)) {\n      this._unwrap(moduleExports!.Request.prototype, 'promise');\n    }\n  }\n\n  private _startAwsV3Span(\n    normalizedRequest: NormalizedRequest,\n    metadata: RequestMetadata\n  ): Span {\n    const name =\n      metadata.spanName ??\n      `${normalizedRequest.serviceName}.${normalizedRequest.commandName}`;\n    const newSpan = this.tracer.startSpan(name, {\n      kind: metadata.spanKind ?? SpanKind.CLIENT,\n      attributes: {\n        ...extractAttributesFromNormalizedRequest(normalizedRequest),\n        ...metadata.spanAttributes,\n      },\n    });\n\n    return newSpan;\n  }\n\n  private _startAwsV2Span(\n    request: AWS.Request<any, any>,\n    metadata: RequestMetadata,\n    normalizedRequest: NormalizedRequest\n  ): Span {\n    const operation = (request as any).operation;\n    const service = (request as any).service;\n    const serviceIdentifier = service?.serviceIdentifier;\n    const name =\n      metadata.spanName ??\n      `${normalizedRequest.serviceName}.${normalizedRequest.commandName}`;\n\n    const newSpan = this.tracer.startSpan(name, {\n      kind: metadata.spanKind ?? SpanKind.CLIENT,\n      attributes: {\n        [AttributeNames.AWS_OPERATION]: operation,\n        [AttributeNames.AWS_SIGNATURE_VERSION]:\n          service?.config?.signatureVersion,\n        [AttributeNames.AWS_SERVICE_API]: service?.api?.className,\n        [AttributeNames.AWS_SERVICE_IDENTIFIER]: serviceIdentifier,\n        [AttributeNames.AWS_SERVICE_NAME]: service?.api?.abbreviation,\n        ...extractAttributesFromNormalizedRequest(normalizedRequest),\n        ...metadata.spanAttributes,\n      },\n    });\n\n    return newSpan;\n  }\n\n  private _callUserPreRequestHook(\n    span: Span,\n    request: NormalizedRequest,\n    moduleVersion: string | undefined\n  ) {\n    if (this._config?.preRequestHook) {\n      const requestInfo: AwsSdkRequestHookInformation = {\n        moduleVersion,\n        request,\n      };\n      safeExecuteInTheMiddle(\n        () => this._config.preRequestHook!(span, requestInfo),\n        (e: Error | undefined) => {\n          if (e)\n            diag.error(\n              `${AwsInstrumentation.component} instrumentation: preRequestHook error`,\n              e\n            );\n        },\n        true\n      );\n    }\n  }\n\n  private _callUserResponseHook(span: Span, response: NormalizedResponse) {\n    const responseHook = this._config?.responseHook;\n    if (!responseHook) return;\n\n    const responseInfo: AwsSdkResponseHookInformation = {\n      response,\n    };\n    safeExecuteInTheMiddle(\n      () => responseHook(span, responseInfo),\n      (e: Error | undefined) => {\n        if (e)\n          diag.error(\n            `${AwsInstrumentation.component} instrumentation: responseHook error`,\n            e\n          );\n      },\n      true\n    );\n  }\n\n  private _registerV2CompletedEvent(\n    span: Span,\n    v2Request: V2PluginRequest,\n    normalizedRequest: NormalizedRequest,\n    completedEventContext: Context\n  ) {\n    const self = this;\n    v2Request.on('complete', response => {\n      // read issue https://github.com/aspecto-io/opentelemetry-ext-js/issues/60\n      context.with(completedEventContext, () => {\n        if (!v2Request[REQUEST_SPAN_KEY]) {\n          return;\n        }\n        delete v2Request[REQUEST_SPAN_KEY];\n\n        const requestId = response.requestId;\n        const normalizedResponse: NormalizedResponse = {\n          data: response.data,\n          request: normalizedRequest,\n          requestId: requestId,\n        };\n\n        self._callUserResponseHook(span, normalizedResponse);\n        if (response.error) {\n          span.recordException(response.error);\n        } else {\n          this.servicesExtensions.responseHook(\n            normalizedResponse,\n            span,\n            self.tracer,\n            self._config\n          );\n        }\n\n        span.setAttribute(AttributeNames.AWS_REQUEST_ID, requestId);\n\n        const httpStatusCode = response.httpResponse?.statusCode;\n        if (httpStatusCode) {\n          span.setAttribute(\n            SemanticAttributes.HTTP_STATUS_CODE,\n            httpStatusCode\n          );\n        }\n        span.end();\n      });\n    });\n  }\n\n  private _getV3ConstructStackPatch(\n    moduleVersion: string | undefined,\n    original: (...args: unknown[]) => MiddlewareStack<any, any>\n  ) {\n    const self = this;\n    return function constructStack(\n      this: any,\n      ...args: unknown[]\n    ): MiddlewareStack<any, any> {\n      const stack: MiddlewareStack<any, any> = original.apply(this, args);\n      self.patchV3MiddlewareStack(moduleVersion, stack);\n      return stack;\n    };\n  }\n\n  private _getV3SmithyClientSendPatch(\n    original: (...args: unknown[]) => Promise<any>\n  ) {\n    return function send(\n      this: any,\n      command: V3PluginCommand,\n      ...args: unknown[]\n    ): Promise<any> {\n      command[V3_CLIENT_CONFIG_KEY] = this.config;\n      return original.apply(this, [command, ...args]);\n    };\n  }\n\n  private patchV3MiddlewareStack(\n    moduleVersion: string | undefined,\n    middlewareStackToPatch: MiddlewareStack<any, any>\n  ) {\n    if (!isWrapped(middlewareStackToPatch.resolve)) {\n      this._wrap(\n        middlewareStackToPatch,\n        'resolve',\n        this._getV3MiddlewareStackResolvePatch.bind(this, moduleVersion)\n      );\n    }\n\n    // 'clone' and 'concat' functions are internally calling 'constructStack' which is in same\n    // module, thus not patched, and we need to take care of it specifically.\n    this._wrap(\n      middlewareStackToPatch,\n      'clone',\n      this._getV3MiddlewareStackClonePatch.bind(this, moduleVersion)\n    );\n    this._wrap(\n      middlewareStackToPatch,\n      'concat',\n      this._getV3MiddlewareStackClonePatch.bind(this, moduleVersion)\n    );\n  }\n\n  private _getV3MiddlewareStackClonePatch(\n    moduleVersion: string | undefined,\n    original: (...args: any[]) => MiddlewareStack<any, any>\n  ) {\n    const self = this;\n    return function (this: any, ...args: any[]) {\n      const newStack = original.apply(this, args);\n      self.patchV3MiddlewareStack(moduleVersion, newStack);\n      return newStack;\n    };\n  }\n\n  private _getV3MiddlewareStackResolvePatch(\n    moduleVersion: string | undefined,\n    original: (\n      _handler: any,\n      context: HandlerExecutionContext\n    ) => AwsV3MiddlewareHandler<any, any>\n  ) {\n    const self = this;\n    return function (\n      this: any,\n      _handler: any,\n      awsExecutionContext: HandlerExecutionContext\n    ): AwsV3MiddlewareHandler<any, any> {\n      const origHandler = original.call(this, _handler, awsExecutionContext);\n      const patchedHandler = function (\n        this: any,\n        command: InitializeHandlerArguments<any> & {\n          [V3_CLIENT_CONFIG_KEY]?: any;\n        }\n      ): Promise<any> {\n        const clientConfig = command[V3_CLIENT_CONFIG_KEY];\n        const regionPromise = clientConfig?.region?.();\n        const serviceName =\n          clientConfig?.serviceId ??\n          removeSuffixFromStringIfExists(\n            awsExecutionContext.clientName,\n            'Client'\n          );\n        const commandName =\n          awsExecutionContext.commandName ?? command.constructor?.name;\n        const normalizedRequest = normalizeV3Request(\n          serviceName,\n          commandName,\n          command.input,\n          undefined\n        );\n        const requestMetadata = self.servicesExtensions.requestPreSpanHook(\n          normalizedRequest,\n          self._config,\n          self._diag\n        );\n        const span = self._startAwsV3Span(normalizedRequest, requestMetadata);\n        const activeContextWithSpan = trace.setSpan(context.active(), span);\n\n        const handlerPromise = new Promise((resolve, reject) => {\n          Promise.resolve(regionPromise)\n            .then(resolvedRegion => {\n              normalizedRequest.region = resolvedRegion;\n              span.setAttribute(AttributeNames.AWS_REGION, resolvedRegion);\n            })\n            .catch(e => {\n              // there is nothing much we can do in this case.\n              // we'll just continue without region\n              diag.debug(\n                `${AwsInstrumentation.component} instrumentation: failed to extract region from async function`,\n                e\n              );\n            })\n            .finally(() => {\n              self._callUserPreRequestHook(\n                span,\n                normalizedRequest,\n                moduleVersion\n              );\n              const resultPromise = context.with(activeContextWithSpan, () => {\n                self.servicesExtensions.requestPostSpanHook(normalizedRequest);\n                return self._callOriginalFunction(() =>\n                  origHandler.call(this, command)\n                );\n              });\n              const promiseWithResponseLogic = resultPromise\n                .then(response => {\n                  const requestId = response.output?.$metadata?.requestId;\n                  if (requestId) {\n                    span.setAttribute(AttributeNames.AWS_REQUEST_ID, requestId);\n                  }\n\n                  const httpStatusCode =\n                    response.output?.$metadata?.httpStatusCode;\n                  if (httpStatusCode) {\n                    span.setAttribute(\n                      SemanticAttributes.HTTP_STATUS_CODE,\n                      httpStatusCode\n                    );\n                  }\n\n                  const extendedRequestId =\n                    response.output?.$metadata?.extendedRequestId;\n                  if (extendedRequestId) {\n                    span.setAttribute(\n                      AttributeNames.AWS_REQUEST_EXTENDED_ID,\n                      extendedRequestId\n                    );\n                  }\n\n                  const normalizedResponse: NormalizedResponse = {\n                    data: response.output,\n                    request: normalizedRequest,\n                    requestId: requestId,\n                  };\n                  self.servicesExtensions.responseHook(\n                    normalizedResponse,\n                    span,\n                    self.tracer,\n                    self._config\n                  );\n                  self._callUserResponseHook(span, normalizedResponse);\n                  return response;\n                })\n                .catch(err => {\n                  const requestId = err?.RequestId;\n                  if (requestId) {\n                    span.setAttribute(AttributeNames.AWS_REQUEST_ID, requestId);\n                  }\n                  const extendedRequestId = err?.extendedRequestId;\n                  if (extendedRequestId) {\n                    span.setAttribute(\n                      AttributeNames.AWS_REQUEST_EXTENDED_ID,\n                      extendedRequestId\n                    );\n                  }\n\n                  span.setStatus({\n                    code: SpanStatusCode.ERROR,\n                    message: err.message,\n                  });\n                  span.recordException(err);\n                  throw err;\n                })\n                .finally(() => {\n                  span.end();\n                });\n              promiseWithResponseLogic\n                .then(res => {\n                  resolve(res);\n                })\n                .catch(err => reject(err));\n            });\n        });\n\n        return requestMetadata.isIncoming\n          ? bindPromise(handlerPromise, activeContextWithSpan, 2)\n          : handlerPromise;\n      };\n      return patchedHandler;\n    };\n  }\n\n  private _getRequestSendPatch(\n    moduleVersion: string | undefined,\n    original: (callback?: (err: any, data: any) => void) => void\n  ) {\n    const self = this;\n    return function (\n      this: V2PluginRequest,\n      callback?: (err: any, data: any) => void\n    ) {\n      /*\n        if the span was already started, we don't want to start a new one\n        when Request.promise() is called\n      */\n      if (this[REQUEST_SPAN_KEY]) {\n        return original.call(this, callback);\n      }\n\n      const normalizedRequest = normalizeV2Request(this);\n      const requestMetadata = self.servicesExtensions.requestPreSpanHook(\n        normalizedRequest,\n        self._config,\n        self._diag\n      );\n      const span = self._startAwsV2Span(\n        this,\n        requestMetadata,\n        normalizedRequest\n      );\n      this[REQUEST_SPAN_KEY] = span;\n      const activeContextWithSpan = trace.setSpan(context.active(), span);\n      const callbackWithContext = context.bind(activeContextWithSpan, callback);\n\n      self._callUserPreRequestHook(span, normalizedRequest, moduleVersion);\n      self._registerV2CompletedEvent(\n        span,\n        this,\n        normalizedRequest,\n        activeContextWithSpan\n      );\n\n      return context.with(activeContextWithSpan, () => {\n        self.servicesExtensions.requestPostSpanHook(normalizedRequest);\n        return self._callOriginalFunction(() =>\n          original.call(this, callbackWithContext)\n        );\n      });\n    };\n  }\n\n  private _getRequestPromisePatch(\n    moduleVersion: string | undefined,\n    original: (...args: unknown[]) => Promise<any>\n  ) {\n    const self = this;\n    return function (this: V2PluginRequest, ...args: unknown[]): Promise<any> {\n      // if the span was already started, we don't want to start a new one when Request.promise() is called\n      if (this[REQUEST_SPAN_KEY]) {\n        return original.apply(this, args);\n      }\n\n      const normalizedRequest = normalizeV2Request(this);\n      const requestMetadata = self.servicesExtensions.requestPreSpanHook(\n        normalizedRequest,\n        self._config,\n        self._diag\n      );\n      const span = self._startAwsV2Span(\n        this,\n        requestMetadata,\n        normalizedRequest\n      );\n      this[REQUEST_SPAN_KEY] = span;\n\n      const activeContextWithSpan = trace.setSpan(context.active(), span);\n      self._callUserPreRequestHook(span, normalizedRequest, moduleVersion);\n      self._registerV2CompletedEvent(\n        span,\n        this,\n        normalizedRequest,\n        activeContextWithSpan\n      );\n\n      const origPromise: Promise<any> = context.with(\n        activeContextWithSpan,\n        () => {\n          self.servicesExtensions.requestPostSpanHook(normalizedRequest);\n          return self._callOriginalFunction(() =>\n            original.call(this, arguments)\n          );\n        }\n      );\n\n      return requestMetadata.isIncoming\n        ? bindPromise(origPromise, activeContextWithSpan)\n        : origPromise;\n    };\n  }\n\n  private _callOriginalFunction<T>(originalFunction: (...args: any[]) => T): T {\n    if (this._config?.suppressInternalInstrumentation) {\n      return context.with(suppressTracing(context.active()), originalFunction);\n    } else {\n      return originalFunction();\n    }\n  }\n}\n"]}