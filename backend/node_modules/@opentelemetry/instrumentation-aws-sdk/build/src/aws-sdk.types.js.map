{"version": 3, "file": "aws-sdk.types.js", "sourceRoot": "", "sources": ["../../src/aws-sdk.types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * AWS SDK for JavaScript\n * Copyright 2012-2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * This product includes software developed at\n * Amazon Web Services, Inc. (http://aws.amazon.com/).\n */\n\n/*\n  These are slightly modified and simplified versions of the actual SQS types included\n  in the official distribution:\n  https://github.com/aws/aws-sdk-js/blob/master/clients/sqs.d.ts\n  These are brought here to avoid having users install the `aws-sdk` whenever they\n  require this instrumentation.\n*/\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface -- Our lint doesn't like it, but we prefer to keep it the way original source code has it\ninterface Blob {}\ntype Binary = Buffer | Uint8Array | Blob | string;\n\n// eslint-disable-next-line @typescript-eslint/no-namespace -- Prefer to contain the types copied over in one location\nexport namespace SNS {\n  interface MessageAttributeValue {\n    /**\n     * Amazon SNS supports the following logical data types: String, String.Array, Number, and Binary. For more information, see Message Attribute Data Types.\n     */\n    DataType: string;\n    /**\n     * Strings are Unicode with UTF8 binary encoding. For a list of code values, see ASCII Printable Characters.\n     */\n    StringValue?: string;\n    /**\n     * Binary type attributes can store any binary data, for example, compressed data, encrypted data, or images.\n     */\n    BinaryValue?: Binary;\n  }\n\n  export type MessageAttributeMap = { [key: string]: MessageAttributeValue };\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace -- Prefer to contain the types copied over in one location\nexport namespace SQS {\n  type StringList = string[];\n  type BinaryList = Binary[];\n  interface MessageAttributeValue {\n    /**\n     * Strings are Unicode with UTF-8 binary encoding. For a list of code values, see ASCII Printable Characters.\n     */\n    StringValue?: string;\n    /**\n     * Binary type attributes can store any binary data, such as compressed data, encrypted data, or images.\n     */\n    BinaryValue?: Binary;\n    /**\n     * Not implemented. Reserved for future use.\n     */\n    StringListValues?: StringList;\n    /**\n     * Not implemented. Reserved for future use.\n     */\n    BinaryListValues?: BinaryList;\n    /**\n     * Amazon SQS supports the following logical data types: String, Number, and Binary. For the Number data type, you must use StringValue. You can also append custom labels. For more information, see Amazon SQS Message Attributes in the Amazon SQS Developer Guide.\n     */\n    DataType: string;\n  }\n\n  export type MessageBodyAttributeMap = {\n    [key: string]: MessageAttributeValue;\n  };\n\n  type MessageSystemAttributeMap = { [key: string]: string };\n\n  export interface Message {\n    /**\n     * A unique identifier for the message. A MessageIdis considered unique across all accounts for an extended period of time.\n     */\n    MessageId?: string;\n    /**\n     * An identifier associated with the act of receiving the message. A new receipt handle is returned every time you receive a message. When deleting a message, you provide the last received receipt handle to delete the message.\n     */\n    ReceiptHandle?: string;\n    /**\n     * An MD5 digest of the non-URL-encoded message body string.\n     */\n    MD5OfBody?: string;\n    /**\n     * The message's contents (not URL-encoded).\n     */\n    Body?: string;\n    /**\n     * A map of the attributes requested in  ReceiveMessage  to their respective values. Supported attributes:    ApproximateReceiveCount     ApproximateFirstReceiveTimestamp     MessageDeduplicationId     MessageGroupId     SenderId     SentTimestamp     SequenceNumber     ApproximateFirstReceiveTimestamp and SentTimestamp are each returned as an integer representing the epoch time in milliseconds.\n     */\n    Attributes?: MessageSystemAttributeMap;\n    /**\n     * An MD5 digest of the non-URL-encoded message attribute string. You can use this attribute to verify that Amazon SQS received the message correctly. Amazon SQS URL-decodes the message before creating the MD5 digest. For information about MD5, see RFC1321.\n     */\n    MD5OfMessageAttributes?: string;\n    /**\n     * Each message attribute consists of a Name, Type, and Value. For more information, see Amazon SQS message attributes in the Amazon SQS Developer Guide.\n     */\n    MessageAttributes?: MessageBodyAttributeMap;\n  }\n}\n"]}