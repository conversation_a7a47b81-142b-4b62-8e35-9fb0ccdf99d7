{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { SQS } from './aws-sdk.types';\n\nexport type CommandInput = Record<string, any>;\n\n/**\n * These are normalized request and response, which are used by both sdk v2 and v3.\n * They organize the relevant data in one interface which can be processed in a\n * uniform manner in hooks\n */\nexport interface NormalizedRequest {\n  serviceName: string;\n  commandName: string;\n  commandInput: CommandInput;\n  region?: string;\n}\nexport interface NormalizedResponse {\n  data: any;\n  request: NormalizedRequest;\n  requestId: string;\n}\n\nexport interface AwsSdkRequestHookInformation {\n  moduleVersion?: string;\n  request: NormalizedRequest;\n}\nexport interface AwsSdkRequestCustomAttributeFunction {\n  (span: Span, requestInfo: AwsSdkRequestHookInformation): void;\n}\n\nexport interface AwsSdkResponseHookInformation {\n  moduleVersion?: string;\n  response: NormalizedResponse;\n}\n/**\n * span can be used to add custom attributes, or for any other need.\n * response is the object that is returned to the user calling the aws-sdk operation.\n * The response type and attributes on the response are client-specific.\n */\nexport interface AwsSdkResponseCustomAttributeFunction {\n  (span: Span, responseInfo: AwsSdkResponseHookInformation): void;\n}\n\nexport interface AwsSdkSqsProcessHookInformation {\n  message: SQS.Message;\n}\nexport interface AwsSdkSqsProcessCustomAttributeFunction {\n  (span: Span, sqsProcessInfo: AwsSdkSqsProcessHookInformation): void;\n}\n\nexport type AwsSdkDynamoDBStatementSerializer = (\n  operation: string,\n  commandInput: CommandInput\n) => string | undefined;\n\nexport interface AwsSdkInstrumentationConfig extends InstrumentationConfig {\n  /** hook for adding custom attributes before request is sent to aws */\n  preRequestHook?: AwsSdkRequestCustomAttributeFunction;\n\n  /** hook for adding custom attributes when response is received from aws */\n  responseHook?: AwsSdkResponseCustomAttributeFunction;\n\n  /** hook for adding custom attribute when an sqs process span is started */\n  sqsProcessHook?: AwsSdkSqsProcessCustomAttributeFunction;\n\n  /** custom serializer function for the db.statement attribute in DynamoDB spans */\n  dynamoDBStatementSerializer?: AwsSdkDynamoDBStatementSerializer;\n\n  /**\n   * Most aws operation use http request under the hood.\n   * if http instrumentation is enabled, each aws operation will also create\n   * an http/s child describing the communication with amazon servers.\n   * Setting the `suppressInternalInstrumentation` config value to `true` will\n   * cause the instrumentation to suppress instrumentation of underlying operations,\n   * effectively causing those http spans to be non-recordable.\n   */\n  suppressInternalInstrumentation?: boolean;\n\n  /**\n   * In some cases the context propagation headers may be found in the message payload\n   * rather than the message attribute.\n   * When this field is turned on the instrumentation will parse the payload and extract the\n   * context from there.\n   * Even if the field is on and MessageAttribute contains context propagation field are present,\n   * the MessageAttribute will get priority.\n   * By default it is off.\n   */\n  sqsExtractContextPropagationFromPayload?: boolean;\n}\n"]}