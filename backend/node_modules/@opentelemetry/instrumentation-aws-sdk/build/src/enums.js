"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttributeNames = void 0;
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var AttributeNames;
(function (AttributeNames) {
    AttributeNames["AWS_OPERATION"] = "aws.operation";
    AttributeNames["AWS_REGION"] = "aws.region";
    AttributeNames["AWS_SERVICE_API"] = "aws.service.api";
    AttributeNames["AWS_SERVICE_NAME"] = "aws.service.name";
    AttributeNames["AWS_SERVICE_IDENTIFIER"] = "aws.service.identifier";
    AttributeNames["AWS_REQUEST_ID"] = "aws.request.id";
    AttributeNames["AWS_REQUEST_EXTENDED_ID"] = "aws.request.extended_id";
    AttributeNames["AWS_SIGNATURE_VERSION"] = "aws.signature.version";
})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));
//# sourceMappingURL=enums.js.map