{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";;;AAgBA,4CAAsE;AACtE,8EAAyE;AACzE,mCAAyC;AAEzC,MAAM,YAAY,GAAG,CAAC,GAAW,EAAU,EAAE,CAC3C,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAEtE,MAAM,8BAA8B,GAAG,CAC5C,GAAW,EACX,cAAsB,EACd,EAAE;IACV,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC;IAC3C,OAAO,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,KAAK,CAAC,CAAC,YAAY,CAAC,MAAK,cAAc;QACjD,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,YAAY,CAAC;QACzC,CAAC,CAAC,GAAG,CAAC;AACV,CAAC,CAAC;AARW,QAAA,8BAA8B,kCAQzC;AAEK,MAAM,kBAAkB,GAAG,CAAC,YAAiB,EAAqB,EAAE;;IACzE,MAAM,OAAO,GAAG,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,CAAC;IACtC,OAAO;QACL,WAAW,EAAE,MAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAE,SAAS,0CAAE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QACzD,WAAW,EAAE,YAAY,CAAC,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,SAAS,CAAC;QAClD,YAAY,EAAE,YAAY,CAAC,MAAM;QACjC,MAAM,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,0CAAE,MAAM;KAChC,CAAC;AACJ,CAAC,CAAC;AARW,QAAA,kBAAkB,sBAQ7B;AAEK,MAAM,kBAAkB,GAAG,CAChC,WAAmB,EACnB,qBAA6B,EAC7B,YAAiC,EACjC,MAA0B,EACP,EAAE;IACrB,OAAO;QACL,WAAW,EAAE,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QAC7C,WAAW,EAAE,IAAA,sCAA8B,EACzC,qBAAqB,EACrB,SAAS,CACV;QACD,YAAY;QACZ,MAAM;KACP,CAAC;AACJ,CAAC,CAAC;AAfW,QAAA,kBAAkB,sBAe7B;AAEK,MAAM,sCAAsC,GAAG,CACpD,iBAAoC,EACpB,EAAE;IAClB,OAAO;QACL,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAAE,SAAS;QAC1C,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAAE,iBAAiB,CAAC,WAAW;QAC9D,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,iBAAiB,CAAC,WAAW;QAC/D,CAAC,sBAAc,CAAC,UAAU,CAAC,EAAE,iBAAiB,CAAC,MAAM;KACtD,CAAC;AACJ,CAAC,CAAC;AATW,QAAA,sCAAsC,0CASjD;AAEK,MAAM,WAAW,GAAG,CACzB,MAAkB,EAClB,mBAA4B,EAC5B,WAAW,GAAG,CAAC,EACH,EAAE;IACd,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC;IAE7B,MAAM,CAAC,IAAI,GAAG,UACZ,WAAqC,EACrC,UAAoC;QAEpC,MAAM,cAAc,GAAG,aAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;QACtE,MAAM,aAAa,GAAG,aAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;QACpE,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAIlC,IAAI,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;QACvC,OAAO,WAAW,GAAG,CAAC;YACpB,CAAC,CAAC,IAAA,mBAAW,EAAC,cAAc,EAAE,mBAAmB,EAAE,WAAW,GAAG,CAAC,CAAC;YACnE,CAAC,CAAC,cAAc,CAAC;IACrB,CAAC,CAAC;IACF,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAvBW,QAAA,WAAW,eAuBtB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { NormalizedRequest } from './types';\nimport { Context, SpanAttributes, context } from '@opentelemetry/api';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\nimport { AttributeNames } from './enums';\n\nconst toPascalCase = (str: string): string =>\n  typeof str === 'string' ? str.charAt(0).toUpperCase() + str.slice(1) : str;\n\nexport const removeSuffixFromStringIfExists = (\n  str: string,\n  suffixToRemove: string\n): string => {\n  const suffixLength = suffixToRemove.length;\n  return str?.slice(-suffixLength) === suffixToRemove\n    ? str.slice(0, str.length - suffixLength)\n    : str;\n};\n\nexport const normalizeV2Request = (awsV2Request: any): NormalizedRequest => {\n  const service = awsV2Request?.service;\n  return {\n    serviceName: service?.api?.serviceId?.replace(/\\s+/g, ''),\n    commandName: toPascalCase(awsV2Request?.operation),\n    commandInput: awsV2Request.params,\n    region: service?.config?.region,\n  };\n};\n\nexport const normalizeV3Request = (\n  serviceName: string,\n  commandNameWithSuffix: string,\n  commandInput: Record<string, any>,\n  region: string | undefined\n): NormalizedRequest => {\n  return {\n    serviceName: serviceName?.replace(/\\s+/g, ''),\n    commandName: removeSuffixFromStringIfExists(\n      commandNameWithSuffix,\n      'Command'\n    ),\n    commandInput,\n    region,\n  };\n};\n\nexport const extractAttributesFromNormalizedRequest = (\n  normalizedRequest: NormalizedRequest\n): SpanAttributes => {\n  return {\n    [SemanticAttributes.RPC_SYSTEM]: 'aws-api',\n    [SemanticAttributes.RPC_METHOD]: normalizedRequest.commandName,\n    [SemanticAttributes.RPC_SERVICE]: normalizedRequest.serviceName,\n    [AttributeNames.AWS_REGION]: normalizedRequest.region,\n  };\n};\n\nexport const bindPromise = <T = unknown>(\n  target: Promise<T>,\n  contextForCallbacks: Context,\n  rebindCount = 1\n): Promise<T> => {\n  const origThen = target.then;\n  type PromiseThenParameters = Parameters<Promise<T>['then']>;\n  target.then = function <TResult1 = T, TResult2 = never>(\n    onFulfilled: PromiseThenParameters[0],\n    onRejected: PromiseThenParameters[1]\n  ): Promise<TResult1 | TResult2> {\n    const newOnFulfilled = context.bind(contextForCallbacks, onFulfilled);\n    const newOnRejected = context.bind(contextForCallbacks, onRejected);\n    const patchedPromise = origThen.call<\n      Promise<T>,\n      any[],\n      Promise<TResult1 | TResult2>\n    >(this, newOnFulfilled, newOnRejected);\n    return rebindCount > 1\n      ? bindPromise(patchedPromise, contextForCallbacks, rebindCount - 1)\n      : patchedPromise;\n  };\n  return target;\n};\n"]}