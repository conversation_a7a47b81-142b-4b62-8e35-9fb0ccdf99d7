{"version": 3, "file": "enums.js", "sourceRoot": "", "sources": ["../../src/enums.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,IAAY,cASX;AATD,WAAY,cAAc;IACxB,iDAA+B,CAAA;IAC/B,2CAAyB,CAAA;IACzB,qDAAmC,CAAA;IACnC,uDAAqC,CAAA;IACrC,mEAAiD,CAAA;IACjD,mDAAiC,CAAA;IACjC,qEAAmD,CAAA;IACnD,iEAA+C,CAAA;AACjD,CAAC,EATW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QASzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport enum AttributeNames {\n  AWS_OPERATION = 'aws.operation',\n  AWS_REGION = 'aws.region',\n  AWS_SERVICE_API = 'aws.service.api',\n  AWS_SERVICE_NAME = 'aws.service.name',\n  AWS_SERVICE_IDENTIFIER = 'aws.service.identifier',\n  AWS_REQUEST_ID = 'aws.request.id',\n  AWS_REQUEST_EXTENDED_ID = 'aws.request.extended_id',\n  AWS_SIGNATURE_VERSION = 'aws.signature.version',\n}\n"]}