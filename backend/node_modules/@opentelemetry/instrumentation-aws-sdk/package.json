{"name": "@opentelemetry/instrumentation-aws-sdk", "version": "0.37.2", "description": "OpenTelemetry automatic instrumentation for the `aws-sdk` package", "keywords": ["aws", "aws-sdk", "nodejs", "opentelemetry"], "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-aws-sdk#readme", "license": "Apache-2.0", "author": "OpenTelemetry Authors", "bugs": {"url": "https://github.com/open-telemetry/opentelemetry-js-contrib/issues"}, "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc"], "publishConfig": {"access": "public"}, "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-aws-sdk --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json --require '@opentelemetry/contrib-test-utils' 'test/**/*.test.ts'", "test-all-versions": "tav", "version:update": "node ../../../scripts/version-update.js", "watch": "tsc -w"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/propagation-utils": "^0.30.5", "@opentelemetry/semantic-conventions": "^1.0.0"}, "devDependencies": {"@aws-sdk/client-dynamodb": "3.85.0", "@aws-sdk/client-lambda": "3.85.0", "@aws-sdk/client-s3": "3.85.0", "@aws-sdk/client-sns": "3.85.0", "@aws-sdk/client-sqs": "3.85.0", "@aws-sdk/types": "3.78.0", "@opentelemetry/api": "^1.3.0", "@opentelemetry/contrib-test-utils": "^0.35.1", "@opentelemetry/sdk-trace-base": "^1.8.0", "@types/mocha": "8.2.3", "@types/node": "18.6.5", "@types/sinon": "10.0.18", "aws-sdk": "2.1008.0", "eslint": "8.7.0", "expect": "29.2.0", "mocha": "7.2.0", "nock": "13.3.3", "nyc": "15.1.0", "rimraf": "5.0.5", "sinon": "15.2.0", "test-all-versions": "6.0.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "engines": {"node": ">=14"}, "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}