{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type * as fs from 'fs';\n\nimport type * as api from '@opentelemetry/api';\nimport type { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport type FunctionPropertyNames<T> = Exclude<\n  {\n    [K in keyof T]: T[K] extends Function ? K : never;\n  }[keyof T],\n  undefined\n>;\nexport type FunctionProperties<T> = Pick<T, FunctionPropertyNames<T>>;\n\nexport type FunctionPropertyNamesTwoLevels<T> = Exclude<\n  {\n    [K in keyof T]: {\n      [L in keyof T[K]]: L extends string\n        ? T[K][L] extends Function\n          ? K extends string\n            ? L extends string\n              ? `${K}.${L}`\n              : never\n            : never\n          : never\n        : never;\n    }[keyof T[K]];\n  }[keyof T],\n  undefined\n>;\n\nexport type Member<F> =\n  | FunctionPropertyNames<F>\n  | FunctionPropertyNamesTwoLevels<F>;\nexport type FMember =\n  | FunctionPropertyNames<typeof fs>\n  | FunctionPropertyNamesTwoLevels<typeof fs>;\nexport type FPMember =\n  | FunctionPropertyNames<(typeof fs)['promises']>\n  | FunctionPropertyNamesTwoLevels<(typeof fs)['promises']>;\n\nexport type CreateHook = (\n  functionName: FMember | FPMember,\n  info: { args: ArrayLike<unknown> }\n) => boolean;\nexport type EndHook = (\n  functionName: FMember | FPMember,\n  info: { args: ArrayLike<unknown>; span: api.Span; error?: Error }\n) => void;\n\nexport interface FsInstrumentationConfig extends InstrumentationConfig {\n  createHook?: CreateHook;\n  endHook?: EndHook;\n  requireParentSpan?: boolean;\n}\n"]}