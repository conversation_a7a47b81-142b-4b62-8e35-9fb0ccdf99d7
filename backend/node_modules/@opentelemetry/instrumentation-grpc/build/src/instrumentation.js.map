{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,uCAAoC;AACpC,uCAAkD;AAElD,6EAA6E;AAChE,QAAA,cAAc,GAAG,gBAAgB,CAAC;AAE/C,MAAa,mBAAmB;IAO9B,YAAY,MAAkC;QAJ9B,wBAAmB,GACjC,qCAAqC,CAAC;QACxB,2BAAsB,GAAW,iBAAO,CAAC;QAGvD,IAAI,CAAC,sBAAsB,GAAG,IAAI,+BAAqB,CACrD,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,EAC3B,MAAM,CACP,CAAC;IACJ,CAAC;IAEM,SAAS,CAAC,MAAkC;QACjD,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAChD,CAAC;IAED;;;;OAIG;IACI,SAAS;QACd,8FAA8F;QAC9F,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,CAAC;IACjD,CAAC;IAED,IAAI;QACF,mEAAmE;QACnE,OAAO;IACT,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC;IACvC,CAAC;IAED,OAAO;QACL,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,aAA4B;QAClD,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;IAC9D,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,cAA8B;QACrD,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAChE,CAAC;CACF;AAzDD,kDAyDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { GrpcInstrumentationConfig } from './types';\nimport type { MeterProvider, TracerProvider } from '@opentelemetry/api';\n\nimport { VERSION } from './version';\nimport { GrpcJsInstrumentation } from './grpc-js';\n\n/** The metadata key under which span context is stored as a binary value. */\nexport const GRPC_TRACE_KEY = 'grpc-trace-bin';\n\nexport class GrpcInstrumentation {\n  private _grpcJsInstrumentation: GrpcJsInstrumentation;\n\n  public readonly instrumentationName: string =\n    '@opentelemetry/instrumentation-grpc';\n  public readonly instrumentationVersion: string = VERSION;\n\n  constructor(config?: GrpcInstrumentationConfig) {\n    this._grpcJsInstrumentation = new GrpcJsInstrumentation(\n      this.instrumentationName,\n      this.instrumentationVersion,\n      config\n    );\n  }\n\n  public setConfig(config?: GrpcInstrumentationConfig) {\n    this._grpcJsInstrumentation.setConfig(config);\n  }\n\n  /**\n   * @internal\n   * Public reference to the protected BaseInstrumentation `_config` instance to be used by this\n   * plugin's external helper functions\n   */\n  public getConfig(): GrpcInstrumentationConfig {\n    // grpcNative and grpcJs have their own config copy which should be identical so just pick one\n    return this._grpcJsInstrumentation.getConfig();\n  }\n\n  init() {\n    // sub instrumentations will already be init when constructing them\n    return;\n  }\n\n  enable() {\n    this._grpcJsInstrumentation.enable();\n  }\n\n  disable() {\n    this._grpcJsInstrumentation.disable();\n  }\n\n  /**\n   * Sets MeterProvider to this plugin\n   * @param meterProvider\n   */\n  public setMeterProvider(meterProvider: MeterProvider) {\n    this._grpcJsInstrumentation.setMeterProvider(meterProvider);\n  }\n\n  /**\n   * Sets TraceProvider to this plugin\n   * @param tracerProvider\n   */\n  public setTracerProvider(tracerProvider: TracerProvider) {\n    this._grpcJsInstrumentation.setTracerProvider(tracerProvider);\n  }\n}\n"]}