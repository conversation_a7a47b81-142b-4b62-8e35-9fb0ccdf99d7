{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/grpc-js/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { EventEmitter } from 'events';\nimport type { CALL_SPAN_ENDED } from './serverUtils';\nimport type {\n  requestCallback,\n  ServerUnaryCall,\n  ServerReadableStream,\n  ServerWritableStream,\n  ServerDuplexStream,\n  Metadata,\n  Server,\n  makeGenericClientConstructor,\n} from '@grpc/grpc-js';\n\n/**\n * Server Unary callback type\n */\nexport type SendUnaryDataCallback<T> = requestCallback<T>;\n\n/**\n * Intersection type of all grpc server call types\n */\nexport type ServerCall<T, U> =\n  | ServerUnaryCall<T, U>\n  | ServerReadableStream<T, U>\n  | ServerWritableStream<T, U>\n  | ServerDuplexStream<T, U>;\n\n/**\n * {@link ServerCall} ServerCall extended with misc. missing utility types\n */\nexport type ServerCallWithMeta<T, U> = ServerCall<T, U> & {\n  metadata: Metadata;\n};\n\n/**\n * EventEmitter with span ended symbol indicator\n */\nexport type GrpcEmitter = EventEmitter & { [CALL_SPAN_ENDED]?: boolean };\n\n/**\n * Grpc client callback function extended with missing utility types\n */\nexport type GrpcClientFunc = ((...args: unknown[]) => GrpcEmitter) & {\n  path: string;\n  requestStream: boolean;\n  responseStream: boolean;\n};\n\nexport type ServerRegisterFunction = typeof Server.prototype.register;\n\nexport type ClientRequestFunction<ReturnType> = (\n  ...args: unknown[]\n) => ReturnType;\n\nexport type MakeClientConstructorFunction = typeof makeGenericClientConstructor;\n\nexport type { HandleCall } from '@grpc/grpc-js/build/src/server-call';\nexport type { PackageDefinition } from '@grpc/grpc-js/build/src/make-client';\n"]}