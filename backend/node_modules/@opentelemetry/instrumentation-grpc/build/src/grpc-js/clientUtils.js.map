{"version": 3, "file": "clientUtils.js", "sourceRoot": "", "sources": ["../../../src/grpc-js/clientUtils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAUH,4CAA0D;AAC1D,8EAAyE;AACzE,4DAAyD;AACzD,gDAAqD;AACrD,oCAIkB;AAClB,mCAAsC;AAEtC;;;GAGG;AACH,SAAgB,gBAAgB,CAE9B,MAAqB,EACrB,OAAqD;IAErD,MAAM,UAAU,GAAa,EAAE,CAAC;IAEhC,kDAAkD;IAClD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;QAC3D,IAAI,CAAC,IAAA,wBAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,iBAAiB,CAAC,EAAE;YAC/D,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,6CAA6C;YACpE,IACE,YAAY;gBACZ,iDAAiD;gBACjD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC;gBAC7C,IAAI,KAAK,YAAY,CAAC,wBAAwB;cAC9C;gBACA,4CAA4C;gBAC5C,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAC/B;SACF;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC;AACpB,CAAC;AAxBD,4CAwBC;AAED;;;GAGG;AACH,SAAgB,eAAe,CAC7B,IAAU,EACV,QAA6C;IAE7C,MAAM,SAAS,GAAwC,CACrD,GAA+B,EAC/B,GAAkB,EAClB,EAAE;QACF,IAAI,GAAG,EAAE;YACP,IAAI,GAAG,CAAC,IAAI,EAAE;gBACZ,IAAI,CAAC,SAAS,CAAC,IAAA,mCAA2B,EAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtD,IAAI,CAAC,YAAY,CAAC,yCAAkB,CAAC,oBAAoB,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;aACtE;YACD,IAAI,CAAC,aAAa,CAAC;gBACjB,CAAC,+BAAc,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC,IAAI;gBAC1C,CAAC,+BAAc,CAAC,kBAAkB,CAAC,EAAE,GAAG,CAAC,OAAO;aACjD,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,oBAAoB,EACvC,iCAAmB,CACpB,CAAC;SACH;QAED,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,OAAO,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;AACnD,CAAC;AA5BD,0CA4BC;AAED,SAAgB,0BAA0B,CACxC,IAAU,EACV,IAAkB,EAClB,eAAoC;IAEpC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,gBAAqB,EAAE,EAAE;QAC5C,eAAe,CAAC,MAAM,CAAC,uBAAuB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;AACL,CAAC;AARD,gEAQC;AAED,SAAgB,yBAAyB,CAAC,IAAU,EAAE,IAAkB;IACtE,8CAA8C;IAC9C,8CAA8C;IAC9C,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,MAAM,OAAO,GAAG,GAAG,EAAE;QACnB,IAAI,CAAC,SAAS,EAAE;YACd,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,SAAS,GAAG,IAAI,CAAC;SAClB;IACH,CAAC,CAAC;IACF,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;IACrC,IAAI,CAAC,EAAE,CAAC,qBAAY,EAAE,CAAC,GAAiB,EAAE,EAAE;QAC1C,IAAI,SAAS,EAAE;YACb,OAAO;SACR;QAED,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,IAAA,gDAAwC,EAAC,GAAG,CAAC,IAAI,CAAC;YACxD,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,CAAC;YACjB,CAAC,+BAAc,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC,IAAI;YAC1C,CAAC,+BAAc,CAAC,kBAAkB,CAAC,EAAE,GAAG,CAAC,OAAO;YAChD,CAAC,yCAAkB,CAAC,oBAAoB,CAAC,EAAE,GAAG,CAAC,IAAI;SACpD,CAAC,CAAC;QAEH,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,MAAkB,EAAE,EAAE;QACvC,IAAI,SAAS,EAAE;YACb,OAAO;SACR;QAED,IAAI,CAAC,SAAS,CAAC,IAAA,mCAA2B,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,YAAY,CAAC,yCAAkB,CAAC,oBAAoB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QAExE,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;AACL,CAAC;AAvCD,8DAuCC;AAED;;;GAGG;AACH,SAAgB,wBAAwB,CACtC,eAAoC,EACpC,QAAwB,EACxB,IAAe,EACf,QAAyB,EACzB,IAAmB;IAEnB,OAAO,CAAC,IAAU,EAAE,EAAE;QACpB,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YAC5B,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;gBAC7C,OAAO,OAAO,GAAG,KAAK,UAAU,CAAC;YACnC,CAAC,CAAC,CAAC;YACH,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE;gBAC5B,IAAI,CAAC,iBAAiB,CAAC,GAAG,eAAe,CACvC,IAAI,EACJ,IAAI,CAAC,iBAAiB,CAAwC,CAC/D,CAAC;aACH;SACF;QAED,cAAc,CAAC,QAAQ,CAAC,CAAC;QACzB,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE;YACrC,eAAe,CAAC,MAAM,CAAC,uBAAuB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC3B,yBAAyB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACvC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAlCD,4DAkCC;AAED,SAAgB,gBAAgB,CAAC,IAA+B;IAC9D,0DAA0D;IAC1D,uEAAuE;IACvE,wEAAwE;IACxE,sCAAsC;IACtC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,GAAuB,EAAE,EAAE;QAChD,OAAO,CACL,GAAG;YACH,OAAO,GAAG,KAAK,QAAQ;YACtB,GAAgB,CAAC,cAAc,CAAC,IAAI,6KAA6K;YAClN,OAAQ,GAAgB,CAAC,MAAM,KAAK,UAAU,CAC/C,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAbD,4CAaC;AAED;;;GAGG;AACH,SAAgB,uBAAuB,CACrC,OAAsB,EACtB,IAAsC,EACtC,WAAmB;IAEnB,IAAI,QAAyB,CAAC;IAC9B,MAAM,aAAa,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;QACxB,uCAAuC;QACvC,QAAQ,GAAG,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;KACvC;SAAM;QACL,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAa,CAAC;KAC5C;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAfD,0DAeC;AAED;;;GAGG;AACH,SAAgB,8BAA8B,CAC5C,UAAyB,EACzB,QAAwB,EACxB,IAAsC;IAEtC,OAAO,uBAAuB,CAC5B,UAAU,EACV,IAAI,EACJ,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/B,CAAC;AACJ,CAAC;AAVD,wEAUC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,QAAkB;IAC/C,iBAAW,CAAC,MAAM,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE;QAC7C,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;KACpC,CAAC,CAAC;AACL,CAAC;AAJD,wCAIC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { EventEmitter } from 'events';\nimport type { Span, SpanStatus } from '@opentelemetry/api';\nimport type { Client, Metadata, ServiceError } from '@grpc/grpc-js';\nimport type * as grpcJs from '@grpc/grpc-js';\nimport type { GrpcJsInstrumentation } from './';\nimport type { GrpcClientFunc, SendUnaryDataCallback } from './types';\nimport type { metadataCaptureType } from '../internal-types';\n\nimport { propagation, context } from '@opentelemetry/api';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\nimport { AttributeNames } from '../enums/AttributeNames';\nimport { GRPC_STATUS_CODE_OK } from '../status-code';\nimport {\n  _grpcStatusCodeToSpanStatus,\n  _grpcStatusCodeToOpenTelemetryStatusCode,\n  _methodIsIgnored,\n} from '../utils';\nimport { errorMonitor } from 'events';\n\n/**\n * Parse a package method list and return a list of methods to patch\n * with both possible casings e.g. \"TestMethod\" & \"testMethod\"\n */\nexport function getMethodsToWrap(\n  this: GrpcJsInstrumentation,\n  client: typeof Client,\n  methods: { [key: string]: { originalName?: string } }\n): string[] {\n  const methodList: string[] = [];\n\n  // For a method defined in .proto as \"UnaryMethod\"\n  Object.entries(methods).forEach(([name, { originalName }]) => {\n    if (!_methodIsIgnored(name, this.getConfig().ignoreGrpcMethods)) {\n      methodList.push(name); // adds camel case method name: \"unaryMethod\"\n      if (\n        originalName &&\n        // eslint-disable-next-line no-prototype-builtins\n        client.prototype.hasOwnProperty(originalName) &&\n        name !== originalName // do not add duplicates\n      ) {\n        // adds original method name: \"UnaryMethod\",\n        methodList.push(originalName);\n      }\n    }\n  });\n\n  return methodList;\n}\n\n/**\n * Patches a callback so that the current span for this trace is also ended\n * when the callback is invoked.\n */\nexport function patchedCallback(\n  span: Span,\n  callback: SendUnaryDataCallback<ResponseType>\n) {\n  const wrappedFn: SendUnaryDataCallback<ResponseType> = (\n    err: grpcJs.ServiceError | null,\n    res?: ResponseType\n  ) => {\n    if (err) {\n      if (err.code) {\n        span.setStatus(_grpcStatusCodeToSpanStatus(err.code));\n        span.setAttribute(SemanticAttributes.RPC_GRPC_STATUS_CODE, err.code);\n      }\n      span.setAttributes({\n        [AttributeNames.GRPC_ERROR_NAME]: err.name,\n        [AttributeNames.GRPC_ERROR_MESSAGE]: err.message,\n      });\n    } else {\n      span.setAttribute(\n        SemanticAttributes.RPC_GRPC_STATUS_CODE,\n        GRPC_STATUS_CODE_OK\n      );\n    }\n\n    span.end();\n    callback(err, res);\n  };\n  return context.bind(context.active(), wrappedFn);\n}\n\nexport function patchResponseMetadataEvent(\n  span: Span,\n  call: EventEmitter,\n  metadataCapture: metadataCaptureType\n) {\n  call.on('metadata', (responseMetadata: any) => {\n    metadataCapture.client.captureResponseMetadata(span, responseMetadata);\n  });\n}\n\nexport function patchResponseStreamEvents(span: Span, call: EventEmitter) {\n  // Both error and status events can be emitted\n  // the first one emitted set spanEnded to true\n  let spanEnded = false;\n  const endSpan = () => {\n    if (!spanEnded) {\n      span.end();\n      spanEnded = true;\n    }\n  };\n  context.bind(context.active(), call);\n  call.on(errorMonitor, (err: ServiceError) => {\n    if (spanEnded) {\n      return;\n    }\n\n    span.setStatus({\n      code: _grpcStatusCodeToOpenTelemetryStatusCode(err.code),\n      message: err.message,\n    });\n    span.setAttributes({\n      [AttributeNames.GRPC_ERROR_NAME]: err.name,\n      [AttributeNames.GRPC_ERROR_MESSAGE]: err.message,\n      [SemanticAttributes.RPC_GRPC_STATUS_CODE]: err.code,\n    });\n\n    endSpan();\n  });\n\n  call.on('status', (status: SpanStatus) => {\n    if (spanEnded) {\n      return;\n    }\n\n    span.setStatus(_grpcStatusCodeToSpanStatus(status.code));\n    span.setAttribute(SemanticAttributes.RPC_GRPC_STATUS_CODE, status.code);\n\n    endSpan();\n  });\n}\n\n/**\n * Execute grpc client call. Apply completitionspan properties and end the\n * span on callback or receiving an emitted event.\n */\nexport function makeGrpcClientRemoteCall(\n  metadataCapture: metadataCaptureType,\n  original: GrpcClientFunc,\n  args: unknown[],\n  metadata: grpcJs.Metadata,\n  self: grpcJs.Client\n): (span: Span) => EventEmitter {\n  return (span: Span) => {\n    // if unary or clientStream\n    if (!original.responseStream) {\n      const callbackFuncIndex = args.findIndex(arg => {\n        return typeof arg === 'function';\n      });\n      if (callbackFuncIndex !== -1) {\n        args[callbackFuncIndex] = patchedCallback(\n          span,\n          args[callbackFuncIndex] as SendUnaryDataCallback<ResponseType>\n        );\n      }\n    }\n\n    setSpanContext(metadata);\n    const call = original.apply(self, args);\n\n    call.on('metadata', responseMetadata => {\n      metadataCapture.client.captureResponseMetadata(span, responseMetadata);\n    });\n\n    // if server stream or bidi\n    if (original.responseStream) {\n      patchResponseStreamEvents(span, call);\n    }\n    return call;\n  };\n}\n\nexport function getMetadataIndex(args: Array<unknown | Metadata>): number {\n  // This finds an instance of Metadata among the arguments.\n  // A possible issue that could occur is if the 'options' parameter from\n  // the user contains an '_internal_repr' as well as a 'getMap' function,\n  // but this is an extremely rare case.\n  return args.findIndex((arg: unknown | Metadata) => {\n    return (\n      arg &&\n      typeof arg === 'object' &&\n      (arg as Metadata)['internalRepr'] && // changed from _internal_repr in grpc --> @grpc/grpc-js https://github.com/grpc/grpc-node/blob/95289edcaf36979cccf12797cc27335da8d01f03/packages/grpc-js/src/metadata.ts#L88\n      typeof (arg as Metadata).getMap === 'function'\n    );\n  });\n}\n\n/**\n * Returns the metadata argument from user provided arguments (`args`)\n * If no metadata is provided in `args`: adds empty metadata to `args` and returns that empty metadata\n */\nexport function extractMetadataOrSplice(\n  grpcLib: typeof grpcJs,\n  args: Array<unknown | grpcJs.Metadata>,\n  spliceIndex: number\n) {\n  let metadata: grpcJs.Metadata;\n  const metadataIndex = getMetadataIndex(args);\n  if (metadataIndex === -1) {\n    // Create metadata if it does not exist\n    metadata = new grpcLib.Metadata();\n    args.splice(spliceIndex, 0, metadata);\n  } else {\n    metadata = args[metadataIndex] as Metadata;\n  }\n  return metadata;\n}\n\n/**\n * Returns the metadata argument from user provided arguments (`args`)\n * Adds empty metadata to arguments if the default is used.\n */\nexport function extractMetadataOrSpliceDefault(\n  grpcClient: typeof grpcJs,\n  original: GrpcClientFunc,\n  args: Array<unknown | grpcJs.Metadata>\n): grpcJs.Metadata {\n  return extractMetadataOrSplice(\n    grpcClient,\n    args,\n    original.requestStream ? 0 : 1\n  );\n}\n\n/**\n * Inject opentelemetry trace context into `metadata` for use by another\n * grpc receiver\n * @param metadata\n */\nexport function setSpanContext(metadata: Metadata): void {\n  propagation.inject(context.active(), metadata, {\n    set: (meta, k, v) => meta.set(k, v),\n  });\n}\n"]}