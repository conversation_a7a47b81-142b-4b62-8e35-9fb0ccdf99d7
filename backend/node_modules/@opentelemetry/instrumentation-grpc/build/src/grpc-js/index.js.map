{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/grpc-js/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AA8BH,4CAQ4B;AAC5B,oEAIwC;AACxC,8EAAyE;AAEzE,+CAIuB;AACvB,+CASuB;AACvB,oCAKkB;AAClB,8DAA2D;AAE3D,MAAa,qBAAsB,SAAQ,qCAAmB;IAG5D,YACE,IAAY,EACZ,OAAe,EACf,MAAkC;QAElC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACxD,CAAC;IAED,IAAI;QACF,OAAO;YACL,IAAI,qDAAmC,CACrC,eAAe,EACf,CAAC,KAAK,CAAC,EACP,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE;gBACzB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAC;gBAChE,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;oBACtD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;iBAC1D;gBACD,uBAAuB;gBACvB,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,UAAU,EACV,IAAI,CAAC,YAAY,EAAE,CACpB,CAAC;gBACF,uBAAuB;gBACvB,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,4BAA4B,CAAC,EAAE;oBACzD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;iBAC7D;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,8BAA8B,EAC9B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CACjC,CAAC;gBACF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,qBAAqB,CAAC,EAAE;oBAClD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;iBACtD;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,uBAAuB,EACvB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CACjC,CAAC;gBACF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,qBAAqB,CAAC,EAAE;oBAClD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;iBACtD;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,uBAAuB,EACvB,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAChD,CAAC;gBACF,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;oBAC7C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;oBACjE,IAAI,CAAC,OAAO,CACV,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,yBAAyB,CAC1B,CAAC;oBACF,IAAI,CAAC,OAAO,CACV,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,yBAAyB,CAC1B,CAAC;oBACF,IAAI,CAAC,OAAO,CACV,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,uBAAuB,CACxB,CAAC;iBACH;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,kBAAkB,EAClB,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE,KAAK,CAAQ,CAC5D,CAAC;gBACF,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,yBAAyB,EACzB,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE,KAAK,CAAQ,CAC5D,CAAC;gBACF,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,yBAAyB,EACzB,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE,IAAI,CAAQ,CAC3D,CAAC;gBACF,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,uBAAuB,EACvB,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE,IAAI,CAAQ,CAC3D,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE;gBACzB,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAC;gBAEhE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBACzD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;gBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;gBAC5D,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;gBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;gBACjE,IAAI,CAAC,OAAO,CACV,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,yBAAyB,CAC1B,CAAC;gBACF,IAAI,CAAC,OAAO,CACV,aAAa,CAAC,MAAM,CAAC,SAAS,EAC9B,yBAAyB,CAC1B,CAAC;gBACF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;YACxE,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAEQ,SAAS;QAChB,OAAO,KAAK,CAAC,SAAS,EAAE,CAAC;IAC3B,CAAC;IAEQ,SAAS,CAAC,MAAkC;QACnD,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACxD,CAAC;IAED;;;OAGG;IACK,YAAY;QAGlB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,gBAAwC,EAAE,EAAE;YAClD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACnD,OAAO,SAAS,QAAQ,CAEtB,IAAY,EACZ,OAAqC,EACrC,SAA6B,EAC7B,WAAiC,EACjC,IAAY;gBAEZ,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,IAAI,CAClD,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,WAAW,EACX,IAAI,CACL,CAAC;gBACF,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAE9C,eAAe,CAAC,KAAK,CACnB,UAAU,EACV,MAAM,EACN,CAAC,YAA0C,EAAE,EAAE;oBAC7C,OAAO,SAAS,IAAI,CAElB,IAAmD,EACnD,QAAwC;wBAExC,MAAM,IAAI,GAAG,IAAI,CAAC;wBAElB,IAAI,IAAA,sCAAwB,EAAC,IAAI,EAAE,MAAM,CAAC,iBAAiB,CAAC,EAAE;4BAC5D,OAAO,IAAA,0CAA4B,EACjC,IAAI,EACJ,YAAY,EACZ,IAAI,EACJ,QAAQ,CACT,CAAC;yBACH;wBAED,MAAM,QAAQ,GAAG,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC;wBACjD,MAAM,WAAW,GAAgB;4BAC/B,IAAI,EAAE,cAAQ,CAAC,MAAM;yBACtB,CAAC;wBAEF,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,eAAe,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAC7C,CAAC;wBAEF,aAAO,CAAC,IAAI,CACV,iBAAW,CAAC,OAAO,CAAC,kBAAY,EAAE,IAAI,CAAC,QAAQ,EAAE;4BAC/C,GAAG,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;4BACnD,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;yBAC/C,CAAC,EACF,GAAG,EAAE;4BACH,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,gCAAwB,EAAC,IAAI,CAAC,CAAC;4BAE3D,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM;iCAChC,SAAS,CAAC,QAAQ,EAAE,WAAW,CAAC;iCAChC,aAAa,CAAC;gCACb,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAC7B,iCAAe,CAAC,UAAU;gCAC5B,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAAE,MAAM;gCACvC,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,OAAO;6BAC1C,CAAC,CAAC;4BAEL,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,sBAAsB,CAC5D,IAAI,EACJ,IAAI,CAAC,QAAQ,CACd,CAAC;4BAEF,eAAe,CAAC,KAAK,CACnB,IAAI,EACJ,cAAc,EACd,oBAAoB,CAAC,EAAE,CAAC,CAAC,gBAA0B,EAAE,EAAE;gCACrD,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,uBAAuB,CAC7D,IAAI,EACJ,gBAAgB,CACjB,CAAC;gCACF,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;4BACpD,CAAC,CACF,CAAC;4BAEF,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;gCACvD,kCAAoB,CAAC,IAAI,CACvB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,YAAY,EACZ,IAAI,EACJ,QAAQ,CACT,CAAC;4BACJ,CAAC,CAAC,CAAC;wBACL,CAAC,CACF,CAAC;oBACJ,CAAC,CAAC;gBACJ,CAAC,CACF,CAAC;gBACF,OAAO,sBAAsB,CAAC;YAChC,CAAqC,CAAC;QACxC,CAAC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACK,yBAAyB,CAC/B,OAAsB,EACtB,iBAA0B;QAI1B,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAA2C,EAAE,EAAE;YACrD,eAAe,CAAC,KAAK,CAAC,KAAK,CACzB,gDAAgD,CACjD,CAAC;YAEF,OAAO,SAAS,uBAAuB;gBACrC,0CAA0C;gBAC1C,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,oBAAoB,EAAE,GAC3C,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBAE7C,8DAA8D;gBAC9D,IACE,MAAM,IAAI,IAAI;oBACd,IAAA,wBAAgB,EACd,oBAAoB,EACpB,eAAe,CAAC,SAAS,EAAE,CAAC,iBAAiB,CAC9C,EACD;oBACA,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;iBAC7C;gBAED,MAAM,YAAY,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;gBACpC,MAAM,QAAQ,GAAG,IAAA,qCAAuB,EAAC,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;gBAEnE,MAAM,IAAI,GAAG,eAAe,CAAC,gBAAgB,CAC3C,IAAI,EACJ,oBAAoB,EACpB,OAAO,EACP,QAAQ,CACT,CAAC;gBACF,eAAe,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAE/C,2DAA2D;gBAC3D,IAAI,CAAC,iBAAiB,EAAE;oBACtB,4DAA4D;oBAC5D,wFAAwF;oBACxF,6DAA6D;oBAC7D,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;oBAC7C,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;oBAC5C,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;wBAClC,YAAY,CAAC,YAAY,CAAC,GAAG,IAAA,6BAAe,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;qBAC9D;iBACF;gBAED,OAAO,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;oBAC9D,IAAA,4BAAc,EAAC,QAAQ,CAAC,CAAC;oBAEzB,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;oBACrD,IAAA,wCAA0B,EACxB,IAAI,EACJ,IAAI,EACJ,eAAe,CAAC,gBAAgB,CACjC,CAAC;oBAEF,sEAAsE;oBACtE,IAAI,iBAAiB,EAAE;wBACrB,IAAA,uCAAyB,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;qBACvC;oBAED,OAAO,IAAI,CAAC;gBACd,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,YAAY,CAClB,UAAyB;QAIzB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAuC,EAAE,EAAE;YACjD,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC/C,OAAO,SAAS,qBAAqB,CAEnC,OAA0B,EAC1B,WAAmB,EACnB,OAAgB;gBAEhB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;gBAClE,eAAe,CAAC,SAAS,CACvB,MAAM,CAAC,SAAS,EAChB,8BAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,EAAE,OAAO,CAAC,EACvD,eAAe,CAAC,wBAAwB,CAAC,UAAU,CAAC,CACrD,CAAC;gBACF,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,2BAA2B,CAAC,UAAyB;QAC3D,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QAC9D,OAAO,CAAC,QAAsC,EAAE,EAAE;YAChD,OAAO,SAAS,4BAA4B,CAE1C,UAA6B;gBAE7B,MAAM,MAAM,GAAe,QAAQ,CAAC,IAAI,CACtC,IAAI,EACJ,UAAU,CACG,CAAC;gBAChB,eAAe,CAAC,mBAAmB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBACxD,OAAO,MAAM,CAAC;YAChB,CAAiC,CAAC;QACpC,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,wBAAwB,CAC9B,UAAyB;QAEzB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,QAAwB,EAAE,EAAE;YAClC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACxD,SAAS,iBAAiB;gBACxB,MAAM,IAAI,GAAG,QAAQ,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;gBAC5B,MAAM,QAAQ,GAAG,4CAA8B,CAAC,IAAI,CAClD,eAAe,EACf,UAAU,EACV,QAAQ,EACR,IAAI,CACL,CAAC;gBACF,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,gCAAwB,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAEpE,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM;qBAChC,SAAS,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,cAAQ,CAAC,MAAM,EAAE,CAAC;qBAC1C,aAAa,CAAC;oBACb,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAAE,MAAM;oBACvC,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAAE,MAAM;oBACvC,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,OAAO;iBAC1C,CAAC,CAAC;gBACL,eAAe,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAE/C,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,sBAAsB,CAC5D,IAAI,EACJ,QAAQ,CACT,CAAC;gBAEF,OAAO,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,CAC9D,IAAA,sCAAwB,EACtB,eAAe,CAAC,gBAAgB,EAChC,QAAQ,EACR,IAAI,EACJ,QAAQ,EACR,IAAI,CACL,CAAC,IAAI,CAAC,CACR,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YAC3C,OAAO,iBAAiB,CAAC;QAC3B,CAAC,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,MAAc;QACvC,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,oBAAoB,EAAE,EAAE,EAAE,CAAC;SAC5D;QACD,MAAM,IAAI,GAAG,QAAQ,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC;QAC/C,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,oBAAoB,EAAE,GAC7C,IAAA,gCAAwB,EAAC,MAAM,CAAC,CAAC;QACnC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC;IACjD,CAAC;IAEO,gBAAgB,CACtB,IAAY,EACZ,oBAA4B,EAC5B,OAAe,EACf,QAA0B;QAE1B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM;aACrB,SAAS,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,cAAQ,CAAC,MAAM,EAAE,CAAC;aAC1C,aAAa,CAAC;YACb,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAAE,MAAM;YACvC,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAAE,oBAAoB;YACrD,CAAC,yCAAkB,CAAC,WAAW,CAAC,EAAE,OAAO;SAC1C,CAAC,CAAC;QAEL,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SACrE;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB,CAAC,MAAqB,EAAE,IAAU;QAC1D,6FAA6F;QAC7F,MAAM,SAAS,GAAG,iBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC;QAClE,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,MAAM,IAAI,IAAI,EAAE;YACjD,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,aAAa,EAChC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CACzB,CAAC;YACF,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,aAAa,EAChC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CACnC,CAAC;SACH;IACH,CAAC;IAED;;;;;OAKG;IACK,mBAAmB,CACzB,UAAyB,EACzB,MAAkB;QAElB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACtC,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;gBACjC,IAAI,CAAC,SAAS,CACZ,OAAO,CAAC,SAAS,EACjB,8BAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,EACrD,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CACrD,CAAC;aACH;iBAAM,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;gBAC7C,aAAa;gBACb,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,OAAqB,CAAC,CAAC;aACxE;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,sBAAsB;;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,OAAO;YACL,MAAM,EAAE;gBACN,sBAAsB,EAAE,IAAA,uBAAe,EACrC,SAAS,EACT,MAAA,MAAA,MAAA,MAAM,CAAC,wBAAwB,0CAAE,MAAM,0CAAE,eAAe,mCAAI,EAAE,CAC/D;gBACD,uBAAuB,EAAE,IAAA,uBAAe,EACtC,UAAU,EACV,MAAA,MAAA,MAAA,MAAM,CAAC,wBAAwB,0CAAE,MAAM,0CAAE,gBAAgB,mCAAI,EAAE,CAChE;aACF;YACD,MAAM,EAAE;gBACN,sBAAsB,EAAE,IAAA,uBAAe,EACrC,SAAS,EACT,MAAA,MAAA,MAAA,MAAM,CAAC,wBAAwB,0CAAE,MAAM,0CAAE,eAAe,mCAAI,EAAE,CAC/D;gBACD,uBAAuB,EAAE,IAAA,uBAAe,EACtC,UAAU,EACV,MAAA,MAAA,MAAA,MAAM,CAAC,wBAAwB,0CAAE,MAAM,0CAAE,gBAAgB,mCAAI,EAAE,CAChE;aACF;SACF,CAAC;IACJ,CAAC;CACF;AA3fD,sDA2fC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { EventEmitter } from 'events';\n\nimport type {\n  Server,\n  serialize as Serialize,\n  deserialize as Deserialize,\n  Metadata,\n  Client,\n  ServiceDefinition,\n  loadPackageDefinition,\n  GrpcObject,\n} from '@grpc/grpc-js';\n\nimport type * as grpcJs from '@grpc/grpc-js';\n\nimport type {\n  ServerCallWithMeta,\n  SendUnaryDataCallback,\n  ServerRegisterFunction,\n  HandleCall,\n  MakeClientConstructorFunction,\n  PackageDefinition,\n  GrpcClientFunc,\n  ClientRequestFunction,\n} from './types';\nimport type { GrpcInstrumentationConfig } from '../types';\nimport type { metadataCaptureType } from '../internal-types';\n\nimport {\n  context,\n  propagation,\n  ROOT_CONTEXT,\n  SpanOptions,\n  SpanKind,\n  trace,\n  Span,\n} from '@opentelemetry/api';\nimport {\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n  InstrumentationBase,\n} from '@opentelemetry/instrumentation';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\n\nimport {\n  shouldNotTraceServerCall,\n  handleServerFunction,\n  handleUntracedServerFunction,\n} from './serverUtils';\nimport {\n  getMethodsToWrap,\n  makeGrpcClientRemoteCall,\n  extractMetadataOrSpliceDefault,\n  setSpanContext,\n  patchedCallback,\n  patchResponseStreamEvents,\n  patchResponseMetadataEvent,\n  extractMetadataOrSplice,\n} from './clientUtils';\nimport {\n  _extractMethodAndService,\n  metadataCapture,\n  URI_REGEX,\n  _methodIsIgnored,\n} from '../utils';\nimport { AttributeValues } from '../enums/AttributeValues';\n\nexport class GrpcJsInstrumentation extends InstrumentationBase {\n  private _metadataCapture: metadataCaptureType;\n\n  constructor(\n    name: string,\n    version: string,\n    config?: GrpcInstrumentationConfig\n  ) {\n    super(name, version, config);\n    this._metadataCapture = this._createMetadataCapture();\n  }\n\n  init() {\n    return [\n      new InstrumentationNodeModuleDefinition<typeof grpcJs>(\n        '@grpc/grpc-js',\n        ['1.*'],\n        (moduleExports, version) => {\n          this._diag.debug(`Applying patch for @grpc/grpc-js@${version}`);\n          if (isWrapped(moduleExports.Server.prototype.register)) {\n            this._unwrap(moduleExports.Server.prototype, 'register');\n          }\n          // Patch Server methods\n          this._wrap(\n            moduleExports.Server.prototype,\n            'register',\n            this._patchServer()\n          );\n          // Patch Client methods\n          if (isWrapped(moduleExports.makeGenericClientConstructor)) {\n            this._unwrap(moduleExports, 'makeGenericClientConstructor');\n          }\n          this._wrap(\n            moduleExports,\n            'makeGenericClientConstructor',\n            this._patchClient(moduleExports)\n          );\n          if (isWrapped(moduleExports.makeClientConstructor)) {\n            this._unwrap(moduleExports, 'makeClientConstructor');\n          }\n          this._wrap(\n            moduleExports,\n            'makeClientConstructor',\n            this._patchClient(moduleExports)\n          );\n          if (isWrapped(moduleExports.loadPackageDefinition)) {\n            this._unwrap(moduleExports, 'loadPackageDefinition');\n          }\n          this._wrap(\n            moduleExports,\n            'loadPackageDefinition',\n            this._patchLoadPackageDefinition(moduleExports)\n          );\n          if (isWrapped(moduleExports.Client.prototype)) {\n            this._unwrap(moduleExports.Client.prototype, 'makeUnaryRequest');\n            this._unwrap(\n              moduleExports.Client.prototype,\n              'makeClientStreamRequest'\n            );\n            this._unwrap(\n              moduleExports.Client.prototype,\n              'makeServerStreamRequest'\n            );\n            this._unwrap(\n              moduleExports.Client.prototype,\n              'makeBidiStreamRequest'\n            );\n          }\n          this._wrap(\n            moduleExports.Client.prototype,\n            'makeUnaryRequest',\n            this._patchClientRequestMethod(moduleExports, false) as any\n          );\n          this._wrap(\n            moduleExports.Client.prototype,\n            'makeClientStreamRequest',\n            this._patchClientRequestMethod(moduleExports, false) as any\n          );\n          this._wrap(\n            moduleExports.Client.prototype,\n            'makeServerStreamRequest',\n            this._patchClientRequestMethod(moduleExports, true) as any\n          );\n          this._wrap(\n            moduleExports.Client.prototype,\n            'makeBidiStreamRequest',\n            this._patchClientRequestMethod(moduleExports, true) as any\n          );\n          return moduleExports;\n        },\n        (moduleExports, version) => {\n          if (moduleExports === undefined) return;\n          this._diag.debug(`Removing patch for @grpc/grpc-js@${version}`);\n\n          this._unwrap(moduleExports.Server.prototype, 'register');\n          this._unwrap(moduleExports, 'makeClientConstructor');\n          this._unwrap(moduleExports, 'makeGenericClientConstructor');\n          this._unwrap(moduleExports, 'loadPackageDefinition');\n          this._unwrap(moduleExports.Client.prototype, 'makeUnaryRequest');\n          this._unwrap(\n            moduleExports.Client.prototype,\n            'makeClientStreamRequest'\n          );\n          this._unwrap(\n            moduleExports.Client.prototype,\n            'makeServerStreamRequest'\n          );\n          this._unwrap(moduleExports.Client.prototype, 'makeBidiStreamRequest');\n        }\n      ),\n    ];\n  }\n\n  override getConfig(): GrpcInstrumentationConfig {\n    return super.getConfig();\n  }\n\n  override setConfig(config?: GrpcInstrumentationConfig): void {\n    super.setConfig(config);\n    this._metadataCapture = this._createMetadataCapture();\n  }\n\n  /**\n   * Patch for grpc.Server.prototype.register(...) function. Provides auto-instrumentation for\n   * client_stream, server_stream, bidi, unary server handler calls.\n   */\n  private _patchServer(): (\n    originalRegister: ServerRegisterFunction\n  ) => ServerRegisterFunction {\n    const instrumentation = this;\n    return (originalRegister: ServerRegisterFunction) => {\n      const config = this.getConfig();\n      instrumentation._diag.debug('patched gRPC server');\n      return function register<RequestType, ResponseType>(\n        this: Server,\n        name: string,\n        handler: HandleCall<unknown, unknown>,\n        serialize: Serialize<unknown>,\n        deserialize: Deserialize<unknown>,\n        type: string\n      ): boolean {\n        const originalRegisterResult = originalRegister.call(\n          this,\n          name,\n          handler,\n          serialize,\n          deserialize,\n          type\n        );\n        const handlerSet = this['handlers'].get(name);\n\n        instrumentation._wrap(\n          handlerSet,\n          'func',\n          (originalFunc: HandleCall<unknown, unknown>) => {\n            return function func(\n              this: typeof handlerSet,\n              call: ServerCallWithMeta<RequestType, ResponseType>,\n              callback: SendUnaryDataCallback<unknown>\n            ) {\n              const self = this;\n\n              if (shouldNotTraceServerCall(name, config.ignoreGrpcMethods)) {\n                return handleUntracedServerFunction(\n                  type,\n                  originalFunc,\n                  call,\n                  callback\n                );\n              }\n\n              const spanName = `grpc.${name.replace('/', '')}`;\n              const spanOptions: SpanOptions = {\n                kind: SpanKind.SERVER,\n              };\n\n              instrumentation._diag.debug(\n                `patch func: ${JSON.stringify(spanOptions)}`\n              );\n\n              context.with(\n                propagation.extract(ROOT_CONTEXT, call.metadata, {\n                  get: (carrier, key) => carrier.get(key).map(String),\n                  keys: carrier => Object.keys(carrier.getMap()),\n                }),\n                () => {\n                  const { service, method } = _extractMethodAndService(name);\n\n                  const span = instrumentation.tracer\n                    .startSpan(spanName, spanOptions)\n                    .setAttributes({\n                      [SemanticAttributes.RPC_SYSTEM]:\n                        AttributeValues.RPC_SYSTEM,\n                      [SemanticAttributes.RPC_METHOD]: method,\n                      [SemanticAttributes.RPC_SERVICE]: service,\n                    });\n\n                  instrumentation._metadataCapture.server.captureRequestMetadata(\n                    span,\n                    call.metadata\n                  );\n\n                  instrumentation._wrap(\n                    call,\n                    'sendMetadata',\n                    originalSendMetadata => (responseMetadata: Metadata) => {\n                      instrumentation._metadataCapture.server.captureResponseMetadata(\n                        span,\n                        responseMetadata\n                      );\n                      originalSendMetadata.call(call, responseMetadata);\n                    }\n                  );\n\n                  context.with(trace.setSpan(context.active(), span), () => {\n                    handleServerFunction.call(\n                      self,\n                      span,\n                      type,\n                      originalFunc,\n                      call,\n                      callback\n                    );\n                  });\n                }\n              );\n            };\n          }\n        );\n        return originalRegisterResult;\n      } as typeof Server.prototype.register;\n    };\n  }\n\n  /**\n   * Patch for grpc.Client.make*Request(...) functions.\n   * Provides auto-instrumentation for client requests when using a Client without\n   * makeGenericClientConstructor/makeClientConstructor\n   */\n  private _patchClientRequestMethod<ReturnType extends EventEmitter>(\n    grpcLib: typeof grpcJs,\n    hasResponseStream: boolean\n  ): (\n    original: ClientRequestFunction<ReturnType>\n  ) => ClientRequestFunction<ReturnType> {\n    const instrumentation = this;\n    return (original: ClientRequestFunction<ReturnType>) => {\n      instrumentation._diag.debug(\n        'patched makeClientStreamRequest on grpc client'\n      );\n\n      return function makeClientStreamRequest(this: grpcJs.Client) {\n        // method must always be at first position\n        const method = arguments[0];\n        const { name, service, methodAttributeValue } =\n          instrumentation._splitMethodString(method);\n\n        // Do not attempt to trace/inject context if method is ignored\n        if (\n          method != null &&\n          _methodIsIgnored(\n            methodAttributeValue,\n            instrumentation.getConfig().ignoreGrpcMethods\n          )\n        ) {\n          return original.apply(this, [...arguments]);\n        }\n\n        const modifiedArgs = [...arguments];\n        const metadata = extractMetadataOrSplice(grpcLib, modifiedArgs, 4);\n\n        const span = instrumentation.createClientSpan(\n          name,\n          methodAttributeValue,\n          service,\n          metadata\n        );\n        instrumentation.extractNetMetadata(this, span);\n\n        // Callback is only present when there is no responseStream\n        if (!hasResponseStream) {\n          // Replace the callback with the patched one if it is there.\n          // If the callback arg is not a function on the last position then the client will throw\n          // and never call the callback -> so there's nothing to patch\n          const lastArgIndex = modifiedArgs.length - 1;\n          const callback = modifiedArgs[lastArgIndex];\n          if (typeof callback === 'function') {\n            modifiedArgs[lastArgIndex] = patchedCallback(span, callback);\n          }\n        }\n\n        return context.with(trace.setSpan(context.active(), span), () => {\n          setSpanContext(metadata);\n\n          const call = original.apply(this, [...modifiedArgs]);\n          patchResponseMetadataEvent(\n            span,\n            call,\n            instrumentation._metadataCapture\n          );\n\n          // Subscribe to response stream events when there's a response stream.\n          if (hasResponseStream) {\n            patchResponseStreamEvents(span, call);\n          }\n\n          return call;\n        });\n      };\n    };\n  }\n\n  /**\n   * Entry point for applying client patches to `grpc.makeClientConstructor(...)` equivalents\n   * @param this GrpcJsPlugin\n   */\n  private _patchClient(\n    grpcClient: typeof grpcJs\n  ): (\n    original: MakeClientConstructorFunction\n  ) => MakeClientConstructorFunction {\n    const instrumentation = this;\n    return (original: MakeClientConstructorFunction) => {\n      instrumentation._diag.debug('patching client');\n      return function makeClientConstructor(\n        this: typeof Client,\n        methods: ServiceDefinition,\n        serviceName: string,\n        options?: object\n      ) {\n        const client = original.call(this, methods, serviceName, options);\n        instrumentation._massWrap<typeof client.prototype, string>(\n          client.prototype,\n          getMethodsToWrap.call(instrumentation, client, methods),\n          instrumentation._getPatchedClientMethods(grpcClient)\n        );\n        return client;\n      };\n    };\n  }\n\n  /**\n   * Entry point for client patching for grpc.loadPackageDefinition(...)\n   * @param this - GrpcJsPlugin\n   */\n  private _patchLoadPackageDefinition(grpcClient: typeof grpcJs) {\n    const instrumentation = this;\n    instrumentation._diag.debug('patching loadPackageDefinition');\n    return (original: typeof loadPackageDefinition) => {\n      return function patchedLoadPackageDefinition(\n        this: null,\n        packageDef: PackageDefinition\n      ) {\n        const result: GrpcObject = original.call(\n          this,\n          packageDef\n        ) as GrpcObject;\n        instrumentation._patchLoadedPackage(grpcClient, result);\n        return result;\n      } as typeof loadPackageDefinition;\n    };\n  }\n\n  /**\n   * Parse initial client call properties and start a span to trace its execution\n   */\n  private _getPatchedClientMethods(\n    grpcClient: typeof grpcJs\n  ): (original: GrpcClientFunc) => () => EventEmitter {\n    const instrumentation = this;\n    return (original: GrpcClientFunc) => {\n      instrumentation._diag.debug('patch all client methods');\n      function clientMethodTrace(this: Client) {\n        const name = `grpc.${original.path.replace('/', '')}`;\n        const args = [...arguments];\n        const metadata = extractMetadataOrSpliceDefault.call(\n          instrumentation,\n          grpcClient,\n          original,\n          args\n        );\n        const { service, method } = _extractMethodAndService(original.path);\n\n        const span = instrumentation.tracer\n          .startSpan(name, { kind: SpanKind.CLIENT })\n          .setAttributes({\n            [SemanticAttributes.RPC_SYSTEM]: 'grpc',\n            [SemanticAttributes.RPC_METHOD]: method,\n            [SemanticAttributes.RPC_SERVICE]: service,\n          });\n        instrumentation.extractNetMetadata(this, span);\n\n        instrumentation._metadataCapture.client.captureRequestMetadata(\n          span,\n          metadata\n        );\n\n        return context.with(trace.setSpan(context.active(), span), () =>\n          makeGrpcClientRemoteCall(\n            instrumentation._metadataCapture,\n            original,\n            args,\n            metadata,\n            this\n          )(span)\n        );\n      }\n      Object.assign(clientMethodTrace, original);\n      return clientMethodTrace;\n    };\n  }\n\n  private _splitMethodString(method: string) {\n    if (method == null) {\n      return { name: '', service: '', methodAttributeValue: '' };\n    }\n    const name = `grpc.${method.replace('/', '')}`;\n    const { service, method: methodAttributeValue } =\n      _extractMethodAndService(method);\n    return { name, service, methodAttributeValue };\n  }\n\n  private createClientSpan(\n    name: string,\n    methodAttributeValue: string,\n    service: string,\n    metadata?: grpcJs.Metadata\n  ) {\n    const span = this.tracer\n      .startSpan(name, { kind: SpanKind.CLIENT })\n      .setAttributes({\n        [SemanticAttributes.RPC_SYSTEM]: 'grpc',\n        [SemanticAttributes.RPC_METHOD]: methodAttributeValue,\n        [SemanticAttributes.RPC_SERVICE]: service,\n      });\n\n    if (metadata != null) {\n      this._metadataCapture.client.captureRequestMetadata(span, metadata);\n    }\n    return span;\n  }\n\n  private extractNetMetadata(client: grpcJs.Client, span: Span) {\n    // set net.peer.* from target (e.g., \"dns:otel-productcatalogservice:8080\") as a hint to APMs\n    const parsedUri = URI_REGEX.exec(client.getChannel().getTarget());\n    if (parsedUri != null && parsedUri.groups != null) {\n      span.setAttribute(\n        SemanticAttributes.NET_PEER_NAME,\n        parsedUri.groups['name']\n      );\n      span.setAttribute(\n        SemanticAttributes.NET_PEER_PORT,\n        parseInt(parsedUri.groups['port'])\n      );\n    }\n  }\n\n  /**\n   * Utility function to patch *all* functions loaded through a proto file.\n   * Recursively searches for Client classes and patches all methods, reversing the\n   * parsing done by grpc.loadPackageDefinition\n   * https://github.com/grpc/grpc-node/blob/1d14203c382509c3f36132bd0244c99792cb6601/packages/grpc-js/src/make-client.ts#L200-L217\n   */\n  private _patchLoadedPackage(\n    grpcClient: typeof grpcJs,\n    result: GrpcObject\n  ): void {\n    Object.values(result).forEach(service => {\n      if (typeof service === 'function') {\n        this._massWrap<typeof service.prototype, string>(\n          service.prototype,\n          getMethodsToWrap.call(this, service, service.service),\n          this._getPatchedClientMethods.call(this, grpcClient)\n        );\n      } else if (typeof service.format !== 'string') {\n        // GrpcObject\n        this._patchLoadedPackage.call(this, grpcClient, service as GrpcObject);\n      }\n    });\n  }\n\n  private _createMetadataCapture(): metadataCaptureType {\n    const config = this.getConfig();\n\n    return {\n      client: {\n        captureRequestMetadata: metadataCapture(\n          'request',\n          config.metadataToSpanAttributes?.client?.requestMetadata ?? []\n        ),\n        captureResponseMetadata: metadataCapture(\n          'response',\n          config.metadataToSpanAttributes?.client?.responseMetadata ?? []\n        ),\n      },\n      server: {\n        captureRequestMetadata: metadataCapture(\n          'request',\n          config.metadataToSpanAttributes?.server?.requestMetadata ?? []\n        ),\n        captureResponseMetadata: metadataCapture(\n          'response',\n          config.metadataToSpanAttributes?.server?.responseMetadata ?? []\n        ),\n      },\n    };\n  }\n}\n"]}