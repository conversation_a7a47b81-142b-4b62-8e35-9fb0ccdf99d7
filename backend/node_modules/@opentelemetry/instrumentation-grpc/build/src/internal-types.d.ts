import type { Span } from '@opentelemetry/api';
import type { Metadata } from '@grpc/grpc-js';
export declare type metadataCaptureType = {
    client: {
        captureRequestMetadata: (span: Span, metadata: Metadata) => void;
        captureResponseMetadata: (span: Span, metadata: Metadata) => void;
    };
    server: {
        captureRequestMetadata: (span: Span, metadata: Metadata) => void;
        captureResponseMetadata: (span: Span, metadata: Metadata) => void;
    };
};
//# sourceMappingURL=internal-types.d.ts.map