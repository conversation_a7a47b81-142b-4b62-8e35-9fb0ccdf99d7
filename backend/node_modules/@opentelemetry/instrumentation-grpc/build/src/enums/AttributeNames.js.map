{"version": 3, "file": "AttributeNames.js", "sourceRoot": "", "sources": ["../../../src/enums/AttributeNames.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAWU,QAAA,cAAc,GAA6B;IACtD,eAAe,EAAE,iBAAiB;IAClC,kBAAkB,EAAE,oBAAoB;CACzC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * https://github.com/open-telemetry/opentelemetry-specification/blob/master/specification/trace/semantic_conventions/http.md\n */\n\ninterface AttributesType {\n  GRPC_ERROR_NAME: string;\n  GRPC_ERROR_MESSAGE: string;\n}\n\nexport const AttributeNames: Readonly<AttributesType> = {\n  GRPC_ERROR_NAME: 'grpc.error_name',\n  GRPC_ERROR_MESSAGE: 'grpc.error_message',\n};\n"]}