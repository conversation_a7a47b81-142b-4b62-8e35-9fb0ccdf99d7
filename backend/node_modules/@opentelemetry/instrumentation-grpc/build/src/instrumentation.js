"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcInstrumentation = exports.GRPC_TRACE_KEY = void 0;
const version_1 = require("./version");
const grpc_js_1 = require("./grpc-js");
/** The metadata key under which span context is stored as a binary value. */
exports.GRPC_TRACE_KEY = 'grpc-trace-bin';
class GrpcInstrumentation {
    constructor(config) {
        this.instrumentationName = '@opentelemetry/instrumentation-grpc';
        this.instrumentationVersion = version_1.VERSION;
        this._grpcJsInstrumentation = new grpc_js_1.GrpcJsInstrumentation(this.instrumentationName, this.instrumentationVersion, config);
    }
    setConfig(config) {
        this._grpcJsInstrumentation.setConfig(config);
    }
    /**
     * @internal
     * Public reference to the protected BaseInstrumentation `_config` instance to be used by this
     * plugin's external helper functions
     */
    getConfig() {
        // grpcNative and grpcJs have their own config copy which should be identical so just pick one
        return this._grpcJsInstrumentation.getConfig();
    }
    init() {
        // sub instrumentations will already be init when constructing them
        return;
    }
    enable() {
        this._grpcJsInstrumentation.enable();
    }
    disable() {
        this._grpcJsInstrumentation.disable();
    }
    /**
     * Sets MeterProvider to this plugin
     * @param meterProvider
     */
    setMeterProvider(meterProvider) {
        this._grpcJsInstrumentation.setMeterProvider(meterProvider);
    }
    /**
     * Sets TraceProvider to this plugin
     * @param tracerProvider
     */
    setTracerProvider(tracerProvider) {
        this._grpcJsInstrumentation.setTracerProvider(tracerProvider);
    }
}
exports.GrpcInstrumentation = GrpcInstrumentation;
//# sourceMappingURL=instrumentation.js.map