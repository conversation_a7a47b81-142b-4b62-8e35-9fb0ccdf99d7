import type { GrpcInstrumentationConfig } from './types';
import type { <PERSON>er<PERSON><PERSON>ider, TracerProvider } from '@opentelemetry/api';
/** The metadata key under which span context is stored as a binary value. */
export declare const GRPC_TRACE_KEY = "grpc-trace-bin";
export declare class GrpcInstrumentation {
    private _grpcJsInstrumentation;
    readonly instrumentationName: string;
    readonly instrumentationVersion: string;
    constructor(config?: GrpcInstrumentationConfig);
    setConfig(config?: GrpcInstrumentationConfig): void;
    /**
     * @internal
     * Public reference to the protected BaseInstrumentation `_config` instance to be used by this
     * plugin's external helper functions
     */
    getConfig(): GrpcInstrumentationConfig;
    init(): void;
    enable(): void;
    disable(): void;
    /**
     * Sets MeterProvider to this plugin
     * @param meterProvider
     */
    setMeterProvider(meterProvider: MeterProvider): void;
    /**
     * Sets TraceProvider to this plugin
     * @param tracerProvider
     */
    setTracerProvider(tracerProvider: TracerProvider): void;
}
//# sourceMappingURL=instrumentation.d.ts.map