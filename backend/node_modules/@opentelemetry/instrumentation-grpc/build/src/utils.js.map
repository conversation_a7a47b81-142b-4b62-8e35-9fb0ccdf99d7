{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAoD;AAKpD,uGAAuG;AAC1F,QAAA,SAAS,GACpB,8EAA8E,CAAC;AAEjF,mCAAmC;AAC5B,MAAM,SAAS,GAAsD,CAC1E,IAAI,EACJ,EAAE,EACF,EAAE;IACF,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;IACf,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,KAAK,EAAE,CAAC;QACR,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE;YACX,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC;AAZW,QAAA,SAAS,aAYpB;AAEF;;;GAGG;AACI,MAAM,wCAAwC,GAAG,CACtD,MAAmB,EACH,EAAE;IAClB,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,CAAC,EAAE;QACxC,OAAO,oBAAc,CAAC,KAAK,CAAC;KAC7B;IACD,OAAO,oBAAc,CAAC,KAAK,CAAC;AAC9B,CAAC,CAAC;AAPW,QAAA,wCAAwC,4CAOnD;AAEK,MAAM,2BAA2B,GAAG,CAAC,MAAc,EAAc,EAAE;IACxE,OAAO,EAAE,IAAI,EAAE,IAAA,gDAAwC,EAAC,MAAM,CAAC,EAAE,CAAC;AACpE,CAAC,CAAC;AAFW,QAAA,2BAA2B,+BAEtC;AAEF;;;;GAIG;AACH,MAAM,iBAAiB,GAAG,CACxB,UAAkB,EAClB,OAAsB,EACb,EAAE;IACX,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,OAAO,OAAO,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,WAAW,EAAE,CAAC;KAC3D;SAAM,IAAI,OAAO,YAAY,MAAM,EAAE;QACpC,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACjC;SAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;QACxC,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC;KAC5B;SAAM;QACL,OAAO,KAAK,CAAC;KACd;AACH,CAAC,CAAC;AAEF;;;;;;GAMG;AACI,MAAM,gBAAgB,GAAG,CAC9B,UAAkB,EAClB,cAAgC,EACvB,EAAE;IACX,IAAI,CAAC,cAAc,EAAE;QACnB,0BAA0B;QAC1B,OAAO,KAAK,CAAC;KACd;IAED,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE;QACpC,IAAI,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE;YAC1C,OAAO,IAAI,CAAC;SACb;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAhBW,QAAA,gBAAgB,oBAgB3B;AAEF;;;GAGG;AACI,MAAM,wBAAwB,GAAG,CACtC,IAAY,EACyB,EAAE;IACvC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACzD,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;IAC5C,MAAM,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEvC,OAAO;QACL,OAAO;QACP,MAAM;KACP,CAAC;AACJ,CAAC,CAAC;AAXW,QAAA,wBAAwB,4BAWnC;AAEF,SAAgB,eAAe,CAC7B,IAA4B,EAC5B,aAAuB;IAEvB,MAAM,4BAA4B,GAAG,IAAI,GAAG,CAC1C,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,KAAK,CAAC,WAAW,EAAE;QACnB,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;KACvC,CAAC,CACH,CAAC;IAEF,OAAO,CAAC,IAAU,EAAE,QAAkB,EAAE,EAAE;QACxC,KAAK,MAAM,CACT,gBAAgB,EAChB,kBAAkB,EACnB,IAAI,4BAA4B,EAAE;YACjC,MAAM,cAAc,GAAG,QAAQ;iBAC5B,GAAG,CAAC,gBAAgB,CAAC;iBACrB,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEzE,IAAI,cAAc,KAAK,SAAS,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC/D,SAAS;aACV;YAED,MAAM,GAAG,GAAG,OAAO,IAAI,aAAa,kBAAkB,EAAE,CAAC;YAEzD,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;SACxC;IACH,CAAC,CAAC;AACJ,CAAC;AA7BD,0CA6BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanStatusCode } from '@opentelemetry/api';\nimport type { SpanStatus, Span } from '@opentelemetry/api';\nimport type { status as GrpcStatus, Metadata } from '@grpc/grpc-js';\nimport type { IgnoreMatcher } from './types';\n\n// e.g., \"dns:otel-productcatalogservice:8080\" or \"otel-productcatalogservice:8080\" or \"127.0.0.1:8080\"\nexport const URI_REGEX =\n  /(?:([A-Za-z0-9+.-]+):(?:\\/\\/)?)?(?<name>[A-Za-z0-9+.-]+):(?<port>[0-9+.-]+)$/;\n\n// Equivalent to lodash _.findIndex\nexport const findIndex: <T>(args: T[], fn: (arg: T) => boolean) => number = (\n  args,\n  fn\n) => {\n  let index = -1;\n  for (const arg of args) {\n    index++;\n    if (fn(arg)) {\n      return index;\n    }\n  }\n  return -1;\n};\n\n/**\n * Convert a grpc status code to an opentelemetry SpanStatus code.\n * @param status\n */\nexport const _grpcStatusCodeToOpenTelemetryStatusCode = (\n  status?: GrpcStatus\n): SpanStatusCode => {\n  if (status !== undefined && status === 0) {\n    return SpanStatusCode.UNSET;\n  }\n  return SpanStatusCode.ERROR;\n};\n\nexport const _grpcStatusCodeToSpanStatus = (status: number): SpanStatus => {\n  return { code: _grpcStatusCodeToOpenTelemetryStatusCode(status) };\n};\n\n/**\n * Returns true if methodName matches pattern\n * @param methodName the name of the method\n * @param pattern Match pattern\n */\nconst _satisfiesPattern = (\n  methodName: string,\n  pattern: IgnoreMatcher\n): boolean => {\n  if (typeof pattern === 'string') {\n    return pattern.toLowerCase() === methodName.toLowerCase();\n  } else if (pattern instanceof RegExp) {\n    return pattern.test(methodName);\n  } else if (typeof pattern === 'function') {\n    return pattern(methodName);\n  } else {\n    return false;\n  }\n};\n\n/**\n * Returns true if the current plugin configuration\n * ignores the given method.\n * @param methodName the name of the method\n * @param ignoredMethods a list of matching patterns\n * @param onException an error handler for matching exceptions\n */\nexport const _methodIsIgnored = (\n  methodName: string,\n  ignoredMethods?: IgnoreMatcher[]\n): boolean => {\n  if (!ignoredMethods) {\n    // No ignored gRPC methods\n    return false;\n  }\n\n  for (const pattern of ignoredMethods) {\n    if (_satisfiesPattern(methodName, pattern)) {\n      return true;\n    }\n  }\n\n  return false;\n};\n\n/**\n * Return method and service values getting from grpc name/path\n * @param name the grpc name/path\n */\nexport const _extractMethodAndService = (\n  name: string\n): { service: string; method: string } => {\n  const serviceMethod = name.replace(/^\\//, '').split('/');\n  const service = serviceMethod.shift() || '';\n  const method = serviceMethod.join('/');\n\n  return {\n    service,\n    method,\n  };\n};\n\nexport function metadataCapture(\n  type: 'request' | 'response',\n  metadataToAdd: string[]\n) {\n  const normalizedMetadataAttributes = new Map(\n    metadataToAdd.map(value => [\n      value.toLowerCase(),\n      value.toLowerCase().replace(/-/g, '_'),\n    ])\n  );\n\n  return (span: Span, metadata: Metadata) => {\n    for (const [\n      capturedMetadata,\n      normalizedMetadata,\n    ] of normalizedMetadataAttributes) {\n      const metadataValues = metadata\n        .get(capturedMetadata)\n        .flatMap(value => (typeof value === 'string' ? value.toString() : []));\n\n      if (metadataValues === undefined || metadataValues.length === 0) {\n        continue;\n      }\n\n      const key = `rpc.${type}.metadata.${normalizedMetadata}`;\n\n      span.setAttribute(key, metadataValues);\n    }\n  };\n}\n"]}