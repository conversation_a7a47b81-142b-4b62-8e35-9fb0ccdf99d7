{"name": "@opentelemetry/instrumentation-grpc", "version": "0.46.0", "description": "OpenTelemetry grpc automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build", "clean": "tsc --build --clean", "test": "npm run protos:generate && nyc ts-mocha -p tsconfig.json test/**/*.test.ts", "tdd": "npm run test -- --watch-extensions ts --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "version": "node ../../../scripts/version-update.js", "watch": "tsc --build --watch", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "node ../../../scripts/version-update.js", "peer-api-check": "node ../../../scripts/peer-api-check.js", "protos:generate": "cd test/fixtures && buf generate"}, "keywords": ["opentelemetry", "grpc", "nodejs", "tracing", "profiling", "instrumentation"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@bufbuild/buf": "1.21.0-1", "@grpc/grpc-js": "^1.7.1", "@grpc/proto-loader": "^0.7.10", "@opentelemetry/api": "1.7.0", "@opentelemetry/context-async-hooks": "1.19.0", "@opentelemetry/core": "1.19.0", "@opentelemetry/sdk-trace-base": "1.19.0", "@opentelemetry/sdk-trace-node": "1.19.0", "@protobuf-ts/grpc-transport": "2.9.3", "@protobuf-ts/runtime": "2.9.3", "@protobuf-ts/runtime-rpc": "2.9.3", "@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/semver": "7.5.6", "@types/sinon": "10.0.20", "codecov": "3.8.3", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "semver": "7.5.4", "sinon": "15.1.2", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "dependencies": {"@opentelemetry/instrumentation": "0.46.0", "@opentelemetry/semantic-conventions": "1.19.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/opentelemetry-instrumentation-grpc", "sideEffects": false, "gitHead": "d3c311aec24137084dc820805a2597e120335672"}