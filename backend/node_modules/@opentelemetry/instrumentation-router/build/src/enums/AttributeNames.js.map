{"version": 3, "file": "AttributeNames.js", "sourceRoot": "", "sources": ["../../../src/enums/AttributeNames.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,IAAY,cAKX;AALD,WAAY,cAAc;IACxB,sCAAoB,CAAA;IACpB,sCAAoB,CAAA;IACpB,0CAAwB,CAAA;IACxB,4CAA0B,CAAA;AAC5B,CAAC,EALW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAKzB;AAED,kBAAe,cAAc,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum AttributeNames {\n  TYPE = 'router.type',\n  NAME = 'router.name',\n  METHOD = 'router.method',\n  VERSION = 'router.version',\n}\n\nexport default AttributeNames;\n"]}