{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;AAEH,0CAA0C;AAC1C,oEAMwC;AACxC,8EAAyE;AAMzE,uCAAoC;AACpC,yCAAyC;AACzC,iCAAiC;AACjC,2DAAoD;AACpD,iDAA0C;AAE1C,MAAqB,qBAAsB,SAAQ,qCAAwB;IACzE,YAAY,MAA8B;QACxC,KAAK,CACH,kCAAkC,SAAS,CAAC,WAAW,EAAE,EACzD,iBAAO,EACP,MAAM,CACP,CAAC;IACJ,CAAC;IAID,IAAI;QACF,MAAM,MAAM,GAAG,IAAI,qDAAmC,CACpD,SAAS,CAAC,WAAW,EACrB,SAAS,CAAC,kBAAkB,EAC5B,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,sBAAsB,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CAC/D,CAAC;YACF,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;YACpC,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,sBAAsB,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CAC/D,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,CACF,CAAC;QAEF,MAAM,CAAC,KAAK,CAAC,IAAI,CACf,IAAI,+CAA6B,CAC/B,qBAAqB,EACrB,SAAS,CAAC,kBAAkB,EAC5B,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,wCAAwC,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CACjF,CAAC;YACF,MAAM,KAAK,GAAQ,aAAa,CAAC;YACjC,IAAI,IAAA,2BAAS,EAAC,KAAK,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;gBAC7C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;aACjD;YACD,IAAI,CAAC,KAAK,CACR,KAAK,CAAC,SAAS,EACf,gBAAgB,EAChB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CACvC,CAAC;YACF,IAAI,IAAA,2BAAS,EAAC,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;gBAC3C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;aAC/C;YACD,IAAI,CAAC,KAAK,CACR,KAAK,CAAC,SAAS,EACf,cAAc,EACd,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CACrC,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,wCAAwC,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CACjF,CAAC;YACF,MAAM,KAAK,GAAQ,aAAa,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YAC9C,OAAO,aAAa,CAAC;QACvB,CAAC,CACF,CACF,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,0FAA0F;IAClF,sBAAsB,CAAC,QAAwC;QACrE,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,sBAAsB,CAEpC,GAAyB,EACzB,GAAwB,EACxB,IAAyB;YAEzB,8EAA8E;YAC9E,mCAAmC;YACnC,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3D,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;aAC5C;YACD,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC,UAAU,CACzD,IAAI,EACJ,GAAG,EACH,GAAG,EACH,IAAI,CACL,CAAC;YACF,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;QAC1E,CAAC,CAAC;IACJ,CAAC;IAED,wFAAwF;IAChF,oBAAoB,CAAC,QAAsC;QACjE,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,SAAS,sBAAsB,CAEpC,KAAY,EACZ,GAAyB,EACzB,GAAwB,EACxB,IAAyB;YAEzB,8EAA8E;YAC9E,mCAAmC;YACnC,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7D,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;aACnD;YACD,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC,UAAU,CACzD,IAAI,EACJ,GAAG,EACH,GAAG,EACH,IAAI,CACL,CAAC;YACF,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,GAAG,EACH,GAAG,EACH,WAAW,CACZ,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,UAAU,CAChB,KAAmB,EACnB,GAAyB,EACzB,GAAwB,EACxB,IAAyB;;QAEzB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,aAAa,CAAC;QAClD,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM;YACvB,CAAC,CAAC,mBAAS,CAAC,eAAe;YAC3B,CAAC,CAAC,mBAAS,CAAC,UAAU,CAAC;QACzB,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,MAAA,MAAA,GAAG,CAAC,KAAK,0CAAE,IAAI,mCAAI,EAAE,CAAC,IAAI,GAAG,CAAC;QAC3D,MAAM,QAAQ,GACZ,IAAI,KAAK,mBAAS,CAAC,eAAe;YAChC,CAAC,CAAC,qBAAqB,KAAK,EAAE;YAC9B,CAAC,CAAC,gBAAgB,MAAM,EAAE,CAAC;QAC/B,MAAM,UAAU,GAAG;YACjB,CAAC,wBAAc,CAAC,IAAI,CAAC,EAAE,MAAM;YAC7B,CAAC,wBAAc,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc;YAC7C,CAAC,wBAAc,CAAC,IAAI,CAAC,EAAE,IAAI;YAC3B,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAAE,KAAK;SACvC,CAAC;QAEF,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACpC,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAA8B,CAAC;QAC1E,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAChC,QAAQ,EACR;YACE,UAAU;SACX,EACD,MAAM,CACsB,CAAC;QAC/B,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEhD,KAAK,CAAC,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACtD,+DAA+D;QAC/D,GAAG,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE3C,MAAM,WAAW,GAAwB,GAAG,CAAC,EAAE;YAC7C,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;aAC3B;YACD,OAAO,EAAE,CAAC;YACV,IAAI,MAAM,EAAE;gBACV,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;aACvD;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;YACxC,WAAW;SACZ,CAAC;IACJ,CAAC;CACF;AAtLD,wCAsLC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  InstrumentationConfig,\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n} from '@opentelemetry/instrumentation';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\n\nimport * as http from 'http';\nimport type * as Router from 'router';\n\nimport * as types from './internal-types';\nimport { VERSION } from './version';\nimport * as constants from './constants';\nimport * as utils from './utils';\nimport AttributeNames from './enums/AttributeNames';\nimport LayerType from './enums/LayerType';\n\nexport default class RouterInstrumentation extends InstrumentationBase<any> {\n  constructor(config?: InstrumentationConfig) {\n    super(\n      `@opentelemetry/instrumentation-${constants.MODULE_NAME}`,\n      VERSION,\n      config\n    );\n  }\n\n  private _moduleVersion?: string;\n\n  init() {\n    const module = new InstrumentationNodeModuleDefinition<any>(\n      constants.MODULE_NAME,\n      constants.SUPPORTED_VERSIONS,\n      (moduleExports, moduleVersion) => {\n        api.diag.debug(\n          `Applying patch for ${constants.MODULE_NAME}@${moduleVersion}`\n        );\n        this._moduleVersion = moduleVersion;\n        return moduleExports;\n      },\n      (moduleExports, moduleVersion) => {\n        api.diag.debug(\n          `Removing patch for ${constants.MODULE_NAME}@${moduleVersion}`\n        );\n        return moduleExports;\n      }\n    );\n\n    module.files.push(\n      new InstrumentationNodeModuleFile<typeof Router>(\n        'router/lib/layer.js',\n        constants.SUPPORTED_VERSIONS,\n        (moduleExports, moduleVersion) => {\n          api.diag.debug(\n            `Applying patch for \"lib/layer.js\" of ${constants.MODULE_NAME}@${moduleVersion}`\n          );\n          const Layer: any = moduleExports;\n          if (isWrapped(Layer.prototype.handle_request)) {\n            this._unwrap(Layer.prototype, 'handle_request');\n          }\n          this._wrap(\n            Layer.prototype,\n            'handle_request',\n            this._requestHandlerPatcher.bind(this)\n          );\n          if (isWrapped(Layer.prototype.handle_error)) {\n            this._unwrap(Layer.prototype, 'handle_error');\n          }\n          this._wrap(\n            Layer.prototype,\n            'handle_error',\n            this._errorHandlerPatcher.bind(this)\n          );\n          return moduleExports;\n        },\n        (moduleExports, moduleVersion) => {\n          api.diag.debug(\n            `Removing patch for \"lib/layer.js\" of ${constants.MODULE_NAME}@${moduleVersion}`\n          );\n          const Layer: any = moduleExports;\n          this._unwrap(Layer.prototype, 'handle_request');\n          this._unwrap(Layer.prototype, 'handle_error');\n          return moduleExports;\n        }\n      )\n    );\n\n    return module;\n  }\n\n  // Define handle_request wrapper separately to ensure the signature has the correct length\n  private _requestHandlerPatcher(original: Router.Layer['handle_request']) {\n    const instrumentation = this;\n    return function wrapped_handle_request(\n      this: Router.Layer,\n      req: Router.RoutedRequest,\n      res: http.ServerResponse,\n      next: Router.NextFunction\n    ) {\n      // Skip creating spans if the registered handler is of invalid length, because\n      // we know router will ignore those\n      if (utils.isInternal(this.handle) || this.handle.length > 3) {\n        return original.call(this, req, res, next);\n      }\n      const { context, wrappedNext } = instrumentation._setupSpan(\n        this,\n        req,\n        res,\n        next\n      );\n      return api.context.with(context, original, this, req, res, wrappedNext);\n    };\n  }\n\n  // Define handle_error wrapper separately to ensure the signature has the correct length\n  private _errorHandlerPatcher(original: Router.Layer['handle_error']) {\n    const instrumentation = this;\n    return function wrapped_handle_request(\n      this: Router.Layer,\n      error: Error,\n      req: Router.RoutedRequest,\n      res: http.ServerResponse,\n      next: Router.NextFunction\n    ) {\n      // Skip creating spans if the registered handler is of invalid length, because\n      // we know router will ignore those\n      if (utils.isInternal(this.handle) || this.handle.length !== 4) {\n        return original.call(this, error, req, res, next);\n      }\n      const { context, wrappedNext } = instrumentation._setupSpan(\n        this,\n        req,\n        res,\n        next\n      );\n      return api.context.with(\n        context,\n        original,\n        this,\n        error,\n        req,\n        res,\n        wrappedNext\n      );\n    };\n  }\n\n  private _setupSpan(\n    layer: Router.Layer,\n    req: Router.RoutedRequest,\n    res: http.ServerResponse,\n    next: Router.NextFunction\n  ) {\n    const fnName = layer.handle.name || '<anonymous>';\n    const type = layer.method\n      ? LayerType.REQUEST_HANDLER\n      : LayerType.MIDDLEWARE;\n    const route = req.baseUrl + (req.route?.path ?? '') || '/';\n    const spanName =\n      type === LayerType.REQUEST_HANDLER\n        ? `request handler - ${route}`\n        : `middleware - ${fnName}`;\n    const attributes = {\n      [AttributeNames.NAME]: fnName,\n      [AttributeNames.VERSION]: this._moduleVersion,\n      [AttributeNames.TYPE]: type,\n      [SemanticAttributes.HTTP_ROUTE]: route,\n    };\n\n    const parent = api.context.active();\n    const parentSpan = api.trace.getSpan(parent) as types.InstrumentationSpan;\n    const span = this.tracer.startSpan(\n      spanName,\n      {\n        attributes,\n      },\n      parent\n    ) as types.InstrumentationSpan;\n    const endSpan = utils.once(span.end.bind(span));\n\n    utils.renameHttpSpan(parentSpan, layer.method, route);\n    // make sure spans are ended at least when response is finished\n    res.prependOnceListener('finish', endSpan);\n\n    const wrappedNext: Router.NextFunction = err => {\n      if (err) {\n        span.recordException(err);\n      }\n      endSpan();\n      if (parent) {\n        return api.context.with(parent, next, undefined, err);\n      }\n      return next(err);\n    };\n\n    return {\n      context: api.trace.setSpan(parent, span),\n      wrappedNext,\n    };\n  }\n}\n"]}