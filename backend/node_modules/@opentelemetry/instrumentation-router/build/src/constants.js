"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROUTER_HANDLE_FN = exports.ROUTE_ROUTER_FN = exports.SUPPORTED_VERSIONS = exports.MODULE_NAME = void 0;
exports.MODULE_NAME = 'router';
exports.SUPPORTED_VERSIONS = ['1'];
// Router.prototype.handle
exports.ROUTE_ROUTER_FN = `function router(req, res, next) {
    router.handle(req, res, next)
  }`;
// Route.prototype.dispatch
exports.ROUTER_HANDLE_FN = `function handle(req, res, next) {
    route.dispatch(req, res, next)
  }`;
//# sourceMappingURL=constants.js.map