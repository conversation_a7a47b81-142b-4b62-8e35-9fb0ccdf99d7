{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,yCAAyC;AAGzC,0EAA0E;AACnE,MAAM,UAAU,GAAG,CAAC,EAAY,EAAE,EAAE;IACzC,6CAA6C;IAC7C,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,gBAAgB,EAAE;QACxE,OAAO,IAAI,CAAC;KACb;IACD,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,eAAe,EAAE;QACvE,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AATW,QAAA,UAAU,cASrB;AAEK,MAAM,cAAc,GAAG,CAC5B,IAAgC,EAChC,MAAe,EACf,KAAc,EACd,EAAE;;IACF,IACE,OAAO,MAAM,KAAK,QAAQ;QAC1B,OAAO,KAAK,KAAK,QAAQ;SACzB,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,UAAU,CAAC,OAAO,CAAC,CAAA,EAC/B;QACA,IAAI,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,WAAW,EAAE,IAAI,KAAK,EAAE,CAAC,CAAC;KACrD;AACH,CAAC,CAAC;AAZW,QAAA,cAAc,kBAYzB;AAEK,MAAM,IAAI,GAAG,CAAC,EAAY,EAAE,EAAE;IACnC,IAAI,GAAG,GAAG,IAAI,CAAC;IACf,OAAO,GAAG,EAAE;QACV,IAAI,GAAG,EAAE;YACP,GAAG,GAAG,KAAK,CAAC;YACZ,EAAE,EAAE,CAAC;SACN;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AARW,QAAA,IAAI,QAQf", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as constants from './constants';\nimport * as types from './internal-types';\n\n// Detect whether a function is a router package internal plumming handler\nexport const isInternal = (fn: Function) => {\n  // Note that both of those functions are sync\n  if (fn.name === 'handle' && fn.toString() === constants.ROUTER_HANDLE_FN) {\n    return true;\n  }\n  if (fn.name === 'router' && fn.toString() === constants.ROUTE_ROUTER_FN) {\n    return true;\n  }\n  return false;\n};\n\nexport const renameHttpSpan = (\n  span?: types.InstrumentationSpan,\n  method?: string,\n  route?: string\n) => {\n  if (\n    typeof method === 'string' &&\n    typeof route === 'string' &&\n    span?.name?.startsWith('HTTP ')\n  ) {\n    span.updateName(`${method.toUpperCase()} ${route}`);\n  }\n};\n\nexport const once = (fn: Function) => {\n  let run = true;\n  return () => {\n    if (run) {\n      run = false;\n      fn();\n    }\n  };\n};\n"]}