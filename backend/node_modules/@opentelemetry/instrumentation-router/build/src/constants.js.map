{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/constants.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEU,QAAA,WAAW,GAAG,QAAQ,CAAC;AACvB,QAAA,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC;AAExC,0BAA0B;AACb,QAAA,eAAe,GAAG;;IAE3B,CAAC;AAEL,2BAA2B;AACd,QAAA,gBAAgB,GAAG;;IAE5B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const MODULE_NAME = 'router';\nexport const SUPPORTED_VERSIONS = ['1'];\n\n// Router.prototype.handle\nexport const ROUTE_ROUTER_FN = `function router(req, res, next) {\n    router.handle(req, res, next)\n  }`;\n\n// Route.prototype.dispatch\nexport const ROUTER_HANDLE_FN = `function handle(req, res, next) {\n    route.dispatch(req, res, next)\n  }`;\n"]}