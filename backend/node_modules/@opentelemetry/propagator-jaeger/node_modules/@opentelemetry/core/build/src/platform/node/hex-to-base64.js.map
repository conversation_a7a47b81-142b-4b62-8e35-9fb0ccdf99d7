{"version": 3, "file": "hex-to-base64.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/hex-to-base64.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,SAAS,QAAQ,CAAC,QAAgB;IAChC,MAAM;IACN,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,EAAE,EAAE;QACpC,OAAO,QAAQ,GAAG,EAAE,CAAC;KACtB;IAED,MAAM;IACN,IAAI,QAAQ,IAAI,EAAE,IAAI,QAAQ,IAAI,GAAG,EAAE;QACrC,OAAO,QAAQ,GAAG,EAAE,CAAC;KACtB;IAED,MAAM;IACN,OAAO,QAAQ,GAAG,EAAE,CAAC;AACvB,CAAC;AAED,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAE/B,SAAgB,WAAW,CAAC,MAAc;IACxC,IAAI,GAAG,CAAC;IACR,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;QACxB,GAAG,GAAG,IAAI,CAAC;KACZ;SAAM,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;QAC/B,GAAG,GAAG,KAAK,CAAC;KACb;SAAM;QACL,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KACvC;IACD,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACzC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9C,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;KAC1C;IAED,OAAO,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAChC,CAAC;AAlBD,kCAkBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction intValue(charCode: number): number {\n  // 0-9\n  if (charCode >= 48 && charCode <= 57) {\n    return charCode - 48;\n  }\n\n  // a-f\n  if (charCode >= 97 && charCode <= 102) {\n    return charCode - 87;\n  }\n\n  // A-F\n  return charCode - 55;\n}\n\nconst buf8 = Buffer.alloc(8);\nconst buf16 = Buffer.alloc(16);\n\nexport function hexToBase64(hexStr: string): string {\n  let buf;\n  if (hexStr.length === 16) {\n    buf = buf8;\n  } else if (hexStr.length === 32) {\n    buf = buf16;\n  } else {\n    buf = Buffer.alloc(hexStr.length / 2);\n  }\n  let offset = 0;\n\n  for (let i = 0; i < hexStr.length; i += 2) {\n    const hi = intValue(hexStr.charCodeAt(i));\n    const lo = intValue(hexStr.charCodeAt(i + 1));\n    buf.writeUInt8((hi << 4) | lo, offset++);\n  }\n\n  return buf.toString('base64');\n}\n"]}