{"version": 3, "file": "logging-error-handler.js", "sourceRoot": "", "sources": ["../../../src/common/logging-error-handler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAAqD;AAGrD;;;GAGG;AACH,SAAgB,mBAAmB;IACjC,OAAO,CAAC,EAAa,EAAE,EAAE;QACvB,UAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;AACJ,CAAC;AAJD,kDAIC;AAED;;;GAGG;AACH,SAAS,kBAAkB,CAAC,EAAsB;IAChD,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;QAC1B,OAAO,EAAE,CAAC;KACX;SAAM;QACL,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;KAC7C;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,gBAAgB,CAAC,EAAa;IACrC,MAAM,MAAM,GAAG,EAA4B,CAAC;IAC5C,IAAI,OAAO,GAAG,EAAE,CAAC;IAEjB,OAAO,OAAO,KAAK,IAAI,EAAE;QACvB,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACzD,IAAI,MAAM,CAAC,YAAY,CAAC;gBAAE,OAAO;YACjC,MAAM,KAAK,GAAG,OAAO,CAAC,YAAoC,CAAC,CAAC;YAC5D,IAAI,KAAK,EAAE;gBACT,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;aACtC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;KAC1C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag, Exception } from '@opentelemetry/api';\nimport { ErrorHandler } from './types';\n\n/**\n * Returns a function that logs an error using the provided logger, or a\n * console logger if one was not provided.\n */\nexport function loggingErrorHandler(): ErrorHandler {\n  return (ex: Exception) => {\n    diag.error(stringifyException(ex));\n  };\n}\n\n/**\n * Converts an exception into a string representation\n * @param {Exception} ex\n */\nfunction stringifyException(ex: Exception | string): string {\n  if (typeof ex === 'string') {\n    return ex;\n  } else {\n    return JSON.stringify(flattenException(ex));\n  }\n}\n\n/**\n * Flattens an exception into key-value pairs by traversing the prototype chain\n * and coercing values to strings. Duplicate properties will not be overwritten;\n * the first insert wins.\n */\nfunction flattenException(ex: Exception): Record<string, string> {\n  const result = {} as Record<string, string>;\n  let current = ex;\n\n  while (current !== null) {\n    Object.getOwnPropertyNames(current).forEach(propertyName => {\n      if (result[propertyName]) return;\n      const value = current[propertyName as keyof typeof current];\n      if (value) {\n        result[propertyName] = String(value);\n      }\n    });\n    current = Object.getPrototypeOf(current);\n  }\n\n  return result;\n}\n"]}