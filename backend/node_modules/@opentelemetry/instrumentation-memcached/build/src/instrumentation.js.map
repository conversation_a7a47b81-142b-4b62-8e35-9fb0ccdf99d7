{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAC1C,oEAIwC;AAExC,8EAG6C;AAC7C,iCAAiC;AAEjC,uCAAoC;AAEpC,MAAa,eAAgB,SAAQ,qCAAqC;IASxE,YAAY,SAAgC,EAAE;QAC5C,KAAK,CACH,0CAA0C,EAC1C,iBAAO,EACP,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,eAAe,CAAC,cAAc,EAAE,MAAM,CAAC,CAC1D,CAAC;IACJ,CAAC;IAEQ,SAAS,CAAC,SAAgC,EAAE;QACnD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,eAAe,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;IAC3E,CAAC;IAED,IAAI;QACF,OAAO;YACL,IAAI,qDAAmC,CACrC,WAAW,EACX,CAAC,OAAO,CAAC,EACT,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;gBAC/B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,YAAY,eAAe,CAAC,SAAS,IAAI,aAAa,EAAE,CACzD,CAAC;gBACF,IAAI,CAAC,aAAa,CAChB,aAAa,EACb,aAAa,CAAC,SAAS,EACvB,SAAS,EACT,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAC3C,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;gBAC/B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,cAAc,eAAe,CAAC,SAAS,IAAI,aAAa,EAAE,CAC3D,CAAC;gBACF,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,qDAAqD;gBACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,SAA4B,CAAC,CAAC;YACtE,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAED,WAAW,CACT,aAAiC,EACjC,QAGQ;QAER,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAEL,aAA0C,EAC1C,MAAe;YAEf,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;gBACvC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAgB,CAAC,CAAC;aAC/C;YACD,qCAAqC;YACrC,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAC3C,2BAA2B,EAC3B;gBACE,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,MAAM;gBACzB,UAAU,kBACR,mBAAmB,EAAE,aAAa,IAC/B,eAAe,CAAC,iBAAiB,CACrC;aACF,CACF,CAAC;YACF,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAEvD,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,eAAe,CAAC,iBAAiB,CAAC,IAAI,CACpC,eAAe,EACf,aAAa,EACb,IAAI,EACJ,MAAM,EACN,aAAa,EACb,IAAI,CACL,EACD,MAAM,CACP,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,iBAAiB,CACf,QAAqC,EACrC,MAAiB,EACjB,MAA0B,EAC1B,eAA4B,EAC5B,IAAc;QAEd,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO;YACL,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAgB,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;YAEhC,IAAI,CAAC,UAAU,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3C,IAAI,CAAC,aAAa,iBAChB,kBAAkB,EAAE,KAAK,CAAC,GAAG,EAC7B,uBAAuB,EAAE,KAAK,CAAC,QAAQ,EACvC,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,IAAI,EAC7C,CAAC,yCAAkB,CAAC,YAAY,CAAC,EAC/B,eAAe,CAAC,OACjB,CAAC,yBAAyB;oBACzB,CAAC,CAAC,KAAK,CAAC,OAAO;oBACf,CAAC,CAAC,SAAS,IACV,KAAK,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EACjD,CAAC;YAEH,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAC/B,eAAe,EACf,UAAuC,GAAQ;gBAC7C,IAAI,GAAG,EAAE;oBACP,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;oBAC1B,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;wBAC9B,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;iBACJ;gBAED,IAAI,CAAC,GAAG,EAAE,CAAC;gBAEX,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;oBAClC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAgB,CAAC,CAAC;iBAC/C;YACH,CAAC,CACF,CAAC;YAEF,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;IACJ,CAAC;IAEO,aAAa,CACnB,aAAiC,EACjC,GAAQ,EACR,UAAkB,EAClB,OAA+B;QAE/B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,YAAY,UAAU,cAAc,eAAe,CAAC,SAAS,IAAI,aAAa,EAAE,CACjF,CAAC;QACF,IAAI,IAAA,2BAAS,EAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;SAC/B;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;;AA9JH,0CA+JC;AA9JiB,yBAAS,GAAG,WAAW,CAAC;AACxB,iCAAiB,GAAG;IAClC,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,SAAS;CACzD,CAAC;AACc,8BAAc,GAA0B;IACtD,yBAAyB,EAAE,KAAK;CACjC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  isWrapped,\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n} from '@opentelemetry/instrumentation';\nimport type * as Memcached from 'memcached';\nimport {\n  DbSystemValues,\n  SemanticAttributes,\n} from '@opentelemetry/semantic-conventions';\nimport * as utils from './utils';\nimport { InstrumentationConfig } from './types';\nimport { VERSION } from './version';\n\nexport class Instrumentation extends InstrumentationBase<typeof Memcached> {\n  static readonly COMPONENT = 'memcached';\n  static readonly COMMON_ATTRIBUTES = {\n    [SemanticAttributes.DB_SYSTEM]: DbSystemValues.MEMCACHED,\n  };\n  static readonly DEFAULT_CONFIG: InstrumentationConfig = {\n    enhancedDatabaseReporting: false,\n  };\n\n  constructor(config: InstrumentationConfig = {}) {\n    super(\n      '@opentelemetry/instrumentation-memcached',\n      VERSION,\n      Object.assign({}, Instrumentation.DEFAULT_CONFIG, config)\n    );\n  }\n\n  override setConfig(config: InstrumentationConfig = {}) {\n    this._config = Object.assign({}, Instrumentation.DEFAULT_CONFIG, config);\n  }\n\n  init() {\n    return [\n      new InstrumentationNodeModuleDefinition<typeof Memcached>(\n        'memcached',\n        ['>=2.2'],\n        (moduleExports, moduleVersion) => {\n          this._diag.debug(\n            `Patching ${Instrumentation.COMPONENT}@${moduleVersion}`\n          );\n          this.ensureWrapped(\n            moduleVersion,\n            moduleExports.prototype,\n            'command',\n            this.wrapCommand.bind(this, moduleVersion)\n          );\n          return moduleExports;\n        },\n        (moduleExports, moduleVersion) => {\n          this._diag.debug(\n            `Unpatching ${Instrumentation.COMPONENT}@${moduleVersion}`\n          );\n          if (moduleExports === undefined) return;\n          // `command` is documented API missing from the types\n          this._unwrap(moduleExports.prototype, 'command' as keyof Memcached);\n        }\n      ),\n    ];\n  }\n\n  wrapCommand(\n    moduleVersion: undefined | string,\n    original: (\n      queryCompiler: () => Memcached.CommandData,\n      server?: string\n    ) => any\n  ) {\n    const instrumentation = this;\n    return function (\n      this: Memcached,\n      queryCompiler: () => Memcached.CommandData,\n      server?: string\n    ) {\n      if (typeof queryCompiler !== 'function') {\n        return original.apply(this, arguments as any);\n      }\n      // The name will be overwritten later\n      const span = instrumentation.tracer.startSpan(\n        'unknown memcached command',\n        {\n          kind: api.SpanKind.CLIENT,\n          attributes: {\n            'memcached.version': moduleVersion,\n            ...Instrumentation.COMMON_ATTRIBUTES,\n          },\n        }\n      );\n      const parentContext = api.context.active();\n      const context = api.trace.setSpan(parentContext, span);\n\n      return api.context.with(\n        context,\n        original,\n        this,\n        instrumentation.wrapQueryCompiler.call(\n          instrumentation,\n          queryCompiler,\n          this,\n          server,\n          parentContext,\n          span\n        ),\n        server\n      );\n    };\n  }\n\n  wrapQueryCompiler(\n    original: () => Memcached.CommandData,\n    client: Memcached,\n    server: undefined | string,\n    callbackContext: api.Context,\n    span: api.Span\n  ) {\n    const instrumentation = this;\n    return function (this: Memcached) {\n      const query = original.apply(this, arguments as any);\n      const callback = query.callback;\n\n      span.updateName(`memcached ${query.type}`);\n      span.setAttributes({\n        'db.memcached.key': query.key,\n        'db.memcached.lifetime': query.lifetime,\n        [SemanticAttributes.DB_OPERATION]: query.type,\n        [SemanticAttributes.DB_STATEMENT]: (\n          instrumentation._config as InstrumentationConfig\n        ).enhancedDatabaseReporting\n          ? query.command\n          : undefined,\n        ...utils.getPeerAttributes(client, server, query),\n      });\n\n      query.callback = api.context.bind(\n        callbackContext,\n        function (this: Memcached.CommandData, err: any) {\n          if (err) {\n            span.recordException(err);\n            span.setStatus({\n              code: api.SpanStatusCode.ERROR,\n              message: err.message,\n            });\n          }\n\n          span.end();\n\n          if (typeof callback === 'function') {\n            return callback.apply(this, arguments as any);\n          }\n        }\n      );\n\n      return query;\n    };\n  }\n\n  private ensureWrapped(\n    moduleVersion: string | undefined,\n    obj: any,\n    methodName: string,\n    wrapper: (original: any) => any\n  ) {\n    this._diag.debug(\n      `Applying ${methodName} patch for ${Instrumentation.COMPONENT}@${moduleVersion}`\n    );\n    if (isWrapped(obj[methodName])) {\n      this._unwrap(obj, methodName);\n    }\n    this._wrap(obj, methodName, wrapper);\n  }\n}\n"]}