{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8EAAyE;AAElE,MAAM,iBAAiB,GAAG,CAC/B,MAAW,CAAC,qDAAqD,EACjE,MAA0B,EAC1B,KAA4B,EAC5B,EAAE;IACF,IAAI,CAAC,MAAM,EAAE;QACX,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SAC5B;aAAM;YACL,IAAI,UAAU,GACZ,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACjE,MAAM,eAAe,GAAG,KAAK,CAAC,iBAAiB,CAAC;YAEhD,IAAI,UAAU,IAAI,eAAe,EAAE;gBACjC,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAChC,KAAK,CAAC,GAAG,EACT,MAAM,CAAC,UAAU,GAAG,CAAC,EACrB,IAAI,CACL,CAAC;gBACF,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;aAC7B;iBAAM;gBACL,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACzC;SACF;KACF;IAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjD,OAAO;YACL,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI;YACxC,CAAC,yCAAkB,CAAC,aAAa,CAAC,EAAE,IAAI;SACzC,CAAC;KACH;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAlCW,QAAA,iBAAiB,qBAkC5B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as Memcached from 'memcached';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\n\nexport const getPeerAttributes = (\n  client: any /* Memcached, but the type definitions are lacking */,\n  server: string | undefined,\n  query: Memcached.CommandData\n) => {\n  if (!server) {\n    if (client.servers.length === 1) {\n      server = client.servers[0];\n    } else {\n      let redundancy =\n        client.redundancy && client.redundancy < client.servers.length;\n      const queryRedundancy = query.redundancyEnabled;\n\n      if (redundancy && queryRedundancy) {\n        redundancy = client.HashRing.range(\n          query.key,\n          client.redundancy + 1,\n          true\n        );\n        server = redundancy.shift();\n      } else {\n        server = client.HashRing.get(query.key);\n      }\n    }\n  }\n\n  if (typeof server === 'string') {\n    const [host, port] = server && server.split(':');\n    return {\n      [SemanticAttributes.NET_PEER_NAME]: host,\n      [SemanticAttributes.NET_PEER_PORT]: port,\n    };\n  }\n  return {};\n};\n"]}