{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAQ4B;AAC5B,oEAIwC;AACxC,8EAG6C;AAE7C,qDAAkD;AAElD,mCAMiB;AACjB,uCAAoC;AAQpC,MAAa,oBAAqB,SAAQ,qCAEzC;IAMC,YAAY,MAAmC;QAC7C,KAAK,CAAC,sCAAsC,EAAE,iBAAO,EAAE,MAAM,CAAC,CAAC;QAC/D,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAEQ,gBAAgB,CAAC,aAA4B;QACpD,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QACtC,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAEO,qBAAqB;QAC3B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CACrD,6BAA6B,EAAE,gCAAgC;QAC/D;YACE,WAAW,EACT,yFAAyF;YAC3F,IAAI,EAAE,cAAc;SACrB,CACF,CAAC;IACJ,CAAC;IAES,IAAI;QACZ,OAAO;YACL,IAAI,qDAAmC,CACrC,OAAO,EACP,CAAC,KAAK,CAAC,EACP,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;gBAC/B,UAAI,CAAC,KAAK,CAAC,kBAAkB,aAAa,EAAE,CAAC,CAAC;gBAE9C,UAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBAC9C,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE;oBAC7C,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;iBACjD;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,kBAAkB,EAClB,IAAI,CAAC,sBAAsB,EAAS,CACrC,CAAC;gBAEF,UAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBACxC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,UAAU,CAAC,EAAE;oBACvC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;iBAC3C;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,YAAY,EACZ,IAAI,CAAC,gBAAgB,EAAS,CAC/B,CAAC;gBAEF,UAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;gBAC/C,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,iBAAiB,CAAC,EAAE;oBAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;iBAClD;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,mBAAmB,EACnB,IAAI,CAAC,uBAAuB,EAAS,CACtC,CAAC;gBAEF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,aAAa,CAAC,EAAE;gBACd,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;gBAChD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;gBAC1C,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;YACnD,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAED,yBAAyB;IACjB,sBAAsB;QAC5B,OAAO,CAAC,wBAAkC,EAAE,EAAE;YAC5C,MAAM,UAAU,GAAG,IAAI,CAAC;YACxB,UAAI,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAEzE,OAAO,SAAS,gBAAgB,CAC9B,cAAoD;gBAEpD,MAAM,cAAc,GAAG,wBAAwB,CAAC,GAAG,SAAS,CAAC,CAAC;gBAE9D,+CAA+C;gBAC/C,UAAU,CAAC,KAAK,CACd,cAAc,EACd,OAAO,EACP,UAAU,CAAC,WAAW,CAAC,cAAc,CAAQ,CAC9C,CAAC;gBAEF,OAAO,cAAc,CAAC;YACxB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,yBAAyB;IACjB,gBAAgB;QACtB,OAAO,CAAC,kBAA4B,EAAE,EAAE;YACtC,MAAM,UAAU,GAAG,IAAI,CAAC;YACxB,UAAI,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;YACnE,OAAO,SAAS,UAAU,CAAC,OAAuC;gBAChE,MAAM,IAAI,GAAG,kBAAkB,CAAC,GAAG,SAAS,CAAC,CAAC;gBAE9C,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC9D,UAAU,CAAC,KAAK,CACd,IAAI,EACJ,eAAe,EACf,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,CACrC,CAAC;gBACF,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC9D,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;gBAEnD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IACO,aAAa,CAAC,IAAS;QAC7B,OAAO,CAAC,eAAyB,EAAE,EAAE;YACnC,MAAM,UAAU,GAAG,IAAI,CAAC;YACxB,UAAI,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;YACjE,OAAO,SAAS,GAAG,CAAC,QAAkB;gBACpC,MAAM,IAAI,GAAI,IAAY,CAAC,eAAe,CAAC,MAAM,CAAC;gBAClD,MAAM,KAAK,GAAI,IAAY,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACpD,MAAM,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;gBAC3B,MAAM,QAAQ,GAAG,IAAA,mBAAW,EAAC,IAAI,CAAC,CAAC;gBACnC,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE;oBACvC,KAAK,EAAE,MAAM;oBACb,IAAI,EAAE,QAAQ;iBACf,CAAC,CAAC;gBACH,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE;oBACvC,KAAK,EAAE,MAAM;oBACb,IAAI,EAAE,QAAQ;iBACf,CAAC,CAAC;gBACH,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACzC,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,yBAAyB;IACjB,uBAAuB;QAC7B,OAAO,CAAC,yBAAmC,EAAE,EAAE;YAC7C,MAAM,UAAU,GAAG,IAAI,CAAC;YACxB,UAAI,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;YAC1E,OAAO,SAAS,UAAU,CAAC,OAAuC;gBAChE,MAAM,OAAO,GAAG,yBAAyB,CAAC,GAAG,SAAS,CAAC,CAAC;gBAExD,+CAA+C;gBAC/C,UAAU,CAAC,KAAK,CACd,OAAO,EACP,eAAe,EACf,UAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,CACxC,CAAC;gBACF,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;gBAEhE,OAAO,OAAO,CAAC;YACjB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IACO,SAAS,CAAC,OAA+B;QAC/C,OAAO,CAAC,WAAqB,EAAE,EAAE;YAC/B,MAAM,UAAU,GAAG,IAAI,CAAC;YACxB,UAAI,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YACzE,OAAO,SAAS,GAAG,CAAC,EAAU,EAAE,MAAe;gBAC7C,oCAAoC;gBACpC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;oBAC3B,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;oBACnC,OAAO,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;iBAC9C;gBACD,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;gBACtC,MAAM,KAAK,GAAG,OAAO,CAAC,QAAwC,CAAQ,CAAC;gBACvE,IAAI,KAAK,EAAE;oBACT,MAAM,MAAM,GACV,OAAO,EAAE,KAAK,QAAQ;wBACpB,CAAC,CAAC,WAAW,GAAI,OAAe,CAAC,OAAO;wBACxC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBAEjB,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;oBAChC,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;iBACpD;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,4BAA4B;IACpB,mBAAmB,CAAC,IAA8C;QACxE,OAAO,CAAC,qBAA+B,EAAE,EAAE;YACzC,MAAM,UAAU,GAAG,IAAI,CAAC;YACxB,UAAI,CAAC,KAAK,CACR,8DAA8D,CAC/D,CAAC;YAEF,OAAO,SAAS,aAAa,CAC3B,IAAc,EACd,IAAc,EACd,IAAc;gBAEd,oCAAoC;gBACpC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;oBAC3B,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;oBAC1C,OAAO,qBAAqB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACrD;gBAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;oBACxD,MAAM,OAAO,GAAG,UAAU,CAAC,6BAA6B,CACtD,IAAiC,CAClC,CAAC;oBACF,OAAO,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBAClD;gBACD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;oBACxD,MAAM,OAAO,GAAG,UAAU,CAAC,6BAA6B,CACtD,IAAiC,CAClC,CAAC;oBACF,OAAO,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;iBACxD;gBACD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;oBACxD,MAAM,OAAO,GAAG,UAAU,CAAC,6BAA6B,CACtD,IAAiC,CAClC,CAAC;oBACF,OAAO,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;iBAC9D;gBAED,OAAO,qBAAqB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACtD,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,6BAA6B,CAAC,EAA6B;QACjE,MAAM,UAAU,GAAG,IAAI,CAAC;QACxB,MAAM,aAAa,GAAG,aAAO,CAAC,MAAM,EAAE,CAAC;QACvC,OAAO,UAEL,GAA0B,EAC1B,UAAqC;YAErC,IAAI,UAAU,EAAE;gBACd,2CAA2C;gBAC3C,oBAAoB;gBACpB,IAAI,CAAC,IAAA,2BAAS,EAAC,UAAU,CAAC,KAAK,CAAC,EAAE;oBAChC,UAAU,CAAC,KAAK,CACd,UAAU,EACV,OAAO,EACP,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CACnC,CAAC;iBACH;aACF;YACD,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;gBAC5B,aAAO,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;aACxD;QACH,CAAC,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,UAAmD;QACrE,OAAO,CAAC,aAAuB,EAA4B,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC;YACxB,UAAI,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAExD,OAAO,SAAS,KAAK,CACnB,KAA0D,EAC1D,iBAAwD,EACxD,SAAoC;gBAEpC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;oBAC3B,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;oBACxC,OAAO,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;iBACnD;gBAED,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAA,mBAAW,EAAC,KAAK,CAAC,EAAE;oBAC3D,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU,kCACL,oBAAoB,CAAC,iBAAiB,GACtC,IAAA,+BAAuB,EAAC,UAAU,CAAC,MAAM,CAAC,CAC9C;iBACF,CAAC,CAAC;gBAEH,IAAI,CAAC,YAAY,CACf,yCAAkB,CAAC,YAAY,EAC/B,IAAA,sBAAc,EAAC,KAAK,CAAC,CACtB,CAAC;gBAEF,MAAM,qBAAqB,GACzB,UAAU,CAAC,SAAS,EAAE,CAAC;gBAEzB,IAAI,qBAAqB,CAAC,yBAAyB,EAAE;oBACnD,IAAI,MAAM,CAAC;oBAEX,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;wBACpC,MAAM,GAAG,iBAAiB,CAAC;qBAC5B;yBAAM,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;wBACvB,MAAM,GAAG,CAAC,iBAAiB,CAAC,CAAC;qBAC9B;oBAED,IAAI,CAAC,YAAY,CACf,+BAAc,CAAC,YAAY,EAC3B,IAAA,mBAAW,EAAC,KAAK,EAAE,MAAM,CAAC,CAC3B,CAAC;iBACH;gBAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAC7C,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,UAAU,CACjC,CAAC;gBAEF,MAAM,aAAa,GAAG,aAAO,CAAC,MAAM,EAAE,CAAC;gBAEvC,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE;oBAClB,MAAM,eAAe,GAAqB,aAAO,CAAC,IAAI,CACpD,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EACrC,GAAG,EAAE;wBACH,OAAO,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;oBACpD,CAAC,CACF,CAAC;oBACF,aAAO,CAAC,IAAI,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;oBAE7C,OAAO,eAAe;yBACnB,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CACjB,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;wBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CACH;yBACA,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;wBACd,IAAI,CAAC,GAAG,EAAE,CAAC;oBACb,CAAC,CAAC,CAAC;iBACN;qBAAM;oBACL,UAAU,CAAC,KAAK,CACd,SAAS,EACT,OAAO,EACP,UAAU,CAAC,mBAAmB,CAAC,IAAI,EAAE,aAAa,CAAC,CACpD,CAAC;oBAEF,OAAO,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;wBAC9D,OAAO,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;oBACpD,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,IAAU,EAAE,aAAsB;QAC5D,OAAO,CAAC,gBAA0B,EAAE,EAAE;YACpC,OAAO,UACL,GAAiC,EACjC,OAAa,EACb,MAA+B;gBAE/B,IAAI,GAAG,EAAE;oBACP,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;wBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;iBACJ;gBACD,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,OAAO,aAAO,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,CACtC,gBAAgB,CAAC,GAAG,SAAS,CAAC,CAC/B,CAAC;YACJ,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IACO,iBAAiB,CACvB,IAAqB,EACrB,UAAgC,EAChC,EAAU;QAEV,gCAAgC;QAChC,MAAM,QAAQ,GAAG,EAAE,IAAI,IAAA,mBAAW,EAAC,IAAI,CAAC,CAAC;QAEzC,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,EAAE;YACjC,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;gBAClC,KAAK,EAAE,MAAM;gBACb,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;YAC9B,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACnC,KAAK,EAAE,MAAM;gBACb,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;YACH,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;gBAClC,KAAK,EAAE,MAAM;gBACb,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;YAC9B,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBACnC,KAAK,EAAE,MAAM;gBACb,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;YACH,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;gBAClC,KAAK,EAAE,MAAM;gBACb,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;;AAhZH,oDAiZC;AA9YiB,sCAAiB,GAAG;IAClC,CAAC,yCAAkB,CAAC,SAAS,CAAC,EAAE,qCAAc,CAAC,KAAK;CACrD,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  context,\n  Context,\n  diag,\n  trace,\n  Span,\n  SpanKind,\n  SpanStatusCode,\n} from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n} from '@opentelemetry/instrumentation';\nimport {\n  DbSystemValues,\n  SemanticAttributes,\n} from '@opentelemetry/semantic-conventions';\nimport type * as mysqlTypes from 'mysql';\nimport { AttributeNames } from './AttributeNames';\nimport { MySQLInstrumentationConfig } from './types';\nimport {\n  getConnectionAttributes,\n  getDbStatement,\n  getDbValues,\n  getSpanName,\n  getPoolName,\n} from './utils';\nimport { VERSION } from './version';\nimport { UpDownCounter, MeterProvider } from '@opentelemetry/api';\n\ntype getConnectionCallbackType = (\n  err: mysqlTypes.MysqlError,\n  connection: mysqlTypes.PoolConnection\n) => void;\n\nexport class MySQLInstrumentation extends InstrumentationBase<\n  typeof mysqlTypes\n> {\n  static readonly COMMON_ATTRIBUTES = {\n    [SemanticAttributes.DB_SYSTEM]: DbSystemValues.MYSQL,\n  };\n  private _connectionsUsage!: UpDownCounter;\n\n  constructor(config?: MySQLInstrumentationConfig) {\n    super('@opentelemetry/instrumentation-mysql', VERSION, config);\n    this._setMetricInstruments();\n  }\n\n  override setMeterProvider(meterProvider: MeterProvider) {\n    super.setMeterProvider(meterProvider);\n    this._setMetricInstruments();\n  }\n\n  private _setMetricInstruments() {\n    this._connectionsUsage = this.meter.createUpDownCounter(\n      'db.client.connections.usage', //TODO:: use semantic convention\n      {\n        description:\n          'The number of connections that are currently in state described by the state attribute.',\n        unit: '{connection}',\n      }\n    );\n  }\n\n  protected init() {\n    return [\n      new InstrumentationNodeModuleDefinition<typeof mysqlTypes>(\n        'mysql',\n        ['2.*'],\n        (moduleExports, moduleVersion) => {\n          diag.debug(`Patching mysql@${moduleVersion}`);\n\n          diag.debug('Patching mysql.createConnection');\n          if (isWrapped(moduleExports.createConnection)) {\n            this._unwrap(moduleExports, 'createConnection');\n          }\n          this._wrap(\n            moduleExports,\n            'createConnection',\n            this._patchCreateConnection() as any\n          );\n\n          diag.debug('Patching mysql.createPool');\n          if (isWrapped(moduleExports.createPool)) {\n            this._unwrap(moduleExports, 'createPool');\n          }\n          this._wrap(\n            moduleExports,\n            'createPool',\n            this._patchCreatePool() as any\n          );\n\n          diag.debug('Patching mysql.createPoolCluster');\n          if (isWrapped(moduleExports.createPoolCluster)) {\n            this._unwrap(moduleExports, 'createPoolCluster');\n          }\n          this._wrap(\n            moduleExports,\n            'createPoolCluster',\n            this._patchCreatePoolCluster() as any\n          );\n\n          return moduleExports;\n        },\n        moduleExports => {\n          if (moduleExports === undefined) return;\n          this._unwrap(moduleExports, 'createConnection');\n          this._unwrap(moduleExports, 'createPool');\n          this._unwrap(moduleExports, 'createPoolCluster');\n        }\n      ),\n    ];\n  }\n\n  // global export function\n  private _patchCreateConnection() {\n    return (originalCreateConnection: Function) => {\n      const thisPlugin = this;\n      diag.debug('MySQLInstrumentation#patch: patched mysql createConnection');\n\n      return function createConnection(\n        _connectionUri: string | mysqlTypes.ConnectionConfig\n      ) {\n        const originalResult = originalCreateConnection(...arguments);\n\n        // This is unwrapped on next call after unpatch\n        thisPlugin._wrap(\n          originalResult,\n          'query',\n          thisPlugin._patchQuery(originalResult) as any\n        );\n\n        return originalResult;\n      };\n    };\n  }\n\n  // global export function\n  private _patchCreatePool() {\n    return (originalCreatePool: Function) => {\n      const thisPlugin = this;\n      diag.debug('MySQLInstrumentation#patch: patched mysql createPool');\n      return function createPool(_config: string | mysqlTypes.PoolConfig) {\n        const pool = originalCreatePool(...arguments);\n\n        thisPlugin._wrap(pool, 'query', thisPlugin._patchQuery(pool));\n        thisPlugin._wrap(\n          pool,\n          'getConnection',\n          thisPlugin._patchGetConnection(pool)\n        );\n        thisPlugin._wrap(pool, 'end', thisPlugin._patchPoolEnd(pool));\n        thisPlugin._setPoolcallbacks(pool, thisPlugin, '');\n\n        return pool;\n      };\n    };\n  }\n  private _patchPoolEnd(pool: any) {\n    return (originalPoolEnd: Function) => {\n      const thisPlugin = this;\n      diag.debug('MySQLInstrumentation#patch: patched mysql pool end');\n      return function end(callback?: unknown) {\n        const nAll = (pool as any)._allConnections.length;\n        const nFree = (pool as any)._freeConnections.length;\n        const nUsed = nAll - nFree;\n        const poolName = getPoolName(pool);\n        thisPlugin._connectionsUsage.add(-nUsed, {\n          state: 'used',\n          name: poolName,\n        });\n        thisPlugin._connectionsUsage.add(-nFree, {\n          state: 'idle',\n          name: poolName,\n        });\n        originalPoolEnd.apply(pool, arguments);\n      };\n    };\n  }\n\n  // global export function\n  private _patchCreatePoolCluster() {\n    return (originalCreatePoolCluster: Function) => {\n      const thisPlugin = this;\n      diag.debug('MySQLInstrumentation#patch: patched mysql createPoolCluster');\n      return function createPool(_config: string | mysqlTypes.PoolConfig) {\n        const cluster = originalCreatePoolCluster(...arguments);\n\n        // This is unwrapped on next call after unpatch\n        thisPlugin._wrap(\n          cluster,\n          'getConnection',\n          thisPlugin._patchGetConnection(cluster)\n        );\n        thisPlugin._wrap(cluster, 'add', thisPlugin._patchAdd(cluster));\n\n        return cluster;\n      };\n    };\n  }\n  private _patchAdd(cluster: mysqlTypes.PoolCluster) {\n    return (originalAdd: Function) => {\n      const thisPlugin = this;\n      diag.debug('MySQLInstrumentation#patch: patched mysql pool cluster add');\n      return function add(id: string, config: unknown) {\n        // Unwrap if unpatch has been called\n        if (!thisPlugin['_enabled']) {\n          thisPlugin._unwrap(cluster, 'add');\n          return originalAdd.apply(cluster, arguments);\n        }\n        originalAdd.apply(cluster, arguments);\n        const nodes = cluster['_nodes' as keyof mysqlTypes.PoolCluster] as any;\n        if (nodes) {\n          const nodeId =\n            typeof id === 'object'\n              ? 'CLUSTER::' + (cluster as any)._lastId\n              : String(id);\n\n          const pool = nodes[nodeId].pool;\n          thisPlugin._setPoolcallbacks(pool, thisPlugin, id);\n        }\n      };\n    };\n  }\n\n  // method on cluster or pool\n  private _patchGetConnection(pool: mysqlTypes.Pool | mysqlTypes.PoolCluster) {\n    return (originalGetConnection: Function) => {\n      const thisPlugin = this;\n      diag.debug(\n        'MySQLInstrumentation#patch: patched mysql pool getConnection'\n      );\n\n      return function getConnection(\n        arg1?: unknown,\n        arg2?: unknown,\n        arg3?: unknown\n      ) {\n        // Unwrap if unpatch has been called\n        if (!thisPlugin['_enabled']) {\n          thisPlugin._unwrap(pool, 'getConnection');\n          return originalGetConnection.apply(pool, arguments);\n        }\n\n        if (arguments.length === 1 && typeof arg1 === 'function') {\n          const patchFn = thisPlugin._getConnectionCallbackPatchFn(\n            arg1 as getConnectionCallbackType\n          );\n          return originalGetConnection.call(pool, patchFn);\n        }\n        if (arguments.length === 2 && typeof arg2 === 'function') {\n          const patchFn = thisPlugin._getConnectionCallbackPatchFn(\n            arg2 as getConnectionCallbackType\n          );\n          return originalGetConnection.call(pool, arg1, patchFn);\n        }\n        if (arguments.length === 3 && typeof arg3 === 'function') {\n          const patchFn = thisPlugin._getConnectionCallbackPatchFn(\n            arg3 as getConnectionCallbackType\n          );\n          return originalGetConnection.call(pool, arg1, arg2, patchFn);\n        }\n\n        return originalGetConnection.apply(pool, arguments);\n      };\n    };\n  }\n\n  private _getConnectionCallbackPatchFn(cb: getConnectionCallbackType) {\n    const thisPlugin = this;\n    const activeContext = context.active();\n    return function (\n      this: any,\n      err: mysqlTypes.MysqlError,\n      connection: mysqlTypes.PoolConnection\n    ) {\n      if (connection) {\n        // this is the callback passed into a query\n        // no need to unwrap\n        if (!isWrapped(connection.query)) {\n          thisPlugin._wrap(\n            connection,\n            'query',\n            thisPlugin._patchQuery(connection)\n          );\n        }\n      }\n      if (typeof cb === 'function') {\n        context.with(activeContext, cb, this, err, connection);\n      }\n    };\n  }\n\n  private _patchQuery(connection: mysqlTypes.Connection | mysqlTypes.Pool) {\n    return (originalQuery: Function): mysqlTypes.QueryFunction => {\n      const thisPlugin = this;\n      diag.debug('MySQLInstrumentation: patched mysql query');\n\n      return function query(\n        query: string | mysqlTypes.Query | mysqlTypes.QueryOptions,\n        _valuesOrCallback?: unknown[] | mysqlTypes.queryCallback,\n        _callback?: mysqlTypes.queryCallback\n      ) {\n        if (!thisPlugin['_enabled']) {\n          thisPlugin._unwrap(connection, 'query');\n          return originalQuery.apply(connection, arguments);\n        }\n\n        const span = thisPlugin.tracer.startSpan(getSpanName(query), {\n          kind: SpanKind.CLIENT,\n          attributes: {\n            ...MySQLInstrumentation.COMMON_ATTRIBUTES,\n            ...getConnectionAttributes(connection.config),\n          },\n        });\n\n        span.setAttribute(\n          SemanticAttributes.DB_STATEMENT,\n          getDbStatement(query)\n        );\n\n        const instrumentationConfig: MySQLInstrumentationConfig =\n          thisPlugin.getConfig();\n\n        if (instrumentationConfig.enhancedDatabaseReporting) {\n          let values;\n\n          if (Array.isArray(_valuesOrCallback)) {\n            values = _valuesOrCallback;\n          } else if (arguments[2]) {\n            values = [_valuesOrCallback];\n          }\n\n          span.setAttribute(\n            AttributeNames.MYSQL_VALUES,\n            getDbValues(query, values)\n          );\n        }\n\n        const cbIndex = Array.from(arguments).findIndex(\n          arg => typeof arg === 'function'\n        );\n\n        const parentContext = context.active();\n\n        if (cbIndex === -1) {\n          const streamableQuery: mysqlTypes.Query = context.with(\n            trace.setSpan(context.active(), span),\n            () => {\n              return originalQuery.apply(connection, arguments);\n            }\n          );\n          context.bind(parentContext, streamableQuery);\n\n          return streamableQuery\n            .on('error', err =>\n              span.setStatus({\n                code: SpanStatusCode.ERROR,\n                message: err.message,\n              })\n            )\n            .on('end', () => {\n              span.end();\n            });\n        } else {\n          thisPlugin._wrap(\n            arguments,\n            cbIndex,\n            thisPlugin._patchCallbackQuery(span, parentContext)\n          );\n\n          return context.with(trace.setSpan(context.active(), span), () => {\n            return originalQuery.apply(connection, arguments);\n          });\n        }\n      };\n    };\n  }\n\n  private _patchCallbackQuery(span: Span, parentContext: Context) {\n    return (originalCallback: Function) => {\n      return function (\n        err: mysqlTypes.MysqlError | null,\n        results?: any,\n        fields?: mysqlTypes.FieldInfo[]\n      ) {\n        if (err) {\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: err.message,\n          });\n        }\n        span.end();\n        return context.with(parentContext, () =>\n          originalCallback(...arguments)\n        );\n      };\n    };\n  }\n  private _setPoolcallbacks(\n    pool: mysqlTypes.Pool,\n    thisPlugin: MySQLInstrumentation,\n    id: string\n  ) {\n    //TODO:: use semantic convention\n    const poolName = id || getPoolName(pool);\n\n    pool.on('connection', connection => {\n      thisPlugin._connectionsUsage.add(1, {\n        state: 'idle',\n        name: poolName,\n      });\n    });\n\n    pool.on('acquire', connection => {\n      thisPlugin._connectionsUsage.add(-1, {\n        state: 'idle',\n        name: poolName,\n      });\n      thisPlugin._connectionsUsage.add(1, {\n        state: 'used',\n        name: poolName,\n      });\n    });\n\n    pool.on('release', connection => {\n      thisPlugin._connectionsUsage.add(-1, {\n        state: 'used',\n        name: poolName,\n      });\n      thisPlugin._connectionsUsage.add(1, {\n        state: 'idle',\n        name: poolName,\n      });\n    });\n  }\n}\n"]}