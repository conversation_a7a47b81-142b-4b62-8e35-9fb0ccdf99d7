import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import type * as mysqlTypes from 'mysql';
import { MySQLInstrumentationConfig } from './types';
import { MeterProvider } from '@opentelemetry/api';
export declare class MySQLInstrumentation extends InstrumentationBase<typeof mysqlTypes> {
    static readonly COMMON_ATTRIBUTES: {
        [x: string]: "mysql";
    };
    private _connectionsUsage;
    constructor(config?: MySQLInstrumentationConfig);
    setMeterProvider(meterProvider: MeterProvider): void;
    private _setMetricInstruments;
    protected init(): InstrumentationNodeModuleDefinition<typeof mysqlTypes>[];
    private _patchCreateConnection;
    private _patchCreatePool;
    private _patchPoolEnd;
    private _patchCreatePoolCluster;
    private _patchAdd;
    private _patchGetConnection;
    private _getConnectionCallbackPatchFn;
    private _patchQuery;
    private _patchCallbackQuery;
    private _setPoolcallbacks;
}
//# sourceMappingURL=instrumentation.d.ts.map