{"name": "@opentelemetry/instrumentation-mysql", "version": "0.34.5", "description": "OpenTelemetry mysql automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint:fix": "eslint . --ext .ts --fix", "lint": "eslint . --ext .ts", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-mysql --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha tsconfig.json 'test/**/*.test.ts'", "version:update": "node ../../../scripts/version-update.js", "compile:examples": "cd examples && npm run compile"}, "keywords": ["instrumentation", "mysql", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.35.1", "@opentelemetry/sdk-metrics": "^1.8.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "@types/sinon": "10.0.18", "mocha": "7.2.0", "mysql": "2.18.1", "nyc": "15.1.0", "rimraf": "5.0.5", "sinon": "15.2.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/mysql": "2.15.22"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-mysql#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}