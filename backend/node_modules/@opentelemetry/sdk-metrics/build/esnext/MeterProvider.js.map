{"version": 3, "file": "MeterProvider.js", "sourceRoot": "", "sources": ["../../src/MeterProvider.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,IAAI,EAIJ,eAAe,GAChB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAa,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAE/D,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAmB1D;;;GAGG;AACH,SAAS,eAAe,CACtB,iBAA0B,EAC1B,gBAAsC;IAEtC,MAAM,QAAQ,GAAG,gBAAgB,aAAhB,gBAAgB,cAAhB,gBAAgB,GAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;IAEtD,IAAI,iBAAiB,EAAE;QACrB,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;KAC3C;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,aAAa;IAIxB,YAAY,OAA8B;;QAFlC,cAAS,GAAG,KAAK,CAAC;QAGxB,IAAI,CAAC,YAAY,GAAG,IAAI,wBAAwB,CAC9C,eAAe,CACb,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,yBAAyB,mCAAI,IAAI,EAC1C,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAClB,CACF,CAAC;QACF,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,KAAI,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACtD,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;gBAChC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aAC9C;SACF;QAED,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,KAAI,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1D,KAAK,MAAM,YAAY,IAAI,OAAO,CAAC,OAAO,EAAE;gBAC1C,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;aACpC;SACF;IACH,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,IAAY,EAAE,OAAO,GAAG,EAAE,EAAE,UAAwB,EAAE;QAC7D,sHAAsH;QACtH,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC7D,OAAO,eAAe,EAAE,CAAC;SAC1B;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC;YAC3C,IAAI;YACJ,OAAO;YACP,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC,KAAK,CAAC;IACX,CAAC;IAED;;;;;;;;;;;OAWG;IACH,eAAe,CAAC,YAA0B;QACxC,MAAM,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QACvE,YAAY,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,QAAQ,CAAC,OAAyB;QACtC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAChE,OAAO;SACR;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACjD,OAAO,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,UAAU,CAAC,OAA2B;QAC1C,8BAA8B;QAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;YACzE,OAAO;SACR;QAED,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACjD,OAAO,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  MeterProvider as IMeterProvider,\n  Meter as IMeter,\n  MeterOptions,\n  createNoopMeter,\n} from '@opentelemetry/api';\nimport { IResource, Resource } from '@opentelemetry/resources';\nimport { MetricReader } from './export/MetricReader';\nimport { MeterProviderSharedState } from './state/MeterProviderSharedState';\nimport { MetricCollector } from './state/MetricCollector';\nimport { ForceFlushOptions, ShutdownOptions } from './types';\nimport { View } from './view/View';\n\n/**\n * MeterProviderOptions provides an interface for configuring a MeterProvider.\n */\nexport interface MeterProviderOptions {\n  /** Resource associated with metric telemetry  */\n  resource?: IResource;\n  views?: View[];\n  readers?: MetricReader[];\n  /**\n   * Merge resource with {@link Resource.default()}?\n   * Default: {@code true}\n   */\n  mergeResourceWithDefaults?: boolean;\n}\n\n/**\n * @param mergeWithDefaults\n * @param providedResource\n */\nfunction prepareResource(\n  mergeWithDefaults: boolean,\n  providedResource: Resource | undefined\n) {\n  const resource = providedResource ?? Resource.empty();\n\n  if (mergeWithDefaults) {\n    return Resource.default().merge(resource);\n  }\n  return resource;\n}\n\n/**\n * This class implements the {@link MeterProvider} interface.\n */\nexport class MeterProvider implements IMeterProvider {\n  private _sharedState: MeterProviderSharedState;\n  private _shutdown = false;\n\n  constructor(options?: MeterProviderOptions) {\n    this._sharedState = new MeterProviderSharedState(\n      prepareResource(\n        options?.mergeResourceWithDefaults ?? true,\n        options?.resource\n      )\n    );\n    if (options?.views != null && options.views.length > 0) {\n      for (const view of options.views) {\n        this._sharedState.viewRegistry.addView(view);\n      }\n    }\n\n    if (options?.readers != null && options.readers.length > 0) {\n      for (const metricReader of options.readers) {\n        this.addMetricReader(metricReader);\n      }\n    }\n  }\n\n  /**\n   * Get a meter with the configuration of the MeterProvider.\n   */\n  getMeter(name: string, version = '', options: MeterOptions = {}): IMeter {\n    // https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/metrics/sdk.md#meter-creation\n    if (this._shutdown) {\n      diag.warn('A shutdown MeterProvider cannot provide a Meter');\n      return createNoopMeter();\n    }\n\n    return this._sharedState.getMeterSharedState({\n      name,\n      version,\n      schemaUrl: options.schemaUrl,\n    }).meter;\n  }\n\n  /**\n   * Register a {@link MetricReader} to the meter provider. After the\n   * registration, the MetricReader can start metrics collection.\n   *\n   * <p> NOTE: {@link MetricReader} instances MUST be added before creating any instruments.\n   * A {@link MetricReader} instance registered later may receive no or incomplete metric data.\n   *\n   * @param metricReader the metric reader to be registered.\n   *\n   * @deprecated This method will be removed in SDK 2.0. Please use\n   * {@link MeterProviderOptions.readers} via the {@link MeterProvider} constructor instead\n   */\n  addMetricReader(metricReader: MetricReader) {\n    const collector = new MetricCollector(this._sharedState, metricReader);\n    metricReader.setMetricProducer(collector);\n    this._sharedState.metricCollectors.push(collector);\n  }\n\n  /**\n   * Shut down the MeterProvider and all registered\n   * MetricReaders.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  async shutdown(options?: ShutdownOptions): Promise<void> {\n    if (this._shutdown) {\n      diag.warn('shutdown may only be called once per MeterProvider');\n      return;\n    }\n\n    this._shutdown = true;\n\n    await Promise.all(\n      this._sharedState.metricCollectors.map(collector => {\n        return collector.shutdown(options);\n      })\n    );\n  }\n\n  /**\n   * Notifies all registered MetricReaders to flush any buffered data.\n   *\n   * Returns a promise which is resolved when all flushes are complete.\n   */\n  async forceFlush(options?: ForceFlushOptions): Promise<void> {\n    // do not flush after shutdown\n    if (this._shutdown) {\n      diag.warn('invalid attempt to force flush after MeterProvider shutdown');\n      return;\n    }\n\n    await Promise.all(\n      this._sharedState.metricCollectors.map(collector => {\n        return collector.forceFlush(options);\n      })\n    );\n  }\n}\n"]}