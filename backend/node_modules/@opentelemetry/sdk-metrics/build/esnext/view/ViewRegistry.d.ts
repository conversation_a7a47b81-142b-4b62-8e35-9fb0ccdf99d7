import { InstrumentationScope } from '@opentelemetry/core';
import { InstrumentDescriptor } from '../InstrumentDescriptor';
import { View } from './View';
export declare class ViewRegistry {
    private _registeredViews;
    addView(view: View): void;
    findViews(instrument: InstrumentDescriptor, meter: InstrumentationScope): View[];
    private _matchInstrument;
    private _matchMeter;
}
//# sourceMappingURL=ViewRegistry.d.ts.map