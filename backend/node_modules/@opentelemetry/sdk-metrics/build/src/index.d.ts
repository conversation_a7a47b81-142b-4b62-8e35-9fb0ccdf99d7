import { MetricDescriptor } from './export/MetricData';
export { Sum, LastValue, Histogram, ExponentialHistogram, } from './aggregator/types';
export { AggregationSelector, AggregationTemporalitySelector, } from './export/AggregationSelector';
export { AggregationTemporality } from './export/AggregationTemporality';
export { DataPoint, DataPointType, SumMetricData, GaugeMetricData, HistogramMetricData, ExponentialHistogramMetricData, ResourceMetrics, ScopeMetrics, MetricData, MetricDescriptor, CollectionResult, } from './export/MetricData';
export { PushMetricExporter } from './export/MetricExporter';
export { MetricReader, MetricReaderOptions } from './export/MetricReader';
export { PeriodicExportingMetricReader, PeriodicExportingMetricReaderOptions, } from './export/PeriodicExportingMetricReader';
export { InMemoryMetricExporter } from './export/InMemoryMetricExporter';
export { ConsoleMetricExporter } from './export/ConsoleMetricExporter';
export { MetricCollectOptions, MetricProducer } from './export/MetricProducer';
export { InstrumentType } from './InstrumentDescriptor';
/**
 * @deprecated Use {@link MetricDescriptor} instead.
 */
export declare type InstrumentDescriptor = MetricDescriptor;
export { MeterProvider, MeterProviderOptions } from './MeterProvider';
export { DefaultAggregation, ExplicitBucketHistogramAggregation, ExponentialHistogramAggregation, DropAggregation, HistogramAggregation, LastValueAggregation, SumAggregation, Aggregation, } from './view/Aggregation';
export { View, ViewOptions } from './view/View';
export { TimeoutError } from './utils';
//# sourceMappingURL=index.d.ts.map