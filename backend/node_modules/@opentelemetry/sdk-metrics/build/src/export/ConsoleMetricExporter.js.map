{"version": 3, "file": "ConsoleMetricExporter.js", "sourceRoot": "", "sources": ["../../../src/export/ConsoleMetricExporter.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,8CAAqE;AAKrE,+DAG+B;AAM/B;;;;;GAKG;AAEH,+BAA+B;AAC/B,MAAa,qBAAqB;IAIhC,YAAY,OAAsC;;QAHxC,cAAS,GAAG,KAAK,CAAC;QAI1B,IAAI,CAAC,oBAAoB;YACvB,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,mBAAmB,mCAAI,8DAAwC,CAAC;IAC7E,CAAC;IAED,MAAM,CACJ,OAAwB,EACxB,cAA8C;QAE9C,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,uFAAuF;YACvF,YAAY,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,uBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;YAChE,OAAO;SACR;QAED,OAAO,qBAAqB,CAAC,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IACrE,CAAC;IAED,UAAU;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,4BAA4B,CAC1B,eAA+B;QAE/B,OAAO,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;IACpD,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAEO,MAAM,CAAC,YAAY,CACzB,OAAwB,EACxB,IAAoC;QAEpC,KAAK,MAAM,YAAY,IAAI,OAAO,CAAC,YAAY,EAAE;YAC/C,KAAK,MAAM,MAAM,IAAI,YAAY,CAAC,OAAO,EAAE;gBACzC,OAAO,CAAC,GAAG,CACT;oBACE,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;oBACnC,UAAU,EAAE,MAAM,CAAC,UAAU;iBAC9B,EACD,EAAE,KAAK,EAAE,IAAI,EAAE,CAChB,CAAC;aACH;SACF;QAED,IAAI,CAAC,EAAE,IAAI,EAAE,uBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;IAC3C,CAAC;CACF;AAxDD,sDAwDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ExportResult, ExportResultCode } from '@opentelemetry/core';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { AggregationTemporality } from './AggregationTemporality';\nimport { ResourceMetrics } from './MetricData';\nimport { PushMetricExporter } from './MetricExporter';\nimport {\n  AggregationTemporalitySelector,\n  DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR,\n} from './AggregationSelector';\n\ninterface ConsoleMetricExporterOptions {\n  temporalitySelector?: AggregationTemporalitySelector;\n}\n\n/**\n * This is an implementation of {@link PushMetricExporter} that prints metrics to the\n * console. This class can be used for diagnostic purposes.\n *\n * NOTE: This {@link PushMetricExporter} is intended for diagnostics use only, output rendered to the console may change at any time.\n */\n\n/* eslint-disable no-console */\nexport class ConsoleMetricExporter implements PushMetricExporter {\n  protected _shutdown = false;\n  protected _temporalitySelector: AggregationTemporalitySelector;\n\n  constructor(options?: ConsoleMetricExporterOptions) {\n    this._temporalitySelector =\n      options?.temporalitySelector ?? DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR;\n  }\n\n  export(\n    metrics: ResourceMetrics,\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    if (this._shutdown) {\n      // If the exporter is shutting down, by spec, we need to return FAILED as export result\n      setImmediate(resultCallback, { code: ExportResultCode.FAILED });\n      return;\n    }\n\n    return ConsoleMetricExporter._sendMetrics(metrics, resultCallback);\n  }\n\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  selectAggregationTemporality(\n    _instrumentType: InstrumentType\n  ): AggregationTemporality {\n    return this._temporalitySelector(_instrumentType);\n  }\n\n  shutdown(): Promise<void> {\n    this._shutdown = true;\n    return Promise.resolve();\n  }\n\n  private static _sendMetrics(\n    metrics: ResourceMetrics,\n    done: (result: ExportResult) => void\n  ): void {\n    for (const scopeMetrics of metrics.scopeMetrics) {\n      for (const metric of scopeMetrics.metrics) {\n        console.dir(\n          {\n            descriptor: metric.descriptor,\n            dataPointType: metric.dataPointType,\n            dataPoints: metric.dataPoints,\n          },\n          { depth: null }\n        );\n      }\n    }\n\n    done({ code: ExportResultCode.SUCCESS });\n  }\n}\n"]}