{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAOH,SAAgB,YAAY,CAAI,IAAc;IAC5C,OAAO,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,CAAC;AAC7C,CAAC;AAFD,oCAEC;AAED;;;GAGG;AACH,SAAgB,cAAc,CAAC,UAAsB;IACnD,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACnC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAEjC,gDAAgD;IAChD,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;IACnB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC;AAPD,wCAOC;AAED;;;GAGG;AACH,SAAgB,sBAAsB,CACpC,oBAA0C;;IAE1C,OAAO,GAAG,oBAAoB,CAAC,IAAI,IAAI,MAAA,oBAAoB,CAAC,OAAO,mCAAI,EAAE,IACvE,MAAA,oBAAoB,CAAC,SAAS,mCAAI,EACpC,EAAE,CAAC;AACL,CAAC;AAND,wDAMC;AAED;;GAEG;AACH,MAAa,YAAa,SAAQ,KAAK;IACrC,YAAY,OAAgB;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,0FAA0F;QAC1F,6IAA6I;QAC7I,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;CACF;AARD,oCAQC;AAED;;;;;;;;GAQG;AACH,SAAgB,eAAe,CAC7B,OAAmB,EACnB,OAAe;IAEf,IAAI,aAA4C,CAAC;IAEjD,MAAM,cAAc,GAAG,IAAI,OAAO,CAAQ,SAAS,eAAe,CAChE,QAAQ,EACR,MAAM;QAEN,aAAa,GAAG,UAAU,CAAC,SAAS,cAAc;YAChD,MAAM,CAAC,IAAI,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACnD,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,CACjD,MAAM,CAAC,EAAE;QACP,YAAY,CAAC,aAAa,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC;IAChB,CAAC,EACD,MAAM,CAAC,EAAE;QACP,YAAY,CAAC,aAAa,CAAC,CAAC;QAC5B,MAAM,MAAM,CAAC;IACf,CAAC,CACF,CAAC;AACJ,CAAC;AAzBD,0CAyBC;AAgBD;;GAEG;AACI,KAAK,UAAU,iBAAiB,CACrC,QAAsB;IAEtB,OAAO,OAAO,CAAC,GAAG,CAChB,QAAQ,CAAC,GAAG,CAAsC,KAAK,EAAC,CAAC,EAAC,EAAE;QAC1D,IAAI;YACF,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC;YACpB,OAAO;gBACL,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,GAAG;aACX,CAAC;SACH;QAAC,OAAO,CAAC,EAAE;YACV,OAAO;gBACL,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,CAAC;aACV,CAAC;SACH;IACH,CAAC,CAAC,CACH,CAAC;AACJ,CAAC;AAnBD,8CAmBC;AAED,SAAgB,kCAAkC,CAChD,EAAoC;IAEpC,OAAO,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC;AAClC,CAAC;AAJD,gFAIC;AAED;;GAEG;AACH,SAAgB,OAAO,CAAO,GAAQ,EAAE,EAAkB;IACxD,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;QACf,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAND,0BAMC;AAED,SAAgB,SAAS,CAAC,GAAiB,EAAE,GAAiB;IAC5D,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;IACD,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;QACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAClB,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAVD,8BAUC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAAC,GAAa,EAAE,KAAa;IACzD,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;IACxB,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IAErB,OAAO,EAAE,IAAI,EAAE,EAAE;QACf,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3C,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE;YACpB,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;SACd;aAAM;YACL,GAAG,GAAG,GAAG,CAAC;YACV,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;SACd;KACF;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAhBD,wCAgBC;AAED,SAAgB,qBAAqB,CAAC,GAAW,EAAE,GAAW;IAC5D,OAAO,GAAG,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,WAAW,EAAE,CAAC;AACjD,CAAC;AAFD,sDAEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes } from '@opentelemetry/api';\nimport { InstrumentationScope } from '@opentelemetry/core';\n\nexport type Maybe<T> = T | undefined;\n\nexport function isNotNullish<T>(item: Maybe<T>): item is T {\n  return item !== undefined && item !== null;\n}\n\n/**\n * Converting the unordered attributes into unique identifier string.\n * @param attributes user provided unordered Attributes.\n */\nexport function hashAttributes(attributes: Attributes): string {\n  let keys = Object.keys(attributes);\n  if (keys.length === 0) return '';\n\n  // Return a string that is stable on key orders.\n  keys = keys.sort();\n  return JSON.stringify(keys.map(key => [key, attributes[key]]));\n}\n\n/**\n * Converting the instrumentation scope object to a unique identifier string.\n * @param instrumentationScope\n */\nexport function instrumentationScopeId(\n  instrumentationScope: InstrumentationScope\n): string {\n  return `${instrumentationScope.name}:${instrumentationScope.version ?? ''}:${\n    instrumentationScope.schemaUrl ?? ''\n  }`;\n}\n\n/**\n * Error that is thrown on timeouts.\n */\nexport class TimeoutError extends Error {\n  constructor(message?: string) {\n    super(message);\n\n    // manually adjust prototype to retain `instanceof` functionality when targeting ES5, see:\n    // https://github.com/Microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, TimeoutError.prototype);\n  }\n}\n\n/**\n * Adds a timeout to a promise and rejects if the specified timeout has elapsed. Also rejects if the specified promise\n * rejects, and resolves if the specified promise resolves.\n *\n * <p> NOTE: this operation will continue even after it throws a {@link TimeoutError}.\n *\n * @param promise promise to use with timeout.\n * @param timeout the timeout in milliseconds until the returned promise is rejected.\n */\nexport function callWithTimeout<T>(\n  promise: Promise<T>,\n  timeout: number\n): Promise<T> {\n  let timeoutHandle: ReturnType<typeof setTimeout>;\n\n  const timeoutPromise = new Promise<never>(function timeoutFunction(\n    _resolve,\n    reject\n  ) {\n    timeoutHandle = setTimeout(function timeoutHandler() {\n      reject(new TimeoutError('Operation timed out.'));\n    }, timeout);\n  });\n\n  return Promise.race([promise, timeoutPromise]).then(\n    result => {\n      clearTimeout(timeoutHandle);\n      return result;\n    },\n    reason => {\n      clearTimeout(timeoutHandle);\n      throw reason;\n    }\n  );\n}\n\nexport interface PromiseAllSettledFulfillResult<T> {\n  status: 'fulfilled';\n  value: T;\n}\n\nexport interface PromiseAllSettledRejectionResult {\n  status: 'rejected';\n  reason: unknown;\n}\n\nexport type PromiseAllSettledResult<T> =\n  | PromiseAllSettledFulfillResult<T>\n  | PromiseAllSettledRejectionResult;\n\n/**\n * Node.js v12.9 lower and browser compatible `Promise.allSettled`.\n */\nexport async function PromiseAllSettled<T>(\n  promises: Promise<T>[]\n): Promise<PromiseAllSettledResult<T>[]> {\n  return Promise.all(\n    promises.map<Promise<PromiseAllSettledResult<T>>>(async p => {\n      try {\n        const ret = await p;\n        return {\n          status: 'fulfilled',\n          value: ret,\n        };\n      } catch (e) {\n        return {\n          status: 'rejected',\n          reason: e,\n        };\n      }\n    })\n  );\n}\n\nexport function isPromiseAllSettledRejectionResult(\n  it: PromiseAllSettledResult<unknown>\n): it is PromiseAllSettledRejectionResult {\n  return it.status === 'rejected';\n}\n\n/**\n * Node.js v11.0 lower and browser compatible `Array.prototype.flatMap`.\n */\nexport function FlatMap<T, R>(arr: T[], fn: (it: T) => R[]): R[] {\n  const result: R[] = [];\n  arr.forEach(it => {\n    result.push(...fn(it));\n  });\n  return result;\n}\n\nexport function setEquals(lhs: Set<unknown>, rhs: Set<unknown>): boolean {\n  if (lhs.size !== rhs.size) {\n    return false;\n  }\n  for (const item of lhs) {\n    if (!rhs.has(item)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Binary search the sorted array to the find upper bound for the value.\n * @param arr\n * @param value\n * @returns\n */\nexport function binarySearchUB(arr: number[], value: number): number {\n  let lo = 0;\n  let hi = arr.length - 1;\n  let ret = arr.length;\n\n  while (hi >= lo) {\n    const mid = lo + Math.trunc((hi - lo) / 2);\n    if (arr[mid] < value) {\n      lo = mid + 1;\n    } else {\n      ret = mid;\n      hi = mid - 1;\n    }\n  }\n\n  return ret;\n}\n\nexport function equalsCaseInsensitive(lhs: string, rhs: string): boolean {\n  return lhs.toLowerCase() === rhs.toLowerCase();\n}\n"]}