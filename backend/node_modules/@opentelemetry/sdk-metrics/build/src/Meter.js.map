{"version": 3, "file": "Meter.js", "sourceRoot": "", "sources": ["../../src/Meter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAcH,iEAGgC;AAChC,+CAQuB;AAIvB;;GAEG;AACH,MAAa,KAAK;IAChB,YAAoB,iBAAmC;QAAnC,sBAAiB,GAAjB,iBAAiB,CAAkB;IAAG,CAAC;IAE3D;;OAEG;IACH,WAAW,CAAC,IAAY,EAAE,OAAuB;QAC/C,MAAM,UAAU,GAAG,IAAA,iDAA0B,EAC3C,IAAI,EACJ,qCAAc,CAAC,KAAK,EACpB,OAAO,CACR,CAAC;QACF,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACzE,OAAO,IAAI,6BAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAY,EAAE,OAAuB;QACnD,MAAM,UAAU,GAAG,IAAA,iDAA0B,EAC3C,IAAI,EACJ,qCAAc,CAAC,SAAS,EACxB,OAAO,CACR,CAAC;QACF,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACzE,OAAO,IAAI,iCAAmB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,IAAY,EAAE,OAAuB;QACjD,MAAM,UAAU,GAAG,IAAA,iDAA0B,EAC3C,IAAI,EACJ,qCAAc,CAAC,OAAO,EACtB,OAAO,CACR,CAAC;QACF,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACzE,OAAO,IAAI,+BAAiB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,IAAY,EAAE,OAAuB;QACvD,MAAM,UAAU,GAAG,IAAA,iDAA0B,EAC3C,IAAI,EACJ,qCAAc,CAAC,eAAe,EAC9B,OAAO,CACR,CAAC;QACF,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACzE,OAAO,IAAI,qCAAuB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,qBAAqB,CACnB,IAAY,EACZ,OAAuB;QAEvB,MAAM,UAAU,GAAG,IAAA,iDAA0B,EAC3C,IAAI,EACJ,qCAAc,CAAC,gBAAgB,EAC/B,OAAO,CACR,CAAC;QACF,MAAM,QAAQ,GACZ,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;QAChE,OAAO,IAAI,uCAAyB,CAClC,UAAU,EACV,QAAQ,EACR,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,uBAAuB,CACrB,IAAY,EACZ,OAAuB;QAEvB,MAAM,UAAU,GAAG,IAAA,iDAA0B,EAC3C,IAAI,EACJ,qCAAc,CAAC,kBAAkB,EACjC,OAAO,CACR,CAAC;QACF,MAAM,QAAQ,GACZ,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;QAChE,OAAO,IAAI,yCAA2B,CACpC,UAAU,EACV,QAAQ,EACR,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,6BAA6B,CAC3B,IAAY,EACZ,OAAuB;QAEvB,MAAM,UAAU,GAAG,IAAA,iDAA0B,EAC3C,IAAI,EACJ,qCAAc,CAAC,0BAA0B,EACzC,OAAO,CACR,CAAC;QACF,MAAM,QAAQ,GACZ,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;QAChE,OAAO,IAAI,+CAAiC,CAC1C,UAAU,EACV,QAAQ,EACR,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,0BAA0B,CACxB,QAAiC,EACjC,WAAyB;QAEzB,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,gBAAgB,CACxD,QAAQ,EACR,WAAW,CACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,6BAA6B,CAC3B,QAAiC,EACjC,WAAyB;QAEzB,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,mBAAmB,CAC3D,QAAQ,EACR,WAAW,CACZ,CAAC;IACJ,CAAC;CACF;AA/ID,sBA+IC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Meter as IMeter,\n  MetricOptions,\n  Histogram,\n  Counter,\n  UpDownCounter,\n  ObservableGauge,\n  ObservableCounter,\n  ObservableUpDownCounter,\n  BatchObservableCallback,\n  Observable,\n} from '@opentelemetry/api';\nimport {\n  createInstrumentDescriptor,\n  InstrumentType,\n} from './InstrumentDescriptor';\nimport {\n  CounterInstrument,\n  GaugeInstrument,\n  HistogramInstrument,\n  ObservableCounterInstrument,\n  ObservableGaugeInstrument,\n  ObservableUpDownCounterInstrument,\n  UpDownCounterInstrument,\n} from './Instruments';\nimport { MeterSharedState } from './state/MeterSharedState';\nimport { Gauge } from './types';\n\n/**\n * This class implements the {@link IMeter} interface.\n */\nexport class Meter implements IMeter {\n  constructor(private _meterSharedState: MeterSharedState) {}\n\n  /**\n   * Create a {@link Gauge} instrument.\n   */\n  createGauge(name: string, options?: MetricOptions): Gauge {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.GAUGE,\n      options\n    );\n    const storage = this._meterSharedState.registerMetricStorage(descriptor);\n    return new GaugeInstrument(storage, descriptor);\n  }\n\n  /**\n   * Create a {@link Histogram} instrument.\n   */\n  createHistogram(name: string, options?: MetricOptions): Histogram {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.HISTOGRAM,\n      options\n    );\n    const storage = this._meterSharedState.registerMetricStorage(descriptor);\n    return new HistogramInstrument(storage, descriptor);\n  }\n\n  /**\n   * Create a {@link Counter} instrument.\n   */\n  createCounter(name: string, options?: MetricOptions): Counter {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.COUNTER,\n      options\n    );\n    const storage = this._meterSharedState.registerMetricStorage(descriptor);\n    return new CounterInstrument(storage, descriptor);\n  }\n\n  /**\n   * Create a {@link UpDownCounter} instrument.\n   */\n  createUpDownCounter(name: string, options?: MetricOptions): UpDownCounter {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.UP_DOWN_COUNTER,\n      options\n    );\n    const storage = this._meterSharedState.registerMetricStorage(descriptor);\n    return new UpDownCounterInstrument(storage, descriptor);\n  }\n\n  /**\n   * Create a {@link ObservableGauge} instrument.\n   */\n  createObservableGauge(\n    name: string,\n    options?: MetricOptions\n  ): ObservableGauge {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.OBSERVABLE_GAUGE,\n      options\n    );\n    const storages =\n      this._meterSharedState.registerAsyncMetricStorage(descriptor);\n    return new ObservableGaugeInstrument(\n      descriptor,\n      storages,\n      this._meterSharedState.observableRegistry\n    );\n  }\n\n  /**\n   * Create a {@link ObservableCounter} instrument.\n   */\n  createObservableCounter(\n    name: string,\n    options?: MetricOptions\n  ): ObservableCounter {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.OBSERVABLE_COUNTER,\n      options\n    );\n    const storages =\n      this._meterSharedState.registerAsyncMetricStorage(descriptor);\n    return new ObservableCounterInstrument(\n      descriptor,\n      storages,\n      this._meterSharedState.observableRegistry\n    );\n  }\n\n  /**\n   * Create a {@link ObservableUpDownCounter} instrument.\n   */\n  createObservableUpDownCounter(\n    name: string,\n    options?: MetricOptions\n  ): ObservableUpDownCounter {\n    const descriptor = createInstrumentDescriptor(\n      name,\n      InstrumentType.OBSERVABLE_UP_DOWN_COUNTER,\n      options\n    );\n    const storages =\n      this._meterSharedState.registerAsyncMetricStorage(descriptor);\n    return new ObservableUpDownCounterInstrument(\n      descriptor,\n      storages,\n      this._meterSharedState.observableRegistry\n    );\n  }\n\n  /**\n   * @see {@link Meter.addBatchObservableCallback}\n   */\n  addBatchObservableCallback(\n    callback: BatchObservableCallback,\n    observables: Observable[]\n  ) {\n    this._meterSharedState.observableRegistry.addBatchCallback(\n      callback,\n      observables\n    );\n  }\n\n  /**\n   * @see {@link Meter.removeBatchObservableCallback}\n   */\n  removeBatchObservableCallback(\n    callback: BatchObservableCallback,\n    observables: Observable[]\n  ) {\n    this._meterSharedState.observableRegistry.removeBatchCallback(\n      callback,\n      observables\n    );\n  }\n}\n"]}