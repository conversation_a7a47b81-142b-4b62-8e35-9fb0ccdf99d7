{"version": 3, "file": "AsyncMetricStorage.js", "sourceRoot": "", "sources": ["../../../src/state/AsyncMetricStorage.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,mDAAgD;AAEhD,iEAA8D;AAC9D,uEAAoE;AAGpE,uCAA6C;AAG7C;;;;GAIG;AACH,MAAa,kBACX,SAAQ,6BAAa;IAMrB,YACE,qBAA2C,EAC3C,UAAyB,EACjB,oBAAyC,EACjD,gBAAyC,EACjC,4BAAqC;QAE7C,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAJrB,yBAAoB,GAApB,oBAAoB,CAAqB;QAEzC,iCAA4B,GAA5B,4BAA4B,CAAS;QAG7C,IAAI,CAAC,mBAAmB,GAAG,IAAI,2CAAoB,CACjD,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;QACF,IAAI,CAAC,sBAAsB,GAAG,IAAI,iDAAuB,CACvD,UAAU,EACV,gBAAgB,CACjB,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,YAAsC,EAAE,eAAuB;QACpE,MAAM,SAAS,GAAG,IAAI,0BAAgB,EAAU,CAAC;QACjD,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,EAAE;YACjE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;OAMG;IACH,OAAO,CACL,SAAgC,EAChC,cAAsB;QAEtB,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QAEzD,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAC7C,SAAS,EACT,IAAI,CAAC,qBAAqB,EAC1B,aAAa,EACb,cAAc,CACf,CAAC;IACJ,CAAC;CACF;AArDD,gDAqDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport { Accumulation, Aggregator } from '../aggregator/types';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\nimport { AttributesProcessor } from '../view/AttributesProcessor';\nimport { MetricStorage } from './MetricStorage';\nimport { MetricData } from '../export/MetricData';\nimport { DeltaMetricProcessor } from './DeltaMetricProcessor';\nimport { TemporalMetricProcessor } from './TemporalMetricProcessor';\nimport { Maybe } from '../utils';\nimport { MetricCollectorHandle } from './MetricCollector';\nimport { AttributeHashMap } from './HashMap';\nimport { AsyncWritableMetricStorage } from './WritableMetricStorage';\n\n/**\n * Internal interface.\n *\n * Stores and aggregates {@link MetricData} for asynchronous instruments.\n */\nexport class AsyncMetricStorage<T extends Maybe<Accumulation>>\n  extends MetricStorage\n  implements AsyncWritableMetricStorage\n{\n  private _deltaMetricStorage: DeltaMetricProcessor<T>;\n  private _temporalMetricStorage: TemporalMetricProcessor<T>;\n\n  constructor(\n    _instrumentDescriptor: InstrumentDescriptor,\n    aggregator: Aggregator<T>,\n    private _attributesProcessor: AttributesProcessor,\n    collectorHandles: MetricCollectorHandle[],\n    private _aggregationCardinalityLimit?: number\n  ) {\n    super(_instrumentDescriptor);\n    this._deltaMetricStorage = new DeltaMetricProcessor(\n      aggregator,\n      this._aggregationCardinalityLimit\n    );\n    this._temporalMetricStorage = new TemporalMetricProcessor(\n      aggregator,\n      collectorHandles\n    );\n  }\n\n  record(measurements: AttributeHashMap<number>, observationTime: HrTime) {\n    const processed = new AttributeHashMap<number>();\n    Array.from(measurements.entries()).forEach(([attributes, value]) => {\n      processed.set(this._attributesProcessor.process(attributes), value);\n    });\n    this._deltaMetricStorage.batchCumulate(processed, observationTime);\n  }\n\n  /**\n   * Collects the metrics from this storage. The ObservableCallback is invoked\n   * during the collection.\n   *\n   * Note: This is a stateful operation and may reset any interval-related\n   * state for the MetricCollector.\n   */\n  collect(\n    collector: MetricCollectorHandle,\n    collectionTime: HrTime\n  ): Maybe<MetricData> {\n    const accumulations = this._deltaMetricStorage.collect();\n\n    return this._temporalMetricStorage.buildMetrics(\n      collector,\n      this._instrumentDescriptor,\n      accumulations,\n      collectionTime\n    );\n  }\n}\n"]}