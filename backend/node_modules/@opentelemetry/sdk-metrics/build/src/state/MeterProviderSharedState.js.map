{"version": 3, "file": "MeterProviderSharedState.js", "sourceRoot": "", "sources": ["../../../src/state/MeterProviderSharedState.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,oCAAkD;AAClD,uDAAoD;AACpD,yDAAsD;AAGtD;;GAEG;AACH,MAAa,wBAAwB;IAOnC,YAAmB,QAAmB;QAAnB,aAAQ,GAAR,QAAQ,CAAW;QANtC,iBAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QAElC,qBAAgB,GAAsB,EAAE,CAAC;QAEzC,sBAAiB,GAAkC,IAAI,GAAG,EAAE,CAAC;IAEpB,CAAC;IAE1C,mBAAmB,CAAC,oBAA0C;QAC5D,MAAM,EAAE,GAAG,IAAA,8BAAsB,EAAC,oBAAoB,CAAC,CAAC;QACxD,IAAI,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACtD,IAAI,gBAAgB,IAAI,IAAI,EAAE;YAC5B,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;YACpE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;SAClD;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,kBAAkB,CAAC,cAA8B;QAC/C,MAAM,MAAM,GAA2C,EAAE,CAAC;QAC1D,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC7C,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SACvE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA1BD,4DA0BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { IResource } from '@opentelemetry/resources';\nimport { Aggregation, InstrumentType } from '..';\nimport { instrumentationScopeId } from '../utils';\nimport { ViewRegistry } from '../view/ViewRegistry';\nimport { MeterSharedState } from './MeterSharedState';\nimport { MetricCollector, MetricCollectorHandle } from './MetricCollector';\n\n/**\n * An internal record for shared meter provider states.\n */\nexport class MeterProviderSharedState {\n  viewRegistry = new ViewRegistry();\n\n  metricCollectors: MetricCollector[] = [];\n\n  meterSharedStates: Map<string, MeterSharedState> = new Map();\n\n  constructor(public resource: IResource) {}\n\n  getMeterSharedState(instrumentationScope: InstrumentationScope) {\n    const id = instrumentationScopeId(instrumentationScope);\n    let meterSharedState = this.meterSharedStates.get(id);\n    if (meterSharedState == null) {\n      meterSharedState = new MeterSharedState(this, instrumentationScope);\n      this.meterSharedStates.set(id, meterSharedState);\n    }\n    return meterSharedState;\n  }\n\n  selectAggregations(instrumentType: InstrumentType) {\n    const result: [MetricCollectorHandle, Aggregation][] = [];\n    for (const collector of this.metricCollectors) {\n      result.push([collector, collector.selectAggregation(instrumentType)]);\n    }\n    return result;\n  }\n}\n"]}