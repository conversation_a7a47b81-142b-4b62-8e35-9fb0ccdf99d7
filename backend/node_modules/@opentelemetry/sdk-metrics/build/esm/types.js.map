{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Context, Attributes } from '@opentelemetry/api';\n\nexport type CommonReaderOptions = {\n  timeoutMillis?: number;\n};\n\nexport type CollectionOptions = CommonReaderOptions;\n\nexport type ShutdownOptions = CommonReaderOptions;\n\nexport type ForceFlushOptions = CommonReaderOptions;\n\n/**\n * This is intentionally not using the API's type as it's only available from @opentelemetry/api 1.9.0 and up.\n * In SDK 2.0 we'll be able to bump the minimum API version and remove this workaround.\n */\nexport interface Gauge<AttributesTypes extends Attributes = Attributes> {\n  /**\n   * Records a measurement. Value of the measurement must not be negative.\n   */\n  record(value: number, attributes?: AttributesTypes, context?: Context): void;\n}\n"]}