{"version": 3, "file": "AsyncMetricStorage.js", "sourceRoot": "", "sources": ["../../../src/state/AsyncMetricStorage.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMH,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAGpE,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAG7C;;;;GAIG;AACH;IACU,sCAAa;IAMrB,4BACE,qBAA2C,EAC3C,UAAyB,EACjB,oBAAyC,EACjD,gBAAyC,EACjC,4BAAqC;QAL/C,YAOE,kBAAM,qBAAqB,CAAC,SAS7B;QAbS,0BAAoB,GAApB,oBAAoB,CAAqB;QAEzC,kCAA4B,GAA5B,4BAA4B,CAAS;QAG7C,KAAI,CAAC,mBAAmB,GAAG,IAAI,oBAAoB,CACjD,UAAU,EACV,KAAI,CAAC,4BAA4B,CAClC,CAAC;QACF,KAAI,CAAC,sBAAsB,GAAG,IAAI,uBAAuB,CACvD,UAAU,EACV,gBAAgB,CACjB,CAAC;;IACJ,CAAC;IAED,mCAAM,GAAN,UAAO,YAAsC,EAAE,eAAuB;QAAtE,iBAMC;QALC,IAAM,SAAS,GAAG,IAAI,gBAAgB,EAAU,CAAC;QACjD,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,EAAmB;gBAAnB,KAAA,aAAmB,EAAlB,UAAU,QAAA,EAAE,KAAK,QAAA;YAC5D,SAAS,CAAC,GAAG,CAAC,KAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;OAMG;IACH,oCAAO,GAAP,UACE,SAAgC,EAChC,cAAsB;QAEtB,IAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QAEzD,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAC7C,SAAS,EACT,IAAI,CAAC,qBAAqB,EAC1B,aAAa,EACb,cAAc,CACf,CAAC;IACJ,CAAC;IACH,yBAAC;AAAD,CAAC,AArDD,CACU,aAAa,GAoDtB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { HrTime } from '@opentelemetry/api';\nimport { Accumulation, Aggregator } from '../aggregator/types';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\nimport { AttributesProcessor } from '../view/AttributesProcessor';\nimport { MetricStorage } from './MetricStorage';\nimport { MetricData } from '../export/MetricData';\nimport { DeltaMetricProcessor } from './DeltaMetricProcessor';\nimport { TemporalMetricProcessor } from './TemporalMetricProcessor';\nimport { Maybe } from '../utils';\nimport { MetricCollectorHandle } from './MetricCollector';\nimport { AttributeHashMap } from './HashMap';\nimport { AsyncWritableMetricStorage } from './WritableMetricStorage';\n\n/**\n * Internal interface.\n *\n * Stores and aggregates {@link MetricData} for asynchronous instruments.\n */\nexport class AsyncMetricStorage<T extends Maybe<Accumulation>>\n  extends MetricStorage\n  implements AsyncWritableMetricStorage\n{\n  private _deltaMetricStorage: DeltaMetricProcessor<T>;\n  private _temporalMetricStorage: TemporalMetricProcessor<T>;\n\n  constructor(\n    _instrumentDescriptor: InstrumentDescriptor,\n    aggregator: Aggregator<T>,\n    private _attributesProcessor: AttributesProcessor,\n    collectorHandles: MetricCollectorHandle[],\n    private _aggregationCardinalityLimit?: number\n  ) {\n    super(_instrumentDescriptor);\n    this._deltaMetricStorage = new DeltaMetricProcessor(\n      aggregator,\n      this._aggregationCardinalityLimit\n    );\n    this._temporalMetricStorage = new TemporalMetricProcessor(\n      aggregator,\n      collectorHandles\n    );\n  }\n\n  record(measurements: AttributeHashMap<number>, observationTime: HrTime) {\n    const processed = new AttributeHashMap<number>();\n    Array.from(measurements.entries()).forEach(([attributes, value]) => {\n      processed.set(this._attributesProcessor.process(attributes), value);\n    });\n    this._deltaMetricStorage.batchCumulate(processed, observationTime);\n  }\n\n  /**\n   * Collects the metrics from this storage. The ObservableCallback is invoked\n   * during the collection.\n   *\n   * Note: This is a stateful operation and may reset any interval-related\n   * state for the MetricCollector.\n   */\n  collect(\n    collector: MetricCollectorHandle,\n    collectionTime: HrTime\n  ): Maybe<MetricData> {\n    const accumulations = this._deltaMetricStorage.collect();\n\n    return this._temporalMetricStorage.buildMetrics(\n      collector,\n      this._instrumentDescriptor,\n      accumulations,\n      collectionTime\n    );\n  }\n}\n"]}