{"version": 3, "file": "Histogram.js", "sourceRoot": "", "sources": ["../../../src/aggregator/Histogram.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;AAEH,OAAO,EAIL,cAAc,GACf,MAAM,SAAS,CAAC;AACjB,OAAO,EACL,aAAa,GAGd,MAAM,sBAAsB,CAAC;AAE9B,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,cAAc,EAAS,MAAM,UAAU,CAAC;AAoBjD,SAAS,wBAAwB,CAAC,UAAoB;IACpD,IAAM,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,cAAM,OAAA,CAAC,EAAD,CAAC,CAAC,CAAC;IACvC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACf,OAAO;QACL,OAAO,EAAE;YACP,UAAU,YAAA;YACV,MAAM,QAAA;SACP;QACD,GAAG,EAAE,CAAC;QACN,KAAK,EAAE,CAAC;QACR,SAAS,EAAE,KAAK;QAChB,GAAG,EAAE,QAAQ;QACb,GAAG,EAAE,CAAC,QAAQ;KACf,CAAC;AACJ,CAAC;AAED;IACE,+BACS,SAAiB,EACP,WAAqB,EAC9B,aAAoB,EACpB,QAAmE;QADnE,8BAAA,EAAA,oBAAoB;QACpB,yBAAA,EAAA,WAA8B,wBAAwB,CAAC,WAAW,CAAC;QAHpE,cAAS,GAAT,SAAS,CAAQ;QACP,gBAAW,GAAX,WAAW,CAAU;QAC9B,kBAAa,GAAb,aAAa,CAAO;QACpB,aAAQ,GAAR,QAAQ,CAA2D;IAC1E,CAAC;IAEJ,sCAAM,GAAN,UAAO,KAAa;QAClB,4EAA4E;QAC5E,qGAAqG;QACrG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvB,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,KAAK,CAAC;QAE3B,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;SAChC;QAED,IAAM,GAAG,GAAG,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,4CAAY,GAAZ,UAAa,SAAiB;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,4CAAY,GAAZ;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IACH,4BAAC;AAAD,CAAC,AAnCD,IAmCC;;AAED;;;GAGG;AACH;IAGE;;;OAGG;IACH,6BACmB,WAAqB,EACrB,aAAsB;QADtB,gBAAW,GAAX,WAAW,CAAU;QACrB,kBAAa,GAAb,aAAa,CAAS;QARlC,SAAI,GAA6B,cAAc,CAAC,SAAS,CAAC;IAS9D,CAAC;IAEJ,gDAAkB,GAAlB,UAAmB,SAAiB;QAClC,OAAO,IAAI,qBAAqB,CAC9B,SAAS,EACT,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,mCAAK,GAAL,UACE,QAA+B,EAC/B,KAA4B;QAE5B,IAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QAC9C,IAAM,UAAU,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;QAExC,IAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC;QACpD,IAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;QAE9C,IAAM,YAAY,GAAG,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACtD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YACpD,YAAY,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;SAC5D;QAED,IAAI,GAAG,GAAG,QAAQ,CAAC;QACnB,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;QAEpB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,aAAa,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,EAAE;gBACnD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;gBAClD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;aACnD;iBAAM,IAAI,aAAa,CAAC,SAAS,EAAE;gBAClC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;gBACxB,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;aACzB;iBAAM,IAAI,UAAU,CAAC,SAAS,EAAE;gBAC/B,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;gBACrB,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;aACtB;SACF;QAED,OAAO,IAAI,qBAAqB,CAC9B,QAAQ,CAAC,SAAS,EAClB,aAAa,CAAC,OAAO,CAAC,UAAU,EAChC,IAAI,CAAC,aAAa,EAClB;YACE,OAAO,EAAE;gBACP,UAAU,EAAE,aAAa,CAAC,OAAO,CAAC,UAAU;gBAC5C,MAAM,EAAE,YAAY;aACrB;YACD,KAAK,EAAE,aAAa,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK;YAC7C,GAAG,EAAE,aAAa,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG;YACvC,SAAS,EACP,IAAI,CAAC,aAAa;gBAClB,CAAC,aAAa,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC;YACnD,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,GAAG;SACT,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kCAAI,GAAJ,UACE,QAA+B,EAC/B,OAA8B;QAE9B,IAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QAC9C,IAAM,YAAY,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;QAE5C,IAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC;QACpD,IAAM,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC;QAElD,IAAM,YAAY,GAAG,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACtD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YACpD,YAAY,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;SAC9D;QAED,OAAO,IAAI,qBAAqB,CAC9B,OAAO,CAAC,SAAS,EACjB,aAAa,CAAC,OAAO,CAAC,UAAU,EAChC,IAAI,CAAC,aAAa,EAClB;YACE,OAAO,EAAE;gBACP,UAAU,EAAE,aAAa,CAAC,OAAO,CAAC,UAAU;gBAC5C,MAAM,EAAE,YAAY;aACrB;YACD,KAAK,EAAE,YAAY,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK;YAC/C,GAAG,EAAE,YAAY,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG;YACzC,SAAS,EAAE,KAAK;YAChB,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE,CAAC,QAAQ;SACf,CACF,CAAC;IACJ,CAAC;IAED,0CAAY,GAAZ,UACE,UAA4B,EAC5B,sBAA8C,EAC9C,wBAAqE,EACrE,OAAe;QAEf,OAAO;YACL,UAAU,YAAA;YACV,sBAAsB,wBAAA;YACtB,aAAa,EAAE,aAAa,CAAC,SAAS;YACtC,UAAU,EAAE,wBAAwB,CAAC,GAAG,CAAC,UAAC,EAA0B;oBAA1B,KAAA,aAA0B,EAAzB,UAAU,QAAA,EAAE,YAAY,QAAA;gBACjE,IAAM,UAAU,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;gBAE/C,kDAAkD;gBAClD,IAAM,oBAAoB,GACxB,UAAU,CAAC,IAAI,KAAK,cAAc,CAAC,KAAK;oBACxC,UAAU,CAAC,IAAI,KAAK,cAAc,CAAC,eAAe;oBAClD,UAAU,CAAC,IAAI,KAAK,cAAc,CAAC,gBAAgB;oBACnD,UAAU,CAAC,IAAI,KAAK,cAAc,CAAC,0BAA0B,CAAC;gBAEhE,OAAO;oBACL,UAAU,YAAA;oBACV,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,OAAO,SAAA;oBACP,KAAK,EAAE;wBACL,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACtD,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACtD,GAAG,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;wBACvD,OAAO,EAAE,UAAU,CAAC,OAAO;wBAC3B,KAAK,EAAE,UAAU,CAAC,KAAK;qBACxB;iBACF,CAAC;YACJ,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;IACH,0BAAC;AAAD,CAAC,AAnJD,IAmJC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Accumulation,\n  AccumulationRecord,\n  Aggregator,\n  AggregatorKind,\n} from './types';\nimport {\n  DataPointType,\n  HistogramMetricData,\n  MetricDescriptor,\n} from '../export/MetricData';\nimport { HrTime } from '@opentelemetry/api';\nimport { InstrumentType } from '../InstrumentDescriptor';\nimport { binarySearchUB, Maybe } from '../utils';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\n\n/**\n * Internal value type for HistogramAggregation.\n * Differs from the exported type as undefined sum/min/max complicate arithmetic\n * performed by this aggregation, but are required to be undefined in the exported types.\n */\ninterface InternalHistogram {\n  buckets: {\n    boundaries: number[];\n    counts: number[];\n  };\n  sum: number;\n  count: number;\n  hasMinMax: boolean;\n  min: number;\n  max: number;\n}\n\nfunction createNewEmptyCheckpoint(boundaries: number[]): InternalHistogram {\n  const counts = boundaries.map(() => 0);\n  counts.push(0);\n  return {\n    buckets: {\n      boundaries,\n      counts,\n    },\n    sum: 0,\n    count: 0,\n    hasMinMax: false,\n    min: Infinity,\n    max: -Infinity,\n  };\n}\n\nexport class HistogramAccumulation implements Accumulation {\n  constructor(\n    public startTime: HrTime,\n    private readonly _boundaries: number[],\n    private _recordMinMax = true,\n    private _current: InternalHistogram = createNewEmptyCheckpoint(_boundaries)\n  ) {}\n\n  record(value: number): void {\n    // NaN does not fall into any bucket, is not zero and should not be counted,\n    // NaN is never greater than max nor less than min, therefore return as there's nothing for us to do.\n    if (Number.isNaN(value)) {\n      return;\n    }\n\n    this._current.count += 1;\n    this._current.sum += value;\n\n    if (this._recordMinMax) {\n      this._current.min = Math.min(value, this._current.min);\n      this._current.max = Math.max(value, this._current.max);\n      this._current.hasMinMax = true;\n    }\n\n    const idx = binarySearchUB(this._boundaries, value);\n    this._current.buckets.counts[idx] += 1;\n  }\n\n  setStartTime(startTime: HrTime): void {\n    this.startTime = startTime;\n  }\n\n  toPointValue(): InternalHistogram {\n    return this._current;\n  }\n}\n\n/**\n * Basic aggregator which observes events and counts them in pre-defined buckets\n * and provides the total sum and count of all observations.\n */\nexport class HistogramAggregator implements Aggregator<HistogramAccumulation> {\n  public kind: AggregatorKind.HISTOGRAM = AggregatorKind.HISTOGRAM;\n\n  /**\n   * @param _boundaries sorted upper bounds of recorded values.\n   * @param _recordMinMax If set to true, min and max will be recorded. Otherwise, min and max will not be recorded.\n   */\n  constructor(\n    private readonly _boundaries: number[],\n    private readonly _recordMinMax: boolean\n  ) {}\n\n  createAccumulation(startTime: HrTime) {\n    return new HistogramAccumulation(\n      startTime,\n      this._boundaries,\n      this._recordMinMax\n    );\n  }\n\n  /**\n   * Return the result of the merge of two histogram accumulations. As long as one Aggregator\n   * instance produces all Accumulations with constant boundaries we don't need to worry about\n   * merging accumulations with different boundaries.\n   */\n  merge(\n    previous: HistogramAccumulation,\n    delta: HistogramAccumulation\n  ): HistogramAccumulation {\n    const previousValue = previous.toPointValue();\n    const deltaValue = delta.toPointValue();\n\n    const previousCounts = previousValue.buckets.counts;\n    const deltaCounts = deltaValue.buckets.counts;\n\n    const mergedCounts = new Array(previousCounts.length);\n    for (let idx = 0; idx < previousCounts.length; idx++) {\n      mergedCounts[idx] = previousCounts[idx] + deltaCounts[idx];\n    }\n\n    let min = Infinity;\n    let max = -Infinity;\n\n    if (this._recordMinMax) {\n      if (previousValue.hasMinMax && deltaValue.hasMinMax) {\n        min = Math.min(previousValue.min, deltaValue.min);\n        max = Math.max(previousValue.max, deltaValue.max);\n      } else if (previousValue.hasMinMax) {\n        min = previousValue.min;\n        max = previousValue.max;\n      } else if (deltaValue.hasMinMax) {\n        min = deltaValue.min;\n        max = deltaValue.max;\n      }\n    }\n\n    return new HistogramAccumulation(\n      previous.startTime,\n      previousValue.buckets.boundaries,\n      this._recordMinMax,\n      {\n        buckets: {\n          boundaries: previousValue.buckets.boundaries,\n          counts: mergedCounts,\n        },\n        count: previousValue.count + deltaValue.count,\n        sum: previousValue.sum + deltaValue.sum,\n        hasMinMax:\n          this._recordMinMax &&\n          (previousValue.hasMinMax || deltaValue.hasMinMax),\n        min: min,\n        max: max,\n      }\n    );\n  }\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   */\n  diff(\n    previous: HistogramAccumulation,\n    current: HistogramAccumulation\n  ): HistogramAccumulation {\n    const previousValue = previous.toPointValue();\n    const currentValue = current.toPointValue();\n\n    const previousCounts = previousValue.buckets.counts;\n    const currentCounts = currentValue.buckets.counts;\n\n    const diffedCounts = new Array(previousCounts.length);\n    for (let idx = 0; idx < previousCounts.length; idx++) {\n      diffedCounts[idx] = currentCounts[idx] - previousCounts[idx];\n    }\n\n    return new HistogramAccumulation(\n      current.startTime,\n      previousValue.buckets.boundaries,\n      this._recordMinMax,\n      {\n        buckets: {\n          boundaries: previousValue.buckets.boundaries,\n          counts: diffedCounts,\n        },\n        count: currentValue.count - previousValue.count,\n        sum: currentValue.sum - previousValue.sum,\n        hasMinMax: false,\n        min: Infinity,\n        max: -Infinity,\n      }\n    );\n  }\n\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<HistogramAccumulation>[],\n    endTime: HrTime\n  ): Maybe<HistogramMetricData> {\n    return {\n      descriptor,\n      aggregationTemporality,\n      dataPointType: DataPointType.HISTOGRAM,\n      dataPoints: accumulationByAttributes.map(([attributes, accumulation]) => {\n        const pointValue = accumulation.toPointValue();\n\n        // determine if instrument allows negative values.\n        const allowsNegativeValues =\n          descriptor.type === InstrumentType.GAUGE ||\n          descriptor.type === InstrumentType.UP_DOWN_COUNTER ||\n          descriptor.type === InstrumentType.OBSERVABLE_GAUGE ||\n          descriptor.type === InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;\n\n        return {\n          attributes,\n          startTime: accumulation.startTime,\n          endTime,\n          value: {\n            min: pointValue.hasMinMax ? pointValue.min : undefined,\n            max: pointValue.hasMinMax ? pointValue.max : undefined,\n            sum: !allowsNegativeValues ? pointValue.sum : undefined,\n            buckets: pointValue.buckets,\n            count: pointValue.count,\n          },\n        };\n      }),\n    };\n  }\n}\n"]}