{"version": 3, "file": "LogarithmMapping.js", "sourceRoot": "", "sources": ["../../../../../src/aggregator/exponential-histogram/mapping/LogarithmMapping.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,KAAK,OAAO,MAAM,WAAW,CAAC;AACrC,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC;AAChC,OAAO,EAAW,YAAY,EAAE,MAAM,SAAS,CAAC;AAEhD;;;GAGG;AACH;IAKE,0BAAY,KAAa;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACH,qCAAU,GAAV,UAAW,KAAa;QACtB,IAAI,KAAK,IAAI,OAAO,CAAC,SAAS,EAAE;YAC9B,OAAO,IAAI,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;SAChD;QAED,kCAAkC;QAClC,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvC,IAAM,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC1C,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACjC;QAED,uEAAuE;QACvE,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9D,IAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,IAAI,QAAQ,EAAE;YACrB,OAAO,QAAQ,CAAC;SACjB;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACH,wCAAa,GAAb,UAAc,KAAa;QACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,IAAI,QAAQ,EAAE;YACrB,IAAI,KAAK,KAAK,QAAQ,EAAE;gBACtB,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;aACvE;YACD,MAAM,IAAI,YAAY,CACpB,eAAa,KAAK,sCAAiC,QAAU,CAC9D,CAAC;SACH;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACrD,IAAI,KAAK,IAAI,QAAQ,EAAE;YACrB,IAAI,KAAK,KAAK,QAAQ,EAAE;gBACtB,OAAO,OAAO,CAAC,SAAS,CAAC;aAC1B;iBAAM,IAAI,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE;gBACjC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;aACvE;YACD,MAAM,IAAI,YAAY,CACpB,eAAa,KAAK,sCAAiC,QAAU,CAC9D,CAAC;SACH;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IAC/C,CAAC;IAMD,sBAAI,mCAAK;QAJT;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAEO,uDAA4B,GAApC;QACE,OAAO,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,CAAC;IACpD,CAAC;IAEO,uDAA4B,GAApC;QACE,OAAO,CAAC,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAChE,CAAC;IACH,uBAAC;AAAD,CAAC,AApFD,IAoFC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as ieee754 from './ieee754';\nimport * as util from '../util';\nimport { Mapping, MappingError } from './types';\n\n/**\n * LogarithmMapping implements exponential mapping functions for scale > 0.\n * For scales <= 0 the exponent mapping should be used.\n */\nexport class LogarithmMapping implements Mapping {\n  private readonly _scale: number;\n  private readonly _scaleFactor: number;\n  private readonly _inverseFactor: number;\n\n  constructor(scale: number) {\n    this._scale = scale;\n    this._scaleFactor = util.ldexp(Math.LOG2E, scale);\n    this._inverseFactor = util.ldexp(Math.LN2, -scale);\n  }\n\n  /**\n   * Maps positive floating point values to indexes corresponding to scale\n   * @param value\n   * @returns {number} index for provided value at the current scale\n   */\n  mapToIndex(value: number): number {\n    if (value <= ieee754.MIN_VALUE) {\n      return this._minNormalLowerBoundaryIndex() - 1;\n    }\n\n    // exact power of two special case\n    if (ieee754.getSignificand(value) === 0) {\n      const exp = ieee754.getNormalBase2(value);\n      return (exp << this._scale) - 1;\n    }\n\n    // non-power of two cases. use Math.floor to round the scaled logarithm\n    const index = Math.floor(Math.log(value) * this._scaleFactor);\n    const maxIndex = this._maxNormalLowerBoundaryIndex();\n    if (index >= maxIndex) {\n      return maxIndex;\n    }\n\n    return index;\n  }\n\n  /**\n   * Returns the lower bucket boundary for the given index for scale\n   *\n   * @param index\n   * @returns {number}\n   */\n  lowerBoundary(index: number): number {\n    const maxIndex = this._maxNormalLowerBoundaryIndex();\n    if (index >= maxIndex) {\n      if (index === maxIndex) {\n        return 2 * Math.exp((index - (1 << this._scale)) / this._scaleFactor);\n      }\n      throw new MappingError(\n        `overflow: ${index} is > maximum lower boundary: ${maxIndex}`\n      );\n    }\n\n    const minIndex = this._minNormalLowerBoundaryIndex();\n    if (index <= minIndex) {\n      if (index === minIndex) {\n        return ieee754.MIN_VALUE;\n      } else if (index === minIndex - 1) {\n        return Math.exp((index + (1 << this._scale)) / this._scaleFactor) / 2;\n      }\n      throw new MappingError(\n        `overflow: ${index} is < minimum lower boundary: ${minIndex}`\n      );\n    }\n\n    return Math.exp(index * this._inverseFactor);\n  }\n\n  /**\n   * The scale used by this mapping\n   * @returns {number}\n   */\n  get scale(): number {\n    return this._scale;\n  }\n\n  private _minNormalLowerBoundaryIndex(): number {\n    return ieee754.MIN_NORMAL_EXPONENT << this._scale;\n  }\n\n  private _maxNormalLowerBoundaryIndex(): number {\n    return ((ieee754.MAX_NORMAL_EXPONENT + 1) << this._scale) - 1;\n  }\n}\n"]}