{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,6BAA6B;AAC7B,yBAAyB;AAEzB,oEAMwC;AACxC,4CAc4B;AAC5B,4EAG4C;AAC5C,8EAG6C;AAU7C,uCAAoC;AACpC,qCAA8B;AAG9B,MAAM,aAAa,GAAG,IAAI,uCAAiB,EAAE,CAAC;AAC9C,MAAM,YAAY,GAA+C;IAC/D,IAAI,CAAC,OAAO;QACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IACD,GAAG,CAAC,OAAO,EAAE,GAAW;QACtB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;CACF,CAAC;AAEW,QAAA,0BAA0B,GAAG,kBAAkB,CAAC;AAE7D,MAAa,wBAAyB,SAAQ,qCAAmB;IAI/D,YAA+B,UAA0C,EAAE;QACzE,KAAK,CAAC,2CAA2C,EAAE,iBAAO,EAAE,OAAO,CAAC,CAAC;QADxC,YAAO,GAAP,OAAO,CAAqC;QAEzE,IAAI,IAAI,CAAC,OAAO,CAAC,4BAA4B,IAAI,IAAI,EAAE;YACrD,IACE,OAAO,aAAG,CAAC,6CAA6C,CAAC;gBACvD,QAAQ;gBACV,aAAG,CACD,6CAA6C,CAC9C,CAAC,iBAAiB,EAAE,KAAK,MAAM,EAChC;gBACA,IAAI,CAAC,OAAO,CAAC,4BAA4B,GAAG,IAAI,CAAC;aAClD;SACF;IACH,CAAC;IAEQ,SAAS,CAAC,SAAyC,EAAE;QAC5D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,IAAI;;QACF,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;QAC9C,MAAM,UAAU,GAAG,MAAA,IAAI,CAAC,OAAO,CAAC,aAAa,mCAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;QAEtE,+GAA+G;QAC/G,IAAI,CAAC,QAAQ,IAAI,CAAC,UAAU,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,iFAAiF,EACjF,EAAE,QAAQ,EAAE,UAAU,EAAE,CACzB,CAAC;YACF,OAAO,EAAE,CAAC;SACX;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAE5E,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAErD,qDAAqD;QACrD,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC7B,sEAAsE;YACtE,8EAA8E;YAC9E,IAAI;gBACF,EAAE,CAAC,QAAQ,CAAC,GAAG,QAAQ,KAAK,CAAC,CAAC;gBAC9B,QAAQ,IAAI,KAAK,CAAC;aACnB;YAAC,OAAO,CAAC,EAAE;gBACV,mBAAmB;gBACnB,QAAQ,IAAI,MAAM,CAAC;aACpB;SACF;QAED,UAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE;YACzC,QAAQ;YACR,UAAU;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,YAAY;SACb,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,qDAAmC;YACrC,mGAAmG;YACnG,yDAAyD;YACzD,QAAQ,EACR,CAAC,GAAG,CAAC,EACL,SAAS,EACT,SAAS,EACT;gBACE,IAAI,+CAA6B,CAC/B,MAAM,EACN,CAAC,GAAG,CAAC,EACL,CAAC,aAA2B,EAAE,EAAE;oBAC9B,UAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;oBAChD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,YAAY,CAAC,CAAC,EAAE;wBAC1C,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;qBAC3C;oBACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;oBAC5D,OAAO,aAAa,CAAC;gBACvB,CAAC,EACD,CAAC,aAA4B,EAAE,EAAE;oBAC/B,IAAI,aAAa,IAAI,IAAI;wBAAE,OAAO;oBAClC,UAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;oBAChD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;gBAC5C,CAAC,CACF;aACF,CACF;SACF,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,OAAO,CAAC,QAAiB,EAAE,EAAE;YAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,QAAiB;QACxC,UAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC;QAEpB,OAAO,SAAS,cAAc;QAE5B,iDAAiD;QACjD,8DAA8D;QAC9D,KAAU,EACV,OAAgB,EAChB,QAAkB;YAElB,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;YAC9B,MAAM,MAAM,GAAG,wBAAwB,CAAC,gBAAgB,CACtD,KAAK,EACL,OAAO,EACP,MAAM,CAAC,4BAA4B,KAAK,IAAI,EAC5C,MAAM,CAAC,qBAAqB;gBAC1B,wBAAwB,CAAC,6BAA6B,CACzD,CAAC;YAEF,MAAM,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC;YAClC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAClC,IAAI,EACJ;gBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;gBACrB,UAAU,EAAE;oBACV,CAAC,yCAAkB,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,YAAY;oBACzD,CAAC,iDAA0B,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,kBAAkB;oBAChE,CAAC,iDAA0B,CAAC,gBAAgB,CAAC,EAC3C,wBAAwB,CAAC,iBAAiB,CACxC,OAAO,CAAC,kBAAkB,CAC3B;iBACJ;aACF,EACD,MAAM,CACP,CAAC;YAEF,IAAI,MAAM,CAAC,WAAW,EAAE;gBACtB,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,MAAM,CAAC,WAAY,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EACnD,CAAC,CAAC,EAAE;oBACF,IAAI,CAAC;wBACH,UAAI,CAAC,KAAK,CAAC,+CAA+C,EAAE,CAAC,CAAC,CAAC;gBACnE,CAAC,EACD,IAAI,CACL,CAAC;aACH;YAED,OAAO,aAAW,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;gBACxD,6GAA6G;gBAC7G,6GAA6G;gBAC7G,+GAA+G;gBAC/G,sCAAsC;gBACtC,MAAM,eAAe,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC7D,MAAM,YAAY,GAAG,IAAA,wCAAsB,EACzC,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC,EAC7D,KAAK,CAAC,EAAE;oBACN,IAAI,KAAK,IAAI,IAAI,EAAE;wBACjB,sEAAsE;wBACtE,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;wBACvC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;qBACxC;gBACH,CAAC,CACyB,CAAC;gBAC7B,IAAI,OAAO,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,CAAA,KAAK,UAAU,EAAE;oBAC5C,OAAO,YAAY,CAAC,IAAI,CACtB,KAAK,CAAC,EAAE;wBACN,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;wBAC7C,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAC3B,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CACvD,CAAC;oBACJ,CAAC,EACD,CAAC,GAAmB,EAAE,EAAE;wBACtB,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;wBACrC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAC9C,CAAC;oBACJ,CAAC,CACF,CAAC;iBACH;gBACD,OAAO,YAAY,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAEQ,iBAAiB,CAAC,cAA8B;QACvD,KAAK,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;IAClE,CAAC;IAEO,gBAAgB,CAAC,cAA8B;QACrD,IAAI,CAAC,cAAc;YAAE,OAAO,SAAS,CAAC;QAEtC,8DAA8D;QAC9D,IAAI,eAAe,GAAQ,cAAc,CAAC;QAE1C,IAAI,OAAO,eAAe,CAAC,WAAW,KAAK,UAAU,EAAE;YACrD,eAAe,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;SACjD;QAED,IAAI,OAAO,eAAe,CAAC,UAAU,KAAK,UAAU,EAAE;YACpD,OAAO,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACzD;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEQ,gBAAgB,CAAC,aAA4B;QACpD,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QACtC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;IACnE,CAAC;IAEO,iBAAiB,CAAC,aAA4B;QACpD,IAAI,CAAC,aAAa;YAAE,OAAO,SAAS,CAAC;QAErC,8DAA8D;QAC9D,MAAM,eAAe,GAAQ,aAAa,CAAC;QAE3C,IAAI,OAAO,eAAe,CAAC,UAAU,KAAK,UAAU,EAAE;YACpD,OAAO,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACzD;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,aAAa,CAAC,QAAkB,EAAE,IAAU;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,SAAS,eAAe,CAAc,GAAG,EAAE,GAAG;YACnD,UAAI,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;YACzD,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAE1C,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;gBAC9B,UAAI,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;gBAC1D,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAEO,QAAQ,CACd,IAAU,EACV,GAAsC,EACtC,QAAoB;QAEpB,IAAI,GAAG,EAAE;YACP,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;SAC3B;QAED,IAAI,UAAU,CAAC;QACf,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,UAAU,GAAG,GAAG,CAAC;SAClB;aAAM,IAAI,GAAG,EAAE;YACd,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC;SAC1B;QACD,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gBAC1B,OAAO,EAAE,UAAU;aACpB,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,GAAG,EAAE,CAAC;QAEX,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;SAC1C;aAAM;YACL,UAAI,CAAC,KAAK,CACR,sGAAsG,CACvG,CAAC;SACH;QACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;SAC3C;aAAM;YACL,UAAI,CAAC,KAAK,CACR,wGAAwG,CACzG,CAAC;SACH;QAED,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACjD,CAAC;IAEO,kBAAkB,CACxB,IAAU,EACV,GAA2B,EAC3B,GAAS;;QAET,IAAI,MAAA,IAAI,CAAC,OAAO,0CAAE,YAAY,EAAE;YAC9B,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAa,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EACpD,CAAC,CAAC,EAAE;gBACF,IAAI,CAAC;oBACH,UAAI,CAAC,KAAK,CAAC,gDAAgD,EAAE,CAAC,CAAC,CAAC;YACpE,CAAC,EACD,IAAI,CACL,CAAC;SACH;IACH,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,GAAW;QAC1C,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACrB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;SACjB;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,6BAA6B,CAAC,KAAU;QACrD,6EAA6E;QAC7E,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;QACxC,OAAO,iBAAW,CAAC,OAAO,CAAC,aAAW,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAC9E,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAC7B,KAAU,EACV,OAAgB,EAChB,4BAAqC,EACrC,qBAA4C;;QAE5C,IAAI,MAAM,GAA4B,SAAS,CAAC;QAChD,IAAI,CAAC,4BAA4B,EAAE;YACjC,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,kCAA0B,CAAC,CAAC;YAClE,IAAI,iBAAiB,EAAE;gBACrB,MAAM,GAAG,aAAa,CAAC,OAAO,CAC5B,aAAW,CAAC,MAAM,EAAE,EACpB,EAAE,CAAC,6CAAuB,CAAC,EAAE,iBAAiB,EAAE,EAChD,YAAY,CACb,CAAC;aACH;YACD,IAAI,MAAM,EAAE;gBACV,MAAM,WAAW,GAAG,MAAA,WAAK,CAAC,OAAO,CAAC,MAAM,CAAC,0CAAE,WAAW,EAAE,CAAC;gBACzD,IACE,WAAW;oBACX,CAAC,WAAW,CAAC,UAAU,GAAG,gBAAU,CAAC,OAAO,CAAC,KAAK,gBAAU,CAAC,OAAO,EACpE;oBACA,wFAAwF;oBACxF,uFAAuF;oBACvF,gCAAgC;oBAChC,OAAO,MAAM,CAAC;iBACf;aACF;SACF;QACD,MAAM,gBAAgB,GAAG,IAAA,wCAAsB,EAC7C,GAAG,EAAE,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,EAC3C,CAAC,CAAC,EAAE;YACF,IAAI,CAAC;gBACH,UAAI,CAAC,KAAK,CACR,yDAAyD,EACzD,CAAC,CACF,CAAC;QACN,CAAC,EACD,IAAI,CACL,CAAC;QACF,IAAI,MAAA,WAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,0CAAE,WAAW,EAAE,EAAE;YAClD,OAAO,gBAAgB,CAAC;SACzB;QACD,IAAI,CAAC,MAAM,EAAE;YACX,oDAAoD;YACpD,OAAO,kBAAY,CAAC;SACrB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA5WD,4DA4WC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as path from 'path';\nimport * as fs from 'fs';\n\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  Context as OtelContext,\n  context as otelContext,\n  diag,\n  trace,\n  propagation,\n  MeterProvider,\n  Span,\n  SpanKind,\n  SpanStatusCode,\n  TextMapGetter,\n  TraceFlags,\n  TracerProvider,\n  ROOT_CONTEXT,\n} from '@opentelemetry/api';\nimport {\n  AWSXRAY_TRACE_ID_HEADER,\n  AWSXRayPropagator,\n} from '@opentelemetry/propagator-aws-xray';\nimport {\n  SemanticAttributes,\n  SemanticResourceAttributes,\n} from '@opentelemetry/semantic-conventions';\n\nimport {\n  APIGatewayProxyEventHeaders,\n  Callback,\n  Context,\n  Handler,\n} from 'aws-lambda';\n\nimport { AwsLambdaInstrumentationConfig, EventContextExtractor } from './types';\nimport { VERSION } from './version';\nimport { env } from 'process';\nimport { LambdaModule } from './internal-types';\n\nconst awsPropagator = new AWSXRayPropagator();\nconst headerGetter: TextMapGetter<APIGatewayProxyEventHeaders> = {\n  keys(carrier): string[] {\n    return Object.keys(carrier);\n  },\n  get(carrier, key: string) {\n    return carrier[key];\n  },\n};\n\nexport const traceContextEnvironmentKey = '_X_AMZN_TRACE_ID';\n\nexport class AwsLambdaInstrumentation extends InstrumentationBase {\n  private _traceForceFlusher?: () => Promise<void>;\n  private _metricForceFlusher?: () => Promise<void>;\n\n  constructor(protected override _config: AwsLambdaInstrumentationConfig = {}) {\n    super('@opentelemetry/instrumentation-aws-lambda', VERSION, _config);\n    if (this._config.disableAwsContextPropagation == null) {\n      if (\n        typeof env['OTEL_LAMBDA_DISABLE_AWS_CONTEXT_PROPAGATION'] ===\n          'string' &&\n        env[\n          'OTEL_LAMBDA_DISABLE_AWS_CONTEXT_PROPAGATION'\n        ].toLocaleLowerCase() === 'true'\n      ) {\n        this._config.disableAwsContextPropagation = true;\n      }\n    }\n  }\n\n  override setConfig(config: AwsLambdaInstrumentationConfig = {}) {\n    this._config = config;\n  }\n\n  init() {\n    const taskRoot = process.env.LAMBDA_TASK_ROOT;\n    const handlerDef = this._config.lambdaHandler ?? process.env._HANDLER;\n\n    // _HANDLER and LAMBDA_TASK_ROOT are always defined in Lambda but guard bail out if in the future this changes.\n    if (!taskRoot || !handlerDef) {\n      this._diag.debug(\n        'Skipping lambda instrumentation: no _HANDLER/lambdaHandler or LAMBDA_TASK_ROOT.',\n        { taskRoot, handlerDef }\n      );\n      return [];\n    }\n\n    const handler = path.basename(handlerDef);\n    const moduleRoot = handlerDef.substr(0, handlerDef.length - handler.length);\n\n    const [module, functionName] = handler.split('.', 2);\n\n    // Lambda loads user function using an absolute path.\n    let filename = path.resolve(taskRoot, moduleRoot, module);\n    if (!filename.endsWith('.js')) {\n      // its impossible to know in advance if the user has a cjs or js file.\n      // check that the .js file exists otherwise fallback to next known possibility\n      try {\n        fs.statSync(`${filename}.js`);\n        filename += '.js';\n      } catch (e) {\n        // fallback to .cjs\n        filename += '.cjs';\n      }\n    }\n\n    diag.debug('Instrumenting lambda handler', {\n      taskRoot,\n      handlerDef,\n      handler,\n      moduleRoot,\n      module,\n      filename,\n      functionName,\n    });\n\n    return [\n      new InstrumentationNodeModuleDefinition(\n        // NB: The patching infrastructure seems to match names backwards, this must be the filename, while\n        // InstrumentationNodeModuleFile must be the module name.\n        filename,\n        ['*'],\n        undefined,\n        undefined,\n        [\n          new InstrumentationNodeModuleFile(\n            module,\n            ['*'],\n            (moduleExports: LambdaModule) => {\n              diag.debug('Applying patch for lambda handler');\n              if (isWrapped(moduleExports[functionName])) {\n                this._unwrap(moduleExports, functionName);\n              }\n              this._wrap(moduleExports, functionName, this._getHandler());\n              return moduleExports;\n            },\n            (moduleExports?: LambdaModule) => {\n              if (moduleExports == null) return;\n              diag.debug('Removing patch for lambda handler');\n              this._unwrap(moduleExports, functionName);\n            }\n          ),\n        ]\n      ),\n    ];\n  }\n\n  private _getHandler() {\n    return (original: Handler) => {\n      return this._getPatchHandler(original);\n    };\n  }\n\n  private _getPatchHandler(original: Handler) {\n    diag.debug('patch handler function');\n    const plugin = this;\n\n    return function patchedHandler(\n      this: never,\n      // The event can be a user type, it truly is any.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      event: any,\n      context: Context,\n      callback: Callback\n    ) {\n      const config = plugin._config;\n      const parent = AwsLambdaInstrumentation._determineParent(\n        event,\n        context,\n        config.disableAwsContextPropagation === true,\n        config.eventContextExtractor ||\n          AwsLambdaInstrumentation._defaultEventContextExtractor\n      );\n\n      const name = context.functionName;\n      const span = plugin.tracer.startSpan(\n        name,\n        {\n          kind: SpanKind.SERVER,\n          attributes: {\n            [SemanticAttributes.FAAS_EXECUTION]: context.awsRequestId,\n            [SemanticResourceAttributes.FAAS_ID]: context.invokedFunctionArn,\n            [SemanticResourceAttributes.CLOUD_ACCOUNT_ID]:\n              AwsLambdaInstrumentation._extractAccountId(\n                context.invokedFunctionArn\n              ),\n          },\n        },\n        parent\n      );\n\n      if (config.requestHook) {\n        safeExecuteInTheMiddle(\n          () => config.requestHook!(span, { event, context }),\n          e => {\n            if (e)\n              diag.error('aws-lambda instrumentation: requestHook error', e);\n          },\n          true\n        );\n      }\n\n      return otelContext.with(trace.setSpan(parent, span), () => {\n        // Lambda seems to pass a callback even if handler is of Promise form, so we wrap all the time before calling\n        // the handler and see if the result is a Promise or not. In such a case, the callback is usually ignored. If\n        // the handler happened to both call the callback and complete a returned Promise, whichever happens first will\n        // win and the latter will be ignored.\n        const wrappedCallback = plugin._wrapCallback(callback, span);\n        const maybePromise = safeExecuteInTheMiddle(\n          () => original.apply(this, [event, context, wrappedCallback]),\n          error => {\n            if (error != null) {\n              // Exception thrown synchronously before resolving callback / promise.\n              plugin._applyResponseHook(span, error);\n              plugin._endSpan(span, error, () => {});\n            }\n          }\n        ) as Promise<{}> | undefined;\n        if (typeof maybePromise?.then === 'function') {\n          return maybePromise.then(\n            value => {\n              plugin._applyResponseHook(span, null, value);\n              return new Promise(resolve =>\n                plugin._endSpan(span, undefined, () => resolve(value))\n              );\n            },\n            (err: Error | string) => {\n              plugin._applyResponseHook(span, err);\n              return new Promise((resolve, reject) =>\n                plugin._endSpan(span, err, () => reject(err))\n              );\n            }\n          );\n        }\n        return maybePromise;\n      });\n    };\n  }\n\n  override setTracerProvider(tracerProvider: TracerProvider) {\n    super.setTracerProvider(tracerProvider);\n    this._traceForceFlusher = this._traceForceFlush(tracerProvider);\n  }\n\n  private _traceForceFlush(tracerProvider: TracerProvider) {\n    if (!tracerProvider) return undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let currentProvider: any = tracerProvider;\n\n    if (typeof currentProvider.getDelegate === 'function') {\n      currentProvider = currentProvider.getDelegate();\n    }\n\n    if (typeof currentProvider.forceFlush === 'function') {\n      return currentProvider.forceFlush.bind(currentProvider);\n    }\n\n    return undefined;\n  }\n\n  override setMeterProvider(meterProvider: MeterProvider) {\n    super.setMeterProvider(meterProvider);\n    this._metricForceFlusher = this._metricForceFlush(meterProvider);\n  }\n\n  private _metricForceFlush(meterProvider: MeterProvider) {\n    if (!meterProvider) return undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const currentProvider: any = meterProvider;\n\n    if (typeof currentProvider.forceFlush === 'function') {\n      return currentProvider.forceFlush.bind(currentProvider);\n    }\n\n    return undefined;\n  }\n\n  private _wrapCallback(original: Callback, span: Span): Callback {\n    const plugin = this;\n    return function wrappedCallback(this: never, err, res) {\n      diag.debug('executing wrapped lookup callback function');\n      plugin._applyResponseHook(span, err, res);\n\n      plugin._endSpan(span, err, () => {\n        diag.debug('executing original lookup callback function');\n        return original.apply(this, [err, res]);\n      });\n    };\n  }\n\n  private _endSpan(\n    span: Span,\n    err: string | Error | null | undefined,\n    callback: () => void\n  ) {\n    if (err) {\n      span.recordException(err);\n    }\n\n    let errMessage;\n    if (typeof err === 'string') {\n      errMessage = err;\n    } else if (err) {\n      errMessage = err.message;\n    }\n    if (errMessage) {\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: errMessage,\n      });\n    }\n\n    span.end();\n\n    const flushers = [];\n    if (this._traceForceFlusher) {\n      flushers.push(this._traceForceFlusher());\n    } else {\n      diag.error(\n        'Spans may not be exported for the lambda function because we are not force flushing before callback.'\n      );\n    }\n    if (this._metricForceFlusher) {\n      flushers.push(this._metricForceFlusher());\n    } else {\n      diag.error(\n        'Metrics may not be exported for the lambda function because we are not force flushing before callback.'\n      );\n    }\n\n    Promise.all(flushers).then(callback, callback);\n  }\n\n  private _applyResponseHook(\n    span: Span,\n    err?: Error | string | null,\n    res?: any\n  ) {\n    if (this._config?.responseHook) {\n      safeExecuteInTheMiddle(\n        () => this._config.responseHook!(span, { err, res }),\n        e => {\n          if (e)\n            diag.error('aws-lambda instrumentation: responseHook error', e);\n        },\n        true\n      );\n    }\n  }\n\n  private static _extractAccountId(arn: string): string | undefined {\n    const parts = arn.split(':');\n    if (parts.length >= 5) {\n      return parts[4];\n    }\n    return undefined;\n  }\n\n  private static _defaultEventContextExtractor(event: any): OtelContext {\n    // The default extractor tries to get sampled trace header from HTTP headers.\n    const httpHeaders = event.headers || {};\n    return propagation.extract(otelContext.active(), httpHeaders, headerGetter);\n  }\n\n  private static _determineParent(\n    event: any,\n    context: Context,\n    disableAwsContextPropagation: boolean,\n    eventContextExtractor: EventContextExtractor\n  ): OtelContext {\n    let parent: OtelContext | undefined = undefined;\n    if (!disableAwsContextPropagation) {\n      const lambdaTraceHeader = process.env[traceContextEnvironmentKey];\n      if (lambdaTraceHeader) {\n        parent = awsPropagator.extract(\n          otelContext.active(),\n          { [AWSXRAY_TRACE_ID_HEADER]: lambdaTraceHeader },\n          headerGetter\n        );\n      }\n      if (parent) {\n        const spanContext = trace.getSpan(parent)?.spanContext();\n        if (\n          spanContext &&\n          (spanContext.traceFlags & TraceFlags.SAMPLED) === TraceFlags.SAMPLED\n        ) {\n          // Trace header provided by Lambda only sampled if a sampled context was propagated from\n          // an upstream cloud service such as S3, or the user is using X-Ray. In these cases, we\n          // need to use it as the parent.\n          return parent;\n        }\n      }\n    }\n    const extractedContext = safeExecuteInTheMiddle(\n      () => eventContextExtractor(event, context),\n      e => {\n        if (e)\n          diag.error(\n            'aws-lambda instrumentation: eventContextExtractor error',\n            e\n          );\n      },\n      true\n    );\n    if (trace.getSpan(extractedContext)?.spanContext()) {\n      return extractedContext;\n    }\n    if (!parent) {\n      // No context in Lambda environment or HTTP headers.\n      return ROOT_CONTEXT;\n    }\n    return parent;\n  }\n}\n"]}