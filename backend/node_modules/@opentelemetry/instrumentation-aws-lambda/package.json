{"name": "@opentelemetry/instrumentation-aws-lambda", "version": "0.37.4", "description": "OpenTelemetry AWS Lambda automatic instrumentation package.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "tdd": "npm run test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/instrumentation-aws-lambda --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "version:update": "node ../../../scripts/version-update.js", "compile": "tsc -p ."}, "keywords": ["aws-lambda", "instrumentation", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/core": "^1.8.0", "@opentelemetry/sdk-metrics": "^1.8.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.6.5", "mocha": "7.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/propagator-aws-xray": "^1.3.1", "@opentelemetry/resources": "^1.8.0", "@opentelemetry/semantic-conventions": "^1.0.0", "@types/aws-lambda": "8.10.122"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-aws-lambda#readme", "gitHead": "90928231259bbbdf6980f184bc7420503048b77e"}