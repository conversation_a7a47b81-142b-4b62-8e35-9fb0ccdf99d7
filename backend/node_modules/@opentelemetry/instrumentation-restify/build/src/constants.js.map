{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/constants.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACU,QAAA,kBAAkB,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACpC,QAAA,eAAe,GAAG;IAC7B,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAK;IACL,OAAO;CACR,CAAC;AACW,QAAA,WAAW,GAAG,SAAS,CAAC;AACxB,QAAA,kBAAkB,GAAG,CAAC,aAAa,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport const RESTIFY_MW_METHODS = ['use', 'pre'];\nexport const RESTIFY_METHODS = [\n  'del',\n  'get',\n  'head',\n  'opts',\n  'post',\n  'put',\n  'patch',\n];\nexport const MODULE_NAME = 'restify';\nexport const SUPPORTED_VERSIONS = ['>=4.0.0 <12'];\n"]}