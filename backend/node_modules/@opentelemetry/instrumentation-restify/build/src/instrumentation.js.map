{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,0CAA0C;AAE1C,mCAAoC;AACpC,yDAAyD;AACzD,uCAAoC;AACpC,yCAAyC;AACzC,oEAMwC;AACxC,8EAAyE;AACzE,mCAAqD;AACrD,8CAA8D;AAG9D,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;AAErB,MAAa,sBAAuB,SAAQ,qCAAwB;IAClE,YAAY,SAAuC,EAAE;QACnD,KAAK,CACH,kCAAkC,SAAS,CAAC,WAAW,EAAE,EACzD,iBAAO,EACP,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAC1B,CAAC;QAII,gBAAW,GAAG,KAAK,CAAC;IAH5B,CAAC;IAKQ,SAAS,CAAC,SAAuC,EAAE;QAC1D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,OAAuC,CAAC;IACtD,CAAC;IAED,IAAI;QACF,MAAM,MAAM,GAAG,IAAI,qDAAmC,CACpD,SAAS,CAAC,WAAW,EACrB,SAAS,CAAC,kBAAkB,EAC5B,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;YACpC,OAAO,aAAa,CAAC;QACvB,CAAC,CACF,CAAC;QAEF,MAAM,CAAC,KAAK,CAAC,IAAI,CACf,IAAI,+CAA6B,CAC/B,uBAAuB,EACvB,SAAS,CAAC,kBAAkB,EAC5B,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,KAAK,CACR,sBAAsB,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CAC/D,CAAC;YACF,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,MAAM,MAAM,GAAQ,aAAa,CAAC;YAClC,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,eAAe,EAAE;gBAC5C,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;oBACrC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;iBACtC;gBACD,IAAI,CAAC,KAAK,CACR,MAAM,CAAC,SAAS,EAChB,IAAoB,EACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAC/B,CAAC;aACH;YACD,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,kBAAkB,EAAE;gBAC/C,IAAI,IAAA,2BAAS,EAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;oBACrC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;iBACtC;gBACD,IAAI,CAAC,KAAK,CACR,MAAM,CAAC,SAAS,EAChB,IAAoB,EACpB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CACnC,CAAC;aACH;YACD,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAa,EAAE,aAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,KAAK,CACR,sBAAsB,SAAS,CAAC,WAAW,IAAI,aAAa,EAAE,CAC/D,CAAC;YACF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,aAAa,EAAE;gBACjB,MAAM,MAAM,GAAQ,aAAa,CAAC;gBAClC,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,eAAe,EAAE;oBAC5C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,IAAoB,CAAC,CAAC;iBACtD;gBACD,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,kBAAkB,EAAE;oBAC/C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,IAAoB,CAAC,CAAC;iBACtD;aACF;QACH,CAAC,CACF,CACF,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,kBAAkB,CAAC,QAAkB,EAAE,UAAmB;QAChE,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAwB,GAAG,OAAoC;YACpE,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,eAAe,CAAC,eAAe,CAC7B,EAAE,IAAI,EAAE,iBAAS,CAAC,UAAU,EAAE,UAAU,EAAE,EAC1C,OAAO,CACR,CACF,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,QAAkB,EAAE,UAAmB;QAC5D,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAEL,IAAS,EACT,GAAG,OAAoC;YAEvC,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,IAAI,EACJ,GAAG,eAAe,CAAC,eAAe,CAChC,EAAE,IAAI,EAAE,iBAAS,CAAC,eAAe,EAAE,IAAI,EAAE,UAAU,EAAE,EACrD,OAAO,CACR,CACF,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,+EAA+E;IACvE,eAAe,CACrB,QAAwB,EACxB,OAA6D;QAE7D,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC1B,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;SACxE;QACD,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,OAAO,CACL,GAAkB,EAClB,GAAqB,EACrB,IAAkB,EAClB,EAAE;;gBACF,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpB,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;iBAChC;gBACD,MAAM,KAAK,GACT,OAAO,GAAG,CAAC,QAAQ,KAAK,UAAU;oBAChC,CAAC,CAAC,MAAA,GAAG,CAAC,QAAQ,EAAE,0CAAE,IAAI;oBACtB,CAAC,CAAC,MAAA,GAAG,CAAC,KAAK,0CAAE,IAAI,CAAC;gBAEtB,oEAAoE;gBACpE,MAAM,YAAY,GAAG,IAAA,qBAAc,EAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC1D,IAAI,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,MAAK,cAAO,CAAC,IAAI,EAAE;oBACvC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;iBAC5B;gBAED,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC;gBACzC,MAAM,QAAQ,GACZ,QAAQ,CAAC,IAAI,KAAK,iBAAS,CAAC,eAAe;oBACzC,CAAC,CAAC,qBAAqB,KAAK,EAAE;oBAC9B,CAAC,CAAC,gBAAgB,MAAM,IAAI,WAAW,EAAE,CAAC;gBAC9C,MAAM,UAAU,GAAG;oBACjB,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,MAAM;oBAC5C,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,IAAI,KAAK;oBACrE,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI;oBACnD,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,UAAU;oBAC3D,CAAC,yCAAkB,CAAC,UAAU,CAAC,EAAE,KAAK;iBACvC,CAAC;gBACF,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAChC,QAAQ,EACR;oBACE,UAAU;iBACX,EACD,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CACrB,CAAC;gBAEF,MAAM,eAAe,GAAG,IAAI,CAAC;gBAC7B,MAAM,WAAW,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC;gBAC5D,IAAI,WAAW,EAAE;oBACf,IAAA,wCAAsB,EACpB,GAAG,EAAE;wBACH,OAAO,WAAY,CAAC,IAAI,EAAE;4BACxB,OAAO,EAAE,GAAG;4BACZ,SAAS,EAAE,QAAQ,CAAC,IAAI;yBACzB,CAAC,CAAC;oBACL,CAAC,EACD,CAAC,CAAC,EAAE;wBACF,IAAI,CAAC,EAAE;4BACL,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;yBACvD;oBACH,CAAC,EACD,IAAI,CACL,CAAC;iBACH;gBAED,MAAM,WAAW,GAAG,CAAC,GAAS,EAAE,EAAE;oBAChC,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,IAAI,CAAC,GAAG,CAAC,CAAC;gBACZ,CAAC,CAAC;gBACF,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;gBAEnC,MAAM,WAAW,GAAG,CAAC,OAAyB,EAAE,EAAE;oBAChD,OAAO,OAAO;yBACX,IAAI,CAAC,KAAK,CAAC,EAAE;wBACZ,IAAI,CAAC,GAAG,EAAE,CAAC;wBACX,OAAO,KAAK,CAAC;oBACf,CAAC,CAAC;yBACD,KAAK,CAAC,GAAG,CAAC,EAAE;wBACX,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;wBAC1B,IAAI,CAAC,GAAG,EAAE,CAAC;wBACX,MAAM,GAAG,CAAC;oBACZ,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC;gBAEF,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;gBACjE,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,UAAU,EACV,CAAC,GAAkB,EAAE,GAAqB,EAAE,IAAkB,EAAE,EAAE;oBAChE,IAAI,IAAA,uBAAe,EAAC,OAAO,CAAC,EAAE;wBAC5B,OAAO,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;qBAC7C;oBACD,IAAI;wBACF,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;wBACvC,IAAI,IAAA,iBAAS,EAAC,MAAM,CAAC,EAAE;4BACrB,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;yBAC5B;wBACD,IAAI,CAAC,GAAG,EAAE,CAAC;wBACX,OAAO,MAAM,CAAC;qBACf;oBAAC,OAAO,GAAQ,EAAE;wBACjB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;wBAC1B,IAAI,CAAC,GAAG,EAAE,CAAC;wBACX,MAAM,GAAG,CAAC;qBACX;gBACH,CAAC,EACD,IAAI,EACJ,GAAG,EACH,GAAG,EACH,WAAW,CACZ,CAAC;YACJ,CAAC,CAAC;SACH;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AAtOD,wDAsOC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as types from './internal-types';\nimport type * as restify from 'restify';\n\nimport * as api from '@opentelemetry/api';\nimport type { Server } from 'restify';\nimport { LayerType } from './types';\nimport * as AttributeNames from './enums/AttributeNames';\nimport { VERSION } from './version';\nimport * as constants from './constants';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { SemanticAttributes } from '@opentelemetry/semantic-conventions';\nimport { isPromise, isAsyncFunction } from './utils';\nimport { getRPCMetadata, RPCType } from '@opentelemetry/core';\nimport type { RestifyInstrumentationConfig } from './types';\n\nconst { diag } = api;\n\nexport class RestifyInstrumentation extends InstrumentationBase<any> {\n  constructor(config: RestifyInstrumentationConfig = {}) {\n    super(\n      `@opentelemetry/instrumentation-${constants.MODULE_NAME}`,\n      VERSION,\n      Object.assign({}, config)\n    );\n  }\n\n  private _moduleVersion?: string;\n  private _isDisabled = false;\n\n  override setConfig(config: RestifyInstrumentationConfig = {}) {\n    this._config = Object.assign({}, config);\n  }\n\n  override getConfig(): RestifyInstrumentationConfig {\n    return this._config as RestifyInstrumentationConfig;\n  }\n\n  init() {\n    const module = new InstrumentationNodeModuleDefinition<any>(\n      constants.MODULE_NAME,\n      constants.SUPPORTED_VERSIONS,\n      (moduleExports, moduleVersion) => {\n        this._moduleVersion = moduleVersion;\n        return moduleExports;\n      }\n    );\n\n    module.files.push(\n      new InstrumentationNodeModuleFile<any>(\n        'restify/lib/server.js',\n        constants.SUPPORTED_VERSIONS,\n        (moduleExports, moduleVersion) => {\n          diag.debug(\n            `Applying patch for ${constants.MODULE_NAME}@${moduleVersion}`\n          );\n          this._isDisabled = false;\n          const Server: any = moduleExports;\n          for (const name of constants.RESTIFY_METHODS) {\n            if (isWrapped(Server.prototype[name])) {\n              this._unwrap(Server.prototype, name);\n            }\n            this._wrap(\n              Server.prototype,\n              name as keyof Server,\n              this._methodPatcher.bind(this)\n            );\n          }\n          for (const name of constants.RESTIFY_MW_METHODS) {\n            if (isWrapped(Server.prototype[name])) {\n              this._unwrap(Server.prototype, name);\n            }\n            this._wrap(\n              Server.prototype,\n              name as keyof Server,\n              this._middlewarePatcher.bind(this)\n            );\n          }\n          return moduleExports;\n        },\n        (moduleExports, moduleVersion) => {\n          diag.debug(\n            `Removing patch for ${constants.MODULE_NAME}@${moduleVersion}`\n          );\n          this._isDisabled = true;\n          if (moduleExports) {\n            const Server: any = moduleExports;\n            for (const name of constants.RESTIFY_METHODS) {\n              this._unwrap(Server.prototype, name as keyof Server);\n            }\n            for (const name of constants.RESTIFY_MW_METHODS) {\n              this._unwrap(Server.prototype, name as keyof Server);\n            }\n          }\n        }\n      )\n    );\n\n    return module;\n  }\n\n  private _middlewarePatcher(original: Function, methodName?: string) {\n    const instrumentation = this;\n    return function (this: Server, ...handler: types.NestedRequestHandlers) {\n      return original.call(\n        this,\n        instrumentation._handlerPatcher(\n          { type: LayerType.MIDDLEWARE, methodName },\n          handler\n        )\n      );\n    };\n  }\n\n  private _methodPatcher(original: Function, methodName?: string) {\n    const instrumentation = this;\n    return function (\n      this: Server,\n      path: any,\n      ...handler: types.NestedRequestHandlers\n    ) {\n      return original.call(\n        this,\n        path,\n        ...instrumentation._handlerPatcher(\n          { type: LayerType.REQUEST_HANDLER, path, methodName },\n          handler\n        )\n      );\n    };\n  }\n\n  // will return the same type as `handler`, but all functions recusively patched\n  private _handlerPatcher(\n    metadata: types.Metadata,\n    handler: restify.RequestHandler | types.NestedRequestHandlers\n  ): any {\n    if (Array.isArray(handler)) {\n      return handler.map(handler => this._handlerPatcher(metadata, handler));\n    }\n    if (typeof handler === 'function') {\n      return (\n        req: types.Request,\n        res: restify.Response,\n        next: restify.Next\n      ) => {\n        if (this._isDisabled) {\n          return handler(req, res, next);\n        }\n        const route =\n          typeof req.getRoute === 'function'\n            ? req.getRoute()?.path\n            : req.route?.path;\n\n        // replace HTTP instrumentations name with one that contains a route\n        const httpMetadata = getRPCMetadata(api.context.active());\n        if (httpMetadata?.type === RPCType.HTTP) {\n          httpMetadata.route = route;\n        }\n\n        const fnName = handler.name || undefined;\n        const spanName =\n          metadata.type === LayerType.REQUEST_HANDLER\n            ? `request handler - ${route}`\n            : `middleware - ${fnName || 'anonymous'}`;\n        const attributes = {\n          [AttributeNames.AttributeNames.NAME]: fnName,\n          [AttributeNames.AttributeNames.VERSION]: this._moduleVersion || 'n/a',\n          [AttributeNames.AttributeNames.TYPE]: metadata.type,\n          [AttributeNames.AttributeNames.METHOD]: metadata.methodName,\n          [SemanticAttributes.HTTP_ROUTE]: route,\n        };\n        const span = this.tracer.startSpan(\n          spanName,\n          {\n            attributes,\n          },\n          api.context.active()\n        );\n\n        const instrumentation = this;\n        const requestHook = instrumentation.getConfig().requestHook;\n        if (requestHook) {\n          safeExecuteInTheMiddle(\n            () => {\n              return requestHook!(span, {\n                request: req,\n                layerType: metadata.type,\n              });\n            },\n            e => {\n              if (e) {\n                instrumentation._diag.error('request hook failed', e);\n              }\n            },\n            true\n          );\n        }\n\n        const patchedNext = (err?: any) => {\n          span.end();\n          next(err);\n        };\n        patchedNext.ifError = next.ifError;\n\n        const wrapPromise = (promise: Promise<unknown>) => {\n          return promise\n            .then(value => {\n              span.end();\n              return value;\n            })\n            .catch(err => {\n              span.recordException(err);\n              span.end();\n              throw err;\n            });\n        };\n\n        const newContext = api.trace.setSpan(api.context.active(), span);\n        return api.context.with(\n          newContext,\n          (req: types.Request, res: restify.Response, next: restify.Next) => {\n            if (isAsyncFunction(handler)) {\n              return wrapPromise(handler(req, res, next));\n            }\n            try {\n              const result = handler(req, res, next);\n              if (isPromise(result)) {\n                return wrapPromise(result);\n              }\n              span.end();\n              return result;\n            } catch (err: any) {\n              span.recordException(err);\n              span.end();\n              throw err;\n            }\n          },\n          this,\n          req,\n          res,\n          patchedNext\n        );\n      };\n    }\n\n    return handler;\n  }\n}\n"]}