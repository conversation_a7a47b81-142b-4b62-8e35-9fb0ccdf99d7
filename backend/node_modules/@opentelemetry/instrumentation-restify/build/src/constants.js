"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SUPPORTED_VERSIONS = exports.MODULE_NAME = exports.RESTIFY_METHODS = exports.RESTIFY_MW_METHODS = void 0;
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
exports.RESTIFY_MW_METHODS = ['use', 'pre'];
exports.RESTIFY_METHODS = [
    'del',
    'get',
    'head',
    'opts',
    'post',
    'put',
    'patch',
];
exports.MODULE_NAME = 'restify';
exports.SUPPORTED_VERSIONS = ['>=4.0.0 <12'];
//# sourceMappingURL=constants.js.map