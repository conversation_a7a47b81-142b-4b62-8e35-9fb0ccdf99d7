{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Span } from '@opentelemetry/api';\nimport type * as restify from 'restify';\nimport { LayerType } from './types';\n\ndeclare interface RequestWithRoute extends restify.Request {\n  route: { path: string };\n  getRoute: () => { path: string };\n}\n\nexport declare type Request = RequestWithRoute;\nexport declare type Metadata = {\n  path?: string;\n  methodName?: string;\n  type: LayerType;\n};\n\nexport type NestedRequestHandlers = Array<\n  NestedRequestHandlers | restify.RequestHandler\n>;\n\n/**\n * extends opentelemetry/api Span object to instrument the root span name of http instrumentation\n */\nexport interface InstrumentationSpan extends Span {\n  name?: string;\n}\n"]}