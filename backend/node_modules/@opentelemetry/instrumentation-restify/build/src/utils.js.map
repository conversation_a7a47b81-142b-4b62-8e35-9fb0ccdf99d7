{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,gDAAgD;AACzC,MAAM,SAAS,GAAG,CAAC,KAAW,EAA6B,EAAE;IAClE,OAAO,CAAC,CAAC,CACP,KAAK;QACL,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU;QAChC,OAAO,KAAK,CAAC,KAAK,KAAK,UAAU;QACjC,KAAK,CAAC,QAAQ,EAAE,KAAK,kBAAkB,CACxC,CAAC;AACJ,CAAC,CAAC;AAPW,QAAA,SAAS,aAOpB;AAEF,sDAAsD;AAC/C,MAAM,eAAe,GAAG,CAAC,KAAe,EAAE,EAAE;;IACjD,OAAO,CAAC,CAAC,CACP,KAAK;QACL,OAAO,KAAK,KAAK,UAAU;QAC3B,CAAA,MAAA,KAAK,CAAC,WAAW,0CAAE,IAAI,MAAK,eAAe,CAC5C,CAAC;AACJ,CAAC,CAAC;AANW,QAAA,eAAe,mBAM1B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// util.types.isPromise is supported from 10.0.0\nexport const isPromise = (value?: any): value is Promise<unknown> => {\n  return !!(\n    value &&\n    typeof value.then === 'function' &&\n    typeof value.catch === 'function' &&\n    value.toString() === '[object Promise]'\n  );\n};\n\n// util.types.isAsyncFunction is supported from 10.0.0\nexport const isAsyncFunction = (value?: unknown) => {\n  return !!(\n    value &&\n    typeof value === 'function' &&\n    value.constructor?.name === 'AsyncFunction'\n  );\n};\n"]}