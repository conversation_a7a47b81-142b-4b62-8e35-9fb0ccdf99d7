{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,IAAY,SAGX;AAHD,WAAY,SAAS;IACnB,sCAAyB,CAAA;IACzB,gDAAmC,CAAA;AACrC,CAAC,EAHW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAGpB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport enum LayerType {\n  MIDDLEWARE = 'middleware',\n  REQUEST_HANDLER = 'request_handler',\n}\n\nexport interface RestifyRequestInfo {\n  request: any; // Request type from @types/restify package\n  layerType: LayerType;\n}\n\n/**\n * Function that can be used to add custom attributes to the current span\n * @param span - The restify handler span.\n * @param info - The restify request info object.\n */\nexport interface RestifyCustomAttributeFunction {\n  (span: Span, info: RestifyRequestInfo): void;\n}\n\n/**\n * Options available for the restify Instrumentation\n */\nexport interface RestifyInstrumentationConfig extends InstrumentationConfig {\n  /** Function for adding custom attributes to each handler span */\n  requestHook?: RestifyCustomAttributeFunction;\n}\n"]}