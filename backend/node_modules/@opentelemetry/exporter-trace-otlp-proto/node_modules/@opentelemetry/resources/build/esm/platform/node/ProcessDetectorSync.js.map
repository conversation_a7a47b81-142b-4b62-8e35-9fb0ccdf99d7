{"version": 3, "file": "ProcessDetectorSync.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/ProcessDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,0BAA0B,EAAE,MAAM,qCAAqC,CAAC;AACjF,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAI1C,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAEzB;;;GAGG;AACH;IAAA;IA8BA,CAAC;IA7BC,oCAAM,GAAN,UAAO,OAAiC;;QACtC,IAAM,UAAU;YACd,GAAC,0BAA0B,CAAC,WAAW,IAAG,OAAO,CAAC,GAAG;YACrD,GAAC,0BAA0B,CAAC,uBAAuB,IAAG,OAAO,CAAC,KAAK;YACnE,GAAC,0BAA0B,CAAC,uBAAuB,IAAG,OAAO,CAAC,QAAQ;YACtE,GAAC,0BAA0B,CAAC,oBAAoB;gBAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;sBACZ,OAAO,CAAC,QAAQ,kBAChB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SACzB;YACD,GAAC,0BAA0B,CAAC,uBAAuB,IACjD,OAAO,CAAC,QAAQ,CAAC,IAAI;YACvB,GAAC,0BAA0B,CAAC,oBAAoB,IAAG,QAAQ;YAC3D,GAAC,0BAA0B,CAAC,2BAA2B,IAAG,SAAS;eACpE,CAAC;QAEF,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,UAAU,CAAC,0BAA0B,CAAC,eAAe,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAC1E;QAED,IAAI;YACF,IAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC/B,UAAU,CAAC,0BAA0B,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC;SAC1E;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,oCAAkC,CAAG,CAAC,CAAC;SACnD;QAED,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IACH,0BAAC;AAAD,CAAC,AA9BD,IA8BC;AAED,MAAM,CAAC,IAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../types';\nimport { ResourceDetectionConfig } from '../../config';\nimport { IResource } from '../../IResource';\nimport * as os from 'os';\n\n/**\n * ProcessDetectorSync will be used to detect the resources related current process running\n * and being instrumented from the NodeJS Process module.\n */\nclass ProcessDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): IResource {\n    const attributes: ResourceAttributes = {\n      [SemanticResourceAttributes.PROCESS_PID]: process.pid,\n      [SemanticResourceAttributes.PROCESS_EXECUTABLE_NAME]: process.title,\n      [SemanticResourceAttributes.PROCESS_EXECUTABLE_PATH]: process.execPath,\n      [SemanticResourceAttributes.PROCESS_COMMAND_ARGS]: [\n        process.argv[0],\n        ...process.execArgv,\n        ...process.argv.slice(1),\n      ],\n      [SemanticResourceAttributes.PROCESS_RUNTIME_VERSION]:\n        process.versions.node,\n      [SemanticResourceAttributes.PROCESS_RUNTIME_NAME]: 'nodejs',\n      [SemanticResourceAttributes.PROCESS_RUNTIME_DESCRIPTION]: 'Node.js',\n    };\n\n    if (process.argv.length > 1) {\n      attributes[SemanticResourceAttributes.PROCESS_COMMAND] = process.argv[1];\n    }\n\n    try {\n      const userInfo = os.userInfo();\n      attributes[SemanticResourceAttributes.PROCESS_OWNER] = userInfo.username;\n    } catch (e) {\n      diag.debug(`error obtaining process owner: ${e}`);\n    }\n\n    return new Resource(attributes);\n  }\n}\n\nexport const processDetectorSync = new ProcessDetectorSync();\n"]}