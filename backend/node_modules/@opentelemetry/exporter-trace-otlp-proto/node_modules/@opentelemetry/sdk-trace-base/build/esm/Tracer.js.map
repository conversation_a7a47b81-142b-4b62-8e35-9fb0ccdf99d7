{"version": 3, "file": "Tracer.js", "sourceRoot": "", "sources": ["../../src/Tracer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,KAAK,GAAG,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAEL,kBAAkB,EAClB,mBAAmB,GACpB,MAAM,qBAAqB,CAAC;AAG7B,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAE9B,OAAO,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AAIxC,OAAO,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAC;AAE/C;;GAEG;AACH;IAQE;;OAEG;IACH,gBACE,sBAA8C,EAC9C,MAAoB,EACZ,eAAoC;QAApC,oBAAe,GAAf,eAAe,CAAqB;QAE5C,IAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,aAAa,CAAC;QAChD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC;QAC1C,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,WAAW,IAAI,IAAI,iBAAiB,EAAE,CAAC;QAClE,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IACvD,CAAC;IAED;;;OAGG;IACH,0BAAS,GAAT,UACE,IAAY,EACZ,OAA6B,EAC7B,OAA8B;;QAD9B,wBAAA,EAAA,YAA6B;QAC7B,wBAAA,EAAA,UAAU,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE;QAE9B,wEAAwE;QACxE,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SACzC;QACD,IAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,mBAAmB,CAAC,OAAO,CAAC,EAAE;YAChC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;YAClE,IAAM,gBAAgB,GAAG,GAAG,CAAC,KAAK,CAAC,eAAe,CAChD,GAAG,CAAC,oBAAoB,CACzB,CAAC;YACF,OAAO,gBAAgB,CAAC;SACzB;QAED,IAAM,iBAAiB,GAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,WAAW,EAAE,CAAC;QACpD,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;QAClD,IAAI,OAAO,CAAC;QACZ,IAAI,UAAU,CAAC;QACf,IAAI,YAAY,CAAC;QACjB,IACE,CAAC,iBAAiB;YAClB,CAAC,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,EAChD;YACA,iBAAiB;YACjB,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;SAC/C;aAAM;YACL,kBAAkB;YAClB,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC;YACpC,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;YAC1C,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC;SACzC;QAED,IAAM,QAAQ,GAAG,MAAA,OAAO,CAAC,IAAI,mCAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC;QACvD,IAAM,KAAK,GAAG,CAAC,MAAA,OAAO,CAAC,KAAK,mCAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI;YAC1C,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,UAAU,EAAE,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC;aAChD,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,IAAM,UAAU,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC1D,yBAAyB;QACzB,IAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAC/C,OAAO,EACP,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;QAEF,UAAU,GAAG,MAAA,cAAc,CAAC,UAAU,mCAAI,UAAU,CAAC;QAErD,IAAM,UAAU,GACd,cAAc,CAAC,QAAQ,KAAK,GAAG,CAAC,gBAAgB,CAAC,kBAAkB;YACjE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO;YACxB,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;QAC1B,IAAM,WAAW,GAAG,EAAE,OAAO,SAAA,EAAE,MAAM,QAAA,EAAE,UAAU,YAAA,EAAE,UAAU,YAAA,EAAE,CAAC;QAChE,IAAI,cAAc,CAAC,QAAQ,KAAK,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE;YAC/D,GAAG,CAAC,IAAI,CAAC,KAAK,CACZ,+DAA+D,CAChE,CAAC;YACF,IAAM,gBAAgB,GAAG,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAChE,OAAO,gBAAgB,CAAC;SACzB;QAED,IAAM,IAAI,GAAG,IAAI,IAAI,CACnB,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,OAAO,CAAC,SAAS,CAClB,CAAC;QACF,2EAA2E;QAC3E,4EAA4E;QAC5E,IAAM,cAAc,GAAG,kBAAkB,CACvC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,CACrD,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IA4DD,gCAAe,GAAf,UACE,IAAY,EACZ,IAA0B,EAC1B,IAAsB,EACtB,IAAQ;QAER,IAAI,IAAiC,CAAC;QACtC,IAAI,GAA4B,CAAC;QACjC,IAAI,EAAK,CAAC;QAEV,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,OAAO;SACR;aAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,EAAE,GAAG,IAAS,CAAC;SAChB;aAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,IAAI,GAAG,IAAmC,CAAC;YAC3C,EAAE,GAAG,IAAS,CAAC;SAChB;aAAM;YACL,IAAI,GAAG,IAAmC,CAAC;YAC3C,GAAG,GAAG,IAA+B,CAAC;YACtC,EAAE,GAAG,IAAS,CAAC;SAChB;QAED,IAAM,aAAa,GAAG,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QAClD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;QACvD,IAAM,kBAAkB,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAElE,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IACnE,CAAC;IAED,gDAAgD;IAChD,iCAAgB,GAAhB;QACE,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,6CAA6C;IAC7C,8BAAa,GAAb;QACE,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,uCAAsB,GAAtB;QACE,OAAO,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC;IACvD,CAAC;IACH,aAAC;AAAD,CAAC,AA3ND,IA2NC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport {\n  InstrumentationLibrary,\n  sanitizeAttributes,\n  isTracingSuppressed,\n} from '@opentelemetry/core';\nimport { IResource } from '@opentelemetry/resources';\nimport { BasicTracerProvider } from './BasicTracerProvider';\nimport { Span } from './Span';\nimport { GeneralLimits, SpanLimits, TracerConfig } from './types';\nimport { mergeConfig } from './utility';\nimport { SpanProcessor } from './SpanProcessor';\nimport { Sampler } from './Sampler';\nimport { IdGenerator } from './IdGenerator';\nimport { RandomIdGenerator } from './platform';\n\n/**\n * This class represents a basic tracer.\n */\nexport class Tracer implements api.Tracer {\n  private readonly _sampler: Sampler;\n  private readonly _generalLimits: GeneralLimits;\n  private readonly _spanLimits: SpanLimits;\n  private readonly _idGenerator: IdGenerator;\n  readonly resource: IResource;\n  readonly instrumentationLibrary: InstrumentationLibrary;\n\n  /**\n   * Constructs a new Tracer instance.\n   */\n  constructor(\n    instrumentationLibrary: InstrumentationLibrary,\n    config: TracerConfig,\n    private _tracerProvider: BasicTracerProvider\n  ) {\n    const localConfig = mergeConfig(config);\n    this._sampler = localConfig.sampler;\n    this._generalLimits = localConfig.generalLimits;\n    this._spanLimits = localConfig.spanLimits;\n    this._idGenerator = config.idGenerator || new RandomIdGenerator();\n    this.resource = _tracerProvider.resource;\n    this.instrumentationLibrary = instrumentationLibrary;\n  }\n\n  /**\n   * Starts a new Span or returns the default NoopSpan based on the sampling\n   * decision.\n   */\n  startSpan(\n    name: string,\n    options: api.SpanOptions = {},\n    context = api.context.active()\n  ): api.Span {\n    // remove span from context in case a root span is requested via options\n    if (options.root) {\n      context = api.trace.deleteSpan(context);\n    }\n    const parentSpan = api.trace.getSpan(context);\n\n    if (isTracingSuppressed(context)) {\n      api.diag.debug('Instrumentation suppressed, returning Noop Span');\n      const nonRecordingSpan = api.trace.wrapSpanContext(\n        api.INVALID_SPAN_CONTEXT\n      );\n      return nonRecordingSpan;\n    }\n\n    const parentSpanContext = parentSpan?.spanContext();\n    const spanId = this._idGenerator.generateSpanId();\n    let traceId;\n    let traceState;\n    let parentSpanId;\n    if (\n      !parentSpanContext ||\n      !api.trace.isSpanContextValid(parentSpanContext)\n    ) {\n      // New root span.\n      traceId = this._idGenerator.generateTraceId();\n    } else {\n      // New child span.\n      traceId = parentSpanContext.traceId;\n      traceState = parentSpanContext.traceState;\n      parentSpanId = parentSpanContext.spanId;\n    }\n\n    const spanKind = options.kind ?? api.SpanKind.INTERNAL;\n    const links = (options.links ?? []).map(link => {\n      return {\n        context: link.context,\n        attributes: sanitizeAttributes(link.attributes),\n      };\n    });\n    const attributes = sanitizeAttributes(options.attributes);\n    // make sampling decision\n    const samplingResult = this._sampler.shouldSample(\n      context,\n      traceId,\n      name,\n      spanKind,\n      attributes,\n      links\n    );\n\n    traceState = samplingResult.traceState ?? traceState;\n\n    const traceFlags =\n      samplingResult.decision === api.SamplingDecision.RECORD_AND_SAMPLED\n        ? api.TraceFlags.SAMPLED\n        : api.TraceFlags.NONE;\n    const spanContext = { traceId, spanId, traceFlags, traceState };\n    if (samplingResult.decision === api.SamplingDecision.NOT_RECORD) {\n      api.diag.debug(\n        'Recording is off, propagating context in a non-recording span'\n      );\n      const nonRecordingSpan = api.trace.wrapSpanContext(spanContext);\n      return nonRecordingSpan;\n    }\n\n    const span = new Span(\n      this,\n      context,\n      name,\n      spanContext,\n      spanKind,\n      parentSpanId,\n      links,\n      options.startTime\n    );\n    // Set initial span attributes. The attributes object may have been mutated\n    // by the sampler, so we sanitize the merged attributes before setting them.\n    const initAttributes = sanitizeAttributes(\n      Object.assign(attributes, samplingResult.attributes)\n    );\n    span.setAttributes(initAttributes);\n    return span;\n  }\n\n  /**\n   * Starts a new {@link Span} and calls the given function passing it the\n   * created span as first argument.\n   * Additionally the new span gets set in context and this context is activated\n   * for the duration of the function call.\n   *\n   * @param name The name of the span\n   * @param [options] SpanOptions used for span creation\n   * @param [context] Context to use to extract parent\n   * @param fn function called in the context of the span and receives the newly created span as an argument\n   * @returns return value of fn\n   * @example\n   *   const something = tracer.startActiveSpan('op', span => {\n   *     try {\n   *       do some work\n   *       span.setStatus({code: SpanStatusCode.OK});\n   *       return something;\n   *     } catch (err) {\n   *       span.setStatus({\n   *         code: SpanStatusCode.ERROR,\n   *         message: err.message,\n   *       });\n   *       throw err;\n   *     } finally {\n   *       span.end();\n   *     }\n   *   });\n   * @example\n   *   const span = tracer.startActiveSpan('op', span => {\n   *     try {\n   *       do some work\n   *       return span;\n   *     } catch (err) {\n   *       span.setStatus({\n   *         code: SpanStatusCode.ERROR,\n   *         message: err.message,\n   *       });\n   *       throw err;\n   *     }\n   *   });\n   *   do some more work\n   *   span.end();\n   */\n  startActiveSpan<F extends (span: api.Span) => ReturnType<F>>(\n    name: string,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: api.Span) => ReturnType<F>>(\n    name: string,\n    opts: api.SpanOptions,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: api.Span) => ReturnType<F>>(\n    name: string,\n    opts: api.SpanOptions,\n    ctx: api.Context,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: api.Span) => ReturnType<F>>(\n    name: string,\n    arg2?: F | api.SpanOptions,\n    arg3?: F | api.Context,\n    arg4?: F\n  ): ReturnType<F> | undefined {\n    let opts: api.SpanOptions | undefined;\n    let ctx: api.Context | undefined;\n    let fn: F;\n\n    if (arguments.length < 2) {\n      return;\n    } else if (arguments.length === 2) {\n      fn = arg2 as F;\n    } else if (arguments.length === 3) {\n      opts = arg2 as api.SpanOptions | undefined;\n      fn = arg3 as F;\n    } else {\n      opts = arg2 as api.SpanOptions | undefined;\n      ctx = arg3 as api.Context | undefined;\n      fn = arg4 as F;\n    }\n\n    const parentContext = ctx ?? api.context.active();\n    const span = this.startSpan(name, opts, parentContext);\n    const contextWithSpanSet = api.trace.setSpan(parentContext, span);\n\n    return api.context.with(contextWithSpanSet, fn, undefined, span);\n  }\n\n  /** Returns the active {@link GeneralLimits}. */\n  getGeneralLimits(): GeneralLimits {\n    return this._generalLimits;\n  }\n\n  /** Returns the active {@link SpanLimits}. */\n  getSpanLimits(): SpanLimits {\n    return this._spanLimits;\n  }\n\n  getActiveSpanProcessor(): SpanProcessor {\n    return this._tracerProvider.getActiveSpanProcessor();\n  }\n}\n"]}