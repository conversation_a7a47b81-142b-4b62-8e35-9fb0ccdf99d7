{"version": 3, "file": "utility.js", "sourceRoot": "", "sources": ["../../src/utility.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,qCAAkE;AAGlE,8CAI6B;AAE7B;;;GAGG;AACH,SAAgB,WAAW,CAAC,UAAwB;IAKlD,MAAM,mBAAmB,GAA0B;QACjD,OAAO,EAAE,IAAA,4BAAmB,GAAE;KAC/B,CAAC;IAEF,MAAM,cAAc,GAAG,IAAA,0BAAiB,GAAE,CAAC;IAE3C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAC1B,EAAE,EACF,cAAc,EACd,mBAAmB,EACnB,UAAU,CACX,CAAC;IAEF,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAClC,EAAE,EACF,cAAc,CAAC,aAAa,EAC5B,UAAU,CAAC,aAAa,IAAI,EAAE,CAC/B,CAAC;IAEF,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAC/B,EAAE,EACF,cAAc,CAAC,UAAU,EACzB,UAAU,CAAC,UAAU,IAAI,EAAE,CAC5B,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AA/BD,kCA+BC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,UAAwB;;IACxD,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;IAE5D,MAAM,eAAe,GAAG,IAAA,4BAAqB,GAAE,CAAC;IAEhD;;OAEG;IACH,UAAU,CAAC,mBAAmB;QAC5B,MAAA,MAAA,MAAA,MAAA,MAAA,UAAU,CAAC,UAAU,0CAAE,mBAAmB,mCAC1C,MAAA,UAAU,CAAC,aAAa,0CAAE,mBAAmB,mCAC7C,eAAe,CAAC,+BAA+B,mCAC/C,eAAe,CAAC,0BAA0B,mCAC1C,oCAA6B,CAAC;IAEhC;;OAEG;IACH,UAAU,CAAC,yBAAyB;QAClC,MAAA,MAAA,MAAA,MAAA,MAAA,UAAU,CAAC,UAAU,0CAAE,yBAAyB,mCAChD,MAAA,UAAU,CAAC,aAAa,0CAAE,yBAAyB,mCACnD,eAAe,CAAC,sCAAsC,mCACtD,eAAe,CAAC,iCAAiC,mCACjD,2CAAoC,CAAC;IAEvC,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;AACvD,CAAC;AA1BD,8CA0BC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { buildSamplerFromEnv, loadDefaultConfig } from './config';\nimport { Sampler } from './Sampler';\nimport { SpanLimits, TracerConfig, GeneralLimits } from './types';\nimport {\n  DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  getEnvWithoutDefaults,\n} from '@opentelemetry/core';\n\n/**\n * Function to merge Default configuration (as specified in './config') with\n * user provided configurations.\n */\nexport function mergeConfig(userConfig: TracerConfig): TracerConfig & {\n  sampler: Sampler;\n  spanLimits: SpanLimits;\n  generalLimits: GeneralLimits;\n} {\n  const perInstanceDefaults: Partial<TracerConfig> = {\n    sampler: buildSamplerFromEnv(),\n  };\n\n  const DEFAULT_CONFIG = loadDefaultConfig();\n\n  const target = Object.assign(\n    {},\n    DEFAULT_CONFIG,\n    perInstanceDefaults,\n    userConfig\n  );\n\n  target.generalLimits = Object.assign(\n    {},\n    DEFAULT_CONFIG.generalLimits,\n    userConfig.generalLimits || {}\n  );\n\n  target.spanLimits = Object.assign(\n    {},\n    DEFAULT_CONFIG.spanLimits,\n    userConfig.spanLimits || {}\n  );\n\n  return target;\n}\n\n/**\n * When general limits are provided and model specific limits are not,\n * configures the model specific limits by using the values from the general ones.\n * @param userConfig User provided tracer configuration\n */\nexport function reconfigureLimits(userConfig: TracerConfig): TracerConfig {\n  const spanLimits = Object.assign({}, userConfig.spanLimits);\n\n  const parsedEnvConfig = getEnvWithoutDefaults();\n\n  /**\n   * Reassign span attribute count limit to use first non null value defined by user or use default value\n   */\n  spanLimits.attributeCountLimit =\n    userConfig.spanLimits?.attributeCountLimit ??\n    userConfig.generalLimits?.attributeCountLimit ??\n    parsedEnvConfig.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT ??\n    parsedEnvConfig.OTEL_ATTRIBUTE_COUNT_LIMIT ??\n    DEFAULT_ATTRIBUTE_COUNT_LIMIT;\n\n  /**\n   * Reassign span attribute value length limit to use first non null value defined by user or use default value\n   */\n  spanLimits.attributeValueLengthLimit =\n    userConfig.spanLimits?.attributeValueLengthLimit ??\n    userConfig.generalLimits?.attributeValueLengthLimit ??\n    parsedEnvConfig.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT ??\n    parsedEnvConfig.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT ??\n    DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT;\n\n  return Object.assign({}, userConfig, { spanLimits });\n}\n"]}