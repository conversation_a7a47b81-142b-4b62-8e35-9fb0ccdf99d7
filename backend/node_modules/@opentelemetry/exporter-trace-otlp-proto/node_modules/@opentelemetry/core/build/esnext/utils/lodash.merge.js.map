{"version": 3, "file": "lodash.merge.js", "sourceRoot": "", "sources": ["../../../src/utils/lodash.merge.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,uDAAuD;AAEvD;;;IAGI;AAEJ,MAAM,SAAS,GAAG,iBAAiB,CAAC;AACpC,MAAM,OAAO,GAAG,eAAe,CAAC;AAChC,MAAM,YAAY,GAAG,oBAAoB,CAAC;AAC1C,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;AACrC,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC;AACxC,MAAM,gBAAgB,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACnD,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;AAC5D,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;AACrC,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AAClD,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;AAC/D,MAAM,oBAAoB,GAAG,WAAW,CAAC,QAAQ,CAAC;AAElD;;;;;;;GAOG;AACH,SAAS,OAAO,CAAC,IAAc,EAAE,SAAc;IAC7C,OAAO,UAAU,GAAQ;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAM,UAAU,aAAa,CAAC,KAAU;IACtC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;QAC3D,OAAO,KAAK,CAAC;KACd;IACD,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IAClC,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,IAAI,CAAC;KACb;IACD,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC;IAC5E,OAAO,CACL,OAAO,IAAI,IAAI,UAAU;QACzB,IAAI,YAAY,IAAI;QACpB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,gBAAgB,CAC7C,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,SAAS,YAAY,CAAC,KAAU;IAC9B,OAAO,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,IAAI,QAAQ,CAAC;AACnD,CAAC;AAED;;;;;;GAMG;AACH,SAAS,UAAU,CAAC,KAAU;IAC5B,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC;KACrD;IACD,OAAO,cAAc,IAAI,cAAc,IAAI,MAAM,CAAC,KAAK,CAAC;QACtD,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC;QAClB,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC;AAED;;;;;;GAMG;AACH,SAAS,SAAS,CAAC,KAAU;IAC3B,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,cAAqB,CAAC,EAC7D,GAAG,GAAG,KAAK,CAAC,cAAqB,CAAC,CAAC;IACrC,IAAI,QAAQ,GAAG,KAAK,CAAC;IAErB,IAAI;QACF,KAAK,CAAC,cAAqB,CAAC,GAAG,SAAS,CAAC;QACzC,QAAQ,GAAG,IAAI,CAAC;KACjB;IAAC,OAAO,CAAC,EAAE;QACV,UAAU;KACX;IAED,MAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,IAAI,QAAQ,EAAE;QACZ,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,cAAqB,CAAC,GAAG,GAAG,CAAC;SACpC;aAAM;YACL,OAAO,KAAK,CAAC,cAAqB,CAAC,CAAC;SACrC;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,SAAS,cAAc,CAAC,KAAU;IAChC,OAAO,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1C,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n/**\n * based on lodash in order to support esm builds without esModuleInterop.\n * lodash is using MIT License.\n **/\n\nconst objectTag = '[object Object]';\nconst nullTag = '[object Null]';\nconst undefinedTag = '[object Undefined]';\nconst funcProto = Function.prototype;\nconst funcToString = funcProto.toString;\nconst objectCtorString = funcToString.call(Object);\nconst getPrototype = overArg(Object.getPrototypeOf, Object);\nconst objectProto = Object.prototype;\nconst hasOwnProperty = objectProto.hasOwnProperty;\nconst symToStringTag = Symbol ? Symbol.toStringTag : undefined;\nconst nativeObjectToString = objectProto.toString;\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func: Function, transform: any): any {\n  return function (arg: any) {\n    return func(transform(arg));\n  };\n}\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nexport function isPlainObject(value: any) {\n  if (!isObjectLike(value) || baseGetTag(value) !== objectTag) {\n    return false;\n  }\n  const proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  const Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return (\n    typeof Ctor == 'function' &&\n    Ctor instanceof Ctor &&\n    funcToString.call(Ctor) === objectCtorString\n  );\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value: any) {\n  return value != null && typeof value == 'object';\n}\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value: any) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return symToStringTag && symToStringTag in Object(value)\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value: any) {\n  const isOwn = hasOwnProperty.call(value, symToStringTag as any),\n    tag = value[symToStringTag as any];\n  let unmasked = false;\n\n  try {\n    value[symToStringTag as any] = undefined;\n    unmasked = true;\n  } catch (e) {\n    // silence\n  }\n\n  const result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag as any] = tag;\n    } else {\n      delete value[symToStringTag as any];\n    }\n  }\n  return result;\n}\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value: any) {\n  return nativeObjectToString.call(value);\n}\n"]}