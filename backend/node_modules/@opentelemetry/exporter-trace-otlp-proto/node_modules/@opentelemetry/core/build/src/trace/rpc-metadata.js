"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRPCMetadata = exports.deleteRPCMetadata = exports.setRPCMetadata = exports.RPCType = void 0;
const api_1 = require("@opentelemetry/api");
const RPC_METADATA_KEY = (0, api_1.createContextKey)('OpenTelemetry SDK Context Key RPC_METADATA');
var RPCType;
(function (RPCType) {
    RPCType["HTTP"] = "http";
})(RPCType = exports.RPCType || (exports.RPCType = {}));
function setRPCMetadata(context, meta) {
    return context.setValue(RPC_METADATA_KEY, meta);
}
exports.setRPCMetadata = setRPCMetadata;
function deleteRPCMetadata(context) {
    return context.deleteValue(RPC_METADATA_KEY);
}
exports.deleteRPCMetadata = deleteRPCMetadata;
function getRPCMetadata(context) {
    return context.getValue(RPC_METADATA_KEY);
}
exports.getRPCMetadata = getRPCMetadata;
//# sourceMappingURL=rpc-metadata.js.map