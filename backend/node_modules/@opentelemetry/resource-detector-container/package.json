{"name": "@opentelemetry/resource-detector-container", "version": "0.3.11", "description": "Opentelemetry resource detector to get container resource attributes", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "precompile": "tsc --version && lerna run version:update --scope @opentelemetry/resource-detector-container --include-dependencies", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "tdd": "npm run test -- --watch-extensions ts --watch", "version:update": "node ../../../scripts/version-update.js", "watch": "tsc -w"}, "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": "^1.0.0", "@opentelemetry/contrib-test-utils": "^0.40.0", "@types/mocha": "8.2.3", "@types/node": "18.6.5", "@types/sinon": "10.0.18", "eslint-plugin-header": "^3.1.1", "mocha": "7.2.0", "nock": "13.3.3", "nyc": "15.1.0", "rimraf": "5.0.5", "sinon": "15.2.0", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}, "dependencies": {"@opentelemetry/resources": "^1.0.0", "@opentelemetry/semantic-conventions": "^1.22.0"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/detectors/node/opentelemetry-resource-detector-container#readme", "gitHead": "93e7aab9a38e22c9ef6c0a9053f817e7e52a687c"}