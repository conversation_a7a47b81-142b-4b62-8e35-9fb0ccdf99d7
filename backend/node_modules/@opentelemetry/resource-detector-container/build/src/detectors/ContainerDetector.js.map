{"version": 3, "file": "ContainerDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/ContainerDetector.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,wDAIkC;AAElC,8EAA+E;AAE/E,yBAAyB;AACzB,6BAA6B;AAC7B,4CAA0C;AAE1C,MAAa,iBAAiB;IAA9B;QACW,wBAAmB,GAAG,EAAE,CAAC;QACzB,2BAAsB,GAAG,mBAAmB,CAAC;QAC7C,2BAAsB,GAAG,sBAAsB,CAAC;QAChD,iBAAY,GAAG,MAAM,CAAC;QACtB,aAAQ,GAAG,UAAU,CAAC;IA2FjC,CAAC;IAvFC,KAAK,CAAC,MAAM,CAAC,OAAiC;QAC5C,IAAI;YACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YACjD,OAAO,CAAC,WAAW;gBACjB,CAAC,CAAC,oBAAQ,CAAC,KAAK,EAAE;gBAClB,CAAC,CAAC,IAAI,oBAAQ,CAAC;oBACX,CAAC,+CAAwB,CAAC,EAAE,WAAW;iBACxC,CAAC,CAAC;SACR;QAAC,OAAO,CAAC,EAAE;YACV,UAAI,CAAC,IAAI,CACP,+HAA+H,EAC/H,CAAC,CACF,CAAC;YACF,OAAO,oBAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,aAAa,CACnD,IAAI,CAAC,sBAAsB,EAC3B,IAAI,CAAC,YAAY,CAClB,CAAC;QACF,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7C,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;YAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;gBACvB,SAAS;aACV;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC9C,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;gBACnB,uGAAuG;gBACvG,gGAAgG;gBAChG,OAAO,WAAW,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;aAC5C;iBAAM;gBACL,IAAI,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAC5C,IAAI,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAE1C,QAAQ,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;gBAC9C,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;oBACjB,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;iBAC7B;gBACD,IAAI,QAAQ,GAAG,MAAM,EAAE;oBACrB,SAAS;iBACV;gBACD,OAAO,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;aAChD;SACF;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,aAAa,CACnD,IAAI,CAAC,sBAAsB,EAC3B,IAAI,CAAC,YAAY,CAClB,CAAC;QACF,MAAM,GAAG,GAAG,OAAO;aAChB,IAAI,EAAE;aACN,KAAK,CAAC,IAAI,CAAC;aACX,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxC,MAAM,cAAc,GAAG,GAAG,aAAH,GAAG,uBAAH,GAAG,CACtB,KAAK,CAAC,GAAG,EACV,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpD,OAAO,cAAc,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;;;MAIE;IACM,KAAK,CAAC,eAAe;QAC3B,IAAI;YACF,OAAO,CACL,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC,CACrE,CAAC;SACH;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,YAAY,KAAK,EAAE;gBACtB,MAAM,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC;gBAC/B,UAAI,CAAC,IAAI,CACP,sDAAsD,EACtD,YAAY,CACb,CAAC;aACH;SACF;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;;AA/FH,8CAgGC;AAzFgB,+BAAa,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;AA2FhD,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  Detector,\n  Resource,\n  ResourceDetectionConfig,\n} from '@opentelemetry/resources';\n\nimport { SEMRESATTRS_CONTAINER_ID } from '@opentelemetry/semantic-conventions';\n\nimport * as fs from 'fs';\nimport * as util from 'util';\nimport { diag } from '@opentelemetry/api';\n\nexport class ContainerDetector implements Detector {\n  readonly CONTAINER_ID_LENGTH = 64;\n  readonly DEFAULT_CGROUP_V1_PATH = '/proc/self/cgroup';\n  readonly DEFAULT_CGROUP_V2_PATH = '/proc/self/mountinfo';\n  readonly UTF8_UNICODE = 'utf8';\n  readonly HOSTNAME = 'hostname';\n\n  private static readFileAsync = util.promisify(fs.readFile);\n\n  async detect(_config?: ResourceDetectionConfig): Promise<Resource> {\n    try {\n      const containerId = await this._getContainerId();\n      return !containerId\n        ? Resource.empty()\n        : new Resource({\n            [SEMRESATTRS_CONTAINER_ID]: containerId,\n          });\n    } catch (e) {\n      diag.info(\n        'Container Detector did not identify running inside a supported container, no container attributes will be added to resource: ',\n        e\n      );\n      return Resource.empty();\n    }\n  }\n\n  private async _getContainerIdV1() {\n    const rawData = await ContainerDetector.readFileAsync(\n      this.DEFAULT_CGROUP_V1_PATH,\n      this.UTF8_UNICODE\n    );\n    const splitData = rawData.trim().split('\\n');\n    for (const line of splitData) {\n      const lastSlashIdx = line.lastIndexOf('/');\n      if (lastSlashIdx === -1) {\n        continue;\n      }\n      const lastSection = line.substring(lastSlashIdx + 1);\n      const colonIdx = lastSection.lastIndexOf(':');\n      if (colonIdx !== -1) {\n        // since containerd v1.5.0+, containerId is divided by the last colon when the cgroupDriver is systemd:\n        // https://github.com/containerd/containerd/blob/release/1.5/pkg/cri/server/helpers_linux.go#L64\n        return lastSection.substring(colonIdx + 1);\n      } else {\n        let startIdx = lastSection.lastIndexOf('-');\n        let endIdx = lastSection.lastIndexOf('.');\n\n        startIdx = startIdx === -1 ? 0 : startIdx + 1;\n        if (endIdx === -1) {\n          endIdx = lastSection.length;\n        }\n        if (startIdx > endIdx) {\n          continue;\n        }\n        return lastSection.substring(startIdx, endIdx);\n      }\n    }\n    return undefined;\n  }\n\n  private async _getContainerIdV2() {\n    const rawData = await ContainerDetector.readFileAsync(\n      this.DEFAULT_CGROUP_V2_PATH,\n      this.UTF8_UNICODE\n    );\n    const str = rawData\n      .trim()\n      .split('\\n')\n      .find(s => s.includes(this.HOSTNAME));\n    const containerIdStr = str\n      ?.split('/')\n      .find(s => s.length === this.CONTAINER_ID_LENGTH);\n    return containerIdStr || '';\n  }\n\n  /*\n    cgroupv1 path would still exist in case of container running on v2\n    but the cgroupv1 path would no longer have the container id and would\n    fallback on the cgroupv2 implementation.\n  */\n  private async _getContainerId(): Promise<string | undefined> {\n    try {\n      return (\n        (await this._getContainerIdV1()) || (await this._getContainerIdV2())\n      );\n    } catch (e) {\n      if (e instanceof Error) {\n        const errorMessage = e.message;\n        diag.info(\n          'Container Detector failed to read the Container ID: ',\n          errorMessage\n        );\n      }\n    }\n    return undefined;\n  }\n}\n\nexport const containerDetector = new ContainerDetector();\n"]}