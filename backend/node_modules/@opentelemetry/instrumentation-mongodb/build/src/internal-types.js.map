{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAqJH,IAAY,kBAMX;AAND,WAAY,kBAAkB;IAC5B,sDAAgC,CAAA;IAChC,uDAAiC,CAAA;IACjC,4CAAsB,CAAA;IACtB,qCAAe,CAAA;IACf,yCAAmB,CAAA;AACrB,CAAC,EANW,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAM7B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { Span } from '@opentelemetry/api';\n\nexport interface MongoDBInstrumentationExecutionResponseHook {\n  (span: Span, responseInfo: MongoResponseHookInformation): void;\n}\n\n/**\n * Function that can be used to serialize db.statement tag\n * @param cmd - MongoDB command object\n *\n * @returns serialized string that will be used as the db.statement attribute.\n */\nexport type DbStatementSerializer = (cmd: Record<string, unknown>) => string;\n\nexport interface MongoDBInstrumentationConfig extends InstrumentationConfig {\n  /**\n   * If true, additional information about query parameters and\n   * results will be attached (as `attributes`) to spans representing\n   * database operations.\n   */\n  enhancedDatabaseReporting?: boolean;\n\n  /**\n   * Hook that allows adding custom span attributes based on the data\n   * returned from MongoDB actions.\n   *\n   * @default undefined\n   */\n  responseHook?: MongoDBInstrumentationExecutionResponseHook;\n\n  /**\n   * Custom serializer function for the db.statement tag\n   */\n  dbStatementSerializer?: DbStatementSerializer;\n}\n\nexport type Func<T> = (...args: unknown[]) => T;\nexport type MongoInternalCommand = {\n  findandmodify: boolean;\n  createIndexes: boolean;\n  count: boolean;\n  ismaster: boolean;\n  indexes?: unknown[];\n  query?: Record<string, unknown>;\n  limit?: number;\n  q?: Record<string, unknown>;\n  u?: Record<string, unknown>;\n};\n\nexport type ServerSession = {\n  id: any;\n  lastUse: number;\n  txnNumber: number;\n  isDirty: boolean;\n};\n\nexport type CursorState = { cmd: MongoInternalCommand } & Record<\n  string,\n  unknown\n>;\n\nexport interface MongoResponseHookInformation {\n  data: CommandResult;\n}\n\n// https://github.com/mongodb/node-mongodb-native/blob/3.6/lib/core/connection/command_result.js\nexport type CommandResult = {\n  result?: unknown;\n  connection?: unknown;\n  message?: unknown;\n};\n\n// https://github.com/mongodb/node-mongodb-native/blob/3.6/lib/core/wireprotocol/index.js\nexport type WireProtocolInternal = {\n  insert: (\n    server: MongoInternalTopology,\n    ns: string,\n    ops: unknown[],\n    options: unknown | Function,\n    callback?: Function\n  ) => unknown;\n  update: (\n    server: MongoInternalTopology,\n    ns: string,\n    ops: unknown[],\n    options: unknown | Function,\n    callback?: Function\n  ) => unknown;\n  remove: (\n    server: MongoInternalTopology,\n    ns: string,\n    ops: unknown[],\n    options: unknown | Function,\n    callback?: Function\n  ) => unknown;\n  killCursors: (\n    server: MongoInternalTopology,\n    ns: string,\n    cursorState: CursorState,\n    callback: Function\n  ) => unknown;\n  getMore: (\n    server: MongoInternalTopology,\n    ns: string,\n    cursorState: CursorState,\n    batchSize: number,\n    options: unknown | Function,\n    callback?: Function\n  ) => unknown;\n  query: (\n    server: MongoInternalTopology,\n    ns: string,\n    cmd: MongoInternalCommand,\n    cursorState: CursorState,\n    options: unknown | Function,\n    callback?: Function\n  ) => unknown;\n  command: (\n    server: MongoInternalTopology,\n    ns: string,\n    cmd: MongoInternalCommand,\n    options: unknown | Function,\n    callback?: Function\n  ) => unknown;\n};\n\n// https://github.com/mongodb/node-mongodb-native/blob/3.6/lib/topologies/server.js#L172\n// https://github.com/mongodb/node-mongodb-native/blob/2.2/lib/server.js#L174\nexport type MongoInternalTopology = {\n  s?: {\n    // those are for mongodb@3\n    options?: {\n      host?: string;\n      port?: number;\n      servername?: string;\n    };\n    // those are for mongodb@2\n    host?: string;\n    port?: number;\n  };\n  // mongodb@3 with useUnifiedTopology option\n  description?: {\n    address?: string;\n  };\n};\n\nexport enum MongodbCommandType {\n  CREATE_INDEXES = 'createIndexes',\n  FIND_AND_MODIFY = 'findAndModify',\n  IS_MASTER = 'isMaster',\n  COUNT = 'count',\n  UNKNOWN = 'unknown',\n}\n\n// https://github.com/mongodb/js-bson/blob/main/src/bson.ts\nexport type Document = {\n  [key: string]: any;\n};\n\n// https://github.com/mongodb/node-mongodb-native/blob/v4.2.2/src/cmap/connection.ts\nexport type V4Connection = {\n  command(\n    ns: any,\n    cmd: Document,\n    options: undefined | unknown,\n    callback: any\n  ): void;\n};\n\n// https://github.com/mongodb/node-mongodb-native/blob/v4.2.2/src/cmap/connection_pool.ts\nexport type V4ConnectionPool = {\n  // Instrumentation just cares about carrying the async context so\n  // types of callback params are not needed\n  checkOut: (callback: (error: any, connection: any) => void) => void;\n};\n\n// https://github.com/mongodb/node-mongodb-native/blob/v4.2.2/src/cmap/connect.ts\nexport type V4Connect = {\n  connect: (options: any, callback: any) => void;\n};\n\n// https://github.com/mongodb/node-mongodb-native/blob/v4.2.2/src/sessions.ts\nexport type V4Session = {\n  acquire: () => ServerSession;\n  release: (session: ServerSession) => void;\n};\n"]}