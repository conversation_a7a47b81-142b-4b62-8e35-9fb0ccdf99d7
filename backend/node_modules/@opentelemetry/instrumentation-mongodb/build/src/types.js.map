{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAkDH,IAAY,kBAMX;AAND,WAAY,kBAAkB;IAC5B,sDAAgC,CAAA;IAChC,uDAAiC,CAAA;IACjC,4CAAsB,CAAA;IACtB,qCAAe,CAAA;IACf,yCAAmB,CAAA;AACrB,CAAC,EANW,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAM7B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { Span } from '@opentelemetry/api';\n\nexport interface MongoDBInstrumentationExecutionResponseHook {\n  (span: Span, responseInfo: MongoResponseHookInformation): void;\n}\n\n/**\n * Function that can be used to serialize db.statement tag\n * @param cmd - MongoDB command object\n *\n * @returns serialized string that will be used as the db.statement attribute.\n */\nexport type DbStatementSerializer = (cmd: Record<string, unknown>) => string;\n\nexport interface MongoDBInstrumentationConfig extends InstrumentationConfig {\n  /**\n   * If true, additional information about query parameters and\n   * results will be attached (as `attributes`) to spans representing\n   * database operations.\n   */\n  enhancedDatabaseReporting?: boolean;\n\n  /**\n   * Hook that allows adding custom span attributes based on the data\n   * returned from MongoDB actions.\n   *\n   * @default undefined\n   */\n  responseHook?: MongoDBInstrumentationExecutionResponseHook;\n\n  /**\n   * Custom serializer function for the db.statement tag\n   */\n  dbStatementSerializer?: DbStatementSerializer;\n}\n\nexport interface MongoResponseHookInformation {\n  data: CommandResult;\n}\n\n// https://github.com/mongodb/node-mongodb-native/blob/3.6/lib/core/connection/command_result.js\nexport type CommandResult = {\n  result?: unknown;\n  connection?: unknown;\n  message?: unknown;\n};\n\nexport enum MongodbCommandType {\n  CREATE_INDEXES = 'createIndexes',\n  FIND_AND_MODIFY = 'findAndModify',\n  IS_MASTER = 'isMaster',\n  COUNT = 'count',\n  UNKNOWN = 'unknown',\n}\n"]}