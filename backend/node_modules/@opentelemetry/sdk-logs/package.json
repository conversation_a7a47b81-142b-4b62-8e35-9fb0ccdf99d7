{"name": "@opentelemetry/sdk-logs", "version": "0.45.1", "publishConfig": {"access": "public"}, "description": "OpenTelemetry logs SDK", "author": "OpenTelemetry Authors", "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/sdk-logs", "license": "Apache-2.0", "main": "build/src/index.js", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "browser": {"./src/platform/index.ts": "./src/platform/browser/index.ts", "./build/esm/platform/index.js": "./build/esm/platform/browser/index.js", "./build/esnext/platform/index.js": "./build/esnext/platform/browser/index.js", "./build/src/platform/index.js": "./build/src/platform/browser/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/open-telemetry/opentelemetry-js.git"}, "bugs": {"url": "https://github.com/open-telemetry/opentelemetry-js/issues"}, "engines": {"node": ">=14"}, "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "test:browser": "karma start --single-run", "tdd": "npm run test -- --watch-extensions ts --watch", "tdd:browser": "karma start", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "node ../../../scripts/version-update.js", "peer-api-check": "node ../../../scripts/peer-api-check.js"}, "keywords": ["opentelemetry", "nodejs", "logs", "stats", "profiling"], "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "sideEffects": false, "peerDependencies": {"@opentelemetry/api": ">=1.4.0 <1.8.0", "@opentelemetry/api-logs": ">=0.39.1"}, "devDependencies": {"@babel/core": "7.22.20", "@opentelemetry/api": ">=1.4.0 <1.8.0", "@opentelemetry/api-logs": "0.45.1", "@types/mocha": "10.0.3", "@types/node": "18.6.5", "@types/sinon": "10.0.20", "babel-plugin-istanbul": "6.1.1", "codecov": "3.8.3", "cross-var": "1.1.0", "karma": "6.4.2", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-spec-reporter": "0.0.36", "karma-webpack": "4.0.2", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "sinon": "15.1.2", "ts-loader": "8.4.0", "ts-mocha": "10.0.0", "typescript": "4.4.4", "webpack": "4.46.0", "webpack-cli": "4.10.0", "webpack-merge": "5.9.0"}, "dependencies": {"@opentelemetry/core": "1.18.1", "@opentelemetry/resources": "1.18.1"}, "gitHead": "f665499096189390e691cf1a772e677fa67812d7"}