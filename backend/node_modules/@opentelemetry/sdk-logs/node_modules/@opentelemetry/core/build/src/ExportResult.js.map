{"version": 3, "file": "ExportResult.js", "sourceRoot": "", "sources": ["../../src/ExportResult.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAOH,IAAY,gBAGX;AAHD,WAAY,gBAAgB;IAC1B,6DAAO,CAAA;IACP,2DAAM,CAAA;AACR,CAAC,EAHW,gBAAgB,GAAhB,wBAAgB,KAAhB,wBAAgB,QAG3B", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface ExportResult {\n  code: ExportResultCode;\n  error?: Error;\n}\n\nexport enum ExportResultCode {\n  SUCCESS,\n  FAILED,\n}\n"]}