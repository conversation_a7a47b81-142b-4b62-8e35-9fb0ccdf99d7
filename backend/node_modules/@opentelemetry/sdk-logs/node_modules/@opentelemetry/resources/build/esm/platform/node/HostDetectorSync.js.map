{"version": 3, "file": "HostDetectorSync.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/HostDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,0BAA0B,EAAE,MAAM,qCAAqC,CAAC;AACjF,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAG1C,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAEzD;;;GAGG;AACH;IAAA;IAmBA,CAAC;IAlBC,iCAAM,GAAN,UAAO,OAAiC;;QACtC,IAAM,UAAU;YACd,GAAC,0BAA0B,CAAC,SAAS,IAAG,QAAQ,EAAE;YAClD,GAAC,0BAA0B,CAAC,SAAS,IAAG,aAAa,CAAC,IAAI,EAAE,CAAC;eAC9D,CAAC;QAEF,OAAO,IAAI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEO,8CAAmB,GAA3B;QACE,OAAO,YAAY,EAAE,CAAC,IAAI,CAAC,UAAA,SAAS;YAClC,IAAM,UAAU,GAAuB,EAAE,CAAC;YAC1C,IAAI,SAAS,EAAE;gBACb,UAAU,CAAC,0BAA0B,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;aAC5D;YACD,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IACH,uBAAC;AAAD,CAAC,AAnBD,IAmBC;AAED,MAAM,CAAC,IAAM,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../types';\nimport { ResourceDetectionConfig } from '../../config';\nimport { arch, hostname } from 'os';\nimport { normalizeArch } from './utils';\nimport { getMachineId } from './machine-id/getMachineId';\n\n/**\n * HostDetectorSync detects the resources related to the host current process is\n * running on. Currently only non-cloud-based attributes are included.\n */\nclass HostDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): Resource {\n    const attributes: ResourceAttributes = {\n      [SemanticResourceAttributes.HOST_NAME]: hostname(),\n      [SemanticResourceAttributes.HOST_ARCH]: normalizeArch(arch()),\n    };\n\n    return new Resource(attributes, this._getAsyncAttributes());\n  }\n\n  private _getAsyncAttributes(): Promise<ResourceAttributes> {\n    return getMachineId().then(machineId => {\n      const attributes: ResourceAttributes = {};\n      if (machineId) {\n        attributes[SemanticResourceAttributes.HOST_ID] = machineId;\n      }\n      return attributes;\n    });\n  }\n}\n\nexport const hostDetectorSync = new HostDetectorSync();\n"]}