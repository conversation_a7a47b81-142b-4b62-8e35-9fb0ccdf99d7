{"version": 3, "file": "detect-resources.js", "sourceRoot": "", "sources": ["../../src/detect-resources.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAEtC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAIxC;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,eAAe,GAAG,UAC7B,MAAoC;IAApC,uBAAA,EAAA,WAAoC;;;;;wBAEL,qBAAM,OAAO,CAAC,GAAG,CAC9C,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAM,CAAC;;;;;;oCAEf,qBAAM,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAA;;oCAAjC,QAAQ,GAAG,SAAsB;oCACvC,IAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,qBAAkB,EAAE,QAAQ,CAAC,CAAC;oCAC9D,sBAAO,QAAQ,EAAC;;;oCAEhB,IAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,iBAAY,GAAC,CAAC,OAAS,CAAC,CAAC;oCACzD,sBAAO,QAAQ,CAAC,KAAK,EAAE,EAAC;;;;yBAE3B,CAAC,CACH,EAAA;;oBAXK,SAAS,GAAgB,SAW9B;oBAED,yDAAyD;oBACzD,YAAY,CAAC,SAAS,CAAC,CAAC;oBAExB,sBAAO,SAAS,CAAC,MAAM,CACrB,UAAC,GAAG,EAAE,QAAQ,IAAK,OAAA,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAnB,CAAmB,EACtC,QAAQ,CAAC,KAAK,EAAE,CACjB,EAAC;;;;CACH,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,UACjC,MAAoC;;IAApC,uBAAA,EAAA,WAAoC;IAEpC,IAAM,SAAS,GAAgB,CAAC,MAAA,MAAM,CAAC,SAAS,mCAAI,EAAE,CAAC,CAAC,GAAG,CACzD,UAAC,CAA0B;QACzB,IAAI;YACF,IAAM,mBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,UAAmB,CAAC;YACxB,IAAI,aAAa,CAAW,mBAAiB,CAAC,EAAE;gBAC9C,IAAM,aAAa,GAAG;;;;oCACK,qBAAM,mBAAiB,EAAA;;gCAA1C,gBAAgB,GAAG,SAAuB;gCAChD,sBAAO,gBAAgB,CAAC,UAAU,EAAC;;;qBACpC,CAAC;gBACF,UAAQ,GAAG,IAAI,QAAQ,CAAC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;aAC9C;iBAAM;gBACL,UAAQ,GAAG,mBAA8B,CAAC;aAC3C;YAED,IAAI,UAAQ,CAAC,sBAAsB,EAAE;gBACnC,KAAK,UAAQ;qBACV,sBAAsB,EAAE;qBACxB,IAAI,CAAC;oBACJ,OAAA,IAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,qBAAkB,EAAE,UAAQ,CAAC;gBAA7D,CAA6D,CAC9D,CAAC;aACL;iBAAM;gBACL,IAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,qBAAkB,EAAE,UAAQ,CAAC,CAAC;aAC/D;YAED,OAAO,UAAQ,CAAC;SACjB;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAI,CAAC,CAAC,WAAW,CAAC,IAAI,iBAAY,CAAC,CAAC,OAAS,CAAC,CAAC;YACzD,OAAO,QAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;IACH,CAAC,CACF,CAAC;IAEF,IAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CACtC,UAAC,GAAG,EAAE,QAAQ,IAAK,OAAA,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAnB,CAAmB,EACtC,QAAQ,CAAC,KAAK,EAAE,CACjB,CAAC;IAEF,IAAI,eAAe,CAAC,sBAAsB,EAAE;QAC1C,KAAK,eAAe,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC;YACjD,yDAAyD;YACzD,YAAY,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,eAAe,CAAC;AACzB,CAAC,CAAC;AAEF;;;;GAIG;AACH,IAAM,YAAY,GAAG,UAAC,SAA2B;IAC/C,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;QACxB,iCAAiC;QACjC,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/C,IAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACzE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;SACnC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Resource } from './Resource';\nimport { ResourceDetectionConfig } from './config';\nimport { diag } from '@opentelemetry/api';\nimport { isPromiseLike } from './utils';\nimport { Detector, DetectorSync } from './types';\nimport { IResource } from './IResource';\n\n/**\n * Runs all resource detectors and returns the results merged into a single Resource. Promise\n * does not resolve until all the underlying detectors have resolved, unlike\n * detectResourcesSync.\n *\n * @deprecated use detectResourcesSync() instead.\n * @param config Configuration for resource detection\n */\nexport const detectResources = async (\n  config: ResourceDetectionConfig = {}\n): Promise<IResource> => {\n  const resources: IResource[] = await Promise.all(\n    (config.detectors || []).map(async d => {\n      try {\n        const resource = await d.detect(config);\n        diag.debug(`${d.constructor.name} found resource.`, resource);\n        return resource;\n      } catch (e) {\n        diag.debug(`${d.constructor.name} failed: ${e.message}`);\n        return Resource.empty();\n      }\n    })\n  );\n\n  // Future check if verbose logging is enabled issue #1903\n  logResources(resources);\n\n  return resources.reduce(\n    (acc, resource) => acc.merge(resource),\n    Resource.empty()\n  );\n};\n\n/**\n * Runs all resource detectors synchronously, merging their results. In case of attribute collision later resources will take precedence.\n *\n * @param config Configuration for resource detection\n */\nexport const detectResourcesSync = (\n  config: ResourceDetectionConfig = {}\n): IResource => {\n  const resources: IResource[] = (config.detectors ?? []).map(\n    (d: Detector | DetectorSync) => {\n      try {\n        const resourceOrPromise = d.detect(config);\n        let resource: IResource;\n        if (isPromiseLike<Resource>(resourceOrPromise)) {\n          const createPromise = async () => {\n            const resolvedResource = await resourceOrPromise;\n            return resolvedResource.attributes;\n          };\n          resource = new Resource({}, createPromise());\n        } else {\n          resource = resourceOrPromise as IResource;\n        }\n\n        if (resource.waitForAsyncAttributes) {\n          void resource\n            .waitForAsyncAttributes()\n            .then(() =>\n              diag.debug(`${d.constructor.name} found resource.`, resource)\n            );\n        } else {\n          diag.debug(`${d.constructor.name} found resource.`, resource);\n        }\n\n        return resource;\n      } catch (e) {\n        diag.error(`${d.constructor.name} failed: ${e.message}`);\n        return Resource.empty();\n      }\n    }\n  );\n\n  const mergedResources = resources.reduce(\n    (acc, resource) => acc.merge(resource),\n    Resource.empty()\n  );\n\n  if (mergedResources.waitForAsyncAttributes) {\n    void mergedResources.waitForAsyncAttributes().then(() => {\n      // Future check if verbose logging is enabled issue #1903\n      logResources(resources);\n    });\n  }\n\n  return mergedResources;\n};\n\n/**\n * Writes debug information about the detected resources to the logger defined in the resource detection config, if one is provided.\n *\n * @param resources The array of {@link Resource} that should be logged. Empty entries will be ignored.\n */\nconst logResources = (resources: Array<IResource>) => {\n  resources.forEach(resource => {\n    // Print only populated resources\n    if (Object.keys(resource.attributes).length > 0) {\n      const resourceDebugString = JSON.stringify(resource.attributes, null, 4);\n      diag.verbose(resourceDebugString);\n    }\n  });\n};\n"]}