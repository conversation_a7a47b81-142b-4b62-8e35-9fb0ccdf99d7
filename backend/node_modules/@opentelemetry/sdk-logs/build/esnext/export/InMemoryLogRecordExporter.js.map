{"version": 3, "file": "InMemoryLogRecordExporter.js", "sourceRoot": "", "sources": ["../../../src/export/InMemoryLogRecordExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAKvD;;;;GAIG;AACH,MAAM,OAAO,yBAAyB;IAAtC;QACU,wBAAmB,GAAwB,EAAE,CAAC;QAEtD;;;WAGG;QACO,aAAQ,GAAG,KAAK,CAAC;IA8B7B,CAAC;IA5BQ,MAAM,CACX,IAAyB,EACzB,cAA8C;QAE9C,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,cAAc,CAAC;gBACpB,IAAI,EAAE,gBAAgB,CAAC,MAAM;gBAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,2BAA2B,CAAC;aAC9C,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACvC,cAAc,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;IACrD,CAAC;IAEM,QAAQ;QACb,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAEM,qBAAqB;QAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;IAChC,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ExportResult } from '@opentelemetry/core';\nimport { ExportResultCode } from '@opentelemetry/core';\n\nimport type { ReadableLogRecord } from './ReadableLogRecord';\nimport type { LogRecordExporter } from './LogRecordExporter';\n\n/**\n * This class can be used for testing purposes. It stores the exported LogRecords\n * in a list in memory that can be retrieved using the `getFinishedLogRecords()`\n * method.\n */\nexport class InMemoryLogRecordExporter implements LogRecordExporter {\n  private _finishedLogRecords: ReadableLogRecord[] = [];\n\n  /**\n   * Indicates if the exporter has been \"shutdown.\"\n   * When false, exported log records will not be stored in-memory.\n   */\n  protected _stopped = false;\n\n  public export(\n    logs: ReadableLogRecord[],\n    resultCallback: (result: ExportResult) => void\n  ) {\n    if (this._stopped) {\n      return resultCallback({\n        code: ExportResultCode.FAILED,\n        error: new Error('Exporter has been stopped'),\n      });\n    }\n\n    this._finishedLogRecords.push(...logs);\n    resultCallback({ code: ExportResultCode.SUCCESS });\n  }\n\n  public shutdown(): Promise<void> {\n    this._stopped = true;\n    this.reset();\n    return Promise.resolve();\n  }\n\n  public getFinishedLogRecords(): ReadableLogRecord[] {\n    return this._finishedLogRecords;\n  }\n\n  public reset(): void {\n    this._finishedLogRecords = [];\n  }\n}\n"]}