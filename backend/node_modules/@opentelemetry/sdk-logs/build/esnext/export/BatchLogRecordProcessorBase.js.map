{"version": 3, "file": "BatchLogRecordProcessorBase.js", "sourceRoot": "", "sources": ["../../../src/export/BatchLogRecordProcessorBase.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,gBAAgB,EAChB,MAAM,EACN,kBAAkB,EAClB,UAAU,EACV,eAAe,EACf,cAAc,GACf,MAAM,qBAAqB,CAAC;AAO7B,MAAM,OAAgB,2BAA2B;IAY/C,YACmB,SAA4B,EAC7C,MAAU;;QADO,cAAS,GAAT,SAAS,CAAmB;QALvC,wBAAmB,GAAgB,EAAE,CAAC;QAQ5C,MAAM,GAAG,GAAG,MAAM,EAAE,CAAC;QACrB,IAAI,CAAC,mBAAmB;YACtB,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,kBAAkB,mCAAI,GAAG,CAAC,+BAA+B,CAAC;QACpE,IAAI,CAAC,aAAa,GAAG,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,YAAY,mCAAI,GAAG,CAAC,wBAAwB,CAAC;QAC1E,IAAI,CAAC,qBAAqB;YACxB,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,oBAAoB,mCAAI,GAAG,CAAC,wBAAwB,CAAC;QAC/D,IAAI,CAAC,oBAAoB;YACvB,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,mBAAmB,mCAAI,GAAG,CAAC,wBAAwB,CAAC;QAE9D,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,EAAE;YACjD,IAAI,CAAC,IAAI,CACP,wIAAwI,CACzI,CAAC;YACF,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC;SAC/C;IACH,CAAC;IAEM,MAAM,CAAC,SAAoB;QAChC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QACD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAEM,UAAU;QACf,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,SAAS;QACrB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACvB,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED,qCAAqC;IAC7B,YAAY,CAAC,SAAoB;QACvC,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;YACzD,OAAO;SACR;QACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;;;SAIK;IACG,SAAS;QACf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAC3D,CAAC;YACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBACnC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aACtC;YACD,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAClB,IAAI,CAAC,GAAG,EAAE;gBACT,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC;iBACD,KAAK,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YACzC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,eAAe,CACb,IAAI,CAAC,OAAO,CACV,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAC7D,EACD,IAAI,CAAC,oBAAoB,CAC1B;iBACE,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;iBACrB,KAAK,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,OAAO;SACR;QACD,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,cAAc,EAAE;iBAClB,IAAI,CAAC,GAAG,EAAE;gBACT,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACvC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;iBACzB;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,CAAC,EAAE;gBACT,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;QACP,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC/B,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAEO,WAAW;QACjB,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;IACH,CAAC;IAEO,OAAO,CAAC,UAAuB;QACrC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,GAAiB,EAAE,EAAE;;gBACtD,IAAI,GAAG,CAAC,IAAI,KAAK,gBAAgB,CAAC,OAAO,EAAE;oBACzC,MAAM,CACJ,MAAA,GAAG,CAAC,KAAK,mCACP,IAAI,KAAK,CACP,iEAAiE,GAAG,GAAG,CACxE,CACJ,CAAC;oBACF,OAAO;iBACR;gBACD,OAAO,CAAC,GAAG,CAAC,CAAC;YACf,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CAGF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ExportResult } from '@opentelemetry/core';\nimport { diag } from '@opentelemetry/api';\nimport {\n  ExportResultCode,\n  getEnv,\n  globalErrorHandler,\n  unrefTimer,\n  callWithTimeout,\n  BindOnceFuture,\n} from '@opentelemetry/core';\n\nimport type { BufferConfig } from '../types';\nimport type { LogRecord } from '../LogRecord';\nimport type { LogRecordExporter } from './LogRecordExporter';\nimport type { LogRecordProcessor } from '../LogRecordProcessor';\n\nexport abstract class BatchLogRecordProcessorBase<T extends BufferConfig>\n  implements LogRecordProcessor\n{\n  private readonly _maxExportBatchSize: number;\n  private readonly _maxQueueSize: number;\n  private readonly _scheduledDelayMillis: number;\n  private readonly _exportTimeoutMillis: number;\n\n  private _finishedLogRecords: LogRecord[] = [];\n  private _timer: NodeJS.Timeout | undefined;\n  private _shutdownOnce: BindOnceFuture<void>;\n\n  constructor(\n    private readonly _exporter: LogRecordExporter,\n    config?: T\n  ) {\n    const env = getEnv();\n    this._maxExportBatchSize =\n      config?.maxExportBatchSize ?? env.OTEL_BLRP_MAX_EXPORT_BATCH_SIZE;\n    this._maxQueueSize = config?.maxQueueSize ?? env.OTEL_BLRP_MAX_QUEUE_SIZE;\n    this._scheduledDelayMillis =\n      config?.scheduledDelayMillis ?? env.OTEL_BLRP_SCHEDULE_DELAY;\n    this._exportTimeoutMillis =\n      config?.exportTimeoutMillis ?? env.OTEL_BLRP_EXPORT_TIMEOUT;\n\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n\n    if (this._maxExportBatchSize > this._maxQueueSize) {\n      diag.warn(\n        'BatchLogRecordProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize'\n      );\n      this._maxExportBatchSize = this._maxQueueSize;\n    }\n  }\n\n  public onEmit(logRecord: LogRecord): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n    this._addToBuffer(logRecord);\n  }\n\n  public forceFlush(): Promise<void> {\n    if (this._shutdownOnce.isCalled) {\n      return this._shutdownOnce.promise;\n    }\n    return this._flushAll();\n  }\n\n  public shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private async _shutdown(): Promise<void> {\n    this.onShutdown();\n    await this._flushAll();\n    await this._exporter.shutdown();\n  }\n\n  /** Add a LogRecord in the buffer. */\n  private _addToBuffer(logRecord: LogRecord) {\n    if (this._finishedLogRecords.length >= this._maxQueueSize) {\n      return;\n    }\n    this._finishedLogRecords.push(logRecord);\n    this._maybeStartTimer();\n  }\n\n  /**\n   * Send all LogRecords to the exporter respecting the batch size limit\n   * This function is used only on forceFlush or shutdown,\n   * for all other cases _flush should be used\n   * */\n  private _flushAll(): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const promises = [];\n      const batchCount = Math.ceil(\n        this._finishedLogRecords.length / this._maxExportBatchSize\n      );\n      for (let i = 0; i < batchCount; i++) {\n        promises.push(this._flushOneBatch());\n      }\n      Promise.all(promises)\n        .then(() => {\n          resolve();\n        })\n        .catch(reject);\n    });\n  }\n\n  private _flushOneBatch(): Promise<void> {\n    this._clearTimer();\n    if (this._finishedLogRecords.length === 0) {\n      return Promise.resolve();\n    }\n    return new Promise((resolve, reject) => {\n      callWithTimeout(\n        this._export(\n          this._finishedLogRecords.splice(0, this._maxExportBatchSize)\n        ),\n        this._exportTimeoutMillis\n      )\n        .then(() => resolve())\n        .catch(reject);\n    });\n  }\n\n  private _maybeStartTimer() {\n    if (this._timer !== undefined) {\n      return;\n    }\n    this._timer = setTimeout(() => {\n      this._flushOneBatch()\n        .then(() => {\n          if (this._finishedLogRecords.length > 0) {\n            this._clearTimer();\n            this._maybeStartTimer();\n          }\n        })\n        .catch(e => {\n          globalErrorHandler(e);\n        });\n    }, this._scheduledDelayMillis);\n    unrefTimer(this._timer);\n  }\n\n  private _clearTimer() {\n    if (this._timer !== undefined) {\n      clearTimeout(this._timer);\n      this._timer = undefined;\n    }\n  }\n\n  private _export(logRecords: LogRecord[]): Promise<ExportResult> {\n    return new Promise((resolve, reject) => {\n      this._exporter.export(logRecords, (res: ExportResult) => {\n        if (res.code !== ExportResultCode.SUCCESS) {\n          reject(\n            res.error ??\n              new Error(\n                `BatchLogRecordProcessorBase: log record export failed (status ${res})`\n              )\n          );\n          return;\n        }\n        resolve(res);\n      });\n    });\n  }\n\n  protected abstract onShutdown(): void;\n}\n"]}