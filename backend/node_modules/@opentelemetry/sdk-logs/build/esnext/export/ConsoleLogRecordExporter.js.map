{"version": 3, "file": "ConsoleLogRecordExporter.js", "sourceRoot": "", "sources": ["../../../src/export/ConsoleLogRecordExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAgB,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAKvD;;;GAGG;AAEH,+BAA+B;AAC/B,MAAM,OAAO,wBAAwB;IACnC;;;;OAIG;IACI,MAAM,CACX,IAAyB,EACzB,cAA8C;QAE9C,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACK,WAAW,CAAC,SAA4B;;QAC9C,OAAO;YACL,SAAS,EAAE,oBAAoB,CAAC,SAAS,CAAC,MAAM,CAAC;YACjD,OAAO,EAAE,MAAA,SAAS,CAAC,WAAW,0CAAE,OAAO;YACvC,MAAM,EAAE,MAAA,SAAS,CAAC,WAAW,0CAAE,MAAM;YACrC,UAAU,EAAE,MAAA,SAAS,CAAC,WAAW,0CAAE,UAAU;YAC7C,YAAY,EAAE,SAAS,CAAC,YAAY;YACpC,cAAc,EAAE,SAAS,CAAC,cAAc;YACxC,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,UAAU,EAAE,SAAS,CAAC,UAAU;SACjC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACK,eAAe,CACrB,UAA+B,EAC/B,IAAqC;QAErC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;SACxD;QACD,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;IAC7C,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExportResult, hrTimeToMicroseconds } from '@opentelemetry/core';\nimport { ExportResultCode } from '@opentelemetry/core';\n\nimport type { ReadableLogRecord } from './ReadableLogRecord';\nimport type { LogRecordExporter } from './LogRecordExporter';\n\n/**\n * This is implementation of {@link LogRecordExporter} that prints LogRecords to the\n * console. This class can be used for diagnostic purposes.\n */\n\n/* eslint-disable no-console */\nexport class ConsoleLogRecordExporter implements LogRecordExporter {\n  /**\n   * Export logs.\n   * @param logs\n   * @param resultCallback\n   */\n  public export(\n    logs: ReadableLogRecord[],\n    resultCallback: (result: ExportResult) => void\n  ) {\n    this._sendLogRecords(logs, resultCallback);\n  }\n\n  /**\n   * Shutdown the exporter.\n   */\n  public shutdown(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  /**\n   * converts logRecord info into more readable format\n   * @param logRecord\n   */\n  private _exportInfo(logRecord: ReadableLogRecord) {\n    return {\n      timestamp: hrTimeToMicroseconds(logRecord.hrTime),\n      traceId: logRecord.spanContext?.traceId,\n      spanId: logRecord.spanContext?.spanId,\n      traceFlags: logRecord.spanContext?.traceFlags,\n      severityText: logRecord.severityText,\n      severityNumber: logRecord.severityNumber,\n      body: logRecord.body,\n      attributes: logRecord.attributes,\n    };\n  }\n\n  /**\n   * Showing logs  in console\n   * @param logRecords\n   * @param done\n   */\n  private _sendLogRecords(\n    logRecords: ReadableLogRecord[],\n    done?: (result: ExportResult) => void\n  ): void {\n    for (const logRecord of logRecords) {\n      console.dir(this._exportInfo(logRecord), { depth: 3 });\n    }\n    done?.({ code: ExportResultCode.SUCCESS });\n  }\n}\n"]}