{"version": 3, "file": "BatchLogRecordProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/platform/browser/export/BatchLogRecordProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,6FAA0F;AAE1F,MAAa,uBAAwB,SAAQ,yDAAiE;IAI5G,YACE,QAA2B,EAC3B,MAA6C;QAE7C,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACvB,CAAC;IAES,UAAU;QAClB,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,OAAO;SACR;QACD,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,QAAQ,CAAC,mBAAmB,CAC1B,kBAAkB,EAClB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;SACH;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAClE;IACH,CAAC;IAEO,OAAO,CAAC,MAA6C;QAC3D,IACE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,8BAA8B,MAAK,IAAI;YAC/C,OAAO,QAAQ,KAAK,WAAW,EAC/B;YACA,OAAO;SACR;QACD,IAAI,CAAC,yBAAyB,GAAG,GAAG,EAAE;YACpC,IAAI,QAAQ,CAAC,eAAe,KAAK,QAAQ,EAAE;gBACzC,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;aACxB;QACH,CAAC,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAE;YAC5B,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC,CAAC;QACF,QAAQ,CAAC,gBAAgB,CACvB,kBAAkB,EAClB,IAAI,CAAC,yBAAyB,CAC/B,CAAC;QAEF,oGAAoG;QACpG,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAChE,CAAC;CACF;AAlDD,0DAkDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { LogRecordExporter } from './../../../export/LogRecordExporter';\nimport type { BatchLogRecordProcessorBrowserConfig } from '../../../types';\nimport { BatchLogRecordProcessorBase } from '../../../export/BatchLogRecordProcessorBase';\n\nexport class BatchLogRecordProcessor extends BatchLogRecordProcessorBase<BatchLogRecordProcessorBrowserConfig> {\n  private _visibilityChangeListener?: () => void;\n  private _pageHideListener?: () => void;\n\n  constructor(\n    exporter: LogRecordExporter,\n    config?: BatchLogRecordProcessorBrowserConfig\n  ) {\n    super(exporter, config);\n    this._onInit(config);\n  }\n\n  protected onShutdown(): void {\n    if (typeof document === 'undefined') {\n      return;\n    }\n    if (this._visibilityChangeListener) {\n      document.removeEventListener(\n        'visibilitychange',\n        this._visibilityChangeListener\n      );\n    }\n    if (this._pageHideListener) {\n      document.removeEventListener('pagehide', this._pageHideListener);\n    }\n  }\n\n  private _onInit(config?: BatchLogRecordProcessorBrowserConfig): void {\n    if (\n      config?.disableAutoFlushOnDocumentHide === true ||\n      typeof document === 'undefined'\n    ) {\n      return;\n    }\n    this._visibilityChangeListener = () => {\n      if (document.visibilityState === 'hidden') {\n        void this.forceFlush();\n      }\n    };\n    this._pageHideListener = () => {\n      void this.forceFlush();\n    };\n    document.addEventListener(\n      'visibilitychange',\n      this._visibilityChangeListener\n    );\n\n    // use 'pagehide' event as a fallback for Safari; see https://bugs.webkit.org/show_bug.cgi?id=116769\n    document.addEventListener('pagehide', this._pageHideListener);\n  }\n}\n"]}