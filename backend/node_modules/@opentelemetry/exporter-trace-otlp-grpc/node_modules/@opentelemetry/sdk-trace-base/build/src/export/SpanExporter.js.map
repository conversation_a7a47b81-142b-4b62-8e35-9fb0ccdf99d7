{"version": 3, "file": "SpanExporter.js", "sourceRoot": "", "sources": ["../../../src/export/SpanExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExportResult } from '@opentelemetry/core';\nimport { ReadableSpan } from './ReadableSpan';\n\n/**\n * An interface that allows different tracing services to export recorded data\n * for sampled spans in their own format.\n *\n * To export data this MUST be register to the Tracer SDK using a optional\n * config.\n */\nexport interface SpanExporter {\n  /**\n   * Called to export sampled {@link ReadableSpan}s.\n   * @param spans the list of sampled Spans to be exported.\n   */\n  export(\n    spans: ReadableSpan[],\n    resultCallback: (result: ExportResult) => void\n  ): void;\n\n  /** Stops the exporter. */\n  shutdown(): Promise<void>;\n\n  /** Immediately export all spans */\n  forceFlush?(): Promise<void>;\n}\n"]}