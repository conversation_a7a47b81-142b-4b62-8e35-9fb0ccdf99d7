{"version": 3, "file": "AlwaysOnSampler.js", "sourceRoot": "", "sources": ["../../../../src/trace/sampler/AlwaysOnSampler.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAW,gBAAgB,EAAkB,MAAM,oBAAoB,CAAC;AAE/E;;;GAGG;AACH;IAAA;IAUA,CAAC;IATC,sCAAY,GAAZ;QACE,OAAO;YACL,QAAQ,EAAE,gBAAgB,CAAC,kBAAkB;SAC9C,CAAC;IACJ,CAAC;IAED,kCAAQ,GAAR;QACE,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IACH,sBAAC;AAAD,CAAC,AAVD,IAUC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Sampler, SamplingDecision, SamplingResult } from '@opentelemetry/api';\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n * Sampler that samples all traces.\n */\nexport class AlwaysOnSampler implements Sampler {\n  shouldSample(): SamplingResult {\n    return {\n      decision: SamplingDecision.RECORD_AND_SAMPLED,\n    };\n  }\n\n  toString(): string {\n    return 'AlwaysOnSampler';\n  }\n}\n"]}