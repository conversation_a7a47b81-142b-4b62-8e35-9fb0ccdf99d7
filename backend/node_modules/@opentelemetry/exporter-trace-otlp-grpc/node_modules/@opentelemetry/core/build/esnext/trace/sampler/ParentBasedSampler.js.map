{"version": 3, "file": "ParentBasedSampler.js", "sourceRoot": "", "sources": ["../../../../src/trace/sampler/ParentBasedSampler.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAEL,kBAAkB,EAMlB,UAAU,EACV,KAAK,GACN,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD;;;;GAIG;AACH,MAAM,OAAO,kBAAkB;IAO7B,YAAY,MAAgC;;QAC1C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,kBAAkB,CAChB,IAAI,KAAK,CAAC,wDAAwD,CAAC,CACpE,CAAC;YACF,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,EAAE,CAAC;SACpC;QAED,IAAI,CAAC,oBAAoB;YACvB,MAAA,MAAM,CAAC,mBAAmB,mCAAI,IAAI,eAAe,EAAE,CAAC;QACtD,IAAI,CAAC,uBAAuB;YAC1B,MAAA,MAAM,CAAC,sBAAsB,mCAAI,IAAI,gBAAgB,EAAE,CAAC;QAC1D,IAAI,CAAC,mBAAmB;YACtB,MAAA,MAAM,CAAC,kBAAkB,mCAAI,IAAI,eAAe,EAAE,CAAC;QACrD,IAAI,CAAC,sBAAsB;YACzB,MAAA,MAAM,CAAC,qBAAqB,mCAAI,IAAI,gBAAgB,EAAE,CAAC;IAC3D,CAAC;IAED,YAAY,CACV,OAAgB,EAChB,OAAe,EACf,QAAgB,EAChB,QAAkB,EAClB,UAA0B,EAC1B,KAAa;QAEb,MAAM,aAAa,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAEpD,IAAI,CAAC,aAAa,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,EAAE;YACxD,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAC5B,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;SACH;QAED,IAAI,aAAa,CAAC,QAAQ,EAAE;YAC1B,IAAI,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,EAAE;gBACjD,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAC3C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;aACH;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAC9C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;SACH;QAED,IAAI,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,EAAE;YACjD,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAC1C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;SACH;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAC7C,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,CACN,CAAC;IACJ,CAAC;IAED,QAAQ;QACN,OAAO,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,yBAAyB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,4BAA4B,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,wBAAwB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,2BAA2B,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,GAAG,CAAC;IAClT,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  isSpanContextValid,\n  Link,\n  Sampler,\n  SamplingResult,\n  SpanAttributes,\n  SpanKind,\n  TraceFlags,\n  trace,\n} from '@opentelemetry/api';\nimport { globalErrorHandler } from '../../common/global-error-handler';\nimport { AlwaysOffSampler } from './AlwaysOffSampler';\nimport { AlwaysOnSampler } from './AlwaysOnSampler';\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n * A composite sampler that either respects the parent span's sampling decision\n * or delegates to `delegateSampler` for root spans.\n */\nexport class ParentBasedSampler implements Sampler {\n  private _root: Sampler;\n  private _remoteParentSampled: Sampler;\n  private _remoteParentNotSampled: Sampler;\n  private _localParentSampled: Sampler;\n  private _localParentNotSampled: Sampler;\n\n  constructor(config: ParentBasedSamplerConfig) {\n    this._root = config.root;\n\n    if (!this._root) {\n      globalErrorHandler(\n        new Error('ParentBasedSampler must have a root sampler configured')\n      );\n      this._root = new AlwaysOnSampler();\n    }\n\n    this._remoteParentSampled =\n      config.remoteParentSampled ?? new AlwaysOnSampler();\n    this._remoteParentNotSampled =\n      config.remoteParentNotSampled ?? new AlwaysOffSampler();\n    this._localParentSampled =\n      config.localParentSampled ?? new AlwaysOnSampler();\n    this._localParentNotSampled =\n      config.localParentNotSampled ?? new AlwaysOffSampler();\n  }\n\n  shouldSample(\n    context: Context,\n    traceId: string,\n    spanName: string,\n    spanKind: SpanKind,\n    attributes: SpanAttributes,\n    links: Link[]\n  ): SamplingResult {\n    const parentContext = trace.getSpanContext(context);\n\n    if (!parentContext || !isSpanContextValid(parentContext)) {\n      return this._root.shouldSample(\n        context,\n        traceId,\n        spanName,\n        spanKind,\n        attributes,\n        links\n      );\n    }\n\n    if (parentContext.isRemote) {\n      if (parentContext.traceFlags & TraceFlags.SAMPLED) {\n        return this._remoteParentSampled.shouldSample(\n          context,\n          traceId,\n          spanName,\n          spanKind,\n          attributes,\n          links\n        );\n      }\n      return this._remoteParentNotSampled.shouldSample(\n        context,\n        traceId,\n        spanName,\n        spanKind,\n        attributes,\n        links\n      );\n    }\n\n    if (parentContext.traceFlags & TraceFlags.SAMPLED) {\n      return this._localParentSampled.shouldSample(\n        context,\n        traceId,\n        spanName,\n        spanKind,\n        attributes,\n        links\n      );\n    }\n\n    return this._localParentNotSampled.shouldSample(\n      context,\n      traceId,\n      spanName,\n      spanKind,\n      attributes,\n      links\n    );\n  }\n\n  toString(): string {\n    return `ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`;\n  }\n}\n\ninterface ParentBasedSamplerConfig {\n  /** Sampler called for spans with no parent */\n  root: Sampler;\n  /** Sampler called for spans with a remote parent which was sampled. Default AlwaysOn */\n  remoteParentSampled?: Sampler;\n  /** Sampler called for spans with a remote parent which was not sampled. Default AlwaysOff */\n  remoteParentNotSampled?: Sampler;\n  /** Sampler called for spans with a local parent which was sampled. Default AlwaysOn */\n  localParentSampled?: Sampler;\n  /** Sampler called for spans with a local parent which was not sampled. Default AlwaysOff */\n  localParentNotSampled?: Sampler;\n}\n"]}