{"version": 3, "file": "global-error-handler.js", "sourceRoot": "", "sources": ["../../../src/common/global-error-handler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,mEAA8D;AAG9D,wCAAwC;AACxC,IAAI,eAAe,GAAG,IAAA,2CAAmB,GAAE,CAAC;AAE5C;;;GAGG;AACH,SAAgB,qBAAqB,CAAC,OAAqB;IACzD,eAAe,GAAG,OAAO,CAAC;AAC5B,CAAC;AAFD,sDAEC;AAED;;;GAGG;AACH,SAAgB,kBAAkB,CAAC,EAAa;IAC9C,IAAI;QACF,eAAe,CAAC,EAAE,CAAC,CAAC;KACrB;IAAC,WAAM,GAAE,CAAC,+BAA+B;AAC5C,CAAC;AAJD,gDAIC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Exception } from '@opentelemetry/api';\nimport { loggingErrorHandler } from './logging-error-handler';\nimport { ErrorHandler } from './types';\n\n/** The global error handler delegate */\nlet delegateHandler = loggingErrorHandler();\n\n/**\n * Set the global error handler\n * @param {ErrorHandler} handler\n */\nexport function setGlobalErrorHandler(handler: ErrorHandler): void {\n  delegateHandler = handler;\n}\n\n/**\n * Return the global error handler\n * @param {Exception} ex\n */\nexport function globalErrorHandler(ex: Exception): void {\n  try {\n    delegateHandler(ex);\n  } catch {} // eslint-disable-line no-empty\n}\n"]}