import { BatchSpanProcessorBase } from '../../../export/BatchSpanProcessorBase';
import { SpanExporter } from '../../../export/SpanExporter';
import { BatchSpanProcessorBrowserConfig } from '../../../types';
export declare class BatchSpanProcessor extends BatchSpanProcessorBase<BatchSpanProcessorBrowserConfig> {
    private _visibilityChangeListener?;
    private _pageHideListener?;
    constructor(_exporter: SpanExporter, config?: BatchSpanProcessorBrowserConfig);
    private onInit;
    protected onShutdown(): void;
}
//# sourceMappingURL=BatchSpanProcessor.d.ts.map