{"version": 3, "file": "MultiSpanProcessor.js", "sourceRoot": "", "sources": ["../../src/MultiSpanProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;AAGH,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAKzD;;;GAGG;AACH;IACE,4BAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAEjE,uCAAU,GAAV;;QACE,IAAM,QAAQ,GAAoB,EAAE,CAAC;;YAErC,KAA4B,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAA,gBAAA,4BAAE;gBAA7C,IAAM,aAAa,WAAA;gBACtB,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;aAC3C;;;;;;;;;QACD,OAAO,IAAI,OAAO,CAAC,UAAA,OAAO;YACxB,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAClB,IAAI,CAAC;gBACJ,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC;iBACD,KAAK,CAAC,UAAA,KAAK;gBACV,kBAAkB,CAChB,KAAK,IAAI,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAC5D,CAAC;gBACF,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED,oCAAO,GAAP,UAAQ,IAAU,EAAE,OAAgB;;;YAClC,KAA4B,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAA,gBAAA,4BAAE;gBAA7C,IAAM,aAAa,WAAA;gBACtB,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aACtC;;;;;;;;;IACH,CAAC;IAED,kCAAK,GAAL,UAAM,IAAkB;;;YACtB,KAA4B,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAA,gBAAA,4BAAE;gBAA7C,IAAM,aAAa,WAAA;gBACtB,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aAC3B;;;;;;;;;IACH,CAAC;IAED,qCAAQ,GAAR;;QACE,IAAM,QAAQ,GAAoB,EAAE,CAAC;;YAErC,KAA4B,IAAA,KAAA,SAAA,IAAI,CAAC,eAAe,CAAA,gBAAA,4BAAE;gBAA7C,IAAM,aAAa,WAAA;gBACtB,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;aACzC;;;;;;;;;QACD,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YACjC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;gBACzB,OAAO,EAAE,CAAC;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;IACH,yBAAC;AAAD,CAAC,AA/CD,IA+CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '@opentelemetry/api';\nimport { globalErrorHandler } from '@opentelemetry/core';\nimport { ReadableSpan } from './export/ReadableSpan';\nimport { Span } from './Span';\nimport { SpanProcessor } from './SpanProcessor';\n\n/**\n * Implementation of the {@link SpanProcessor} that simply forwards all\n * received events to a list of {@link SpanProcessor}s.\n */\nexport class MultiSpanProcessor implements SpanProcessor {\n  constructor(private readonly _spanProcessors: SpanProcessor[]) {}\n\n  forceFlush(): Promise<void> {\n    const promises: Promise<void>[] = [];\n\n    for (const spanProcessor of this._spanProcessors) {\n      promises.push(spanProcessor.forceFlush());\n    }\n    return new Promise(resolve => {\n      Promise.all(promises)\n        .then(() => {\n          resolve();\n        })\n        .catch(error => {\n          globalErrorHandler(\n            error || new Error('MultiSpanProcessor: forceFlush failed')\n          );\n          resolve();\n        });\n    });\n  }\n\n  onStart(span: Span, context: Context): void {\n    for (const spanProcessor of this._spanProcessors) {\n      spanProcessor.onStart(span, context);\n    }\n  }\n\n  onEnd(span: ReadableSpan): void {\n    for (const spanProcessor of this._spanProcessors) {\n      spanProcessor.onEnd(span);\n    }\n  }\n\n  shutdown(): Promise<void> {\n    const promises: Promise<void>[] = [];\n\n    for (const spanProcessor of this._spanProcessors) {\n      promises.push(spanProcessor.shutdown());\n    }\n    return new Promise((resolve, reject) => {\n      Promise.all(promises).then(() => {\n        resolve();\n      }, reject);\n    });\n  }\n}\n"]}