{"version": 3, "file": "BrowserDetectorSync.js", "sourceRoot": "", "sources": ["../../../src/detectors/BrowserDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,0BAA0B,EAAE,MAAM,qCAAqC,CAAC;AACjF,OAAO,EAA2B,QAAQ,EAA2B,MAAM,IAAI,CAAC;AAEhF,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C;;GAEG;AACH,MAAM,mBAAmB;IACvB,MAAM,CAAC,MAAgC;QACrC,MAAM,SAAS,GAAG,OAAO,SAAS,KAAK,WAAW,CAAC;QACnD,IAAI,CAAC,SAAS,EAAE;YACd,OAAO,QAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;QACD,MAAM,eAAe,GAAuB;YAC1C,CAAC,0BAA0B,CAAC,oBAAoB,CAAC,EAAE,SAAS;YAC5D,CAAC,0BAA0B,CAAC,2BAA2B,CAAC,EAAE,aAAa;YACvE,CAAC,0BAA0B,CAAC,uBAAuB,CAAC,EAAE,SAAS,CAAC,SAAS;SAC1E,CAAC;QACF,OAAO,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IACD;;;;;;OAMG;IACK,sBAAsB,CAC5B,eAAmC,EACnC,OAAiC;QAEjC,IACE,eAAe,CAAC,0BAA0B,CAAC,uBAAuB,CAAC,KAAK,EAAE,EAC1E;YACA,IAAI,CAAC,KAAK,CACR,qEAAqE,CACtE,CAAC;YACF,OAAO,QAAQ,CAAC,KAAK,EAAE,CAAC;SACzB;aAAM;YACL,OAAO,IAAI,QAAQ,mBACd,eAAe,EAClB,CAAC;SACJ;IACH,CAAC;CACF;AAED,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';\nimport { DetectorSync, IResource, Resource, ResourceDetectionConfig } from '..';\nimport { ResourceAttributes } from '../types';\nimport { diag } from '@opentelemetry/api';\n\n/**\n * BrowserDetectorSync will be used to detect the resources related to browser.\n */\nclass BrowserDetectorSync implements DetectorSync {\n  detect(config?: ResourceDetectionConfig): IResource {\n    const isBrowser = typeof navigator !== 'undefined';\n    if (!isBrowser) {\n      return Resource.empty();\n    }\n    const browserResource: ResourceAttributes = {\n      [SemanticResourceAttributes.PROCESS_RUNTIME_NAME]: 'browser',\n      [SemanticResourceAttributes.PROCESS_RUNTIME_DESCRIPTION]: 'Web Browser',\n      [SemanticResourceAttributes.PROCESS_RUNTIME_VERSION]: navigator.userAgent,\n    };\n    return this._getResourceAttributes(browserResource, config);\n  }\n  /**\n   * Validates process resource attribute map from process variables\n   *\n   * @param browserResource The un-sanitized resource attributes from process as key/value pairs.\n   * @param config: Config\n   * @returns The sanitized resource attributes.\n   */\n  private _getResourceAttributes(\n    browserResource: ResourceAttributes,\n    _config?: ResourceDetectionConfig\n  ) {\n    if (\n      browserResource[SemanticResourceAttributes.PROCESS_RUNTIME_VERSION] === ''\n    ) {\n      diag.debug(\n        'BrowserDetector failed: Unable to find required browser resources. '\n      );\n      return Resource.empty();\n    } else {\n      return new Resource({\n        ...browserResource,\n      });\n    }\n  }\n}\n\nexport const browserDetectorSync = new BrowserDetectorSync();\n"]}