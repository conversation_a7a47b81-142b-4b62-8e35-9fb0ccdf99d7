{"version": 3, "file": "getMachineId-linux.js", "sourceRoot": "", "sources": ["../../../../../src/platform/node/machine-id/getMachineId-linux.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,MAAM,UAAgB,YAAY;;;;;;;oBAC1B,KAAK,GAAG,CAAC,iBAAiB,EAAE,0BAA0B,CAAC,CAAC;;;;oBAE3C,UAAA,SAAA,KAAK,CAAA;;;;oBAAb,IAAI;;;;oBAEI,qBAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAA;;oBAAtD,MAAM,GAAG,SAA6C;oBAC5D,sBAAO,MAAM,CAAC,IAAI,EAAE,EAAC;;;oBAErB,IAAI,CAAC,KAAK,CAAC,+BAA6B,GAAG,CAAC,CAAC;;;;;;;;;;;;;;;;yBAIjD,sBAAO,EAAE,EAAC;;;;CACX", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { promises as fs } from 'fs';\nimport { diag } from '@opentelemetry/api';\n\nexport async function getMachineId(): Promise<string> {\n  const paths = ['/etc/machine-id', '/var/lib/dbus/machine-id'];\n\n  for (const path of paths) {\n    try {\n      const result = await fs.readFile(path, { encoding: 'utf8' });\n      return result.trim();\n    } catch (e) {\n      diag.debug(`error reading machine id: ${e}`);\n    }\n  }\n\n  return '';\n}\n"]}