{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ResourceDetectionConfig } from './config';\nimport { SpanAttributes } from '@opentelemetry/api';\nimport { IResource } from './IResource';\n\n/**\n * Interface for Resource attributes.\n * General `Attributes` interface is added in api v1.1.0.\n * To backward support older api (1.0.x), the deprecated `SpanAttributes` is used here.\n */\nexport type ResourceAttributes = SpanAttributes;\n\n/**\n * @deprecated please use {@link DetectorSync}\n */\nexport interface Detector {\n  detect(config?: ResourceDetectionConfig): Promise<IResource>;\n}\n\n/**\n * Interface for a synchronous Resource Detector. In order to detect attributes asynchronously, a detector\n * can pass a Promise as the second parameter to the Resource constructor.\n */\nexport interface DetectorSync {\n  detect(config?: ResourceDetectionConfig): IResource;\n}\n"]}