{"version": 3, "file": "OSDetectorSync.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/OSDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,0BAA0B,EAAE,MAAM,qCAAqC,CAAC;AACjF,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAG1C,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC;AACvC,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC;;;GAGG;AACH;IAAA;IAQA,CAAC;IAPC,+BAAM,GAAN,UAAO,OAAiC;;QACtC,IAAM,UAAU;YACd,GAAC,0BAA0B,CAAC,OAAO,IAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC/D,GAAC,0BAA0B,CAAC,UAAU,IAAG,OAAO,EAAE;eACnD,CAAC;QACF,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IACH,qBAAC;AAAD,CAAC,AARD,IAQC;AAED,MAAM,CAAC,IAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../types';\nimport { ResourceDetectionConfig } from '../../config';\nimport { platform, release } from 'os';\nimport { normalizeType } from './utils';\n\n/**\n * OSDetectorSync detects the resources related to the operating system (OS) on\n * which the process represented by this resource is running.\n */\nclass OSDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): Resource {\n    const attributes: ResourceAttributes = {\n      [SemanticResourceAttributes.OS_TYPE]: normalizeType(platform()),\n      [SemanticResourceAttributes.OS_VERSION]: release(),\n    };\n    return new Resource(attributes);\n  }\n}\n\nexport const osDetectorSync = new OSDetectorSync();\n"]}