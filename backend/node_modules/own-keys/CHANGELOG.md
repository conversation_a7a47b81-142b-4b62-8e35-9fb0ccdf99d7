# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.1](https://github.com/ljharb/own-keys/compare/v1.0.0...v1.0.1) - 2024-12-29

### Commits

- [Tests] sort with a proper comparator [`4a65b56`](https://github.com/ljharb/own-keys/commit/4a65b569d10985032a0773806dcdec8866132baa)
- [Fix] fix function name when `Reflect.ownKeys` is absent [`5cf17cb`](https://github.com/ljharb/own-keys/commit/5cf17cb1c9adfa836a1ddc467da4da20973db2ae)

## v1.0.0 - 2024-12-28

### Commits

- Initial implementation, tests, readme, types [`870a06f`](https://github.com/ljharb/own-keys/commit/870a06f9d7a2a6ecee283319d9a7388f8fc1c811)
- Initial commit [`70323dc`](https://github.com/ljharb/own-keys/commit/70323dc09541b4ee80426acfc89762605d7fc7bf)
- npm init [`4a3ad06`](https://github.com/ljharb/own-keys/commit/4a3ad0659defe9a67541c198743bd7733d437725)
- [Tests] node 0.8 sorts object keys differently [`b449364`](https://github.com/ljharb/own-keys/commit/b4493649ead7441fd8e65ab4718acbd088ceac70)
- Only apps should have lockfiles [`a54acbf`](https://github.com/ljharb/own-keys/commit/a54acbf2c6b8ef320abf7493b7c88002c690f9c0)
