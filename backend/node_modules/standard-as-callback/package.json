{"name": "standard-as-callback", "version": "2.1.0", "description": "A performant and standard (Bluebird) library that registers a node-style callback on a promise", "main": "built/index.js", "types": "built/index.d.ts", "directories": {"lib": "built"}, "files": ["built/"], "scripts": {"build": "rm -rf built && tsc", "test": "npm run build && mocha", "prepublishOnly": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/luin/asCallback.git"}, "keywords": ["as<PERSON><PERSON><PERSON>", "nodeify", "promise", "bluebird"], "author": "luin <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/luin/asCallback/issues"}, "homepage": "https://github.com/luin/asCallback#readme", "devDependencies": {"mocha": "^8.3.2", "promise-timeout": "^1.3.0", "sinon": "^9.2.4", "typescript": "^4.2.3"}}