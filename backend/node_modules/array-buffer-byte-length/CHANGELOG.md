# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.2](https://github.com/inspect-js/array-buffer-byte-length/compare/v1.0.1...v1.0.2) - 2024-12-19

### Commits

- [types] use shared config [`b15321c`](https://github.com/inspect-js/array-buffer-byte-length/commit/b15321cf546dae5d3bc2b354fb8e2a4629d6afb3)
- [actions] split out node 10-20, and 20+ [`fa1eb7d`](https://github.com/inspect-js/array-buffer-byte-length/commit/fa1eb7df107d382b47d7b9e7a07e1c4588e81cbd)
- [<PERSON>] update `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/object-inspect`, `@types/tape`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`53e3f93`](https://github.com/inspect-js/array-buffer-byte-length/commit/53e3f93c456b7a8a8217a832b48c416962a176e0)
- [Tests] replace `aud` with `npm audit` [`2d1b66f`](https://github.com/inspect-js/array-buffer-byte-length/commit/2d1b66ff6417b825f58adf859b540c2303d2dbde)
- [Refactor] use `call-bound` directly [`433be34`](https://github.com/inspect-js/array-buffer-byte-length/commit/433be3427e20526d74ab1faaaf0117aaf967f406)
- [Deps] update `call-bind`, `is-array-buffer` [`4ff7467`](https://github.com/inspect-js/array-buffer-byte-length/commit/4ff74673567dcae00e5a8c5d4d0f8c23b29c9a0a)
- [Dev Deps] update `tape`, `typescript` [`42c9f19`](https://github.com/inspect-js/array-buffer-byte-length/commit/42c9f1998fe8630634388b6db2c0563be05c6897)
- [Tests] add attw [`29a72f1`](https://github.com/inspect-js/array-buffer-byte-length/commit/29a72f12eb83dcfaef6a6bed0403e56699669f27)
- [Deps] update `call-bind` [`1e9e902`](https://github.com/inspect-js/array-buffer-byte-length/commit/1e9e90292388a609aa0e722bddbc9313a575ef10)
- [types] remove incorrect overload [`b509518`](https://github.com/inspect-js/array-buffer-byte-length/commit/b509518bc188f79c46aa851311f20c188faa0678)
- [Dev Deps] add missing peer dep [`31962b6`](https://github.com/inspect-js/array-buffer-byte-length/commit/31962b631a173696ceef94e9d00edf13af87d305)

## [v1.0.1](https://github.com/inspect-js/array-buffer-byte-length/compare/v1.0.0...v1.0.1) - 2024-02-03

### Commits

- [patch] add types [`598d446`](https://github.com/inspect-js/array-buffer-byte-length/commit/598d446f45c8f4246493b2a1fa2b32cd0c669602)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `npmignore`, `object-inspect`, `tape` [`2572345`](https://github.com/inspect-js/array-buffer-byte-length/commit/257234593f576a7cbb1dce1b21d52abeb68db34d)
- [Tests] add coverage [`d27357d`](https://github.com/inspect-js/array-buffer-byte-length/commit/d27357de558c3272341e252c3acc010d38edeb0f)
- [Deps] update `call-bind`, `is-array-buffer` [`2ea13ad`](https://github.com/inspect-js/array-buffer-byte-length/commit/2ea13adc85b7d775d1649ac8e9469ac380cb3665)
- [meta] add missing `engines.node` [`380e96d`](https://github.com/inspect-js/array-buffer-byte-length/commit/380e96d1c91dd579df0261950b46b62d4fed7a23)
- [Deps] update `is-array-buffer` [`cfa7093`](https://github.com/inspect-js/array-buffer-byte-length/commit/cfa7093daaeeccbaa5228a22e6ec32a307d81549)
- [meta] add `sideEffects` flag [`7297ddd`](https://github.com/inspect-js/array-buffer-byte-length/commit/7297dddd40a8f310bb69726a7a6edfae6111b8de)

## v1.0.0 - 2023-02-28

### Commits

- Initial implementation, tests, readme [`2db6cad`](https://github.com/inspect-js/array-buffer-byte-length/commit/2db6cad79270ab1966f5ea80160abbcd4534c91d)
- Initial commit [`b2a0c9c`](https://github.com/inspect-js/array-buffer-byte-length/commit/b2a0c9c2246514b7999d331aad868c4f32326db7)
- npm init [`376acdb`](https://github.com/inspect-js/array-buffer-byte-length/commit/376acdbd4435cb1d4c31d107cacb3b86f2363aee)
- Only apps should have lockfiles [`70cf325`](https://github.com/inspect-js/array-buffer-byte-length/commit/70cf32526fc727d0d16a12d85a4bddea70075e31)
