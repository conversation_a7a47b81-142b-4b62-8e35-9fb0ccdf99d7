{"version": 3, "file": "kill_cursors.js", "sourceRoot": "", "sources": ["../../src/operations/kill_cursors.ts"], "names": [], "mappings": ";;;AAEA,+DAAkE;AAClE,oCAA8D;AAK9D,2CAA8F;AAY9F,MAAa,oBAAqB,SAAQ,6BAAuB;IAI/D,YAAY,QAAc,EAAE,EAAoB,EAAE,MAAc,EAAE,OAAyB;QACzF,KAAK,CAAC,OAAO,CAAC,CAAC;QAJR,iCAA4B,GAAG,2BAAe,CAAC;QAKtD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,IAAa,WAAW;QACtB,OAAO,aAAsB,CAAC;IAChC,CAAC;IAEQ,YAAY,CAAC,WAAuB,EAAE,QAAwB;QACrE,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC;QACvC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,gEAAgE;YAChE,wFAAwF;YACxF,MAAM,IAAI,yBAAiB,CAAC,yDAAyD,CAAC,CAAC;QACzF,CAAC;QAED,MAAM,kBAAkB,GAAuB;YAC7C,WAAW;YACX,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;SACzB,CAAC;QAEF,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAEQ,YAAY,CAAC,cAA8B;QAClD,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,cAAc;SACf,CAAC;IACJ,CAAC;IAEQ,WAAW,CAAC,MAAkB;QACrC,iFAAiF;IACnF,CAAC;CACF;AAzCD,oDAyCC;AAED,IAAA,yBAAa,EAAC,oBAAoB,EAAE,CAAC,kBAAM,CAAC,uBAAuB,CAAC,CAAC,CAAC"}