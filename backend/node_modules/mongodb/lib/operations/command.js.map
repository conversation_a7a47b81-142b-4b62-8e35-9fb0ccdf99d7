{"version": 3, "file": "command.js", "sourceRoot": "", "sources": ["../../src/operations/command.ts"], "names": [], "mappings": ";;;AAEA,+DAAsF;AACtF,oCAAqD;AACrD,wCAKoB;AACpB,kDAA8C;AAK9C,oCAAwF;AACxF,oDAA0E;AAE1E,2CAA+E;AAwE/E,gBAAgB;AAChB,MAAsB,gBAAoB,SAAQ,6BAAoB;IAMpE,YAAY,MAAwB,EAAE,OAAiC;QACrE,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAE7B,kFAAkF;QAClF,kFAAkF;QAClF,qBAAqB;QACrB,MAAM,cAAc,GAAG,OAAO,EAAE,MAAM,IAAI,OAAO,EAAE,MAAM,CAAC;QAC1D,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,EAAE,GAAG,IAAI,wBAAgB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,GAAG,MAAM;gBACd,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC;gBAC3C,CAAC,CAAC,IAAI,wBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,0BAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY,GAAG,4BAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,OAAO,GAAG,iBAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,IAAI,CAAC,OAAO;gBAAE,IAAA,uCAA6B,EAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9E,CAAC;aAAM,IAAI,OAAO,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC;YACpC,MAAM,IAAI,iCAAyB,CAAC,mDAAmD,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAED,IAAa,aAAa;QACxB,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;QAC9B,CAAC;QACD,OAAO,KAAK,CAAC,aAAa,CAAC;IAC7B,CAAC;IAIQ,YAAY,CAAC,cAA8B;QAClD,OAAO;YACL,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,IAAI,CAAC,WAAW;YACnB,cAAc;YACd,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;IACJ,CAAC;IAEQ,YAAY,CAAC,UAAsB,EAAE,OAAuB;QACnE,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAE/D,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAEnE,IAAI,IAAI,CAAC,WAAW,IAAI,IAAA,kCAA0B,EAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9E,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAClF,4BAAY,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,CAAC;QAED,IACE,IAAI,CAAC,OAAO,CAAC,SAAS;YACtB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,QAAQ;YAC1C,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,cAAc,CAAC,EACtC,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YAC/C,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC7C,CAAC;QAED,IACE,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI;YAC5B,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,iBAAiB,CAAC;YACxC,IAAA,sBAAc,EAAC,UAAU,CAAC,IAAI,+CAAmC,EACjE,CAAC;YACD,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACzC,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,OAAO,IAAA,6BAAmB,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA3FD,4CA2FC"}