import type * as grpc from '../index';
import type { MessageTypeDefinition } from '@grpc/proto-loader';

import type { Any as _google_protobuf_Any, Any__Output as _google_protobuf_Any__Output } from './google/protobuf/Any';
import type { BoolValue as _google_protobuf_BoolValue, BoolValue__Output as _google_protobuf_BoolValue__Output } from './google/protobuf/BoolValue';
import type { BytesValue as _google_protobuf_BytesValue, BytesValue__Output as _google_protobuf_BytesValue__Output } from './google/protobuf/BytesValue';
import type { DoubleValue as _google_protobuf_DoubleValue, DoubleValue__Output as _google_protobuf_DoubleValue__Output } from './google/protobuf/DoubleValue';
import type { Duration as _google_protobuf_Duration, Duration__Output as _google_protobuf_Duration__Output } from './google/protobuf/Duration';
import type { FloatValue as _google_protobuf_FloatValue, FloatValue__Output as _google_protobuf_FloatValue__Output } from './google/protobuf/FloatValue';
import type { Int32Value as _google_protobuf_Int32Value, Int32Value__Output as _google_protobuf_Int32Value__Output } from './google/protobuf/Int32Value';
import type { Int64Value as _google_protobuf_Int64Value, Int64Value__Output as _google_protobuf_Int64Value__Output } from './google/protobuf/Int64Value';
import type { StringValue as _google_protobuf_StringValue, StringValue__Output as _google_protobuf_StringValue__Output } from './google/protobuf/StringValue';
import type { Timestamp as _google_protobuf_Timestamp, Timestamp__Output as _google_protobuf_Timestamp__Output } from './google/protobuf/Timestamp';
import type { UInt32Value as _google_protobuf_UInt32Value, UInt32Value__Output as _google_protobuf_UInt32Value__Output } from './google/protobuf/UInt32Value';
import type { UInt64Value as _google_protobuf_UInt64Value, UInt64Value__Output as _google_protobuf_UInt64Value__Output } from './google/protobuf/UInt64Value';
import type { Address as _grpc_channelz_v1_Address, Address__Output as _grpc_channelz_v1_Address__Output } from './grpc/channelz/v1/Address';
import type { Channel as _grpc_channelz_v1_Channel, Channel__Output as _grpc_channelz_v1_Channel__Output } from './grpc/channelz/v1/Channel';
import type { ChannelConnectivityState as _grpc_channelz_v1_ChannelConnectivityState, ChannelConnectivityState__Output as _grpc_channelz_v1_ChannelConnectivityState__Output } from './grpc/channelz/v1/ChannelConnectivityState';
import type { ChannelData as _grpc_channelz_v1_ChannelData, ChannelData__Output as _grpc_channelz_v1_ChannelData__Output } from './grpc/channelz/v1/ChannelData';
import type { ChannelRef as _grpc_channelz_v1_ChannelRef, ChannelRef__Output as _grpc_channelz_v1_ChannelRef__Output } from './grpc/channelz/v1/ChannelRef';
import type { ChannelTrace as _grpc_channelz_v1_ChannelTrace, ChannelTrace__Output as _grpc_channelz_v1_ChannelTrace__Output } from './grpc/channelz/v1/ChannelTrace';
import type { ChannelTraceEvent as _grpc_channelz_v1_ChannelTraceEvent, ChannelTraceEvent__Output as _grpc_channelz_v1_ChannelTraceEvent__Output } from './grpc/channelz/v1/ChannelTraceEvent';
import type { ChannelzClient as _grpc_channelz_v1_ChannelzClient, ChannelzDefinition as _grpc_channelz_v1_ChannelzDefinition } from './grpc/channelz/v1/Channelz';
import type { GetChannelRequest as _grpc_channelz_v1_GetChannelRequest, GetChannelRequest__Output as _grpc_channelz_v1_GetChannelRequest__Output } from './grpc/channelz/v1/GetChannelRequest';
import type { GetChannelResponse as _grpc_channelz_v1_GetChannelResponse, GetChannelResponse__Output as _grpc_channelz_v1_GetChannelResponse__Output } from './grpc/channelz/v1/GetChannelResponse';
import type { GetServerRequest as _grpc_channelz_v1_GetServerRequest, GetServerRequest__Output as _grpc_channelz_v1_GetServerRequest__Output } from './grpc/channelz/v1/GetServerRequest';
import type { GetServerResponse as _grpc_channelz_v1_GetServerResponse, GetServerResponse__Output as _grpc_channelz_v1_GetServerResponse__Output } from './grpc/channelz/v1/GetServerResponse';
import type { GetServerSocketsRequest as _grpc_channelz_v1_GetServerSocketsRequest, GetServerSocketsRequest__Output as _grpc_channelz_v1_GetServerSocketsRequest__Output } from './grpc/channelz/v1/GetServerSocketsRequest';
import type { GetServerSocketsResponse as _grpc_channelz_v1_GetServerSocketsResponse, GetServerSocketsResponse__Output as _grpc_channelz_v1_GetServerSocketsResponse__Output } from './grpc/channelz/v1/GetServerSocketsResponse';
import type { GetServersRequest as _grpc_channelz_v1_GetServersRequest, GetServersRequest__Output as _grpc_channelz_v1_GetServersRequest__Output } from './grpc/channelz/v1/GetServersRequest';
import type { GetServersResponse as _grpc_channelz_v1_GetServersResponse, GetServersResponse__Output as _grpc_channelz_v1_GetServersResponse__Output } from './grpc/channelz/v1/GetServersResponse';
import type { GetSocketRequest as _grpc_channelz_v1_GetSocketRequest, GetSocketRequest__Output as _grpc_channelz_v1_GetSocketRequest__Output } from './grpc/channelz/v1/GetSocketRequest';
import type { GetSocketResponse as _grpc_channelz_v1_GetSocketResponse, GetSocketResponse__Output as _grpc_channelz_v1_GetSocketResponse__Output } from './grpc/channelz/v1/GetSocketResponse';
import type { GetSubchannelRequest as _grpc_channelz_v1_GetSubchannelRequest, GetSubchannelRequest__Output as _grpc_channelz_v1_GetSubchannelRequest__Output } from './grpc/channelz/v1/GetSubchannelRequest';
import type { GetSubchannelResponse as _grpc_channelz_v1_GetSubchannelResponse, GetSubchannelResponse__Output as _grpc_channelz_v1_GetSubchannelResponse__Output } from './grpc/channelz/v1/GetSubchannelResponse';
import type { GetTopChannelsRequest as _grpc_channelz_v1_GetTopChannelsRequest, GetTopChannelsRequest__Output as _grpc_channelz_v1_GetTopChannelsRequest__Output } from './grpc/channelz/v1/GetTopChannelsRequest';
import type { GetTopChannelsResponse as _grpc_channelz_v1_GetTopChannelsResponse, GetTopChannelsResponse__Output as _grpc_channelz_v1_GetTopChannelsResponse__Output } from './grpc/channelz/v1/GetTopChannelsResponse';
import type { Security as _grpc_channelz_v1_Security, Security__Output as _grpc_channelz_v1_Security__Output } from './grpc/channelz/v1/Security';
import type { Server as _grpc_channelz_v1_Server, Server__Output as _grpc_channelz_v1_Server__Output } from './grpc/channelz/v1/Server';
import type { ServerData as _grpc_channelz_v1_ServerData, ServerData__Output as _grpc_channelz_v1_ServerData__Output } from './grpc/channelz/v1/ServerData';
import type { ServerRef as _grpc_channelz_v1_ServerRef, ServerRef__Output as _grpc_channelz_v1_ServerRef__Output } from './grpc/channelz/v1/ServerRef';
import type { Socket as _grpc_channelz_v1_Socket, Socket__Output as _grpc_channelz_v1_Socket__Output } from './grpc/channelz/v1/Socket';
import type { SocketData as _grpc_channelz_v1_SocketData, SocketData__Output as _grpc_channelz_v1_SocketData__Output } from './grpc/channelz/v1/SocketData';
import type { SocketOption as _grpc_channelz_v1_SocketOption, SocketOption__Output as _grpc_channelz_v1_SocketOption__Output } from './grpc/channelz/v1/SocketOption';
import type { SocketOptionLinger as _grpc_channelz_v1_SocketOptionLinger, SocketOptionLinger__Output as _grpc_channelz_v1_SocketOptionLinger__Output } from './grpc/channelz/v1/SocketOptionLinger';
import type { SocketOptionTcpInfo as _grpc_channelz_v1_SocketOptionTcpInfo, SocketOptionTcpInfo__Output as _grpc_channelz_v1_SocketOptionTcpInfo__Output } from './grpc/channelz/v1/SocketOptionTcpInfo';
import type { SocketOptionTimeout as _grpc_channelz_v1_SocketOptionTimeout, SocketOptionTimeout__Output as _grpc_channelz_v1_SocketOptionTimeout__Output } from './grpc/channelz/v1/SocketOptionTimeout';
import type { SocketRef as _grpc_channelz_v1_SocketRef, SocketRef__Output as _grpc_channelz_v1_SocketRef__Output } from './grpc/channelz/v1/SocketRef';
import type { Subchannel as _grpc_channelz_v1_Subchannel, Subchannel__Output as _grpc_channelz_v1_Subchannel__Output } from './grpc/channelz/v1/Subchannel';
import type { SubchannelRef as _grpc_channelz_v1_SubchannelRef, SubchannelRef__Output as _grpc_channelz_v1_SubchannelRef__Output } from './grpc/channelz/v1/SubchannelRef';

type SubtypeConstructor<Constructor extends new (...args: any) => any, Subtype> = {
  new(...args: ConstructorParameters<Constructor>): Subtype;
};

export interface ProtoGrpcType {
  google: {
    protobuf: {
      Any: MessageTypeDefinition<_google_protobuf_Any, _google_protobuf_Any__Output>
      BoolValue: MessageTypeDefinition<_google_protobuf_BoolValue, _google_protobuf_BoolValue__Output>
      BytesValue: MessageTypeDefinition<_google_protobuf_BytesValue, _google_protobuf_BytesValue__Output>
      DoubleValue: MessageTypeDefinition<_google_protobuf_DoubleValue, _google_protobuf_DoubleValue__Output>
      Duration: MessageTypeDefinition<_google_protobuf_Duration, _google_protobuf_Duration__Output>
      FloatValue: MessageTypeDefinition<_google_protobuf_FloatValue, _google_protobuf_FloatValue__Output>
      Int32Value: MessageTypeDefinition<_google_protobuf_Int32Value, _google_protobuf_Int32Value__Output>
      Int64Value: MessageTypeDefinition<_google_protobuf_Int64Value, _google_protobuf_Int64Value__Output>
      StringValue: MessageTypeDefinition<_google_protobuf_StringValue, _google_protobuf_StringValue__Output>
      Timestamp: MessageTypeDefinition<_google_protobuf_Timestamp, _google_protobuf_Timestamp__Output>
      UInt32Value: MessageTypeDefinition<_google_protobuf_UInt32Value, _google_protobuf_UInt32Value__Output>
      UInt64Value: MessageTypeDefinition<_google_protobuf_UInt64Value, _google_protobuf_UInt64Value__Output>
    }
  }
  grpc: {
    channelz: {
      v1: {
        Address: MessageTypeDefinition<_grpc_channelz_v1_Address, _grpc_channelz_v1_Address__Output>
        Channel: MessageTypeDefinition<_grpc_channelz_v1_Channel, _grpc_channelz_v1_Channel__Output>
        ChannelConnectivityState: MessageTypeDefinition<_grpc_channelz_v1_ChannelConnectivityState, _grpc_channelz_v1_ChannelConnectivityState__Output>
        ChannelData: MessageTypeDefinition<_grpc_channelz_v1_ChannelData, _grpc_channelz_v1_ChannelData__Output>
        ChannelRef: MessageTypeDefinition<_grpc_channelz_v1_ChannelRef, _grpc_channelz_v1_ChannelRef__Output>
        ChannelTrace: MessageTypeDefinition<_grpc_channelz_v1_ChannelTrace, _grpc_channelz_v1_ChannelTrace__Output>
        ChannelTraceEvent: MessageTypeDefinition<_grpc_channelz_v1_ChannelTraceEvent, _grpc_channelz_v1_ChannelTraceEvent__Output>
        /**
         * Channelz is a service exposed by gRPC servers that provides detailed debug
         * information.
         */
        Channelz: SubtypeConstructor<typeof grpc.Client, _grpc_channelz_v1_ChannelzClient> & { service: _grpc_channelz_v1_ChannelzDefinition }
        GetChannelRequest: MessageTypeDefinition<_grpc_channelz_v1_GetChannelRequest, _grpc_channelz_v1_GetChannelRequest__Output>
        GetChannelResponse: MessageTypeDefinition<_grpc_channelz_v1_GetChannelResponse, _grpc_channelz_v1_GetChannelResponse__Output>
        GetServerRequest: MessageTypeDefinition<_grpc_channelz_v1_GetServerRequest, _grpc_channelz_v1_GetServerRequest__Output>
        GetServerResponse: MessageTypeDefinition<_grpc_channelz_v1_GetServerResponse, _grpc_channelz_v1_GetServerResponse__Output>
        GetServerSocketsRequest: MessageTypeDefinition<_grpc_channelz_v1_GetServerSocketsRequest, _grpc_channelz_v1_GetServerSocketsRequest__Output>
        GetServerSocketsResponse: MessageTypeDefinition<_grpc_channelz_v1_GetServerSocketsResponse, _grpc_channelz_v1_GetServerSocketsResponse__Output>
        GetServersRequest: MessageTypeDefinition<_grpc_channelz_v1_GetServersRequest, _grpc_channelz_v1_GetServersRequest__Output>
        GetServersResponse: MessageTypeDefinition<_grpc_channelz_v1_GetServersResponse, _grpc_channelz_v1_GetServersResponse__Output>
        GetSocketRequest: MessageTypeDefinition<_grpc_channelz_v1_GetSocketRequest, _grpc_channelz_v1_GetSocketRequest__Output>
        GetSocketResponse: MessageTypeDefinition<_grpc_channelz_v1_GetSocketResponse, _grpc_channelz_v1_GetSocketResponse__Output>
        GetSubchannelRequest: MessageTypeDefinition<_grpc_channelz_v1_GetSubchannelRequest, _grpc_channelz_v1_GetSubchannelRequest__Output>
        GetSubchannelResponse: MessageTypeDefinition<_grpc_channelz_v1_GetSubchannelResponse, _grpc_channelz_v1_GetSubchannelResponse__Output>
        GetTopChannelsRequest: MessageTypeDefinition<_grpc_channelz_v1_GetTopChannelsRequest, _grpc_channelz_v1_GetTopChannelsRequest__Output>
        GetTopChannelsResponse: MessageTypeDefinition<_grpc_channelz_v1_GetTopChannelsResponse, _grpc_channelz_v1_GetTopChannelsResponse__Output>
        Security: MessageTypeDefinition<_grpc_channelz_v1_Security, _grpc_channelz_v1_Security__Output>
        Server: MessageTypeDefinition<_grpc_channelz_v1_Server, _grpc_channelz_v1_Server__Output>
        ServerData: MessageTypeDefinition<_grpc_channelz_v1_ServerData, _grpc_channelz_v1_ServerData__Output>
        ServerRef: MessageTypeDefinition<_grpc_channelz_v1_ServerRef, _grpc_channelz_v1_ServerRef__Output>
        Socket: MessageTypeDefinition<_grpc_channelz_v1_Socket, _grpc_channelz_v1_Socket__Output>
        SocketData: MessageTypeDefinition<_grpc_channelz_v1_SocketData, _grpc_channelz_v1_SocketData__Output>
        SocketOption: MessageTypeDefinition<_grpc_channelz_v1_SocketOption, _grpc_channelz_v1_SocketOption__Output>
        SocketOptionLinger: MessageTypeDefinition<_grpc_channelz_v1_SocketOptionLinger, _grpc_channelz_v1_SocketOptionLinger__Output>
        SocketOptionTcpInfo: MessageTypeDefinition<_grpc_channelz_v1_SocketOptionTcpInfo, _grpc_channelz_v1_SocketOptionTcpInfo__Output>
        SocketOptionTimeout: MessageTypeDefinition<_grpc_channelz_v1_SocketOptionTimeout, _grpc_channelz_v1_SocketOptionTimeout__Output>
        SocketRef: MessageTypeDefinition<_grpc_channelz_v1_SocketRef, _grpc_channelz_v1_SocketRef__Output>
        Subchannel: MessageTypeDefinition<_grpc_channelz_v1_Subchannel, _grpc_channelz_v1_Subchannel__Output>
        SubchannelRef: MessageTypeDefinition<_grpc_channelz_v1_SubchannelRef, _grpc_channelz_v1_SubchannelRef__Output>
      }
    }
  }
}

