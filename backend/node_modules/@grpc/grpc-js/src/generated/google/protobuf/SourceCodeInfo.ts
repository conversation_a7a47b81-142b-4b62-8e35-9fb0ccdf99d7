// Original file: null


export interface _google_protobuf_SourceCodeInfo_Location {
  'path'?: (number)[];
  'span'?: (number)[];
  'leadingComments'?: (string);
  'trailingComments'?: (string);
  'leadingDetachedComments'?: (string)[];
}

export interface _google_protobuf_SourceCodeInfo_Location__Output {
  'path': (number)[];
  'span': (number)[];
  'leadingComments': (string);
  'trailingComments': (string);
  'leadingDetachedComments': (string)[];
}

export interface SourceCodeInfo {
  'location'?: (_google_protobuf_SourceCodeInfo_Location)[];
}

export interface SourceCodeInfo__Output {
  'location': (_google_protobuf_SourceCodeInfo_Location__Output)[];
}
