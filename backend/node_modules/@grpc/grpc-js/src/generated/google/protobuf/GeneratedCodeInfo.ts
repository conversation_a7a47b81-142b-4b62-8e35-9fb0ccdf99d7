// Original file: null


export interface _google_protobuf_GeneratedCodeInfo_Annotation {
  'path'?: (number)[];
  'sourceFile'?: (string);
  'begin'?: (number);
  'end'?: (number);
  'semantic'?: (_google_protobuf_GeneratedCodeInfo_Annotation_Semantic);
}

export interface _google_protobuf_GeneratedCodeInfo_Annotation__Output {
  'path': (number)[];
  'sourceFile': (string);
  'begin': (number);
  'end': (number);
  'semantic': (_google_protobuf_GeneratedCodeInfo_Annotation_Semantic__Output);
}

// Original file: null

export const _google_protobuf_GeneratedCodeInfo_Annotation_Semantic = {
  NONE: 'NONE',
  SET: 'SET',
  ALIAS: 'ALIAS',
} as const;

export type _google_protobuf_GeneratedCodeInfo_Annotation_Semantic =
  | 'NONE'
  | 0
  | 'SET'
  | 1
  | 'ALIAS'
  | 2

export type _google_protobuf_GeneratedCodeInfo_Annotation_Semantic__Output = typeof _google_protobuf_GeneratedCodeInfo_Annotation_Semantic[keyof typeof _google_protobuf_GeneratedCodeInfo_Annotation_Semantic]

export interface GeneratedCodeInfo {
  'annotation'?: (_google_protobuf_GeneratedCodeInfo_Annotation)[];
}

export interface GeneratedCodeInfo__Output {
  'annotation': (_google_protobuf_GeneratedCodeInfo_Annotation__Output)[];
}
