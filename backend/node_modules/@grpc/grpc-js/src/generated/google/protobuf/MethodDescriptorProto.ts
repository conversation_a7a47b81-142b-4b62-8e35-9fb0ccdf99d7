// Original file: null

import type { MethodOptions as _google_protobuf_MethodOptions, MethodOptions__Output as _google_protobuf_MethodOptions__Output } from '../../google/protobuf/MethodOptions';

export interface MethodDescriptorProto {
  'name'?: (string);
  'inputType'?: (string);
  'outputType'?: (string);
  'options'?: (_google_protobuf_MethodOptions | null);
  'clientStreaming'?: (boolean);
  'serverStreaming'?: (boolean);
}

export interface MethodDescriptorProto__Output {
  'name': (string);
  'inputType': (string);
  'outputType': (string);
  'options': (_google_protobuf_MethodOptions__Output | null);
  'clientStreaming': (boolean);
  'serverStreaming': (boolean);
}
