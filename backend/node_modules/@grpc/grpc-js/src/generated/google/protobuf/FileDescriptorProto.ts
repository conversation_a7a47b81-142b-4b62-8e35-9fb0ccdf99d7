// Original file: null

import type { DescriptorProto as _google_protobuf_DescriptorProto, DescriptorProto__Output as _google_protobuf_DescriptorProto__Output } from '../../google/protobuf/DescriptorProto';
import type { EnumDescriptorProto as _google_protobuf_EnumDescriptorProto, EnumDescriptorProto__Output as _google_protobuf_EnumDescriptorProto__Output } from '../../google/protobuf/EnumDescriptorProto';
import type { ServiceDescriptorProto as _google_protobuf_ServiceDescriptorProto, ServiceDescriptorProto__Output as _google_protobuf_ServiceDescriptorProto__Output } from '../../google/protobuf/ServiceDescriptorProto';
import type { FieldDescriptorProto as _google_protobuf_FieldDescriptorProto, FieldDescriptorProto__Output as _google_protobuf_FieldDescriptorProto__Output } from '../../google/protobuf/FieldDescriptorProto';
import type { FileOptions as _google_protobuf_FileOptions, FileOptions__Output as _google_protobuf_FileOptions__Output } from '../../google/protobuf/FileOptions';
import type { SourceCodeInfo as _google_protobuf_SourceCodeInfo, SourceCodeInfo__Output as _google_protobuf_SourceCodeInfo__Output } from '../../google/protobuf/SourceCodeInfo';
import type { Edition as _google_protobuf_Edition, Edition__Output as _google_protobuf_Edition__Output } from '../../google/protobuf/Edition';

export interface FileDescriptorProto {
  'name'?: (string);
  'package'?: (string);
  'dependency'?: (string)[];
  'messageType'?: (_google_protobuf_DescriptorProto)[];
  'enumType'?: (_google_protobuf_EnumDescriptorProto)[];
  'service'?: (_google_protobuf_ServiceDescriptorProto)[];
  'extension'?: (_google_protobuf_FieldDescriptorProto)[];
  'options'?: (_google_protobuf_FileOptions | null);
  'sourceCodeInfo'?: (_google_protobuf_SourceCodeInfo | null);
  'publicDependency'?: (number)[];
  'weakDependency'?: (number)[];
  'syntax'?: (string);
  'edition'?: (_google_protobuf_Edition);
  'optionDependency'?: (string)[];
}

export interface FileDescriptorProto__Output {
  'name': (string);
  'package': (string);
  'dependency': (string)[];
  'messageType': (_google_protobuf_DescriptorProto__Output)[];
  'enumType': (_google_protobuf_EnumDescriptorProto__Output)[];
  'service': (_google_protobuf_ServiceDescriptorProto__Output)[];
  'extension': (_google_protobuf_FieldDescriptorProto__Output)[];
  'options': (_google_protobuf_FileOptions__Output | null);
  'sourceCodeInfo': (_google_protobuf_SourceCodeInfo__Output | null);
  'publicDependency': (number)[];
  'weakDependency': (number)[];
  'syntax': (string);
  'edition': (_google_protobuf_Edition__Output);
  'optionDependency': (string)[];
}
