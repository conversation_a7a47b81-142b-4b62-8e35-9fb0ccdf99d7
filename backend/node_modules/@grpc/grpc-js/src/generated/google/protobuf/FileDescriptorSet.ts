// Original file: null

import type { FileDescriptorProto as _google_protobuf_FileDescriptorProto, FileDescriptorProto__Output as _google_protobuf_FileDescriptorProto__Output } from '../../google/protobuf/FileDescriptorProto';

export interface FileDescriptorSet {
  'file'?: (_google_protobuf_FileDescriptorProto)[];
}

export interface FileDescriptorSet__Output {
  'file': (_google_protobuf_FileDescriptorProto__Output)[];
}
