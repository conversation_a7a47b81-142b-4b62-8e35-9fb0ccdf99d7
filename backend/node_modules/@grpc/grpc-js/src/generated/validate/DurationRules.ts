// Original file: proto/protoc-gen-validate/validate/validate.proto

import type { Duration as _google_protobuf_Duration, Duration__Output as _google_protobuf_Duration__Output } from '../google/protobuf/Duration';

/**
 * DurationRules describe the constraints applied exclusively to the
 * `google.protobuf.Duration` well-known type
 */
export interface DurationRules {
  /**
   * Required specifies that this field must be set
   */
  'required'?: (boolean);
  /**
   * Const specifies that this field must be exactly the specified value
   */
  'const'?: (_google_protobuf_Duration | null);
  /**
   * Lt specifies that this field must be less than the specified value,
   * exclusive
   */
  'lt'?: (_google_protobuf_Duration | null);
  /**
   * Lt specifies that this field must be less than the specified value,
   * inclusive
   */
  'lte'?: (_google_protobuf_Duration | null);
  /**
   * Gt specifies that this field must be greater than the specified value,
   * exclusive
   */
  'gt'?: (_google_protobuf_Duration | null);
  /**
   * G<PERSON> specifies that this field must be greater than the specified value,
   * inclusive
   */
  'gte'?: (_google_protobuf_Duration | null);
  /**
   * In specifies that this field must be equal to one of the specified
   * values
   */
  'in'?: (_google_protobuf_Duration)[];
  /**
   * NotIn specifies that this field cannot be equal to one of the specified
   * values
   */
  'not_in'?: (_google_protobuf_Duration)[];
}

/**
 * DurationRules describe the constraints applied exclusively to the
 * `google.protobuf.Duration` well-known type
 */
export interface DurationRules__Output {
  /**
   * Required specifies that this field must be set
   */
  'required': (boolean);
  /**
   * Const specifies that this field must be exactly the specified value
   */
  'const': (_google_protobuf_Duration__Output | null);
  /**
   * Lt specifies that this field must be less than the specified value,
   * exclusive
   */
  'lt': (_google_protobuf_Duration__Output | null);
  /**
   * Lt specifies that this field must be less than the specified value,
   * inclusive
   */
  'lte': (_google_protobuf_Duration__Output | null);
  /**
   * Gt specifies that this field must be greater than the specified value,
   * exclusive
   */
  'gt': (_google_protobuf_Duration__Output | null);
  /**
   * Gte specifies that this field must be greater than the specified value,
   * inclusive
   */
  'gte': (_google_protobuf_Duration__Output | null);
  /**
   * In specifies that this field must be equal to one of the specified
   * values
   */
  'in': (_google_protobuf_Duration__Output)[];
  /**
   * NotIn specifies that this field cannot be equal to one of the specified
   * values
   */
  'not_in': (_google_protobuf_Duration__Output)[];
}
