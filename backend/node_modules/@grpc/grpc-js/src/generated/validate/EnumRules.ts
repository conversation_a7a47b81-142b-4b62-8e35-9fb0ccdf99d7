// Original file: proto/protoc-gen-validate/validate/validate.proto


/**
 * EnumRules describe the constraints applied to enum values
 */
export interface EnumRules {
  /**
   * Const specifies that this field must be exactly the specified value
   */
  'const'?: (number);
  /**
   * DefinedOnly specifies that this field must be only one of the defined
   * values for this enum, failing on any undefined value.
   */
  'defined_only'?: (boolean);
  /**
   * In specifies that this field must be equal to one of the specified
   * values
   */
  'in'?: (number)[];
  /**
   * NotIn specifies that this field cannot be equal to one of the specified
   * values
   */
  'not_in'?: (number)[];
}

/**
 * EnumRules describe the constraints applied to enum values
 */
export interface EnumRules__Output {
  /**
   * Const specifies that this field must be exactly the specified value
   */
  'const': (number);
  /**
   * DefinedOnly specifies that this field must be only one of the defined
   * values for this enum, failing on any undefined value.
   */
  'defined_only': (boolean);
  /**
   * In specifies that this field must be equal to one of the specified
   * values
   */
  'in': (number)[];
  /**
   * NotIn specifies that this field cannot be equal to one of the specified
   * values
   */
  'not_in': (number)[];
}
