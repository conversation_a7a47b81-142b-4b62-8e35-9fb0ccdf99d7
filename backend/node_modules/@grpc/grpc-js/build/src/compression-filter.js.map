{"version": 3, "file": "compression-filter.js", "sourceRoot": "", "sources": ["../../src/compression-filter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,6BAA6B;AAK7B,qEAAiE;AACjE,2CAAwH;AACxH,qCAA6D;AAC7D,qCAAqC;AAGrC,MAAM,yBAAyB,GAAG,CAChC,GAAW,EACmB,EAAE;IAChC,OAAO,CACL,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,8CAAqB,CAAC,GAAG,CAAC,KAAK,QAAQ,CAC1E,CAAC;AACJ,CAAC,CAAC;AAQF,MAAe,kBAAkB;IAG/B;;;;OAIG;IACH,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,QAAiB;QACnD,IAAI,aAAa,GAAG,OAAO,CAAC;QAC5B,IAAI,QAAQ,EAAE,CAAC;YACb,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QAC5D,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5D,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC9C,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC9B,OAAO,MAAM,CAAC;IAChB,CAAC;IACD;;;OAGG;IACH,KAAK,CAAC,WAAW,CAAC,IAAY;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,aAAa,GAA4B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,UAAU,EAAE,CAAC;YACf,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AAED,MAAM,eAAgB,SAAQ,kBAAkB;IAC9C,KAAK,CAAC,eAAe,CAAC,OAAe;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,QAAiB;QACnD,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACtD;0BACkB;QAClB,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxB,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACxC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACxB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,iBAAiB,CAAC,OAAe;QAC/B,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CACP,qEAAqE,CACtE,CACF,CAAC;IACJ,CAAC;CACF;AAED,MAAM,cAAe,SAAQ,kBAAkB;IAC7C,YAAoB,oBAA4B;QAC9C,KAAK,EAAE,CAAC;QADU,yBAAoB,GAApB,oBAAoB,CAAQ;IAEhD,CAAC;IAED,eAAe,CAAC,OAAe;QAC7B,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACpC,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB,CAAC,OAAe;QAC/B,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,MAAM,YAAY,GAAa,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC1C,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE;gBACxC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACzB,WAAW,IAAI,KAAK,CAAC,UAAU,CAAC;gBAChC,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAChF,YAAY,CAAC,OAAO,EAAE,CAAC;oBACvB,MAAM,CAAC;wBACL,IAAI,EAAE,kBAAM,CAAC,kBAAkB;wBAC/B,OAAO,EAAE,4DAA4D,IAAI,CAAC,oBAAoB,EAAE;qBACjG,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YACH,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBAC1B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;YACH,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5B,YAAY,CAAC,GAAG,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,WAAY,SAAQ,kBAAkB;IAC1C,YAAoB,oBAA4B;QAC9C,KAAK,EAAE,CAAC;QADU,yBAAoB,GAApB,oBAAoB,CAAQ;IAEhD,CAAC;IAED,eAAe,CAAC,OAAe;QAC7B,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACjC,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB,CAAC,OAAe;QAC/B,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,MAAM,YAAY,GAAa,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE;gBACxC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACzB,WAAW,IAAI,KAAK,CAAC,UAAU,CAAC;gBAChC,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAChF,YAAY,CAAC,OAAO,EAAE,CAAC;oBACvB,MAAM,CAAC;wBACL,IAAI,EAAE,kBAAM,CAAC,kBAAkB;wBAC/B,OAAO,EAAE,4DAA4D,IAAI,CAAC,oBAAoB,EAAE;qBACjG,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YACH,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBAC1B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;YACH,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5B,YAAY,CAAC,GAAG,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,cAAe,SAAQ,kBAAkB;IAC7C,YAA6B,eAAuB;QAClD,KAAK,EAAE,CAAC;QADmB,oBAAe,GAAf,eAAe,CAAQ;IAEpD,CAAC;IACD,eAAe,CAAC,OAAe;QAC7B,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CACP,mEAAmE,IAAI,CAAC,eAAe,EAAE,CAC1F,CACF,CAAC;IACJ,CAAC;IAED,iBAAiB,CAAC,OAAe;QAC/B,6BAA6B;QAC7B,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CAAC,qCAAqC,IAAI,CAAC,eAAe,EAAE,CAAC,CACvE,CAAC;IACJ,CAAC;CACF;AAED,SAAS,qBAAqB,CAAC,eAAuB,EAAE,qBAA6B;IACnF,QAAQ,eAAe,EAAE,CAAC;QACxB,KAAK,UAAU;YACb,OAAO,IAAI,eAAe,EAAE,CAAC;QAC/B,KAAK,SAAS;YACZ,OAAO,IAAI,cAAc,CAAC,qBAAqB,CAAC,CAAC;QACnD,KAAK,MAAM;YACT,OAAO,IAAI,WAAW,CAAC,qBAAqB,CAAC,CAAC;QAChD;YACE,OAAO,IAAI,cAAc,CAAC,eAAe,CAAC,CAAC;IAC/C,CAAC;AACH,CAAC;AAED,MAAa,iBAAkB,SAAQ,mBAAU;IAO/C,YACE,cAA8B,EACtB,kBAAiD;;QAEzD,KAAK,EAAE,CAAC;QAFA,uBAAkB,GAAlB,kBAAkB,CAA+B;QARnD,oBAAe,GAAuB,IAAI,eAAe,EAAE,CAAC;QAC5D,uBAAkB,GAAuB,IAAI,eAAe,EAAE,CAAC;QAC/D,gCAA2B,GAAyB,UAAU,CAAC;QAUrE,MAAM,uBAAuB,GAC3B,cAAc,CAAC,oCAAoC,CAAC,CAAC;QACvD,IAAI,CAAC,uBAAuB,GAAG,MAAA,cAAc,CAAC,iCAAiC,CAAC,mCAAI,8CAAkC,CAAC;QACvH,IAAI,CAAC,oBAAoB,GAAG,MAAA,cAAc,CAAC,8BAA8B,CAAC,mCAAI,2CAA+B,CAAC;QAC9G,IAAI,uBAAuB,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,yBAAyB,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACvD,MAAM,sBAAsB,GAAG,8CAAqB,CAClD,uBAAuB,CACA,CAAC;gBAC1B,MAAM,wBAAwB,GAC5B,MAAA,kBAAkB,CAAC,6BAA6B,0CAAE,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC/D;;;;;;mBAMG;gBACH,IACE,CAAC,wBAAwB;oBACzB,wBAAwB,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EACzD,CAAC;oBACD,IAAI,CAAC,2BAA2B,GAAG,sBAAsB,CAAC;oBAC1D,IAAI,CAAC,eAAe,GAAG,qBAAqB,CAC1C,IAAI,CAAC,2BAA2B,EAChC,CAAC,CAAC,CACH,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CACT,wBAAY,CAAC,KAAK,EAClB,yEAAyE,uBAAuB,EAAE,CACnG,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAA2B;QAC5C,MAAM,OAAO,GAAa,MAAM,QAAQ,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,uBAAuB,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;QAE3C,6FAA6F;QAC7F,IAAI,IAAI,CAAC,2BAA2B,KAAK,UAAU,EAAE,CAAC;YACpD,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,eAAe,CAAC,QAAkB;QAChC,MAAM,eAAe,GAAoB,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACvE,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAkB,eAAe,CAAC,CAAC,CAAC,CAAC;YACnD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,CAAC,kBAAkB,GAAG,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC1F,CAAC;QACH,CAAC;QACD,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAEjC;kGAC0F;QAC1F,MAAM,8BAA8B,GAAG,QAAQ,CAAC,GAAG,CACjD,sBAAsB,CACvB,CAAC,CAAC,CAAuB,CAAC;QAC3B,IAAI,8BAA8B,EAAE,CAAC;YACnC,IAAI,CAAC,kBAAkB,CAAC,6BAA6B;gBACnD,8BAA8B,CAAC;YACjC,MAAM,wBAAwB,GAC5B,8BAA8B,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAE5C,IACE,CAAC,wBAAwB,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,EACpE,CAAC;gBACD,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;gBAC7C,IAAI,CAAC,2BAA2B,GAAG,UAAU,CAAC;YAChD,CAAC;QACH,CAAC;QACD,QAAQ,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QACxC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAA6B;;QAC7C;;6EAEqE;QACrE,MAAM,eAAe,GAAgB,MAAM,OAAO,CAAC;QACnD,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACnG,MAAM;gBACJ,IAAI,EAAE,kBAAM,CAAC,kBAAkB;gBAC/B,OAAO,EAAE,qDAAqD,IAAI,CAAC,oBAAoB,EAAE;aAC1F,CAAC;QACJ,CAAC;QACD,IAAI,QAAiB,CAAC;QACtB,IAAI,IAAI,CAAC,eAAe,YAAY,eAAe,EAAE,CAAC;YACpD,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,CAAC,CAAC,MAAA,eAAe,CAAC,KAAK,mCAAI,CAAC,CAAC,gCAAwB,CAAC,KAAK,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAC9C,eAAe,CAAC,OAAO,EACvB,QAAQ,CACT;YACD,KAAK,EAAE,eAAe,CAAC,KAAK;SAC7B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAAwB;QAC3C;;;2BAGmB;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,CAAC;IAC5D,CAAC;CACF;AAnID,8CAmIC;AAED,MAAa,wBAAwB;IAInC,YAAY,OAAgB,EAAmB,OAAuB;QAAvB,YAAO,GAAP,OAAO,CAAgB;QAD9D,uBAAkB,GAAkC,EAAE,CAAC;IACU,CAAC;IAC1E,YAAY;QACV,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACtE,CAAC;CACF;AARD,4DAQC"}