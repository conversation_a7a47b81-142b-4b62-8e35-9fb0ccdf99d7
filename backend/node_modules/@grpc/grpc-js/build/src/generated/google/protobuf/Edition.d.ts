export declare const Edition: {
    readonly EDITION_UNKNOWN: "EDITION_UNKNOWN";
    readonly EDITION_LEGACY: "EDITION_LEGACY";
    readonly EDITION_PROTO2: "EDITION_PROTO2";
    readonly EDITION_PROTO3: "EDITION_PROTO3";
    readonly EDITION_2023: "EDITION_2023";
    readonly EDITION_2024: "EDITION_2024";
    readonly EDITION_1_TEST_ONLY: "EDITION_1_TEST_ONLY";
    readonly EDITION_2_TEST_ONLY: "EDITION_2_TEST_ONLY";
    readonly EDITION_99997_TEST_ONLY: "EDITION_99997_TEST_ONLY";
    readonly EDITION_99998_TEST_ONLY: "EDITION_99998_TEST_ONLY";
    readonly EDITION_99999_TEST_ONLY: "EDITION_99999_TEST_ONLY";
    readonly EDITION_MAX: "EDITION_MAX";
};
export type Edition = 'EDITION_UNKNOWN' | 0 | 'EDITION_LEGACY' | 900 | 'EDITION_PROTO2' | 998 | 'EDITION_PROTO3' | 999 | 'EDITION_2023' | 1000 | 'EDITION_2024' | 1001 | 'EDITION_1_TEST_ONLY' | 1 | 'EDITION_2_TEST_ONLY' | 2 | 'EDITION_99997_TEST_ONLY' | 99997 | 'EDITION_99998_TEST_ONLY' | 99998 | 'EDITION_99999_TEST_ONLY' | 99999 | 'EDITION_MAX' | 2147483647;
export type Edition__Output = typeof Edition[keyof typeof Edition];
