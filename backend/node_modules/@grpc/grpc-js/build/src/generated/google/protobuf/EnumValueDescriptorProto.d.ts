import type { EnumValueOptions as _google_protobuf_EnumValueOptions, EnumValueOptions__Output as _google_protobuf_EnumValueOptions__Output } from '../../google/protobuf/EnumValueOptions';
export interface EnumValueDescriptorProto {
    'name'?: (string);
    'number'?: (number);
    'options'?: (_google_protobuf_EnumValueOptions | null);
}
export interface EnumValueDescriptorProto__Output {
    'name': (string);
    'number': (number);
    'options': (_google_protobuf_EnumValueOptions__Output | null);
}
