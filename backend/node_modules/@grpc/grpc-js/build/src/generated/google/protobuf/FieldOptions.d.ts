import type { FeatureSet as _google_protobuf_FeatureSet, FeatureSet__Output as _google_protobuf_FeatureSet__Output } from '../../google/protobuf/FeatureSet';
import type { UninterpretedOption as _google_protobuf_UninterpretedOption, UninterpretedOption__Output as _google_protobuf_UninterpretedOption__Output } from '../../google/protobuf/UninterpretedOption';
import type { FieldRules as _validate_FieldRules, FieldRules__Output as _validate_FieldRules__Output } from '../../validate/FieldRules';
import type { Edition as _google_protobuf_Edition, Edition__Output as _google_protobuf_Edition__Output } from '../../google/protobuf/Edition';
export declare const _google_protobuf_FieldOptions_CType: {
    readonly STRING: "STRING";
    readonly CORD: "CORD";
    readonly STRING_PIECE: "STRING_PIECE";
};
export type _google_protobuf_FieldOptions_CType = 'STRING' | 0 | 'CORD' | 1 | 'STRING_PIECE' | 2;
export type _google_protobuf_FieldOptions_CType__Output = typeof _google_protobuf_FieldOptions_CType[keyof typeof _google_protobuf_FieldOptions_CType];
export interface _google_protobuf_FieldOptions_EditionDefault {
    'edition'?: (_google_protobuf_Edition);
    'value'?: (string);
}
export interface _google_protobuf_FieldOptions_EditionDefault__Output {
    'edition': (_google_protobuf_Edition__Output);
    'value': (string);
}
export interface _google_protobuf_FieldOptions_FeatureSupport {
    'editionIntroduced'?: (_google_protobuf_Edition);
    'editionDeprecated'?: (_google_protobuf_Edition);
    'deprecationWarning'?: (string);
    'editionRemoved'?: (_google_protobuf_Edition);
}
export interface _google_protobuf_FieldOptions_FeatureSupport__Output {
    'editionIntroduced': (_google_protobuf_Edition__Output);
    'editionDeprecated': (_google_protobuf_Edition__Output);
    'deprecationWarning': (string);
    'editionRemoved': (_google_protobuf_Edition__Output);
}
export declare const _google_protobuf_FieldOptions_JSType: {
    readonly JS_NORMAL: "JS_NORMAL";
    readonly JS_STRING: "JS_STRING";
    readonly JS_NUMBER: "JS_NUMBER";
};
export type _google_protobuf_FieldOptions_JSType = 'JS_NORMAL' | 0 | 'JS_STRING' | 1 | 'JS_NUMBER' | 2;
export type _google_protobuf_FieldOptions_JSType__Output = typeof _google_protobuf_FieldOptions_JSType[keyof typeof _google_protobuf_FieldOptions_JSType];
export declare const _google_protobuf_FieldOptions_OptionRetention: {
    readonly RETENTION_UNKNOWN: "RETENTION_UNKNOWN";
    readonly RETENTION_RUNTIME: "RETENTION_RUNTIME";
    readonly RETENTION_SOURCE: "RETENTION_SOURCE";
};
export type _google_protobuf_FieldOptions_OptionRetention = 'RETENTION_UNKNOWN' | 0 | 'RETENTION_RUNTIME' | 1 | 'RETENTION_SOURCE' | 2;
export type _google_protobuf_FieldOptions_OptionRetention__Output = typeof _google_protobuf_FieldOptions_OptionRetention[keyof typeof _google_protobuf_FieldOptions_OptionRetention];
export declare const _google_protobuf_FieldOptions_OptionTargetType: {
    readonly TARGET_TYPE_UNKNOWN: "TARGET_TYPE_UNKNOWN";
    readonly TARGET_TYPE_FILE: "TARGET_TYPE_FILE";
    readonly TARGET_TYPE_EXTENSION_RANGE: "TARGET_TYPE_EXTENSION_RANGE";
    readonly TARGET_TYPE_MESSAGE: "TARGET_TYPE_MESSAGE";
    readonly TARGET_TYPE_FIELD: "TARGET_TYPE_FIELD";
    readonly TARGET_TYPE_ONEOF: "TARGET_TYPE_ONEOF";
    readonly TARGET_TYPE_ENUM: "TARGET_TYPE_ENUM";
    readonly TARGET_TYPE_ENUM_ENTRY: "TARGET_TYPE_ENUM_ENTRY";
    readonly TARGET_TYPE_SERVICE: "TARGET_TYPE_SERVICE";
    readonly TARGET_TYPE_METHOD: "TARGET_TYPE_METHOD";
};
export type _google_protobuf_FieldOptions_OptionTargetType = 'TARGET_TYPE_UNKNOWN' | 0 | 'TARGET_TYPE_FILE' | 1 | 'TARGET_TYPE_EXTENSION_RANGE' | 2 | 'TARGET_TYPE_MESSAGE' | 3 | 'TARGET_TYPE_FIELD' | 4 | 'TARGET_TYPE_ONEOF' | 5 | 'TARGET_TYPE_ENUM' | 6 | 'TARGET_TYPE_ENUM_ENTRY' | 7 | 'TARGET_TYPE_SERVICE' | 8 | 'TARGET_TYPE_METHOD' | 9;
export type _google_protobuf_FieldOptions_OptionTargetType__Output = typeof _google_protobuf_FieldOptions_OptionTargetType[keyof typeof _google_protobuf_FieldOptions_OptionTargetType];
export interface FieldOptions {
    'ctype'?: (_google_protobuf_FieldOptions_CType);
    'packed'?: (boolean);
    'deprecated'?: (boolean);
    'lazy'?: (boolean);
    'jstype'?: (_google_protobuf_FieldOptions_JSType);
    /**
     * @deprecated
     */
    'weak'?: (boolean);
    'unverifiedLazy'?: (boolean);
    'debugRedact'?: (boolean);
    'retention'?: (_google_protobuf_FieldOptions_OptionRetention);
    'targets'?: (_google_protobuf_FieldOptions_OptionTargetType)[];
    'editionDefaults'?: (_google_protobuf_FieldOptions_EditionDefault)[];
    'features'?: (_google_protobuf_FeatureSet | null);
    'featureSupport'?: (_google_protobuf_FieldOptions_FeatureSupport | null);
    'uninterpretedOption'?: (_google_protobuf_UninterpretedOption)[];
    '.validate.rules'?: (_validate_FieldRules | null);
}
export interface FieldOptions__Output {
    'ctype': (_google_protobuf_FieldOptions_CType__Output);
    'packed': (boolean);
    'deprecated': (boolean);
    'lazy': (boolean);
    'jstype': (_google_protobuf_FieldOptions_JSType__Output);
    /**
     * @deprecated
     */
    'weak': (boolean);
    'unverifiedLazy': (boolean);
    'debugRedact': (boolean);
    'retention': (_google_protobuf_FieldOptions_OptionRetention__Output);
    'targets': (_google_protobuf_FieldOptions_OptionTargetType__Output)[];
    'editionDefaults': (_google_protobuf_FieldOptions_EditionDefault__Output)[];
    'features': (_google_protobuf_FeatureSet__Output | null);
    'featureSupport': (_google_protobuf_FieldOptions_FeatureSupport__Output | null);
    'uninterpretedOption': (_google_protobuf_UninterpretedOption__Output)[];
    '.validate.rules': (_validate_FieldRules__Output | null);
}
