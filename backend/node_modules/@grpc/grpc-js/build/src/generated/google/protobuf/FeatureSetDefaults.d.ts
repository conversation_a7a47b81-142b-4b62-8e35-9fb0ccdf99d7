import type { Edition as _google_protobuf_Edition, Edition__Output as _google_protobuf_Edition__Output } from '../../google/protobuf/Edition';
import type { FeatureSet as _google_protobuf_FeatureSet, FeatureSet__Output as _google_protobuf_FeatureSet__Output } from '../../google/protobuf/FeatureSet';
export interface _google_protobuf_FeatureSetDefaults_FeatureSetEditionDefault {
    'edition'?: (_google_protobuf_Edition);
    'overridableFeatures'?: (_google_protobuf_FeatureSet | null);
    'fixedFeatures'?: (_google_protobuf_FeatureSet | null);
}
export interface _google_protobuf_FeatureSetDefaults_FeatureSetEditionDefault__Output {
    'edition': (_google_protobuf_Edition__Output);
    'overridableFeatures': (_google_protobuf_FeatureSet__Output | null);
    'fixedFeatures': (_google_protobuf_FeatureSet__Output | null);
}
export interface FeatureSetDefaults {
    'defaults'?: (_google_protobuf_FeatureSetDefaults_FeatureSetEditionDefault)[];
    'minimumEdition'?: (_google_protobuf_Edition);
    'maximumEdition'?: (_google_protobuf_Edition);
}
export interface FeatureSetDefaults__Output {
    'defaults': (_google_protobuf_FeatureSetDefaults_FeatureSetEditionDefault__Output)[];
    'minimumEdition': (_google_protobuf_Edition__Output);
    'maximumEdition': (_google_protobuf_Edition__Output);
}
