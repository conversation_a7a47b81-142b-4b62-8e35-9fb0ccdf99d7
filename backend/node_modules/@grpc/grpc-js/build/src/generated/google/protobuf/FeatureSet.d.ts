export declare const _google_protobuf_FeatureSet_VisibilityFeature_DefaultSymbolVisibility: {
    readonly DEFAULT_SYMBOL_VISIBILITY_UNKNOWN: "DEFAULT_SYMBOL_VISIBILITY_UNKNOWN";
    readonly EXPORT_ALL: "EXPORT_ALL";
    readonly EXPORT_TOP_LEVEL: "EXPORT_TOP_LEVEL";
    readonly LOCAL_ALL: "LOCAL_ALL";
    readonly STRICT: "STRICT";
};
export type _google_protobuf_FeatureSet_VisibilityFeature_DefaultSymbolVisibility = 'DEFAULT_SYMBOL_VISIBILITY_UNKNOWN' | 0 | 'EXPORT_ALL' | 1 | 'EXPORT_TOP_LEVEL' | 2 | 'LOCAL_ALL' | 3 | 'STRICT' | 4;
export type _google_protobuf_FeatureSet_VisibilityFeature_DefaultSymbolVisibility__Output = typeof _google_protobuf_FeatureSet_VisibilityFeature_DefaultSymbolVisibility[keyof typeof _google_protobuf_FeatureSet_VisibilityFeature_DefaultSymbolVisibility];
export declare const _google_protobuf_FeatureSet_EnforceNamingStyle: {
    readonly ENFORCE_NAMING_STYLE_UNKNOWN: "ENFORCE_NAMING_STYLE_UNKNOWN";
    readonly STYLE2024: "STYLE2024";
    readonly STYLE_LEGACY: "STYLE_LEGACY";
};
export type _google_protobuf_FeatureSet_EnforceNamingStyle = 'ENFORCE_NAMING_STYLE_UNKNOWN' | 0 | 'STYLE2024' | 1 | 'STYLE_LEGACY' | 2;
export type _google_protobuf_FeatureSet_EnforceNamingStyle__Output = typeof _google_protobuf_FeatureSet_EnforceNamingStyle[keyof typeof _google_protobuf_FeatureSet_EnforceNamingStyle];
export declare const _google_protobuf_FeatureSet_EnumType: {
    readonly ENUM_TYPE_UNKNOWN: "ENUM_TYPE_UNKNOWN";
    readonly OPEN: "OPEN";
    readonly CLOSED: "CLOSED";
};
export type _google_protobuf_FeatureSet_EnumType = 'ENUM_TYPE_UNKNOWN' | 0 | 'OPEN' | 1 | 'CLOSED' | 2;
export type _google_protobuf_FeatureSet_EnumType__Output = typeof _google_protobuf_FeatureSet_EnumType[keyof typeof _google_protobuf_FeatureSet_EnumType];
export declare const _google_protobuf_FeatureSet_FieldPresence: {
    readonly FIELD_PRESENCE_UNKNOWN: "FIELD_PRESENCE_UNKNOWN";
    readonly EXPLICIT: "EXPLICIT";
    readonly IMPLICIT: "IMPLICIT";
    readonly LEGACY_REQUIRED: "LEGACY_REQUIRED";
};
export type _google_protobuf_FeatureSet_FieldPresence = 'FIELD_PRESENCE_UNKNOWN' | 0 | 'EXPLICIT' | 1 | 'IMPLICIT' | 2 | 'LEGACY_REQUIRED' | 3;
export type _google_protobuf_FeatureSet_FieldPresence__Output = typeof _google_protobuf_FeatureSet_FieldPresence[keyof typeof _google_protobuf_FeatureSet_FieldPresence];
export declare const _google_protobuf_FeatureSet_JsonFormat: {
    readonly JSON_FORMAT_UNKNOWN: "JSON_FORMAT_UNKNOWN";
    readonly ALLOW: "ALLOW";
    readonly LEGACY_BEST_EFFORT: "LEGACY_BEST_EFFORT";
};
export type _google_protobuf_FeatureSet_JsonFormat = 'JSON_FORMAT_UNKNOWN' | 0 | 'ALLOW' | 1 | 'LEGACY_BEST_EFFORT' | 2;
export type _google_protobuf_FeatureSet_JsonFormat__Output = typeof _google_protobuf_FeatureSet_JsonFormat[keyof typeof _google_protobuf_FeatureSet_JsonFormat];
export declare const _google_protobuf_FeatureSet_MessageEncoding: {
    readonly MESSAGE_ENCODING_UNKNOWN: "MESSAGE_ENCODING_UNKNOWN";
    readonly LENGTH_PREFIXED: "LENGTH_PREFIXED";
    readonly DELIMITED: "DELIMITED";
};
export type _google_protobuf_FeatureSet_MessageEncoding = 'MESSAGE_ENCODING_UNKNOWN' | 0 | 'LENGTH_PREFIXED' | 1 | 'DELIMITED' | 2;
export type _google_protobuf_FeatureSet_MessageEncoding__Output = typeof _google_protobuf_FeatureSet_MessageEncoding[keyof typeof _google_protobuf_FeatureSet_MessageEncoding];
export declare const _google_protobuf_FeatureSet_RepeatedFieldEncoding: {
    readonly REPEATED_FIELD_ENCODING_UNKNOWN: "REPEATED_FIELD_ENCODING_UNKNOWN";
    readonly PACKED: "PACKED";
    readonly EXPANDED: "EXPANDED";
};
export type _google_protobuf_FeatureSet_RepeatedFieldEncoding = 'REPEATED_FIELD_ENCODING_UNKNOWN' | 0 | 'PACKED' | 1 | 'EXPANDED' | 2;
export type _google_protobuf_FeatureSet_RepeatedFieldEncoding__Output = typeof _google_protobuf_FeatureSet_RepeatedFieldEncoding[keyof typeof _google_protobuf_FeatureSet_RepeatedFieldEncoding];
export declare const _google_protobuf_FeatureSet_Utf8Validation: {
    readonly UTF8_VALIDATION_UNKNOWN: "UTF8_VALIDATION_UNKNOWN";
    readonly VERIFY: "VERIFY";
    readonly NONE: "NONE";
};
export type _google_protobuf_FeatureSet_Utf8Validation = 'UTF8_VALIDATION_UNKNOWN' | 0 | 'VERIFY' | 2 | 'NONE' | 3;
export type _google_protobuf_FeatureSet_Utf8Validation__Output = typeof _google_protobuf_FeatureSet_Utf8Validation[keyof typeof _google_protobuf_FeatureSet_Utf8Validation];
export interface _google_protobuf_FeatureSet_VisibilityFeature {
}
export interface _google_protobuf_FeatureSet_VisibilityFeature__Output {
}
export interface FeatureSet {
    'fieldPresence'?: (_google_protobuf_FeatureSet_FieldPresence);
    'enumType'?: (_google_protobuf_FeatureSet_EnumType);
    'repeatedFieldEncoding'?: (_google_protobuf_FeatureSet_RepeatedFieldEncoding);
    'utf8Validation'?: (_google_protobuf_FeatureSet_Utf8Validation);
    'messageEncoding'?: (_google_protobuf_FeatureSet_MessageEncoding);
    'jsonFormat'?: (_google_protobuf_FeatureSet_JsonFormat);
    'enforceNamingStyle'?: (_google_protobuf_FeatureSet_EnforceNamingStyle);
    'defaultSymbolVisibility'?: (_google_protobuf_FeatureSet_VisibilityFeature_DefaultSymbolVisibility);
}
export interface FeatureSet__Output {
    'fieldPresence': (_google_protobuf_FeatureSet_FieldPresence__Output);
    'enumType': (_google_protobuf_FeatureSet_EnumType__Output);
    'repeatedFieldEncoding': (_google_protobuf_FeatureSet_RepeatedFieldEncoding__Output);
    'utf8Validation': (_google_protobuf_FeatureSet_Utf8Validation__Output);
    'messageEncoding': (_google_protobuf_FeatureSet_MessageEncoding__Output);
    'jsonFormat': (_google_protobuf_FeatureSet_JsonFormat__Output);
    'enforceNamingStyle': (_google_protobuf_FeatureSet_EnforceNamingStyle__Output);
    'defaultSymbolVisibility': (_google_protobuf_FeatureSet_VisibilityFeature_DefaultSymbolVisibility__Output);
}
