import type { FeatureSet as _google_protobuf_FeatureSet, FeatureSet__Output as _google_protobuf_FeatureSet__Output } from '../../google/protobuf/FeatureSet';
import type { UninterpretedOption as _google_protobuf_UninterpretedOption, UninterpretedOption__Output as _google_protobuf_UninterpretedOption__Output } from '../../google/protobuf/UninterpretedOption';
export interface MessageOptions {
    'messageSetWireFormat'?: (boolean);
    'noStandardDescriptorAccessor'?: (boolean);
    'deprecated'?: (boolean);
    'mapEntry'?: (boolean);
    /**
     * @deprecated
     */
    'deprecatedLegacyJsonFieldConflicts'?: (boolean);
    'features'?: (_google_protobuf_FeatureSet | null);
    'uninterpretedOption'?: (_google_protobuf_UninterpretedOption)[];
    '.validate.disabled'?: (boolean);
}
export interface MessageOptions__Output {
    'messageSetWireFormat': (boolean);
    'noStandardDescriptorAccessor': (boolean);
    'deprecated': (boolean);
    'mapEntry': (boolean);
    /**
     * @deprecated
     */
    'deprecatedLegacyJsonFieldConflicts': (boolean);
    'features': (_google_protobuf_FeatureSet__Output | null);
    'uninterpretedOption': (_google_protobuf_UninterpretedOption__Output)[];
    '.validate.disabled': (boolean);
}
