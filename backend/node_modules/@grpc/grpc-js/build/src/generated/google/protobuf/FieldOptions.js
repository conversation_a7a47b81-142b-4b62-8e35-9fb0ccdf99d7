"use strict";
// Original file: null
Object.defineProperty(exports, "__esModule", { value: true });
exports._google_protobuf_FieldOptions_OptionTargetType = exports._google_protobuf_FieldOptions_OptionRetention = exports._google_protobuf_FieldOptions_JSType = exports._google_protobuf_FieldOptions_CType = void 0;
// Original file: null
exports._google_protobuf_FieldOptions_CType = {
    STRING: 'STRING',
    CORD: 'CORD',
    STRING_PIECE: 'STRING_PIECE',
};
// Original file: null
exports._google_protobuf_FieldOptions_JSType = {
    JS_NORMAL: 'JS_NORMAL',
    JS_STRING: 'JS_STRING',
    JS_NUMBER: 'JS_NUMBER',
};
// Original file: null
exports._google_protobuf_FieldOptions_OptionRetention = {
    RETENTION_UNKNOWN: 'RETENTION_UNKNOWN',
    RETENTION_RUNTIME: 'RETENTION_RUNTIME',
    RETENTION_SOURCE: 'RETENTION_SOURCE',
};
// Original file: null
exports._google_protobuf_FieldOptions_OptionTargetType = {
    TARGET_TYPE_UNKNOWN: 'TARGET_TYPE_UNKNOWN',
    TARGET_TYPE_FILE: 'TARGET_TYPE_FILE',
    TARGET_TYPE_EXTENSION_RANGE: 'TARGET_TYPE_EXTENSION_RANGE',
    TARGET_TYPE_MESSAGE: 'TARGET_TYPE_MESSAGE',
    TARGET_TYPE_FIELD: 'TARGET_TYPE_FIELD',
    TARGET_TYPE_ONEOF: 'TARGET_TYPE_ONEOF',
    TARGET_TYPE_ENUM: 'TARGET_TYPE_ENUM',
    TARGET_TYPE_ENUM_ENTRY: 'TARGET_TYPE_ENUM_ENTRY',
    TARGET_TYPE_SERVICE: 'TARGET_TYPE_SERVICE',
    TARGET_TYPE_METHOD: 'TARGET_TYPE_METHOD',
};
//# sourceMappingURL=FieldOptions.js.map