"use strict";
// Original file: null
Object.defineProperty(exports, "__esModule", { value: true });
exports._google_protobuf_FieldDescriptorProto_Type = exports._google_protobuf_FieldDescriptorProto_Label = void 0;
// Original file: null
exports._google_protobuf_FieldDescriptorProto_Label = {
    LABEL_OPTIONAL: 'LABEL_OPTIONAL',
    LABEL_REPEATED: 'LABEL_REPEATED',
    LABEL_REQUIRED: 'LABEL_REQUIRED',
};
// Original file: null
exports._google_protobuf_FieldDescriptorProto_Type = {
    TYPE_DOUBLE: 'TYPE_DOUBLE',
    TYPE_FLOAT: 'TYPE_FLOAT',
    TYPE_INT64: 'TYPE_INT64',
    TYPE_UINT64: 'TYPE_UINT64',
    TYPE_INT32: 'TYPE_INT32',
    TYPE_FIXED64: 'TYPE_FIXED64',
    TYPE_FIXED32: 'TYPE_FIXED32',
    TYPE_BOOL: 'TYPE_BOOL',
    TYPE_STRING: 'TYPE_STRING',
    TYPE_GROUP: 'TYPE_GROUP',
    TYPE_MESSAGE: 'TYPE_MESSAGE',
    TYPE_BYTES: 'TYPE_BYTES',
    TYPE_UINT32: 'TYPE_UINT32',
    TYPE_ENUM: 'TYPE_ENUM',
    TYPE_SFIXED32: 'TYPE_SFIXED32',
    TYPE_SFIXED64: 'TYPE_SFIXED64',
    TYPE_SINT32: 'TYPE_SINT32',
    TYPE_SINT64: 'TYPE_SINT64',
};
//# sourceMappingURL=FieldDescriptorProto.js.map