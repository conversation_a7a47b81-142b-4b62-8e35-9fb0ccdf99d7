"use strict";
// Original file: null
Object.defineProperty(exports, "__esModule", { value: true });
exports._google_protobuf_FeatureSet_Utf8Validation = exports._google_protobuf_FeatureSet_RepeatedFieldEncoding = exports._google_protobuf_FeatureSet_MessageEncoding = exports._google_protobuf_FeatureSet_JsonFormat = exports._google_protobuf_FeatureSet_FieldPresence = exports._google_protobuf_FeatureSet_EnumType = exports._google_protobuf_FeatureSet_EnforceNamingStyle = exports._google_protobuf_FeatureSet_VisibilityFeature_DefaultSymbolVisibility = void 0;
// Original file: null
exports._google_protobuf_FeatureSet_VisibilityFeature_DefaultSymbolVisibility = {
    DEFAULT_SYMBOL_VISIBILITY_UNKNOWN: 'DEFAULT_SYMBOL_VISIBILITY_UNKNOWN',
    EXPORT_ALL: 'EXPORT_ALL',
    EXPORT_TOP_LEVEL: 'EXPORT_TOP_LEVEL',
    LOCAL_ALL: 'LOCAL_ALL',
    STRICT: 'STRICT',
};
// Original file: null
exports._google_protobuf_FeatureSet_EnforceNamingStyle = {
    ENFORCE_NAMING_STYLE_UNKNOWN: 'ENFORCE_NAMING_STYLE_UNKNOWN',
    STYLE2024: 'STYLE2024',
    STYLE_LEGACY: 'STYLE_LEGACY',
};
// Original file: null
exports._google_protobuf_FeatureSet_EnumType = {
    ENUM_TYPE_UNKNOWN: 'ENUM_TYPE_UNKNOWN',
    OPEN: 'OPEN',
    CLOSED: 'CLOSED',
};
// Original file: null
exports._google_protobuf_FeatureSet_FieldPresence = {
    FIELD_PRESENCE_UNKNOWN: 'FIELD_PRESENCE_UNKNOWN',
    EXPLICIT: 'EXPLICIT',
    IMPLICIT: 'IMPLICIT',
    LEGACY_REQUIRED: 'LEGACY_REQUIRED',
};
// Original file: null
exports._google_protobuf_FeatureSet_JsonFormat = {
    JSON_FORMAT_UNKNOWN: 'JSON_FORMAT_UNKNOWN',
    ALLOW: 'ALLOW',
    LEGACY_BEST_EFFORT: 'LEGACY_BEST_EFFORT',
};
// Original file: null
exports._google_protobuf_FeatureSet_MessageEncoding = {
    MESSAGE_ENCODING_UNKNOWN: 'MESSAGE_ENCODING_UNKNOWN',
    LENGTH_PREFIXED: 'LENGTH_PREFIXED',
    DELIMITED: 'DELIMITED',
};
// Original file: null
exports._google_protobuf_FeatureSet_RepeatedFieldEncoding = {
    REPEATED_FIELD_ENCODING_UNKNOWN: 'REPEATED_FIELD_ENCODING_UNKNOWN',
    PACKED: 'PACKED',
    EXPANDED: 'EXPANDED',
};
// Original file: null
exports._google_protobuf_FeatureSet_Utf8Validation = {
    UTF8_VALIDATION_UNKNOWN: 'UTF8_VALIDATION_UNKNOWN',
    VERIFY: 'VERIFY',
    NONE: 'NONE',
};
//# sourceMappingURL=FeatureSet.js.map