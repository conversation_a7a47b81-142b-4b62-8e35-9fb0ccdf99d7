/**
 * AnyRules describe constraints applied exclusively to the
 * `google.protobuf.Any` well-known type
 */
export interface AnyRules {
    /**
     * Required specifies that this field must be set
     */
    'required'?: (boolean);
    /**
     * In specifies that this field's `type_url` must be equal to one of the
     * specified values.
     */
    'in'?: (string)[];
    /**
     * NotIn specifies that this field's `type_url` must not be equal to any of
     * the specified values.
     */
    'not_in'?: (string)[];
}
/**
 * AnyRules describe constraints applied exclusively to the
 * `google.protobuf.Any` well-known type
 */
export interface AnyRules__Output {
    /**
     * Required specifies that this field must be set
     */
    'required': (boolean);
    /**
     * In specifies that this field's `type_url` must be equal to one of the
     * specified values.
     */
    'in': (string)[];
    /**
     * NotIn specifies that this field's `type_url` must not be equal to any of
     * the specified values.
     */
    'not_in': (string)[];
}
