/**
 * SFixed32Rules describes the constraints applied to `sfixed32` values
 */
export interface SFixed32Rules {
    /**
     * Const specifies that this field must be exactly the specified value
     */
    'const'?: (number);
    /**
     * Lt specifies that this field must be less than the specified value,
     * exclusive
     */
    'lt'?: (number);
    /**
     * Lte specifies that this field must be less than or equal to the
     * specified value, inclusive
     */
    'lte'?: (number);
    /**
     * Gt specifies that this field must be greater than the specified value,
     * exclusive. If the value of Gt is larger than a specified Lt or Lte, the
     * range is reversed.
     */
    'gt'?: (number);
    /**
     * Gte specifies that this field must be greater than or equal to the
     * specified value, inclusive. If the value of Gte is larger than a
     * specified Lt or Lte, the range is reversed.
     */
    'gte'?: (number);
    /**
     * In specifies that this field must be equal to one of the specified
     * values
     */
    'in'?: (number)[];
    /**
     * NotIn specifies that this field cannot be equal to one of the specified
     * values
     */
    'not_in'?: (number)[];
}
/**
 * SFixed32Rules describes the constraints applied to `sfixed32` values
 */
export interface SFixed32Rules__Output {
    /**
     * Const specifies that this field must be exactly the specified value
     */
    'const': (number);
    /**
     * Lt specifies that this field must be less than the specified value,
     * exclusive
     */
    'lt': (number);
    /**
     * Lte specifies that this field must be less than or equal to the
     * specified value, inclusive
     */
    'lte': (number);
    /**
     * Gt specifies that this field must be greater than the specified value,
     * exclusive. If the value of Gt is larger than a specified Lt or Lte, the
     * range is reversed.
     */
    'gt': (number);
    /**
     * Gte specifies that this field must be greater than or equal to the
     * specified value, inclusive. If the value of Gte is larger than a
     * specified Lt or Lte, the range is reversed.
     */
    'gte': (number);
    /**
     * In specifies that this field must be equal to one of the specified
     * values
     */
    'in': (number)[];
    /**
     * NotIn specifies that this field cannot be equal to one of the specified
     * values
     */
    'not_in': (number)[];
}
