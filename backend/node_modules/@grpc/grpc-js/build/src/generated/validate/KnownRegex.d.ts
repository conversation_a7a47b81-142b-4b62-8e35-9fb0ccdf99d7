/**
 * WellKnownRegex contain some well-known patterns.
 */
export declare const KnownRegex: {
    readonly UNKNOWN: "UNKNOWN";
    /**
     * HTTP header name as defined by RFC 7230.
     */
    readonly HTTP_HEADER_NAME: "HTTP_HEADER_NAME";
    /**
     * HTTP header value as defined by RFC 7230.
     */
    readonly HTTP_HEADER_VALUE: "HTTP_HEADER_VALUE";
};
/**
 * WellKnownRegex contain some well-known patterns.
 */
export type KnownRegex = 'UNKNOWN' | 0
/**
 * HTTP header name as defined by RFC 7230.
 */
 | 'HTTP_HEADER_NAME' | 1
/**
 * HTTP header value as defined by RFC 7230.
 */
 | 'HTTP_HEADER_VALUE' | 2;
/**
 * WellKnownRegex contain some well-known patterns.
 */
export type KnownRegex__Output = typeof KnownRegex[keyof typeof KnownRegex];
