/**
 * MessageRules describe the constraints applied to embedded message values.
 * For message-type fields, validation is performed recursively.
 */
export interface MessageRules {
    /**
     * <PERSON><PERSON> specifies that the validation rules of this field should not be
     * evaluated
     */
    'skip'?: (boolean);
    /**
     * Required specifies that this field must be set
     */
    'required'?: (boolean);
}
/**
 * MessageRules describe the constraints applied to embedded message values.
 * For message-type fields, validation is performed recursively.
 */
export interface MessageRules__Output {
    /**
     * <PERSON><PERSON> specifies that the validation rules of this field should not be
     * evaluated
     */
    'skip': (boolean);
    /**
     * Required specifies that this field must be set
     */
    'required': (boolean);
}
