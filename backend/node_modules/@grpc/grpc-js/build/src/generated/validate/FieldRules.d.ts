import type { FloatRules as _validate_FloatRules, FloatRules__Output as _validate_FloatRules__Output } from '../validate/FloatRules';
import type { DoubleRules as _validate_DoubleRules, DoubleRules__Output as _validate_DoubleRules__Output } from '../validate/DoubleRules';
import type { Int32Rules as _validate_Int32Rules, Int32Rules__Output as _validate_Int32Rules__Output } from '../validate/Int32Rules';
import type { Int64Rules as _validate_Int64Rules, Int64Rules__Output as _validate_Int64Rules__Output } from '../validate/Int64Rules';
import type { UInt32Rules as _validate_UInt32Rules, UInt32Rules__Output as _validate_UInt32Rules__Output } from '../validate/UInt32Rules';
import type { UInt64Rules as _validate_UInt64Rules, UInt64Rules__Output as _validate_UInt64Rules__Output } from '../validate/UInt64Rules';
import type { SInt32Rules as _validate_SInt32Rules, SInt32Rules__Output as _validate_SInt32Rules__Output } from '../validate/SInt32Rules';
import type { SInt64Rules as _validate_SInt64Rules, SInt64Rules__Output as _validate_SInt64Rules__Output } from '../validate/SInt64Rules';
import type { Fixed32Rules as _validate_Fixed32Rules, Fixed32Rules__Output as _validate_Fixed32Rules__Output } from '../validate/Fixed32Rules';
import type { Fixed64Rules as _validate_Fixed64Rules, Fixed64Rules__Output as _validate_Fixed64Rules__Output } from '../validate/Fixed64Rules';
import type { SFixed32Rules as _validate_SFixed32Rules, SFixed32Rules__Output as _validate_SFixed32Rules__Output } from '../validate/SFixed32Rules';
import type { SFixed64Rules as _validate_SFixed64Rules, SFixed64Rules__Output as _validate_SFixed64Rules__Output } from '../validate/SFixed64Rules';
import type { BoolRules as _validate_BoolRules, BoolRules__Output as _validate_BoolRules__Output } from '../validate/BoolRules';
import type { StringRules as _validate_StringRules, StringRules__Output as _validate_StringRules__Output } from '../validate/StringRules';
import type { BytesRules as _validate_BytesRules, BytesRules__Output as _validate_BytesRules__Output } from '../validate/BytesRules';
import type { EnumRules as _validate_EnumRules, EnumRules__Output as _validate_EnumRules__Output } from '../validate/EnumRules';
import type { MessageRules as _validate_MessageRules, MessageRules__Output as _validate_MessageRules__Output } from '../validate/MessageRules';
import type { RepeatedRules as _validate_RepeatedRules, RepeatedRules__Output as _validate_RepeatedRules__Output } from '../validate/RepeatedRules';
import type { MapRules as _validate_MapRules, MapRules__Output as _validate_MapRules__Output } from '../validate/MapRules';
import type { AnyRules as _validate_AnyRules, AnyRules__Output as _validate_AnyRules__Output } from '../validate/AnyRules';
import type { DurationRules as _validate_DurationRules, DurationRules__Output as _validate_DurationRules__Output } from '../validate/DurationRules';
import type { TimestampRules as _validate_TimestampRules, TimestampRules__Output as _validate_TimestampRules__Output } from '../validate/TimestampRules';
/**
 * FieldRules encapsulates the rules for each type of field. Depending on the
 * field, the correct set should be used to ensure proper validations.
 */
export interface FieldRules {
    /**
     * Scalar Field Types
     */
    'float'?: (_validate_FloatRules | null);
    'double'?: (_validate_DoubleRules | null);
    'int32'?: (_validate_Int32Rules | null);
    'int64'?: (_validate_Int64Rules | null);
    'uint32'?: (_validate_UInt32Rules | null);
    'uint64'?: (_validate_UInt64Rules | null);
    'sint32'?: (_validate_SInt32Rules | null);
    'sint64'?: (_validate_SInt64Rules | null);
    'fixed32'?: (_validate_Fixed32Rules | null);
    'fixed64'?: (_validate_Fixed64Rules | null);
    'sfixed32'?: (_validate_SFixed32Rules | null);
    'sfixed64'?: (_validate_SFixed64Rules | null);
    'bool'?: (_validate_BoolRules | null);
    'string'?: (_validate_StringRules | null);
    'bytes'?: (_validate_BytesRules | null);
    /**
     * Complex Field Types
     */
    'enum'?: (_validate_EnumRules | null);
    'message'?: (_validate_MessageRules | null);
    'repeated'?: (_validate_RepeatedRules | null);
    'map'?: (_validate_MapRules | null);
    /**
     * Well-Known Field Types
     */
    'any'?: (_validate_AnyRules | null);
    'duration'?: (_validate_DurationRules | null);
    'timestamp'?: (_validate_TimestampRules | null);
    'type'?: "float" | "double" | "int32" | "int64" | "uint32" | "uint64" | "sint32" | "sint64" | "fixed32" | "fixed64" | "sfixed32" | "sfixed64" | "bool" | "string" | "bytes" | "enum" | "repeated" | "map" | "any" | "duration" | "timestamp";
}
/**
 * FieldRules encapsulates the rules for each type of field. Depending on the
 * field, the correct set should be used to ensure proper validations.
 */
export interface FieldRules__Output {
    /**
     * Scalar Field Types
     */
    'float'?: (_validate_FloatRules__Output | null);
    'double'?: (_validate_DoubleRules__Output | null);
    'int32'?: (_validate_Int32Rules__Output | null);
    'int64'?: (_validate_Int64Rules__Output | null);
    'uint32'?: (_validate_UInt32Rules__Output | null);
    'uint64'?: (_validate_UInt64Rules__Output | null);
    'sint32'?: (_validate_SInt32Rules__Output | null);
    'sint64'?: (_validate_SInt64Rules__Output | null);
    'fixed32'?: (_validate_Fixed32Rules__Output | null);
    'fixed64'?: (_validate_Fixed64Rules__Output | null);
    'sfixed32'?: (_validate_SFixed32Rules__Output | null);
    'sfixed64'?: (_validate_SFixed64Rules__Output | null);
    'bool'?: (_validate_BoolRules__Output | null);
    'string'?: (_validate_StringRules__Output | null);
    'bytes'?: (_validate_BytesRules__Output | null);
    /**
     * Complex Field Types
     */
    'enum'?: (_validate_EnumRules__Output | null);
    'message': (_validate_MessageRules__Output | null);
    'repeated'?: (_validate_RepeatedRules__Output | null);
    'map'?: (_validate_MapRules__Output | null);
    /**
     * Well-Known Field Types
     */
    'any'?: (_validate_AnyRules__Output | null);
    'duration'?: (_validate_DurationRules__Output | null);
    'timestamp'?: (_validate_TimestampRules__Output | null);
    'type'?: "float" | "double" | "int32" | "int64" | "uint32" | "uint64" | "sint32" | "sint64" | "fixed32" | "fixed64" | "sfixed32" | "sfixed64" | "bool" | "string" | "bytes" | "enum" | "repeated" | "map" | "any" | "duration" | "timestamp";
}
