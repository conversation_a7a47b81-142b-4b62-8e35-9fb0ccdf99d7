"use strict";
// Original file: proto/protoc-gen-validate/validate/validate.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnownRegex = void 0;
/**
 * WellKnownRegex contain some well-known patterns.
 */
exports.KnownRegex = {
    UNKNOWN: 'UNKNOWN',
    /**
     * HTTP header name as defined by RFC 7230.
     */
    HTTP_HEADER_NAME: 'HTTP_HEADER_NAME',
    /**
     * HTTP header value as defined by RFC 7230.
     */
    HTTP_HEADER_VALUE: 'HTTP_HEADER_VALUE',
};
//# sourceMappingURL=KnownRegex.js.map