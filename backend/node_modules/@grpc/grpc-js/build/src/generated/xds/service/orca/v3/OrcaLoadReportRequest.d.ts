import type { Duration as _google_protobuf_Duration, Duration__Output as _google_protobuf_Duration__Output } from '../../../../google/protobuf/Duration';
export interface OrcaLoadReportRequest {
    /**
     * Interval for generating Open RCA core metric responses.
     */
    'report_interval'?: (_google_protobuf_Duration | null);
    /**
     * Request costs to collect. If this is empty, all known requests costs tracked by
     * the load reporting agent will be returned. This provides an opportunity for
     * the client to selectively obtain a subset of tracked costs.
     */
    'request_cost_names'?: (string)[];
}
export interface OrcaLoadReportRequest__Output {
    /**
     * Interval for generating Open RCA core metric responses.
     */
    'report_interval': (_google_protobuf_Duration__Output | null);
    /**
     * Request costs to collect. If this is empty, all known requests costs tracked by
     * the load reporting agent will be returned. This provides an opportunity for
     * the client to selectively obtain a subset of tracked costs.
     */
    'request_cost_names': (string)[];
}
