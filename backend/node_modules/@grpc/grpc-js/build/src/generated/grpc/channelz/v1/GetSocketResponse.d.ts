import type { Socket as _grpc_channelz_v1_Socket, Socket__Output as _grpc_channelz_v1_Socket__Output } from '../../../grpc/channelz/v1/Socket';
export interface GetSocketResponse {
    /**
     * The Socket that corresponds to the requested socket_id.  This field
     * should be set.
     */
    'socket'?: (_grpc_channelz_v1_Socket | null);
}
export interface GetSocketResponse__Output {
    /**
     * The Socket that corresponds to the requested socket_id.  This field
     * should be set.
     */
    'socket': (_grpc_channelz_v1_Socket__Output | null);
}
