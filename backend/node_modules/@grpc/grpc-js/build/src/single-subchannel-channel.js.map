{"version": 3, "file": "single-subchannel-channel.js", "sourceRoot": "", "sources": ["../../src/single-subchannel-channel.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAKH,+CAAkD;AAGlD,yCAAqJ;AACrJ,6DAAgE;AAChE,6DAAyD;AACzD,2CAAgD;AAChD,iEAAwE;AACxE,yCAA0D;AAC1D,iDAAiE;AACjE,yCAAsC;AACtC,yCAAiD;AAGjD,6CAAmE;AAEnE,MAAM,qBAAqB;IAWzB,YAAoB,UAAsB,EAAU,MAAc,EAAE,kBAAsC,EAAU,OAA0B,EAAU,UAAkB;;QAAtJ,eAAU,GAAV,UAAU,CAAY;QAAU,WAAM,GAAN,MAAM,CAAQ;QAAkD,YAAO,GAAP,OAAO,CAAmB;QAAU,eAAU,GAAV,UAAU,CAAQ;QAVlK,cAAS,GAA0B,IAAI,CAAC;QACxC,mBAAc,GACpB,IAAI,CAAC;QACC,gBAAW,GAAG,KAAK,CAAC;QACpB,qBAAgB,GAAG,KAAK,CAAC;QACzB,kBAAa,GAAwB,IAAI,CAAC;QAG1C,sBAAiB,GAAG,KAAK,CAAC;QAC1B,uBAAkB,GAAG,KAAK,CAAC;QAEjC,MAAM,SAAS,GAAa,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnD,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB;;2BAEmB;QACnB,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC1B,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;QACD,MAAM,QAAQ,GAAG,MAAA,MAAA,IAAA,0BAAa,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0CAAE,IAAI,mCAAI,WAAW,CAAC;QACvE;oDAC4C;QAC5C,IAAI,CAAC,UAAU,GAAG,WAAW,QAAQ,IAAI,WAAW,EAAE,CAAC;QACvD,MAAM,OAAO,GAAG,IAAA,6BAAkB,EAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;YACzB,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC;gBACjB,IAAI,CAAC,gBAAgB,CAAC,kBAAM,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,GAAG,EAAE;oBACd,IAAI,CAAC,gBAAgB,CAAC,kBAAM,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;gBACvE,CAAC,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;QACH,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC,YAAY,EAAE,CAAC;IACvD,CAAC;IAED,gBAAgB,CAAC,MAAc,EAAE,OAAe;QAC9C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG;gBACnB,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE,IAAI,mBAAQ,EAAE;aACzB,CAAC;QACJ,CAAC;IAEH,CAAC;IACD,OAAO;;QACL,OAAO,MAAA,MAAA,IAAI,CAAC,SAAS,0CAAE,OAAO,EAAE,mCAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;IACnE,CAAC;IACD,KAAK,CAAC,KAAK,CAAC,QAAkB,EAAE,QAA8B;QAC5D,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC7C,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,KAAK,sCAAiB,CAAC,KAAK,EAAE,CAAC;YACvE,QAAQ,CAAC,eAAe,CAAC;gBACvB,IAAI,EAAE,kBAAM,CAAC,WAAW;gBACxB,OAAO,EAAE,sBAAsB;gBAC/B,QAAQ,EAAE,IAAI,mBAAQ,EAAE;aACzB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QACD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxF,IAAI,aAAuB,CAAC;QAC5B,IAAI,CAAC;YACH,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE;iBACvD,gBAAgB,CAAC,EAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,UAAU,EAAC,CAAC,CAAC;QAChF,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,KAAK,GAAG,CAA+B,CAAC;YAC9C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,qDAA8B,EACtD,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAM,CAAC,OAAO,EAC5D,mDAAmD,KAAK,CAAC,OAAO,EAAE,CACnE,CAAC;YACF,QAAQ,CAAC,eAAe,CACtB;gBACE,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,OAAO;gBAChB,QAAQ,EAAE,IAAI,mBAAQ,EAAE;aACzB,CACF,CAAC;YACF,OAAO;QACT,CAAC;QACD,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACtC,MAAM,aAAa,GAAyB;YAC1C,iBAAiB,EAAE,KAAK,EAAC,QAAQ,EAAC,EAAE;gBAClC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC/E,CAAC;YACD,gBAAgB,EAAE,KAAK,EAAC,OAAO,EAAC,EAAE;gBAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAC9B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACvE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;gBAC/B,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;gBAC3C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;YACD,eAAe,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;gBAC9B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBACtE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC3B,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;SACF,CAAA;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAC1G,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClG,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IACD,KAAK,CAAC,sBAAsB,CAAC,OAAuB,EAAE,OAAe;QACnE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAC,CAAC,CAAC,CAAC;QACtH,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC;YACxE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,CAAC,OAAO,EAAE,CAAC;QACtE,CAAC;IACH,CAAC;IACD,SAAS;QACP,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,CAAC;IACH,CAAC;IACD,SAAS;QACP,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC/C,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,CAAC;IACH,CAAC;IACD,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IACD,cAAc,CAAC,WAA4B;QACzC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACD,cAAc;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAED,MAAa,uBAAuB;IAOlC,YAAoB,UAAsB,EAAU,MAAe,EAAE,OAAuB;QAAxE,eAAU,GAAV,UAAU,CAAY;QAAU,WAAM,GAAN,MAAM,CAAS;QAL3D,oBAAe,GAAG,KAAK,CAAC;QACxB,kBAAa,GAAG,IAAI,wBAAa,EAAE,CAAC;QACpC,gBAAW,GAAG,IAAI,8BAAmB,EAAE,CAAC;QACxC,oBAAe,GAAG,IAAI,kCAAuB,EAAE,CAAC;QAGtD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,GAAG,IAAA,kCAAuB,EAAC,IAAA,wBAAW,EAAC,MAAM,CAAC,EAAG,GAAG,EAAE,CAAC,CAAC;YACtE,MAAM,EAAE,GAAG,IAAA,wBAAW,EAAC,MAAM,CAAC,KAAK,UAAU,CAAC,UAAU,EAAE,GAAG;YAC7D,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE;YAC7C,KAAK,EAAE,IAAI,CAAC,aAAa;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;SAC/C,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,iCAAkB,CAAC,CAAC,IAAI,6CAAwB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC;QACpE,CAAC;QACD,IAAA,gCAAqB,EAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;IAED,SAAS;QACP,OAAO,IAAA,wBAAW,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IACD,oBAAoB,CAAC,YAAqB;QACxC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACD,sBAAsB,CAAC,YAA+B,EAAE,QAAuB,EAAE,QAAiC;QAChH,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACD,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IACD,UAAU,CAAC,MAAc,EAAE,QAAkB;QAC3C,MAAM,WAAW,GAAsB;YACrC,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,IAAA,8BAAmB,EAAC,IAAI,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE,qBAAS,CAAC,QAAQ;YACzB,UAAU,EAAE,IAAI;SACjB,CAAC;QACF,OAAO,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,kBAAkB,EAAE,WAAW,EAAE,IAAA,+BAAiB,GAAE,CAAC,CAAC;IACvH,CAAC;CACF;AAlDD,0DAkDC"}