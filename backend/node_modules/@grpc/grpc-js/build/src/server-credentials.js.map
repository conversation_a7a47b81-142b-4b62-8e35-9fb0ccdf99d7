{"version": 3, "file": "server-credentials.js", "sourceRoot": "", "sources": ["../../src/server-credentials.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AA0RH,gGASC;AA2CD,0FAEC;AA7UD,+CAAmE;AAcnE,MAAsB,iBAAiB;IAGrC,YAAoB,wBAAoD,EAAE,cAAqC;QAA3F,6BAAwB,GAAxB,wBAAwB,CAA4B;QAFhE,aAAQ,GAA8B,IAAI,GAAG,EAAE,CAAC;QAChD,yBAAoB,GAAgC,IAAI,CAAC;QAE/D,IAAI,CAAC,oBAAoB,GAAG,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,IAAI,CAAC;IACrD,CAAC;IAED,WAAW,CAAC,OAA6B;QACvC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IACD,cAAc,CAAC,OAA6B;QAC1C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IACS,eAAe;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC5B,CAAC;IACS,0BAA0B,CAAC,OAAoC;QACvE,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACpC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IACD,SAAS;QACP,OAAO,IAAI,CAAC,wBAAwB,KAAK,IAAI,CAAC;IAChD,CAAC;IACD,wBAAwB;QACtB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IACD,sBAAsB;QACpB,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACvC,CAAC;IACD,gBAAgB;QACd,OAAO,EAAE,CAAC;IACZ,CAAC;IAGD,MAAM,CAAC,cAAc;QACnB,OAAO,IAAI,yBAAyB,EAAE,CAAC;IACzC,CAAC;IAED,MAAM,CAAC,SAAS,CACd,SAAwB,EACxB,YAA2B,EAC3B,sBAAsB,GAAG,KAAK;;QAE9B,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,SAAS,CAAC,+BAA+B,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAChD,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,MAAM,GAAG,GAAa,EAAE,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAE7B,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9C,MAAM,IAAI,SAAS,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,SAAS,CAAC,eAAe,CAAC,gCAAgC,CAAC,CAAC;YACxE,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,SAAS,CAAC,eAAe,CAAC,+BAA+B,CAAC,CAAC;YACvE,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,uBAAuB,CAAC;YACjC,WAAW,EAAE,sBAAsB;YACnC,OAAO,EAAE,2BAAa;SACvB,EAAE;YACD,EAAE,EAAE,MAAA,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,IAAA,iCAAmB,GAAE,mCAAI,SAAS;YACnD,IAAI;YACJ,GAAG;SACJ,CAAC,CAAC;IACL,CAAC;CACF;AAxFD,8CAwFC;AAED,MAAM,yBAA0B,SAAQ,iBAAiB;IACvD;QACE,KAAK,CAAC,IAAI,CAAC,CAAC;IACd,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAAC,KAAwB;QAC9B,OAAO,KAAK,YAAY,yBAAyB,CAAC;IACpD,CAAC;CACF;AAED,MAAM,uBAAwB,SAAQ,iBAAiB;IAGrD,YAAY,kBAAuC,EAAE,cAAoC;QACvF,KAAK,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,mCAAO,kBAAkB,GAAK,cAAc,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;OAKG;IACH,OAAO,CAAC,KAAwB;QAC9B,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,CAAC,CAAC,KAAK,YAAY,uBAAuB,CAAC,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,4BAA4B;QAC5B,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;YAC1E,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC9C,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;gBACzC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QACD,8BAA8B;QAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1E,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC3D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC5D,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;wBAChC,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;wBAC3B,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBAC7C,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QACD,6BAA6B;QAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACxE,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;gBACzD,OAAO,KAAK,CAAC;YACf,CAAC;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACjD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC9B,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;wBACzB,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;gBAC3C,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QACD,qCAAqC;QACrC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;QACD;uCAC+B;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,MAAM,oCAAqC,SAAQ,iBAAiB;IAKlE,YACU,2BAAgD,EAChD,qBAAiD,EACjD,wBAAiC;QAEzC,KAAK,CAAC;YACJ,WAAW,EAAE,qBAAqB,KAAK,IAAI;YAC3C,kBAAkB,EAAE,wBAAwB;YAC5C,OAAO,EAAE,2BAAa;SACvB,CAAC,CAAC;QARK,gCAA2B,GAA3B,2BAA2B,CAAqB;QAChD,0BAAqB,GAArB,qBAAqB,CAA4B;QACjD,6BAAwB,GAAxB,wBAAwB,CAAS;QAPnC,mBAAc,GAA+B,IAAI,CAAC;QAClD,yBAAoB,GAAqC,IAAI,CAAC;QAC9D,gCAA2B,GAAgC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrG,sCAAiC,GAAsC,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAWjI,CAAC;IACD,WAAW,CAAC,OAA6B;;QACvC,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE,CAAC;YACjC,MAAA,IAAI,CAAC,qBAAqB,0CAAE,wBAAwB,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACvF,IAAI,CAAC,2BAA2B,CAAC,8BAA8B,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC1G,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IACD,cAAc,CAAC,OAA6B;;QAC1C,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAC9B,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE,CAAC;YACjC,MAAA,IAAI,CAAC,qBAAqB,0CAAE,2BAA2B,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1F,IAAI,CAAC,2BAA2B,CAAC,iCAAiC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAC7G,CAAC;IACH,CAAC;IACD,OAAO,CAAC,KAAwB;QAC9B,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,CAAC,CAAC,KAAK,YAAY,oCAAoC,CAAC,EAAE,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,CACL,IAAI,CAAC,qBAAqB,KAAK,KAAK,CAAC,qBAAqB;YAC1D,IAAI,CAAC,2BAA2B,KAAK,KAAK,CAAC,2BAA2B;YACtE,IAAI,CAAC,wBAAwB,KAAK,KAAK,CAAC,wBAAwB,CACjE,CAAA;IACH,CAAC;IAEO,6BAA6B;;QACnC,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YACxE,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO;YACL,EAAE,EAAE,MAAA,IAAI,CAAC,cAAc,0CAAE,aAAa;YACtC,IAAI,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC;YAC7C,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;SAC5C,CAAC;IACJ,CAAC;IAEO,cAAc;QACpB,MAAM,oBAAoB,GAAG,IAAI,CAAC,6BAA6B,EAAE,CAAC;QAClE,IAAI,CAAC,0BAA0B,CAAC,oBAAoB,CAAC,CAAC;IACxD,CAAC;IAEO,yBAAyB,CAAC,MAAkC;QAClE,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,iCAAiC,CAAC,MAAwC;QAChF,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC;QACnC,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;CACF;AAED,SAAgB,0CAA0C,CACxD,qBAA0C,EAC1C,2BAAuD,EACvD,wBAAiC;IAEjC,OAAO,IAAI,oCAAoC,CAC7C,qBAAqB,EACrB,2BAA2B,EAC3B,wBAAwB,CAAC,CAAC;AAC9B,CAAC;AAED,MAAM,4BAA6B,SAAQ,iBAAiB;IAC1D,YAA6B,gBAAmC,EAAmB,YAAiC;QAClH,KAAK,CAAC,EAAE,CAAC,CAAC;QADiB,qBAAgB,GAAhB,gBAAgB,CAAmB;QAAmB,iBAAY,GAAZ,YAAY,CAAqB;IAEpH,CAAC;IACD,SAAS;QACP,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;IAC3C,CAAC;IACD,OAAO,CAAC,KAAwB;QAC9B,IAAI,CAAC,CAAC,KAAK,YAAY,4BAA4B,CAAC,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;gBACnD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACQ,gBAAgB;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IACQ,WAAW,CAAC,OAA6B;QAChD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IACQ,cAAc,CAAC,OAA6B;QACnD,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IACQ,sBAAsB;QAC7B,OAAO,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;IACxD,CAAC;IACQ,wBAAwB;QAC/B,OAAO,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;IAC1D,CAAC;CACF;AAED,SAAgB,uCAAuC,CAAC,WAA8B,EAAE,YAAiC;IACvH,OAAO,IAAI,4BAA4B,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACrE,CAAC"}