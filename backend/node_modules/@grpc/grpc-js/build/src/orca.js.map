{"version": 3, "file": "orca.js", "sourceRoot": "", "sources": ["../../src/orca.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AA2MH,4CAGC;AAcD,kDAkBC;AAxOD,+CAAsD;AAEtD,yCAAmF;AAEnF,+DAA2D;AAI3D,iEAAiG;AAEjG,2CAAqC;AACrC,uDAAmD;AACnD,6DAAyD;AAEzD,MAAM,eAAe,GAA6B,IAAI,CAAC;AACvD,SAAS,aAAa;IACpB,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,eAAe,CAAC;IACzB,CAAC;IACD;yDACqD;IACrD,MAAM,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC;SACjD,QAA2B,CAAC;IAC/B,MAAM,WAAW,GAAG,cAAc,CAAC,gCAAgC,EAAE;QACnE,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,MAAM;QACb,KAAK,EAAE,MAAM;QACb,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;QACZ,WAAW,EAAE;YACX,GAAG,SAAS,kBAAkB;YAC9B,GAAG,SAAS,kCAAkC;SAC/C;KACF,CAAC,CAAC;IACH,OAAO,IAAA,mCAAqB,EAAC,WAAW,CAAiC,CAAC;AAC5E,CAAC;AAED;;GAEG;AACH,MAAa,wBAAwB;IAArC;QACU,YAAO,GAAmB,EAAE,CAAC;IAkFvC,CAAC;IAhFC;;;;OAIG;IACH,uBAAuB,CAAC,IAAY,EAAE,KAAa;QACjD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACH,uBAAuB,CAAC,IAAY,EAAE,KAAa;QACjD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,EAAE,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,iBAAiB,CAAC,IAAY,EAAE,KAAa;QAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC;QAClC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACH,0BAA0B,CAAC,KAAa;QACtC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,6BAA6B,CAAC,KAAa;QACzC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,kCAAkC,CAAC,KAAa;QAC9C,IAAI,CAAC,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,KAAa;QAC3B,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;IACtC,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,KAAa;QAC3B,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,SAAS;QACP,MAAM,SAAS,GAAG,aAAa,EAAE,CAAC;QAClC,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3E,CAAC;CACF;AAnFD,4DAmFC;AAED,MAAM,0BAA0B,GAAG,KAAM,CAAC;AAE1C,MAAa,oBAAoB;IAAjC;QACU,YAAO,GAAmB,EAAE,CAAC;QAE7B,0BAAqB,GAA2B;YACtD,iBAAiB,EAAE,IAAI,CAAC,EAAE;gBACxB,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;oBACnD,IAAA,uBAAY,EAAC,IAAA,oCAAyB,EAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;oBACvE,0BAA0B,CAAC;gBAC7B,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,EAAE;oBACnC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3B,CAAC,EAAE,cAAc,CAAC,CAAC;gBACnB,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE;oBACxB,aAAa,CAAC,WAAW,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAA;YACJ,CAAC;SACF,CAAA;IAqDH,CAAC;IAnDC,oBAAoB,CAAC,IAAY,EAAE,KAAa;QAC9C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,EAAE,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IACzC,CAAC;IAED,wBAAwB,CAAC,OAAiC;QACxD,IAAI,CAAC,OAAO,CAAC,WAAW,qBAAO,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED,uBAAuB,CAAC,IAAY;;QAC3B,MAAA,IAAI,CAAC,OAAO,CAAC,WAAW,+CAAG,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,uBAAuB,CAAC,KAAa;QACnC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;IACvC,CAAC;IAED,0BAA0B;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;IACtC,CAAC;IAED,+BAA+B,CAAC,KAAa;QAC3C,IAAI,CAAC,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED,kCAAkC;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC;IAC9C,CAAC;IAED,YAAY,CAAC,KAAa;QACxB,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;IACtC,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;IACrC,CAAC;IAED,YAAY,CAAC,KAAa;QACxB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IAC1B,CAAC;IAED,WAAW,CAAC,MAAc;QACxB,MAAM,iBAAiB,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC;QACrF,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACnE,CAAC;CACF;AApED,oDAoEC;AAED,SAAgB,gBAAgB,CAAC,OAAgB;IAC/C,MAAM,WAAW,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;IACvE,OAAO,IAAI,WAAW,CAAC,QAAQ,EAAE,wCAAkB,CAAC,cAAc,EAAE,EAAE,EAAC,eAAe,EAAE,OAAO,EAAC,CAAC,CAAC;AACpG,CAAC;AAIY,QAAA,mBAAmB,GAAG,2BAA2B,CAAC;AAC/D,MAAM,sBAAsB,GAAG,uBAAuB,CAAC;AAEvD;;;;;;GAMG;AACH,SAAgB,mBAAmB,CAAC,QAAyB,EAAE,mBAAuC;IACpG,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;QACjC,IAAI,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,sBAAsB,CAAyC,CAAC;QAC1G,IAAI,gBAAgB,EAAE,CAAC;YACrB,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,MAAM,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,2BAAmB,CAAC,CAAC;YAC/D,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,MAAM,SAAS,GAAG,aAAa,EAAE,CAAC;gBAClC,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAW,CAAC,CAAC;gBAC5G,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAC3B,QAAQ,CAAC,SAAS,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QACD,IAAI,mBAAmB,EAAE,CAAC;YACxB,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC,CAAA;AACH,CAAC;AAED,MAAM,iBAAiB,GAAG,kBAAkB,CAAC;AAE7C,MAAM,qBAAqB;IAEzB,YAAoB,eAAgC,EAAU,UAAkB;QAA5D,oBAAe,GAAf,eAAe,CAAiB;QAAU,eAAU,GAAV,UAAU,CAAQ;QADxE,iBAAY,GAAwB,IAAI,CAAC;IACkC,CAAC;IACpF,aAAa,CAAC,UAAsB;QAClC,MAAM,QAAQ,GAAG,UAAU,CAAC,uBAAuB,CAAC,iBAAiB,EAAE,4BAA4B,CAAC,CAAC;QACrG,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;QAC7B,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IACD,OAAO;;QACL,MAAA,IAAI,CAAC,YAAY,0CAAE,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IACD,WAAW;QACT,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IACD,eAAe,CAAC,OAA+B;QAC7C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;CACF;AAED,MAAM,sBAAsB;IAQ1B,YAAoB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QAPlC,iBAAY,GAA+B,IAAI,GAAG,EAAE,CAAC;QACrD,kBAAa,GAAG,IAAI,CAAC;QAErB,gBAAW,GAAwD,IAAI,CAAC;QACxE,oBAAe,GAAG,QAAQ,CAAC;QAC3B,iBAAY,GAAG,IAAI,gCAAc,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;QAC1E,4BAAuB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEvE,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACxC,UAAU,CAAC,4BAA4B,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACxE,CAAC;IACD,cAAc,CAAC,WAAkC;QAC/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACnC,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IACD,iBAAiB,CAAC,WAAkC;;QAClD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;YACtD,MAAA,IAAI,CAAC,WAAW,0CAAE,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,UAAU,CAAC,+BAA+B,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAChF,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IACO,yBAAyB;;QAC/B,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,KAAK,sCAAiB,CAAC,KAAK,EAAE,CAAC;YAC9H,OAAO;QACT,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QACrG,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,WAAW,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;YAC9D,MAAA,IAAI,CAAC,WAAW,0CAAE,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;YACnC,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAC,eAAe,EAAE,IAAA,uBAAY,EAAC,WAAW,CAAC,EAAC,CAAC,CAAC;YAChG,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;YAC/B,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,MAA8B,EAAE,EAAE;gBACxD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAClC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAmB,EAAE,EAAE;gBAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAM,CAAC,aAAa,EAAE,CAAC;oBACxC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;oBAC3B,OAAO;gBACT,CAAC;gBACD,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAM,CAAC,SAAS,EAAE,CAAC;oBACpC,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AAED,MAAa,+BAAgC,SAAQ,4CAAqB;IACxE,YAAY,KAA0B,EAAE,eAAgC,EAAE,UAAkB;QAC1F,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,cAAc,CAAC,IAAI,qBAAqB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED,oBAAoB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;CACF;AATD,0EASC;AAED,SAAS,4BAA4B,CAAC,UAAsB;IAC1D,OAAO,IAAI,sBAAsB,CAAC,UAAU,CAAC,CAAC;AAChD,CAAC"}