syntax = "proto3";

package xds.service.orca.v3;

option java_outer_classname = "OrcaProto";
option java_multiple_files = true;
option java_package = "com.github.xds.service.orca.v3";
option go_package = "github.com/cncf/xds/go/xds/service/orca/v3";

import "xds/data/orca/v3/orca_load_report.proto";

import "google/protobuf/duration.proto";

// See section `Out-of-band (OOB) reporting` of the design document in
// :ref:`https://github.com/envoyproxy/envoy/issues/6614`.

// Out-of-band (OOB) load reporting service for the additional load reporting
// agent that does not sit in the request path. Reports are periodically sampled
// with sufficient frequency to provide temporal association with requests.
// OOB reporting compensates the limitation of in-band reporting in revealing
// costs for backends that do not provide a steady stream of telemetry such as
// long running stream operations and zero QPS services. This is a server
// streaming service, client needs to terminate current RPC and initiate
// a new call to change backend reporting frequency.
service OpenRcaService {
  rpc StreamCoreMetrics(OrcaLoadReportRequest) returns (stream xds.data.orca.v3.OrcaLoadReport);
}

message OrcaLoadReportRequest {
  // Interval for generating Open RCA core metric responses.
  google.protobuf.Duration report_interval = 1;
  // Request costs to collect. If this is empty, all known requests costs tracked by
  // the load reporting agent will be returned. This provides an opportunity for
  // the client to selectively obtain a subset of tracked costs.
  repeated string request_cost_names = 2;
}
