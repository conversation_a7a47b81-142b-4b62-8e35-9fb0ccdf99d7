{"name": "@grpc/grpc-js", "version": "1.14.0", "description": "gRPC Library for Node - pure JS implementation", "homepage": "https://grpc.io/", "repository": "https://github.com/grpc/grpc-node/tree/master/packages/grpc-js", "main": "build/src/index.js", "engines": {"node": ">=12.10.0"}, "keywords": [], "author": {"name": "Google Inc."}, "types": "build/src/index.d.ts", "license": "Apache-2.0", "devDependencies": {"@grpc/proto-loader": "file:../proto-loader", "@types/gulp": "^4.0.17", "@types/gulp-mocha": "0.0.37", "@types/lodash": "^4.14.202", "@types/mocha": "^10.0.6", "@types/ncp": "^2.0.8", "@types/node": ">=20.11.20", "@types/pify": "^5.0.4", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@typescript-eslint/typescript-estree": "^7.1.0", "clang-format": "^1.8.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "execa": "^2.0.3", "gulp": "^4.0.2", "gulp-mocha": "^6.0.0", "lodash": "^4.17.21", "madge": "^5.0.1", "mocha-jenkins-reporter": "^0.4.1", "ncp": "^2.0.0", "pify": "^4.0.1", "prettier": "^2.8.8", "rimraf": "^3.0.2", "semver": "^7.6.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "contributors": [{"name": "Google Inc."}], "scripts": {"build": "npm run compile", "clean": "rimraf ./build", "compile": "tsc -p .", "format": "clang-format -i -style=\"{Language: JavaScript, BasedOnStyle: Google, ColumnLimit: 80}\" src/*.ts test/*.ts", "lint": "eslint src/*.ts test/*.ts", "prepare": "npm run copy-protos && npm run generate-types && npm run generate-test-types && npm run compile", "test": "gulp test", "check": "npm run lint", "fix": "eslint --fix src/*.ts test/*.ts", "pretest": "npm run generate-types && npm run generate-test-types && npm run compile", "posttest": "npm run check && madge -c ./build/src", "generate-types": "proto-loader-gen-types --keepCase --longs String --enums String --defaults --oneofs --includeComments --includeDirs proto/ --include-dirs proto/ proto/xds/ proto/protoc-gen-validate/ -O src/generated/ --grpcLib ../index channelz.proto xds/service/orca/v3/orca.proto", "generate-test-types": "proto-loader-gen-types --keepCase --longs String --enums String --defaults --oneofs --includeComments --include-dirs test/fixtures/ -O test/generated/ --grpcLib ../../src/index test_service.proto echo_service.proto", "copy-protos": "node ./copy-protos"}, "dependencies": {"@grpc/proto-loader": "^0.8.0", "@js-sdsl/ordered-map": "^4.4.2"}, "files": ["src/**/*.ts", "build/src/**/*.{js,d.ts,js.map}", "proto/**/*.proto", "proto/**/LICENSE", "LICENSE", "deps/envoy-api/envoy/api/v2/**/*.proto", "deps/envoy-api/envoy/config/**/*.proto", "deps/envoy-api/envoy/service/**/*.proto", "deps/envoy-api/envoy/type/**/*.proto", "deps/udpa/udpa/**/*.proto", "deps/googleapis/google/api/*.proto", "deps/googleapis/google/rpc/*.proto", "deps/protoc-gen-validate/validate/**/*.proto"]}