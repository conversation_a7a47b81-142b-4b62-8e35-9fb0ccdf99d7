{"name": "undici-types", "version": "7.14.0", "description": "A stand-alone types package for Undici", "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "license": "MIT", "types": "index.d.ts", "files": ["*.d.ts"], "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup", "author": true}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood", "author": true}, {"name": "<PERSON>", "url": "https://github.com/mcollina", "author": true}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev", "author": true}, {"name": "<PERSON>", "url": "https://github.com/ronag", "author": true}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak", "author": true}, {"name": "<PERSON>", "url": "https://github.com/delvedor", "author": true}]}