{"name": "prom-client", "version": "15.1.3", "description": "Client for prometheus", "main": "index.js", "files": ["lib/", "index.js", "index.d.ts"], "engines": {"node": "^16 || ^18 || >=20"}, "scripts": {"benchmarks": "node ./benchmarks/index.js", "test": "npm run lint && npm run check-prettier && npm run compile-typescript && npm run test-unit", "lint": "eslint .", "test-unit": "jest", "run-prettier": "prettier . .eslintrc", "check-prettier": "npm run run-prettier -- --check", "compile-typescript": "tsc --project .", "prepare": "husky"}, "repository": {"type": "git", "url": "**************:siimon/prom-client.git"}, "keywords": ["Prometheus", "Metrics", "Client"], "author": "<PERSON>", "license": "Apache-2.0", "homepage": "https://github.com/siimon/prom-client", "devDependencies": {"@clevernature/benchmark-regression": "^1.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.32.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-n": "^16.0.0", "eslint-plugin-prettier": "^5.0.1", "express": "^4.13.3", "husky": "^9.0.0", "jest": "^29.3.1", "lint-staged": "^13.1.0", "nock": "^13.0.5", "prettier": "3.3.2", "typescript": "^5.0.4"}, "dependencies": {"@opentelemetry/api": "^1.4.0", "tdigest": "^0.1.1"}, "types": "./index.d.ts", "jest": {"testEnvironment": "node", "testRegex": ".*Test\\.js$"}, "lint-staged": {"*.{js,ts}": "eslint --fix", "*.{md,json,yml}": "prettier --write", ".travis.yml": "prettier --write"}, "prettier": {"singleQuote": true, "useTabs": true, "arrowParens": "avoid", "trailingComma": "all", "endOfLine": "auto", "overrides": [{"files": "*.md", "options": {"useTabs": false}}, {"files": ".eslintrc", "options": {"parser": "json"}}]}}