{"name": "internal-slot", "version": "1.1.0", "description": "ES spec-like internal slots", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/internal-slot.git"}, "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "homepage": "https://github.com/ljharb/internal-slot#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "publishConfig": {"ignore": [".github/workflows"]}}