{"name": "@socket.io/redis-adapter", "version": "8.3.0", "description": "The Socket.IO Redis adapter, allowing to broadcast events between several Socket.IO servers", "license": "MIT", "repository": {"type": "git", "url": "**************:socketio/socket.io-redis-adapter.git"}, "files": ["dist/"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"compile": "rimraf ./dist && tsc", "test": "npm run format:check && tsc && nyc mocha --bail --require ts-node/register test/test-runner.ts", "format:check": "prettier --parser typescript --check 'lib/**/*.ts' 'test/**/*.ts'", "format:fix": "prettier --parser typescript --write 'lib/**/*.ts' 'test/**/*.ts'", "prepack": "npm run compile"}, "dependencies": {"debug": "~4.3.1", "notepack.io": "~3.0.1", "uid2": "1.0.0"}, "peerDependencies": {"socket.io-adapter": "^2.5.4"}, "devDependencies": {"@types/expect.js": "^0.3.29", "@types/mocha": "^8.2.1", "@types/node": "^14.14.7", "expect.js": "0.3.1", "ioredis": "^5.3.2", "mocha": "^10.1.0", "nyc": "^15.1.0", "prettier": "^2.8.7", "redis": "^4.6.6", "redis-v3": "npm:redis@^3.1.2", "rimraf": "^5.0.5", "socket.io": "^4.6.1", "socket.io-client": "^4.1.1", "ts-node": "^10.9.1", "typescript": "^4.9.5"}, "engines": {"node": ">=10.0.0"}}