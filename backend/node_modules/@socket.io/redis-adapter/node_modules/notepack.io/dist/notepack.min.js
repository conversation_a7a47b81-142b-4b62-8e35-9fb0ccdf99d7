/*!
 * Notepack.io 3.0.1
 * (c) 2014-2022 <PERSON>
 * Released under the MIT License.
 */
!function(t){function r(t){if(this.t=0,t instanceof ArrayBuffer)this.i=t,this.s=new DataView(this.i);else{if(!ArrayBuffer.isView(t))throw new Error("Invalid argument");this.i=t.buffer,this.s=new DataView(this.i,t.byteOffset,t.byteLength)}}self.notepack={encode:function(t){var i=[],s=[],r=function t(i,s,r){var e=typeof r,h=0,n=0,a=0,f=0,u=0,o=0;if("string"===e){if((u=M(r))<32)i.push(160|u),o=1;else if(u<256)i.push(217,u),o=2;else if(u<65536)i.push(218,u>>8,u),o=3;else{if(!(u<**********))throw new Error("String too long");i.push(219,u>>24,u>>16,u>>8,u),o=5}return s.push({h:r,u:u,t:i.length}),o+u}if("number"===e)return Math.floor(r)===r&&isFinite(r)?0<=r?r<128?(i.push(r),1):r<256?(i.push(204,r),2):r<65536?(i.push(205,r>>8,r),3):r<**********?(i.push(206,r>>24,r>>16,r>>8,r),5):(a=r/Math.pow(2,32)>>0,f=r>>>0,i.push(207,a>>24,a>>16,a>>8,a,f>>24,f>>16,f>>8,f),9):-32<=r?(i.push(r),1):-128<=r?(i.push(208,r),2):-32768<=r?(i.push(209,r>>8,r),3):-**********<=r?(i.push(210,r>>24,r>>16,r>>8,r),5):(a=Math.floor(r/Math.pow(2,32)),f=r>>>0,i.push(211,a>>24,a>>16,a>>8,a,f>>24,f>>16,f>>8,f),9):(i.push(203),s.push({o:r,u:8,t:i.length}),9);if("object"===e){if(null===r)return i.push(192),1;if(Array.isArray(r)){if((u=r.length)<16)i.push(144|u),o=1;else if(u<65536)i.push(220,u>>8,u),o=3;else{if(!(u<**********))throw new Error("Array too large");i.push(221,u>>24,u>>16,u>>8,u),o=5}for(h=0;h<u;h++)o+=t(i,s,r[h]);return o}if(r instanceof Date){var c=r.getTime(),l=Math.floor(c/1e3),w=1e6*(c-1e3*l);return 0<=l&&0<=w&&l<=E?0===w&&l<=d?(i.push(214,255,l>>24,l>>16,l>>8,l),6):(a=l/**********,f=4294967295&l,i.push(215,255,w>>22,w>>14,w>>6,a,f>>24,f>>16,f>>8,f),10):(a=Math.floor(l/**********),f=l>>>0,i.push(199,12,255,w>>24,w>>16,w>>8,w,a>>24,a>>16,a>>8,a,f>>24,f>>16,f>>8,f),15)}if(r instanceof ArrayBuffer){if((u=r.byteLength)<256)i.push(196,u),o=2;else if(u<65536)i.push(197,u>>8,u),o=3;else{if(!(u<**********))throw new Error("Buffer too large");i.push(198,u>>24,u>>16,u>>8,u),o=5}return s.push({l:r,u:u,t:i.length}),o+u}if("function"==typeof r.toJSON)return t(i,s,r.toJSON());var v=[],g="",y=Object.keys(r);for(h=0,n=y.length;h<n;h++)g=y[h],void 0!==r[g]&&"function"!=typeof r[g]&&v.push(g);if((u=v.length)<16)i.push(128|u),o=1;else if(u<65536)i.push(222,u>>8,u),o=3;else{if(!(u<**********))throw new Error("Object too large");i.push(223,u>>24,u>>16,u>>8,u),o=5}for(h=0;h<u;h++)g=v[h],o+=t(i,s,g),o+=t(i,s,r[g]);return o}if("boolean"===e)return i.push(r?195:194),1;if("undefined"===e)return i.push(192),1;if("function"==typeof r.toJSON)return t(i,s,r.toJSON());throw new Error("Could not encode")}(i,s,t),e=new ArrayBuffer(r),h=new DataView(e),n=0,a=0,f=-1;0<s.length&&(f=s[0].t);for(var u,o=0,c=0,l=0,w=i.length;l<w;l++)if(h.setUint8(a+l,i[l]),l+1===f){if(o=(u=s[n]).u,c=a+f,u.l)for(var v=new Uint8Array(u.l),g=0;g<o;g++)h.setUint8(c+g,v[g]);else u.h?y(h,c,u.h):void 0!==u.o&&h.setFloat64(c,u.o);a+=o,s[++n]&&(f=s[n].t)}return e},decode:function(t){var i=new r(t),s=i.v();if(i.t===t.byteLength)return s;throw new Error(t.byteLength-i.t+" trailing bytes")}},r.prototype.g=function(t){for(var i=new Array(t),s=0;s<t;s++)i[s]=this.v();return i},r.prototype.M=function(t){for(var i={},s=0;s<t;s++)i[this.v()]=this.v();return i},r.prototype.h=function(t){var i=function(t,i,s){for(var r="",e=0,h=i,n=i+s;h<n;h++){var a=t.getUint8(h);if(0!=(128&a))if(192!=(224&a))if(224!=(240&a)){if(240!=(248&a))throw new Error("Invalid byte "+a.toString(16));65536<=(e=(7&a)<<18|(63&t.getUint8(++h))<<12|(63&t.getUint8(++h))<<6|(63&t.getUint8(++h))<<0)?(e-=65536,r+=String.fromCharCode(55296+(e>>>10),56320+(1023&e))):r+=String.fromCharCode(e)}else r+=String.fromCharCode((15&a)<<12|(63&t.getUint8(++h))<<6|(63&t.getUint8(++h))<<0);else r+=String.fromCharCode((31&a)<<6|63&t.getUint8(++h));else r+=String.fromCharCode(a)}return r}(this.s,this.t,t);return this.t+=t,i},r.prototype.l=function(t){var i=this.i.slice(this.t,this.t+t);return this.t+=t,i},r.prototype.v=function(){var t,i=this.s.getUint8(this.t++),s=0,r=0,e=0,h=0;if(i<192)return i<128?i:i<144?this.M(15&i):i<160?this.g(15&i):this.h(31&i);if(223<i)return-1*(255-i+1);switch(i){case 192:return null;case 194:return!1;case 195:return!0;case 196:return s=this.s.getUint8(this.t),this.t+=1,this.l(s);case 197:return s=this.s.getUint16(this.t),this.t+=2,this.l(s);case 198:return s=this.s.getUint32(this.t),this.t+=4,this.l(s);case 199:if(s=this.s.getUint8(this.t),r=this.s.getInt8(this.t+1),this.t+=2,-1!==r)return[r,this.l(s)];var n=this.s.getUint32(this.t);return e=this.s.getInt32(this.t+4),h=this.s.getUint32(this.t+8),this.t+=12,new Date(1e3*(***********e+h)+n/1e6);case 200:return s=this.s.getUint16(this.t),r=this.s.getInt8(this.t+2),this.t+=3,[r,this.l(s)];case 201:return s=this.s.getUint32(this.t),r=this.s.getInt8(this.t+4),this.t+=5,[r,this.l(s)];case 202:return t=this.s.getFloat32(this.t),this.t+=4,t;case 203:return t=this.s.getFloat64(this.t),this.t+=8,t;case 204:return t=this.s.getUint8(this.t),this.t+=1,t;case 205:return t=this.s.getUint16(this.t),this.t+=2,t;case 206:return t=this.s.getUint32(this.t),this.t+=4,t;case 207:return e=this.s.getUint32(this.t)*Math.pow(2,32),h=this.s.getUint32(this.t+4),this.t+=8,e+h;case 208:return t=this.s.getInt8(this.t),this.t+=1,t;case 209:return t=this.s.getInt16(this.t),this.t+=2,t;case 210:return t=this.s.getInt32(this.t),this.t+=4,t;case 211:return e=this.s.getInt32(this.t)*Math.pow(2,32),h=this.s.getUint32(this.t+4),this.t+=8,e+h;case 212:return r=this.s.getInt8(this.t),this.t+=1,0===r?void(this.t+=1):[r,this.l(1)];case 213:return r=this.s.getInt8(this.t),this.t+=1,[r,this.l(2)];case 214:return r=this.s.getInt8(this.t),this.t+=1,-1===r?(t=this.s.getUint32(this.t),this.t+=4,new Date(1e3*t)):[r,this.l(4)];case 215:return r=this.s.getInt8(this.t),this.t+=1,0===r?(e=this.s.getInt32(this.t)*Math.pow(2,32),h=this.s.getUint32(this.t+4),this.t+=8,new Date(e+h)):-1!==r?[r,this.l(8)]:(e=this.s.getUint32(this.t),h=this.s.getUint32(this.t+4),this.t+=8,new Date(1e3*(***********(3&e)+h)+(e>>>2)/1e6));case 216:return r=this.s.getInt8(this.t),this.t+=1,[r,this.l(16)];case 217:return s=this.s.getUint8(this.t),this.t+=1,this.h(s);case 218:return s=this.s.getUint16(this.t),this.t+=2,this.h(s);case 219:return s=this.s.getUint32(this.t),this.t+=4,this.h(s);case 220:return s=this.s.getUint16(this.t),this.t+=2,this.g(s);case 221:return s=this.s.getUint32(this.t),this.t+=4,this.g(s);case 222:return s=this.s.getUint16(this.t),this.t+=2,this.M(s);case 223:return s=this.s.getUint32(this.t),this.t+=4,this.M(s)}throw new Error("Could not parse")};var d=4294967295,E=17179869183;function y(t,i,s){for(var r=0,e=0,h=s.length;e<h;e++)(r=s.charCodeAt(e))<128?t.setUint8(i++,r):(r<2048?t.setUint8(i++,192|r>>6):(r<55296||57344<=r?t.setUint8(i++,224|r>>12):(e++,r=65536+((1023&r)<<10|1023&s.charCodeAt(e)),t.setUint8(i++,240|r>>18),t.setUint8(i++,128|r>>12&63)),t.setUint8(i++,128|r>>6&63)),t.setUint8(i++,128|63&r))}function M(t){for(var i=0,s=0,r=0,e=t.length;r<e;r++)(i=t.charCodeAt(r))<128?s+=1:i<2048?s+=2:i<55296||57344<=i?s+=3:(r++,s+=4);return s}}();
