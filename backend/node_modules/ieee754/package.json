{"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.1.13", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": ["<PERSON><PERSON> <<EMAIL>>"], "devDependencies": {"airtap": "0.1.0", "standard": "*", "tape": "^4.0.0"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}}