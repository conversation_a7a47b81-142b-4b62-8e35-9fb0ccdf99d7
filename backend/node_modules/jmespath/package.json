{"name": "jmespath", "description": "JMESPath implementation in javascript", "version": "0.16.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jamesls.com/"}, "homepage": "https://github.com/jmespath/jmespath.js", "contributors": [], "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.11.1", "grunt-eslint": "^17.3.1", "mocha": "^2.1.0"}, "dependencies": {}, "main": "jmespath.js", "directories": {"test": "test"}, "engines": {"node": ">= 0.6.0"}, "repository": {"type": "git", "url": "git://github.com/jmespath/jmespath.js"}, "bugs": {"url": "http://github.com/jmespath/jmespath.js/issues", "mail": ""}, "license": "Apache-2.0", "keywords": ["jmespath", "jsonpath", "json", "xpath"], "scripts": {"test": "mocha test/"}}