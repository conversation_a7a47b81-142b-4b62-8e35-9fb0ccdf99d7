/*! jmespath 2016-03-22 */
!function(a){"use strict";function b(a){return null!==a?"[object Array]"===Object.prototype.toString.call(a):!1}function c(a){return null!==a?"[object Object]"===Object.prototype.toString.call(a):!1}function d(a,e){if(a===e)return!0;var f=Object.prototype.toString.call(a);if(f!==Object.prototype.toString.call(e))return!1;if(b(a)===!0){if(a.length!==e.length)return!1;for(var g=0;g<a.length;g++)if(d(a[g],e[g])===!1)return!1;return!0}if(c(a)===!0){var h={};for(var i in a)if(hasOwnProperty.call(a,i)){if(d(a[i],e[i])===!1)return!1;h[i]=!0}for(var j in e)if(hasOwnProperty.call(e,j)&&h[j]!==!0)return!1;return!0}return!1}function e(a){if(""===a||a===!1||null===a)return!0;if(b(a)&&0===a.length)return!0;if(c(a)){for(var d in a)if(a.hasOwnProperty(d))return!1;return!0}return!1}function f(a){for(var b=Object.keys(a),c=[],d=0;d<b.length;d++)c.push(a[b[d]]);return c}function g(a){return a>="a"&&"z">=a||a>="A"&&"Z">=a||"_"===a}function h(a){return a>="0"&&"9">=a||"-"===a}function i(a){return a>="a"&&"z">=a||a>="A"&&"Z">=a||a>="0"&&"9">=a||"_"===a}function j(){}function k(){}function l(a){this.runtime=a}function m(a){this.a=a,this.functionTable={abs:{b:this.c,d:[{types:[r]}]},avg:{b:this.e,d:[{types:[z]}]},ceil:{b:this.f,d:[{types:[r]}]},contains:{b:this.g,d:[{types:[t,u]},{types:[s]}]},ends_with:{b:this.h,d:[{types:[t]},{types:[t]}]},floor:{b:this.i,d:[{types:[r]}]},length:{b:this.j,d:[{types:[t,u,v]}]},map:{b:this.k,d:[{types:[x]},{types:[u]}]},max:{b:this.l,d:[{types:[z,A]}]},merge:{b:this.m,d:[{types:[v],variadic:!0}]},max_by:{b:this.n,d:[{types:[u]},{types:[x]}]},sum:{b:this.o,d:[{types:[z]}]},starts_with:{b:this.p,d:[{types:[t]},{types:[t]}]},min:{b:this.q,d:[{types:[z,A]}]},min_by:{b:this.r,d:[{types:[u]},{types:[x]}]},type:{b:this.s,d:[{types:[s]}]},keys:{b:this.t,d:[{types:[v]}]},values:{b:this.u,d:[{types:[v]}]},sort:{b:this.v,d:[{types:[A,z]}]},sort_by:{b:this.w,d:[{types:[u]},{types:[x]}]},join:{b:this.x,d:[{types:[t]},{types:[A]}]},reverse:{b:this.y,d:[{types:[t,u]}]},to_array:{b:this.z,d:[{types:[s]}]},to_string:{b:this.A,d:[{types:[s]}]},to_number:{b:this.B,d:[{types:[s]}]},not_null:{b:this.C,d:[{types:[s],variadic:!0}]}}}function n(a){var b=new k,c=b.parse(a);return c}function o(a){var b=new j;return b.tokenize(a)}function p(a,b){var c=new k,d=new m,e=new l(d);d.a=e;var f=c.parse(b);return e.search(f,a)}var q;q="function"==typeof String.prototype.trimLeft?function(a){return a.trimLeft()}:function(a){return a.match(/^\s*(.*)/)[1]};var r=0,s=1,t=2,u=3,v=4,w=5,x=6,y=7,z=8,A=9,B="EOF",C="UnquotedIdentifier",D="QuotedIdentifier",E="Rbracket",F="Rparen",G="Comma",H="Colon",I="Rbrace",J="Number",K="Current",L="Expref",M="Pipe",N="Or",O="And",P="EQ",Q="GT",R="LT",S="GTE",T="LTE",U="NE",V="Flatten",W="Star",X="Filter",Y="Dot",Z="Not",$="Lbrace",_="Lbracket",aa="Lparen",ba="Literal",ca={".":Y,"*":W,",":G,":":H,"{":$,"}":I,"]":E,"(":aa,")":F,"@":K},da={"<":!0,">":!0,"=":!0,"!":!0},ea={" ":!0,"	":!0,"\n":!0};j.prototype={tokenize:function(a){var b=[];this.D=0;for(var c,d,e;this.D<a.length;)if(g(a[this.D]))c=this.D,d=this.E(a),b.push({type:C,value:d,start:c});else if(void 0!==ca[a[this.D]])b.push({type:ca[a[this.D]],value:a[this.D],start:this.D}),this.D++;else if(h(a[this.D]))e=this.F(a),b.push(e);else if("["===a[this.D])e=this.G(a),b.push(e);else if('"'===a[this.D])c=this.D,d=this.H(a),b.push({type:D,value:d,start:c});else if("'"===a[this.D])c=this.D,d=this.I(a),b.push({type:ba,value:d,start:c});else if("`"===a[this.D]){c=this.D;var f=this.J(a);b.push({type:ba,value:f,start:c})}else if(void 0!==da[a[this.D]])b.push(this.K(a));else if(void 0!==ea[a[this.D]])this.D++;else if("&"===a[this.D])c=this.D,this.D++,"&"===a[this.D]?(this.D++,b.push({type:O,value:"&&",start:c})):b.push({type:L,value:"&",start:c});else{if("|"!==a[this.D]){var i=new Error("Unknown character:"+a[this.D]);throw i.name="LexerError",i}c=this.D,this.D++,"|"===a[this.D]?(this.D++,b.push({type:N,value:"||",start:c})):b.push({type:M,value:"|",start:c})}return b},E:function(a){var b=this.D;for(this.D++;this.D<a.length&&i(a[this.D]);)this.D++;return a.slice(b,this.D)},H:function(a){var b=this.D;this.D++;for(var c=a.length;'"'!==a[this.D]&&this.D<c;){var d=this.D;"\\"!==a[d]||"\\"!==a[d+1]&&'"'!==a[d+1]?d++:d+=2,this.D=d}return this.D++,JSON.parse(a.slice(b,this.D))},I:function(a){var b=this.D;this.D++;for(var c=a.length;"'"!==a[this.D]&&this.D<c;){var d=this.D;"\\"!==a[d]||"\\"!==a[d+1]&&"'"!==a[d+1]?d++:d+=2,this.D=d}this.D++;var e=a.slice(b+1,this.D-1);return e.replace("\\'","'")},F:function(a){var b=this.D;this.D++;for(var c=a.length;h(a[this.D])&&this.D<c;)this.D++;var d=parseInt(a.slice(b,this.D));return{type:J,value:d,start:b}},G:function(a){var b=this.D;return this.D++,"?"===a[this.D]?(this.D++,{type:X,value:"[?",start:b}):"]"===a[this.D]?(this.D++,{type:V,value:"[]",start:b}):{type:_,value:"[",start:b}},K:function(a){var b=this.D,c=a[b];return this.D++,"!"===c?"="===a[this.D]?(this.D++,{type:U,value:"!=",start:b}):{type:Z,value:"!",start:b}:"<"===c?"="===a[this.D]?(this.D++,{type:T,value:"<=",start:b}):{type:R,value:"<",start:b}:">"===c?"="===a[this.D]?(this.D++,{type:S,value:">=",start:b}):{type:Q,value:">",start:b}:"="===c&&"="===a[this.D]?(this.D++,{type:P,value:"==",start:b}):void 0},J:function(a){this.D++;for(var b,c=this.D,d=a.length;"`"!==a[this.D]&&this.D<d;){var e=this.D;"\\"!==a[e]||"\\"!==a[e+1]&&"`"!==a[e+1]?e++:e+=2,this.D=e}var f=q(a.slice(c,this.D));return f=f.replace("\\`","`"),b=this.L(f)?JSON.parse(f):JSON.parse('"'+f+'"'),this.D++,b},L:function(a){var b='[{"',c=["true","false","null"],d="-0123456789";if(""===a)return!1;if(b.indexOf(a[0])>=0)return!0;if(c.indexOf(a)>=0)return!0;if(!(d.indexOf(a[0])>=0))return!1;try{return JSON.parse(a),!0}catch(e){return!1}}};var fa={};fa[B]=0,fa[C]=0,fa[D]=0,fa[E]=0,fa[F]=0,fa[G]=0,fa[I]=0,fa[J]=0,fa[K]=0,fa[L]=0,fa[M]=1,fa[N]=2,fa[O]=3,fa[P]=5,fa[Q]=5,fa[R]=5,fa[S]=5,fa[T]=5,fa[U]=5,fa[V]=9,fa[W]=20,fa[X]=21,fa[Y]=40,fa[Z]=45,fa[$]=50,fa[_]=55,fa[aa]=60,k.prototype={parse:function(a){this.M(a),this.index=0;var b=this.expression(0);if(this.N(0)!==B){var c=this.O(0),d=new Error("Unexpected token type: "+c.type+", value: "+c.value);throw d.name="ParserError",d}return b},M:function(a){var b=new j,c=b.tokenize(a);c.push({type:B,value:"",start:a.length}),this.tokens=c},expression:function(a){var b=this.O(0);this.P();for(var c=this.nud(b),d=this.N(0);a<fa[d];)this.P(),c=this.led(d,c),d=this.N(0);return c},N:function(a){return this.tokens[this.index+a].type},O:function(a){return this.tokens[this.index+a]},P:function(){this.index++},nud:function(a){var b,c,d;switch(a.type){case ba:return{type:"Literal",value:a.value};case C:return{type:"Field",name:a.value};case D:var e={type:"Field",name:a.value};if(this.N(0)===aa)throw new Error("Quoted identifier not allowed for function names.");return e;case Z:return c=this.expression(fa.Not),{type:"NotExpression",children:[c]};case W:return b={type:"Identity"},c=null,c=this.N(0)===E?{type:"Identity"}:this.Q(fa.Star),{type:"ValueProjection",children:[b,c]};case X:return this.led(a.type,{type:"Identity"});case $:return this.R();case V:return b={type:V,children:[{type:"Identity"}]},c=this.Q(fa.Flatten),{type:"Projection",children:[b,c]};case _:return this.N(0)===J||this.N(0)===H?(c=this.S(),this.T({type:"Identity"},c)):this.N(0)===W&&this.N(1)===E?(this.P(),this.P(),c=this.Q(fa.Star),{type:"Projection",children:[{type:"Identity"},c]}):this.U();case K:return{type:K};case L:return d=this.expression(fa.Expref),{type:"ExpressionReference",children:[d]};case aa:for(var f=[];this.N(0)!==F;)this.N(0)===K?(d={type:K},this.P()):d=this.expression(0),f.push(d);return this.V(F),f[0];default:this.W(a)}},led:function(a,b){var c;switch(a){case Y:var d=fa.Dot;return this.N(0)!==W?(c=this.X(d),{type:"Subexpression",children:[b,c]}):(this.P(),c=this.Q(d),{type:"ValueProjection",children:[b,c]});case M:return c=this.expression(fa.Pipe),{type:M,children:[b,c]};case N:return c=this.expression(fa.Or),{type:"OrExpression",children:[b,c]};case O:return c=this.expression(fa.And),{type:"AndExpression",children:[b,c]};case aa:for(var e,f,g=b.name,h=[];this.N(0)!==F;)this.N(0)===K?(e={type:K},this.P()):e=this.expression(0),this.N(0)===G&&this.V(G),h.push(e);return this.V(F),f={type:"Function",name:g,children:h};case X:var i=this.expression(0);return this.V(E),c=this.N(0)===V?{type:"Identity"}:this.Q(fa.Filter),{type:"FilterProjection",children:[b,c,i]};case V:var j={type:V,children:[b]},k=this.Q(fa.Flatten);return{type:"Projection",children:[j,k]};case P:case U:case Q:case S:case R:case T:return this.Y(b,a);case _:var l=this.O(0);return l.type===J||l.type===H?(c=this.S(),this.T(b,c)):(this.V(W),this.V(E),c=this.Q(fa.Star),{type:"Projection",children:[b,c]});default:this.W(this.O(0))}},V:function(a){if(this.N(0)!==a){var b=this.O(0),c=new Error("Expected "+a+", got: "+b.type);throw c.name="ParserError",c}this.P()},W:function(a){var b=new Error("Invalid token ("+a.type+'): "'+a.value+'"');throw b.name="ParserError",b},S:function(){if(this.N(0)===H||this.N(1)===H)return this.Z();var a={type:"Index",value:this.O(0).value};return this.P(),this.V(E),a},T:function(a,b){var c={type:"IndexExpression",children:[a,b]};return"Slice"===b.type?{type:"Projection",children:[c,this.Q(fa.Star)]}:c},Z:function(){for(var a=[null,null,null],b=0,c=this.N(0);c!==E&&3>b;){if(c===H)b++,this.P();else{if(c!==J){var d=this.N(0),e=new Error("Syntax error, unexpected token: "+d.value+"("+d.type+")");throw e.name="Parsererror",e}a[b]=this.O(0).value,this.P()}c=this.N(0)}return this.V(E),{type:"Slice",children:a}},Y:function(a,b){var c=this.expression(fa[b]);return{type:"Comparator",name:b,children:[a,c]}},X:function(a){var b=this.N(0),c=[C,D,W];return c.indexOf(b)>=0?this.expression(a):b===_?(this.V(_),this.U()):b===$?(this.V($),this.R()):void 0},Q:function(a){var b;if(fa[this.N(0)]<10)b={type:"Identity"};else if(this.N(0)===_)b=this.expression(a);else if(this.N(0)===X)b=this.expression(a);else{if(this.N(0)!==Y){var c=this.O(0),d=new Error("Sytanx error, unexpected token: "+c.value+"("+c.type+")");throw d.name="ParserError",d}this.V(Y),b=this.X(a)}return b},U:function(){for(var a=[];this.N(0)!==E;){var b=this.expression(0);if(a.push(b),this.N(0)===G&&(this.V(G),this.N(0)===E))throw new Error("Unexpected token Rbracket")}return this.V(E),{type:"MultiSelectList",children:a}},R:function(){for(var a,b,c,d,e=[],f=[C,D];;){if(a=this.O(0),f.indexOf(a.type)<0)throw new Error("Expecting an identifier token, got: "+a.type);if(b=a.value,this.P(),this.V(H),c=this.expression(0),d={type:"KeyValuePair",name:b,value:c},e.push(d),this.N(0)===G)this.V(G);else if(this.N(0)===I){this.V(I);break}}return{type:"MultiSelectHash",children:e}}},l.prototype={search:function(a,b){return this.visit(a,b)},visit:function(a,g){var h,i,j,k,l,m,n,o,p,q;switch(a.type){case"Field":return null===g?null:c(g)?(m=g[a.name],void 0===m?null:m):null;case"Subexpression":for(j=this.visit(a.children[0],g),q=1;q<a.children.length;q++)if(j=this.visit(a.children[1],j),null===j)return null;return j;case"IndexExpression":return n=this.visit(a.children[0],g),o=this.visit(a.children[1],n);case"Index":if(!b(g))return null;var r=a.value;return 0>r&&(r=g.length+r),j=g[r],void 0===j&&(j=null),j;case"Slice":if(!b(g))return null;var s=a.children.slice(0),t=this.computeSliceParams(g.length,s),u=t[0],v=t[1],w=t[2];if(j=[],w>0)for(q=u;v>q;q+=w)j.push(g[q]);else for(q=u;q>v;q+=w)j.push(g[q]);return j;case"Projection":var x=this.visit(a.children[0],g);if(!b(x))return null;for(p=[],q=0;q<x.length;q++)i=this.visit(a.children[1],x[q]),null!==i&&p.push(i);return p;case"ValueProjection":if(x=this.visit(a.children[0],g),!c(x))return null;p=[];var y=f(x);for(q=0;q<y.length;q++)i=this.visit(a.children[1],y[q]),null!==i&&p.push(i);return p;case"FilterProjection":if(x=this.visit(a.children[0],g),!b(x))return null;var z=[],A=[];for(q=0;q<x.length;q++)h=this.visit(a.children[2],x[q]),e(h)||z.push(x[q]);for(var B=0;B<z.length;B++)i=this.visit(a.children[1],z[B]),null!==i&&A.push(i);return A;case"Comparator":switch(k=this.visit(a.children[0],g),l=this.visit(a.children[1],g),a.name){case P:j=d(k,l);break;case U:j=!d(k,l);break;case Q:j=k>l;break;case S:j=k>=l;break;case R:j=l>k;break;case T:j=l>=k;break;default:throw new Error("Unknown comparator: "+a.name)}return j;case V:var C=this.visit(a.children[0],g);if(!b(C))return null;var D=[];for(q=0;q<C.length;q++)i=C[q],b(i)?D.push.apply(D,i):D.push(i);return D;case"Identity":return g;case"MultiSelectList":if(null===g)return null;for(p=[],q=0;q<a.children.length;q++)p.push(this.visit(a.children[q],g));return p;case"MultiSelectHash":if(null===g)return null;p={};var E;for(q=0;q<a.children.length;q++)E=a.children[q],p[E.name]=this.visit(E.value,g);return p;case"OrExpression":return h=this.visit(a.children[0],g),e(h)&&(h=this.visit(a.children[1],g)),h;case"AndExpression":return k=this.visit(a.children[0],g),e(k)===!0?k:this.visit(a.children[1],g);case"NotExpression":return k=this.visit(a.children[0],g),e(k);case"Literal":return a.value;case M:return n=this.visit(a.children[0],g),this.visit(a.children[1],n);case K:return g;case"Function":var F=[];for(q=0;q<a.children.length;q++)F.push(this.visit(a.children[q],g));return this.runtime.callFunction(a.name,F);case"ExpressionReference":var G=a.children[0];return G.jmespathType=L,G;default:throw new Error("Unknown node type: "+a.type)}},computeSliceParams:function(a,b){var c=b[0],d=b[1],e=b[2],f=[null,null,null];if(null===e)e=1;else if(0===e){var g=new Error("Invalid slice, step cannot be 0");throw g.name="RuntimeError",g}var h=0>e?!0:!1;return c=null===c?h?a-1:0:this.capSliceRange(a,c,e),d=null===d?h?-1:a:this.capSliceRange(a,d,e),f[0]=c,f[1]=d,f[2]=e,f},capSliceRange:function(a,b,c){return 0>b?(b+=a,0>b&&(b=0>c?-1:0)):b>=a&&(b=0>c?a-1:a),b}},m.prototype={callFunction:function(a,b){var c=this.functionTable[a];if(void 0===c)throw new Error("Unknown function: "+a+"()");return this.$(a,b,c.d),c.b.call(this,b)},$:function(a,b,c){var d;if(c[c.length-1].variadic){if(b.length<c.length)throw d=1===c.length?" argument":" arguments",new Error("ArgumentError: "+a+"() takes at least"+c.length+d+" but received "+b.length)}else if(b.length!==c.length)throw d=1===c.length?" argument":" arguments",new Error("ArgumentError: "+a+"() takes "+c.length+d+" but received "+b.length);for(var e,f,g,h=0;h<c.length;h++){g=!1,e=c[h].types,f=this._(b[h]);for(var i=0;i<e.length;i++)if(this.aa(f,e[i],b[h])){g=!0;break}if(!g)throw new Error("TypeError: "+a+"() expected argument "+(h+1)+" to be type "+e+" but received type "+f+" instead.")}},aa:function(a,b,c){if(b===s)return!0;if(b!==A&&b!==z&&b!==u)return a===b;if(b===u)return a===u;if(a===u){var d;b===z?d=r:b===A&&(d=t);for(var e=0;e<c.length;e++)if(!this.aa(this._(c[e]),d,c[e]))return!1;return!0}},_:function(a){switch(Object.prototype.toString.call(a)){case"[object String]":return t;case"[object Number]":return r;case"[object Array]":return u;case"[object Boolean]":return w;case"[object Null]":return y;case"[object Object]":return a.jmespathType===L?x:v}},p:function(a){return 0===a[0].lastIndexOf(a[1])},h:function(a){var b=a[0],c=a[1];return-1!==b.indexOf(c,b.length-c.length)},y:function(a){var b=this._(a[0]);if(b===t){for(var c=a[0],d="",e=c.length-1;e>=0;e--)d+=c[e];return d}var f=a[0].slice(0);return f.reverse(),f},c:function(a){return Math.abs(a[0])},f:function(a){return Math.ceil(a[0])},e:function(a){for(var b=0,c=a[0],d=0;d<c.length;d++)b+=c[d];return b/c.length},g:function(a){return a[0].indexOf(a[1])>=0},i:function(a){return Math.floor(a[0])},j:function(a){return c(a[0])?Object.keys(a[0]).length:a[0].length},k:function(a){for(var b=[],c=this.a,d=a[0],e=a[1],f=0;f<e.length;f++)b.push(c.visit(d,e[f]));return b},m:function(a){for(var b={},c=0;c<a.length;c++){var d=a[c];for(var e in d)b[e]=d[e]}return b},l:function(a){if(a[0].length>0){var b=this._(a[0][0]);if(b===r)return Math.max.apply(Math,a[0]);for(var c=a[0],d=c[0],e=1;e<c.length;e++)d.localeCompare(c[e])<0&&(d=c[e]);return d}return null},q:function(a){if(a[0].length>0){var b=this._(a[0][0]);if(b===r)return Math.min.apply(Math,a[0]);for(var c=a[0],d=c[0],e=1;e<c.length;e++)c[e].localeCompare(d)<0&&(d=c[e]);return d}return null},o:function(a){for(var b=0,c=a[0],d=0;d<c.length;d++)b+=c[d];return b},s:function(a){switch(this._(a[0])){case r:return"number";case t:return"string";case u:return"array";case v:return"object";case w:return"boolean";case x:return"expref";case y:return"null"}},t:function(a){return Object.keys(a[0])},u:function(a){for(var b=a[0],c=Object.keys(b),d=[],e=0;e<c.length;e++)d.push(b[c[e]]);return d},x:function(a){var b=a[0],c=a[1];return c.join(b)},z:function(a){return this._(a[0])===u?a[0]:[a[0]]},A:function(a){return this._(a[0])===t?a[0]:JSON.stringify(a[0])},B:function(a){var b,c=this._(a[0]);return c===r?a[0]:c!==t||(b=+a[0],isNaN(b))?null:b},C:function(a){for(var b=0;b<a.length;b++)if(this._(a[b])!==y)return a[b];return null},v:function(a){var b=a[0].slice(0);return b.sort(),b},w:function(a){var b=a[0].slice(0);if(0===b.length)return b;var c=this.a,d=a[1],e=this._(c.visit(d,b[0]));if([r,t].indexOf(e)<0)throw new Error("TypeError");for(var f=this,g=[],h=0;h<b.length;h++)g.push([h,b[h]]);g.sort(function(a,b){var g=c.visit(d,a[1]),h=c.visit(d,b[1]);if(f._(g)!==e)throw new Error("TypeError: expected "+e+", received "+f._(g));if(f._(h)!==e)throw new Error("TypeError: expected "+e+", received "+f._(h));return g>h?1:h>g?-1:a[0]-b[0]});for(var i=0;i<g.length;i++)b[i]=g[i][1];return b},n:function(a){for(var b,c,d=a[1],e=a[0],f=this.createKeyFunction(d,[r,t]),g=-(1/0),h=0;h<e.length;h++)c=f(e[h]),c>g&&(g=c,b=e[h]);return b},r:function(a){for(var b,c,d=a[1],e=a[0],f=this.createKeyFunction(d,[r,t]),g=1/0,h=0;h<e.length;h++)c=f(e[h]),g>c&&(g=c,b=e[h]);return b},createKeyFunction:function(a,b){var c=this,d=this.a,e=function(e){var f=d.visit(a,e);if(b.indexOf(c._(f))<0){var g="TypeError: expected one of "+b+", received "+c._(f);throw new Error(g)}return f};return e}},a.tokenize=o,a.compile=n,a.search=p,a.strictDeepEqual=d}("undefined"==typeof exports?this.jmespath={}:exports);