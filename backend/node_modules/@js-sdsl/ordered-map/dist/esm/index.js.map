{"version": 3, "sources": ["../../../../../node_modules/tslib/tslib.es6.js", "index.js", "../../../.build-data/copied-source/src/container/TreeContainer/Base/TreeNode.ts", "../../../.build-data/copied-source/src/container/ContainerBase/index.ts", "../../../.build-data/copied-source/src/utils/throwError.ts", "../../../.build-data/copied-source/src/container/TreeContainer/Base/index.ts", "../../../.build-data/copied-source/src/container/TreeContainer/Base/TreeIterator.ts", "../../../.build-data/copied-source/src/container/TreeContainer/OrderedMap.ts"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "__extends", "TypeError", "String", "__", "this", "constructor", "create", "__generator", "thisArg", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "next", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "step", "op", "done", "value", "pop", "length", "push", "e", "SuppressedError", "error", "suppressed", "message", "Error", "name", "TreeNode", "key", "color", "_left", "undefined", "_right", "_parent", "_key", "_value", "_color", "_pre", "preNode", "isRootOrHeader", "pre", "_next", "nextNode", "_rotateLeft", "PP", "V", "R", "_rotateRight", "F", "K", "TreeNodeEnableIndex", "_super", "_this", "apply", "arguments", "_subTreeSize", "parent", "_recount", "ContainerIterator", "iteratorType", "equals", "iter", "_node", "Base", "_length", "defineProperty", "get", "enumerable", "configurable", "size", "empty", "Container", "throwIteratorAccessError", "RangeError", "TreeC<PERSON>r", "cmp", "enableIndex", "x", "_root", "_cmp", "_TreeNodeClass", "_header", "_lowerBound", "curNode", "resNode", "cmpResult", "_upperBound", "_reverseLowerBound", "_reverseUpperBound", "_eraseNodeSelfBalance", "parentNode", "brother", "_eraseNode", "clear", "swapNode", "_inOrderTraversal", "param", "pos", "callback", "nodeList", "index", "stack", "_insertNodeSelfBalance", "grandParent", "uncle", "GP", "_set", "hint", "minNode", "compareToMin", "maxNode", "compareToMax", "iterNode", "iterCmpRes", "preCmpRes", "parent_1", "_getTreeNodeByKey", "updateKeyByIterator", "node", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "eraseElementByPos", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eraseElementByIterator", "hasNoRight", "isNormal", "getHeight", "traversal", "Math", "max", "TreeIterator", "header", "root", "isAccessible", "OrderedMapIterator", "container", "self", "Proxy", "target", "prop", "set", "newValue", "copy", "OrderedMap", "for<PERSON>ach", "el", "setElement", "begin", "end", "rBegin", "rEnd", "front", "back", "lowerBound", "upperBound", "reverseLowerBound", "reverseUpperBound", "map", "getElementByPos", "find", "getElement<PERSON>y<PERSON>ey", "union", "other", "i", "_a"], "mappings": "AAgBA,IAAIA,gBAAgB,SAASC,GAAGC;IAC5BF,gBAAgBG,OAAOC,kBAClB;QAAEC,WAAW;iBAAgBC,SAAS,SAAUL,GAAGC;QAAKD,EAAEI,YAAYH;AAAG,SAC1E,SAAUD,GAAGC;QAAK,KAAK,IAAIK,KAAKL,GAAG,IAAIC,OAAOK,UAAUC,eAAeC,KAAKR,GAAGK,IAAIN,EAAEM,KAAKL,EAAEK;ACIlG;IDHE,OAAOP,cAAcC,GAAGC;AAC5B;;AAEO,SAASS,UAAUV,GAAGC;IACzB,WAAWA,MAAM,cAAcA,MAAM,MACjC,MAAM,IAAIU,UAAU,yBAAyBC,OAAOX,KAAK;IAC7DF,cAAcC,GAAGC;IACjB,SAASY;QAAOC,KAAKC,cAAcf;AAAG;IACtCA,EAAEO,YAAYN,MAAM,OAAOC,OAAOc,OAAOf,MAAMY,GAAGN,YAAYN,EAAEM,WAAW,IAAIM;AACnF;;AA+FO,SAASI,YAAYC,GAASC;IACjC,IAAIC,IAAI;QAAEC,OAAO;QAAGC,MAAM;YAAa,IAAIC,EAAE,KAAK,GAAG,MAAMA,EAAE;YAAI,OAAOA,EAAE;ACrFxE;QDqF+EC,MAAM;QAAIC,KAAK;OAAMC,GAAGC,GAAGJ,GAAGK;IAC/G,OAAOA,IAAI;QAAEC,MAAMC,KAAK;QAAIC,OAASD,KAAK;QAAIE,QAAUF,KAAK;cAAaG,WAAW,eAAeL,EAAEK,OAAOC,YAAY;QAAa,OAAOpB;ACxE/I,QDwEyJc;IACvJ,SAASE,KAAKK;QAAK,OAAO,SAAUC;YAAK,OAAOC,KAAK,EAACF,GAAGC;ACrEzD;ADqEiE;IACjE,SAASC,KAAKC;QACV,IAAIZ,GAAG,MAAM,IAAIf,UAAU;QAC3B,OAAOiB,MAAMA,IAAI,GAAGU,EAAG,OAAOlB,IAAI,KAAKA;YACnC,IAAIM,IAAI,GAAGC,MAAMJ,IAAIe,EAAG,KAAK,IAAIX,EAAE,YAAYW,EAAG,KAAKX,EAAE,cAAcJ,IAAII,EAAE,cAAcJ,EAAEd,KAAKkB;YAAI,KAAKA,EAAEE,WAAWN,IAAIA,EAAEd,KAAKkB,GAAGW,EAAG,KAAKC,MAAM,OAAOhB;YAC3J,IAAII,IAAI,GAAGJ,GAAGe,IAAK,EAACA,EAAG,KAAK,GAAGf,EAAEiB;YACjC,QAAQF,EAAG;cACP,KAAK;cAAG,KAAK;gBAAGf,IAAIe;gBAAI;;cACxB,KAAK;gBAAGlB,EAAEC;gBAAS,OAAO;oBAAEmB,OAAOF,EAAG;oBAAIC,MAAM;;;cAChD,KAAK;gBAAGnB,EAAEC;gBAASM,IAAIW,EAAG;gBAAIA,IAAK,EAAC;gBAAI;;cACxC,KAAK;gBAAGA,IAAKlB,EAAEK,IAAIgB;gBAAOrB,EAAEI,KAAKiB;gBAAO;;cACxC;gBACI,MAAMlB,IAAIH,EAAEI,MAAMD,IAAIA,EAAEmB,SAAS,KAAKnB,EAAEA,EAAEmB,SAAS,QAAQJ,EAAG,OAAO,KAAKA,EAAG,OAAO,IAAI;oBAAElB,IAAI;oBAAG;AAAU;gBAC3G,IAAIkB,EAAG,OAAO,OAAOf,KAAMe,EAAG,KAAKf,EAAE,MAAMe,EAAG,KAAKf,EAAE,KAAM;oBAAEH,EAAEC,QAAQiB,EAAG;oBAAI;AAAO;gBACrF,IAAIA,EAAG,OAAO,KAAKlB,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIA,IAAIe;oBAAI;AAAO;gBACpE,IAAIf,KAAKH,EAAEC,QAAQE,EAAE,IAAI;oBAAEH,EAAEC,QAAQE,EAAE;oBAAIH,EAAEK,IAAIkB,KAAKL;oBAAK;AAAO;gBAClE,IAAIf,EAAE,IAAIH,EAAEK,IAAIgB;gBAChBrB,EAAEI,KAAKiB;gBAAO;;YAEtBH,IAAKnB,EAAKV,KAAKS,GAASE;ACrChC,UDsCM,OAAOwB;YAAKN,IAAK,EAAC,GAAGM;YAAIjB,IAAI;AAAG,UAAC;YAAWD,IAAIH,IAAI;AAAG;QACzD,IAAIe,EAAG,KAAK,GAAG,MAAMA,EAAG;QAAI,OAAO;YAAEE,OAAOF,EAAG,KAAKA,EAAG,UAAU;YAAGC,MAAM;;AAC9E;AACJ;;OAqK8BM,oBAAoB,aAAaA,kBAAkB,SAAUC,GAAOC,GAAYC;IAC1G,IAAIJ,IAAI,IAAIK,MAAMD;IAClB,OAAOJ,EAAEM,OAAO,mBAAmBN,EAAEE,QAAQA,GAAOF,EAAEG,aAAaA,GAAYH;AACnF;;AEzTA,IAAAO,WAAA;IAOE,SAAAA,SACEC,GACAZ,GACAa;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAwC;AAAA;QAN1CvC,KAAKwC,IAA+BC;QACpCzC,KAAM0C,IAA+BD;QACrCzC,KAAO2C,IAA+BF;QAMpCzC,KAAK4C,IAAON;QACZtC,KAAK6C,IAASnB;QACd1B,KAAK8C,IAASP;AACf;IAKDF,SAAA5C,UAAAsD,IAAA;QACE,IAAIC,IAA0BhD;QAC9B,IAAMiD,IAAiBD,EAAQL,EAASA,MAAYK;QACpD,IAAIC,KAAkBD,EAAQF,MAAM,GAAwB;YAC1DE,IAAUA,EAAQN;AACnB,eAAM,IAAIM,EAAQR,GAAO;YACxBQ,IAAUA,EAAQR;YAClB,OAAOQ,EAAQN,GAAQ;gBACrBM,IAAUA,EAAQN;AACnB;AACF,eAAM;YAEL,IAAIO,GAAgB;gBAClB,OAAOD,EAAQL;AAChB;YACD,IAAIO,IAAMF,EAAQL;YAClB,OAAOO,EAAIV,MAAUQ,GAAS;gBAC5BA,IAAUE;gBACVA,IAAMF,EAAQL;AACf;YACDK,IAAUE;AACX;QACD,OAAOF;ADuHT;ICjHAX,SAAA5C,UAAA0D,IAAA;QACE,IAAIC,IAA2BpD;QAC/B,IAAIoD,EAASV,GAAQ;YACnBU,IAAWA,EAASV;YACpB,OAAOU,EAASZ,GAAO;gBACrBY,IAAWA,EAASZ;AACrB;YACD,OAAOY;AACR,eAAM;YACL,IAAIF,IAAME,EAAST;YACnB,OAAOO,EAAIR,MAAWU,GAAU;gBAC9BA,IAAWF;gBACXA,IAAME,EAAST;AAChB;YACD,IAAIS,EAASV,MAAWQ,GAAK;gBAC3B,OAAOA;ADuHT,mBCtHO,OAAOE;AACf;ADuHH;ICjHAf,SAAA5C,UAAA4D,IAAA;QACE,IAAMC,IAAKtD,KAAK2C;QAChB,IAAMY,IAAIvD,KAAK0C;QACf,IAAMc,IAAID,EAAEf;QAEZ,IAAIc,EAAGX,MAAY3C,MAAMsD,EAAGX,IAAUY,QACjC,IAAID,EAAGd,MAAUxC,MAAMsD,EAAGd,IAAQe,QAClCD,EAAGZ,IAASa;QAEjBA,EAAEZ,IAAUW;QACZC,EAAEf,IAAQxC;QAEVA,KAAK2C,IAAUY;QACfvD,KAAK0C,IAASc;QAEd,IAAIA,GAAGA,EAAEb,IAAU3C;QAEnB,OAAOuD;ADgHT;IC1GAlB,SAAA5C,UAAAgE,IAAA;QACE,IAAMH,IAAKtD,KAAK2C;QAChB,IAAMe,IAAI1D,KAAKwC;QACf,IAAMmB,IAAID,EAAEhB;QAEZ,IAAIY,EAAGX,MAAY3C,MAAMsD,EAAGX,IAAUe,QACjC,IAAIJ,EAAGd,MAAUxC,MAAMsD,EAAGd,IAAQkB,QAClCJ,EAAGZ,IAASgB;QAEjBA,EAAEf,IAAUW;QACZI,EAAEhB,IAAS1C;QAEXA,KAAK2C,IAAUe;QACf1D,KAAKwC,IAAQmB;QAEb,IAAIA,GAAGA,EAAEhB,IAAU3C;QAEnB,OAAO0D;ADyGT;ICvGF,OAACrB;AAAD,CAjHA;;AAmHA,IAAAuB,sBAAA,SAAAC;IAA+CjE,UAAcgE,qBAAAC;IAA7D,SAAAD;QAAA,IA+BCE,IAAAD,MAAA,QAAAA,EAAAE,MAAA/D,MAAAgE,cAAAhE;QA9BC8D,EAAYG,IAAG;QD4Gb,OAAOH;AC9EX;IAzBEF,oBAAAnE,UAAA4D,IAAA;QACE,IAAMa,IAASL,EAAMpE,UAAA4D,EAAW1D,KAAAK;QAChCA,KAAKmE;QACLD,EAAOC;QACP,OAAOD;AD8GT;ICxGAN,oBAAAnE,UAAAgE,IAAA;QACE,IAAMS,IAASL,EAAMpE,UAAAgE,EAAY9D,KAAAK;QACjCA,KAAKmE;QACLD,EAAOC;QACP,OAAOD;AD8GT;IC5GAN,oBAAAnE,UAAA0E,IAAA;QACEnE,KAAKiE,IAAe;QACpB,IAAIjE,KAAKwC,GAAO;YACdxC,KAAKiE,KAAiBjE,KAAKwC,EAAoCyB;AAChE;QACD,IAAIjE,KAAK0C,GAAQ;YACf1C,KAAKiE,KAAiBjE,KAAK0C,EAAqCuB;AACjE;AD8GH;IC5GF,OAACL;AAAD,CA/BA,CAA+CvB;;AChH/C,IAAA+B,oBAAA;IAkBE,SAAAA,kBAAsBC;QAAA,IAAAA,WAAA,GAAA;YAAAA,IAAkC;AAAA;QACtDrE,KAAKqE,eAAeA;AACrB;IAODD,kBAAM3E,UAAA6E,SAAN,SAAOC;QACL,OAAOvE,KAAKwE,MAAUD,EAAKC;AFqP7B;IEnMF,OAACJ;AAAD,CA9EA;;AAgFA,IAAAK,OAAA;IAAA,SAAAA;QAKYzE,KAAO0E,IAAG;AAmCtB;IA5BEtF,OAAAuF,eAAIF,KAAMhF,WAAA,UAAA;QAAVmF,KAAA;YACE,OAAO5E,KAAK0E;AFwMZ;QACAG,YAAY;QACZC,cAAc;;IElMhBL,KAAAhF,UAAAsF,OAAA;QACE,OAAO/E,KAAK0E;AF2Md;IEnMAD,KAAAhF,UAAAuF,QAAA;QACE,OAAOhF,KAAK0E,MAAY;AF2M1B;IElMF,OAACD;AAAD,CAxCA;;AA0CA,IAAAQ,YAAA,SAAApB;IAA2CjE,UAAIqF,WAAApB;IAA/C,SAAAoB;QFsMI,OAAOpB,MAAW,QAAQA,EAAOE,MAAM/D,MAAMgE,cAAchE;AEtG/D;IAAA,OAACiF;AAAD,CAhGA,CAA2CR;;AF+M3C,SG7UgBS;IACd,MAAM,IAAIC,WAAW;AACtB;;ACAD,IAAAC,gBAAA,SAAAvB;IAA2CjE,UAAqBwF,eAAAvB;IAqB9D,SACEuB,cAAAC,GAMAC;QANA,IAAAD,WAAA,GAAA;YAAAA,IAAA,SACUE,GAAM1E;gBACd,IAAI0E,IAAI1E,GAAG,QAAQ;gBACnB,IAAI0E,IAAI1E,GAAG,OAAO;gBAClB,OAAO;AJgUP;AI/TD;QACD,IAAAyE,WAAA,GAAA;YAAAA,IAAmB;AAAA;QAPrB,IAAAxB,IASED,EAAAA,KAAAA,SAKD7D;QA1BS8D,EAAK0B,IAA+B/C;QAsB5CqB,EAAK2B,IAAOJ;QACZvB,EAAKwB,cAAcA;QACnBxB,EAAK4B,IAAiBJ,IAAc1B,sBAAsBvB;QAC1DyB,EAAK6B,IAAU,IAAI7B,EAAK4B;QJsUxB,OAAO5B;AIrUR;IAISsB,cAAA3F,UAAAmG,IAAV,SAAsBC,GAAqCvD;QACzD,IAAIwD,IAAU9F,KAAK2F;QACnB,OAAOE,GAAS;YACd,IAAME,IAAY/F,KAAKyF,EAAKI,EAAQjD,GAAON;YAC3C,IAAIyD,IAAY,GAAG;gBACjBF,IAAUA,EAAQnD;AACnB,mBAAM,IAAIqD,IAAY,GAAG;gBACxBD,IAAUD;gBACVA,IAAUA,EAAQrD;AJuUpB,mBItUO,OAAOqD;AACf;QACD,OAAOC;AJuUT;IIlUUV,cAAA3F,UAAAuG,IAAV,SAAsBH,GAAqCvD;QACzD,IAAIwD,IAAU9F,KAAK2F;QACnB,OAAOE,GAAS;YACd,IAAME,IAAY/F,KAAKyF,EAAKI,EAAQjD,GAAON;YAC3C,IAAIyD,KAAa,GAAG;gBAClBF,IAAUA,EAAQnD;AACnB,mBAAM;gBACLoD,IAAUD;gBACVA,IAAUA,EAAQrD;AACnB;AACF;QACD,OAAOsD;AJuUT;IIlUUV,cAAA3F,UAAAwG,IAAV,SAA6BJ,GAAqCvD;QAChE,IAAIwD,IAAU9F,KAAK2F;QACnB,OAAOE,GAAS;YACd,IAAME,IAAY/F,KAAKyF,EAAKI,EAAQjD,GAAON;YAC3C,IAAIyD,IAAY,GAAG;gBACjBD,IAAUD;gBACVA,IAAUA,EAAQnD;AACnB,mBAAM,IAAIqD,IAAY,GAAG;gBACxBF,IAAUA,EAAQrD;AJuUpB,mBItUO,OAAOqD;AACf;QACD,OAAOC;AJuUT;IIlUUV,cAAA3F,UAAAyG,IAAV,SAA6BL,GAAqCvD;QAChE,IAAIwD,IAAU9F,KAAK2F;QACnB,OAAOE,GAAS;YACd,IAAME,IAAY/F,KAAKyF,EAAKI,EAAQjD,GAAON;YAC3C,IAAIyD,IAAY,GAAG;gBACjBD,IAAUD;gBACVA,IAAUA,EAAQnD;AACnB,mBAAM;gBACLmD,IAAUA,EAAQrD;AACnB;AACF;QACD,OAAOsD;AJuUT;IIlUUV,cAAqB3F,UAAA0G,IAA/B,SAAgCN;QAC9B,OAAO,MAAM;YACX,IAAMO,IAAaP,EAAQlD;YAC3B,IAAIyD,MAAepG,KAAK2F,GAAS;YACjC,IAAIE,EAAQ/C,MAAM,GAAwB;gBACxC+C,EAAQ/C,IAAM;gBACd;AACD;YACD,IAAI+C,MAAYO,EAAW5D,GAAO;gBAChC,IAAM6D,IAAUD,EAAW1D;gBAC3B,IAAI2D,EAAQvD,MAAM,GAAwB;oBACxCuD,EAAQvD,IAAM;oBACdsD,EAAWtD,IAAM;oBACjB,IAAIsD,MAAepG,KAAKwF,GAAO;wBAC7BxF,KAAKwF,IAAQY,EAAW/C;AACzB,2BAAM+C,EAAW/C;AACnB,uBAAM;oBACL,IAAIgD,EAAQ3D,KAAU2D,EAAQ3D,EAAOI,MAAM,GAAwB;wBACjEuD,EAAQvD,IAASsD,EAAWtD;wBAC5BsD,EAAWtD,IAAM;wBACjBuD,EAAQ3D,EAAOI,IAAM;wBACrB,IAAIsD,MAAepG,KAAKwF,GAAO;4BAC7BxF,KAAKwF,IAAQY,EAAW/C;AACzB,+BAAM+C,EAAW/C;wBAClB;AACD,2BAAM,IAAIgD,EAAQ7D,KAAS6D,EAAQ7D,EAAMM,MAAM,GAAwB;wBACtEuD,EAAQvD,IAAM;wBACduD,EAAQ7D,EAAMM,IAAM;wBACpBuD,EAAQ5C;AACT,2BAAM;wBACL4C,EAAQvD,IAAM;wBACd+C,IAAUO;AACX;AACF;AACF,mBAAM;gBACL,IAAMC,IAAUD,EAAW5D;gBAC3B,IAAI6D,EAAQvD,MAAM,GAAwB;oBACxCuD,EAAQvD,IAAM;oBACdsD,EAAWtD,IAAM;oBACjB,IAAIsD,MAAepG,KAAKwF,GAAO;wBAC7BxF,KAAKwF,IAAQY,EAAW3C;AACzB,2BAAM2C,EAAW3C;AACnB,uBAAM;oBACL,IAAI4C,EAAQ7D,KAAS6D,EAAQ7D,EAAMM,MAAM,GAAwB;wBAC/DuD,EAAQvD,IAASsD,EAAWtD;wBAC5BsD,EAAWtD,IAAM;wBACjBuD,EAAQ7D,EAAMM,IAAM;wBACpB,IAAIsD,MAAepG,KAAKwF,GAAO;4BAC7BxF,KAAKwF,IAAQY,EAAW3C;AACzB,+BAAM2C,EAAW3C;wBAClB;AACD,2BAAM,IAAI4C,EAAQ3D,KAAU2D,EAAQ3D,EAAOI,MAAM,GAAwB;wBACxEuD,EAAQvD,IAAM;wBACduD,EAAQ3D,EAAOI,IAAM;wBACrBuD,EAAQhD;AACT,2BAAM;wBACLgD,EAAQvD,IAAM;wBACd+C,IAAUO;AACX;AACF;AACF;AACF;AJuUH;IIlUUhB,cAAU3F,UAAA6G,IAApB,SAAqBT;QACnB,IAAI7F,KAAK0E,MAAY,GAAG;YACtB1E,KAAKuG;YACL;AACD;QACD,IAAIC,IAAWX;QACf,OAAOW,EAAShE,KAASgE,EAAS9D,GAAQ;YACxC,IAAI8D,EAAS9D,GAAQ;gBACnB8D,IAAWA,EAAS9D;gBACpB,OAAO8D,EAAShE,GAAOgE,IAAWA,EAAShE;AAC5C,mBAAM;gBACLgE,IAAWA,EAAShE;AACrB;YACD,IAAMF,IAAMuD,EAAQjD;YACpBiD,EAAQjD,IAAO4D,EAAS5D;YACxB4D,EAAS5D,IAAON;YAChB,IAAMZ,IAAQmE,EAAQhD;YACtBgD,EAAQhD,IAAS2D,EAAS3D;YAC1B2D,EAAS3D,IAASnB;YAClBmE,IAAUW;AACX;QACD,IAAIxG,KAAK2F,EAAQnD,MAAUgE,GAAU;YACnCxG,KAAK2F,EAAQnD,IAAQgE,EAAS7D;AJuUhC,eItUO,IAAI3C,KAAK2F,EAAQjD,MAAW8D,GAAU;YAC3CxG,KAAK2F,EAAQjD,IAAS8D,EAAS7D;AAChC;QACD3C,KAAKmG,EAAsBK;QAC3B,IAAI7D,IAAU6D,EAAS7D;QACvB,IAAI6D,MAAa7D,EAAQH,GAAO;YAC9BG,EAAQH,IAAQC;AACjB,eAAME,EAAQD,IAASD;QACxBzC,KAAK0E,KAAW;QAChB1E,KAAKwF,EAAO1C,IAAM;QAClB,IAAI9C,KAAKsF,aAAa;YACpB,OAAO3C,MAAY3C,KAAK2F,GAAS;gBAC/BhD,EAAQsB,KAAgB;gBACxBtB,IAAUA,EAAQA;AACnB;AACF;AJuUH;II7TUyC,cAAiB3F,UAAAgH,IAA3B,SACEC;QAEA,IAAMC,WAAaD,MAAU,WAAWA,IAAQjE;QAChD,IAAMmE,WAAkBF,MAAU,aAAaA,IAAQjE;QACvD,IAAMoE,WAAkBH,MAAU,cAAgC,KAAKjE;QACvE,IAAIqE,IAAQ;QACZ,IAAIjB,IAAU7F,KAAKwF;QACnB,IAAMuB,IAA0B;QAChC,OAAOA,EAAMnF,UAAUiE,GAAS;YAC9B,IAAIA,GAAS;gBACXkB,EAAMlF,KAAKgE;gBACXA,IAAUA,EAAQrD;AACnB,mBAAM;gBACLqD,IAAUkB,EAAMpF;gBAChB,IAAImF,MAAUH,GAAK,OAAOd;gBAC1BgB,KAAYA,EAAShF,KAAKgE;gBAC1Be,KAAYA,EAASf,GAASiB,GAAO9G;gBACrC8G,KAAS;gBACTjB,IAAUA,EAAQnD;AACnB;AACF;QACD,OAAOmE;AJgUT;II3TUzB,cAAsB3F,UAAAuH,IAAhC,SAAiCnB;QAC/B,OAAO,MAAM;YACX,IAAMO,IAAaP,EAAQlD;YAC3B,IAAIyD,EAAWtD,MAA8B,GAAE;YAC/C,IAAMmE,IAAcb,EAAWzD;YAC/B,IAAIyD,MAAea,EAAYzE,GAAO;gBACpC,IAAM0E,IAAQD,EAAYvE;gBAC1B,IAAIwE,KAASA,EAAMpE,MAAM,GAAwB;oBAC/CoE,EAAMpE,IAASsD,EAAWtD,IAAM;oBAChC,IAAImE,MAAgBjH,KAAKwF,GAAO;oBAChCyB,EAAYnE,IAAM;oBAClB+C,IAAUoB;oBACV;AACD,uBAAM,IAAIpB,MAAYO,EAAW1D,GAAQ;oBACxCmD,EAAQ/C,IAAM;oBACd,IAAI+C,EAAQrD,GAAO;wBACjBqD,EAAQrD,EAAMG,IAAUyD;AACzB;oBACD,IAAIP,EAAQnD,GAAQ;wBAClBmD,EAAQnD,EAAOC,IAAUsE;AAC1B;oBACDb,EAAW1D,IAASmD,EAAQrD;oBAC5ByE,EAAYzE,IAAQqD,EAAQnD;oBAC5BmD,EAAQrD,IAAQ4D;oBAChBP,EAAQnD,IAASuE;oBACjB,IAAIA,MAAgBjH,KAAKwF,GAAO;wBAC9BxF,KAAKwF,IAAQK;wBACb7F,KAAK2F,EAAQhD,IAAUkD;AACxB,2BAAM;wBACL,IAAMsB,IAAKF,EAAYtE;wBACvB,IAAIwE,EAAG3E,MAAUyE,GAAa;4BAC5BE,EAAG3E,IAAQqD;AACZ,+BAAMsB,EAAGzE,IAASmD;AACpB;oBACDA,EAAQlD,IAAUsE,EAAYtE;oBAC9ByD,EAAWzD,IAAUkD;oBACrBoB,EAAYtE,IAAUkD;oBACtBoB,EAAYnE,IAAM;AACnB,uBAAM;oBACLsD,EAAWtD,IAAM;oBACjB,IAAImE,MAAgBjH,KAAKwF,GAAO;wBAC9BxF,KAAKwF,IAAQyB,EAAYxD;AAC1B,2BAAMwD,EAAYxD;oBACnBwD,EAAYnE,IAAM;oBAClB;AACD;AACF,mBAAM;gBACL,IAAMoE,IAAQD,EAAYzE;gBAC1B,IAAI0E,KAASA,EAAMpE,MAAM,GAAwB;oBAC/CoE,EAAMpE,IAASsD,EAAWtD,IAAM;oBAChC,IAAImE,MAAgBjH,KAAKwF,GAAO;oBAChCyB,EAAYnE,IAAM;oBAClB+C,IAAUoB;oBACV;AACD,uBAAM,IAAIpB,MAAYO,EAAW5D,GAAO;oBACvCqD,EAAQ/C,IAAM;oBACd,IAAI+C,EAAQrD,GAAO;wBACjBqD,EAAQrD,EAAMG,IAAUsE;AACzB;oBACD,IAAIpB,EAAQnD,GAAQ;wBAClBmD,EAAQnD,EAAOC,IAAUyD;AAC1B;oBACDa,EAAYvE,IAASmD,EAAQrD;oBAC7B4D,EAAW5D,IAAQqD,EAAQnD;oBAC3BmD,EAAQrD,IAAQyE;oBAChBpB,EAAQnD,IAAS0D;oBACjB,IAAIa,MAAgBjH,KAAKwF,GAAO;wBAC9BxF,KAAKwF,IAAQK;wBACb7F,KAAK2F,EAAQhD,IAAUkD;AACxB,2BAAM;wBACL,IAAMsB,IAAKF,EAAYtE;wBACvB,IAAIwE,EAAG3E,MAAUyE,GAAa;4BAC5BE,EAAG3E,IAAQqD;AACZ,+BAAMsB,EAAGzE,IAASmD;AACpB;oBACDA,EAAQlD,IAAUsE,EAAYtE;oBAC9ByD,EAAWzD,IAAUkD;oBACrBoB,EAAYtE,IAAUkD;oBACtBoB,EAAYnE,IAAM;AACnB,uBAAM;oBACLsD,EAAWtD,IAAM;oBACjB,IAAImE,MAAgBjH,KAAKwF,GAAO;wBAC9BxF,KAAKwF,IAAQyB,EAAY5D;AAC1B,2BAAM4D,EAAY5D;oBACnB4D,EAAYnE,IAAM;oBAClB;AACD;AACF;YACD,IAAI9C,KAAKsF,aAAa;gBACQc,EAAYjC;gBACZ8C,EAAa9C;gBACb0B,EAAS1B;AACtC;YACD;AACD;AJgUH;II3TUiB,cAAA3F,UAAA2H,IAAV,SAAe9E,GAAQZ,GAAW2F;QAChC,IAAIrH,KAAKwF,MAAU/C,WAAW;YAC5BzC,KAAK0E,KAAW;YAChB1E,KAAKwF,IAAQ,IAAIxF,KAAK0F,EAAepD,GAAKZ,GAAK;YAC/C1B,KAAKwF,EAAM7C,IAAU3C,KAAK2F;YAC1B3F,KAAK2F,EAAQhD,IAAU3C,KAAK2F,EAAQnD,IAAQxC,KAAK2F,EAAQjD,IAAS1C,KAAKwF;YACvE,OAAOxF,KAAK0E;AACb;QACD,IAAImB;QACJ,IAAMyB,IAAUtH,KAAK2F,EAAQnD;QAC7B,IAAM+E,IAAevH,KAAKyF,EAAK6B,EAAQ1E,GAAON;QAC9C,IAAIiF,MAAiB,GAAG;YACtBD,EAAQzE,IAASnB;YACjB,OAAO1B,KAAK0E;AACb,eAAM,IAAI6C,IAAe,GAAG;YAC3BD,EAAQ9E,IAAQ,IAAIxC,KAAK0F,EAAepD,GAAKZ;YAC7C4F,EAAQ9E,EAAMG,IAAU2E;YACxBzB,IAAUyB,EAAQ9E;YAClBxC,KAAK2F,EAAQnD,IAAQqD;AACtB,eAAM;YACL,IAAM2B,IAAUxH,KAAK2F,EAAQjD;YAC7B,IAAM+E,IAAezH,KAAKyF,EAAK+B,EAAQ5E,GAAON;YAC9C,IAAImF,MAAiB,GAAG;gBACtBD,EAAQ3E,IAASnB;gBACjB,OAAO1B,KAAK0E;AACb,mBAAM,IAAI+C,IAAe,GAAG;gBAC3BD,EAAQ9E,IAAS,IAAI1C,KAAK0F,EAAepD,GAAKZ;gBAC9C8F,EAAQ9E,EAAOC,IAAU6E;gBACzB3B,IAAU2B,EAAQ9E;gBAClB1C,KAAK2F,EAAQjD,IAASmD;AACvB,mBAAM;gBACL,IAAIwB,MAAS5E,WAAW;oBACtB,IAAMiF,IAAWL,EAAK7C;oBACtB,IAAIkD,MAAa1H,KAAK2F,GAAS;wBAC7B,IAAMgC,IAAa3H,KAAKyF,EAAKiC,EAAS9E,GAAON;wBAC7C,IAAIqF,MAAe,GAAG;4BACpBD,EAAS7E,IAASnB;4BAClB,OAAO1B,KAAK0E;AACb,+BAAiC,IAAIiD,IAAa,GAAG;4BACpD,IAAM3E,IAAU0E,EAAS3E;4BACzB,IAAM6E,IAAY5H,KAAKyF,EAAKzC,EAAQJ,GAAON;4BAC3C,IAAIsF,MAAc,GAAG;gCACnB5E,EAAQH,IAASnB;gCACjB,OAAO1B,KAAK0E;AACb,mCAAM,IAAIkD,IAAY,GAAG;gCACxB/B,IAAU,IAAI7F,KAAK0F,EAAepD,GAAKZ;gCACvC,IAAIsB,EAAQN,MAAWD,WAAW;oCAChCO,EAAQN,IAASmD;oCACjBA,EAAQlD,IAAUK;AACnB,uCAAM;oCACL0E,EAASlF,IAAQqD;oCACjBA,EAAQlD,IAAU+E;AACnB;AACF;AACF;AACF;AACF;gBACD,IAAI7B,MAAYpD,WAAW;oBACzBoD,IAAU7F,KAAKwF;oBACf,OAAO,MAAM;wBACX,IAAMO,IAAY/F,KAAKyF,EAAKI,EAAQjD,GAAON;wBAC3C,IAAIyD,IAAY,GAAG;4BACjB,IAAIF,EAAQrD,MAAUC,WAAW;gCAC/BoD,EAAQrD,IAAQ,IAAIxC,KAAK0F,EAAepD,GAAKZ;gCAC7CmE,EAAQrD,EAAMG,IAAUkD;gCACxBA,IAAUA,EAAQrD;gCAClB;AACD;4BACDqD,IAAUA,EAAQrD;AACnB,+BAAM,IAAIuD,IAAY,GAAG;4BACxB,IAAIF,EAAQnD,MAAWD,WAAW;gCAChCoD,EAAQnD,IAAS,IAAI1C,KAAK0F,EAAepD,GAAKZ;gCAC9CmE,EAAQnD,EAAOC,IAAUkD;gCACzBA,IAAUA,EAAQnD;gCAClB;AACD;4BACDmD,IAAUA,EAAQnD;AACnB,+BAAM;4BACLmD,EAAQhD,IAASnB;4BACjB,OAAO1B,KAAK0E;AACb;AACF;AACF;AACF;AACF;QACD,IAAI1E,KAAKsF,aAAa;YACpB,IAAIuC,IAAShC,EAAQlD;YACrB,OAAOkF,MAAW7H,KAAK2F,GAAS;gBAC9BkC,EAAO5D,KAAgB;gBACvB4D,IAASA,EAAOlF;AACjB;AACF;QACD3C,KAAKgH,EAAuBnB;QAC5B7F,KAAK0E,KAAW;QAChB,OAAO1E,KAAK0E;AJgUd;II3TUU,cAAA3F,UAAAqI,IAAV,SAA4BjC,GAAqCvD;QAC/D,OAAOuD,GAAS;YACd,IAAME,IAAY/F,KAAKyF,EAAKI,EAAQjD,GAAON;YAC3C,IAAIyD,IAAY,GAAG;gBACjBF,IAAUA,EAAQnD;AACnB,mBAAM,IAAIqD,IAAY,GAAG;gBACxBF,IAAUA,EAAQrD;AJgUpB,mBI/TO,OAAOqD;AACf;QACD,OAAOA,KAAW7F,KAAK2F;AJgUzB;II9TAP,cAAA3F,UAAA8G,QAAA;QACEvG,KAAK0E,IAAU;QACf1E,KAAKwF,IAAQ/C;QACbzC,KAAK2F,EAAQhD,IAAUF;QACvBzC,KAAK2F,EAAQnD,IAAQxC,KAAK2F,EAAQjD,IAASD;AJgU7C;IIpTA2C,cAAA3F,UAAAsI,sBAAA,SAAoBxD,GAA0BjC;QAC5C,IAAM0F,IAAOzD,EAAKC;QAClB,IAAIwD,MAAShI,KAAK2F,GAAS;YACzBT;AACD;QACD,IAAIlF,KAAK0E,MAAY,GAAG;YACtBsD,EAAKpF,IAAON;YACZ,OAAO;AACR;QACD,IAAM2F,IAAUD,EAAK7E,IAAQP;QAC7B,IAAIoF,MAAShI,KAAK2F,EAAQnD,GAAO;YAC/B,IAAIxC,KAAKyF,EAAKwC,GAAS3F,KAAO,GAAG;gBAC/B0F,EAAKpF,IAAON;gBACZ,OAAO;AACR;YACD,OAAO;AACR;QACD,IAAM4F,IAASF,EAAKjF,IAAOH;QAC3B,IAAIoF,MAAShI,KAAK2F,EAAQjD,GAAQ;YAChC,IAAI1C,KAAKyF,EAAKyC,GAAQ5F,KAAO,GAAG;gBAC9B0F,EAAKpF,IAAON;gBACZ,OAAO;AACR;YACD,OAAO;AACR;QACD,IACEtC,KAAKyF,EAAKyC,GAAQ5F,MAAQ,KAC1BtC,KAAKyF,EAAKwC,GAAS3F,MAAQ,GAC3B,OAAO;QACT0F,EAAKpF,IAAON;QACZ,OAAO;AJ6TT;II3TA8C,cAAiB3F,UAAA0I,oBAAjB,SAAkBxB;QACU,IAAAA,IAAG,KAAHA,IAAQ3G,KAAK0E,IAtfP,GAAA;YAAE,MAAU,IAAIS;AACjD;QAsfC,IAAM6C,IAAOhI,KAAKyG,EAAkBE;QACpC3G,KAAKsG,EAAW0B;QAChB,OAAOhI,KAAK0E;AJ+Td;IIxTAU,cAAiB3F,UAAA2I,oBAAjB,SAAkB9F;QAChB,IAAItC,KAAK0E,MAAY,GAAG,OAAO;QAC/B,IAAMmB,IAAU7F,KAAK8H,EAAkB9H,KAAKwF,GAAOlD;QACnD,IAAIuD,MAAY7F,KAAK2F,GAAS,OAAO;QACrC3F,KAAKsG,EAAWT;QAChB,OAAO;AJ+TT;II7TAT,cAAsB3F,UAAA4I,yBAAtB,SAAuB9D;QACrB,IAAMyD,IAAOzD,EAAKC;QAClB,IAAIwD,MAAShI,KAAK2F,GAAS;YACzBT;AACD;QACD,IAAMoD,IAAaN,EAAKtF,MAAWD;QACnC,IAAM8F,IAAWhE,EAAKF,iBAAY;QAElC,IAAIkE,GAAU;YAEZ,IAAID,GAAY/D,EAAKxD;AACtB,eAAM;YAGL,KAAKuH,KAAcN,EAAKxF,MAAUC,WAAW8B,EAAKxD;AACnD;QACDf,KAAKsG,EAAW0B;QAChB,OAAOzD;AJ+TT;IIzTAa,cAAA3F,UAAA+I,YAAA;QACE,IAAIxI,KAAK0E,MAAY,GAAG,OAAO;QAC/B,SAAS+D,UAAU5C;YACjB,KAAKA,GAAS,OAAO;YACrB,OAAO6C,KAAKC,IAAIF,UAAU5C,EAAQrD,IAAQiG,UAAU5C,EAAQnD,MAAW;AACxE;QACD,OAAO+F,UAAUzI,KAAKwF;AJ+TxB;IInSF,OAACJ;AAAD,CAhkBA,CAA2CH;;ACA3C,IAAA2D,eAAA,SAAA/E;IAA0CjE,UAA6BgJ,cAAA/E;IAarE,SAAA+E,aACEZ,GACAa,GACAxE;QAHF,IAKEP,IAAAD,EAAAlE,KAAAK,MAAMqE,MAoCPrE;QAnCC8D,EAAKU,IAAQwD;QACblE,EAAK6B,IAAUkD;QACf,IAAI/E,EAAKO,iBAAY,GAA0B;YAC7CP,EAAKZ,MAAM;gBACT,IAAIlD,KAAKwE,MAAUxE,KAAK2F,EAAQnD,GAAO;oBACrC0C;AACD;gBACDlF,KAAKwE,IAAQxE,KAAKwE,EAAMzB;gBACxB,OAAO/C;AL41BT;YKz1BA8D,EAAK/C,OAAO;gBACV,IAAIf,KAAKwE,MAAUxE,KAAK2F,GAAS;oBAC/BT;AACD;gBACDlF,KAAKwE,IAAQxE,KAAKwE,EAAMrB;gBACxB,OAAOnD;AL21BT;AKz1BD,eAAM;YACL8D,EAAKZ,MAAM;gBACT,IAAIlD,KAAKwE,MAAUxE,KAAK2F,EAAQjD,GAAQ;oBACtCwC;AACD;gBACDlF,KAAKwE,IAAQxE,KAAKwE,EAAMrB;gBACxB,OAAOnD;AL21BT;YKx1BA8D,EAAK/C,OAAO;gBACV,IAAIf,KAAKwE,MAAUxE,KAAK2F,GAAS;oBAC/BT;AACD;gBACDlF,KAAKwE,IAAQxE,KAAKwE,EAAMzB;gBACxB,OAAO/C;AL01BT;AKx1BD;QL01BD,OAAO8D;AKz1BR;IAUD1E,OAAAuF,eAAIiE,aAAKnJ,WAAA,SAAA;QAATmF,KAAA;YACE,IAAIJ,IAAQxE,KAAKwE;YACjB,IAAMsE,IAAO9I,KAAK2F,EAAQhD;YAC1B,IAAI6B,MAAUxE,KAAK2F,GAAS;gBAC1B,IAAImD,GAAM;oBACR,OAAOA,EAAK7E,IAAe;AAC5B;gBACD,OAAO;AACR;YACD,IAAI6C,IAAQ;YACZ,IAAItC,EAAMhC,GAAO;gBACfsE,KAAUtC,EAAMhC,EAAoCyB;AACrD;YACD,OAAOO,MAAUsE,GAAM;gBACrB,IAAMnG,IAAU6B,EAAM7B;gBACtB,IAAI6B,MAAU7B,EAAQD,GAAQ;oBAC5BoE,KAAS;oBACT,IAAInE,EAAQH,GAAO;wBACjBsE,KAAUnE,EAAQH,EAAoCyB;AACvD;AACF;gBACDO,IAAQ7B;AACT;YACD,OAAOmE;AL41BP;QACAjC,YAAY;QACZC,cAAc;;IK51BhB8D,aAAAnJ,UAAAsJ,eAAA;QACE,OAAO/I,KAAKwE,MAAUxE,KAAK2F;AL+1B7B;IKz1BF,OAACiD;AAAD,CAhGA,CAA0CxE;;ACC1C,IAAA4E,qBAAA,SAAAnF;IAAuCjE,UAAkBoJ,oBAAAnF;IAEvD,SAAAmF,mBACEhB,GACAa,GACAI,GACA5E;QAJF,IAAAP,IAMED,EAAAA,KAAAA,MAAMmE,GAAMa,GAAQxE,MAErBrE;QADC8D,EAAKmF,YAAYA;QNw7BjB,OAAOnF;AMv7BR;IACD1E,OAAAuF,eAAIqE,mBAAOvJ,WAAA,WAAA;QAAXmF,KAAA;YACE,IAAI5E,KAAKwE,MAAUxE,KAAK2F,GAAS;gBAC/BT;AACD;YACD,IAAMgE,IAAOlJ;YACb,OAAO,IAAImJ,MAAuB,IAAI;gBACpCvE,KAAA,SAAIwE,GAAQC;oBACV,IAAIA,MAAS,KAAK,OAAOH,EAAK1E,EAAM5B,QAC/B,IAAIyG,MAAS,KAAK,OAAOH,EAAK1E,EAAM3B;oBACzCuG,EAAO,KAAKF,EAAK1E,EAAM5B;oBACvBwG,EAAO,KAAKF,EAAK1E,EAAM3B;oBACvB,OAAOuG,EAAOC;ANy7Bd;gBMv7BFC,KAAA,SAAIhJ,GAAG+I,GAAWE;oBAChB,IAAIF,MAAS,KAAK;wBAChB,MAAM,IAAIxJ,UAAU;AACrB;oBACDqJ,EAAK1E,EAAM3B,IAAS0G;oBACpB,OAAO;AACR;;AN07BH;QACA1E,YAAY;QACZC,cAAc;;IMz7BhBkE,mBAAAvJ,UAAA+J,OAAA;QACE,OAAO,IAAIR,mBACThJ,KAAKwE,GACLxE,KAAK2F,GACL3F,KAAKiJ,WACLjJ,KAAKqE;ANw7BT;IMn7BF,OAAC2E;AAAD,CA3CA,CAAuCJ;;AA+CvC,IAAAa,aAAA,SAAA5F;IAA+BjE,UAAmB6J,YAAA5F;IAWhD,SAAA4F,WACER,GACA5D,GACAC;QAFA,IAAA2D,WAAA,GAAA;YAAAA,IAAqC;AAAA;QADvC,IAAAnF,IAKED,EAAMlE,KAAAK,MAAAqF,GAAKC,MAKZtF;QAJC,IAAMkJ,IAAOpF;QACbmF,EAAUS,SAAQ,SAAUC;YAC1BT,EAAKU,WAAWD,EAAG,IAAIA,EAAG;AAC3B;QNm7BD,OAAO7F;AMl7BR;IACD2F,WAAAhK,UAAAoK,QAAA;QACE,OAAO,IAAIb,mBAAyBhJ,KAAK2F,EAAQnD,KAASxC,KAAK2F,GAAS3F,KAAK2F,GAAS3F;ANo7BxF;IMl7BAyJ,WAAAhK,UAAAqK,MAAA;QACE,OAAO,IAAId,mBAAyBhJ,KAAK2F,GAAS3F,KAAK2F,GAAS3F;ANo7BlE;IMl7BAyJ,WAAAhK,UAAAsK,SAAA;QACE,OAAO,IAAIf,mBACThJ,KAAK2F,EAAQjD,KAAU1C,KAAK2F,GAC5B3F,KAAK2F,GACL3F,MAAI;ANi7BR;IM76BAyJ,WAAAhK,UAAAuK,OAAA;QACE,OAAO,IAAIhB,mBAAyBhJ,KAAK2F,GAAS3F,KAAK2F,GAAS3F,MAAI;ANg7BtE;IM96BAyJ,WAAAhK,UAAAwK,QAAA;QACE,IAAIjK,KAAK0E,MAAY,GAAG;QACxB,IAAM4C,IAAUtH,KAAK2F,EAAQnD;QAC7B,OAAe,EAAC8E,EAAQ1E,GAAM0E,EAAQzE;ANi7BxC;IM/6BA4G,WAAAhK,UAAAyK,OAAA;QACE,IAAIlK,KAAK0E,MAAY,GAAG;QACxB,IAAM8C,IAAUxH,KAAK2F,EAAQjD;QAC7B,OAAe,EAAC8E,EAAQ5E,GAAM4E,EAAQ3E;ANi7BxC;IM/6BA4G,WAAUhK,UAAA0K,aAAV,SAAW7H;QACT,IAAMwD,IAAU9F,KAAK4F,EAAY5F,KAAKwF,GAAOlD;QAC7C,OAAO,IAAI0G,mBAAyBlD,GAAS9F,KAAK2F,GAAS3F;ANi7B7D;IM/6BAyJ,WAAUhK,UAAA2K,aAAV,SAAW9H;QACT,IAAMwD,IAAU9F,KAAKgG,EAAYhG,KAAKwF,GAAOlD;QAC7C,OAAO,IAAI0G,mBAAyBlD,GAAS9F,KAAK2F,GAAS3F;ANi7B7D;IM/6BAyJ,WAAiBhK,UAAA4K,oBAAjB,SAAkB/H;QAChB,IAAMwD,IAAU9F,KAAKiG,EAAmBjG,KAAKwF,GAAOlD;QACpD,OAAO,IAAI0G,mBAAyBlD,GAAS9F,KAAK2F,GAAS3F;ANi7B7D;IM/6BAyJ,WAAiBhK,UAAA6K,oBAAjB,SAAkBhI;QAChB,IAAMwD,IAAU9F,KAAKkG,EAAmBlG,KAAKwF,GAAOlD;QACpD,OAAO,IAAI0G,mBAAyBlD,GAAS9F,KAAK2F,GAAS3F;ANi7B7D;IM/6BAyJ,WAAOhK,UAAAiK,UAAP,SAAQ9C;QACN5G,KAAKyG,GAAkB,SAAUuB,GAAMlB,GAAOyD;YAC5C3D,EAAiB,EAACoB,EAAKpF,GAAMoF,EAAKnF,KAASiE,GAAOyD;AACnD;ANi7BH;IMn6BAd,WAAAhK,UAAAmK,aAAA,SAAWtH,GAAQZ,GAAU2F;QAC3B,OAAOrH,KAAKoH,EAAK9E,GAAKZ,GAAO2F;ANi7B/B;IM/6BAoC,WAAehK,UAAA+K,kBAAf,SAAgB7D;QACY,IAAAA,IAAG,KAAHA,IAAQ3G,KAAK0E,IArIf,GAAA;YAAC,MAAU,IAAIS;AAC1C;QAqIG,IAAM6C,IAAOhI,KAAKyG,EAAkBE;QACpC,OAAe,EAACqB,EAAKpF,GAAMoF,EAAKnF;ANm7BlC;IMj7BA4G,WAAIhK,UAAAgL,OAAJ,SAAKnI;QACH,IAAMuD,IAAU7F,KAAK8H,EAAkB9H,KAAKwF,GAAOlD;QACnD,OAAO,IAAI0G,mBAAyBnD,GAAS7F,KAAK2F,GAAS3F;ANm7B7D;IM36BAyJ,WAAehK,UAAAiL,kBAAf,SAAgBpI;QACd,IAAMuD,IAAU7F,KAAK8H,EAAkB9H,KAAKwF,GAAOlD;QACnD,OAAOuD,EAAQhD;ANm7BjB;IMj7BA4G,WAAKhK,UAAAkL,QAAL,SAAMC;QACJ,IAAM1B,IAAOlJ;QACb4K,EAAMlB,SAAQ,SAAUC;YACtBT,EAAKU,WAAWD,EAAG,IAAIA,EAAG;AAC3B;QACD,OAAO3J,KAAK0E;ANm7Bd;IMj7BE+E,WAAAhK,UAAC0B,OAAOC,YAAV;QNm7BE,IAAIQ,GAAQiF,GAAUgE,GAAG7C;QACzB,OAAO7H,YAAYH,OAAM,SAAU8K;YACjC,QAAQA,EAAGvK;cACT,KAAK;gBMr7BHqB,IAAS5B,KAAK0E;gBACdmC,IAAW7G,KAAKyG;gBACboE,IAAI;gBNu7BPC,EAAGvK,QAAQ;;cACb,KAAK;gBACH,MMz7BUsK,IAAIjJ,IAAM,OAAA,EAAA,GAAA;gBAClBoG,IAAOnB,EAASgE;gBACtB,OAAc,EAAA,GAAA,EAAC7C,EAAKpF,GAAMoF,EAAKnF;;cN07B7B,KAAK;gBM17BPiI,EAAAtK;gBN47BIsK,EAAGvK,QAAQ;;cACb,KAAK;kBM/7BqBsK;gBNi8BxB,OAAO,EAAC,GAAa;;cACvB,KAAK;gBACH,OAAO,EAAC;;AAEd;AACF;IM/7BF,OAACpB;AAAD,CAzHA,CAA+BrE;;SN6jCtBqE", "file": "index.js", "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends,\r\n    __assign,\r\n    __rest,\r\n    __decorate,\r\n    __param,\r\n    __metadata,\r\n    __awaiter,\r\n    __generator,\r\n    __createBinding,\r\n    __exportStar,\r\n    __values,\r\n    __read,\r\n    __spread,\r\n    __spreadArrays,\r\n    __spreadArray,\r\n    __await,\r\n    __asyncGenerator,\r\n    __asyncDelegator,\r\n    __asyncValues,\r\n    __makeTemplateObject,\r\n    __importStar,\r\n    __importDefault,\r\n    __classPrivateFieldGet,\r\n    __classPrivateFieldSet,\r\n    __classPrivateFieldIn,\r\n    __addDisposableResource,\r\n    __disposeResources,\r\n};\r\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\nfunction __generator(thisArg, body) {\n  var _ = {\n      label: 0,\n      sent: function () {\n        if (t[0] & 1) throw t[1];\n        return t[1];\n      },\n      trys: [],\n      ops: []\n    },\n    f,\n    y,\n    t,\n    g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n    while (g && (g = 0, op[0] && (_ = 0)), _) try {\n      if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n      if (y = 0, t) op = [op[0] & 2, t.value];\n      switch (op[0]) {\n        case 0:\n        case 1:\n          t = op;\n          break;\n        case 4:\n          _.label++;\n          return {\n            value: op[1],\n            done: false\n          };\n        case 5:\n          _.label++;\n          y = op[1];\n          op = [0];\n          continue;\n        case 7:\n          op = _.ops.pop();\n          _.trys.pop();\n          continue;\n        default:\n          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n            _ = 0;\n            continue;\n          }\n          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n            _.label = op[1];\n            break;\n          }\n          if (op[0] === 6 && _.label < t[1]) {\n            _.label = t[1];\n            t = op;\n            break;\n          }\n          if (t && _.label < t[2]) {\n            _.label = t[2];\n            _.ops.push(op);\n            break;\n          }\n          if (t[2]) _.ops.pop();\n          _.trys.pop();\n          continue;\n      }\n      op = body.call(thisArg, _);\n    } catch (e) {\n      op = [6, e];\n      y = 0;\n    } finally {\n      f = t = 0;\n    }\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nvar TreeNode = /** @class */function () {\n  function TreeNode(key, value, color) {\n    if (color === void 0) {\n      color = 1 /* TreeNodeColor.RED */;\n    }\n    this._left = undefined;\n    this._right = undefined;\n    this._parent = undefined;\n    this._key = key;\n    this._value = value;\n    this._color = color;\n  }\n  /**\n   * @description Get the pre node.\n   * @returns TreeNode about the pre node.\n   */\n  TreeNode.prototype._pre = function () {\n    var preNode = this;\n    var isRootOrHeader = preNode._parent._parent === preNode;\n    if (isRootOrHeader && preNode._color === 1 /* TreeNodeColor.RED */) {\n      preNode = preNode._right;\n    } else if (preNode._left) {\n      preNode = preNode._left;\n      while (preNode._right) {\n        preNode = preNode._right;\n      }\n    } else {\n      // Must be root and left is null\n      if (isRootOrHeader) {\n        return preNode._parent;\n      }\n      var pre = preNode._parent;\n      while (pre._left === preNode) {\n        preNode = pre;\n        pre = preNode._parent;\n      }\n      preNode = pre;\n    }\n    return preNode;\n  };\n  /**\n   * @description Get the next node.\n   * @returns TreeNode about the next node.\n   */\n  TreeNode.prototype._next = function () {\n    var nextNode = this;\n    if (nextNode._right) {\n      nextNode = nextNode._right;\n      while (nextNode._left) {\n        nextNode = nextNode._left;\n      }\n      return nextNode;\n    } else {\n      var pre = nextNode._parent;\n      while (pre._right === nextNode) {\n        nextNode = pre;\n        pre = nextNode._parent;\n      }\n      if (nextNode._right !== pre) {\n        return pre;\n      } else return nextNode;\n    }\n  };\n  /**\n   * @description Rotate left.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  TreeNode.prototype._rotateLeft = function () {\n    var PP = this._parent;\n    var V = this._right;\n    var R = V._left;\n    if (PP._parent === this) PP._parent = V;else if (PP._left === this) PP._left = V;else PP._right = V;\n    V._parent = PP;\n    V._left = this;\n    this._parent = V;\n    this._right = R;\n    if (R) R._parent = this;\n    return V;\n  };\n  /**\n   * @description Rotate right.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  TreeNode.prototype._rotateRight = function () {\n    var PP = this._parent;\n    var F = this._left;\n    var K = F._right;\n    if (PP._parent === this) PP._parent = F;else if (PP._left === this) PP._left = F;else PP._right = F;\n    F._parent = PP;\n    F._right = this;\n    this._parent = F;\n    this._left = K;\n    if (K) K._parent = this;\n    return F;\n  };\n  return TreeNode;\n}();\nvar TreeNodeEnableIndex = /** @class */function (_super) {\n  __extends(TreeNodeEnableIndex, _super);\n  function TreeNodeEnableIndex() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this._subTreeSize = 1;\n    return _this;\n  }\n  /**\n   * @description Rotate left and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  TreeNodeEnableIndex.prototype._rotateLeft = function () {\n    var parent = _super.prototype._rotateLeft.call(this);\n    this._recount();\n    parent._recount();\n    return parent;\n  };\n  /**\n   * @description Rotate right and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  TreeNodeEnableIndex.prototype._rotateRight = function () {\n    var parent = _super.prototype._rotateRight.call(this);\n    this._recount();\n    parent._recount();\n    return parent;\n  };\n  TreeNodeEnableIndex.prototype._recount = function () {\n    this._subTreeSize = 1;\n    if (this._left) {\n      this._subTreeSize += this._left._subTreeSize;\n    }\n    if (this._right) {\n      this._subTreeSize += this._right._subTreeSize;\n    }\n  };\n  return TreeNodeEnableIndex;\n}(TreeNode);\n\nvar ContainerIterator = /** @class */function () {\n  /**\n   * @internal\n   */\n  function ContainerIterator(iteratorType) {\n    if (iteratorType === void 0) {\n      iteratorType = 0 /* IteratorType.NORMAL */;\n    }\n    this.iteratorType = iteratorType;\n  }\n  /**\n   * @param iter - The other iterator you want to compare.\n   * @returns Whether this equals to obj.\n   * @example\n   * container.find(1).equals(container.end());\n   */\n  ContainerIterator.prototype.equals = function (iter) {\n    return this._node === iter._node;\n  };\n  return ContainerIterator;\n}();\nvar Base = /** @class */function () {\n  function Base() {\n    /**\n     * @description Container's size.\n     * @internal\n     */\n    this._length = 0;\n  }\n  Object.defineProperty(Base.prototype, \"length\", {\n    /**\n     * @returns The size of the container.\n     * @example\n     * const container = new Vector([1, 2]);\n     * console.log(container.length); // 2\n     */\n    get: function () {\n      return this._length;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * @returns The size of the container.\n   * @example\n   * const container = new Vector([1, 2]);\n   * console.log(container.size()); // 2\n   */\n  Base.prototype.size = function () {\n    return this._length;\n  };\n  /**\n   * @returns Whether the container is empty.\n   * @example\n   * container.clear();\n   * console.log(container.empty());  // true\n   */\n  Base.prototype.empty = function () {\n    return this._length === 0;\n  };\n  return Base;\n}();\nvar Container = /** @class */function (_super) {\n  __extends(Container, _super);\n  function Container() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return Container;\n}(Base);\n\n/**\n * @description Throw an iterator access error.\n * @internal\n */\nfunction throwIteratorAccessError() {\n  throw new RangeError('Iterator access denied!');\n}\n\nvar TreeContainer = /** @class */function (_super) {\n  __extends(TreeContainer, _super);\n  /**\n   * @internal\n   */\n  function TreeContainer(cmp, enableIndex) {\n    if (cmp === void 0) {\n      cmp = function (x, y) {\n        if (x < y) return -1;\n        if (x > y) return 1;\n        return 0;\n      };\n    }\n    if (enableIndex === void 0) {\n      enableIndex = false;\n    }\n    var _this = _super.call(this) || this;\n    /**\n     * @internal\n     */\n    _this._root = undefined;\n    _this._cmp = cmp;\n    _this.enableIndex = enableIndex;\n    _this._TreeNodeClass = enableIndex ? TreeNodeEnableIndex : TreeNode;\n    _this._header = new _this._TreeNodeClass();\n    return _this;\n  }\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._lowerBound = function (curNode, key) {\n    var resNode = this._header;\n    while (curNode) {\n      var cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        resNode = curNode;\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  };\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._upperBound = function (curNode, key) {\n    var resNode = this._header;\n    while (curNode) {\n      var cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult <= 0) {\n        curNode = curNode._right;\n      } else {\n        resNode = curNode;\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  };\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._reverseLowerBound = function (curNode, key) {\n    var resNode = this._header;\n    while (curNode) {\n      var cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  };\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._reverseUpperBound = function (curNode, key) {\n    var resNode = this._header;\n    while (curNode) {\n      var cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else {\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  };\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._eraseNodeSelfBalance = function (curNode) {\n    while (true) {\n      var parentNode = curNode._parent;\n      if (parentNode === this._header) return;\n      if (curNode._color === 1 /* TreeNodeColor.RED */) {\n        curNode._color = 0 /* TreeNodeColor.BLACK */;\n        return;\n      }\n      if (curNode === parentNode._left) {\n        var brother = parentNode._right;\n        if (brother._color === 1 /* TreeNodeColor.RED */) {\n          brother._color = 0 /* TreeNodeColor.BLACK */;\n          parentNode._color = 1 /* TreeNodeColor.RED */;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateLeft();\n          } else parentNode._rotateLeft();\n        } else {\n          if (brother._right && brother._right._color === 1 /* TreeNodeColor.RED */) {\n            brother._color = parentNode._color;\n            parentNode._color = 0 /* TreeNodeColor.BLACK */;\n            brother._right._color = 0 /* TreeNodeColor.BLACK */;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateLeft();\n            } else parentNode._rotateLeft();\n            return;\n          } else if (brother._left && brother._left._color === 1 /* TreeNodeColor.RED */) {\n            brother._color = 1 /* TreeNodeColor.RED */;\n            brother._left._color = 0 /* TreeNodeColor.BLACK */;\n            brother._rotateRight();\n          } else {\n            brother._color = 1 /* TreeNodeColor.RED */;\n            curNode = parentNode;\n          }\n        }\n      } else {\n        var brother = parentNode._left;\n        if (brother._color === 1 /* TreeNodeColor.RED */) {\n          brother._color = 0 /* TreeNodeColor.BLACK */;\n          parentNode._color = 1 /* TreeNodeColor.RED */;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateRight();\n          } else parentNode._rotateRight();\n        } else {\n          if (brother._left && brother._left._color === 1 /* TreeNodeColor.RED */) {\n            brother._color = parentNode._color;\n            parentNode._color = 0 /* TreeNodeColor.BLACK */;\n            brother._left._color = 0 /* TreeNodeColor.BLACK */;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateRight();\n            } else parentNode._rotateRight();\n            return;\n          } else if (brother._right && brother._right._color === 1 /* TreeNodeColor.RED */) {\n            brother._color = 1 /* TreeNodeColor.RED */;\n            brother._right._color = 0 /* TreeNodeColor.BLACK */;\n            brother._rotateLeft();\n          } else {\n            brother._color = 1 /* TreeNodeColor.RED */;\n            curNode = parentNode;\n          }\n        }\n      }\n    }\n  };\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._eraseNode = function (curNode) {\n    if (this._length === 1) {\n      this.clear();\n      return;\n    }\n    var swapNode = curNode;\n    while (swapNode._left || swapNode._right) {\n      if (swapNode._right) {\n        swapNode = swapNode._right;\n        while (swapNode._left) swapNode = swapNode._left;\n      } else {\n        swapNode = swapNode._left;\n      }\n      var key = curNode._key;\n      curNode._key = swapNode._key;\n      swapNode._key = key;\n      var value = curNode._value;\n      curNode._value = swapNode._value;\n      swapNode._value = value;\n      curNode = swapNode;\n    }\n    if (this._header._left === swapNode) {\n      this._header._left = swapNode._parent;\n    } else if (this._header._right === swapNode) {\n      this._header._right = swapNode._parent;\n    }\n    this._eraseNodeSelfBalance(swapNode);\n    var _parent = swapNode._parent;\n    if (swapNode === _parent._left) {\n      _parent._left = undefined;\n    } else _parent._right = undefined;\n    this._length -= 1;\n    this._root._color = 0 /* TreeNodeColor.BLACK */;\n    if (this.enableIndex) {\n      while (_parent !== this._header) {\n        _parent._subTreeSize -= 1;\n        _parent = _parent._parent;\n      }\n    }\n  };\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._inOrderTraversal = function (param) {\n    var pos = typeof param === 'number' ? param : undefined;\n    var callback = typeof param === 'function' ? param : undefined;\n    var nodeList = typeof param === 'undefined' ? [] : undefined;\n    var index = 0;\n    var curNode = this._root;\n    var stack = [];\n    while (stack.length || curNode) {\n      if (curNode) {\n        stack.push(curNode);\n        curNode = curNode._left;\n      } else {\n        curNode = stack.pop();\n        if (index === pos) return curNode;\n        nodeList && nodeList.push(curNode);\n        callback && callback(curNode, index, this);\n        index += 1;\n        curNode = curNode._right;\n      }\n    }\n    return nodeList;\n  };\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._insertNodeSelfBalance = function (curNode) {\n    while (true) {\n      var parentNode = curNode._parent;\n      if (parentNode._color === 0 /* TreeNodeColor.BLACK */) return;\n      var grandParent = parentNode._parent;\n      if (parentNode === grandParent._left) {\n        var uncle = grandParent._right;\n        if (uncle && uncle._color === 1 /* TreeNodeColor.RED */) {\n          uncle._color = parentNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (grandParent === this._root) return;\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._right) {\n          curNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (curNode._left) {\n            curNode._left._parent = parentNode;\n          }\n          if (curNode._right) {\n            curNode._right._parent = grandParent;\n          }\n          parentNode._right = curNode._left;\n          grandParent._left = curNode._right;\n          curNode._left = parentNode;\n          curNode._right = grandParent;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            var GP = grandParent._parent;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n        } else {\n          parentNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateRight();\n          } else grandParent._rotateRight();\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n          return;\n        }\n      } else {\n        var uncle = grandParent._left;\n        if (uncle && uncle._color === 1 /* TreeNodeColor.RED */) {\n          uncle._color = parentNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (grandParent === this._root) return;\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._left) {\n          curNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (curNode._left) {\n            curNode._left._parent = grandParent;\n          }\n          if (curNode._right) {\n            curNode._right._parent = parentNode;\n          }\n          grandParent._right = curNode._left;\n          parentNode._left = curNode._right;\n          curNode._left = grandParent;\n          curNode._right = parentNode;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            var GP = grandParent._parent;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n        } else {\n          parentNode._color = 0 /* TreeNodeColor.BLACK */;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateLeft();\n          } else grandParent._rotateLeft();\n          grandParent._color = 1 /* TreeNodeColor.RED */;\n          return;\n        }\n      }\n      if (this.enableIndex) {\n        parentNode._recount();\n        grandParent._recount();\n        curNode._recount();\n      }\n      return;\n    }\n  };\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._set = function (key, value, hint) {\n    if (this._root === undefined) {\n      this._length += 1;\n      this._root = new this._TreeNodeClass(key, value, 0 /* TreeNodeColor.BLACK */);\n      this._root._parent = this._header;\n      this._header._parent = this._header._left = this._header._right = this._root;\n      return this._length;\n    }\n    var curNode;\n    var minNode = this._header._left;\n    var compareToMin = this._cmp(minNode._key, key);\n    if (compareToMin === 0) {\n      minNode._value = value;\n      return this._length;\n    } else if (compareToMin > 0) {\n      minNode._left = new this._TreeNodeClass(key, value);\n      minNode._left._parent = minNode;\n      curNode = minNode._left;\n      this._header._left = curNode;\n    } else {\n      var maxNode = this._header._right;\n      var compareToMax = this._cmp(maxNode._key, key);\n      if (compareToMax === 0) {\n        maxNode._value = value;\n        return this._length;\n      } else if (compareToMax < 0) {\n        maxNode._right = new this._TreeNodeClass(key, value);\n        maxNode._right._parent = maxNode;\n        curNode = maxNode._right;\n        this._header._right = curNode;\n      } else {\n        if (hint !== undefined) {\n          var iterNode = hint._node;\n          if (iterNode !== this._header) {\n            var iterCmpRes = this._cmp(iterNode._key, key);\n            if (iterCmpRes === 0) {\n              iterNode._value = value;\n              return this._length;\n            } else /* istanbul ignore else */if (iterCmpRes > 0) {\n                var preNode = iterNode._pre();\n                var preCmpRes = this._cmp(preNode._key, key);\n                if (preCmpRes === 0) {\n                  preNode._value = value;\n                  return this._length;\n                } else if (preCmpRes < 0) {\n                  curNode = new this._TreeNodeClass(key, value);\n                  if (preNode._right === undefined) {\n                    preNode._right = curNode;\n                    curNode._parent = preNode;\n                  } else {\n                    iterNode._left = curNode;\n                    curNode._parent = iterNode;\n                  }\n                }\n              }\n          }\n        }\n        if (curNode === undefined) {\n          curNode = this._root;\n          while (true) {\n            var cmpResult = this._cmp(curNode._key, key);\n            if (cmpResult > 0) {\n              if (curNode._left === undefined) {\n                curNode._left = new this._TreeNodeClass(key, value);\n                curNode._left._parent = curNode;\n                curNode = curNode._left;\n                break;\n              }\n              curNode = curNode._left;\n            } else if (cmpResult < 0) {\n              if (curNode._right === undefined) {\n                curNode._right = new this._TreeNodeClass(key, value);\n                curNode._right._parent = curNode;\n                curNode = curNode._right;\n                break;\n              }\n              curNode = curNode._right;\n            } else {\n              curNode._value = value;\n              return this._length;\n            }\n          }\n        }\n      }\n    }\n    if (this.enableIndex) {\n      var parent_1 = curNode._parent;\n      while (parent_1 !== this._header) {\n        parent_1._subTreeSize += 1;\n        parent_1 = parent_1._parent;\n      }\n    }\n    this._insertNodeSelfBalance(curNode);\n    this._length += 1;\n    return this._length;\n  };\n  /**\n   * @internal\n   */\n  TreeContainer.prototype._getTreeNodeByKey = function (curNode, key) {\n    while (curNode) {\n      var cmpResult = this._cmp(curNode._key, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return curNode || this._header;\n  };\n  TreeContainer.prototype.clear = function () {\n    this._length = 0;\n    this._root = undefined;\n    this._header._parent = undefined;\n    this._header._left = this._header._right = undefined;\n  };\n  /**\n   * @description Update node's key by iterator.\n   * @param iter - The iterator you want to change.\n   * @param key - The key you want to update.\n   * @returns Whether the modification is successful.\n   * @example\n   * const st = new orderedSet([1, 2, 5]);\n   * const iter = st.find(2);\n   * st.updateKeyByIterator(iter, 3); // then st will become [1, 3, 5]\n   */\n  TreeContainer.prototype.updateKeyByIterator = function (iter, key) {\n    var node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    if (this._length === 1) {\n      node._key = key;\n      return true;\n    }\n    var nextKey = node._next()._key;\n    if (node === this._header._left) {\n      if (this._cmp(nextKey, key) > 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    var preKey = node._pre()._key;\n    if (node === this._header._right) {\n      if (this._cmp(preKey, key) < 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    if (this._cmp(preKey, key) >= 0 || this._cmp(nextKey, key) <= 0) return false;\n    node._key = key;\n    return true;\n  };\n  TreeContainer.prototype.eraseElementByPos = function (pos) {\n    if (pos < 0 || pos > this._length - 1) {\n      throw new RangeError();\n    }\n    var node = this._inOrderTraversal(pos);\n    this._eraseNode(node);\n    return this._length;\n  };\n  /**\n   * @description Remove the element of the specified key.\n   * @param key - The key you want to remove.\n   * @returns Whether erase successfully.\n   */\n  TreeContainer.prototype.eraseElementByKey = function (key) {\n    if (this._length === 0) return false;\n    var curNode = this._getTreeNodeByKey(this._root, key);\n    if (curNode === this._header) return false;\n    this._eraseNode(curNode);\n    return true;\n  };\n  TreeContainer.prototype.eraseElementByIterator = function (iter) {\n    var node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    var hasNoRight = node._right === undefined;\n    var isNormal = iter.iteratorType === 0 /* IteratorType.NORMAL */;\n    // For the normal iterator, the `next` node will be swapped to `this` node when has right.\n    if (isNormal) {\n      // So we should move it to next when it's right is null.\n      if (hasNoRight) iter.next();\n    } else {\n      // For the reverse iterator, only when it doesn't have right and has left the `next` node will be swapped.\n      // So when it has right, or it is a leaf node we should move it to `next`.\n      if (!hasNoRight || node._left === undefined) iter.next();\n    }\n    this._eraseNode(node);\n    return iter;\n  };\n  /**\n   * @description Get the height of the tree.\n   * @returns Number about the height of the RB-tree.\n   */\n  TreeContainer.prototype.getHeight = function () {\n    if (this._length === 0) return 0;\n    function traversal(curNode) {\n      if (!curNode) return 0;\n      return Math.max(traversal(curNode._left), traversal(curNode._right)) + 1;\n    }\n    return traversal(this._root);\n  };\n  return TreeContainer;\n}(Container);\n\nvar TreeIterator = /** @class */function (_super) {\n  __extends(TreeIterator, _super);\n  /**\n   * @internal\n   */\n  function TreeIterator(node, header, iteratorType) {\n    var _this = _super.call(this, iteratorType) || this;\n    _this._node = node;\n    _this._header = header;\n    if (_this.iteratorType === 0 /* IteratorType.NORMAL */) {\n      _this.pre = function () {\n        if (this._node === this._header._left) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n      _this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n    } else {\n      _this.pre = function () {\n        if (this._node === this._header._right) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n      _this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n    }\n    return _this;\n  }\n  Object.defineProperty(TreeIterator.prototype, \"index\", {\n    /**\n     * @description Get the sequential index of the iterator in the tree container.<br/>\n     *              <strong>Note:</strong>\n     *              This function only takes effect when the specified tree container `enableIndex = true`.\n     * @returns The index subscript of the node in the tree.\n     * @example\n     * const st = new OrderedSet([1, 2, 3], true);\n     * console.log(st.begin().next().index);  // 1\n     */\n    get: function () {\n      var _node = this._node;\n      var root = this._header._parent;\n      if (_node === this._header) {\n        if (root) {\n          return root._subTreeSize - 1;\n        }\n        return 0;\n      }\n      var index = 0;\n      if (_node._left) {\n        index += _node._left._subTreeSize;\n      }\n      while (_node !== root) {\n        var _parent = _node._parent;\n        if (_node === _parent._right) {\n          index += 1;\n          if (_parent._left) {\n            index += _parent._left._subTreeSize;\n          }\n        }\n        _node = _parent;\n      }\n      return index;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  TreeIterator.prototype.isAccessible = function () {\n    return this._node !== this._header;\n  };\n  return TreeIterator;\n}(ContainerIterator);\n\nvar OrderedMapIterator = /** @class */function (_super) {\n  __extends(OrderedMapIterator, _super);\n  function OrderedMapIterator(node, header, container, iteratorType) {\n    var _this = _super.call(this, node, header, iteratorType) || this;\n    _this.container = container;\n    return _this;\n  }\n  Object.defineProperty(OrderedMapIterator.prototype, \"pointer\", {\n    get: function () {\n      if (this._node === this._header) {\n        throwIteratorAccessError();\n      }\n      var self = this;\n      return new Proxy([], {\n        get: function (target, prop) {\n          if (prop === '0') return self._node._key;else if (prop === '1') return self._node._value;\n          target[0] = self._node._key;\n          target[1] = self._node._value;\n          return target[prop];\n        },\n        set: function (_, prop, newValue) {\n          if (prop !== '1') {\n            throw new TypeError('prop must be 1');\n          }\n          self._node._value = newValue;\n          return true;\n        }\n      });\n    },\n    enumerable: false,\n    configurable: true\n  });\n  OrderedMapIterator.prototype.copy = function () {\n    return new OrderedMapIterator(this._node, this._header, this.container, this.iteratorType);\n  };\n  return OrderedMapIterator;\n}(TreeIterator);\nvar OrderedMap = /** @class */function (_super) {\n  __extends(OrderedMap, _super);\n  /**\n   * @param container - The initialization container.\n   * @param cmp - The compare function.\n   * @param enableIndex - Whether to enable iterator indexing function.\n   * @example\n   * new OrderedMap();\n   * new OrderedMap([[0, 1], [2, 1]]);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y, true);\n   */\n  function OrderedMap(container, cmp, enableIndex) {\n    if (container === void 0) {\n      container = [];\n    }\n    var _this = _super.call(this, cmp, enableIndex) || this;\n    var self = _this;\n    container.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n    return _this;\n  }\n  OrderedMap.prototype.begin = function () {\n    return new OrderedMapIterator(this._header._left || this._header, this._header, this);\n  };\n  OrderedMap.prototype.end = function () {\n    return new OrderedMapIterator(this._header, this._header, this);\n  };\n  OrderedMap.prototype.rBegin = function () {\n    return new OrderedMapIterator(this._header._right || this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n  };\n\n  OrderedMap.prototype.rEnd = function () {\n    return new OrderedMapIterator(this._header, this._header, this, 1 /* IteratorType.REVERSE */);\n  };\n\n  OrderedMap.prototype.front = function () {\n    if (this._length === 0) return;\n    var minNode = this._header._left;\n    return [minNode._key, minNode._value];\n  };\n  OrderedMap.prototype.back = function () {\n    if (this._length === 0) return;\n    var maxNode = this._header._right;\n    return [maxNode._key, maxNode._value];\n  };\n  OrderedMap.prototype.lowerBound = function (key) {\n    var resNode = this._lowerBound(this._root, key);\n    return new OrderedMapIterator(resNode, this._header, this);\n  };\n  OrderedMap.prototype.upperBound = function (key) {\n    var resNode = this._upperBound(this._root, key);\n    return new OrderedMapIterator(resNode, this._header, this);\n  };\n  OrderedMap.prototype.reverseLowerBound = function (key) {\n    var resNode = this._reverseLowerBound(this._root, key);\n    return new OrderedMapIterator(resNode, this._header, this);\n  };\n  OrderedMap.prototype.reverseUpperBound = function (key) {\n    var resNode = this._reverseUpperBound(this._root, key);\n    return new OrderedMapIterator(resNode, this._header, this);\n  };\n  OrderedMap.prototype.forEach = function (callback) {\n    this._inOrderTraversal(function (node, index, map) {\n      callback([node._key, node._value], index, map);\n    });\n  };\n  /**\n   * @description Insert a key-value pair or set value by the given key.\n   * @param key - The key want to insert.\n   * @param value - The value want to set.\n   * @param hint - You can give an iterator hint to improve insertion efficiency.\n   * @return The size of container after setting.\n   * @example\n   * const mp = new OrderedMap([[2, 0], [4, 0], [5, 0]]);\n   * const iter = mp.begin();\n   * mp.setElement(1, 0);\n   * mp.setElement(3, 0, iter);  // give a hint will be faster.\n   */\n  OrderedMap.prototype.setElement = function (key, value, hint) {\n    return this._set(key, value, hint);\n  };\n  OrderedMap.prototype.getElementByPos = function (pos) {\n    if (pos < 0 || pos > this._length - 1) {\n      throw new RangeError();\n    }\n    var node = this._inOrderTraversal(pos);\n    return [node._key, node._value];\n  };\n  OrderedMap.prototype.find = function (key) {\n    var curNode = this._getTreeNodeByKey(this._root, key);\n    return new OrderedMapIterator(curNode, this._header, this);\n  };\n  /**\n   * @description Get the value of the element of the specified key.\n   * @param key - The specified key you want to get.\n   * @example\n   * const val = container.getElementByKey(1);\n   */\n  OrderedMap.prototype.getElementByKey = function (key) {\n    var curNode = this._getTreeNodeByKey(this._root, key);\n    return curNode._value;\n  };\n  OrderedMap.prototype.union = function (other) {\n    var self = this;\n    other.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n    return this._length;\n  };\n  OrderedMap.prototype[Symbol.iterator] = function () {\n    var length, nodeList, i, node;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          length = this._length;\n          nodeList = this._inOrderTraversal();\n          i = 0;\n          _a.label = 1;\n        case 1:\n          if (!(i < length)) return [3 /*break*/, 4];\n          node = nodeList[i];\n          return [4 /*yield*/, [node._key, node._value]];\n        case 2:\n          _a.sent();\n          _a.label = 3;\n        case 3:\n          ++i;\n          return [3 /*break*/, 1];\n        case 4:\n          return [2 /*return*/];\n      }\n    });\n  };\n\n  return OrderedMap;\n}(TreeContainer);\n\nexport { OrderedMap };\n//# sourceMappingURL=index.js.map\n", "export const enum TreeNodeColor {\n  RED = 1,\n  BLACK = 0\n}\n\nexport class TreeNode<K, V> {\n  _color: TreeNodeColor;\n  _key: K | undefined;\n  _value: V | undefined;\n  _left: TreeNode<K, V> | undefined = undefined;\n  _right: TreeNode<K, V> | undefined = undefined;\n  _parent: TreeNode<K, V> | undefined = undefined;\n  constructor(\n    key?: K,\n    value?: V,\n    color: TreeNodeColor = TreeNodeColor.RED\n  ) {\n    this._key = key;\n    this._value = value;\n    this._color = color;\n  }\n  /**\n   * @description Get the pre node.\n   * @returns TreeNode about the pre node.\n   */\n  _pre() {\n    let preNode: TreeNode<K, V> = this;\n    const isRootOrHeader = preNode._parent!._parent === preNode;\n    if (isRootOrHeader && preNode._color === TreeNodeColor.RED) {\n      preNode = preNode._right!;\n    } else if (preNode._left) {\n      preNode = preNode._left;\n      while (preNode._right) {\n        preNode = preNode._right;\n      }\n    } else {\n      // Must be root and left is null\n      if (isRootOrHeader) {\n        return preNode._parent!;\n      }\n      let pre = preNode._parent!;\n      while (pre._left === preNode) {\n        preNode = pre;\n        pre = preNode._parent!;\n      }\n      preNode = pre;\n    }\n    return preNode;\n  }\n  /**\n   * @description Get the next node.\n   * @returns TreeNode about the next node.\n   */\n  _next() {\n    let nextNode: TreeNode<K, V> = this;\n    if (nextNode._right) {\n      nextNode = nextNode._right;\n      while (nextNode._left) {\n        nextNode = nextNode._left;\n      }\n      return nextNode;\n    } else {\n      let pre = nextNode._parent!;\n      while (pre._right === nextNode) {\n        nextNode = pre;\n        pre = nextNode._parent!;\n      }\n      if (nextNode._right !== pre) {\n        return pre;\n      } else return nextNode;\n    }\n  }\n  /**\n   * @description Rotate left.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const PP = this._parent!;\n    const V = this._right!;\n    const R = V._left;\n\n    if (PP._parent === this) PP._parent = V;\n    else if (PP._left === this) PP._left = V;\n    else PP._right = V;\n\n    V._parent = PP;\n    V._left = this;\n\n    this._parent = V;\n    this._right = R;\n\n    if (R) R._parent = this;\n\n    return V;\n  }\n  /**\n   * @description Rotate right.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const PP = this._parent!;\n    const F = this._left!;\n    const K = F._right;\n\n    if (PP._parent === this) PP._parent = F;\n    else if (PP._left === this) PP._left = F;\n    else PP._right = F;\n\n    F._parent = PP;\n    F._right = this;\n\n    this._parent = F;\n    this._left = K;\n\n    if (K) K._parent = this;\n\n    return F;\n  }\n}\n\nexport class TreeNodeEnableIndex<K, V> extends TreeNode<K, V> {\n  _subTreeSize = 1;\n  /**\n   * @description Rotate left and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateLeft() {\n    const parent = super._rotateLeft() as TreeNodeEnableIndex<K, V>;\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  /**\n   * @description Rotate right and do recount.\n   * @returns TreeNode about moved to original position after rotation.\n   */\n  _rotateRight() {\n    const parent = super._rotateRight() as TreeNodeEnableIndex<K, V>;\n    this._recount();\n    parent._recount();\n    return parent;\n  }\n  _recount() {\n    this._subTreeSize = 1;\n    if (this._left) {\n      this._subTreeSize += (this._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n    if (this._right) {\n      this._subTreeSize += (this._right as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n  }\n}\n", "/**\n * @description The iterator type including `NORMAL` and `REVERSE`.\n */\nexport const enum IteratorType {\n  NORMAL = 0,\n  REVERSE = 1\n}\n\nexport abstract class ContainerIterator<T> {\n  /**\n   * @description The container pointed to by the iterator.\n   */\n  abstract readonly container: Container<T>;\n  /**\n   * @internal\n   */\n  abstract _node: unknown;\n  /**\n   * @description Iterator's type.\n   * @example\n   * console.log(container.end().iteratorType === IteratorType.NORMAL);  // true\n   */\n  readonly iteratorType: IteratorType;\n  /**\n   * @internal\n   */\n  protected constructor(iteratorType = IteratorType.NORMAL) {\n    this.iteratorType = iteratorType;\n  }\n  /**\n   * @param iter - The other iterator you want to compare.\n   * @returns Whether this equals to obj.\n   * @example\n   * container.find(1).equals(container.end());\n   */\n  equals(iter: ContainerIterator<T>) {\n    return this._node === iter._node;\n  }\n  /**\n   * @description Pointers to element.\n   * @returns The value of the pointer's element.\n   * @example\n   * const val = container.begin().pointer;\n   */\n  abstract get pointer(): T;\n  /**\n   * @description Set pointer's value (some containers are unavailable).\n   * @param newValue - The new value you want to set.\n   * @example\n   * (<LinkList<number>>container).begin().pointer = 1;\n   */\n  abstract set pointer(newValue: T);\n  /**\n   * @description Move `this` iterator to pre.\n   * @returns The iterator's self.\n   * @example\n   * const iter = container.find(1);  // container = [0, 1]\n   * const pre = iter.pre();\n   * console.log(pre === iter);  // true\n   * console.log(pre.equals(iter));  // true\n   * console.log(pre.pointer, iter.pointer); // 0, 0\n   */\n  abstract pre(): this;\n  /**\n   * @description Move `this` iterator to next.\n   * @returns The iterator's self.\n   * @example\n   * const iter = container.find(1);  // container = [1, 2]\n   * const next = iter.next();\n   * console.log(next === iter);  // true\n   * console.log(next.equals(iter));  // true\n   * console.log(next.pointer, iter.pointer); // 2, 2\n   */\n  abstract next(): this;\n  /**\n   * @description Get a copy of itself.\n   * @returns The copy of self.\n   * @example\n   * const iter = container.find(1);  // container = [1, 2]\n   * const next = iter.copy().next();\n   * console.log(next === iter);  // false\n   * console.log(next.equals(iter));  // false\n   * console.log(next.pointer, iter.pointer); // 2, 1\n   */\n  abstract copy(): ContainerIterator<T>;\n  abstract isAccessible(): boolean;\n}\n\nexport abstract class Base {\n  /**\n   * @description Container's size.\n   * @internal\n   */\n  protected _length = 0;\n  /**\n   * @returns The size of the container.\n   * @example\n   * const container = new Vector([1, 2]);\n   * console.log(container.length); // 2\n   */\n  get length() {\n    return this._length;\n  }\n  /**\n   * @returns The size of the container.\n   * @example\n   * const container = new Vector([1, 2]);\n   * console.log(container.size()); // 2\n   */\n  size() {\n    return this._length;\n  }\n  /**\n   * @returns Whether the container is empty.\n   * @example\n   * container.clear();\n   * console.log(container.empty());  // true\n   */\n  empty() {\n    return this._length === 0;\n  }\n  /**\n   * @description Clear the container.\n   * @example\n   * container.clear();\n   * console.log(container.empty());  // true\n   */\n  abstract clear(): void;\n}\n\nexport abstract class Container<T> extends Base {\n  /**\n   * @returns Iterator pointing to the beginning element.\n   * @example\n   * const begin = container.begin();\n   * const end = container.end();\n   * for (const it = begin; !it.equals(end); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract begin(): ContainerIterator<T>;\n  /**\n   * @returns Iterator pointing to the super end like c++.\n   * @example\n   * const begin = container.begin();\n   * const end = container.end();\n   * for (const it = begin; !it.equals(end); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract end(): ContainerIterator<T>;\n  /**\n   * @returns Iterator pointing to the end element.\n   * @example\n   * const rBegin = container.rBegin();\n   * const rEnd = container.rEnd();\n   * for (const it = rBegin; !it.equals(rEnd); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract rBegin(): ContainerIterator<T>;\n  /**\n   * @returns Iterator pointing to the super begin like c++.\n   * @example\n   * const rBegin = container.rBegin();\n   * const rEnd = container.rEnd();\n   * for (const it = rBegin; !it.equals(rEnd); it.next()) {\n   *   doSomething(it.pointer);\n   * }\n   */\n  abstract rEnd(): ContainerIterator<T>;\n  /**\n   * @returns The first element of the container.\n   */\n  abstract front(): T | undefined;\n  /**\n   * @returns The last element of the container.\n   */\n  abstract back(): T | undefined;\n  /**\n   * @param element - The element you want to find.\n   * @returns An iterator pointing to the element if found, or super end if not found.\n   * @example\n   * container.find(1).equals(container.end());\n   */\n  abstract find(element: T): ContainerIterator<T>;\n  /**\n   * @description Iterate over all elements in the container.\n   * @param callback - Callback function like Array.forEach.\n   * @example\n   * container.forEach((element, index) => console.log(element, index));\n   */\n  abstract forEach(callback: (element: T, index: number, container: Container<T>) => void): void;\n  /**\n   * @description Gets the value of the element at the specified position.\n   * @example\n   * const val = container.getElementByPos(-1); // throw a RangeError\n   */\n  abstract getElementByPos(pos: number): T;\n  /**\n   * @description Removes the element at the specified position.\n   * @param pos - The element's position you want to remove.\n   * @returns The container length after erasing.\n   * @example\n   * container.eraseElementByPos(-1); // throw a RangeError\n   */\n  abstract eraseElementByPos(pos: number): number;\n  /**\n   * @description Removes element by iterator and move `iter` to next.\n   * @param iter - The iterator you want to erase.\n   * @returns The next iterator.\n   * @example\n   * container.eraseElementByIterator(container.begin());\n   * container.eraseElementByIterator(container.end()); // throw a RangeError\n   */\n  abstract eraseElementByIterator(\n    iter: ContainerIterator<T>\n  ): ContainerIterator<T>;\n  /**\n   * @description Using for `for...of` syntax like Array.\n   * @example\n   * for (const element of container) {\n   *   console.log(element);\n   * }\n   */\n  abstract [Symbol.iterator](): Generator<T, void>;\n}\n\n/**\n * @description The initial data type passed in when initializing the container.\n */\nexport type initContainer<T> = {\n  size?: number | (() => number);\n  length?: number;\n  forEach: (callback: (el: T) => void) => void;\n}\n", "/**\n * @description Throw an iterator access error.\n * @internal\n */\nexport function throwIteratorAccessError() {\n  throw new RangeError('Iterator access denied!');\n}\n", "import type TreeIterator from './TreeIterator';\nimport { TreeNode, TreeNodeColor, TreeNodeEnableIndex } from './TreeNode';\nimport { Container, IteratorType } from '@/container/ContainerBase';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nabstract class TreeContainer<K, V> extends Container<K | [K, V]> {\n  enableIndex: boolean;\n  /**\n   * @internal\n   */\n  protected _header: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _root: TreeNode<K, V> | undefined = undefined;\n  /**\n   * @internal\n   */\n  protected readonly _cmp: (x: K, y: K) => number;\n  /**\n   * @internal\n   */\n  protected readonly _TreeNodeClass: typeof TreeNode | typeof TreeNodeEnableIndex;\n  /**\n   * @internal\n   */\n  protected constructor(\n    cmp: (x: K, y: K) => number =\n    function (x: K, y: K) {\n      if (x < y) return -1;\n      if (x > y) return 1;\n      return 0;\n    },\n    enableIndex = false\n  ) {\n    super();\n    this._cmp = cmp;\n    this.enableIndex = enableIndex;\n    this._TreeNodeClass = enableIndex ? TreeNodeEnableIndex : TreeNode;\n    this._header = new this._TreeNodeClass();\n  }\n  /**\n   * @internal\n   */\n  protected _lowerBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        resNode = curNode;\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _upperBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult <= 0) {\n        curNode = curNode._right;\n      } else {\n        resNode = curNode;\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _reverseLowerBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _reverseUpperBound(curNode: TreeNode<K, V> | undefined, key: K) {\n    let resNode = this._header;\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        resNode = curNode;\n        curNode = curNode._right;\n      } else {\n        curNode = curNode._left;\n      }\n    }\n    return resNode;\n  }\n  /**\n   * @internal\n   */\n  protected _eraseNodeSelfBalance(curNode: TreeNode<K, V>) {\n    while (true) {\n      const parentNode = curNode._parent!;\n      if (parentNode === this._header) return;\n      if (curNode._color === TreeNodeColor.RED) {\n        curNode._color = TreeNodeColor.BLACK;\n        return;\n      }\n      if (curNode === parentNode._left) {\n        const brother = parentNode._right!;\n        if (brother._color === TreeNodeColor.RED) {\n          brother._color = TreeNodeColor.BLACK;\n          parentNode._color = TreeNodeColor.RED;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateLeft();\n          } else parentNode._rotateLeft();\n        } else {\n          if (brother._right && brother._right._color === TreeNodeColor.RED) {\n            brother._color = parentNode._color;\n            parentNode._color = TreeNodeColor.BLACK;\n            brother._right._color = TreeNodeColor.BLACK;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateLeft();\n            } else parentNode._rotateLeft();\n            return;\n          } else if (brother._left && brother._left._color === TreeNodeColor.RED) {\n            brother._color = TreeNodeColor.RED;\n            brother._left._color = TreeNodeColor.BLACK;\n            brother._rotateRight();\n          } else {\n            brother._color = TreeNodeColor.RED;\n            curNode = parentNode;\n          }\n        }\n      } else {\n        const brother = parentNode._left!;\n        if (brother._color === TreeNodeColor.RED) {\n          brother._color = TreeNodeColor.BLACK;\n          parentNode._color = TreeNodeColor.RED;\n          if (parentNode === this._root) {\n            this._root = parentNode._rotateRight();\n          } else parentNode._rotateRight();\n        } else {\n          if (brother._left && brother._left._color === TreeNodeColor.RED) {\n            brother._color = parentNode._color;\n            parentNode._color = TreeNodeColor.BLACK;\n            brother._left._color = TreeNodeColor.BLACK;\n            if (parentNode === this._root) {\n              this._root = parentNode._rotateRight();\n            } else parentNode._rotateRight();\n            return;\n          } else if (brother._right && brother._right._color === TreeNodeColor.RED) {\n            brother._color = TreeNodeColor.RED;\n            brother._right._color = TreeNodeColor.BLACK;\n            brother._rotateLeft();\n          } else {\n            brother._color = TreeNodeColor.RED;\n            curNode = parentNode;\n          }\n        }\n      }\n    }\n  }\n  /**\n   * @internal\n   */\n  protected _eraseNode(curNode: TreeNode<K, V>) {\n    if (this._length === 1) {\n      this.clear();\n      return;\n    }\n    let swapNode = curNode;\n    while (swapNode._left || swapNode._right) {\n      if (swapNode._right) {\n        swapNode = swapNode._right;\n        while (swapNode._left) swapNode = swapNode._left;\n      } else {\n        swapNode = swapNode._left!;\n      }\n      const key = curNode._key;\n      curNode._key = swapNode._key;\n      swapNode._key = key;\n      const value = curNode._value;\n      curNode._value = swapNode._value;\n      swapNode._value = value;\n      curNode = swapNode;\n    }\n    if (this._header._left === swapNode) {\n      this._header._left = swapNode._parent;\n    } else if (this._header._right === swapNode) {\n      this._header._right = swapNode._parent;\n    }\n    this._eraseNodeSelfBalance(swapNode);\n    let _parent = swapNode._parent as TreeNodeEnableIndex<K, V>;\n    if (swapNode === _parent._left) {\n      _parent._left = undefined;\n    } else _parent._right = undefined;\n    this._length -= 1;\n    this._root!._color = TreeNodeColor.BLACK;\n    if (this.enableIndex) {\n      while (_parent !== this._header) {\n        _parent._subTreeSize -= 1;\n        _parent = _parent._parent as TreeNodeEnableIndex<K, V>;\n      }\n    }\n  }\n  protected _inOrderTraversal(): TreeNode<K, V>[];\n  protected _inOrderTraversal(pos: number): TreeNode<K, V>;\n  protected _inOrderTraversal(\n    callback: (node: TreeNode<K, V>, index: number, map: this) => void\n  ): TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _inOrderTraversal(\n    param?: number | ((node: TreeNode<K, V>, index: number, map: this) => void)\n  ) {\n    const pos = typeof param === 'number' ? param : undefined;\n    const callback = typeof param === 'function' ? param : undefined;\n    const nodeList = typeof param === 'undefined' ? <TreeNode<K, V>[]>[] : undefined;\n    let index = 0;\n    let curNode = this._root;\n    const stack: TreeNode<K, V>[] = [];\n    while (stack.length || curNode) {\n      if (curNode) {\n        stack.push(curNode);\n        curNode = curNode._left;\n      } else {\n        curNode = stack.pop()!;\n        if (index === pos) return curNode;\n        nodeList && nodeList.push(curNode);\n        callback && callback(curNode, index, this);\n        index += 1;\n        curNode = curNode._right;\n      }\n    }\n    return nodeList;\n  }\n  /**\n   * @internal\n   */\n  protected _insertNodeSelfBalance(curNode: TreeNode<K, V>) {\n    while (true) {\n      const parentNode = curNode._parent!;\n      if (parentNode._color === TreeNodeColor.BLACK) return;\n      const grandParent = parentNode._parent!;\n      if (parentNode === grandParent._left) {\n        const uncle = grandParent._right;\n        if (uncle && uncle._color === TreeNodeColor.RED) {\n          uncle._color = parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) return;\n          grandParent._color = TreeNodeColor.RED;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._right) {\n          curNode._color = TreeNodeColor.BLACK;\n          if (curNode._left) {\n            curNode._left._parent = parentNode;\n          }\n          if (curNode._right) {\n            curNode._right._parent = grandParent;\n          }\n          parentNode._right = curNode._left;\n          grandParent._left = curNode._right;\n          curNode._left = parentNode;\n          curNode._right = grandParent;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent!;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = TreeNodeColor.RED;\n        } else {\n          parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateRight();\n          } else grandParent._rotateRight();\n          grandParent._color = TreeNodeColor.RED;\n          return;\n        }\n      } else {\n        const uncle = grandParent._left;\n        if (uncle && uncle._color === TreeNodeColor.RED) {\n          uncle._color = parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) return;\n          grandParent._color = TreeNodeColor.RED;\n          curNode = grandParent;\n          continue;\n        } else if (curNode === parentNode._left) {\n          curNode._color = TreeNodeColor.BLACK;\n          if (curNode._left) {\n            curNode._left._parent = grandParent;\n          }\n          if (curNode._right) {\n            curNode._right._parent = parentNode;\n          }\n          grandParent._right = curNode._left;\n          parentNode._left = curNode._right;\n          curNode._left = grandParent;\n          curNode._right = parentNode;\n          if (grandParent === this._root) {\n            this._root = curNode;\n            this._header._parent = curNode;\n          } else {\n            const GP = grandParent._parent!;\n            if (GP._left === grandParent) {\n              GP._left = curNode;\n            } else GP._right = curNode;\n          }\n          curNode._parent = grandParent._parent;\n          parentNode._parent = curNode;\n          grandParent._parent = curNode;\n          grandParent._color = TreeNodeColor.RED;\n        } else {\n          parentNode._color = TreeNodeColor.BLACK;\n          if (grandParent === this._root) {\n            this._root = grandParent._rotateLeft();\n          } else grandParent._rotateLeft();\n          grandParent._color = TreeNodeColor.RED;\n          return;\n        }\n      }\n      if (this.enableIndex) {\n        (<TreeNodeEnableIndex<K, V>>parentNode)._recount();\n        (<TreeNodeEnableIndex<K, V>>grandParent)._recount();\n        (<TreeNodeEnableIndex<K, V>>curNode)._recount();\n      }\n      return;\n    }\n  }\n  /**\n   * @internal\n   */\n  protected _set(key: K, value?: V, hint?: TreeIterator<K, V>) {\n    if (this._root === undefined) {\n      this._length += 1;\n      this._root = new this._TreeNodeClass(key, value, TreeNodeColor.BLACK);\n      this._root._parent = this._header;\n      this._header._parent = this._header._left = this._header._right = this._root;\n      return this._length;\n    }\n    let curNode;\n    const minNode = this._header._left!;\n    const compareToMin = this._cmp(minNode._key!, key);\n    if (compareToMin === 0) {\n      minNode._value = value;\n      return this._length;\n    } else if (compareToMin > 0) {\n      minNode._left = new this._TreeNodeClass(key, value);\n      minNode._left._parent = minNode;\n      curNode = minNode._left;\n      this._header._left = curNode;\n    } else {\n      const maxNode = this._header._right!;\n      const compareToMax = this._cmp(maxNode._key!, key);\n      if (compareToMax === 0) {\n        maxNode._value = value;\n        return this._length;\n      } else if (compareToMax < 0) {\n        maxNode._right = new this._TreeNodeClass(key, value);\n        maxNode._right._parent = maxNode;\n        curNode = maxNode._right;\n        this._header._right = curNode;\n      } else {\n        if (hint !== undefined) {\n          const iterNode = hint._node;\n          if (iterNode !== this._header) {\n            const iterCmpRes = this._cmp(iterNode._key!, key);\n            if (iterCmpRes === 0) {\n              iterNode._value = value;\n              return this._length;\n            } else /* istanbul ignore else */ if (iterCmpRes > 0) {\n              const preNode = iterNode._pre();\n              const preCmpRes = this._cmp(preNode._key!, key);\n              if (preCmpRes === 0) {\n                preNode._value = value;\n                return this._length;\n              } else if (preCmpRes < 0) {\n                curNode = new this._TreeNodeClass(key, value);\n                if (preNode._right === undefined) {\n                  preNode._right = curNode;\n                  curNode._parent = preNode;\n                } else {\n                  iterNode._left = curNode;\n                  curNode._parent = iterNode;\n                }\n              }\n            }\n          }\n        }\n        if (curNode === undefined) {\n          curNode = this._root;\n          while (true) {\n            const cmpResult = this._cmp(curNode._key!, key);\n            if (cmpResult > 0) {\n              if (curNode._left === undefined) {\n                curNode._left = new this._TreeNodeClass(key, value);\n                curNode._left._parent = curNode;\n                curNode = curNode._left;\n                break;\n              }\n              curNode = curNode._left;\n            } else if (cmpResult < 0) {\n              if (curNode._right === undefined) {\n                curNode._right = new this._TreeNodeClass(key, value);\n                curNode._right._parent = curNode;\n                curNode = curNode._right;\n                break;\n              }\n              curNode = curNode._right;\n            } else {\n              curNode._value = value;\n              return this._length;\n            }\n          }\n        }\n      }\n    }\n    if (this.enableIndex) {\n      let parent = curNode._parent as TreeNodeEnableIndex<K, V>;\n      while (parent !== this._header) {\n        parent._subTreeSize += 1;\n        parent = parent._parent as TreeNodeEnableIndex<K, V>;\n      }\n    }\n    this._insertNodeSelfBalance(curNode);\n    this._length += 1;\n    return this._length;\n  }\n  /**\n   * @internal\n   */\n  protected _getTreeNodeByKey(curNode: TreeNode<K, V> | undefined, key: K) {\n    while (curNode) {\n      const cmpResult = this._cmp(curNode._key!, key);\n      if (cmpResult < 0) {\n        curNode = curNode._right;\n      } else if (cmpResult > 0) {\n        curNode = curNode._left;\n      } else return curNode;\n    }\n    return curNode || this._header;\n  }\n  clear() {\n    this._length = 0;\n    this._root = undefined;\n    this._header._parent = undefined;\n    this._header._left = this._header._right = undefined;\n  }\n  /**\n   * @description Update node's key by iterator.\n   * @param iter - The iterator you want to change.\n   * @param key - The key you want to update.\n   * @returns Whether the modification is successful.\n   * @example\n   * const st = new orderedSet([1, 2, 5]);\n   * const iter = st.find(2);\n   * st.updateKeyByIterator(iter, 3); // then st will become [1, 3, 5]\n   */\n  updateKeyByIterator(iter: TreeIterator<K, V>, key: K): boolean {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    if (this._length === 1) {\n      node._key = key;\n      return true;\n    }\n    const nextKey = node._next()._key!;\n    if (node === this._header._left) {\n      if (this._cmp(nextKey, key) > 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    const preKey = node._pre()._key!;\n    if (node === this._header._right) {\n      if (this._cmp(preKey, key) < 0) {\n        node._key = key;\n        return true;\n      }\n      return false;\n    }\n    if (\n      this._cmp(preKey, key) >= 0 ||\n      this._cmp(nextKey, key) <= 0\n    ) return false;\n    node._key = key;\n    return true;\n  }\n  eraseElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    const node = this._inOrderTraversal(pos);\n    this._eraseNode(node);\n    return this._length;\n  }\n  /**\n   * @description Remove the element of the specified key.\n   * @param key - The key you want to remove.\n   * @returns Whether erase successfully.\n   */\n  eraseElementByKey(key: K) {\n    if (this._length === 0) return false;\n    const curNode = this._getTreeNodeByKey(this._root, key);\n    if (curNode === this._header) return false;\n    this._eraseNode(curNode);\n    return true;\n  }\n  eraseElementByIterator(iter: TreeIterator<K, V>) {\n    const node = iter._node;\n    if (node === this._header) {\n      throwIteratorAccessError();\n    }\n    const hasNoRight = node._right === undefined;\n    const isNormal = iter.iteratorType === IteratorType.NORMAL;\n    // For the normal iterator, the `next` node will be swapped to `this` node when has right.\n    if (isNormal) {\n      // So we should move it to next when it's right is null.\n      if (hasNoRight) iter.next();\n    } else {\n      // For the reverse iterator, only when it doesn't have right and has left the `next` node will be swapped.\n      // So when it has right, or it is a leaf node we should move it to `next`.\n      if (!hasNoRight || node._left === undefined) iter.next();\n    }\n    this._eraseNode(node);\n    return iter;\n  }\n  /**\n   * @description Get the height of the tree.\n   * @returns Number about the height of the RB-tree.\n   */\n  getHeight() {\n    if (this._length === 0) return 0;\n    function traversal(curNode: TreeNode<K, V> | undefined): number {\n      if (!curNode) return 0;\n      return Math.max(traversal(curNode._left), traversal(curNode._right)) + 1;\n    }\n    return traversal(this._root);\n  }\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element less than the given key.\n   */\n  abstract reverseUpperBound(key: K): TreeIterator<K, V>;\n  /**\n   * @description Union the other tree to self.\n   * @param other - The other tree container you want to merge.\n   * @returns The size of the tree after union.\n   */\n  abstract union(other: TreeContainer<K, V>): number;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element not greater than the given key.\n   */\n  abstract reverseLowerBound(key: K): TreeIterator<K, V>;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element not less than the given key.\n   */\n  abstract lowerBound(key: K): TreeIterator<K, V>;\n  /**\n   * @param key - The given key you want to compare.\n   * @returns An iterator to the first element greater than the given key.\n   */\n  abstract upperBound(key: K): TreeIterator<K, V>;\n}\n\nexport default TreeContainer;\n", "import { TreeNode } from './TreeNode';\nimport type { TreeNodeEnableIndex } from './TreeNode';\nimport { ContainerIterator, IteratorType } from '@/container/ContainerBase';\nimport TreeContainer from '@/container/TreeContainer/Base/index';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nabstract class TreeIterator<K, V> extends ContainerIterator<K | [K, V]> {\n  abstract readonly container: TreeContainer<K, V>;\n  /**\n   * @internal\n   */\n  _node: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected _header: TreeNode<K, V>;\n  /**\n   * @internal\n   */\n  protected constructor(\n    node: TreeNode<K, V>,\n    header: TreeNode<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(iteratorType);\n    this._node = node;\n    this._header = header;\n    if (this.iteratorType === IteratorType.NORMAL) {\n      this.pre = function () {\n        if (this._node === this._header._left) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n    } else {\n      this.pre = function () {\n        if (this._node === this._header._right) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._next();\n        return this;\n      };\n\n      this.next = function () {\n        if (this._node === this._header) {\n          throwIteratorAccessError();\n        }\n        this._node = this._node._pre();\n        return this;\n      };\n    }\n  }\n  /**\n   * @description Get the sequential index of the iterator in the tree container.<br/>\n   *              <strong>Note:</strong>\n   *              This function only takes effect when the specified tree container `enableIndex = true`.\n   * @returns The index subscript of the node in the tree.\n   * @example\n   * const st = new OrderedSet([1, 2, 3], true);\n   * console.log(st.begin().next().index);  // 1\n   */\n  get index() {\n    let _node = this._node as TreeNodeEnableIndex<K, V>;\n    const root = this._header._parent as TreeNodeEnableIndex<K, V>;\n    if (_node === this._header) {\n      if (root) {\n        return root._subTreeSize - 1;\n      }\n      return 0;\n    }\n    let index = 0;\n    if (_node._left) {\n      index += (_node._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n    }\n    while (_node !== root) {\n      const _parent = _node._parent as TreeNodeEnableIndex<K, V>;\n      if (_node === _parent._right) {\n        index += 1;\n        if (_parent._left) {\n          index += (_parent._left as TreeNodeEnableIndex<K, V>)._subTreeSize;\n        }\n      }\n      _node = _parent;\n    }\n    return index;\n  }\n  isAccessible() {\n    return this._node !== this._header;\n  }\n  // @ts-ignore\n  pre(): this;\n  // @ts-ignore\n  next(): this;\n}\n\nexport default TreeIterator;\n", "import TreeContainer from './Base';\nimport TreeIterator from './Base/TreeIterator';\nimport { TreeNode } from './Base/TreeNode';\nimport { initContainer, IteratorType } from '@/container/ContainerBase';\nimport $checkWithinAccessParams from '@/utils/checkParams.macro';\nimport { throwIteratorAccessError } from '@/utils/throwError';\n\nclass OrderedMapIterator<K, V> extends TreeIterator<K, V> {\n  container: OrderedMap<K, V>;\n  constructor(\n    node: TreeNode<K, V>,\n    header: TreeNode<K, V>,\n    container: OrderedMap<K, V>,\n    iteratorType?: IteratorType\n  ) {\n    super(node, header, iteratorType);\n    this.container = container;\n  }\n  get pointer() {\n    if (this._node === this._header) {\n      throwIteratorAccessError();\n    }\n    const self = this;\n    return new Proxy(<[K, V]><unknown>[], {\n      get(target, prop: '0' | '1') {\n        if (prop === '0') return self._node._key!;\n        else if (prop === '1') return self._node._value!;\n        target[0] = self._node._key!;\n        target[1] = self._node._value!;\n        return target[prop];\n      },\n      set(_, prop: '1', newValue: V) {\n        if (prop !== '1') {\n          throw new TypeError('prop must be 1');\n        }\n        self._node._value = newValue;\n        return true;\n      }\n    });\n  }\n  copy() {\n    return new OrderedMapIterator<K, V>(\n      this._node,\n      this._header,\n      this.container,\n      this.iteratorType\n    );\n  }\n  // @ts-ignore\n  equals(iter: OrderedMapIterator<K, V>): boolean;\n}\n\nexport type { OrderedMapIterator };\n\nclass OrderedMap<K, V> extends TreeContainer<K, V> {\n  /**\n   * @param container - The initialization container.\n   * @param cmp - The compare function.\n   * @param enableIndex - Whether to enable iterator indexing function.\n   * @example\n   * new OrderedMap();\n   * new OrderedMap([[0, 1], [2, 1]]);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y);\n   * new OrderedMap([[0, 1], [2, 1]], (x, y) => x - y, true);\n   */\n  constructor(\n    container: initContainer<[K, V]> = [],\n    cmp?: (x: K, y: K) => number,\n    enableIndex?: boolean\n  ) {\n    super(cmp, enableIndex);\n    const self = this;\n    container.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n  }\n  begin() {\n    return new OrderedMapIterator<K, V>(this._header._left || this._header, this._header, this);\n  }\n  end() {\n    return new OrderedMapIterator<K, V>(this._header, this._header, this);\n  }\n  rBegin() {\n    return new OrderedMapIterator<K, V>(\n      this._header._right || this._header,\n      this._header,\n      this,\n      IteratorType.REVERSE\n    );\n  }\n  rEnd() {\n    return new OrderedMapIterator<K, V>(this._header, this._header, this, IteratorType.REVERSE);\n  }\n  front() {\n    if (this._length === 0) return;\n    const minNode = this._header._left!;\n    return <[K, V]>[minNode._key, minNode._value];\n  }\n  back() {\n    if (this._length === 0) return;\n    const maxNode = this._header._right!;\n    return <[K, V]>[maxNode._key, maxNode._value];\n  }\n  lowerBound(key: K) {\n    const resNode = this._lowerBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  upperBound(key: K) {\n    const resNode = this._upperBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  reverseLowerBound(key: K) {\n    const resNode = this._reverseLowerBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  reverseUpperBound(key: K) {\n    const resNode = this._reverseUpperBound(this._root, key);\n    return new OrderedMapIterator<K, V>(resNode, this._header, this);\n  }\n  forEach(callback: (element: [K, V], index: number, map: OrderedMap<K, V>) => void) {\n    this._inOrderTraversal(function (node, index, map) {\n      callback(<[K, V]>[node._key, node._value], index, map);\n    });\n  }\n  /**\n   * @description Insert a key-value pair or set value by the given key.\n   * @param key - The key want to insert.\n   * @param value - The value want to set.\n   * @param hint - You can give an iterator hint to improve insertion efficiency.\n   * @return The size of container after setting.\n   * @example\n   * const mp = new OrderedMap([[2, 0], [4, 0], [5, 0]]);\n   * const iter = mp.begin();\n   * mp.setElement(1, 0);\n   * mp.setElement(3, 0, iter);  // give a hint will be faster.\n   */\n  setElement(key: K, value: V, hint?: OrderedMapIterator<K, V>) {\n    return this._set(key, value, hint);\n  }\n  getElementByPos(pos: number) {\n    $checkWithinAccessParams!(pos, 0, this._length - 1);\n    const node = this._inOrderTraversal(pos);\n    return <[K, V]>[node._key, node._value];\n  }\n  find(key: K) {\n    const curNode = this._getTreeNodeByKey(this._root, key);\n    return new OrderedMapIterator<K, V>(curNode, this._header, this);\n  }\n  /**\n   * @description Get the value of the element of the specified key.\n   * @param key - The specified key you want to get.\n   * @example\n   * const val = container.getElementByKey(1);\n   */\n  getElementByKey(key: K) {\n    const curNode = this._getTreeNodeByKey(this._root, key);\n    return curNode._value;\n  }\n  union(other: OrderedMap<K, V>) {\n    const self = this;\n    other.forEach(function (el) {\n      self.setElement(el[0], el[1]);\n    });\n    return this._length;\n  }\n  * [Symbol.iterator]() {\n    const length = this._length;\n    const nodeList = this._inOrderTraversal();\n    for (let i = 0; i < length; ++i) {\n      const node = nodeList[i];\n      yield <[K, V]>[node._key, node._value];\n    }\n  }\n  // @ts-ignore\n  eraseElementByIterator(iter: OrderedMapIterator<K, V>): OrderedMapIterator<K, V>;\n}\n\nexport default OrderedMap;\n"]}