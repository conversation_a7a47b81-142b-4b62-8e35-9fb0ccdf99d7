#!/bin/bash

# Copyright (c) 2015 Uber Technologies, Inc.
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.

set -e
set -x

if [ -z "$1" ]; then
    echo "must pass in version as first arg" >&2
    exit 1
fi

if ! which tac >/dev/null 2>&1; then
    function tac()
    {
        tail -r
    }
fi

GIT_TAG=$(npm version "$1")

if [[ $(head -n1 CHANGELOG.md) = '# vNEXT'* ]]; then
    sed -i -e "/^# vNEXT/s/vNEXT.*/$GIT_TAG/" CHANGELOG.md
    git commit --amend --no-edit CHANGELOG.md
    git tag -a -f -F <(
        git cat-file tag "$GIT_TAG"  | tac | sed -e '/^$/q' | tac | tail -n+2
        ) "$GIT_TAG" HEAD
fi

if head_ref=$(git symbolic-ref HEAD 2>/dev/null); then
    branch_name=${head_ref##*/}
    git push origin "$branch_name" --tags
else
    git push origin --tags
fi

git archive --prefix=package/ --format tgz HEAD >package.tgz
${NPM:-npm} publish --registry=https://registry.npmjs.org/ package.tgz --tag "${NPM_TAG:-alpha}"
rm package.tgz
npm cache clean thriftrw
