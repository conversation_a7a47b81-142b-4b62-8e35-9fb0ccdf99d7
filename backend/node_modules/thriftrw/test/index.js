// Copyright (c) 2018 Uber Technologies, Inc.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.

'use strict';

require('./binary');
require('./boolean');
require('./double');
require('./i8');
require('./i16');
require('./i32');
require('./i64');
require('./map-entries');
require('./thrift-idl');
require('./map-object');
require('./string');
require('./tlist');
require('./tmap');
require('./tstruct');
require('./void');
require('./skip');
require('./struct');
require('./struct-skip');
require('./recursion');
require('./exception');
require('./union');
require('./service');
require('./thrift');
require('./list');
require('./set');
require('./map');
require('./typedef');
require('./const');
require('./default');
require('./enum');
require('./unrecognized-exception');
require('./include.js');
require('./type-mismatch');
require('./lcp');
require('./idls');
require('./asts');
require('./message');
