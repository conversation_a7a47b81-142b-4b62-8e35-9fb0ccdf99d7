{"name": "thriftrw", "version": "3.11.4", "description": "thrift encoding/decoding using bufrw", "keywords": [], "author": "<PERSON><PERSON> <<EMAIL>>", "repository": "git://github.com/thriftrw/thriftrw-node.git", "main": "index.js", "homepage": "https://github.com/thriftrw/thriftrw-node", "bugs": {"url": "https://github.com/thriftrw/thriftrw-node/issues", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>"}], "dependencies": {"bufrw": "^1.2.1", "error": "7.0.2", "long": "^2.4.0"}, "devDependencies": {"coveralls": "^2.10.0", "debug": "^2.1.2", "eslint": "^2.2.0", "fast-stats": "0.0.3", "faucet": "0.0.1", "istanbul": "^0.3.5", "itape": "^1.5.0", "jsen": "^0.6.0", "opn": "^1.0.1", "pegjs": "^0.8.0", "pre-commit": "0.0.9", "tape": "^2.0.0", "uber-licence": "^2.0.0"}, "licenses": [{"type": "MIT", "url": "http://github.com/thriftrw/thriftrw-node/raw/master/LICENSE"}], "bin": {"thrift2json": "./thrift2json.js"}, "scripts": {"add-licence": "uber-licence", "build-parser": "pegjs --allowed-start-rules Program thrift-idl.pegjs && uber-licence --file thrift-idl.js > /dev/null", "check-cover": "istanbul check-coverage", "check-licence": "uber-licence --dry", "check-ls": "npm ls 1>/dev/null", "cover": "istanbul cover --report html --print none -- test/index.js | faucet && istanbul report text && npm run check-cover -s", "lint": "eslint .", "test": "npm run check-ls -s && npm run build-parser -s && npm run lint -s && npm run cover -s", "trace": "itape test/index.js --trace", "travis": "npm run cover -s && istanbul report lcov && ((cat coverage/lcov.info | coveralls) || exit 0)", "view-cover": "opn ./coverage/index.html"}, "engines": {"node": ">= 0.10.x"}, "pre-commit": ["check-licence", "test"], "pre-commit.silent": true, "itape": {"trace": {"debuglog": ["thriftrw"], "leakedHandles": {"timeout": 5001, "debugSockets": true}, "formatStack": true}}, "uber-ngen-version": "5.2.0"}