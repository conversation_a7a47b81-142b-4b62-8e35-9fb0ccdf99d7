// Copyright (c) 2018 Uber Technologies, Inc.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.

'use strict';

function lengthOfCommonPrefix(strings) {
    if (strings.length === 0) {
        return Infinity;
    }
    var longest = strings[0];
    var length = longest.length;
    for (var i = 1; i < strings.length; i++) {
        var string = strings[i];
        for (var j = 0; j < Math.min(length, string.length); j++) {
            if (string[j] !== longest[j]) {
                break;
            }
        }
        length = j;
    }
    return length;
}

function longestCommonPath(paths) {
    var length = lengthOfCommonPrefix(paths);
    var end = paths[0].lastIndexOf('/', length - 1);
    return paths[0].slice(0, end + 1);
}

module.exports.lengthOfCommonPrefix = lengthOfCommonPrefix;
module.exports.longestCommonPath = longestCommonPath;
