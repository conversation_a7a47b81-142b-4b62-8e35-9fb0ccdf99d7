{"name": "string-template", "version": "0.2.1", "description": "A simple string template function based on named or indexed arguments", "keywords": ["template", "string", "format", "replace", "arguments"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "repository": "git://github.com/<PERSON>-<PERSON>/string-template.git", "main": "index", "homepage": "https://github.com/<PERSON>-<PERSON>/string-template", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/string-template/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"tape": "~1.1.1"}, "licenses": [{"type": "MIT", "url": "http://github.com/<PERSON>-<PERSON>/string-template/raw/master/LICENSE"}], "scripts": {"test": "node ./test/index.js", "travis-test": "istanbul cover ./test/index.js && ((cat coverage/lcov.info | coveralls) || exit 0)", "cover": "istanbul cover --report none --print detail ./test/index.js", "view-cover": "istanbul report html && google-chrome ./coverage/index.html"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}