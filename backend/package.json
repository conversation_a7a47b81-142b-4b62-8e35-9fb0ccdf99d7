{"name": "university-portal-api", "version": "1.0.0", "description": "High-performance university portal API backend", "main": "src/server.js", "type": "module", "scripts": {"build": "tsc", "start": "node --loader ts-node/esm src/server.ts", "dev": "nodemon --exec node --loader ts-node/esm src/server.ts", "dev:ts": "tsc-watch --onSuccess \"node --loader ts-node/esm src/server.ts\"", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "load:test": "docker run --rm -v $(pwd)/load-tests:/scripts loadimpact/k6 run /scripts/load-test.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "docker:build": "docker build -t university-portal-api .", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose up --build", "seed": "node src/scripts/seed-database.js", "migrate": "node src/scripts/migrate.js"}, "keywords": ["university", "portal", "api", "nodejs", "mongodb", "redis", "microservices"], "author": "University Portal Team", "license": "MIT", "dependencies": {"@opentelemetry/api": "^1.7.0", "@opentelemetry/auto-instrumentations-node": "^0.40.3", "@opentelemetry/exporter-jaeger": "^1.18.1", "@opentelemetry/sdk-node": "^0.45.1", "@socket.io/redis-adapter": "^8.3.0", "argon2": "^0.31.2", "aws-sdk": "^2.1490.0", "bcrypt": "^5.1.1", "celebrate": "^15.0.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongo-sanitize": "^1.1.0", "mongodb": "^6.20.0", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "prom-client": "^15.0.0", "qrcode": "^1.5.3", "redis": "^4.7.1", "socket.io": "^4.7.4", "socket.io-redis": "^6.1.1", "speakeasy": "^2.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/express": "^5.0.3", "@types/jest": "^29.5.8", "@types/node": "^24.7.2", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "k6": "^0.0.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsc-watch": "^7.2.0", "typescript": "^5.9.3"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/scripts/**", "!src/config/**"], "coverageThreshold": {"global": {"branches": 85, "functions": 85, "lines": 85, "statements": 85}}}}