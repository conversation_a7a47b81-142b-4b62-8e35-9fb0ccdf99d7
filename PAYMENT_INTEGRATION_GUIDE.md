# Kenyan Payment Integration Implementation Guide

## 🏦 **Overview**

This guide provides a comprehensive implementation plan for integrating Kenyan payment methods into the university portal, including mobile payments (M-Pesa, JamboPay) and bank payments (I&M Bank, KCB BUNI, Pesalink).

## 📋 **Table of Contents**

1. [Preparatory Steps](#preparatory-steps)
2. [API Credentials Management](#api-credentials-management)
3. [Backend Architecture](#backend-architecture)
4. [Payment Method Implementations](#payment-method-implementations)
5. [Webhook Implementation](#webhook-implementation)
6. [Error Handling & Reconciliation](#error-handling--reconciliation)
7. [Testing Strategy](#testing-strategy)
8. [Security Guidelines](#security-guidelines)
9. [Logging & Monitoring](#logging--monitoring)
10. [Code Implementation](#code-implementation)

## 🚀 **Preparatory Steps**

### **1. Business Account Registration**

**M-Pesa (Safaricom):**
- Register business account with Safaricom
- Apply for Lipa na M-Pesa merchant account
- Provide business registration documents
- Wait for approval (2-4 weeks)

**JamboPay:**
- Register at [jambopay.com](https://jambopay.com)
- Complete KYC verification
- Provide business details and bank account

**Bank APIs:**
- **I&M Bank**: Contact I&M Bank API team
- **KCB BUNI**: Register at [buni.kcbgroup.com](https://buni.kcbgroup.com)
- **Pesalink**: Register at [pesalink.co.ke](https://pesalink.co.ke)

### **2. Developer Account Setup**

**Required Information:**
- Business registration certificate
- Tax compliance certificate
- Bank account details
- Technical contact information
- IP whitelist for production

**Documentation Access:**
- API documentation
- Sandbox credentials
- Webhook specifications
- Rate limits and SLA

## 🔐 **API Credentials Management**

### **Secure Credential Storage**

```typescript
// config/payment-config.ts
export interface PaymentConfig {
  mpesa: {
    consumerKey: string;
    consumerSecret: string;
    businessShortCode: string;
    passkey: string;
    environment: 'sandbox' | 'production';
    callbackUrl: string;
  };
  jambopay: {
    apiKey: string;
    merchantId: string;
    environment: 'sandbox' | 'production';
    callbackUrl: string;
  };
  imBank: {
    clientId: string;
    clientSecret: string;
    baseUrl: string;
    environment: 'sandbox' | 'production';
  };
  kcbBuni: {
    apiKey: string;
    merchantId: string;
    baseUrl: string;
    environment: 'sandbox' | 'production';
  };
  pesalink: {
    apiKey: string;
    merchantId: string;
    baseUrl: string;
    environment: 'sandbox' | 'production';
  };
}

// Environment variables
const paymentConfig: PaymentConfig = {
  mpesa: {
    consumerKey: process.env.MPESA_CONSUMER_KEY!,
    consumerSecret: process.env.MPESA_CONSUMER_SECRET!,
    businessShortCode: process.env.MPESA_BUSINESS_SHORT_CODE!,
    passkey: process.env.MPESA_PASSKEY!,
    environment: process.env.MPESA_ENVIRONMENT as 'sandbox' | 'production',
    callbackUrl: process.env.MPESA_CALLBACK_URL!,
  },
  // ... other payment methods
};
```

### **Credential Encryption**

```typescript
// utils/encryption.ts
import crypto from 'crypto';

export class CredentialEncryption {
  private static readonly algorithm = 'aes-256-gcm';
  private static readonly key = crypto.scryptSync(process.env.ENCRYPTION_KEY!, 'salt', 32);

  static encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key);
    cipher.setAAD(Buffer.from('payment-credentials'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  static decrypt(encryptedText: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedText.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');
    
    const decipher = crypto.createDecipher(this.algorithm, this.key);
    decipher.setAAD(Buffer.from('payment-credentials'));
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

## 🏗️ **Backend Architecture**

### **Database Schema**

```sql
-- Payment Methods Table
CREATE TABLE payment_methods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  code VARCHAR(50) UNIQUE NOT NULL,
  provider VARCHAR(100) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  config JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Payment Transactions Table
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES users(id),
  fee_id UUID REFERENCES fees(id),
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'KES',
  payment_method_id UUID REFERENCES payment_methods(id),
  provider_transaction_id VARCHAR(255),
  status VARCHAR(50) DEFAULT 'pending',
  reference_number VARCHAR(100) UNIQUE NOT NULL,
  metadata JSONB,
  callback_data JSONB,
  initiated_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP,
  failed_at TIMESTAMP,
  failure_reason TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Payment Webhooks Table
CREATE TABLE payment_webhooks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id UUID REFERENCES payment_transactions(id),
  provider VARCHAR(100) NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  payload JSONB NOT NULL,
  signature VARCHAR(500),
  processed BOOLEAN DEFAULT false,
  processed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Payment Reconciliation Table
CREATE TABLE payment_reconciliations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id UUID REFERENCES payment_transactions(id),
  provider_transaction_id VARCHAR(255),
  amount DECIMAL(10,2) NOT NULL,
  status VARCHAR(50) NOT NULL,
  reconciled_at TIMESTAMP DEFAULT NOW(),
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### **Service Architecture**

```typescript
// services/payment-service.ts
export interface PaymentService {
  initiatePayment(request: PaymentRequest): Promise<PaymentResponse>;
  verifyPayment(transactionId: string): Promise<PaymentStatus>;
  handleWebhook(payload: any, signature: string): Promise<void>;
  reconcilePayment(transactionId: string): Promise<ReconciliationResult>;
}

// services/mpesa-service.ts
export class MpesaService implements PaymentService {
  private config: MpesaConfig;
  private httpClient: AxiosInstance;

  constructor(config: MpesaConfig) {
    this.config = config;
    this.httpClient = axios.create({
      baseURL: config.environment === 'production' 
        ? 'https://api.safaricom.co.ke' 
        : 'https://sandbox.safaricom.co.ke',
      timeout: 30000,
    });
  }

  async initiatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      const accessToken = await this.getAccessToken();
      const timestamp = this.generateTimestamp();
      const password = this.generatePassword(timestamp);
      
      const stkPushRequest = {
        BusinessShortCode: this.config.businessShortCode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: 'CustomerPayBillOnline',
        Amount: request.amount,
        PartyA: request.phoneNumber,
        PartyB: this.config.businessShortCode,
        PhoneNumber: request.phoneNumber,
        CallBackURL: this.config.callbackUrl,
        AccountReference: request.referenceNumber,
        TransactionDesc: request.description,
      };

      const response = await this.httpClient.post('/mpesa/stkpush/v1/processrequest', stkPushRequest, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      return {
        success: true,
        transactionId: response.data.CheckoutRequestID,
        message: 'Payment initiated successfully',
        providerResponse: response.data,
      };
    } catch (error) {
      throw new PaymentError('M-Pesa payment initiation failed', error);
    }
  }

  private async getAccessToken(): Promise<string> {
    const auth = Buffer.from(`${this.config.consumerKey}:${this.config.consumerSecret}`).toString('base64');
    
    const response = await this.httpClient.get('/oauth/v1/generate?grant_type=client_credentials', {
      headers: {
        'Authorization': `Basic ${auth}`,
      },
    });

    return response.data.access_token;
  }

  private generateTimestamp(): string {
    return new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3);
  }

  private generatePassword(timestamp: string): string {
    const data = `${this.config.businessShortCode}${this.config.passkey}${timestamp}`;
    return Buffer.from(data).toString('base64');
  }
}
```

## 💳 **Payment Method Implementations**

### **1. M-Pesa STK Push Implementation**

```typescript
// controllers/payment-controller.ts
export class PaymentController {
  constructor(
    private paymentService: PaymentService,
    private transactionService: TransactionService,
    private notificationService: NotificationService
  ) {}

  async initiateMpesaPayment(req: Request, res: Response) {
    try {
      const { studentId, feeId, phoneNumber, amount } = req.body;
      
      // Validate student and fee
      const student = await this.studentService.findById(studentId);
      const fee = await this.feeService.findById(feeId);
      
      if (!student || !fee) {
        return res.status(404).json({
          success: false,
          message: 'Student or fee not found'
        });
      }

      // Create transaction record
      const transaction = await this.transactionService.create({
        studentId,
        feeId,
        amount,
        paymentMethod: 'mpesa',
        referenceNumber: this.generateReferenceNumber(),
        metadata: {
          phoneNumber,
          feeDetails: fee
        }
      });

      // Initiate M-Pesa payment
      const paymentRequest: PaymentRequest = {
        amount,
        phoneNumber,
        referenceNumber: transaction.referenceNumber,
        description: `Fee payment for ${fee.name}`,
        callbackUrl: `${process.env.API_BASE_URL}/api/payments/mpesa/callback`
      };

      const paymentResponse = await this.paymentService.initiatePayment(paymentRequest);
      
      // Update transaction with provider details
      await this.transactionService.update(transaction.id, {
        providerTransactionId: paymentResponse.transactionId,
        status: 'initiated',
        metadata: {
          ...transaction.metadata,
          providerResponse: paymentResponse.providerResponse
        }
      });

      // Send notification to student
      await this.notificationService.send({
        userId: studentId,
        title: 'Payment Initiated',
        message: `M-Pesa payment of KES ${amount} has been initiated. Please complete the payment on your phone.`,
        type: 'info'
      });

      res.json({
        success: true,
        data: {
          transactionId: transaction.id,
          referenceNumber: transaction.referenceNumber,
          amount,
          message: 'Payment initiated successfully. Please complete the payment on your phone.'
        }
      });

    } catch (error) {
      logger.error('M-Pesa payment initiation failed:', error);
      res.status(500).json({
        success: false,
        message: 'Payment initiation failed',
        error: error.message
      });
    }
  }
}
```

### **2. Bank Transfer Implementation**

```typescript
// services/bank-transfer-service.ts
export class BankTransferService implements PaymentService {
  constructor(
    private imBankService: ImBankService,
    private kcbBuniService: KcbBuniService,
    private pesalinkService: PesalinkService
  ) {}

  async initiatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    const { bankCode, accountNumber, amount, referenceNumber } = request;
    
    try {
      let response: PaymentResponse;
      
      switch (bankCode) {
        case 'IMB':
          response = await this.imBankService.initiateTransfer({
            accountNumber,
            amount,
            referenceNumber,
            description: request.description
          });
          break;
          
        case 'KCB':
          response = await this.kcbBuniService.initiateTransfer({
            accountNumber,
            amount,
            referenceNumber,
            description: request.description
          });
          break;
          
        case 'PESALINK':
          response = await this.pesalinkService.initiateTransfer({
            accountNumber,
            amount,
            referenceNumber,
            description: request.description
          });
          break;
          
        default:
          throw new Error(`Unsupported bank code: ${bankCode}`);
      }

      return response;
    } catch (error) {
      throw new PaymentError('Bank transfer initiation failed', error);
    }
  }
}

// services/im-bank-service.ts
export class ImBankService {
  private config: ImBankConfig;
  private httpClient: AxiosInstance;

  constructor(config: ImBankConfig) {
    this.config = config;
    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: 30000,
    });
  }

  async initiateTransfer(request: BankTransferRequest): Promise<PaymentResponse> {
    try {
      const accessToken = await this.getAccessToken();
      
      const transferRequest = {
        accountNumber: request.accountNumber,
        amount: request.amount,
        referenceNumber: request.referenceNumber,
        description: request.description,
        currency: 'KES',
        timestamp: new Date().toISOString()
      };

      const response = await this.httpClient.post('/api/v1/transfers', transferRequest, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      return {
        success: true,
        transactionId: response.data.transactionId,
        message: 'Bank transfer initiated successfully',
        providerResponse: response.data,
      };
    } catch (error) {
      throw new PaymentError('I&M Bank transfer failed', error);
    }
  }

  private async getAccessToken(): Promise<string> {
    const response = await this.httpClient.post('/oauth/token', {
      grant_type: 'client_credentials',
      client_id: this.config.clientId,
      client_secret: this.config.clientSecret,
    });

    return response.data.access_token;
  }
}
```

## 🔔 **Webhook Implementation**

### **Webhook Handler**

```typescript
// controllers/webhook-controller.ts
export class WebhookController {
  constructor(
    private paymentService: PaymentService,
    private transactionService: TransactionService,
    private notificationService: NotificationService,
    private reconciliationService: ReconciliationService
  ) {}

  async handleMpesaCallback(req: Request, res: Response) {
    try {
      const payload = req.body;
      const signature = req.headers['x-mpesa-signature'] as string;
      
      // Verify webhook signature
      if (!this.verifyMpesaSignature(payload, signature)) {
        logger.warn('Invalid M-Pesa webhook signature');
        return res.status(400).json({ success: false, message: 'Invalid signature' });
      }

      // Log webhook for audit
      await this.logWebhook('mpesa', 'payment_callback', payload, signature);

      const { Body } = payload;
      const { stkCallback } = Body;
      
      if (stkCallback.ResultCode === 0) {
        // Payment successful
        await this.handleSuccessfulPayment(stkCallback);
      } else {
        // Payment failed
        await this.handleFailedPayment(stkCallback);
      }

      res.json({ success: true, message: 'Webhook processed successfully' });
    } catch (error) {
      logger.error('M-Pesa webhook processing failed:', error);
      res.status(500).json({ success: false, message: 'Webhook processing failed' });
    }
  }

  private async handleSuccessfulPayment(callback: any) {
    const { CheckoutRequestID, MerchantRequestID, ResultDesc } = callback;
    
    // Find transaction by provider transaction ID
    const transaction = await this.transactionService.findByProviderTransactionId(CheckoutRequestID);
    
    if (!transaction) {
      logger.error(`Transaction not found for M-Pesa callback: ${CheckoutRequestID}`);
      return;
    }

    // Update transaction status
    await this.transactionService.update(transaction.id, {
      status: 'completed',
      completedAt: new Date(),
      callbackData: callback,
      metadata: {
        ...transaction.metadata,
        resultDescription: ResultDesc
      }
    });

    // Update fee payment status
    await this.feeService.markAsPaid(transaction.feeId, transaction.id);

    // Send success notification
    await this.notificationService.send({
      userId: transaction.studentId,
      title: 'Payment Successful',
      message: `Your payment of KES ${transaction.amount} has been processed successfully.`,
      type: 'success'
    });

    // Trigger reconciliation
    await this.reconciliationService.reconcilePayment(transaction.id);

    logger.info(`Payment completed successfully for transaction: ${transaction.id}`);
  }

  private async handleFailedPayment(callback: any) {
    const { CheckoutRequestID, ResultDesc } = callback;
    
    const transaction = await this.transactionService.findByProviderTransactionId(CheckoutRequestID);
    
    if (!transaction) {
      logger.error(`Transaction not found for failed M-Pesa callback: ${CheckoutRequestID}`);
      return;
    }

    // Update transaction status
    await this.transactionService.update(transaction.id, {
      status: 'failed',
      failedAt: new Date(),
      failureReason: ResultDesc,
      callbackData: callback
    });

    // Send failure notification
    await this.notificationService.send({
      userId: transaction.studentId,
      title: 'Payment Failed',
      message: `Your payment of KES ${transaction.amount} failed. Reason: ${ResultDesc}`,
      type: 'error'
    });

    logger.info(`Payment failed for transaction: ${transaction.id}, Reason: ${ResultDesc}`);
  }

  private verifyMpesaSignature(payload: any, signature: string): boolean {
    // Implement M-Pesa signature verification
    const expectedSignature = crypto
      .createHmac('sha256', process.env.MPESA_WEBHOOK_SECRET!)
      .update(JSON.stringify(payload))
      .digest('hex');
    
    return signature === expectedSignature;
  }
}
```

## ⚠️ **Error Handling & Reconciliation**

### **Error Handling Strategy**

```typescript
// utils/payment-error-handler.ts
export class PaymentErrorHandler {
  static async handlePaymentError(error: Error, transactionId: string) {
    const errorType = this.classifyError(error);
    
    switch (errorType) {
      case 'NETWORK_ERROR':
        await this.handleNetworkError(error, transactionId);
        break;
      case 'AUTHENTICATION_ERROR':
        await this.handleAuthenticationError(error, transactionId);
        break;
      case 'VALIDATION_ERROR':
        await this.handleValidationError(error, transactionId);
        break;
      case 'PROVIDER_ERROR':
        await this.handleProviderError(error, transactionId);
        break;
      default:
        await this.handleUnknownError(error, transactionId);
    }
  }

  private static async handleNetworkError(error: Error, transactionId: string) {
    // Retry logic for network errors
    const transaction = await this.transactionService.findById(transactionId);
    
    if (transaction.retryCount < 3) {
      await this.scheduleRetry(transactionId, 30000); // Retry in 30 seconds
    } else {
      await this.markTransactionAsFailed(transactionId, 'Network timeout after 3 retries');
    }
  }

  private static async scheduleRetry(transactionId: string, delay: number) {
    setTimeout(async () => {
      try {
        await this.paymentService.retryPayment(transactionId);
      } catch (error) {
        await this.handlePaymentError(error, transactionId);
      }
    }, delay);
  }
}
```

### **Reconciliation Process**

```typescript
// services/reconciliation-service.ts
export class ReconciliationService {
  async reconcilePayment(transactionId: string): Promise<ReconciliationResult> {
    try {
      const transaction = await this.transactionService.findById(transactionId);
      
      // Get provider transaction details
      const providerDetails = await this.getProviderTransactionDetails(transaction);
      
      // Compare amounts
      const amountMatch = this.compareAmounts(transaction.amount, providerDetails.amount);
      
      // Check status consistency
      const statusMatch = this.compareStatuses(transaction.status, providerDetails.status);
      
      const reconciliationResult: ReconciliationResult = {
        transactionId,
        providerTransactionId: providerDetails.id,
        amountMatch,
        statusMatch,
        reconciledAt: new Date(),
        notes: this.generateReconciliationNotes(amountMatch, statusMatch)
      };

      // Save reconciliation record
      await this.saveReconciliationRecord(reconciliationResult);

      // Handle discrepancies
      if (!amountMatch || !statusMatch) {
        await this.handleReconciliationDiscrepancy(transaction, providerDetails);
      }

      return reconciliationResult;
    } catch (error) {
      logger.error('Reconciliation failed:', error);
      throw new ReconciliationError('Failed to reconcile payment', error);
    }
  }

  private async getProviderTransactionDetails(transaction: PaymentTransaction) {
    switch (transaction.paymentMethod) {
      case 'mpesa':
        return await this.mpesaService.getTransactionDetails(transaction.providerTransactionId);
      case 'jambopay':
        return await this.jambopayService.getTransactionDetails(transaction.providerTransactionId);
      default:
        throw new Error(`Unsupported payment method: ${transaction.paymentMethod}`);
    }
  }
}
```

## 🧪 **Testing Strategy**

### **Sandbox Testing**

```typescript
// tests/payment-integration.test.ts
describe('Payment Integration Tests', () => {
  let paymentService: PaymentService;
  let testTransaction: PaymentTransaction;

  beforeEach(async () => {
    // Setup test environment
    await setupTestDatabase();
    paymentService = new PaymentService(testConfig);
  });

  describe('M-Pesa Integration', () => {
    it('should initiate STK push successfully', async () => {
      const paymentRequest: PaymentRequest = {
        amount: 1000,
        phoneNumber: '************',
        referenceNumber: 'TEST001',
        description: 'Test payment'
      };

      const response = await paymentService.initiatePayment(paymentRequest);
      
      expect(response.success).toBe(true);
      expect(response.transactionId).toBeDefined();
      expect(response.message).toContain('initiated successfully');
    });

    it('should handle webhook callback correctly', async () => {
      const mockCallback = {
        Body: {
          stkCallback: {
            ResultCode: 0,
            CheckoutRequestID: 'test-checkout-id',
            MerchantRequestID: 'test-merchant-id',
            ResultDesc: 'Success'
          }
        }
      };

      await webhookController.handleMpesaCallback(
        { body: mockCallback, headers: {} } as Request,
        {} as Response
      );

      const transaction = await transactionService.findByProviderTransactionId('test-checkout-id');
      expect(transaction.status).toBe('completed');
    });
  });

  describe('Error Handling', () => {
    it('should handle network timeout gracefully', async () => {
      // Mock network timeout
      jest.spyOn(axios, 'post').mockRejectedValue(new Error('Network timeout'));

      const paymentRequest: PaymentRequest = {
        amount: 1000,
        phoneNumber: '************',
        referenceNumber: 'TEST002',
        description: 'Test payment'
      };

      await expect(paymentService.initiatePayment(paymentRequest))
        .rejects.toThrow('Payment initiation failed');
    });
  });
});
```

### **Load Testing**

```typescript
// tests/load-testing.ts
import k6 from 'k6';
import http from 'k6/http';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200 users
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.1'],     // Error rate under 10%
  },
};

export default function() {
  const paymentData = {
    studentId: 'test-student-id',
    feeId: 'test-fee-id',
    phoneNumber: '************',
    amount: 1000
  };

  const response = http.post('http://localhost:3001/api/payments/mpesa/initiate', 
    JSON.stringify(paymentData), 
    {
      headers: { 'Content-Type': 'application/json' },
    }
  );

  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 2s': (r) => r.timings.duration < 2000,
  });
}
```

## 🔒 **Security Guidelines**

### **Data Protection**

```typescript
// middleware/payment-security.ts
export class PaymentSecurityMiddleware {
  static validatePaymentRequest(req: Request, res: Response, next: NextFunction) {
    const { phoneNumber, amount, studentId } = req.body;
    
    // Validate phone number format
    if (!this.isValidPhoneNumber(phoneNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phone number format'
      });
    }
    
    // Validate amount
    if (amount <= 0 || amount > 150000) { // M-Pesa limit
      return res.status(400).json({
        success: false,
        message: 'Invalid payment amount'
      });
    }
    
    // Rate limiting per student
    if (this.isRateLimited(studentId)) {
      return res.status(429).json({
        success: false,
        message: 'Too many payment attempts. Please try again later.'
      });
    }
    
    next();
  }

  static sanitizePaymentData(data: any): any {
    return {
      phoneNumber: data.phoneNumber?.replace(/[^0-9]/g, ''),
      amount: parseFloat(data.amount),
      referenceNumber: data.referenceNumber?.replace(/[^A-Za-z0-9]/g, ''),
      description: data.description?.substring(0, 100)
    };
  }
}
```

### **PCI Compliance**

```typescript
// utils/pci-compliance.ts
export class PCICompliance {
  static maskSensitiveData(data: any): any {
    const masked = { ...data };
    
    if (masked.phoneNumber) {
      masked.phoneNumber = this.maskPhoneNumber(masked.phoneNumber);
    }
    
    if (masked.accountNumber) {
      masked.accountNumber = this.maskAccountNumber(masked.accountNumber);
    }
    
    return masked;
  }

  static maskPhoneNumber(phoneNumber: string): string {
    return phoneNumber.replace(/(\d{3})\d{4}(\d{3})/, '$1****$2');
  }

  static maskAccountNumber(accountNumber: string): string {
    return accountNumber.replace(/(\d{4})\d+(\d{4})/, '$1****$2');
  }

  static auditLog(transactionId: string, action: string, data: any) {
    const auditEntry = {
      transactionId,
      action,
      timestamp: new Date().toISOString(),
      data: this.maskSensitiveData(data),
      userId: data.userId,
      ipAddress: data.ipAddress
    };
    
    // Log to secure audit system
    this.logToAuditSystem(auditEntry);
  }
}
```

## 📊 **Logging & Monitoring**

### **Structured Logging**

```typescript
// utils/payment-logger.ts
export class PaymentLogger {
  static logPaymentInitiation(transactionId: string, details: any) {
    logger.info('Payment initiated', {
      transactionId,
      paymentMethod: details.paymentMethod,
      amount: details.amount,
      studentId: details.studentId,
      timestamp: new Date().toISOString(),
      level: 'INFO',
      category: 'PAYMENT_INITIATION'
    });
  }

  static logPaymentCompletion(transactionId: string, details: any) {
    logger.info('Payment completed', {
      transactionId,
      status: details.status,
      providerTransactionId: details.providerTransactionId,
      processingTime: details.processingTime,
      timestamp: new Date().toISOString(),
      level: 'INFO',
      category: 'PAYMENT_COMPLETION'
    });
  }

  static logPaymentFailure(transactionId: string, error: Error) {
    logger.error('Payment failed', {
      transactionId,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      level: 'ERROR',
      category: 'PAYMENT_FAILURE'
    });
  }
}
```

### **Monitoring Dashboard**

```typescript
// services/monitoring-service.ts
export class MonitoringService {
  async getPaymentMetrics(timeRange: string) {
    const metrics = await this.calculateMetrics(timeRange);
    
    return {
      totalTransactions: metrics.totalTransactions,
      successfulPayments: metrics.successfulPayments,
      failedPayments: metrics.failedPayments,
      successRate: metrics.successRate,
      averageProcessingTime: metrics.averageProcessingTime,
      totalAmount: metrics.totalAmount,
      paymentMethodBreakdown: metrics.paymentMethodBreakdown,
      hourlyTrends: metrics.hourlyTrends
    };
  }

  async sendAlerts() {
    const metrics = await this.getPaymentMetrics('1h');
    
    // Alert on high failure rate
    if (metrics.successRate < 0.95) {
      await this.sendAlert('HIGH_FAILURE_RATE', {
        successRate: metrics.successRate,
        failedPayments: metrics.failedPayments
      });
    }
    
    // Alert on processing time issues
    if (metrics.averageProcessingTime > 30000) { // 30 seconds
      await this.sendAlert('HIGH_PROCESSING_TIME', {
        averageProcessingTime: metrics.averageProcessingTime
      });
    }
  }
}
```

## 🚀 **Production Deployment**

### **Environment Configuration**

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  university-portal-api:
    build: .
    environment:
      - NODE_ENV=production
      - MPESA_ENVIRONMENT=production
      - MPESA_CONSUMER_KEY=${MPESA_CONSUMER_KEY}
      - MPESA_CONSUMER_SECRET=${MPESA_CONSUMER_SECRET}
      - MPESA_BUSINESS_SHORT_CODE=${MPESA_BUSINESS_SHORT_CODE}
      - MPESA_PASSKEY=${MPESA_PASSKEY}
      - MPESA_CALLBACK_URL=${MPESA_CALLBACK_URL}
      - JAMBOPAY_API_KEY=${JAMBOPAY_API_KEY}
      - JAMBOPAY_MERCHANT_ID=${JAMBOPAY_MERCHANT_ID}
      - IM_BANK_CLIENT_ID=${IM_BANK_CLIENT_ID}
      - IM_BANK_CLIENT_SECRET=${IM_BANK_CLIENT_SECRET}
      - KCB_BUNI_API_KEY=${KCB_BUNI_API_KEY}
      - PESALINK_API_KEY=${PESALINK_API_KEY}
    ports:
      - "3001:3001"
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
```

### **Health Checks**

```typescript
// routes/health.ts
router.get('/health/payments', async (req, res) => {
  try {
    const healthChecks = await Promise.allSettled([
      mpesaService.healthCheck(),
      jambopayService.healthCheck(),
      imBankService.healthCheck(),
      kcbBuniService.healthCheck(),
      pesalinkService.healthCheck()
    ]);

    const results = healthChecks.map((result, index) => ({
      provider: ['mpesa', 'jambopay', 'im_bank', 'kcb_buni', 'pesalink'][index],
      status: result.status === 'fulfilled' ? 'healthy' : 'unhealthy',
      details: result.status === 'fulfilled' ? result.value : result.reason
    }));

    const overallStatus = results.every(r => r.status === 'healthy') ? 'healthy' : 'degraded';

    res.json({
      status: overallStatus,
      timestamp: new Date().toISOString(),
      providers: results
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});
```

This comprehensive implementation provides a production-ready payment integration system for Kenyan payment methods, with proper error handling, security measures, and monitoring capabilities.
