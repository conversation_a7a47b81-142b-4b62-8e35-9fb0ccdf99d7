version: '3.8'

services:
  # Frontend Development Server
  university-portal-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:3001
      - NEXT_PUBLIC_WS_URL=ws://localhost:3001
    networks:
      - university-portal
    restart: unless-stopped
    depends_on:
      - university-portal-api

  # Backend API (reference)
  university-portal-api:
    image: university-portal-api:latest
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/university_portal
      - REDIS_URL=redis://redis:6379
    networks:
      - university-portal
    restart: unless-stopped

  # MongoDB (reference)
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb-data:/data/db
    networks:
      - university-portal

  # Redis (reference)
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - university-portal

volumes:
  mongodb-data:
  redis-data:

networks:
  university-portal:
    driver: bridge
