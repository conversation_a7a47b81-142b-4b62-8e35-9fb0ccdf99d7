// Real-time service for Socket.io integration
import { io, Socket } from 'socket.io-client';
import { useAuthStore } from '@/lib/auth-store';

export interface RealtimeEvent {
  type: string;
  data: any;
  timestamp: string;
  id: string;
}

export interface NotificationEvent {
  notification: {
    id: string;
    userId: string;
    title: string;
    type: string;
  };
  sentBy: string;
}

export interface GradeEvent {
  grade: {
    id: string;
    studentId: string;
    studentName: string;
    courseId: string;
    courseCode: string;
    score: number;
    maxScore: number;
    grade: string;
    type: string;
  };
  gradedBy: string;
}

export interface AssignmentEvent {
  assignment: {
    id: string;
    title: string;
    courseId: string;
    courseCode: string;
    dueDate: string;
    type: string;
  };
  createdBy: string;
}

export interface AttendanceEvent {
  attendance: {
    id: string;
    studentId: string;
    sessionId: string;
    courseId: string;
    status: string;
    date: string;
  };
  markedBy: string;
}

class RealtimeService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;

  constructor() {
    this.connect();
  }

  private connect(): void {
    const user = useAuthStore.getState().user;
    
    if (!user) {
      console.log('No user found, skipping socket connection');
      return;
    }

    const token = this.getToken();
    if (!token) {
      console.log('No token found, skipping socket connection');
      return;
    }

    const serverURL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001';
    
    // Determine namespace based on user roles
    const namespace = this.getNamespaceForRoles(user.roles);
    
    this.socket = io(`${serverURL}${namespace}`, {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectInterval,
    });

    this.setupEventListeners();
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('accessToken');
    }
    return null;
  }

  private getNamespaceForRoles(roles: string[]): string {
    // Return the first matching namespace based on role priority
    const rolePriority = ['admin', 'finance', 'registrar', 'lecturer', 'student'];
    
    for (const role of rolePriority) {
      if (roles.includes(role)) {
        return `/${role}`;
      }
    }
    
    return '/default';
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Socket connected:', this.socket?.id);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Subscribe to relevant events based on user role
      this.subscribeToEvents();
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.isConnected = false;
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.handleReconnection();
    });

    this.socket.on('realtime_update', (event: RealtimeEvent) => {
      console.log('Real-time update received:', event);
      this.handleRealtimeUpdate(event);
    });

    this.socket.on('notification', (event: NotificationEvent) => {
      console.log('Notification received:', event);
      this.handleNotification(event);
    });

    this.socket.on('grade_update', (event: GradeEvent) => {
      console.log('Grade update received:', event);
      this.handleGradeUpdate(event);
    });

    this.socket.on('assignment_submitted', (event: AssignmentEvent) => {
      console.log('Assignment submitted:', event);
      this.handleAssignmentSubmitted(event);
    });

    this.socket.on('attendance_update', (event: AttendanceEvent) => {
      console.log('Attendance update received:', event);
      this.handleAttendanceUpdate(event);
    });

    this.socket.on('system_announcement', (event: any) => {
      console.log('System announcement received:', event);
      this.handleSystemAnnouncement(event);
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }

  private subscribeToEvents(): void {
    if (!this.socket) return;

    const user = useAuthStore.getState().user;
    if (!user) return;

    // Subscribe to role-based events
    const events = this.getEventsForRole(user.roles);
    
    this.socket.emit('subscribe', { events });
    
    // Join user-specific rooms
    this.socket.emit('join_course', { courseId: 'all' }); // Join all courses for now
    
    if (user.roles.includes('student')) {
      this.socket.emit('request_grade_updates');
    }
    
    if (user.roles.includes('lecturer')) {
      this.socket.emit('join_my_courses');
    }
    
    if (user.roles.includes('admin')) {
      this.socket.emit('join_system_monitoring');
    }
  }

  private getEventsForRole(roles: string[]): string[] {
    const roleEventPermissions = {
      student: [
        'course_created',
        'course_updated',
        'grade_created',
        'grade_updated',
        'assignment_created',
        'assignment_updated',
        'assignment_graded',
        'attendance_marked',
        'session_created',
        'session_started',
        'notification_created',
        'system_alert'
      ],
      lecturer: [
        'user_created',
        'course_created',
        'course_updated',
        'course_enrolled',
        'course_dropped',
        'assignment_submitted',
        'attendance_marked',
        'session_created',
        'session_updated',
        'notification_created',
        'system_alert'
      ],
      admin: [
        // Admins can receive all events
        'user_created',
        'user_updated',
        'user_deleted',
        'course_created',
        'course_updated',
        'course_deleted',
        'grade_created',
        'grade_updated',
        'grade_deleted',
        'assignment_created',
        'assignment_updated',
        'assignment_deleted',
        'attendance_marked',
        'attendance_updated',
        'notification_created',
        'system_alert',
        'system_maintenance'
      ],
      finance: [
        'user_created',
        'course_created',
        'course_updated',
        'notification_created',
        'system_alert'
      ],
      registrar: [
        'user_created',
        'user_updated',
        'course_created',
        'course_updated',
        'course_enrolled',
        'course_dropped',
        'notification_created',
        'system_alert'
      ]
    };

    const events: string[] = [];
    
    for (const role of roles) {
      if (roleEventPermissions[role as keyof typeof roleEventPermissions]) {
        events.push(...roleEventPermissions[role as keyof typeof roleEventPermissions]);
      }
    }

    return [...new Set(events)]; // Remove duplicates
  }

  private handleReconnection(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectInterval);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  private handleRealtimeUpdate(event: RealtimeEvent): void {
    // Emit custom events that components can listen to
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('realtime-update', { detail: event }));
    }
  }

  private handleNotification(event: NotificationEvent): void {
    // Emit notification event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('notification-received', { detail: event }));
    }
  }

  private handleGradeUpdate(event: GradeEvent): void {
    // Emit grade update event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('grade-updated', { detail: event }));
    }
  }

  private handleAssignmentSubmitted(event: AssignmentEvent): void {
    // Emit assignment submitted event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('assignment-submitted', { detail: event }));
    }
  }

  private handleAttendanceUpdate(event: AttendanceEvent): void {
    // Emit attendance update event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('attendance-updated', { detail: event }));
    }
  }

  private handleSystemAnnouncement(event: any): void {
    // Emit system announcement event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('system-announcement', { detail: event }));
    }
  }

  // Public methods
  public joinCourse(courseId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_course', { courseId });
    }
  }

  public leaveCourse(courseId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave_course', { courseId });
    }
  }

  public startAttendance(sessionId: string, courseId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('start_attendance', { sessionId, courseId });
    }
  }

  public endAttendance(sessionId: string, courseId: string): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('end_attendance', { sessionId, courseId });
    }
  }

  public broadcastAnnouncement(message: string, targetRoles?: string[], targetDepartments?: string[]): void {
    if (this.socket && this.isConnected) {
      this.socket.emit('broadcast_announcement', {
        message,
        targetRoles,
        targetDepartments
      });
    }
  }

  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  public isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  public reconnect(): void {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.connect();
  }
}

// Create singleton instance
export const realtimeService = new RealtimeService();

// Export types
export type { RealtimeEvent, NotificationEvent, GradeEvent, AssignmentEvent, AttendanceEvent };
