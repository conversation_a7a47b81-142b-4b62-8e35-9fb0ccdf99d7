// React hooks for API integration
import { useState, useEffect, useCallback } from 'react';
import { apiClient, User, Course, Grade, Assignment, Attendance, Notification } from './api-client';
import { useAuthStore } from './auth-store';

// Generic hook for API calls
export function useApiCall<T>(
  apiCall: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      setData(result);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    execute();
  }, [execute]);

  return { data, loading, error, refetch: execute };
}

// Hook for paginated API calls
export function usePaginatedApiCall<T>(
  apiCall: (params?: any) => Promise<{ data: T[]; pagination: any }>,
  initialParams: any = {}
) {
  const [data, setData] = useState<T[]>([]);
  const [pagination, setPagination] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [params, setParams] = useState(initialParams);

  const execute = useCallback(async (newParams?: any) => {
    try {
      setLoading(true);
      setError(null);
      const queryParams = newParams || params;
      const result = await apiCall(queryParams);
      setData(result.data);
      setPagination(result.pagination);
      if (newParams) setParams(queryParams);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [apiCall, params]);

  useEffect(() => {
    execute();
  }, [execute]);

  const updateParams = useCallback((newParams: any) => {
    setParams(prev => ({ ...prev, ...newParams }));
  }, []);

  return { 
    data, 
    pagination, 
    loading, 
    error, 
    refetch: execute, 
    updateParams,
    params 
  };
}

// Auth hooks
export function useAuth() {
  const { user, isAuthenticated, isLoading } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const login = useCallback(async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiClient.login(email, password);
      useAuthStore.getState().setUser(result.user);
      return result;
    } catch (err: any) {
      setError(err.message || 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      await apiClient.logout();
    } catch (err) {
      console.error('Logout error:', err);
    } finally {
      useAuthStore.getState().logout();
    }
  }, []);

  const updateProfile = useCallback(async (userData: Partial<User>) => {
    try {
      setLoading(true);
      setError(null);
      const updatedUser = await apiClient.updateProfile(userData);
      useAuthStore.getState().setUser(updatedUser);
      return updatedUser;
    } catch (err: any) {
      setError(err.message || 'Profile update failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    updateProfile,
    loading,
    error
  };
}

// Course hooks
export function useCourses(params?: any) {
  return usePaginatedApiCall(apiClient.getCourses.bind(apiClient), params);
}

export function useCourse(id: string) {
  return useApiCall(() => apiClient.getCourse(id), [id]);
}

export function useCourseEnrollment(courseId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const enroll = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      await apiClient.enrollInCourse(courseId);
    } catch (err: any) {
      setError(err.message || 'Enrollment failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  const drop = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      await apiClient.dropCourse(courseId);
    } catch (err: any) {
      setError(err.message || 'Drop failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  return { enroll, drop, loading, error };
}

// Grade hooks
export function useGrades(params?: any) {
  return usePaginatedApiCall(apiClient.getGrades.bind(apiClient), params);
}

export function useStudentGrades(studentId: string) {
  return useApiCall(() => apiClient.getStudentGrades(studentId), [studentId]);
}

// Assignment hooks
export function useAssignments(params?: any) {
  return usePaginatedApiCall(apiClient.getAssignments.bind(apiClient), params);
}

export function useAssignment(id: string) {
  return useApiCall(() => apiClient.getAssignment(id), [id]);
}

export function useAssignmentSubmission(assignmentId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const submit = useCallback(async (files: File[], comments?: string) => {
    try {
      setLoading(true);
      setError(null);
      await apiClient.submitAssignment(assignmentId, files, comments);
    } catch (err: any) {
      setError(err.message || 'Submission failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [assignmentId]);

  return { submit, loading, error };
}

// Attendance hooks
export function useAttendance(params?: any) {
  return usePaginatedApiCall(apiClient.getAttendance.bind(apiClient), params);
}

export function useStudentAttendance(studentId: string) {
  return useApiCall(() => apiClient.getStudentAttendance(studentId), [studentId]);
}

// Notification hooks
export function useNotifications(params?: any) {
  return usePaginatedApiCall(apiClient.getNotifications.bind(apiClient), params);
}

export function useUnreadCount() {
  return useApiCall(apiClient.getUnreadCount.bind(apiClient));
}

export function useNotificationActions() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      setLoading(true);
      setError(null);
      await apiClient.markNotificationAsRead(notificationId);
    } catch (err: any) {
      setError(err.message || 'Failed to mark as read');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      await apiClient.markAllNotificationsAsRead();
    } catch (err: any) {
      setError(err.message || 'Failed to mark all as read');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { markAsRead, markAllAsRead, loading, error };
}

// Admin hooks
export function useDashboardStats() {
  return useApiCall(apiClient.getDashboardStats.bind(apiClient));
}

export function useSystemHealth() {
  return useApiCall(apiClient.getSystemHealth.bind(apiClient));
}

// Real-time hooks
export function useRealtimeEvents() {
  const [events, setEvents] = useState<any[]>([]);

  useEffect(() => {
    const handleRealtimeUpdate = (event: CustomEvent) => {
      setEvents(prev => [...prev.slice(-9), event.detail]); // Keep last 10 events
    };

    const handleNotification = (event: CustomEvent) => {
      setEvents(prev => [...prev.slice(-9), { type: 'notification', ...event.detail }]);
    };

    const handleGradeUpdate = (event: CustomEvent) => {
      setEvents(prev => [...prev.slice(-9), { type: 'grade', ...event.detail }]);
    };

    const handleAssignmentSubmitted = (event: CustomEvent) => {
      setEvents(prev => [...prev.slice(-9), { type: 'assignment', ...event.detail }]);
    };

    const handleAttendanceUpdate = (event: CustomEvent) => {
      setEvents(prev => [...prev.slice(-9), { type: 'attendance', ...event.detail }]);
    };

    const handleSystemAnnouncement = (event: CustomEvent) => {
      setEvents(prev => [...prev.slice(-9), { type: 'announcement', ...event.detail }]);
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('realtime-update', handleRealtimeUpdate);
      window.addEventListener('notification-received', handleNotification);
      window.addEventListener('grade-updated', handleGradeUpdate);
      window.addEventListener('assignment-submitted', handleAssignmentSubmitted);
      window.addEventListener('attendance-updated', handleAttendanceUpdate);
      window.addEventListener('system-announcement', handleSystemAnnouncement);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('realtime-update', handleRealtimeUpdate);
        window.removeEventListener('notification-received', handleNotification);
        window.removeEventListener('grade-updated', handleGradeUpdate);
        window.removeEventListener('assignment-submitted', handleAssignmentSubmitted);
        window.removeEventListener('attendance-updated', handleAttendanceUpdate);
        window.removeEventListener('system-announcement', handleSystemAnnouncement);
      }
    };
  }, []);

  return events;
}

// Custom hook for real-time connection status
export function useRealtimeStatus() {
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const checkConnection = () => {
      // This would check the realtime service connection status
      // For now, we'll simulate it
      setIsConnected(true);
    };

    checkConnection();
    const interval = setInterval(checkConnection, 5000);

    return () => clearInterval(interval);
  }, []);

  return isConnected;
}
