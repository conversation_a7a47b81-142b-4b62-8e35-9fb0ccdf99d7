"use client"

export interface Notification {
  id: string
  title: string
  message: string
  type: "info" | "success" | "warning" | "error"
  timestamp: Date
  read: boolean
  link?: string
}

export class NotificationService {
  private static instance: NotificationService
  private notifications: Notification[] = []
  private listeners: Set<(notifications: Notification[]) => void> = new Set()

  private constructor() {
    // Load notifications from localStorage
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem("notifications")
      if (stored) {
        this.notifications = JSON.parse(stored).map((n: any) => ({
          ...n,
          timestamp: new Date(n.timestamp),
        }))
      }
    }
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  async requestPermission(): Promise<boolean> {
    if (!("Notification" in window)) {
      return false
    }

    if (Notification.permission === "granted") {
      return true
    }

    if (Notification.permission !== "denied") {
      const permission = await Notification.requestPermission()
      return permission === "granted"
    }

    return false
  }

  addNotification(notification: Omit<Notification, "id" | "timestamp" | "read">): void {
    const newNotification: Notification = {
      ...notification,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      read: false,
    }

    this.notifications.unshift(newNotification)
    this.save()
    this.notify()

    // Show browser notification if permission granted
    if (Notification.permission === "granted") {
      new Notification(notification.title, {
        body: notification.message,
        icon: "/icon-192.jpg",
        badge: "/icon-192.jpg",
      })
    }
  }

  markAsRead(id: string): void {
    const notification = this.notifications.find((n) => n.id === id)
    if (notification) {
      notification.read = true
      this.save()
      this.notify()
    }
  }

  markAllAsRead(): void {
    this.notifications.forEach((n) => (n.read = true))
    this.save()
    this.notify()
  }

  deleteNotification(id: string): void {
    this.notifications = this.notifications.filter((n) => n.id !== id)
    this.save()
    this.notify()
  }

  clearAll(): void {
    this.notifications = []
    this.save()
    this.notify()
  }

  getNotifications(): Notification[] {
    return this.notifications
  }

  getUnreadCount(): number {
    return this.notifications.filter((n) => !n.read).length
  }

  subscribe(listener: (notifications: Notification[]) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  private save(): void {
    if (typeof window !== "undefined") {
      localStorage.setItem("notifications", JSON.stringify(this.notifications))
    }
  }

  private notify(): void {
    this.listeners.forEach((listener) => listener(this.notifications))
  }
}

export const notificationService = NotificationService.getInstance()
