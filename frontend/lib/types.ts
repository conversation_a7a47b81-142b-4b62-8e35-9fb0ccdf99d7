export type UserRole = "student" | "lecturer" | "admin"

export interface User {
  id: string
  email: string
  name: string
  roles: UserRole[]
  avatar?: string
  studentId?: string
  staffId?: string
  department?: string
  level?: number
  programme?: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface Course {
  id: string
  code: string
  title: string
  credits: number
  lecturer: string
  schedule: string
  enrolled?: number
  capacity?: number
  prerequisites?: string[]
}

export interface Grade {
  courseCode: string
  courseTitle: string
  grade: string
  score: number
  credits: number
  semester: string
}

export interface Assignment {
  id: string
  title: string
  courseCode: string
  dueDate: string
  status: "pending" | "submitted" | "graded"
  score?: number
  maxScore: number
}

export interface Notification {
  id: string
  title: string
  message: string
  type: "info" | "success" | "warning" | "error"
  read: boolean
  timestamp: string
}
