"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuthStore } from "./auth-store"
import type { UserRole } from "./types"

export function withRole(allowedRoles: UserRole[]) {
  return <P extends object>(Component: React.ComponentType<P>) =>
    function ProtectedComponent(props: P) {
      const router = useRouter()
      const { user, isAuthenticated, isLoading } = useAuthStore()

      useEffect(() => {
        if (!isLoading) {
          if (!isAuthenticated) {
            router.push("/login")
          } else if (user && !user.roles.some((role) => allowedRoles.includes(role))) {
            router.push("/403")
          }
        }
      }, [isAuthenticated, isLoading, user, router])

      if (isLoading) {
        return (
          <div className="flex h-screen items-center justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
          </div>
        )
      }

      if (!isAuthenticated || !user || !user.roles.some((role) => allowedRoles.includes(role))) {
        return null
      }

      return <Component {...props} />
    }
}
