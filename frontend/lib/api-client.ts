// API client for connecting frontend to backend
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { useAuthStore } from '@/lib/auth-store';

// API Response types
interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  code?: string;
  errors?: any[];
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationData;
}

// User types matching backend
export interface User {
  id: string;
  email: string;
  name: string;
  roles: ('student' | 'lecturer' | 'admin' | 'finance' | 'registrar')[];
  avatar?: string;
  studentId?: string;
  staffId?: string;
  department?: string;
  level?: number;
  programme?: string;
  isActive?: boolean;
  lastLogin?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Course {
  id: string;
  code: string;
  title: string;
  description?: string;
  credits: number;
  lecturer: {
    id: string;
    name: string;
    email: string;
    staffId?: string;
  };
  department: string;
  capacity: number;
  enrolled: number;
  prerequisites?: Course[];
  semester: string;
  academicYear: string;
  schedule: Array<{
    day: string;
    startTime: string;
    endTime: string;
    room: string;
    type: string;
  }>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Grade {
  id: string;
  student: {
    id: string;
    name: string;
    email: string;
    studentId?: string;
  };
  course: {
    id: string;
    code: string;
    title: string;
  };
  assignment?: {
    id: string;
    title: string;
  };
  score: number;
  maxScore: number;
  percentage: number;
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  type: 'assignment' | 'quiz' | 'exam' | 'project' | 'participation';
  semester: string;
  academicYear: string;
  gradedBy: {
    id: string;
    name: string;
    email: string;
  };
  gradedAt: string;
  comments?: string;
  isFinal: boolean;
}

export interface Assignment {
  id: string;
  title: string;
  description?: string;
  course: {
    id: string;
    code: string;
    title: string;
  };
  lecturer: {
    id: string;
    name: string;
    email: string;
  };
  dueDate: string;
  maxScore: number;
  type: 'assignment' | 'quiz' | 'exam' | 'project' | 'participation';
  instructions?: string;
  attachments: Array<{
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    url: string;
  }>;
  isPublished: boolean;
  submissions: Array<{
    student: {
      id: string;
      name: string;
      email: string;
      studentId?: string;
    };
    submittedAt: string;
    files: Array<{
      filename: string;
      originalName: string;
      mimetype: string;
      size: number;
      url: string;
    }>;
    comments?: string;
    grade?: number;
    feedback?: string;
    gradedAt?: string;
    gradedBy?: {
      id: string;
      name: string;
      email: string;
    };
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface Attendance {
  id: string;
  student: {
    id: string;
    name: string;
    email: string;
    studentId?: string;
  };
  session: {
    id: string;
    course: {
      id: string;
      code: string;
      title: string;
    };
    date: string;
    startTime: string;
    endTime: string;
    room?: string;
    type: 'lecture' | 'lab' | 'tutorial' | 'seminar';
    topic?: string;
  };
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  markedBy: {
    id: string;
    name: string;
    email: string;
  };
  markedAt: string;
  notes?: string;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  link?: string;
  read: boolean;
  readAt?: string;
  metadata?: {
    sentBy?: string;
    sentAt: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    category?: 'system' | 'academic' | 'financial' | 'administrative' | 'social';
  };
  createdAt: string;
}

// API Client class
class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle errors
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          this.logout();
        }
        return Promise.reject(error);
      }
    );
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('accessToken');
    }
    return null;
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('accessToken', token);
    }
  }

  private logout(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      useAuthStore.getState().logout();
    }
  }

  // Auth methods
  async login(email: string, password: string): Promise<{ user: User; tokens: { accessToken: string; refreshToken: string } }> {
    const response = await this.client.post<ApiResponse<{ user: User; tokens: { accessToken: string; refreshToken: string } }>>('/api/auth/login', {
      email,
      password,
    });

    if (response.data.success && response.data.data) {
      this.setToken(response.data.data.tokens.accessToken);
      if (typeof window !== 'undefined') {
        localStorage.setItem('refreshToken', response.data.data.tokens.refreshToken);
      }
      return response.data.data;
    }

    throw new Error(response.data.message || 'Login failed');
  }

  async register(userData: {
    email: string;
    password: string;
    name: string;
    roles: string[];
    studentId?: string;
    staffId?: string;
    department?: string;
    level?: number;
    programme?: string;
  }): Promise<{ user: User; tokens: { accessToken: string; refreshToken: string } }> {
    const response = await this.client.post<ApiResponse<{ user: User; tokens: { accessToken: string; refreshToken: string } }>>('/api/auth/register', userData);

    if (response.data.success && response.data.data) {
      this.setToken(response.data.data.tokens.accessToken);
      if (typeof window !== 'undefined') {
        localStorage.setItem('refreshToken', response.data.data.tokens.refreshToken);
      }
      return response.data.data;
    }

    throw new Error(response.data.message || 'Registration failed');
  }

  async refreshToken(): Promise<{ accessToken: string; refreshToken: string }> {
    const refreshToken = typeof window !== 'undefined' ? localStorage.getItem('refreshToken') : null;
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.client.post<ApiResponse<{ accessToken: string; refreshToken: string }>>('/api/auth/refresh', {
      refreshToken,
    });

    if (response.data.success && response.data.data) {
      this.setToken(response.data.data.accessToken);
      if (typeof window !== 'undefined') {
        localStorage.setItem('refreshToken', response.data.data.refreshToken);
      }
      return response.data.data;
    }

    throw new Error(response.data.message || 'Token refresh failed');
  }

  async logout(): Promise<void> {
    try {
      await this.client.post('/api/auth/logout');
    } finally {
      this.logout();
    }
  }

  // User methods
  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<ApiResponse<{ user: User }>>('/api/users/profile');
    
    if (response.data.success && response.data.data) {
      return response.data.data.user;
    }

    throw new Error(response.data.message || 'Failed to get user profile');
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await this.client.put<ApiResponse<{ user: User }>>('/api/users/profile', userData);
    
    if (response.data.success && response.data.data) {
      return response.data.data.user;
    }

    throw new Error(response.data.message || 'Failed to update profile');
  }

  // Course methods
  async getCourses(params?: {
    page?: number;
    limit?: number;
    department?: string;
    lecturer?: string;
    semester?: string;
    search?: string;
  }): Promise<PaginatedResponse<Course>> {
    const response = await this.client.get<ApiResponse<PaginatedResponse<Course>>>('/api/courses', { params });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch courses');
  }

  async getCourse(id: string): Promise<Course> {
    const response = await this.client.get<ApiResponse<{ course: Course }>>(`/api/courses/${id}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data.course;
    }

    throw new Error(response.data.message || 'Failed to fetch course');
  }

  async enrollInCourse(courseId: string): Promise<void> {
    const response = await this.client.post<ApiResponse>(`/api/courses/${courseId}/enroll`);
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to enroll in course');
    }
  }

  async dropCourse(courseId: string): Promise<void> {
    const response = await this.client.delete<ApiResponse>(`/api/courses/${courseId}/enroll`);
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to drop course');
    }
  }

  // Grade methods
  async getGrades(params?: {
    page?: number;
    limit?: number;
    student?: string;
    course?: string;
    semester?: string;
    type?: string;
  }): Promise<PaginatedResponse<Grade>> {
    const response = await this.client.get<ApiResponse<PaginatedResponse<Grade>>>('/api/grades', { params });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch grades');
  }

  async getStudentGrades(studentId: string): Promise<Grade[]> {
    const response = await this.client.get<ApiResponse<{ grades: Grade[] }>>(`/api/grades/student/${studentId}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data.grades;
    }

    throw new Error(response.data.message || 'Failed to fetch student grades');
  }

  // Assignment methods
  async getAssignments(params?: {
    page?: number;
    limit?: number;
    course?: string;
    lecturer?: string;
    type?: string;
    status?: string;
  }): Promise<PaginatedResponse<Assignment>> {
    const response = await this.client.get<ApiResponse<PaginatedResponse<Assignment>>>('/api/assignments', { params });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch assignments');
  }

  async getAssignment(id: string): Promise<Assignment> {
    const response = await this.client.get<ApiResponse<{ assignment: Assignment }>>(`/api/assignments/${id}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data.assignment;
    }

    throw new Error(response.data.message || 'Failed to fetch assignment');
  }

  async submitAssignment(assignmentId: string, files: File[], comments?: string): Promise<void> {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));
    if (comments) formData.append('comments', comments);

    const response = await this.client.post<ApiResponse>(`/api/assignments/${assignmentId}/submit`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to submit assignment');
    }
  }

  // Attendance methods
  async getAttendance(params?: {
    page?: number;
    limit?: number;
    student?: string;
    course?: string;
    session?: string;
    date?: string;
    status?: string;
  }): Promise<PaginatedResponse<Attendance>> {
    const response = await this.client.get<ApiResponse<PaginatedResponse<Attendance>>>('/api/attendance', { params });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch attendance');
  }

  async getStudentAttendance(studentId: string): Promise<Attendance[]> {
    const response = await this.client.get<ApiResponse<{ attendance: Attendance[] }>>(`/api/attendance/student/${studentId}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data.attendance;
    }

    throw new Error(response.data.message || 'Failed to fetch student attendance');
  }

  // Notification methods
  async getNotifications(params?: {
    page?: number;
    limit?: number;
    type?: string;
    read?: boolean;
  }): Promise<PaginatedResponse<Notification>> {
    const response = await this.client.get<ApiResponse<PaginatedResponse<Notification>>>('/api/notifications', { params });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch notifications');
  }

  async getUnreadCount(): Promise<number> {
    const response = await this.client.get<ApiResponse<{ unreadCount: number }>>('/api/notifications/unread');
    
    if (response.data.success && response.data.data) {
      return response.data.data.unreadCount;
    }

    throw new Error(response.data.message || 'Failed to fetch unread count');
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    const response = await this.client.put<ApiResponse>(`/api/notifications/${notificationId}/read`);
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to mark notification as read');
    }
  }

  async markAllNotificationsAsRead(): Promise<void> {
    const response = await this.client.put<ApiResponse>('/api/notifications/read-all');
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to mark all notifications as read');
    }
  }

  // Admin methods
  async getDashboardStats(): Promise<any> {
    const response = await this.client.get<ApiResponse<{ stats: any }>>('/api/admin/dashboard');
    
    if (response.data.success && response.data.data) {
      return response.data.data.stats;
    }

    throw new Error(response.data.message || 'Failed to fetch dashboard stats');
  }

  async getSystemHealth(): Promise<any> {
    const response = await this.client.get<ApiResponse<{ health: any }>>('/api/admin/system-health');
    
    if (response.data.success && response.data.data) {
      return response.data.data.health;
    }

    throw new Error(response.data.message || 'Failed to fetch system health');
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export types
export type { ApiResponse, PaginatedResponse, PaginationData };
