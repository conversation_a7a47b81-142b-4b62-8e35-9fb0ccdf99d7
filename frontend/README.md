# University Portal Frontend

This is the frontend application for the University Portal system, built with Next.js 15, TypeScript, and Tailwind CSS. It provides role-based access for admins, lecturers, and students with a modern, responsive interface.

## 🚀 Features

- **Role-based dashboards** for students, lecturers, and administrators
- **Real-time notifications** and updates
- **Progressive Web App** (PWA) support with offline capabilities
- **Dark/Light theme** toggle with system preference detection
- **Responsive design** with mobile-first approach
- **Modern UI components** built with shadcn/ui
- **Form validation** with React Hook Form and Zod
- **State management** with Zustand

## 🛠️ Tech Stack

- **Next.js 15** with App Router
- **React 19** with TypeScript
- **Tailwind CSS 4.1.9** for styling
- **shadcn/ui** component library
- **Zustand** for state management
- **React Hook Form** with Zod validation
- **PWA** capabilities with service workers

## 📋 Prerequisites

- Node.js 20+
- pnpm (recommended) or npm
- Backend API running (see backend README)

## 🚀 Getting Started

1. **Install dependencies**
```bash
pnpm install
```

2. **Set up environment variables**
```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

3. **Start the development server**
```bash
pnpm run dev
```

4. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
frontend/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin dashboard pages
│   ├── lecturer/          # Lecturer dashboard pages
│   ├── student/           # Student dashboard pages
│   ├── login/             # Authentication pages
│   └── layout.tsx         # Root layout
├── components/            # Reusable React components
│   ├── ui/               # shadcn/ui components
│   ├── dashboard-layout.tsx
│   ├── notifications-panel.tsx
│   └── theme-provider.tsx
├── lib/                  # Utilities and stores
│   ├── auth-store.ts     # Authentication state
│   ├── notifications.ts  # Notification service
│   └── with-role.tsx     # Role-based HOC
├── hooks/                # Custom React hooks
├── public/               # Static assets
└── styles/              # Global styles
```

## 🔐 Authentication

The frontend integrates with the backend API for authentication. Supported roles:

- **Student**: Access to courses, grades, assignments, timetable
- **Lecturer**: Course management, attendance, grading, schedule  
- **Admin**: User management, system administration, reports

### Development Login Credentials
- Student: `<EMAIL>` / `password123`
- Lecturer: `<EMAIL>` / `password123`
- Admin: `<EMAIL>` / `password123`

## 📱 Available Scripts

- `pnpm run dev` - Start development server
- `pnpm run build` - Build for production
- `pnpm run start` - Start production server
- `pnpm run lint` - Run ESLint
- `pnpm run test` - Run tests
- `pnpm run test:watch` - Run tests in watch mode

## 🎨 UI Components

The application uses shadcn/ui components with custom styling:

- **Dashboard Layout**: Collapsible sidebar with navigation
- **Theme Toggle**: Dark/light mode switcher
- **Notifications Panel**: Real-time notification system
- **PWA Installer**: Progressive Web App installation prompt
- **Form Components**: Validated forms with error handling

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file:

```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
```

### Theme Configuration

The app supports system, light, and dark themes. Theme preference is stored in localStorage and synced across tabs.

## 📱 Progressive Web App

The application is a PWA with:

- **Service Worker**: Offline functionality
- **Web App Manifest**: Installable on mobile devices
- **Offline Support**: Cached resources for offline use
- **Push Notifications**: Real-time updates (when supported)

## 🧪 Testing

```bash
# Run tests
pnpm run test

# Run tests in watch mode
pnpm run test:watch

# Run tests with coverage
pnpm run test:coverage
```

## 🚀 Deployment

### Production Build
```bash
pnpm run build
pnpm run start
```

### Docker Deployment
```bash
# Build Docker image
docker build -t university-portal-frontend .

# Run container
docker run -p 3000:3000 university-portal-frontend
```

## 🔗 Integration

The frontend integrates with the backend API:

- **Authentication**: JWT token-based auth
- **Real-time**: WebSocket connections for live updates
- **File Upload**: Support for document and image uploads
- **Notifications**: Real-time notification system

## 📚 Documentation

- **Component Library**: shadcn/ui documentation
- **Next.js**: App Router documentation
- **Tailwind CSS**: Utility-first CSS framework
- **Zustand**: State management library

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new components
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

Built with ❤️ by the University Portal Team
