"use client"

import { useState } from "react"
import { withR<PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import {
  BookOpen,
  Building2,
  DollarSign,
  Download,
  Home,
  Search,
  Settings,
  TrendingUp,
  Users,
  CreditCard,
  TrendingDown,
  PieChart,
  BarChart3,
  Calendar,
  FileText
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const navItems = [
  { title: "Dashboard", href: "/admin", icon: Home },
  { title: "Users", href: "/admin/users", icon: Users },
  { title: "Courses", href: "/admin/courses", icon: BookO<PERSON> },
  { title: "Departments", href: "/admin/departments", icon: Building2 },
  { title: "Finance", href: "/admin/finance", icon: DollarSign },
  { title: "Reports", href: "/admin/reports", icon: TrendingUp },
  { title: "Settings", href: "/admin/settings", icon: Settings },
]

function FinancePage() {
  const [timeRange, setTimeRange] = useState("monthly")

  const financialOverview = {
    totalRevenue: 12500000,
    totalExpenses: 9800000,
    netProfit: 2700000,
    budgetUtilization: 78.4,
    studentFees: 8500000,
    governmentFunding: 3000000,
    donations: 1000000,
  }

  const monthlyData = [
    { month: "Jan", revenue: 1200000, expenses: 950000, profit: 250000 },
    { month: "Feb", revenue: 1150000, expenses: 920000, profit: 230000 },
    { month: "Mar", revenue: 1300000, expenses: 980000, profit: 320000 },
    { month: "Apr", revenue: 1250000, expenses: 960000, profit: 290000 },
    { month: "May", revenue: 1400000, expenses: 1020000, profit: 380000 },
    { month: "Jun", revenue: 1350000, expenses: 980000, profit: 370000 },
  ]

  const expenseCategories = [
    { category: "Faculty Salaries", amount: 4500000, percentage: 45.9, trend: "up" },
    { category: "Infrastructure", amount: 1800000, percentage: 18.4, trend: "down" },
    { category: "Research & Development", amount: 1200000, percentage: 12.2, trend: "up" },
    { category: "Student Services", amount: 800000, percentage: 8.2, trend: "stable" },
    { category: "Administration", amount: 600000, percentage: 6.1, trend: "stable" },
    { category: "Utilities", amount: 400000, percentage: 4.1, trend: "up" },
    { category: "Other", amount: 500000, percentage: 5.1, trend: "down" },
  ]

  const revenueSources = [
    { source: "Student Tuition", amount: 8500000, percentage: 68.0, trend: "up" },
    { source: "Government Funding", amount: 3000000, percentage: 24.0, trend: "stable" },
    { source: "Donations & Grants", amount: 1000000, percentage: 8.0, trend: "up" },
  ]

  const recentTransactions = [
    { id: "TXN001", type: "revenue", description: "Student Tuition - Semester 1", amount: 2500000, date: "2025-01-15", status: "completed" },
    { id: "TXN002", type: "expense", description: "Faculty Salary Payment", amount: -450000, date: "2025-01-10", status: "completed" },
    { id: "TXN003", type: "revenue", description: "Research Grant - NSF", amount: 500000, date: "2025-01-08", status: "completed" },
    { id: "TXN004", type: "expense", description: "Infrastructure Maintenance", amount: -120000, date: "2025-01-05", status: "completed" },
    { id: "TXN005", type: "revenue", description: "Alumni Donation", amount: 250000, date: "2025-01-03", status: "completed" },
  ]

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-success" />
      case "down":
        return <TrendingDown className="h-4 w-4 text-destructive" />
      default:
        return <div className="h-4 w-4 rounded-full bg-muted" />
    }
  }

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Financial Overview</h1>
            <p className="text-muted-foreground">Monitor revenue, expenses, and financial performance</p>
          </div>
          <div className="flex gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Time Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Financial Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success">${(financialOverview.totalRevenue / 1000000).toFixed(1)}M</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success">+12.5%</span> from last year
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">${(financialOverview.totalExpenses / 1000000).toFixed(1)}M</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-destructive">+8.2%</span> from last year
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success">${(financialOverview.netProfit / 1000000).toFixed(1)}M</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success">+18.7%</span> profit margin
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{financialOverview.budgetUtilization}%</div>
              <p className="text-xs text-muted-foreground">Current fiscal year</p>
            </CardContent>
          </Card>
        </div>

        {/* Revenue vs Expenses Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue vs Expenses</CardTitle>
            <CardDescription>Monthly financial performance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {monthlyData.map((data) => (
                <div key={data.month} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">{data.month}</span>
                    <div className="flex gap-4">
                      <span className="text-success">Revenue: ${(data.revenue / 1000000).toFixed(1)}M</span>
                      <span className="text-destructive">Expenses: ${(data.expenses / 1000000).toFixed(1)}M</span>
                      <span className="text-primary">Profit: ${(data.profit / 1000000).toFixed(1)}M</span>
                    </div>
                  </div>
                  <div className="flex h-2 w-full rounded-full bg-muted">
                    <div 
                      className="h-2 rounded-l-full bg-success" 
                      style={{ width: `${(data.revenue / 1500000) * 100}%` }}
                    />
                    <div 
                      className="h-2 rounded-r-full bg-destructive" 
                      style={{ width: `${(data.expenses / 1500000) * 100}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Revenue Sources */}
          <Card>
            <CardHeader>
              <CardTitle>Revenue Sources</CardTitle>
              <CardDescription>Breakdown of income streams</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {revenueSources.map((source) => (
                <div key={source.source} className="flex items-center justify-between rounded-lg border border-border p-3">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-success/10">
                      <DollarSign className="h-5 w-5 text-success" />
                    </div>
                    <div>
                      <p className="font-medium">{source.source}</p>
                      <p className="text-sm text-muted-foreground">{source.percentage}% of total</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <p className="font-semibold">${(source.amount / 1000000).toFixed(1)}M</p>
                    {getTrendIcon(source.trend)}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Expense Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Expense Categories</CardTitle>
              <CardDescription>Where money is being spent</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {expenseCategories.map((expense) => (
                <div key={expense.category} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">{expense.category}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">${(expense.amount / 1000000).toFixed(1)}M</span>
                      {getTrendIcon(expense.trend)}
                    </div>
                  </div>
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div 
                      className="h-2 rounded-full bg-destructive" 
                      style={{ width: `${expense.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Recent Transactions */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>Latest financial activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border text-left text-sm text-muted-foreground">
                    <th className="pb-3 font-medium">Transaction ID</th>
                    <th className="pb-3 font-medium">Description</th>
                    <th className="pb-3 font-medium">Amount</th>
                    <th className="pb-3 font-medium">Date</th>
                    <th className="pb-3 font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {recentTransactions.map((transaction) => (
                    <tr key={transaction.id} className="border-b border-border last:border-0">
                      <td className="py-3 font-mono text-sm">{transaction.id}</td>
                      <td className="py-3 font-medium">{transaction.description}</td>
                      <td className={`py-3 font-semibold ${transaction.amount > 0 ? 'text-success' : 'text-destructive'}`}>
                        {transaction.amount > 0 ? '+' : ''}${Math.abs(transaction.amount).toLocaleString()}
                      </td>
                      <td className="py-3 text-sm text-muted-foreground">{transaction.date}</td>
                      <td className="py-3">
                        <Badge variant="default" className="bg-success text-success-foreground">
                          {transaction.status}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Financial Health Indicators */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Cash Flow</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success">$2.7M</div>
              <p className="text-xs text-muted-foreground">Positive cash flow</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Debt Ratio</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-warning">15.2%</div>
              <p className="text-xs text-muted-foreground">Manageable debt level</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">ROI</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success">12.8%</div>
              <p className="text-xs text-muted-foreground">Return on investment</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["admin"])(FinancePage)
