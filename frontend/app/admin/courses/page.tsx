"use client"

import { withR<PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  BookOpen,
  Building2,
  DollarSign,
  Edit,
  Home,
  MoreVertical,
  Plus,
  Search,
  Settings,
  Trash2,
  TrendingUp,
  Users,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const navItems = [
  { title: "Dashboard", href: "/admin", icon: Home },
  { title: "Users", href: "/admin/users", icon: Users },
  { title: "Courses", href: "/admin/courses", icon: BookO<PERSON> },
  { title: "Departments", href: "/admin/departments", icon: Building2 },
  { title: "Finance", href: "/admin/finance", icon: DollarSign },
  { title: "Reports", href: "/admin/reports", icon: TrendingUp },
  { title: "Settings", href: "/admin/settings", icon: Settings },
]

function CoursesPage() {
  const courses = [
    {
      code: "CS301",
      title: "Data Structures and Algorithms",
      department: "Computer Science",
      lecturer: "Dr. Sarah Johnson",
      credits: 3,
      enrolled: 45,
      capacity: 50,
      status: "active",
    },
    {
      code: "CS302",
      title: "Algorithm Design and Analysis",
      department: "Computer Science",
      lecturer: "Prof. Michael Chen",
      credits: 3,
      enrolled: 38,
      capacity: 45,
      status: "active",
    },
    {
      code: "CS401",
      title: "Machine Learning",
      department: "Computer Science",
      lecturer: "Dr. Sarah Johnson",
      credits: 3,
      enrolled: 28,
      capacity: 40,
      status: "active",
    },
    {
      code: "ENG201",
      title: "Thermodynamics",
      department: "Engineering",
      lecturer: "Prof. Robert Taylor",
      credits: 4,
      enrolled: 52,
      capacity: 60,
      status: "active",
    },
    {
      code: "BUS301",
      title: "Marketing Management",
      department: "Business",
      lecturer: "Dr. Lisa Anderson",
      credits: 3,
      enrolled: 65,
      capacity: 70,
      status: "active",
    },
  ]

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Course Management</h1>
            <p className="text-muted-foreground">Manage courses and assignments</p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Course
          </Button>
        </div>

        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input placeholder="Search courses..." className="pl-10" />
        </div>

        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">342</div>
              <p className="text-xs text-muted-foreground">Across all departments</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Active Courses</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">298</div>
              <p className="text-xs text-muted-foreground">This semester</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Avg Enrollment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">42</div>
              <p className="text-xs text-muted-foreground">Students per course</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Capacity Used</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">84%</div>
              <p className="text-xs text-muted-foreground">Overall utilization</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Courses</CardTitle>
            <CardDescription>Complete course catalog</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border text-left text-sm text-muted-foreground">
                    <th className="pb-3 font-medium">Code</th>
                    <th className="pb-3 font-medium">Title</th>
                    <th className="pb-3 font-medium">Department</th>
                    <th className="pb-3 font-medium">Lecturer</th>
                    <th className="pb-3 font-medium">Credits</th>
                    <th className="pb-3 font-medium">Enrollment</th>
                    <th className="pb-3 font-medium">Status</th>
                    <th className="pb-3 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {courses.map((course) => (
                    <tr key={course.code} className="border-b border-border last:border-0">
                      <td className="py-3 font-mono text-sm">{course.code}</td>
                      <td className="py-3 font-medium">{course.title}</td>
                      <td className="py-3 text-sm">{course.department}</td>
                      <td className="py-3 text-sm text-muted-foreground">{course.lecturer}</td>
                      <td className="py-3">{course.credits}</td>
                      <td className="py-3">
                        <span className="text-sm">
                          {course.enrolled}/{course.capacity}
                        </span>
                        <span className="ml-2 text-xs text-muted-foreground">
                          ({Math.round((course.enrolled / course.capacity) * 100)}%)
                        </span>
                      </td>
                      <td className="py-3">
                        <Badge variant="default" className="bg-success text-success-foreground">
                          {course.status}
                        </Badge>
                      </td>
                      <td className="py-3">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Course
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Users className="mr-2 h-4 w-4" />
                              View Students
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Course
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["admin"])(CoursesPage)
