"use client"

import { useState } from "react"
import { with<PERSON><PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import {
  BookOpen,
  Building2,
  DollarSign,
  Download,
  Home,
  Search,
  Settings,
  TrendingUp,
  Users,
  BarChart3,
  PieChart,
  FileText,
  Calendar,
  Filter,
  Eye,
  RefreshCw
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const navItems = [
  { title: "Dashboard", href: "/admin", icon: Home },
  { title: "Users", href: "/admin/users", icon: Users },
  { title: "Courses", href: "/admin/courses", icon: BookO<PERSON> },
  { title: "Departments", href: "/admin/departments", icon: Building2 },
  { title: "Finance", href: "/admin/finance", icon: DollarSign },
  { title: "Reports", href: "/admin/reports", icon: TrendingUp },
  { title: "Settings", href: "/admin/settings", icon: Settings },
]

function ReportsPage() {
  const [selectedReport, setSelectedReport] = useState("enrollment")
  const [timeRange, setTimeRange] = useState("yearly")

  const enrollmentData = [
    { department: "Computer Science", current: 856, previous: 742, growth: 15.4 },
    { department: "Engineering", current: 742, previous: 698, growth: 6.3 },
    { department: "Business", current: 623, previous: 589, growth: 5.8 },
    { department: "Medicine", current: 426, previous: 412, growth: 3.4 },
    { department: "Arts & Humanities", current: 312, previous: 298, growth: 4.7 },
  ]

  const academicPerformance = [
    { course: "CS301 - Data Structures", avgGrade: 78.5, passRate: 92.3, students: 45 },
    { course: "CS302 - Algorithms", avgGrade: 82.1, passRate: 89.7, students: 38 },
    { course: "CS303 - Database Systems", avgGrade: 75.8, passRate: 87.2, students: 42 },
    { course: "ENG201 - Mechanics", avgGrade: 79.2, passRate: 91.5, students: 52 },
    { course: "BUS101 - Management", avgGrade: 81.4, passRate: 94.1, students: 28 },
  ]

  const facultyMetrics = [
    { lecturer: "Dr. Sarah Johnson", courses: 3, students: 125, avgRating: 4.8, research: 12 },
    { lecturer: "Prof. Michael Chen", courses: 2, students: 89, avgRating: 4.7, research: 8 },
    { lecturer: "Dr. Emily Rodriguez", courses: 4, students: 156, avgRating: 4.6, research: 15 },
    { lecturer: "Prof. James Wilson", courses: 2, students: 78, avgRating: 4.9, research: 18 },
    { lecturer: "Dr. Lisa Anderson", courses: 3, students: 98, avgRating: 4.5, research: 6 },
  ]

  const systemMetrics = {
    totalUsers: 3159,
    activeUsers: 2847,
    systemUptime: 99.8,
    avgResponseTime: 45,
    storageUsed: 67,
    bandwidthUsage: 23,
  }

  const reportTemplates = [
    { id: "enrollment", name: "Enrollment Report", description: "Student enrollment trends and statistics", icon: Users },
    { id: "academic", name: "Academic Performance", description: "Course performance and grade analysis", icon: BookOpen },
    { id: "faculty", name: "Faculty Performance", description: "Teaching effectiveness and research metrics", icon: TrendingUp },
    { id: "financial", name: "Financial Report", description: "Revenue, expenses, and budget analysis", icon: DollarSign },
    { id: "system", name: "System Analytics", description: "Platform usage and performance metrics", icon: BarChart3 },
  ]

  const getGrowthColor = (growth: number) => {
    if (growth > 10) return "text-success"
    if (growth > 5) return "text-warning"
    return "text-destructive"
  }

  const getPerformanceColor = (grade: number) => {
    if (grade >= 80) return "text-success"
    if (grade >= 70) return "text-warning"
    return "text-destructive"
  }

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics & Reports</h1>
            <p className="text-muted-foreground">Comprehensive insights and data analysis</p>
          </div>
          <div className="flex gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Time Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Data
            </Button>
            <Button>
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Report Templates */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {reportTemplates.map((template) => {
            const Icon = template.icon
            return (
              <Card 
                key={template.id} 
                className={`cursor-pointer transition-all hover:shadow-lg ${
                  selectedReport === template.id ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setSelectedReport(template.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                      <Icon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-base">{template.name}</CardTitle>
                      <CardDescription className="text-xs">{template.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <Button variant="ghost" size="sm">
                      <Eye className="mr-1 h-3 w-3" />
                      View
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Download className="mr-1 h-3 w-3" />
                      Export
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* System Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{systemMetrics.totalUsers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success">+{systemMetrics.activeUsers}</span> active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-success">{systemMetrics.systemUptime}%</div>
              <p className="text-xs text-muted-foreground">Last 30 days</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{systemMetrics.avgResponseTime}ms</div>
              <p className="text-xs text-muted-foreground">API performance</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{systemMetrics.storageUsed}%</div>
              <p className="text-xs text-muted-foreground">Of total capacity</p>
            </CardContent>
          </Card>
        </div>

        {/* Enrollment Report */}
        {selectedReport === "enrollment" && (
          <Card>
            <CardHeader>
              <CardTitle>Enrollment Trends</CardTitle>
              <CardDescription>Student enrollment by department</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {enrollmentData.map((dept) => (
                  <div key={dept.department} className="flex items-center justify-between rounded-lg border border-border p-4">
                    <div className="flex items-center gap-4">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                        <Users className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{dept.department}</h3>
                        <p className="text-sm text-muted-foreground">
                          Previous: {dept.previous} → Current: {dept.current}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold">{dept.current}</p>
                      <p className={`text-sm font-medium ${getGrowthColor(dept.growth)}`}>
                        +{dept.growth}% growth
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Academic Performance Report */}
        {selectedReport === "academic" && (
          <Card>
            <CardHeader>
              <CardTitle>Academic Performance</CardTitle>
              <CardDescription>Course performance metrics and grade analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border text-left text-sm text-muted-foreground">
                      <th className="pb-3 font-medium">Course</th>
                      <th className="pb-3 font-medium">Students</th>
                      <th className="pb-3 font-medium">Avg Grade</th>
                      <th className="pb-3 font-medium">Pass Rate</th>
                      <th className="pb-3 font-medium">Performance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {academicPerformance.map((course) => (
                      <tr key={course.course} className="border-b border-border last:border-0">
                        <td className="py-3 font-medium">{course.course}</td>
                        <td className="py-3">{course.students}</td>
                        <td className={`py-3 font-semibold ${getPerformanceColor(course.avgGrade)}`}>
                          {course.avgGrade}%
                        </td>
                        <td className="py-3">{course.passRate}%</td>
                        <td className="py-3">
                          <div className="h-2 w-20 rounded-full bg-muted">
                            <div 
                              className="h-2 rounded-full bg-primary" 
                              style={{ width: `${course.avgGrade}%` }}
                            />
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Faculty Performance Report */}
        {selectedReport === "faculty" && (
          <Card>
            <CardHeader>
              <CardTitle>Faculty Performance</CardTitle>
              <CardDescription>Teaching effectiveness and research metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {facultyMetrics.map((faculty) => (
                  <div key={faculty.lecturer} className="flex items-center justify-between rounded-lg border border-border p-4">
                    <div className="flex items-center gap-4">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                        <TrendingUp className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{faculty.lecturer}</h3>
                        <p className="text-sm text-muted-foreground">
                          {faculty.courses} courses • {faculty.students} students
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-6">
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">Rating</p>
                        <p className="text-xl font-bold">{faculty.avgRating}/5.0</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">Research</p>
                        <p className="text-xl font-bold">{faculty.research}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* System Analytics */}
        {selectedReport === "system" && (
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Bandwidth Usage</CardTitle>
                <CardDescription>Network traffic and usage patterns</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Current Usage</span>
                    <span className="text-muted-foreground">{systemMetrics.bandwidthUsage}%</span>
                  </div>
                  <Progress value={systemMetrics.bandwidthUsage} className="h-2" />
                </div>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="rounded-lg border border-border p-3">
                    <p className="text-sm text-muted-foreground">Peak Hours</p>
                    <p className="text-lg font-bold">9-11 AM</p>
                  </div>
                  <div className="rounded-lg border border-border p-3">
                    <p className="text-sm text-muted-foreground">Data Transfer</p>
                    <p className="text-lg font-bold">2.3 TB</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Storage Analytics</CardTitle>
                <CardDescription>Disk usage and file distribution</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Storage Used</span>
                    <span className="text-muted-foreground">{systemMetrics.storageUsed}%</span>
                  </div>
                  <Progress value={systemMetrics.storageUsed} className="h-2" />
                </div>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="rounded-lg border border-border p-3">
                    <p className="text-sm text-muted-foreground">Documents</p>
                    <p className="text-lg font-bold">45%</p>
                  </div>
                  <div className="rounded-lg border border-border p-3">
                    <p className="text-sm text-muted-foreground">Media</p>
                    <p className="text-lg font-bold">32%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}

export default withRole(["admin"])(ReportsPage)
