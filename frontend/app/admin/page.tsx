"use client"

import { withRole } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  BookOpen,
  Building2,
  DollarSign,
  GraduationCap,
  Home,
  Settings,
  TrendingUp,
  UserCog,
  Users,
} from "lucide-react"

const navItems = [
  { title: "Dashboard", href: "/admin", icon: Home },
  { title: "Users", href: "/admin/users", icon: Users },
  { title: "Courses", href: "/admin/courses", icon: BookOpen },
  { title: "Departments", href: "/admin/departments", icon: Building2 },
  { title: "Finance", href: "/admin/finance", icon: DollarSign },
  { title: "Reports", href: "/admin/reports", icon: TrendingUp },
  { title: "Setting<PERSON>", href: "/admin/settings", icon: Setting<PERSON> },
]

function AdminDashboard() {
  const recentActivities = [
    { action: "New student registered", user: "<PERSON>", time: "5 minutes ago", type: "user" },
    { action: "Course CS401 updated", user: "Dr. <PERSON>", time: "15 minutes ago", type: "course" },
    { action: "Fee payment received", user: "Alice Johnson", time: "1 hour ago", type: "finance" },
    { action: "New lecturer added", user: "Prof. Michael Chen", time: "2 hours ago", type: "user" },
  ]

  const systemStats = [
    { label: "Server Status", value: "Operational", status: "success" },
    { label: "Database", value: "98% Healthy", status: "success" },
    { label: "API Response", value: "45ms avg", status: "success" },
    { label: "Storage Used", value: "67%", status: "warning" },
  ]

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">System overview and management</p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2,847</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success">+12%</span> from last semester
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Lecturers</CardTitle>
              <UserCog className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">156</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success">+8</span> new this semester
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">342</div>
              <p className="text-xs text-muted-foreground">Across 12 departments</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue (This Month)</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$1.2M</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success">+18%</span> from last month
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activities</CardTitle>
              <CardDescription>Latest system events and actions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentActivities.map((activity, i) => (
                <div key={i} className="flex items-center justify-between rounded-lg border border-border p-3">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                      {activity.type === "user" && <Users className="h-5 w-5 text-primary" />}
                      {activity.type === "course" && <BookOpen className="h-5 w-5 text-primary" />}
                      {activity.type === "finance" && <DollarSign className="h-5 w-5 text-primary" />}
                    </div>
                    <div>
                      <p className="font-medium">{activity.action}</p>
                      <p className="text-sm text-muted-foreground">{activity.user}</p>
                    </div>
                  </div>
                  <span className="text-xs text-muted-foreground">{activity.time}</span>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* System Health */}
          <Card>
            <CardHeader>
              <CardTitle>System Health</CardTitle>
              <CardDescription>Monitor system performance</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {systemStats.map((stat, i) => (
                <div key={i} className="flex items-center justify-between rounded-lg border border-border p-3">
                  <span className="font-medium">{stat.label}</span>
                  <Badge
                    variant={stat.status === "success" ? "default" : "secondary"}
                    className={
                      stat.status === "success"
                        ? "bg-success text-success-foreground"
                        : "bg-warning text-warning-foreground"
                    }
                  >
                    {stat.value}
                  </Badge>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Department Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Department Overview</CardTitle>
            <CardDescription>Student enrollment by department</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {[
                { name: "Computer Science", students: 856, growth: 15 },
                { name: "Engineering", students: 742, growth: 8 },
                { name: "Business", students: 623, growth: 12 },
                { name: "Medicine", students: 426, growth: 5 },
              ].map((dept) => (
                <div key={dept.name} className="rounded-lg border border-border p-4">
                  <h3 className="font-semibold">{dept.name}</h3>
                  <p className="text-2xl font-bold">{dept.students}</p>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-success">+{dept.growth}%</span> this year
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["admin"])(AdminDashboard)
