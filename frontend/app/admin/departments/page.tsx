"use client"

import { useState } from "react"
import { withR<PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  BookOpen,
  Building2,
  DollarSign,
  Edit,
  Home,
  MoreVertical,
  Plus,
  Search,
  Settings,
  TrendingUp,
  Trash2,
  UserCog,
  Users,
  GraduationCap,
  Users2
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const navItems = [
  { title: "Dashboard", href: "/admin", icon: Home },
  { title: "Users", href: "/admin/users", icon: Users },
  { title: "Courses", href: "/admin/courses", icon: BookOpen },
  { title: "Departments", href: "/admin/departments", icon: Building2 },
  { title: "Finance", href: "/admin/finance", icon: DollarSign },
  { title: "Reports", href: "/admin/reports", icon: TrendingUp },
  { title: "Settings", href: "/admin/settings", icon: Settings },
]

function DepartmentsPage() {
  const [searchTerm, setSearchTerm] = useState("")

  const departments = [
    {
      id: "DEPT001",
      name: "Computer Science",
      code: "CS",
      head: "Dr. Sarah Johnson",
      email: "<EMAIL>",
      students: 856,
      lecturers: 24,
      courses: 45,
      budget: 2500000,
      status: "active",
      established: "1995-09-01",
    },
    {
      id: "DEPT002",
      name: "Engineering",
      code: "ENG",
      head: "Prof. Michael Chen",
      email: "<EMAIL>",
      students: 742,
      lecturers: 28,
      courses: 52,
      budget: 3200000,
      status: "active",
      established: "1988-09-01",
    },
    {
      id: "DEPT003",
      name: "Business Administration",
      code: "BUS",
      head: "Dr. Emily Rodriguez",
      email: "<EMAIL>",
      students: 623,
      lecturers: 18,
      courses: 38,
      budget: 1800000,
      status: "active",
      established: "1992-09-01",
    },
    {
      id: "DEPT004",
      name: "Medicine",
      code: "MED",
      head: "Prof. James Wilson",
      email: "<EMAIL>",
      students: 426,
      lecturers: 32,
      courses: 28,
      budget: 4500000,
      status: "active",
      established: "1985-09-01",
    },
    {
      id: "DEPT005",
      name: "Arts & Humanities",
      code: "AH",
      head: "Dr. Lisa Anderson",
      email: "<EMAIL>",
      students: 312,
      lecturers: 15,
      courses: 22,
      budget: 1200000,
      status: "active",
      established: "1990-09-01",
    },
  ]

  const totalStats = {
    departments: departments.length,
    students: departments.reduce((sum, dept) => sum + dept.students, 0),
    lecturers: departments.reduce((sum, dept) => sum + dept.lecturers, 0),
    budget: departments.reduce((sum, dept) => sum + dept.budget, 0),
  }

  const filteredDepartments = departments.filter(dept =>
    dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.head.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Department Management</h1>
            <p className="text-muted-foreground">Manage academic departments and faculty organization</p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Department
          </Button>
        </div>

        <div className="flex flex-col gap-4 md:flex-row">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input 
              placeholder="Search departments..." 
              className="pl-10" 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Department Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Departments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{totalStats.departments}</div>
              <p className="text-xs text-muted-foreground">Active departments</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{totalStats.students.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Across all departments</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Faculty Members</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{totalStats.lecturers}</div>
              <p className="text-xs text-muted-foreground">Teaching staff</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">${(totalStats.budget / 1000000).toFixed(1)}M</div>
              <p className="text-xs text-muted-foreground">Annual allocation</p>
            </CardContent>
          </Card>
        </div>

        {/* Department List */}
        <Card>
          <CardHeader>
            <CardTitle>All Departments</CardTitle>
            <CardDescription>Complete list of academic departments</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border text-left text-sm text-muted-foreground">
                    <th className="pb-3 font-medium">Department</th>
                    <th className="pb-3 font-medium">Head</th>
                    <th className="pb-3 font-medium">Students</th>
                    <th className="pb-3 font-medium">Faculty</th>
                    <th className="pb-3 font-medium">Courses</th>
                    <th className="pb-3 font-medium">Budget</th>
                    <th className="pb-3 font-medium">Status</th>
                    <th className="pb-3 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredDepartments.map((dept) => (
                    <tr key={dept.id} className="border-b border-border last:border-0">
                      <td className="py-3">
                        <div>
                          <div className="font-medium">{dept.name}</div>
                          <div className="text-sm text-muted-foreground">{dept.code}</div>
                        </div>
                      </td>
                      <td className="py-3">
                        <div>
                          <div className="font-medium">{dept.head}</div>
                          <div className="text-sm text-muted-foreground">{dept.email}</div>
                        </div>
                      </td>
                      <td className="py-3">
                        <div className="flex items-center gap-1">
                          <Users2 className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{dept.students}</span>
                        </div>
                      </td>
                      <td className="py-3">
                        <div className="flex items-center gap-1">
                          <UserCog className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{dept.lecturers}</span>
                        </div>
                      </td>
                      <td className="py-3">
                        <div className="flex items-center gap-1">
                          <BookOpen className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{dept.courses}</span>
                        </div>
                      </td>
                      <td className="py-3">
                        <div className="font-medium">${(dept.budget / 1000000).toFixed(1)}M</div>
                      </td>
                      <td className="py-3">
                        <Badge variant="default" className="bg-success text-success-foreground">
                          {dept.status}
                        </Badge>
                      </td>
                      <td className="py-3">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Department
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Building2 className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Users className="mr-2 h-4 w-4" />
                              Manage Faculty
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Department
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Department Overview Cards */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Largest Departments */}
          <Card>
            <CardHeader>
              <CardTitle>Largest Departments</CardTitle>
              <CardDescription>By student enrollment</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {departments
                .sort((a, b) => b.students - a.students)
                .slice(0, 5)
                .map((dept, i) => (
                  <div key={dept.id} className="flex items-center justify-between rounded-lg border border-border p-3">
                    <div className="flex items-center gap-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                        <Building2 className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{dept.name}</p>
                        <p className="text-sm text-muted-foreground">{dept.code}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">{dept.students}</p>
                      <p className="text-xs text-muted-foreground">students</p>
                    </div>
                  </div>
                ))}
            </CardContent>
          </Card>

          {/* Budget Allocation */}
          <Card>
            <CardHeader>
              <CardTitle>Budget Allocation</CardTitle>
              <CardDescription>Department budget distribution</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {departments
                .sort((a, b) => b.budget - a.budget)
                .map((dept) => {
                  const percentage = (dept.budget / totalStats.budget) * 100
                  return (
                    <div key={dept.id} className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">{dept.name}</span>
                        <span className="text-muted-foreground">${(dept.budget / 1000000).toFixed(1)}M</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div 
                          className="h-2 rounded-full bg-primary" 
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  )
                })}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["admin"])(DepartmentsPage)
