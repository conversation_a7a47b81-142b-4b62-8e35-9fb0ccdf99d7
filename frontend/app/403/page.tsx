import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ShieldAlert } from "lucide-react"

export default function ForbiddenPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background p-4">
      <ShieldAlert className="h-20 w-20 text-destructive" />
      <h1 className="mt-6 text-4xl font-bold">403 - Access Denied</h1>
      <p className="mt-2 text-center text-muted-foreground">You don't have permission to access this page.</p>
      <Button asChild className="mt-6">
        <Link href="/login">Return to Login</Link>
      </Button>
    </div>
  )
}
