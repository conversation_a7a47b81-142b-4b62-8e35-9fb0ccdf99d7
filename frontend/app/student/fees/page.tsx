"use client"

import { withR<PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { 
  BookOpen, 
  Calendar, 
  CreditCard, 
  Download, 
  FileText, 
  GraduationCap, 
  Home, 
  Search, 
  TrendingUp, 
  Wallet,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react"

const navItems = [
  { title: "Dashboard", href: "/student", icon: Home },
  { title: "Courses", href: "/student/courses", icon: BookOpen },
  { title: "Grades", href: "/student/grades", icon: TrendingUp },
  { title: "Timetable", href: "/student/timetable", icon: Calendar },
  { title: "Assignments", href: "/student/assignments", icon: FileText },
  { title: "Fees", href: "/student/fees", icon: Wallet },
  { title: "Registration", href: "/student/registration", icon: GraduationCap },
]

function FeesPage() {
  const feeBreakdown = [
    { category: "Tuition Fees", amount: 15000, paid: 12000, due: "2025-05-15", status: "partial" },
    { category: "Registration Fees", amount: 500, paid: 500, due: "2025-01-15", status: "paid" },
    { category: "Library Fees", amount: 200, paid: 200, due: "2025-01-15", status: "paid" },
    { category: "Lab Fees", amount: 800, paid: 0, due: "2025-03-15", status: "pending" },
    { category: "Examination Fees", amount: 300, paid: 0, due: "2025-04-15", status: "pending" },
  ]

  const paymentHistory = [
    { id: "PAY001", amount: 5000, date: "2025-01-10", method: "Bank Transfer", status: "completed", description: "Tuition Payment - Semester 1" },
    { id: "PAY002", amount: 500, date: "2025-01-08", method: "Credit Card", status: "completed", description: "Registration Fees" },
    { id: "PAY003", amount: 200, date: "2025-01-08", method: "Credit Card", status: "completed", description: "Library Fees" },
    { id: "PAY004", amount: 2000, date: "2024-12-15", method: "Bank Transfer", status: "completed", description: "Tuition Payment - Advance" },
    { id: "PAY005", amount: 5000, date: "2024-11-20", method: "Bank Transfer", status: "completed", description: "Tuition Payment - Semester 1" },
  ]

  const getStatusBadge = (status: string) => {
    const variants = {
      paid: { variant: "default" as const, className: "bg-success text-success-foreground", icon: CheckCircle },
      partial: { variant: "secondary" as const, className: "bg-warning text-warning-foreground", icon: Clock },
      pending: { variant: "destructive" as const, className: "bg-destructive text-destructive-foreground", icon: AlertCircle },
    }
    return variants[status as keyof typeof variants] || variants.pending
  }

  const totalAmount = feeBreakdown.reduce((sum, fee) => sum + fee.amount, 0)
  const totalPaid = feeBreakdown.reduce((sum, fee) => sum + fee.paid, 0)
  const totalOutstanding = totalAmount - totalPaid
  const paymentProgress = (totalPaid / totalAmount) * 100

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Fee Management</h1>
            <p className="text-muted-foreground">Track your payments and outstanding balances</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Download Statement
            </Button>
            <Button>
              <CreditCard className="mr-2 h-4 w-4" />
              Make Payment
            </Button>
          </div>
        </div>

        {/* Fee Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Fees</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${totalAmount.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Academic Year 2024-25</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Amount Paid</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-success">${totalPaid.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success">{paymentProgress.toFixed(1)}%</span> of total fees
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">${totalOutstanding.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Due in installments</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Next Due Date</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Mar 15</div>
              <p className="text-xs text-muted-foreground">Lab Fees - $800</p>
            </CardContent>
          </Card>
        </div>

        {/* Payment Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Progress</CardTitle>
            <CardDescription>Your fee payment status for the academic year</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">Overall Progress</span>
                <span className="text-muted-foreground">{paymentProgress.toFixed(1)}%</span>
              </div>
              <Progress value={paymentProgress} className="h-3" />
            </div>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="rounded-lg border border-border p-4">
                <p className="text-sm text-muted-foreground">Paid</p>
                <p className="text-2xl font-bold text-success">${totalPaid.toLocaleString()}</p>
              </div>
              <div className="rounded-lg border border-border p-4">
                <p className="text-sm text-muted-foreground">Outstanding</p>
                <p className="text-2xl font-bold text-destructive">${totalOutstanding.toLocaleString()}</p>
              </div>
              <div className="rounded-lg border border-border p-4">
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">${totalAmount.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Fee Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Fee Breakdown</CardTitle>
              <CardDescription>Detailed breakdown of all fees</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {feeBreakdown.map((fee, i) => {
                const statusBadge = getStatusBadge(fee.status)
                const StatusIcon = statusBadge.icon
                return (
                  <div key={i} className="flex items-center justify-between rounded-lg border border-border p-3">
                    <div className="flex items-center gap-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                        <StatusIcon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{fee.category}</p>
                        <p className="text-sm text-muted-foreground">Due: {fee.due}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">${fee.amount.toLocaleString()}</p>
                      <Badge variant={statusBadge.variant} className={statusBadge.className}>
                        {fee.status}
                      </Badge>
                    </div>
                  </div>
                )
              })}
            </CardContent>
          </Card>

          {/* Payment History */}
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>Recent payment transactions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {paymentHistory.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between rounded-lg border border-border p-3">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-success/10">
                      <CheckCircle className="h-5 w-5 text-success" />
                    </div>
                    <div>
                      <p className="font-medium">{payment.description}</p>
                      <p className="text-sm text-muted-foreground">{payment.method} • {payment.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-success">+${payment.amount.toLocaleString()}</p>
                    <Badge variant="default" className="bg-success text-success-foreground">
                      {payment.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Payment Methods */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>Available payment options</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="rounded-lg border border-border p-4 text-center">
                <CreditCard className="mx-auto mb-2 h-8 w-8 text-primary" />
                <h3 className="font-semibold">Credit/Debit Card</h3>
                <p className="text-sm text-muted-foreground">Instant payment processing</p>
                <Button className="mt-2 w-full" size="sm">
                  Pay Now
                </Button>
              </div>
              <div className="rounded-lg border border-border p-4 text-center">
                <Wallet className="mx-auto mb-2 h-8 w-8 text-primary" />
                <h3 className="font-semibold">Bank Transfer</h3>
                <p className="text-sm text-muted-foreground">Direct bank transfer</p>
                <Button variant="outline" className="mt-2 w-full" size="sm">
                  Get Details
                </Button>
              </div>
              <div className="rounded-lg border border-border p-4 text-center">
                <FileText className="mx-auto mb-2 h-8 w-8 text-primary" />
                <h3 className="font-semibold">Installment Plan</h3>
                <p className="text-sm text-muted-foreground">Spread payments over time</p>
                <Button variant="outline" className="mt-2 w-full" size="sm">
                  Apply
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["student"])(FeesPage)
