"use client"

import { withRole } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { BookOpen, Calendar, Clock, Download, FileText, GraduationCap, Home, MapPin, TrendingUp, Wallet } from "lucide-react"

const navItems = [
  { title: "Dashboard", href: "/student", icon: Home },
  { title: "Courses", href: "/student/courses", icon: BookOpen },
  { title: "Grades", href: "/student/grades", icon: TrendingUp },
  { title: "Timetable", href: "/student/timetable", icon: Calendar },
  { title: "Assignments", href: "/student/assignments", icon: FileText },
  { title: "Fees", href: "/student/fees", icon: Wallet },
  { title: "Registration", href: "/student/registration", icon: GraduationCap },
]

function TimetablePage() {
  const weekDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
  const timeSlots = [
    "08:00 - 09:00",
    "09:00 - 10:30",
    "10:30 - 12:00",
    "12:00 - 13:00",
    "13:00 - 14:00",
    "14:00 - 15:30",
    "15:30 - 17:00",
    "17:00 - 18:30",
  ]

  const schedule = {
    Monday: [
      { time: "09:00 - 10:30", course: "CS301", title: "Data Structures", room: "LH-101", type: "Lecture" },
      { time: "14:00 - 15:30", course: "CS303", title: "Database Systems", room: "LAB-3", type: "Lab" },
    ],
    Tuesday: [
      { time: "09:00 - 10:30", course: "CS304", title: "Software Engineering", room: "LH-102", type: "Lecture" },
      { time: "11:00 - 12:30", course: "CS302", title: "Algorithms", room: "LH-205", type: "Lecture" },
    ],
    Wednesday: [
      { time: "09:00 - 10:30", course: "CS301", title: "Data Structures", room: "LH-101", type: "Lecture" },
      { time: "11:00 - 12:30", course: "CS305", title: "Computer Networks", room: "LAB-1", type: "Lab" },
      { time: "16:00 - 17:30", course: "CS306", title: "Operating Systems", room: "LH-103", type: "Lecture" },
    ],
    Thursday: [
      { time: "09:00 - 10:30", course: "CS304", title: "Software Engineering", room: "LH-102", type: "Lecture" },
      { time: "11:00 - 12:30", course: "CS302", title: "Algorithms", room: "LH-205", type: "Lecture" },
    ],
    Friday: [
      { time: "11:00 - 12:30", course: "CS305", title: "Computer Networks", room: "LAB-1", type: "Lab" },
      { time: "14:00 - 15:30", course: "CS303", title: "Database Systems", room: "LAB-3", type: "Lab" },
    ],
  }

  const upcomingClasses = [
    {
      course: "CS301",
      title: "Data Structures",
      time: "Tomorrow, 09:00 AM",
      room: "LH-101",
      lecturer: "Dr. Sarah Johnson",
      type: "Lecture",
    },
    {
      course: "CS302",
      title: "Algorithms",
      time: "Tomorrow, 11:00 AM",
      room: "LH-205",
      lecturer: "Prof. Michael Chen",
      type: "Lecture",
    },
    {
      course: "CS303",
      title: "Database Systems",
      time: "Tomorrow, 02:00 PM",
      room: "LAB-3",
      lecturer: "Dr. Emily Rodriguez",
      type: "Lab",
    },
  ]

  const getTypeColor = (type: string) => {
    return type === "Lecture" ? "bg-primary text-primary-foreground" : "bg-secondary text-secondary-foreground"
  }

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">My Timetable</h1>
            <p className="text-muted-foreground">Spring 2025 - Week 2</p>
          </div>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Export to Calendar
          </Button>
        </div>

        {/* Weekly Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Schedule</CardTitle>
            <CardDescription>Your class schedule for this week</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <div className="min-w-[800px]">
                <div className="grid grid-cols-6 gap-2">
                  {/* Header */}
                  <div className="font-medium text-sm text-muted-foreground p-2">Time</div>
                  {weekDays.map((day) => (
                    <div key={day} className="font-medium text-sm text-center p-2">
                      {day}
                    </div>
                  ))}

                  {/* Time slots */}
                  {timeSlots.map((slot) => (
                    <>
                      <div key={slot} className="text-xs text-muted-foreground p-2 border-t border-border">
                        {slot}
                      </div>
                      {weekDays.map((day) => {
                        const daySchedule = schedule[day as keyof typeof schedule] || []
                        const classInSlot = daySchedule.find((c) => c.time === slot)

                        return (
                          <div key={`${day}-${slot}`} className="border-t border-border p-1">
                            {classInSlot ? (
                              <div className="rounded-lg border border-primary/20 bg-primary/5 p-2 text-xs">
                                <div className="flex items-center justify-between mb-1">
                                  <Badge variant="outline" className="text-[10px] px-1 py-0">
                                    {classInSlot.course}
                                  </Badge>
                                  <Badge className={`text-[10px] px-1 py-0 ${getTypeColor(classInSlot.type)}`}>
                                    {classInSlot.type}
                                  </Badge>
                                </div>
                                <p className="font-medium text-xs mb-1">{classInSlot.title}</p>
                                <div className="flex items-center gap-1 text-muted-foreground">
                                  <MapPin className="h-3 w-3" />
                                  <span className="text-[10px]">{classInSlot.room}</span>
                                </div>
                              </div>
                            ) : (
                              <div className="h-full min-h-[60px]" />
                            )}
                          </div>
                        )
                      })}
                    </>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Classes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Upcoming Classes
            </CardTitle>
            <CardDescription>Next classes in your schedule</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingClasses.map((cls, i) => (
              <div key={i} className="flex items-center justify-between rounded-lg border border-border p-4">
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                    <BookOpen className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline">{cls.course}</Badge>
                      <Badge className={getTypeColor(cls.type)}>{cls.type}</Badge>
                    </div>
                    <p className="font-semibold">{cls.title}</p>
                    <p className="text-sm text-muted-foreground">{cls.lecturer}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-1 text-sm font-medium mb-1">
                    <Clock className="h-4 w-4" />
                    {cls.time}
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    {cls.room}
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Weekly Stats */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">18</div>
              <p className="text-xs text-muted-foreground">This week</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Lectures</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">67% of classes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Lab Sessions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">6</div>
              <p className="text-xs text-muted-foreground">33% of classes</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["student"])(TimetablePage)

