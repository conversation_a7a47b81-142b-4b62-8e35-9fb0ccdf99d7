"use client"

import { withRole } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { BookOpen, Calendar, Download, FileText, GraduationCap, Home, TrendingUp, Wallet } from "lucide-react"
import { Button } from "@/components/ui/button"

const navItems = [
  { title: "Dashboard", href: "/student", icon: Home },
  { title: "Courses", href: "/student/courses", icon: BookOpen },
  { title: "Grades", href: "/student/grades", icon: TrendingUp },
  { title: "Timetable", href: "/student/timetable", icon: Calendar },
  { title: "Assignments", href: "/student/assignments", icon: FileText },
  { title: "Fees", href: "/student/fees", icon: Wallet },
  { title: "Registration", href: "/student/registration", icon: GraduationCap },
]

function GradesPage() {
  const semesters = [
    {
      name: "Fall 2024",
      gpa: 3.75,
      courses: [
        { code: "CS201", title: "Programming II", grade: "A", score: 92, credits: 3 },
        { code: "CS202", title: "Discrete Mathematics", grade: "A-", score: 88, credits: 3 },
        { code: "CS203", title: "Computer Architecture", grade: "B+", score: 85, credits: 3 },
        { code: "CS204", title: "Web Development", grade: "A", score: 94, credits: 3 },
        { code: "GEN201", title: "Technical Writing", grade: "A-", score: 90, credits: 2 },
      ],
    },
    {
      name: "Spring 2024",
      gpa: 3.58,
      courses: [
        { code: "CS101", title: "Programming I", grade: "A-", score: 88, credits: 3 },
        { code: "CS102", title: "Digital Logic", grade: "B+", score: 87, credits: 3 },
        { code: "MATH101", title: "Calculus I", grade: "B", score: 82, credits: 4 },
        { code: "PHY101", title: "Physics I", grade: "B+", score: 85, credits: 3 },
        { code: "GEN101", title: "English Composition", grade: "A", score: 92, credits: 3 },
      ],
    },
  ]

  const getGradeColor = (grade: string) => {
    if (grade.startsWith("A")) return "bg-success text-success-foreground"
    if (grade.startsWith("B")) return "bg-info text-info-foreground"
    if (grade.startsWith("C")) return "bg-warning text-warning-foreground"
    return "bg-destructive text-destructive-foreground"
  }

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Academic Grades</h1>
            <p className="text-muted-foreground">Your complete academic record</p>
          </div>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Download Transcript
          </Button>
        </div>

        {/* GPA Overview */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Cumulative GPA</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">3.67</div>
              <Progress value={91.75} className="mt-2 h-2" />
              <p className="mt-2 text-xs text-muted-foreground">Out of 4.0</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Credits Earned</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">72</div>
              <Progress value={60} className="mt-2 h-2" />
              <p className="mt-2 text-xs text-muted-foreground">Out of 120 required</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Class Rank</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">12th</div>
              <p className="mt-2 text-xs text-muted-foreground">Out of 250 students</p>
              <Badge variant="secondary" className="mt-2">
                Top 5%
              </Badge>
            </CardContent>
          </Card>
        </div>

        {/* Semester Grades */}
        {semesters.map((semester) => (
          <Card key={semester.name}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{semester.name}</CardTitle>
                  <CardDescription>Semester GPA: {semester.gpa}</CardDescription>
                </div>
                <Badge variant="outline" className="text-base">
                  GPA: {semester.gpa}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border text-left text-sm text-muted-foreground">
                      <th className="pb-3 font-medium">Course Code</th>
                      <th className="pb-3 font-medium">Course Title</th>
                      <th className="pb-3 font-medium">Credits</th>
                      <th className="pb-3 font-medium">Score</th>
                      <th className="pb-3 font-medium">Grade</th>
                    </tr>
                  </thead>
                  <tbody>
                    {semester.courses.map((course) => (
                      <tr key={course.code} className="border-b border-border last:border-0">
                        <td className="py-3 font-mono text-sm">{course.code}</td>
                        <td className="py-3">{course.title}</td>
                        <td className="py-3">{course.credits}</td>
                        <td className="py-3">{course.score}%</td>
                        <td className="py-3">
                          <Badge className={getGradeColor(course.grade)}>{course.grade}</Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </DashboardLayout>
  )
}

export default withRole(["student"])(GradesPage)
