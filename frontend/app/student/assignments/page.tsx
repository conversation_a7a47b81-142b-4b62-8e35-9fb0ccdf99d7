"use client"

import { useState } from "react"
import { with<PERSON><PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  BookOpen,
  Calendar,
  CheckCircle2,
  Clock,
  FileText,
  GraduationCap,
  Home,
  TrendingUp,
  Upload,
  Wallet,
  AlertCircle,
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const navItems = [
  { title: "Dashboard", href: "/student", icon: Home },
  { title: "Courses", href: "/student/courses", icon: BookOpen },
  { title: "Grades", href: "/student/grades", icon: TrendingUp },
  { title: "Timetable", href: "/student/timetable", icon: Calendar },
  { title: "Assignments", href: "/student/assignments", icon: FileText },
  { title: "Fees", href: "/student/fees", icon: Wallet },
  { title: "Registration", href: "/student/registration", icon: GraduationCap },
]

function AssignmentsPage() {
  const [courseFilter, setCourseFilter] = useState("all")

  const assignments = [
    {
      id: "A001",
      title: "Binary Search Tree Implementation",
      course: "CS301",
      courseTitle: "Data Structures",
      dueDate: "2025-01-10",
      dueTime: "23:59",
      status: "pending",
      maxScore: 100,
      description: "Implement a balanced BST with insert, delete, and search operations",
      submitted: false,
      daysLeft: 4,
    },
    {
      id: "A002",
      title: "Algorithm Complexity Analysis",
      course: "CS302",
      courseTitle: "Algorithms",
      dueDate: "2025-01-08",
      dueTime: "23:59",
      status: "pending",
      maxScore: 100,
      description: "Analyze time and space complexity of sorting algorithms",
      submitted: false,
      daysLeft: 2,
    },
    {
      id: "A003",
      title: "Database Design Project",
      course: "CS303",
      courseTitle: "Database Systems",
      dueDate: "2025-01-15",
      dueTime: "23:59",
      status: "pending",
      maxScore: 150,
      description: "Design and implement a normalized database for an e-commerce system",
      submitted: false,
      daysLeft: 9,
    },
    {
      id: "A004",
      title: "Data Structure Quiz",
      course: "CS301",
      courseTitle: "Data Structures",
      dueDate: "2024-12-20",
      dueTime: "23:59",
      status: "graded",
      maxScore: 50,
      score: 46,
      description: "Online quiz covering trees and graphs",
      submitted: true,
      submittedDate: "2024-12-19",
    },
    {
      id: "A005",
      title: "Sorting Algorithm Implementation",
      course: "CS302",
      courseTitle: "Algorithms",
      dueDate: "2024-12-15",
      dueTime: "23:59",
      status: "graded",
      maxScore: 100,
      score: 92,
      description: "Implement and compare QuickSort and MergeSort",
      submitted: true,
      submittedDate: "2024-12-14",
    },
    {
      id: "A006",
      title: "SQL Query Assignment",
      course: "CS303",
      courseTitle: "Database Systems",
      dueDate: "2025-01-05",
      dueTime: "23:59",
      status: "submitted",
      maxScore: 75,
      description: "Write complex SQL queries for given scenarios",
      submitted: true,
      submittedDate: "2025-01-04",
    },
  ]

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: { variant: "secondary" as const, label: "Pending", icon: Clock },
      submitted: { variant: "default" as const, label: "Submitted", icon: CheckCircle2 },
      graded: { variant: "default" as const, label: "Graded", icon: CheckCircle2 },
    }
    return variants[status as keyof typeof variants] || variants.pending
  }

  const getUrgencyColor = (daysLeft: number) => {
    if (daysLeft <= 2) return "text-destructive"
    if (daysLeft <= 5) return "text-warning"
    return "text-muted-foreground"
  }

  const pendingAssignments = assignments.filter((a) => a.status === "pending")
  const submittedAssignments = assignments.filter((a) => a.status === "submitted")
  const gradedAssignments = assignments.filter((a) => a.status === "graded")

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Assignments</h1>
            <p className="text-muted-foreground">Track and submit your coursework</p>
          </div>
          <Select value={courseFilter} onValueChange={setCourseFilter}>
            <SelectTrigger className="w-full md:w-64">
              <SelectValue placeholder="Filter by course" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Courses</SelectItem>
              <SelectItem value="CS301">CS301 - Data Structures</SelectItem>
              <SelectItem value="CS302">CS302 - Algorithms</SelectItem>
              <SelectItem value="CS303">CS303 - Database Systems</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{assignments.length}</div>
              <p className="text-xs text-muted-foreground">This semester</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-warning">{pendingAssignments.length}</div>
              <p className="text-xs text-muted-foreground">Due soon</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Submitted</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{submittedAssignments.length}</div>
              <p className="text-xs text-muted-foreground">Awaiting grading</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Average Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-success">92%</div>
              <p className="text-xs text-muted-foreground">From graded work</p>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="pending" className="space-y-4">
          <TabsList>
            <TabsTrigger value="pending">
              Pending ({pendingAssignments.length})
            </TabsTrigger>
            <TabsTrigger value="submitted">
              Submitted ({submittedAssignments.length})
            </TabsTrigger>
            <TabsTrigger value="graded">
              Graded ({gradedAssignments.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="space-y-4">
            {pendingAssignments.map((assignment) => (
              <Card key={assignment.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{assignment.course}</Badge>
                        <Badge variant={getStatusBadge(assignment.status).variant}>
                          {getStatusBadge(assignment.status).label}
                        </Badge>
                        {assignment.daysLeft <= 2 && (
                          <Badge variant="destructive" className="flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            Urgent
                          </Badge>
                        )}
                      </div>
                      <CardTitle className="text-xl">{assignment.title}</CardTitle>
                      <CardDescription>{assignment.courseTitle}</CardDescription>
                    </div>
                    <Button>
                      <Upload className="mr-2 h-4 w-4" />
                      Submit
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{assignment.description}</p>
                  <div className="flex flex-wrap gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>
                        Due: {new Date(assignment.dueDate).toLocaleDateString()} at {assignment.dueTime}
                      </span>
                    </div>
                    <div className={`flex items-center gap-2 font-medium ${getUrgencyColor(assignment.daysLeft)}`}>
                      <Clock className="h-4 w-4" />
                      <span>{assignment.daysLeft} days left</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span>Max Score: {assignment.maxScore}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="submitted" className="space-y-4">
            {submittedAssignments.map((assignment) => (
              <Card key={assignment.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{assignment.course}</Badge>
                        <Badge variant="default" className="bg-info text-info-foreground">
                          <CheckCircle2 className="mr-1 h-3 w-3" />
                          Submitted
                        </Badge>
                      </div>
                      <CardTitle className="text-xl">{assignment.title}</CardTitle>
                      <CardDescription>{assignment.courseTitle}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{assignment.description}</p>
                  <div className="flex flex-wrap gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-success" />
                      <span>Submitted on: {new Date(assignment.submittedDate!).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span>Max Score: {assignment.maxScore}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="graded" className="space-y-4">
            {gradedAssignments.map((assignment) => (
              <Card key={assignment.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{assignment.course}</Badge>
                        <Badge variant="default" className="bg-success text-success-foreground">
                          <CheckCircle2 className="mr-1 h-3 w-3" />
                          Graded
                        </Badge>
                      </div>
                      <CardTitle className="text-xl">{assignment.title}</CardTitle>
                      <CardDescription>{assignment.courseTitle}</CardDescription>
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-bold text-success">{assignment.score}</div>
                      <div className="text-sm text-muted-foreground">/ {assignment.maxScore}</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{assignment.description}</p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Score</span>
                      <span className="font-medium">
                        {((assignment.score! / assignment.maxScore) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={(assignment.score! / assignment.maxScore) * 100} className="h-2" />
                  </div>
                  <div className="flex flex-wrap gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-success" />
                      <span>Submitted: {new Date(assignment.submittedDate!).toLocaleDateString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["student"])(AssignmentsPage)

