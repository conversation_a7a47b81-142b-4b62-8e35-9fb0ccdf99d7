"use client"

import { withRole } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  BookOpen,
  Calendar,
  Clock,
  FileText,
  GraduationCap,
  Home,
  Search,
  TrendingUp,
  Users,
  Wallet,
} from "lucide-react"

const navItems = [
  { title: "Dashboard", href: "/student", icon: Home },
  { title: "Courses", href: "/student/courses", icon: BookOpen },
  { title: "Grades", href: "/student/grades", icon: TrendingUp },
  { title: "Timetable", href: "/student/timetable", icon: Calendar },
  { title: "Assignments", href: "/student/assignments", icon: FileText },
  { title: "Fees", href: "/student/fees", icon: Wallet },
  { title: "Registration", href: "/student/registration", icon: GraduationCap },
]

function CoursesPage() {
  const courses = [
    {
      code: "CS301",
      title: "Data Structures and <PERSON>gorithms",
      credits: 3,
      lecturer: "Dr. <PERSON>",
      schedule: "Mon, Wed 09:00-10:30",
      room: "LH-101",
      enrolled: 45,
      capacity: 50,
    },
    {
      code: "CS302",
      title: "Algorithm Design and Analysis",
      credits: 3,
      lecturer: "Prof. <PERSON> Chen",
      schedule: "Tue, Thu 11:00-12:30",
      room: "LH-205",
      enrolled: 38,
      capacity: 45,
    },
    {
      code: "CS303",
      title: "Database Management Systems",
      credits: 3,
      lecturer: "Dr. Emily Rodriguez",
      schedule: "Mon, Wed 14:00-15:30",
      room: "LAB-3",
      enrolled: 42,
      capacity: 45,
    },
    {
      code: "CS304",
      title: "Software Engineering",
      credits: 3,
      lecturer: "Dr. James Wilson",
      schedule: "Tue, Thu 09:00-10:30",
      room: "LH-102",
      enrolled: 50,
      capacity: 50,
    },
    {
      code: "CS305",
      title: "Computer Networks",
      credits: 3,
      lecturer: "Prof. Lisa Anderson",
      schedule: "Wed, Fri 11:00-12:30",
      room: "LAB-1",
      enrolled: 35,
      capacity: 40,
    },
    {
      code: "CS306",
      title: "Operating Systems",
      credits: 3,
      lecturer: "Dr. Robert Taylor",
      schedule: "Mon, Wed 16:00-17:30",
      room: "LH-103",
      enrolled: 48,
      capacity: 50,
    },
  ]

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">My Courses</h1>
            <p className="text-muted-foreground">Spring 2025 - 18 Credit Hours</p>
          </div>
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input placeholder="Search courses..." className="pl-10" />
          </div>
        </div>

        <div className="grid gap-4">
          {courses.map((course) => (
            <Card key={course.code} className="transition-shadow hover:shadow-lg">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{course.code}</Badge>
                      <Badge variant="secondary">{course.credits} Credits</Badge>
                    </div>
                    <CardTitle className="text-xl">{course.title}</CardTitle>
                    <CardDescription className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {course.lecturer}
                    </CardDescription>
                  </div>
                  <Button size="sm">View Details</Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{course.schedule}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span>{course.room}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {course.enrolled}/{course.capacity} Students
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["student"])(CoursesPage)
