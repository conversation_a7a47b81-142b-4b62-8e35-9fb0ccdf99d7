"use client"

import { useState } from "react"
import { withR<PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  BookOpen,
  Calendar,
  CheckCircle2,
  FileText,
  GraduationCap,
  Home,
  TrendingUp,
  Wallet,
  AlertCircle,
} from "lucide-react"

const navItems = [
  { title: "Dashboard", href: "/student", icon: Home },
  { title: "Courses", href: "/student/courses", icon: BookOpen },
  { title: "Grades", href: "/student/grades", icon: TrendingUp },
  { title: "Timetable", href: "/student/timetable", icon: Calendar },
  { title: "Assignments", href: "/student/assignments", icon: FileText },
  { title: "Fees", href: "/student/fees", icon: Wallet },
  { title: "Registration", href: "/student/registration", icon: GraduationCap },
]

function RegistrationPage() {
  const [selectedCourses, setSelectedCourses] = useState<string[]>([])

  const availableCourses = [
    {
      code: "CS401",
      title: "Machine Learning",
      credits: 3,
      lecturer: "Dr. Sarah Johnson",
      schedule: "Mon, Wed 09:00-10:30",
      enrolled: 28,
      capacity: 40,
      prerequisites: ["CS301", "CS302"],
      hasPrerequisites: true,
    },
    {
      code: "CS402",
      title: "Artificial Intelligence",
      credits: 3,
      lecturer: "Prof. Michael Chen",
      schedule: "Tue, Thu 11:00-12:30",
      enrolled: 35,
      capacity: 40,
      prerequisites: ["CS301"],
      hasPrerequisites: true,
    },
    {
      code: "CS403",
      title: "Cloud Computing",
      credits: 3,
      lecturer: "Dr. Emily Rodriguez",
      schedule: "Mon, Wed 14:00-15:30",
      enrolled: 22,
      capacity: 35,
      prerequisites: ["CS303"],
      hasPrerequisites: true,
    },
    {
      code: "CS404",
      title: "Mobile App Development",
      credits: 3,
      lecturer: "Dr. James Wilson",
      schedule: "Tue, Thu 09:00-10:30",
      enrolled: 30,
      capacity: 35,
      prerequisites: ["CS304"],
      hasPrerequisites: true,
    },
    {
      code: "CS405",
      title: "Cybersecurity",
      credits: 3,
      lecturer: "Prof. Lisa Anderson",
      schedule: "Wed, Fri 11:00-12:30",
      enrolled: 38,
      capacity: 40,
      prerequisites: ["CS305"],
      hasPrerequisites: true,
    },
  ]

  const toggleCourse = (code: string) => {
    setSelectedCourses((prev) => (prev.includes(code) ? prev.filter((c) => c !== code) : [...prev, code]))
  }

  const totalCredits = selectedCourses.reduce((sum, code) => {
    const course = availableCourses.find((c) => c.code === code)
    return sum + (course?.credits || 0)
  }, 0)

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Course Registration</h1>
          <p className="text-muted-foreground">Fall 2025 - Registration Period</p>
        </div>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Registration opens on May 1, 2025. You can select courses now and submit when registration opens.
          </AlertDescription>
        </Alert>

        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Available Courses</CardTitle>
                <CardDescription>Select courses for Fall 2025 semester</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {availableCourses.map((course) => (
                  <div key={course.code} className="flex items-start gap-4 rounded-lg border border-border p-4">
                    <Checkbox
                      checked={selectedCourses.includes(course.code)}
                      onCheckedChange={() => toggleCourse(course.code)}
                      className="mt-1"
                    />
                    <div className="flex-1 space-y-2">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{course.code}</Badge>
                            <Badge variant="secondary">{course.credits} Credits</Badge>
                            {course.hasPrerequisites && (
                              <Badge variant="default" className="bg-success">
                                <CheckCircle2 className="mr-1 h-3 w-3" />
                                Prerequisites Met
                              </Badge>
                            )}
                          </div>
                          <h3 className="mt-1 font-semibold">{course.title}</h3>
                          <p className="text-sm text-muted-foreground">{course.lecturer}</p>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                        <span>{course.schedule}</span>
                        <span>
                          {course.enrolled}/{course.capacity} enrolled
                        </span>
                      </div>
                      {course.prerequisites.length > 0 && (
                        <p className="text-xs text-muted-foreground">
                          Prerequisites: {course.prerequisites.join(", ")}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Registration Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Selected Courses</span>
                    <span className="font-medium">{selectedCourses.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Total Credits</span>
                    <span className="font-medium">{totalCredits}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Max Credits</span>
                    <span className="font-medium">21</span>
                  </div>
                </div>

                {totalCredits > 21 && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>You've exceeded the maximum credit limit of 21 hours.</AlertDescription>
                  </Alert>
                )}

                {totalCredits < 12 && selectedCourses.length > 0 && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>Minimum 12 credits required for full-time status.</AlertDescription>
                  </Alert>
                )}

                <Button
                  className="w-full"
                  disabled={selectedCourses.length === 0 || totalCredits > 21 || totalCredits < 12}
                >
                  Submit Registration
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Selected Courses</CardTitle>
              </CardHeader>
              <CardContent>
                {selectedCourses.length === 0 ? (
                  <p className="text-sm text-muted-foreground">No courses selected</p>
                ) : (
                  <ul className="space-y-2">
                    {selectedCourses.map((code) => {
                      const course = availableCourses.find((c) => c.code === code)
                      return (
                        <li key={code} className="text-sm">
                          <span className="font-mono">{code}</span> - {course?.credits} credits
                        </li>
                      )
                    })}
                  </ul>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["student"])(RegistrationPage)
