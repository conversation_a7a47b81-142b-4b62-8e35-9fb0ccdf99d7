"use client"

import { withR<PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Calendar, DollarSign, FileText, GraduationCap, Home, TrendingUp, Wallet } from "lucide-react"
import { useAuthStore } from "@/lib/auth-store"

const navItems = [
  { title: "Dashboard", href: "/student", icon: Home },
  { title: "Courses", href: "/student/courses", icon: BookOpen },
  { title: "Grades", href: "/student/grades", icon: TrendingUp },
  { title: "Timetable", href: "/student/timetable", icon: Calendar },
  { title: "Assignments", href: "/student/assignments", icon: FileText },
  { title: "Fees", href: "/student/fees", icon: Wallet },
  { title: "Registration", href: "/student/registration", icon: GraduationCap },
]

function StudentDashboard() {
  const { user } = useAuthStore()

  const upcomingClasses = [
    { course: "CS301", title: "Data Structures", time: "09:00 AM", room: "LH-101" },
    { course: "CS302", title: "Algorithms", time: "11:00 AM", room: "LH-205" },
    { course: "CS303", title: "Database Systems", time: "02:00 PM", room: "LAB-3" },
  ]

  const recentAssignments = [
    { title: "Algorithm Analysis", course: "CS302", due: "2 days", status: "pending" },
    { title: "Database Design", course: "CS303", due: "5 days", status: "pending" },
    { title: "Data Structure Project", course: "CS301", due: "Submitted", status: "submitted" },
  ]

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Welcome back, {user?.name}</h1>
          <p className="text-muted-foreground">
            {user?.programme} - Level {user?.level}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current GPA</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3.67</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-success">+0.12</span> from last semester
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Enrolled Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">6</div>
              <p className="text-xs text-muted-foreground">18 credit hours</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Assignments</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">4</div>
              <p className="text-xs text-muted-foreground">2 due this week</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Fee Balance</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$2,450</div>
              <p className="text-xs text-muted-foreground">Due: May 15, 2025</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Today's Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Today's Schedule
              </CardTitle>
              <CardDescription>Monday, January 6, 2025</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {upcomingClasses.map((cls, i) => (
                <div key={i} className="flex items-center justify-between rounded-lg border border-border p-3">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                      <BookOpen className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">{cls.title}</p>
                      <p className="text-sm text-muted-foreground">{cls.course}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{cls.time}</p>
                    <p className="text-xs text-muted-foreground">{cls.room}</p>
                  </div>
                </div>
              ))}
              <Button variant="outline" className="w-full bg-transparent">
                View Full Timetable
              </Button>
            </CardContent>
          </Card>

          {/* Recent Assignments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Recent Assignments
              </CardTitle>
              <CardDescription>Track your pending work</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentAssignments.map((assignment, i) => (
                <div key={i} className="flex items-center justify-between rounded-lg border border-border p-3">
                  <div className="flex-1">
                    <p className="font-medium">{assignment.title}</p>
                    <p className="text-sm text-muted-foreground">{assignment.course}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={assignment.status === "submitted" ? "default" : "secondary"}>
                      {assignment.due}
                    </Badge>
                  </div>
                </div>
              ))}
              <Button variant="outline" className="w-full bg-transparent">
                View All Assignments
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Academic Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Academic Progress</CardTitle>
            <CardDescription>Your journey to graduation</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">Credits Completed</span>
                <span className="text-muted-foreground">72 / 120</span>
              </div>
              <Progress value={60} className="h-2" />
            </div>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="rounded-lg border border-border p-4">
                <p className="text-sm text-muted-foreground">Core Courses</p>
                <p className="text-2xl font-bold">18/24</p>
              </div>
              <div className="rounded-lg border border-border p-4">
                <p className="text-sm text-muted-foreground">Electives</p>
                <p className="text-2xl font-bold">12/18</p>
              </div>
              <div className="rounded-lg border border-border p-4">
                <p className="text-sm text-muted-foreground">General Ed</p>
                <p className="text-2xl font-bold">6/6</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["student"])(StudentDashboard)
