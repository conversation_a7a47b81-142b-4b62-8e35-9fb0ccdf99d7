import type { MetadataRoute } from "next"

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: "University Portal",
    short_name: "UniPortal",
    description: "Comprehensive university management system for students, lecturers, and administrators",
    start_url: "/",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#0070f3",
    orientation: "portrait",
    icons: [
      {
        src: "/icon-192.jpg",
        sizes: "192x192",
        type: "image/png",
        purpose: "any maskable",
      },
      {
        src: "/icon-512.jpg",
        sizes: "512x512",
        type: "image/png",
        purpose: "any maskable",
      },
    ],
    categories: ["education", "productivity"],
    shortcuts: [
      {
        name: "Dashboard",
        short_name: "Dashboard",
        description: "Go to your dashboard",
        url: "/",
        icons: [{ src: "/icon-192.jpg", sizes: "192x192" }],
      },
      {
        name: "Courses",
        short_name: "Courses",
        description: "View your courses",
        url: "/student/courses",
        icons: [{ src: "/icon-192.jpg", sizes: "192x192" }],
      },
    ],
  }
}
