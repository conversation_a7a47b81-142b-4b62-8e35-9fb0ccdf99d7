"use client"

import { useState } from "react"
import { with<PERSON><PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { BookOpen, Calendar, ClipboardCheck, Download, FileText, Home, Save, TrendingUp } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const navItems = [
  { title: "Dashboard", href: "/lecturer", icon: Home },
  { title: "My Courses", href: "/lecturer/courses", icon: BookOpen },
  { title: "Attendance", href: "/lecturer/attendance", icon: ClipboardCheck },
  { title: "Gradebook", href: "/lecturer/gradebook", icon: TrendingUp },
  { title: "Assignments", href: "/lecturer/assignments", icon: FileText },
  { title: "Schedule", href: "/lecturer/schedule", icon: Calendar },
]

function GradebookPage() {
  const [selectedCourse, setSelectedCourse] = useState("CS301")
  const [hasChanges, setHasChanges] = useState(false)

  const students = [
    {
      id: "STU001",
      name: "Alice Johnson",
      midterm: 85,
      final: 88,
      assignments: 92,
      project: 90,
      total: 88.75,
      grade: "A-",
    },
    {
      id: "STU002",
      name: "Bob Smith",
      midterm: 78,
      final: 82,
      assignments: 85,
      project: 80,
      total: 81.25,
      grade: "B+",
    },
    {
      id: "STU003",
      name: "Carol Williams",
      midterm: 92,
      final: 95,
      assignments: 98,
      project: 96,
      total: 95.25,
      grade: "A",
    },
    {
      id: "STU004",
      name: "David Brown",
      midterm: 72,
      final: 75,
      assignments: 78,
      project: 74,
      total: 74.75,
      grade: "C+",
    },
    {
      id: "STU005",
      name: "Emma Davis",
      midterm: 88,
      final: 90,
      assignments: 94,
      project: 92,
      total: 91,
      grade: "A-",
    },
  ]

  const gradeDistribution = [
    { grade: "A", count: 8, percentage: 18 },
    { grade: "A-", count: 12, percentage: 27 },
    { grade: "B+", count: 10, percentage: 22 },
    { grade: "B", count: 8, percentage: 18 },
    { grade: "C+", count: 5, percentage: 11 },
    { grade: "C", count: 2, percentage: 4 },
  ]

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Gradebook</h1>
            <p className="text-muted-foreground">Manage student grades and assessments</p>
          </div>
          <div className="flex gap-2">
            {hasChanges && (
              <Button onClick={() => setHasChanges(false)}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            )}
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export CSV
            </Button>
          </div>
        </div>

        <div className="flex flex-col gap-4 md:flex-row">
          <Select value={selectedCourse} onValueChange={setSelectedCourse}>
            <SelectTrigger className="w-full md:w-64">
              <SelectValue placeholder="Select course" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="CS301">CS301 - Data Structures</SelectItem>
              <SelectItem value="CS302">CS302 - Algorithms</SelectItem>
              <SelectItem value="CS401">CS401 - Machine Learning</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Class Average</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">82.5%</div>
              <p className="text-xs text-muted-foreground">B+ average</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Highest Score</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-success">95.3%</div>
              <p className="text-xs text-muted-foreground">Carol Williams</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">At Risk Students</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-destructive">4</div>
              <p className="text-xs text-muted-foreground">Below 70%</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Pending Grades</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">Assignments to grade</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Student Grades</CardTitle>
              <CardDescription>
                CS301 - Data Structures (Midterm: 25%, Final: 35%, Assignments: 25%, Project: 15%)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border text-left text-xs text-muted-foreground">
                      <th className="pb-3 font-medium">Student</th>
                      <th className="pb-3 font-medium">Midterm</th>
                      <th className="pb-3 font-medium">Final</th>
                      <th className="pb-3 font-medium">Assignments</th>
                      <th className="pb-3 font-medium">Project</th>
                      <th className="pb-3 font-medium">Total</th>
                      <th className="pb-3 font-medium">Grade</th>
                    </tr>
                  </thead>
                  <tbody>
                    {students.map((student) => (
                      <tr key={student.id} className="border-b border-border last:border-0">
                        <td className="py-3">
                          <div>
                            <p className="font-medium">{student.name}</p>
                            <p className="text-xs text-muted-foreground">{student.id}</p>
                          </div>
                        </td>
                        <td className="py-3">
                          <Input
                            type="number"
                            defaultValue={student.midterm}
                            className="h-8 w-16"
                            onChange={() => setHasChanges(true)}
                          />
                        </td>
                        <td className="py-3">
                          <Input
                            type="number"
                            defaultValue={student.final}
                            className="h-8 w-16"
                            onChange={() => setHasChanges(true)}
                          />
                        </td>
                        <td className="py-3">
                          <Input
                            type="number"
                            defaultValue={student.assignments}
                            className="h-8 w-16"
                            onChange={() => setHasChanges(true)}
                          />
                        </td>
                        <td className="py-3">
                          <Input
                            type="number"
                            defaultValue={student.project}
                            className="h-8 w-16"
                            onChange={() => setHasChanges(true)}
                          />
                        </td>
                        <td className="py-3 font-semibold">{student.total}%</td>
                        <td className="py-3">
                          <Badge
                            variant={
                              student.grade.startsWith("A")
                                ? "default"
                                : student.grade.startsWith("B")
                                  ? "secondary"
                                  : "outline"
                            }
                          >
                            {student.grade}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Grade Distribution</CardTitle>
              <CardDescription>Class performance breakdown</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {gradeDistribution.map((item) => (
                <div key={item.grade} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Grade {item.grade}</span>
                    <span className="text-muted-foreground">
                      {item.count} ({item.percentage}%)
                    </span>
                  </div>
                  <div className="h-2 w-full overflow-hidden rounded-full bg-secondary">
                    <div className="h-full bg-primary transition-all" style={{ width: `${item.percentage * 4}%` }} />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["lecturer"])(GradebookPage)
