"use client"

import { useState } from "react"
import { with<PERSON><PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { 
  BookOpen, 
  Calendar, 
  ClipboardCheck, 
  FileText, 
  Home, 
  Search, 
  TrendingUp, 
  Users,
  Plus,
  Edit,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  Download,
  Upload
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const navItems = [
  { title: "Dashboard", href: "/lecturer", icon: Home },
  { title: "My Courses", href: "/lecturer/courses", icon: BookOpen },
  { title: "Attendance", href: "/lecturer/attendance", icon: Clip<PERSON><PERSON>heck },
  { title: "Gradebook", href: "/lecturer/gradebook", icon: TrendingUp },
  { title: "Assignments", href: "/lecturer/assignments", icon: FileText },
  { title: "Schedule", href: "/lecturer/schedule", icon: Calendar },
]

function LecturerAssignmentsPage() {
  const [selectedCourse, setSelectedCourse] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")

  const assignments = [
    {
      id: "ASS001",
      title: "Data Structure Implementation",
      course: "CS301",
      courseTitle: "Data Structures and Algorithms",
      dueDate: "2025-01-20",
      maxScore: 100,
      submissions: 42,
      totalStudents: 45,
      avgScore: 78.5,
      status: "active",
      description: "Implement basic data structures including arrays, linked lists, and stacks.",
      type: "Programming",
    },
    {
      id: "ASS002",
      title: "Algorithm Analysis Report",
      course: "CS302",
      courseTitle: "Algorithm Design and Analysis",
      dueDate: "2025-01-25",
      maxScore: 100,
      submissions: 35,
      totalStudents: 38,
      avgScore: 82.1,
      status: "active",
      description: "Analyze the time and space complexity of given algorithms.",
      type: "Report",
    },
    {
      id: "ASS003",
      title: "Machine Learning Project",
      course: "CS401",
      courseTitle: "Machine Learning Fundamentals",
      dueDate: "2025-02-01",
      maxScore: 150,
      submissions: 25,
      totalStudents: 28,
      avgScore: 85.2,
      status: "active",
      description: "Build and evaluate a machine learning model using real-world data.",
      type: "Project",
    },
    {
      id: "ASS004",
      title: "Binary Tree Traversal",
      course: "CS301",
      courseTitle: "Data Structures and Algorithms",
      dueDate: "2025-01-15",
      maxScore: 100,
      submissions: 45,
      totalStudents: 45,
      avgScore: 76.8,
      status: "graded",
      description: "Implement various binary tree traversal algorithms.",
      type: "Programming",
    },
    {
      id: "ASS005",
      title: "Sorting Algorithm Comparison",
      course: "CS302",
      courseTitle: "Algorithm Design and Analysis",
      dueDate: "2025-01-10",
      maxScore: 100,
      submissions: 38,
      totalStudents: 38,
      avgScore: 79.3,
      status: "graded",
      description: "Compare the performance of different sorting algorithms.",
      type: "Analysis",
    },
  ]

  const recentSubmissions = [
    { student: "Alice Johnson", assignment: "Data Structure Implementation", course: "CS301", submitted: "2025-01-18", score: 85 },
    { student: "Bob Smith", assignment: "Algorithm Analysis Report", course: "CS302", submitted: "2025-01-19", score: 92 },
    { student: "Carol Williams", assignment: "Machine Learning Project", course: "CS401", submitted: "2025-01-20", score: 88 },
    { student: "David Brown", assignment: "Data Structure Implementation", course: "CS301", submitted: "2025-01-19", score: 76 },
  ]

  const filteredAssignments = assignments.filter(assignment => {
    const matchesCourse = selectedCourse === "all" || assignment.course === selectedCourse
    const matchesSearch = assignment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.course.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCourse && matchesSearch
  })

  const getStatusBadge = (status: string) => {
    const variants = {
      active: { variant: "default" as const, className: "bg-info text-info-foreground", icon: Clock },
      graded: { variant: "default" as const, className: "bg-success text-success-foreground", icon: CheckCircle },
      draft: { variant: "secondary" as const, className: "", icon: Edit },
    }
    return variants[status as keyof typeof variants] || variants.draft
  }

  const getTypeBadge = (type: string) => {
    const variants = {
      Programming: { variant: "outline" as const, className: "" },
      Report: { variant: "outline" as const, className: "" },
      Project: { variant: "outline" as const, className: "" },
      Analysis: { variant: "outline" as const, className: "" },
    }
    return variants[type as keyof typeof variants] || variants.Programming
  }

  const totalAssignments = assignments.length
  const activeAssignments = assignments.filter(a => a.status === "active").length
  const totalSubmissions = assignments.reduce((sum, a) => sum + a.submissions, 0)
  const avgScore = assignments.reduce((sum, a) => sum + a.avgScore, 0) / assignments.length

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Assignment Management</h1>
            <p className="text-muted-foreground">Create, manage, and grade student assignments</p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Assignment
          </Button>
        </div>

        <div className="flex flex-col gap-4 md:flex-row">
          <Select value={selectedCourse} onValueChange={setSelectedCourse}>
            <SelectTrigger className="w-full md:w-64">
              <SelectValue placeholder="Filter by course" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Courses</SelectItem>
              <SelectItem value="CS301">CS301 - Data Structures</SelectItem>
              <SelectItem value="CS302">CS302 - Algorithms</SelectItem>
              <SelectItem value="CS401">CS401 - Machine Learning</SelectItem>
            </SelectContent>
          </Select>
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input 
              placeholder="Search assignments..." 
              className="pl-10" 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Assignment Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalAssignments}</div>
              <p className="text-xs text-muted-foreground">{activeAssignments} active</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
              <Upload className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalSubmissions}</div>
              <p className="text-xs text-muted-foreground">Student submissions</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{avgScore.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Overall performance</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Grading</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-warning">12</div>
              <p className="text-xs text-muted-foreground">Submissions to review</p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Submissions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Recent Submissions
            </CardTitle>
            <CardDescription>Latest student submissions requiring attention</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentSubmissions.map((submission, i) => (
              <div key={i} className="flex items-center justify-between rounded-lg border border-border p-3">
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">{submission.student}</p>
                    <p className="text-sm text-muted-foreground">{submission.assignment} - {submission.course}</p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <p className="text-sm font-medium">Submitted: {submission.submitted}</p>
                    <p className="text-xs text-muted-foreground">Score: {submission.score}/100</p>
                  </div>
                  <Button variant="outline" size="sm">
                    <Eye className="mr-1 h-3 w-3" />
                    Review
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Assignment List */}
        <div className="grid gap-4">
          {filteredAssignments.map((assignment) => {
            const statusBadge = getStatusBadge(assignment.status)
            const typeBadge = getTypeBadge(assignment.type)
            const StatusIcon = statusBadge.icon
            const submissionRate = (assignment.submissions / assignment.totalStudents) * 100
            
            return (
              <Card key={assignment.id} className="transition-shadow hover:shadow-lg">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{assignment.course}</Badge>
                        <Badge variant={typeBadge.variant} className={typeBadge.className}>
                          {assignment.type}
                        </Badge>
                        <Badge variant={statusBadge.variant} className={statusBadge.className}>
                          <StatusIcon className="mr-1 h-3 w-3" />
                          {assignment.status}
                        </Badge>
                      </div>
                      <CardTitle className="text-xl">{assignment.title}</CardTitle>
                      <CardDescription>{assignment.description}</CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="mr-1 h-3 w-3" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>Due: {assignment.dueDate}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>{assignment.submissions}/{assignment.totalStudents} Submitted</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                      <span>Avg: {assignment.avgScore}%</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span>Max: {assignment.maxScore} pts</span>
                    </div>
                  </div>
                  
                  <div className="mt-4 space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="font-medium">Submission Rate</span>
                      <span className="text-muted-foreground">{submissionRate.toFixed(1)}%</span>
                    </div>
                    <Progress value={submissionRate} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Assignment Performance Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Assignment Performance Overview</CardTitle>
            <CardDescription>Performance metrics across all assignments</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {assignments.map((assignment) => (
                <div key={assignment.id} className="flex items-center justify-between rounded-lg border border-border p-4">
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                      <FileText className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{assignment.title}</h3>
                      <p className="text-sm text-muted-foreground">{assignment.course} • {assignment.type}</p>
                    </div>
                  </div>
                  <div className="flex gap-6 text-center">
                    <div>
                      <p className="text-2xl font-bold">{assignment.submissions}</p>
                      <p className="text-xs text-muted-foreground">Submissions</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold">{assignment.avgScore}%</p>
                      <p className="text-xs text-muted-foreground">Avg Score</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold">{assignment.maxScore}</p>
                      <p className="text-xs text-muted-foreground">Max Points</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["lecturer"])(LecturerAssignmentsPage)
