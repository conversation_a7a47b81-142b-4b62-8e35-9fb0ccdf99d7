"use client"

import { useState } from "react"
import { with<PERSON><PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { 
  BookOpen, 
  Calendar, 
  ClipboardCheck, 
  FileText, 
  Home, 
  Search, 
  TrendingUp, 
  Users,
  Plus,
  Edit,
  Eye,
  BarChart3,
  Clock,
  MapPin,
  GraduationCap
} from "lucide-react"

const navItems = [
  { title: "Dashboard", href: "/lecturer", icon: Home },
  { title: "My Courses", href: "/lecturer/courses", icon: BookOpen },
  { title: "Attendance", href: "/lecturer/attendance", icon: ClipboardCheck },
  { title: "Gradebook", href: "/lecturer/gradebook", icon: TrendingUp },
  { title: "Assignments", href: "/lecturer/assignments", icon: FileText },
  { title: "Schedule", href: "/lecturer/schedule", icon: Calendar },
]

function LecturerCoursesPage() {
  const [searchTerm, setSearchTerm] = useState("")

  const courses = [
    {
      id: "CS301",
      title: "Data Structures and Algorithms",
      code: "CS301",
      credits: 3,
      students: 45,
      capacity: 50,
      schedule: "Mon, Wed 09:00-10:30",
      room: "LH-101",
      semester: "Spring 2025",
      avgGrade: 78.5,
      attendance: 92.3,
      assignments: 8,
      status: "active",
      description: "Fundamental data structures and algorithm analysis techniques.",
    },
    {
      id: "CS302",
      title: "Algorithm Design and Analysis",
      code: "CS302",
      credits: 3,
      students: 38,
      capacity: 45,
      schedule: "Tue, Thu 11:00-12:30",
      room: "LH-205",
      semester: "Spring 2025",
      avgGrade: 82.1,
      attendance: 88.7,
      assignments: 6,
      status: "active",
      description: "Advanced algorithm design patterns and complexity analysis.",
    },
    {
      id: "CS401",
      title: "Machine Learning Fundamentals",
      code: "CS401",
      credits: 4,
      students: 28,
      capacity: 30,
      schedule: "Mon, Wed, Fri 14:00-15:00",
      room: "LAB-3",
      semester: "Spring 2025",
      avgGrade: 85.2,
      attendance: 95.1,
      assignments: 10,
      status: "active",
      description: "Introduction to machine learning algorithms and applications.",
    },
  ]

  const upcomingClasses = [
    { course: "CS301", title: "Data Structures", time: "09:00 AM", room: "LH-101", date: "Today" },
    { course: "CS302", title: "Algorithms", time: "11:00 AM", room: "LH-205", date: "Today" },
    { course: "CS401", title: "Machine Learning", time: "02:00 PM", room: "LAB-3", date: "Tomorrow" },
  ]

  const filteredCourses = courses.filter(course =>
    course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    course.code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusBadge = (status: string) => {
    return status === "active" 
      ? { variant: "default" as const, className: "bg-success text-success-foreground" }
      : { variant: "secondary" as const, className: "" }
  }

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">My Courses</h1>
            <p className="text-muted-foreground">Manage your teaching courses and student enrollment</p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Course
          </Button>
        </div>

        <div className="flex flex-col gap-4 md:flex-row">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input 
              placeholder="Search courses..." 
              className="pl-10" 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Course Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{courses.length}</div>
              <p className="text-xs text-muted-foreground">Active this semester</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{courses.reduce((sum, course) => sum + course.students, 0)}</div>
              <p className="text-xs text-muted-foreground">Across all courses</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Attendance</CardTitle>
              <ClipboardCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(courses.reduce((sum, course) => sum + course.attendance, 0) / courses.length)}%
              </div>
              <p className="text-xs text-muted-foreground">Overall attendance rate</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Grade</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(courses.reduce((sum, course) => sum + course.avgGrade, 0) / courses.length)}%
              </div>
              <p className="text-xs text-muted-foreground">Student performance</p>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Classes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Upcoming Classes
            </CardTitle>
            <CardDescription>Your next teaching sessions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingClasses.map((cls, i) => (
              <div key={i} className="flex items-center justify-between rounded-lg border border-border p-3">
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                    <BookOpen className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">{cls.title}</p>
                    <p className="text-sm text-muted-foreground">{cls.course}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{cls.time}</p>
                  <p className="text-xs text-muted-foreground">{cls.room}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Course List */}
        <div className="grid gap-4">
          {filteredCourses.map((course) => {
            const statusBadge = getStatusBadge(course.status)
            return (
              <Card key={course.id} className="transition-shadow hover:shadow-lg">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{course.code}</Badge>
                        <Badge variant="secondary">{course.credits} Credits</Badge>
                        <Badge variant={statusBadge.variant} className={statusBadge.className}>
                          {course.status}
                        </Badge>
                      </div>
                      <CardTitle className="text-xl">{course.title}</CardTitle>
                      <CardDescription>{course.description}</CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="mr-1 h-3 w-3" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <div className="flex items-center gap-2 text-sm">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>{course.students}/{course.capacity} Students</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{course.schedule}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{course.room}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span>{course.assignments} Assignments</span>
                    </div>
                  </div>
                  
                  <div className="mt-4 grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">Attendance</span>
                        <span className="text-muted-foreground">{course.attendance}%</span>
                      </div>
                      <Progress value={course.attendance} className="h-2" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">Avg Grade</span>
                        <span className="text-muted-foreground">{course.avgGrade}%</span>
                      </div>
                      <Progress value={course.avgGrade} className="h-2" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">Enrollment</span>
                        <span className="text-muted-foreground">{Math.round((course.students / course.capacity) * 100)}%</span>
                      </div>
                      <Progress value={(course.students / course.capacity) * 100} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Course Performance Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Course Performance Overview</CardTitle>
            <CardDescription>Comparative analysis of your courses</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {courses.map((course) => (
                <div key={course.id} className="flex items-center justify-between rounded-lg border border-border p-4">
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                      <BookOpen className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{course.title}</h3>
                      <p className="text-sm text-muted-foreground">{course.code} • {course.students} students</p>
                    </div>
                  </div>
                  <div className="flex gap-6 text-center">
                    <div>
                      <p className="text-2xl font-bold">{course.attendance}%</p>
                      <p className="text-xs text-muted-foreground">Attendance</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold">{course.avgGrade}%</p>
                      <p className="text-xs text-muted-foreground">Avg Grade</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold">{course.assignments}</p>
                      <p className="text-xs text-muted-foreground">Assignments</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["lecturer"])(LecturerCoursesPage)
