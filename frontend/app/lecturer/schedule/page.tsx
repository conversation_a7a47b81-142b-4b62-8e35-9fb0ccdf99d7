"use client"

import { useState } from "react"
import { withR<PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  BookOpen, 
  Calendar, 
  ClipboardCheck, 
  FileText, 
  Home, 
  Search, 
  TrendingUp, 
  Users,
  Plus,
  Edit,
  Eye,
  Clock,
  MapPin,
  CalendarDays,
  ChevronLeft,
  ChevronRight,
  Filter
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const navItems = [
  { title: "Dashboard", href: "/lecturer", icon: Home },
  { title: "My Courses", href: "/lecturer/courses", icon: BookOpen },
  { title: "Attendance", href: "/lecturer/attendance", icon: Clip<PERSON><PERSON>heck },
  { title: "Gradebook", href: "/lecturer/gradebook", icon: TrendingUp },
  { title: "Assignments", href: "/lecturer/assignments", icon: FileText },
  { title: "Schedule", href: "/lecturer/schedule", icon: Calendar },
]

function LecturerSchedulePage() {
  const [currentWeek, setCurrentWeek] = useState(0)
  const [viewMode, setViewMode] = useState("week")

  const scheduleData = [
    {
      id: "SCH001",
      course: "CS301",
      title: "Data Structures and Algorithms",
      type: "Lecture",
      day: "Monday",
      time: "09:00-10:30",
      room: "LH-101",
      students: 45,
      status: "scheduled",
    },
    {
      id: "SCH002",
      course: "CS301",
      title: "Data Structures and Algorithms",
      type: "Lecture",
      day: "Wednesday",
      time: "09:00-10:30",
      room: "LH-101",
      students: 45,
      status: "scheduled",
    },
    {
      id: "SCH003",
      course: "CS302",
      title: "Algorithm Design and Analysis",
      type: "Lecture",
      day: "Tuesday",
      time: "11:00-12:30",
      room: "LH-205",
      students: 38,
      status: "scheduled",
    },
    {
      id: "SCH004",
      course: "CS302",
      title: "Algorithm Design and Analysis",
      type: "Lecture",
      day: "Thursday",
      time: "11:00-12:30",
      room: "LH-205",
      students: 38,
      status: "scheduled",
    },
    {
      id: "SCH005",
      course: "CS401",
      title: "Machine Learning Fundamentals",
      type: "Lecture",
      day: "Monday",
      time: "14:00-15:00",
      room: "LAB-3",
      students: 28,
      status: "scheduled",
    },
    {
      id: "SCH006",
      course: "CS401",
      title: "Machine Learning Fundamentals",
      type: "Lab",
      day: "Wednesday",
      time: "14:00-16:00",
      room: "LAB-3",
      students: 28,
      status: "scheduled",
    },
    {
      id: "SCH007",
      course: "CS401",
      title: "Machine Learning Fundamentals",
      type: "Lecture",
      day: "Friday",
      time: "14:00-15:00",
      room: "LAB-3",
      students: 28,
      status: "scheduled",
    },
  ]

  const upcomingSessions = [
    { course: "CS301", title: "Data Structures", time: "09:00 AM", room: "LH-101", date: "Today", type: "Lecture" },
    { course: "CS302", title: "Algorithms", time: "11:00 AM", room: "LH-205", date: "Today", type: "Lecture" },
    { course: "CS401", title: "Machine Learning", time: "02:00 PM", room: "LAB-3", date: "Tomorrow", type: "Lab" },
  ]

  const weeklySchedule = {
    Monday: scheduleData.filter(s => s.day === "Monday"),
    Tuesday: scheduleData.filter(s => s.day === "Tuesday"),
    Wednesday: scheduleData.filter(s => s.day === "Wednesday"),
    Thursday: scheduleData.filter(s => s.day === "Thursday"),
    Friday: scheduleData.filter(s => s.day === "Friday"),
    Saturday: [],
    Sunday: [],
  }

  const getTypeBadge = (type: string) => {
    const variants = {
      Lecture: { variant: "default" as const, className: "bg-primary text-primary-foreground" },
      Lab: { variant: "secondary" as const, className: "bg-info text-info-foreground" },
      Seminar: { variant: "outline" as const, className: "" },
      Tutorial: { variant: "outline" as const, className: "" },
    }
    return variants[type as keyof typeof variants] || variants.Lecture
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      scheduled: { variant: "default" as const, className: "bg-success text-success-foreground" },
      completed: { variant: "secondary" as const, className: "" },
      cancelled: { variant: "destructive" as const, className: "bg-destructive text-destructive-foreground" },
    }
    return variants[status as keyof typeof variants] || variants.scheduled
  }

  const totalSessions = scheduleData.length
  const totalHours = scheduleData.reduce((sum, session) => {
    const [start, end] = session.time.split("-")
    const startTime = parseInt(start.split(":")[0]) + parseInt(start.split(":")[1]) / 60
    const endTime = parseInt(end.split(":")[0]) + parseInt(end.split(":")[1]) / 60
    return sum + (endTime - startTime)
  }, 0)

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Teaching Schedule</h1>
            <p className="text-muted-foreground">Manage your teaching schedule and sessions</p>
          </div>
          <div className="flex gap-2">
            <Select value={viewMode} onValueChange={setViewMode}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">Week</SelectItem>
                <SelectItem value="month">Month</SelectItem>
                <SelectItem value="list">List</SelectItem>
              </SelectContent>
            </Select>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Session
            </Button>
          </div>
        </div>

        {/* Schedule Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalSessions}</div>
              <p className="text-xs text-muted-foreground">Per week</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Teaching Hours</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalHours.toFixed(1)}h</div>
              <p className="text-xs text-muted-foreground">Weekly hours</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{scheduleData.reduce((sum, s) => sum + s.students, 0)}</div>
              <p className="text-xs text-muted-foreground">Across all courses</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Rooms Used</CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{new Set(scheduleData.map(s => s.room)).size}</div>
              <p className="text-xs text-muted-foreground">Different locations</p>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Sessions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarDays className="h-5 w-5" />
              Upcoming Sessions
            </CardTitle>
            <CardDescription>Your next teaching sessions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingSessions.map((session, i) => (
              <div key={i} className="flex items-center justify-between rounded-lg border border-border p-3">
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                    <BookOpen className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">{session.title}</p>
                    <p className="text-sm text-muted-foreground">{session.course} • {session.type}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{session.time}</p>
                  <p className="text-xs text-muted-foreground">{session.room}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Weekly Schedule */}
        {viewMode === "week" && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Weekly Schedule</CardTitle>
                  <CardDescription>Your teaching schedule for this week</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm font-medium">Week {currentWeek + 1}</span>
                  <Button variant="outline" size="sm">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-7">
                {Object.entries(weeklySchedule).map(([day, sessions]) => (
                  <div key={day} className="space-y-2">
                    <h3 className="font-semibold text-center">{day}</h3>
                    <div className="space-y-2">
                      {sessions.length === 0 ? (
                        <div className="text-center text-muted-foreground text-sm py-4">
                          No sessions
                        </div>
                      ) : (
                        sessions.map((session) => {
                          const typeBadge = getTypeBadge(session.type)
                          return (
                            <div key={session.id} className="rounded-lg border border-border p-2 text-xs">
                              <div className="font-medium">{session.course}</div>
                              <div className="text-muted-foreground">{session.time}</div>
                              <div className="text-muted-foreground">{session.room}</div>
                              <Badge variant={typeBadge.variant} className={`mt-1 ${typeBadge.className}`}>
                                {session.type}
                              </Badge>
                            </div>
                          )
                        })
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Schedule List View */}
        {viewMode === "list" && (
          <Card>
            <CardHeader>
              <CardTitle>All Sessions</CardTitle>
              <CardDescription>Complete list of your teaching sessions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scheduleData.map((session) => {
                  const typeBadge = getTypeBadge(session.type)
                  const statusBadge = getStatusBadge(session.status)
                  return (
                    <div key={session.id} className="flex items-center justify-between rounded-lg border border-border p-4">
                      <div className="flex items-center gap-4">
                        <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                          <BookOpen className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold">{session.title}</h3>
                          <p className="text-sm text-muted-foreground">{session.course} • {session.day}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <p className="text-sm font-medium">{session.time}</p>
                          <p className="text-xs text-muted-foreground">{session.room}</p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm font-medium">{session.students}</p>
                          <p className="text-xs text-muted-foreground">students</p>
                        </div>
                        <div className="flex gap-2">
                          <Badge variant={typeBadge.variant} className={typeBadge.className}>
                            {session.type}
                          </Badge>
                          <Badge variant={statusBadge.variant} className={statusBadge.className}>
                            {session.status}
                          </Badge>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="mr-1 h-3 w-3" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="mr-1 h-3 w-3" />
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Room Utilization */}
        <Card>
          <CardHeader>
            <CardTitle>Room Utilization</CardTitle>
            <CardDescription>Usage statistics for your assigned rooms</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {Array.from(new Set(scheduleData.map(s => s.room))).map((room) => {
                const roomSessions = scheduleData.filter(s => s.room === room)
                const totalHours = roomSessions.reduce((sum, session) => {
                  const [start, end] = session.time.split("-")
                  const startTime = parseInt(start.split(":")[0]) + parseInt(start.split(":")[1]) / 60
                  const endTime = parseInt(end.split(":")[0]) + parseInt(end.split(":")[1]) / 60
                  return sum + (endTime - startTime)
                }, 0)
                
                return (
                  <div key={room} className="rounded-lg border border-border p-4">
                    <h3 className="font-semibold">{room}</h3>
                    <p className="text-sm text-muted-foreground">{roomSessions.length} sessions</p>
                    <p className="text-sm text-muted-foreground">{totalHours.toFixed(1)} hours/week</p>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["lecturer"])(LecturerSchedulePage)
