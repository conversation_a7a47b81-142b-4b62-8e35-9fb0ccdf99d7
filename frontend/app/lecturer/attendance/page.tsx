"use client"

import { useState } from "react"
import { withR<PERSON> } from "@/lib/with-role"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { BookOpen, Calendar, ClipboardCheck, FileText, Home, QrCode, Search, TrendingUp } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const navItems = [
  { title: "Dashboard", href: "/lecturer", icon: Home },
  { title: "My Courses", href: "/lecturer/courses", icon: BookOpen },
  { title: "Attendance", href: "/lecturer/attendance", icon: ClipboardCheck },
  { title: "Gradebook", href: "/lecturer/gradebook", icon: TrendingUp },
  { title: "Assignments", href: "/lecturer/assignments", icon: FileText },
  { title: "Schedule", href: "/lecturer/schedule", icon: Calendar },
]

function AttendancePage() {
  const [selectedCourse, setSelectedCourse] = useState("CS301")
  const [showQR, setShowQR] = useState(false)

  const students = [
    { id: "STU001", name: "Alice Johnson", attendance: 22, total: 24, percentage: 92 },
    { id: "STU002", name: "Bob Smith", attendance: 20, total: 24, percentage: 83 },
    { id: "STU003", name: "Carol Williams", attendance: 24, total: 24, percentage: 100 },
    { id: "STU004", name: "David Brown", attendance: 18, total: 24, percentage: 75 },
    { id: "STU005", name: "Emma Davis", attendance: 23, total: 24, percentage: 96 },
    { id: "STU006", name: "Frank Miller", attendance: 21, total: 24, percentage: 88 },
  ]

  const getAttendanceColor = (percentage: number) => {
    if (percentage >= 90) return "text-success"
    if (percentage >= 75) return "text-warning"
    return "text-destructive"
  }

  return (
    <DashboardLayout navItems={navItems}>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Attendance Tracking</h1>
            <p className="text-muted-foreground">Monitor and record student attendance</p>
          </div>
          <Button onClick={() => setShowQR(!showQR)}>
            <QrCode className="mr-2 h-4 w-4" />
            {showQR ? "Hide QR Code" : "Generate QR Code"}
          </Button>
        </div>

        {showQR && (
          <Card className="border-primary">
            <CardHeader>
              <CardTitle>Session QR Code</CardTitle>
              <CardDescription>Students scan this code to mark attendance</CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center gap-4">
              <div className="flex h-64 w-64 items-center justify-center rounded-lg bg-white p-4">
                <div className="grid h-full w-full grid-cols-8 gap-1">
                  {Array.from({ length: 64 }).map((_, i) => (
                    <div key={i} className={`${Math.random() > 0.5 ? "bg-black" : "bg-white"}`} />
                  ))}
                </div>
              </div>
              <div className="text-center">
                <p className="font-mono text-sm">Session ID: CS301-20250106-0900</p>
                <p className="text-xs text-muted-foreground">Valid for 15 minutes</p>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="flex flex-col gap-4 md:flex-row">
          <Select value={selectedCourse} onValueChange={setSelectedCourse}>
            <SelectTrigger className="w-full md:w-64">
              <SelectValue placeholder="Select course" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="CS301">CS301 - Data Structures</SelectItem>
              <SelectItem value="CS302">CS302 - Algorithms</SelectItem>
              <SelectItem value="CS401">CS401 - Machine Learning</SelectItem>
            </SelectContent>
          </Select>
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input placeholder="Search students..." className="pl-10" />
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Average Attendance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">88%</div>
              <p className="text-xs text-muted-foreground">Across all students</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">At Risk Students</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-destructive">3</div>
              <p className="text-xs text-muted-foreground">Below 75% attendance</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Perfect Attendance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-success">8</div>
              <p className="text-xs text-muted-foreground">100% attendance rate</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Student Attendance Records</CardTitle>
            <CardDescription>CS301 - Data Structures (24 sessions)</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border text-left text-sm text-muted-foreground">
                    <th className="pb-3 font-medium">Student ID</th>
                    <th className="pb-3 font-medium">Name</th>
                    <th className="pb-3 font-medium">Present</th>
                    <th className="pb-3 font-medium">Total</th>
                    <th className="pb-3 font-medium">Percentage</th>
                    <th className="pb-3 font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {students.map((student) => (
                    <tr key={student.id} className="border-b border-border last:border-0">
                      <td className="py-3 font-mono text-sm">{student.id}</td>
                      <td className="py-3">{student.name}</td>
                      <td className="py-3">{student.attendance}</td>
                      <td className="py-3">{student.total}</td>
                      <td className={`py-3 font-semibold ${getAttendanceColor(student.percentage)}`}>
                        {student.percentage}%
                      </td>
                      <td className="py-3">
                        <Badge
                          variant={
                            student.percentage >= 90
                              ? "default"
                              : student.percentage >= 75
                                ? "secondary"
                                : "destructive"
                          }
                        >
                          {student.percentage >= 90 ? "Excellent" : student.percentage >= 75 ? "Good" : "At Risk"}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

export default withRole(["lecturer"])(AttendancePage)
