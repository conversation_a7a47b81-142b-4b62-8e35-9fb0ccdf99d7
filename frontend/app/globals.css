@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* University portal theme - professional dark with blue accent */
  --background: oklch(0.98 0 0);
  --foreground: oklch(0.15 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0 0);
  --primary: oklch(0.55 0.18 250);
  --primary-foreground: oklch(0.99 0 0);
  --secondary: oklch(0.95 0.01 250);
  --secondary-foreground: oklch(0.15 0 0);
  --muted: oklch(0.96 0.005 250);
  --muted-foreground: oklch(0.5 0.01 250);
  --accent: oklch(0.94 0.02 250);
  --accent-foreground: oklch(0.15 0 0);
  --destructive: oklch(0.58 0.22 25);
  --destructive-foreground: oklch(0.99 0 0);
  --border: oklch(0.9 0.01 250);
  --input: oklch(0.9 0.01 250);
  --ring: oklch(0.55 0.18 250);
  --chart-1: oklch(0.55 0.18 250);
  --chart-2: oklch(0.65 0.2 180);
  --chart-3: oklch(0.7 0.15 140);
  --chart-4: oklch(0.6 0.22 300);
  --chart-5: oklch(0.68 0.18 60);
  --radius: 0.5rem;
  --sidebar: oklch(0.99 0 0);
  --sidebar-foreground: oklch(0.15 0 0);
  --sidebar-primary: oklch(0.55 0.18 250);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.95 0.01 250);
  --sidebar-accent-foreground: oklch(0.15 0 0);
  --sidebar-border: oklch(0.92 0.01 250);
  --sidebar-ring: oklch(0.55 0.18 250);
  --success: oklch(0.65 0.18 150);
  --success-foreground: oklch(0.99 0 0);
  --warning: oklch(0.75 0.15 80);
  --warning-foreground: oklch(0.15 0 0);
  --info: oklch(0.6 0.18 230);
  --info-foreground: oklch(0.99 0 0);
}

.dark {
  /* Dark theme inspired by Supabase/Vercel - deep blacks with subtle borders */
  --background: oklch(0.12 0.01 250);
  --foreground: oklch(0.95 0.005 250);
  --card: oklch(0.15 0.01 250);
  --card-foreground: oklch(0.95 0.005 250);
  --popover: oklch(0.13 0.01 250);
  --popover-foreground: oklch(0.95 0.005 250);
  --primary: oklch(0.6 0.2 250);
  --primary-foreground: oklch(0.99 0 0);
  --secondary: oklch(0.2 0.02 250);
  --secondary-foreground: oklch(0.95 0.005 250);
  --muted: oklch(0.22 0.02 250);
  --muted-foreground: oklch(0.6 0.02 250);
  --accent: oklch(0.24 0.03 250);
  --accent-foreground: oklch(0.95 0.005 250);
  --destructive: oklch(0.5 0.2 25);
  --destructive-foreground: oklch(0.95 0.005 250);
  --border: oklch(0.25 0.02 250);
  --input: oklch(0.25 0.02 250);
  --ring: oklch(0.6 0.2 250);
  --chart-1: oklch(0.6 0.2 250);
  --chart-2: oklch(0.65 0.2 180);
  --chart-3: oklch(0.7 0.18 140);
  --chart-4: oklch(0.62 0.22 300);
  --chart-5: oklch(0.68 0.18 60);
  --sidebar: oklch(0.14 0.01 250);
  --sidebar-foreground: oklch(0.95 0.005 250);
  --sidebar-primary: oklch(0.6 0.2 250);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.2 0.02 250);
  --sidebar-accent-foreground: oklch(0.95 0.005 250);
  --sidebar-border: oklch(0.22 0.02 250);
  --sidebar-ring: oklch(0.6 0.2 250);
  --success: oklch(0.6 0.18 150);
  --success-foreground: oklch(0.99 0 0);
  --warning: oklch(0.7 0.15 80);
  --warning-foreground: oklch(0.12 0.01 250);
  --info: oklch(0.58 0.18 230);
  --info-foreground: oklch(0.99 0 0);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
