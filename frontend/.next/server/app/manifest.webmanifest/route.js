/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/manifest.webmanifest/route";
exports.ids = ["app/manifest.webmanifest/route"];
exports.modules = {

/***/ "(rsc)/./app/manifest.ts":
/*!*************************!*\
  !*** ./app/manifest.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ manifest)\n/* harmony export */ });\nfunction manifest() {\n    return {\n        name: \"University Portal\",\n        short_name: \"UniPortal\",\n        description: \"Comprehensive university management system for students, lecturers, and administrators\",\n        start_url: \"/\",\n        display: \"standalone\",\n        background_color: \"#ffffff\",\n        theme_color: \"#0070f3\",\n        orientation: \"portrait\",\n        icons: [\n            {\n                src: \"/icon-192.jpg\",\n                sizes: \"192x192\",\n                type: \"image/png\",\n                purpose: \"any maskable\"\n            },\n            {\n                src: \"/icon-512.jpg\",\n                sizes: \"512x512\",\n                type: \"image/png\",\n                purpose: \"any maskable\"\n            }\n        ],\n        categories: [\n            \"education\",\n            \"productivity\"\n        ],\n        shortcuts: [\n            {\n                name: \"Dashboard\",\n                short_name: \"Dashboard\",\n                description: \"Go to your dashboard\",\n                url: \"/\",\n                icons: [\n                    {\n                        src: \"/icon-192.jpg\",\n                        sizes: \"192x192\"\n                    }\n                ]\n            },\n            {\n                name: \"Courses\",\n                short_name: \"Courses\",\n                description: \"View your courses\",\n                url: \"/student/courses\",\n                icons: [\n                    {\n                        src: \"/icon-192.jpg\",\n                        sizes: \"192x192\"\n                    }\n                ]\n            }\n        ]\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/manifest.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_filePath_2Fhome_2Fjoshuamuli_2FDownloads_2FuniversityPortal_2Funiversity_portal_2Ffrontend_2Fapp_2Fmanifest_ts_isDynamicRouteExtension_1_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?filePath=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp%2Fmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp%2Fmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/manifest.webmanifest/route\",\n        pathname: \"/manifest.webmanifest\",\n        filename: \"manifest\",\n        bundlePath: \"app/manifest.webmanifest/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?filePath=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp%2Fmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_filePath_2Fhome_2Fjoshuamuli_2FDownloads_2FuniversityPortal_2Funiversity_portal_2Ffrontend_2Fapp_2Fmanifest_ts_isDynamicRouteExtension_1_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp%2Fmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp%2Fmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__ ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _home_joshuamuli_Downloads_universityPortal_university_portal_frontend_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app/manifest.ts */ \"(rsc)/./app/manifest.ts\");\n/* harmony import */ var next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/webpack/loaders/metadata/resolve-route-data */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/metadata/resolve-route-data.js\");\n/* harmony import */ var next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__);\n/* dynamic asset route */\n\n\n\n\nconst contentType = \"application/manifest+json\"\nconst fileType = \"manifest\"\n\n\n  if (typeof _home_joshuamuli_Downloads_universityPortal_university_portal_frontend_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__[\"default\"] !== 'function') {\n    throw new Error('Default export is missing in \"/home/<USER>/Downloads/universityPortal/university-portal/frontend/app/manifest.ts\"')\n  }\n  \n\n\nasync function GET() {\n  const data = await (0,_home_joshuamuli_Downloads_universityPortal_university_portal_frontend_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()\n  const content = (0,next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__.resolveRouteData)(data, fileType)\n\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(content, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLXJvdXRlLWxvYWRlci5qcz9maWxlUGF0aD0lMkZob21lJTJGam9zaHVhbXVsaSUyRkRvd25sb2FkcyUyRnVuaXZlcnNpdHlQb3J0YWwlMkZ1bml2ZXJzaXR5LXBvcnRhbCUyRmZyb250ZW5kJTJGYXBwJTJGbWFuaWZlc3QudHMmaXNEeW5hbWljUm91dGVFeHRlbnNpb249MSE/X19uZXh0X21ldGFkYXRhX3JvdXRlX18iLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUMwQztBQUNrRTtBQUNkOztBQUU5RjtBQUNBOzs7QUFHQSxhQUFhLDhIQUFPO0FBQ3BCO0FBQ0E7QUFDQTs7O0FBR087QUFDUCxxQkFBcUIsa0lBQU87QUFDNUIsa0JBQWtCLDZHQUFnQjs7QUFFbEMsYUFBYSxxREFBWTtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIIiwic291cmNlcyI6WyI/X19uZXh0X21ldGFkYXRhX3JvdXRlX18iXSwic291cmNlc0NvbnRlbnQiOlsiLyogZHluYW1pYyBhc3NldCByb3V0ZSAqL1xuaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgaGFuZGxlciBmcm9tIFwiL2hvbWUvam9zaHVhbXVsaS9Eb3dubG9hZHMvdW5pdmVyc2l0eVBvcnRhbC91bml2ZXJzaXR5LXBvcnRhbC9mcm9udGVuZC9hcHAvbWFuaWZlc3QudHNcIlxuaW1wb3J0IHsgcmVzb2x2ZVJvdXRlRGF0YSB9IGZyb20gJ25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbWV0YWRhdGEvcmVzb2x2ZS1yb3V0ZS1kYXRhJ1xuXG5jb25zdCBjb250ZW50VHlwZSA9IFwiYXBwbGljYXRpb24vbWFuaWZlc3QranNvblwiXG5jb25zdCBmaWxlVHlwZSA9IFwibWFuaWZlc3RcIlxuXG5cbiAgaWYgKHR5cGVvZiBoYW5kbGVyICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdEZWZhdWx0IGV4cG9ydCBpcyBtaXNzaW5nIGluIFwiL2hvbWUvam9zaHVhbXVsaS9Eb3dubG9hZHMvdW5pdmVyc2l0eVBvcnRhbC91bml2ZXJzaXR5LXBvcnRhbC9mcm9udGVuZC9hcHAvbWFuaWZlc3QudHNcIicpXG4gIH1cbiAgXG5cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVCgpIHtcbiAgY29uc3QgZGF0YSA9IGF3YWl0IGhhbmRsZXIoKVxuICBjb25zdCBjb250ZW50ID0gcmVzb2x2ZVJvdXRlRGF0YShkYXRhLCBmaWxlVHlwZSlcblxuICByZXR1cm4gbmV3IE5leHRSZXNwb25zZShjb250ZW50LCB7XG4gICAgaGVhZGVyczoge1xuICAgICAgJ0NvbnRlbnQtVHlwZSc6IGNvbnRlbnRUeXBlLFxuICAgICAgJ0NhY2hlLUNvbnRyb2wnOiBcInB1YmxpYywgbWF4LWFnZT0wLCBtdXN0LXJldmFsaWRhdGVcIixcbiAgICB9LFxuICB9KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp%2Fmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fjoshuamuli%2FDownloads%2FuniversityPortal%2Funiversity-portal%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();