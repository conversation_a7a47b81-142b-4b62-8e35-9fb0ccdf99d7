"use client";

// src/nextjs/index.tsx
import React, { Suspense } from "react";

// src/react.tsx
import { useEffect } from "react";

// package.json
var name = "@vercel/analytics";
var version = "1.3.1";

// src/queue.ts
var initQueue = () => {
  if (window.va)
    return;
  window.va = function a(...params) {
    (window.vaq = window.vaq || []).push(params);
  };
};

// src/utils.ts
function isBrowser() {
  return typeof window !== "undefined";
}
function detectEnvironment() {
  try {
    const env = process.env.NODE_ENV;
    if (env === "development" || env === "test") {
      return "development";
    }
  } catch (e) {
  }
  return "production";
}
function setMode(mode = "auto") {
  if (mode === "auto") {
    window.vam = detectEnvironment();
    return;
  }
  window.vam = mode;
}
function getMode() {
  const mode = isBrowser() ? window.vam : detectEnvironment();
  return mode || "production";
}
function isDevelopment() {
  return getMode() === "development";
}
function computeRoute(pathname, pathParams) {
  if (!pathname || !pathParams) {
    return pathname;
  }
  let result = pathname;
  try {
    const entries = Object.entries(pathParams);
    for (const [key, value] of entries) {
      if (!Array.isArray(value)) {
        const matcher = turnValueToRegExp(value);
        if (matcher.test(result)) {
          result = result.replace(matcher, `/[${key}]`);
        }
      }
    }
    for (const [key, value] of entries) {
      if (Array.isArray(value)) {
        const matcher = turnValueToRegExp(value.join("/"));
        if (matcher.test(result)) {
          result = result.replace(matcher, `/[...${key}]`);
        }
      }
    }
    return result;
  } catch (e) {
    return pathname;
  }
}
function turnValueToRegExp(value) {
  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);
}
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

// src/generic.ts
var DEV_SCRIPT_URL = "https://va.vercel-scripts.com/v1/script.debug.js";
var PROD_SCRIPT_URL = "/_vercel/insights/script.js";
function inject(props = {
  debug: true
}) {
  var _a;
  if (!isBrowser())
    return;
  setMode(props.mode);
  initQueue();
  if (props.beforeSend) {
    (_a = window.va) == null ? void 0 : _a.call(window, "beforeSend", props.beforeSend);
  }
  const src = props.scriptSrc || (isDevelopment() ? DEV_SCRIPT_URL : PROD_SCRIPT_URL);
  if (document.head.querySelector(`script[src*="${src}"]`))
    return;
  const script = document.createElement("script");
  script.src = src;
  script.defer = true;
  script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : "");
  script.dataset.sdkv = version;
  if (props.disableAutoTrack) {
    script.dataset.disableAutoTrack = "1";
  }
  if (props.endpoint) {
    script.dataset.endpoint = props.endpoint;
  }
  if (props.dsn) {
    script.dataset.dsn = props.dsn;
  }
  script.onerror = () => {
    const errorMessage = isDevelopment() ? "Please check if any ad blockers are enabled and try again." : "Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";
    console.log(
      `[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`
    );
  };
  if (isDevelopment() && props.debug === false) {
    script.dataset.debug = "false";
  }
  document.head.appendChild(script);
}
function pageview({ route, path }) {
  var _a;
  (_a = window.va) == null ? void 0 : _a.call(window, "pageview", {
    route,
    path
  });
}

// src/react.tsx
function Analytics(props) {
  useEffect(() => {
    inject({
      framework: props.framework || "react",
      ...props.route !== void 0 && { disableAutoTrack: true },
      ...props
    });
  }, []);
  useEffect(() => {
    if (props.route && props.path) {
      pageview({
        route: props.route,
        path: props.path
      });
    }
  }, [props.route, props.path]);
  return null;
}

// src/nextjs/utils.ts
import { useParams, usePathname, useSearchParams } from "next/navigation.js";
var useRoute = () => {
  const params = useParams();
  const searchParams = useSearchParams();
  const path = usePathname();
  const finalParams = {
    ...Object.fromEntries(searchParams.entries()),
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- can be empty in pages router
    ...params || {}
  };
  return {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- can be empty in pages router
    route: params ? computeRoute(path, finalParams) : null,
    path
  };
};

// src/nextjs/index.tsx
function AnalyticsComponent(props) {
  const { route, path } = useRoute();
  return /* @__PURE__ */ React.createElement(Analytics, { path, route, ...props, framework: "next" });
}
function Analytics2(props) {
  return /* @__PURE__ */ React.createElement(Suspense, { fallback: null }, /* @__PURE__ */ React.createElement(AnalyticsComponent, { ...props }));
}
export {
  Analytics2 as Analytics
};
//# sourceMappingURL=index.mjs.map