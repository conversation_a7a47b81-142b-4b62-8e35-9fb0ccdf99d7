{"name": "@vercel/analytics", "version": "1.3.1", "description": "Gain real-time traffic insights with Vercel Web Analytics", "keywords": ["analytics", "vercel"], "repository": {"url": "github:vercel/analytics", "directory": "packages/web"}, "license": "MPL-2.0", "exports": {"./package.json": "./package.json", ".": {"browser": "./dist/index.mjs", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./react": {"browser": "./dist/react/index.mjs", "import": "./dist/react/index.mjs", "require": "./dist/react/index.js"}, "./next": {"browser": "./dist/next/index.mjs", "import": "./dist/next/index.mjs", "require": "./dist/next/index.js"}, "./server": {"node": "./dist/server/index.js", "edge-light": "./dist/server/index.mjs", "import": "./dist/server/index.mjs", "require": "./dist/server/index.js", "default": "./dist/server/index.js"}}, "main": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "react": ["dist/react/index.d.ts"], "server": ["dist/server/index.d.ts"], "next": ["dist/next/index.d.ts"]}}, "eslintConfig": {"extends": ["@vercel/eslint-config"], "rules": {"tsdoc/syntax": "off"}, "ignorePatterns": ["jest.setup.ts"]}, "dependencies": {"server-only": "^0.0.1"}, "devDependencies": {"@swc/core": "^1.3.66", "@swc/jest": "^0.2.26", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/react": "^18.2.14", "@types/testing-library__jest-dom": "^5.14.6", "@vercel/eslint-config": "0.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tsup": "7.1.0"}, "peerDependencies": {"next": ">= 13", "react": "^18 || ^19"}, "peerDependenciesMeta": {"next": {"optional": true}, "react": {"optional": true}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint .", "lint-fix": "eslint . --fix", "test": "jest", "type-check": "tsc --noEmit"}}