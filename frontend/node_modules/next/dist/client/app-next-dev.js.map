{"version": 3, "sources": ["../../src/client/app-next-dev.ts"], "sourcesContent": ["// TODO-APP: hydration warning\n\nimport './app-webpack'\nimport { appBootstrap } from './app-bootstrap'\nimport { initializeDevBuildIndicatorForAppRouter } from './dev/dev-build-indicator/initialize-for-app-router'\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index')\n  hydrate()\n  initializeDevBuildIndicatorForAppRouter()\n})\n"], "names": ["appBootstrap", "hydrate", "require", "initializeDevBuildIndicatorForAppRouter"], "mappings": "AAAA,8BAA8B;;;;;QAEvB;8BACsB;wCAC2B;AAExDA,IAAAA,0BAAY,EAAC;IACX,MAAM,EAAEC,OAAO,EAAE,GAAGC,QAAQ;IAC5BD;IACAE,IAAAA,+DAAuC;AACzC"}