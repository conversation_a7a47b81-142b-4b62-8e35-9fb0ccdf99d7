{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-router-state-patch-to-tree.ts"], "sourcesContent": ["import type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { matchSegment } from '../match-segments'\nimport { addRefreshMarkerToActiveParallelSegments } from './refetch-inactive-parallel-segments'\n\n/**\n * Deep merge of the two router states. Parallel route keys are preserved if the patch doesn't have them.\n */\nfunction applyPatch(\n  initialTree: FlightRouterState,\n  patchTree: FlightRouterState\n): FlightRouterState {\n  const [initialSegment, initialParallelRoutes] = initialTree\n  const [patchSegment, patchParallelRoutes] = patchTree\n\n  // if the applied patch segment is __DEFAULT__ then it can be ignored in favor of the initial tree\n  // this is because the __DEFAULT__ segment is used as a placeholder on navigation\n  if (\n    patchSegment === DEFAULT_SEGMENT_KEY &&\n    initialSegment !== DEFAULT_SEGMENT_KEY\n  ) {\n    return initialTree\n  }\n\n  if (matchSegment(initialSegment, patchSegment)) {\n    const newParallelRoutes: FlightRouterState[1] = {}\n    for (const key in initialParallelRoutes) {\n      const isInPatchTreeParallelRoutes =\n        typeof patchParallelRoutes[key] !== 'undefined'\n      if (isInPatchTreeParallelRoutes) {\n        newParallelRoutes[key] = applyPatch(\n          initialParallelRoutes[key],\n          patchParallelRoutes[key]\n        )\n      } else {\n        newParallelRoutes[key] = initialParallelRoutes[key]\n      }\n    }\n\n    for (const key in patchParallelRoutes) {\n      if (newParallelRoutes[key]) {\n        continue\n      }\n\n      newParallelRoutes[key] = patchParallelRoutes[key]\n    }\n\n    const tree: FlightRouterState = [initialSegment, newParallelRoutes]\n\n    // Copy over the existing tree\n    if (initialTree[2]) {\n      tree[2] = initialTree[2]\n    }\n\n    if (initialTree[3]) {\n      tree[3] = initialTree[3]\n    }\n\n    if (initialTree[4]) {\n      tree[4] = initialTree[4]\n    }\n\n    return tree\n  }\n\n  return patchTree\n}\n\n/**\n * Apply the router state from the Flight response, but skip patching default segments.\n * Useful for patching the router cache when navigating, where we persist the existing default segment if there isn't a new one.\n * Creates a new router state tree.\n */\nexport function applyRouterStatePatchToTree(\n  flightSegmentPath: FlightSegmentPath,\n  flightRouterState: FlightRouterState,\n  treePatch: FlightRouterState,\n  path: string\n): FlightRouterState | null {\n  const [segment, parallelRoutes, url, refetch, isRootLayout] =\n    flightRouterState\n\n  // Root refresh\n  if (flightSegmentPath.length === 1) {\n    const tree: FlightRouterState = applyPatch(flightRouterState, treePatch)\n\n    addRefreshMarkerToActiveParallelSegments(tree, path)\n\n    return tree\n  }\n\n  const [currentSegment, parallelRouteKey] = flightSegmentPath\n\n  // Tree path returned from the server should always match up with the current tree in the browser\n  if (!matchSegment(currentSegment, segment)) {\n    return null\n  }\n\n  const lastSegment = flightSegmentPath.length === 2\n\n  let parallelRoutePatch\n  if (lastSegment) {\n    parallelRoutePatch = applyPatch(parallelRoutes[parallelRouteKey], treePatch)\n  } else {\n    parallelRoutePatch = applyRouterStatePatchToTree(\n      getNextFlightSegmentPath(flightSegmentPath),\n      parallelRoutes[parallelRouteKey],\n      treePatch,\n      path\n    )\n\n    if (parallelRoutePatch === null) {\n      return null\n    }\n  }\n\n  const tree: FlightRouterState = [\n    flightSegmentPath[0],\n    {\n      ...parallelRoutes,\n      [parallelRouteKey]: parallelRoutePatch,\n    },\n    url,\n    refetch,\n  ]\n\n  // Current segment is the root layout\n  if (isRootLayout) {\n    tree[4] = true\n  }\n\n  addRefreshMarkerToActiveParallelSegments(tree, path)\n\n  return tree\n}\n"], "names": ["applyRouterStatePatchToTree", "applyPatch", "initialTree", "patchTree", "initialSegment", "initialParallelRoutes", "patchSegment", "patchParallelRoutes", "DEFAULT_SEGMENT_KEY", "matchSegment", "newParallelRoutes", "key", "isInPatchTreeParallelRoutes", "tree", "flightSegmentPath", "flightRouterState", "treePatch", "path", "segment", "parallelRoutes", "url", "refetch", "isRootLayout", "length", "addRefreshMarkerToActiveParallelSegments", "currentSegment", "parallelRouteKey", "lastSegment", "parallelRoutePatch", "getNextFlightSegmentPath"], "mappings": ";;;;+BA6EgBA;;;eAAAA;;;yBAzEoB;mCACK;+BACZ;iDAC4B;AAEzD;;CAEC,GACD,SAASC,WACPC,WAA8B,EAC9BC,SAA4B;IAE5B,MAAM,CAACC,gBAAgBC,sBAAsB,GAAGH;IAChD,MAAM,CAACI,cAAcC,oBAAoB,GAAGJ;IAE5C,kGAAkG;IAClG,iFAAiF;IACjF,IACEG,iBAAiBE,4BAAmB,IACpCJ,mBAAmBI,4BAAmB,EACtC;QACA,OAAON;IACT;IAEA,IAAIO,IAAAA,2BAAY,EAACL,gBAAgBE,eAAe;QAC9C,MAAMI,oBAA0C,CAAC;QACjD,IAAK,MAAMC,OAAON,sBAAuB;YACvC,MAAMO,8BACJ,OAAOL,mBAAmB,CAACI,IAAI,KAAK;YACtC,IAAIC,6BAA6B;gBAC/BF,iBAAiB,CAACC,IAAI,GAAGV,WACvBI,qBAAqB,CAACM,IAAI,EAC1BJ,mBAAmB,CAACI,IAAI;YAE5B,OAAO;gBACLD,iBAAiB,CAACC,IAAI,GAAGN,qBAAqB,CAACM,IAAI;YACrD;QACF;QAEA,IAAK,MAAMA,OAAOJ,oBAAqB;YACrC,IAAIG,iBAAiB,CAACC,IAAI,EAAE;gBAC1B;YACF;YAEAD,iBAAiB,CAACC,IAAI,GAAGJ,mBAAmB,CAACI,IAAI;QACnD;QAEA,MAAME,OAA0B;YAACT;YAAgBM;SAAkB;QAEnE,8BAA8B;QAC9B,IAAIR,WAAW,CAAC,EAAE,EAAE;YAClBW,IAAI,CAAC,EAAE,GAAGX,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBW,IAAI,CAAC,EAAE,GAAGX,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBW,IAAI,CAAC,EAAE,GAAGX,WAAW,CAAC,EAAE;QAC1B;QAEA,OAAOW;IACT;IAEA,OAAOV;AACT;AAOO,SAASH,4BACdc,iBAAoC,EACpCC,iBAAoC,EACpCC,SAA4B,EAC5BC,IAAY;IAEZ,MAAM,CAACC,SAASC,gBAAgBC,KAAKC,SAASC,aAAa,GACzDP;IAEF,eAAe;IACf,IAAID,kBAAkBS,MAAM,KAAK,GAAG;QAClC,MAAMV,OAA0BZ,WAAWc,mBAAmBC;QAE9DQ,IAAAA,yEAAwC,EAACX,MAAMI;QAE/C,OAAOJ;IACT;IAEA,MAAM,CAACY,gBAAgBC,iBAAiB,GAAGZ;IAE3C,iGAAiG;IACjG,IAAI,CAACL,IAAAA,2BAAY,EAACgB,gBAAgBP,UAAU;QAC1C,OAAO;IACT;IAEA,MAAMS,cAAcb,kBAAkBS,MAAM,KAAK;IAEjD,IAAIK;IACJ,IAAID,aAAa;QACfC,qBAAqB3B,WAAWkB,cAAc,CAACO,iBAAiB,EAAEV;IACpE,OAAO;QACLY,qBAAqB5B,4BACnB6B,IAAAA,2CAAwB,EAACf,oBACzBK,cAAc,CAACO,iBAAiB,EAChCV,WACAC;QAGF,IAAIW,uBAAuB,MAAM;YAC/B,OAAO;QACT;IACF;IAEA,MAAMf,OAA0B;QAC9BC,iBAAiB,CAAC,EAAE;QACpB;YACE,GAAGK,cAAc;YACjB,CAACO,iBAAiB,EAAEE;QACtB;QACAR;QACAC;KACD;IAED,qCAAqC;IACrC,IAAIC,cAAc;QAChBT,IAAI,CAAC,EAAE,GAAG;IACZ;IAEAW,IAAAA,yEAAwC,EAACX,MAAMI;IAE/C,OAAOJ;AACT"}