{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/hot-reloader-client.tsx"], "sourcesContent": ["import type { ReactNode } from 'react'\nimport {\n  use<PERSON>allback,\n  useEffect,\n  startTransition,\n  useMemo,\n  useRef,\n  useSyncExternalStore,\n} from 'react'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport formatWebpackMessages from '../utils/format-webpack-messages'\nimport { useRouter } from '../../navigation'\nimport {\n  ACTION_BEFORE_REFRESH,\n  ACTION_BUILD_ERROR,\n  ACTION_BUILD_OK,\n  ACTION_DEBUG_INFO,\n  ACTION_DEV_INDICATOR,\n  ACTION_REFRESH,\n  ACTION_STATIC_INDICATOR,\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n  ACTION_VERSION_INFO,\n  useErrorOverlayReducer,\n} from '../shared'\nimport { parseStack } from '../utils/parse-stack'\nimport { AppDevOverlay } from './app-dev-overlay'\nimport { useErrorHandler } from '../../errors/use-error-handler'\nimport { RuntimeErrorHandler } from '../../errors/runtime-error-handler'\nimport {\n  useSendMessage,\n  useTurbopack,\n  useWebsocket,\n  useWebsocketPing,\n} from '../utils/use-websocket'\nimport { parseComponentStack } from '../utils/parse-component-stack'\nimport type { VersionInfo } from '../../../../server/dev/parse-version-info'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport { extractModulesFromTurbopackMessage } from '../../../../server/dev/extract-modules-from-turbopack-message'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from '../shared'\nimport type { HydrationErrorState } from '../../errors/hydration-error-info'\nimport type { DebugInfo } from '../types'\nimport { useUntrackedPathname } from '../../navigation-untracked'\nimport { getReactStitchedError } from '../../errors/stitched-error'\nimport { shouldRenderRootLevelErrorOverlay } from '../../../lib/is-error-thrown-while-rendering-rsc'\nimport { handleDevBuildIndicatorHmrEvents } from '../../../dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events'\nimport type { GlobalErrorComponent } from '../../error-boundary'\nimport type { DevIndicatorServerState } from '../../../../server/dev/dev-indicator-server-state'\n\nexport interface Dispatcher {\n  onBuildOk(): void\n  onBuildError(message: string): void\n  onVersionInfo(versionInfo: VersionInfo): void\n  onDebugInfo(debugInfo: DebugInfo): void\n  onBeforeRefresh(): void\n  onRefresh(): void\n  onStaticIndicator(status: boolean): void\n  onDevIndicator(devIndicator: DevIndicatorServerState): void\n}\n\nlet mostRecentCompilationHash: any = null\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now())\nlet reloading = false\nlet startLatency: number | null = null\nlet turbopackLastUpdateLatency: number | null = null\nlet turbopackUpdatedModules: Set<string> = new Set()\n\nlet pendingHotUpdateWebpack = Promise.resolve()\nlet resolvePendingHotUpdateWebpack: () => void = () => {}\nfunction setPendingHotUpdateWebpack() {\n  pendingHotUpdateWebpack = new Promise((resolve) => {\n    resolvePendingHotUpdateWebpack = () => {\n      resolve()\n    }\n  })\n}\n\nexport function waitForWebpackRuntimeHotUpdate() {\n  return pendingHotUpdateWebpack\n}\n\nfunction handleBeforeHotUpdateWebpack(\n  dispatcher: Dispatcher,\n  hasUpdates: boolean\n) {\n  if (hasUpdates) {\n    dispatcher.onBeforeRefresh()\n  }\n}\n\nfunction handleSuccessfulHotUpdateWebpack(\n  dispatcher: Dispatcher,\n  sendMessage: (message: string) => void,\n  updatedModules: ReadonlyArray<string>\n) {\n  resolvePendingHotUpdateWebpack()\n  dispatcher.onBuildOk()\n  reportHmrLatency(sendMessage, updatedModules)\n\n  dispatcher.onRefresh()\n}\n\nfunction reportHmrLatency(\n  sendMessage: (message: string) => void,\n  updatedModules: ReadonlyArray<string>\n) {\n  if (!startLatency) return\n  // turbopack has a debounce for the \"built\" event which we don't want to\n  // incorrectly show in this number, use the last TURBOPACK_MESSAGE time\n  let endLatency = turbopackLastUpdateLatency ?? Date.now()\n  const latency = endLatency - startLatency\n  console.log(`[Fast Refresh] done in ${latency}ms`)\n  sendMessage(\n    JSON.stringify({\n      event: 'client-hmr-latency',\n      id: window.__nextDevClientId,\n      startTime: startLatency,\n      endTime: endLatency,\n      page: window.location.pathname,\n      updatedModules,\n      // Whether the page (tab) was hidden at the time the event occurred.\n      // This can impact the accuracy of the event's timing.\n      isPageHidden: document.visibilityState === 'hidden',\n    })\n  )\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */\nfunction isUpdateAvailable() {\n  if (process.env.TURBOPACK) {\n    return true\n  }\n\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  // @ts-expect-error module.hot exists\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: any) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: any) {\n      if (status === 'idle') {\n        // @ts-expect-error module.hot exists\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    // @ts-expect-error module.hot exists\n    module.hot.addStatusHandler(handler)\n  }\n}\n\nfunction performFullReload(err: any, sendMessage: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  if (reloading) return\n  reloading = true\n  window.location.reload()\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(\n  onBeforeUpdate: (hasUpdates: boolean) => void,\n  onHotUpdateSuccess: (updatedModules: string[]) => void,\n  sendMessage: any,\n  dispatcher: Dispatcher\n) {\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    resolvePendingHotUpdateWebpack()\n    dispatcher.onBuildOk()\n    reportHmrLatency(sendMessage, [])\n    return\n  }\n\n  function handleApplyUpdates(err: any, updatedModules: string[] | null) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || !updatedModules) {\n      if (err) {\n        console.warn(\n          '[Fast Refresh] performing full reload\\n\\n' +\n            \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n            'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n            'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n            'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n            'Fast Refresh requires at least one parent function component in your React tree.'\n        )\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err, sendMessage)\n      return\n    }\n\n    const hasUpdates = Boolean(updatedModules.length)\n    if (typeof onHotUpdateSuccess === 'function') {\n      // Maybe we want to do something.\n      onHotUpdateSuccess(updatedModules)\n    }\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdates(\n        hasUpdates ? () => {} : onBeforeUpdate,\n        hasUpdates ? () => dispatcher.onBuildOk() : onHotUpdateSuccess,\n        sendMessage,\n        dispatcher\n      )\n    } else {\n      dispatcher.onBuildOk()\n      if (process.env.__NEXT_TEST_MODE) {\n        afterApplyUpdates(() => {\n          if (self.__NEXT_HMR_CB) {\n            self.__NEXT_HMR_CB()\n            self.__NEXT_HMR_CB = null\n          }\n        })\n      }\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  // @ts-expect-error module.hot exists\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: any[] | null) => {\n      if (!updatedModules) {\n        return null\n      }\n\n      if (typeof onBeforeUpdate === 'function') {\n        const hasUpdates = Boolean(updatedModules.length)\n        onBeforeUpdate(hasUpdates)\n      }\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      // @ts-expect-error module.hot exists\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: any[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\n/** Handles messages from the sevrer for the App Router. */\nfunction processMessage(\n  obj: HMR_ACTION_TYPES,\n  sendMessage: (message: string) => void,\n  processTurbopackMessage: (msg: TurbopackMsgToBrowser) => void,\n  router: ReturnType<typeof useRouter>,\n  dispatcher: Dispatcher,\n  appIsrManifestRef: ReturnType<typeof useRef>,\n  pathnameRef: ReturnType<typeof useRef>\n) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  function handleErrors(errors: ReadonlyArray<unknown>) {\n    // \"Massage\" webpack messages.\n    const formatted = formatWebpackMessages({\n      errors: errors,\n      warnings: [],\n    })\n\n    // Only show the first error.\n    dispatcher.onBuildError(formatted.errors[0])\n\n    // Also log them to the console.\n    for (let i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n\n    // Do not attempt to reload now.\n    // We will reload on next success instead.\n    if (process.env.__NEXT_TEST_MODE) {\n      if (self.__NEXT_HMR_CB) {\n        self.__NEXT_HMR_CB(formatted.errors[0])\n        self.__NEXT_HMR_CB = null\n      }\n    }\n  }\n\n  function handleHotUpdate() {\n    if (process.env.TURBOPACK) {\n      dispatcher.onBuildOk()\n      reportHmrLatency(sendMessage, [...turbopackUpdatedModules])\n    } else {\n      tryApplyUpdates(\n        function onBeforeHotUpdate(hasUpdates: boolean) {\n          handleBeforeHotUpdateWebpack(dispatcher, hasUpdates)\n        },\n        function onSuccessfulHotUpdate(webpackUpdatedModules: string[]) {\n          // Only dismiss it when we're sure it's a hot update.\n          // Otherwise it would flicker right before the reload.\n          handleSuccessfulHotUpdateWebpack(\n            dispatcher,\n            sendMessage,\n            webpackUpdatedModules\n          )\n        },\n        sendMessage,\n        dispatcher\n      )\n    }\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      if (process.env.__NEXT_DEV_INDICATOR) {\n        if (appIsrManifestRef) {\n          appIsrManifestRef.current = obj.data\n\n          // handle initial status on receiving manifest\n          // navigation is handled in useEffect for pathname changes\n          // as we'll receive the updated manifest before usePathname\n          // triggers for new value\n          if ((pathnameRef.current as string) in obj.data) {\n            dispatcher.onStaticIndicator(true)\n          } else {\n            dispatcher.onStaticIndicator(false)\n          }\n        }\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      startLatency = Date.now()\n      turbopackLastUpdateLatency = null\n      turbopackUpdatedModules.clear()\n      if (!process.env.TURBOPACK) {\n        setPendingHotUpdateWebpack()\n      }\n      console.log('[Fast Refresh] rebuilding')\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      if (obj.hash) {\n        handleAvailableHash(obj.hash)\n      }\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('debug' in obj && obj.debug) dispatcher.onDebugInfo(obj.debug)\n      if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator)\n\n      const hasErrors = Boolean(errors && errors.length)\n      // Compilation with errors (e.g. syntax error or missing modules).\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        handleErrors(errors)\n        return\n      }\n\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        // Print warnings to the console.\n        const formattedMessages = formatWebpackMessages({\n          warnings: warnings,\n          errors: [],\n        })\n\n        for (let i = 0; i < formattedMessages.warnings.length; i++) {\n          if (i === 5) {\n            console.warn(\n              'There were more warnings in other files.\\n' +\n                'You can find a complete log in the terminal.'\n            )\n            break\n          }\n          console.warn(stripAnsi(formattedMessages.warnings[i]))\n        }\n\n        // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: __nextDevClientId,\n        })\n      )\n\n      if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n        // Handle hot updates\n        handleHotUpdate()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n        data: {\n          sessionId: obj.data.sessionId,\n        },\n      })\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      dispatcher.onBeforeRefresh()\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n        data: obj.data,\n      })\n      dispatcher.onRefresh()\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null, sendMessage)\n      }\n      for (const module of extractModulesFromTurbopackMessage(obj.data)) {\n        turbopackUpdatedModules.add(module)\n      }\n      turbopackLastUpdateLatency = Date.now()\n      break\n    }\n    // TODO-APP: make server component change more granular\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      sendMessage(\n        JSON.stringify({\n          event: 'server-component-reload-page',\n          clientId: __nextDevClientId,\n          hash: obj.hash,\n        })\n      )\n\n      // Store the latest hash in a session cookie so that it's sent back to the\n      // server with any subsequent requests.\n      document.cookie = `__next_hmr_refresh_hash__=${obj.hash}`\n\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        if (reloading) return\n        reloading = true\n        return window.location.reload()\n      }\n\n      startTransition(() => {\n        router.hmrRefresh()\n        dispatcher.onRefresh()\n      })\n\n      if (process.env.__NEXT_TEST_MODE) {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      }\n\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n      sendMessage(\n        JSON.stringify({\n          event: 'client-reload-page',\n          clientId: __nextDevClientId,\n        })\n      )\n      if (reloading) return\n      reloading = true\n      return window.location.reload()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE: {\n      // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n      return router.hmrRefresh()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n      return\n    }\n    default: {\n    }\n  }\n}\n\nexport default function HotReload({\n  assetPrefix,\n  children,\n  globalError,\n}: {\n  assetPrefix: string\n  children: ReactNode\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const [state, dispatch] = useErrorOverlayReducer('app')\n\n  const dispatcher = useMemo<Dispatcher>(() => {\n    return {\n      onBuildOk() {\n        dispatch({ type: ACTION_BUILD_OK })\n      },\n      onBuildError(message) {\n        dispatch({ type: ACTION_BUILD_ERROR, message })\n      },\n      onBeforeRefresh() {\n        dispatch({ type: ACTION_BEFORE_REFRESH })\n      },\n      onRefresh() {\n        dispatch({ type: ACTION_REFRESH })\n      },\n      onVersionInfo(versionInfo) {\n        dispatch({ type: ACTION_VERSION_INFO, versionInfo })\n      },\n      onStaticIndicator(status: boolean) {\n        dispatch({ type: ACTION_STATIC_INDICATOR, staticIndicator: status })\n      },\n      onDebugInfo(debugInfo) {\n        dispatch({ type: ACTION_DEBUG_INFO, debugInfo })\n      },\n      onDevIndicator(devIndicator) {\n        dispatch({\n          type: ACTION_DEV_INDICATOR,\n          devIndicator,\n        })\n      },\n    }\n  }, [dispatch])\n\n  //  We render a separate error overlay at the root when an error is thrown from rendering RSC, so\n  //  we should not render an additional error overlay in the descendent. However, we need to\n  //  keep rendering these hooks to ensure HMR works when the error is addressed.\n  const shouldRenderErrorOverlay = useSyncExternalStore(\n    () => () => {},\n    () => !shouldRenderRootLevelErrorOverlay(),\n    () => true\n  )\n\n  const handleOnUnhandledError = useCallback(\n    (error: Error): void => {\n      const errorDetails = (error as any).details as\n        | HydrationErrorState\n        | undefined\n      // Component stack is added to the error in use-error-handler in case there was a hydration error\n      const componentStackTrace =\n        (error as any)._componentStack || errorDetails?.componentStack\n      const warning = errorDetails?.warning\n\n      dispatch({\n        type: ACTION_UNHANDLED_ERROR,\n        reason: error,\n        frames: parseStack(error.stack || ''),\n        componentStackFrames:\n          typeof componentStackTrace === 'string'\n            ? parseComponentStack(componentStackTrace)\n            : undefined,\n        warning,\n      })\n    },\n    [dispatch]\n  )\n\n  const handleOnUnhandledRejection = useCallback(\n    (reason: Error): void => {\n      const stitchedError = getReactStitchedError(reason)\n      dispatch({\n        type: ACTION_UNHANDLED_REJECTION,\n        reason: stitchedError,\n        frames: parseStack(stitchedError.stack || ''),\n      })\n    },\n    [dispatch]\n  )\n  useErrorHandler(handleOnUnhandledError, handleOnUnhandledRejection)\n\n  const webSocketRef = useWebsocket(assetPrefix)\n  useWebsocketPing(webSocketRef)\n  const sendMessage = useSendMessage(webSocketRef)\n  const processTurbopackMessage = useTurbopack(sendMessage, (err) =>\n    performFullReload(err, sendMessage)\n  )\n\n  const router = useRouter()\n\n  // We don't want access of the pathname for the dev tools to trigger a dynamic\n  // access (as the dev overlay will never be present in production).\n  const pathname = useUntrackedPathname()\n  const appIsrManifestRef = useRef<Record<string, false | number>>({})\n  const pathnameRef = useRef(pathname)\n\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    // this conditional is only for dead-code elimination which\n    // isn't a runtime conditional only build-time so ignore hooks rule\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      pathnameRef.current = pathname\n\n      const appIsrManifest = appIsrManifestRef.current\n\n      if (appIsrManifest) {\n        if (pathname && pathname in appIsrManifest) {\n          try {\n            dispatcher.onStaticIndicator(true)\n          } catch (reason) {\n            let message = ''\n\n            if (reason instanceof DOMException) {\n              // Most likely a SecurityError, because of an unavailable localStorage\n              message = reason.stack ?? reason.message\n            } else if (reason instanceof Error) {\n              message = 'Error: ' + reason.message + '\\n' + (reason.stack ?? '')\n            } else {\n              message = 'Unexpected Exception: ' + reason\n            }\n\n            console.warn('[HMR] ' + message)\n          }\n        } else {\n          dispatcher.onStaticIndicator(false)\n        }\n      }\n    }, [pathname, dispatcher])\n  }\n\n  useEffect(() => {\n    const websocket = webSocketRef.current\n    if (!websocket) return\n\n    const handler = (event: MessageEvent<any>) => {\n      try {\n        const obj = JSON.parse(event.data)\n        handleDevBuildIndicatorHmrEvents(obj)\n        processMessage(\n          obj,\n          sendMessage,\n          processTurbopackMessage,\n          router,\n          dispatcher,\n          appIsrManifestRef,\n          pathnameRef\n        )\n      } catch (err: any) {\n        console.warn(\n          '[HMR] Invalid message: ' +\n            JSON.stringify(event.data) +\n            '\\n' +\n            (err?.stack ?? '')\n        )\n      }\n    }\n\n    websocket.addEventListener('message', handler)\n    return () => websocket.removeEventListener('message', handler)\n  }, [\n    sendMessage,\n    router,\n    webSocketRef,\n    dispatcher,\n    processTurbopackMessage,\n    appIsrManifestRef,\n  ])\n\n  if (shouldRenderErrorOverlay) {\n    return (\n      <AppDevOverlay state={state} globalError={globalError}>\n        {children}\n      </AppDevOverlay>\n    )\n  }\n\n  return children\n}\n"], "names": ["HotReload", "waitForWebpackRuntimeHotUpdate", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "startLatency", "turbopackLastUpdateLatency", "turbopackUpdatedModules", "Set", "pendingHotUpdateWebpack", "Promise", "resolve", "resolvePendingHotUpdateWebpack", "setPendingHotUpdateWebpack", "handleBeforeHotUpdateWebpack", "dispatcher", "hasUpdates", "onBeforeRefresh", "handleSuccessfulHotUpdateWebpack", "sendMessage", "updatedModules", "onBuildOk", "reportHmrLatency", "onRefresh", "endLatency", "latency", "console", "log", "JSON", "stringify", "event", "id", "window", "startTime", "endTime", "page", "location", "pathname", "isPageHidden", "document", "visibilityState", "handleAvailableHash", "hash", "isUpdateAvailable", "process", "env", "TURBOPACK", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "stackTrace", "stack", "split", "slice", "join", "message", "hadRuntimeError", "RuntimeError<PERSON>andler", "dependency<PERSON><PERSON>n", "undefined", "reload", "tryApplyUpdates", "onBeforeUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "warn", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "Boolean", "length", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "apply", "processMessage", "obj", "processTurbopackMessage", "router", "appIsrManifestRef", "pathnameRef", "handleErrors", "errors", "formatted", "formatWebpackMessages", "warnings", "onBuildError", "i", "error", "stripAnsi", "handleHotUpdate", "onBeforeHotUpdate", "onSuccessfulHotUpdate", "webpackUpdatedModules", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "ISR_MANIFEST", "__NEXT_DEV_INDICATOR", "current", "data", "onStaticIndicator", "BUILDING", "clear", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "debug", "onDebugInfo", "onDevIndicator", "devIndicator", "hasErrors", "errorCount", "clientId", "hasWarnings", "warningCount", "formattedMessages", "TURBOPACK_CONNECTED", "type", "sessionId", "TURBOPACK_MESSAGE", "extractModulesFromTurbopackMessage", "add", "SERVER_COMPONENT_CHANGES", "cookie", "startTransition", "hmrRefresh", "RELOAD_PAGE", "ADDED_PAGE", "REMOVED_PAGE", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "assetPrefix", "children", "globalError", "state", "dispatch", "useErrorOverlayReducer", "useMemo", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_BEFORE_REFRESH", "ACTION_REFRESH", "ACTION_VERSION_INFO", "ACTION_STATIC_INDICATOR", "staticIndicator", "debugInfo", "ACTION_DEBUG_INFO", "ACTION_DEV_INDICATOR", "shouldRenderErrorOverlay", "useSyncExternalStore", "shouldRenderRootLevelErrorOverlay", "handleOnUnhandledError", "useCallback", "errorDetails", "details", "componentStackTrace", "_componentStack", "componentStack", "warning", "ACTION_UNHANDLED_ERROR", "reason", "frames", "parseStack", "componentStackFrames", "parseComponentStack", "handleOnUnhandledRejection", "stitchedError", "getReactStitchedError", "ACTION_UNHANDLED_REJECTION", "useErrorHandler", "webSocketRef", "useWebsocket", "useWebsocketPing", "useSendMessage", "useTurbopack", "useRouter", "useUntrackedPathname", "useRef", "useEffect", "appIsrManifest", "DOMException", "websocket", "handleDevBuildIndicatorHmrEvents", "addEventListener", "removeEventListener", "AppDevOverlay"], "mappings": ";;;;;;;;;;;;;;;IA0hBA,OAyLC;eAzLuBA;;IAzcRC,8BAA8B;eAA9BA;;;;;uBAzET;oEACe;gFACY;4BACR;wBAanB;4BACoB;+BACG;iCACE;qCACI;8BAM7B;qCAC6B;kCAEQ;oDAKO;qCAId;+BACC;gDACY;kDACD;AAejD,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAChB,IAAIC,eAA8B;AAClC,IAAIC,6BAA4C;AAChD,IAAIC,0BAAuC,IAAIC;AAE/C,IAAIC,0BAA0BC,QAAQC,OAAO;AAC7C,IAAIC,iCAA6C,KAAO;AACxD,SAASC;IACPJ,0BAA0B,IAAIC,QAAQ,CAACC;QACrCC,iCAAiC;YAC/BD;QACF;IACF;AACF;AAEO,SAASf;IACd,OAAOa;AACT;AAEA,SAASK,6BACPC,UAAsB,EACtBC,UAAmB;IAEnB,IAAIA,YAAY;QACdD,WAAWE,eAAe;IAC5B;AACF;AAEA,SAASC,iCACPH,UAAsB,EACtBI,WAAsC,EACtCC,cAAqC;IAErCR;IACAG,WAAWM,SAAS;IACpBC,iBAAiBH,aAAaC;IAE9BL,WAAWQ,SAAS;AACtB;AAEA,SAASD,iBACPH,WAAsC,EACtCC,cAAqC;IAErC,IAAI,CAACf,cAAc;IACnB,wEAAwE;IACxE,uEAAuE;IACvE,IAAImB,aAAalB,qCAAAA,6BAA8BJ,KAAKC,GAAG;IACvD,MAAMsB,UAAUD,aAAanB;IAC7BqB,QAAQC,GAAG,CAAC,AAAC,4BAAyBF,UAAQ;IAC9CN,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPC,IAAIC,OAAOlC,iBAAiB;QAC5BmC,WAAW5B;QACX6B,SAASV;QACTW,MAAMH,OAAOI,QAAQ,CAACC,QAAQ;QAC9BjB;QACA,oEAAoE;QACpE,sDAAsD;QACtDkB,cAAcC,SAASC,eAAe,KAAK;IAC7C;AAEJ;AAEA,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtC7C,4BAA4B6C;AAC9B;AAEA;;;;CAIC,GACD,SAASC;IACP,IAAIC,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,OAAO;IACT;IAEA,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOjD,8BAA8BkD;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,qCAAqC;IACrC,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrB,qCAAqC;gBACrCF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACA,qCAAqC;QACrCJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEvC,WAAgB;IACnD,MAAMwC,aACJD,OACC,CAAA,AAACA,IAAIE,KAAK,IAAIF,IAAIE,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDL,IAAIM,OAAO,IACXN,MAAM,EAAC;IAEXvC,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACP6B;QACAM,iBAAiB,CAAC,CAACC,wCAAmB,CAACD,eAAe;QACtDE,iBAAiBT,MAAMA,IAAIS,eAAe,GAAGC;IAC/C;IAGF,IAAIhE,WAAW;IACfA,YAAY;IACZ4B,OAAOI,QAAQ,CAACiC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,gBACPC,cAA6C,EAC7CC,kBAAsD,EACtDrD,WAAgB,EAChBJ,UAAsB;IAEtB,IAAI,CAAC4B,uBAAuB,CAACK,mBAAmB;QAC9CpC;QACAG,WAAWM,SAAS;QACpBC,iBAAiBH,aAAa,EAAE;QAChC;IACF;IAEA,SAASsD,mBAAmBf,GAAQ,EAAEtC,cAA+B;QACnE,IAAIsC,OAAOQ,wCAAmB,CAACD,eAAe,IAAI,CAAC7C,gBAAgB;YACjE,IAAIsC,KAAK;gBACPhC,QAAQgD,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAIR,wCAAmB,CAACD,eAAe,EAAE;gBAC9CvC,QAAQgD,IAAI,CAACC,4CAAoC;YACnD;YACAlB,kBAAkBC,KAAKvC;YACvB;QACF;QAEA,MAAMH,aAAa4D,QAAQxD,eAAeyD,MAAM;QAChD,IAAI,OAAOL,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBpD;QACrB;QAEA,IAAIuB,qBAAqB;YACvB,+DAA+D;YAC/D2B,gBACEtD,aAAa,KAAO,IAAIuD,gBACxBvD,aAAa,IAAMD,WAAWM,SAAS,KAAKmD,oBAC5CrD,aACAJ;QAEJ,OAAO;YACLA,WAAWM,SAAS;YACpB,IAAIuB,QAAQC,GAAG,CAACiC,gBAAgB,EAAE;gBAChC1B,kBAAkB;oBAChB,IAAI2B,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,qCAAqC;IACrC/B,OAAOC,GAAG,CACP+B,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAAC9D;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOmD,mBAAmB,YAAY;YACxC,MAAMvD,aAAa4D,QAAQxD,eAAeyD,MAAM;YAChDN,eAAevD;QACjB;QACA,2DAA2D;QAC3D,qCAAqC;QACrC,OAAOiC,OAAOC,GAAG,CAACiC,KAAK;IACzB,GACCD,IAAI,CACH,CAAC9D;QACCqD,mBAAmB,MAAMrD;IAC3B,GACA,CAACsC;QACCe,mBAAmBf,KAAK;IAC1B;AAEN;AAEA,yDAAyD,GACzD,SAAS0B,eACPC,GAAqB,EACrBlE,WAAsC,EACtCmE,uBAA6D,EAC7DC,MAAoC,EACpCxE,UAAsB,EACtByE,iBAA4C,EAC5CC,WAAsC;IAEtC,IAAI,CAAE,CAAA,YAAYJ,GAAE,GAAI;QACtB;IACF;IAEA,SAASK,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYC,IAAAA,8BAAqB,EAAC;YACtCF,QAAQA;YACRG,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B/E,WAAWgF,YAAY,CAACH,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAIK,IAAI,GAAGA,IAAIJ,UAAUD,MAAM,CAACd,MAAM,EAAEmB,IAAK;YAChDtE,QAAQuE,KAAK,CAACC,IAAAA,kBAAS,EAACN,UAAUD,MAAM,CAACK,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIpD,QAAQC,GAAG,CAACiC,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACY,UAAUD,MAAM,CAAC,EAAE;gBACtCZ,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,SAASmB;QACP,IAAIvD,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzB/B,WAAWM,SAAS;YACpBC,iBAAiBH,aAAa;mBAAIZ;aAAwB;QAC5D,OAAO;YACL+D,gBACE,SAAS8B,kBAAkBpF,UAAmB;gBAC5CF,6BAA6BC,YAAYC;YAC3C,GACA,SAASqF,sBAAsBC,qBAA+B;gBAC5D,qDAAqD;gBACrD,sDAAsD;gBACtDpF,iCACEH,YACAI,aACAmF;YAEJ,GACAnF,aACAJ;QAEJ;IACF;IAEA,OAAQsE,IAAIkB,MAAM;QAChB,KAAKC,6CAA2B,CAACC,YAAY;YAAE;gBAC7C,IAAI7D,QAAQC,GAAG,CAAC6D,oBAAoB,EAAE;oBACpC,IAAIlB,mBAAmB;wBACrBA,kBAAkBmB,OAAO,GAAGtB,IAAIuB,IAAI;wBAEpC,8CAA8C;wBAC9C,0DAA0D;wBAC1D,2DAA2D;wBAC3D,yBAAyB;wBACzB,IAAI,AAACnB,YAAYkB,OAAO,IAAetB,IAAIuB,IAAI,EAAE;4BAC/C7F,WAAW8F,iBAAiB,CAAC;wBAC/B,OAAO;4BACL9F,WAAW8F,iBAAiB,CAAC;wBAC/B;oBACF;gBACF;gBACA;YACF;QACA,KAAKL,6CAA2B,CAACM,QAAQ;YAAE;gBACzCzG,eAAeH,KAAKC,GAAG;gBACvBG,6BAA6B;gBAC7BC,wBAAwBwG,KAAK;gBAC7B,IAAI,CAACnE,QAAQC,GAAG,CAACC,SAAS,EAAE;oBAC1BjC;gBACF;gBACAa,QAAQC,GAAG,CAAC;gBACZ;YACF;QACA,KAAK6E,6CAA2B,CAACQ,KAAK;QACtC,KAAKR,6CAA2B,CAACS,IAAI;YAAE;gBACrC,IAAI5B,IAAI3C,IAAI,EAAE;oBACZD,oBAAoB4C,IAAI3C,IAAI;gBAC9B;gBAEA,MAAM,EAAEiD,MAAM,EAAEG,QAAQ,EAAE,GAAGT;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKtE,WAAWmG,aAAa,CAAC7B,IAAI8B,WAAW;gBAClE,IAAI,WAAW9B,OAAOA,IAAI+B,KAAK,EAAErG,WAAWsG,WAAW,CAAChC,IAAI+B,KAAK;gBACjE,IAAI,kBAAkB/B,KAAKtE,WAAWuG,cAAc,CAACjC,IAAIkC,YAAY;gBAErE,MAAMC,YAAY5C,QAAQe,UAAUA,OAAOd,MAAM;gBACjD,kEAAkE;gBAClE,IAAI2C,WAAW;oBACbrG,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP2F,YAAY9B,OAAOd,MAAM;wBACzB6C,UAAU5H;oBACZ;oBAGF4F,aAAaC;oBACb;gBACF;gBAEA,MAAMgC,cAAc/C,QAAQkB,YAAYA,SAASjB,MAAM;gBACvD,IAAI8C,aAAa;oBACfxG,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACP8F,cAAc9B,SAASjB,MAAM;wBAC7B6C,UAAU5H;oBACZ;oBAGF,iCAAiC;oBACjC,MAAM+H,oBAAoBhC,IAAAA,8BAAqB,EAAC;wBAC9CC,UAAUA;wBACVH,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAIK,IAAI,GAAGA,IAAI6B,kBAAkB/B,QAAQ,CAACjB,MAAM,EAAEmB,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXtE,QAAQgD,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACAhD,QAAQgD,IAAI,CAACwB,IAAAA,kBAAS,EAAC2B,kBAAkB/B,QAAQ,CAACE,EAAE;oBACtD;gBAEA,uHAAuH;gBACzH;gBAEA7E,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP4F,UAAU5H;gBACZ;gBAGF,IAAIuF,IAAIkB,MAAM,KAAKC,6CAA2B,CAACQ,KAAK,EAAE;oBACpD,qBAAqB;oBACrBb;gBACF;gBACA;YACF;QACA,KAAKK,6CAA2B,CAACsB,mBAAmB;YAAE;gBACpDxC,wBAAwB;oBACtByC,MAAMvB,6CAA2B,CAACsB,mBAAmB;oBACrDlB,MAAM;wBACJoB,WAAW3C,IAAIuB,IAAI,CAACoB,SAAS;oBAC/B;gBACF;gBACA;YACF;QACA,KAAKxB,6CAA2B,CAACyB,iBAAiB;YAAE;gBAClDlH,WAAWE,eAAe;gBAC1BqE,wBAAwB;oBACtByC,MAAMvB,6CAA2B,CAACyB,iBAAiB;oBACnDrB,MAAMvB,IAAIuB,IAAI;gBAChB;gBACA7F,WAAWQ,SAAS;gBACpB,IAAI2C,wCAAmB,CAACD,eAAe,EAAE;oBACvCvC,QAAQgD,IAAI,CAACC,4CAAoC;oBACjDlB,kBAAkB,MAAMtC;gBAC1B;gBACA,KAAK,MAAM8B,WAAUiF,IAAAA,sEAAkC,EAAC7C,IAAIuB,IAAI,EAAG;oBACjErG,wBAAwB4H,GAAG,CAAClF;gBAC9B;gBACA3C,6BAA6BJ,KAAKC,GAAG;gBACrC;YACF;QACA,uDAAuD;QACvD,KAAKqG,6CAA2B,CAAC4B,wBAAwB;YAAE;gBACzDjH,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP4F,UAAU5H;oBACV4C,MAAM2C,IAAI3C,IAAI;gBAChB;gBAGF,0EAA0E;gBAC1E,uCAAuC;gBACvCH,SAAS8F,MAAM,GAAG,AAAC,+BAA4BhD,IAAI3C,IAAI;gBAEvD,IAAIwB,wCAAmB,CAACD,eAAe,EAAE;oBACvC,IAAI7D,WAAW;oBACfA,YAAY;oBACZ,OAAO4B,OAAOI,QAAQ,CAACiC,MAAM;gBAC/B;gBAEAiE,IAAAA,sBAAe,EAAC;oBACd/C,OAAOgD,UAAU;oBACjBxH,WAAWQ,SAAS;gBACtB;gBAEA,IAAIqB,QAAQC,GAAG,CAACiC,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAKwB,6CAA2B,CAACgC,WAAW;YAAE;gBAC5CrH,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACP4F,UAAU5H;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAO4B,OAAOI,QAAQ,CAACiC,MAAM;YAC/B;QACA,KAAKmC,6CAA2B,CAACiC,UAAU;QAC3C,KAAKjC,6CAA2B,CAACkC,YAAY;YAAE;gBAC7C,qFAAqF;gBACrF,OAAOnD,OAAOgD,UAAU;YAC1B;QACA,KAAK/B,6CAA2B,CAACmC,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGvD;gBACtB,IAAIuD,WAAW;oBACb,MAAM,EAAE5E,OAAO,EAAEJ,KAAK,EAAE,GAAGhC,KAAKiH,KAAK,CAACD;oBACtC,MAAM3C,QAAQ,qBAAkB,CAAlB,IAAI6C,MAAM9E,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/BiC,MAAMrC,KAAK,GAAGA;oBACd8B,aAAa;wBAACO;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKO,6CAA2B,CAACuC,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS,CACT;IACF;AACF;AAEe,SAASpJ,UAAU,KAQjC;IARiC,IAAA,EAChCqJ,WAAW,EACXC,QAAQ,EACRC,WAAW,EAKZ,GARiC;IAShC,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,8BAAsB,EAAC;IAEjD,MAAMtI,aAAauI,IAAAA,cAAO,EAAa;QACrC,OAAO;YACLjI;gBACE+H,SAAS;oBAAErB,MAAMwB,uBAAe;gBAAC;YACnC;YACAxD,cAAa/B,OAAO;gBAClBoF,SAAS;oBAAErB,MAAMyB,0BAAkB;oBAAExF;gBAAQ;YAC/C;YACA/C;gBACEmI,SAAS;oBAAErB,MAAM0B,6BAAqB;gBAAC;YACzC;YACAlI;gBACE6H,SAAS;oBAAErB,MAAM2B,sBAAc;gBAAC;YAClC;YACAxC,eAAcC,WAAW;gBACvBiC,SAAS;oBAAErB,MAAM4B,2BAAmB;oBAAExC;gBAAY;YACpD;YACAN,mBAAkB1D,MAAe;gBAC/BiG,SAAS;oBAAErB,MAAM6B,+BAAuB;oBAAEC,iBAAiB1G;gBAAO;YACpE;YACAkE,aAAYyC,SAAS;gBACnBV,SAAS;oBAAErB,MAAMgC,yBAAiB;oBAAED;gBAAU;YAChD;YACAxC,gBAAeC,YAAY;gBACzB6B,SAAS;oBACPrB,MAAMiC,4BAAoB;oBAC1BzC;gBACF;YACF;QACF;IACF,GAAG;QAAC6B;KAAS;IAEb,iGAAiG;IACjG,2FAA2F;IAC3F,+EAA+E;IAC/E,MAAMa,2BAA2BC,IAAAA,2BAAoB,EACnD,IAAM,KAAO,GACb,IAAM,CAACC,IAAAA,iEAAiC,KACxC,IAAM;IAGR,MAAMC,yBAAyBC,IAAAA,kBAAW,EACxC,CAACpE;QACC,MAAMqE,eAAe,AAACrE,MAAcsE,OAAO;QAG3C,iGAAiG;QACjG,MAAMC,sBACJ,AAACvE,MAAcwE,eAAe,KAAIH,gCAAAA,aAAcI,cAAc;QAChE,MAAMC,UAAUL,gCAAAA,aAAcK,OAAO;QAErCvB,SAAS;YACPrB,MAAM6C,8BAAsB;YAC5BC,QAAQ5E;YACR6E,QAAQC,IAAAA,sBAAU,EAAC9E,MAAMrC,KAAK,IAAI;YAClCoH,sBACE,OAAOR,wBAAwB,WAC3BS,IAAAA,wCAAmB,EAACT,uBACpBpG;YACNuG;QACF;IACF,GACA;QAACvB;KAAS;IAGZ,MAAM8B,6BAA6Bb,IAAAA,kBAAW,EAC5C,CAACQ;QACC,MAAMM,gBAAgBC,IAAAA,oCAAqB,EAACP;QAC5CzB,SAAS;YACPrB,MAAMsD,kCAA0B;YAChCR,QAAQM;YACRL,QAAQC,IAAAA,sBAAU,EAACI,cAAcvH,KAAK,IAAI;QAC5C;IACF,GACA;QAACwF;KAAS;IAEZkC,IAAAA,gCAAe,EAAClB,wBAAwBc;IAExC,MAAMK,eAAeC,IAAAA,0BAAY,EAACxC;IAClCyC,IAAAA,8BAAgB,EAACF;IACjB,MAAMpK,cAAcuK,IAAAA,4BAAc,EAACH;IACnC,MAAMjG,0BAA0BqG,IAAAA,0BAAY,EAACxK,aAAa,CAACuC,MACzDD,kBAAkBC,KAAKvC;IAGzB,MAAMoE,SAASqG,IAAAA,qBAAS;IAExB,8EAA8E;IAC9E,mEAAmE;IACnE,MAAMvJ,WAAWwJ,IAAAA,yCAAoB;IACrC,MAAMrG,oBAAoBsG,IAAAA,aAAM,EAAiC,CAAC;IAClE,MAAMrG,cAAcqG,IAAAA,aAAM,EAACzJ;IAE3B,IAAIO,QAAQC,GAAG,CAAC6D,oBAAoB,EAAE;QACpC,2DAA2D;QAC3D,mEAAmE;QACnE,sDAAsD;QACtDqF,IAAAA,gBAAS,EAAC;YACRtG,YAAYkB,OAAO,GAAGtE;YAEtB,MAAM2J,iBAAiBxG,kBAAkBmB,OAAO;YAEhD,IAAIqF,gBAAgB;gBAClB,IAAI3J,YAAYA,YAAY2J,gBAAgB;oBAC1C,IAAI;wBACFjL,WAAW8F,iBAAiB,CAAC;oBAC/B,EAAE,OAAOgE,QAAQ;wBACf,IAAI7G,UAAU;wBAEd,IAAI6G,kBAAkBoB,cAAc;gCAExBpB;4BADV,sEAAsE;4BACtE7G,UAAU6G,CAAAA,gBAAAA,OAAOjH,KAAK,YAAZiH,gBAAgBA,OAAO7G,OAAO;wBAC1C,OAAO,IAAI6G,kBAAkB/B,OAAO;gCACa+B;4BAA/C7G,UAAU,YAAY6G,OAAO7G,OAAO,GAAG,OAAQ6G,CAAAA,CAAAA,iBAAAA,OAAOjH,KAAK,YAAZiH,iBAAgB,EAAC;wBAClE,OAAO;4BACL7G,UAAU,2BAA2B6G;wBACvC;wBAEAnJ,QAAQgD,IAAI,CAAC,WAAWV;oBAC1B;gBACF,OAAO;oBACLjD,WAAW8F,iBAAiB,CAAC;gBAC/B;YACF;QACF,GAAG;YAACxE;YAAUtB;SAAW;IAC3B;IAEAgL,IAAAA,gBAAS,EAAC;QACR,MAAMG,YAAYX,aAAa5E,OAAO;QACtC,IAAI,CAACuF,WAAW;QAEhB,MAAM5I,UAAU,CAACxB;YACf,IAAI;gBACF,MAAMuD,MAAMzD,KAAKiH,KAAK,CAAC/G,MAAM8E,IAAI;gBACjCuF,IAAAA,kEAAgC,EAAC9G;gBACjCD,eACEC,KACAlE,aACAmE,yBACAC,QACAxE,YACAyE,mBACAC;YAEJ,EAAE,OAAO/B,KAAU;oBAKZA;gBAJLhC,QAAQgD,IAAI,CACV,4BACE9C,KAAKC,SAAS,CAACC,MAAM8E,IAAI,IACzB,OACClD,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKE,KAAK,YAAVF,aAAc,EAAC;YAEtB;QACF;QAEAwI,UAAUE,gBAAgB,CAAC,WAAW9I;QACtC,OAAO,IAAM4I,UAAUG,mBAAmB,CAAC,WAAW/I;IACxD,GAAG;QACDnC;QACAoE;QACAgG;QACAxK;QACAuE;QACAE;KACD;IAED,IAAIyE,0BAA0B;QAC5B,qBACE,qBAACqC,4BAAa;YAACnD,OAAOA;YAAOD,aAAaA;sBACvCD;;IAGP;IAEA,OAAOA;AACT"}