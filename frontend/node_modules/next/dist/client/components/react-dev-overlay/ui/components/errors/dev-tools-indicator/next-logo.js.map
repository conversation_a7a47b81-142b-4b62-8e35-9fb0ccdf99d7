{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.tsx"], "sourcesContent": ["import { forwardRef, useEffect, useMemo, useRef, useState } from 'react'\nimport { css } from '../../../../utils/css'\nimport mergeRefs from '../../../utils/merge-refs'\nimport { useMinimumLoadingTimeMultiple } from './use-minimum-loading-time-multiple'\n\ninterface Props extends React.ComponentProps<'button'> {\n  issueCount: number\n  isDevBuilding: boolean\n  isDevRendering: boolean\n  isBuildError: boolean\n  onTriggerClick: () => void\n  toggleErrorOverlay: () => void\n}\n\nconst SIZE = '2.25rem' // 36px in 16px base\nconst SIZE_PX = 36\nconst SHORT_DURATION_MS = 150\n\nexport const NextLogo = forwardRef(function NextLogo(\n  {\n    disabled,\n    issueCount,\n    isDevBuilding,\n    isDevRendering,\n    isBuildError,\n    onTriggerClick,\n    toggleErrorOverlay,\n    ...props\n  }: Props,\n  propRef: React.Ref<HTMLButtonElement>\n) {\n  const hasError = issueCount > 0\n  const [isErrorExpanded, setIsErrorExpanded] = useState(hasError)\n  const [dismissed, setDismissed] = useState(false)\n  const newErrorDetected = useUpdateAnimation(issueCount, SHORT_DURATION_MS)\n\n  const triggerRef = useRef<HTMLButtonElement | null>(null)\n  const ref = useRef<HTMLDivElement | null>(null)\n  const [measuredWidth, pristine] = useMeasureWidth(ref)\n\n  const isLoading = useMinimumLoadingTimeMultiple(\n    isDevBuilding || isDevRendering\n  )\n  const isExpanded = isErrorExpanded || disabled\n\n  const style = useMemo(() => {\n    let width: number | string = SIZE\n    // Animates the badge, if expanded\n    if (measuredWidth > SIZE_PX) width = measuredWidth\n    // No animations on page load, assume the intrinsic width immediately\n    if (pristine && hasError) width = 'auto'\n    // Default state, collapsed\n    return { width }\n  }, [measuredWidth, pristine, hasError])\n\n  useEffect(() => {\n    setIsErrorExpanded(hasError)\n  }, [hasError])\n\n  return (\n    <div\n      data-next-badge-root\n      style={\n        {\n          '--size': SIZE,\n          '--duration-short': `${SHORT_DURATION_MS}ms`,\n          // if the indicator is disabled, hide the badge\n          // also allow the \"disabled\" state be dismissed, as long as there are no build errors\n          display: disabled && (!hasError || dismissed) ? 'none' : 'block',\n        } as React.CSSProperties\n      }\n    >\n      {/* Styles */}\n      <style>\n        {css`\n          [data-next-badge-root] {\n            --timing: cubic-bezier(0.23, 0.88, 0.26, 0.92);\n            --duration-long: 250ms;\n            --color-outer-border: #171717;\n            --color-inner-border: hsla(0, 0%, 100%, 0.14);\n            --color-hover-alpha-subtle: hsla(0, 0%, 100%, 0.13);\n            --color-hover-alpha-error: hsla(0, 0%, 100%, 0.2);\n            --color-hover-alpha-error-2: hsla(0, 0%, 100%, 0.25);\n            --mark-size: calc(var(--size) - var(--size-2) * 2);\n\n            --focus-color: var(--color-blue-800);\n            --focus-ring: 2px solid var(--focus-color);\n\n            &:has([data-next-badge][data-error='true']) {\n              --focus-color: #fff;\n            }\n          }\n\n          [data-disabled-icon] {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            padding-right: 4px;\n          }\n\n          [data-next-badge] {\n            -webkit-font-smoothing: antialiased;\n            width: var(--size);\n            height: var(--size);\n            display: flex;\n            align-items: center;\n            position: relative;\n            background: rgba(0, 0, 0, 0.8);\n            box-shadow:\n              0 0 0 1px var(--color-outer-border),\n              inset 0 0 0 1px var(--color-inner-border),\n              0px 16px 32px -8px rgba(0, 0, 0, 0.24);\n            backdrop-filter: blur(48px);\n            border-radius: var(--rounded-full);\n            user-select: none;\n            cursor: pointer;\n            scale: 1;\n            overflow: hidden;\n            will-change: scale, box-shadow, width, background;\n            transition:\n              scale var(--duration-short) var(--timing),\n              width var(--duration-long) var(--timing),\n              box-shadow var(--duration-long) var(--timing),\n              background var(--duration-short) ease;\n\n            &:active[data-error='false'] {\n              scale: 0.95;\n            }\n\n            &[data-animate='true']:not(:hover) {\n              scale: 1.02;\n            }\n\n            &[data-error='false']:has([data-next-mark]:focus-visible) {\n              outline: var(--focus-ring);\n              outline-offset: 3px;\n            }\n\n            &[data-error='true'] {\n              background: #ca2a30;\n              --color-inner-border: #e5484d;\n\n              [data-next-mark] {\n                background: var(--color-hover-alpha-error);\n                outline-offset: 0px;\n\n                &:focus-visible {\n                  outline: var(--focus-ring);\n                  outline-offset: -1px;\n                }\n\n                &:hover {\n                  background: var(--color-hover-alpha-error-2);\n                }\n              }\n            }\n\n            &[data-error-expanded='false'][data-error='true'] ~ [data-dot] {\n              scale: 1;\n            }\n\n            > div {\n              display: flex;\n            }\n          }\n\n          [data-issues-collapse]:focus-visible {\n            outline: var(--focus-ring);\n          }\n\n          [data-issues]:has([data-issues-open]:focus-visible) {\n            outline: var(--focus-ring);\n            outline-offset: -1px;\n          }\n\n          [data-dot] {\n            content: '';\n            width: var(--size-8);\n            height: var(--size-8);\n            background: #fff;\n            box-shadow: 0 0 0 1px var(--color-outer-border);\n            border-radius: 50%;\n            position: absolute;\n            top: 2px;\n            right: 0px;\n            scale: 0;\n            pointer-events: none;\n            transition: scale 200ms var(--timing);\n            transition-delay: var(--duration-short);\n          }\n\n          [data-issues] {\n            --padding-left: 8px;\n            display: flex;\n            gap: 2px;\n            align-items: center;\n            padding-left: 8px;\n            padding-right: 8px;\n            height: var(--size-32);\n            margin: 0 2px;\n            border-radius: var(--rounded-full);\n            transition: background var(--duration-short) ease;\n\n            &:has([data-issues-open]:hover) {\n              background: var(--color-hover-alpha-error);\n            }\n\n            &:has([data-issues-collapse]) {\n              padding-right: calc(var(--padding-left) / 2);\n            }\n\n            [data-cross] {\n              translate: 0px -1px;\n            }\n          }\n\n          [data-issues-open] {\n            font-size: var(--size-13);\n            color: white;\n            width: fit-content;\n            height: 100%;\n            display: flex;\n            gap: 2px;\n            align-items: center;\n            margin: 0;\n            line-height: var(--size-36);\n            font-weight: 500;\n            z-index: 2;\n            white-space: nowrap;\n\n            &:focus-visible {\n              outline: 0;\n            }\n          }\n\n          [data-issues-collapse] {\n            width: var(--size-24);\n            height: var(--size-24);\n            border-radius: var(--rounded-full);\n            transition: background var(--duration-short) ease;\n\n            &:hover {\n              background: var(--color-hover-alpha-error);\n            }\n          }\n\n          [data-cross] {\n            color: #fff;\n            width: var(--size-12);\n            height: var(--size-12);\n          }\n\n          [data-next-mark] {\n            width: var(--mark-size);\n            height: var(--mark-size);\n            margin-left: 2px;\n            display: flex;\n            align-items: center;\n            border-radius: var(--rounded-full);\n            transition: background var(--duration-long) var(--timing);\n\n            &:focus-visible {\n              outline: 0;\n            }\n\n            &:hover {\n              background: var(--color-hover-alpha-subtle);\n            }\n\n            svg {\n              flex-shrink: 0;\n              width: var(--size-40);\n              height: var(--size-40);\n            }\n          }\n\n          [data-issues-count-animation] {\n            display: grid;\n            place-items: center center;\n            font-variant-numeric: tabular-nums;\n\n            &[data-animate='false'] {\n              [data-issues-count-exit],\n              [data-issues-count-enter] {\n                animation-duration: 0ms;\n              }\n            }\n\n            > * {\n              grid-area: 1 / 1;\n            }\n\n            [data-issues-count-exit] {\n              animation: fadeOut 300ms var(--timing) forwards;\n            }\n\n            [data-issues-count-enter] {\n              animation: fadeIn 300ms var(--timing) forwards;\n            }\n          }\n\n          [data-issues-count-plural] {\n            display: inline-block;\n            &[data-animate='true'] {\n              animation: fadeIn 300ms var(--timing) forwards;\n            }\n          }\n\n          .path0 {\n            animation: draw0 1.5s ease-in-out infinite;\n          }\n\n          .path1 {\n            animation: draw1 1.5s ease-out infinite;\n            animation-delay: 0.3s;\n          }\n\n          .paused {\n            stroke-dashoffset: 0;\n          }\n\n          @keyframes fadeIn {\n            0% {\n              opacity: 0;\n              filter: blur(2px);\n              transform: translateY(8px);\n            }\n            100% {\n              opacity: 1;\n              filter: blur(0px);\n              transform: translateY(0);\n            }\n          }\n\n          @keyframes fadeOut {\n            0% {\n              opacity: 1;\n              filter: blur(0px);\n              transform: translateY(0);\n            }\n            100% {\n              opacity: 0;\n              transform: translateY(-12px);\n              filter: blur(2px);\n            }\n          }\n\n          @keyframes draw0 {\n            0%,\n            25% {\n              stroke-dashoffset: -29.6;\n            }\n            25%,\n            50% {\n              stroke-dashoffset: 0;\n            }\n            50%,\n            75% {\n              stroke-dashoffset: 0;\n            }\n            75%,\n            100% {\n              stroke-dashoffset: 29.6;\n            }\n          }\n\n          @keyframes draw1 {\n            0%,\n            20% {\n              stroke-dashoffset: -11.6;\n            }\n            20%,\n            50% {\n              stroke-dashoffset: 0;\n            }\n            50%,\n            75% {\n              stroke-dashoffset: 0;\n            }\n            75%,\n            100% {\n              stroke-dashoffset: 11.6;\n            }\n          }\n\n          @media (prefers-reduced-motion) {\n            [data-issues-count-exit],\n            [data-issues-count-enter],\n            [data-issues-count-plural] {\n              animation-duration: 0ms !important;\n            }\n          }\n        `}\n      </style>\n      <div\n        data-next-badge\n        data-error={hasError}\n        data-error-expanded={isExpanded}\n        data-animate={newErrorDetected}\n        style={style}\n      >\n        <div ref={ref}>\n          {/* Children */}\n          {!disabled && (\n            <button\n              ref={mergeRefs(triggerRef, propRef)}\n              data-next-mark\n              data-next-mark-loading={isLoading}\n              onClick={onTriggerClick}\n              {...props}\n            >\n              <NextMark isLoading={isLoading} isDevBuilding={isDevBuilding} />\n            </button>\n          )}\n          {isExpanded && (\n            <div data-issues>\n              <button\n                data-issues-open\n                aria-label=\"Open issues overlay\"\n                onClick={toggleErrorOverlay}\n              >\n                {disabled && (\n                  <div data-disabled-icon>\n                    <Warning />\n                  </div>\n                )}\n                <AnimateCount\n                  // Used the key to force a re-render when the count changes.\n                  key={issueCount}\n                  animate={newErrorDetected}\n                  data-issues-count-animation\n                >\n                  {issueCount}\n                </AnimateCount>{' '}\n                <div>\n                  Issue\n                  {issueCount > 1 && (\n                    <span\n                      aria-hidden\n                      data-issues-count-plural\n                      // This only needs to animate once the count changes from 1 -> 2,\n                      // otherwise it should stay static between re-renders.\n                      data-animate={newErrorDetected && issueCount === 2}\n                    >\n                      s\n                    </span>\n                  )}\n                </div>\n              </button>\n              {!isBuildError && (\n                <button\n                  data-issues-collapse\n                  aria-label=\"Collapse issues badge\"\n                  onClick={() => {\n                    if (disabled) {\n                      setDismissed(true)\n                    } else {\n                      setIsErrorExpanded(false)\n                    }\n                    // Move focus to the trigger to prevent having it stuck on this element\n                    triggerRef.current?.focus()\n                  }}\n                >\n                  <Cross data-cross />\n                </button>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n      <div aria-hidden data-dot />\n    </div>\n  )\n})\n\nfunction AnimateCount({\n  children: count,\n  animate = true,\n  ...props\n}: {\n  children: number\n  animate: boolean\n}) {\n  return (\n    <div {...props} data-animate={animate}>\n      <div aria-hidden data-issues-count-exit>\n        {count - 1}\n      </div>\n      <div data-issues-count data-issues-count-enter>\n        {count}\n      </div>\n    </div>\n  )\n}\n\nfunction useMeasureWidth(\n  ref: React.RefObject<HTMLDivElement | null>\n): [number, boolean] {\n  const [width, setWidth] = useState<number>(0)\n  const [pristine, setPristine] = useState(true)\n\n  useEffect(() => {\n    const el = ref.current\n\n    if (!el) {\n      return\n    }\n\n    const observer = new ResizeObserver(() => {\n      const { width: w } = el.getBoundingClientRect()\n      setWidth((prevWidth) => {\n        if (prevWidth !== 0) {\n          setPristine(false)\n        }\n        return w\n      })\n    })\n\n    observer.observe(el)\n    return () => observer.disconnect()\n  }, [ref])\n\n  return [width, pristine]\n}\n\nfunction useUpdateAnimation(issueCount: number, animationDurationMs = 0) {\n  const lastUpdatedTimeStamp = useRef<number | null>(null)\n  const [animate, setAnimate] = useState(false)\n\n  useEffect(() => {\n    if (issueCount > 0) {\n      const deltaMs = lastUpdatedTimeStamp.current\n        ? Date.now() - lastUpdatedTimeStamp.current\n        : -1\n      lastUpdatedTimeStamp.current = Date.now()\n\n      // We don't animate if `issueCount` changes too quickly\n      if (deltaMs <= animationDurationMs) {\n        return\n      }\n\n      setAnimate(true)\n      // It is important to use a CSS transitioned state, not a CSS keyframed animation\n      // because if the issue count increases faster than the animation duration, it\n      // will abruptly stop and not transition smoothly back to its original state.\n      const timeoutId = window.setTimeout(() => {\n        setAnimate(false)\n      }, animationDurationMs)\n\n      return () => {\n        clearTimeout(timeoutId)\n      }\n    }\n  }, [issueCount, animationDurationMs])\n\n  return animate\n}\n\nfunction NextMark({\n  isLoading,\n  isDevBuilding,\n}: {\n  isLoading?: boolean\n  isDevBuilding?: boolean\n}) {\n  const strokeColor = isDevBuilding ? 'rgba(255,255,255,0.7)' : 'white'\n  return (\n    <svg\n      width=\"40\"\n      height=\"40\"\n      viewBox=\"0 0 40 40\"\n      fill=\"none\"\n      data-next-mark-loading={isLoading}\n    >\n      <g transform=\"translate(8.5, 13)\">\n        <path\n          className={isLoading ? 'path0' : 'paused'}\n          d=\"M13.3 15.2 L2.34 1 V12.6\"\n          fill=\"none\"\n          stroke=\"url(#next_logo_paint0_linear_1357_10853)\"\n          strokeWidth=\"1.86\"\n          mask=\"url(#next_logo_mask0)\"\n          strokeDasharray=\"29.6\"\n          strokeDashoffset=\"29.6\"\n        />\n        <path\n          className={isLoading ? 'path1' : 'paused'}\n          d=\"M11.825 1.5 V13.1\"\n          strokeWidth=\"1.86\"\n          stroke=\"url(#next_logo_paint1_linear_1357_10853)\"\n          strokeDasharray=\"11.6\"\n          strokeDashoffset=\"11.6\"\n        />\n      </g>\n      <defs>\n        <linearGradient\n          id=\"next_logo_paint0_linear_1357_10853\"\n          x1=\"9.95555\"\n          y1=\"11.1226\"\n          x2=\"15.4778\"\n          y2=\"17.9671\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop stopColor={strokeColor} />\n          <stop offset=\"0.604072\" stopColor={strokeColor} stopOpacity=\"0\" />\n          <stop offset=\"1\" stopColor={strokeColor} stopOpacity=\"0\" />\n        </linearGradient>\n        <linearGradient\n          id=\"next_logo_paint1_linear_1357_10853\"\n          x1=\"11.8222\"\n          y1=\"1.40039\"\n          x2=\"11.791\"\n          y2=\"9.62542\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop stopColor={strokeColor} />\n          <stop offset=\"1\" stopColor={strokeColor} stopOpacity=\"0\" />\n        </linearGradient>\n        <mask id=\"next_logo_mask0\">\n          <rect width=\"100%\" height=\"100%\" fill=\"white\" />\n          <rect width=\"5\" height=\"1.5\" fill=\"black\" />\n        </mask>\n      </defs>\n    </svg>\n  )\n}\n\nfunction Warning() {\n  return (\n    <svg\n      width=\"12\"\n      height=\"12\"\n      viewBox=\"0 0 12 12\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M3.98071 1.125L1.125 3.98071L1.125 8.01929L3.98071 10.875H8.01929L10.875 8.01929V3.98071L8.01929 1.125H3.98071ZM3.82538 0C3.62647 0 3.4357 0.0790176 3.29505 0.21967L0.21967 3.29505C0.0790176 3.4357 0 3.62647 0 3.82538V8.17462C0 8.37353 0.0790178 8.5643 0.21967 8.70495L3.29505 11.7803C3.4357 11.921 3.62647 12 3.82538 12H8.17462C8.37353 12 8.5643 11.921 8.70495 11.7803L11.7803 8.70495C11.921 8.5643 12 8.37353 12 8.17462V3.82538C12 3.62647 11.921 3.4357 11.7803 3.29505L8.70495 0.21967C8.5643 0.0790177 8.37353 0 8.17462 0H3.82538ZM6.5625 2.8125V3.375V6V6.5625H5.4375V6V3.375V2.8125H6.5625ZM6 9C6.41421 9 6.75 8.66421 6.75 8.25C6.75 7.83579 6.41421 7.5 6 7.5C5.58579 7.5 5.25 7.83579 5.25 8.25C5.25 8.66421 5.58579 9 6 9Z\"\n        fill=\"#EAEAEA\"\n      />\n    </svg>\n  )\n}\n\nexport function Cross(props: React.SVGProps<SVGSVGElement>) {\n  return (\n    <svg\n      width=\"12\"\n      height=\"12\"\n      viewBox=\"0 0 14 14\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M3.08889 11.8384L2.62486 12.3024L1.69678 11.3744L2.16082 10.9103L6.07178 6.99937L2.16082 3.08841L1.69678 2.62437L2.62486 1.69629L3.08889 2.16033L6.99986 6.07129L10.9108 2.16033L11.3749 1.69629L12.3029 2.62437L11.8389 3.08841L7.92793 6.99937L11.8389 10.9103L12.3029 11.3744L11.3749 12.3024L10.9108 11.8384L6.99986 7.92744L3.08889 11.8384Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n"], "names": ["Cross", "NextLogo", "SIZE", "SIZE_PX", "SHORT_DURATION_MS", "forwardRef", "propRef", "disabled", "issueCount", "isDevBuilding", "isDevRendering", "isBuildError", "onTriggerClick", "toggleError<PERSON><PERSON>lay", "props", "<PERSON><PERSON><PERSON><PERSON>", "isErrorExpanded", "setIsErrorExpanded", "useState", "dismissed", "setDismissed", "newErrorDetected", "useUpdateAnimation", "triggerRef", "useRef", "ref", "measuredWidth", "pristine", "useMeasureWidth", "isLoading", "useMinimumLoadingTimeMultiple", "isExpanded", "style", "useMemo", "width", "useEffect", "div", "data-next-badge-root", "display", "css", "data-next-badge", "data-error", "data-error-expanded", "data-animate", "button", "mergeRefs", "data-next-mark", "data-next-mark-loading", "onClick", "NextMark", "data-issues", "data-issues-open", "aria-label", "data-disabled-icon", "Warning", "AnimateCount", "animate", "data-issues-count-animation", "span", "aria-hidden", "data-issues-count-plural", "data-issues-collapse", "current", "focus", "data-cross", "data-dot", "children", "count", "data-issues-count-exit", "data-issues-count", "data-issues-count-enter", "<PERSON><PERSON><PERSON><PERSON>", "setPristine", "el", "observer", "ResizeObserver", "w", "getBoundingClientRect", "prevWidth", "observe", "disconnect", "animationDurationMs", "lastUpdatedTimeStamp", "setAnimate", "deltaMs", "Date", "now", "timeoutId", "window", "setTimeout", "clearTimeout", "strokeColor", "svg", "height", "viewBox", "fill", "g", "transform", "path", "className", "d", "stroke", "strokeWidth", "mask", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "defs", "linearGradient", "id", "x1", "y1", "x2", "y2", "gradientUnits", "stop", "stopColor", "offset", "stopOpacity", "rect", "xmlns", "fillRule", "clipRule"], "mappings": ";;;;;;;;;;;;;;;IAsoBgBA,KAAK;eAALA;;IApnBHC,QAAQ;eAARA;;;;;;uBAlBoD;qBAC7C;oEACE;+CACwB;;;;;;;;;;AAW9C,MAAMC,OAAO,UAAU,oBAAoB;;AAC3C,MAAMC,UAAU;AAChB,MAAMC,oBAAoB;AAEnB,MAAMH,yBAAWI,IAAAA,iBAAU,EAAC,SAASJ,SAC1C,KASQ,EACRK,OAAqC;IAVrC,IAAA,EACEC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClB,GAAGC,OACG,GATR;IAYA,MAAMC,WAAWP,aAAa;IAC9B,MAAM,CAACQ,iBAAiBC,mBAAmB,GAAGC,IAAAA,eAAQ,EAACH;IACvD,MAAM,CAACI,WAAWC,aAAa,GAAGF,IAAAA,eAAQ,EAAC;IAC3C,MAAMG,mBAAmBC,mBAAmBd,YAAYJ;IAExD,MAAMmB,aAAaC,IAAAA,aAAM,EAA2B;IACpD,MAAMC,MAAMD,IAAAA,aAAM,EAAwB;IAC1C,MAAM,CAACE,eAAeC,SAAS,GAAGC,gBAAgBH;IAElD,MAAMI,YAAYC,IAAAA,4DAA6B,EAC7CrB,iBAAiBC;IAEnB,MAAMqB,aAAaf,mBAAmBT;IAEtC,MAAMyB,QAAQC,IAAAA,cAAO,EAAC;QACpB,IAAIC,QAAyBhC;QAC7B,kCAAkC;QAClC,IAAIwB,gBAAgBvB,SAAS+B,QAAQR;QACrC,qEAAqE;QACrE,IAAIC,YAAYZ,UAAUmB,QAAQ;QAClC,2BAA2B;QAC3B,OAAO;YAAEA;QAAM;IACjB,GAAG;QAACR;QAAeC;QAAUZ;KAAS;IAEtCoB,IAAAA,gBAAS,EAAC;QACRlB,mBAAmBF;IACrB,GAAG;QAACA;KAAS;IAEb,qBACE,sBAACqB;QACCC,sBAAoB;QACpBL,OACE;YACE,UAAU9B;YACV,oBAAoB,AAAC,KAAEE,oBAAkB;YACzC,+CAA+C;YAC/C,qFAAqF;YACrFkC,SAAS/B,YAAa,CAAA,CAACQ,YAAYI,SAAQ,IAAK,SAAS;QAC3D;;0BAIF,qBAACa;8BACEO,QAAG;;0BAgUN,qBAACH;gBACCI,iBAAe;gBACfC,cAAY1B;gBACZ2B,uBAAqBX;gBACrBY,gBAActB;gBACdW,OAAOA;0BAEP,cAAA,sBAACI;oBAAIX,KAAKA;;wBAEP,CAAClB,0BACA,qBAACqC;4BACCnB,KAAKoB,IAAAA,kBAAS,EAACtB,YAAYjB;4BAC3BwC,gBAAc;4BACdC,0BAAwBlB;4BACxBmB,SAASpC;4BACR,GAAGE,KAAK;sCAET,cAAA,qBAACmC;gCAASpB,WAAWA;gCAAWpB,eAAeA;;;wBAGlDsB,4BACC,sBAACK;4BAAIc,aAAW;;8CACd,sBAACN;oCACCO,kBAAgB;oCAChBC,cAAW;oCACXJ,SAASnC;;wCAERN,0BACC,qBAAC6B;4CAAIiB,oBAAkB;sDACrB,cAAA,qBAACC;;sDAGL,qBAACC;4CAGCC,SAASnC;4CACToC,6BAA2B;sDAE1BjD;2CAJIA;wCAKS;sDAChB,sBAAC4B;;gDAAI;gDAEF5B,aAAa,mBACZ,qBAACkD;oDACCC,aAAW;oDACXC,0BAAwB;oDACxB,iEAAiE;oDACjE,sDAAsD;oDACtDjB,gBAActB,oBAAoBb,eAAe;8DAClD;;;;;;gCAMN,CAACG,8BACA,qBAACiC;oCACCiB,sBAAoB;oCACpBT,cAAW;oCACXJ,SAAS;4CAMP,uEAAuE;wCACvEzB;wCANA,IAAIhB,UAAU;4CACZa,aAAa;wCACf,OAAO;4CACLH,mBAAmB;wCACrB;yCAEAM,sBAAAA,WAAWuC,OAAO,qBAAlBvC,oBAAoBwC,KAAK;oCAC3B;8CAEA,cAAA,qBAAC/D;wCAAMgE,YAAU;;;;;;;;0BAO7B,qBAAC5B;gBAAIuB,aAAW;gBAACM,UAAQ;;;;AAG/B;AAEA,SAASV,aAAa,KAOrB;IAPqB,IAAA,EACpBW,UAAUC,KAAK,EACfX,UAAU,IAAI,EACd,GAAG1C,OAIJ,GAPqB;IAQpB,qBACE,sBAACsB;QAAK,GAAGtB,KAAK;QAAE6B,gBAAca;;0BAC5B,qBAACpB;gBAAIuB,aAAW;gBAACS,wBAAsB;0BACpCD,QAAQ;;0BAEX,qBAAC/B;gBAAIiC,mBAAiB;gBAACC,yBAAuB;0BAC3CH;;;;AAIT;AAEA,SAASvC,gBACPH,GAA2C;IAE3C,MAAM,CAACS,OAAOqC,SAAS,GAAGrD,IAAAA,eAAQ,EAAS;IAC3C,MAAM,CAACS,UAAU6C,YAAY,GAAGtD,IAAAA,eAAQ,EAAC;IAEzCiB,IAAAA,gBAAS,EAAC;QACR,MAAMsC,KAAKhD,IAAIqC,OAAO;QAEtB,IAAI,CAACW,IAAI;YACP;QACF;QAEA,MAAMC,WAAW,IAAIC,eAAe;YAClC,MAAM,EAAEzC,OAAO0C,CAAC,EAAE,GAAGH,GAAGI,qBAAqB;YAC7CN,SAAS,CAACO;gBACR,IAAIA,cAAc,GAAG;oBACnBN,YAAY;gBACd;gBACA,OAAOI;YACT;QACF;QAEAF,SAASK,OAAO,CAACN;QACjB,OAAO,IAAMC,SAASM,UAAU;IAClC,GAAG;QAACvD;KAAI;IAER,OAAO;QAACS;QAAOP;KAAS;AAC1B;AAEA,SAASL,mBAAmBd,UAAkB,EAAEyE,mBAAuB;IAAvBA,IAAAA,gCAAAA,sBAAsB;IACpE,MAAMC,uBAAuB1D,IAAAA,aAAM,EAAgB;IACnD,MAAM,CAACgC,SAAS2B,WAAW,GAAGjE,IAAAA,eAAQ,EAAC;IAEvCiB,IAAAA,gBAAS,EAAC;QACR,IAAI3B,aAAa,GAAG;YAClB,MAAM4E,UAAUF,qBAAqBpB,OAAO,GACxCuB,KAAKC,GAAG,KAAKJ,qBAAqBpB,OAAO,GACzC,CAAC;YACLoB,qBAAqBpB,OAAO,GAAGuB,KAAKC,GAAG;YAEvC,uDAAuD;YACvD,IAAIF,WAAWH,qBAAqB;gBAClC;YACF;YAEAE,WAAW;YACX,iFAAiF;YACjF,8EAA8E;YAC9E,6EAA6E;YAC7E,MAAMI,YAAYC,OAAOC,UAAU,CAAC;gBAClCN,WAAW;YACb,GAAGF;YAEH,OAAO;gBACLS,aAAaH;YACf;QACF;IACF,GAAG;QAAC/E;QAAYyE;KAAoB;IAEpC,OAAOzB;AACT;AAEA,SAASP,SAAS,KAMjB;IANiB,IAAA,EAChBpB,SAAS,EACTpB,aAAa,EAId,GANiB;IAOhB,MAAMkF,cAAclF,gBAAgB,0BAA0B;IAC9D,qBACE,sBAACmF;QACC1D,OAAM;QACN2D,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLhD,0BAAwBlB;;0BAExB,sBAACmE;gBAAEC,WAAU;;kCACX,qBAACC;wBACCC,WAAWtE,YAAY,UAAU;wBACjCuE,GAAE;wBACFL,MAAK;wBACLM,QAAO;wBACPC,aAAY;wBACZC,MAAK;wBACLC,iBAAgB;wBAChBC,kBAAiB;;kCAEnB,qBAACP;wBACCC,WAAWtE,YAAY,UAAU;wBACjCuE,GAAE;wBACFE,aAAY;wBACZD,QAAO;wBACPG,iBAAgB;wBAChBC,kBAAiB;;;;0BAGrB,sBAACC;;kCACC,sBAACC;wBACCC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,WAAWxB;;0CACjB,qBAACuB;gCAAKE,QAAO;gCAAWD,WAAWxB;gCAAa0B,aAAY;;0CAC5D,qBAACH;gCAAKE,QAAO;gCAAID,WAAWxB;gCAAa0B,aAAY;;;;kCAEvD,sBAACV;wBACCC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,WAAWxB;;0CACjB,qBAACuB;gCAAKE,QAAO;gCAAID,WAAWxB;gCAAa0B,aAAY;;;;kCAEvD,sBAACd;wBAAKK,IAAG;;0CACP,qBAACU;gCAAKpF,OAAM;gCAAO2D,QAAO;gCAAOE,MAAK;;0CACtC,qBAACuB;gCAAKpF,OAAM;gCAAI2D,QAAO;gCAAME,MAAK;;;;;;;;AAK5C;AAEA,SAASzC;IACP,qBACE,qBAACsC;QACC1D,OAAM;QACN2D,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLwB,OAAM;kBAEN,cAAA,qBAACrB;YACCsB,UAAS;YACTC,UAAS;YACTrB,GAAE;YACFL,MAAK;;;AAIb;AAEO,SAAS/F,MAAMc,KAAoC;IACxD,qBACE,qBAAC8E;QACC1D,OAAM;QACN2D,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLwB,OAAM;QACL,GAAGzG,KAAK;kBAET,cAAA,qBAACoF;YACCsB,UAAS;YACTC,UAAS;YACTrB,GAAE;YACFL,MAAK;;;AAIb"}