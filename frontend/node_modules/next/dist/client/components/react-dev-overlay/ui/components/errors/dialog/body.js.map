{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dialog/body.tsx"], "sourcesContent": ["import { DialogBody } from '../../dialog'\n\ntype ErrorOverlayDialogBodyProps = {\n  children?: React.ReactNode\n  onClose?: () => void\n}\n\nexport function ErrorOverlayDialogBody({\n  children,\n}: ErrorOverlayDialogBodyProps) {\n  return (\n    <DialogBody className=\"nextjs-container-errors-body\">{children}</DialogBody>\n  )\n}\n\nexport const DIALOG_BODY_STYLES = ``\n"], "names": ["DIALOG_BODY_STYLES", "ErrorOverlayDialogBody", "children", "DialogBody", "className"], "mappings": ";;;;;;;;;;;;;;;IAeaA,kBAAkB;eAAlBA;;IARGC,sBAAsB;eAAtBA;;;;wBAPW;AAOpB,SAASA,uBAAuB,KAET;IAFS,IAAA,EACrCC,QAAQ,EACoB,GAFS;IAGrC,qBACE,qBAACC,kBAAU;QAACC,WAAU;kBAAgCF;;AAE1D;AAEO,MAAMF,qBAAsB"}