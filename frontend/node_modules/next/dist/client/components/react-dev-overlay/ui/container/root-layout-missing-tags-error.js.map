{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/container/root-layout-missing-tags-error.tsx"], "sourcesContent": ["import { useCallback } from 'react'\nimport { HotlinkedText } from '../components/hot-linked-text'\nimport { ErrorOverlayLayout } from '../components/errors/error-overlay-layout/error-overlay-layout'\nimport type { ErrorBaseProps } from '../components/errors/error-overlay/error-overlay'\n\ninterface RootLayoutMissingTagsErrorProps extends ErrorBaseProps {\n  missingTags: string[]\n}\n\nexport function RootLayoutMissingTagsError({\n  missingTags,\n  ...props\n}: RootLayoutMissingTagsErrorProps) {\n  const noop = useCallback(() => {}, [])\n  const error = new Error(\n    `The following tags are missing in the Root Layout: ${missingTags\n      .map((tagName) => `<${tagName}>`)\n      .join(\n        ', '\n      )}.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags`\n  )\n  return (\n    <ErrorOverlayLayout\n      errorType=\"Missing Required HTML Tag\"\n      error={error}\n      errorMessage={<HotlinkedText text={error.message} />}\n      onClose={noop}\n      {...props}\n    />\n  )\n}\n"], "names": ["RootLayoutMissingTagsError", "missingTags", "props", "noop", "useCallback", "error", "Error", "map", "tagName", "join", "ErrorOverlayLayout", "errorType", "errorMessage", "HotlinkedText", "text", "message", "onClose"], "mappings": ";;;;+BASgBA;;;eAAAA;;;;uBATY;+BACE;oCACK;AAO5B,SAASA,2BAA2B,KAGT;IAHS,IAAA,EACzCC,WAAW,EACX,GAAGC,OAC6B,GAHS;IAIzC,MAAMC,OAAOC,IAAAA,kBAAW,EAAC,KAAO,GAAG,EAAE;IACrC,MAAMC,QAAQ,qBAMb,CANa,IAAIC,MAChB,AAAC,wDAAqDL,YACnDM,GAAG,CAAC,CAACC,UAAY,AAAC,MAAGA,UAAQ,KAC7BC,IAAI,CACH,QACA,8EALQ,qBAAA;eAAA;oBAAA;sBAAA;IAMd;IACA,qBACE,qBAACC,sCAAkB;QACjBC,WAAU;QACVN,OAAOA;QACPO,4BAAc,qBAACC,4BAAa;YAACC,MAAMT,MAAMU,OAAO;;QAChDC,SAASb;QACR,GAAGD,KAAK;;AAGf"}