{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.tsx"], "sourcesContent": ["import type { OverlayState } from '../../../../shared'\n\nimport { Suspense } from 'react'\nimport { BuildError } from '../../../container/build-error'\nimport { Errors } from '../../../container/errors'\nimport { RootLayoutMissingTagsError } from '../../../container/root-layout-missing-tags-error'\nimport { useDelayedRender } from '../../../hooks/use-delayed-render'\nimport type { ReadyRuntimeError } from '../../../../utils/get-error-by-type'\n\nconst transitionDurationMs = 200\n\nexport interface ErrorBaseProps {\n  rendered: boolean\n  transitionDurationMs: number\n  isTurbopack: boolean\n  versionInfo: OverlayState['versionInfo']\n}\n\nexport function ErrorOverlay({\n  state,\n  runtimeErrors,\n  isErrorOverlayOpen,\n  setIsErrorOverlayOpen,\n}: {\n  state: OverlayState\n  runtimeErrors: ReadyRuntimeError[]\n  isErrorOverlayOpen: boolean\n  setIsErrorOverlayOpen: (value: boolean) => void\n}) {\n  const isTurbopack = !!process.env.TURBOPACK\n\n  // This hook lets us do an exit animation before unmounting the component\n  const { mounted, rendered } = useDelayedRender(isErrorOverlayOpen, {\n    exitDelay: transitionDurationMs,\n  })\n\n  const commonProps = {\n    rendered,\n    transitionDurationMs,\n    isTurbopack,\n    versionInfo: state.versionInfo,\n  }\n\n  if (!!state.rootLayoutMissingTags?.length) {\n    return (\n      <RootLayoutMissingTagsError\n        {...commonProps}\n        // This is not a runtime error, forcedly display error overlay\n        rendered\n        missingTags={state.rootLayoutMissingTags}\n      />\n    )\n  }\n\n  if (state.buildError !== null) {\n    return (\n      <BuildError\n        {...commonProps}\n        message={state.buildError}\n        // This is not a runtime error, forcedly display error overlay\n        rendered\n      />\n    )\n  }\n\n  // No Runtime Errors.\n  if (!runtimeErrors.length) {\n    // Workaround React quirk that triggers \"Switch to client-side rendering\" if\n    // we return no Suspense boundary here.\n    return <Suspense />\n  }\n\n  if (!mounted) {\n    // Workaround React quirk that triggers \"Switch to client-side rendering\" if\n    // we return no Suspense boundary here.\n    return <Suspense />\n  }\n\n  return (\n    <Errors\n      {...commonProps}\n      debugInfo={state.debugInfo}\n      runtimeErrors={runtimeErrors}\n      onClose={() => {\n        setIsErrorOverlayOpen(false)\n      }}\n    />\n  )\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDurationMs", "state", "runtimeErrors", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "isTurbopack", "process", "env", "TURBOPACK", "mounted", "rendered", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitDelay", "commonProps", "versionInfo", "rootLayoutMissingTags", "length", "RootLayoutMissingTagsError", "missingTags", "buildError", "BuildError", "message", "Suspense", "Errors", "debugInfo", "onClose"], "mappings": ";;;;+BAkBgBA;;;eAAAA;;;;uBAhBS;4BACE;wBACJ;4CACoB;kCACV;AAGjC,MAAMC,uBAAuB;AAStB,SAASD,aAAa,KAU5B;IAV4B,IAAA,EAC3BE,KAAK,EACLC,aAAa,EACbC,kBAAkB,EAClBC,qBAAqB,EAMtB,GAV4B;QAyBrBH;IAdN,MAAMI,cAAc,CAAC,CAACC,QAAQC,GAAG,CAACC,SAAS;IAE3C,yEAAyE;IACzE,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,kCAAgB,EAACR,oBAAoB;QACjES,WAAWZ;IACb;IAEA,MAAMa,cAAc;QAClBH;QACAV;QACAK;QACAS,aAAab,MAAMa,WAAW;IAChC;IAEA,IAAI,CAAC,GAACb,+BAAAA,MAAMc,qBAAqB,qBAA3Bd,6BAA6Be,MAAM,GAAE;QACzC,qBACE,qBAACC,sDAA0B;YACxB,GAAGJ,WAAW;YACf,8DAA8D;YAC9DH,QAAQ;YACRQ,aAAajB,MAAMc,qBAAqB;;IAG9C;IAEA,IAAId,MAAMkB,UAAU,KAAK,MAAM;QAC7B,qBACE,qBAACC,sBAAU;YACR,GAAGP,WAAW;YACfQ,SAASpB,MAAMkB,UAAU;YACzB,8DAA8D;YAC9DT,QAAQ;;IAGd;IAEA,qBAAqB;IACrB,IAAI,CAACR,cAAcc,MAAM,EAAE;QACzB,4EAA4E;QAC5E,uCAAuC;QACvC,qBAAO,qBAACM,eAAQ;IAClB;IAEA,IAAI,CAACb,SAAS;QACZ,4EAA4E;QAC5E,uCAAuC;QACvC,qBAAO,qBAACa,eAAQ;IAClB;IAEA,qBACE,qBAACC,cAAM;QACJ,GAAGV,WAAW;QACfW,WAAWvB,MAAMuB,SAAS;QAC1BtB,eAAeA;QACfuB,SAAS;YACPrB,sBAAsB;QACxB;;AAGN"}