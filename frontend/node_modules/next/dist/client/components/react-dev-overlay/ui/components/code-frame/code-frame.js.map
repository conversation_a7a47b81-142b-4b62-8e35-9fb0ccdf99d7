{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/code-frame/code-frame.tsx"], "sourcesContent": ["import type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\n\nimport Anser from 'next/dist/compiled/anser'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\n\nimport { useMemo } from 'react'\nimport { HotlinkedText } from '../hot-linked-text'\nimport { getFrameSource } from '../../../utils/stack-frame'\nimport { useOpenInEditor } from '../../utils/use-open-in-editor'\nimport { ExternalIcon } from '../../icons/external'\nimport { FileIcon } from '../../icons/file'\n\nexport type CodeFrameProps = { stackFrame: StackFrame; codeFrame: string }\n\nexport function CodeFrame({ stackFrame, codeFrame }: CodeFrameProps) {\n  // Strip leading spaces out of the code frame:\n  const formattedFrame = useMemo<string>(() => {\n    const lines = codeFrame.split(/\\r?\\n/g)\n\n    // Find the minimum length of leading spaces after `|` in the code frame\n    const miniLeadingSpacesLength = lines\n      .map((line) =>\n        /^>? +\\d+ +\\| [ ]+/.exec(stripAnsi(line)) === null\n          ? null\n          : /^>? +\\d+ +\\| ( *)/.exec(stripAnsi(line))\n      )\n      .filter(Boolean)\n      .map((v) => v!.pop()!)\n      .reduce((c, n) => (isNaN(c) ? n.length : Math.min(c, n.length)), NaN)\n\n    // When the minimum length of leading spaces is greater than 1, remove them\n    // from the code frame to help the indentation looks better when there's a lot leading spaces.\n    if (miniLeadingSpacesLength > 1) {\n      return lines\n        .map((line, a) =>\n          ~(a = line.indexOf('|'))\n            ? line.substring(0, a) +\n              line.substring(a).replace(`^\\\\ {${miniLeadingSpacesLength}}`, '')\n            : line\n        )\n        .join('\\n')\n    }\n    return lines.join('\\n')\n  }, [codeFrame])\n\n  const decoded = useMemo(() => {\n    return Anser.ansiToJson(formattedFrame, {\n      json: true,\n      use_classes: true,\n      remove_empty: true,\n    })\n  }, [formattedFrame])\n\n  const open = useOpenInEditor({\n    file: stackFrame.file,\n    lineNumber: stackFrame.lineNumber,\n    column: stackFrame.column,\n  })\n\n  const fileExtension = stackFrame?.file?.split('.').pop()\n\n  // TODO: make the caret absolute\n  return (\n    <div data-nextjs-codeframe>\n      <div className=\"code-frame-header\">\n        {/* TODO: This is <div> in `Terminal` component.\n        Changing now will require multiple test snapshots updates.\n        Leaving as <div> as is trivial and does not affect the UI.\n        Change when the new redbox matcher `toDisplayRedbox` is used.\n        */}\n        <p className=\"code-frame-link\">\n          <span className=\"code-frame-icon\">\n            <FileIcon lang={fileExtension} />\n          </span>\n          <span data-text>\n            {getFrameSource(stackFrame)} @{' '}\n            <HotlinkedText text={stackFrame.methodName} />\n          </span>\n          <button\n            aria-label=\"Open in editor\"\n            data-with-open-in-editor-link-source-file\n            onClick={open}\n          >\n            <span className=\"code-frame-icon\" data-icon=\"right\">\n              <ExternalIcon width={16} height={16} />\n            </span>\n          </button>\n        </p>\n      </div>\n      <pre className=\"code-frame-pre\">\n        {decoded.map((entry, index) => (\n          <span\n            key={`frame-${index}`}\n            style={{\n              color: entry.fg ? `var(--color-${entry.fg})` : undefined,\n              ...(entry.decoration === 'bold'\n                ? // TODO(jiwon): This used to be 800, but the symbols like `─┬─` are\n                  // having longer width than expected on Geist Mono font-weight\n                  // above 600, hence a temporary fix is to use 500 for bold.\n                  { fontWeight: 500 }\n                : entry.decoration === 'italic'\n                  ? { fontStyle: 'italic' }\n                  : undefined),\n            }}\n          >\n            {entry.content}\n          </span>\n        ))}\n      </pre>\n    </div>\n  )\n}\n\nexport const CODE_FRAME_STYLES = `\n  [data-nextjs-codeframe] {\n    background-color: var(--color-background-200);\n    overflow: hidden;\n    color: var(--color-gray-1000);\n    text-overflow: ellipsis;\n    border: 1px solid var(--color-gray-400);\n    border-radius: 8px;\n    font-family: var(--font-stack-monospace);\n    font-size: var(--size-12);\n    line-height: var(--size-16);\n    margin: 8px 0;\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n  }\n\n  .code-frame-link,\n  .code-frame-pre {\n    padding: 12px;\n  }\n\n  .code-frame-link svg {\n    flex-shrink: 0;\n  }\n\n  .code-frame-link [data-text] {\n    display: inline-flex;\n    text-align: left;\n    margin: auto 6px;\n  }\n\n  .code-frame-pre {\n    white-space: pre-wrap;\n  }\n\n  .code-frame-header {\n    width: 100%;\n    transition: background 100ms ease-out;\n    border-radius: 8px 8px 0 0;\n    border-bottom: 1px solid var(--color-gray-400);\n  }\n\n  [data-with-open-in-editor-link-source-file] {\n    padding: 4px;\n    margin: -4px 0 -4px auto;\n    border-radius: var(--rounded-full);\n    margin-left: auto;\n\n    &:focus-visible {\n      outline: var(--focus-ring);\n      outline-offset: -2px;\n    }\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n  }\n\n  [data-nextjs-codeframe]::selection,\n  [data-nextjs-codeframe] *::selection {\n    background-color: var(--color-ansi-selection);\n  }\n\n  [data-nextjs-codeframe] *:not(a) {\n    color: inherit;\n    background-color: transparent;\n    font-family: var(--font-stack-monospace);\n  }\n\n  [data-nextjs-codeframe] > * {\n    margin: 0;\n  }\n\n  .code-frame-link {\n    display: flex;\n    margin: 0;\n    outline: 0;\n  }\n  .code-frame-link [data-icon='right'] {\n    margin-left: auto;\n  }\n\n  [data-nextjs-codeframe] div > pre {\n    overflow: hidden;\n    display: inline-block;\n  }\n\n  [data-nextjs-codeframe] svg {\n    color: var(--color-gray-900);\n  }\n`\n"], "names": ["CODE_FRAME_STYLES", "CodeFrame", "stackFrame", "codeFrame", "formattedFrame", "useMemo", "lines", "split", "miniLeadingSpacesLength", "map", "line", "exec", "stripAnsi", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "a", "indexOf", "substring", "replace", "join", "decoded", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "open", "useOpenInEditor", "file", "lineNumber", "column", "fileExtension", "div", "data-nextjs-codeframe", "className", "p", "span", "FileIcon", "lang", "data-text", "getFrameSource", "HotlinkedText", "text", "methodName", "button", "aria-label", "data-with-open-in-editor-link-source-file", "onClick", "data-icon", "ExternalIcon", "width", "height", "pre", "entry", "index", "style", "color", "fg", "undefined", "decoration", "fontWeight", "fontStyle", "content"], "mappings": ";;;;;;;;;;;;;;;IAiHaA,iBAAiB;eAAjBA;;IAnGGC,SAAS;eAATA;;;;;gEAZE;oEACI;uBAEE;+BACM;4BACC;iCACC;0BACH;sBACJ;AAIlB,SAASA,UAAU,KAAyC;IAAzC,IAAA,EAAEC,UAAU,EAAEC,SAAS,EAAkB,GAAzC;QA6CFD;IA5CtB,8CAA8C;IAC9C,MAAME,iBAAiBC,IAAAA,cAAO,EAAS;QACrC,MAAMC,QAAQH,UAAUI,KAAK,CAAC;QAE9B,wEAAwE;QACxE,MAAMC,0BAA0BF,MAC7BG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,WAAW,OAC1C,OACA,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,QAExCG,MAAM,CAACC,SACPL,GAAG,CAAC,CAACM,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;QAEnE,2EAA2E;QAC3E,8FAA8F;QAC9F,IAAIhB,0BAA0B,GAAG;YAC/B,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMe,IACV,CAAEA,CAAAA,IAAIf,KAAKgB,OAAO,CAAC,IAAG,IAClBhB,KAAKiB,SAAS,CAAC,GAAGF,KAClBf,KAAKiB,SAAS,CAACF,GAAGG,OAAO,CAAC,AAAC,UAAOpB,0BAAwB,KAAI,MAC9DE,MAELmB,IAAI,CAAC;QACV;QACA,OAAOvB,MAAMuB,IAAI,CAAC;IACpB,GAAG;QAAC1B;KAAU;IAEd,MAAM2B,UAAUzB,IAAAA,cAAO,EAAC;QACtB,OAAO0B,cAAK,CAACC,UAAU,CAAC5B,gBAAgB;YACtC6B,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAAC/B;KAAe;IAEnB,MAAMgC,OAAOC,IAAAA,gCAAe,EAAC;QAC3BC,MAAMpC,WAAWoC,IAAI;QACrBC,YAAYrC,WAAWqC,UAAU;QACjCC,QAAQtC,WAAWsC,MAAM;IAC3B;IAEA,MAAMC,gBAAgBvC,+BAAAA,mBAAAA,WAAYoC,IAAI,qBAAhBpC,iBAAkBK,KAAK,CAAC,KAAKS,GAAG;IAEtD,gCAAgC;IAChC,qBACE,sBAAC0B;QAAIC,uBAAqB;;0BACxB,qBAACD;gBAAIE,WAAU;0BAMb,cAAA,sBAACC;oBAAED,WAAU;;sCACX,qBAACE;4BAAKF,WAAU;sCACd,cAAA,qBAACG,cAAQ;gCAACC,MAAMP;;;sCAElB,sBAACK;4BAAKG,WAAS;;gCACZC,IAAAA,0BAAc,EAAChD;gCAAY;gCAAG;8CAC/B,qBAACiD,4BAAa;oCAACC,MAAMlD,WAAWmD,UAAU;;;;sCAE5C,qBAACC;4BACCC,cAAW;4BACXC,2CAAyC;4BACzCC,SAASrB;sCAET,cAAA,qBAACU;gCAAKF,WAAU;gCAAkBc,aAAU;0CAC1C,cAAA,qBAACC,sBAAY;oCAACC,OAAO;oCAAIC,QAAQ;;;;;;;0BAKzC,qBAACC;gBAAIlB,WAAU;0BACZd,QAAQrB,GAAG,CAAC,CAACsD,OAAOC,sBACnB,qBAAClB;wBAECmB,OAAO;4BACLC,OAAOH,MAAMI,EAAE,GAAG,AAAC,iBAAcJ,MAAMI,EAAE,GAAC,MAAKC;4BAC/C,GAAIL,MAAMM,UAAU,KAAK,SAErB,8DAA8D;4BAC9D,2DAA2D;4BAC3D;gCAAEC,YAAY;4BAAI,IAClBP,MAAMM,UAAU,KAAK,WACnB;gCAAEE,WAAW;4BAAS,IACtBH,SAAS;wBACjB;kCAECL,MAAMS,OAAO;uBAbT,AAAC,WAAQR;;;;AAmB1B;AAEO,MAAMhE,oBAAqB"}