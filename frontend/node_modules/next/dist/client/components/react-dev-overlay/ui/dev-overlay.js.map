{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/ui/dev-overlay.tsx"], "sourcesContent": ["import type { OverlayState } from '../shared'\n\nimport { ShadowPortal } from './components/shadow-portal'\nimport { Base } from './styles/base'\nimport { ComponentStyles } from './styles/component-styles'\nimport { CssReset } from './styles/css-reset'\nimport { Colors } from './styles/colors'\nimport { ErrorOverlay } from './components/errors/error-overlay/error-overlay'\nimport { DevToolsIndicator } from './components/errors/dev-tools-indicator/dev-tools-indicator'\nimport { RenderError } from './container/runtime-error/render-error'\nimport { DarkTheme } from './styles/dark-theme'\n\nexport function DevOverlay({\n  state,\n  isErrorOverlayOpen,\n  setIsErrorOverlayOpen,\n}: {\n  state: OverlayState\n  isErrorOverlayOpen: boolean\n  setIsErrorOverlayOpen: (\n    isErrorOverlayOpen: boolean | ((prev: boolean) => boolean)\n  ) => void\n}) {\n  return (\n    <ShadowPortal>\n      <CssReset />\n      <Base />\n      <Colors />\n      <ComponentStyles />\n      <DarkTheme />\n\n      <RenderError state={state} isAppDir={true}>\n        {({ runtimeErrors, totalErrorCount }) => {\n          const isBuildError = runtimeErrors.length === 0\n          return (\n            <>\n              <DevToolsIndicator\n                state={state}\n                errorCount={totalErrorCount}\n                isBuildError={isBuildError}\n                setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n              />\n\n              <ErrorOverlay\n                state={state}\n                runtimeErrors={runtimeErrors}\n                isErrorOverlayOpen={isErrorOverlayOpen}\n                setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n              />\n            </>\n          )\n        }}\n      </RenderError>\n    </ShadowPortal>\n  )\n}\n"], "names": ["DevOverlay", "state", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "ShadowPort<PERSON>", "CssReset", "Base", "Colors", "ComponentStyles", "DarkTheme", "RenderError", "isAppDir", "runtimeErrors", "totalErrorCount", "isBuildError", "length", "DevToolsIndicator", "errorCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;;8BAVa;sBACR;iCACW;0BACP;wBACF;8BACM;mCACK;6BACN;2BACF;AAEnB,SAASA,WAAW,KAU1B;IAV0B,IAAA,EACzBC,KAAK,EACLC,kBAAkB,EAClBC,qBAAqB,EAOtB,GAV0B;IAWzB,qBACE,sBAACC,0BAAY;;0BACX,qBAACC,kBAAQ;0BACT,qBAACC,UAAI;0BACL,qBAACC,cAAM;0BACP,qBAACC,gCAAe;0BAChB,qBAACC,oBAAS;0BAEV,qBAACC,wBAAW;gBAACT,OAAOA;gBAAOU,UAAU;0BAClC;wBAAC,EAAEC,aAAa,EAAEC,eAAe,EAAE;oBAClC,MAAMC,eAAeF,cAAcG,MAAM,KAAK;oBAC9C,qBACE;;0CACE,qBAACC,oCAAiB;gCAChBf,OAAOA;gCACPgB,YAAYJ;gCACZC,cAAcA;gCACdX,uBAAuBA;;0CAGzB,qBAACe,0BAAY;gCACXjB,OAAOA;gCACPW,eAAeA;gCACfV,oBAAoBA;gCACpBC,uBAAuBA;;;;gBAI/B;;;;AAIR"}