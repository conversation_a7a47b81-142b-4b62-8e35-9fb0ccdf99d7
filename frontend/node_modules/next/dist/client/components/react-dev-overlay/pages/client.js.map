{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/client.ts"], "sourcesContent": ["import * as Bus from './bus'\nimport { parseStack } from '../utils/parse-stack'\nimport { parseComponentStack } from '../utils/parse-component-stack'\nimport {\n  hydrationErrorState,\n  storeHydrationErrorStateFromConsoleArgs,\n} from '../../errors/hydration-error-info'\nimport {\n  ACTION_BEFORE_REFRESH,\n  ACTION_BUILD_ERROR,\n  ACTION_BUILD_OK,\n  ACTION_DEV_INDICATOR,\n  ACTION_REFRESH,\n  ACTION_STATIC_INDICATOR,\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n  ACTION_VERSION_INFO,\n} from '../shared'\nimport type { VersionInfo } from '../../../../server/dev/parse-version-info'\nimport { attachHydrationErrorState } from '../../errors/attach-hydration-error-state'\nimport type { DevIndicatorServerState } from '../../../../server/dev/dev-indicator-server-state'\n\nlet isRegistered = false\nlet stackTraceLimit: number | undefined = undefined\n\nfunction handleError(error: unknown) {\n  if (!error || !(error instanceof Error) || typeof error.stack !== 'string') {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  attachHydrationErrorState(error)\n\n  const componentStackTrace =\n    (error as any)._componentStack || hydrationErrorState.componentStack\n  const componentStackFrames =\n    typeof componentStackTrace === 'string'\n      ? parseComponentStack(componentStackTrace)\n      : undefined\n\n  // Skip ModuleBuildError and ModuleNotFoundError, as it will be sent through onBuildError callback.\n  // This is to avoid same error as different type showing up on client to cause flashing.\n  if (\n    error.name !== 'ModuleBuildError' &&\n    error.name !== 'ModuleNotFoundError'\n  ) {\n    Bus.emit({\n      type: ACTION_UNHANDLED_ERROR,\n      reason: error,\n      frames: parseStack(error.stack),\n      componentStackFrames,\n    })\n  }\n}\n\nlet origConsoleError = console.error\nfunction nextJsHandleConsoleError(...args: any[]) {\n  // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n  const error = process.env.NODE_ENV !== 'production' ? args[1] : args[0]\n  storeHydrationErrorStateFromConsoleArgs(...args)\n  handleError(error)\n  origConsoleError.apply(window.console, args)\n}\n\nfunction onUnhandledError(event: ErrorEvent) {\n  const error = event?.error\n  handleError(error)\n}\n\nfunction onUnhandledRejection(ev: PromiseRejectionEvent) {\n  const reason = ev?.reason\n  if (\n    !reason ||\n    !(reason instanceof Error) ||\n    typeof reason.stack !== 'string'\n  ) {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  const e = reason\n  Bus.emit({\n    type: ACTION_UNHANDLED_REJECTION,\n    reason: reason,\n    frames: parseStack(e.stack!),\n  })\n}\n\nexport function register() {\n  if (isRegistered) {\n    return\n  }\n  isRegistered = true\n\n  try {\n    const limit = Error.stackTraceLimit\n    Error.stackTraceLimit = 50\n    stackTraceLimit = limit\n  } catch {}\n\n  window.addEventListener('error', onUnhandledError)\n  window.addEventListener('unhandledrejection', onUnhandledRejection)\n  window.console.error = nextJsHandleConsoleError\n}\n\nexport function unregister() {\n  if (!isRegistered) {\n    return\n  }\n  isRegistered = false\n\n  if (stackTraceLimit !== undefined) {\n    try {\n      Error.stackTraceLimit = stackTraceLimit\n    } catch {}\n    stackTraceLimit = undefined\n  }\n\n  window.removeEventListener('error', onUnhandledError)\n  window.removeEventListener('unhandledrejection', onUnhandledRejection)\n  window.console.error = origConsoleError\n}\n\nexport function onBuildOk() {\n  Bus.emit({ type: ACTION_BUILD_OK })\n}\n\nexport function onBuildError(message: string) {\n  Bus.emit({ type: ACTION_BUILD_ERROR, message })\n}\n\nexport function onRefresh() {\n  Bus.emit({ type: ACTION_REFRESH })\n}\n\nexport function onBeforeRefresh() {\n  Bus.emit({ type: ACTION_BEFORE_REFRESH })\n}\n\nexport function onVersionInfo(versionInfo: VersionInfo) {\n  Bus.emit({ type: ACTION_VERSION_INFO, versionInfo })\n}\n\nexport function onStaticIndicator(isStatic: boolean) {\n  Bus.emit({ type: ACTION_STATIC_INDICATOR, staticIndicator: isStatic })\n}\n\nexport function onDevIndicator(devIndicatorsState: DevIndicatorServerState) {\n  Bus.emit({ type: ACTION_DEV_INDICATOR, devIndicator: devIndicatorsState })\n}\n\nexport { getErrorByType } from '../utils/get-error-by-type'\nexport { getServerError } from '../utils/node-stack-frames'\n"], "names": ["getErrorByType", "getServerError", "onBeforeRefresh", "onBuildError", "onBuildOk", "onDevIndicator", "onRefresh", "onStaticIndicator", "onVersionInfo", "register", "unregister", "isRegistered", "stackTraceLimit", "undefined", "handleError", "error", "Error", "stack", "attachHydrationErrorState", "componentStackTrace", "_componentStack", "hydrationErrorState", "componentStack", "componentStackFrames", "parseComponentStack", "name", "Bus", "emit", "type", "ACTION_UNHANDLED_ERROR", "reason", "frames", "parseStack", "origConsoleError", "console", "nextJsHandleConsoleError", "args", "process", "env", "NODE_ENV", "storeHydrationErrorStateFromConsoleArgs", "apply", "window", "onUnhandledError", "event", "onUnhandledRejection", "ev", "e", "ACTION_UNHANDLED_REJECTION", "limit", "addEventListener", "removeEventListener", "ACTION_BUILD_OK", "message", "ACTION_BUILD_ERROR", "ACTION_REFRESH", "ACTION_BEFORE_REFRESH", "versionInfo", "ACTION_VERSION_INFO", "isStatic", "ACTION_STATIC_INDICATOR", "staticIndicator", "devIndicatorsState", "ACTION_DEV_INDICATOR", "devIndicator"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAuJSA,cAAc;eAAdA,8BAAc;;IACdC,cAAc;eAAdA,+BAAc;;IAjBPC,eAAe;eAAfA;;IARAC,YAAY;eAAZA;;IAJAC,SAAS;eAATA;;IAwBAC,cAAc;eAAdA;;IAhBAC,SAAS;eAATA;;IAYAC,iBAAiB;eAAjBA;;IAJAC,aAAa;eAAbA;;IAnDAC,QAAQ;eAARA;;IAiBAC,UAAU;eAAVA;;;;+DAzGK;4BACM;qCACS;oCAI7B;wBAWA;2CAEmC;gCAoIX;iCACA;AAlI/B,IAAIC,eAAe;AACnB,IAAIC,kBAAsCC;AAE1C,SAASC,YAAYC,KAAc;IACjC,IAAI,CAACA,SAAS,CAAEA,CAAAA,iBAAiBC,KAAI,KAAM,OAAOD,MAAME,KAAK,KAAK,UAAU;QAC1E,8DAA8D;QAC9D;IACF;IAEAC,IAAAA,oDAAyB,EAACH;IAE1B,MAAMI,sBACJ,AAACJ,MAAcK,eAAe,IAAIC,uCAAmB,CAACC,cAAc;IACtE,MAAMC,uBACJ,OAAOJ,wBAAwB,WAC3BK,IAAAA,wCAAmB,EAACL,uBACpBN;IAEN,mGAAmG;IACnG,wFAAwF;IACxF,IACEE,MAAMU,IAAI,KAAK,sBACfV,MAAMU,IAAI,KAAK,uBACf;QACAC,KAAIC,IAAI,CAAC;YACPC,MAAMC,8BAAsB;YAC5BC,QAAQf;YACRgB,QAAQC,IAAAA,sBAAU,EAACjB,MAAME,KAAK;YAC9BM;QACF;IACF;AACF;AAEA,IAAIU,mBAAmBC,QAAQnB,KAAK;AACpC,SAASoB;IAAyB,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAc;;IAC9C,iJAAiJ;IACjJ,MAAMrB,QAAQsB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAeH,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;IACvEI,IAAAA,2DAAuC,KAAIJ;IAC3CtB,YAAYC;IACZkB,iBAAiBQ,KAAK,CAACC,OAAOR,OAAO,EAAEE;AACzC;AAEA,SAASO,iBAAiBC,KAAiB;IACzC,MAAM7B,QAAQ6B,yBAAAA,MAAO7B,KAAK;IAC1BD,YAAYC;AACd;AAEA,SAAS8B,qBAAqBC,EAAyB;IACrD,MAAMhB,SAASgB,sBAAAA,GAAIhB,MAAM;IACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBd,KAAI,KACxB,OAAOc,OAAOb,KAAK,KAAK,UACxB;QACA,8DAA8D;QAC9D;IACF;IAEA,MAAM8B,IAAIjB;IACVJ,KAAIC,IAAI,CAAC;QACPC,MAAMoB,kCAA0B;QAChClB,QAAQA;QACRC,QAAQC,IAAAA,sBAAU,EAACe,EAAE9B,KAAK;IAC5B;AACF;AAEO,SAASR;IACd,IAAIE,cAAc;QAChB;IACF;IACAA,eAAe;IAEf,IAAI;QACF,MAAMsC,QAAQjC,MAAMJ,eAAe;QACnCI,MAAMJ,eAAe,GAAG;QACxBA,kBAAkBqC;IACpB,EAAE,UAAM,CAAC;IAETP,OAAOQ,gBAAgB,CAAC,SAASP;IACjCD,OAAOQ,gBAAgB,CAAC,sBAAsBL;IAC9CH,OAAOR,OAAO,CAACnB,KAAK,GAAGoB;AACzB;AAEO,SAASzB;IACd,IAAI,CAACC,cAAc;QACjB;IACF;IACAA,eAAe;IAEf,IAAIC,oBAAoBC,WAAW;QACjC,IAAI;YACFG,MAAMJ,eAAe,GAAGA;QAC1B,EAAE,UAAM,CAAC;QACTA,kBAAkBC;IACpB;IAEA6B,OAAOS,mBAAmB,CAAC,SAASR;IACpCD,OAAOS,mBAAmB,CAAC,sBAAsBN;IACjDH,OAAOR,OAAO,CAACnB,KAAK,GAAGkB;AACzB;AAEO,SAAS7B;IACdsB,KAAIC,IAAI,CAAC;QAAEC,MAAMwB,uBAAe;IAAC;AACnC;AAEO,SAASjD,aAAakD,OAAe;IAC1C3B,KAAIC,IAAI,CAAC;QAAEC,MAAM0B,0BAAkB;QAAED;IAAQ;AAC/C;AAEO,SAAS/C;IACdoB,KAAIC,IAAI,CAAC;QAAEC,MAAM2B,sBAAc;IAAC;AAClC;AAEO,SAASrD;IACdwB,KAAIC,IAAI,CAAC;QAAEC,MAAM4B,6BAAqB;IAAC;AACzC;AAEO,SAAShD,cAAciD,WAAwB;IACpD/B,KAAIC,IAAI,CAAC;QAAEC,MAAM8B,2BAAmB;QAAED;IAAY;AACpD;AAEO,SAASlD,kBAAkBoD,QAAiB;IACjDjC,KAAIC,IAAI,CAAC;QAAEC,MAAMgC,+BAAuB;QAAEC,iBAAiBF;IAAS;AACtE;AAEO,SAAStD,eAAeyD,kBAA2C;IACxEpC,KAAIC,IAAI,CAAC;QAAEC,MAAMmC,4BAAoB;QAAEC,cAAcF;IAAmB;AAC1E"}