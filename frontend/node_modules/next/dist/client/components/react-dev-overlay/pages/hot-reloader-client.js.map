{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/hot-reloader-client.ts"], "sourcesContent": ["// TODO: Remove use of `any` type. Fix no-use-before-define violations.\n/* eslint-disable @typescript-eslint/no-use-before-define */\n/**\n * MIT License\n *\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n// This file is a modified version of the Create React App HMR dev client that\n// can be found here:\n// https://github.com/facebook/create-react-app/blob/v3.4.1/packages/react-dev-utils/webpackHotDevClient.js\n\nimport {\n  register,\n  onBuildError,\n  onBuildOk,\n  onBeforeRefresh,\n  onRefresh,\n  onVersionInfo,\n  onStaticIndicator,\n  onDevIndicator,\n} from './client'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { addMessageListener, sendMessage } from './websocket'\nimport formatWebpackMessages from '../utils/format-webpack-messages'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport { extractModulesFromTurbopackMessage } from '../../../../server/dev/extract-modules-from-turbopack-message'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from '../shared'\nimport { RuntimeErrorHandler } from '../../errors/runtime-error-handler'\n// This alternative WebpackDevServer combines the functionality of:\n// https://github.com/webpack/webpack-dev-server/blob/webpack-1/client/index.js\n// https://github.com/webpack/webpack/blob/webpack-1/hot/dev-server.js\n\n// It only supports their simplest configuration (hot updates on same server).\n// It makes some opinionated choices on top, like adding a syntax error overlay\n// that looks similar to our console output. The error overlay is inspired by:\n// https://github.com/glenjamin/webpack-hot-middleware\n\ndeclare global {\n  const __webpack_hash__: string\n  interface Window {\n    __nextDevClientId: number\n    __NEXT_HMR_LATENCY_CB: any\n  }\n}\n\nwindow.__nextDevClientId = Math.round(Math.random() * 100 + Date.now())\n\nlet customHmrEventHandler: any\nlet turbopackMessageListeners: ((msg: TurbopackMsgToBrowser) => void)[] = []\nlet MODE: 'webpack' | 'turbopack' = 'webpack'\nexport default function connect(mode: 'webpack' | 'turbopack') {\n  MODE = mode\n  register()\n\n  addMessageListener((payload) => {\n    if (!('action' in payload)) {\n      return\n    }\n\n    try {\n      processMessage(payload)\n    } catch (err: any) {\n      console.warn(\n        '[HMR] Invalid message: ' +\n          JSON.stringify(payload) +\n          '\\n' +\n          (err?.stack ?? '')\n      )\n    }\n  })\n\n  return {\n    subscribeToHmrEvent(handler: any) {\n      customHmrEventHandler = handler\n    },\n    onUnrecoverableError() {\n      RuntimeErrorHandler.hadRuntimeError = true\n    },\n    addTurbopackMessageListener(cb: (msg: TurbopackMsgToBrowser) => void) {\n      turbopackMessageListeners.push(cb)\n    },\n    sendTurbopackMessage(msg: string) {\n      sendMessage(msg)\n    },\n    handleUpdateError(err: unknown) {\n      performFullReload(err)\n    },\n  }\n}\n\n// Remember some state related to hot module replacement.\nvar isFirstCompilation = true\nvar mostRecentCompilationHash: string | null = null\nvar hasCompileErrors = false\n\nfunction clearOutdatedErrors() {\n  // Clean up outdated compile errors, if any.\n  if (typeof console !== 'undefined' && typeof console.clear === 'function') {\n    if (hasCompileErrors) {\n      console.clear()\n    }\n  }\n}\n\n// Successful compilation.\nfunction handleSuccess() {\n  clearOutdatedErrors()\n\n  if (MODE === 'webpack') {\n    const isHotUpdate =\n      !isFirstCompilation ||\n      (window.__NEXT_DATA__.page !== '/_error' && isUpdateAvailable())\n    isFirstCompilation = false\n    hasCompileErrors = false\n\n    // Attempt to apply hot updates or reload.\n    if (isHotUpdate) {\n      tryApplyUpdates(onBeforeFastRefresh, onFastRefresh)\n    }\n  } else {\n    reportHmrLatency([...turbopackUpdatedModules])\n    onBuildOk()\n  }\n}\n\n// Compilation with warnings (e.g. ESLint).\nfunction handleWarnings(warnings: any) {\n  clearOutdatedErrors()\n\n  const isHotUpdate = !isFirstCompilation\n  isFirstCompilation = false\n  hasCompileErrors = false\n\n  function printWarnings() {\n    // Print warnings to the console.\n    const formatted = formatWebpackMessages({\n      warnings: warnings,\n      errors: [],\n    })\n\n    if (typeof console !== 'undefined' && typeof console.warn === 'function') {\n      for (let i = 0; i < formatted.warnings?.length; i++) {\n        if (i === 5) {\n          console.warn(\n            'There were more warnings in other files.\\n' +\n              'You can find a complete log in the terminal.'\n          )\n          break\n        }\n        console.warn(stripAnsi(formatted.warnings[i]))\n      }\n    }\n  }\n\n  printWarnings()\n\n  // Attempt to apply hot updates or reload.\n  if (isHotUpdate) {\n    tryApplyUpdates(onBeforeFastRefresh, onFastRefresh)\n  }\n}\n\n// Compilation with errors (e.g. syntax error or missing modules).\nfunction handleErrors(errors: any) {\n  clearOutdatedErrors()\n\n  isFirstCompilation = false\n  hasCompileErrors = true\n\n  // \"Massage\" webpack messages.\n  var formatted = formatWebpackMessages({\n    errors: errors,\n    warnings: [],\n  })\n\n  // Only show the first error.\n\n  onBuildError(formatted.errors[0])\n\n  // Also log them to the console.\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    for (var i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n  }\n\n  // Do not attempt to reload now.\n  // We will reload on next success instead.\n  if (process.env.__NEXT_TEST_MODE) {\n    if (self.__NEXT_HMR_CB) {\n      self.__NEXT_HMR_CB(formatted.errors[0])\n      self.__NEXT_HMR_CB = null\n    }\n  }\n}\n\nlet startLatency: number | null = null\nlet turbopackLastUpdateLatency: number | null = null\nlet turbopackUpdatedModules: Set<string> = new Set()\nlet isrManifest: Record<string, boolean> = {}\n\nfunction onBeforeFastRefresh(updatedModules: string[]) {\n  if (updatedModules.length > 0) {\n    // Only trigger a pending state if we have updates to apply\n    // (cf. onFastRefresh)\n    onBeforeRefresh()\n  }\n}\n\nfunction onFastRefresh(updatedModules: ReadonlyArray<string> = []) {\n  onBuildOk()\n  if (updatedModules.length === 0) {\n    return\n  }\n\n  onRefresh()\n\n  reportHmrLatency()\n}\n\nfunction reportHmrLatency(updatedModules: ReadonlyArray<string> = []) {\n  if (!startLatency) return\n  // turbopack has a debounce for the BUILT event which we don't want to\n  // incorrectly show in this number, use the last TURBOPACK_MESSAGE time\n  let endLatency = turbopackLastUpdateLatency ?? Date.now()\n  const latency = endLatency - startLatency\n  console.log(`[Fast Refresh] done in ${latency}ms`)\n  sendMessage(\n    JSON.stringify({\n      event: 'client-hmr-latency',\n      id: window.__nextDevClientId,\n      startTime: startLatency,\n      endTime: endLatency,\n      page: window.location.pathname,\n      updatedModules,\n      // Whether the page (tab) was hidden at the time the event occurred.\n      // This can impact the accuracy of the event's timing.\n      isPageHidden: document.visibilityState === 'hidden',\n    })\n  )\n  if (self.__NEXT_HMR_LATENCY_CB) {\n    self.__NEXT_HMR_LATENCY_CB(latency)\n  }\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\nexport function handleStaticIndicator() {\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    const routeInfo = window.next.router.components[window.next.router.pathname]\n    const pageComponent = routeInfo?.Component\n    const appComponent = window.next.router.components['/_app']?.Component\n    const isDynamicPage =\n      Boolean(pageComponent?.getInitialProps) || Boolean(routeInfo.__N_SSP)\n    const hasAppGetInitialProps =\n      Boolean(appComponent?.getInitialProps) &&\n      appComponent?.getInitialProps !== appComponent?.origGetInitialProps\n\n    const isPageStatic =\n      window.location.pathname in isrManifest ||\n      (!isDynamicPage && !hasAppGetInitialProps)\n\n    onStaticIndicator(isPageStatic)\n  }\n}\n\n/** Handles messages from the sevrer for the Pages Router. */\nfunction processMessage(obj: HMR_ACTION_TYPES) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  // Use turbopack message for analytics, (still need built for webpack)\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      isrManifest = obj.data\n      handleStaticIndicator()\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      startLatency = Date.now()\n      turbopackLastUpdateLatency = null\n      turbopackUpdatedModules.clear()\n      console.log('[Fast Refresh] rebuilding')\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      if (obj.hash) handleAvailableHash(obj.hash)\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) onVersionInfo(obj.versionInfo)\n      if ('devIndicator' in obj) onDevIndicator(obj.devIndicator)\n\n      const hasErrors = Boolean(errors && errors.length)\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleErrors(errors)\n      }\n\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleWarnings(warnings)\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: window.__nextDevClientId,\n        })\n      )\n      return handleSuccess()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      if (hasCompileErrors || RuntimeErrorHandler.hadRuntimeError) {\n        window.location.reload()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n          data: obj.data,\n        })\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      const updatedModules = extractModulesFromTurbopackMessage(obj.data)\n      onBeforeFastRefresh([...updatedModules])\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n          data: obj.data,\n        })\n      }\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null)\n      }\n      onRefresh()\n      for (const module of updatedModules) {\n        turbopackUpdatedModules.add(module)\n      }\n      turbopackLastUpdateLatency = Date.now()\n      break\n    }\n    default: {\n      if (customHmrEventHandler) {\n        customHmrEventHandler(obj)\n        break\n      }\n      break\n    }\n  }\n}\n\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: () => void) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: string) {\n      if (status === 'idle') {\n        // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n    module.hot.addStatusHandler(handler)\n  }\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(\n  onBeforeHotUpdate: ((updatedModules: string[]) => unknown) | undefined,\n  onHotUpdateSuccess: (updatedModules: string[]) => unknown\n) {\n  // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n  if (!module.hot) {\n    // HotModuleReplacementPlugin is not in Webpack configuration.\n    console.error('HotModuleReplacementPlugin is not in Webpack configuration.')\n    // window.location.reload();\n    return\n  }\n\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    onBuildOk()\n    return\n  }\n\n  function handleApplyUpdates(err: any, updatedModules: string[] | null) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || !updatedModules) {\n      if (err) {\n        console.warn(\n          '[Fast Refresh] performing full reload\\n\\n' +\n            \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n            'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n            'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n            'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n            'Fast Refresh requires at least one parent function component in your React tree.'\n        )\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(\n          '[Fast Refresh] performing full reload because your application had an unrecoverable error'\n        )\n      }\n      performFullReload(err)\n      return\n    }\n\n    if (typeof onHotUpdateSuccess === 'function') {\n      // Maybe we want to do something.\n      onHotUpdateSuccess(updatedModules)\n    }\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      // However, this time, don't trigger a pending refresh state.\n      tryApplyUpdates(\n        updatedModules.length > 0 ? undefined : onBeforeHotUpdate,\n        updatedModules.length > 0 ? onBuildOk : onHotUpdateSuccess\n      )\n    } else {\n      onBuildOk()\n      if (process.env.__NEXT_TEST_MODE) {\n        afterApplyUpdates(() => {\n          if (self.__NEXT_HMR_CB) {\n            self.__NEXT_HMR_CB()\n            self.__NEXT_HMR_CB = null\n          }\n        })\n      }\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: any) => {\n      if (!updatedModules) {\n        return null\n      }\n\n      if (typeof onBeforeHotUpdate === 'function') {\n        onBeforeHotUpdate(updatedModules)\n      }\n      // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: any) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\nexport function performFullReload(err: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  window.location.reload()\n}\n"], "names": ["connect", "handleStaticIndicator", "performFullReload", "window", "__nextDevClientId", "Math", "round", "random", "Date", "now", "customHmrEventHandler", "turbopackMessageListeners", "MODE", "mode", "register", "addMessageListener", "payload", "processMessage", "err", "console", "warn", "JSON", "stringify", "stack", "subscribeToHmrEvent", "handler", "onUnrecoverableError", "RuntimeError<PERSON>andler", "hadRuntimeError", "addTurbopackMessageListener", "cb", "push", "sendTurbopackMessage", "msg", "sendMessage", "handleUpdateError", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "clear", "handleSuccess", "isHotUpdate", "__NEXT_DATA__", "page", "isUpdateAvailable", "tryApplyUpdates", "onBeforeFastRefresh", "onFastRefresh", "reportHmrLatency", "turbopackUpdatedModules", "onBuildOk", "handleWarnings", "warnings", "printWarnings", "formatted", "formatWebpackMessages", "errors", "i", "length", "stripAnsi", "handleErrors", "onBuildError", "error", "process", "env", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "startLatency", "turbopackLastUpdateLatency", "Set", "isrManifest", "updatedModules", "onBeforeRefresh", "onRefresh", "endLatency", "latency", "log", "event", "id", "startTime", "endTime", "location", "pathname", "isPageHidden", "document", "visibilityState", "__NEXT_HMR_LATENCY_CB", "handleAvailableHash", "hash", "__NEXT_DEV_INDICATOR", "routeInfo", "next", "router", "components", "pageComponent", "Component", "appComponent", "isDynamicPage", "Boolean", "getInitialProps", "__N_SSP", "hasAppGetInitialProps", "origGetInitialProps", "isPageStatic", "onStaticIndicator", "obj", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "ISR_MANIFEST", "data", "BUILDING", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "onDevIndicator", "devIndicator", "hasErrors", "errorCount", "clientId", "hasWarnings", "warningCount", "SERVER_COMPONENT_CHANGES", "reload", "SERVER_ERROR", "errorJSON", "message", "parse", "Error", "TURBOPACK_CONNECTED", "listener", "type", "TURBOPACK_MESSAGE", "extractModulesFromTurbopackMessage", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "module", "add", "__webpack_hash__", "canApplyUpdates", "hot", "status", "afterApplyUpdates", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "onBeforeHotUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "undefined", "check", "then", "apply", "stackTrace", "split", "slice", "join", "dependency<PERSON><PERSON>n"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,8EAA8E;AAC9E,qBAAqB;AACrB,2GAA2G;;;;;;;;;;;;;;;;;IA6C3G,OAsCC;eAtCuBA;;IAyMRC,qBAAqB;eAArBA;;IA6PAC,iBAAiB;eAAjBA;;;;wBAxeT;oEACe;2BAC0B;gFACd;kCACU;oDAKO;wBACE;qCACjB;AAkBpCC,OAAOC,iBAAiB,GAAGC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEpE,IAAIC;AACJ,IAAIC,4BAAsE,EAAE;AAC5E,IAAIC,OAAgC;AACrB,SAASZ,QAAQa,IAA6B;IAC3DD,OAAOC;IACPC,IAAAA,gBAAQ;IAERC,IAAAA,6BAAkB,EAAC,CAACC;QAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;YAC1B;QACF;QAEA,IAAI;YACFC,eAAeD;QACjB,EAAE,OAAOE,KAAU;gBAKZA;YAJLC,QAAQC,IAAI,CACV,4BACEC,KAAKC,SAAS,CAACN,WACf,OACCE,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKK,KAAK,YAAVL,aAAc,EAAC;QAEtB;IACF;IAEA,OAAO;QACLM,qBAAoBC,OAAY;YAC9Bf,wBAAwBe;QAC1B;QACAC;YACEC,wCAAmB,CAACC,eAAe,GAAG;QACxC;QACAC,6BAA4BC,EAAwC;YAClEnB,0BAA0BoB,IAAI,CAACD;QACjC;QACAE,sBAAqBC,GAAW;YAC9BC,IAAAA,sBAAW,EAACD;QACd;QACAE,mBAAkBjB,GAAY;YAC5BhB,kBAAkBgB;QACpB;IACF;AACF;AAEA,yDAAyD;AACzD,IAAIkB,qBAAqB;AACzB,IAAIC,4BAA2C;AAC/C,IAAIC,mBAAmB;AAEvB,SAASC;IACP,4CAA4C;IAC5C,IAAI,OAAOpB,YAAY,eAAe,OAAOA,QAAQqB,KAAK,KAAK,YAAY;QACzE,IAAIF,kBAAkB;YACpBnB,QAAQqB,KAAK;QACf;IACF;AACF;AAEA,0BAA0B;AAC1B,SAASC;IACPF;IAEA,IAAI3B,SAAS,WAAW;QACtB,MAAM8B,cACJ,CAACN,sBACAjC,OAAOwC,aAAa,CAACC,IAAI,KAAK,aAAaC;QAC9CT,qBAAqB;QACrBE,mBAAmB;QAEnB,0CAA0C;QAC1C,IAAII,aAAa;YACfI,gBAAgBC,qBAAqBC;QACvC;IACF,OAAO;QACLC,iBAAiB;eAAIC;SAAwB;QAC7CC,IAAAA,iBAAS;IACX;AACF;AAEA,2CAA2C;AAC3C,SAASC,eAAeC,QAAa;IACnCd;IAEA,MAAMG,cAAc,CAACN;IACrBA,qBAAqB;IACrBE,mBAAmB;IAEnB,SAASgB;QACP,iCAAiC;QACjC,MAAMC,YAAYC,IAAAA,8BAAqB,EAAC;YACtCH,UAAUA;YACVI,QAAQ,EAAE;QACZ;QAEA,IAAI,OAAOtC,YAAY,eAAe,OAAOA,QAAQC,IAAI,KAAK,YAAY;gBACpDmC;YAApB,IAAK,IAAIG,IAAI,GAAGA,MAAIH,sBAAAA,UAAUF,QAAQ,qBAAlBE,oBAAoBI,MAAM,GAAED,IAAK;gBACnD,IAAIA,MAAM,GAAG;oBACXvC,QAAQC,IAAI,CACV,+CACE;oBAEJ;gBACF;gBACAD,QAAQC,IAAI,CAACwC,IAAAA,kBAAS,EAACL,UAAUF,QAAQ,CAACK,EAAE;YAC9C;QACF;IACF;IAEAJ;IAEA,0CAA0C;IAC1C,IAAIZ,aAAa;QACfI,gBAAgBC,qBAAqBC;IACvC;AACF;AAEA,kEAAkE;AAClE,SAASa,aAAaJ,MAAW;IAC/BlB;IAEAH,qBAAqB;IACrBE,mBAAmB;IAEnB,8BAA8B;IAC9B,IAAIiB,YAAYC,IAAAA,8BAAqB,EAAC;QACpCC,QAAQA;QACRJ,UAAU,EAAE;IACd;IAEA,6BAA6B;IAE7BS,IAAAA,oBAAY,EAACP,UAAUE,MAAM,CAAC,EAAE;IAEhC,gCAAgC;IAChC,IAAI,OAAOtC,YAAY,eAAe,OAAOA,QAAQ4C,KAAK,KAAK,YAAY;QACzE,IAAK,IAAIL,IAAI,GAAGA,IAAIH,UAAUE,MAAM,CAACE,MAAM,EAAED,IAAK;YAChDvC,QAAQ4C,KAAK,CAACH,IAAAA,kBAAS,EAACL,UAAUE,MAAM,CAACC,EAAE;QAC7C;IACF;IAEA,gCAAgC;IAChC,0CAA0C;IAC1C,IAAIM,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QAChC,IAAIC,KAAKC,aAAa,EAAE;YACtBD,KAAKC,aAAa,CAACb,UAAUE,MAAM,CAAC,EAAE;YACtCU,KAAKC,aAAa,GAAG;QACvB;IACF;AACF;AAEA,IAAIC,eAA8B;AAClC,IAAIC,6BAA4C;AAChD,IAAIpB,0BAAuC,IAAIqB;AAC/C,IAAIC,cAAuC,CAAC;AAE5C,SAASzB,oBAAoB0B,cAAwB;IACnD,IAAIA,eAAed,MAAM,GAAG,GAAG;QAC7B,2DAA2D;QAC3D,sBAAsB;QACtBe,IAAAA,uBAAe;IACjB;AACF;AAEA,SAAS1B,cAAcyB,cAA0C;IAA1CA,IAAAA,2BAAAA,iBAAwC,EAAE;IAC/DtB,IAAAA,iBAAS;IACT,IAAIsB,eAAed,MAAM,KAAK,GAAG;QAC/B;IACF;IAEAgB,IAAAA,iBAAS;IAET1B;AACF;AAEA,SAASA,iBAAiBwB,cAA0C;IAA1CA,IAAAA,2BAAAA,iBAAwC,EAAE;IAClE,IAAI,CAACJ,cAAc;IACnB,sEAAsE;IACtE,uEAAuE;IACvE,IAAIO,aAAaN,qCAAAA,6BAA8B9D,KAAKC,GAAG;IACvD,MAAMoE,UAAUD,aAAaP;IAC7BlD,QAAQ2D,GAAG,CAAC,AAAC,4BAAyBD,UAAQ;IAC9C3C,IAAAA,sBAAW,EACTb,KAAKC,SAAS,CAAC;QACbyD,OAAO;QACPC,IAAI7E,OAAOC,iBAAiB;QAC5B6E,WAAWZ;QACXa,SAASN;QACThC,MAAMzC,OAAOgF,QAAQ,CAACC,QAAQ;QAC9BX;QACA,oEAAoE;QACpE,sDAAsD;QACtDY,cAAcC,SAASC,eAAe,KAAK;IAC7C;IAEF,IAAIpB,KAAKqB,qBAAqB,EAAE;QAC9BrB,KAAKqB,qBAAqB,CAACX;IAC7B;AACF;AAEA,kDAAkD;AAClD,SAASY,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCrD,4BAA4BqD;AAC9B;AAEO,SAASzF;IACd,IAAI+D,QAAQC,GAAG,CAAC0B,oBAAoB,EAAE;YAGfxF;QAFrB,MAAMyF,YAAYzF,OAAO0F,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC5F,OAAO0F,IAAI,CAACC,MAAM,CAACV,QAAQ,CAAC;QAC5E,MAAMY,gBAAgBJ,6BAAAA,UAAWK,SAAS;QAC1C,MAAMC,gBAAe/F,sCAAAA,OAAO0F,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC,QAAQ,qBAAtC5F,oCAAwC8F,SAAS;QACtE,MAAME,gBACJC,QAAQJ,iCAAAA,cAAeK,eAAe,KAAKD,QAAQR,UAAUU,OAAO;QACtE,MAAMC,wBACJH,QAAQF,gCAAAA,aAAcG,eAAe,KACrCH,CAAAA,gCAAAA,aAAcG,eAAe,OAAKH,gCAAAA,aAAcM,mBAAmB;QAErE,MAAMC,eACJtG,OAAOgF,QAAQ,CAACC,QAAQ,IAAIZ,eAC3B,CAAC2B,iBAAiB,CAACI;QAEtBG,IAAAA,yBAAiB,EAACD;IACpB;AACF;AAEA,2DAA2D,GAC3D,SAASxF,eAAe0F,GAAqB;IAC3C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;QACtB;IACF;IAEA,sEAAsE;IACtE,OAAQA,IAAIC,MAAM;QAChB,KAAKC,6CAA2B,CAACC,YAAY;YAAE;gBAC7CtC,cAAcmC,IAAII,IAAI;gBACtB9G;gBACA;YACF;QACA,KAAK4G,6CAA2B,CAACG,QAAQ;YAAE;gBACzC3C,eAAe7D,KAAKC,GAAG;gBACvB6D,6BAA6B;gBAC7BpB,wBAAwBV,KAAK;gBAC7BrB,QAAQ2D,GAAG,CAAC;gBACZ;YACF;QACA,KAAK+B,6CAA2B,CAACI,KAAK;QACtC,KAAKJ,6CAA2B,CAACK,IAAI;YAAE;gBACrC,IAAIP,IAAIjB,IAAI,EAAED,oBAAoBkB,IAAIjB,IAAI;gBAE1C,MAAM,EAAEjC,MAAM,EAAEJ,QAAQ,EAAE,GAAGsD;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKQ,IAAAA,qBAAa,EAACR,IAAIS,WAAW;gBACvD,IAAI,kBAAkBT,KAAKU,IAAAA,sBAAc,EAACV,IAAIW,YAAY;gBAE1D,MAAMC,YAAYnB,QAAQ3C,UAAUA,OAAOE,MAAM;gBACjD,IAAI4D,WAAW;oBACbrF,IAAAA,sBAAW,EACTb,KAAKC,SAAS,CAAC;wBACbyD,OAAO;wBACPyC,YAAY/D,OAAOE,MAAM;wBACzB8D,UAAUtH,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOyD,aAAaJ;gBACtB;gBAEA,MAAMiE,cAActB,QAAQ/C,YAAYA,SAASM,MAAM;gBACvD,IAAI+D,aAAa;oBACfxF,IAAAA,sBAAW,EACTb,KAAKC,SAAS,CAAC;wBACbyD,OAAO;wBACP4C,cAActE,SAASM,MAAM;wBAC7B8D,UAAUtH,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOgD,eAAeC;gBACxB;gBAEAnB,IAAAA,sBAAW,EACTb,KAAKC,SAAS,CAAC;oBACbyD,OAAO;oBACP0C,UAAUtH,OAAOC,iBAAiB;gBACpC;gBAEF,OAAOqC;YACT;QACA,KAAKoE,6CAA2B,CAACe,wBAAwB;YAAE;gBACzD,IAAItF,oBAAoBX,wCAAmB,CAACC,eAAe,EAAE;oBAC3DzB,OAAOgF,QAAQ,CAAC0C,MAAM;gBACxB;gBACA;YACF;QACA,KAAKhB,6CAA2B,CAACiB,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGpB;gBACtB,IAAIoB,WAAW;oBACb,MAAM,EAAEC,OAAO,EAAEzG,KAAK,EAAE,GAAGF,KAAK4G,KAAK,CAACF;oBACtC,MAAMhE,QAAQ,qBAAkB,CAAlB,IAAImE,MAAMF,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/BjE,MAAMxC,KAAK,GAAGA;oBACdsC,aAAa;wBAACE;qBAAM;gBACtB;gBACA;YACF;QACA,KAAK8C,6CAA2B,CAACsB,mBAAmB;YAAE;gBACpD,KAAK,MAAMC,YAAYzH,0BAA2B;oBAChDyH,SAAS;wBACPC,MAAMxB,6CAA2B,CAACsB,mBAAmB;wBACrDpB,MAAMJ,IAAII,IAAI;oBAChB;gBACF;gBACA;YACF;QACA,KAAKF,6CAA2B,CAACyB,iBAAiB;YAAE;gBAClD,MAAM7D,iBAAiB8D,IAAAA,sEAAkC,EAAC5B,IAAII,IAAI;gBAClEhE,oBAAoB;uBAAI0B;iBAAe;gBACvC,KAAK,MAAM2D,YAAYzH,0BAA2B;oBAChDyH,SAAS;wBACPC,MAAMxB,6CAA2B,CAACyB,iBAAiB;wBACnDvB,MAAMJ,IAAII,IAAI;oBAChB;gBACF;gBACA,IAAIpF,wCAAmB,CAACC,eAAe,EAAE;oBACvCT,QAAQC,IAAI,CAACoH,4CAAoC;oBACjDtI,kBAAkB;gBACpB;gBACAyE,IAAAA,iBAAS;gBACT,KAAK,MAAM8D,WAAUhE,eAAgB;oBACnCvB,wBAAwBwF,GAAG,CAACD;gBAC9B;gBACAnE,6BAA6B9D,KAAKC,GAAG;gBACrC;YACF;QACA;YAAS;gBACP,IAAIC,uBAAuB;oBACzBA,sBAAsBiG;oBACtB;gBACF;gBACA;YACF;IACF;AACF;AAEA,mDAAmD;AACnD,SAAS9D;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOR,8BAA8BsG;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,yIAAyI;IACzI,OAAOH,OAAOI,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAc;IACvC,IAAIJ,mBAAmB;QACrBI;IACF,OAAO;QACL,SAASvH,QAAQqH,MAAc;YAC7B,IAAIA,WAAW,QAAQ;gBACrB,yIAAyI;gBACzIL,OAAOI,GAAG,CAACI,mBAAmB,CAACxH;gBAC/BuH;YACF;QACF;QACA,yIAAyI;QACzIP,OAAOI,GAAG,CAACK,gBAAgB,CAACzH;IAC9B;AACF;AAEA,iEAAiE;AACjE,SAASqB,gBACPqG,iBAAsE,EACtEC,kBAAyD;IAEzD,yIAAyI;IACzI,IAAI,CAACX,OAAOI,GAAG,EAAE;QACf,8DAA8D;QAC9D1H,QAAQ4C,KAAK,CAAC;QACd,4BAA4B;QAC5B;IACF;IAEA,IAAI,CAAClB,uBAAuB,CAAC+F,mBAAmB;QAC9CzF,IAAAA,iBAAS;QACT;IACF;IAEA,SAASkG,mBAAmBnI,GAAQ,EAAEuD,cAA+B;QACnE,IAAIvD,OAAOS,wCAAmB,CAACC,eAAe,IAAI,CAAC6C,gBAAgB;YACjE,IAAIvD,KAAK;gBACPC,QAAQC,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAIO,wCAAmB,CAACC,eAAe,EAAE;gBAC9CT,QAAQC,IAAI,CACV;YAEJ;YACAlB,kBAAkBgB;YAClB;QACF;QAEA,IAAI,OAAOkI,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmB3E;QACrB;QAEA,IAAI5B,qBAAqB;YACvB,+DAA+D;YAC/D,6DAA6D;YAC7DC,gBACE2B,eAAed,MAAM,GAAG,IAAI2F,YAAYH,mBACxC1E,eAAed,MAAM,GAAG,IAAIR,iBAAS,GAAGiG;QAE5C,OAAO;YACLjG,IAAAA,iBAAS;YACT,IAAIa,QAAQC,GAAG,CAACC,gBAAgB,EAAE;gBAChC6E,kBAAkB;oBAChB,IAAI5E,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,yIAAyI;IACzIqE,OAAOI,GAAG,CACPU,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAAC/E;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAO0E,sBAAsB,YAAY;YAC3CA,kBAAkB1E;QACpB;QACA,yIAAyI;QACzI,OAAOgE,OAAOI,GAAG,CAACY,KAAK;IACzB,GACCD,IAAI,CACH,CAAC/E;QACC4E,mBAAmB,MAAM5E;IAC3B,GACA,CAACvD;QACCmI,mBAAmBnI,KAAK;IAC1B;AAEN;AAEO,SAAShB,kBAAkBgB,GAAQ;IACxC,MAAMwI,aACJxI,OACC,CAAA,AAACA,IAAIK,KAAK,IAAIL,IAAIK,KAAK,CAACoI,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpD3I,IAAI8G,OAAO,IACX9G,MAAM,EAAC;IAEXgB,IAAAA,sBAAW,EACTb,KAAKC,SAAS,CAAC;QACbyD,OAAO;QACP2E;QACA9H,iBAAiB,CAAC,CAACD,wCAAmB,CAACC,eAAe;QACtDkI,iBAAiB5I,MAAMA,IAAI4I,eAAe,GAAGR;IAC/C;IAGFnJ,OAAOgF,QAAQ,CAAC0C,MAAM;AACxB"}