{"version": 3, "sources": ["../../../src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["RenderFromTemplateContext", "children", "useContext", "TemplateContext"], "mappings": "AAAA;;;;;+BAKA;;;eAAwBA;;;;;iEAHoB;+CACZ;AAEjB,SAASA;IACtB,MAAMC,WAAWC,IAAAA,iBAAU,EAACC,8CAAe;IAC3C,qBAAO;kBAAGF;;AACZ"}