{"version": 3, "sources": ["../../../../src/client/components/segment-cache-impl/prefetch.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../../server/app-render/types'\nimport { createPrefetchURL } from '../app-router'\nimport { createCacheKey } from './cache-key'\nimport { schedulePrefetchTask } from './scheduler'\nimport { PrefetchPriority } from '../segment-cache'\n\n/**\n * Entrypoint for prefetching a URL into the Segment Cache.\n * @param href - The URL to prefetch. Typically this will come from a <Link>,\n * or router.prefetch. It must be validated before we attempt to prefetch it.\n * @param nextUrl - A special header used by the server for interception routes.\n * Roughly  corresponds to the current URL.\n * @param treeAtTimeOfPrefetch - The FlightRouterState at the time the prefetch\n * was requested. This is only used when PPR is disabled.\n * @param includeDynamicData - Whether to prefetch dynamic data, in addition to\n * static data. This is used by <Link prefetch={true}>.\n */\nexport function prefetch(\n  href: string,\n  nextUrl: string | null,\n  treeAtTimeOfPrefetch: FlightRouterState,\n  includeDynamicData: boolean\n) {\n  const url = createPrefetchURL(href)\n  if (url === null) {\n    // This href should not be prefetched.\n    return\n  }\n  const cacheKey = createCacheKey(url.href, nextUrl)\n  schedulePrefetchTask(\n    cacheKey,\n    treeAtTimeOfPrefetch,\n    includeDynamicData,\n    PrefetchPriority.Default\n  )\n}\n"], "names": ["prefetch", "href", "nextUrl", "treeAtTimeOfPrefetch", "includeDynamicData", "url", "createPrefetchURL", "cache<PERSON>ey", "createCacheKey", "schedulePrefetchTask", "PrefetchPriority", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;+BAiBgBA;;;eAAAA;;;2BAhBkB;0BACH;2BACM;8BACJ;AAa1B,SAASA,SACdC,IAAY,EACZC,OAAsB,EACtBC,oBAAuC,EACvCC,kBAA2B;IAE3B,MAAMC,MAAMC,IAAAA,4BAAiB,EAACL;IAC9B,IAAII,QAAQ,MAAM;QAChB,sCAAsC;QACtC;IACF;IACA,MAAME,WAAWC,IAAAA,wBAAc,EAACH,IAAIJ,IAAI,EAAEC;IAC1CO,IAAAA,+BAAoB,EAClBF,UACAJ,sBACAC,oBACAM,8BAAgB,CAACC,OAAO;AAE5B"}