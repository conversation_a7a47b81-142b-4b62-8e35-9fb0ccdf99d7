{"version": 3, "sources": ["../../../src/client/components/not-found-error.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from './http-access-fallback/error-fallback'\n\nexport default function NotFound() {\n  return (\n    <HTTPAccessErrorFallback\n      status={404}\n      message=\"This page could not be found.\"\n    />\n  )\n}\n"], "names": ["NotFound", "HTTPAccessErrorFallback", "status", "message"], "mappings": ";;;;+BAEA;;;eAAwBA;;;;+BAFgB;AAEzB,SAASA;IACtB,qBACE,qBAACC,sCAAuB;QACtBC,QAAQ;QACRC,SAAQ;;AAGd"}