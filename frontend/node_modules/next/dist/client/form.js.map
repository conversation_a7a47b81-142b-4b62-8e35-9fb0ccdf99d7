{"version": 3, "sources": ["../../src/client/form.tsx"], "sourcesContent": ["'use client'\n\nimport { type FormEvent, useContext, forwardRef } from 'react'\nimport { addBasePath } from './add-base-path'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport type { NextRouter } from './router'\nimport {\n  checkFormActionUrl,\n  createFormSubmitDestinationUrl,\n  DISALLOWED_FORM_PROPS,\n  hasReactClientActionAttributes,\n  hasUnsupportedSubmitterAttributes,\n  type FormProps,\n} from './form-shared'\n\nexport type { FormProps }\n\nconst Form = forwardRef<HTMLFormElement, FormProps>(function FormComponent(\n  { replace, scroll, prefetch: prefetchProp, ...props },\n  ref\n) {\n  const router = useContext(RouterContext)\n\n  const actionProp = props.action\n  const isNavigatingForm = typeof actionProp === 'string'\n\n  // Validate `action`\n  if (process.env.NODE_ENV === 'development') {\n    if (isNavigatingForm) {\n      checkFormActionUrl(actionProp, 'action')\n    }\n  }\n\n  // Validate `prefetch`\n  if (process.env.NODE_ENV === 'development') {\n    if (prefetchProp !== undefined) {\n      console.error(\n        'Passing `prefetch` to a <Form> has no effect in the pages directory.'\n      )\n    }\n  }\n\n  // Validate `scroll` and `replace`\n  if (process.env.NODE_ENV === 'development') {\n    if (!isNavigatingForm && (replace !== undefined || scroll !== undefined)) {\n      console.error(\n        'Passing `replace` or `scroll` to a <Form> whose `action` is a function has no effect.\\n' +\n          'See the relevant docs to learn how to control this behavior for navigations triggered from actions:\\n' +\n          '  `router.replace()` - https://nextjs.org/docs/pages/api-reference/functions/use-router#routerreplace\\n'\n      )\n    }\n  }\n\n  // Clean up any unsupported form props (and warn if present)\n  for (const key of DISALLOWED_FORM_PROPS) {\n    if (key in props) {\n      if (process.env.NODE_ENV === 'development') {\n        console.error(`<Form> does not support changing \\`${key}\\`.`)\n      }\n      delete (props as Record<string, unknown>)[key]\n    }\n  }\n\n  if (!isNavigatingForm) {\n    return <form {...props} ref={ref} />\n  }\n\n  const actionHref = addBasePath(actionProp)\n\n  return (\n    <form\n      {...props}\n      ref={ref}\n      action={actionHref}\n      onSubmit={(event) =>\n        onFormSubmit(event, {\n          router,\n          actionHref,\n          replace,\n          scroll,\n          onSubmit: props.onSubmit,\n        })\n      }\n    />\n  )\n})\n\nexport default Form\n\nfunction onFormSubmit(\n  event: FormEvent<HTMLFormElement>,\n  {\n    actionHref,\n    onSubmit,\n    replace,\n    scroll,\n    router,\n  }: {\n    actionHref: string\n    onSubmit: FormProps['onSubmit']\n    replace: FormProps['replace']\n    scroll: FormProps['scroll']\n    router: NextRouter | null\n  }\n) {\n  if (typeof onSubmit === 'function') {\n    onSubmit(event)\n\n    // if the user called event.preventDefault(), do nothing.\n    // (this matches what Link does for `onClick`)\n    if (event.defaultPrevented) {\n      return\n    }\n  }\n\n  if (!router) {\n    // Form was somehow used outside of the router (but not in app/, the implementation is forked!).\n    // We can't perform a soft navigation, so let the native submit handling do its thing.\n    return\n  }\n\n  const formElement = event.currentTarget\n  const submitter = (event.nativeEvent as SubmitEvent).submitter\n\n  let action = actionHref\n\n  if (submitter) {\n    // this is page-router-only, so we don't need to worry about false positives\n    // from the attributes that react adds for server actions.\n    if (hasUnsupportedSubmitterAttributes(submitter)) {\n      return\n    }\n\n    // client actions have `formAction=\"javascript:...\"`. We obviously can't prefetch/navigate to that.\n    if (hasReactClientActionAttributes(submitter)) {\n      return\n    }\n\n    // If the submitter specified an alternate formAction,\n    // use that URL instead -- this is what a native form would do.\n    // NOTE: `submitter.formAction` is unreliable, because it will give us `location.href` if it *wasn't* set\n    // NOTE: this should not have `basePath` added, because we can't add it before hydration\n    const submitterFormAction = submitter.getAttribute('formAction')\n    if (submitterFormAction !== null) {\n      if (process.env.NODE_ENV === 'development') {\n        checkFormActionUrl(submitterFormAction, 'formAction')\n      }\n      action = submitterFormAction\n    }\n  }\n\n  const targetUrl = createFormSubmitDestinationUrl(action, formElement)\n\n  // Finally, no more reasons for bailing out.\n  event.preventDefault()\n\n  const method = replace ? 'replace' : 'push'\n  const targetHref = targetUrl.href // TODO: will pages router be happy about an absolute URL here?\n\n  // TODO(form): Make this use a transition so that pending states work\n  //\n  // Unlike the app router, pages router doesn't use startTransition,\n  // and can't easily be wrapped in one because of implementation details\n  // (e.g. it doesn't use any react state)\n  // But it's important to have this wrapped in a transition because\n  // pending states from e.g. `useFormStatus` rely on that.\n  // So this needs some follow up work.\n  router[method](targetHref, undefined, { scroll })\n}\n"], "names": ["Form", "forwardRef", "FormComponent", "ref", "replace", "scroll", "prefetch", "prefetchProp", "props", "router", "useContext", "RouterContext", "actionProp", "action", "isNavigatingForm", "process", "env", "NODE_ENV", "checkFormActionUrl", "undefined", "console", "error", "key", "DISALLOWED_FORM_PROPS", "form", "actionHref", "addBasePath", "onSubmit", "event", "onFormSubmit", "defaultPrevented", "formElement", "currentTarget", "submitter", "nativeEvent", "hasUnsupportedSubmitterAttributes", "hasReactClientActionAttributes", "submitterFormAction", "getAttribute", "targetUrl", "createFormSubmitDestinationUrl", "preventDefault", "method", "targetHref", "href"], "mappings": "AAAA;;;;;+BAuFA;;;eAAA;;;;uBArFuD;6BAC3B;4CACE;4BASvB;AAIP,MAAMA,qBAAOC,IAAAA,iBAAU,EAA6B,SAASC,cAC3D,KAAqD,EACrDC,GAAG;IADH,IAAA,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAUC,YAAY,EAAE,GAAGC,OAAO,GAArD;IAGA,MAAMC,SAASC,IAAAA,iBAAU,EAACC,yCAAa;IAEvC,MAAMC,aAAaJ,MAAMK,MAAM;IAC/B,MAAMC,mBAAmB,OAAOF,eAAe;IAE/C,oBAAoB;IACpB,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IAAIH,kBAAkB;YACpBI,IAAAA,8BAAkB,EAACN,YAAY;QACjC;IACF;IAEA,sBAAsB;IACtB,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IAAIV,iBAAiBY,WAAW;YAC9BC,QAAQC,KAAK,CACX;QAEJ;IACF;IAEA,kCAAkC;IAClC,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,IAAI,CAACH,oBAAqBV,CAAAA,YAAYe,aAAad,WAAWc,SAAQ,GAAI;YACxEC,QAAQC,KAAK,CACX,4FACE,0GACA;QAEN;IACF;IAEA,4DAA4D;IAC5D,KAAK,MAAMC,OAAOC,iCAAqB,CAAE;QACvC,IAAID,OAAOd,OAAO;YAChB,IAAIO,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;gBAC1CG,QAAQC,KAAK,CAAC,AAAC,uCAAqCC,MAAI;YAC1D;YACA,OAAO,AAACd,KAAiC,CAACc,IAAI;QAChD;IACF;IAEA,IAAI,CAACR,kBAAkB;QACrB,qBAAO,qBAACU;YAAM,GAAGhB,KAAK;YAAEL,KAAKA;;IAC/B;IAEA,MAAMsB,aAAaC,IAAAA,wBAAW,EAACd;IAE/B,qBACE,qBAACY;QACE,GAAGhB,KAAK;QACTL,KAAKA;QACLU,QAAQY;QACRE,UAAU,CAACC,QACTC,aAAaD,OAAO;gBAClBnB;gBACAgB;gBACArB;gBACAC;gBACAsB,UAAUnB,MAAMmB,QAAQ;YAC1B;;AAIR;MAEA,WAAe3B;AAEf,SAAS6B,aACPD,KAAiC,EACjC,KAYC;IAZD,IAAA,EACEH,UAAU,EACVE,QAAQ,EACRvB,OAAO,EACPC,MAAM,EACNI,MAAM,EAOP,GAZD;IAcA,IAAI,OAAOkB,aAAa,YAAY;QAClCA,SAASC;QAET,yDAAyD;QACzD,8CAA8C;QAC9C,IAAIA,MAAME,gBAAgB,EAAE;YAC1B;QACF;IACF;IAEA,IAAI,CAACrB,QAAQ;QACX,gGAAgG;QAChG,sFAAsF;QACtF;IACF;IAEA,MAAMsB,cAAcH,MAAMI,aAAa;IACvC,MAAMC,YAAY,AAACL,MAAMM,WAAW,CAAiBD,SAAS;IAE9D,IAAIpB,SAASY;IAEb,IAAIQ,WAAW;QACb,4EAA4E;QAC5E,0DAA0D;QAC1D,IAAIE,IAAAA,6CAAiC,EAACF,YAAY;YAChD;QACF;QAEA,mGAAmG;QACnG,IAAIG,IAAAA,0CAA8B,EAACH,YAAY;YAC7C;QACF;QAEA,sDAAsD;QACtD,+DAA+D;QAC/D,yGAAyG;QACzG,wFAAwF;QACxF,MAAMI,sBAAsBJ,UAAUK,YAAY,CAAC;QACnD,IAAID,wBAAwB,MAAM;YAChC,IAAItB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;gBAC1CC,IAAAA,8BAAkB,EAACmB,qBAAqB;YAC1C;YACAxB,SAASwB;QACX;IACF;IAEA,MAAME,YAAYC,IAAAA,0CAA8B,EAAC3B,QAAQkB;IAEzD,4CAA4C;IAC5CH,MAAMa,cAAc;IAEpB,MAAMC,SAAStC,UAAU,YAAY;IACrC,MAAMuC,aAAaJ,UAAUK,IAAI,CAAC,+DAA+D;;IAEjG,qEAAqE;IACrE,EAAE;IACF,mEAAmE;IACnE,uEAAuE;IACvE,wCAAwC;IACxC,kEAAkE;IAClE,yDAAyD;IACzD,qCAAqC;IACrCnC,MAAM,CAACiC,OAAO,CAACC,YAAYxB,WAAW;QAAEd;IAAO;AACjD"}