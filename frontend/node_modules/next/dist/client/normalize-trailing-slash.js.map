{"version": 3, "sources": ["../../src/client/normalize-trailing-slash.ts"], "sourcesContent": ["import { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { parsePath } from '../shared/lib/router/utils/parse-path'\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = (path: string) => {\n  if (!path.startsWith('/') || process.env.__NEXT_MANUAL_TRAILING_SLASH) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  if (process.env.__NEXT_TRAILING_SLASH) {\n    if (/\\.[^/]+\\/?$/.test(pathname)) {\n      return `${removeTrailingSlash(pathname)}${query}${hash}`\n    } else if (pathname.endsWith('/')) {\n      return `${pathname}${query}${hash}`\n    } else {\n      return `${pathname}/${query}${hash}`\n    }\n  }\n\n  return `${removeTrailingSlash(pathname)}${query}${hash}`\n}\n"], "names": ["normalizePathTrailingSlash", "path", "startsWith", "process", "env", "__NEXT_MANUAL_TRAILING_SLASH", "pathname", "query", "hash", "parsePath", "__NEXT_TRAILING_SLASH", "test", "removeTrailingSlash", "endsWith"], "mappings": ";;;;+BAOaA;;;eAAAA;;;qCAPuB;2BACV;AAMnB,MAAMA,6BAA6B,CAACC;IACzC,IAAI,CAACA,KAAKC,UAAU,CAAC,QAAQC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;QACrE,OAAOJ;IACT;IAEA,MAAM,EAAEK,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,IAAAA,oBAAS,EAACR;IAC5C,IAAIE,QAAQC,GAAG,CAACM,qBAAqB,EAAE;QACrC,IAAI,cAAcC,IAAI,CAACL,WAAW;YAChC,OAAO,AAAC,KAAEM,IAAAA,wCAAmB,EAACN,YAAYC,QAAQC;QACpD,OAAO,IAAIF,SAASO,QAAQ,CAAC,MAAM;YACjC,OAAO,AAAC,KAAEP,WAAWC,QAAQC;QAC/B,OAAO;YACL,OAAO,AAAGF,WAAS,MAAGC,QAAQC;QAChC;IACF;IAEA,OAAO,AAAC,KAAEI,IAAAA,wCAAmB,EAACN,YAAYC,QAAQC;AACpD"}