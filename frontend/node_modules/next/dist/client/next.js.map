{"version": 3, "sources": ["../../src/client/next.ts"], "sourcesContent": ["import './webpack'\nimport { initialize, hydrate, version, router, emitter } from './'\n\ndeclare global {\n  interface Window {\n    next: any\n  }\n}\n\nwindow.next = {\n  version,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n}\n\ninitialize({})\n  .then(() => hydrate())\n  .catch(console.error)\n"], "names": ["window", "next", "version", "router", "emitter", "initialize", "then", "hydrate", "catch", "console", "error"], "mappings": ";;;;QAAO;kBACuD;AAQ9DA,OAAOC,IAAI,GAAG;IACZC,SAAAA,SAAO;IACP,0DAA0D;IAC1D,IAAIC,UAAS;QACX,OAAOA,QAAM;IACf;IACAC,SAAAA,SAAO;AACT;AAEAC,IAAAA,YAAU,EAAC,CAAC,GACTC,IAAI,CAAC,IAAMC,IAAAA,SAAO,KAClBC,KAAK,CAACC,QAAQC,KAAK"}