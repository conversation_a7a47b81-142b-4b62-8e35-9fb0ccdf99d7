{"version": 3, "sources": ["../../../src/client/dev/amp-dev.ts"], "sourcesContent": ["/* globals __webpack_hash__ */\nimport { displayContent } from './fouc'\nimport initOnDemandEntries from './on-demand-entries-client'\nimport {\n  addMessageListener,\n  connectHMR,\n} from '../components/react-dev-overlay/pages/websocket'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../server/dev/hot-reloader-types'\n\ndeclare global {\n  const __webpack_runtime_id__: string\n}\n\nconst data = JSON.parse(\n  (document.getElementById('__NEXT_DATA__') as any).textContent\n)\nwindow.__NEXT_DATA__ = data\n\nlet { assetPrefix, page } = data\nassetPrefix = assetPrefix || ''\nlet mostRecentHash: null | string = null\n/* eslint-disable-next-line */\nlet curHash = __webpack_hash__\nconst hotUpdatePath =\n  assetPrefix + (assetPrefix.endsWith('/') ? '' : '/') + '_next/static/webpack/'\n\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  /* eslint-disable-next-line */\n  return mostRecentHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  // @ts-expect-error TODO: module.hot exists but type needs to be added. Can't use `as any` here as webpack parses for `module.hot` calls.\n  return module.hot.status() === 'idle'\n}\n\n// This function reads code updates on the fly and hard\n// reloads the page when it has changed.\nasync function tryApplyUpdates() {\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    return\n  }\n  try {\n    const res = await fetch(\n      typeof __webpack_runtime_id__ !== 'undefined'\n        ? // eslint-disable-next-line no-undef\n          `${hotUpdatePath}${curHash}.${__webpack_runtime_id__}.hot-update.json`\n        : `${hotUpdatePath}${curHash}.hot-update.json`\n    )\n    const jsonData = await res.json()\n    const curPage = page === '/' ? 'index' : page\n    // webpack 5 uses an array instead\n    const pageUpdated = (\n      Array.isArray(jsonData.c) ? jsonData.c : Object.keys(jsonData.c)\n    ).some((mod: string) => {\n      return (\n        mod.indexOf(\n          `pages${curPage.startsWith('/') ? curPage : `/${curPage}`}`\n        ) !== -1 ||\n        mod.indexOf(\n          `pages${curPage.startsWith('/') ? curPage : `/${curPage}`}`.replace(\n            /\\//g,\n            '\\\\'\n          )\n        ) !== -1\n      )\n    })\n\n    if (pageUpdated) {\n      window.location.reload()\n    } else {\n      curHash = mostRecentHash as string\n    }\n  } catch (err) {\n    console.error('Error occurred checking for update', err)\n    window.location.reload()\n  }\n}\n\naddMessageListener((message) => {\n  if (!('action' in message)) {\n    return\n  }\n\n  try {\n    // actions which are not related to amp-dev\n    if (\n      message.action === HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR ||\n      message.action === HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE\n    ) {\n      return\n    }\n    if (\n      message.action === HMR_ACTIONS_SENT_TO_BROWSER.SYNC ||\n      message.action === HMR_ACTIONS_SENT_TO_BROWSER.BUILT\n    ) {\n      if (!message.hash) {\n        return\n      }\n      mostRecentHash = message.hash\n      tryApplyUpdates()\n    } else if (message.action === HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE) {\n      window.location.reload()\n    }\n  } catch (err: any) {\n    console.warn(\n      '[HMR] Invalid message: ' +\n        JSON.stringify(message) +\n        '\\n' +\n        (err?.stack ?? '')\n    )\n  }\n})\n\nconnectHMR({\n  assetPrefix,\n  path: '/_next/webpack-hmr',\n})\ndisplayContent()\n\ninitOnDemandEntries(data.page)\n"], "names": ["data", "JSON", "parse", "document", "getElementById", "textContent", "window", "__NEXT_DATA__", "assetPrefix", "page", "mostRecentHash", "curHash", "__webpack_hash__", "hotUpdatePath", "endsWith", "isUpdateAvailable", "canApplyUpdates", "module", "hot", "status", "tryApplyUpdates", "res", "fetch", "__webpack_runtime_id__", "jsonData", "json", "curPage", "pageUpdated", "Array", "isArray", "c", "Object", "keys", "some", "mod", "indexOf", "startsWith", "replace", "location", "reload", "err", "console", "error", "addMessageListener", "message", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "SERVER_ERROR", "DEV_PAGES_MANIFEST_UPDATE", "SYNC", "BUILT", "hash", "RELOAD_PAGE", "warn", "stringify", "stack", "connectHMR", "path", "displayContent", "initOnDemandEntries"], "mappings": "AAAA,4BAA4B;;;;;sBACG;gFACC;2BAIzB;kCACqC;AAM5C,MAAMA,OAAOC,KAAKC,KAAK,CACrB,AAACC,SAASC,cAAc,CAAC,iBAAyBC,WAAW;AAE/DC,OAAOC,aAAa,GAAGP;AAEvB,IAAI,EAAEQ,WAAW,EAAEC,IAAI,EAAE,GAAGT;AAC5BQ,cAAcA,eAAe;AAC7B,IAAIE,iBAAgC;AACpC,4BAA4B,GAC5B,IAAIC,UAAUC;AACd,MAAMC,gBACJL,cAAeA,CAAAA,YAAYM,QAAQ,CAAC,OAAO,KAAK,GAAE,IAAK;AAEzD,mDAAmD;AACnD,SAASC;IACP,2DAA2D;IAC3D,8CAA8C;IAC9C,4BAA4B,GAC5B,OAAOL,mBAAmBE;AAC5B;AAEA,6CAA6C;AAC7C,SAASI;IACP,yIAAyI;IACzI,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AAEA,uDAAuD;AACvD,wCAAwC;AACxC,eAAeC;IACb,IAAI,CAACL,uBAAuB,CAACC,mBAAmB;QAC9C;IACF;IACA,IAAI;QACF,MAAMK,MAAM,MAAMC,MAChB,OAAOC,2BAA2B,cAE9B,AAAC,KAAEV,gBAAgBF,UAAQ,MAAGY,yBAAuB,qBACrD,AAAC,KAAEV,gBAAgBF,UAAQ;QAEjC,MAAMa,WAAW,MAAMH,IAAII,IAAI;QAC/B,MAAMC,UAAUjB,SAAS,MAAM,UAAUA;QACzC,kCAAkC;QAClC,MAAMkB,cAAc,AAClBC,CAAAA,MAAMC,OAAO,CAACL,SAASM,CAAC,IAAIN,SAASM,CAAC,GAAGC,OAAOC,IAAI,CAACR,SAASM,CAAC,CAAA,EAC/DG,IAAI,CAAC,CAACC;YACN,OACEA,IAAIC,OAAO,CACT,AAAC,UAAOT,CAAAA,QAAQU,UAAU,CAAC,OAAOV,UAAU,AAAC,MAAGA,OAAQ,OACpD,CAAC,KACPQ,IAAIC,OAAO,CACT,CAAA,AAAC,UAAOT,CAAAA,QAAQU,UAAU,CAAC,OAAOV,UAAU,AAAC,MAAGA,OAAQ,CAAE,EAAEW,OAAO,CACjE,OACA,WAEE,CAAC;QAEX;QAEA,IAAIV,aAAa;YACfrB,OAAOgC,QAAQ,CAACC,MAAM;QACxB,OAAO;YACL5B,UAAUD;QACZ;IACF,EAAE,OAAO8B,KAAK;QACZC,QAAQC,KAAK,CAAC,sCAAsCF;QACpDlC,OAAOgC,QAAQ,CAACC,MAAM;IACxB;AACF;AAEAI,IAAAA,6BAAkB,EAAC,CAACC;IAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;QAC1B;IACF;IAEA,IAAI;QACF,2CAA2C;QAC3C,IACEA,QAAQC,MAAM,KAAKC,6CAA2B,CAACC,YAAY,IAC3DH,QAAQC,MAAM,KAAKC,6CAA2B,CAACE,yBAAyB,EACxE;YACA;QACF;QACA,IACEJ,QAAQC,MAAM,KAAKC,6CAA2B,CAACG,IAAI,IACnDL,QAAQC,MAAM,KAAKC,6CAA2B,CAACI,KAAK,EACpD;YACA,IAAI,CAACN,QAAQO,IAAI,EAAE;gBACjB;YACF;YACAzC,iBAAiBkC,QAAQO,IAAI;YAC7B/B;QACF,OAAO,IAAIwB,QAAQC,MAAM,KAAKC,6CAA2B,CAACM,WAAW,EAAE;YACrE9C,OAAOgC,QAAQ,CAACC,MAAM;QACxB;IACF,EAAE,OAAOC,KAAU;YAKZA;QAJLC,QAAQY,IAAI,CACV,4BACEpD,KAAKqD,SAAS,CAACV,WACf,OACCJ,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKe,KAAK,YAAVf,aAAc,EAAC;IAEtB;AACF;AAEAgB,IAAAA,qBAAU,EAAC;IACThD;IACAiD,MAAM;AACR;AACAC,IAAAA,oBAAc;AAEdC,IAAAA,8BAAmB,EAAC3D,KAAKS,IAAI"}