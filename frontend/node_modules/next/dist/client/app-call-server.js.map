{"version": 3, "sources": ["../../src/client/app-call-server.ts"], "sourcesContent": ["import { startTransition, useCallback } from 'react'\nimport {\n  ACTION_SERVER_ACTION,\n  type ReducerActions,\n  type ServerActionDispatcher,\n} from './components/router-reducer/router-reducer-types'\n\nlet globalServerActionDispatcher = null as ServerActionDispatcher | null\n\nexport function useServerActionDispatcher(\n  dispatch: React.Dispatch<ReducerActions>\n) {\n  const serverActionDispatcher: ServerActionDispatcher = useCallback(\n    (actionPayload) => {\n      startTransition(() => {\n        dispatch({\n          ...actionPayload,\n          type: ACTION_SERVER_ACTION,\n        })\n      })\n    },\n    [dispatch]\n  )\n  globalServerActionDispatcher = serverActionDispatcher\n}\n\nexport async function callServer(actionId: string, actionArgs: any[]) {\n  const actionDispatcher = globalServerActionDispatcher\n\n  if (!actionDispatcher) {\n    throw new Error('Invariant: missing action dispatcher.')\n  }\n\n  return new Promise((resolve, reject) => {\n    actionDispatcher({\n      actionId,\n      actionArgs,\n      resolve,\n      reject,\n    })\n  })\n}\n"], "names": ["callServer", "useServerActionDispatcher", "globalServerActionDispatcher", "dispatch", "serverActionDispatcher", "useCallback", "actionPayload", "startTransition", "type", "ACTION_SERVER_ACTION", "actionId", "actionArgs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "Promise", "resolve", "reject"], "mappings": ";;;;;;;;;;;;;;;IA0BsBA,UAAU;eAAVA;;IAjBNC,yBAAyB;eAAzBA;;;uBAT6B;oCAKtC;AAEP,IAAIC,+BAA+B;AAE5B,SAASD,0BACdE,QAAwC;IAExC,MAAMC,yBAAiDC,IAAAA,kBAAW,EAChE,CAACC;QACCC,IAAAA,sBAAe,EAAC;YACdJ,SAAS;gBACP,GAAGG,aAAa;gBAChBE,MAAMC,wCAAoB;YAC5B;QACF;IACF,GACA;QAACN;KAAS;IAEZD,+BAA+BE;AACjC;AAEO,eAAeJ,WAAWU,QAAgB,EAAEC,UAAiB;IAClE,MAAMC,mBAAmBV;IAEzB,IAAI,CAACU,kBAAkB;QACrB,MAAM,qBAAkD,CAAlD,IAAIC,MAAM,0CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiD;IACzD;IAEA,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3BJ,iBAAiB;YACfF;YACAC;YACAI;YACAC;QACF;IACF;AACF"}