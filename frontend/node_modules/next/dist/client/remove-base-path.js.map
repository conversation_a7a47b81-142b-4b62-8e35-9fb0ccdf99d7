{"version": 3, "sources": ["../../src/client/remove-base-path.ts"], "sourcesContent": ["import { hasBasePath } from './has-base-path'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function removeBasePath(path: string): string {\n  if (process.env.__NEXT_MANUAL_CLIENT_BASE_PATH) {\n    if (!hasBasePath(path)) {\n      return path\n    }\n  }\n\n  // Can't trim the basePath if it has zero length!\n  if (basePath.length === 0) return path\n\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n"], "names": ["removeBasePath", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "path", "__NEXT_MANUAL_CLIENT_BASE_PATH", "has<PERSON>ase<PERSON><PERSON>", "length", "slice", "startsWith"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;6BAJY;AAE5B,MAAMC,WAAW,AAACC,QAAQC,GAAG,CAACC,sBAAsB,IAAe;AAE5D,SAASJ,eAAeK,IAAY;IACzC,IAAIH,QAAQC,GAAG,CAACG,8BAA8B,EAAE;QAC9C,IAAI,CAACC,IAAAA,wBAAW,EAACF,OAAO;YACtB,OAAOA;QACT;IACF;IAEA,iDAAiD;IACjD,IAAIJ,SAASO,MAAM,KAAK,GAAG,OAAOH;IAElCA,OAAOA,KAAKI,KAAK,CAACR,SAASO,MAAM;IACjC,IAAI,CAACH,KAAKK,UAAU,CAAC,MAAML,OAAO,AAAC,MAAGA;IACtC,OAAOA;AACT"}