{"version": 3, "sources": ["../../src/client/app-next.ts"], "sourcesContent": ["// This import must go first because it needs to patch webpack chunk loading\n// before React patches chunk loading.\nimport './app-webpack'\nimport { appBootstrap } from './app-bootstrap'\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index')\n  // Include app-router and layout-router in the main chunk\n  require('next/dist/client/components/app-router')\n  require('next/dist/client/components/layout-router')\n  hydrate()\n})\n"], "names": ["appBootstrap", "hydrate", "require"], "mappings": "AAAA,4EAA4E;AAC5E,sCAAsC;;;;;QAC/B;8BACsB;AAE7BA,IAAAA,0BAAY,EAAC;IACX,MAAM,EAAEC,OAAO,EAAE,GAAGC,QAAQ;IAC5B,yDAAyD;IACzDA,QAAQ;IACRA,QAAQ;IACRD;AACF"}