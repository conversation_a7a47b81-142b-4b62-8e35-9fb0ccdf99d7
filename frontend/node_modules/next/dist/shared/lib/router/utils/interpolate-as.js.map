{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/interpolate-as.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nimport { getRouteMatcher } from './route-matcher'\nimport { getRouteRegex } from './route-regex'\n\nexport function interpolateAs(\n  route: string,\n  asPathname: string,\n  query: ParsedUrlQuery\n) {\n  let interpolatedRoute = ''\n\n  const dynamicRegex = getRouteRegex(route)\n  const dynamicGroups = dynamicRegex.groups\n  const dynamicMatches =\n    // Try to match the dynamic route against the asPath\n    (asPathname !== route ? getRouteMatcher(dynamicRegex)(asPathname) : '') ||\n    // Fall back to reading the values from the href\n    // TODO: should this take priority; also need to change in the router.\n    query\n\n  interpolatedRoute = route\n  const params = Object.keys(dynamicGroups)\n\n  if (\n    !params.every((param) => {\n      let value = dynamicMatches[param] || ''\n      const { repeat, optional } = dynamicGroups[param]\n\n      // support single-level catch-all\n      // TODO: more robust handling for user-error (passing `/`)\n      let replaced = `[${repeat ? '...' : ''}${param}]`\n      if (optional) {\n        replaced = `${!value ? '/' : ''}[${replaced}]`\n      }\n      if (repeat && !Array.isArray(value)) value = [value]\n\n      return (\n        (optional || param in dynamicMatches) &&\n        // Interpolate group into data URL if present\n        (interpolatedRoute =\n          interpolatedRoute!.replace(\n            replaced,\n            repeat\n              ? (value as string[])\n                  .map(\n                    // these values should be fully encoded instead of just\n                    // path delimiter escaped since they are being inserted\n                    // into the URL and we expect URL encoded segments\n                    // when parsing dynamic route params\n                    (segment) => encodeURIComponent(segment)\n                  )\n                  .join('/')\n              : encodeURIComponent(value as string)\n          ) || '/')\n      )\n    })\n  ) {\n    interpolatedRoute = '' // did not satisfy all requirements\n\n    // n.b. We ignore this error because we handle warning for this case in\n    // development in the `<Link>` component directly.\n  }\n  return {\n    params,\n    result: interpolatedRoute,\n  }\n}\n"], "names": ["interpolateAs", "route", "asPathname", "query", "interpolatedRoute", "dynamicRegex", "getRouteRegex", "dynamicGroups", "groups", "dynamicMatches", "getRouteMatcher", "params", "Object", "keys", "every", "param", "value", "repeat", "optional", "replaced", "Array", "isArray", "replace", "map", "segment", "encodeURIComponent", "join", "result"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;8BAHgB;4BACF;AAEvB,SAASA,cACdC,KAAa,EACbC,UAAkB,EAClBC,KAAqB;IAErB,IAAIC,oBAAoB;IAExB,MAAMC,eAAeC,IAAAA,yBAAa,EAACL;IACnC,MAAMM,gBAAgBF,aAAaG,MAAM;IACzC,MAAMC,iBAEJ,AADA,oDAAoD;IACnDP,CAAAA,eAAeD,QAAQS,IAAAA,6BAAe,EAACL,cAAcH,cAAc,EAAC,KACrE,gDAAgD;IAChD,sEAAsE;IACtEC;IAEFC,oBAAoBH;IACpB,MAAMU,SAASC,OAAOC,IAAI,CAACN;IAE3B,IACE,CAACI,OAAOG,KAAK,CAAC,CAACC;QACb,IAAIC,QAAQP,cAAc,CAACM,MAAM,IAAI;QACrC,MAAM,EAAEE,MAAM,EAAEC,QAAQ,EAAE,GAAGX,aAAa,CAACQ,MAAM;QAEjD,iCAAiC;QACjC,0DAA0D;QAC1D,IAAII,WAAW,AAAC,MAAGF,CAAAA,SAAS,QAAQ,EAAC,IAAIF,QAAM;QAC/C,IAAIG,UAAU;YACZC,WAAW,AAAG,CAAA,CAACH,QAAQ,MAAM,EAAC,IAAE,MAAGG,WAAS;QAC9C;QACA,IAAIF,UAAU,CAACG,MAAMC,OAAO,CAACL,QAAQA,QAAQ;YAACA;SAAM;QAEpD,OACE,AAACE,CAAAA,YAAYH,SAASN,cAAa,KACnC,6CAA6C;QAC5CL,CAAAA,oBACCA,kBAAmBkB,OAAO,CACxBH,UACAF,SACI,AAACD,MACEO,GAAG,CACF,uDAAuD;QACvD,uDAAuD;QACvD,kDAAkD;QAClD,oCAAoC;QACpC,CAACC,UAAYC,mBAAmBD,UAEjCE,IAAI,CAAC,OACRD,mBAAmBT,WACpB,GAAE;IAEb,IACA;QACAZ,oBAAoB,GAAG,mCAAmC;;IAE1D,uEAAuE;IACvE,kDAAkD;IACpD;IACA,OAAO;QACLO;QACAgB,QAAQvB;IACV;AACF"}