{"version": 3, "sources": ["../../../../src/shared/lib/router/action-queue.ts"], "sourcesContent": ["import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n} from '../../../client/components/router-reducer/router-reducer-types'\nimport { reducer } from '../../../client/components/router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../is-thenable'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // The rest of the current queue should still execute after this navigation.\n    // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n    newAction.next = actionQueue.pending.next\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nlet globalActionQueue: AppRouterActionQueue | null = null\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n  }\n\n  if (typeof window !== 'undefined') {\n    // The action queue is lazily created on hydration, but after that point\n    // it doesn't change. So we can store it in a global rather than pass\n    // it around everywhere via props/context.\n    if (globalActionQueue !== null) {\n      throw new Error(\n        'Internal Next.js Error: createMutableActionQueue was called more ' +\n          'than once'\n      )\n    }\n    globalActionQueue = actionQueue\n  }\n\n  return actionQueue\n}\n\nexport function getCurrentAppRouterState(): AppRouterState | null {\n  return globalActionQueue !== null ? globalActionQueue.state : null\n}\n"], "names": ["createMutableActionQueue", "getCurrentAppRouterState", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "needsRefresh", "dispatch", "type", "ACTION_REFRESH", "origin", "window", "location", "prevState", "state", "payload", "actionResult", "handleResult", "nextState", "discarded", "resolve", "isThenable", "then", "err", "reject", "dispatchAction", "resolvers", "ACTION_RESTORE", "deferred<PERSON><PERSON><PERSON>", "Promise", "startTransition", "newAction", "last", "ACTION_NAVIGATE", "ACTION_SERVER_ACTION", "globalActionQueue", "initialState", "result", "reducer", "Error"], "mappings": ";;;;;;;;;;;;;;;IAmLgBA,wBAAwB;eAAxBA;;IA+BAC,wBAAwB;eAAxBA;;;oCA1MT;+BACiB;uBACQ;4BACL;AAqB3B,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF,OAAO;YACL,4DAA4D;YAC5D,IAAID,YAAYM,YAAY,EAAE;gBAC5BN,YAAYM,YAAY,GAAG;gBAC3BN,YAAYO,QAAQ,CAClB;oBACEC,MAAMC,kCAAc;oBACpBC,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAT;YAEJ;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMY,YAAYb,YAAYc,KAAK;IAEnCd,YAAYE,OAAO,GAAGG;IAEtB,MAAMU,UAAUV,OAAOU,OAAO;IAC9B,MAAMC,eAAehB,YAAYK,MAAM,CAACQ,WAAWE;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIb,OAAOc,SAAS,EAAE;YACpB;QACF;QAEAnB,YAAYc,KAAK,GAAGI;QAEpBnB,oBAAoBC,aAAaC;QACjCI,OAAOe,OAAO,CAACF;IACjB;IAEA,8DAA8D;IAC9D,IAAIG,IAAAA,sBAAU,EAACL,eAAe;QAC5BA,aAAaM,IAAI,CAACL,cAAc,CAACM;YAC/BxB,oBAAoBC,aAAaC;YACjCI,OAAOmB,MAAM,CAACD;QAChB;IACF,OAAO;QACLN,aAAaD;IACf;AACF;AAEA,SAASS,eACPzB,WAAiC,EACjCe,OAAuB,EACvBd,QAA8B;IAE9B,IAAIyB,YAGA;QAAEN,SAASnB;QAAUuB,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIT,QAAQP,IAAI,KAAKmB,kCAAc,EAAE;QACnC,6DAA6D;QAC7D,MAAMC,kBAAkB,IAAIC,QAAwB,CAACT,SAASI;YAC5DE,YAAY;gBAAEN;gBAASI;YAAO;QAChC;QAEAM,IAAAA,sBAAe,EAAC;YACd,oGAAoG;YACpG,iEAAiE;YACjE7B,SAAS2B;QACX;IACF;IAEA,MAAMG,YAA6B;QACjChB;QACAZ,MAAM;QACNiB,SAASM,UAAUN,OAAO;QAC1BI,QAAQE,UAAUF,MAAM;IAC1B;IAEA,8BAA8B;IAC9B,IAAIxB,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAYgC,IAAI,GAAGD;QAEnB3B,UAAU;YACRJ;YACAK,QAAQ0B;YACR9B;QACF;IACF,OAAO,IACLc,QAAQP,IAAI,KAAKyB,mCAAe,IAChClB,QAAQP,IAAI,KAAKmB,kCAAc,EAC/B;QACA,+EAA+E;QAC/E,oHAAoH;QACpH3B,YAAYE,OAAO,CAACiB,SAAS,GAAG;QAEhC,4EAA4E;QAC5E,sIAAsI;QACtIY,UAAU5B,IAAI,GAAGH,YAAYE,OAAO,CAACC,IAAI;QAEzC,2GAA2G;QAC3G,IAAIH,YAAYE,OAAO,CAACa,OAAO,CAACP,IAAI,KAAK0B,wCAAoB,EAAE;YAC7DlC,YAAYM,YAAY,GAAG;QAC7B;QAEAF,UAAU;YACRJ;YACAK,QAAQ0B;YACR9B;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAYgC,IAAI,KAAK,MAAM;YAC7BhC,YAAYgC,IAAI,CAAC7B,IAAI,GAAG4B;QAC1B;QACA/B,YAAYgC,IAAI,GAAGD;IACrB;AACF;AAEA,IAAII,oBAAiD;AAE9C,SAAStC,yBACduC,YAA4B;IAE5B,MAAMpC,cAAoC;QACxCc,OAAOsB;QACP7B,UAAU,CAACQ,SAAyBd,WAClCwB,eAAezB,aAAae,SAASd;QACvCI,QAAQ,OAAOS,OAAuBT;YACpC,MAAMgC,SAASC,IAAAA,sBAAO,EAACxB,OAAOT;YAC9B,OAAOgC;QACT;QACAnC,SAAS;QACT8B,MAAM;IACR;IAEA,IAAI,OAAOrB,WAAW,aAAa;QACjC,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1C,IAAIwB,sBAAsB,MAAM;YAC9B,MAAM,qBAGL,CAHK,IAAII,MACR,sEACE,cAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACAJ,oBAAoBnC;IACtB;IAEA,OAAOA;AACT;AAEO,SAASF;IACd,OAAOqC,sBAAsB,OAAOA,kBAAkBrB,KAAK,GAAG;AAChE"}