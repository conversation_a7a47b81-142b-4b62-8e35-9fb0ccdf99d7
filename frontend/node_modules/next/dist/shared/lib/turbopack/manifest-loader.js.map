{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/manifest-loader.ts"], "sourcesContent": ["import type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../../../build/webpack/plugins/middleware-plugin'\nimport type {\n  StatsAsset,\n  StatsChunk,\n  StatsChunkGroup,\n  StatsModule,\n  StatsCompilation as WebpackStats,\n} from 'webpack'\nimport type { BuildManifest } from '../../../server/get-page-files'\nimport type { AppBuildManifest } from '../../../build/webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from '../../../build/webpack/plugins/pages-manifest-plugin'\nimport { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport type { ActionManifest } from '../../../build/webpack/plugins/flight-client-entry-plugin'\nimport type { NextFontManifest } from '../../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { REACT_LOADABLE_MANIFEST } from '../constants'\nimport {\n  APP_BUILD_MANIFEST,\n  APP_PATHS_MANIFEST,\n  BUILD_MANIFEST,\n  INTERCEPTION_ROUTE_REWRITE_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  PAGES_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n  WEBPACK_STATS,\n} from '../constants'\nimport { join, posix } from 'path'\nimport { readFile } from 'fs/promises'\nimport type { SetupOpts } from '../../../server/lib/router-utils/setup-dev-bundler'\nimport { deleteCache } from '../../../server/dev/require-cache'\nimport { writeFileAtomic } from '../../../lib/fs/write-atomic'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport {\n  type ClientBuildManifest,\n  normalizeRewritesForBuildManifest,\n  srcEmptySsgManifest,\n  processRoute,\n} from '../../../build/webpack/plugins/build-manifest-plugin'\nimport getAssetPathFromRoute from '../router/utils/get-asset-path-from-route'\nimport { getEntryKey, type EntryKey } from './entry-key'\nimport type { CustomRoutes } from '../../../lib/load-custom-routes'\nimport { getSortedRoutes } from '../router/utils'\nimport { existsSync } from 'fs'\nimport {\n  addMetadataIdToRoute,\n  addRouteSuffix,\n  removeRouteSuffix,\n} from '../../../server/dev/turbopack-utils'\nimport { tryToParsePath } from '../../../lib/try-to-parse-path'\nimport type { Entrypoints } from '../../../build/swc/types'\n\ninterface InstrumentationDefinition {\n  files: string[]\n  name: 'instrumentation'\n}\n\ntype TurbopackMiddlewareManifest = MiddlewareManifest & {\n  instrumentation?: InstrumentationDefinition\n}\n\nconst getManifestPath = (\n  page: string,\n  distDir: string,\n  name: string,\n  type: string\n) => {\n  let manifestPath = posix.join(\n    distDir,\n    `server`,\n    type,\n    type === 'middleware' || type === 'instrumentation'\n      ? ''\n      : type === 'app'\n        ? page\n        : getAssetPathFromRoute(page),\n    name\n  )\n  return manifestPath\n}\n\nasync function readPartialManifest<T>(\n  distDir: string,\n  name:\n    | typeof MIDDLEWARE_MANIFEST\n    | typeof BUILD_MANIFEST\n    | typeof APP_BUILD_MANIFEST\n    | typeof PAGES_MANIFEST\n    | typeof WEBPACK_STATS\n    | typeof APP_PATHS_MANIFEST\n    | `${typeof SERVER_REFERENCE_MANIFEST}.json`\n    | `${typeof NEXT_FONT_MANIFEST}.json`\n    | typeof REACT_LOADABLE_MANIFEST,\n  pageName: string,\n  type: 'pages' | 'app' | 'middleware' | 'instrumentation' = 'pages'\n): Promise<T> {\n  const page = pageName\n  const isSitemapRoute = /[\\\\/]sitemap(.xml)?\\/route$/.test(page)\n  let manifestPath = getManifestPath(page, distDir, name, type)\n\n  // Check the ambiguity of /sitemap and /sitemap.xml\n  if (isSitemapRoute && !existsSync(manifestPath)) {\n    manifestPath = getManifestPath(\n      pageName.replace(/\\/sitemap\\/route$/, '/sitemap.xml/route'),\n      distDir,\n      name,\n      type\n    )\n  }\n  // existsSync is faster than using the async version\n  if (!existsSync(manifestPath) && page.endsWith('/route')) {\n    // TODO: Improve implementation of metadata routes, currently it requires this extra check for the variants of the files that can be written.\n    let metadataPage = addRouteSuffix(\n      addMetadataIdToRoute(removeRouteSuffix(page))\n    )\n    manifestPath = getManifestPath(metadataPage, distDir, name, type)\n  }\n  return JSON.parse(await readFile(posix.join(manifestPath), 'utf-8')) as T\n}\n\nexport class TurbopackManifestLoader {\n  private actionManifests: Map<EntryKey, ActionManifest> = new Map()\n  private appBuildManifests: Map<EntryKey, AppBuildManifest> = new Map()\n  private appPathsManifests: Map<EntryKey, PagesManifest> = new Map()\n  private buildManifests: Map<EntryKey, BuildManifest> = new Map()\n  private fontManifests: Map<EntryKey, NextFontManifest> = new Map()\n  private middlewareManifests: Map<EntryKey, TurbopackMiddlewareManifest> =\n    new Map()\n  private pagesManifests: Map<string, PagesManifest> = new Map()\n  private webpackStats: Map<EntryKey, WebpackStats> = new Map()\n  private encryptionKey: string\n\n  private readonly distDir: string\n  private readonly buildId: string\n\n  constructor({\n    distDir,\n    buildId,\n    encryptionKey,\n  }: {\n    buildId: string\n    distDir: string\n    encryptionKey: string\n  }) {\n    this.distDir = distDir\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n  }\n\n  delete(key: EntryKey) {\n    this.actionManifests.delete(key)\n    this.appBuildManifests.delete(key)\n    this.appPathsManifests.delete(key)\n    this.buildManifests.delete(key)\n    this.fontManifests.delete(key)\n    this.middlewareManifests.delete(key)\n    this.pagesManifests.delete(key)\n    this.webpackStats.delete(key)\n  }\n\n  async loadActionManifest(pageName: string): Promise<void> {\n    this.actionManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${SERVER_REFERENCE_MANIFEST}.json`,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async mergeActionManifests(manifests: Iterable<ActionManifest>) {\n    type ActionEntries = ActionManifest['edge' | 'node']\n    const manifest: ActionManifest = {\n      node: {},\n      edge: {},\n      encryptionKey: this.encryptionKey,\n    }\n\n    function mergeActionIds(\n      actionEntries: ActionEntries,\n      other: ActionEntries\n    ): void {\n      for (const key in other) {\n        const action = (actionEntries[key] ??= {\n          workers: {},\n          layer: {},\n        })\n        Object.assign(action.workers, other[key].workers)\n        Object.assign(action.layer, other[key].layer)\n      }\n    }\n\n    for (const m of manifests) {\n      mergeActionIds(manifest.node, m.node)\n      mergeActionIds(manifest.edge, m.edge)\n    }\n    for (const key in manifest.node) {\n      const entry = manifest.node[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n    for (const key in manifest.edge) {\n      const entry = manifest.edge[key]\n      entry.workers = sortObjectByKey(entry.workers)\n      entry.layer = sortObjectByKey(entry.layer)\n    }\n\n    return manifest\n  }\n\n  private async writeActionManifest(): Promise<void> {\n    const actionManifest = await this.mergeActionManifests(\n      this.actionManifests.values()\n    )\n    const actionManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.json`\n    )\n    const actionManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${SERVER_REFERENCE_MANIFEST}.js`\n    )\n    const json = JSON.stringify(actionManifest, null, 2)\n    deleteCache(actionManifestJsonPath)\n    deleteCache(actionManifestJsPath)\n    await writeFileAtomic(actionManifestJsonPath, json)\n    await writeFileAtomic(\n      actionManifestJsPath,\n      `self.__RSC_SERVER_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  async loadAppBuildManifest(pageName: string): Promise<void> {\n    this.appBuildManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_BUILD_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private mergeAppBuildManifests(manifests: Iterable<AppBuildManifest>) {\n    const manifest: AppBuildManifest = {\n      pages: {},\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n    }\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeAppBuildManifest(): Promise<void> {\n    const appBuildManifest = this.mergeAppBuildManifests(\n      this.appBuildManifests.values()\n    )\n    const appBuildManifestPath = join(this.distDir, APP_BUILD_MANIFEST)\n    deleteCache(appBuildManifestPath)\n    await writeFileAtomic(\n      appBuildManifestPath,\n      JSON.stringify(appBuildManifest, null, 2)\n    )\n  }\n\n  async loadAppPathsManifest(pageName: string): Promise<void> {\n    this.appPathsManifests.set(\n      getEntryKey('app', 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        APP_PATHS_MANIFEST,\n        pageName,\n        'app'\n      )\n    )\n  }\n\n  private async writeAppPathsManifest(): Promise<void> {\n    const appPathsManifest = this.mergePagesManifests(\n      this.appPathsManifests.values()\n    )\n    const appPathsManifestPath = join(\n      this.distDir,\n      'server',\n      APP_PATHS_MANIFEST\n    )\n    deleteCache(appPathsManifestPath)\n    await writeFileAtomic(\n      appPathsManifestPath,\n      JSON.stringify(appPathsManifest, null, 2)\n    )\n  }\n\n  private async writeWebpackStats(): Promise<void> {\n    const webpackStats = this.mergeWebpackStats(this.webpackStats.values())\n    const path = join(this.distDir, 'server', WEBPACK_STATS)\n    deleteCache(path)\n    await writeFileAtomic(path, JSON.stringify(webpackStats, null, 2))\n  }\n\n  async loadBuildManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.buildManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(this.distDir, BUILD_MANIFEST, pageName, type)\n    )\n  }\n\n  async loadWebpackStats(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.webpackStats.set(\n      getEntryKey(type, 'client', pageName),\n      await readPartialManifest(this.distDir, WEBPACK_STATS, pageName, type)\n    )\n  }\n\n  private mergeWebpackStats(statsFiles: Iterable<WebpackStats>): WebpackStats {\n    const entrypoints: Record<string, StatsChunkGroup> = {}\n    const assets: Map<string, StatsAsset> = new Map()\n    const chunks: Map<string, StatsChunk> = new Map()\n    const modules: Map<string | number, StatsModule> = new Map()\n\n    for (const statsFile of statsFiles) {\n      if (statsFile.entrypoints) {\n        for (const [k, v] of Object.entries(statsFile.entrypoints)) {\n          if (!entrypoints[k]) {\n            entrypoints[k] = v\n          }\n        }\n      }\n\n      if (statsFile.assets) {\n        for (const asset of statsFile.assets) {\n          if (!assets.has(asset.name)) {\n            assets.set(asset.name, asset)\n          }\n        }\n      }\n\n      if (statsFile.chunks) {\n        for (const chunk of statsFile.chunks) {\n          if (!chunks.has(chunk.name)) {\n            chunks.set(chunk.name, chunk)\n          }\n        }\n      }\n\n      if (statsFile.modules) {\n        for (const module of statsFile.modules) {\n          const id = module.id\n          if (id != null) {\n            // Merge the chunk list for the module. This can vary across endpoints.\n            const existing = modules.get(id)\n            if (existing == null) {\n              modules.set(id, module)\n            } else if (module.chunks != null && existing.chunks != null) {\n              for (const chunk of module.chunks) {\n                if (!existing.chunks.includes(chunk)) {\n                  existing.chunks.push(chunk)\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return {\n      entrypoints,\n      assets: [...assets.values()],\n      chunks: [...chunks.values()],\n      modules: [...modules.values()],\n    }\n  }\n\n  private mergeBuildManifests(manifests: Iterable<BuildManifest>) {\n    const manifest: Partial<BuildManifest> & Pick<BuildManifest, 'pages'> = {\n      pages: {\n        '/_app': [],\n      },\n      // Something in next.js depends on these to exist even for app dir rendering\n      devFiles: [],\n      ampDevFiles: [],\n      polyfillFiles: [],\n      lowPriorityFiles: [\n        `static/${this.buildId}/_ssgManifest.js`,\n        `static/${this.buildId}/_buildManifest.js`,\n      ],\n      rootMainFiles: [],\n      ampFirstPages: [],\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.pages, m.pages)\n      if (m.rootMainFiles.length) manifest.rootMainFiles = m.rootMainFiles\n      // polyfillFiles should always be the same, so we can overwrite instead of actually merging\n      if (m.polyfillFiles.length) manifest.polyfillFiles = m.polyfillFiles\n    }\n    manifest.pages = sortObjectByKey(manifest.pages) as BuildManifest['pages']\n    return manifest\n  }\n\n  private async writeBuildManifest(\n    entrypoints: Entrypoints,\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined,\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n  ): Promise<void> {\n    const rewrites = productionRewrites ?? {\n      ...devRewrites,\n      beforeFiles: (devRewrites?.beforeFiles ?? []).map(processRoute),\n      afterFiles: (devRewrites?.afterFiles ?? []).map(processRoute),\n      fallback: (devRewrites?.fallback ?? []).map(processRoute),\n    }\n    const buildManifest = this.mergeBuildManifests(this.buildManifests.values())\n    const buildManifestPath = join(this.distDir, BUILD_MANIFEST)\n    const middlewareBuildManifestPath = join(\n      this.distDir,\n      'server',\n      `${MIDDLEWARE_BUILD_MANIFEST}.js`\n    )\n    const interceptionRewriteManifestPath = join(\n      this.distDir,\n      'server',\n      `${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`\n    )\n    deleteCache(buildManifestPath)\n    deleteCache(middlewareBuildManifestPath)\n    deleteCache(interceptionRewriteManifestPath)\n    await writeFileAtomic(\n      buildManifestPath,\n      JSON.stringify(buildManifest, null, 2)\n    )\n    await writeFileAtomic(\n      middlewareBuildManifestPath,\n      // we use globalThis here because middleware can be node\n      // which doesn't have \"self\"\n      `globalThis.__BUILD_MANIFEST=${JSON.stringify(buildManifest)};`\n    )\n\n    const interceptionRewrites = JSON.stringify(\n      rewrites.beforeFiles.filter(isInterceptionRouteRewrite)\n    )\n\n    await writeFileAtomic(\n      interceptionRewriteManifestPath,\n      `self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST=${JSON.stringify(\n        interceptionRewrites\n      )};`\n    )\n\n    const pagesKeys = [...entrypoints.page.keys()]\n    if (entrypoints.global.app) {\n      pagesKeys.push('/_app')\n    }\n    if (entrypoints.global.error) {\n      pagesKeys.push('/_error')\n    }\n\n    const sortedPageKeys = getSortedRoutes(pagesKeys)\n    const content: ClientBuildManifest = {\n      __rewrites: normalizeRewritesForBuildManifest(rewrites) as any,\n      ...Object.fromEntries(\n        sortedPageKeys.map((pathname) => [\n          pathname,\n          [`static/chunks/pages${pathname === '/' ? '/index' : pathname}.js`],\n        ])\n      ),\n      sortedPages: sortedPageKeys,\n    }\n    const buildManifestJs = `self.__BUILD_MANIFEST = ${JSON.stringify(\n      content\n    )};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()`\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_buildManifest.js'),\n      buildManifestJs\n    )\n    await writeFileAtomic(\n      join(this.distDir, 'static', this.buildId, '_ssgManifest.js'),\n      srcEmptySsgManifest\n    )\n  }\n\n  private async writeClientMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    const matchers = middlewareManifest?.middleware['/']?.matchers || []\n\n    const clientMiddlewareManifestPath = join(\n      this.distDir,\n      'static',\n      this.buildId,\n      `${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n    )\n    deleteCache(clientMiddlewareManifestPath)\n    await writeFileAtomic(\n      clientMiddlewareManifestPath,\n      JSON.stringify(matchers, null, 2)\n    )\n  }\n\n  private async writeFallbackBuildManifest(): Promise<void> {\n    const fallbackBuildManifest = this.mergeBuildManifests(\n      [\n        this.buildManifests.get(getEntryKey('pages', 'server', '_app')),\n        this.buildManifests.get(getEntryKey('pages', 'server', '_error')),\n      ].filter(Boolean) as BuildManifest[]\n    )\n    const fallbackBuildManifestPath = join(\n      this.distDir,\n      `fallback-${BUILD_MANIFEST}`\n    )\n    deleteCache(fallbackBuildManifestPath)\n    await writeFileAtomic(\n      fallbackBuildManifestPath,\n      JSON.stringify(fallbackBuildManifest, null, 2)\n    )\n  }\n\n  async loadFontManifest(\n    pageName: string,\n    type: 'app' | 'pages' = 'pages'\n  ): Promise<void> {\n    this.fontManifests.set(\n      getEntryKey(type, 'server', pageName),\n      await readPartialManifest(\n        this.distDir,\n        `${NEXT_FONT_MANIFEST}.json`,\n        pageName,\n        type\n      )\n    )\n  }\n\n  private mergeFontManifests(manifests: Iterable<NextFontManifest>) {\n    const manifest: NextFontManifest = {\n      app: {},\n      appUsingSizeAdjust: false,\n      pages: {},\n      pagesUsingSizeAdjust: false,\n    }\n    for (const m of manifests) {\n      Object.assign(manifest.app, m.app)\n      Object.assign(manifest.pages, m.pages)\n\n      manifest.appUsingSizeAdjust =\n        manifest.appUsingSizeAdjust || m.appUsingSizeAdjust\n      manifest.pagesUsingSizeAdjust =\n        manifest.pagesUsingSizeAdjust || m.pagesUsingSizeAdjust\n    }\n    manifest.app = sortObjectByKey(manifest.app)\n    manifest.pages = sortObjectByKey(manifest.pages)\n    return manifest\n  }\n\n  private async writeNextFontManifest(): Promise<void> {\n    const fontManifest = this.mergeFontManifests(this.fontManifests.values())\n    const json = JSON.stringify(fontManifest, null, 2)\n\n    const fontManifestJsonPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.json`\n    )\n    const fontManifestJsPath = join(\n      this.distDir,\n      'server',\n      `${NEXT_FONT_MANIFEST}.js`\n    )\n    deleteCache(fontManifestJsonPath)\n    deleteCache(fontManifestJsPath)\n    await writeFileAtomic(fontManifestJsonPath, json)\n    await writeFileAtomic(\n      fontManifestJsPath,\n      `self.__NEXT_FONT_MANIFEST=${JSON.stringify(json)}`\n    )\n  }\n\n  async loadMiddlewareManifest(\n    pageName: string,\n    type: 'pages' | 'app' | 'middleware' | 'instrumentation'\n  ): Promise<void> {\n    this.middlewareManifests.set(\n      getEntryKey(\n        type === 'middleware' || type === 'instrumentation' ? 'root' : type,\n        'server',\n        pageName\n      ),\n      await readPartialManifest(\n        this.distDir,\n        MIDDLEWARE_MANIFEST,\n        pageName,\n        type\n      )\n    )\n  }\n\n  getMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.get(key)\n  }\n\n  deleteMiddlewareManifest(key: EntryKey) {\n    return this.middlewareManifests.delete(key)\n  }\n\n  private mergeMiddlewareManifests(\n    manifests: Iterable<TurbopackMiddlewareManifest>\n  ): MiddlewareManifest {\n    const manifest: MiddlewareManifest = {\n      version: 3,\n      middleware: {},\n      sortedMiddleware: [],\n      functions: {},\n    }\n    let instrumentation: InstrumentationDefinition | undefined = undefined\n    for (const m of manifests) {\n      Object.assign(manifest.functions, m.functions)\n      Object.assign(manifest.middleware, m.middleware)\n      if (m.instrumentation) {\n        instrumentation = m.instrumentation\n      }\n    }\n    manifest.functions = sortObjectByKey(manifest.functions)\n    manifest.middleware = sortObjectByKey(manifest.middleware)\n    const updateFunctionDefinition = (\n      fun: EdgeFunctionDefinition\n    ): EdgeFunctionDefinition => {\n      return {\n        ...fun,\n        files: [...(instrumentation?.files ?? []), ...fun.files],\n      }\n    }\n    for (const key of Object.keys(manifest.middleware)) {\n      const value = manifest.middleware[key]\n      manifest.middleware[key] = updateFunctionDefinition(value)\n    }\n    for (const key of Object.keys(manifest.functions)) {\n      const value = manifest.functions[key]\n      manifest.functions[key] = updateFunctionDefinition(value)\n    }\n    for (const fun of Object.values(manifest.functions).concat(\n      Object.values(manifest.middleware)\n    )) {\n      for (const matcher of fun.matchers) {\n        if (!matcher.regexp) {\n          matcher.regexp = pathToRegexp(matcher.originalSource, [], {\n            delimiter: '/',\n            sensitive: false,\n            strict: true,\n          }).source.replaceAll('\\\\/', '/')\n        }\n      }\n    }\n    manifest.sortedMiddleware = Object.keys(manifest.middleware)\n\n    return manifest\n  }\n\n  private async writeMiddlewareManifest(): Promise<void> {\n    const middlewareManifest = this.mergeMiddlewareManifests(\n      this.middlewareManifests.values()\n    )\n\n    // Normalize regexes as it uses path-to-regexp\n    for (const key in middlewareManifest.middleware) {\n      middlewareManifest.middleware[key].matchers.forEach((matcher) => {\n        if (!matcher.regexp.startsWith('^')) {\n          const parsedPage = tryToParsePath(matcher.regexp)\n          if (parsedPage.error || !parsedPage.regexStr) {\n            throw new Error(`Invalid source: ${matcher.regexp}`)\n          }\n          matcher.regexp = parsedPage.regexStr\n        }\n      })\n    }\n\n    const middlewareManifestPath = join(\n      this.distDir,\n      'server',\n      MIDDLEWARE_MANIFEST\n    )\n    deleteCache(middlewareManifestPath)\n    await writeFileAtomic(\n      middlewareManifestPath,\n      JSON.stringify(middlewareManifest, null, 2)\n    )\n  }\n\n  async loadPagesManifest(pageName: string): Promise<void> {\n    this.pagesManifests.set(\n      getEntryKey('pages', 'server', pageName),\n      await readPartialManifest(this.distDir, PAGES_MANIFEST, pageName)\n    )\n  }\n\n  private mergePagesManifests(manifests: Iterable<PagesManifest>) {\n    const manifest: PagesManifest = {}\n    for (const m of manifests) {\n      Object.assign(manifest, m)\n    }\n    return sortObjectByKey(manifest)\n  }\n\n  private async writePagesManifest(): Promise<void> {\n    const pagesManifest = this.mergePagesManifests(this.pagesManifests.values())\n    const pagesManifestPath = join(this.distDir, 'server', PAGES_MANIFEST)\n    deleteCache(pagesManifestPath)\n    await writeFileAtomic(\n      pagesManifestPath,\n      JSON.stringify(pagesManifest, null, 2)\n    )\n  }\n\n  async writeManifests({\n    devRewrites,\n    productionRewrites,\n    entrypoints,\n  }: {\n    devRewrites: SetupOpts['fsChecker']['rewrites'] | undefined\n    productionRewrites: CustomRoutes['rewrites'] | undefined\n    entrypoints: Entrypoints\n  }) {\n    await this.writeActionManifest()\n    await this.writeAppBuildManifest()\n    await this.writeAppPathsManifest()\n    await this.writeBuildManifest(entrypoints, devRewrites, productionRewrites)\n    await this.writeFallbackBuildManifest()\n    await this.writeMiddlewareManifest()\n    await this.writeClientMiddlewareManifest()\n    await this.writeNextFontManifest()\n    await this.writePagesManifest()\n\n    if (process.env.TURBOPACK_STATS != null) {\n      await this.writeWebpackStats()\n    }\n  }\n}\n\nfunction sortObjectByKey(obj: Record<string, any>) {\n  return Object.keys(obj)\n    .sort()\n    .reduce(\n      (acc, key) => {\n        acc[key] = obj[key]\n        return acc\n      },\n      {} as Record<string, any>\n    )\n}\n"], "names": ["TurbopackManifestLoader", "getManifestPath", "page", "distDir", "name", "type", "manifestPath", "posix", "join", "getAssetPathFromRoute", "readPartialManifest", "pageName", "isSitemapRoute", "test", "existsSync", "replace", "endsWith", "metadataPage", "addRouteSuffix", "addMetadataIdToRoute", "removeRouteSuffix", "JSON", "parse", "readFile", "delete", "key", "actionManifests", "appBuildManifests", "appPathsManifests", "buildManifests", "fontManifests", "middlewareManifests", "pagesManifests", "webpackStats", "loadActionManifest", "set", "getEntry<PERSON>ey", "SERVER_REFERENCE_MANIFEST", "mergeActionManifests", "manifests", "manifest", "node", "edge", "<PERSON><PERSON><PERSON>", "mergeActionIds", "actionEntries", "other", "action", "workers", "layer", "Object", "assign", "m", "entry", "sortObjectByKey", "writeActionManifest", "actionManifest", "values", "actionManifestJsonPath", "actionManifestJsPath", "json", "stringify", "deleteCache", "writeFileAtomic", "loadAppBuildManifest", "APP_BUILD_MANIFEST", "mergeAppBuildManifests", "pages", "writeAppBuildManifest", "appBuildManifest", "appBuildManifestPath", "loadAppPathsManifest", "APP_PATHS_MANIFEST", "writeAppPathsManifest", "appPathsManifest", "mergePagesManifests", "appPathsManifestPath", "writeWebpackStats", "mergeWebpackStats", "path", "WEBPACK_STATS", "loadBuildManifest", "BUILD_MANIFEST", "loadWebpackStats", "statsFiles", "entrypoints", "assets", "Map", "chunks", "modules", "statsFile", "k", "v", "entries", "asset", "has", "chunk", "module", "id", "existing", "get", "includes", "push", "mergeBuildManifests", "devFiles", "ampDevFiles", "polyfillFiles", "lowPriorityFiles", "buildId", "rootMainFiles", "ampFirstPages", "length", "writeBuildManifest", "devRewrites", "productionRewrites", "rewrites", "beforeFiles", "map", "processRoute", "afterFiles", "fallback", "buildManifest", "buildManifestPath", "middlewareBuildManifestPath", "MIDDLEWARE_BUILD_MANIFEST", "interceptionRewriteManifestPath", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "interceptionRewrites", "filter", "isInterceptionRouteRewrite", "pagesKeys", "keys", "global", "app", "error", "sortedPageKeys", "getSortedRoutes", "content", "__rewrites", "normalizeRewritesForBuildManifest", "fromEntries", "pathname", "sortedPages", "buildManifestJs", "srcEmptySsgManifest", "writeClientMiddlewareManifest", "middlewareManifest", "mergeMiddlewareManifests", "matchers", "middleware", "clientMiddlewareManifestPath", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "writeFallbackBuildManifest", "fallbackBuildManifest", "Boolean", "fallbackBuildManifestPath", "loadFontManifest", "NEXT_FONT_MANIFEST", "mergeFontManifests", "appUsingSizeAdjust", "pagesUsingSizeAdjust", "writeNextFontManifest", "fontManifest", "fontManifestJsonPath", "fontManifestJsPath", "loadMiddlewareManifest", "MIDDLEWARE_MANIFEST", "getMiddlewareManifest", "deleteMiddlewareManifest", "version", "sortedMiddleware", "functions", "instrumentation", "undefined", "updateFunctionDefinition", "fun", "files", "value", "concat", "matcher", "regexp", "pathToRegexp", "originalSource", "delimiter", "sensitive", "strict", "source", "replaceAll", "writeMiddlewareManifest", "for<PERSON>ach", "startsWith", "parsedPage", "tryToParsePath", "regexStr", "Error", "middlewareManifestPath", "loadPagesManifest", "PAGES_MANIFEST", "writePagesManifest", "pagesManifest", "pagesManifestPath", "writeManifests", "process", "env", "TURBOPACK_STATS", "constructor", "obj", "sort", "reduce", "acc"], "mappings": ";;;;+BA4HaA;;;eAAAA;;;;8BA9GgB;2BAgBtB;sBACqB;0BACH;8BAEG;6BACI;oDACW;qCAMpC;gFAC2B;0BACS;uBAEX;oBACL;gCAKpB;gCACwB;AAY/B,MAAMC,kBAAkB,CACtBC,MACAC,SACAC,MACAC;IAEA,IAAIC,eAAeC,WAAK,CAACC,IAAI,CAC3BL,SACC,UACDE,MACAA,SAAS,gBAAgBA,SAAS,oBAC9B,KACAA,SAAS,QACPH,OACAO,IAAAA,8BAAqB,EAACP,OAC5BE;IAEF,OAAOE;AACT;AAEA,eAAeI,oBACbP,OAAe,EACfC,IASkC,EAClCO,QAAgB,EAChBN,IAAkE;IAAlEA,IAAAA,iBAAAA,OAA2D;IAE3D,MAAMH,OAAOS;IACb,MAAMC,iBAAiB,8BAA8BC,IAAI,CAACX;IAC1D,IAAII,eAAeL,gBAAgBC,MAAMC,SAASC,MAAMC;IAExD,mDAAmD;IACnD,IAAIO,kBAAkB,CAACE,IAAAA,cAAU,EAACR,eAAe;QAC/CA,eAAeL,gBACbU,SAASI,OAAO,CAAC,qBAAqB,uBACtCZ,SACAC,MACAC;IAEJ;IACA,oDAAoD;IACpD,IAAI,CAACS,IAAAA,cAAU,EAACR,iBAAiBJ,KAAKc,QAAQ,CAAC,WAAW;QACxD,6IAA6I;QAC7I,IAAIC,eAAeC,IAAAA,8BAAc,EAC/BC,IAAAA,oCAAoB,EAACC,IAAAA,iCAAiB,EAAClB;QAEzCI,eAAeL,gBAAgBgB,cAAcd,SAASC,MAAMC;IAC9D;IACA,OAAOgB,KAAKC,KAAK,CAAC,MAAMC,IAAAA,kBAAQ,EAAChB,WAAK,CAACC,IAAI,CAACF,eAAe;AAC7D;AAEO,MAAMN;IA6BXwB,OAAOC,GAAa,EAAE;QACpB,IAAI,CAACC,eAAe,CAACF,MAAM,CAACC;QAC5B,IAAI,CAACE,iBAAiB,CAACH,MAAM,CAACC;QAC9B,IAAI,CAACG,iBAAiB,CAACJ,MAAM,CAACC;QAC9B,IAAI,CAACI,cAAc,CAACL,MAAM,CAACC;QAC3B,IAAI,CAACK,aAAa,CAACN,MAAM,CAACC;QAC1B,IAAI,CAACM,mBAAmB,CAACP,MAAM,CAACC;QAChC,IAAI,CAACO,cAAc,CAACR,MAAM,CAACC;QAC3B,IAAI,CAACQ,YAAY,CAACT,MAAM,CAACC;IAC3B;IAEA,MAAMS,mBAAmBvB,QAAgB,EAAiB;QACxD,IAAI,CAACe,eAAe,CAACS,GAAG,CACtBC,IAAAA,qBAAW,EAAC,OAAO,UAAUzB,WAC7B,MAAMD,oBACJ,IAAI,CAACP,OAAO,EACZ,AAAC,KAAEkC,oCAAyB,GAAC,SAC7B1B,UACA;IAGN;IAEA,MAAc2B,qBAAqBC,SAAmC,EAAE;QAEtE,MAAMC,WAA2B;YAC/BC,MAAM,CAAC;YACPC,MAAM,CAAC;YACPC,eAAe,IAAI,CAACA,aAAa;QACnC;QAEA,SAASC,eACPC,aAA4B,EAC5BC,KAAoB;YAEpB,IAAK,MAAMrB,OAAOqB,MAAO;oBACPD,gBAAcpB;;gBAA9B,MAAMsB,SAAUF,MAAAA,iBAAAA,cAAa,CAACpB,OAAAA,IAAI,gBAAlBoB,cAAa,CAACpB,KAAI,GAAK;oBACrCuB,SAAS,CAAC;oBACVC,OAAO,CAAC;gBACV;gBACAC,OAAOC,MAAM,CAACJ,OAAOC,OAAO,EAAEF,KAAK,CAACrB,IAAI,CAACuB,OAAO;gBAChDE,OAAOC,MAAM,CAACJ,OAAOE,KAAK,EAAEH,KAAK,CAACrB,IAAI,CAACwB,KAAK;YAC9C;QACF;QAEA,KAAK,MAAMG,KAAKb,UAAW;YACzBK,eAAeJ,SAASC,IAAI,EAAEW,EAAEX,IAAI;YACpCG,eAAeJ,SAASE,IAAI,EAAEU,EAAEV,IAAI;QACtC;QACA,IAAK,MAAMjB,OAAOe,SAASC,IAAI,CAAE;YAC/B,MAAMY,QAAQb,SAASC,IAAI,CAAChB,IAAI;YAChC4B,MAAML,OAAO,GAAGM,gBAAgBD,MAAML,OAAO;YAC7CK,MAAMJ,KAAK,GAAGK,gBAAgBD,MAAMJ,KAAK;QAC3C;QACA,IAAK,MAAMxB,OAAOe,SAASE,IAAI,CAAE;YAC/B,MAAMW,QAAQb,SAASE,IAAI,CAACjB,IAAI;YAChC4B,MAAML,OAAO,GAAGM,gBAAgBD,MAAML,OAAO;YAC7CK,MAAMJ,KAAK,GAAGK,gBAAgBD,MAAMJ,KAAK;QAC3C;QAEA,OAAOT;IACT;IAEA,MAAce,sBAAqC;QACjD,MAAMC,iBAAiB,MAAM,IAAI,CAAClB,oBAAoB,CACpD,IAAI,CAACZ,eAAe,CAAC+B,MAAM;QAE7B,MAAMC,yBAAyBlD,IAAAA,UAAI,EACjC,IAAI,CAACL,OAAO,EACZ,UACA,AAAC,KAAEkC,oCAAyB,GAAC;QAE/B,MAAMsB,uBAAuBnD,IAAAA,UAAI,EAC/B,IAAI,CAACL,OAAO,EACZ,UACA,AAAC,KAAEkC,oCAAyB,GAAC;QAE/B,MAAMuB,OAAOvC,KAAKwC,SAAS,CAACL,gBAAgB,MAAM;QAClDM,IAAAA,yBAAW,EAACJ;QACZI,IAAAA,yBAAW,EAACH;QACZ,MAAMI,IAAAA,4BAAe,EAACL,wBAAwBE;QAC9C,MAAMG,IAAAA,4BAAe,EACnBJ,sBACA,AAAC,gCAA6BtC,KAAKwC,SAAS,CAACD;IAEjD;IAEA,MAAMI,qBAAqBrD,QAAgB,EAAiB;QAC1D,IAAI,CAACgB,iBAAiB,CAACQ,GAAG,CACxBC,IAAAA,qBAAW,EAAC,OAAO,UAAUzB,WAC7B,MAAMD,oBACJ,IAAI,CAACP,OAAO,EACZ8D,6BAAkB,EAClBtD,UACA;IAGN;IAEQuD,uBAAuB3B,SAAqC,EAAE;QACpE,MAAMC,WAA6B;YACjC2B,OAAO,CAAC;QACV;QACA,KAAK,MAAMf,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAAS2B,KAAK,EAAEf,EAAEe,KAAK;QACvC;QACA3B,SAAS2B,KAAK,GAAGb,gBAAgBd,SAAS2B,KAAK;QAC/C,OAAO3B;IACT;IAEA,MAAc4B,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACH,sBAAsB,CAClD,IAAI,CAACvC,iBAAiB,CAAC8B,MAAM;QAE/B,MAAMa,uBAAuB9D,IAAAA,UAAI,EAAC,IAAI,CAACL,OAAO,EAAE8D,6BAAkB;QAClEH,IAAAA,yBAAW,EAACQ;QACZ,MAAMP,IAAAA,4BAAe,EACnBO,sBACAjD,KAAKwC,SAAS,CAACQ,kBAAkB,MAAM;IAE3C;IAEA,MAAME,qBAAqB5D,QAAgB,EAAiB;QAC1D,IAAI,CAACiB,iBAAiB,CAACO,GAAG,CACxBC,IAAAA,qBAAW,EAAC,OAAO,UAAUzB,WAC7B,MAAMD,oBACJ,IAAI,CAACP,OAAO,EACZqE,6BAAkB,EAClB7D,UACA;IAGN;IAEA,MAAc8D,wBAAuC;QACnD,MAAMC,mBAAmB,IAAI,CAACC,mBAAmB,CAC/C,IAAI,CAAC/C,iBAAiB,CAAC6B,MAAM;QAE/B,MAAMmB,uBAAuBpE,IAAAA,UAAI,EAC/B,IAAI,CAACL,OAAO,EACZ,UACAqE,6BAAkB;QAEpBV,IAAAA,yBAAW,EAACc;QACZ,MAAMb,IAAAA,4BAAe,EACnBa,sBACAvD,KAAKwC,SAAS,CAACa,kBAAkB,MAAM;IAE3C;IAEA,MAAcG,oBAAmC;QAC/C,MAAM5C,eAAe,IAAI,CAAC6C,iBAAiB,CAAC,IAAI,CAAC7C,YAAY,CAACwB,MAAM;QACpE,MAAMsB,OAAOvE,IAAAA,UAAI,EAAC,IAAI,CAACL,OAAO,EAAE,UAAU6E,wBAAa;QACvDlB,IAAAA,yBAAW,EAACiB;QACZ,MAAMhB,IAAAA,4BAAe,EAACgB,MAAM1D,KAAKwC,SAAS,CAAC5B,cAAc,MAAM;IACjE;IAEA,MAAMgD,kBACJtE,QAAgB,EAChBN,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACwB,cAAc,CAACM,GAAG,CACrBC,IAAAA,qBAAW,EAAC/B,MAAM,UAAUM,WAC5B,MAAMD,oBAAoB,IAAI,CAACP,OAAO,EAAE+E,yBAAc,EAAEvE,UAAUN;IAEtE;IAEA,MAAM8E,iBACJxE,QAAgB,EAChBN,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAAC4B,YAAY,CAACE,GAAG,CACnBC,IAAAA,qBAAW,EAAC/B,MAAM,UAAUM,WAC5B,MAAMD,oBAAoB,IAAI,CAACP,OAAO,EAAE6E,wBAAa,EAAErE,UAAUN;IAErE;IAEQyE,kBAAkBM,UAAkC,EAAgB;QAC1E,MAAMC,cAA+C,CAAC;QACtD,MAAMC,SAAkC,IAAIC;QAC5C,MAAMC,SAAkC,IAAID;QAC5C,MAAME,UAA6C,IAAIF;QAEvD,KAAK,MAAMG,aAAaN,WAAY;YAClC,IAAIM,UAAUL,WAAW,EAAE;gBACzB,KAAK,MAAM,CAACM,GAAGC,EAAE,IAAI1C,OAAO2C,OAAO,CAACH,UAAUL,WAAW,EAAG;oBAC1D,IAAI,CAACA,WAAW,CAACM,EAAE,EAAE;wBACnBN,WAAW,CAACM,EAAE,GAAGC;oBACnB;gBACF;YACF;YAEA,IAAIF,UAAUJ,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASJ,UAAUJ,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOS,GAAG,CAACD,MAAM1F,IAAI,GAAG;wBAC3BkF,OAAOnD,GAAG,CAAC2D,MAAM1F,IAAI,EAAE0F;oBACzB;gBACF;YACF;YAEA,IAAIJ,UAAUF,MAAM,EAAE;gBACpB,KAAK,MAAMQ,SAASN,UAAUF,MAAM,CAAE;oBACpC,IAAI,CAACA,OAAOO,GAAG,CAACC,MAAM5F,IAAI,GAAG;wBAC3BoF,OAAOrD,GAAG,CAAC6D,MAAM5F,IAAI,EAAE4F;oBACzB;gBACF;YACF;YAEA,IAAIN,UAAUD,OAAO,EAAE;gBACrB,KAAK,MAAMQ,UAAUP,UAAUD,OAAO,CAAE;oBACtC,MAAMS,KAAKD,OAAOC,EAAE;oBACpB,IAAIA,MAAM,MAAM;wBACd,uEAAuE;wBACvE,MAAMC,WAAWV,QAAQW,GAAG,CAACF;wBAC7B,IAAIC,YAAY,MAAM;4BACpBV,QAAQtD,GAAG,CAAC+D,IAAID;wBAClB,OAAO,IAAIA,OAAOT,MAAM,IAAI,QAAQW,SAASX,MAAM,IAAI,MAAM;4BAC3D,KAAK,MAAMQ,SAASC,OAAOT,MAAM,CAAE;gCACjC,IAAI,CAACW,SAASX,MAAM,CAACa,QAAQ,CAACL,QAAQ;oCACpCG,SAASX,MAAM,CAACc,IAAI,CAACN;gCACvB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACLX;YACAC,QAAQ;mBAAIA,OAAO7B,MAAM;aAAG;YAC5B+B,QAAQ;mBAAIA,OAAO/B,MAAM;aAAG;YAC5BgC,SAAS;mBAAIA,QAAQhC,MAAM;aAAG;QAChC;IACF;IAEQ8C,oBAAoBhE,SAAkC,EAAE;QAC9D,MAAMC,WAAkE;YACtE2B,OAAO;gBACL,SAAS,EAAE;YACb;YACA,4EAA4E;YAC5EqC,UAAU,EAAE;YACZC,aAAa,EAAE;YACfC,eAAe,EAAE;YACjBC,kBAAkB;gBACf,YAAS,IAAI,CAACC,OAAO,GAAC;gBACtB,YAAS,IAAI,CAACA,OAAO,GAAC;aACxB;YACDC,eAAe,EAAE;YACjBC,eAAe,EAAE;QACnB;QACA,KAAK,MAAM1D,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAAS2B,KAAK,EAAEf,EAAEe,KAAK;YACrC,IAAIf,EAAEyD,aAAa,CAACE,MAAM,EAAEvE,SAASqE,aAAa,GAAGzD,EAAEyD,aAAa;YACpE,2FAA2F;YAC3F,IAAIzD,EAAEsD,aAAa,CAACK,MAAM,EAAEvE,SAASkE,aAAa,GAAGtD,EAAEsD,aAAa;QACtE;QACAlE,SAAS2B,KAAK,GAAGb,gBAAgBd,SAAS2B,KAAK;QAC/C,OAAO3B;IACT;IAEA,MAAcwE,mBACZ3B,WAAwB,EACxB4B,WAA2D,EAC3DC,kBAAwD,EACzC;YAGCD,0BACDA,yBACFA;QAJb,MAAME,WAAWD,6BAAAA,qBAAsB;YACrC,GAAGD,WAAW;YACdG,aAAa,AAACH,CAAAA,CAAAA,2BAAAA,+BAAAA,YAAaG,WAAW,YAAxBH,2BAA4B,EAAE,AAAD,EAAGI,GAAG,CAACC,iCAAY;YAC9DC,YAAY,AAACN,CAAAA,CAAAA,0BAAAA,+BAAAA,YAAaM,UAAU,YAAvBN,0BAA2B,EAAE,AAAD,EAAGI,GAAG,CAACC,iCAAY;YAC5DE,UAAU,AAACP,CAAAA,CAAAA,wBAAAA,+BAAAA,YAAaO,QAAQ,YAArBP,wBAAyB,EAAE,AAAD,EAAGI,GAAG,CAACC,iCAAY;QAC1D;QACA,MAAMG,gBAAgB,IAAI,CAAClB,mBAAmB,CAAC,IAAI,CAAC1E,cAAc,CAAC4B,MAAM;QACzE,MAAMiE,oBAAoBlH,IAAAA,UAAI,EAAC,IAAI,CAACL,OAAO,EAAE+E,yBAAc;QAC3D,MAAMyC,8BAA8BnH,IAAAA,UAAI,EACtC,IAAI,CAACL,OAAO,EACZ,UACA,AAAC,KAAEyH,oCAAyB,GAAC;QAE/B,MAAMC,kCAAkCrH,IAAAA,UAAI,EAC1C,IAAI,CAACL,OAAO,EACZ,UACA,AAAC,KAAE2H,8CAAmC,GAAC;QAEzChE,IAAAA,yBAAW,EAAC4D;QACZ5D,IAAAA,yBAAW,EAAC6D;QACZ7D,IAAAA,yBAAW,EAAC+D;QACZ,MAAM9D,IAAAA,4BAAe,EACnB2D,mBACArG,KAAKwC,SAAS,CAAC4D,eAAe,MAAM;QAEtC,MAAM1D,IAAAA,4BAAe,EACnB4D,6BACA,wDAAwD;QACxD,4BAA4B;QAC3B,iCAA8BtG,KAAKwC,SAAS,CAAC4D,iBAAe;QAG/D,MAAMM,uBAAuB1G,KAAKwC,SAAS,CACzCsD,SAASC,WAAW,CAACY,MAAM,CAACC,8DAA0B;QAGxD,MAAMlE,IAAAA,4BAAe,EACnB8D,iCACA,AAAC,gDAA6CxG,KAAKwC,SAAS,CAC1DkE,wBACA;QAGJ,MAAMG,YAAY;eAAI7C,YAAYnF,IAAI,CAACiI,IAAI;SAAG;QAC9C,IAAI9C,YAAY+C,MAAM,CAACC,GAAG,EAAE;YAC1BH,UAAU5B,IAAI,CAAC;QACjB;QACA,IAAIjB,YAAY+C,MAAM,CAACE,KAAK,EAAE;YAC5BJ,UAAU5B,IAAI,CAAC;QACjB;QAEA,MAAMiC,iBAAiBC,IAAAA,sBAAe,EAACN;QACvC,MAAMO,UAA+B;YACnCC,YAAYC,IAAAA,sDAAiC,EAACxB;YAC9C,GAAGjE,OAAO0F,WAAW,CACnBL,eAAelB,GAAG,CAAC,CAACwB,WAAa;oBAC/BA;oBACA;wBAAE,wBAAqBA,CAAAA,aAAa,MAAM,WAAWA,QAAO,IAAE;qBAAK;iBACpE,EACF;YACDC,aAAaP;QACf;QACA,MAAMQ,kBAAkB,AAAC,6BAA0B1H,KAAKwC,SAAS,CAC/D4E,WACA;QACF,MAAM1E,IAAAA,4BAAe,EACnBvD,IAAAA,UAAI,EAAC,IAAI,CAACL,OAAO,EAAE,UAAU,IAAI,CAACyG,OAAO,EAAE,sBAC3CmC;QAEF,MAAMhF,IAAAA,4BAAe,EACnBvD,IAAAA,UAAI,EAAC,IAAI,CAACL,OAAO,EAAE,UAAU,IAAI,CAACyG,OAAO,EAAE,oBAC3CoC,wCAAmB;IAEvB;IAEA,MAAcC,gCAA+C;YAK1CC;QAJjB,MAAMA,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACpH,mBAAmB,CAAC0B,MAAM;QAGjC,MAAM2F,WAAWF,CAAAA,uCAAAA,kCAAAA,mBAAoBG,UAAU,CAAC,IAAI,qBAAnCH,gCAAqCE,QAAQ,KAAI,EAAE;QAEpE,MAAME,+BAA+B9I,IAAAA,UAAI,EACvC,IAAI,CAACL,OAAO,EACZ,UACA,IAAI,CAACyG,OAAO,EACZ,AAAC,KAAE2C,+CAAoC;QAEzCzF,IAAAA,yBAAW,EAACwF;QACZ,MAAMvF,IAAAA,4BAAe,EACnBuF,8BACAjI,KAAKwC,SAAS,CAACuF,UAAU,MAAM;IAEnC;IAEA,MAAcI,6BAA4C;QACxD,MAAMC,wBAAwB,IAAI,CAAClD,mBAAmB,CACpD;YACE,IAAI,CAAC1E,cAAc,CAACuE,GAAG,CAAChE,IAAAA,qBAAW,EAAC,SAAS,UAAU;YACvD,IAAI,CAACP,cAAc,CAACuE,GAAG,CAAChE,IAAAA,qBAAW,EAAC,SAAS,UAAU;SACxD,CAAC4F,MAAM,CAAC0B;QAEX,MAAMC,4BAA4BnJ,IAAAA,UAAI,EACpC,IAAI,CAACL,OAAO,EACZ,AAAC,cAAW+E,yBAAc;QAE5BpB,IAAAA,yBAAW,EAAC6F;QACZ,MAAM5F,IAAAA,4BAAe,EACnB4F,2BACAtI,KAAKwC,SAAS,CAAC4F,uBAAuB,MAAM;IAEhD;IAEA,MAAMG,iBACJjJ,QAAgB,EAChBN,IAA+B,EAChB;QADfA,IAAAA,iBAAAA,OAAwB;QAExB,IAAI,CAACyB,aAAa,CAACK,GAAG,CACpBC,IAAAA,qBAAW,EAAC/B,MAAM,UAAUM,WAC5B,MAAMD,oBACJ,IAAI,CAACP,OAAO,EACZ,AAAC,KAAE0J,6BAAkB,GAAC,SACtBlJ,UACAN;IAGN;IAEQyJ,mBAAmBvH,SAAqC,EAAE;QAChE,MAAMC,WAA6B;YACjC6F,KAAK,CAAC;YACN0B,oBAAoB;YACpB5F,OAAO,CAAC;YACR6F,sBAAsB;QACxB;QACA,KAAK,MAAM5G,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAAS6F,GAAG,EAAEjF,EAAEiF,GAAG;YACjCnF,OAAOC,MAAM,CAACX,SAAS2B,KAAK,EAAEf,EAAEe,KAAK;YAErC3B,SAASuH,kBAAkB,GACzBvH,SAASuH,kBAAkB,IAAI3G,EAAE2G,kBAAkB;YACrDvH,SAASwH,oBAAoB,GAC3BxH,SAASwH,oBAAoB,IAAI5G,EAAE4G,oBAAoB;QAC3D;QACAxH,SAAS6F,GAAG,GAAG/E,gBAAgBd,SAAS6F,GAAG;QAC3C7F,SAAS2B,KAAK,GAAGb,gBAAgBd,SAAS2B,KAAK;QAC/C,OAAO3B;IACT;IAEA,MAAcyH,wBAAuC;QACnD,MAAMC,eAAe,IAAI,CAACJ,kBAAkB,CAAC,IAAI,CAAChI,aAAa,CAAC2B,MAAM;QACtE,MAAMG,OAAOvC,KAAKwC,SAAS,CAACqG,cAAc,MAAM;QAEhD,MAAMC,uBAAuB3J,IAAAA,UAAI,EAC/B,IAAI,CAACL,OAAO,EACZ,UACA,AAAC,KAAE0J,6BAAkB,GAAC;QAExB,MAAMO,qBAAqB5J,IAAAA,UAAI,EAC7B,IAAI,CAACL,OAAO,EACZ,UACA,AAAC,KAAE0J,6BAAkB,GAAC;QAExB/F,IAAAA,yBAAW,EAACqG;QACZrG,IAAAA,yBAAW,EAACsG;QACZ,MAAMrG,IAAAA,4BAAe,EAACoG,sBAAsBvG;QAC5C,MAAMG,IAAAA,4BAAe,EACnBqG,oBACA,AAAC,+BAA4B/I,KAAKwC,SAAS,CAACD;IAEhD;IAEA,MAAMyG,uBACJ1J,QAAgB,EAChBN,IAAwD,EACzC;QACf,IAAI,CAAC0B,mBAAmB,CAACI,GAAG,CAC1BC,IAAAA,qBAAW,EACT/B,SAAS,gBAAgBA,SAAS,oBAAoB,SAASA,MAC/D,UACAM,WAEF,MAAMD,oBACJ,IAAI,CAACP,OAAO,EACZmK,8BAAmB,EACnB3J,UACAN;IAGN;IAEAkK,sBAAsB9I,GAAa,EAAE;QACnC,OAAO,IAAI,CAACM,mBAAmB,CAACqE,GAAG,CAAC3E;IACtC;IAEA+I,yBAAyB/I,GAAa,EAAE;QACtC,OAAO,IAAI,CAACM,mBAAmB,CAACP,MAAM,CAACC;IACzC;IAEQ0H,yBACN5G,SAAgD,EAC5B;QACpB,MAAMC,WAA+B;YACnCiI,SAAS;YACTpB,YAAY,CAAC;YACbqB,kBAAkB,EAAE;YACpBC,WAAW,CAAC;QACd;QACA,IAAIC,kBAAyDC;QAC7D,KAAK,MAAMzH,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,SAASmI,SAAS,EAAEvH,EAAEuH,SAAS;YAC7CzH,OAAOC,MAAM,CAACX,SAAS6G,UAAU,EAAEjG,EAAEiG,UAAU;YAC/C,IAAIjG,EAAEwH,eAAe,EAAE;gBACrBA,kBAAkBxH,EAAEwH,eAAe;YACrC;QACF;QACApI,SAASmI,SAAS,GAAGrH,gBAAgBd,SAASmI,SAAS;QACvDnI,SAAS6G,UAAU,GAAG/F,gBAAgBd,SAAS6G,UAAU;QACzD,MAAMyB,2BAA2B,CAC/BC;gBAIcH;YAFd,OAAO;gBACL,GAAGG,GAAG;gBACNC,OAAO;uBAAKJ,CAAAA,yBAAAA,mCAAAA,gBAAiBI,KAAK,YAAtBJ,yBAA0B,EAAE;uBAAMG,IAAIC,KAAK;iBAAC;YAC1D;QACF;QACA,KAAK,MAAMvJ,OAAOyB,OAAOiF,IAAI,CAAC3F,SAAS6G,UAAU,EAAG;YAClD,MAAM4B,QAAQzI,SAAS6G,UAAU,CAAC5H,IAAI;YACtCe,SAAS6G,UAAU,CAAC5H,IAAI,GAAGqJ,yBAAyBG;QACtD;QACA,KAAK,MAAMxJ,OAAOyB,OAAOiF,IAAI,CAAC3F,SAASmI,SAAS,EAAG;YACjD,MAAMM,QAAQzI,SAASmI,SAAS,CAAClJ,IAAI;YACrCe,SAASmI,SAAS,CAAClJ,IAAI,GAAGqJ,yBAAyBG;QACrD;QACA,KAAK,MAAMF,OAAO7H,OAAOO,MAAM,CAACjB,SAASmI,SAAS,EAAEO,MAAM,CACxDhI,OAAOO,MAAM,CAACjB,SAAS6G,UAAU,GAChC;YACD,KAAK,MAAM8B,WAAWJ,IAAI3B,QAAQ,CAAE;gBAClC,IAAI,CAAC+B,QAAQC,MAAM,EAAE;oBACnBD,QAAQC,MAAM,GAAGC,IAAAA,0BAAY,EAACF,QAAQG,cAAc,EAAE,EAAE,EAAE;wBACxDC,WAAW;wBACXC,WAAW;wBACXC,QAAQ;oBACV,GAAGC,MAAM,CAACC,UAAU,CAAC,OAAO;gBAC9B;YACF;QACF;QACAnJ,SAASkI,gBAAgB,GAAGxH,OAAOiF,IAAI,CAAC3F,SAAS6G,UAAU;QAE3D,OAAO7G;IACT;IAEA,MAAcoJ,0BAAyC;QACrD,MAAM1C,qBAAqB,IAAI,CAACC,wBAAwB,CACtD,IAAI,CAACpH,mBAAmB,CAAC0B,MAAM;QAGjC,8CAA8C;QAC9C,IAAK,MAAMhC,OAAOyH,mBAAmBG,UAAU,CAAE;YAC/CH,mBAAmBG,UAAU,CAAC5H,IAAI,CAAC2H,QAAQ,CAACyC,OAAO,CAAC,CAACV;gBACnD,IAAI,CAACA,QAAQC,MAAM,CAACU,UAAU,CAAC,MAAM;oBACnC,MAAMC,aAAaC,IAAAA,8BAAc,EAACb,QAAQC,MAAM;oBAChD,IAAIW,WAAWzD,KAAK,IAAI,CAACyD,WAAWE,QAAQ,EAAE;wBAC5C,MAAM,qBAA8C,CAA9C,IAAIC,MAAM,AAAC,qBAAkBf,QAAQC,MAAM,GAA3C,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6C;oBACrD;oBACAD,QAAQC,MAAM,GAAGW,WAAWE,QAAQ;gBACtC;YACF;QACF;QAEA,MAAME,yBAAyB3L,IAAAA,UAAI,EACjC,IAAI,CAACL,OAAO,EACZ,UACAmK,8BAAmB;QAErBxG,IAAAA,yBAAW,EAACqI;QACZ,MAAMpI,IAAAA,4BAAe,EACnBoI,wBACA9K,KAAKwC,SAAS,CAACqF,oBAAoB,MAAM;IAE7C;IAEA,MAAMkD,kBAAkBzL,QAAgB,EAAiB;QACvD,IAAI,CAACqB,cAAc,CAACG,GAAG,CACrBC,IAAAA,qBAAW,EAAC,SAAS,UAAUzB,WAC/B,MAAMD,oBAAoB,IAAI,CAACP,OAAO,EAAEkM,yBAAc,EAAE1L;IAE5D;IAEQgE,oBAAoBpC,SAAkC,EAAE;QAC9D,MAAMC,WAA0B,CAAC;QACjC,KAAK,MAAMY,KAAKb,UAAW;YACzBW,OAAOC,MAAM,CAACX,UAAUY;QAC1B;QACA,OAAOE,gBAAgBd;IACzB;IAEA,MAAc8J,qBAAoC;QAChD,MAAMC,gBAAgB,IAAI,CAAC5H,mBAAmB,CAAC,IAAI,CAAC3C,cAAc,CAACyB,MAAM;QACzE,MAAM+I,oBAAoBhM,IAAAA,UAAI,EAAC,IAAI,CAACL,OAAO,EAAE,UAAUkM,yBAAc;QACrEvI,IAAAA,yBAAW,EAAC0I;QACZ,MAAMzI,IAAAA,4BAAe,EACnByI,mBACAnL,KAAKwC,SAAS,CAAC0I,eAAe,MAAM;IAExC;IAEA,MAAME,eAAe,KAQpB,EAAE;QARkB,IAAA,EACnBxF,WAAW,EACXC,kBAAkB,EAClB7B,WAAW,EAKZ,GARoB;QASnB,MAAM,IAAI,CAAC9B,mBAAmB;QAC9B,MAAM,IAAI,CAACa,qBAAqB;QAChC,MAAM,IAAI,CAACK,qBAAqB;QAChC,MAAM,IAAI,CAACuC,kBAAkB,CAAC3B,aAAa4B,aAAaC;QACxD,MAAM,IAAI,CAACsC,0BAA0B;QACrC,MAAM,IAAI,CAACoC,uBAAuB;QAClC,MAAM,IAAI,CAAC3C,6BAA6B;QACxC,MAAM,IAAI,CAACgB,qBAAqB;QAChC,MAAM,IAAI,CAACqC,kBAAkB;QAE7B,IAAII,QAAQC,GAAG,CAACC,eAAe,IAAI,MAAM;YACvC,MAAM,IAAI,CAAC/H,iBAAiB;QAC9B;IACF;IAlmBAgI,YAAY,EACV1M,OAAO,EACPyG,OAAO,EACPjE,aAAa,EAKd,CAAE;aAtBKjB,kBAAiD,IAAI6D;aACrD5D,oBAAqD,IAAI4D;aACzD3D,oBAAkD,IAAI2D;aACtD1D,iBAA+C,IAAI0D;aACnDzD,gBAAiD,IAAIyD;aACrDxD,sBACN,IAAIwD;aACEvD,iBAA6C,IAAIuD;aACjDtD,eAA4C,IAAIsD;QAetD,IAAI,CAACpF,OAAO,GAAGA;QACf,IAAI,CAACyG,OAAO,GAAGA;QACf,IAAI,CAACjE,aAAa,GAAGA;IACvB;AAulBF;AAEA,SAASW,gBAAgBwJ,GAAwB;IAC/C,OAAO5J,OAAOiF,IAAI,CAAC2E,KAChBC,IAAI,GACJC,MAAM,CACL,CAACC,KAAKxL;QACJwL,GAAG,CAACxL,IAAI,GAAGqL,GAAG,CAACrL,IAAI;QACnB,OAAOwL;IACT,GACA,CAAC;AAEP"}