{"version": 3, "sources": ["../../../../src/shared/lib/page-path/remove-page-path-tail.ts"], "sourcesContent": ["import { normalizePathSep } from './normalize-path-sep'\n\n/**\n * Removes the file extension for a page and the trailing `index` if it exists\n * making sure to not return an empty string. The page head is not touched\n * and returned as it is passed. Examples:\n *   - `/foo/bar/baz/index.js` -> `/foo/bar/baz`\n *   - `/foo/bar/baz.js` -> `/foo/bar/baz`\n *\n * @param pagePath A page to a page file (absolute or relative)\n * @param options.extensions Extensions allowed for the page.\n * @param options.keepIndex When true the trailing `index` is _not_ removed.\n */\nexport function removePagePathTail(\n  pagePath: string,\n  options: {\n    extensions: ReadonlyArray<string>\n    keepIndex?: boolean\n  }\n) {\n  pagePath = normalizePathSep(pagePath).replace(\n    new RegExp(`\\\\.+(?:${options.extensions.join('|')})$`),\n    ''\n  )\n\n  if (options.keepIndex !== true) {\n    pagePath = pagePath.replace(/\\/index$/, '') || '/'\n  }\n\n  return pagePath\n}\n"], "names": ["removePagePathTail", "pagePath", "options", "normalizePathSep", "replace", "RegExp", "extensions", "join", "keepIndex"], "mappings": ";;;;+BAagBA;;;eAAAA;;;kCAbiB;AAa1B,SAASA,mBACdC,QAAgB,EAChBC,OAGC;IAEDD,WAAWE,IAAAA,kCAAgB,EAACF,UAAUG,OAAO,CAC3C,IAAIC,OAAO,AAAC,YAASH,QAAQI,UAAU,CAACC,IAAI,CAAC,OAAK,OAClD;IAGF,IAAIL,QAAQM,SAAS,KAAK,MAAM;QAC9BP,WAAWA,SAASG,OAAO,CAAC,YAAY,OAAO;IACjD;IAEA,OAAOH;AACT"}