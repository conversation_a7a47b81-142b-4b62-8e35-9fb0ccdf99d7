{"version": 3, "sources": ["../../../src/shared/lib/constants.ts"], "sourcesContent": ["import MODERN_BROWSERSLIST_TARGET from './modern-browserslist-target'\n\nexport { MODERN_BROWSERSLIST_TARGET }\n\nexport type ValueOf<T> = Required<T>[keyof T]\n\nexport const COMPILER_NAMES = {\n  client: 'client',\n  server: 'server',\n  edgeServer: 'edge-server',\n} as const\n\nexport type CompilerNameValues = ValueOf<typeof COMPILER_NAMES>\n\nexport const COMPILER_INDEXES: {\n  [compilerKey in CompilerNameValues]: number\n} = {\n  [COMPILER_NAMES.client]: 0,\n  [COMPILER_NAMES.server]: 1,\n  [COMPILER_NAMES.edgeServer]: 2,\n} as const\n\nexport const UNDERSCORE_NOT_FOUND_ROUTE = '/_not-found'\nexport const UNDERSCORE_NOT_FOUND_ROUTE_ENTRY = `${UNDERSCORE_NOT_FOUND_ROUTE}/page`\nexport const PHASE_EXPORT = 'phase-export'\nexport const PHASE_PRODUCTION_BUILD = 'phase-production-build'\nexport const PHASE_PRODUCTION_SERVER = 'phase-production-server'\nexport const PHASE_DEVELOPMENT_SERVER = 'phase-development-server'\nexport const PHASE_TEST = 'phase-test'\nexport const PHASE_INFO = 'phase-info'\nexport const PAGES_MANIFEST = 'pages-manifest.json'\nexport const WEBPACK_STATS = 'webpack-stats.json'\nexport const APP_PATHS_MANIFEST = 'app-paths-manifest.json'\nexport const APP_PATH_ROUTES_MANIFEST = 'app-path-routes-manifest.json'\nexport const BUILD_MANIFEST = 'build-manifest.json'\nexport const APP_BUILD_MANIFEST = 'app-build-manifest.json'\nexport const FUNCTIONS_CONFIG_MANIFEST = 'functions-config-manifest.json'\nexport const SUBRESOURCE_INTEGRITY_MANIFEST = 'subresource-integrity-manifest'\nexport const NEXT_FONT_MANIFEST = 'next-font-manifest'\nexport const EXPORT_MARKER = 'export-marker.json'\nexport const EXPORT_DETAIL = 'export-detail.json'\nexport const PRERENDER_MANIFEST = 'prerender-manifest.json'\nexport const ROUTES_MANIFEST = 'routes-manifest.json'\nexport const IMAGES_MANIFEST = 'images-manifest.json'\nexport const SERVER_FILES_MANIFEST = 'required-server-files.json'\nexport const DEV_CLIENT_PAGES_MANIFEST = '_devPagesManifest.json'\nexport const MIDDLEWARE_MANIFEST = 'middleware-manifest.json'\nexport const TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST =\n  '_clientMiddlewareManifest.json'\nexport const DEV_CLIENT_MIDDLEWARE_MANIFEST = '_devMiddlewareManifest.json'\nexport const REACT_LOADABLE_MANIFEST = 'react-loadable-manifest.json'\nexport const SERVER_DIRECTORY = 'server'\nexport const CONFIG_FILES = [\n  'next.config.js',\n  'next.config.mjs',\n  'next.config.ts',\n]\nexport const BUILD_ID_FILE = 'BUILD_ID'\nexport const BLOCKED_PAGES = ['/_document', '/_app', '/_error']\nexport const CLIENT_PUBLIC_FILES_PATH = 'public'\nexport const CLIENT_STATIC_FILES_PATH = 'static'\nexport const STRING_LITERAL_DROP_BUNDLE = '__NEXT_DROP_CLIENT_FILE__'\nexport const NEXT_BUILTIN_DOCUMENT = '__NEXT_BUILTIN_DOCUMENT__'\nexport const BARREL_OPTIMIZATION_PREFIX = '__barrel_optimize__'\n\n// server/[entry]/page_client-reference-manifest.js\nexport const CLIENT_REFERENCE_MANIFEST = 'client-reference-manifest'\n// server/server-reference-manifest\nexport const SERVER_REFERENCE_MANIFEST = 'server-reference-manifest'\n// server/middleware-build-manifest.js\nexport const MIDDLEWARE_BUILD_MANIFEST = 'middleware-build-manifest'\n// server/middleware-react-loadable-manifest.js\nexport const MIDDLEWARE_REACT_LOADABLE_MANIFEST =\n  'middleware-react-loadable-manifest'\n// server/interception-route-rewrite-manifest.js\nexport const INTERCEPTION_ROUTE_REWRITE_MANIFEST =\n  'interception-route-rewrite-manifest'\n// server/dynamic-css-manifest.js\nexport const DYNAMIC_CSS_MANIFEST = 'dynamic-css-manifest'\n\n// static/runtime/main.js\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN = `main`\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN_APP = `${CLIENT_STATIC_FILES_RUNTIME_MAIN}-app`\n// next internal client components chunk for layouts\nexport const APP_CLIENT_INTERNALS = 'app-pages-internals'\n// static/runtime/react-refresh.js\nexport const CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH = `react-refresh`\n// static/runtime/amp.js\nexport const CLIENT_STATIC_FILES_RUNTIME_AMP = `amp`\n// static/runtime/webpack.js\nexport const CLIENT_STATIC_FILES_RUNTIME_WEBPACK = `webpack`\n// static/runtime/polyfills.js\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS = 'polyfills'\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL = Symbol(\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS\n)\nexport const DEFAULT_RUNTIME_WEBPACK = 'webpack-runtime'\nexport const EDGE_RUNTIME_WEBPACK = 'edge-runtime-webpack'\nexport const STATIC_PROPS_ID = '__N_SSG'\nexport const SERVER_PROPS_ID = '__N_SSP'\nexport const DEFAULT_SERIF_FONT = {\n  name: 'Times New Roman',\n  xAvgCharWidth: 821,\n  azAvgWidth: 854.3953488372093,\n  unitsPerEm: 2048,\n}\nexport const DEFAULT_SANS_SERIF_FONT = {\n  name: 'Arial',\n  xAvgCharWidth: 904,\n  azAvgWidth: 934.5116279069767,\n  unitsPerEm: 2048,\n}\nexport const STATIC_STATUS_PAGES = ['/500']\nexport const TRACE_OUTPUT_VERSION = 1\n// in `MB`\nexport const TURBO_TRACE_DEFAULT_MEMORY_LIMIT = 6000\n\nexport const RSC_MODULE_TYPES = {\n  client: 'client',\n  server: 'server',\n} as const\n\n// comparing\n// https://nextjs.org/docs/api-reference/edge-runtime\n// with\n// https://nodejs.org/docs/latest/api/globals.html\nexport const EDGE_UNSUPPORTED_NODE_APIS = [\n  'clearImmediate',\n  'setImmediate',\n  'BroadcastChannel',\n  'ByteLengthQueuingStrategy',\n  'CompressionStream',\n  'CountQueuingStrategy',\n  'DecompressionStream',\n  'DomException',\n  'MessageChannel',\n  'MessageEvent',\n  'MessagePort',\n  'ReadableByteStreamController',\n  'ReadableStreamBYOBRequest',\n  'ReadableStreamDefaultController',\n  'TransformStreamDefaultController',\n  'WritableStreamDefaultController',\n]\n\nexport const SYSTEM_ENTRYPOINTS = new Set<string>([\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n])\n"], "names": ["APP_BUILD_MANIFEST", "APP_CLIENT_INTERNALS", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "BARREL_OPTIMIZATION_PREFIX", "BLOCKED_PAGES", "BUILD_ID_FILE", "BUILD_MANIFEST", "CLIENT_PUBLIC_FILES_PATH", "CLIENT_REFERENCE_MANIFEST", "CLIENT_STATIC_FILES_PATH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "COMPILER_INDEXES", "COMPILER_NAMES", "CONFIG_FILES", "DEFAULT_RUNTIME_WEBPACK", "DEFAULT_SANS_SERIF_FONT", "DEFAULT_SERIF_FONT", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "DEV_CLIENT_PAGES_MANIFEST", "DYNAMIC_CSS_MANIFEST", "EDGE_RUNTIME_WEBPACK", "EDGE_UNSUPPORTED_NODE_APIS", "EXPORT_DETAIL", "EXPORT_MARKER", "FUNCTIONS_CONFIG_MANIFEST", "IMAGES_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "MODERN_BROWSERSLIST_TARGET", "NEXT_BUILTIN_DOCUMENT", "NEXT_FONT_MANIFEST", "PAGES_MANIFEST", "PHASE_DEVELOPMENT_SERVER", "PHASE_EXPORT", "PHASE_INFO", "PHASE_PRODUCTION_BUILD", "PHASE_PRODUCTION_SERVER", "PHASE_TEST", "PRERENDER_MANIFEST", "REACT_LOADABLE_MANIFEST", "ROUTES_MANIFEST", "RSC_MODULE_TYPES", "SERVER_DIRECTORY", "SERVER_FILES_MANIFEST", "SERVER_PROPS_ID", "SERVER_REFERENCE_MANIFEST", "STATIC_PROPS_ID", "STATIC_STATUS_PAGES", "STRING_LITERAL_DROP_BUNDLE", "SUBRESOURCE_INTEGRITY_MANIFEST", "SYSTEM_ENTRYPOINTS", "TRACE_OUTPUT_VERSION", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "TURBO_TRACE_DEFAULT_MEMORY_LIMIT", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "WEBPACK_STATS", "client", "server", "edgeServer", "Symbol", "name", "xAvgCharWidth", "azAvgWidth", "unitsPerEm", "Set"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmCaA,kBAAkB;eAAlBA;;IAiDAC,oBAAoB;eAApBA;;IApDAC,kBAAkB;eAAlBA;;IACAC,wBAAwB;eAAxBA;;IA8BAC,0BAA0B;eAA1BA;;IALAC,aAAa;eAAbA;;IADAC,aAAa;eAAbA;;IAvBAC,cAAc;eAAdA;;IAyBAC,wBAAwB;eAAxBA;;IAOAC,yBAAyB;eAAzBA;;IANAC,wBAAwB;eAAxBA;;IA4BAC,+BAA+B;eAA/BA;;IAPAC,gCAAgC;eAAhCA;;IACAC,oCAAoC;eAApCA;;IAUAC,qCAAqC;eAArCA;;IACAC,4CAA4C;eAA5CA;;IAPAC,yCAAyC;eAAzCA;;IAIAC,mCAAmC;eAAnCA;;IA5EAC,gBAAgB;eAAhBA;;IARAC,cAAc;eAAdA;;IA8CAC,YAAY;eAAZA;;IA4CAC,uBAAuB;eAAvBA;;IAUAC,uBAAuB;eAAvBA;;IANAC,kBAAkB;eAAlBA;;IAnDAC,8BAA8B;eAA9BA;;IAJAC,yBAAyB;eAAzBA;;IAiCAC,oBAAoB;eAApBA;;IAmBAC,oBAAoB;eAApBA;;IA6BAC,0BAA0B;eAA1BA;;IAtFAC,aAAa;eAAbA;;IADAC,aAAa;eAAbA;;IAHAC,yBAAyB;eAAzBA;;IAOAC,eAAe;eAAfA;;IAgCAC,mCAAmC;eAAnCA;;IALAC,yBAAyB;eAAzBA;;IAxBAC,mBAAmB;eAAnBA;;IA0BAC,kCAAkC;eAAlCA;;IAtEJC,0BAA0B;eAA1BA,iCAA0B;;IA4DtBC,qBAAqB;eAArBA;;IAxBAC,kBAAkB;eAAlBA;;IARAC,cAAc;eAAdA;;IAHAC,wBAAwB;eAAxBA;;IAHAC,YAAY;eAAZA;;IAKAC,UAAU;eAAVA;;IAJAC,sBAAsB;eAAtBA;;IACAC,uBAAuB;eAAvBA;;IAEAC,UAAU;eAAVA;;IAaAC,kBAAkB;eAAlBA;;IASAC,uBAAuB;eAAvBA;;IARAC,eAAe;eAAfA;;IA2EAC,gBAAgB;eAAhBA;;IAlEAC,gBAAgB;eAAhBA;;IAPAC,qBAAqB;eAArBA;;IAuDAC,eAAe;eAAfA;;IA/BAC,yBAAyB;eAAzBA;;IA8BAC,eAAe;eAAfA;;IAcAC,mBAAmB;eAAnBA;;IAnDAC,0BAA0B;eAA1BA;;IAxBAC,8BAA8B;eAA9BA;;IA4GAC,kBAAkB;eAAlBA;;IAhCAC,oBAAoB;eAApBA;;IAlEAC,oCAAoC;eAApCA;;IAoEAC,gCAAgC;eAAhCA;;IA7FAC,0BAA0B;eAA1BA;;IACAC,gCAAgC;eAAhCA;;IAQAC,aAAa;eAAbA;;;;mFA/B0B;AAMhC,MAAM9C,iBAAiB;IAC5B+C,QAAQ;IACRC,QAAQ;IACRC,YAAY;AACd;AAIO,MAAMlD,mBAET;IACF,CAACC,eAAe+C,MAAM,CAAC,EAAE;IACzB,CAAC/C,eAAegD,MAAM,CAAC,EAAE;IACzB,CAAChD,eAAeiD,UAAU,CAAC,EAAE;AAC/B;AAEO,MAAML,6BAA6B;AACnC,MAAMC,mCAAmC,AAAC,KAAED,6BAA2B;AACvE,MAAMrB,eAAe;AACrB,MAAME,yBAAyB;AAC/B,MAAMC,0BAA0B;AAChC,MAAMJ,2BAA2B;AACjC,MAAMK,aAAa;AACnB,MAAMH,aAAa;AACnB,MAAMH,iBAAiB;AACvB,MAAMyB,gBAAgB;AACtB,MAAM/D,qBAAqB;AAC3B,MAAMC,2BAA2B;AACjC,MAAMI,iBAAiB;AACvB,MAAMP,qBAAqB;AAC3B,MAAM+B,4BAA4B;AAClC,MAAM2B,iCAAiC;AACvC,MAAMnB,qBAAqB;AAC3B,MAAMT,gBAAgB;AACtB,MAAMD,gBAAgB;AACtB,MAAMkB,qBAAqB;AAC3B,MAAME,kBAAkB;AACxB,MAAMjB,kBAAkB;AACxB,MAAMoB,wBAAwB;AAC9B,MAAM3B,4BAA4B;AAClC,MAAMU,sBAAsB;AAC5B,MAAM0B,uCACX;AACK,MAAMrC,iCAAiC;AACvC,MAAMwB,0BAA0B;AAChC,MAAMG,mBAAmB;AACzB,MAAM/B,eAAe;IAC1B;IACA;IACA;CACD;AACM,MAAMd,gBAAgB;AACtB,MAAMD,gBAAgB;IAAC;IAAc;IAAS;CAAU;AACxD,MAAMG,2BAA2B;AACjC,MAAME,2BAA2B;AACjC,MAAM+C,6BAA6B;AACnC,MAAMnB,wBAAwB;AAC9B,MAAMlC,6BAA6B;AAGnC,MAAMK,4BAA4B;AAElC,MAAM6C,4BAA4B;AAElC,MAAMpB,4BAA4B;AAElC,MAAME,qCACX;AAEK,MAAMH,sCACX;AAEK,MAAMP,uBAAuB;AAG7B,MAAMd,mCAAoC;AAC1C,MAAMC,uCAAuC,AAAC,KAAED,mCAAiC;AAEjF,MAAMX,uBAAuB;AAE7B,MAAMe,4CAA6C;AAEnD,MAAML,kCAAmC;AAEzC,MAAMM,sCAAuC;AAE7C,MAAMH,wCAAwC;AAC9C,MAAMC,+CAA+CsD,OAC1DvD;AAEK,MAAMO,0BAA0B;AAChC,MAAMM,uBAAuB;AAC7B,MAAM4B,kBAAkB;AACxB,MAAMF,kBAAkB;AACxB,MAAM9B,qBAAqB;IAChC+C,MAAM;IACNC,eAAe;IACfC,YAAY;IACZC,YAAY;AACd;AACO,MAAMnD,0BAA0B;IACrCgD,MAAM;IACNC,eAAe;IACfC,YAAY;IACZC,YAAY;AACd;AACO,MAAMjB,sBAAsB;IAAC;CAAO;AACpC,MAAMI,uBAAuB;AAE7B,MAAME,mCAAmC;AAEzC,MAAMZ,mBAAmB;IAC9BgB,QAAQ;IACRC,QAAQ;AACV;AAMO,MAAMvC,6BAA6B;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM+B,qBAAqB,IAAIe,IAAY;IAChD9D;IACAI;IACAL;IACAE;CACD"}