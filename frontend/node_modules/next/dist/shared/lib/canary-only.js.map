{"version": 3, "sources": ["../../../src/shared/lib/canary-only.ts"], "sourcesContent": ["export function isStableBuild() {\n  return (\n    !process.env.__NEXT_VERSION?.includes('canary') &&\n    !process.env.__NEXT_TEST_MODE &&\n    !process.env.NEXT_PRIVATE_LOCAL_DEV\n  )\n}\n\nexport class CanaryOnlyError extends Error {\n  constructor(arg: { feature: string } | string) {\n    if (typeof arg === 'object' && 'feature' in arg) {\n      super(\n        `The experimental feature \"${arg.feature}\" can only be enabled when using the latest canary version of Next.js.`\n      )\n    } else {\n      super(arg)\n    }\n\n    // This error is meant to interrupt the server start/build process\n    // but the stack trace isn't meaningful, as it points to internal code.\n    this.stack = undefined\n  }\n}\n"], "names": ["CanaryOnlyError", "isStableBuild", "process", "env", "__NEXT_VERSION", "includes", "__NEXT_TEST_MODE", "NEXT_PRIVATE_LOCAL_DEV", "Error", "constructor", "arg", "feature", "stack", "undefined"], "mappings": ";;;;;;;;;;;;;;;IAQaA,eAAe;eAAfA;;IARGC,aAAa;eAAbA;;;AAAT,SAASA;QAEXC;IADH,OACE,GAACA,8BAAAA,QAAQC,GAAG,CAACC,cAAc,qBAA1BF,4BAA4BG,QAAQ,CAAC,cACtC,CAACH,QAAQC,GAAG,CAACG,gBAAgB,IAC7B,CAACJ,QAAQC,GAAG,CAACI,sBAAsB;AAEvC;AAEO,MAAMP,wBAAwBQ;IACnCC,YAAYC,GAAiC,CAAE;QAC7C,IAAI,OAAOA,QAAQ,YAAY,aAAaA,KAAK;YAC/C,KAAK,CACH,AAAC,+BAA4BA,IAAIC,OAAO,GAAC;QAE7C,OAAO;YACL,KAAK,CAACD;QACR;QAEA,kEAAkE;QAClE,uEAAuE;QACvE,IAAI,CAACE,KAAK,GAAGC;IACf;AACF"}