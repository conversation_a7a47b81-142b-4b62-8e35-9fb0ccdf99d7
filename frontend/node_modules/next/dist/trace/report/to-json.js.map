{"version": 3, "sources": ["../../../src/trace/report/to-json.ts"], "sourcesContent": ["import { traceGlobals, traceId } from '../shared'\nimport fs from 'fs'\nimport path from 'path'\nimport { PHASE_DEVELOPMENT_SERVER } from '../../shared/lib/constants'\nimport type { TraceEvent } from '../types'\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst localEndpoint = {\n  serviceName: 'nextjs',\n  ipv4: '127.0.0.1',\n  port: 9411,\n}\n\ntype Event = TraceEvent & {\n  localEndpoint?: typeof localEndpoint\n}\n\n// Batch events as zipkin allows for multiple events to be sent in one go\nexport function batcher(reportEvents: (evts: Event[]) => Promise<void>) {\n  const events: Event[] = []\n  // Promise queue to ensure events are always sent on flushAll\n  const queue = new Set()\n  return {\n    flushAll: async () => {\n      await Promise.all(queue)\n      if (events.length > 0) {\n        await reportEvents(events)\n        events.length = 0\n      }\n    },\n    report: (event: Event) => {\n      events.push(event)\n\n      if (events.length > 100) {\n        const evts = events.slice()\n        events.length = 0\n        const report = reportEvents(evts)\n        queue.add(report)\n        report.then(() => queue.delete(report))\n      }\n    },\n  }\n}\n\nlet writeStream: RotatingWriteStream\nlet batch: ReturnType<typeof batcher> | undefined\n\nconst writeStreamOptions = {\n  flags: 'a',\n  encoding: 'utf8' as const,\n}\nclass RotatingWriteStream {\n  file: string\n  writeStream!: fs.WriteStream\n  size: number\n  sizeLimit: number\n  private rotatePromise: Promise<void> | undefined\n  private drainPromise: Promise<void> | undefined\n  constructor(file: string, sizeLimit: number) {\n    this.file = file\n    this.size = 0\n    this.sizeLimit = sizeLimit\n    this.createWriteStream()\n  }\n  private createWriteStream() {\n    this.writeStream = fs.createWriteStream(this.file, writeStreamOptions)\n  }\n  // Recreate the file\n  private async rotate() {\n    await this.end()\n    try {\n      fs.unlinkSync(this.file)\n    } catch (err: any) {\n      // It's fine if the file does not exist yet\n      if (err.code !== 'ENOENT') {\n        throw err\n      }\n    }\n    this.size = 0\n    this.createWriteStream()\n    this.rotatePromise = undefined\n  }\n  async write(data: string): Promise<void> {\n    if (this.rotatePromise) await this.rotatePromise\n\n    this.size += data.length\n    if (this.size > this.sizeLimit) {\n      await (this.rotatePromise = this.rotate())\n    }\n\n    if (!this.writeStream.write(data, 'utf8')) {\n      if (this.drainPromise === undefined) {\n        this.drainPromise = new Promise<void>((resolve, _reject) => {\n          this.writeStream.once('drain', () => {\n            this.drainPromise = undefined\n            resolve()\n          })\n        })\n      }\n      await this.drainPromise\n    }\n  }\n\n  end(): Promise<void> {\n    return new Promise((resolve) => {\n      this.writeStream.end(resolve)\n    })\n  }\n}\n\nconst reportToLocalHost = (event: TraceEvent) => {\n  const distDir = traceGlobals.get('distDir')\n  const phase = traceGlobals.get('phase')\n  if (!distDir || !phase) {\n    return\n  }\n\n  if (!batch) {\n    batch = batcher(async (events: Event[]) => {\n      if (!writeStream) {\n        await fs.promises.mkdir(distDir, { recursive: true })\n        const file = path.join(distDir, 'trace')\n        writeStream = new RotatingWriteStream(\n          file,\n          // Development is limited to 50MB, production is unlimited\n          phase === PHASE_DEVELOPMENT_SERVER ? 52428800 : Infinity\n        )\n      }\n      const eventsJson = JSON.stringify(events)\n      try {\n        await writeStream.write(eventsJson + '\\n')\n      } catch (err) {\n        console.log(err)\n      }\n    })\n  }\n\n  batch.report({\n    ...event,\n    traceId,\n  })\n}\n\nexport default {\n  flushAll: (opts?: { end: boolean }) =>\n    batch\n      ? batch.flushAll().then(() => {\n          const phase = traceGlobals.get('phase')\n          // Only end writeStream when manually flushing in production\n          if (opts?.end || phase !== PHASE_DEVELOPMENT_SERVER) {\n            return writeStream.end()\n          }\n        })\n      : undefined,\n  report: reportToLocalHost,\n}\n"], "names": ["batcher", "localEndpoint", "serviceName", "ipv4", "port", "reportEvents", "events", "queue", "Set", "flushAll", "Promise", "all", "length", "report", "event", "push", "evts", "slice", "add", "then", "delete", "writeStream", "batch", "writeStreamOptions", "flags", "encoding", "RotatingWriteStream", "constructor", "file", "sizeLimit", "size", "createWriteStream", "fs", "rotate", "end", "unlinkSync", "err", "code", "rotatePromise", "undefined", "write", "data", "drainPromise", "resolve", "_reject", "once", "reportToLocalHost", "distDir", "traceGlobals", "get", "phase", "promises", "mkdir", "recursive", "path", "join", "PHASE_DEVELOPMENT_SERVER", "Infinity", "eventsJson", "JSON", "stringify", "console", "log", "traceId", "opts"], "mappings": ";;;;;;;;;;;;;;;IAkBgBA,OAAO;eAAPA;;IA6HhB,OAYC;eAZD;;;wBA/IsC;2DACvB;6DACE;2BACwB;;;;;;AAGzC,6DAA6D;AAC7D,MAAMC,gBAAgB;IACpBC,aAAa;IACbC,MAAM;IACNC,MAAM;AACR;AAOO,SAASJ,QAAQK,YAA8C;IACpE,MAAMC,SAAkB,EAAE;IAC1B,6DAA6D;IAC7D,MAAMC,QAAQ,IAAIC;IAClB,OAAO;QACLC,UAAU;YACR,MAAMC,QAAQC,GAAG,CAACJ;YAClB,IAAID,OAAOM,MAAM,GAAG,GAAG;gBACrB,MAAMP,aAAaC;gBACnBA,OAAOM,MAAM,GAAG;YAClB;QACF;QACAC,QAAQ,CAACC;YACPR,OAAOS,IAAI,CAACD;YAEZ,IAAIR,OAAOM,MAAM,GAAG,KAAK;gBACvB,MAAMI,OAAOV,OAAOW,KAAK;gBACzBX,OAAOM,MAAM,GAAG;gBAChB,MAAMC,SAASR,aAAaW;gBAC5BT,MAAMW,GAAG,CAACL;gBACVA,OAAOM,IAAI,CAAC,IAAMZ,MAAMa,MAAM,CAACP;YACjC;QACF;IACF;AACF;AAEA,IAAIQ;AACJ,IAAIC;AAEJ,MAAMC,qBAAqB;IACzBC,OAAO;IACPC,UAAU;AACZ;AACA,MAAMC;IAOJC,YAAYC,IAAY,EAAEC,SAAiB,CAAE;QAC3C,IAAI,CAACD,IAAI,GAAGA;QACZ,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACD,SAAS,GAAGA;QACjB,IAAI,CAACE,iBAAiB;IACxB;IACQA,oBAAoB;QAC1B,IAAI,CAACV,WAAW,GAAGW,WAAE,CAACD,iBAAiB,CAAC,IAAI,CAACH,IAAI,EAAEL;IACrD;IACA,oBAAoB;IACpB,MAAcU,SAAS;QACrB,MAAM,IAAI,CAACC,GAAG;QACd,IAAI;YACFF,WAAE,CAACG,UAAU,CAAC,IAAI,CAACP,IAAI;QACzB,EAAE,OAAOQ,KAAU;YACjB,2CAA2C;YAC3C,IAAIA,IAAIC,IAAI,KAAK,UAAU;gBACzB,MAAMD;YACR;QACF;QACA,IAAI,CAACN,IAAI,GAAG;QACZ,IAAI,CAACC,iBAAiB;QACtB,IAAI,CAACO,aAAa,GAAGC;IACvB;IACA,MAAMC,MAAMC,IAAY,EAAiB;QACvC,IAAI,IAAI,CAACH,aAAa,EAAE,MAAM,IAAI,CAACA,aAAa;QAEhD,IAAI,CAACR,IAAI,IAAIW,KAAK7B,MAAM;QACxB,IAAI,IAAI,CAACkB,IAAI,GAAG,IAAI,CAACD,SAAS,EAAE;YAC9B,MAAO,CAAA,IAAI,CAACS,aAAa,GAAG,IAAI,CAACL,MAAM,EAAC;QAC1C;QAEA,IAAI,CAAC,IAAI,CAACZ,WAAW,CAACmB,KAAK,CAACC,MAAM,SAAS;YACzC,IAAI,IAAI,CAACC,YAAY,KAAKH,WAAW;gBACnC,IAAI,CAACG,YAAY,GAAG,IAAIhC,QAAc,CAACiC,SAASC;oBAC9C,IAAI,CAACvB,WAAW,CAACwB,IAAI,CAAC,SAAS;wBAC7B,IAAI,CAACH,YAAY,GAAGH;wBACpBI;oBACF;gBACF;YACF;YACA,MAAM,IAAI,CAACD,YAAY;QACzB;IACF;IAEAR,MAAqB;QACnB,OAAO,IAAIxB,QAAQ,CAACiC;YAClB,IAAI,CAACtB,WAAW,CAACa,GAAG,CAACS;QACvB;IACF;AACF;AAEA,MAAMG,oBAAoB,CAAChC;IACzB,MAAMiC,UAAUC,oBAAY,CAACC,GAAG,CAAC;IACjC,MAAMC,QAAQF,oBAAY,CAACC,GAAG,CAAC;IAC/B,IAAI,CAACF,WAAW,CAACG,OAAO;QACtB;IACF;IAEA,IAAI,CAAC5B,OAAO;QACVA,QAAQtB,QAAQ,OAAOM;YACrB,IAAI,CAACe,aAAa;gBAChB,MAAMW,WAAE,CAACmB,QAAQ,CAACC,KAAK,CAACL,SAAS;oBAAEM,WAAW;gBAAK;gBACnD,MAAMzB,OAAO0B,aAAI,CAACC,IAAI,CAACR,SAAS;gBAChC1B,cAAc,IAAIK,oBAChBE,MACA,0DAA0D;gBAC1DsB,UAAUM,mCAAwB,GAAG,WAAWC;YAEpD;YACA,MAAMC,aAAaC,KAAKC,SAAS,CAACtD;YAClC,IAAI;gBACF,MAAMe,YAAYmB,KAAK,CAACkB,aAAa;YACvC,EAAE,OAAOtB,KAAK;gBACZyB,QAAQC,GAAG,CAAC1B;YACd;QACF;IACF;IAEAd,MAAMT,MAAM,CAAC;QACX,GAAGC,KAAK;QACRiD,SAAAA,eAAO;IACT;AACF;MAEA,WAAe;IACbtD,UAAU,CAACuD,OACT1C,QACIA,MAAMb,QAAQ,GAAGU,IAAI,CAAC;YACpB,MAAM+B,QAAQF,oBAAY,CAACC,GAAG,CAAC;YAC/B,4DAA4D;YAC5D,IAAIe,CAAAA,wBAAAA,KAAM9B,GAAG,KAAIgB,UAAUM,mCAAwB,EAAE;gBACnD,OAAOnC,YAAYa,GAAG;YACxB;QACF,KACAK;IACN1B,QAAQiC;AACV"}