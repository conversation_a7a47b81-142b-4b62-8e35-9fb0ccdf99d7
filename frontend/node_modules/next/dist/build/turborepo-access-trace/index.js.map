{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/index.ts"], "sourcesContent": ["export type { SerializableTurborepoAccessTraceResult } from './types'\nexport {\n  turborepoTraceAccess,\n  writeTurborepoAccessTraceResult,\n} from './helpers'\nexport { TurborepoAccessTraceResult } from './result'\n"], "names": ["TurborepoAccessTraceResult", "turborepoTraceAccess", "writeTurborepoAccessTraceResult"], "mappings": ";;;;;;;;;;;;;;;;IAKSA,0BAA0B;eAA1BA,kCAA0B;;IAHjCC,oBAAoB;eAApBA,6BAAoB;;IACpBC,+BAA+B;eAA/BA,wCAA+B;;;yBAC1B;wBACoC"}