{"version": 3, "sources": ["../../../src/build/output/log.ts"], "sourcesContent": ["import { bold, green, magenta, red, yellow, white } from '../../lib/picocolors'\nimport { LRUCache } from '../../server/lib/lru-cache'\n\nexport const prefixes = {\n  wait: white(bold('○')),\n  error: red(bold('⨯')),\n  warn: yellow(bold('⚠')),\n  ready: '▲', // no color\n  info: white(bold(' ')),\n  event: green(bold('✓')),\n  trace: magenta(bold('»')),\n} as const\n\nconst LOGGING_METHOD = {\n  log: 'log',\n  warn: 'warn',\n  error: 'error',\n} as const\n\nfunction prefixedLog(prefixType: keyof typeof prefixes, ...message: any[]) {\n  if ((message[0] === '' || message[0] === undefined) && message.length === 1) {\n    message.shift()\n  }\n\n  const consoleMethod: keyof typeof LOGGING_METHOD =\n    prefixType in LOGGING_METHOD\n      ? LOGGING_METHOD[prefixType as keyof typeof LOGGING_METHOD]\n      : 'log'\n\n  const prefix = prefixes[prefixType]\n  // If there's no message, don't print the prefix but a new line\n  if (message.length === 0) {\n    console[consoleMethod]('')\n  } else {\n    // Ensure if there's ANSI escape codes it's concatenated into one string.\n    // Chrome DevTool can only handle color if it's in one string.\n    if (message.length === 1 && typeof message[0] === 'string') {\n      console[consoleMethod](' ' + prefix + ' ' + message[0])\n    } else {\n      console[consoleMethod](' ' + prefix, ...message)\n    }\n  }\n}\n\nexport function bootstrap(...message: string[]) {\n  // logging format: ' <prefix> <message>'\n  // e.g. ' ✓ Compiled successfully'\n  // Add spaces to align with the indent of other logs\n  console.log('   ' + message.join(' '))\n}\n\nexport function wait(...message: any[]) {\n  prefixedLog('wait', ...message)\n}\n\nexport function error(...message: any[]) {\n  prefixedLog('error', ...message)\n}\n\nexport function warn(...message: any[]) {\n  prefixedLog('warn', ...message)\n}\n\nexport function ready(...message: any[]) {\n  prefixedLog('ready', ...message)\n}\n\nexport function info(...message: any[]) {\n  prefixedLog('info', ...message)\n}\n\nexport function event(...message: any[]) {\n  prefixedLog('event', ...message)\n}\n\nexport function trace(...message: any[]) {\n  prefixedLog('trace', ...message)\n}\n\nconst warnOnceCache = new LRUCache<string>(10_000, (value) => value.length)\nexport function warnOnce(...message: any[]) {\n  const key = message.join(' ')\n  if (!warnOnceCache.has(key)) {\n    warnOnceCache.set(key, key)\n    warn(...message)\n  }\n}\n"], "names": ["bootstrap", "error", "event", "info", "prefixes", "ready", "trace", "wait", "warn", "warnOnce", "white", "bold", "red", "yellow", "green", "magenta", "LOGGING_METHOD", "log", "prefixedLog", "prefixType", "message", "undefined", "length", "shift", "consoleMethod", "prefix", "console", "join", "warnOnceCache", "L<PERSON><PERSON><PERSON>", "value", "key", "has", "set"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IA4CgBA,SAAS;eAATA;;IAWAC,KAAK;eAALA;;IAgBAC,KAAK;eAALA;;IAJAC,IAAI;eAAJA;;IAhEHC,QAAQ;eAARA;;IA4DGC,KAAK;eAALA;;IAYAC,KAAK;eAALA;;IAxBAC,IAAI;eAAJA;;IAQAC,IAAI;eAAJA;;IAqBAC,QAAQ;eAARA;;;4BAhFyC;0BAChC;AAElB,MAAML,WAAW;IACtBG,MAAMG,IAAAA,iBAAK,EAACC,IAAAA,gBAAI,EAAC;IACjBV,OAAOW,IAAAA,eAAG,EAACD,IAAAA,gBAAI,EAAC;IAChBH,MAAMK,IAAAA,kBAAM,EAACF,IAAAA,gBAAI,EAAC;IAClBN,OAAO;IACPF,MAAMO,IAAAA,iBAAK,EAACC,IAAAA,gBAAI,EAAC;IACjBT,OAAOY,IAAAA,iBAAK,EAACH,IAAAA,gBAAI,EAAC;IAClBL,OAAOS,IAAAA,mBAAO,EAACJ,IAAAA,gBAAI,EAAC;AACtB;AAEA,MAAMK,iBAAiB;IACrBC,KAAK;IACLT,MAAM;IACNP,OAAO;AACT;AAEA,SAASiB,YAAYC,UAAiC,EAAE,GAAGC,OAAc;IACvE,IAAI,AAACA,CAAAA,OAAO,CAAC,EAAE,KAAK,MAAMA,OAAO,CAAC,EAAE,KAAKC,SAAQ,KAAMD,QAAQE,MAAM,KAAK,GAAG;QAC3EF,QAAQG,KAAK;IACf;IAEA,MAAMC,gBACJL,cAAcH,iBACVA,cAAc,CAACG,WAA0C,GACzD;IAEN,MAAMM,SAASrB,QAAQ,CAACe,WAAW;IACnC,+DAA+D;IAC/D,IAAIC,QAAQE,MAAM,KAAK,GAAG;QACxBI,OAAO,CAACF,cAAc,CAAC;IACzB,OAAO;QACL,yEAAyE;QACzE,8DAA8D;QAC9D,IAAIJ,QAAQE,MAAM,KAAK,KAAK,OAAOF,OAAO,CAAC,EAAE,KAAK,UAAU;YAC1DM,OAAO,CAACF,cAAc,CAAC,MAAMC,SAAS,MAAML,OAAO,CAAC,EAAE;QACxD,OAAO;YACLM,OAAO,CAACF,cAAc,CAAC,MAAMC,WAAWL;QAC1C;IACF;AACF;AAEO,SAASpB,UAAU,GAAGoB,OAAiB;IAC5C,wCAAwC;IACxC,kCAAkC;IAClC,oDAAoD;IACpDM,QAAQT,GAAG,CAAC,QAAQG,QAAQO,IAAI,CAAC;AACnC;AAEO,SAASpB,KAAK,GAAGa,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASnB,MAAM,GAAGmB,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAASZ,KAAK,GAAGY,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASf,MAAM,GAAGe,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAASjB,KAAK,GAAGiB,OAAc;IACpCF,YAAY,WAAWE;AACzB;AAEO,SAASlB,MAAM,GAAGkB,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEO,SAASd,MAAM,GAAGc,OAAc;IACrCF,YAAY,YAAYE;AAC1B;AAEA,MAAMQ,gBAAgB,IAAIC,kBAAQ,CAAS,OAAQ,CAACC,QAAUA,MAAMR,MAAM;AACnE,SAASb,SAAS,GAAGW,OAAc;IACxC,MAAMW,MAAMX,QAAQO,IAAI,CAAC;IACzB,IAAI,CAACC,cAAcI,GAAG,CAACD,MAAM;QAC3BH,cAAcK,GAAG,CAACF,KAAKA;QACvBvB,QAAQY;IACV;AACF"}