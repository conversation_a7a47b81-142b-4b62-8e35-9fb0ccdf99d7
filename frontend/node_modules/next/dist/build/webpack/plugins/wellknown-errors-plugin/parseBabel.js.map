{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseBabel.ts"], "sourcesContent": ["import { bold, cyan, red, yellow } from '../../../../lib/picocolors'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nexport function getBabelError(\n  fileName: string,\n  err: Error & {\n    code?: string | number\n    loc?: { line: number; column: number }\n  }\n): SimpleWebpackError | false {\n  if (err.code !== 'BABEL_PARSE_ERROR') {\n    return false\n  }\n\n  // https://github.com/babel/babel/blob/34693d6024da3f026534dd8d569f97ac0109602e/packages/babel-core/src/parser/index.js\n  if (err.loc) {\n    const lineNumber = Math.max(1, err.loc.line)\n    const column = Math.max(1, err.loc.column)\n\n    let message = err.message\n      // Remove file information, which instead is provided by webpack.\n      .replace(/^.+?: /, '')\n      // Remove column information from message\n      .replace(\n        new RegExp(`[^\\\\S\\\\r\\\\n]*\\\\(${lineNumber}:${column}\\\\)[^\\\\S\\\\r\\\\n]*`),\n        ''\n      )\n\n    return new SimpleWebpackError(\n      `${cyan(fileName)}:${yellow(lineNumber.toString())}:${yellow(\n        column.toString()\n      )}`,\n      red(bold('Syntax error')).concat(`: ${message}`)\n    )\n  }\n\n  return false\n}\n"], "names": ["getBabelError", "fileName", "err", "code", "loc", "lineNumber", "Math", "max", "line", "column", "message", "replace", "RegExp", "SimpleWebpackError", "cyan", "yellow", "toString", "red", "bold", "concat"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;4BAHwB;oCACL;AAE5B,SAASA,cACdC,QAAgB,EAChBC,GAGC;IAED,IAAIA,IAAIC,IAAI,KAAK,qBAAqB;QACpC,OAAO;IACT;IAEA,uHAAuH;IACvH,IAAID,IAAIE,GAAG,EAAE;QACX,MAAMC,aAAaC,KAAKC,GAAG,CAAC,GAAGL,IAAIE,GAAG,CAACI,IAAI;QAC3C,MAAMC,SAASH,KAAKC,GAAG,CAAC,GAAGL,IAAIE,GAAG,CAACK,MAAM;QAEzC,IAAIC,UAAUR,IAAIQ,OAAO,AACvB,iEAAiE;SAChEC,OAAO,CAAC,UAAU,GACnB,yCAAyC;SACxCA,OAAO,CACN,IAAIC,OAAO,CAAC,gBAAgB,EAAEP,WAAW,CAAC,EAAEI,OAAO,gBAAgB,CAAC,GACpE;QAGJ,OAAO,IAAII,sCAAkB,CAC3B,GAAGC,IAAAA,gBAAI,EAACb,UAAU,CAAC,EAAEc,IAAAA,kBAAM,EAACV,WAAWW,QAAQ,IAAI,CAAC,EAAED,IAAAA,kBAAM,EAC1DN,OAAOO,QAAQ,KACd,EACHC,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,iBAAiBC,MAAM,CAAC,CAAC,EAAE,EAAET,SAAS;IAEnD;IAEA,OAAO;AACT"}