{"version": 3, "sources": ["../../../../src/build/webpack/plugins/css-minimizer-plugin.ts"], "sourcesContent": ["import cssnanoSimple from 'next/dist/compiled/cssnano-simple'\nimport postcssScss from 'next/dist/compiled/postcss-scss'\nimport postcss from 'postcss'\nimport type { Parser } from 'postcss'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport { getCompilationSpan } from '../utils'\n\n// https://github.com/NMFR/optimize-css-assets-webpack-plugin/blob/0a410a9bf28c7b0e81a3470a13748e68ca2f50aa/src/index.js#L20\nconst CSS_REGEX = /\\.css(\\?.*)?$/i\n\ntype CssMinimizerPluginOptions = {\n  postcssOptions: {\n    map: false | { prev?: string | false; inline: boolean; annotation: boolean }\n  }\n}\n\nexport class CssMinimizerPlugin {\n  __next_css_remove = true\n\n  private options: CssMinimizerPluginOptions\n\n  constructor(options: CssMinimizerPluginOptions) {\n    this.options = options\n  }\n\n  optimizeAsset(file: string, asset: any) {\n    const postcssOptions = {\n      ...this.options.postcssOptions,\n      to: file,\n      from: file,\n\n      // We don't actually add this parser to support Sass. It can also be used\n      // for inline comment support. See the README:\n      // https://github.com/postcss/postcss-scss/blob/master/README.md#2-inline-comments-for-postcss\n      parser: postcssScss as any as Parser,\n    }\n\n    let input: string\n    if (postcssOptions.map && asset.sourceAndMap) {\n      const { source, map } = asset.sourceAndMap()\n      input = source\n      postcssOptions.map.prev = map ? map : false\n    } else {\n      input = asset.source()\n    }\n\n    return postcss([cssnanoSimple({ colormin: false }, postcss)])\n      .process(input, postcssOptions)\n      .then((res) => {\n        if (res.map) {\n          return new sources.SourceMapSource(res.css, file, res.map.toJSON())\n        } else {\n          return new sources.RawSource(res.css)\n        }\n      })\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap('CssMinimizerPlugin', (compilation: any) => {\n      const cache = compilation.getCache('CssMinimizerPlugin')\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: 'CssMinimizerPlugin',\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE,\n        },\n        async (assets: any) => {\n          const compilationSpan =\n            getCompilationSpan(compilation) || getCompilationSpan(compiler)\n          const cssMinimizerSpan = compilationSpan!.traceChild(\n            'css-minimizer-plugin'\n          )\n\n          return cssMinimizerSpan.traceAsyncFn(async () => {\n            const files = Object.keys(assets)\n            await Promise.all(\n              files\n                .filter((file) => CSS_REGEX.test(file))\n                .map(async (file) => {\n                  const assetSpan = cssMinimizerSpan.traceChild('minify-css')\n                  assetSpan.setAttribute('file', file)\n\n                  return assetSpan.traceAsyncFn(async () => {\n                    const assetSource = compilation.getAsset(file).source\n                    const etag = cache.getLazyHashedEtag(assetSource)\n                    const cachedResult = await cache.getPromise(file, etag)\n\n                    assetSpan.setAttribute(\n                      'cache',\n                      cachedResult ? 'HIT' : 'MISS'\n                    )\n                    if (cachedResult) {\n                      compilation.updateAsset(file, cachedResult)\n                      return\n                    }\n\n                    const result = await this.optimizeAsset(file, assetSource)\n                    await cache.storePromise(file, etag, result)\n                    compilation.updateAsset(file, result)\n                  })\n                })\n            )\n          })\n        }\n      )\n    })\n  }\n}\n"], "names": ["CssMinimizerPlugin", "CSS_REGEX", "constructor", "options", "__next_css_remove", "optimizeAsset", "file", "asset", "postcssOptions", "to", "from", "parser", "postcssScss", "input", "map", "sourceAndMap", "source", "prev", "postcss", "cssnanoSimple", "colormin", "process", "then", "res", "sources", "SourceMapSource", "css", "toJSON", "RawSource", "apply", "compiler", "hooks", "compilation", "tap", "cache", "getCache", "processAssets", "tapPromise", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "assets", "compilationSpan", "getCompilationSpan", "cssMinimizerSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "files", "Object", "keys", "Promise", "all", "filter", "test", "assetSpan", "setAttribute", "assetSource", "getAsset", "etag", "getLazyHashedEtag", "cachedResult", "getPromise", "updateAsset", "result", "storePromise"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;sEAhBa;oEACF;gEACJ;yBAEa;uBACE;;;;;;AAEnC,4HAA4H;AAC5H,MAAMC,YAAY;AAQX,MAAMD;IAKXE,YAAYC,OAAkC,CAAE;aAJhDC,oBAAoB;QAKlB,IAAI,CAACD,OAAO,GAAGA;IACjB;IAEAE,cAAcC,IAAY,EAAEC,KAAU,EAAE;QACtC,MAAMC,iBAAiB;YACrB,GAAG,IAAI,CAACL,OAAO,CAACK,cAAc;YAC9BC,IAAIH;YACJI,MAAMJ;YAEN,yEAAyE;YACzE,8CAA8C;YAC9C,8FAA8F;YAC9FK,QAAQC,oBAAW;QACrB;QAEA,IAAIC;QACJ,IAAIL,eAAeM,GAAG,IAAIP,MAAMQ,YAAY,EAAE;YAC5C,MAAM,EAAEC,MAAM,EAAEF,GAAG,EAAE,GAAGP,MAAMQ,YAAY;YAC1CF,QAAQG;YACRR,eAAeM,GAAG,CAACG,IAAI,GAAGH,MAAMA,MAAM;QACxC,OAAO;YACLD,QAAQN,MAAMS,MAAM;QACtB;QAEA,OAAOE,IAAAA,gBAAO,EAAC;YAACC,IAAAA,sBAAa,EAAC;gBAAEC,UAAU;YAAM,GAAGF,gBAAO;SAAE,EACzDG,OAAO,CAACR,OAAOL,gBACfc,IAAI,CAAC,CAACC;YACL,IAAIA,IAAIT,GAAG,EAAE;gBACX,OAAO,IAAIU,gBAAO,CAACC,eAAe,CAACF,IAAIG,GAAG,EAAEpB,MAAMiB,IAAIT,GAAG,CAACa,MAAM;YAClE,OAAO;gBACL,OAAO,IAAIH,gBAAO,CAACI,SAAS,CAACL,IAAIG,GAAG;YACtC;QACF;IACJ;IAEAG,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAAC,sBAAsB,CAACD;YACpD,MAAME,QAAQF,YAAYG,QAAQ,CAAC;YACnCH,YAAYD,KAAK,CAACK,aAAa,CAACC,UAAU,CACxC;gBACEC,MAAM;gBACNC,OAAOC,gBAAO,CAACC,WAAW,CAACC,kCAAkC;YAC/D,GACA,OAAOC;gBACL,MAAMC,kBACJC,IAAAA,yBAAkB,EAACb,gBAAgBa,IAAAA,yBAAkB,EAACf;gBACxD,MAAMgB,mBAAmBF,gBAAiBG,UAAU,CAClD;gBAGF,OAAOD,iBAAiBE,YAAY,CAAC;oBACnC,MAAMC,QAAQC,OAAOC,IAAI,CAACR;oBAC1B,MAAMS,QAAQC,GAAG,CACfJ,MACGK,MAAM,CAAC,CAAChD,OAASL,UAAUsD,IAAI,CAACjD,OAChCQ,GAAG,CAAC,OAAOR;wBACV,MAAMkD,YAAYV,iBAAiBC,UAAU,CAAC;wBAC9CS,UAAUC,YAAY,CAAC,QAAQnD;wBAE/B,OAAOkD,UAAUR,YAAY,CAAC;4BAC5B,MAAMU,cAAc1B,YAAY2B,QAAQ,CAACrD,MAAMU,MAAM;4BACrD,MAAM4C,OAAO1B,MAAM2B,iBAAiB,CAACH;4BACrC,MAAMI,eAAe,MAAM5B,MAAM6B,UAAU,CAACzD,MAAMsD;4BAElDJ,UAAUC,YAAY,CACpB,SACAK,eAAe,QAAQ;4BAEzB,IAAIA,cAAc;gCAChB9B,YAAYgC,WAAW,CAAC1D,MAAMwD;gCAC9B;4BACF;4BAEA,MAAMG,SAAS,MAAM,IAAI,CAAC5D,aAAa,CAACC,MAAMoD;4BAC9C,MAAMxB,MAAMgC,YAAY,CAAC5D,MAAMsD,MAAMK;4BACrCjC,YAAYgC,WAAW,CAAC1D,MAAM2D;wBAChC;oBACF;gBAEN;YACF;QAEJ;IACF;AACF"}