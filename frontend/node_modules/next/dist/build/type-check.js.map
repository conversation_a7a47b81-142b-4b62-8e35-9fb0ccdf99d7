{"version": 3, "sources": ["../../src/build/type-check.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../server/config-shared'\nimport type { Telemetry } from '../telemetry/storage'\nimport type { Span } from '../trace'\n\nimport path from 'path'\nimport * as Log from './output/log'\nimport { Worker } from '../lib/worker'\nimport { verifyAndLint } from '../lib/verifyAndLint'\nimport createSpinner from './spinner'\nimport { eventTypeCheckCompleted } from '../telemetry/events'\nimport isError from '../lib/is-error'\n\n/**\n * typescript will be loaded in \"next/lib/verify-typescript-setup\" and\n * then passed to \"next/lib/typescript/runTypeCheck\" as a parameter.\n *\n * Since it is impossible to pass a function from main thread to a worker,\n * instead of running \"next/lib/typescript/runTypeCheck\" in a worker,\n * we will run entire \"next/lib/verify-typescript-setup\" in a worker instead.\n */\nfunction verifyTypeScriptSetup(\n  dir: string,\n  distDir: string,\n  intentDirs: string[],\n  typeCheckPreflight: boolean,\n  tsconfigPath: string,\n  disableStaticImages: boolean,\n  cacheDir: string | undefined,\n  enableWorkerThreads: boolean | undefined,\n  hasAppDir: boolean,\n  hasPagesDir: boolean\n) {\n  const typeCheckWorker = new Worker(\n    require.resolve('../lib/verify-typescript-setup'),\n    {\n      exposedMethods: ['verifyTypeScriptSetup'],\n      numWorkers: 1,\n      enableWorkerThreads,\n      maxRetries: 0,\n    }\n  ) as Worker & {\n    verifyTypeScriptSetup: typeof import('../lib/verify-typescript-setup').verifyTypeScriptSetup\n  }\n\n  return typeCheckWorker\n    .verifyTypeScriptSetup({\n      dir,\n      distDir,\n      intentDirs,\n      typeCheckPreflight,\n      tsconfigPath,\n      disableStaticImages,\n      cacheDir,\n      hasAppDir,\n      hasPagesDir,\n    })\n    .then((result) => {\n      typeCheckWorker.end()\n      return result\n    })\n    .catch(() => {\n      // The error is already logged in the worker, we simply exit the main thread to prevent the\n      // `Jest worker encountered 1 child process exceptions, exceeding retry limit` from showing up\n      process.exit(1)\n    })\n}\n\nexport async function startTypeChecking({\n  cacheDir,\n  config,\n  dir,\n  ignoreESLint,\n  nextBuildSpan,\n  pagesDir,\n  runLint,\n  shouldLint,\n  telemetry,\n  appDir,\n}: {\n  cacheDir: string\n  config: NextConfigComplete\n  dir: string\n  ignoreESLint: boolean\n  nextBuildSpan: Span\n  pagesDir?: string\n  runLint: boolean\n  shouldLint: boolean\n  telemetry: Telemetry\n  appDir?: string\n}) {\n  const ignoreTypeScriptErrors = Boolean(config.typescript.ignoreBuildErrors)\n\n  const eslintCacheDir = path.join(cacheDir, 'eslint/')\n\n  if (ignoreTypeScriptErrors) {\n    Log.info('Skipping validation of types')\n  }\n  if (runLint && ignoreESLint) {\n    // only print log when build require lint while ignoreESLint is enabled\n    Log.info('Skipping linting')\n  }\n\n  let typeCheckingAndLintingSpinnerPrefixText: string | undefined\n  let typeCheckingAndLintingSpinner:\n    | ReturnType<typeof createSpinner>\n    | undefined\n\n  if (!ignoreTypeScriptErrors && shouldLint) {\n    typeCheckingAndLintingSpinnerPrefixText =\n      'Linting and checking validity of types'\n  } else if (!ignoreTypeScriptErrors) {\n    typeCheckingAndLintingSpinnerPrefixText = 'Checking validity of types'\n  } else if (shouldLint) {\n    typeCheckingAndLintingSpinnerPrefixText = 'Linting'\n  }\n\n  // we will not create a spinner if both ignoreTypeScriptErrors and ignoreESLint are\n  // enabled, but we will still verifying project's tsconfig and dependencies.\n  if (typeCheckingAndLintingSpinnerPrefixText) {\n    typeCheckingAndLintingSpinner = createSpinner(\n      typeCheckingAndLintingSpinnerPrefixText\n    )\n  }\n\n  const typeCheckStart = process.hrtime()\n\n  try {\n    const [[verifyResult, typeCheckEnd]] = await Promise.all([\n      nextBuildSpan.traceChild('verify-typescript-setup').traceAsyncFn(() =>\n        verifyTypeScriptSetup(\n          dir,\n          config.distDir,\n          [pagesDir, appDir].filter(Boolean) as string[],\n          !ignoreTypeScriptErrors,\n          config.typescript.tsconfigPath,\n          config.images.disableStaticImages,\n          cacheDir,\n          config.experimental.workerThreads,\n          !!appDir,\n          !!pagesDir\n        ).then((resolved) => {\n          const checkEnd = process.hrtime(typeCheckStart)\n          return [resolved, checkEnd] as const\n        })\n      ),\n      shouldLint &&\n        nextBuildSpan.traceChild('verify-and-lint').traceAsyncFn(async () => {\n          await verifyAndLint(\n            dir,\n            eslintCacheDir,\n            config.eslint?.dirs,\n            config.experimental.workerThreads,\n            telemetry\n          )\n        }),\n    ])\n    typeCheckingAndLintingSpinner?.stopAndPersist()\n\n    if (!ignoreTypeScriptErrors && verifyResult) {\n      telemetry.record(\n        eventTypeCheckCompleted({\n          durationInSeconds: typeCheckEnd[0],\n          typescriptVersion: verifyResult.version,\n          inputFilesCount: verifyResult.result?.inputFilesCount,\n          totalFilesCount: verifyResult.result?.totalFilesCount,\n          incremental: verifyResult.result?.incremental,\n        })\n      )\n    }\n  } catch (err) {\n    // prevent showing jest-worker internal error as it\n    // isn't helpful for users and clutters output\n    if (isError(err) && err.message === 'Call retries were exceeded') {\n      await telemetry.flush()\n      process.exit(1)\n    }\n    throw err\n  }\n}\n"], "names": ["startTypeChecking", "verifyTypeScriptSetup", "dir", "distDir", "intentDirs", "typeCheckPreflight", "tsconfigPath", "disableStaticImages", "cacheDir", "enableWorkerThreads", "hasAppDir", "hasPagesDir", "typeCheckWorker", "Worker", "require", "resolve", "exposedMethods", "numWorkers", "maxRetries", "then", "result", "end", "catch", "process", "exit", "config", "ignoreESLint", "nextBuildSpan", "pagesDir", "runLint", "shouldLint", "telemetry", "appDir", "ignoreTypeScriptErrors", "Boolean", "typescript", "ignoreBuildErrors", "eslintCacheDir", "path", "join", "Log", "info", "typeCheckingAndLintingSpinnerPrefixText", "typeCheckingAndLintingSpinner", "createSpinner", "typeCheckStart", "hrtime", "verifyResult", "typeCheckEnd", "Promise", "all", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "filter", "images", "experimental", "workerThreads", "resolved", "checkEnd", "verifyAndLint", "eslint", "dirs", "stopAndPersist", "record", "eventTypeCheckCompleted", "durationInSeconds", "typescriptVersion", "version", "inputFilesCount", "totalFilesCount", "incremental", "err", "isError", "message", "flush"], "mappings": ";;;;+BAmEsBA;;;eAAAA;;;6DA/DL;6DACI;wBACE;+BACO;gEACJ;wBACc;gEACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB;;;;;;;CAOC,GACD,SAASC,sBACPC,GAAW,EACXC,OAAe,EACfC,UAAoB,EACpBC,kBAA2B,EAC3BC,YAAoB,EACpBC,mBAA4B,EAC5BC,QAA4B,EAC5BC,mBAAwC,EACxCC,SAAkB,EAClBC,WAAoB;IAEpB,MAAMC,kBAAkB,IAAIC,cAAM,CAChCC,QAAQC,OAAO,CAAC,mCAChB;QACEC,gBAAgB;YAAC;SAAwB;QACzCC,YAAY;QACZR;QACAS,YAAY;IACd;IAKF,OAAON,gBACJX,qBAAqB,CAAC;QACrBC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAE;QACAC;IACF,GACCQ,IAAI,CAAC,CAACC;QACLR,gBAAgBS,GAAG;QACnB,OAAOD;IACT,GACCE,KAAK,CAAC;QACL,2FAA2F;QAC3F,8FAA8F;QAC9FC,QAAQC,IAAI,CAAC;IACf;AACJ;AAEO,eAAexB,kBAAkB,EACtCQ,QAAQ,EACRiB,MAAM,EACNvB,GAAG,EACHwB,YAAY,EACZC,aAAa,EACbC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,MAAM,EAYP;IACC,MAAMC,yBAAyBC,QAAQT,OAAOU,UAAU,CAACC,iBAAiB;IAE1E,MAAMC,iBAAiBC,aAAI,CAACC,IAAI,CAAC/B,UAAU;IAE3C,IAAIyB,wBAAwB;QAC1BO,KAAIC,IAAI,CAAC;IACX;IACA,IAAIZ,WAAWH,cAAc;QAC3B,uEAAuE;QACvEc,KAAIC,IAAI,CAAC;IACX;IAEA,IAAIC;IACJ,IAAIC;IAIJ,IAAI,CAACV,0BAA0BH,YAAY;QACzCY,0CACE;IACJ,OAAO,IAAI,CAACT,wBAAwB;QAClCS,0CAA0C;IAC5C,OAAO,IAAIZ,YAAY;QACrBY,0CAA0C;IAC5C;IAEA,mFAAmF;IACnF,4EAA4E;IAC5E,IAAIA,yCAAyC;QAC3CC,gCAAgCC,IAAAA,gBAAa,EAC3CF;IAEJ;IAEA,MAAMG,iBAAiBtB,QAAQuB,MAAM;IAErC,IAAI;QACF,MAAM,CAAC,CAACC,cAAcC,aAAa,CAAC,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACvDvB,cAAcwB,UAAU,CAAC,2BAA2BC,YAAY,CAAC,IAC/DnD,sBACEC,KACAuB,OAAOtB,OAAO,EACd;oBAACyB;oBAAUI;iBAAO,CAACqB,MAAM,CAACnB,UAC1B,CAACD,wBACDR,OAAOU,UAAU,CAAC7B,YAAY,EAC9BmB,OAAO6B,MAAM,CAAC/C,mBAAmB,EACjCC,UACAiB,OAAO8B,YAAY,CAACC,aAAa,EACjC,CAAC,CAACxB,QACF,CAAC,CAACJ,UACFT,IAAI,CAAC,CAACsC;oBACN,MAAMC,WAAWnC,QAAQuB,MAAM,CAACD;oBAChC,OAAO;wBAACY;wBAAUC;qBAAS;gBAC7B;YAEF5B,cACEH,cAAcwB,UAAU,CAAC,mBAAmBC,YAAY,CAAC;oBAIrD3B;gBAHF,MAAMkC,IAAAA,4BAAa,EACjBzD,KACAmC,iBACAZ,iBAAAA,OAAOmC,MAAM,qBAAbnC,eAAeoC,IAAI,EACnBpC,OAAO8B,YAAY,CAACC,aAAa,EACjCzB;YAEJ;SACH;QACDY,iDAAAA,8BAA+BmB,cAAc;QAE7C,IAAI,CAAC7B,0BAA0Bc,cAAc;gBAKtBA,sBACAA,uBACJA;YANjBhB,UAAUgC,MAAM,CACdC,IAAAA,+BAAuB,EAAC;gBACtBC,mBAAmBjB,YAAY,CAAC,EAAE;gBAClCkB,mBAAmBnB,aAAaoB,OAAO;gBACvCC,eAAe,GAAErB,uBAAAA,aAAa3B,MAAM,qBAAnB2B,qBAAqBqB,eAAe;gBACrDC,eAAe,GAAEtB,wBAAAA,aAAa3B,MAAM,qBAAnB2B,sBAAqBsB,eAAe;gBACrDC,WAAW,GAAEvB,wBAAAA,aAAa3B,MAAM,qBAAnB2B,sBAAqBuB,WAAW;YAC/C;QAEJ;IACF,EAAE,OAAOC,KAAK;QACZ,mDAAmD;QACnD,8CAA8C;QAC9C,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,OAAO,KAAK,8BAA8B;YAChE,MAAM1C,UAAU2C,KAAK;YACrBnD,QAAQC,IAAI,CAAC;QACf;QACA,MAAM+C;IACR;AACF"}