{"version": 3, "sources": ["../../src/build/get-babel-config-file.ts"], "sourcesContent": ["import { join } from 'path'\nimport { existsSync } from 'fs'\n\nconst BABEL_CONFIG_FILES = [\n  '.babelrc',\n  '.babelrc.json',\n  '.babelrc.js',\n  '.babelrc.mjs',\n  '.babelrc.cjs',\n  'babel.config.js',\n  'babel.config.json',\n  'babel.config.mjs',\n  'babel.config.cjs',\n]\n\nexport function getBabelConfigFile(dir: string): string | undefined {\n  for (const filename of BABEL_CONFIG_FILES) {\n    const configFilePath = join(dir, filename)\n    const exists = existsSync(configFilePath)\n    if (!exists) {\n      continue\n    }\n    return configFilePath\n  }\n}\n"], "names": ["getBabelConfigFile", "BABEL_CONFIG_FILES", "dir", "filename", "config<PERSON><PERSON><PERSON><PERSON>", "join", "exists", "existsSync"], "mappings": ";;;;+BAegBA;;;eAAAA;;;sBAfK;oBACM;AAE3B,MAAMC,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,mBAAmBE,GAAW;IAC5C,KAAK,MAAMC,YAAYF,mBAAoB;QACzC,MAAMG,iBAAiBC,IAAAA,UAAI,EAACH,KAAKC;QACjC,MAAMG,SAASC,IAAAA,cAAU,EAACH;QAC1B,IAAI,CAACE,QAAQ;YACX;QACF;QACA,OAAOF;IACT;AACF"}