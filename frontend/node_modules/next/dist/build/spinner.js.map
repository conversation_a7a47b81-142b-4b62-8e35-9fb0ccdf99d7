{"version": 3, "sources": ["../../src/build/spinner.ts"], "sourcesContent": ["import ora from 'next/dist/compiled/ora'\nimport * as Log from './output/log'\n\nconst dotsSpinner = {\n  frames: ['.', '..', '...'],\n  interval: 200,\n}\n\nexport default function createSpinner(\n  text: string,\n  options: ora.Options = {},\n  logFn: (...data: any[]) => void = console.log\n) {\n  let spinner: undefined | (ora.Ora & { setText: (text: string) => void })\n\n  let prefixText = ` ${Log.prefixes.info} ${text} `\n\n  if (process.stdout.isTTY) {\n    spinner = ora({\n      text: undefined,\n      prefixText,\n      spinner: dotsSpinner,\n      stream: process.stdout,\n      ...options,\n    }).start() as ora.Ora & { setText: (text: string) => void }\n\n    // Add capturing of console.log/warn/error to allow pausing\n    // the spinner before logging and then restarting spinner after\n    const origLog = console.log\n    const origWarn = console.warn\n    const origError = console.error\n    const origStop = spinner.stop.bind(spinner)\n    const origStopAndPersist = spinner.stopAndPersist.bind(spinner)\n\n    const logHandle = (method: any, args: any[]) => {\n      // Enter a new line before logging new message, to avoid\n      // the new message shows up right after the spinner in the same line.\n      const isInProgress = spinner?.isSpinning\n      if (spinner && isInProgress) {\n        // Reset the current running spinner to empty line by `\\r`\n        spinner.prefixText = '\\r'\n        spinner.text = '\\r'\n        spinner.clear()\n        origStop()\n      }\n      method(...args)\n      if (spinner && isInProgress) {\n        spinner.start()\n      }\n    }\n\n    console.log = (...args: any) => logHandle(origLog, args)\n    console.warn = (...args: any) => logHandle(origWarn, args)\n    console.error = (...args: any) => logHandle(origError, args)\n\n    const resetLog = () => {\n      console.log = origLog\n      console.warn = origWarn\n      console.error = origError\n    }\n    spinner.setText = (newText) => {\n      text = newText\n      prefixText = ` ${Log.prefixes.info} ${newText} `\n      spinner!.prefixText = prefixText\n      return spinner!\n    }\n    spinner.stop = () => {\n      origStop()\n      resetLog()\n      return spinner!\n    }\n    spinner.stopAndPersist = () => {\n      // Add \\r at beginning to reset the current line of loading status text\n      const suffixText = `\\r ${Log.prefixes.event} ${text} `\n      if (spinner) {\n        spinner.text = suffixText\n      } else {\n        logFn(suffixText)\n      }\n      origStopAndPersist()\n      resetLog()\n      return spinner!\n    }\n  } else if (prefixText || text) {\n    logFn(prefixText ? prefixText + '...' : text)\n  }\n\n  return spinner\n}\n"], "names": ["createSpinner", "dots<PERSON>pinner", "frames", "interval", "text", "options", "logFn", "console", "log", "spinner", "prefixText", "Log", "prefixes", "info", "process", "stdout", "isTTY", "ora", "undefined", "stream", "start", "origLog", "origWarn", "warn", "origError", "error", "origStop", "stop", "bind", "origStopAndPersist", "stopAndPersist", "logHandle", "method", "args", "isInProgress", "isSpinning", "clear", "resetLog", "setText", "newText", "suffixText", "event"], "mappings": ";;;;+BAQA;;;eAAwBA;;;4DARR;6DACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,cAAc;IAClBC,QAAQ;QAAC;QAAK;QAAM;KAAM;IAC1BC,UAAU;AACZ;AAEe,SAASH,cACtBI,IAAY,EACZC,UAAuB,CAAC,CAAC,EACzBC,QAAkCC,QAAQC,GAAG;IAE7C,IAAIC;IAEJ,IAAIC,aAAa,CAAC,CAAC,EAAEC,KAAIC,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAET,KAAK,CAAC,CAAC;IAEjD,IAAIU,QAAQC,MAAM,CAACC,KAAK,EAAE;QACxBP,UAAUQ,IAAAA,YAAG,EAAC;YACZb,MAAMc;YACNR;YACAD,SAASR;YACTkB,QAAQL,QAAQC,MAAM;YACtB,GAAGV,OAAO;QACZ,GAAGe,KAAK;QAER,2DAA2D;QAC3D,+DAA+D;QAC/D,MAAMC,UAAUd,QAAQC,GAAG;QAC3B,MAAMc,WAAWf,QAAQgB,IAAI;QAC7B,MAAMC,YAAYjB,QAAQkB,KAAK;QAC/B,MAAMC,WAAWjB,QAAQkB,IAAI,CAACC,IAAI,CAACnB;QACnC,MAAMoB,qBAAqBpB,QAAQqB,cAAc,CAACF,IAAI,CAACnB;QAEvD,MAAMsB,YAAY,CAACC,QAAaC;YAC9B,wDAAwD;YACxD,qEAAqE;YACrE,MAAMC,eAAezB,2BAAAA,QAAS0B,UAAU;YACxC,IAAI1B,WAAWyB,cAAc;gBAC3B,0DAA0D;gBAC1DzB,QAAQC,UAAU,GAAG;gBACrBD,QAAQL,IAAI,GAAG;gBACfK,QAAQ2B,KAAK;gBACbV;YACF;YACAM,UAAUC;YACV,IAAIxB,WAAWyB,cAAc;gBAC3BzB,QAAQW,KAAK;YACf;QACF;QAEAb,QAAQC,GAAG,GAAG,CAAC,GAAGyB,OAAcF,UAAUV,SAASY;QACnD1B,QAAQgB,IAAI,GAAG,CAAC,GAAGU,OAAcF,UAAUT,UAAUW;QACrD1B,QAAQkB,KAAK,GAAG,CAAC,GAAGQ,OAAcF,UAAUP,WAAWS;QAEvD,MAAMI,WAAW;YACf9B,QAAQC,GAAG,GAAGa;YACdd,QAAQgB,IAAI,GAAGD;YACff,QAAQkB,KAAK,GAAGD;QAClB;QACAf,QAAQ6B,OAAO,GAAG,CAACC;YACjBnC,OAAOmC;YACP7B,aAAa,CAAC,CAAC,EAAEC,KAAIC,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAE0B,QAAQ,CAAC,CAAC;YAChD9B,QAASC,UAAU,GAAGA;YACtB,OAAOD;QACT;QACAA,QAAQkB,IAAI,GAAG;YACbD;YACAW;YACA,OAAO5B;QACT;QACAA,QAAQqB,cAAc,GAAG;YACvB,uEAAuE;YACvE,MAAMU,aAAa,CAAC,GAAG,EAAE7B,KAAIC,QAAQ,CAAC6B,KAAK,CAAC,CAAC,EAAErC,KAAK,CAAC,CAAC;YACtD,IAAIK,SAAS;gBACXA,QAAQL,IAAI,GAAGoC;YACjB,OAAO;gBACLlC,MAAMkC;YACR;YACAX;YACAQ;YACA,OAAO5B;QACT;IACF,OAAO,IAAIC,cAAcN,MAAM;QAC7BE,MAAMI,aAAaA,aAAa,QAAQN;IAC1C;IAEA,OAAOK;AACT"}