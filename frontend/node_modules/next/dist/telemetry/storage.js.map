{"version": 3, "sources": ["../../src/telemetry/storage.ts"], "sourcesContent": ["import type { BinaryLike } from 'crypto'\nimport { bold, cyan, magenta } from '../lib/picocolors'\nimport Conf from 'next/dist/compiled/conf'\nimport { createHash, randomBytes } from 'crypto'\nimport isDockerFunction from 'next/dist/compiled/is-docker'\nimport path from 'path'\n\nimport { getAnonymousMeta } from './anonymous-meta'\nimport * as ciEnvironment from '../server/ci-info'\nimport { postNextTelemetryPayload } from './post-telemetry-payload'\nimport { getRawProjectId } from './project-id'\nimport { AbortController } from 'next/dist/compiled/@edge-runtime/ponyfill'\nimport fs from 'fs'\n\n// This is the key that stores whether or not telemetry is enabled or disabled.\nconst TELEMETRY_KEY_ENABLED = 'telemetry.enabled'\n\n// This is the key that specifies when the user was informed about anonymous\n// telemetry collection.\nconst TELEMETRY_KEY_NOTIFY_DATE = 'telemetry.notifiedAt'\n\n// This is a quasi-persistent identifier used to dedupe recurring events. It's\n// generated from random data and completely anonymous.\nconst TELEMETRY_KEY_ID = `telemetry.anonymousId`\n\n// This is the cryptographic salt that is included within every hashed value.\n// This salt value is never sent to us, ensuring privacy and the one-way nature\n// of the hash (prevents dictionary lookups of pre-computed hashes).\n// See the `oneWayHash` function.\nconst TELEMETRY_KEY_SALT = `telemetry.salt`\n\nexport type TelemetryEvent = { eventName: string; payload: object }\n\ntype RecordObject = {\n  isFulfilled: boolean\n  isRejected: boolean\n  value?: any\n  reason?: any\n}\n\nfunction getStorageDirectory(distDir: string): string | undefined {\n  const isLikelyEphemeral = ciEnvironment.isCI || isDockerFunction()\n\n  if (isLikelyEphemeral) {\n    return path.join(distDir, 'cache')\n  }\n\n  return undefined\n}\n\nexport class Telemetry {\n  readonly sessionId: string\n\n  private conf: Conf<any> | null\n  private distDir: string\n  private loadProjectId: undefined | string | Promise<string>\n  private NEXT_TELEMETRY_DISABLED: any\n  private NEXT_TELEMETRY_DEBUG: any\n\n  private queue: Set<Promise<RecordObject>>\n\n  constructor({ distDir }: { distDir: string }) {\n    // Read in the constructor so that .env can be loaded before reading\n    const { NEXT_TELEMETRY_DISABLED, NEXT_TELEMETRY_DEBUG } = process.env\n    this.NEXT_TELEMETRY_DISABLED = NEXT_TELEMETRY_DISABLED\n    this.NEXT_TELEMETRY_DEBUG = NEXT_TELEMETRY_DEBUG\n    this.distDir = distDir\n    const storageDirectory = getStorageDirectory(distDir)\n\n    try {\n      // `conf` incorrectly throws a permission error during initialization\n      // instead of waiting for first use. We need to handle it, otherwise the\n      // process may crash.\n      this.conf = new Conf({ projectName: 'nextjs', cwd: storageDirectory })\n    } catch (_) {\n      this.conf = null\n    }\n    this.sessionId = randomBytes(32).toString('hex')\n    this.queue = new Set()\n\n    this.notify()\n  }\n\n  private notify = () => {\n    if (this.isDisabled || !this.conf) {\n      return\n    }\n\n    // The end-user has already been notified about our telemetry integration. We\n    // don't need to constantly annoy them about it.\n    // We will re-inform users about the telemetry if significant changes are\n    // ever made.\n    if (this.conf.get(TELEMETRY_KEY_NOTIFY_DATE, '')) {\n      return\n    }\n    this.conf.set(TELEMETRY_KEY_NOTIFY_DATE, Date.now().toString())\n\n    console.log(\n      `${magenta(\n        bold('Attention')\n      )}: Next.js now collects completely anonymous telemetry regarding usage.`\n    )\n    console.log(\n      `This information is used to shape Next.js' roadmap and prioritize features.`\n    )\n    console.log(\n      `You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:`\n    )\n    console.log(cyan('https://nextjs.org/telemetry'))\n    console.log()\n  }\n\n  get anonymousId(): string {\n    const val = this.conf && this.conf.get(TELEMETRY_KEY_ID)\n    if (val) {\n      return val\n    }\n\n    const generated = randomBytes(32).toString('hex')\n    this.conf && this.conf.set(TELEMETRY_KEY_ID, generated)\n    return generated\n  }\n\n  get salt(): string {\n    const val = this.conf && this.conf.get(TELEMETRY_KEY_SALT)\n    if (val) {\n      return val\n    }\n\n    const generated = randomBytes(16).toString('hex')\n    this.conf && this.conf.set(TELEMETRY_KEY_SALT, generated)\n    return generated\n  }\n\n  private get isDisabled(): boolean {\n    if (!!this.NEXT_TELEMETRY_DISABLED || !this.conf) {\n      return true\n    }\n    return this.conf.get(TELEMETRY_KEY_ENABLED, true) === false\n  }\n\n  setEnabled = (_enabled: boolean) => {\n    const enabled = !!_enabled\n    this.conf && this.conf.set(TELEMETRY_KEY_ENABLED, enabled)\n    return this.conf && this.conf.path\n  }\n\n  get isEnabled(): boolean {\n    return (\n      !this.NEXT_TELEMETRY_DISABLED &&\n      !!this.conf &&\n      this.conf.get(TELEMETRY_KEY_ENABLED, true) !== false\n    )\n  }\n\n  oneWayHash = (payload: BinaryLike): string => {\n    const hash = createHash('sha256')\n\n    // Always prepend the payload value with salt. This ensures the hash is truly\n    // one-way.\n    hash.update(this.salt)\n\n    // Update is an append operation, not a replacement. The salt from the prior\n    // update is still present!\n    hash.update(payload)\n    return hash.digest('hex')\n  }\n\n  private async getProjectId(): Promise<string> {\n    this.loadProjectId = this.loadProjectId || getRawProjectId()\n    return this.oneWayHash(await this.loadProjectId)\n  }\n\n  record = (\n    _events: TelemetryEvent | TelemetryEvent[],\n    deferred?: boolean\n  ): Promise<RecordObject> => {\n    const prom = (\n      deferred\n        ? // if we know we are going to immediately call\n          // flushDetached we can skip starting the initial\n          // submitRecord which will then be cancelled\n          new Promise((resolve) =>\n            resolve({\n              isFulfilled: true,\n              isRejected: false,\n              value: _events,\n            })\n          )\n        : this.submitRecord(_events)\n    )\n      .then((value) => ({\n        isFulfilled: true,\n        isRejected: false,\n        value,\n      }))\n      .catch((reason) => ({\n        isFulfilled: false,\n        isRejected: true,\n        reason,\n      }))\n      // Acts as `Promise#finally` because `catch` transforms the error\n      .then((res) => {\n        // Clean up the event to prevent unbounded `Set` growth\n        if (!deferred) {\n          this.queue.delete(prom)\n        }\n        return res\n      })\n\n    ;(prom as any)._events = Array.isArray(_events) ? _events : [_events]\n    ;(prom as any)._controller = (prom as any)._controller\n    // Track this `Promise` so we can flush pending events\n    this.queue.add(prom)\n\n    return prom\n  }\n\n  flush = async () => Promise.all(this.queue).catch(() => null)\n\n  // writes current events to disk and spawns separate\n  // detached process to submit the records without blocking\n  // the main process from exiting\n  flushDetached = (mode: 'dev', dir: string) => {\n    const allEvents: TelemetryEvent[] = []\n\n    this.queue.forEach((item: any) => {\n      try {\n        item._controller?.abort()\n        allEvents.push(...item._events)\n      } catch (_) {\n        // if we fail to abort ignore this event\n      }\n    })\n    fs.mkdirSync(this.distDir, { recursive: true })\n    fs.writeFileSync(\n      path.join(this.distDir, '_events.json'),\n      JSON.stringify(allEvents)\n    )\n\n    // Note: cross-spawn is not used here as it causes\n    // a new command window to appear when we don't want it to\n    const child_process =\n      require('child_process') as typeof import('child_process')\n\n    // we use spawnSync when debugging to ensure logs are piped\n    // correctly to stdout/stderr\n    const spawn = this.NEXT_TELEMETRY_DEBUG\n      ? child_process.spawnSync\n      : child_process.spawn\n\n    spawn(process.execPath, [require.resolve('./detached-flush'), mode, dir], {\n      detached: !this.NEXT_TELEMETRY_DEBUG,\n      windowsHide: true,\n      shell: false,\n      ...(this.NEXT_TELEMETRY_DEBUG\n        ? {\n            stdio: 'inherit',\n          }\n        : {}),\n    })\n  }\n\n  private submitRecord = async (\n    _events: TelemetryEvent | TelemetryEvent[]\n  ): Promise<any> => {\n    let events: TelemetryEvent[]\n    if (Array.isArray(_events)) {\n      events = _events\n    } else {\n      events = [_events]\n    }\n\n    if (events.length < 1) {\n      return Promise.resolve()\n    }\n\n    if (this.NEXT_TELEMETRY_DEBUG) {\n      // Print to standard error to simplify selecting the output\n      events.forEach(({ eventName, payload }) =>\n        console.error(\n          `[telemetry] ` + JSON.stringify({ eventName, payload }, null, 2)\n        )\n      )\n      // Do not send the telemetry data if debugging. Users may use this feature\n      // to preview what data would be sent.\n      return Promise.resolve()\n    }\n\n    // Skip recording telemetry if the feature is disabled\n    if (this.isDisabled) {\n      return Promise.resolve()\n    }\n\n    const postController = new AbortController()\n    const res = postNextTelemetryPayload(\n      {\n        context: {\n          anonymousId: this.anonymousId,\n          projectId: await this.getProjectId(),\n          sessionId: this.sessionId,\n        },\n        meta: getAnonymousMeta(),\n        events: events.map(({ eventName, payload }) => ({\n          eventName,\n          fields: payload,\n        })),\n      },\n      postController.signal\n    )\n    res._controller = postController\n    return res\n  }\n}\n"], "names": ["Telemetry", "TELEMETRY_KEY_ENABLED", "TELEMETRY_KEY_NOTIFY_DATE", "TELEMETRY_KEY_ID", "TELEMETRY_KEY_SALT", "getStorageDirectory", "distDir", "isLikelyEphemeral", "ciEnvironment", "isCI", "isDockerFunction", "path", "join", "undefined", "constructor", "notify", "isDisabled", "conf", "get", "set", "Date", "now", "toString", "console", "log", "magenta", "bold", "cyan", "setEnabled", "_enabled", "enabled", "oneWayHash", "payload", "hash", "createHash", "update", "salt", "digest", "record", "_events", "deferred", "prom", "Promise", "resolve", "isFulfilled", "isRejected", "value", "submitRecord", "then", "catch", "reason", "res", "queue", "delete", "Array", "isArray", "_controller", "add", "flush", "all", "flushDetached", "mode", "dir", "allEvents", "for<PERSON>ach", "item", "abort", "push", "_", "fs", "mkdirSync", "recursive", "writeFileSync", "JSON", "stringify", "child_process", "require", "spawn", "NEXT_TELEMETRY_DEBUG", "spawnSync", "process", "execPath", "detached", "windowsHide", "shell", "stdio", "events", "length", "eventName", "error", "postController", "AbortController", "postNextTelemetryPayload", "context", "anonymousId", "projectId", "getProjectId", "sessionId", "meta", "getAnonymousMeta", "map", "fields", "signal", "NEXT_TELEMETRY_DISABLED", "env", "storageDirectory", "Conf", "projectName", "cwd", "randomBytes", "Set", "val", "generated", "isEnabled", "loadProjectId", "getRawProjectId"], "mappings": ";;;;+BAkDaA;;;eAAAA;;;4BAjDuB;6DACnB;wBACuB;iEACX;6DACZ;+BAEgB;gEACF;sCACU;2BACT;0BACA;2DACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEf,+EAA+E;AAC/E,MAAMC,wBAAwB;AAE9B,4EAA4E;AAC5E,wBAAwB;AACxB,MAAMC,4BAA4B;AAElC,8EAA8E;AAC9E,uDAAuD;AACvD,MAAMC,mBAAmB,CAAC,qBAAqB,CAAC;AAEhD,6EAA6E;AAC7E,+EAA+E;AAC/E,oEAAoE;AACpE,iCAAiC;AACjC,MAAMC,qBAAqB,CAAC,cAAc,CAAC;AAW3C,SAASC,oBAAoBC,OAAe;IAC1C,MAAMC,oBAAoBC,QAAcC,IAAI,IAAIC,IAAAA,iBAAgB;IAEhE,IAAIH,mBAAmB;QACrB,OAAOI,aAAI,CAACC,IAAI,CAACN,SAAS;IAC5B;IAEA,OAAOO;AACT;AAEO,MAAMb;IAWXc,YAAY,EAAER,OAAO,EAAuB,CAAE;aAsBtCS,SAAS;YACf,IAAI,IAAI,CAACC,UAAU,IAAI,CAAC,IAAI,CAACC,IAAI,EAAE;gBACjC;YACF;YAEA,6EAA6E;YAC7E,gDAAgD;YAChD,yEAAyE;YACzE,aAAa;YACb,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,CAAChB,2BAA2B,KAAK;gBAChD;YACF;YACA,IAAI,CAACe,IAAI,CAACE,GAAG,CAACjB,2BAA2BkB,KAAKC,GAAG,GAAGC,QAAQ;YAE5DC,QAAQC,GAAG,CACT,GAAGC,IAAAA,mBAAO,EACRC,IAAAA,gBAAI,EAAC,cACL,sEAAsE,CAAC;YAE3EH,QAAQC,GAAG,CACT,CAAC,2EAA2E,CAAC;YAE/ED,QAAQC,GAAG,CACT,CAAC,uIAAuI,CAAC;YAE3ID,QAAQC,GAAG,CAACG,IAAAA,gBAAI,EAAC;YACjBJ,QAAQC,GAAG;QACb;aA+BAI,aAAa,CAACC;YACZ,MAAMC,UAAU,CAAC,CAACD;YAClB,IAAI,CAACZ,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAAClB,uBAAuB6B;YAClD,OAAO,IAAI,CAACb,IAAI,IAAI,IAAI,CAACA,IAAI,CAACN,IAAI;QACpC;aAUAoB,aAAa,CAACC;YACZ,MAAMC,OAAOC,IAAAA,kBAAU,EAAC;YAExB,6EAA6E;YAC7E,WAAW;YACXD,KAAKE,MAAM,CAAC,IAAI,CAACC,IAAI;YAErB,4EAA4E;YAC5E,2BAA2B;YAC3BH,KAAKE,MAAM,CAACH;YACZ,OAAOC,KAAKI,MAAM,CAAC;QACrB;aAOAC,SAAS,CACPC,SACAC;YAEA,MAAMC,OAAO,AACXD,CAAAA,WAEI,iDAAiD;YACjD,4CAA4C;YAC5C,IAAIE,QAAQ,CAACC,UACXA,QAAQ;oBACNC,aAAa;oBACbC,YAAY;oBACZC,OAAOP;gBACT,MAEF,IAAI,CAACQ,YAAY,CAACR,QAAO,EAE5BS,IAAI,CAAC,CAACF,QAAW,CAAA;oBAChBF,aAAa;oBACbC,YAAY;oBACZC;gBACF,CAAA,GACCG,KAAK,CAAC,CAACC,SAAY,CAAA;oBAClBN,aAAa;oBACbC,YAAY;oBACZK;gBACF,CAAA,EACA,iEAAiE;aAChEF,IAAI,CAAC,CAACG;gBACL,uDAAuD;gBACvD,IAAI,CAACX,UAAU;oBACb,IAAI,CAACY,KAAK,CAACC,MAAM,CAACZ;gBACpB;gBACA,OAAOU;YACT;YAEAV,KAAaF,OAAO,GAAGe,MAAMC,OAAO,CAAChB,WAAWA,UAAU;gBAACA;aAAQ;YACnEE,KAAae,WAAW,GAAG,AAACf,KAAae,WAAW;YACtD,sDAAsD;YACtD,IAAI,CAACJ,KAAK,CAACK,GAAG,CAAChB;YAEf,OAAOA;QACT;aAEAiB,QAAQ,UAAYhB,QAAQiB,GAAG,CAAC,IAAI,CAACP,KAAK,EAAEH,KAAK,CAAC,IAAM;QAExD,oDAAoD;QACpD,0DAA0D;QAC1D,gCAAgC;aAChCW,gBAAgB,CAACC,MAAaC;YAC5B,MAAMC,YAA8B,EAAE;YAEtC,IAAI,CAACX,KAAK,CAACY,OAAO,CAAC,CAACC;gBAClB,IAAI;wBACFA;qBAAAA,oBAAAA,KAAKT,WAAW,qBAAhBS,kBAAkBC,KAAK;oBACvBH,UAAUI,IAAI,IAAIF,KAAK1B,OAAO;gBAChC,EAAE,OAAO6B,GAAG;gBACV,wCAAwC;gBAC1C;YACF;YACAC,WAAE,CAACC,SAAS,CAAC,IAAI,CAAChE,OAAO,EAAE;gBAAEiE,WAAW;YAAK;YAC7CF,WAAE,CAACG,aAAa,CACd7D,aAAI,CAACC,IAAI,CAAC,IAAI,CAACN,OAAO,EAAE,iBACxBmE,KAAKC,SAAS,CAACX;YAGjB,kDAAkD;YAClD,0DAA0D;YAC1D,MAAMY,gBACJC,QAAQ;YAEV,2DAA2D;YAC3D,6BAA6B;YAC7B,MAAMC,QAAQ,IAAI,CAACC,oBAAoB,GACnCH,cAAcI,SAAS,GACvBJ,cAAcE,KAAK;YAEvBA,MAAMG,QAAQC,QAAQ,EAAE;gBAACL,QAAQjC,OAAO,CAAC;gBAAqBkB;gBAAMC;aAAI,EAAE;gBACxEoB,UAAU,CAAC,IAAI,CAACJ,oBAAoB;gBACpCK,aAAa;gBACbC,OAAO;gBACP,GAAI,IAAI,CAACN,oBAAoB,GACzB;oBACEO,OAAO;gBACT,IACA,CAAC,CAAC;YACR;QACF;aAEQtC,eAAe,OACrBR;YAEA,IAAI+C;YACJ,IAAIhC,MAAMC,OAAO,CAAChB,UAAU;gBAC1B+C,SAAS/C;YACX,OAAO;gBACL+C,SAAS;oBAAC/C;iBAAQ;YACpB;YAEA,IAAI+C,OAAOC,MAAM,GAAG,GAAG;gBACrB,OAAO7C,QAAQC,OAAO;YACxB;YAEA,IAAI,IAAI,CAACmC,oBAAoB,EAAE;gBAC7B,2DAA2D;gBAC3DQ,OAAOtB,OAAO,CAAC,CAAC,EAAEwB,SAAS,EAAExD,OAAO,EAAE,GACpCT,QAAQkE,KAAK,CACX,CAAC,YAAY,CAAC,GAAGhB,KAAKC,SAAS,CAAC;wBAAEc;wBAAWxD;oBAAQ,GAAG,MAAM;gBAGlE,0EAA0E;gBAC1E,sCAAsC;gBACtC,OAAOU,QAAQC,OAAO;YACxB;YAEA,sDAAsD;YACtD,IAAI,IAAI,CAAC3B,UAAU,EAAE;gBACnB,OAAO0B,QAAQC,OAAO;YACxB;YAEA,MAAM+C,iBAAiB,IAAIC,yBAAe;YAC1C,MAAMxC,MAAMyC,IAAAA,8CAAwB,EAClC;gBACEC,SAAS;oBACPC,aAAa,IAAI,CAACA,WAAW;oBAC7BC,WAAW,MAAM,IAAI,CAACC,YAAY;oBAClCC,WAAW,IAAI,CAACA,SAAS;gBAC3B;gBACAC,MAAMC,IAAAA,+BAAgB;gBACtBb,QAAQA,OAAOc,GAAG,CAAC,CAAC,EAAEZ,SAAS,EAAExD,OAAO,EAAE,GAAM,CAAA;wBAC9CwD;wBACAa,QAAQrE;oBACV,CAAA;YACF,GACA0D,eAAeY,MAAM;YAEvBnD,IAAIK,WAAW,GAAGkC;YAClB,OAAOvC;QACT;QA1PE,oEAAoE;QACpE,MAAM,EAAEoD,uBAAuB,EAAEzB,oBAAoB,EAAE,GAAGE,QAAQwB,GAAG;QACrE,IAAI,CAACD,uBAAuB,GAAGA;QAC/B,IAAI,CAACzB,oBAAoB,GAAGA;QAC5B,IAAI,CAACxE,OAAO,GAAGA;QACf,MAAMmG,mBAAmBpG,oBAAoBC;QAE7C,IAAI;YACF,qEAAqE;YACrE,wEAAwE;YACxE,qBAAqB;YACrB,IAAI,CAACW,IAAI,GAAG,IAAIyF,aAAI,CAAC;gBAAEC,aAAa;gBAAUC,KAAKH;YAAiB;QACtE,EAAE,OAAOrC,GAAG;YACV,IAAI,CAACnD,IAAI,GAAG;QACd;QACA,IAAI,CAACgF,SAAS,GAAGY,IAAAA,mBAAW,EAAC,IAAIvF,QAAQ,CAAC;QAC1C,IAAI,CAAC8B,KAAK,GAAG,IAAI0D;QAEjB,IAAI,CAAC/F,MAAM;IACb;IA+BA,IAAI+E,cAAsB;QACxB,MAAMiB,MAAM,IAAI,CAAC9F,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,CAACf;QACvC,IAAI4G,KAAK;YACP,OAAOA;QACT;QAEA,MAAMC,YAAYH,IAAAA,mBAAW,EAAC,IAAIvF,QAAQ,CAAC;QAC3C,IAAI,CAACL,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAAChB,kBAAkB6G;QAC7C,OAAOA;IACT;IAEA,IAAI5E,OAAe;QACjB,MAAM2E,MAAM,IAAI,CAAC9F,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,CAACd;QACvC,IAAI2G,KAAK;YACP,OAAOA;QACT;QAEA,MAAMC,YAAYH,IAAAA,mBAAW,EAAC,IAAIvF,QAAQ,CAAC;QAC3C,IAAI,CAACL,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAACf,oBAAoB4G;QAC/C,OAAOA;IACT;IAEA,IAAYhG,aAAsB;QAChC,IAAI,CAAC,CAAC,IAAI,CAACuF,uBAAuB,IAAI,CAAC,IAAI,CAACtF,IAAI,EAAE;YAChD,OAAO;QACT;QACA,OAAO,IAAI,CAACA,IAAI,CAACC,GAAG,CAACjB,uBAAuB,UAAU;IACxD;IAQA,IAAIgH,YAAqB;QACvB,OACE,CAAC,IAAI,CAACV,uBAAuB,IAC7B,CAAC,CAAC,IAAI,CAACtF,IAAI,IACX,IAAI,CAACA,IAAI,CAACC,GAAG,CAACjB,uBAAuB,UAAU;IAEnD;IAeA,MAAc+F,eAAgC;QAC5C,IAAI,CAACkB,aAAa,GAAG,IAAI,CAACA,aAAa,IAAIC,IAAAA,0BAAe;QAC1D,OAAO,IAAI,CAACpF,UAAU,CAAC,MAAM,IAAI,CAACmF,aAAa;IACjD;AA8IF"}