{"version": 3, "sources": ["../../../src/telemetry/events/session-stopped.ts"], "sourcesContent": ["const EVENT_VERSION = 'NEXT_CLI_SESSION_STOPPED'\n\nexport type EventCliSessionStopped = {\n  cliCommand: string\n  nextVersion: string\n  nodeVersion: string\n  turboFlag?: boolean | null\n  durationMilliseconds?: number | null\n  pagesDir?: boolean\n  appDir?: boolean\n}\n\nexport function eventCliSessionStopped(\n  event: Omit<EventCliSessionStopped, 'nextVersion' | 'nodeVersion'>\n): { eventName: string; payload: EventCliSessionStopped }[] {\n  // This should be an invariant, if it fails our build tooling is broken.\n  if (typeof process.env.__NEXT_VERSION !== 'string') {\n    return []\n  }\n\n  const payload: EventCliSessionStopped = {\n    nextVersion: process.env.__NEXT_VERSION,\n    nodeVersion: process.version,\n    cliCommand: event.cliCommand,\n    durationMilliseconds: event.durationMilliseconds,\n    ...(typeof event.turboFlag !== 'undefined'\n      ? {\n          turboFlag: !!event.turboFlag,\n        }\n      : {}),\n    pagesDir: event.pagesDir,\n    appDir: event.appDir,\n  }\n  return [{ eventName: EVENT_VERSION, payload }]\n}\n"], "names": ["eventCliSessionStopped", "EVENT_VERSION", "event", "process", "env", "__NEXT_VERSION", "payload", "nextVersion", "nodeVersion", "version", "cliCommand", "durationMilliseconds", "turboFlag", "pagesDir", "appDir", "eventName"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;AAZhB,MAAMC,gBAAgB;AAYf,SAASD,uBACdE,KAAkE;IAElE,wEAAwE;IACxE,IAAI,OAAOC,QAAQC,GAAG,CAACC,cAAc,KAAK,UAAU;QAClD,OAAO,EAAE;IACX;IAEA,MAAMC,UAAkC;QACtCC,aAAaJ,QAAQC,GAAG,CAACC,cAAc;QACvCG,aAAaL,QAAQM,OAAO;QAC5BC,YAAYR,MAAMQ,UAAU;QAC5BC,sBAAsBT,MAAMS,oBAAoB;QAChD,GAAI,OAAOT,MAAMU,SAAS,KAAK,cAC3B;YACEA,WAAW,CAAC,CAACV,MAAMU,SAAS;QAC9B,IACA,CAAC,CAAC;QACNC,UAAUX,MAAMW,QAAQ;QACxBC,QAAQZ,MAAMY,MAAM;IACtB;IACA,OAAO;QAAC;YAAEC,WAAWd;YAAeK;QAAQ;KAAE;AAChD"}