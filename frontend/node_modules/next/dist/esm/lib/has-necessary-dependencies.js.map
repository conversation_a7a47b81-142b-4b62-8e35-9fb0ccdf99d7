{"version": 3, "sources": ["../../src/lib/has-necessary-dependencies.ts"], "sourcesContent": ["import { existsSync, promises as fs } from 'fs'\nimport { resolveFrom } from './resolve-from'\nimport { dirname, join, relative } from 'path'\n\nexport interface MissingDependency {\n  file: string\n  /**\n   * The package's package.json (e.g. require(`${pkg}/package.json`)) MUST resolve.\n   * If `exportsRestrict` is false, `${file}` MUST also resolve.\n   */\n  pkg: string\n  /**\n   * If true, the pkg's package.json needs to be resolvable.\n   * If true, will resolve `file` relative to the real path of the package.json.\n   *\n   * For example, `{ file: '@types/react/index.d.ts', pkg: '@types/react', exportsRestrict: true }`\n   * will try to resolve '@types/react/package.json' first and then assume `@types/react/index.d.ts`\n   * resolves to `path.join(dirname(resolvedPackageJsonPath), 'index.d.ts')`.\n   *\n   * If false, will resolve `file` relative to the baseDir.\n   * ForFor example, `{ file: '@types/react/index.d.ts', pkg: '@types/react', exportsRestrict: true }`\n   * will try to resolve `@types/react/index.d.ts` directly.\n   */\n  exportsRestrict: boolean\n}\n\nexport type NecessaryDependencies = {\n  resolved: Map<string, string>\n  missing: MissingDependency[]\n}\n\nexport async function hasNecessaryDependencies(\n  baseDir: string,\n  requiredPackages: MissingDependency[]\n): Promise<NecessaryDependencies> {\n  let resolutions = new Map<string, string>()\n  const missingPackages: MissingDependency[] = []\n\n  await Promise.all(\n    requiredPackages.map(async (p) => {\n      try {\n        const pkgPath = await fs.realpath(\n          resolveFrom(baseDir, `${p.pkg}/package.json`)\n        )\n        const pkgDir = dirname(pkgPath)\n\n        if (p.exportsRestrict) {\n          const fileNameToVerify = relative(p.pkg, p.file)\n          if (fileNameToVerify) {\n            const fileToVerify = join(pkgDir, fileNameToVerify)\n            if (existsSync(fileToVerify)) {\n              resolutions.set(p.pkg, fileToVerify)\n            } else {\n              return missingPackages.push(p)\n            }\n          } else {\n            resolutions.set(p.pkg, pkgPath)\n          }\n        } else {\n          resolutions.set(p.pkg, resolveFrom(baseDir, p.file))\n        }\n      } catch (_) {\n        return missingPackages.push(p)\n      }\n    })\n  )\n\n  return {\n    resolved: resolutions,\n    missing: missingPackages,\n  }\n}\n"], "names": ["existsSync", "promises", "fs", "resolveFrom", "dirname", "join", "relative", "hasNecessaryDependencies", "baseDir", "requiredPackages", "resolutions", "Map", "missingPackages", "Promise", "all", "map", "p", "pkgPath", "realpath", "pkg", "pkgDir", "exportsRestrict", "fileNameToVerify", "file", "fileToVerify", "set", "push", "_", "resolved", "missing"], "mappings": "AAAA,SAASA,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,OAAM;AA6B9C,OAAO,eAAeC,yBACpBC,OAAe,EACfC,gBAAqC;IAErC,IAAIC,cAAc,IAAIC;IACtB,MAAMC,kBAAuC,EAAE;IAE/C,MAAMC,QAAQC,GAAG,CACfL,iBAAiBM,GAAG,CAAC,OAAOC;QAC1B,IAAI;YACF,MAAMC,UAAU,MAAMf,GAAGgB,QAAQ,CAC/Bf,YAAYK,SAAS,GAAGQ,EAAEG,GAAG,CAAC,aAAa,CAAC;YAE9C,MAAMC,SAAShB,QAAQa;YAEvB,IAAID,EAAEK,eAAe,EAAE;gBACrB,MAAMC,mBAAmBhB,SAASU,EAAEG,GAAG,EAAEH,EAAEO,IAAI;gBAC/C,IAAID,kBAAkB;oBACpB,MAAME,eAAenB,KAAKe,QAAQE;oBAClC,IAAItB,WAAWwB,eAAe;wBAC5Bd,YAAYe,GAAG,CAACT,EAAEG,GAAG,EAAEK;oBACzB,OAAO;wBACL,OAAOZ,gBAAgBc,IAAI,CAACV;oBAC9B;gBACF,OAAO;oBACLN,YAAYe,GAAG,CAACT,EAAEG,GAAG,EAAEF;gBACzB;YACF,OAAO;gBACLP,YAAYe,GAAG,CAACT,EAAEG,GAAG,EAAEhB,YAAYK,SAASQ,EAAEO,IAAI;YACpD;QACF,EAAE,OAAOI,GAAG;YACV,OAAOf,gBAAgBc,IAAI,CAACV;QAC9B;IACF;IAGF,OAAO;QACLY,UAAUlB;QACVmB,SAASjB;IACX;AACF"}