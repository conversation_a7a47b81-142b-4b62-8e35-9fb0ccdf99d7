{"version": 3, "sources": ["../../src/lib/try-to-parse-path.ts"], "sourcesContent": ["import type { Token } from 'next/dist/compiled/path-to-regexp'\nimport { parse, tokensToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport { parse as parseURL } from 'url'\nimport isError from './is-error'\n\ninterface ParseResult {\n  error?: any\n  parsedPath: string\n  regexStr?: string\n  route: string\n  tokens?: Token[]\n}\n\n/**\n * If there is an error show our error link but still show original error or\n * a formatted one if we can\n */\nfunction reportError({ route, parsedPath }: ParseResult, err: any) {\n  let errMatches\n  if (isError(err) && (errMatches = err.message.match(/at (\\d{0,})/))) {\n    const position = parseInt(errMatches[1], 10)\n    console.error(\n      `\\nError parsing \\`${route}\\` ` +\n        `https://nextjs.org/docs/messages/invalid-route-source\\n` +\n        `Reason: ${err.message}\\n\\n` +\n        `  ${parsedPath}\\n` +\n        `  ${new Array(position).fill(' ').join('')}^\\n`\n    )\n  } else {\n    console.error(\n      `\\nError parsing ${route} https://nextjs.org/docs/messages/invalid-route-source`,\n      err\n    )\n  }\n}\n\n/**\n * Attempts to parse a given route with `path-to-regexp` and returns an object\n * with the result. Whenever an error happens on parse, it will print an error\n * attempting to find the error position and showing a link to the docs. When\n * `handleUrl` is set to `true` it will also attempt to parse the route\n * and use the resulting pathname to parse with `path-to-regexp`.\n */\nexport function tryToParsePath(\n  route: string,\n  options?: {\n    handleUrl?: boolean\n  }\n): ParseResult {\n  const result: ParseResult = { route, parsedPath: route }\n  try {\n    if (options?.handleUrl) {\n      const parsed = parseURL(route, true)\n      result.parsedPath = `${parsed.pathname!}${parsed.hash || ''}`\n    }\n\n    result.tokens = parse(result.parsedPath)\n    result.regexStr = tokensToRegexp(result.tokens).source\n  } catch (err) {\n    reportError(result, err)\n    result.error = err\n  }\n\n  return result\n}\n"], "names": ["parse", "tokensToRegexp", "parseURL", "isError", "reportError", "route", "parsed<PERSON><PERSON>", "err", "err<PERSON><PERSON><PERSON>", "message", "match", "position", "parseInt", "console", "error", "Array", "fill", "join", "tryToParsePath", "options", "result", "handleUrl", "parsed", "pathname", "hash", "tokens", "regexStr", "source"], "mappings": "AACA,SAASA,KAAK,EAAEC,cAAc,QAAQ,oCAAmC;AACzE,SAASD,SAASE,QAAQ,QAAQ,MAAK;AACvC,OAAOC,aAAa,aAAY;AAUhC;;;CAGC,GACD,SAASC,YAAY,EAAEC,KAAK,EAAEC,UAAU,EAAe,EAAEC,GAAQ;IAC/D,IAAIC;IACJ,IAAIL,QAAQI,QAASC,CAAAA,aAAaD,IAAIE,OAAO,CAACC,KAAK,CAAC,cAAa,GAAI;QACnE,MAAMC,WAAWC,SAASJ,UAAU,CAAC,EAAE,EAAE;QACzCK,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAET,MAAM,GAAG,CAAC,GAC7B,CAAC,uDAAuD,CAAC,GACzD,CAAC,QAAQ,EAAEE,IAAIE,OAAO,CAAC,IAAI,CAAC,GAC5B,CAAC,EAAE,EAAEH,WAAW,EAAE,CAAC,GACnB,CAAC,EAAE,EAAE,IAAIS,MAAMJ,UAAUK,IAAI,CAAC,KAAKC,IAAI,CAAC,IAAI,GAAG,CAAC;IAEtD,OAAO;QACLJ,QAAQC,KAAK,CACX,CAAC,gBAAgB,EAAET,MAAM,sDAAsD,CAAC,EAChFE;IAEJ;AACF;AAEA;;;;;;CAMC,GACD,OAAO,SAASW,eACdb,KAAa,EACbc,OAEC;IAED,MAAMC,SAAsB;QAAEf;QAAOC,YAAYD;IAAM;IACvD,IAAI;QACF,IAAIc,2BAAAA,QAASE,SAAS,EAAE;YACtB,MAAMC,SAASpB,SAASG,OAAO;YAC/Be,OAAOd,UAAU,GAAG,GAAGgB,OAAOC,QAAQ,GAAID,OAAOE,IAAI,IAAI,IAAI;QAC/D;QAEAJ,OAAOK,MAAM,GAAGzB,MAAMoB,OAAOd,UAAU;QACvCc,OAAOM,QAAQ,GAAGzB,eAAemB,OAAOK,MAAM,EAAEE,MAAM;IACxD,EAAE,OAAOpB,KAAK;QACZH,YAAYgB,QAAQb;QACpBa,OAAON,KAAK,GAAGP;IACjB;IAEA,OAAOa;AACT"}