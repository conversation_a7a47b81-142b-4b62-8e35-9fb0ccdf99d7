{"version": 3, "sources": ["../../src/lib/recursive-delete.ts"], "sourcesContent": ["import type { Dirent } from 'fs'\nimport { promises } from 'fs'\nimport { join, isAbsolute, dirname } from 'path'\nimport isError from './is-error'\nimport { wait } from './wait'\n\nconst unlinkPath = async (p: string, isDir = false, t = 1): Promise<void> => {\n  try {\n    if (isDir) {\n      await promises.rmdir(p)\n    } else {\n      await promises.unlink(p)\n    }\n  } catch (e) {\n    const code = isError(e) && e.code\n    if (\n      (code === 'EBUSY' ||\n        code === 'ENOTEMPTY' ||\n        code === 'EPERM' ||\n        code === 'EMFILE') &&\n      t < 3\n    ) {\n      await wait(t * 100)\n      return unlinkPath(p, isDir, t++)\n    }\n\n    if (code === 'ENOENT') {\n      return\n    }\n\n    throw e\n  }\n}\n\n/**\n * Recursively delete directory contents\n */\nexport async function recursiveDelete(\n  /** Directory to delete the contents of */\n  dir: string,\n  /** Exclude based on relative file path */\n  exclude?: RegExp,\n  /** Ensures that parameter dir exists, this is not passed recursively */\n  previousPath: string = ''\n): Promise<void> {\n  let result\n  try {\n    result = await promises.readdir(dir, { withFileTypes: true })\n  } catch (e) {\n    if (isError(e) && e.code === 'ENOENT') {\n      return\n    }\n    throw e\n  }\n\n  await Promise.all(\n    result.map(async (part: Dirent) => {\n      const absolutePath = join(dir, part.name)\n\n      // readdir does not follow symbolic links\n      // if part is a symbolic link, follow it using stat\n      let isDirectory = part.isDirectory()\n      const isSymlink = part.isSymbolicLink()\n\n      if (isSymlink) {\n        const linkPath = await promises.readlink(absolutePath)\n\n        try {\n          const stats = await promises.stat(\n            isAbsolute(linkPath)\n              ? linkPath\n              : join(dirname(absolutePath), linkPath)\n          )\n          isDirectory = stats.isDirectory()\n        } catch {}\n      }\n\n      const pp = join(previousPath, part.name)\n      const isNotExcluded = !exclude || !exclude.test(pp)\n\n      if (isNotExcluded) {\n        if (isDirectory) {\n          await recursiveDelete(absolutePath, exclude, pp)\n        }\n        return unlinkPath(absolutePath, !isSymlink && isDirectory)\n      }\n    })\n  )\n}\n"], "names": ["promises", "join", "isAbsolute", "dirname", "isError", "wait", "unlinkPath", "p", "isDir", "t", "rmdir", "unlink", "e", "code", "recursiveDelete", "dir", "exclude", "previousPath", "result", "readdir", "withFileTypes", "Promise", "all", "map", "part", "absolutePath", "name", "isDirectory", "isSymlink", "isSymbolicLink", "linkPath", "readlink", "stats", "stat", "pp", "isNotExcluded", "test"], "mappings": "AACA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,SAASC,IAAI,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAM;AAChD,OAAOC,aAAa,aAAY;AAChC,SAASC,IAAI,QAAQ,SAAQ;AAE7B,MAAMC,aAAa,OAAOC,GAAWC,QAAQ,KAAK,EAAEC,IAAI,CAAC;IACvD,IAAI;QACF,IAAID,OAAO;YACT,MAAMR,SAASU,KAAK,CAACH;QACvB,OAAO;YACL,MAAMP,SAASW,MAAM,CAACJ;QACxB;IACF,EAAE,OAAOK,GAAG;QACV,MAAMC,OAAOT,QAAQQ,MAAMA,EAAEC,IAAI;QACjC,IACE,AAACA,CAAAA,SAAS,WACRA,SAAS,eACTA,SAAS,WACTA,SAAS,QAAO,KAClBJ,IAAI,GACJ;YACA,MAAMJ,KAAKI,IAAI;YACf,OAAOH,WAAWC,GAAGC,OAAOC;QAC9B;QAEA,IAAII,SAAS,UAAU;YACrB;QACF;QAEA,MAAMD;IACR;AACF;AAEA;;CAEC,GACD,OAAO,eAAeE,gBACpB,wCAAwC,GACxCC,GAAW,EACX,wCAAwC,GACxCC,OAAgB,EAChB,sEAAsE,GACtEC,eAAuB,EAAE;IAEzB,IAAIC;IACJ,IAAI;QACFA,SAAS,MAAMlB,SAASmB,OAAO,CAACJ,KAAK;YAAEK,eAAe;QAAK;IAC7D,EAAE,OAAOR,GAAG;QACV,IAAIR,QAAQQ,MAAMA,EAAEC,IAAI,KAAK,UAAU;YACrC;QACF;QACA,MAAMD;IACR;IAEA,MAAMS,QAAQC,GAAG,CACfJ,OAAOK,GAAG,CAAC,OAAOC;QAChB,MAAMC,eAAexB,KAAKc,KAAKS,KAAKE,IAAI;QAExC,yCAAyC;QACzC,mDAAmD;QACnD,IAAIC,cAAcH,KAAKG,WAAW;QAClC,MAAMC,YAAYJ,KAAKK,cAAc;QAErC,IAAID,WAAW;YACb,MAAME,WAAW,MAAM9B,SAAS+B,QAAQ,CAACN;YAEzC,IAAI;gBACF,MAAMO,QAAQ,MAAMhC,SAASiC,IAAI,CAC/B/B,WAAW4B,YACPA,WACA7B,KAAKE,QAAQsB,eAAeK;gBAElCH,cAAcK,MAAML,WAAW;YACjC,EAAE,OAAM,CAAC;QACX;QAEA,MAAMO,KAAKjC,KAAKgB,cAAcO,KAAKE,IAAI;QACvC,MAAMS,gBAAgB,CAACnB,WAAW,CAACA,QAAQoB,IAAI,CAACF;QAEhD,IAAIC,eAAe;YACjB,IAAIR,aAAa;gBACf,MAAMb,gBAAgBW,cAAcT,SAASkB;YAC/C;YACA,OAAO5B,WAAWmB,cAAc,CAACG,aAAaD;QAChD;IACF;AAEJ"}