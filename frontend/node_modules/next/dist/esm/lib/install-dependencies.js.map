{"version": 3, "sources": ["../../src/lib/install-dependencies.ts"], "sourcesContent": ["import { cyan } from './picocolors'\nimport path from 'path'\n\nimport type { MissingDependency } from './has-necessary-dependencies'\nimport { getPkgManager } from './helpers/get-pkg-manager'\nimport { install } from './helpers/install'\nimport { getOnline } from './helpers/get-online'\n\nexport type Dependencies = {\n  resolved: Map<string, string>\n}\n\nexport async function installDependencies(\n  baseDir: string,\n  deps: any,\n  dev: boolean = false\n) {\n  const packageManager = getPkgManager(baseDir)\n  const isOnline = await getOnline()\n\n  if (deps.length) {\n    console.log()\n    console.log(\n      `Installing ${\n        dev ? 'devDependencies' : 'dependencies'\n      } (${packageManager}):`\n    )\n    for (const dep of deps) {\n      console.log(`- ${cyan(dep.pkg)}`)\n    }\n    console.log()\n\n    await install(\n      path.resolve(baseDir),\n      deps.map((dep: MissingDependency) => dep.pkg),\n      { devDependencies: dev, isOnline, packageManager }\n    )\n    console.log()\n  }\n}\n"], "names": ["cyan", "path", "getPkgManager", "install", "getOnline", "installDependencies", "baseDir", "deps", "dev", "packageManager", "isOnline", "length", "console", "log", "dep", "pkg", "resolve", "map", "devDependencies"], "mappings": "AAAA,SAASA,IAAI,QAAQ,eAAc;AACnC,OAAOC,UAAU,OAAM;AAGvB,SAASC,aAAa,QAAQ,4BAA2B;AACzD,SAASC,OAAO,QAAQ,oBAAmB;AAC3C,SAASC,SAAS,QAAQ,uBAAsB;AAMhD,OAAO,eAAeC,oBACpBC,OAAe,EACfC,IAAS,EACTC,MAAe,KAAK;IAEpB,MAAMC,iBAAiBP,cAAcI;IACrC,MAAMI,WAAW,MAAMN;IAEvB,IAAIG,KAAKI,MAAM,EAAE;QACfC,QAAQC,GAAG;QACXD,QAAQC,GAAG,CACT,CAAC,WAAW,EACVL,MAAM,oBAAoB,eAC3B,EAAE,EAAEC,eAAe,EAAE,CAAC;QAEzB,KAAK,MAAMK,OAAOP,KAAM;YACtBK,QAAQC,GAAG,CAAC,CAAC,EAAE,EAAEb,KAAKc,IAAIC,GAAG,GAAG;QAClC;QACAH,QAAQC,GAAG;QAEX,MAAMV,QACJF,KAAKe,OAAO,CAACV,UACbC,KAAKU,GAAG,CAAC,CAACH,MAA2BA,IAAIC,GAAG,GAC5C;YAAEG,iBAAiBV;YAAKE;YAAUD;QAAe;QAEnDG,QAAQC,GAAG;IACb;AACF"}