{"version": 3, "sources": ["../../../../src/lib/metadata/generate/opengraph.tsx"], "sourcesContent": ["import type { ResolvedMetadata } from '../types/metadata-interface'\nimport type { TwitterAppDescriptor } from '../types/twitter-types'\n\nimport { Meta, MetaFilter, MultiMeta } from './meta'\n\nexport function OpenGraphMetadata({\n  openGraph,\n}: {\n  openGraph: ResolvedMetadata['openGraph']\n}) {\n  if (!openGraph) {\n    return null\n  }\n\n  let typedOpenGraph\n  if ('type' in openGraph) {\n    const openGraphType = openGraph.type\n    switch (openGraphType) {\n      case 'website':\n        typedOpenGraph = [Meta({ property: 'og:type', content: 'website' })]\n        break\n      case 'article':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'article' }),\n          Meta({\n            property: 'article:published_time',\n            content: openGraph.publishedTime?.toString(),\n          }),\n          Meta({\n            property: 'article:modified_time',\n            content: openGraph.modifiedTime?.toString(),\n          }),\n          Meta({\n            property: 'article:expiration_time',\n            content: openGraph.expirationTime?.toString(),\n          }),\n          MultiMeta({\n            propertyPrefix: 'article:author',\n            contents: openGraph.authors,\n          }),\n          Meta({ property: 'article:section', content: openGraph.section }),\n          MultiMeta({\n            propertyPrefix: 'article:tag',\n            contents: openGraph.tags,\n          }),\n        ]\n        break\n      case 'book':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'book' }),\n          Meta({ property: 'book:isbn', content: openGraph.isbn }),\n          Meta({\n            property: 'book:release_date',\n            content: openGraph.releaseDate,\n          }),\n          MultiMeta({\n            propertyPrefix: 'book:author',\n            contents: openGraph.authors,\n          }),\n          MultiMeta({ propertyPrefix: 'book:tag', contents: openGraph.tags }),\n        ]\n        break\n      case 'profile':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'profile' }),\n          Meta({\n            property: 'profile:first_name',\n            content: openGraph.firstName,\n          }),\n          Meta({ property: 'profile:last_name', content: openGraph.lastName }),\n          Meta({ property: 'profile:username', content: openGraph.username }),\n          Meta({ property: 'profile:gender', content: openGraph.gender }),\n        ]\n        break\n      case 'music.song':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'music.song' }),\n          Meta({\n            property: 'music:duration',\n            content: openGraph.duration?.toString(),\n          }),\n          MultiMeta({\n            propertyPrefix: 'music:album',\n            contents: openGraph.albums,\n          }),\n          MultiMeta({\n            propertyPrefix: 'music:musician',\n            contents: openGraph.musicians,\n          }),\n        ]\n        break\n      case 'music.album':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'music.album' }),\n          MultiMeta({\n            propertyPrefix: 'music:song',\n            contents: openGraph.songs,\n          }),\n          MultiMeta({\n            propertyPrefix: 'music:musician',\n            contents: openGraph.musicians,\n          }),\n          Meta({\n            property: 'music:release_date',\n            content: openGraph.releaseDate,\n          }),\n        ]\n        break\n      case 'music.playlist':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'music.playlist' }),\n          MultiMeta({\n            propertyPrefix: 'music:song',\n            contents: openGraph.songs,\n          }),\n          MultiMeta({\n            propertyPrefix: 'music:creator',\n            contents: openGraph.creators,\n          }),\n        ]\n        break\n      case 'music.radio_station':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'music.radio_station' }),\n          MultiMeta({\n            propertyPrefix: 'music:creator',\n            contents: openGraph.creators,\n          }),\n        ]\n        break\n\n      case 'video.movie':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'video.movie' }),\n          MultiMeta({\n            propertyPrefix: 'video:actor',\n            contents: openGraph.actors,\n          }),\n          MultiMeta({\n            propertyPrefix: 'video:director',\n            contents: openGraph.directors,\n          }),\n          MultiMeta({\n            propertyPrefix: 'video:writer',\n            contents: openGraph.writers,\n          }),\n          Meta({ property: 'video:duration', content: openGraph.duration }),\n          Meta({\n            property: 'video:release_date',\n            content: openGraph.releaseDate,\n          }),\n          MultiMeta({ propertyPrefix: 'video:tag', contents: openGraph.tags }),\n        ]\n        break\n      case 'video.episode':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'video.episode' }),\n          MultiMeta({\n            propertyPrefix: 'video:actor',\n            contents: openGraph.actors,\n          }),\n          MultiMeta({\n            propertyPrefix: 'video:director',\n            contents: openGraph.directors,\n          }),\n          MultiMeta({\n            propertyPrefix: 'video:writer',\n            contents: openGraph.writers,\n          }),\n          Meta({ property: 'video:duration', content: openGraph.duration }),\n          Meta({\n            property: 'video:release_date',\n            content: openGraph.releaseDate,\n          }),\n          MultiMeta({ propertyPrefix: 'video:tag', contents: openGraph.tags }),\n          Meta({ property: 'video:series', content: openGraph.series }),\n        ]\n        break\n      case 'video.tv_show':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'video.tv_show' }),\n        ]\n        break\n      case 'video.other':\n        typedOpenGraph = [Meta({ property: 'og:type', content: 'video.other' })]\n        break\n\n      default:\n        const _exhaustiveCheck: never = openGraphType\n        throw new Error(`Invalid OpenGraph type: ${_exhaustiveCheck}`)\n    }\n  }\n\n  return MetaFilter([\n    Meta({ property: 'og:determiner', content: openGraph.determiner }),\n    Meta({ property: 'og:title', content: openGraph.title?.absolute }),\n    Meta({ property: 'og:description', content: openGraph.description }),\n    Meta({ property: 'og:url', content: openGraph.url?.toString() }),\n    Meta({ property: 'og:site_name', content: openGraph.siteName }),\n    Meta({ property: 'og:locale', content: openGraph.locale }),\n    Meta({ property: 'og:country_name', content: openGraph.countryName }),\n    Meta({ property: 'og:ttl', content: openGraph.ttl?.toString() }),\n    MultiMeta({ propertyPrefix: 'og:image', contents: openGraph.images }),\n    MultiMeta({ propertyPrefix: 'og:video', contents: openGraph.videos }),\n    MultiMeta({ propertyPrefix: 'og:audio', contents: openGraph.audio }),\n    MultiMeta({ propertyPrefix: 'og:email', contents: openGraph.emails }),\n    MultiMeta({\n      propertyPrefix: 'og:phone_number',\n      contents: openGraph.phoneNumbers,\n    }),\n    MultiMeta({\n      propertyPrefix: 'og:fax_number',\n      contents: openGraph.faxNumbers,\n    }),\n    MultiMeta({\n      propertyPrefix: 'og:locale:alternate',\n      contents: openGraph.alternateLocale,\n    }),\n    ...(typedOpenGraph ? typedOpenGraph : []),\n  ])\n}\n\nfunction TwitterAppItem({\n  app,\n  type,\n}: {\n  app: TwitterAppDescriptor\n  type: 'iphone' | 'ipad' | 'googleplay'\n}) {\n  return [\n    Meta({ name: `twitter:app:name:${type}`, content: app.name }),\n    Meta({ name: `twitter:app:id:${type}`, content: app.id[type] }),\n    Meta({\n      name: `twitter:app:url:${type}`,\n      content: app.url?.[type]?.toString(),\n    }),\n  ]\n}\n\nexport function TwitterMetadata({\n  twitter,\n}: {\n  twitter: ResolvedMetadata['twitter']\n}) {\n  if (!twitter) return null\n  const { card } = twitter\n\n  return MetaFilter([\n    Meta({ name: 'twitter:card', content: card }),\n    Meta({ name: 'twitter:site', content: twitter.site }),\n    Meta({ name: 'twitter:site:id', content: twitter.siteId }),\n    Meta({ name: 'twitter:creator', content: twitter.creator }),\n    Meta({ name: 'twitter:creator:id', content: twitter.creatorId }),\n    Meta({ name: 'twitter:title', content: twitter.title?.absolute }),\n    Meta({ name: 'twitter:description', content: twitter.description }),\n    MultiMeta({ namePrefix: 'twitter:image', contents: twitter.images }),\n    ...(card === 'player'\n      ? twitter.players.flatMap((player) => [\n          Meta({\n            name: 'twitter:player',\n            content: player.playerUrl.toString(),\n          }),\n          Meta({\n            name: 'twitter:player:stream',\n            content: player.streamUrl.toString(),\n          }),\n          Meta({ name: 'twitter:player:width', content: player.width }),\n          Meta({ name: 'twitter:player:height', content: player.height }),\n        ])\n      : []),\n    ...(card === 'app'\n      ? [\n          TwitterAppItem({ app: twitter.app, type: 'iphone' }),\n          TwitterAppItem({ app: twitter.app, type: 'ipad' }),\n          TwitterAppItem({ app: twitter.app, type: 'googleplay' }),\n        ]\n      : []),\n  ])\n}\n\nexport function AppLinksMeta({\n  appLinks,\n}: {\n  appLinks: ResolvedMetadata['appLinks']\n}) {\n  if (!appLinks) return null\n  return MetaFilter([\n    MultiMeta({ propertyPrefix: 'al:ios', contents: appLinks.ios }),\n    MultiMeta({ propertyPrefix: 'al:iphone', contents: appLinks.iphone }),\n    MultiMeta({ propertyPrefix: 'al:ipad', contents: appLinks.ipad }),\n    MultiMeta({ propertyPrefix: 'al:android', contents: appLinks.android }),\n    MultiMeta({\n      propertyPrefix: 'al:windows_phone',\n      contents: appLinks.windows_phone,\n    }),\n    MultiMeta({ propertyPrefix: 'al:windows', contents: appLinks.windows }),\n    MultiMeta({\n      propertyPrefix: 'al:windows_universal',\n      contents: appLinks.windows_universal,\n    }),\n    MultiMeta({ propertyPrefix: 'al:web', contents: appLinks.web }),\n  ])\n}\n"], "names": ["Meta", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MultiMeta", "OpenGraphMetadata", "openGraph", "typedOpenGraph", "openGraphType", "type", "property", "content", "publishedTime", "toString", "modifiedTime", "expirationTime", "propertyPrefix", "contents", "authors", "section", "tags", "isbn", "releaseDate", "firstName", "lastName", "username", "gender", "duration", "albums", "musicians", "songs", "creators", "actors", "directors", "writers", "series", "_exhaustiveCheck", "Error", "determiner", "title", "absolute", "description", "url", "siteName", "locale", "countryName", "ttl", "images", "videos", "audio", "emails", "phoneNumbers", "faxNumbers", "alternateLocale", "TwitterAppItem", "app", "name", "id", "TwitterMetadata", "twitter", "card", "site", "siteId", "creator", "creatorId", "namePrefix", "players", "flatMap", "player", "playerUrl", "streamUrl", "width", "height", "AppLinksMeta", "appLinks", "ios", "iphone", "ipad", "android", "windows_phone", "windows", "windows_universal", "web"], "mappings": "AAGA,SAASA,IAAI,EAAEC,UAAU,EAAEC,SAAS,QAAQ,SAAQ;AAEpD,OAAO,SAASC,kBAAkB,EAChCC,SAAS,EAGV;QA0LyCA,kBAEFA,gBAIAA;IA/LtC,IAAI,CAACA,WAAW;QACd,OAAO;IACT;IAEA,IAAIC;IACJ,IAAI,UAAUD,WAAW;QACvB,MAAME,gBAAgBF,UAAUG,IAAI;QACpC,OAAQD;YACN,KAAK;gBACHD,iBAAiB;oBAACL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAU;iBAAG;gBACpE;YACF,KAAK;oBAKUL,0BAIAA,yBAIAA;gBAZbC,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAU;oBAC/CT,KAAK;wBACHQ,UAAU;wBACVC,OAAO,GAAEL,2BAAAA,UAAUM,aAAa,qBAAvBN,yBAAyBO,QAAQ;oBAC5C;oBACAX,KAAK;wBACHQ,UAAU;wBACVC,OAAO,GAAEL,0BAAAA,UAAUQ,YAAY,qBAAtBR,wBAAwBO,QAAQ;oBAC3C;oBACAX,KAAK;wBACHQ,UAAU;wBACVC,OAAO,GAAEL,4BAAAA,UAAUS,cAAc,qBAAxBT,0BAA0BO,QAAQ;oBAC7C;oBACAT,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUY,OAAO;oBAC7B;oBACAhB,KAAK;wBAAEQ,UAAU;wBAAmBC,SAASL,UAAUa,OAAO;oBAAC;oBAC/Df,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUc,IAAI;oBAC1B;iBACD;gBACD;YACF,KAAK;gBACHb,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAO;oBAC5CT,KAAK;wBAAEQ,UAAU;wBAAaC,SAASL,UAAUe,IAAI;oBAAC;oBACtDnB,KAAK;wBACHQ,UAAU;wBACVC,SAASL,UAAUgB,WAAW;oBAChC;oBACAlB,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUY,OAAO;oBAC7B;oBACAd,UAAU;wBAAEY,gBAAgB;wBAAYC,UAAUX,UAAUc,IAAI;oBAAC;iBAClE;gBACD;YACF,KAAK;gBACHb,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAU;oBAC/CT,KAAK;wBACHQ,UAAU;wBACVC,SAASL,UAAUiB,SAAS;oBAC9B;oBACArB,KAAK;wBAAEQ,UAAU;wBAAqBC,SAASL,UAAUkB,QAAQ;oBAAC;oBAClEtB,KAAK;wBAAEQ,UAAU;wBAAoBC,SAASL,UAAUmB,QAAQ;oBAAC;oBACjEvB,KAAK;wBAAEQ,UAAU;wBAAkBC,SAASL,UAAUoB,MAAM;oBAAC;iBAC9D;gBACD;YACF,KAAK;oBAKUpB;gBAJbC,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAa;oBAClDT,KAAK;wBACHQ,UAAU;wBACVC,OAAO,GAAEL,sBAAAA,UAAUqB,QAAQ,qBAAlBrB,oBAAoBO,QAAQ;oBACvC;oBACAT,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUsB,MAAM;oBAC5B;oBACAxB,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUuB,SAAS;oBAC/B;iBACD;gBACD;YACF,KAAK;gBACHtB,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAc;oBACnDP,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUwB,KAAK;oBAC3B;oBACA1B,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUuB,SAAS;oBAC/B;oBACA3B,KAAK;wBACHQ,UAAU;wBACVC,SAASL,UAAUgB,WAAW;oBAChC;iBACD;gBACD;YACF,KAAK;gBACHf,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAiB;oBACtDP,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUwB,KAAK;oBAC3B;oBACA1B,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUyB,QAAQ;oBAC9B;iBACD;gBACD;YACF,KAAK;gBACHxB,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAsB;oBAC3DP,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAUyB,QAAQ;oBAC9B;iBACD;gBACD;YAEF,KAAK;gBACHxB,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAc;oBACnDP,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAU0B,MAAM;oBAC5B;oBACA5B,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAU2B,SAAS;oBAC/B;oBACA7B,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAU4B,OAAO;oBAC7B;oBACAhC,KAAK;wBAAEQ,UAAU;wBAAkBC,SAASL,UAAUqB,QAAQ;oBAAC;oBAC/DzB,KAAK;wBACHQ,UAAU;wBACVC,SAASL,UAAUgB,WAAW;oBAChC;oBACAlB,UAAU;wBAAEY,gBAAgB;wBAAaC,UAAUX,UAAUc,IAAI;oBAAC;iBACnE;gBACD;YACF,KAAK;gBACHb,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAgB;oBACrDP,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAU0B,MAAM;oBAC5B;oBACA5B,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAU2B,SAAS;oBAC/B;oBACA7B,UAAU;wBACRY,gBAAgB;wBAChBC,UAAUX,UAAU4B,OAAO;oBAC7B;oBACAhC,KAAK;wBAAEQ,UAAU;wBAAkBC,SAASL,UAAUqB,QAAQ;oBAAC;oBAC/DzB,KAAK;wBACHQ,UAAU;wBACVC,SAASL,UAAUgB,WAAW;oBAChC;oBACAlB,UAAU;wBAAEY,gBAAgB;wBAAaC,UAAUX,UAAUc,IAAI;oBAAC;oBAClElB,KAAK;wBAAEQ,UAAU;wBAAgBC,SAASL,UAAU6B,MAAM;oBAAC;iBAC5D;gBACD;YACF,KAAK;gBACH5B,iBAAiB;oBACfL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAgB;iBACtD;gBACD;YACF,KAAK;gBACHJ,iBAAiB;oBAACL,KAAK;wBAAEQ,UAAU;wBAAWC,SAAS;oBAAc;iBAAG;gBACxE;YAEF;gBACE,MAAMyB,mBAA0B5B;gBAChC,MAAM,qBAAwD,CAAxD,IAAI6B,MAAM,CAAC,wBAAwB,EAAED,kBAAkB,GAAvD,qBAAA;2BAAA;gCAAA;kCAAA;gBAAuD;QACjE;IACF;IAEA,OAAOjC,WAAW;QAChBD,KAAK;YAAEQ,UAAU;YAAiBC,SAASL,UAAUgC,UAAU;QAAC;QAChEpC,KAAK;YAAEQ,UAAU;YAAYC,OAAO,GAAEL,mBAAAA,UAAUiC,KAAK,qBAAfjC,iBAAiBkC,QAAQ;QAAC;QAChEtC,KAAK;YAAEQ,UAAU;YAAkBC,SAASL,UAAUmC,WAAW;QAAC;QAClEvC,KAAK;YAAEQ,UAAU;YAAUC,OAAO,GAAEL,iBAAAA,UAAUoC,GAAG,qBAAbpC,eAAeO,QAAQ;QAAG;QAC9DX,KAAK;YAAEQ,UAAU;YAAgBC,SAASL,UAAUqC,QAAQ;QAAC;QAC7DzC,KAAK;YAAEQ,UAAU;YAAaC,SAASL,UAAUsC,MAAM;QAAC;QACxD1C,KAAK;YAAEQ,UAAU;YAAmBC,SAASL,UAAUuC,WAAW;QAAC;QACnE3C,KAAK;YAAEQ,UAAU;YAAUC,OAAO,GAAEL,iBAAAA,UAAUwC,GAAG,qBAAbxC,eAAeO,QAAQ;QAAG;QAC9DT,UAAU;YAAEY,gBAAgB;YAAYC,UAAUX,UAAUyC,MAAM;QAAC;QACnE3C,UAAU;YAAEY,gBAAgB;YAAYC,UAAUX,UAAU0C,MAAM;QAAC;QACnE5C,UAAU;YAAEY,gBAAgB;YAAYC,UAAUX,UAAU2C,KAAK;QAAC;QAClE7C,UAAU;YAAEY,gBAAgB;YAAYC,UAAUX,UAAU4C,MAAM;QAAC;QACnE9C,UAAU;YACRY,gBAAgB;YAChBC,UAAUX,UAAU6C,YAAY;QAClC;QACA/C,UAAU;YACRY,gBAAgB;YAChBC,UAAUX,UAAU8C,UAAU;QAChC;QACAhD,UAAU;YACRY,gBAAgB;YAChBC,UAAUX,UAAU+C,eAAe;QACrC;WACI9C,iBAAiBA,iBAAiB,EAAE;KACzC;AACH;AAEA,SAAS+C,eAAe,EACtBC,GAAG,EACH9C,IAAI,EAIL;QAMc8C,eAAAA;IALb,OAAO;QACLrD,KAAK;YAAEsD,MAAM,CAAC,iBAAiB,EAAE/C,MAAM;YAAEE,SAAS4C,IAAIC,IAAI;QAAC;QAC3DtD,KAAK;YAAEsD,MAAM,CAAC,eAAe,EAAE/C,MAAM;YAAEE,SAAS4C,IAAIE,EAAE,CAAChD,KAAK;QAAC;QAC7DP,KAAK;YACHsD,MAAM,CAAC,gBAAgB,EAAE/C,MAAM;YAC/BE,OAAO,GAAE4C,WAAAA,IAAIb,GAAG,sBAAPa,gBAAAA,QAAS,CAAC9C,KAAK,qBAAf8C,cAAiB1C,QAAQ;QACpC;KACD;AACH;AAEA,OAAO,SAAS6C,gBAAgB,EAC9BC,OAAO,EAGR;QAU0CA;IATzC,IAAI,CAACA,SAAS,OAAO;IACrB,MAAM,EAAEC,IAAI,EAAE,GAAGD;IAEjB,OAAOxD,WAAW;QAChBD,KAAK;YAAEsD,MAAM;YAAgB7C,SAASiD;QAAK;QAC3C1D,KAAK;YAAEsD,MAAM;YAAgB7C,SAASgD,QAAQE,IAAI;QAAC;QACnD3D,KAAK;YAAEsD,MAAM;YAAmB7C,SAASgD,QAAQG,MAAM;QAAC;QACxD5D,KAAK;YAAEsD,MAAM;YAAmB7C,SAASgD,QAAQI,OAAO;QAAC;QACzD7D,KAAK;YAAEsD,MAAM;YAAsB7C,SAASgD,QAAQK,SAAS;QAAC;QAC9D9D,KAAK;YAAEsD,MAAM;YAAiB7C,OAAO,GAAEgD,iBAAAA,QAAQpB,KAAK,qBAAboB,eAAenB,QAAQ;QAAC;QAC/DtC,KAAK;YAAEsD,MAAM;YAAuB7C,SAASgD,QAAQlB,WAAW;QAAC;QACjErC,UAAU;YAAE6D,YAAY;YAAiBhD,UAAU0C,QAAQZ,MAAM;QAAC;WAC9Da,SAAS,WACTD,QAAQO,OAAO,CAACC,OAAO,CAAC,CAACC,SAAW;gBAClClE,KAAK;oBACHsD,MAAM;oBACN7C,SAASyD,OAAOC,SAAS,CAACxD,QAAQ;gBACpC;gBACAX,KAAK;oBACHsD,MAAM;oBACN7C,SAASyD,OAAOE,SAAS,CAACzD,QAAQ;gBACpC;gBACAX,KAAK;oBAAEsD,MAAM;oBAAwB7C,SAASyD,OAAOG,KAAK;gBAAC;gBAC3DrE,KAAK;oBAAEsD,MAAM;oBAAyB7C,SAASyD,OAAOI,MAAM;gBAAC;aAC9D,IACD,EAAE;WACFZ,SAAS,QACT;YACEN,eAAe;gBAAEC,KAAKI,QAAQJ,GAAG;gBAAE9C,MAAM;YAAS;YAClD6C,eAAe;gBAAEC,KAAKI,QAAQJ,GAAG;gBAAE9C,MAAM;YAAO;YAChD6C,eAAe;gBAAEC,KAAKI,QAAQJ,GAAG;gBAAE9C,MAAM;YAAa;SACvD,GACD,EAAE;KACP;AACH;AAEA,OAAO,SAASgE,aAAa,EAC3BC,QAAQ,EAGT;IACC,IAAI,CAACA,UAAU,OAAO;IACtB,OAAOvE,WAAW;QAChBC,UAAU;YAAEY,gBAAgB;YAAUC,UAAUyD,SAASC,GAAG;QAAC;QAC7DvE,UAAU;YAAEY,gBAAgB;YAAaC,UAAUyD,SAASE,MAAM;QAAC;QACnExE,UAAU;YAAEY,gBAAgB;YAAWC,UAAUyD,SAASG,IAAI;QAAC;QAC/DzE,UAAU;YAAEY,gBAAgB;YAAcC,UAAUyD,SAASI,OAAO;QAAC;QACrE1E,UAAU;YACRY,gBAAgB;YAChBC,UAAUyD,SAASK,aAAa;QAClC;QACA3E,UAAU;YAAEY,gBAAgB;YAAcC,UAAUyD,SAASM,OAAO;QAAC;QACrE5E,UAAU;YACRY,gBAAgB;YAChBC,UAAUyD,SAASO,iBAAiB;QACtC;QACA7E,UAAU;YAAEY,gBAAgB;YAAUC,UAAUyD,SAASQ,GAAG;QAAC;KAC9D;AACH"}