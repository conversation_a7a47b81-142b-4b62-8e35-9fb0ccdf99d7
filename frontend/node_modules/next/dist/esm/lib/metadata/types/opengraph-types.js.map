{"version": 3, "sources": ["../../../../src/lib/metadata/types/opengraph-types.ts"], "sourcesContent": ["import type { AbsoluteTemplateString, TemplateString } from './metadata-types'\n\nexport type OpenGraphType =\n  | 'article'\n  | 'book'\n  | 'music.song'\n  | 'music.album'\n  | 'music.playlist'\n  | 'music.radio_station'\n  | 'profile'\n  | 'website'\n  | 'video.tv_show'\n  | 'video.other'\n  | 'video.movie'\n  | 'video.episode'\n\nexport type OpenGraph =\n  | OpenGraphWebsite\n  | OpenGraphArticle\n  | OpenGraphBook\n  | OpenGraphProfile\n  | OpenGraphMusicSong\n  | OpenGraphMusicAlbum\n  | OpenGraphMusicPlaylist\n  | OpenGraphRadioStation\n  | OpenGraphVideoMovie\n  | OpenGraphVideoEpisode\n  | OpenGraphVideoTVShow\n  | OpenGraphVideoOther\n  | OpenGraphMetadata\n\n// update this type to reflect actual locales\ntype Locale = string\n\ntype OpenGraphMetadata = {\n  determiner?: 'a' | 'an' | 'the' | 'auto' | '' | undefined\n  title?: string | TemplateString | undefined\n  description?: string | undefined\n  emails?: string | Array<string> | undefined\n  phoneNumbers?: string | Array<string> | undefined\n  faxNumbers?: string | Array<string> | undefined\n  siteName?: string | undefined\n  locale?: Locale | undefined\n  alternateLocale?: Locale | Array<Locale> | undefined\n  images?: OGImage | Array<OGImage> | undefined\n  audio?: OGAudio | Array<OGAudio> | undefined\n  videos?: OGVideo | Array<OGVideo> | undefined\n  url?: string | URL | undefined\n  countryName?: string | undefined\n  ttl?: number | undefined\n}\ntype OpenGraphWebsite = OpenGraphMetadata & {\n  type: 'website'\n}\ntype OpenGraphArticle = OpenGraphMetadata & {\n  type: 'article'\n  publishedTime?: string | undefined // datetime\n  modifiedTime?: string | undefined // datetime\n  expirationTime?: string | undefined // datetime\n  authors?: null | string | URL | Array<string | URL> | undefined\n  section?: null | string | undefined\n  tags?: null | string | Array<string> | undefined\n}\ntype OpenGraphBook = OpenGraphMetadata & {\n  type: 'book'\n  isbn?: null | string | undefined\n  releaseDate?: null | string | undefined // datetime\n  authors?: null | string | URL | Array<string | URL> | undefined\n  tags?: null | string | Array<string> | undefined\n}\ntype OpenGraphProfile = OpenGraphMetadata & {\n  type: 'profile'\n  firstName?: null | string | undefined\n  lastName?: null | string | undefined\n  username?: null | string | undefined\n  gender?: null | string | undefined\n}\ntype OpenGraphMusicSong = OpenGraphMetadata & {\n  type: 'music.song'\n  duration?: null | number | undefined\n  albums?:\n    | null\n    | string\n    | URL\n    | OGAlbum\n    | Array<string | URL | OGAlbum>\n    | undefined\n  musicians?: null | string | URL | Array<string | URL> | undefined\n}\ntype OpenGraphMusicAlbum = OpenGraphMetadata & {\n  type: 'music.album'\n  songs?:\n    | null\n    | string\n    | URL\n    | OGSong\n    | Array<string | URL | OGSong>\n    | undefined\n  musicians?: null | string | URL | Array<string | URL> | undefined\n  releaseDate?: null | string | undefined // datetime\n}\ntype OpenGraphMusicPlaylist = OpenGraphMetadata & {\n  type: 'music.playlist'\n  songs?:\n    | null\n    | string\n    | URL\n    | OGSong\n    | Array<string | URL | OGSong>\n    | undefined\n  creators?: null | string | URL | Array<string | URL> | undefined\n}\ntype OpenGraphRadioStation = OpenGraphMetadata & {\n  type: 'music.radio_station'\n  creators?: null | string | URL | Array<string | URL> | undefined\n}\ntype OpenGraphVideoMovie = OpenGraphMetadata & {\n  type: 'video.movie'\n  actors?:\n    | null\n    | string\n    | URL\n    | OGActor\n    | Array<string | URL | OGActor>\n    | undefined\n  directors?: null | string | URL | Array<string | URL> | undefined\n  writers?: null | string | URL | Array<string | URL> | undefined\n  duration?: null | number | undefined\n  releaseDate?: null | string | undefined // datetime\n  tags?: null | string | Array<string> | undefined\n}\ntype OpenGraphVideoEpisode = OpenGraphMetadata & {\n  type: 'video.episode'\n  actors?:\n    | null\n    | string\n    | URL\n    | OGActor\n    | Array<string | URL | OGActor>\n    | undefined\n  directors?: null | string | URL | Array<string | URL> | undefined\n  writers?: null | string | URL | Array<string | URL> | undefined\n  duration?: null | number | undefined\n  releaseDate?: null | string | undefined // datetime\n  tags?: null | string | Array<string> | undefined\n  series?: null | string | URL | undefined\n}\ntype OpenGraphVideoTVShow = OpenGraphMetadata & {\n  type: 'video.tv_show'\n}\ntype OpenGraphVideoOther = OpenGraphMetadata & {\n  type: 'video.other'\n}\n\ntype OGImage = string | OGImageDescriptor | URL\ntype OGImageDescriptor = {\n  url: string | URL\n  secureUrl?: string | URL | undefined\n  alt?: string | undefined\n  type?: string | undefined\n  width?: string | number | undefined\n  height?: string | number | undefined\n}\ntype OGAudio = string | OGAudioDescriptor | URL\ntype OGAudioDescriptor = {\n  url: string | URL\n  secureUrl?: string | URL | undefined\n  type?: string | undefined\n}\ntype OGVideo = string | OGVideoDescriptor | URL\ntype OGVideoDescriptor = {\n  url: string | URL\n  secureUrl?: string | URL | undefined\n  type?: string | undefined\n  width?: string | number | undefined\n  height?: string | number | undefined\n}\n\nexport type ResolvedOpenGraph =\n  | ResolvedOpenGraphWebsite\n  | ResolvedOpenGraphArticle\n  | ResolvedOpenGraphBook\n  | ResolvedOpenGraphProfile\n  | ResolvedOpenGraphMusicSong\n  | ResolvedOpenGraphMusicAlbum\n  | ResolvedOpenGraphMusicPlaylist\n  | ResolvedOpenGraphRadioStation\n  | ResolvedOpenGraphVideoMovie\n  | ResolvedOpenGraphVideoEpisode\n  | ResolvedOpenGraphVideoTVShow\n  | ResolvedOpenGraphVideoOther\n  | ResolvedOpenGraphMetadata\n\ntype ResolvedOpenGraphMetadata = {\n  determiner?: 'a' | 'an' | 'the' | 'auto' | '' | undefined\n  title: AbsoluteTemplateString\n  description?: string | undefined\n  emails?: Array<string> | undefined\n  phoneNumbers?: Array<string> | undefined\n  faxNumbers?: Array<string> | undefined\n  siteName?: string | undefined\n  locale?: Locale | undefined\n  alternateLocale?: Array<Locale> | undefined\n  images?: Array<OGImage> | undefined\n  audio?: Array<OGAudio> | undefined\n  videos?: Array<OGVideo> | undefined\n  url: null | URL | string\n  countryName?: string | undefined\n  ttl?: number | undefined\n}\ntype ResolvedOpenGraphWebsite = ResolvedOpenGraphMetadata & {\n  type: 'website'\n}\ntype ResolvedOpenGraphArticle = ResolvedOpenGraphMetadata & {\n  type: 'article'\n  publishedTime?: string | undefined // datetime\n  modifiedTime?: string | undefined // datetime\n  expirationTime?: string | undefined // datetime\n  authors?: Array<string> | undefined\n  section?: string | undefined\n  tags?: Array<string> | undefined\n}\ntype ResolvedOpenGraphBook = ResolvedOpenGraphMetadata & {\n  type: 'book'\n  isbn?: string | undefined\n  releaseDate?: string | undefined // datetime\n  authors?: Array<string> | undefined\n  tags?: Array<string> | undefined\n}\ntype ResolvedOpenGraphProfile = ResolvedOpenGraphMetadata & {\n  type: 'profile'\n  firstName?: string | undefined\n  lastName?: string | undefined\n  username?: string | undefined\n  gender?: string | undefined\n}\ntype ResolvedOpenGraphMusicSong = ResolvedOpenGraphMetadata & {\n  type: 'music.song'\n  duration?: number | undefined\n  albums?: Array<OGAlbum> | undefined\n  musicians?: Array<string | URL> | undefined\n}\ntype ResolvedOpenGraphMusicAlbum = ResolvedOpenGraphMetadata & {\n  type: 'music.album'\n  songs?: Array<string | URL | OGSong> | undefined\n  musicians?: Array<string | URL> | undefined\n  releaseDate?: string | undefined // datetime\n}\ntype ResolvedOpenGraphMusicPlaylist = ResolvedOpenGraphMetadata & {\n  type: 'music.playlist'\n  songs?: Array<string | URL | OGSong> | undefined\n  creators?: Array<string | URL> | undefined\n}\ntype ResolvedOpenGraphRadioStation = ResolvedOpenGraphMetadata & {\n  type: 'music.radio_station'\n  creators?: Array<string | URL> | undefined\n}\ntype ResolvedOpenGraphVideoMovie = ResolvedOpenGraphMetadata & {\n  type: 'video.movie'\n  actors?: Array<string | URL | OGActor> | undefined\n  directors?: Array<string | URL> | undefined\n  writers?: Array<string | URL> | undefined\n  duration?: number | undefined\n  releaseDate?: string | undefined // datetime\n  tags?: Array<string> | undefined\n}\ntype ResolvedOpenGraphVideoEpisode = ResolvedOpenGraphMetadata & {\n  type: 'video.episode'\n  actors?: Array<string | URL | OGActor> | undefined\n  directors?: Array<string | URL> | undefined\n  writers?: Array<string | URL> | undefined\n  duration?: number | undefined\n  releaseDate?: string | undefined // datetime\n  tags?: Array<string> | undefined\n  series?: string | URL | undefined\n}\ntype ResolvedOpenGraphVideoTVShow = ResolvedOpenGraphMetadata & {\n  type: 'video.tv_show'\n}\ntype ResolvedOpenGraphVideoOther = ResolvedOpenGraphMetadata & {\n  type: 'video.other'\n}\n\ntype OGSong = {\n  url: string | URL\n  disc?: number | undefined\n  track?: number | undefined\n}\ntype OGAlbum = {\n  url: string | URL\n  disc?: number | undefined\n  track?: number | undefined\n}\ntype OGActor = {\n  url: string | URL\n  role?: string | undefined\n}\n"], "names": [], "mappings": "AAkLA,WAa6B"}