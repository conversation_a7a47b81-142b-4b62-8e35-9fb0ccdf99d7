import { red } from './picocolors';
import { Worker } from './worker';
import { existsSync } from 'fs';
import { join } from 'path';
import { ESLINT_DEFAULT_DIRS } from './constants';
import { eventLintCheckCompleted } from '../telemetry/events';
import { CompileError } from './compile-error';
import isError from './is-error';
export async function verifyAndLint(dir, cacheLocation, configLintDirs, enableWorkerThreads, telemetry) {
    let lintWorkers;
    try {
        lintWorkers = new Worker(require.resolve('./eslint/runLintCheck'), {
            exposedMethods: [
                'runLintCheck'
            ],
            numWorkers: 1,
            enableWorkerThreads,
            maxRetries: 0
        });
        const lintDirs = (configLintDirs ?? ESLINT_DEFAULT_DIRS).reduce((res, d)=>{
            const currDir = join(dir, d);
            if (!existsSync(currDir)) return res;
            res.push(currDir);
            return res;
        }, []);
        const lintResults = await (lintWorkers == null ? void 0 : lintWorkers.runLintCheck(dir, lintDirs, {
            lintDuringBuild: true,
            eslintOptions: {
                cacheLocation
            }
        }));
        const lintOutput = typeof lintResults === 'string' ? lintResults : lintResults == null ? void 0 : lintResults.output;
        if (typeof lintResults !== 'string' && (lintResults == null ? void 0 : lintResults.eventInfo)) {
            telemetry.record(eventLintCheckCompleted({
                ...lintResults.eventInfo,
                buildLint: true
            }));
        }
        if (typeof lintResults !== 'string' && (lintResults == null ? void 0 : lintResults.isError) && lintOutput) {
            await telemetry.flush();
            throw Object.defineProperty(new CompileError(lintOutput), "__NEXT_ERROR_CODE", {
                value: "E394",
                enumerable: false,
                configurable: true
            });
        }
        if (lintOutput) {
            console.log(lintOutput);
        }
    } catch (err) {
        if (isError(err)) {
            if (err.type === 'CompileError' || err instanceof CompileError) {
                console.error(red('\nFailed to compile.'));
                console.error(err.message);
                process.exit(1);
            } else if (err.type === 'FatalError') {
                console.error(err.message);
                process.exit(1);
            }
        }
        throw err;
    } finally{
        try {
            lintWorkers == null ? void 0 : lintWorkers.end();
        } catch  {}
    }
}

//# sourceMappingURL=verifyAndLint.js.map