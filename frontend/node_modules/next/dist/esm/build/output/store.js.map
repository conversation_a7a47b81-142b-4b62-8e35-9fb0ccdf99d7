{"version": 3, "sources": ["../../../src/build/output/store.ts"], "sourcesContent": ["import createStore from 'next/dist/compiled/unistore'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { type Span, flushAllTraces, trace } from '../../trace'\nimport { teardownTraceSubscriber } from '../swc'\nimport * as Log from './log'\n\nconst MAX_LOG_SKIP_DURATION = 500 // 500ms\n\nexport type OutputState =\n  | {\n      bootstrap: true\n      appUrl: string | null\n      bindAddr: string | null\n      logging: boolean\n    }\n  | ({\n      bootstrap: false\n      appUrl: string | null\n      bindAddr: string | null\n      logging: boolean\n    } & (\n      | {\n          loading: true\n          trigger: string | undefined\n          url: string | undefined\n        }\n      | {\n          loading: false\n          typeChecking: boolean\n          totalModulesCount: number\n          errors: string[] | null\n          warnings: string[] | null\n          hasEdgeServer: boolean\n        }\n    ))\n\nexport function formatTrigger(trigger: string) {\n  // Format dynamic sitemap routes to simpler file path\n  // e.g., /sitemap.xml[] -> /sitemap.xml\n  if (trigger.includes('[__metadata_id__]')) {\n    trigger = trigger.replace('/[__metadata_id__]', '/[id]')\n  }\n\n  if (trigger.length > 1 && trigger.endsWith('/')) {\n    trigger = trigger.slice(0, -1)\n  }\n  return trigger\n}\n\nexport const store = createStore<OutputState>({\n  appUrl: null,\n  bindAddr: null,\n  bootstrap: true,\n  logging: true,\n})\n\nlet lastStore: OutputState = {\n  appUrl: null,\n  bindAddr: null,\n  bootstrap: true,\n  logging: true,\n}\nfunction hasStoreChanged(nextStore: OutputState) {\n  if (\n    (\n      [\n        ...new Set([...Object.keys(lastStore), ...Object.keys(nextStore)]),\n      ] as Array<keyof OutputState>\n    ).every((key) => Object.is(lastStore[key], nextStore[key]))\n  ) {\n    return false\n  }\n\n  lastStore = nextStore\n  return true\n}\n\nlet startTime = 0\nlet trigger = '' // default, use empty string for trigger\nlet triggerUrl: string | undefined = undefined\nlet loadingLogTimer: NodeJS.Timeout | null = null\nlet traceSpan: Span | null = null\nlet logging = true\n\nstore.subscribe((state) => {\n  // Update persisted logging state\n  if ('logging' in state) {\n    logging = state.logging\n  }\n\n  // If logging is disabled, do not log\n  if (!logging) {\n    return\n  }\n\n  if (!hasStoreChanged(state)) {\n    return\n  }\n\n  if (state.bootstrap) {\n    return\n  }\n\n  if (state.loading) {\n    if (state.trigger) {\n      trigger = formatTrigger(state.trigger)\n      triggerUrl = state.url\n      if (trigger !== 'initial') {\n        traceSpan = trace('compile-path', undefined, {\n          trigger: trigger,\n        })\n        if (!loadingLogTimer) {\n          // Only log compiling if compiled is not finished in 3 seconds\n          loadingLogTimer = setTimeout(() => {\n            if (\n              triggerUrl &&\n              triggerUrl !== trigger &&\n              process.env.NEXT_TRIGGER_URL\n            ) {\n              Log.wait(`Compiling ${trigger} (${triggerUrl}) ...`)\n            } else {\n              Log.wait(`Compiling ${trigger} ...`)\n            }\n          }, MAX_LOG_SKIP_DURATION)\n        }\n      }\n    }\n    if (startTime === 0) {\n      startTime = Date.now()\n    }\n    return\n  }\n\n  if (state.errors) {\n    // Log compilation errors\n    Log.error(state.errors[0])\n\n    const cleanError = stripAnsi(state.errors[0])\n    if (cleanError.indexOf('SyntaxError') > -1) {\n      const matches = cleanError.match(/\\[.*\\]=/)\n      if (matches) {\n        for (const match of matches) {\n          const prop = (match.split(']').shift() || '').slice(1)\n          console.log(\n            `AMP bind syntax [${prop}]='' is not supported in JSX, use 'data-amp-bind-${prop}' instead. https://nextjs.org/docs/messages/amp-bind-jsx-alt`\n          )\n        }\n        return\n      }\n    }\n    startTime = 0\n    // Ensure traces are flushed after each compile in development mode\n    flushAllTraces()\n    teardownTraceSubscriber()\n    return\n  }\n\n  let timeMessage = ''\n  if (startTime) {\n    const time = Date.now() - startTime\n    startTime = 0\n\n    timeMessage =\n      ' ' +\n      (time > 2000 ? `in ${Math.round(time / 100) / 10}s` : `in ${time}ms`)\n  }\n\n  let modulesMessage = ''\n  if (state.totalModulesCount) {\n    modulesMessage = ` (${state.totalModulesCount} modules)`\n  }\n\n  if (state.warnings) {\n    Log.warn(state.warnings.join('\\n\\n'))\n    // Ensure traces are flushed after each compile in development mode\n    flushAllTraces()\n    teardownTraceSubscriber()\n    return\n  }\n\n  if (state.typeChecking) {\n    Log.info(\n      `bundled ${trigger}${timeMessage}${modulesMessage}, type checking...`\n    )\n    return\n  }\n\n  if (trigger === 'initial') {\n    trigger = ''\n  } else {\n    if (loadingLogTimer) {\n      clearTimeout(loadingLogTimer)\n      loadingLogTimer = null\n    }\n    if (traceSpan) {\n      traceSpan.stop()\n      traceSpan = null\n    }\n    Log.event(\n      `Compiled${trigger ? ' ' + trigger : ''}${timeMessage}${modulesMessage}`\n    )\n    trigger = ''\n  }\n\n  // Ensure traces are flushed after each compile in development mode\n  flushAllTraces()\n  teardownTraceSubscriber()\n})\n"], "names": ["createStore", "stripAnsi", "flushAllTraces", "trace", "teardownTraceSubscriber", "Log", "MAX_LOG_SKIP_DURATION", "formatTrigger", "trigger", "includes", "replace", "length", "endsWith", "slice", "store", "appUrl", "bindAddr", "bootstrap", "logging", "lastStore", "hasStoreChanged", "nextStore", "Set", "Object", "keys", "every", "key", "is", "startTime", "triggerUrl", "undefined", "loadingLogTimer", "traceSpan", "subscribe", "state", "loading", "url", "setTimeout", "process", "env", "NEXT_TRIGGER_URL", "wait", "Date", "now", "errors", "error", "cleanError", "indexOf", "matches", "match", "prop", "split", "shift", "console", "log", "timeMessage", "time", "Math", "round", "modulesMessage", "totalModulesCount", "warnings", "warn", "join", "typeChecking", "info", "clearTimeout", "stop", "event"], "mappings": "AAAA,OAAOA,iBAAiB,8BAA6B;AACrD,OAAOC,eAAe,gCAA+B;AACrD,SAAoBC,cAAc,EAAEC,KAAK,QAAQ,cAAa;AAC9D,SAASC,uBAAuB,QAAQ,SAAQ;AAChD,YAAYC,SAAS,QAAO;AAE5B,MAAMC,wBAAwB,IAAI,QAAQ;;AA8B1C,OAAO,SAASC,cAAcC,OAAe;IAC3C,qDAAqD;IACrD,uCAAuC;IACvC,IAAIA,QAAQC,QAAQ,CAAC,sBAAsB;QACzCD,UAAUA,QAAQE,OAAO,CAAC,sBAAsB;IAClD;IAEA,IAAIF,QAAQG,MAAM,GAAG,KAAKH,QAAQI,QAAQ,CAAC,MAAM;QAC/CJ,UAAUA,QAAQK,KAAK,CAAC,GAAG,CAAC;IAC9B;IACA,OAAOL;AACT;AAEA,OAAO,MAAMM,QAAQd,YAAyB;IAC5Ce,QAAQ;IACRC,UAAU;IACVC,WAAW;IACXC,SAAS;AACX,GAAE;AAEF,IAAIC,YAAyB;IAC3BJ,QAAQ;IACRC,UAAU;IACVC,WAAW;IACXC,SAAS;AACX;AACA,SAASE,gBAAgBC,SAAsB;IAC7C,IACE,AACE;WACK,IAAIC,IAAI;eAAIC,OAAOC,IAAI,CAACL;eAAeI,OAAOC,IAAI,CAACH;SAAW;KAClE,CACDI,KAAK,CAAC,CAACC,MAAQH,OAAOI,EAAE,CAACR,SAAS,CAACO,IAAI,EAAEL,SAAS,CAACK,IAAI,IACzD;QACA,OAAO;IACT;IAEAP,YAAYE;IACZ,OAAO;AACT;AAEA,IAAIO,YAAY;AAChB,IAAIpB,UAAU,GAAG,wCAAwC;;AACzD,IAAIqB,aAAiCC;AACrC,IAAIC,kBAAyC;AAC7C,IAAIC,YAAyB;AAC7B,IAAId,UAAU;AAEdJ,MAAMmB,SAAS,CAAC,CAACC;IACf,iCAAiC;IACjC,IAAI,aAAaA,OAAO;QACtBhB,UAAUgB,MAAMhB,OAAO;IACzB;IAEA,qCAAqC;IACrC,IAAI,CAACA,SAAS;QACZ;IACF;IAEA,IAAI,CAACE,gBAAgBc,QAAQ;QAC3B;IACF;IAEA,IAAIA,MAAMjB,SAAS,EAAE;QACnB;IACF;IAEA,IAAIiB,MAAMC,OAAO,EAAE;QACjB,IAAID,MAAM1B,OAAO,EAAE;YACjBA,UAAUD,cAAc2B,MAAM1B,OAAO;YACrCqB,aAAaK,MAAME,GAAG;YACtB,IAAI5B,YAAY,WAAW;gBACzBwB,YAAY7B,MAAM,gBAAgB2B,WAAW;oBAC3CtB,SAASA;gBACX;gBACA,IAAI,CAACuB,iBAAiB;oBACpB,8DAA8D;oBAC9DA,kBAAkBM,WAAW;wBAC3B,IACER,cACAA,eAAerB,WACf8B,QAAQC,GAAG,CAACC,gBAAgB,EAC5B;4BACAnC,IAAIoC,IAAI,CAAC,CAAC,UAAU,EAAEjC,QAAQ,EAAE,EAAEqB,WAAW,KAAK,CAAC;wBACrD,OAAO;4BACLxB,IAAIoC,IAAI,CAAC,CAAC,UAAU,EAAEjC,QAAQ,IAAI,CAAC;wBACrC;oBACF,GAAGF;gBACL;YACF;QACF;QACA,IAAIsB,cAAc,GAAG;YACnBA,YAAYc,KAAKC,GAAG;QACtB;QACA;IACF;IAEA,IAAIT,MAAMU,MAAM,EAAE;QAChB,yBAAyB;QACzBvC,IAAIwC,KAAK,CAACX,MAAMU,MAAM,CAAC,EAAE;QAEzB,MAAME,aAAa7C,UAAUiC,MAAMU,MAAM,CAAC,EAAE;QAC5C,IAAIE,WAAWC,OAAO,CAAC,iBAAiB,CAAC,GAAG;YAC1C,MAAMC,UAAUF,WAAWG,KAAK,CAAC;YACjC,IAAID,SAAS;gBACX,KAAK,MAAMC,SAASD,QAAS;oBAC3B,MAAME,OAAO,AAACD,CAAAA,MAAME,KAAK,CAAC,KAAKC,KAAK,MAAM,EAAC,EAAGvC,KAAK,CAAC;oBACpDwC,QAAQC,GAAG,CACT,CAAC,iBAAiB,EAAEJ,KAAK,iDAAiD,EAAEA,KAAK,4DAA4D,CAAC;gBAElJ;gBACA;YACF;QACF;QACAtB,YAAY;QACZ,mEAAmE;QACnE1B;QACAE;QACA;IACF;IAEA,IAAImD,cAAc;IAClB,IAAI3B,WAAW;QACb,MAAM4B,OAAOd,KAAKC,GAAG,KAAKf;QAC1BA,YAAY;QAEZ2B,cACE,MACCC,CAAAA,OAAO,OAAO,CAAC,GAAG,EAAEC,KAAKC,KAAK,CAACF,OAAO,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAEA,KAAK,EAAE,CAAC,AAAD;IACvE;IAEA,IAAIG,iBAAiB;IACrB,IAAIzB,MAAM0B,iBAAiB,EAAE;QAC3BD,iBAAiB,CAAC,EAAE,EAAEzB,MAAM0B,iBAAiB,CAAC,SAAS,CAAC;IAC1D;IAEA,IAAI1B,MAAM2B,QAAQ,EAAE;QAClBxD,IAAIyD,IAAI,CAAC5B,MAAM2B,QAAQ,CAACE,IAAI,CAAC;QAC7B,mEAAmE;QACnE7D;QACAE;QACA;IACF;IAEA,IAAI8B,MAAM8B,YAAY,EAAE;QACtB3D,IAAI4D,IAAI,CACN,CAAC,QAAQ,EAAEzD,UAAU+C,cAAcI,eAAe,kBAAkB,CAAC;QAEvE;IACF;IAEA,IAAInD,YAAY,WAAW;QACzBA,UAAU;IACZ,OAAO;QACL,IAAIuB,iBAAiB;YACnBmC,aAAanC;YACbA,kBAAkB;QACpB;QACA,IAAIC,WAAW;YACbA,UAAUmC,IAAI;YACdnC,YAAY;QACd;QACA3B,IAAI+D,KAAK,CACP,CAAC,QAAQ,EAAE5D,UAAU,MAAMA,UAAU,KAAK+C,cAAcI,gBAAgB;QAE1EnD,UAAU;IACZ;IAEA,mEAAmE;IACnEN;IACAE;AACF"}