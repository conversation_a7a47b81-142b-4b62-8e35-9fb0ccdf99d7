{"version": 3, "sources": ["../../../src/build/turbopack-build/impl.ts"], "sourcesContent": ["import path from 'path'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isRelevantWarning,\n  type EntryIssuesMap,\n} from '../../shared/lib/turbopack/utils'\nimport { NextBuildContext } from '../build-context'\nimport { createDefineEnv, loadBindings } from '../swc'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport {\n  handleEntrypoints,\n  handlePagesErrorRoute,\n  handleRouteType,\n} from '../handle-entrypoints'\nimport type { Entrypoints } from '../swc/types'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { createProgress } from '../progress'\nimport * as Log from '../output/log'\nimport { promises as fs } from 'fs'\nimport { PHASE_PRODUCTION_BUILD } from '../../shared/lib/constants'\nimport loadConfig from '../../server/config'\nimport { hasCustomExportOutput } from '../../export/utils'\nimport { Telemetry } from '../../telemetry/storage'\nimport { setGlobal } from '../../trace'\n\nconst IS_TURBOPACK_BUILD = process.env.TURBOPACK && process.env.TURBOPACK_BUILD\n\nexport async function turbopackBuild(): Promise<{\n  duration: number\n  buildTraceContext: undefined\n  shutdownPromise: Promise<void>\n}> {\n  if (!IS_TURBOPACK_BUILD) {\n    throw new Error(\"next build doesn't support turbopack yet\")\n  }\n\n  await validateTurboNextConfig({\n    dir: NextBuildContext.dir!,\n    isDev: false,\n  })\n\n  const config = NextBuildContext.config!\n  const dir = NextBuildContext.dir!\n  const distDir = NextBuildContext.distDir!\n  const buildId = NextBuildContext.buildId!\n  const encryptionKey = NextBuildContext.encryptionKey!\n  const previewProps = NextBuildContext.previewProps!\n  const hasRewrites = NextBuildContext.hasRewrites!\n  const rewrites = NextBuildContext.rewrites!\n  const appDirOnly = NextBuildContext.appDirOnly!\n  const noMangling = NextBuildContext.noMangling!\n\n  const startTime = process.hrtime()\n  const bindings = await loadBindings(config?.experimental?.useWasmBinary)\n  const dev = false\n\n  // const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  const supportedBrowsers = [\n    'last 1 Chrome versions, last 1 Firefox versions, last 1 Safari versions, last 1 Edge versions',\n  ]\n\n  const persistentCaching = isPersistentCachingEnabled(config)\n  const project = await bindings.turbo.createProject(\n    {\n      projectPath: dir,\n      rootPath:\n        config.experimental?.turbo?.root || config.outputFileTracingRoot || dir,\n      distDir,\n      nextConfig: config,\n      jsConfig: await getTurbopackJsConfig(dir, config),\n      watch: {\n        enable: false,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters: NextBuildContext.clientRouterFilters!,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix: config.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // Implemented separately in Turbopack, doesn't have to be passed here.\n        middlewareMatchers: undefined,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling,\n    },\n    {\n      persistentCaching,\n      memoryLimit: config.experimental.turbo?.memoryLimit,\n      dependencyTracking: persistentCaching,\n    }\n  )\n\n  await fs.mkdir(path.join(distDir, 'server'), { recursive: true })\n  await fs.mkdir(path.join(distDir, 'static', buildId), {\n    recursive: true,\n  })\n  await fs.writeFile(\n    path.join(distDir, 'package.json'),\n    JSON.stringify(\n      {\n        type: 'commonjs',\n      },\n      null,\n      2\n    )\n  )\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const entrypointsSubscription = project.entrypointsSubscribe()\n  const currentEntrypoints: Entrypoints = {\n    global: {\n      app: undefined,\n      document: undefined,\n      error: undefined,\n\n      middleware: undefined,\n      instrumentation: undefined,\n    },\n\n    app: new Map(),\n    page: new Map(),\n  }\n\n  const currentEntryIssues: EntryIssuesMap = new Map()\n\n  const manifestLoader = new TurbopackManifestLoader({\n    buildId,\n    distDir,\n    encryptionKey,\n  })\n\n  const entrypointsResult = await entrypointsSubscription.next()\n  if (entrypointsResult.done) {\n    throw new Error('Turbopack did not return any entrypoints')\n  }\n  entrypointsSubscription.return?.().catch(() => {})\n\n  const entrypoints = entrypointsResult.value\n\n  const topLevelErrors: {\n    message: string\n  }[] = []\n  for (const issue of entrypoints.issues) {\n    topLevelErrors.push({\n      message: formatIssue(issue),\n    })\n  }\n\n  if (topLevelErrors.length > 0) {\n    throw new Error(\n      `Turbopack build failed with ${\n        topLevelErrors.length\n      } issues:\\n${topLevelErrors.map((e) => e.message).join('\\n')}`\n    )\n  }\n\n  await handleEntrypoints({\n    entrypoints,\n    currentEntrypoints,\n    currentEntryIssues,\n    manifestLoader,\n    productionRewrites: rewrites,\n    logErrors: false,\n  })\n\n  const progress = createProgress(\n    currentEntrypoints.page.size + currentEntrypoints.app.size + 1,\n    'Building'\n  )\n  const promises: Promise<any>[] = []\n\n  // Concurrency will start at INITIAL_CONCURRENCY and\n  // slowly ramp up to CONCURRENCY by increasing the\n  // concurrency by 1 every time a task is completed.\n  const INITIAL_CONCURRENCY = 5\n  const CONCURRENCY = 10\n\n  const sema = new Sema(INITIAL_CONCURRENCY)\n  let remainingRampup = CONCURRENCY - INITIAL_CONCURRENCY\n  const enqueue = (fn: () => Promise<void>) => {\n    promises.push(\n      (async () => {\n        await sema.acquire()\n        try {\n          await fn()\n        } finally {\n          sema.release()\n          if (remainingRampup > 0) {\n            remainingRampup--\n            sema.release()\n          }\n          progress.run()\n        }\n      })()\n    )\n  }\n\n  if (!appDirOnly) {\n    for (const [page, route] of currentEntrypoints.page) {\n      enqueue(() =>\n        handleRouteType({\n          page,\n          route,\n          currentEntryIssues,\n          entrypoints: currentEntrypoints,\n          manifestLoader,\n          productionRewrites: rewrites,\n          logErrors: false,\n        })\n      )\n    }\n  }\n\n  for (const [page, route] of currentEntrypoints.app) {\n    enqueue(() =>\n      handleRouteType({\n        page,\n        route,\n        currentEntryIssues,\n        entrypoints: currentEntrypoints,\n        manifestLoader,\n        productionRewrites: rewrites,\n        logErrors: false,\n      })\n    )\n  }\n\n  enqueue(() =>\n    handlePagesErrorRoute({\n      currentEntryIssues,\n      entrypoints: currentEntrypoints,\n      manifestLoader,\n      productionRewrites: rewrites,\n      logErrors: false,\n    })\n  )\n  await Promise.all(promises)\n\n  await manifestLoader.writeManifests({\n    devRewrites: undefined,\n    productionRewrites: rewrites,\n    entrypoints: currentEntrypoints,\n  })\n\n  const errors: {\n    page: string\n    message: string\n  }[] = []\n  const warnings: {\n    page: string\n    message: string\n  }[] = []\n  for (const [page, entryIssues] of currentEntryIssues) {\n    for (const issue of entryIssues.values()) {\n      if (issue.severity !== 'warning') {\n        errors.push({\n          page,\n          message: formatIssue(issue),\n        })\n      } else {\n        if (isRelevantWarning(issue)) {\n          warnings.push({\n            page,\n            message: formatIssue(issue),\n          })\n        }\n      }\n    }\n  }\n\n  const shutdownPromise = project.shutdown()\n\n  if (warnings.length > 0) {\n    Log.warn(\n      `Turbopack build collected ${warnings.length} warnings:\\n${warnings\n        .map((e) => {\n          return 'Page: ' + e.page + '\\n' + e.message\n        })\n        .join('\\n')}`\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new Error(\n      `Turbopack build failed with ${errors.length} errors:\\n${errors\n        .map((e) => {\n          return 'Page: ' + e.page + '\\n' + e.message\n        })\n        .join('\\n')}`\n    )\n  }\n\n  const time = process.hrtime(startTime)\n  return {\n    duration: time[0] + time[1] / 1e9,\n    buildTraceContext: undefined,\n    shutdownPromise,\n  }\n}\n\nlet shutdownPromise: Promise<void> | undefined\nexport async function workerMain(workerData: {\n  buildContext: typeof NextBuildContext\n}): Promise<Awaited<ReturnType<typeof turbopackBuild>>> {\n  // setup new build context from the serialized data passed from the parent\n  Object.assign(NextBuildContext, workerData.buildContext)\n\n  /// load the config because it's not serializable\n  NextBuildContext.config = await loadConfig(\n    PHASE_PRODUCTION_BUILD,\n    NextBuildContext.dir!\n  )\n\n  // Matches handling in build/index.ts\n  // https://github.com/vercel/next.js/blob/84f347fc86f4efc4ec9f13615c215e4b9fb6f8f0/packages/next/src/build/index.ts#L815-L818\n  // Ensures the `config.distDir` option is matched.\n  if (hasCustomExportOutput(NextBuildContext.config)) {\n    NextBuildContext.config.distDir = '.next'\n  }\n\n  // Clone the telemetry for worker\n  const telemetry = new Telemetry({\n    distDir: NextBuildContext.config.distDir,\n  })\n  setGlobal('telemetry', telemetry)\n\n  const result = await turbopackBuild()\n  shutdownPromise = result.shutdownPromise\n  return result\n}\n\nexport async function waitForShutdown(): Promise<void> {\n  if (shutdownPromise) {\n    await shutdownPromise\n  }\n}\n"], "names": ["path", "validateTurboNextConfig", "formatIssue", "getTurbopackJsConfig", "isPersistentCachingEnabled", "isRelevantWarning", "NextBuildContext", "createDefineEnv", "loadBindings", "<PERSON><PERSON>", "handleEntrypoints", "handlePagesErrorRoute", "handleRouteType", "TurbopackManifestLoader", "createProgress", "Log", "promises", "fs", "PHASE_PRODUCTION_BUILD", "loadConfig", "hasCustomExportOutput", "Telemetry", "setGlobal", "IS_TURBOPACK_BUILD", "process", "env", "TURBOPACK", "TURBOPACK_BUILD", "turbopackBuild", "config", "Error", "dir", "isDev", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "hasRewrites", "rewrites", "appDirOnly", "noMangling", "startTime", "hrtime", "bindings", "experimental", "useWasmBinary", "dev", "supportedBrowsers", "persistentCaching", "project", "turbo", "createProject", "projectPath", "rootPath", "root", "outputFileTracingRoot", "nextConfig", "jsConfig", "watch", "enable", "defineEnv", "isTurbopack", "clientRouterFilters", "fetchCacheKeyPrefix", "middlewareMatchers", "undefined", "browserslistQuery", "join", "memoryLimit", "dependencyTracking", "mkdir", "recursive", "writeFile", "JSON", "stringify", "type", "entrypointsSubscription", "entrypointsSubscribe", "currentEntrypoints", "global", "app", "document", "error", "middleware", "instrumentation", "Map", "page", "currentEntryIssues", "manifest<PERSON><PERSON>der", "entrypointsResult", "next", "done", "return", "catch", "entrypoints", "value", "topLevelErrors", "issue", "issues", "push", "message", "length", "map", "e", "productionRewrites", "logErrors", "progress", "size", "INITIAL_CONCURRENCY", "CONCURRENCY", "sema", "remainingRampup", "enqueue", "fn", "acquire", "release", "run", "route", "Promise", "all", "writeManifests", "devRewrites", "errors", "warnings", "entryIssues", "values", "severity", "shutdownPromise", "shutdown", "warn", "time", "duration", "buildTraceContext", "worker<PERSON>ain", "workerData", "Object", "assign", "buildContext", "telemetry", "result", "waitForShutdown"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SACEC,WAAW,EACXC,oBAAoB,EACpBC,0BAA0B,EAC1BC,iBAAiB,QAEZ,mCAAkC;AACzC,SAASC,gBAAgB,QAAQ,mBAAkB;AACnD,SAASC,eAAe,EAAEC,YAAY,QAAQ,SAAQ;AACtD,SAASC,IAAI,QAAQ,gCAA+B;AACpD,SACEC,iBAAiB,EACjBC,qBAAqB,EACrBC,eAAe,QACV,wBAAuB;AAE9B,SAASC,uBAAuB,QAAQ,6CAA4C;AACpF,SAASC,cAAc,QAAQ,cAAa;AAC5C,YAAYC,SAAS,gBAAe;AACpC,SAASC,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,sBAAsB,QAAQ,6BAA4B;AACnE,OAAOC,gBAAgB,sBAAqB;AAC5C,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,SAAS,QAAQ,cAAa;AAEvC,MAAMC,qBAAqBC,QAAQC,GAAG,CAACC,SAAS,IAAIF,QAAQC,GAAG,CAACE,eAAe;AAE/E,OAAO,eAAeC;QA0BgBC,sBAa9BA,4BAAAA,uBA4BWA;IA9DjB,IAAI,CAACN,oBAAoB;QACvB,MAAM,qBAAqD,CAArD,IAAIO,MAAM,6CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IAEA,MAAM7B,wBAAwB;QAC5B8B,KAAKzB,iBAAiByB,GAAG;QACzBC,OAAO;IACT;IAEA,MAAMH,SAASvB,iBAAiBuB,MAAM;IACtC,MAAME,MAAMzB,iBAAiByB,GAAG;IAChC,MAAME,UAAU3B,iBAAiB2B,OAAO;IACxC,MAAMC,UAAU5B,iBAAiB4B,OAAO;IACxC,MAAMC,gBAAgB7B,iBAAiB6B,aAAa;IACpD,MAAMC,eAAe9B,iBAAiB8B,YAAY;IAClD,MAAMC,cAAc/B,iBAAiB+B,WAAW;IAChD,MAAMC,WAAWhC,iBAAiBgC,QAAQ;IAC1C,MAAMC,aAAajC,iBAAiBiC,UAAU;IAC9C,MAAMC,aAAalC,iBAAiBkC,UAAU;IAE9C,MAAMC,YAAYjB,QAAQkB,MAAM;IAChC,MAAMC,WAAW,MAAMnC,aAAaqB,2BAAAA,uBAAAA,OAAQe,YAAY,qBAApBf,qBAAsBgB,aAAa;IACvE,MAAMC,MAAM;IAEZ,iEAAiE;IACjE,MAAMC,oBAAoB;QACxB;KACD;IAED,MAAMC,oBAAoB5C,2BAA2ByB;IACrD,MAAMoB,UAAU,MAAMN,SAASO,KAAK,CAACC,aAAa,CAChD;QACEC,aAAarB;QACbsB,UACExB,EAAAA,wBAAAA,OAAOe,YAAY,sBAAnBf,6BAAAA,sBAAqBqB,KAAK,qBAA1BrB,2BAA4ByB,IAAI,KAAIzB,OAAO0B,qBAAqB,IAAIxB;QACtEE;QACAuB,YAAY3B;QACZ4B,UAAU,MAAMtD,qBAAqB4B,KAAKF;QAC1C6B,OAAO;YACLC,QAAQ;QACV;QACAb;QACArB,KAAKD,QAAQC,GAAG;QAChBmC,WAAWrD,gBAAgB;YACzBsD,aAAa;YACbC,qBAAqBxD,iBAAiBwD,mBAAmB;YACzDjC;YACAiB;YACAb;YACA8B,qBAAqBlC,OAAOe,YAAY,CAACmB,mBAAmB;YAC5D1B;YACA,uEAAuE;YACvE2B,oBAAoBC;QACtB;QACA/B;QACAC;QACAC;QACA8B,mBAAmBnB,kBAAkBoB,IAAI,CAAC;QAC1C3B;IACF,GACA;QACEQ;QACAoB,WAAW,GAAEvC,8BAAAA,OAAOe,YAAY,CAACM,KAAK,qBAAzBrB,4BAA2BuC,WAAW;QACnDC,oBAAoBrB;IACtB;IAGF,MAAM/B,GAAGqD,KAAK,CAACtE,KAAKmE,IAAI,CAAClC,SAAS,WAAW;QAAEsC,WAAW;IAAK;IAC/D,MAAMtD,GAAGqD,KAAK,CAACtE,KAAKmE,IAAI,CAAClC,SAAS,UAAUC,UAAU;QACpDqC,WAAW;IACb;IACA,MAAMtD,GAAGuD,SAAS,CAChBxE,KAAKmE,IAAI,CAAClC,SAAS,iBACnBwC,KAAKC,SAAS,CACZ;QACEC,MAAM;IACR,GACA,MACA;IAIJ,6DAA6D;IAC7D,MAAMC,0BAA0B3B,QAAQ4B,oBAAoB;IAC5D,MAAMC,qBAAkC;QACtCC,QAAQ;YACNC,KAAKf;YACLgB,UAAUhB;YACViB,OAAOjB;YAEPkB,YAAYlB;YACZmB,iBAAiBnB;QACnB;QAEAe,KAAK,IAAIK;QACTC,MAAM,IAAID;IACZ;IAEA,MAAME,qBAAqC,IAAIF;IAE/C,MAAMG,iBAAiB,IAAI3E,wBAAwB;QACjDqB;QACAD;QACAE;IACF;IAEA,MAAMsD,oBAAoB,MAAMb,wBAAwBc,IAAI;IAC5D,IAAID,kBAAkBE,IAAI,EAAE;QAC1B,MAAM,qBAAqD,CAArD,IAAI7D,MAAM,6CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IACA8C,wBAAwBgB,MAAM,oBAA9BhB,wBAAwBgB,MAAM,MAA9BhB,yBAAmCiB,KAAK,CAAC,KAAO;IAEhD,MAAMC,cAAcL,kBAAkBM,KAAK;IAE3C,MAAMC,iBAEA,EAAE;IACR,KAAK,MAAMC,SAASH,YAAYI,MAAM,CAAE;QACtCF,eAAeG,IAAI,CAAC;YAClBC,SAASlG,YAAY+F;QACvB;IACF;IAEA,IAAID,eAAeK,MAAM,GAAG,GAAG;QAC7B,MAAM,qBAIL,CAJK,IAAIvE,MACR,CAAC,4BAA4B,EAC3BkE,eAAeK,MAAM,CACtB,UAAU,EAAEL,eAAeM,GAAG,CAAC,CAACC,IAAMA,EAAEH,OAAO,EAAEjC,IAAI,CAAC,OAAO,GAH1D,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,MAAMzD,kBAAkB;QACtBoF;QACAhB;QACAS;QACAC;QACAgB,oBAAoBlE;QACpBmE,WAAW;IACb;IAEA,MAAMC,WAAW5F,eACfgE,mBAAmBQ,IAAI,CAACqB,IAAI,GAAG7B,mBAAmBE,GAAG,CAAC2B,IAAI,GAAG,GAC7D;IAEF,MAAM3F,WAA2B,EAAE;IAEnC,oDAAoD;IACpD,kDAAkD;IAClD,mDAAmD;IACnD,MAAM4F,sBAAsB;IAC5B,MAAMC,cAAc;IAEpB,MAAMC,OAAO,IAAIrG,KAAKmG;IACtB,IAAIG,kBAAkBF,cAAcD;IACpC,MAAMI,UAAU,CAACC;QACfjG,SAASmF,IAAI,CACX,AAAC,CAAA;YACC,MAAMW,KAAKI,OAAO;YAClB,IAAI;gBACF,MAAMD;YACR,SAAU;gBACRH,KAAKK,OAAO;gBACZ,IAAIJ,kBAAkB,GAAG;oBACvBA;oBACAD,KAAKK,OAAO;gBACd;gBACAT,SAASU,GAAG;YACd;QACF,CAAA;IAEJ;IAEA,IAAI,CAAC7E,YAAY;QACf,KAAK,MAAM,CAAC+C,MAAM+B,MAAM,IAAIvC,mBAAmBQ,IAAI,CAAE;YACnD0B,QAAQ,IACNpG,gBAAgB;oBACd0E;oBACA+B;oBACA9B;oBACAO,aAAahB;oBACbU;oBACAgB,oBAAoBlE;oBACpBmE,WAAW;gBACb;QAEJ;IACF;IAEA,KAAK,MAAM,CAACnB,MAAM+B,MAAM,IAAIvC,mBAAmBE,GAAG,CAAE;QAClDgC,QAAQ,IACNpG,gBAAgB;gBACd0E;gBACA+B;gBACA9B;gBACAO,aAAahB;gBACbU;gBACAgB,oBAAoBlE;gBACpBmE,WAAW;YACb;IAEJ;IAEAO,QAAQ,IACNrG,sBAAsB;YACpB4E;YACAO,aAAahB;YACbU;YACAgB,oBAAoBlE;YACpBmE,WAAW;QACb;IAEF,MAAMa,QAAQC,GAAG,CAACvG;IAElB,MAAMwE,eAAegC,cAAc,CAAC;QAClCC,aAAaxD;QACbuC,oBAAoBlE;QACpBwD,aAAahB;IACf;IAEA,MAAM4C,SAGA,EAAE;IACR,MAAMC,WAGA,EAAE;IACR,KAAK,MAAM,CAACrC,MAAMsC,YAAY,IAAIrC,mBAAoB;QACpD,KAAK,MAAMU,SAAS2B,YAAYC,MAAM,GAAI;YACxC,IAAI5B,MAAM6B,QAAQ,KAAK,WAAW;gBAChCJ,OAAOvB,IAAI,CAAC;oBACVb;oBACAc,SAASlG,YAAY+F;gBACvB;YACF,OAAO;gBACL,IAAI5F,kBAAkB4F,QAAQ;oBAC5B0B,SAASxB,IAAI,CAAC;wBACZb;wBACAc,SAASlG,YAAY+F;oBACvB;gBACF;YACF;QACF;IACF;IAEA,MAAM8B,kBAAkB9E,QAAQ+E,QAAQ;IAExC,IAAIL,SAAStB,MAAM,GAAG,GAAG;QACvBtF,IAAIkH,IAAI,CACN,CAAC,0BAA0B,EAAEN,SAAStB,MAAM,CAAC,YAAY,EAAEsB,SACxDrB,GAAG,CAAC,CAACC;YACJ,OAAO,WAAWA,EAAEjB,IAAI,GAAG,OAAOiB,EAAEH,OAAO;QAC7C,GACCjC,IAAI,CAAC,OAAO;IAEnB;IAEA,IAAIuD,OAAOrB,MAAM,GAAG,GAAG;QACrB,MAAM,qBAML,CANK,IAAIvE,MACR,CAAC,4BAA4B,EAAE4F,OAAOrB,MAAM,CAAC,UAAU,EAAEqB,OACtDpB,GAAG,CAAC,CAACC;YACJ,OAAO,WAAWA,EAAEjB,IAAI,GAAG,OAAOiB,EAAEH,OAAO;QAC7C,GACCjC,IAAI,CAAC,OAAO,GALX,qBAAA;mBAAA;wBAAA;0BAAA;QAMN;IACF;IAEA,MAAM+D,OAAO1G,QAAQkB,MAAM,CAACD;IAC5B,OAAO;QACL0F,UAAUD,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,GAAG;QAC9BE,mBAAmBnE;QACnB8D;IACF;AACF;AAEA,IAAIA;AACJ,OAAO,eAAeM,WAAWC,UAEhC;IACC,0EAA0E;IAC1EC,OAAOC,MAAM,CAAClI,kBAAkBgI,WAAWG,YAAY;IAEvD,iDAAiD;IACjDnI,iBAAiBuB,MAAM,GAAG,MAAMV,WAC9BD,wBACAZ,iBAAiByB,GAAG;IAGtB,qCAAqC;IACrC,6HAA6H;IAC7H,kDAAkD;IAClD,IAAIX,sBAAsBd,iBAAiBuB,MAAM,GAAG;QAClDvB,iBAAiBuB,MAAM,CAACI,OAAO,GAAG;IACpC;IAEA,iCAAiC;IACjC,MAAMyG,YAAY,IAAIrH,UAAU;QAC9BY,SAAS3B,iBAAiBuB,MAAM,CAACI,OAAO;IAC1C;IACAX,UAAU,aAAaoH;IAEvB,MAAMC,SAAS,MAAM/G;IACrBmG,kBAAkBY,OAAOZ,eAAe;IACxC,OAAOY;AACT;AAEA,OAAO,eAAeC;IACpB,IAAIb,iBAAiB;QACnB,MAAMA;IACR;AACF"}