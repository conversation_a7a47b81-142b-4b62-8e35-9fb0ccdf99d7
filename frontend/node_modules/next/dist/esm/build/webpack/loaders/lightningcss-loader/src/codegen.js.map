{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/codegen.ts"], "sourcesContent": ["import type { LoaderContext } from 'next/dist/compiled/webpack/webpack'\nimport camelCase from '../../css-loader/src/camelcase'\nimport {\n  dashesCamelCase,\n  normalizeSourceMapForRuntime,\n} from '../../css-loader/src/utils'\n\nexport interface CssImport {\n  icss?: boolean\n  importName: string\n  url: string\n  type?: 'url' | string\n  index?: number\n}\n\nexport interface CssExport {\n  name: string\n  value: string\n}\n\nexport interface ApiParam {\n  url?: string\n  importName?: string\n\n  layer?: string\n  supports?: string\n  media?: string\n\n  dedupe?: boolean\n  index?: number\n}\n\nexport interface ApiReplacement {\n  replacementName: string\n  localName?: string\n  importName: string\n  needQuotes?: boolean\n  hash?: string\n}\n\nexport function getImportCode(imports: CssImport[], options: any) {\n  let code = ''\n\n  for (const item of imports) {\n    const { importName, url, icss } = item\n\n    if (options.esModule) {\n      if (icss && options.modules.namedExport) {\n        code += `import ${\n          options.modules.exportOnlyLocals ? '' : `${importName}, `\n        }* as ${importName}_NAMED___ from ${url};\\n`\n      } else {\n        code += `import ${importName} from ${url};\\n`\n      }\n    } else {\n      code += `var ${importName} = require(${url});\\n`\n    }\n  }\n\n  return code ? `// Imports\\n${code}` : ''\n}\n\nexport function getModuleCode(\n  result: { map: any; css: any },\n  api: ApiParam[],\n  replacements: ApiReplacement[],\n  options: any,\n  loaderContext: LoaderContext<any>\n) {\n  if (options.modules.exportOnlyLocals === true) {\n    return ''\n  }\n\n  const sourceMapValue = options.sourceMap\n    ? `,${normalizeSourceMapForRuntime(result.map, loaderContext)}`\n    : ''\n\n  let code = JSON.stringify(result.css)\n  let beforeCode = `var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(${options.sourceMap});\\n`\n\n  for (const item of api) {\n    const { url, media, dedupe } = item\n\n    beforeCode += url\n      ? `___CSS_LOADER_EXPORT___.push([module.id, ${JSON.stringify(\n          `@import url(${url});`\n        )}${media ? `, ${JSON.stringify(media)}` : ''}]);\\n`\n      : `___CSS_LOADER_EXPORT___.i(${item.importName}${\n          media ? `, ${JSON.stringify(media)}` : dedupe ? ', \"\"' : ''\n        }${dedupe ? ', true' : ''});\\n`\n  }\n\n  for (const item of replacements) {\n    const { replacementName, importName, localName } = item\n\n    if (localName) {\n      code = code.replace(new RegExp(replacementName, 'g'), () =>\n        options.modules.namedExport\n          ? `\" + ${importName}_NAMED___[${JSON.stringify(\n              camelCase(localName)\n            )}] + \"`\n          : `\" + ${importName}.locals[${JSON.stringify(localName)}] + \"`\n      )\n    } else {\n      const { hash, needQuotes } = item\n      const getUrlOptions = [\n        ...(hash ? [`hash: ${JSON.stringify(hash)}`] : []),\n        ...(needQuotes ? 'needQuotes: true' : []),\n      ]\n      const preparedOptions =\n        getUrlOptions.length > 0 ? `, { ${getUrlOptions.join(', ')} }` : ''\n\n      beforeCode += `var ${replacementName} = ___CSS_LOADER_GET_URL_IMPORT___(${importName}${preparedOptions});\\n`\n      code = code.replace(\n        new RegExp(replacementName, 'g'),\n        () => `\" + ${replacementName} + \"`\n      )\n    }\n  }\n\n  return `${beforeCode}// Module\\n___CSS_LOADER_EXPORT___.push([module.id, ${code}, \"\"${sourceMapValue}]);\\n`\n}\n\nexport function getExportCode(\n  exports: CssExport[],\n  replacements: ApiReplacement[],\n  options: any\n) {\n  let code = '// Exports\\n'\n  let localsCode = ''\n\n  const addExportToLocalsCode = (name: string, value: any) => {\n    if (options.modules.namedExport) {\n      localsCode += `export const ${camelCase(name)} = ${JSON.stringify(\n        value\n      )};\\n`\n    } else {\n      if (localsCode) {\n        localsCode += `,\\n`\n      }\n\n      localsCode += `\\t${JSON.stringify(name)}: ${JSON.stringify(value)}`\n    }\n  }\n\n  for (const { name, value } of exports) {\n    switch (options.modules.exportLocalsConvention) {\n      case 'camelCase': {\n        addExportToLocalsCode(name, value)\n\n        const modifiedName = camelCase(name)\n\n        if (modifiedName !== name) {\n          addExportToLocalsCode(modifiedName, value)\n        }\n        break\n      }\n      case 'camelCaseOnly': {\n        addExportToLocalsCode(camelCase(name), value)\n        break\n      }\n      case 'dashes': {\n        addExportToLocalsCode(name, value)\n\n        const modifiedName = dashesCamelCase(name)\n\n        if (modifiedName !== name) {\n          addExportToLocalsCode(modifiedName, value)\n        }\n        break\n      }\n      case 'dashesOnly': {\n        addExportToLocalsCode(dashesCamelCase(name), value)\n        break\n      }\n      case 'asIs':\n      default:\n        addExportToLocalsCode(name, value)\n        break\n    }\n  }\n\n  for (const item of replacements) {\n    const { replacementName, localName } = item\n\n    if (localName) {\n      const { importName } = item\n\n      localsCode = localsCode.replace(new RegExp(replacementName, 'g'), () => {\n        if (options.modules.namedExport) {\n          return `\" + ${importName}_NAMED___[${JSON.stringify(\n            camelCase(localName)\n          )}] + \"`\n        } else if (options.modules.exportOnlyLocals) {\n          return `\" + ${importName}[${JSON.stringify(localName)}] + \"`\n        }\n\n        return `\" + ${importName}.locals[${JSON.stringify(localName)}] + \"`\n      })\n    } else {\n      localsCode = localsCode.replace(\n        new RegExp(replacementName, 'g'),\n        () => `\" + ${replacementName} + \"`\n      )\n    }\n  }\n\n  if (options.modules.exportOnlyLocals) {\n    code += options.modules.namedExport\n      ? localsCode\n      : `${\n          options.esModule ? 'export default' : 'module.exports ='\n        } {\\n${localsCode}\\n};\\n`\n\n    return code\n  }\n\n  if (localsCode) {\n    code += options.modules.namedExport\n      ? localsCode\n      : `___CSS_LOADER_EXPORT___.locals = {\\n${localsCode}\\n};\\n`\n  }\n\n  code += `${\n    options.esModule ? 'export default' : 'module.exports ='\n  } ___CSS_LOADER_EXPORT___;\\n`\n\n  return code\n}\n"], "names": ["camelCase", "dashesCamelCase", "normalizeSourceMapForRuntime", "getImportCode", "imports", "options", "code", "item", "importName", "url", "icss", "esModule", "modules", "namedExport", "exportOnlyLocals", "getModuleCode", "result", "api", "replacements", "loaderContext", "sourceMapValue", "sourceMap", "map", "JSON", "stringify", "css", "beforeCode", "media", "dedupe", "replacement<PERSON>ame", "localName", "replace", "RegExp", "hash", "needQuotes", "getUrlOptions", "preparedOptions", "length", "join", "getExportCode", "exports", "localsCode", "addExportToLocalsCode", "name", "value", "exportLocalsConvention", "modifiedName"], "mappings": "AACA,OAAOA,eAAe,iCAAgC;AACtD,SACEC,eAAe,EACfC,4BAA4B,QACvB,6BAA4B;AAmCnC,OAAO,SAASC,cAAcC,OAAoB,EAAEC,OAAY;IAC9D,IAAIC,OAAO;IAEX,KAAK,MAAMC,QAAQH,QAAS;QAC1B,MAAM,EAAEI,UAAU,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGH;QAElC,IAAIF,QAAQM,QAAQ,EAAE;YACpB,IAAID,QAAQL,QAAQO,OAAO,CAACC,WAAW,EAAE;gBACvCP,QAAQ,CAAC,OAAO,EACdD,QAAQO,OAAO,CAACE,gBAAgB,GAAG,KAAK,GAAGN,WAAW,EAAE,CAAC,CAC1D,KAAK,EAAEA,WAAW,eAAe,EAAEC,IAAI,GAAG,CAAC;YAC9C,OAAO;gBACLH,QAAQ,CAAC,OAAO,EAAEE,WAAW,MAAM,EAAEC,IAAI,GAAG,CAAC;YAC/C;QACF,OAAO;YACLH,QAAQ,CAAC,IAAI,EAAEE,WAAW,WAAW,EAAEC,IAAI,IAAI,CAAC;QAClD;IACF;IAEA,OAAOH,OAAO,CAAC,YAAY,EAAEA,MAAM,GAAG;AACxC;AAEA,OAAO,SAASS,cACdC,MAA8B,EAC9BC,GAAe,EACfC,YAA8B,EAC9Bb,OAAY,EACZc,aAAiC;IAEjC,IAAId,QAAQO,OAAO,CAACE,gBAAgB,KAAK,MAAM;QAC7C,OAAO;IACT;IAEA,MAAMM,iBAAiBf,QAAQgB,SAAS,GACpC,CAAC,CAAC,EAAEnB,6BAA6Bc,OAAOM,GAAG,EAAEH,gBAAgB,GAC7D;IAEJ,IAAIb,OAAOiB,KAAKC,SAAS,CAACR,OAAOS,GAAG;IACpC,IAAIC,aAAa,CAAC,0DAA0D,EAAErB,QAAQgB,SAAS,CAAC,IAAI,CAAC;IAErG,KAAK,MAAMd,QAAQU,IAAK;QACtB,MAAM,EAAER,GAAG,EAAEkB,KAAK,EAAEC,MAAM,EAAE,GAAGrB;QAE/BmB,cAAcjB,MACV,CAAC,yCAAyC,EAAEc,KAAKC,SAAS,CACxD,CAAC,YAAY,EAAEf,IAAI,EAAE,CAAC,IACpBkB,QAAQ,CAAC,EAAE,EAAEJ,KAAKC,SAAS,CAACG,QAAQ,GAAG,GAAG,KAAK,CAAC,GACpD,CAAC,0BAA0B,EAAEpB,KAAKC,UAAU,GAC1CmB,QAAQ,CAAC,EAAE,EAAEJ,KAAKC,SAAS,CAACG,QAAQ,GAAGC,SAAS,SAAS,KACxDA,SAAS,WAAW,GAAG,IAAI,CAAC;IACrC;IAEA,KAAK,MAAMrB,QAAQW,aAAc;QAC/B,MAAM,EAAEW,eAAe,EAAErB,UAAU,EAAEsB,SAAS,EAAE,GAAGvB;QAEnD,IAAIuB,WAAW;YACbxB,OAAOA,KAAKyB,OAAO,CAAC,IAAIC,OAAOH,iBAAiB,MAAM,IACpDxB,QAAQO,OAAO,CAACC,WAAW,GACvB,CAAC,IAAI,EAAEL,WAAW,UAAU,EAAEe,KAAKC,SAAS,CAC1CxB,UAAU8B,YACV,KAAK,CAAC,GACR,CAAC,IAAI,EAAEtB,WAAW,QAAQ,EAAEe,KAAKC,SAAS,CAACM,WAAW,KAAK,CAAC;QAEpE,OAAO;YACL,MAAM,EAAEG,IAAI,EAAEC,UAAU,EAAE,GAAG3B;YAC7B,MAAM4B,gBAAgB;mBAChBF,OAAO;oBAAC,CAAC,MAAM,EAAEV,KAAKC,SAAS,CAACS,OAAO;iBAAC,GAAG,EAAE;mBAC7CC,aAAa,qBAAqB,EAAE;aACzC;YACD,MAAME,kBACJD,cAAcE,MAAM,GAAG,IAAI,CAAC,IAAI,EAAEF,cAAcG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;YAEnEZ,cAAc,CAAC,IAAI,EAAEG,gBAAgB,mCAAmC,EAAErB,aAAa4B,gBAAgB,IAAI,CAAC;YAC5G9B,OAAOA,KAAKyB,OAAO,CACjB,IAAIC,OAAOH,iBAAiB,MAC5B,IAAM,CAAC,IAAI,EAAEA,gBAAgB,IAAI,CAAC;QAEtC;IACF;IAEA,OAAO,GAAGH,WAAW,oDAAoD,EAAEpB,KAAK,IAAI,EAAEc,eAAe,KAAK,CAAC;AAC7G;AAEA,OAAO,SAASmB,cACdC,OAAoB,EACpBtB,YAA8B,EAC9Bb,OAAY;IAEZ,IAAIC,OAAO;IACX,IAAImC,aAAa;IAEjB,MAAMC,wBAAwB,CAACC,MAAcC;QAC3C,IAAIvC,QAAQO,OAAO,CAACC,WAAW,EAAE;YAC/B4B,cAAc,CAAC,aAAa,EAAEzC,UAAU2C,MAAM,GAAG,EAAEpB,KAAKC,SAAS,CAC/DoB,OACA,GAAG,CAAC;QACR,OAAO;YACL,IAAIH,YAAY;gBACdA,cAAc,CAAC,GAAG,CAAC;YACrB;YAEAA,cAAc,CAAC,EAAE,EAAElB,KAAKC,SAAS,CAACmB,MAAM,EAAE,EAAEpB,KAAKC,SAAS,CAACoB,QAAQ;QACrE;IACF;IAEA,KAAK,MAAM,EAAED,IAAI,EAAEC,KAAK,EAAE,IAAIJ,QAAS;QACrC,OAAQnC,QAAQO,OAAO,CAACiC,sBAAsB;YAC5C,KAAK;gBAAa;oBAChBH,sBAAsBC,MAAMC;oBAE5B,MAAME,eAAe9C,UAAU2C;oBAE/B,IAAIG,iBAAiBH,MAAM;wBACzBD,sBAAsBI,cAAcF;oBACtC;oBACA;gBACF;YACA,KAAK;gBAAiB;oBACpBF,sBAAsB1C,UAAU2C,OAAOC;oBACvC;gBACF;YACA,KAAK;gBAAU;oBACbF,sBAAsBC,MAAMC;oBAE5B,MAAME,eAAe7C,gBAAgB0C;oBAErC,IAAIG,iBAAiBH,MAAM;wBACzBD,sBAAsBI,cAAcF;oBACtC;oBACA;gBACF;YACA,KAAK;gBAAc;oBACjBF,sBAAsBzC,gBAAgB0C,OAAOC;oBAC7C;gBACF;YACA,KAAK;YACL;gBACEF,sBAAsBC,MAAMC;gBAC5B;QACJ;IACF;IAEA,KAAK,MAAMrC,QAAQW,aAAc;QAC/B,MAAM,EAAEW,eAAe,EAAEC,SAAS,EAAE,GAAGvB;QAEvC,IAAIuB,WAAW;YACb,MAAM,EAAEtB,UAAU,EAAE,GAAGD;YAEvBkC,aAAaA,WAAWV,OAAO,CAAC,IAAIC,OAAOH,iBAAiB,MAAM;gBAChE,IAAIxB,QAAQO,OAAO,CAACC,WAAW,EAAE;oBAC/B,OAAO,CAAC,IAAI,EAAEL,WAAW,UAAU,EAAEe,KAAKC,SAAS,CACjDxB,UAAU8B,YACV,KAAK,CAAC;gBACV,OAAO,IAAIzB,QAAQO,OAAO,CAACE,gBAAgB,EAAE;oBAC3C,OAAO,CAAC,IAAI,EAAEN,WAAW,CAAC,EAAEe,KAAKC,SAAS,CAACM,WAAW,KAAK,CAAC;gBAC9D;gBAEA,OAAO,CAAC,IAAI,EAAEtB,WAAW,QAAQ,EAAEe,KAAKC,SAAS,CAACM,WAAW,KAAK,CAAC;YACrE;QACF,OAAO;YACLW,aAAaA,WAAWV,OAAO,CAC7B,IAAIC,OAAOH,iBAAiB,MAC5B,IAAM,CAAC,IAAI,EAAEA,gBAAgB,IAAI,CAAC;QAEtC;IACF;IAEA,IAAIxB,QAAQO,OAAO,CAACE,gBAAgB,EAAE;QACpCR,QAAQD,QAAQO,OAAO,CAACC,WAAW,GAC/B4B,aACA,GACEpC,QAAQM,QAAQ,GAAG,mBAAmB,mBACvC,IAAI,EAAE8B,WAAW,MAAM,CAAC;QAE7B,OAAOnC;IACT;IAEA,IAAImC,YAAY;QACdnC,QAAQD,QAAQO,OAAO,CAACC,WAAW,GAC/B4B,aACA,CAAC,oCAAoC,EAAEA,WAAW,MAAM,CAAC;IAC/D;IAEAnC,QAAQ,GACND,QAAQM,QAAQ,GAAG,mBAAmB,mBACvC,2BAA2B,CAAC;IAE7B,OAAOL;AACT"}