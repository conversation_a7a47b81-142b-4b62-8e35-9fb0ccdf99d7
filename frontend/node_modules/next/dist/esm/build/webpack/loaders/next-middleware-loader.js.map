{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-middleware-loader.ts"], "sourcesContent": ["import type {\n  MiddlewareConfig,\n  MiddlewareMatcher,\n} from '../../analysis/get-page-static-info'\nimport { getModuleBuildInfo } from './get-module-build-info'\nimport { MIDDLEWARE_LOCATION_REGEXP } from '../../../lib/constants'\nimport { loadEntrypoint } from '../../load-entrypoint'\n\nexport type MiddlewareLoaderOptions = {\n  absolutePagePath: string\n  page: string\n  rootDir: string\n  matchers?: string\n  preferredRegion: string | string[] | undefined\n  middlewareConfig: string\n}\n\n// matchers can have special characters that break the loader params\n// parsing so we base64 encode/decode the string\nexport function encodeMatchers(matchers: MiddlewareMatcher[]) {\n  return Buffer.from(JSON.stringify(matchers)).toString('base64')\n}\n\nexport function decodeMatchers(encodedMatchers: string) {\n  return JSON.parse(\n    Buffer.from(encodedMatchers, 'base64').toString()\n  ) as MiddlewareMatcher[]\n}\n\nexport default async function middlewareLoader(this: any) {\n  const {\n    absolutePagePath,\n    page,\n    rootDir,\n    matchers: encodedMatchers,\n    preferredRegion,\n    middlewareConfig: middlewareConfigBase64,\n  }: MiddlewareLoaderOptions = this.getOptions()\n  const matchers = encodedMatchers ? decodeMatchers(encodedMatchers) : undefined\n  const pagePath = this.utils.contextify(\n    this.context || this.rootContext,\n    absolutePagePath\n  )\n\n  const middlewareConfig: MiddlewareConfig = JSON.parse(\n    Buffer.from(middlewareConfigBase64, 'base64').toString()\n  )\n  const buildInfo = getModuleBuildInfo(this._module)\n  buildInfo.nextEdgeMiddleware = {\n    matchers,\n    page:\n      page.replace(new RegExp(`/${MIDDLEWARE_LOCATION_REGEXP}$`), '') || '/',\n  }\n  buildInfo.rootDir = rootDir\n  buildInfo.route = {\n    page,\n    absolutePagePath,\n    preferredRegion,\n    middlewareConfig,\n  }\n\n  return await loadEntrypoint('middleware', {\n    VAR_USERLAND: pagePath,\n    VAR_DEFINITION_PAGE: page,\n  })\n}\n"], "names": ["getModuleBuildInfo", "MIDDLEWARE_LOCATION_REGEXP", "loadEntrypoint", "encodeMatchers", "matchers", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "decodeMatchers", "encodedMatchers", "parse", "middlewareLoader", "absolutePagePath", "page", "rootDir", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "getOptions", "undefined", "pagePath", "utils", "contextify", "context", "rootContext", "buildInfo", "_module", "nextEdgeMiddleware", "replace", "RegExp", "route", "VAR_USERLAND", "VAR_DEFINITION_PAGE"], "mappings": "AAIA,SAASA,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,0BAA0B,QAAQ,yBAAwB;AACnE,SAASC,cAAc,QAAQ,wBAAuB;AAWtD,oEAAoE;AACpE,gDAAgD;AAChD,OAAO,SAASC,eAAeC,QAA6B;IAC1D,OAAOC,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ,WAAWK,QAAQ,CAAC;AACxD;AAEA,OAAO,SAASC,eAAeC,eAAuB;IACpD,OAAOJ,KAAKK,KAAK,CACfP,OAAOC,IAAI,CAACK,iBAAiB,UAAUF,QAAQ;AAEnD;AAEA,eAAe,eAAeI;IAC5B,MAAM,EACJC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPZ,UAAUO,eAAe,EACzBM,eAAe,EACfC,kBAAkBC,sBAAsB,EACzC,GAA4B,IAAI,CAACC,UAAU;IAC5C,MAAMhB,WAAWO,kBAAkBD,eAAeC,mBAAmBU;IACrE,MAAMC,WAAW,IAAI,CAACC,KAAK,CAACC,UAAU,CACpC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChCZ;IAGF,MAAMI,mBAAqCX,KAAKK,KAAK,CACnDP,OAAOC,IAAI,CAACa,wBAAwB,UAAUV,QAAQ;IAExD,MAAMkB,YAAY3B,mBAAmB,IAAI,CAAC4B,OAAO;IACjDD,UAAUE,kBAAkB,GAAG;QAC7BzB;QACAW,MACEA,KAAKe,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAE9B,2BAA2B,CAAC,CAAC,GAAG,OAAO;IACvE;IACA0B,UAAUX,OAAO,GAAGA;IACpBW,UAAUK,KAAK,GAAG;QAChBjB;QACAD;QACAG;QACAC;IACF;IAEA,OAAO,MAAMhB,eAAe,cAAc;QACxC+B,cAAcX;QACdY,qBAAqBnB;IACvB;AACF"}