{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.ts"], "sourcesContent": ["const isOldIE = (function isOldIE() {\n  let memo: any\n\n  return function memorize() {\n    if (typeof memo === 'undefined') {\n      // Test for IE <= 9 as proposed by Browserhacks\n      // @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805\n      // Tests for existence of standard globals is to allow style-loader\n      // to operate correctly into non-standard environments\n      // @see https://github.com/webpack-contrib/style-loader/issues/177\n      memo = Boolean(window && document && document.all && !window.atob)\n    }\n\n    return memo\n  }\n})()\n\nconst getTargetElement = (function () {\n  const memo: any = {}\n\n  return function memorize(target: any) {\n    if (typeof memo[target] === 'undefined') {\n      let styleTarget: any = document.querySelector(target)\n\n      // Special case to return head of iframe instead of iframe itself\n      if (\n        window.HTMLIFrameElement &&\n        styleTarget instanceof window.HTMLIFrameElement\n      ) {\n        try {\n          // This will throw an exception if access to iframe is blocked\n          // due to cross-origin restrictions\n          styleTarget = (styleTarget as any).contentDocument.head\n        } catch (e) {\n          // istanbul ignore next\n          styleTarget = null\n        }\n      }\n\n      memo[target] = styleTarget\n    }\n\n    return memo[target]\n  }\n})()\n\nconst stylesInDom: any = []\n\nfunction getIndexByIdentifier(identifier: any) {\n  let result = -1\n\n  for (let i = 0; i < stylesInDom.length; i++) {\n    if (stylesInDom[i].identifier === identifier) {\n      result = i\n      break\n    }\n  }\n\n  return result\n}\n\nfunction modulesToDom(list: any, options: any) {\n  const idCountMap: any = {}\n  const identifiers = []\n\n  for (let i = 0; i < list.length; i++) {\n    const item = list[i]\n    const id = options.base ? item[0] + options.base : item[0]\n    const count = idCountMap[id] || 0\n    const identifier = id + ' ' + count.toString()\n\n    idCountMap[id] = count + 1\n\n    const index = getIndexByIdentifier(identifier)\n    const obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n    }\n\n    if (index !== -1) {\n      stylesInDom[index].references++\n      stylesInDom[index].updater(obj)\n    } else {\n      stylesInDom.push({\n        identifier: identifier,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        updater: addStyle(obj, options),\n        references: 1,\n      })\n    }\n\n    identifiers.push(identifier)\n  }\n\n  return identifiers\n}\n\nfunction insertStyleElement(options: any) {\n  const style = document.createElement('style')\n  const attributes = options.attributes || {}\n\n  if (typeof attributes.nonce === 'undefined') {\n    const nonce =\n      // eslint-disable-next-line no-undef\n      typeof __webpack_nonce__ !== 'undefined' ? __webpack_nonce__ : null\n\n    if (nonce) {\n      attributes.nonce = nonce\n    }\n  }\n\n  Object.keys(attributes).forEach(function (key) {\n    style.setAttribute(key, attributes[key])\n  })\n\n  if (typeof options.insert === 'function') {\n    options.insert(style)\n  } else {\n    const target = getTargetElement(options.insert || 'head')\n\n    if (!target) {\n      throw new Error(\n        \"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\"\n      )\n    }\n\n    target.appendChild(style)\n  }\n\n  return style\n}\n\nfunction removeStyleElement(style: any) {\n  // istanbul ignore if\n  if (style.parentNode === null) {\n    return false\n  }\n\n  style.parentNode.removeChild(style)\n}\n\n/* istanbul ignore next  */\nconst replaceText = (function replaceText() {\n  const textStore: any = []\n\n  return function replace(index: any, replacement: any) {\n    textStore[index] = replacement\n\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag(style: any, index: any, remove: any, obj: any) {\n  const css = remove\n    ? ''\n    : obj.media\n      ? '@media ' + obj.media + ' {' + obj.css + '}'\n      : obj.css\n\n  // For old IE\n  /* istanbul ignore if  */\n  if (style.styleSheet) {\n    style.styleSheet.cssText = replaceText(index, css)\n  } else {\n    const cssNode = document.createTextNode(css)\n    const childNodes = style.childNodes\n\n    if (childNodes[index]) {\n      style.removeChild(childNodes[index])\n    }\n\n    if (childNodes.length) {\n      style.insertBefore(cssNode, childNodes[index])\n    } else {\n      style.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag(style: any, _options: any, obj: any) {\n  let css = obj.css\n  const media = obj.media\n  const sourceMap = obj.sourceMap\n\n  if (media) {\n    style.setAttribute('media', media)\n  } else {\n    style.removeAttribute('media')\n  }\n\n  if (sourceMap && typeof btoa !== 'undefined') {\n    css +=\n      '\\n/*# sourceMappingURL=data:application/json;base64,' +\n      btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) +\n      ' */'\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css\n  } else {\n    while (style.firstChild) {\n      style.removeChild(style.firstChild)\n    }\n\n    style.appendChild(document.createTextNode(css))\n  }\n}\n\nlet singleton: any = null\nlet singletonCounter = 0\n\nfunction addStyle(obj: any, options: any) {\n  let style: any\n  let update: any\n  let remove: any\n\n  if (options.singleton) {\n    const styleIndex = singletonCounter++\n\n    style = singleton || (singleton = insertStyleElement(options))\n\n    update = applyToSingletonTag.bind(null, style, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, style, styleIndex, true)\n  } else {\n    style = insertStyleElement(options)\n\n    update = applyToTag.bind(null, style, options)\n    remove = function () {\n      removeStyleElement(style)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle(newObj: any) {\n    if (newObj) {\n      if (\n        newObj.css === obj.css &&\n        newObj.media === obj.media &&\n        newObj.sourceMap === obj.sourceMap\n      ) {\n        return\n      }\n\n      update((obj = newObj))\n    } else {\n      remove()\n    }\n  }\n}\n\nmodule.exports = function (list: any, options: any) {\n  options = options || {}\n\n  // Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n  // tags it will allow on a page\n  if (!options.singleton && typeof options.singleton !== 'boolean') {\n    options.singleton = isOldIE()\n  }\n\n  list = list || []\n\n  let lastIdentifiers = modulesToDom(list, options)\n\n  return function update(newList: any) {\n    newList = newList || []\n\n    if (Object.prototype.toString.call(newList) !== '[object Array]') {\n      return\n    }\n\n    for (let i = 0; i < lastIdentifiers.length; i++) {\n      const identifier = lastIdentifiers[i]\n      const index = getIndexByIdentifier(identifier)\n\n      stylesInDom[index].references--\n    }\n\n    const newLastIdentifiers = modulesToDom(newList, options)\n\n    for (let i = 0; i < lastIdentifiers.length; i++) {\n      const identifier = lastIdentifiers[i]\n      const index = getIndexByIdentifier(identifier)\n\n      if (stylesInDom[index].references === 0) {\n        stylesInDom[index].updater()\n        stylesInDom.splice(index, 1)\n      }\n    }\n\n    lastIdentifiers = newLastIdentifiers\n  }\n}\n"], "names": ["isOldIE", "memo", "memorize", "Boolean", "window", "document", "all", "atob", "getTargetElement", "target", "styleTarget", "querySelector", "HTMLIFrameElement", "contentDocument", "head", "e", "stylesInDom", "getIndexByIdentifier", "identifier", "result", "i", "length", "modulesToDom", "list", "options", "idCountMap", "identifiers", "item", "id", "base", "count", "toString", "index", "obj", "css", "media", "sourceMap", "references", "updater", "push", "addStyle", "insertStyleElement", "style", "createElement", "attributes", "nonce", "__webpack_nonce__", "Object", "keys", "for<PERSON>ach", "key", "setAttribute", "insert", "Error", "append<PERSON><PERSON><PERSON>", "removeStyleElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "replaceText", "textStore", "replace", "replacement", "filter", "join", "applyToSingletonTag", "remove", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "applyToTag", "_options", "removeAttribute", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "singleton", "singletonCounter", "update", "styleIndex", "bind", "updateStyle", "newObj", "module", "exports", "lastIdentifiers", "newList", "prototype", "call", "newLastIdentifiers", "splice"], "mappings": "AAAA,MAAMA,UAAU,AAAC,SAASA;IACxB,IAAIC;IAEJ,OAAO,SAASC;QACd,IAAI,OAAOD,SAAS,aAAa;YAC/B,+CAA+C;YAC/C,sEAAsE;YACtE,mEAAmE;YACnE,sDAAsD;YACtD,kEAAkE;YAClEA,OAAOE,QAAQC,UAAUC,YAAYA,SAASC,GAAG,IAAI,CAACF,OAAOG,IAAI;QACnE;QAEA,OAAON;IACT;AACF;AAEA,MAAMO,mBAAmB,AAAC;IACxB,MAAMP,OAAY,CAAC;IAEnB,OAAO,SAASC,SAASO,MAAW;QAClC,IAAI,OAAOR,IAAI,CAACQ,OAAO,KAAK,aAAa;YACvC,IAAIC,cAAmBL,SAASM,aAAa,CAACF;YAE9C,iEAAiE;YACjE,IACEL,OAAOQ,iBAAiB,IACxBF,uBAAuBN,OAAOQ,iBAAiB,EAC/C;gBACA,IAAI;oBACF,8DAA8D;oBAC9D,mCAAmC;oBACnCF,cAAc,AAACA,YAAoBG,eAAe,CAACC,IAAI;gBACzD,EAAE,OAAOC,GAAG;oBACV,uBAAuB;oBACvBL,cAAc;gBAChB;YACF;YAEAT,IAAI,CAACQ,OAAO,GAAGC;QACjB;QAEA,OAAOT,IAAI,CAACQ,OAAO;IACrB;AACF;AAEA,MAAMO,cAAmB,EAAE;AAE3B,SAASC,qBAAqBC,UAAe;IAC3C,IAAIC,SAAS,CAAC;IAEd,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,YAAYK,MAAM,EAAED,IAAK;QAC3C,IAAIJ,WAAW,CAACI,EAAE,CAACF,UAAU,KAAKA,YAAY;YAC5CC,SAASC;YACT;QACF;IACF;IAEA,OAAOD;AACT;AAEA,SAASG,aAAaC,IAAS,EAAEC,OAAY;IAC3C,MAAMC,aAAkB,CAAC;IACzB,MAAMC,cAAc,EAAE;IAEtB,IAAK,IAAIN,IAAI,GAAGA,IAAIG,KAAKF,MAAM,EAAED,IAAK;QACpC,MAAMO,OAAOJ,IAAI,CAACH,EAAE;QACpB,MAAMQ,KAAKJ,QAAQK,IAAI,GAAGF,IAAI,CAAC,EAAE,GAAGH,QAAQK,IAAI,GAAGF,IAAI,CAAC,EAAE;QAC1D,MAAMG,QAAQL,UAAU,CAACG,GAAG,IAAI;QAChC,MAAMV,aAAaU,KAAK,MAAME,MAAMC,QAAQ;QAE5CN,UAAU,CAACG,GAAG,GAAGE,QAAQ;QAEzB,MAAME,QAAQf,qBAAqBC;QACnC,MAAMe,MAAM;YACVC,KAAKP,IAAI,CAAC,EAAE;YACZQ,OAAOR,IAAI,CAAC,EAAE;YACdS,WAAWT,IAAI,CAAC,EAAE;QACpB;QAEA,IAAIK,UAAU,CAAC,GAAG;YAChBhB,WAAW,CAACgB,MAAM,CAACK,UAAU;YAC7BrB,WAAW,CAACgB,MAAM,CAACM,OAAO,CAACL;QAC7B,OAAO;YACLjB,YAAYuB,IAAI,CAAC;gBACfrB,YAAYA;gBACZ,mEAAmE;gBACnEoB,SAASE,SAASP,KAAKT;gBACvBa,YAAY;YACd;QACF;QAEAX,YAAYa,IAAI,CAACrB;IACnB;IAEA,OAAOQ;AACT;AAEA,SAASe,mBAAmBjB,OAAY;IACtC,MAAMkB,QAAQrC,SAASsC,aAAa,CAAC;IACrC,MAAMC,aAAapB,QAAQoB,UAAU,IAAI,CAAC;IAE1C,IAAI,OAAOA,WAAWC,KAAK,KAAK,aAAa;QAC3C,MAAMA,QACJ,oCAAoC;QACpC,OAAOC,sBAAsB,cAAcA,oBAAoB;QAEjE,IAAID,OAAO;YACTD,WAAWC,KAAK,GAAGA;QACrB;IACF;IAEAE,OAAOC,IAAI,CAACJ,YAAYK,OAAO,CAAC,SAAUC,GAAG;QAC3CR,MAAMS,YAAY,CAACD,KAAKN,UAAU,CAACM,IAAI;IACzC;IAEA,IAAI,OAAO1B,QAAQ4B,MAAM,KAAK,YAAY;QACxC5B,QAAQ4B,MAAM,CAACV;IACjB,OAAO;QACL,MAAMjC,SAASD,iBAAiBgB,QAAQ4B,MAAM,IAAI;QAElD,IAAI,CAAC3C,QAAQ;YACX,MAAM,qBAEL,CAFK,IAAI4C,MACR,4GADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA5C,OAAO6C,WAAW,CAACZ;IACrB;IAEA,OAAOA;AACT;AAEA,SAASa,mBAAmBb,KAAU;IACpC,qBAAqB;IACrB,IAAIA,MAAMc,UAAU,KAAK,MAAM;QAC7B,OAAO;IACT;IAEAd,MAAMc,UAAU,CAACC,WAAW,CAACf;AAC/B;AAEA,yBAAyB,GACzB,MAAMgB,cAAc,AAAC,SAASA;IAC5B,MAAMC,YAAiB,EAAE;IAEzB,OAAO,SAASC,QAAQ5B,KAAU,EAAE6B,WAAgB;QAClDF,SAAS,CAAC3B,MAAM,GAAG6B;QAEnB,OAAOF,UAAUG,MAAM,CAAC3D,SAAS4D,IAAI,CAAC;IACxC;AACF;AAEA,SAASC,oBAAoBtB,KAAU,EAAEV,KAAU,EAAEiC,MAAW,EAAEhC,GAAQ;IACxE,MAAMC,MAAM+B,SACR,KACAhC,IAAIE,KAAK,GACP,YAAYF,IAAIE,KAAK,GAAG,OAAOF,IAAIC,GAAG,GAAG,MACzCD,IAAIC,GAAG;IAEb,aAAa;IACb,uBAAuB,GACvB,IAAIQ,MAAMwB,UAAU,EAAE;QACpBxB,MAAMwB,UAAU,CAACC,OAAO,GAAGT,YAAY1B,OAAOE;IAChD,OAAO;QACL,MAAMkC,UAAU/D,SAASgE,cAAc,CAACnC;QACxC,MAAMoC,aAAa5B,MAAM4B,UAAU;QAEnC,IAAIA,UAAU,CAACtC,MAAM,EAAE;YACrBU,MAAMe,WAAW,CAACa,UAAU,CAACtC,MAAM;QACrC;QAEA,IAAIsC,WAAWjD,MAAM,EAAE;YACrBqB,MAAM6B,YAAY,CAACH,SAASE,UAAU,CAACtC,MAAM;QAC/C,OAAO;YACLU,MAAMY,WAAW,CAACc;QACpB;IACF;AACF;AAEA,SAASI,WAAW9B,KAAU,EAAE+B,QAAa,EAAExC,GAAQ;IACrD,IAAIC,MAAMD,IAAIC,GAAG;IACjB,MAAMC,QAAQF,IAAIE,KAAK;IACvB,MAAMC,YAAYH,IAAIG,SAAS;IAE/B,IAAID,OAAO;QACTO,MAAMS,YAAY,CAAC,SAAShB;IAC9B,OAAO;QACLO,MAAMgC,eAAe,CAAC;IACxB;IAEA,IAAItC,aAAa,OAAOuC,SAAS,aAAa;QAC5CzC,OACE,yDACAyC,KAAKC,SAASC,mBAAmBC,KAAKC,SAAS,CAAC3C,gBAChD;IACJ;IAEA,aAAa;IACb,uBAAuB,GACvB,IAAIM,MAAMwB,UAAU,EAAE;QACpBxB,MAAMwB,UAAU,CAACC,OAAO,GAAGjC;IAC7B,OAAO;QACL,MAAOQ,MAAMsC,UAAU,CAAE;YACvBtC,MAAMe,WAAW,CAACf,MAAMsC,UAAU;QACpC;QAEAtC,MAAMY,WAAW,CAACjD,SAASgE,cAAc,CAACnC;IAC5C;AACF;AAEA,IAAI+C,YAAiB;AACrB,IAAIC,mBAAmB;AAEvB,SAAS1C,SAASP,GAAQ,EAAET,OAAY;IACtC,IAAIkB;IACJ,IAAIyC;IACJ,IAAIlB;IAEJ,IAAIzC,QAAQyD,SAAS,EAAE;QACrB,MAAMG,aAAaF;QAEnBxC,QAAQuC,aAAcA,CAAAA,YAAYxC,mBAAmBjB,QAAO;QAE5D2D,SAASnB,oBAAoBqB,IAAI,CAAC,MAAM3C,OAAO0C,YAAY;QAC3DnB,SAASD,oBAAoBqB,IAAI,CAAC,MAAM3C,OAAO0C,YAAY;IAC7D,OAAO;QACL1C,QAAQD,mBAAmBjB;QAE3B2D,SAASX,WAAWa,IAAI,CAAC,MAAM3C,OAAOlB;QACtCyC,SAAS;YACPV,mBAAmBb;QACrB;IACF;IAEAyC,OAAOlD;IAEP,OAAO,SAASqD,YAAYC,MAAW;QACrC,IAAIA,QAAQ;YACV,IACEA,OAAOrD,GAAG,KAAKD,IAAIC,GAAG,IACtBqD,OAAOpD,KAAK,KAAKF,IAAIE,KAAK,IAC1BoD,OAAOnD,SAAS,KAAKH,IAAIG,SAAS,EAClC;gBACA;YACF;YAEA+C,OAAQlD,MAAMsD;QAChB,OAAO;YACLtB;QACF;IACF;AACF;AAEAuB,OAAOC,OAAO,GAAG,SAAUlE,IAAS,EAAEC,OAAY;IAChDA,UAAUA,WAAW,CAAC;IAEtB,iFAAiF;IACjF,+BAA+B;IAC/B,IAAI,CAACA,QAAQyD,SAAS,IAAI,OAAOzD,QAAQyD,SAAS,KAAK,WAAW;QAChEzD,QAAQyD,SAAS,GAAGjF;IACtB;IAEAuB,OAAOA,QAAQ,EAAE;IAEjB,IAAImE,kBAAkBpE,aAAaC,MAAMC;IAEzC,OAAO,SAAS2D,OAAOQ,OAAY;QACjCA,UAAUA,WAAW,EAAE;QAEvB,IAAI5C,OAAO6C,SAAS,CAAC7D,QAAQ,CAAC8D,IAAI,CAACF,aAAa,kBAAkB;YAChE;QACF;QAEA,IAAK,IAAIvE,IAAI,GAAGA,IAAIsE,gBAAgBrE,MAAM,EAAED,IAAK;YAC/C,MAAMF,aAAawE,eAAe,CAACtE,EAAE;YACrC,MAAMY,QAAQf,qBAAqBC;YAEnCF,WAAW,CAACgB,MAAM,CAACK,UAAU;QAC/B;QAEA,MAAMyD,qBAAqBxE,aAAaqE,SAASnE;QAEjD,IAAK,IAAIJ,IAAI,GAAGA,IAAIsE,gBAAgBrE,MAAM,EAAED,IAAK;YAC/C,MAAMF,aAAawE,eAAe,CAACtE,EAAE;YACrC,MAAMY,QAAQf,qBAAqBC;YAEnC,IAAIF,WAAW,CAACgB,MAAM,CAACK,UAAU,KAAK,GAAG;gBACvCrB,WAAW,CAACgB,MAAM,CAACM,OAAO;gBAC1BtB,YAAY+E,MAAM,CAAC/D,OAAO;YAC5B;QACF;QAEA0D,kBAAkBI;IACpB;AACF"}