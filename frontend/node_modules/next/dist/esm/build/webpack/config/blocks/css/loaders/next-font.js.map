{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/next-font.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { ConfigurationContext } from '../../../utils'\nimport { getClientStyleLoader } from './client'\nimport { cssFileResolve } from './file-resolve'\n\nexport function getNextFontLoader(\n  ctx: ConfigurationContext,\n  postcss: any,\n  fontLoaderPath: string\n): webpack.RuleSetUseItem[] {\n  const loaders: webpack.RuleSetUseItem[] = []\n\n  if (ctx.isClient) {\n    // Add appropriate development mode or production mode style\n    // loader\n    loaders.push(\n      getClientStyleLoader({\n        hasAppDir: ctx.hasAppDir,\n        isDevelopment: ctx.isDevelopment,\n        assetPrefix: ctx.assetPrefix,\n      })\n    )\n  }\n\n  loaders.push({\n    loader: require.resolve('../../../../loaders/css-loader/src'),\n    options: {\n      postcss,\n      importLoaders: 1,\n      // Use CJS mode for backwards compatibility:\n      esModule: false,\n      url: (url: string, resourcePath: string) =>\n        cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n      import: (url: string, _: any, resourcePath: string) =>\n        cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n      modules: {\n        // Do not transform class names (CJS mode backwards compatibility):\n        exportLocalsConvention: 'asIs',\n        // Server-side (Node.js) rendering support:\n        exportOnlyLocals: ctx.isServer,\n        // Disallow global style exports so we can code-split CSS and\n        // not worry about loading order.\n        mode: 'pure',\n        getLocalIdent: (\n          _context: any,\n          _localIdentName: any,\n          exportName: string,\n          _options: any,\n          meta: any\n        ) => {\n          // hash from next-font-loader\n          return `__${exportName}_${meta.fontFamilyHash}`\n        },\n      },\n      fontLoader: true,\n    },\n  })\n\n  loaders.push({\n    loader: 'next-font-loader',\n    options: {\n      isDev: ctx.isDevelopment,\n      isServer: ctx.isServer,\n      assetPrefix: ctx.assetPrefix,\n      fontLoaderPath,\n      postcss,\n    },\n  })\n\n  return loaders\n}\n"], "names": ["getClientStyleLoader", "cssFileResolve", "getNextFontLoader", "ctx", "postcss", "fontLoaderPath", "loaders", "isClient", "push", "hasAppDir", "isDevelopment", "assetPrefix", "loader", "require", "resolve", "options", "importLoaders", "esModule", "url", "resourcePath", "experimental", "urlImports", "import", "_", "modules", "exportLocalsConvention", "exportOnlyLocals", "isServer", "mode", "getLocalIdent", "_context", "_localIdentName", "exportName", "_options", "meta", "fontFamilyHash", "fontLoader", "isDev"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,WAAU;AAC/C,SAASC,cAAc,QAAQ,iBAAgB;AAE/C,OAAO,SAASC,kBACdC,GAAyB,EACzBC,OAAY,EACZC,cAAsB;IAEtB,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVR,qBAAqB;YACnBS,WAAWN,IAAIM,SAAS;YACxBC,eAAeP,IAAIO,aAAa;YAChCC,aAAaR,IAAIQ,WAAW;QAC9B;IAEJ;IAEAL,QAAQE,IAAI,CAAC;QACXI,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPX;YACAY,eAAe;YACf,4CAA4C;YAC5CC,UAAU;YACVC,KAAK,CAACA,KAAaC,eACjBlB,eAAeiB,KAAKC,cAAchB,IAAIiB,YAAY,CAACC,UAAU;YAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BlB,eAAeiB,KAAKC,cAAchB,IAAIiB,YAAY,CAACC,UAAU;YAC/DG,SAAS;gBACP,mEAAmE;gBACnEC,wBAAwB;gBACxB,2CAA2C;gBAC3CC,kBAAkBvB,IAAIwB,QAAQ;gBAC9B,6DAA6D;gBAC7D,iCAAiC;gBACjCC,MAAM;gBACNC,eAAe,CACbC,UACAC,iBACAC,YACAC,UACAC;oBAEA,6BAA6B;oBAC7B,OAAO,CAAC,EAAE,EAAEF,WAAW,CAAC,EAAEE,KAAKC,cAAc,EAAE;gBACjD;YACF;YACAC,YAAY;QACd;IACF;IAEA9B,QAAQE,IAAI,CAAC;QACXI,QAAQ;QACRG,SAAS;YACPsB,OAAOlC,IAAIO,aAAa;YACxBiB,UAAUxB,IAAIwB,QAAQ;YACtBhB,aAAaR,IAAIQ,WAAW;YAC5BN;YACAD;QACF;IACF;IAEA,OAAOE;AACT"}