{"version": 3, "sources": ["../../../src/build/swc/options.ts"], "sourcesContent": ["import path from 'path'\nimport { WEBPACK_LAYERS, type WebpackLayerName } from '../../lib/constants'\nimport type {\n  NextConfig,\n  ExperimentalConfig,\n  EmotionConfig,\n  StyledComponentsConfig,\n} from '../../server/config-shared'\nimport type { ResolvedBaseUrl } from '../load-jsconfig'\nimport { isWebpackServerOnlyLayer, isWebpackAppPagesLayer } from '../utils'\nimport { escapeStringRegexp } from '../../shared/lib/escape-regexp'\n\nconst nextDirname = path.dirname(require.resolve('next/package.json'))\n\nconst nextDistPath = new RegExp(\n  `${escapeStringRegexp(nextDirname)}[\\\\/]dist[\\\\/](shared[\\\\/]lib|client|pages)`\n)\n\nconst nodeModulesPath = /[\\\\/]node_modules[\\\\/]/\n\nconst regeneratorRuntimePath = require.resolve(\n  'next/dist/compiled/regenerator-runtime'\n)\n\nfunction isTypeScriptFile(filename: string) {\n  return filename.endsWith('.ts') || filename.endsWith('.tsx')\n}\n\nfunction isCommonJSFile(filename: string) {\n  return filename.endsWith('.cjs')\n}\n\n// Ensure Next.js internals and .cjs files are output as CJS modules,\n// By default all modules are output as ESM or will treated as CJS if next-swc/auto-cjs plugin detects file is CJS.\nfunction shouldOutputCommonJs(filename: string) {\n  return isCommonJSFile(filename) || nextDistPath.test(filename)\n}\n\nexport function getParserOptions({ filename, jsConfig, ...rest }: any) {\n  const isTSFile = filename.endsWith('.ts')\n  const hasTsSyntax = isTypeScriptFile(filename)\n  const enableDecorators = Boolean(\n    jsConfig?.compilerOptions?.experimentalDecorators\n  )\n  return {\n    ...rest,\n    syntax: hasTsSyntax ? 'typescript' : 'ecmascript',\n    dynamicImport: true,\n    decorators: enableDecorators,\n    // Exclude regular TypeScript files from React transformation to prevent e.g. generic parameters and angle-bracket type assertion from being interpreted as JSX tags.\n    [hasTsSyntax ? 'tsx' : 'jsx']: !isTSFile,\n    importAssertions: true,\n  }\n}\n\nfunction getBaseSWCOptions({\n  filename,\n  jest,\n  development,\n  hasReactRefresh,\n  globalWindow,\n  esm,\n  modularizeImports,\n  swcPlugins,\n  compilerOptions,\n  resolvedBaseUrl,\n  jsConfig,\n  swcCacheDir,\n  serverComponents,\n  serverReferenceHashSalt,\n  bundleLayer,\n  isDynamicIo,\n  cacheHandlers,\n  useCacheEnabled,\n}: {\n  filename: string\n  jest?: boolean\n  development: boolean\n  hasReactRefresh: boolean\n  globalWindow: boolean\n  esm: boolean\n  modularizeImports?: NextConfig['modularizeImports']\n  compilerOptions: NextConfig['compiler']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  resolvedBaseUrl?: ResolvedBaseUrl\n  jsConfig: any\n  swcCacheDir?: string\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n  bundleLayer?: WebpackLayerName\n  isDynamicIo?: boolean\n  cacheHandlers?: ExperimentalConfig['cacheHandlers']\n  useCacheEnabled?: boolean\n}) {\n  const isReactServerLayer = isWebpackServerOnlyLayer(bundleLayer)\n  const isAppRouterPagesLayer = isWebpackAppPagesLayer(bundleLayer)\n  const parserConfig = getParserOptions({ filename, jsConfig })\n  const paths = jsConfig?.compilerOptions?.paths\n  const enableDecorators = Boolean(\n    jsConfig?.compilerOptions?.experimentalDecorators\n  )\n  const emitDecoratorMetadata = Boolean(\n    jsConfig?.compilerOptions?.emitDecoratorMetadata\n  )\n  const useDefineForClassFields = Boolean(\n    jsConfig?.compilerOptions?.useDefineForClassFields\n  )\n  const plugins = (swcPlugins ?? [])\n    .filter(Array.isArray)\n    .map(([name, options]: any) => [require.resolve(name), options])\n\n  return {\n    jsc: {\n      ...(resolvedBaseUrl && paths\n        ? {\n            baseUrl: resolvedBaseUrl.baseUrl,\n            paths,\n          }\n        : {}),\n      externalHelpers: !process.versions.pnp && !jest,\n      parser: parserConfig,\n      experimental: {\n        keepImportAttributes: true,\n        emitAssertForImportAttributes: true,\n        plugins,\n        cacheRoot: swcCacheDir,\n      },\n      transform: {\n        // Enables https://github.com/swc-project/swc/blob/0359deb4841be743d73db4536d4a22ac797d7f65/crates/swc_ecma_ext_transforms/src/jest.rs\n        ...(jest\n          ? {\n              hidden: {\n                jest: true,\n              },\n            }\n          : {}),\n        legacyDecorator: enableDecorators,\n        decoratorMetadata: emitDecoratorMetadata,\n        useDefineForClassFields: useDefineForClassFields,\n        react: {\n          importSource:\n            jsConfig?.compilerOptions?.jsxImportSource ??\n            (compilerOptions?.emotion && !isReactServerLayer\n              ? '@emotion/react'\n              : 'react'),\n          runtime: 'automatic',\n          pragmaFrag: 'React.Fragment',\n          throwIfNamespace: true,\n          development: !!development,\n          useBuiltins: true,\n          refresh: !!hasReactRefresh,\n        },\n        optimizer: {\n          simplify: false,\n          globals: jest\n            ? null\n            : {\n                typeofs: {\n                  window: globalWindow ? 'object' : 'undefined',\n                },\n                envs: {\n                  NODE_ENV: development ? '\"development\"' : '\"production\"',\n                },\n                // TODO: handle process.browser to match babel replacing as well\n              },\n        },\n        regenerator: {\n          importPath: regeneratorRuntimePath,\n        },\n      },\n    },\n    sourceMaps: jest ? 'inline' : undefined,\n    removeConsole: compilerOptions?.removeConsole,\n    // disable \"reactRemoveProperties\" when \"jest\" is true\n    // otherwise the setting from next.config.js will be used\n    reactRemoveProperties: jest\n      ? false\n      : compilerOptions?.reactRemoveProperties,\n    // Map the k-v map to an array of pairs.\n    modularizeImports: modularizeImports\n      ? Object.fromEntries(\n          Object.entries(modularizeImports).map(([mod, config]) => [\n            mod,\n            {\n              ...config,\n              transform:\n                typeof config.transform === 'string'\n                  ? config.transform\n                  : Object.entries(config.transform).map(([key, value]) => [\n                      key,\n                      value,\n                    ]),\n            },\n          ])\n        )\n      : undefined,\n    relay: compilerOptions?.relay,\n    // Always transform styled-jsx and error when `client-only` condition is triggered\n    styledJsx: {},\n    // Disable css-in-js libs (without client-only integration) transform on server layer for server components\n    ...(!isReactServerLayer && {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      emotion: getEmotionOptions(compilerOptions?.emotion, development),\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      styledComponents: getStyledComponentsOptions(\n        compilerOptions?.styledComponents,\n        development\n      ),\n    }),\n    serverComponents:\n      serverComponents && !jest\n        ? {\n            isReactServerLayer,\n            dynamicIoEnabled: isDynamicIo,\n            useCacheEnabled,\n          }\n        : undefined,\n    serverActions:\n      isAppRouterPagesLayer && !jest\n        ? {\n            isReactServerLayer,\n            isDevelopment: development,\n            useCacheEnabled,\n            hashSalt: serverReferenceHashSalt,\n            cacheKinds: ['default', 'remote'].concat(\n              cacheHandlers ? Object.keys(cacheHandlers) : []\n            ),\n          }\n        : undefined,\n    // For app router we prefer to bundle ESM,\n    // On server side of pages router we prefer CJS.\n    preferEsm: esm,\n    lintCodemodComments: true,\n    debugFunctionName: development,\n  }\n}\n\nfunction getStyledComponentsOptions(\n  styledComponentsConfig: undefined | boolean | StyledComponentsConfig,\n  development: any\n) {\n  if (!styledComponentsConfig) {\n    return null\n  } else if (typeof styledComponentsConfig === 'object') {\n    return {\n      ...styledComponentsConfig,\n      displayName: styledComponentsConfig.displayName ?? Boolean(development),\n    }\n  } else {\n    return {\n      displayName: Boolean(development),\n    }\n  }\n}\n\nfunction getEmotionOptions(\n  emotionConfig: undefined | boolean | EmotionConfig,\n  development: boolean\n) {\n  if (!emotionConfig) {\n    return null\n  }\n  let autoLabel = !!development\n  switch (typeof emotionConfig === 'object' && emotionConfig.autoLabel) {\n    case 'never':\n      autoLabel = false\n      break\n    case 'always':\n      autoLabel = true\n      break\n    case 'dev-only':\n    default:\n      break\n  }\n  return {\n    enabled: true,\n    autoLabel,\n    sourcemap: development,\n    ...(typeof emotionConfig === 'object' && {\n      importMap: emotionConfig.importMap,\n      labelFormat: emotionConfig.labelFormat,\n      sourcemap: development && emotionConfig.sourceMap,\n    }),\n  }\n}\n\nexport function getJestSWCOptions({\n  isServer,\n  filename,\n  esm,\n  modularizeImports,\n  swcPlugins,\n  compilerOptions,\n  jsConfig,\n  resolvedBaseUrl,\n  pagesDir,\n  serverReferenceHashSalt,\n}: {\n  isServer: boolean\n  filename: string\n  esm: boolean\n  modularizeImports?: NextConfig['modularizeImports']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  compilerOptions: NextConfig['compiler']\n  jsConfig: any\n  resolvedBaseUrl?: ResolvedBaseUrl\n  pagesDir?: string\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n}) {\n  let baseOptions = getBaseSWCOptions({\n    filename,\n    jest: true,\n    development: false,\n    hasReactRefresh: false,\n    globalWindow: !isServer,\n    modularizeImports,\n    swcPlugins,\n    compilerOptions,\n    jsConfig,\n    resolvedBaseUrl,\n    esm,\n    // Don't apply server layer transformations for Jest\n    // Disable server / client graph assertions for Jest\n    bundleLayer: undefined,\n    serverComponents: false,\n    serverReferenceHashSalt,\n  })\n\n  const useCjsModules = shouldOutputCommonJs(filename)\n  return {\n    ...baseOptions,\n    env: {\n      targets: {\n        // Targets the current version of Node.js\n        node: process.versions.node,\n      },\n    },\n    module: {\n      type: esm && !useCjsModules ? 'es6' : 'commonjs',\n    },\n    disableNextSsg: true,\n    disablePageConfig: true,\n    pagesDir,\n  }\n}\n\nexport function getLoaderSWCOptions({\n  // This is not passed yet as \"paths\" resolving is handled by webpack currently.\n  // resolvedBaseUrl,\n  filename,\n  development,\n  isServer,\n  pagesDir,\n  appDir,\n  isPageFile,\n  isDynamicIo,\n  hasReactRefresh,\n  modularizeImports,\n  optimizeServerReact,\n  optimizePackageImports,\n  swcPlugins,\n  compilerOptions,\n  jsConfig,\n  supportedBrowsers,\n  swcCacheDir,\n  relativeFilePathFromRoot,\n  serverComponents,\n  serverReferenceHashSalt,\n  bundleLayer,\n  esm,\n  cacheHandlers,\n  useCacheEnabled,\n}: {\n  filename: string\n  development: boolean\n  isServer: boolean\n  pagesDir?: string\n  appDir?: string\n  isPageFile: boolean\n  hasReactRefresh: boolean\n  optimizeServerReact?: boolean\n  modularizeImports: NextConfig['modularizeImports']\n  isDynamicIo?: boolean\n  optimizePackageImports?: NonNullable<\n    NextConfig['experimental']\n  >['optimizePackageImports']\n  swcPlugins: ExperimentalConfig['swcPlugins']\n  compilerOptions: NextConfig['compiler']\n  jsConfig: any\n  supportedBrowsers: string[] | undefined\n  swcCacheDir: string\n  relativeFilePathFromRoot: string\n  esm?: boolean\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n  bundleLayer?: WebpackLayerName\n  cacheHandlers: ExperimentalConfig['cacheHandlers']\n  useCacheEnabled?: boolean\n}) {\n  let baseOptions: any = getBaseSWCOptions({\n    filename,\n    development,\n    globalWindow: !isServer,\n    hasReactRefresh,\n    modularizeImports,\n    swcPlugins,\n    compilerOptions,\n    jsConfig,\n    // resolvedBaseUrl,\n    swcCacheDir,\n    bundleLayer,\n    serverComponents,\n    serverReferenceHashSalt,\n    esm: !!esm,\n    isDynamicIo,\n    cacheHandlers,\n    useCacheEnabled,\n  })\n  baseOptions.fontLoaders = {\n    fontLoaders: ['next/font/local', 'next/font/google'],\n    relativeFilePathFromRoot,\n  }\n  baseOptions.cjsRequireOptimizer = {\n    packages: {\n      'next/server': {\n        transforms: {\n          NextRequest: 'next/dist/server/web/spec-extension/request',\n          NextResponse: 'next/dist/server/web/spec-extension/response',\n          ImageResponse: 'next/dist/server/web/spec-extension/image-response',\n          userAgentFromString: 'next/dist/server/web/spec-extension/user-agent',\n          userAgent: 'next/dist/server/web/spec-extension/user-agent',\n        },\n      },\n    },\n  }\n\n  if (optimizeServerReact && isServer && !development) {\n    baseOptions.optimizeServerReact = {\n      optimize_use_state: false,\n    }\n  }\n\n  // Modularize import optimization for barrel files\n  if (optimizePackageImports) {\n    baseOptions.autoModularizeImports = {\n      packages: optimizePackageImports,\n    }\n  }\n\n  const isNodeModules = nodeModulesPath.test(filename)\n  const isAppBrowserLayer = bundleLayer === WEBPACK_LAYERS.appPagesBrowser\n  const moduleResolutionConfig = shouldOutputCommonJs(filename)\n    ? {\n        module: {\n          type: 'commonjs',\n        },\n      }\n    : {}\n\n  let options: any\n  if (isServer) {\n    options = {\n      ...baseOptions,\n      ...moduleResolutionConfig,\n      // Disables getStaticProps/getServerSideProps tree shaking on the server compilation for pages\n      disableNextSsg: true,\n      disablePageConfig: true,\n      isDevelopment: development,\n      isServerCompiler: isServer,\n      pagesDir,\n      appDir,\n      preferEsm: !!esm,\n      isPageFile,\n      env: {\n        targets: {\n          // Targets the current version of Node.js\n          node: process.versions.node,\n        },\n      },\n    }\n  } else {\n    options = {\n      ...baseOptions,\n      ...moduleResolutionConfig,\n      disableNextSsg: !isPageFile,\n      isDevelopment: development,\n      isServerCompiler: isServer,\n      pagesDir,\n      appDir,\n      isPageFile,\n      ...(supportedBrowsers && supportedBrowsers.length > 0\n        ? {\n            env: {\n              targets: supportedBrowsers,\n            },\n          }\n        : {}),\n    }\n    if (!options.env) {\n      // Matches default @babel/preset-env behavior\n      options.jsc.target = 'es5'\n    }\n  }\n\n  // For node_modules in app browser layer, we don't need to do any server side transformation.\n  // Only keep server actions transform to discover server actions from client components.\n  if (isAppBrowserLayer && isNodeModules) {\n    options.disableNextSsg = true\n    options.disablePageConfig = true\n    options.isPageFile = false\n    options.optimizeServerReact = undefined\n    options.cjsRequireOptimizer = undefined\n    // Disable optimizer for node_modules in app browser layer, to avoid unnecessary replacement.\n    // e.g. typeof window could result differently in js worker or browser.\n    if (\n      options.jsc.transform.optimizer.globals?.typeofs &&\n      !filename.includes(nextDirname)\n    ) {\n      delete options.jsc.transform.optimizer.globals.typeofs.window\n    }\n  }\n\n  return options\n}\n"], "names": ["path", "WEBPACK_LAYERS", "isWebpackServerOnlyLayer", "isWebpackAppPagesLayer", "escapeStringRegexp", "nextDirname", "dirname", "require", "resolve", "nextDistPath", "RegExp", "nodeModulesPath", "regeneratorRuntimePath", "isTypeScriptFile", "filename", "endsWith", "isCommonJSFile", "shouldOutputCommonJs", "test", "getParserOptions", "jsConfig", "rest", "isTSFile", "hasTsSyntax", "enableDecorators", "Boolean", "compilerOptions", "experimentalDecorators", "syntax", "dynamicImport", "decorators", "importAssertions", "getBaseSWCOptions", "jest", "development", "hasReactRefresh", "globalWindow", "esm", "modularizeImports", "swcPlugins", "resolvedBaseUrl", "swcCacheDir", "serverComponents", "serverReferenceHashSalt", "bundleLayer", "isDynamicIo", "cacheHandlers", "useCacheEnabled", "isReactServerLayer", "isAppRouterPagesLayer", "parserConfig", "paths", "emitDecoratorMetadata", "useDefineForClassFields", "plugins", "filter", "Array", "isArray", "map", "name", "options", "jsc", "baseUrl", "externalHelpers", "process", "versions", "pnp", "parser", "experimental", "keepImportAttributes", "emitAssertForImportAttributes", "cacheRoot", "transform", "hidden", "legacyDecorator", "decoratorMetadata", "react", "importSource", "jsxImportSource", "emotion", "runtime", "pragmaFrag", "throwIfNamespace", "useBuiltins", "refresh", "optimizer", "simplify", "globals", "typeofs", "window", "envs", "NODE_ENV", "regenerator", "importPath", "sourceMaps", "undefined", "removeConsole", "reactRemoveProperties", "Object", "fromEntries", "entries", "mod", "config", "key", "value", "relay", "styledJsx", "getEmotionOptions", "styledComponents", "getStyledComponentsOptions", "dynamicIoEnabled", "serverActions", "isDevelopment", "hashSalt", "cacheKinds", "concat", "keys", "preferEsm", "lintCodemodComments", "debugFunctionName", "styledComponentsConfig", "displayName", "emotionConfig", "autoLabel", "enabled", "sourcemap", "importMap", "labelFormat", "sourceMap", "getJestSWCOptions", "isServer", "pagesDir", "baseOptions", "useCjsModules", "env", "targets", "node", "module", "type", "disableNextSsg", "disablePageConfig", "getLoaderSWCOptions", "appDir", "isPageFile", "optimizeServerReact", "optimizePackageImports", "supportedBrowsers", "relativeFilePathFromRoot", "fontLoaders", "cjsRequireOptimizer", "packages", "transforms", "NextRequest", "NextResponse", "ImageResponse", "userAgentFromString", "userAgent", "optimize_use_state", "autoModularizeImports", "isNodeModules", "isAppBrowserLayer", "appPagesBrowser", "moduleResolutionConfig", "isServerCompiler", "length", "target", "includes"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,cAAc,QAA+B,sBAAqB;AAQ3E,SAASC,wBAAwB,EAAEC,sBAAsB,QAAQ,WAAU;AAC3E,SAASC,kBAAkB,QAAQ,iCAAgC;AAEnE,MAAMC,cAAcL,KAAKM,OAAO,CAACC,QAAQC,OAAO,CAAC;AAEjD,MAAMC,eAAe,IAAIC,OACvB,GAAGN,mBAAmBC,aAAa,2CAA2C,CAAC;AAGjF,MAAMM,kBAAkB;AAExB,MAAMC,yBAAyBL,QAAQC,OAAO,CAC5C;AAGF,SAASK,iBAAiBC,QAAgB;IACxC,OAAOA,SAASC,QAAQ,CAAC,UAAUD,SAASC,QAAQ,CAAC;AACvD;AAEA,SAASC,eAAeF,QAAgB;IACtC,OAAOA,SAASC,QAAQ,CAAC;AAC3B;AAEA,qEAAqE;AACrE,mHAAmH;AACnH,SAASE,qBAAqBH,QAAgB;IAC5C,OAAOE,eAAeF,aAAaL,aAAaS,IAAI,CAACJ;AACvD;AAEA,OAAO,SAASK,iBAAiB,EAAEL,QAAQ,EAAEM,QAAQ,EAAE,GAAGC,MAAW;QAIjED;IAHF,MAAME,WAAWR,SAASC,QAAQ,CAAC;IACnC,MAAMQ,cAAcV,iBAAiBC;IACrC,MAAMU,mBAAmBC,QACvBL,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2BO,sBAAsB;IAEnD,OAAO;QACL,GAAGN,IAAI;QACPO,QAAQL,cAAc,eAAe;QACrCM,eAAe;QACfC,YAAYN;QACZ,qKAAqK;QACrK,CAACD,cAAc,QAAQ,MAAM,EAAE,CAACD;QAChCS,kBAAkB;IACpB;AACF;AAEA,SAASC,kBAAkB,EACzBlB,QAAQ,EACRmB,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,YAAY,EACZC,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfc,eAAe,EACfpB,QAAQ,EACRqB,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACXC,WAAW,EACXC,aAAa,EACbC,eAAe,EAoBhB;QAIe3B,2BAEZA,4BAGAA,4BAGAA,4BAoCQA;IA/CV,MAAM4B,qBAAqB9C,yBAAyB0C;IACpD,MAAMK,wBAAwB9C,uBAAuByC;IACrD,MAAMM,eAAe/B,iBAAiB;QAAEL;QAAUM;IAAS;IAC3D,MAAM+B,QAAQ/B,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2B+B,KAAK;IAC9C,MAAM3B,mBAAmBC,QACvBL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BO,sBAAsB;IAEnD,MAAMyB,wBAAwB3B,QAC5BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BgC,qBAAqB;IAElD,MAAMC,0BAA0B5B,QAC9BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BiC,uBAAuB;IAEpD,MAAMC,UAAU,AAACf,CAAAA,cAAc,EAAE,AAAD,EAC7BgB,MAAM,CAACC,MAAMC,OAAO,EACpBC,GAAG,CAAC,CAAC,CAACC,MAAMC,QAAa,GAAK;YAACrD,QAAQC,OAAO,CAACmD;YAAOC;SAAQ;IAEjE,OAAO;QACLC,KAAK;YACH,GAAIrB,mBAAmBW,QACnB;gBACEW,SAAStB,gBAAgBsB,OAAO;gBAChCX;YACF,IACA,CAAC,CAAC;YACNY,iBAAiB,CAACC,QAAQC,QAAQ,CAACC,GAAG,IAAI,CAACjC;YAC3CkC,QAAQjB;YACRkB,cAAc;gBACZC,sBAAsB;gBACtBC,+BAA+B;gBAC/BhB;gBACAiB,WAAW9B;YACb;YACA+B,WAAW;gBACT,sIAAsI;gBACtI,GAAIvC,OACA;oBACEwC,QAAQ;wBACNxC,MAAM;oBACR;gBACF,IACA,CAAC,CAAC;gBACNyC,iBAAiBlD;gBACjBmD,mBAAmBvB;gBACnBC,yBAAyBA;gBACzBuB,OAAO;oBACLC,cACEzD,CAAAA,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2B0D,eAAe,KACzCpD,CAAAA,CAAAA,mCAAAA,gBAAiBqD,OAAO,KAAI,CAAC/B,qBAC1B,mBACA,OAAM;oBACZgC,SAAS;oBACTC,YAAY;oBACZC,kBAAkB;oBAClBhD,aAAa,CAAC,CAACA;oBACfiD,aAAa;oBACbC,SAAS,CAAC,CAACjD;gBACb;gBACAkD,WAAW;oBACTC,UAAU;oBACVC,SAAStD,OACL,OACA;wBACEuD,SAAS;4BACPC,QAAQrD,eAAe,WAAW;wBACpC;wBACAsD,MAAM;4BACJC,UAAUzD,cAAc,kBAAkB;wBAC5C;oBAEF;gBACN;gBACA0D,aAAa;oBACXC,YAAYjF;gBACd;YACF;QACF;QACAkF,YAAY7D,OAAO,WAAW8D;QAC9BC,aAAa,EAAEtE,mCAAAA,gBAAiBsE,aAAa;QAC7C,sDAAsD;QACtD,yDAAyD;QACzDC,uBAAuBhE,OACnB,QACAP,mCAAAA,gBAAiBuE,qBAAqB;QAC1C,wCAAwC;QACxC3D,mBAAmBA,oBACf4D,OAAOC,WAAW,CAChBD,OAAOE,OAAO,CAAC9D,mBAAmBoB,GAAG,CAAC,CAAC,CAAC2C,KAAKC,OAAO,GAAK;gBACvDD;gBACA;oBACE,GAAGC,MAAM;oBACT9B,WACE,OAAO8B,OAAO9B,SAAS,KAAK,WACxB8B,OAAO9B,SAAS,GAChB0B,OAAOE,OAAO,CAACE,OAAO9B,SAAS,EAAEd,GAAG,CAAC,CAAC,CAAC6C,KAAKC,MAAM,GAAK;4BACrDD;4BACAC;yBACD;gBACT;aACD,KAEHT;QACJU,KAAK,EAAE/E,mCAAAA,gBAAiB+E,KAAK;QAC7B,kFAAkF;QAClFC,WAAW,CAAC;QACZ,2GAA2G;QAC3G,GAAI,CAAC1D,sBAAsB;YACzB,mEAAmE;YACnE+B,SAAS4B,kBAAkBjF,mCAAAA,gBAAiBqD,OAAO,EAAE7C;YACrD,mEAAmE;YACnE0E,kBAAkBC,2BAChBnF,mCAAAA,gBAAiBkF,gBAAgB,EACjC1E;QAEJ,CAAC;QACDQ,kBACEA,oBAAoB,CAACT,OACjB;YACEe;YACA8D,kBAAkBjE;YAClBE;QACF,IACAgD;QACNgB,eACE9D,yBAAyB,CAAChB,OACtB;YACEe;YACAgE,eAAe9E;YACfa;YACAkE,UAAUtE;YACVuE,YAAY;gBAAC;gBAAW;aAAS,CAACC,MAAM,CACtCrE,gBAAgBoD,OAAOkB,IAAI,CAACtE,iBAAiB,EAAE;QAEnD,IACAiD;QACN,0CAA0C;QAC1C,gDAAgD;QAChDsB,WAAWhF;QACXiF,qBAAqB;QACrBC,mBAAmBrF;IACrB;AACF;AAEA,SAAS2E,2BACPW,sBAAoE,EACpEtF,WAAgB;IAEhB,IAAI,CAACsF,wBAAwB;QAC3B,OAAO;IACT,OAAO,IAAI,OAAOA,2BAA2B,UAAU;QACrD,OAAO;YACL,GAAGA,sBAAsB;YACzBC,aAAaD,uBAAuBC,WAAW,IAAIhG,QAAQS;QAC7D;IACF,OAAO;QACL,OAAO;YACLuF,aAAahG,QAAQS;QACvB;IACF;AACF;AAEA,SAASyE,kBACPe,aAAkD,EAClDxF,WAAoB;IAEpB,IAAI,CAACwF,eAAe;QAClB,OAAO;IACT;IACA,IAAIC,YAAY,CAAC,CAACzF;IAClB,OAAQ,OAAOwF,kBAAkB,YAAYA,cAAcC,SAAS;QAClE,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;QACL;YACE;IACJ;IACA,OAAO;QACLC,SAAS;QACTD;QACAE,WAAW3F;QACX,GAAI,OAAOwF,kBAAkB,YAAY;YACvCI,WAAWJ,cAAcI,SAAS;YAClCC,aAAaL,cAAcK,WAAW;YACtCF,WAAW3F,eAAewF,cAAcM,SAAS;QACnD,CAAC;IACH;AACF;AAEA,OAAO,SAASC,kBAAkB,EAChCC,QAAQ,EACRpH,QAAQ,EACRuB,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfN,QAAQ,EACRoB,eAAe,EACf2F,QAAQ,EACRxF,uBAAuB,EAaxB;IACC,IAAIyF,cAAcpG,kBAAkB;QAClClB;QACAmB,MAAM;QACNC,aAAa;QACbC,iBAAiB;QACjBC,cAAc,CAAC8F;QACf5F;QACAC;QACAb;QACAN;QACAoB;QACAH;QACA,oDAAoD;QACpD,oDAAoD;QACpDO,aAAamD;QACbrD,kBAAkB;QAClBC;IACF;IAEA,MAAM0F,gBAAgBpH,qBAAqBH;IAC3C,OAAO;QACL,GAAGsH,WAAW;QACdE,KAAK;YACHC,SAAS;gBACP,yCAAyC;gBACzCC,MAAMxE,QAAQC,QAAQ,CAACuE,IAAI;YAC7B;QACF;QACAC,QAAQ;YACNC,MAAMrG,OAAO,CAACgG,gBAAgB,QAAQ;QACxC;QACAM,gBAAgB;QAChBC,mBAAmB;QACnBT;IACF;AACF;AAEA,OAAO,SAASU,oBAAoB,EAClC,+EAA+E;AAC/E,mBAAmB;AACnB/H,QAAQ,EACRoB,WAAW,EACXgG,QAAQ,EACRC,QAAQ,EACRW,MAAM,EACNC,UAAU,EACVlG,WAAW,EACXV,eAAe,EACfG,iBAAiB,EACjB0G,mBAAmB,EACnBC,sBAAsB,EACtB1G,UAAU,EACVb,eAAe,EACfN,QAAQ,EACR8H,iBAAiB,EACjBzG,WAAW,EACX0G,wBAAwB,EACxBzG,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACXP,GAAG,EACHS,aAAa,EACbC,eAAe,EA2BhB;IACC,IAAIqF,cAAmBpG,kBAAkB;QACvClB;QACAoB;QACAE,cAAc,CAAC8F;QACf/F;QACAG;QACAC;QACAb;QACAN;QACA,mBAAmB;QACnBqB;QACAG;QACAF;QACAC;QACAN,KAAK,CAAC,CAACA;QACPQ;QACAC;QACAC;IACF;IACAqF,YAAYgB,WAAW,GAAG;QACxBA,aAAa;YAAC;YAAmB;SAAmB;QACpDD;IACF;IACAf,YAAYiB,mBAAmB,GAAG;QAChCC,UAAU;YACR,eAAe;gBACbC,YAAY;oBACVC,aAAa;oBACbC,cAAc;oBACdC,eAAe;oBACfC,qBAAqB;oBACrBC,WAAW;gBACb;YACF;QACF;IACF;IAEA,IAAIZ,uBAAuBd,YAAY,CAAChG,aAAa;QACnDkG,YAAYY,mBAAmB,GAAG;YAChCa,oBAAoB;QACtB;IACF;IAEA,kDAAkD;IAClD,IAAIZ,wBAAwB;QAC1Bb,YAAY0B,qBAAqB,GAAG;YAClCR,UAAUL;QACZ;IACF;IAEA,MAAMc,gBAAgBpJ,gBAAgBO,IAAI,CAACJ;IAC3C,MAAMkJ,oBAAoBpH,gBAAgB3C,eAAegK,eAAe;IACxE,MAAMC,yBAAyBjJ,qBAAqBH,YAChD;QACE2H,QAAQ;YACNC,MAAM;QACR;IACF,IACA,CAAC;IAEL,IAAI9E;IACJ,IAAIsE,UAAU;QACZtE,UAAU;YACR,GAAGwE,WAAW;YACd,GAAG8B,sBAAsB;YACzB,8FAA8F;YAC9FvB,gBAAgB;YAChBC,mBAAmB;YACnB5B,eAAe9E;YACfiI,kBAAkBjC;YAClBC;YACAW;YACAzB,WAAW,CAAC,CAAChF;YACb0G;YACAT,KAAK;gBACHC,SAAS;oBACP,yCAAyC;oBACzCC,MAAMxE,QAAQC,QAAQ,CAACuE,IAAI;gBAC7B;YACF;QACF;IACF,OAAO;QACL5E,UAAU;YACR,GAAGwE,WAAW;YACd,GAAG8B,sBAAsB;YACzBvB,gBAAgB,CAACI;YACjB/B,eAAe9E;YACfiI,kBAAkBjC;YAClBC;YACAW;YACAC;YACA,GAAIG,qBAAqBA,kBAAkBkB,MAAM,GAAG,IAChD;gBACE9B,KAAK;oBACHC,SAASW;gBACX;YACF,IACA,CAAC,CAAC;QACR;QACA,IAAI,CAACtF,QAAQ0E,GAAG,EAAE;YAChB,6CAA6C;YAC7C1E,QAAQC,GAAG,CAACwG,MAAM,GAAG;QACvB;IACF;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,IAAIL,qBAAqBD,eAAe;YASpCnG;QARFA,QAAQ+E,cAAc,GAAG;QACzB/E,QAAQgF,iBAAiB,GAAG;QAC5BhF,QAAQmF,UAAU,GAAG;QACrBnF,QAAQoF,mBAAmB,GAAGjD;QAC9BnC,QAAQyF,mBAAmB,GAAGtD;QAC9B,6FAA6F;QAC7F,uEAAuE;QACvE,IACEnC,EAAAA,2CAAAA,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,qBAAvC3B,yCAAyC4B,OAAO,KAChD,CAAC1E,SAASwJ,QAAQ,CAACjK,cACnB;YACA,OAAOuD,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,CAACC,OAAO,CAACC,MAAM;QAC/D;IACF;IAEA,OAAO7B;AACT"}