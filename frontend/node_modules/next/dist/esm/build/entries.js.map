{"version": 3, "sources": ["../../src/build/entries.ts"], "sourcesContent": ["import type { ClientPagesLoaderOptions } from './webpack/loaders/next-client-pages-loader'\nimport type { MiddlewareLoaderOptions } from './webpack/loaders/next-middleware-loader'\nimport type { EdgeSSRLoaderQuery } from './webpack/loaders/next-edge-ssr-loader'\nimport type { EdgeAppRouteLoaderQuery } from './webpack/loaders/next-edge-app-route-loader'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type {\n  MiddlewareConfig,\n  MiddlewareMatcher,\n  PageStaticInfo,\n} from './analysis/get-page-static-info'\nimport * as Log from './output/log'\nimport type { LoadedEnvFiles } from '@next/env'\nimport type { AppLoaderOptions } from './webpack/loaders/next-app-loader'\n\nimport { posix, join, dirname, extname, normalize } from 'path'\nimport { stringify } from 'querystring'\nimport fs from 'fs'\nimport {\n  PAGES_DIR_ALIAS,\n  ROOT_DIR_ALIAS,\n  APP_DIR_ALIAS,\n  WEBPACK_LAYERS,\n  INSTRUMENTATION_HOOK_FILENAME,\n} from '../lib/constants'\nimport { isAPIRoute } from '../lib/is-api-route'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_CLIENT_INTERNALS,\n  RSC_MODULE_TYPES,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  COMPILER_NAMES,\n  EDGE_RUNTIME_WEBPACK,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport {\n  isMiddlewareFile,\n  isMiddlewareFilename,\n  isInstrumentationHookFile,\n  isInstrumentationHookFilename,\n  reduceAppConfig,\n} from './utils'\nimport {\n  getAppPageStaticInfo,\n  getPageStaticInfo,\n} from './analysis/get-page-static-info'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport type { ServerRuntime } from '../types'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { encodeMatchers } from './webpack/loaders/next-middleware-loader'\nimport type { EdgeFunctionLoaderOptions } from './webpack/loaders/next-edge-function-loader'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport {\n  normalizeMetadataPageToRoute,\n  normalizeMetadataRoute,\n} from '../lib/metadata/get-metadata-route'\nimport { getRouteLoaderEntry } from './webpack/loaders/next-route-loader'\nimport {\n  isInternalComponent,\n  isNonRoutePagesPage,\n} from '../lib/is-internal-component'\nimport { isMetadataRouteFile } from '../lib/metadata/is-metadata-route'\nimport { RouteKind } from '../server/route-kind'\nimport { encodeToBase64 } from './webpack/loaders/utils'\nimport { normalizeCatchAllRoutes } from './normalize-catchall-routes'\nimport type { PageExtensions } from './page-extensions-type'\nimport type { MappedPages } from './build-context'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { isAppPageRoute } from '../lib/is-app-page-route'\n\nexport function sortByPageExts(pageExtensions: PageExtensions) {\n  return (a: string, b: string) => {\n    // prioritize entries according to pageExtensions order\n    // for consistency as fs order can differ across systems\n    // NOTE: this is reversed so preferred comes last and\n    // overrides prior\n    const aExt = extname(a)\n    const bExt = extname(b)\n\n    const aNoExt = a.substring(0, a.length - aExt.length)\n    const bNoExt = a.substring(0, b.length - bExt.length)\n\n    if (aNoExt !== bNoExt) return 0\n\n    // find extension index (skip '.' as pageExtensions doesn't have it)\n    const aExtIndex = pageExtensions.indexOf(aExt.substring(1))\n    const bExtIndex = pageExtensions.indexOf(bExt.substring(1))\n\n    return bExtIndex - aExtIndex\n  }\n}\n\nexport async function getStaticInfoIncludingLayouts({\n  isInsideAppDir,\n  pageExtensions,\n  pageFilePath,\n  appDir,\n  config: nextConfig,\n  isDev,\n  page,\n}: {\n  isInsideAppDir: boolean\n  pageExtensions: PageExtensions\n  pageFilePath: string\n  appDir: string | undefined\n  config: NextConfigComplete\n  isDev: boolean | undefined\n  page: string\n}): Promise<PageStaticInfo> {\n  // TODO: sync types for pages: PAGE_TYPES, ROUTER_TYPE, 'app' | 'pages', etc.\n  const pageType = isInsideAppDir ? PAGE_TYPES.APP : PAGE_TYPES.PAGES\n\n  const pageStaticInfo = await getPageStaticInfo({\n    nextConfig,\n    pageFilePath,\n    isDev,\n    page,\n    pageType,\n  })\n\n  if (pageStaticInfo.type === PAGE_TYPES.PAGES || !appDir) {\n    return pageStaticInfo\n  }\n\n  const segments = [pageStaticInfo]\n\n  // inherit from layout files only if it's a page route\n  if (isAppPageRoute(page)) {\n    const layoutFiles = []\n    const potentialLayoutFiles = pageExtensions.map((ext) => 'layout.' + ext)\n    let dir = dirname(pageFilePath)\n\n    // Uses startsWith to not include directories further up.\n    while (dir.startsWith(appDir)) {\n      for (const potentialLayoutFile of potentialLayoutFiles) {\n        const layoutFile = join(dir, potentialLayoutFile)\n        if (!fs.existsSync(layoutFile)) {\n          continue\n        }\n        layoutFiles.push(layoutFile)\n      }\n      // Walk up the directory tree\n      dir = join(dir, '..')\n    }\n\n    for (const layoutFile of layoutFiles) {\n      const layoutStaticInfo = await getAppPageStaticInfo({\n        nextConfig,\n        pageFilePath: layoutFile,\n        isDev,\n        page,\n        pageType: isInsideAppDir ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n      })\n\n      segments.unshift(layoutStaticInfo)\n    }\n  }\n\n  const config = reduceAppConfig(segments)\n\n  return {\n    ...pageStaticInfo,\n    config,\n    runtime: config.runtime,\n    preferredRegion: config.preferredRegion,\n    maxDuration: config.maxDuration,\n  }\n}\n\ntype ObjectValue<T> = T extends { [key: string]: infer V } ? V : never\n\n/**\n * For a given page path removes the provided extensions.\n */\nexport function getPageFromPath(\n  pagePath: string,\n  pageExtensions: PageExtensions\n) {\n  let page = normalizePathSep(\n    pagePath.replace(new RegExp(`\\\\.+(${pageExtensions.join('|')})$`), '')\n  )\n\n  page = page.replace(/\\/index$/, '')\n\n  return page === '' ? '/' : page\n}\n\nexport function getPageFilePath({\n  absolutePagePath,\n  pagesDir,\n  appDir,\n  rootDir,\n}: {\n  absolutePagePath: string\n  pagesDir: string | undefined\n  appDir: string | undefined\n  rootDir: string\n}) {\n  if (absolutePagePath.startsWith(PAGES_DIR_ALIAS) && pagesDir) {\n    return absolutePagePath.replace(PAGES_DIR_ALIAS, pagesDir)\n  }\n\n  if (absolutePagePath.startsWith(APP_DIR_ALIAS) && appDir) {\n    return absolutePagePath.replace(APP_DIR_ALIAS, appDir)\n  }\n\n  if (absolutePagePath.startsWith(ROOT_DIR_ALIAS)) {\n    return absolutePagePath.replace(ROOT_DIR_ALIAS, rootDir)\n  }\n\n  return require.resolve(absolutePagePath)\n}\n\n/**\n * Creates a mapping of route to page file path for a given list of page paths.\n * For example ['/middleware.ts'] is turned into  { '/middleware': `${ROOT_DIR_ALIAS}/middleware.ts` }\n */\nexport async function createPagesMapping({\n  isDev,\n  pageExtensions,\n  pagePaths,\n  pagesType,\n  pagesDir,\n  appDir,\n}: {\n  isDev: boolean\n  pageExtensions: PageExtensions\n  pagePaths: string[]\n  pagesType: PAGE_TYPES\n  pagesDir: string | undefined\n  appDir: string | undefined\n}): Promise<MappedPages> {\n  const isAppRoute = pagesType === 'app'\n  const pages: MappedPages = {}\n  const promises = pagePaths.map<Promise<void>>(async (pagePath) => {\n    // Do not process .d.ts files as routes\n    if (pagePath.endsWith('.d.ts') && pageExtensions.includes('ts')) {\n      return\n    }\n\n    let pageKey = getPageFromPath(pagePath, pageExtensions)\n    if (isAppRoute) {\n      pageKey = pageKey.replace(/%5F/g, '_')\n      if (pageKey === '/not-found') {\n        pageKey = UNDERSCORE_NOT_FOUND_ROUTE_ENTRY\n      }\n    }\n\n    const normalizedPath = normalizePathSep(\n      join(\n        pagesType === 'pages'\n          ? PAGES_DIR_ALIAS\n          : pagesType === 'app'\n            ? APP_DIR_ALIAS\n            : ROOT_DIR_ALIAS,\n        pagePath\n      )\n    )\n\n    let route = pagesType === 'app' ? normalizeMetadataRoute(pageKey) : pageKey\n\n    if (\n      pagesType === 'app' &&\n      isMetadataRouteFile(pagePath, pageExtensions, true)\n    ) {\n      const filePath = join(appDir!, pagePath)\n      const staticInfo = await getPageStaticInfo({\n        nextConfig: {},\n        pageFilePath: filePath,\n        isDev,\n        page: pageKey,\n        pageType: pagesType,\n      })\n\n      route = normalizeMetadataPageToRoute(\n        route,\n        !!(staticInfo.generateImageMetadata || staticInfo.generateSitemaps)\n      )\n    }\n\n    pages[route] = normalizedPath\n  })\n\n  await Promise.all(promises)\n\n  switch (pagesType) {\n    case PAGE_TYPES.ROOT: {\n      return pages\n    }\n    case PAGE_TYPES.APP: {\n      const hasAppPages = Object.keys(pages).some((page) =>\n        page.endsWith('/page')\n      )\n      return {\n        // If there's any app pages existed, add a default not-found page.\n        // If there's any custom not-found page existed, it will override the default one.\n        ...(hasAppPages && {\n          [UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]:\n            'next/dist/client/components/not-found-error',\n        }),\n        ...pages,\n      }\n    }\n    case PAGE_TYPES.PAGES: {\n      if (isDev) {\n        delete pages['/_app']\n        delete pages['/_error']\n        delete pages['/_document']\n      }\n\n      // In development we always alias these to allow Webpack to fallback to\n      // the correct source file so that HMR can work properly when a file is\n      // added or removed.\n      const root = isDev && pagesDir ? PAGES_DIR_ALIAS : 'next/dist/pages'\n\n      return {\n        '/_app': `${root}/_app`,\n        '/_error': `${root}/_error`,\n        '/_document': `${root}/_document`,\n        ...pages,\n      }\n    }\n    default: {\n      return {}\n    }\n  }\n}\n\nexport interface CreateEntrypointsParams {\n  buildId: string\n  config: NextConfigComplete\n  envFiles: LoadedEnvFiles\n  isDev?: boolean\n  pages: MappedPages\n  pagesDir?: string\n  previewMode: __ApiPreviewProps\n  rootDir: string\n  rootPaths?: MappedPages\n  appDir?: string\n  appPaths?: MappedPages\n  pageExtensions: PageExtensions\n  hasInstrumentationHook?: boolean\n}\n\nexport function getEdgeServerEntry(opts: {\n  rootDir: string\n  absolutePagePath: string\n  buildId: string\n  bundlePath: string\n  config: NextConfigComplete\n  isDev: boolean\n  isServerComponent: boolean\n  page: string\n  pages: MappedPages\n  middleware?: Partial<MiddlewareConfig>\n  pagesType: PAGE_TYPES\n  appDirLoader?: string\n  hasInstrumentationHook?: boolean\n  preferredRegion: string | string[] | undefined\n  middlewareConfig?: MiddlewareConfig\n}) {\n  if (\n    opts.pagesType === 'app' &&\n    isAppRouteRoute(opts.page) &&\n    opts.appDirLoader\n  ) {\n    const loaderParams: EdgeAppRouteLoaderQuery = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      appDirLoader: Buffer.from(opts.appDirLoader || '').toString('base64'),\n      nextConfig: Buffer.from(JSON.stringify(opts.config)).toString('base64'),\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n      cacheHandlers: JSON.stringify(\n        opts.config.experimental.cacheHandlers || {}\n      ),\n    }\n\n    return {\n      import: `next-edge-app-route-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.reactServerComponents,\n    }\n  }\n\n  if (isMiddlewareFile(opts.page)) {\n    const loaderParams: MiddlewareLoaderOptions = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      rootDir: opts.rootDir,\n      matchers: opts.middleware?.matchers\n        ? encodeMatchers(opts.middleware.matchers)\n        : '',\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n    }\n\n    return {\n      import: `next-middleware-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.middleware,\n    }\n  }\n\n  if (isAPIRoute(opts.page)) {\n    const loaderParams: EdgeFunctionLoaderOptions = {\n      absolutePagePath: opts.absolutePagePath,\n      page: opts.page,\n      rootDir: opts.rootDir,\n      preferredRegion: opts.preferredRegion,\n      middlewareConfig: Buffer.from(\n        JSON.stringify(opts.middlewareConfig || {})\n      ).toString('base64'),\n    }\n\n    return {\n      import: `next-edge-function-loader?${stringify(loaderParams)}!`,\n      layer: WEBPACK_LAYERS.apiEdge,\n    }\n  }\n\n  const loaderParams: EdgeSSRLoaderQuery = {\n    absolute500Path: opts.pages['/500'] || '',\n    absoluteAppPath: opts.pages['/_app'],\n    absoluteDocumentPath: opts.pages['/_document'],\n    absoluteErrorPath: opts.pages['/_error'],\n    absolutePagePath: opts.absolutePagePath,\n    dev: opts.isDev,\n    isServerComponent: opts.isServerComponent,\n    page: opts.page,\n    stringifiedConfig: Buffer.from(JSON.stringify(opts.config)).toString(\n      'base64'\n    ),\n    pagesType: opts.pagesType,\n    appDirLoader: Buffer.from(opts.appDirLoader || '').toString('base64'),\n    sriEnabled: !opts.isDev && !!opts.config.experimental.sri?.algorithm,\n    cacheHandler: opts.config.cacheHandler,\n    preferredRegion: opts.preferredRegion,\n    middlewareConfig: Buffer.from(\n      JSON.stringify(opts.middlewareConfig || {})\n    ).toString('base64'),\n    serverActions: opts.config.experimental.serverActions,\n    cacheHandlers: JSON.stringify(opts.config.experimental.cacheHandlers || {}),\n  }\n\n  return {\n    import: `next-edge-ssr-loader?${JSON.stringify(loaderParams)}!`,\n    // The Edge bundle includes the server in its entrypoint, so it has to\n    // be in the SSR layer — we later convert the page request to the RSC layer\n    // via a webpack rule.\n    layer: opts.appDirLoader ? WEBPACK_LAYERS.serverSideRendering : undefined,\n  }\n}\n\nexport function getInstrumentationEntry(opts: {\n  absolutePagePath: string\n  isEdgeServer: boolean\n  isDev: boolean\n}) {\n  // the '../' is needed to make sure the file is not chunked\n  const filename = `${\n    opts.isEdgeServer ? 'edge-' : opts.isDev ? '' : '../'\n  }${INSTRUMENTATION_HOOK_FILENAME}.js`\n\n  return {\n    import: opts.absolutePagePath,\n    filename,\n    layer: WEBPACK_LAYERS.instrument,\n  }\n}\n\nexport function getAppLoader() {\n  return process.env.BUILTIN_APP_LOADER\n    ? `builtin:next-app-loader`\n    : 'next-app-loader'\n}\n\nexport function getAppEntry(opts: Readonly<AppLoaderOptions>) {\n  if (process.env.NEXT_RSPACK && process.env.BUILTIN_APP_LOADER) {\n    ;(opts as any).projectRoot = normalize(join(__dirname, '../../..'))\n  }\n  return {\n    import: `${getAppLoader()}?${stringify(opts)}!`,\n    layer: WEBPACK_LAYERS.reactServerComponents,\n  }\n}\n\nexport function getClientEntry(opts: {\n  absolutePagePath: string\n  page: string\n}) {\n  const loaderOptions: ClientPagesLoaderOptions = {\n    absolutePagePath: opts.absolutePagePath,\n    page: opts.page,\n  }\n\n  const pageLoader = `next-client-pages-loader?${stringify(loaderOptions)}!`\n\n  // Make sure next/router is a dependency of _app or else chunk splitting\n  // might cause the router to not be able to load causing hydration\n  // to fail\n  return opts.page === '/_app'\n    ? [pageLoader, require.resolve('../client/router')]\n    : pageLoader\n}\n\nexport function runDependingOnPageType<T>(params: {\n  onClient: () => T\n  onEdgeServer: () => T\n  onServer: () => T\n  page: string\n  pageRuntime: ServerRuntime\n  pageType?: PAGE_TYPES\n}): void {\n  if (\n    params.pageType === PAGE_TYPES.ROOT &&\n    isInstrumentationHookFile(params.page)\n  ) {\n    params.onServer()\n    params.onEdgeServer()\n    return\n  }\n\n  if (isMiddlewareFile(params.page)) {\n    if (params.pageRuntime === 'nodejs') {\n      params.onServer()\n      return\n    } else {\n      params.onEdgeServer()\n      return\n    }\n  }\n\n  if (isAPIRoute(params.page)) {\n    if (isEdgeRuntime(params.pageRuntime)) {\n      params.onEdgeServer()\n      return\n    }\n\n    params.onServer()\n    return\n  }\n  if (params.page === '/_document') {\n    params.onServer()\n    return\n  }\n  if (\n    params.page === '/_app' ||\n    params.page === '/_error' ||\n    params.page === '/404' ||\n    params.page === '/500'\n  ) {\n    params.onClient()\n    params.onServer()\n    return\n  }\n  if (isEdgeRuntime(params.pageRuntime)) {\n    params.onClient()\n    params.onEdgeServer()\n    return\n  }\n\n  params.onClient()\n  params.onServer()\n  return\n}\n\nexport async function createEntrypoints(\n  params: CreateEntrypointsParams\n): Promise<{\n  client: webpack.EntryObject\n  server: webpack.EntryObject\n  edgeServer: webpack.EntryObject\n  middlewareMatchers: undefined\n}> {\n  const {\n    config,\n    pages,\n    pagesDir,\n    isDev,\n    rootDir,\n    rootPaths,\n    appDir,\n    appPaths,\n    pageExtensions,\n  } = params\n  const edgeServer: webpack.EntryObject = {}\n  const server: webpack.EntryObject = {}\n  const client: webpack.EntryObject = {}\n  let middlewareMatchers: MiddlewareMatcher[] | undefined = undefined\n\n  let appPathsPerRoute: Record<string, string[]> = {}\n  if (appDir && appPaths) {\n    for (const pathname in appPaths) {\n      const normalizedPath = normalizeAppPath(pathname)\n      const actualPath = appPaths[pathname]\n      if (!appPathsPerRoute[normalizedPath]) {\n        appPathsPerRoute[normalizedPath] = []\n      }\n      appPathsPerRoute[normalizedPath].push(\n        // TODO-APP: refactor to pass the page path from createPagesMapping instead.\n        getPageFromPath(actualPath, pageExtensions).replace(APP_DIR_ALIAS, '')\n      )\n    }\n\n    // TODO: find a better place to do this\n    normalizeCatchAllRoutes(appPathsPerRoute)\n\n    // Make sure to sort parallel routes to make the result deterministic.\n    appPathsPerRoute = Object.fromEntries(\n      Object.entries(appPathsPerRoute).map(([k, v]) => [k, v.sort()])\n    )\n  }\n\n  const getEntryHandler =\n    (mappings: MappedPages, pagesType: PAGE_TYPES): ((page: string) => void) =>\n    async (page) => {\n      const bundleFile = normalizePagePath(page)\n      const clientBundlePath = posix.join(pagesType, bundleFile)\n      const serverBundlePath =\n        pagesType === PAGE_TYPES.PAGES\n          ? posix.join('pages', bundleFile)\n          : pagesType === PAGE_TYPES.APP\n            ? posix.join('app', bundleFile)\n            : bundleFile.slice(1)\n\n      const absolutePagePath = mappings[page]\n\n      // Handle paths that have aliases\n      const pageFilePath = getPageFilePath({\n        absolutePagePath,\n        pagesDir,\n        appDir,\n        rootDir,\n      })\n\n      const isInsideAppDir =\n        !!appDir &&\n        (absolutePagePath.startsWith(APP_DIR_ALIAS) ||\n          absolutePagePath.startsWith(appDir))\n\n      const staticInfo: PageStaticInfo = await getStaticInfoIncludingLayouts({\n        isInsideAppDir,\n        pageExtensions,\n        pageFilePath,\n        appDir,\n        config,\n        isDev,\n        page,\n      })\n\n      // TODO(timneutkens): remove this\n      const isServerComponent =\n        isInsideAppDir && staticInfo.rsc !== RSC_MODULE_TYPES.client\n\n      if (isMiddlewareFile(page)) {\n        middlewareMatchers = staticInfo.middleware?.matchers ?? [\n          { regexp: '.*', originalSource: '/:path*' },\n        ]\n      }\n\n      const isInstrumentation =\n        isInstrumentationHookFile(page) && pagesType === PAGE_TYPES.ROOT\n\n      let pageRuntime = staticInfo?.runtime\n\n      if (\n        isMiddlewareFile(page) &&\n        !config.experimental.nodeMiddleware &&\n        pageRuntime === 'nodejs'\n      ) {\n        Log.warn(\n          'nodejs runtime support for middleware requires experimental.nodeMiddleware be enabled in your next.config'\n        )\n        pageRuntime = 'edge'\n      }\n\n      runDependingOnPageType({\n        page,\n        pageRuntime: staticInfo.runtime,\n        pageType: pagesType,\n        onClient: () => {\n          if (isServerComponent || isInsideAppDir) {\n            // We skip the initial entries for server component pages and let the\n            // server compiler inject them instead.\n          } else {\n            client[clientBundlePath] = getClientEntry({\n              absolutePagePath,\n              page,\n            })\n          }\n        },\n        onServer: () => {\n          if (pagesType === 'app' && appDir) {\n            const matchedAppPaths = appPathsPerRoute[normalizeAppPath(page)]\n            server[serverBundlePath] = getAppEntry({\n              page,\n              name: serverBundlePath,\n              pagePath: absolutePagePath,\n              appDir,\n              appPaths: matchedAppPaths,\n              pageExtensions,\n              basePath: config.basePath,\n              assetPrefix: config.assetPrefix,\n              nextConfigOutput: config.output,\n              nextConfigExperimentalUseEarlyImport: config.experimental\n                .useEarlyImport\n                ? true\n                : undefined,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: encodeToBase64(staticInfo.middleware || {}),\n            })\n          } else if (isInstrumentation) {\n            server[serverBundlePath.replace('src/', '')] =\n              getInstrumentationEntry({\n                absolutePagePath,\n                isEdgeServer: false,\n                isDev: false,\n              })\n          } else if (isMiddlewareFile(page)) {\n            server[serverBundlePath.replace('src/', '')] = getEdgeServerEntry({\n              ...params,\n              rootDir,\n              absolutePagePath: absolutePagePath,\n              bundlePath: clientBundlePath,\n              isDev: false,\n              isServerComponent,\n              page,\n              middleware: staticInfo?.middleware,\n              pagesType,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: staticInfo.middleware,\n            })\n          } else if (isAPIRoute(page)) {\n            server[serverBundlePath] = [\n              getRouteLoaderEntry({\n                kind: RouteKind.PAGES_API,\n                page,\n                absolutePagePath,\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: staticInfo.middleware || {},\n              }),\n            ]\n          } else if (\n            !isMiddlewareFile(page) &&\n            !isInternalComponent(absolutePagePath) &&\n            !isNonRoutePagesPage(page)\n          ) {\n            server[serverBundlePath] = [\n              getRouteLoaderEntry({\n                kind: RouteKind.PAGES,\n                page,\n                pages,\n                absolutePagePath,\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: staticInfo.middleware ?? {},\n              }),\n            ]\n          } else {\n            server[serverBundlePath] = [absolutePagePath]\n          }\n        },\n        onEdgeServer: () => {\n          let appDirLoader: string = ''\n          if (isInstrumentation) {\n            edgeServer[serverBundlePath.replace('src/', '')] =\n              getInstrumentationEntry({\n                absolutePagePath,\n                isEdgeServer: true,\n                isDev: false,\n              })\n          } else {\n            if (pagesType === 'app') {\n              const matchedAppPaths = appPathsPerRoute[normalizeAppPath(page)]\n              appDirLoader = getAppEntry({\n                name: serverBundlePath,\n                page,\n                pagePath: absolutePagePath,\n                appDir: appDir!,\n                appPaths: matchedAppPaths,\n                pageExtensions,\n                basePath: config.basePath,\n                assetPrefix: config.assetPrefix,\n                nextConfigOutput: config.output,\n                // This isn't used with edge as it needs to be set on the entry module, which will be the `edgeServerEntry` instead.\n                // Still passing it here for consistency.\n                preferredRegion: staticInfo.preferredRegion,\n                middlewareConfig: Buffer.from(\n                  JSON.stringify(staticInfo.middleware || {})\n                ).toString('base64'),\n              }).import\n            }\n            edgeServer[serverBundlePath] = getEdgeServerEntry({\n              ...params,\n              rootDir,\n              absolutePagePath: absolutePagePath,\n              bundlePath: clientBundlePath,\n              isDev: false,\n              isServerComponent,\n              page,\n              middleware: staticInfo?.middleware,\n              pagesType,\n              appDirLoader,\n              preferredRegion: staticInfo.preferredRegion,\n              middlewareConfig: staticInfo.middleware,\n            })\n          }\n        },\n      })\n    }\n\n  const promises: Promise<void[]>[] = []\n\n  if (appPaths) {\n    const entryHandler = getEntryHandler(appPaths, PAGE_TYPES.APP)\n    promises.push(Promise.all(Object.keys(appPaths).map(entryHandler)))\n  }\n  if (rootPaths) {\n    promises.push(\n      Promise.all(\n        Object.keys(rootPaths).map(getEntryHandler(rootPaths, PAGE_TYPES.ROOT))\n      )\n    )\n  }\n  promises.push(\n    Promise.all(\n      Object.keys(pages).map(getEntryHandler(pages, PAGE_TYPES.PAGES))\n    )\n  )\n\n  await Promise.all(promises)\n\n  // Optimization: If there's only one instrumentation hook in edge compiler, which means there's no edge server entry.\n  // We remove the edge instrumentation entry from edge compiler as it can be pure server side.\n  if (edgeServer.instrumentation && Object.keys(edgeServer).length === 1) {\n    delete edgeServer.instrumentation\n  }\n\n  return {\n    client,\n    server,\n    edgeServer,\n    middlewareMatchers,\n  }\n}\n\nexport function finalizeEntrypoint({\n  name,\n  compilerType,\n  value,\n  isServerComponent,\n  hasAppDir,\n}: {\n  compilerType?: CompilerNameValues\n  name: string\n  value: ObjectValue<webpack.EntryObject>\n  isServerComponent?: boolean\n  hasAppDir?: boolean\n}): ObjectValue<webpack.EntryObject> {\n  const entry =\n    typeof value !== 'object' || Array.isArray(value)\n      ? { import: value }\n      : value\n\n  const isApi = name.startsWith('pages/api/')\n  const isInstrumentation = isInstrumentationHookFilename(name)\n\n  switch (compilerType) {\n    case COMPILER_NAMES.server: {\n      const layer = isApi\n        ? WEBPACK_LAYERS.apiNode\n        : isInstrumentation\n          ? WEBPACK_LAYERS.instrument\n          : isServerComponent\n            ? WEBPACK_LAYERS.reactServerComponents\n            : name.startsWith('pages/')\n              ? WEBPACK_LAYERS.pagesDirNode\n              : undefined\n\n      return {\n        publicPath: isApi ? '' : undefined,\n        runtime: isApi ? 'webpack-api-runtime' : 'webpack-runtime',\n        layer,\n        ...entry,\n      }\n    }\n    case COMPILER_NAMES.edgeServer: {\n      return {\n        layer: isApi\n          ? WEBPACK_LAYERS.apiEdge\n          : isMiddlewareFilename(name) || isInstrumentation\n            ? WEBPACK_LAYERS.middleware\n            : name.startsWith('pages/')\n              ? WEBPACK_LAYERS.pagesDirEdge\n              : undefined,\n        library: { name: ['_ENTRIES', `middleware_[name]`], type: 'assign' },\n        runtime: EDGE_RUNTIME_WEBPACK,\n        asyncChunks: false,\n        ...entry,\n      }\n    }\n    case COMPILER_NAMES.client: {\n      const isAppLayer =\n        hasAppDir &&\n        (name === CLIENT_STATIC_FILES_RUNTIME_MAIN_APP ||\n          name === APP_CLIENT_INTERNALS ||\n          name.startsWith('app/'))\n\n      if (\n        // Client special cases\n        name !== CLIENT_STATIC_FILES_RUNTIME_POLYFILLS &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_MAIN &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_MAIN_APP &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_AMP &&\n        name !== CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH\n      ) {\n        if (isAppLayer) {\n          return {\n            dependOn: CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n            layer: WEBPACK_LAYERS.appPagesBrowser,\n            ...entry,\n          }\n        }\n\n        return {\n          dependOn:\n            name.startsWith('pages/') && name !== 'pages/_app'\n              ? 'pages/_app'\n              : CLIENT_STATIC_FILES_RUNTIME_MAIN,\n          layer: WEBPACK_LAYERS.pagesDirBrowser,\n          ...entry,\n        }\n      }\n\n      if (isAppLayer) {\n        return {\n          layer: WEBPACK_LAYERS.appPagesBrowser,\n          ...entry,\n        }\n      }\n\n      return {\n        layer: WEBPACK_LAYERS.pagesDirBrowser,\n        ...entry,\n      }\n    }\n    default: {\n      // Should never happen.\n      throw new Error('Invalid compiler type')\n    }\n  }\n}\n"], "names": ["Log", "posix", "join", "dirname", "extname", "normalize", "stringify", "fs", "PAGES_DIR_ALIAS", "ROOT_DIR_ALIAS", "APP_DIR_ALIAS", "WEBPACK_LAYERS", "INSTRUMENTATION_HOOK_FILENAME", "isAPIRoute", "isEdgeRuntime", "APP_CLIENT_INTERNALS", "RSC_MODULE_TYPES", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "COMPILER_NAMES", "EDGE_RUNTIME_WEBPACK", "isMiddlewareFile", "isMiddlewareFilename", "isInstrumentationHookFile", "isInstrumentationHookFilename", "reduceAppConfig", "getAppPageStaticInfo", "getPageStaticInfo", "normalizePathSep", "normalizePagePath", "normalizeAppPath", "encodeMatchers", "isAppRouteRoute", "normalizeMetadataPageToRoute", "normalizeMetadataRoute", "getRouteLoaderEntry", "isInternalComponent", "isNonRoutePagesPage", "isMetadataRouteFile", "RouteKind", "encodeToBase64", "normalizeCatchAllRoutes", "PAGE_TYPES", "isAppPageRoute", "sortByPageExts", "pageExtensions", "a", "b", "aExt", "bExt", "aNoExt", "substring", "length", "bNoExt", "aExtIndex", "indexOf", "bExtIndex", "getStaticInfoIncludingLayouts", "isInsideAppDir", "pageFilePath", "appDir", "config", "nextConfig", "isDev", "page", "pageType", "APP", "PAGES", "pageStaticInfo", "type", "segments", "layoutFiles", "potentialLayoutFiles", "map", "ext", "dir", "startsWith", "potentialLayoutFile", "layoutFile", "existsSync", "push", "layoutStaticInfo", "unshift", "runtime", "preferredRegion", "maxDuration", "getPageFromPath", "pagePath", "replace", "RegExp", "getPageFilePath", "absolutePagePath", "pagesDir", "rootDir", "require", "resolve", "createPagesMapping", "pagePaths", "pagesType", "isAppRoute", "pages", "promises", "endsWith", "includes", "page<PERSON><PERSON>", "normalizedPath", "route", "filePath", "staticInfo", "generateImageMetadata", "generateSitemaps", "Promise", "all", "ROOT", "hasAppPages", "Object", "keys", "some", "root", "getEdgeServerEntry", "opts", "appDirLoader", "loaderParams", "<PERSON><PERSON><PERSON>", "from", "toString", "JSON", "middlewareConfig", "cacheHandlers", "experimental", "import", "layer", "reactServerComponents", "matchers", "middleware", "apiEdge", "absolute500Path", "absoluteAppPath", "absoluteDocumentPath", "absoluteErrorPath", "dev", "isServerComponent", "stringifiedConfig", "sriEnabled", "sri", "algorithm", "cache<PERSON><PERSON><PERSON>", "serverActions", "serverSideRendering", "undefined", "getInstrumentationEntry", "filename", "isEdgeServer", "instrument", "getApp<PERSON><PERSON>der", "process", "env", "BUILTIN_APP_LOADER", "getAppEntry", "NEXT_RSPACK", "projectRoot", "__dirname", "getClientEntry", "loaderOptions", "page<PERSON><PERSON>der", "runDependingOnPageType", "params", "onServer", "onEdgeServer", "pageRuntime", "onClient", "createEntrypoints", "rootPaths", "appPaths", "edgeServer", "server", "client", "middlewareMatchers", "appPathsPerRoute", "pathname", "actualPath", "fromEntries", "entries", "k", "v", "sort", "getEntryHandler", "mappings", "bundleFile", "clientBundlePath", "serverBundlePath", "slice", "rsc", "regexp", "originalSource", "isInstrumentation", "nodeMiddleware", "warn", "matchedAppPaths", "name", "basePath", "assetPrefix", "nextConfigOutput", "output", "nextConfigExperimentalUseEarlyImport", "useEarlyImport", "bundlePath", "kind", "PAGES_API", "<PERSON><PERSON><PERSON><PERSON>", "instrumentation", "finalizeEntrypoint", "compilerType", "value", "hasAppDir", "entry", "Array", "isArray", "isApi", "apiNode", "pagesDirNode", "publicPath", "pagesDirEdge", "library", "asyncChunks", "isApp<PERSON><PERSON>er", "dependOn", "appPagesBrowser", "pagesDirBrowser", "Error"], "mappings": "AAWA,YAAYA,SAAS,eAAc;AAInC,SAASC,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,SAAS,QAAQ,OAAM;AAC/D,SAASC,SAAS,QAAQ,cAAa;AACvC,OAAOC,QAAQ,KAAI;AACnB,SACEC,eAAe,EACfC,cAAc,EACdC,aAAa,EACbC,cAAc,EACdC,6BAA6B,QACxB,mBAAkB;AACzB,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SACEC,oBAAoB,EACpBC,gBAAgB,EAChBC,gCAAgC,QAC3B,0BAAyB;AAChC,SACEC,+BAA+B,EAC/BC,gCAAgC,EAChCC,oCAAoC,EACpCC,qCAAqC,EACrCC,yCAAyC,EACzCC,cAAc,EACdC,oBAAoB,QACf,0BAAyB;AAGhC,SACEC,gBAAgB,EAChBC,oBAAoB,EACpBC,yBAAyB,EACzBC,6BAA6B,EAC7BC,eAAe,QACV,UAAS;AAChB,SACEC,oBAAoB,EACpBC,iBAAiB,QACZ,kCAAiC;AACxC,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,iBAAiB,QAAQ,8CAA6C;AAE/E,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SAASC,cAAc,QAAQ,2CAA0C;AAEzE,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SACEC,4BAA4B,EAC5BC,sBAAsB,QACjB,qCAAoC;AAC3C,SAASC,mBAAmB,QAAQ,sCAAqC;AACzE,SACEC,mBAAmB,EACnBC,mBAAmB,QACd,+BAA8B;AACrC,SAASC,mBAAmB,QAAQ,oCAAmC;AACvE,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,cAAc,QAAQ,0BAAyB;AACxD,SAASC,uBAAuB,QAAQ,8BAA6B;AAGrE,SAASC,UAAU,QAAQ,oBAAmB;AAC9C,SAASC,cAAc,QAAQ,2BAA0B;AAEzD,OAAO,SAASC,eAAeC,cAA8B;IAC3D,OAAO,CAACC,GAAWC;QACjB,uDAAuD;QACvD,wDAAwD;QACxD,qDAAqD;QACrD,kBAAkB;QAClB,MAAMC,OAAOhD,QAAQ8C;QACrB,MAAMG,OAAOjD,QAAQ+C;QAErB,MAAMG,SAASJ,EAAEK,SAAS,CAAC,GAAGL,EAAEM,MAAM,GAAGJ,KAAKI,MAAM;QACpD,MAAMC,SAASP,EAAEK,SAAS,CAAC,GAAGJ,EAAEK,MAAM,GAAGH,KAAKG,MAAM;QAEpD,IAAIF,WAAWG,QAAQ,OAAO;QAE9B,oEAAoE;QACpE,MAAMC,YAAYT,eAAeU,OAAO,CAACP,KAAKG,SAAS,CAAC;QACxD,MAAMK,YAAYX,eAAeU,OAAO,CAACN,KAAKE,SAAS,CAAC;QAExD,OAAOK,YAAYF;IACrB;AACF;AAEA,OAAO,eAAeG,8BAA8B,EAClDC,cAAc,EACdb,cAAc,EACdc,YAAY,EACZC,MAAM,EACNC,QAAQC,UAAU,EAClBC,KAAK,EACLC,IAAI,EASL;IACC,6EAA6E;IAC7E,MAAMC,WAAWP,iBAAiBhB,WAAWwB,GAAG,GAAGxB,WAAWyB,KAAK;IAEnE,MAAMC,iBAAiB,MAAMzC,kBAAkB;QAC7CmC;QACAH;QACAI;QACAC;QACAC;IACF;IAEA,IAAIG,eAAeC,IAAI,KAAK3B,WAAWyB,KAAK,IAAI,CAACP,QAAQ;QACvD,OAAOQ;IACT;IAEA,MAAME,WAAW;QAACF;KAAe;IAEjC,sDAAsD;IACtD,IAAIzB,eAAeqB,OAAO;QACxB,MAAMO,cAAc,EAAE;QACtB,MAAMC,uBAAuB3B,eAAe4B,GAAG,CAAC,CAACC,MAAQ,YAAYA;QACrE,IAAIC,MAAM5E,QAAQ4D;QAElB,yDAAyD;QACzD,MAAOgB,IAAIC,UAAU,CAAChB,QAAS;YAC7B,KAAK,MAAMiB,uBAAuBL,qBAAsB;gBACtD,MAAMM,aAAahF,KAAK6E,KAAKE;gBAC7B,IAAI,CAAC1E,GAAG4E,UAAU,CAACD,aAAa;oBAC9B;gBACF;gBACAP,YAAYS,IAAI,CAACF;YACnB;YACA,6BAA6B;YAC7BH,MAAM7E,KAAK6E,KAAK;QAClB;QAEA,KAAK,MAAMG,cAAcP,YAAa;YACpC,MAAMU,mBAAmB,MAAMvD,qBAAqB;gBAClDoC;gBACAH,cAAcmB;gBACdf;gBACAC;gBACAC,UAAUP,iBAAiBhB,WAAWwB,GAAG,GAAGxB,WAAWyB,KAAK;YAC9D;YAEAG,SAASY,OAAO,CAACD;QACnB;IACF;IAEA,MAAMpB,SAASpC,gBAAgB6C;IAE/B,OAAO;QACL,GAAGF,cAAc;QACjBP;QACAsB,SAAStB,OAAOsB,OAAO;QACvBC,iBAAiBvB,OAAOuB,eAAe;QACvCC,aAAaxB,OAAOwB,WAAW;IACjC;AACF;AAIA;;CAEC,GACD,OAAO,SAASC,gBACdC,QAAgB,EAChB1C,cAA8B;IAE9B,IAAImB,OAAOpC,iBACT2D,SAASC,OAAO,CAAC,IAAIC,OAAO,CAAC,KAAK,EAAE5C,eAAe/C,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG;IAGrEkE,OAAOA,KAAKwB,OAAO,CAAC,YAAY;IAEhC,OAAOxB,SAAS,KAAK,MAAMA;AAC7B;AAEA,OAAO,SAAS0B,gBAAgB,EAC9BC,gBAAgB,EAChBC,QAAQ,EACRhC,MAAM,EACNiC,OAAO,EAMR;IACC,IAAIF,iBAAiBf,UAAU,CAACxE,oBAAoBwF,UAAU;QAC5D,OAAOD,iBAAiBH,OAAO,CAACpF,iBAAiBwF;IACnD;IAEA,IAAID,iBAAiBf,UAAU,CAACtE,kBAAkBsD,QAAQ;QACxD,OAAO+B,iBAAiBH,OAAO,CAAClF,eAAesD;IACjD;IAEA,IAAI+B,iBAAiBf,UAAU,CAACvE,iBAAiB;QAC/C,OAAOsF,iBAAiBH,OAAO,CAACnF,gBAAgBwF;IAClD;IAEA,OAAOC,QAAQC,OAAO,CAACJ;AACzB;AAEA;;;CAGC,GACD,OAAO,eAAeK,mBAAmB,EACvCjC,KAAK,EACLlB,cAAc,EACdoD,SAAS,EACTC,SAAS,EACTN,QAAQ,EACRhC,MAAM,EAQP;IACC,MAAMuC,aAAaD,cAAc;IACjC,MAAME,QAAqB,CAAC;IAC5B,MAAMC,WAAWJ,UAAUxB,GAAG,CAAgB,OAAOc;QACnD,uCAAuC;QACvC,IAAIA,SAASe,QAAQ,CAAC,YAAYzD,eAAe0D,QAAQ,CAAC,OAAO;YAC/D;QACF;QAEA,IAAIC,UAAUlB,gBAAgBC,UAAU1C;QACxC,IAAIsD,YAAY;YACdK,UAAUA,QAAQhB,OAAO,CAAC,QAAQ;YAClC,IAAIgB,YAAY,cAAc;gBAC5BA,UAAU3F;YACZ;QACF;QAEA,MAAM4F,iBAAiB7E,iBACrB9B,KACEoG,cAAc,UACV9F,kBACA8F,cAAc,QACZ5F,gBACAD,gBACNkF;QAIJ,IAAImB,QAAQR,cAAc,QAAQhE,uBAAuBsE,WAAWA;QAEpE,IACEN,cAAc,SACd5D,oBAAoBiD,UAAU1C,gBAAgB,OAC9C;YACA,MAAM8D,WAAW7G,KAAK8D,QAAS2B;YAC/B,MAAMqB,aAAa,MAAMjF,kBAAkB;gBACzCmC,YAAY,CAAC;gBACbH,cAAcgD;gBACd5C;gBACAC,MAAMwC;gBACNvC,UAAUiC;YACZ;YAEAQ,QAAQzE,6BACNyE,OACA,CAAC,CAAEE,CAAAA,WAAWC,qBAAqB,IAAID,WAAWE,gBAAgB,AAAD;QAErE;QAEAV,KAAK,CAACM,MAAM,GAAGD;IACjB;IAEA,MAAMM,QAAQC,GAAG,CAACX;IAElB,OAAQH;QACN,KAAKxD,WAAWuE,IAAI;YAAE;gBACpB,OAAOb;YACT;QACA,KAAK1D,WAAWwB,GAAG;YAAE;gBACnB,MAAMgD,cAAcC,OAAOC,IAAI,CAAChB,OAAOiB,IAAI,CAAC,CAACrD,OAC3CA,KAAKsC,QAAQ,CAAC;gBAEhB,OAAO;oBACL,kEAAkE;oBAClE,kFAAkF;oBAClF,GAAIY,eAAe;wBACjB,CAACrG,iCAAiC,EAChC;oBACJ,CAAC;oBACD,GAAGuF,KAAK;gBACV;YACF;QACA,KAAK1D,WAAWyB,KAAK;YAAE;gBACrB,IAAIJ,OAAO;oBACT,OAAOqC,KAAK,CAAC,QAAQ;oBACrB,OAAOA,KAAK,CAAC,UAAU;oBACvB,OAAOA,KAAK,CAAC,aAAa;gBAC5B;gBAEA,uEAAuE;gBACvE,uEAAuE;gBACvE,oBAAoB;gBACpB,MAAMkB,OAAOvD,SAAS6B,WAAWxF,kBAAkB;gBAEnD,OAAO;oBACL,SAAS,GAAGkH,KAAK,KAAK,CAAC;oBACvB,WAAW,GAAGA,KAAK,OAAO,CAAC;oBAC3B,cAAc,GAAGA,KAAK,UAAU,CAAC;oBACjC,GAAGlB,KAAK;gBACV;YACF;QACA;YAAS;gBACP,OAAO,CAAC;YACV;IACF;AACF;AAkBA,OAAO,SAASmB,mBAAmBC,IAgBlC;QA6EgCA;IA5E/B,IACEA,KAAKtB,SAAS,KAAK,SACnBlE,gBAAgBwF,KAAKxD,IAAI,KACzBwD,KAAKC,YAAY,EACjB;QACA,MAAMC,eAAwC;YAC5C/B,kBAAkB6B,KAAK7B,gBAAgB;YACvC3B,MAAMwD,KAAKxD,IAAI;YACfyD,cAAcE,OAAOC,IAAI,CAACJ,KAAKC,YAAY,IAAI,IAAII,QAAQ,CAAC;YAC5D/D,YAAY6D,OAAOC,IAAI,CAACE,KAAK5H,SAAS,CAACsH,KAAK3D,MAAM,GAAGgE,QAAQ,CAAC;YAC9DzC,iBAAiBoC,KAAKpC,eAAe;YACrC2C,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK5H,SAAS,CAACsH,KAAKO,gBAAgB,IAAI,CAAC,IACzCF,QAAQ,CAAC;YACXG,eAAeF,KAAK5H,SAAS,CAC3BsH,KAAK3D,MAAM,CAACoE,YAAY,CAACD,aAAa,IAAI,CAAC;QAE/C;QAEA,OAAO;YACLE,QAAQ,CAAC,2BAA2B,EAAEhI,UAAUwH,cAAc,CAAC,CAAC;YAChES,OAAO5H,eAAe6H,qBAAqB;QAC7C;IACF;IAEA,IAAI/G,iBAAiBmG,KAAKxD,IAAI,GAAG;YAKnBwD;QAJZ,MAAME,eAAwC;YAC5C/B,kBAAkB6B,KAAK7B,gBAAgB;YACvC3B,MAAMwD,KAAKxD,IAAI;YACf6B,SAAS2B,KAAK3B,OAAO;YACrBwC,UAAUb,EAAAA,mBAAAA,KAAKc,UAAU,qBAAfd,iBAAiBa,QAAQ,IAC/BtG,eAAeyF,KAAKc,UAAU,CAACD,QAAQ,IACvC;YACJjD,iBAAiBoC,KAAKpC,eAAe;YACrC2C,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK5H,SAAS,CAACsH,KAAKO,gBAAgB,IAAI,CAAC,IACzCF,QAAQ,CAAC;QACb;QAEA,OAAO;YACLK,QAAQ,CAAC,uBAAuB,EAAEhI,UAAUwH,cAAc,CAAC,CAAC;YAC5DS,OAAO5H,eAAe+H,UAAU;QAClC;IACF;IAEA,IAAI7H,WAAW+G,KAAKxD,IAAI,GAAG;QACzB,MAAM0D,eAA0C;YAC9C/B,kBAAkB6B,KAAK7B,gBAAgB;YACvC3B,MAAMwD,KAAKxD,IAAI;YACf6B,SAAS2B,KAAK3B,OAAO;YACrBT,iBAAiBoC,KAAKpC,eAAe;YACrC2C,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK5H,SAAS,CAACsH,KAAKO,gBAAgB,IAAI,CAAC,IACzCF,QAAQ,CAAC;QACb;QAEA,OAAO;YACLK,QAAQ,CAAC,0BAA0B,EAAEhI,UAAUwH,cAAc,CAAC,CAAC;YAC/DS,OAAO5H,eAAegI,OAAO;QAC/B;IACF;IAEA,MAAMb,eAAmC;QACvCc,iBAAiBhB,KAAKpB,KAAK,CAAC,OAAO,IAAI;QACvCqC,iBAAiBjB,KAAKpB,KAAK,CAAC,QAAQ;QACpCsC,sBAAsBlB,KAAKpB,KAAK,CAAC,aAAa;QAC9CuC,mBAAmBnB,KAAKpB,KAAK,CAAC,UAAU;QACxCT,kBAAkB6B,KAAK7B,gBAAgB;QACvCiD,KAAKpB,KAAKzD,KAAK;QACf8E,mBAAmBrB,KAAKqB,iBAAiB;QACzC7E,MAAMwD,KAAKxD,IAAI;QACf8E,mBAAmBnB,OAAOC,IAAI,CAACE,KAAK5H,SAAS,CAACsH,KAAK3D,MAAM,GAAGgE,QAAQ,CAClE;QAEF3B,WAAWsB,KAAKtB,SAAS;QACzBuB,cAAcE,OAAOC,IAAI,CAACJ,KAAKC,YAAY,IAAI,IAAII,QAAQ,CAAC;QAC5DkB,YAAY,CAACvB,KAAKzD,KAAK,IAAI,CAAC,GAACyD,gCAAAA,KAAK3D,MAAM,CAACoE,YAAY,CAACe,GAAG,qBAA5BxB,8BAA8ByB,SAAS;QACpEC,cAAc1B,KAAK3D,MAAM,CAACqF,YAAY;QACtC9D,iBAAiBoC,KAAKpC,eAAe;QACrC2C,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK5H,SAAS,CAACsH,KAAKO,gBAAgB,IAAI,CAAC,IACzCF,QAAQ,CAAC;QACXsB,eAAe3B,KAAK3D,MAAM,CAACoE,YAAY,CAACkB,aAAa;QACrDnB,eAAeF,KAAK5H,SAAS,CAACsH,KAAK3D,MAAM,CAACoE,YAAY,CAACD,aAAa,IAAI,CAAC;IAC3E;IAEA,OAAO;QACLE,QAAQ,CAAC,qBAAqB,EAAEJ,KAAK5H,SAAS,CAACwH,cAAc,CAAC,CAAC;QAC/D,sEAAsE;QACtE,2EAA2E;QAC3E,sBAAsB;QACtBS,OAAOX,KAAKC,YAAY,GAAGlH,eAAe6I,mBAAmB,GAAGC;IAClE;AACF;AAEA,OAAO,SAASC,wBAAwB9B,IAIvC;IACC,2DAA2D;IAC3D,MAAM+B,WAAW,GACf/B,KAAKgC,YAAY,GAAG,UAAUhC,KAAKzD,KAAK,GAAG,KAAK,QAC/CvD,8BAA8B,GAAG,CAAC;IAErC,OAAO;QACL0H,QAAQV,KAAK7B,gBAAgB;QAC7B4D;QACApB,OAAO5H,eAAekJ,UAAU;IAClC;AACF;AAEA,OAAO,SAASC;IACd,OAAOC,QAAQC,GAAG,CAACC,kBAAkB,GACjC,CAAC,uBAAuB,CAAC,GACzB;AACN;AAEA,OAAO,SAASC,YAAYtC,IAAgC;IAC1D,IAAImC,QAAQC,GAAG,CAACG,WAAW,IAAIJ,QAAQC,GAAG,CAACC,kBAAkB,EAAE;;QAC3DrC,KAAawC,WAAW,GAAG/J,UAAUH,KAAKmK,WAAW;IACzD;IACA,OAAO;QACL/B,QAAQ,GAAGwB,eAAe,CAAC,EAAExJ,UAAUsH,MAAM,CAAC,CAAC;QAC/CW,OAAO5H,eAAe6H,qBAAqB;IAC7C;AACF;AAEA,OAAO,SAAS8B,eAAe1C,IAG9B;IACC,MAAM2C,gBAA0C;QAC9CxE,kBAAkB6B,KAAK7B,gBAAgB;QACvC3B,MAAMwD,KAAKxD,IAAI;IACjB;IAEA,MAAMoG,aAAa,CAAC,yBAAyB,EAAElK,UAAUiK,eAAe,CAAC,CAAC;IAE1E,wEAAwE;IACxE,kEAAkE;IAClE,UAAU;IACV,OAAO3C,KAAKxD,IAAI,KAAK,UACjB;QAACoG;QAAYtE,QAAQC,OAAO,CAAC;KAAoB,GACjDqE;AACN;AAEA,OAAO,SAASC,uBAA0BC,MAOzC;IACC,IACEA,OAAOrG,QAAQ,KAAKvB,WAAWuE,IAAI,IACnC1F,0BAA0B+I,OAAOtG,IAAI,GACrC;QACAsG,OAAOC,QAAQ;QACfD,OAAOE,YAAY;QACnB;IACF;IAEA,IAAInJ,iBAAiBiJ,OAAOtG,IAAI,GAAG;QACjC,IAAIsG,OAAOG,WAAW,KAAK,UAAU;YACnCH,OAAOC,QAAQ;YACf;QACF,OAAO;YACLD,OAAOE,YAAY;YACnB;QACF;IACF;IAEA,IAAI/J,WAAW6J,OAAOtG,IAAI,GAAG;QAC3B,IAAItD,cAAc4J,OAAOG,WAAW,GAAG;YACrCH,OAAOE,YAAY;YACnB;QACF;QAEAF,OAAOC,QAAQ;QACf;IACF;IACA,IAAID,OAAOtG,IAAI,KAAK,cAAc;QAChCsG,OAAOC,QAAQ;QACf;IACF;IACA,IACED,OAAOtG,IAAI,KAAK,WAChBsG,OAAOtG,IAAI,KAAK,aAChBsG,OAAOtG,IAAI,KAAK,UAChBsG,OAAOtG,IAAI,KAAK,QAChB;QACAsG,OAAOI,QAAQ;QACfJ,OAAOC,QAAQ;QACf;IACF;IACA,IAAI7J,cAAc4J,OAAOG,WAAW,GAAG;QACrCH,OAAOI,QAAQ;QACfJ,OAAOE,YAAY;QACnB;IACF;IAEAF,OAAOI,QAAQ;IACfJ,OAAOC,QAAQ;IACf;AACF;AAEA,OAAO,eAAeI,kBACpBL,MAA+B;IAO/B,MAAM,EACJzG,MAAM,EACNuC,KAAK,EACLR,QAAQ,EACR7B,KAAK,EACL8B,OAAO,EACP+E,SAAS,EACThH,MAAM,EACNiH,QAAQ,EACRhI,cAAc,EACf,GAAGyH;IACJ,MAAMQ,aAAkC,CAAC;IACzC,MAAMC,SAA8B,CAAC;IACrC,MAAMC,SAA8B,CAAC;IACrC,IAAIC,qBAAsD5B;IAE1D,IAAI6B,mBAA6C,CAAC;IAClD,IAAItH,UAAUiH,UAAU;QACtB,IAAK,MAAMM,YAAYN,SAAU;YAC/B,MAAMpE,iBAAiB3E,iBAAiBqJ;YACxC,MAAMC,aAAaP,QAAQ,CAACM,SAAS;YACrC,IAAI,CAACD,gBAAgB,CAACzE,eAAe,EAAE;gBACrCyE,gBAAgB,CAACzE,eAAe,GAAG,EAAE;YACvC;YACAyE,gBAAgB,CAACzE,eAAe,CAACzB,IAAI,CACnC,4EAA4E;YAC5EM,gBAAgB8F,YAAYvI,gBAAgB2C,OAAO,CAAClF,eAAe;QAEvE;QAEA,uCAAuC;QACvCmC,wBAAwByI;QAExB,sEAAsE;QACtEA,mBAAmB/D,OAAOkE,WAAW,CACnClE,OAAOmE,OAAO,CAACJ,kBAAkBzG,GAAG,CAAC,CAAC,CAAC8G,GAAGC,EAAE,GAAK;gBAACD;gBAAGC,EAAEC,IAAI;aAAG;IAElE;IAEA,MAAMC,kBACJ,CAACC,UAAuBzF,YACxB,OAAOlC;YACL,MAAM4H,aAAa/J,kBAAkBmC;YACrC,MAAM6H,mBAAmBhM,MAAMC,IAAI,CAACoG,WAAW0F;YAC/C,MAAME,mBACJ5F,cAAcxD,WAAWyB,KAAK,GAC1BtE,MAAMC,IAAI,CAAC,SAAS8L,cACpB1F,cAAcxD,WAAWwB,GAAG,GAC1BrE,MAAMC,IAAI,CAAC,OAAO8L,cAClBA,WAAWG,KAAK,CAAC;YAEzB,MAAMpG,mBAAmBgG,QAAQ,CAAC3H,KAAK;YAEvC,iCAAiC;YACjC,MAAML,eAAe+B,gBAAgB;gBACnCC;gBACAC;gBACAhC;gBACAiC;YACF;YAEA,MAAMnC,iBACJ,CAAC,CAACE,UACD+B,CAAAA,iBAAiBf,UAAU,CAACtE,kBAC3BqF,iBAAiBf,UAAU,CAAChB,OAAM;YAEtC,MAAMgD,aAA6B,MAAMnD,8BAA8B;gBACrEC;gBACAb;gBACAc;gBACAC;gBACAC;gBACAE;gBACAC;YACF;YAEA,iCAAiC;YACjC,MAAM6E,oBACJnF,kBAAkBkD,WAAWoF,GAAG,KAAKpL,iBAAiBoK,MAAM;YAE9D,IAAI3J,iBAAiB2C,OAAO;oBACL4C;gBAArBqE,qBAAqBrE,EAAAA,yBAAAA,WAAW0B,UAAU,qBAArB1B,uBAAuByB,QAAQ,KAAI;oBACtD;wBAAE4D,QAAQ;wBAAMC,gBAAgB;oBAAU;iBAC3C;YACH;YAEA,MAAMC,oBACJ5K,0BAA0ByC,SAASkC,cAAcxD,WAAWuE,IAAI;YAElE,IAAIwD,cAAc7D,8BAAAA,WAAYzB,OAAO;YAErC,IACE9D,iBAAiB2C,SACjB,CAACH,OAAOoE,YAAY,CAACmE,cAAc,IACnC3B,gBAAgB,UAChB;gBACA7K,IAAIyM,IAAI,CACN;gBAEF5B,cAAc;YAChB;YAEAJ,uBAAuB;gBACrBrG;gBACAyG,aAAa7D,WAAWzB,OAAO;gBAC/BlB,UAAUiC;gBACVwE,UAAU;oBACR,IAAI7B,qBAAqBnF,gBAAgB;oBACvC,qEAAqE;oBACrE,uCAAuC;oBACzC,OAAO;wBACLsH,MAAM,CAACa,iBAAiB,GAAG3B,eAAe;4BACxCvE;4BACA3B;wBACF;oBACF;gBACF;gBACAuG,UAAU;oBACR,IAAIrE,cAAc,SAAStC,QAAQ;wBACjC,MAAM0I,kBAAkBpB,gBAAgB,CAACpJ,iBAAiBkC,MAAM;wBAChE+G,MAAM,CAACe,iBAAiB,GAAGhC,YAAY;4BACrC9F;4BACAuI,MAAMT;4BACNvG,UAAUI;4BACV/B;4BACAiH,UAAUyB;4BACVzJ;4BACA2J,UAAU3I,OAAO2I,QAAQ;4BACzBC,aAAa5I,OAAO4I,WAAW;4BAC/BC,kBAAkB7I,OAAO8I,MAAM;4BAC/BC,sCAAsC/I,OAAOoE,YAAY,CACtD4E,cAAc,GACb,OACAxD;4BACJjE,iBAAiBwB,WAAWxB,eAAe;4BAC3C2C,kBAAkBvF,eAAeoE,WAAW0B,UAAU,IAAI,CAAC;wBAC7D;oBACF,OAAO,IAAI6D,mBAAmB;wBAC5BpB,MAAM,CAACe,iBAAiBtG,OAAO,CAAC,QAAQ,IAAI,GAC1C8D,wBAAwB;4BACtB3D;4BACA6D,cAAc;4BACdzF,OAAO;wBACT;oBACJ,OAAO,IAAI1C,iBAAiB2C,OAAO;wBACjC+G,MAAM,CAACe,iBAAiBtG,OAAO,CAAC,QAAQ,IAAI,GAAG+B,mBAAmB;4BAChE,GAAG+C,MAAM;4BACTzE;4BACAF,kBAAkBA;4BAClBmH,YAAYjB;4BACZ9H,OAAO;4BACP8E;4BACA7E;4BACAsE,UAAU,EAAE1B,8BAAAA,WAAY0B,UAAU;4BAClCpC;4BACAd,iBAAiBwB,WAAWxB,eAAe;4BAC3C2C,kBAAkBnB,WAAW0B,UAAU;wBACzC;oBACF,OAAO,IAAI7H,WAAWuD,OAAO;wBAC3B+G,MAAM,CAACe,iBAAiB,GAAG;4BACzB3J,oBAAoB;gCAClB4K,MAAMxK,UAAUyK,SAAS;gCACzBhJ;gCACA2B;gCACAP,iBAAiBwB,WAAWxB,eAAe;gCAC3C2C,kBAAkBnB,WAAW0B,UAAU,IAAI,CAAC;4BAC9C;yBACD;oBACH,OAAO,IACL,CAACjH,iBAAiB2C,SAClB,CAAC5B,oBAAoBuD,qBACrB,CAACtD,oBAAoB2B,OACrB;wBACA+G,MAAM,CAACe,iBAAiB,GAAG;4BACzB3J,oBAAoB;gCAClB4K,MAAMxK,UAAU4B,KAAK;gCACrBH;gCACAoC;gCACAT;gCACAP,iBAAiBwB,WAAWxB,eAAe;gCAC3C2C,kBAAkBnB,WAAW0B,UAAU,IAAI,CAAC;4BAC9C;yBACD;oBACH,OAAO;wBACLyC,MAAM,CAACe,iBAAiB,GAAG;4BAACnG;yBAAiB;oBAC/C;gBACF;gBACA6E,cAAc;oBACZ,IAAI/C,eAAuB;oBAC3B,IAAI0E,mBAAmB;wBACrBrB,UAAU,CAACgB,iBAAiBtG,OAAO,CAAC,QAAQ,IAAI,GAC9C8D,wBAAwB;4BACtB3D;4BACA6D,cAAc;4BACdzF,OAAO;wBACT;oBACJ,OAAO;wBACL,IAAImC,cAAc,OAAO;4BACvB,MAAMoG,kBAAkBpB,gBAAgB,CAACpJ,iBAAiBkC,MAAM;4BAChEyD,eAAeqC,YAAY;gCACzByC,MAAMT;gCACN9H;gCACAuB,UAAUI;gCACV/B,QAAQA;gCACRiH,UAAUyB;gCACVzJ;gCACA2J,UAAU3I,OAAO2I,QAAQ;gCACzBC,aAAa5I,OAAO4I,WAAW;gCAC/BC,kBAAkB7I,OAAO8I,MAAM;gCAC/B,oHAAoH;gCACpH,yCAAyC;gCACzCvH,iBAAiBwB,WAAWxB,eAAe;gCAC3C2C,kBAAkBJ,OAAOC,IAAI,CAC3BE,KAAK5H,SAAS,CAAC0G,WAAW0B,UAAU,IAAI,CAAC,IACzCT,QAAQ,CAAC;4BACb,GAAGK,MAAM;wBACX;wBACA4C,UAAU,CAACgB,iBAAiB,GAAGvE,mBAAmB;4BAChD,GAAG+C,MAAM;4BACTzE;4BACAF,kBAAkBA;4BAClBmH,YAAYjB;4BACZ9H,OAAO;4BACP8E;4BACA7E;4BACAsE,UAAU,EAAE1B,8BAAAA,WAAY0B,UAAU;4BAClCpC;4BACAuB;4BACArC,iBAAiBwB,WAAWxB,eAAe;4BAC3C2C,kBAAkBnB,WAAW0B,UAAU;wBACzC;oBACF;gBACF;YACF;QACF;IAEF,MAAMjC,WAA8B,EAAE;IAEtC,IAAIwE,UAAU;QACZ,MAAMoC,eAAevB,gBAAgBb,UAAUnI,WAAWwB,GAAG;QAC7DmC,SAASrB,IAAI,CAAC+B,QAAQC,GAAG,CAACG,OAAOC,IAAI,CAACyD,UAAUpG,GAAG,CAACwI;IACtD;IACA,IAAIrC,WAAW;QACbvE,SAASrB,IAAI,CACX+B,QAAQC,GAAG,CACTG,OAAOC,IAAI,CAACwD,WAAWnG,GAAG,CAACiH,gBAAgBd,WAAWlI,WAAWuE,IAAI;IAG3E;IACAZ,SAASrB,IAAI,CACX+B,QAAQC,GAAG,CACTG,OAAOC,IAAI,CAAChB,OAAO3B,GAAG,CAACiH,gBAAgBtF,OAAO1D,WAAWyB,KAAK;IAIlE,MAAM4C,QAAQC,GAAG,CAACX;IAElB,qHAAqH;IACrH,6FAA6F;IAC7F,IAAIyE,WAAWoC,eAAe,IAAI/F,OAAOC,IAAI,CAAC0D,YAAY1H,MAAM,KAAK,GAAG;QACtE,OAAO0H,WAAWoC,eAAe;IACnC;IAEA,OAAO;QACLlC;QACAD;QACAD;QACAG;IACF;AACF;AAEA,OAAO,SAASkC,mBAAmB,EACjCZ,IAAI,EACJa,YAAY,EACZC,KAAK,EACLxE,iBAAiB,EACjByE,SAAS,EAOV;IACC,MAAMC,QACJ,OAAOF,UAAU,YAAYG,MAAMC,OAAO,CAACJ,SACvC;QAAEnF,QAAQmF;IAAM,IAChBA;IAEN,MAAMK,QAAQnB,KAAK3H,UAAU,CAAC;IAC9B,MAAMuH,oBAAoB3K,8BAA8B+K;IAExD,OAAQa;QACN,KAAKjM,eAAe4J,MAAM;YAAE;gBAC1B,MAAM5C,QAAQuF,QACVnN,eAAeoN,OAAO,GACtBxB,oBACE5L,eAAekJ,UAAU,GACzBZ,oBACEtI,eAAe6H,qBAAqB,GACpCmE,KAAK3H,UAAU,CAAC,YACdrE,eAAeqN,YAAY,GAC3BvE;gBAEV,OAAO;oBACLwE,YAAYH,QAAQ,KAAKrE;oBACzBlE,SAASuI,QAAQ,wBAAwB;oBACzCvF;oBACA,GAAGoF,KAAK;gBACV;YACF;QACA,KAAKpM,eAAe2J,UAAU;YAAE;gBAC9B,OAAO;oBACL3C,OAAOuF,QACHnN,eAAegI,OAAO,GACtBjH,qBAAqBiL,SAASJ,oBAC5B5L,eAAe+H,UAAU,GACzBiE,KAAK3H,UAAU,CAAC,YACdrE,eAAeuN,YAAY,GAC3BzE;oBACR0E,SAAS;wBAAExB,MAAM;4BAAC;4BAAY,CAAC,iBAAiB,CAAC;yBAAC;wBAAElI,MAAM;oBAAS;oBACnEc,SAAS/D;oBACT4M,aAAa;oBACb,GAAGT,KAAK;gBACV;YACF;QACA,KAAKpM,eAAe6J,MAAM;YAAE;gBAC1B,MAAMiD,aACJX,aACCf,CAAAA,SAASvL,wCACRuL,SAAS5L,wBACT4L,KAAK3H,UAAU,CAAC,OAAM;gBAE1B,IACE,uBAAuB;gBACvB2H,SAAStL,yCACTsL,SAASxL,oCACTwL,SAASvL,wCACTuL,SAASzL,mCACTyL,SAASrL,2CACT;oBACA,IAAI+M,YAAY;wBACd,OAAO;4BACLC,UAAUlN;4BACVmH,OAAO5H,eAAe4N,eAAe;4BACrC,GAAGZ,KAAK;wBACV;oBACF;oBAEA,OAAO;wBACLW,UACE3B,KAAK3H,UAAU,CAAC,aAAa2H,SAAS,eAClC,eACAxL;wBACNoH,OAAO5H,eAAe6N,eAAe;wBACrC,GAAGb,KAAK;oBACV;gBACF;gBAEA,IAAIU,YAAY;oBACd,OAAO;wBACL9F,OAAO5H,eAAe4N,eAAe;wBACrC,GAAGZ,KAAK;oBACV;gBACF;gBAEA,OAAO;oBACLpF,OAAO5H,eAAe6N,eAAe;oBACrC,GAAGb,KAAK;gBACV;YACF;QACA;YAAS;gBACP,uBAAuB;gBACvB,MAAM,qBAAkC,CAAlC,IAAIc,MAAM,0BAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiC;YACzC;IACF;AACF"}