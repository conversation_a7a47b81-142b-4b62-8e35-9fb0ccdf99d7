{"version": 3, "sources": ["../../src/build/index.ts"], "sourcesContent": ["import type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from './webpack/plugins/pages-manifest-plugin'\nimport type { ExportPathMap, NextConfigComplete } from '../server/config-shared'\nimport type { MiddlewareManifest } from './webpack/plugins/middleware-plugin'\nimport type { ActionManifest } from './webpack/plugins/flight-client-entry-plugin'\nimport type { CacheControl, Revalidate } from '../server/lib/cache-control'\n\nimport '../lib/setup-exception-listeners'\n\nimport { loadEnvConfig, type LoadedEnvFiles } from '@next/env'\nimport { bold, yellow } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { makeRe } from 'next/dist/compiled/picomatch'\nimport { existsSync, promises as fs } from 'fs'\nimport os from 'os'\nimport { Worker } from '../lib/worker'\nimport { defaultConfig } from '../server/config-shared'\nimport devalue from 'next/dist/compiled/devalue'\nimport findUp from 'next/dist/compiled/find-up'\nimport { nanoid } from 'next/dist/compiled/nanoid/index.cjs'\nimport path from 'path'\nimport {\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  PAGES_DIR_ALIAS,\n  INSTRUMENTATION_HOOK_FILENAME,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SUFFIX,\n  NEXT_RESUME_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  MATCHED_PATH_HEADER,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n} from '../lib/constants'\nimport { FileType, fileExists } from '../lib/file-exists'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport loadCustomRoutes, {\n  normalizeRouteRegex,\n} from '../lib/load-custom-routes'\nimport type {\n  CustomRoutes,\n  Header,\n  Redirect,\n  Rewrite,\n  RouteHas,\n} from '../lib/load-custom-routes'\nimport { nonNullable } from '../lib/non-nullable'\nimport { recursiveDelete } from '../lib/recursive-delete'\nimport { verifyPartytownSetup } from '../lib/verify-partytown-setup'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  IMAGES_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_FILES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  MIDDLEWARE_MANIFEST,\n  APP_PATHS_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  APP_BUILD_MANIFEST,\n  RSC_MODULE_TYPES,\n  NEXT_FONT_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  DYNAMIC_CSS_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\nimport {\n  getSortedRoutes,\n  isDynamicRoute,\n  getSortedRouteObjects,\n} from '../shared/lib/router/utils'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport loadConfig from '../server/config'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getPagePath } from '../server/require'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n  writeTurborepoAccessTraceResult,\n} from './turborepo-access-trace'\n\nimport {\n  eventBuildOptimize,\n  eventCliSession,\n  eventBuildFeatureUsage,\n  eventNextPlugins,\n  EVENT_BUILD_FEATURE_USAGE,\n  eventPackageUsedInGetServerSideProps,\n  eventBuildCompleted,\n} from '../telemetry/events'\nimport type { EventBuildFeatureUsage } from '../telemetry/events'\nimport { Telemetry } from '../telemetry/storage'\nimport { hadUnsupportedValue } from './analysis/get-page-static-info'\nimport {\n  createPagesMapping,\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from './entries'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { generateBuildId } from './generate-build-id'\nimport { isWriteable } from './is-writeable'\nimport * as Log from './output/log'\nimport createSpinner from './spinner'\nimport { trace, flushAllTraces, setGlobal, type Span } from '../trace'\nimport {\n  detectConflictingPaths,\n  computeFromManifest,\n  getJsPageSizeInKb,\n  printCustomRoutes,\n  printTreeView,\n  copyTracedFiles,\n  isReservedPage,\n  isAppBuiltinNotFoundPage,\n  collectRoutesUsingEdgeRuntime,\n  collectMeta,\n} from './utils'\nimport type { PageInfo, PageInfos } from './utils'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport { writeBuildId } from './write-build-id'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport isError from '../lib/is-error'\nimport type { NextError } from '../lib/is-error'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport { lockfilePatchPromise, teardownTraceSubscriber } from './swc'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getFilesInDir } from '../lib/get-files-in-dir'\nimport { eventSwcPlugins } from '../telemetry/events/swc-plugins'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport {\n  ACTION_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n} from '../client/components/app-router-headers'\nimport { webpackBuild } from './webpack-build'\nimport { NextBuildContext, type MappedPages } from './build-context'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { createClientRouterFilter } from '../lib/create-client-router-filter'\nimport { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { startTypeChecking } from './type-check'\nimport { generateInterceptionRoutesRewrites } from '../lib/generate-interception-routes-rewrites'\n\nimport { buildDataRoute } from '../server/lib/router-utils/build-data-route'\nimport { collectBuildTraces } from './collect-build-traces'\nimport type { BuildTraceContext } from './webpack/plugins/next-trace-entrypoints-plugin'\nimport { formatManifest } from './manifests/formatter/format-manifest'\nimport {\n  recordFrameworkVersion,\n  updateBuildDiagnostics,\n  recordFetchMetrics,\n} from '../diagnostics/build-diagnostics'\nimport { getStartServerInfo, logStartInfo } from '../server/lib/app-info-log'\nimport type { NextEnabledDirectories } from '../server/base-server'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { traceMemoryUsage } from '../lib/memory/trace'\nimport { generateEncryptionKeyBase64 } from '../server/app-render/encryption-utils-server'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport uploadTrace from '../trace/upload-trace'\nimport {\n  checkIsAppPPREnabled,\n  checkIsRoutePPREnabled,\n} from '../server/lib/experimental/ppr'\nimport { FallbackMode, fallbackModeToFallbackField } from '../lib/fallback'\nimport { RenderingMode } from './rendering-mode'\nimport { getParamKeys } from '../server/request/fallback-params'\nimport {\n  formatNodeOptions,\n  getParsedNodeOptionsWithoutInspect,\n} from '../server/lib/utils'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport type { UseCacheTrackerKey } from './webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport {\n  buildPrefetchSegmentDataRoute,\n  type PrefetchSegmentDataRoute,\n} from '../server/lib/router-utils/build-prefetch-segment-data-route'\n\nimport { turbopackBuild } from './turbopack-build'\n\ntype Fallback = null | boolean | string\n\nexport interface PrerenderManifestRoute {\n  dataRoute: string | null\n  experimentalBypassFor?: RouteHas[]\n\n  /**\n   * The headers that should be served along side this prerendered route.\n   */\n  initialHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be served along side this prerendered route.\n   */\n  initialStatus?: number\n\n  /**\n   * The revalidate value for this route. This might be inferred from:\n   * - route segment configs\n   * - fetch calls\n   * - unstable_cache\n   * - \"use cache\"\n   */\n  initialRevalidateSeconds: Revalidate\n\n  /**\n   * The expire value for this route, which is inferred from the \"use cache\"\n   * functions that are used by the route, or the expireTime config.\n   */\n  initialExpireSeconds: number | undefined\n\n  /**\n   * The prefetch data route associated with this page. If not defined, this\n   * page does not support prefetching.\n   */\n  prefetchDataRoute: string | null | undefined\n\n  /**\n   * The dynamic route that this statically prerendered route is based on. If\n   * this is null, then the route was not based on a dynamic route.\n   */\n  srcRoute: string | null\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\nexport interface DynamicPrerenderManifestRoute {\n  dataRoute: string | null\n  dataRouteRegex: string | null\n  experimentalBypassFor?: RouteHas[]\n  fallback: Fallback\n\n  /**\n   * When defined, it describes the revalidation configuration for the fallback\n   * route.\n   */\n  fallbackRevalidate: Revalidate | undefined\n\n  /**\n   * When defined, it describes the expire configuration for the fallback route.\n   */\n  fallbackExpire: number | undefined\n\n  /**\n   * The headers that should used when serving the fallback.\n   */\n  fallbackHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be used when serving the fallback.\n   */\n  fallbackStatus?: number\n\n  /**\n   * The root params that are unknown for this fallback route.\n   */\n  fallbackRootParams: readonly string[] | undefined\n\n  /**\n   * The source route that this fallback route is based on. This is a reference\n   * so that we can associate this dynamic route with the correct source.\n   */\n  fallbackSourceRoute: string | undefined\n\n  prefetchDataRoute: string | null | undefined\n  prefetchDataRouteRegex: string | null | undefined\n  routeRegex: string\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\n/**\n * The headers that are allowed to be used when revalidating routes. Currently\n * this includes both headers used by the pages and app routers.\n */\nconst ALLOWED_HEADERS: string[] = [\n  'host',\n  MATCHED_PATH_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n]\n\nexport type PrerenderManifest = {\n  version: 4\n  routes: { [route: string]: PrerenderManifestRoute }\n  dynamicRoutes: { [route: string]: DynamicPrerenderManifestRoute }\n  notFoundRoutes: string[]\n  preview: __ApiPreviewProps\n}\n\ntype ManifestBuiltRoute = {\n  /**\n   * The route pattern used to match requests for this route.\n   */\n  regex: string\n}\n\nexport type ManifestRewriteRoute = ManifestBuiltRoute & Rewrite\nexport type ManifestRedirectRoute = ManifestBuiltRoute & Redirect\nexport type ManifestHeaderRoute = ManifestBuiltRoute & Header\n\nexport type ManifestRoute = ManifestBuiltRoute & {\n  page: string\n  namedRegex?: string\n  routeKeys?: { [key: string]: string }\n  prefetchSegmentDataRoutes?: PrefetchSegmentDataRoute[]\n}\n\ntype ManifestDataRoute = {\n  page: string\n  routeKeys?: { [key: string]: string }\n  dataRouteRegex: string\n  namedDataRouteRegex?: string\n}\n\nexport type RoutesManifest = {\n  version: number\n  pages404: boolean\n  basePath: string\n  redirects: Array<Redirect>\n  rewrites?:\n    | Array<ManifestRewriteRoute>\n    | {\n        beforeFiles: Array<ManifestRewriteRoute>\n        afterFiles: Array<ManifestRewriteRoute>\n        fallback: Array<ManifestRewriteRoute>\n      }\n  headers: Array<ManifestHeaderRoute>\n  staticRoutes: Array<ManifestRoute>\n  dynamicRoutes: Array<ManifestRoute>\n  dataRoutes: Array<ManifestDataRoute>\n  i18n?: {\n    domains?: ReadonlyArray<{\n      http?: true\n      domain: string\n      locales?: readonly string[]\n      defaultLocale: string\n    }>\n    locales: readonly string[]\n    defaultLocale: string\n    localeDetection?: false\n  }\n  rsc: {\n    header: typeof RSC_HEADER\n    didPostponeHeader: typeof NEXT_DID_POSTPONE_HEADER\n    contentTypeHeader: typeof RSC_CONTENT_TYPE_HEADER\n    varyHeader: string\n    prefetchHeader: typeof NEXT_ROUTER_PREFETCH_HEADER\n    suffix: typeof RSC_SUFFIX\n    prefetchSuffix: typeof RSC_PREFETCH_SUFFIX\n    prefetchSegmentHeader: typeof NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n    prefetchSegmentDirSuffix: typeof RSC_SEGMENTS_DIR_SUFFIX\n    prefetchSegmentSuffix: typeof RSC_SEGMENT_SUFFIX\n  }\n  rewriteHeaders: {\n    pathHeader: typeof NEXT_REWRITTEN_PATH_HEADER\n    queryHeader: typeof NEXT_REWRITTEN_QUERY_HEADER\n  }\n  skipMiddlewareUrlNormalize?: boolean\n  caseSensitive?: boolean\n  /**\n   * Configuration related to Partial Prerendering.\n   */\n  ppr?: {\n    /**\n     * The chained response for the PPR resume.\n     */\n    chain: {\n      /**\n       * The headers that will indicate to Next.js that the request is for a PPR\n       * resume.\n       */\n      headers: Record<string, string>\n    }\n  }\n}\n\nfunction pageToRoute(page: string) {\n  const routeRegex = getNamedRouteRegex(page, {\n    prefixRouteKeys: true,\n  })\n  return {\n    page,\n    regex: normalizeRouteRegex(routeRegex.re.source),\n    routeKeys: routeRegex.routeKeys,\n    namedRegex: routeRegex.namedRegex,\n  }\n}\n\nfunction getCacheDir(distDir: string): string {\n  const cacheDir = path.join(distDir, 'cache')\n  if (ciEnvironment.isCI && !ciEnvironment.hasNextSupport) {\n    const hasCache = existsSync(cacheDir)\n\n    if (!hasCache) {\n      // Intentionally not piping to stderr which is what `Log.warn` does in case people fail in CI when\n      // stderr is detected.\n      console.log(\n        `${Log.prefixes.warn} No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache`\n      )\n    }\n  }\n  return cacheDir\n}\n\nasync function writeFileUtf8(filePath: string, content: string): Promise<void> {\n  await fs.writeFile(filePath, content, 'utf-8')\n}\n\nfunction readFileUtf8(filePath: string): Promise<string> {\n  return fs.readFile(filePath, 'utf8')\n}\n\nasync function writeManifest<T extends object>(\n  filePath: string,\n  manifest: T\n): Promise<void> {\n  await writeFileUtf8(filePath, formatManifest(manifest))\n}\n\nasync function readManifest<T extends object>(filePath: string): Promise<T> {\n  return JSON.parse(await readFileUtf8(filePath))\n}\n\nasync function writePrerenderManifest(\n  distDir: string,\n  manifest: DeepReadonly<PrerenderManifest>\n): Promise<void> {\n  await writeManifest(path.join(distDir, PRERENDER_MANIFEST), manifest)\n}\n\nasync function writeClientSsgManifest(\n  prerenderManifest: DeepReadonly<PrerenderManifest>,\n  {\n    buildId,\n    distDir,\n    locales,\n  }: {\n    buildId: string\n    distDir: string\n    locales: readonly string[] | undefined\n  }\n) {\n  const ssgPages = new Set<string>(\n    [\n      ...Object.entries(prerenderManifest.routes)\n        // Filter out dynamic routes\n        .filter(([, { srcRoute }]) => srcRoute == null)\n        .map(([route]) => normalizeLocalePath(route, locales).pathname),\n      ...Object.keys(prerenderManifest.dynamicRoutes),\n    ].sort()\n  )\n\n  const clientSsgManifestContent = `self.__SSG_MANIFEST=${devalue(\n    ssgPages\n  )};self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n  await writeFileUtf8(\n    path.join(distDir, CLIENT_STATIC_FILES_PATH, buildId, '_ssgManifest.js'),\n    clientSsgManifestContent\n  )\n}\n\nexport interface FunctionsConfigManifest {\n  version: number\n  functions: Record<\n    string,\n    {\n      maxDuration?: number | undefined\n      runtime?: 'nodejs'\n      matchers?: Array<{\n        regexp: string\n        originalSource: string\n        has?: Rewrite['has']\n        missing?: Rewrite['has']\n      }>\n    }\n  >\n}\n\nasync function writeFunctionsConfigManifest(\n  distDir: string,\n  manifest: FunctionsConfigManifest\n): Promise<void> {\n  await writeManifest(\n    path.join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n    manifest\n  )\n}\n\ninterface RequiredServerFilesManifest {\n  version: number\n  config: NextConfigComplete\n  appDir: string\n  relativeAppDir: string\n  files: string[]\n  ignore: string[]\n}\n\nasync function writeRequiredServerFilesManifest(\n  distDir: string,\n  requiredServerFiles: RequiredServerFilesManifest\n) {\n  await writeManifest(\n    path.join(distDir, SERVER_FILES_MANIFEST),\n    requiredServerFiles\n  )\n}\n\nasync function writeImagesManifest(\n  distDir: string,\n  config: NextConfigComplete\n): Promise<void> {\n  const images = { ...config.images }\n  const { deviceSizes, imageSizes } = images\n  ;(images as any).sizes = [...deviceSizes, ...imageSizes]\n\n  // By default, remotePatterns will allow no remote images ([])\n  images.remotePatterns = (config?.images?.remotePatterns || []).map((p) => ({\n    // Modifying the manifest should also modify matchRemotePattern()\n    protocol: p.protocol,\n    hostname: makeRe(p.hostname).source,\n    port: p.port,\n    pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n    search: p.search,\n  }))\n\n  // By default, localPatterns will allow all local images (undefined)\n  if (config?.images?.localPatterns) {\n    images.localPatterns = config.images.localPatterns.map((p) => ({\n      // Modifying the manifest should also modify matchLocalPattern()\n      pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n      search: p.search,\n    }))\n  }\n\n  await writeManifest(path.join(distDir, IMAGES_MANIFEST), {\n    version: 1,\n    images,\n  })\n}\n\nconst STANDALONE_DIRECTORY = 'standalone' as const\nasync function writeStandaloneDirectory(\n  nextBuildSpan: Span,\n  distDir: string,\n  pageKeys: { pages: string[]; app: string[] | undefined },\n  denormalizedAppPages: string[] | undefined,\n  outputFileTracingRoot: string,\n  requiredServerFiles: RequiredServerFilesManifest,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>,\n  loadedEnvFiles: LoadedEnvFiles,\n  appDir: string | undefined\n) {\n  await nextBuildSpan\n    .traceChild('write-standalone-directory')\n    .traceAsyncFn(async () => {\n      await copyTracedFiles(\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        requiredServerFiles.appDir,\n        distDir,\n        pageKeys.pages,\n        denormalizedAppPages,\n        outputFileTracingRoot,\n        requiredServerFiles.config,\n        middlewareManifest,\n        hasNodeMiddleware,\n        hasInstrumentationHook,\n        staticPages\n      )\n\n      for (const file of [\n        ...requiredServerFiles.files,\n        path.join(requiredServerFiles.config.distDir, SERVER_FILES_MANIFEST),\n        ...loadedEnvFiles.reduce<string[]>((acc, envFile) => {\n          if (['.env', '.env.production'].includes(envFile.path)) {\n            acc.push(envFile.path)\n          }\n          return acc\n        }, []),\n      ]) {\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        const filePath = path.join(requiredServerFiles.appDir, file)\n        const outputPath = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, filePath)\n        )\n        await fs.mkdir(path.dirname(outputPath), {\n          recursive: true,\n        })\n        await fs.copyFile(filePath, outputPath)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareOutput = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'middleware.js'\n        )\n\n        await fs.mkdir(path.dirname(middlewareOutput), { recursive: true })\n        await fs.copyFile(\n          path.join(distDir, SERVER_DIRECTORY, 'middleware.js'),\n          middlewareOutput\n        )\n      }\n\n      await recursiveCopy(\n        path.join(distDir, SERVER_DIRECTORY, 'pages'),\n        path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'pages'\n        ),\n        { overwrite: true }\n      )\n      if (appDir) {\n        const originalServerApp = path.join(distDir, SERVER_DIRECTORY, 'app')\n        if (existsSync(originalServerApp)) {\n          await recursiveCopy(\n            originalServerApp,\n            path.join(\n              distDir,\n              STANDALONE_DIRECTORY,\n              path.relative(outputFileTracingRoot, distDir),\n              SERVER_DIRECTORY,\n              'app'\n            ),\n            { overwrite: true }\n          )\n        }\n      }\n    })\n}\n\nfunction getNumberOfWorkers(config: NextConfigComplete) {\n  if (\n    config.experimental.cpus &&\n    config.experimental.cpus !== defaultConfig.experimental!.cpus\n  ) {\n    return config.experimental.cpus\n  }\n\n  if (config.experimental.memoryBasedWorkersCount) {\n    return Math.max(\n      Math.min(config.experimental.cpus || 1, Math.floor(os.freemem() / 1e9)),\n      // enforce a minimum of 4 workers\n      4\n    )\n  }\n\n  if (config.experimental.cpus) {\n    return config.experimental.cpus\n  }\n\n  // Fall back to 4 workers if a count is not specified\n  return 4\n}\n\nconst staticWorkerPath = require.resolve('./worker')\nconst staticWorkerExposedMethods = [\n  'hasCustomGetInitialProps',\n  'isPageStatic',\n  'getDefinedNamedExports',\n  'exportPages',\n] as const\ntype StaticWorker = typeof import('./worker') & Worker\nexport function createStaticWorker(\n  config: NextConfigComplete,\n  progress?: {\n    run: () => void\n    clear: () => void\n  }\n): StaticWorker {\n  // Get the node options without inspect and also remove the\n  // --max-old-space-size flag as it can cause memory issues.\n  const nodeOptions = getParsedNodeOptionsWithoutInspect()\n  delete nodeOptions['max-old-space-size']\n  delete nodeOptions['max_old_space_size']\n\n  return new Worker(staticWorkerPath, {\n    logger: Log,\n    numWorkers: getNumberOfWorkers(config),\n    onActivity: () => {\n      progress?.run()\n    },\n    onActivityAbort: () => {\n      progress?.clear()\n    },\n    forkOptions: {\n      env: { ...process.env, NODE_OPTIONS: formatNodeOptions(nodeOptions) },\n    },\n    enableWorkerThreads: config.experimental.workerThreads,\n    exposedMethods: staticWorkerExposedMethods,\n  }) as StaticWorker\n}\n\nasync function writeFullyStaticExport(\n  config: NextConfigComplete,\n  dir: string,\n  enabledDirectories: NextEnabledDirectories,\n  configOutDir: string,\n  nextBuildSpan: Span\n): Promise<void> {\n  const exportApp = require('../export')\n    .default as typeof import('../export').default\n\n  const pagesWorker = createStaticWorker(config)\n  const appWorker = createStaticWorker(config)\n\n  await exportApp(\n    dir,\n    {\n      buildExport: false,\n      nextConfig: config,\n      enabledDirectories,\n      silent: true,\n      outdir: path.join(dir, configOutDir),\n      numWorkers: getNumberOfWorkers(config),\n    },\n    nextBuildSpan\n  )\n\n  pagesWorker.end()\n  appWorker.end()\n}\n\nasync function getBuildId(\n  isGenerateMode: boolean,\n  distDir: string,\n  nextBuildSpan: Span,\n  config: NextConfigComplete\n) {\n  if (isGenerateMode) {\n    return await fs.readFile(path.join(distDir, 'BUILD_ID'), 'utf8')\n  }\n  return await nextBuildSpan\n    .traceChild('generate-buildid')\n    .traceAsyncFn(() => generateBuildId(config.generateBuildId, nanoid))\n}\n\nexport default async function build(\n  dir: string,\n  reactProductionProfiling = false,\n  debugOutput = false,\n  runLint = true,\n  noMangling = false,\n  appDirOnly = false,\n  turboNextBuild = false,\n  experimentalBuildMode: 'default' | 'compile' | 'generate',\n  traceUploadUrl: string | undefined\n): Promise<void> {\n  const isCompileMode = experimentalBuildMode === 'compile'\n  const isGenerateMode = experimentalBuildMode === 'generate'\n\n  let loadedConfig: NextConfigComplete | undefined\n  try {\n    const nextBuildSpan = trace('next-build', undefined, {\n      buildMode: experimentalBuildMode,\n      isTurboBuild: String(turboNextBuild),\n      version: process.env.__NEXT_VERSION as string,\n    })\n\n    NextBuildContext.nextBuildSpan = nextBuildSpan\n    NextBuildContext.dir = dir\n    NextBuildContext.appDirOnly = appDirOnly\n    NextBuildContext.reactProductionProfiling = reactProductionProfiling\n    NextBuildContext.noMangling = noMangling\n\n    await nextBuildSpan.traceAsyncFn(async () => {\n      // attempt to load global env values so they are available in next.config.js\n      const { loadedEnvFiles } = nextBuildSpan\n        .traceChild('load-dotenv')\n        .traceFn(() => loadEnvConfig(dir, false, Log))\n      NextBuildContext.loadedEnvFiles = loadedEnvFiles\n\n      const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n      const config: NextConfigComplete = await nextBuildSpan\n        .traceChild('load-next-config')\n        .traceAsyncFn(() =>\n          turborepoTraceAccess(\n            () =>\n              loadConfig(PHASE_PRODUCTION_BUILD, dir, {\n                // Log for next.config loading process\n                silent: false,\n                reactProductionProfiling,\n              }),\n            turborepoAccessTraceResult\n          )\n        )\n      loadedConfig = config\n\n      process.env.NEXT_DEPLOYMENT_ID = config.deploymentId || ''\n      NextBuildContext.config = config\n\n      let configOutDir = 'out'\n      if (hasCustomExportOutput(config)) {\n        configOutDir = config.distDir\n        config.distDir = '.next'\n      }\n      const distDir = path.join(dir, config.distDir)\n      NextBuildContext.distDir = distDir\n      setGlobal('phase', PHASE_PRODUCTION_BUILD)\n      setGlobal('distDir', distDir)\n\n      const buildId = await getBuildId(\n        isGenerateMode,\n        distDir,\n        nextBuildSpan,\n        config\n      )\n      NextBuildContext.buildId = buildId\n\n      const customRoutes: CustomRoutes = await nextBuildSpan\n        .traceChild('load-custom-routes')\n        .traceAsyncFn(() => loadCustomRoutes(config))\n\n      const { headers, rewrites, redirects } = customRoutes\n      const combinedRewrites: Rewrite[] = [\n        ...rewrites.beforeFiles,\n        ...rewrites.afterFiles,\n        ...rewrites.fallback,\n      ]\n      const hasRewrites = combinedRewrites.length > 0\n      NextBuildContext.hasRewrites = hasRewrites\n      NextBuildContext.originalRewrites = config._originalRewrites\n      NextBuildContext.originalRedirects = config._originalRedirects\n\n      const cacheDir = getCacheDir(distDir)\n\n      const telemetry = new Telemetry({ distDir })\n\n      setGlobal('telemetry', telemetry)\n\n      const publicDir = path.join(dir, 'public')\n      const { pagesDir, appDir } = findPagesDir(dir)\n      NextBuildContext.pagesDir = pagesDir\n      NextBuildContext.appDir = appDir\n\n      const enabledDirectories: NextEnabledDirectories = {\n        app: typeof appDir === 'string',\n        pages: typeof pagesDir === 'string',\n      }\n\n      // Generate a random encryption key for this build.\n      // This key is used to encrypt cross boundary values and can be used to generate hashes.\n      const encryptionKey = await generateEncryptionKeyBase64({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.encryptionKey = encryptionKey\n\n      const isSrcDir = path\n        .relative(dir, pagesDir || appDir || '')\n        .startsWith('src')\n      const hasPublicDir = existsSync(publicDir)\n\n      telemetry.record(\n        eventCliSession(dir, config, {\n          webpackVersion: 5,\n          cliCommand: 'build',\n          isSrcDir,\n          hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n          isCustomServer: null,\n          turboFlag: false,\n          pagesDir: !!pagesDir,\n          appDir: !!appDir,\n        })\n      )\n\n      eventNextPlugins(path.resolve(dir)).then((events) =>\n        telemetry.record(events)\n      )\n\n      eventSwcPlugins(path.resolve(dir), config).then((events) =>\n        telemetry.record(events)\n      )\n\n      // Always log next version first then start rest jobs\n      const { envInfo, experimentalFeatures } = await getStartServerInfo(\n        dir,\n        false\n      )\n      logStartInfo({\n        networkUrl: null,\n        appUrl: null,\n        envInfo,\n        experimentalFeatures,\n      })\n\n      const ignoreESLint = Boolean(config.eslint.ignoreDuringBuilds)\n      const shouldLint = !ignoreESLint && runLint\n\n      const typeCheckingOptions: Parameters<typeof startTypeChecking>[0] = {\n        dir,\n        appDir,\n        pagesDir,\n        runLint,\n        shouldLint,\n        ignoreESLint,\n        telemetry,\n        nextBuildSpan,\n        config,\n        cacheDir,\n      }\n\n      const distDirCreated = await nextBuildSpan\n        .traceChild('create-dist-dir')\n        .traceAsyncFn(async () => {\n          try {\n            await fs.mkdir(distDir, { recursive: true })\n            return true\n          } catch (err) {\n            if (isError(err) && err.code === 'EPERM') {\n              return false\n            }\n            throw err\n          }\n        })\n\n      if (!distDirCreated || !(await isWriteable(distDir))) {\n        throw new Error(\n          '> Build directory is not writeable. https://nextjs.org/docs/messages/build-dir-not-writeable'\n        )\n      }\n\n      if (config.cleanDistDir && !isGenerateMode) {\n        await recursiveDelete(distDir, /^cache/)\n      }\n\n      // For app directory, we run type checking after build. That's because\n      // we dynamically generate types for each layout and page in the app\n      // directory.\n      if (!appDir && !isCompileMode)\n        await startTypeChecking(typeCheckingOptions)\n\n      if (appDir && 'exportPathMap' in config) {\n        Log.error(\n          'The \"exportPathMap\" configuration cannot be used with the \"app\" directory. Please use generateStaticParams() instead.'\n        )\n        await telemetry.flush()\n        process.exit(1)\n      }\n\n      const buildLintEvent: EventBuildFeatureUsage = {\n        featureName: 'build-lint',\n        invocationCount: shouldLint ? 1 : 0,\n      }\n      telemetry.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: buildLintEvent,\n      })\n\n      const validFileMatcher = createValidFileMatcher(\n        config.pageExtensions,\n        appDir\n      )\n\n      const providedPagePaths: string[] = JSON.parse(\n        process.env.NEXT_PRIVATE_PAGE_PATHS || '[]'\n      )\n\n      let pagesPaths = Boolean(process.env.NEXT_PRIVATE_PAGE_PATHS)\n        ? providedPagePaths\n        : !appDirOnly && pagesDir\n          ? await nextBuildSpan.traceChild('collect-pages').traceAsyncFn(() =>\n              recursiveReadDir(pagesDir, {\n                pathnameFilter: validFileMatcher.isPageFile,\n              })\n            )\n          : []\n\n      const middlewareDetectionRegExp = new RegExp(\n        `^${MIDDLEWARE_FILENAME}\\\\.(?:${config.pageExtensions.join('|')})$`\n      )\n\n      const instrumentationHookDetectionRegExp = new RegExp(\n        `^${INSTRUMENTATION_HOOK_FILENAME}\\\\.(?:${config.pageExtensions.join(\n          '|'\n        )})$`\n      )\n\n      const rootDir = path.join((pagesDir || appDir)!, '..')\n      const includes = [\n        middlewareDetectionRegExp,\n        instrumentationHookDetectionRegExp,\n      ]\n\n      const rootPaths = Array.from(await getFilesInDir(rootDir))\n        .filter((file) => includes.some((include) => include.test(file)))\n        .sort(sortByPageExts(config.pageExtensions))\n        .map((file) => path.join(rootDir, file).replace(dir, ''))\n\n      const hasInstrumentationHook = rootPaths.some((p) =>\n        p.includes(INSTRUMENTATION_HOOK_FILENAME)\n      )\n      const hasMiddlewareFile = rootPaths.some((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n\n      NextBuildContext.hasInstrumentationHook = hasInstrumentationHook\n\n      const previewProps: __ApiPreviewProps = {\n        previewModeId: crypto.randomBytes(16).toString('hex'),\n        previewModeSigningKey: crypto.randomBytes(32).toString('hex'),\n        previewModeEncryptionKey: crypto.randomBytes(32).toString('hex'),\n      }\n      NextBuildContext.previewProps = previewProps\n\n      const mappedPages = await nextBuildSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: false,\n            pageExtensions: config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagesPaths,\n            pagesDir,\n            appDir,\n          })\n        )\n      NextBuildContext.mappedPages = mappedPages\n\n      let mappedAppPages: MappedPages | undefined\n      let denormalizedAppPages: string[] | undefined\n\n      if (appDir) {\n        const providedAppPaths: string[] = JSON.parse(\n          process.env.NEXT_PRIVATE_APP_PATHS || '[]'\n        )\n\n        let appPaths = Boolean(process.env.NEXT_PRIVATE_APP_PATHS)\n          ? providedAppPaths\n          : await nextBuildSpan\n              .traceChild('collect-app-paths')\n              .traceAsyncFn(() =>\n                recursiveReadDir(appDir, {\n                  pathnameFilter: (absolutePath) =>\n                    validFileMatcher.isAppRouterPage(absolutePath) ||\n                    // For now we only collect the root /not-found page in the app\n                    // directory as the 404 fallback\n                    validFileMatcher.isRootNotFound(absolutePath),\n                  ignorePartFilter: (part) => part.startsWith('_'),\n                })\n              )\n\n        mappedAppPages = await nextBuildSpan\n          .traceChild('create-app-mapping')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: appPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        NextBuildContext.mappedAppPages = mappedAppPages\n      }\n\n      const mappedRootPaths = await createPagesMapping({\n        isDev: false,\n        pageExtensions: config.pageExtensions,\n        pagePaths: rootPaths,\n        pagesType: PAGE_TYPES.ROOT,\n        pagesDir: pagesDir,\n        appDir,\n      })\n      NextBuildContext.mappedRootPaths = mappedRootPaths\n\n      const pagesPageKeys = Object.keys(mappedPages)\n\n      const conflictingAppPagePaths: [pagePath: string, appPath: string][] = []\n      const appPageKeys = new Set<string>()\n      if (mappedAppPages) {\n        denormalizedAppPages = Object.keys(mappedAppPages)\n        for (const appKey of denormalizedAppPages) {\n          const normalizedAppPageKey = normalizeAppPath(appKey)\n          const pagePath = mappedPages[normalizedAppPageKey]\n          if (pagePath) {\n            const appPath = mappedAppPages[appKey]\n            conflictingAppPagePaths.push([\n              pagePath.replace(/^private-next-pages/, 'pages'),\n              appPath.replace(/^private-next-app-dir/, 'app'),\n            ])\n          }\n          appPageKeys.add(normalizedAppPageKey)\n        }\n      }\n\n      const appPaths = Array.from(appPageKeys)\n      // Interception routes are modelled as beforeFiles rewrites\n      rewrites.beforeFiles.push(\n        ...generateInterceptionRoutesRewrites(appPaths, config.basePath)\n      )\n\n      NextBuildContext.rewrites = rewrites\n\n      const totalAppPagesCount = appPaths.length\n\n      const pageKeys = {\n        pages: pagesPageKeys,\n        app: appPaths.length > 0 ? appPaths : undefined,\n      }\n\n      // Turbopack already handles conflicting app and page routes.\n      if (!turboNextBuild) {\n        const numConflictingAppPaths = conflictingAppPagePaths.length\n        if (mappedAppPages && numConflictingAppPaths > 0) {\n          Log.error(\n            `Conflicting app and page file${\n              numConflictingAppPaths === 1 ? ' was' : 's were'\n            } found, please remove the conflicting files to continue:`\n          )\n          for (const [pagePath, appPath] of conflictingAppPagePaths) {\n            Log.error(`  \"${pagePath}\" - \"${appPath}\"`)\n          }\n          await telemetry.flush()\n          process.exit(1)\n        }\n      }\n\n      const conflictingPublicFiles: string[] = []\n      const hasPages404 = mappedPages['/404']?.startsWith(PAGES_DIR_ALIAS)\n      const hasApp404 = !!mappedAppPages?.[UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]\n      const hasCustomErrorPage =\n        mappedPages['/_error'].startsWith(PAGES_DIR_ALIAS)\n\n      if (hasPublicDir) {\n        const hasPublicUnderScoreNextDir = existsSync(\n          path.join(publicDir, '_next')\n        )\n        if (hasPublicUnderScoreNextDir) {\n          throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n        }\n      }\n\n      await nextBuildSpan\n        .traceChild('public-dir-conflict-check')\n        .traceAsyncFn(async () => {\n          // Check if pages conflict with files in `public`\n          // Only a page of public file can be served, not both.\n          for (const page in mappedPages) {\n            const hasPublicPageFile = await fileExists(\n              path.join(publicDir, page === '/' ? '/index' : page),\n              FileType.File\n            )\n            if (hasPublicPageFile) {\n              conflictingPublicFiles.push(page)\n            }\n          }\n\n          const numConflicting = conflictingPublicFiles.length\n\n          if (numConflicting) {\n            throw new Error(\n              `Conflicting public and page file${\n                numConflicting === 1 ? ' was' : 's were'\n              } found. https://nextjs.org/docs/messages/conflicting-public-file-page\\n${conflictingPublicFiles.join(\n                '\\n'\n              )}`\n            )\n          }\n        })\n\n      const nestedReservedPages = pageKeys.pages.filter((page) => {\n        return (\n          page.match(/\\/(_app|_document|_error)$/) && path.dirname(page) !== '/'\n        )\n      })\n\n      if (nestedReservedPages.length) {\n        Log.warn(\n          `The following reserved Next.js pages were detected not directly under the pages directory:\\n` +\n            nestedReservedPages.join('\\n') +\n            `\\nSee more info here: https://nextjs.org/docs/messages/nested-reserved-page\\n`\n        )\n      }\n\n      const restrictedRedirectPaths = ['/_next'].map((p) =>\n        config.basePath ? `${config.basePath}${p}` : p\n      )\n\n      const isAppDynamicIOEnabled = Boolean(config.experimental.dynamicIO)\n      const isAuthInterruptsEnabled = Boolean(\n        config.experimental.authInterrupts\n      )\n      const isAppPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n\n      const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n      const routesManifest: RoutesManifest = nextBuildSpan\n        .traceChild('generate-routes-manifest')\n        .traceFn(() => {\n          const sortedRoutes = getSortedRoutes([\n            ...pageKeys.pages,\n            ...(pageKeys.app ?? []),\n          ])\n          const dynamicRoutes: Array<ReturnType<typeof pageToRoute>> = []\n          const staticRoutes: typeof dynamicRoutes = []\n\n          for (const route of sortedRoutes) {\n            if (isDynamicRoute(route)) {\n              dynamicRoutes.push(pageToRoute(route))\n            } else if (!isReservedPage(route)) {\n              staticRoutes.push(pageToRoute(route))\n            }\n          }\n\n          return {\n            version: 3,\n            pages404: true,\n            caseSensitive: !!config.experimental.caseSensitiveRoutes,\n            basePath: config.basePath,\n            redirects: redirects.map((r) =>\n              buildCustomRoute('redirect', r, restrictedRedirectPaths)\n            ),\n            headers: headers.map((r) => buildCustomRoute('header', r)),\n            dynamicRoutes,\n            staticRoutes,\n            dataRoutes: [],\n            i18n: config.i18n || undefined,\n            rsc: {\n              header: RSC_HEADER,\n              // This vary header is used as a default. It is technically re-assigned in `base-server`,\n              // and may include an additional Vary option for `Next-URL`.\n              varyHeader: `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`,\n              prefetchHeader: NEXT_ROUTER_PREFETCH_HEADER,\n              didPostponeHeader: NEXT_DID_POSTPONE_HEADER,\n              contentTypeHeader: RSC_CONTENT_TYPE_HEADER,\n              suffix: RSC_SUFFIX,\n              prefetchSuffix: RSC_PREFETCH_SUFFIX,\n              prefetchSegmentHeader: NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n              prefetchSegmentSuffix: RSC_SEGMENT_SUFFIX,\n              prefetchSegmentDirSuffix: RSC_SEGMENTS_DIR_SUFFIX,\n            },\n            rewriteHeaders: {\n              pathHeader: NEXT_REWRITTEN_PATH_HEADER,\n              queryHeader: NEXT_REWRITTEN_QUERY_HEADER,\n            },\n            skipMiddlewareUrlNormalize: config.skipMiddlewareUrlNormalize,\n            ppr: isAppPPREnabled\n              ? {\n                  chain: {\n                    headers: {\n                      [NEXT_RESUME_HEADER]: '1',\n                    },\n                  },\n                }\n              : undefined,\n          } satisfies RoutesManifest\n        })\n\n      if (rewrites.beforeFiles.length === 0 && rewrites.fallback.length === 0) {\n        routesManifest.rewrites = rewrites.afterFiles.map((r) =>\n          buildCustomRoute('rewrite', r)\n        )\n      } else {\n        routesManifest.rewrites = {\n          beforeFiles: rewrites.beforeFiles.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n          afterFiles: rewrites.afterFiles.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n          fallback: rewrites.fallback.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n        }\n      }\n      let clientRouterFilters:\n        | undefined\n        | ReturnType<typeof createClientRouterFilter>\n\n      if (config.experimental.clientRouterFilter) {\n        const nonInternalRedirects = (config._originalRedirects || []).filter(\n          (r: any) => !r.internal\n        )\n        clientRouterFilters = createClientRouterFilter(\n          [...appPaths],\n          config.experimental.clientRouterFilterRedirects\n            ? nonInternalRedirects\n            : [],\n          config.experimental.clientRouterFilterAllowedRate\n        )\n        NextBuildContext.clientRouterFilters = clientRouterFilters\n      }\n\n      // Ensure commonjs handling is used for files in the distDir (generally .next)\n      // Files outside of the distDir can be \"type\": \"module\"\n      await writeFileUtf8(\n        path.join(distDir, 'package.json'),\n        '{\"type\": \"commonjs\"}'\n      )\n\n      // These are written to distDir, so they need to come after creating and cleaning distDr.\n      await recordFrameworkVersion(process.env.__NEXT_VERSION as string)\n      await updateBuildDiagnostics({\n        buildStage: 'start',\n      })\n\n      const outputFileTracingRoot = config.outputFileTracingRoot || dir\n\n      const pagesManifestPath = path.join(\n        distDir,\n        SERVER_DIRECTORY,\n        PAGES_MANIFEST\n      )\n\n      let buildTraceContext: undefined | BuildTraceContext\n      let buildTracesPromise: Promise<any> | undefined = undefined\n\n      // If there's has a custom webpack config and disable the build worker.\n      // Otherwise respect the option if it's set.\n      const useBuildWorker =\n        config.experimental.webpackBuildWorker ||\n        (config.experimental.webpackBuildWorker === undefined &&\n          !config.webpack)\n      const runServerAndEdgeInParallel =\n        config.experimental.parallelServerCompiles\n      const collectServerBuildTracesInParallel =\n        config.experimental.parallelServerBuildTraces ||\n        (config.experimental.parallelServerBuildTraces === undefined &&\n          isCompileMode)\n\n      nextBuildSpan.setAttribute(\n        'has-custom-webpack-config',\n        String(!!config.webpack)\n      )\n      nextBuildSpan.setAttribute('use-build-worker', String(useBuildWorker))\n\n      if (\n        !useBuildWorker &&\n        (runServerAndEdgeInParallel || collectServerBuildTracesInParallel)\n      ) {\n        throw new Error(\n          'The \"parallelServerBuildTraces\" and \"parallelServerCompiles\" options may only be used when build workers can be used. Read more: https://nextjs.org/docs/messages/parallel-build-without-worker'\n        )\n      }\n\n      Log.info('Creating an optimized production build ...')\n      traceMemoryUsage('Starting build', nextBuildSpan)\n\n      await updateBuildDiagnostics({\n        buildStage: 'compile',\n        buildOptions: {\n          useBuildWorker: String(useBuildWorker),\n        },\n      })\n\n      let shutdownPromise = Promise.resolve()\n      if (!isGenerateMode) {\n        if (turboNextBuild) {\n          const {\n            duration: compilerDuration,\n            shutdownPromise: p,\n            ...rest\n          } = await turbopackBuild(\n            process.env.NEXT_TURBOPACK_USE_WORKER === undefined ||\n              process.env.NEXT_TURBOPACK_USE_WORKER !== '0'\n          )\n          shutdownPromise = p\n          traceMemoryUsage('Finished build', nextBuildSpan)\n\n          buildTraceContext = rest.buildTraceContext\n\n          const durationString = durationToString(compilerDuration)\n          Log.event(`Compiled successfully in ${durationString}`)\n\n          telemetry.record(\n            eventBuildCompleted(pagesPaths, {\n              durationInSeconds: Math.round(compilerDuration),\n              totalAppPagesCount,\n            })\n          )\n        } else {\n          if (\n            runServerAndEdgeInParallel ||\n            collectServerBuildTracesInParallel\n          ) {\n            let durationInSeconds = 0\n\n            await updateBuildDiagnostics({\n              buildStage: 'compile-server',\n            })\n\n            const serverBuildPromise = webpackBuild(useBuildWorker, [\n              'server',\n            ]).then((res) => {\n              traceMemoryUsage('Finished server compilation', nextBuildSpan)\n              buildTraceContext = res.buildTraceContext\n              durationInSeconds += res.duration\n\n              if (collectServerBuildTracesInParallel) {\n                const buildTraceWorker = new Worker(\n                  require.resolve('./collect-build-traces'),\n                  {\n                    numWorkers: 1,\n                    exposedMethods: ['collectBuildTraces'],\n                  }\n                ) as Worker & typeof import('./collect-build-traces')\n\n                buildTracesPromise = buildTraceWorker\n                  .collectBuildTraces({\n                    dir,\n                    config,\n                    distDir,\n                    // Serialize Map as this is sent to the worker.\n                    edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(new Map()),\n                    staticPages: [],\n                    hasSsrAmpPages: false,\n                    buildTraceContext,\n                    outputFileTracingRoot,\n                  })\n                  .catch((err) => {\n                    console.error(err)\n                    process.exit(1)\n                  })\n              }\n            })\n            if (!runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n\n            const edgeBuildPromise = webpackBuild(useBuildWorker, [\n              'edge-server',\n            ]).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage(\n                'Finished edge-server compilation',\n                nextBuildSpan\n              )\n            })\n            if (runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n            await edgeBuildPromise\n\n            await updateBuildDiagnostics({\n              buildStage: 'webpack-compile-client',\n            })\n\n            await webpackBuild(useBuildWorker, ['client']).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage('Finished client compilation', nextBuildSpan)\n            })\n\n            const durationString = durationToString(durationInSeconds)\n            Log.event(`Compiled successfully in ${durationString}`)\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                durationInSeconds,\n                totalAppPagesCount,\n              })\n            )\n          } else {\n            const { duration: compilerDuration, ...rest } = await webpackBuild(\n              useBuildWorker,\n              null\n            )\n            traceMemoryUsage('Finished build', nextBuildSpan)\n\n            buildTraceContext = rest.buildTraceContext\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                durationInSeconds: compilerDuration,\n                totalAppPagesCount,\n              })\n            )\n          }\n        }\n      }\n\n      // For app directory, we run type checking after build.\n      if (appDir && !isCompileMode && !isGenerateMode) {\n        await updateBuildDiagnostics({\n          buildStage: 'type-checking',\n        })\n        await startTypeChecking(typeCheckingOptions)\n        traceMemoryUsage('Finished type checking', nextBuildSpan)\n      }\n\n      const postCompileSpinner = createSpinner('Collecting page data')\n\n      const buildManifestPath = path.join(distDir, BUILD_MANIFEST)\n      const appBuildManifestPath = path.join(distDir, APP_BUILD_MANIFEST)\n\n      let staticAppPagesCount = 0\n      let serverAppPagesCount = 0\n      let edgeRuntimeAppCount = 0\n      let edgeRuntimePagesCount = 0\n      const ssgPages = new Set<string>()\n      const ssgStaticFallbackPages = new Set<string>()\n      const ssgBlockingFallbackPages = new Set<string>()\n      const staticPages = new Set<string>()\n      const invalidPages = new Set<string>()\n      const hybridAmpPages = new Set<string>()\n      const serverPropsPages = new Set<string>()\n      const additionalPaths = new Map<string, PrerenderedRoute[]>()\n      const staticPaths = new Map<string, PrerenderedRoute[]>()\n      const prospectiveRenders = new Map<\n        string,\n        { page: string; originalAppPath: string }\n      >()\n      const appNormalizedPaths = new Map<string, string>()\n      const fallbackModes = new Map<string, FallbackMode>()\n      const appDefaultConfigs = new Map<string, AppSegmentConfig>()\n      const pageInfos: PageInfos = new Map<string, PageInfo>()\n      let pagesManifest = await readManifest<PagesManifest>(pagesManifestPath)\n      const buildManifest = await readManifest<BuildManifest>(buildManifestPath)\n      const appBuildManifest = appDir\n        ? await readManifest<AppBuildManifest>(appBuildManifestPath)\n        : undefined\n\n      const appPathRoutes: Record<string, string> = {}\n\n      if (appDir) {\n        const appPathsManifest = await readManifest<Record<string, string>>(\n          path.join(distDir, SERVER_DIRECTORY, APP_PATHS_MANIFEST)\n        )\n\n        for (const key in appPathsManifest) {\n          appPathRoutes[key] = normalizeAppPath(key)\n        }\n\n        await writeManifest(\n          path.join(distDir, APP_PATH_ROUTES_MANIFEST),\n          appPathRoutes\n        )\n      }\n\n      process.env.NEXT_PHASE = PHASE_PRODUCTION_BUILD\n\n      const worker = createStaticWorker(config)\n\n      const analysisBegin = process.hrtime()\n      const staticCheckSpan = nextBuildSpan.traceChild('static-check')\n\n      const functionsConfigManifest: FunctionsConfigManifest = {\n        version: 1,\n        functions: {},\n      }\n\n      const {\n        customAppGetInitialProps,\n        namedExports,\n        isNextImageImported,\n        hasSsrAmpPages,\n        hasNonStaticErrorPage,\n      } = await staticCheckSpan.traceAsyncFn(async () => {\n        if (isCompileMode) {\n          return {\n            customAppGetInitialProps: false,\n            namedExports: [],\n            isNextImageImported: true,\n            hasSsrAmpPages: !!pagesDir,\n            hasNonStaticErrorPage: true,\n          }\n        }\n\n        const { configFileName, publicRuntimeConfig, serverRuntimeConfig } =\n          config\n        const runtimeEnvConfig = { publicRuntimeConfig, serverRuntimeConfig }\n        const sriEnabled = Boolean(config.experimental.sri?.algorithm)\n\n        const nonStaticErrorPageSpan = staticCheckSpan.traceChild(\n          'check-static-error-page'\n        )\n        const errorPageHasCustomGetInitialProps =\n          nonStaticErrorPageSpan.traceAsyncFn(\n            async () =>\n              hasCustomErrorPage &&\n              (await worker.hasCustomGetInitialProps({\n                page: '/_error',\n                distDir,\n                runtimeEnvConfig,\n                checkingApp: false,\n                sriEnabled,\n              }))\n          )\n\n        const errorPageStaticResult = nonStaticErrorPageSpan.traceAsyncFn(\n          async () =>\n            hasCustomErrorPage &&\n            worker.isPageStatic({\n              dir,\n              page: '/_error',\n              distDir,\n              configFileName,\n              runtimeEnvConfig,\n              dynamicIO: isAppDynamicIOEnabled,\n              authInterrupts: isAuthInterruptsEnabled,\n              httpAgentOptions: config.httpAgentOptions,\n              locales: config.i18n?.locales,\n              defaultLocale: config.i18n?.defaultLocale,\n              nextConfigOutput: config.output,\n              pprConfig: config.experimental.ppr,\n              cacheLifeProfiles: config.experimental.cacheLife,\n              buildId,\n              sriEnabled,\n            })\n        )\n\n        const appPageToCheck = '/_app'\n\n        const customAppGetInitialPropsPromise = worker.hasCustomGetInitialProps(\n          {\n            page: appPageToCheck,\n            distDir,\n            runtimeEnvConfig,\n            checkingApp: true,\n            sriEnabled,\n          }\n        )\n\n        const namedExportsPromise = worker.getDefinedNamedExports({\n          page: appPageToCheck,\n          distDir,\n          runtimeEnvConfig,\n          sriEnabled,\n        })\n\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let isNextImageImported: boolean | undefined\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let hasSsrAmpPages = false\n\n        const computedManifestData = await computeFromManifest(\n          { build: buildManifest, app: appBuildManifest },\n          distDir,\n          config.experimental.gzipSize\n        )\n\n        const middlewareManifest: MiddlewareManifest = require(\n          path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n        )\n\n        const actionManifest = appDir\n          ? (require(\n              path.join(\n                distDir,\n                SERVER_DIRECTORY,\n                SERVER_REFERENCE_MANIFEST + '.json'\n              )\n            ) as ActionManifest)\n          : null\n        const entriesWithAction = actionManifest ? new Set() : null\n        if (actionManifest && entriesWithAction) {\n          for (const id in actionManifest.node) {\n            for (const entry in actionManifest.node[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n          for (const id in actionManifest.edge) {\n            for (const entry in actionManifest.edge[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n        }\n\n        for (const key of Object.keys(middlewareManifest?.functions)) {\n          if (key.startsWith('/api')) {\n            edgeRuntimePagesCount++\n          }\n        }\n\n        await Promise.all(\n          Object.entries(pageKeys)\n            .reduce<Array<{ pageType: keyof typeof pageKeys; page: string }>>(\n              (acc, [key, files]) => {\n                if (!files) {\n                  return acc\n                }\n\n                const pageType = key as keyof typeof pageKeys\n\n                for (const page of files) {\n                  acc.push({ pageType, page })\n                }\n\n                return acc\n              },\n              []\n            )\n            .map(({ pageType, page }) => {\n              const checkPageSpan = staticCheckSpan.traceChild('check-page', {\n                page,\n              })\n              return checkPageSpan.traceAsyncFn(async () => {\n                const actualPage = normalizePagePath(page)\n                const [size, totalSize] = await getJsPageSizeInKb(\n                  pageType,\n                  actualPage,\n                  distDir,\n                  buildManifest,\n                  appBuildManifest,\n                  config.experimental.gzipSize,\n                  computedManifestData\n                )\n\n                let isRoutePPREnabled = false\n                let isSSG = false\n                let isStatic = false\n                let isServerComponent = false\n                let isHybridAmp = false\n                let ssgPageRoutes: string[] | null = null\n                let pagePath = ''\n\n                if (pageType === 'pages') {\n                  pagePath =\n                    pagesPaths.find((p) => {\n                      p = normalizePathSep(p)\n                      return (\n                        p.startsWith(actualPage + '.') ||\n                        p.startsWith(actualPage + '/index.')\n                      )\n                    }) || ''\n                }\n                let originalAppPath: string | undefined\n\n                if (pageType === 'app' && mappedAppPages) {\n                  for (const [originalPath, normalizedPath] of Object.entries(\n                    appPathRoutes\n                  )) {\n                    if (normalizedPath === page) {\n                      pagePath = mappedAppPages[originalPath].replace(\n                        /^private-next-app-dir/,\n                        ''\n                      )\n                      originalAppPath = originalPath\n                      break\n                    }\n                  }\n                }\n\n                const pageFilePath = isAppBuiltinNotFoundPage(pagePath)\n                  ? require.resolve(\n                      'next/dist/client/components/not-found-error'\n                    )\n                  : path.join(\n                      (pageType === 'pages' ? pagesDir : appDir) || '',\n                      pagePath\n                    )\n\n                const isInsideAppDir = pageType === 'app'\n                const staticInfo = pagePath\n                  ? await getStaticInfoIncludingLayouts({\n                      isInsideAppDir,\n                      pageFilePath,\n                      pageExtensions: config.pageExtensions,\n                      appDir,\n                      config,\n                      isDev: false,\n                      // If this route is an App Router page route, inherit the\n                      // route segment configs (e.g. `runtime`) from the layout by\n                      // passing the `originalAppPath`, which should end with `/page`.\n                      page: isInsideAppDir ? originalAppPath! : page,\n                    })\n                  : undefined\n\n                // If there's any thing that would contribute to the functions\n                // configuration, we need to add it to the manifest.\n                if (\n                  typeof staticInfo?.runtime !== 'undefined' ||\n                  typeof staticInfo?.maxDuration !== 'undefined'\n                ) {\n                  functionsConfigManifest.functions[page] = {\n                    maxDuration: staticInfo?.maxDuration,\n                  }\n                }\n\n                const pageRuntime = middlewareManifest.functions[\n                  originalAppPath || page\n                ]\n                  ? 'edge'\n                  : staticInfo?.runtime\n\n                if (!isCompileMode) {\n                  isServerComponent =\n                    pageType === 'app' &&\n                    staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n                  if (pageType === 'app' || !isReservedPage(page)) {\n                    try {\n                      let edgeInfo: any\n\n                      if (isEdgeRuntime(pageRuntime)) {\n                        if (pageType === 'app') {\n                          edgeRuntimeAppCount++\n                        } else {\n                          edgeRuntimePagesCount++\n                        }\n\n                        const manifestKey =\n                          pageType === 'pages' ? page : originalAppPath || ''\n\n                        edgeInfo = middlewareManifest.functions[manifestKey]\n                      }\n\n                      let isPageStaticSpan =\n                        checkPageSpan.traceChild('is-page-static')\n                      let workerResult = await isPageStaticSpan.traceAsyncFn(\n                        () => {\n                          return worker.isPageStatic({\n                            dir,\n                            page,\n                            originalAppPath,\n                            distDir,\n                            configFileName,\n                            runtimeEnvConfig,\n                            httpAgentOptions: config.httpAgentOptions,\n                            locales: config.i18n?.locales,\n                            defaultLocale: config.i18n?.defaultLocale,\n                            parentId: isPageStaticSpan.getId(),\n                            pageRuntime,\n                            edgeInfo,\n                            pageType,\n                            dynamicIO: isAppDynamicIOEnabled,\n                            authInterrupts: isAuthInterruptsEnabled,\n                            cacheHandler: config.cacheHandler,\n                            cacheHandlers: config.experimental.cacheHandlers,\n                            isrFlushToDisk: ciEnvironment.hasNextSupport\n                              ? false\n                              : config.experimental.isrFlushToDisk,\n                            maxMemoryCacheSize: config.cacheMaxMemorySize,\n                            nextConfigOutput: config.output,\n                            pprConfig: config.experimental.ppr,\n                            cacheLifeProfiles: config.experimental.cacheLife,\n                            buildId,\n                            sriEnabled,\n                          })\n                        }\n                      )\n\n                      if (pageType === 'app' && originalAppPath) {\n                        appNormalizedPaths.set(originalAppPath, page)\n                        // TODO-APP: handle prerendering with edge\n                        if (isEdgeRuntime(pageRuntime)) {\n                          isStatic = false\n                          isSSG = false\n\n                          Log.warnOnce(\n                            `Using edge runtime on a page currently disables static generation for that page`\n                          )\n                        } else {\n                          const isDynamic = isDynamicRoute(page)\n\n                          if (\n                            typeof workerResult.isRoutePPREnabled === 'boolean'\n                          ) {\n                            isRoutePPREnabled = workerResult.isRoutePPREnabled\n                          }\n\n                          // If this route can be partially pre-rendered, then\n                          // mark it as such and mark that it can be\n                          // generated server-side.\n                          if (workerResult.isRoutePPREnabled) {\n                            isSSG = true\n                            isStatic = true\n\n                            staticPaths.set(originalAppPath, [])\n                          }\n                          // As PPR isn't enabled for this route, if dynamic IO\n                          // is enabled, and this is a dynamic route, we should\n                          // complete a prospective render for the route so that\n                          // we can use the fallback behavior. This lets us\n                          // check that dynamic pages won't error when they\n                          // enable PPR.\n                          else if (config.experimental.dynamicIO && isDynamic) {\n                            prospectiveRenders.set(originalAppPath, {\n                              page,\n                              originalAppPath,\n                            })\n                          }\n\n                          if (workerResult.prerenderedRoutes) {\n                            staticPaths.set(\n                              originalAppPath,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                            isSSG = true\n                          }\n\n                          const appConfig = workerResult.appConfig || {}\n                          if (appConfig.revalidate !== 0) {\n                            const hasGenerateStaticParams =\n                              workerResult.prerenderedRoutes &&\n                              workerResult.prerenderedRoutes.length > 0\n\n                            if (\n                              config.output === 'export' &&\n                              isDynamic &&\n                              !hasGenerateStaticParams\n                            ) {\n                              throw new Error(\n                                `Page \"${page}\" is missing \"generateStaticParams()\" so it cannot be used with \"output: export\" config.`\n                              )\n                            }\n\n                            // Mark the app as static if:\n                            // - It has no dynamic param\n                            // - It doesn't have generateStaticParams but `dynamic` is set to\n                            //   `error` or `force-static`\n                            if (!isDynamic) {\n                              staticPaths.set(originalAppPath, [\n                                {\n                                  pathname: page,\n                                  encodedPathname: page,\n                                  fallbackRouteParams: undefined,\n                                  fallbackMode:\n                                    workerResult.prerenderFallbackMode,\n                                  fallbackRootParams: undefined,\n                                },\n                              ])\n                              isStatic = true\n                            } else if (\n                              !hasGenerateStaticParams &&\n                              (appConfig.dynamic === 'error' ||\n                                appConfig.dynamic === 'force-static')\n                            ) {\n                              staticPaths.set(originalAppPath, [])\n                              isStatic = true\n                              isRoutePPREnabled = false\n                            }\n                          }\n\n                          if (workerResult.prerenderFallbackMode) {\n                            fallbackModes.set(\n                              originalAppPath,\n                              workerResult.prerenderFallbackMode\n                            )\n                          }\n\n                          appDefaultConfigs.set(originalAppPath, appConfig)\n                        }\n                      } else {\n                        if (isEdgeRuntime(pageRuntime)) {\n                          if (workerResult.hasStaticProps) {\n                            console.warn(\n                              `\"getStaticProps\" is not yet supported fully with \"experimental-edge\", detected on ${page}`\n                            )\n                          }\n                          // TODO: add handling for statically rendering edge\n                          // pages and allow edge with Prerender outputs\n                          workerResult.isStatic = false\n                          workerResult.hasStaticProps = false\n                        }\n\n                        if (\n                          workerResult.isStatic === false &&\n                          (workerResult.isHybridAmp || workerResult.isAmpOnly)\n                        ) {\n                          hasSsrAmpPages = true\n                        }\n\n                        if (workerResult.isHybridAmp) {\n                          isHybridAmp = true\n                          hybridAmpPages.add(page)\n                        }\n\n                        if (workerResult.isNextImageImported) {\n                          isNextImageImported = true\n                        }\n\n                        if (workerResult.hasStaticProps) {\n                          ssgPages.add(page)\n                          isSSG = true\n\n                          if (\n                            workerResult.prerenderedRoutes &&\n                            workerResult.prerenderedRoutes.length > 0\n                          ) {\n                            additionalPaths.set(\n                              page,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                          }\n\n                          if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.BLOCKING_STATIC_RENDER\n                          ) {\n                            ssgBlockingFallbackPages.add(page)\n                          } else if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.PRERENDER\n                          ) {\n                            ssgStaticFallbackPages.add(page)\n                          }\n                        } else if (workerResult.hasServerProps) {\n                          serverPropsPages.add(page)\n                        } else if (\n                          workerResult.isStatic &&\n                          !isServerComponent &&\n                          (await customAppGetInitialPropsPromise) === false\n                        ) {\n                          staticPages.add(page)\n                          isStatic = true\n                        } else if (isServerComponent) {\n                          // This is a static server component page that doesn't have\n                          // gSP or gSSP. We still treat it as a SSG page.\n                          ssgPages.add(page)\n                          isSSG = true\n                        }\n\n                        if (hasPages404 && page === '/404') {\n                          if (\n                            !workerResult.isStatic &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            throw new Error(\n                              `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                            )\n                          }\n                          // we need to ensure the 404 lambda is present since we use\n                          // it when _app has getInitialProps\n                          if (\n                            (await customAppGetInitialPropsPromise) &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            staticPages.delete(page)\n                          }\n                        }\n\n                        if (\n                          STATIC_STATUS_PAGES.includes(page) &&\n                          !workerResult.isStatic &&\n                          !workerResult.hasStaticProps\n                        ) {\n                          throw new Error(\n                            `\\`pages${page}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                          )\n                        }\n                      }\n                    } catch (err) {\n                      if (\n                        !isError(err) ||\n                        err.message !== 'INVALID_DEFAULT_EXPORT'\n                      )\n                        throw err\n                      invalidPages.add(page)\n                    }\n                  }\n\n                  if (pageType === 'app') {\n                    if (isSSG || isStatic) {\n                      staticAppPagesCount++\n                    } else {\n                      serverAppPagesCount++\n                    }\n                  }\n                }\n\n                pageInfos.set(page, {\n                  size,\n                  totalSize,\n                  isStatic,\n                  isSSG,\n                  isRoutePPREnabled,\n                  isHybridAmp,\n                  ssgPageRoutes,\n                  initialCacheControl: undefined,\n                  runtime: pageRuntime,\n                  pageDuration: undefined,\n                  ssgPageDurations: undefined,\n                  hasEmptyPrelude: undefined,\n                })\n              })\n            })\n        )\n\n        if (hadUnsupportedValue) {\n          Log.error(\n            `Invalid config value exports detected, these can cause unexpected behavior from the configs not being applied. Please fix them to continue`\n          )\n          process.exit(1)\n        }\n\n        const errorPageResult = await errorPageStaticResult\n        const nonStaticErrorPage =\n          (await errorPageHasCustomGetInitialProps) ||\n          (errorPageResult && errorPageResult.hasServerProps)\n\n        const returnValue = {\n          customAppGetInitialProps: await customAppGetInitialPropsPromise,\n          namedExports: await namedExportsPromise,\n          isNextImageImported,\n          hasSsrAmpPages,\n          hasNonStaticErrorPage: nonStaticErrorPage,\n        }\n\n        return returnValue\n      })\n\n      if (postCompileSpinner) postCompileSpinner.stopAndPersist()\n      traceMemoryUsage('Finished collecting page data', nextBuildSpan)\n\n      if (customAppGetInitialProps) {\n        console.warn(\n          bold(yellow(`Warning: `)) +\n            yellow(\n              `You have opted-out of Automatic Static Optimization due to \\`getInitialProps\\` in \\`pages/_app\\`. This does not opt-out pages with \\`getStaticProps\\``\n            )\n        )\n        console.warn(\n          'Read more: https://nextjs.org/docs/messages/opt-out-auto-static-optimization\\n'\n        )\n      }\n\n      const { cacheHandler } = config\n\n      const instrumentationHookEntryFiles: string[] = []\n      if (hasInstrumentationHook) {\n        instrumentationHookEntryFiles.push(\n          path.join(SERVER_DIRECTORY, `${INSTRUMENTATION_HOOK_FILENAME}.js`)\n        )\n        // If there's edge routes, append the edge instrumentation hook\n        // Turbopack generates this chunk with a hashed name and references it in middleware-manifest.\n        if (\n          !process.env.TURBOPACK &&\n          (edgeRuntimeAppCount || edgeRuntimePagesCount)\n        ) {\n          instrumentationHookEntryFiles.push(\n            path.join(\n              SERVER_DIRECTORY,\n              `edge-${INSTRUMENTATION_HOOK_FILENAME}.js`\n            )\n          )\n        }\n      }\n\n      const requiredServerFilesManifest = nextBuildSpan\n        .traceChild('generate-required-server-files')\n        .traceFn(() => {\n          const normalizedCacheHandlers: Record<string, string> = {}\n\n          for (const [key, value] of Object.entries(\n            config.experimental.cacheHandlers || {}\n          )) {\n            if (key && value) {\n              normalizedCacheHandlers[key] = path.relative(distDir, value)\n            }\n          }\n\n          const serverFilesManifest: RequiredServerFilesManifest = {\n            version: 1,\n            config: {\n              ...config,\n              configFile: undefined,\n              ...(ciEnvironment.hasNextSupport\n                ? {\n                    compress: false,\n                  }\n                : {}),\n              cacheHandler: cacheHandler\n                ? path.relative(distDir, cacheHandler)\n                : config.cacheHandler,\n              experimental: {\n                ...config.experimental,\n                cacheHandlers: normalizedCacheHandlers,\n                trustHostHeader: ciEnvironment.hasNextSupport,\n\n                // @ts-expect-error internal field TODO: fix this, should use a separate mechanism to pass the info.\n                isExperimentalCompile: isCompileMode,\n              },\n            },\n            appDir: dir,\n            relativeAppDir: path.relative(outputFileTracingRoot, dir),\n            files: [\n              ROUTES_MANIFEST,\n              path.relative(distDir, pagesManifestPath),\n              BUILD_MANIFEST,\n              PRERENDER_MANIFEST,\n              path.join(SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_BUILD_MANIFEST + '.js'),\n              ...(!process.env.TURBOPACK\n                ? [\n                    path.join(\n                      SERVER_DIRECTORY,\n                      MIDDLEWARE_REACT_LOADABLE_MANIFEST + '.js'\n                    ),\n                    REACT_LOADABLE_MANIFEST,\n                  ]\n                : []),\n              ...(appDir\n                ? [\n                    ...(config.experimental.sri\n                      ? [\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.js'\n                          ),\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.json'\n                          ),\n                        ]\n                      : []),\n                    path.join(SERVER_DIRECTORY, APP_PATHS_MANIFEST),\n                    path.join(APP_PATH_ROUTES_MANIFEST),\n                    APP_BUILD_MANIFEST,\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.js'\n                    ),\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.json'\n                    ),\n                  ]\n                : []),\n              ...(pagesDir && !turboNextBuild\n                ? [\n                    DYNAMIC_CSS_MANIFEST + '.json',\n                    path.join(SERVER_DIRECTORY, DYNAMIC_CSS_MANIFEST + '.js'),\n                  ]\n                : []),\n              BUILD_ID_FILE,\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.js'),\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.json'),\n              ...instrumentationHookEntryFiles,\n            ]\n              .filter(nonNullable)\n              .map((file) => path.join(config.distDir, file)),\n            ignore: [] as string[],\n          }\n\n          return serverFilesManifest\n        })\n\n      if (!hasSsrAmpPages) {\n        requiredServerFilesManifest.ignore.push(\n          path.relative(\n            dir,\n            path.join(\n              path.dirname(\n                require.resolve(\n                  'next/dist/compiled/@ampproject/toolbox-optimizer'\n                )\n              ),\n              '**/*'\n            )\n          )\n        )\n      }\n\n      const middlewareFile = rootPaths.find((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n      let hasNodeMiddleware = false\n\n      if (middlewareFile) {\n        const staticInfo = await getStaticInfoIncludingLayouts({\n          isInsideAppDir: false,\n          pageFilePath: path.join(dir, middlewareFile),\n          config,\n          appDir,\n          pageExtensions: config.pageExtensions,\n          isDev: false,\n          page: 'middleware',\n        })\n\n        if (staticInfo.runtime === 'nodejs') {\n          hasNodeMiddleware = true\n          functionsConfigManifest.functions['/_middleware'] = {\n            runtime: staticInfo.runtime,\n            matchers: staticInfo.middleware?.matchers ?? [\n              {\n                regexp: '^.*$',\n                originalSource: '/:path*',\n              },\n            ],\n          }\n\n          if (turboNextBuild) {\n            await writeManifest(\n              path.join(\n                distDir,\n                'static',\n                buildId,\n                TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST\n              ),\n              functionsConfigManifest.functions['/_middleware'].matchers || []\n            )\n          }\n        }\n      }\n\n      await writeFunctionsConfigManifest(distDir, functionsConfigManifest)\n\n      if (!isGenerateMode && !buildTracesPromise) {\n        buildTracesPromise = collectBuildTraces({\n          dir,\n          config,\n          distDir,\n          edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(pageInfos),\n          staticPages: [...staticPages],\n          nextBuildSpan,\n          hasSsrAmpPages,\n          buildTraceContext,\n          outputFileTracingRoot,\n        }).catch((err) => {\n          console.error(err)\n          process.exit(1)\n        })\n      }\n\n      if (serverPropsPages.size > 0 || ssgPages.size > 0) {\n        // We update the routes manifest after the build with the\n        // data routes since we can't determine these until after build\n        routesManifest.dataRoutes = getSortedRoutes([\n          ...serverPropsPages,\n          ...ssgPages,\n        ]).map((page) => {\n          return buildDataRoute(page, buildId)\n        })\n      }\n\n      // We need to write the manifest with rewrites before build\n      await nextBuildSpan\n        .traceChild('write-routes-manifest')\n        .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n\n      // Since custom _app.js can wrap the 404 page we have to opt-out of static optimization if it has getInitialProps\n      // Only export the static 404 when there is no /_error present\n      const useStaticPages404 =\n        !customAppGetInitialProps && (!hasNonStaticErrorPage || hasPages404)\n\n      if (invalidPages.size > 0) {\n        const err = new Error(\n          `Build optimization failed: found page${\n            invalidPages.size === 1 ? '' : 's'\n          } without a React Component as default export in \\n${[...invalidPages]\n            .map((pg) => `pages${pg}`)\n            .join(\n              '\\n'\n            )}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.\\n`\n        ) as NextError\n        err.code = 'BUILD_OPTIMIZATION_FAILED'\n        throw err\n      }\n\n      await writeBuildId(distDir, buildId)\n\n      if (config.experimental.optimizeCss) {\n        const globOrig =\n          require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n\n        const cssFilePaths = await new Promise<string[]>((resolve, reject) => {\n          globOrig(\n            '**/*.css',\n            { cwd: path.join(distDir, 'static') },\n            (err, files) => {\n              if (err) {\n                return reject(err)\n              }\n              resolve(files)\n            }\n          )\n        })\n\n        requiredServerFilesManifest.files.push(\n          ...cssFilePaths.map((filePath) =>\n            path.join(config.distDir, 'static', filePath)\n          )\n        )\n      }\n\n      const features: EventBuildFeatureUsage[] = [\n        {\n          featureName: 'experimental/dynamicIO',\n          invocationCount: config.experimental.dynamicIO ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/optimizeCss',\n          invocationCount: config.experimental.optimizeCss ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/nextScriptWorkers',\n          invocationCount: config.experimental.nextScriptWorkers ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/ppr',\n          invocationCount: config.experimental.ppr ? 1 : 0,\n        },\n      ]\n      telemetry.record(\n        features.map((feature) => {\n          return {\n            eventName: EVENT_BUILD_FEATURE_USAGE,\n            payload: feature,\n          }\n        })\n      )\n\n      await writeRequiredServerFilesManifest(\n        distDir,\n        requiredServerFilesManifest\n      )\n\n      const middlewareManifest: MiddlewareManifest = await readManifest(\n        path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      )\n\n      const prerenderManifest: PrerenderManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: previewProps,\n      }\n\n      const tbdPrerenderRoutes: string[] = []\n\n      const { i18n } = config\n\n      const usedStaticStatusPages = STATIC_STATUS_PAGES.filter(\n        (page) =>\n          mappedPages[page] &&\n          mappedPages[page].startsWith('private-next-pages')\n      )\n      usedStaticStatusPages.forEach((page) => {\n        if (!ssgPages.has(page) && !customAppGetInitialProps) {\n          staticPages.add(page)\n        }\n      })\n\n      const hasPages500 = usedStaticStatusPages.includes('/500')\n      const useDefaultStatic500 =\n        !hasPages500 && !hasNonStaticErrorPage && !customAppGetInitialProps\n\n      const combinedPages = [...staticPages, ...ssgPages]\n      const isApp404Static = staticPaths.has(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      const hasStaticApp404 = hasApp404 && isApp404Static\n\n      await updateBuildDiagnostics({\n        buildStage: 'static-generation',\n      })\n\n      // we need to trigger automatic exporting when we have\n      // - static 404/500\n      // - getStaticProps paths\n      // - experimental app is enabled\n      if (\n        !isCompileMode &&\n        (combinedPages.length > 0 ||\n          useStaticPages404 ||\n          useDefaultStatic500 ||\n          appDir)\n      ) {\n        const staticGenerationSpan =\n          nextBuildSpan.traceChild('static-generation')\n        await staticGenerationSpan.traceAsyncFn(async () => {\n          detectConflictingPaths(\n            [\n              ...combinedPages,\n              ...pageKeys.pages.filter((page) => !combinedPages.includes(page)),\n            ],\n            ssgPages,\n            new Map(\n              Array.from(additionalPaths.entries()).map(\n                ([page, routes]): [string, string[]] => {\n                  return [page, routes.map((route) => route.pathname)]\n                }\n              )\n            )\n          )\n          const exportApp = require('../export')\n            .default as typeof import('../export').default\n\n          const exportConfig: NextConfigComplete = {\n            ...config,\n            // Default map will be the collection of automatic statically exported\n            // pages and incremental pages.\n            // n.b. we cannot handle this above in combinedPages because the dynamic\n            // page must be in the `pages` array, but not in the mapping.\n            exportPathMap: (defaultMap: ExportPathMap) => {\n              // Dynamically routed pages should be prerendered to be used as\n              // a client-side skeleton (fallback) while data is being fetched.\n              // This ensures the end-user never sees a 500 or slow response from the\n              // server.\n              //\n              // Note: prerendering disables automatic static optimization.\n              ssgPages.forEach((page) => {\n                if (isDynamicRoute(page)) {\n                  tbdPrerenderRoutes.push(page)\n\n                  if (ssgStaticFallbackPages.has(page)) {\n                    // Override the rendering for the dynamic page to be treated as a\n                    // fallback render.\n                    if (i18n) {\n                      defaultMap[`/${i18n.defaultLocale}${page}`] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    } else {\n                      defaultMap[page] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    }\n                  } else {\n                    // Remove dynamically routed pages from the default path map when\n                    // fallback behavior is disabled.\n                    delete defaultMap[page]\n                  }\n                }\n              })\n\n              // Append the \"well-known\" routes we should prerender for, e.g. blog\n              // post slugs.\n              additionalPaths.forEach((routes, page) => {\n                routes.forEach((route) => {\n                  defaultMap[route.pathname] = {\n                    page,\n                    _ssgPath: route.encodedPathname,\n                  }\n                })\n              })\n\n              if (useStaticPages404) {\n                defaultMap['/404'] = {\n                  page: hasPages404 ? '/404' : '/_error',\n                }\n              }\n\n              if (useDefaultStatic500) {\n                defaultMap['/500'] = {\n                  page: '/_error',\n                }\n              }\n\n              // TODO: output manifest specific to app paths and their\n              // revalidate periods and dynamicParams settings\n              staticPaths.forEach((routes, originalAppPath) => {\n                const appConfig = appDefaultConfigs.get(originalAppPath)\n                const isDynamicError = appConfig?.dynamic === 'error'\n\n                const isRoutePPREnabled = appConfig\n                  ? checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                  : undefined\n\n                routes.forEach((route) => {\n                  // If the route has any dynamic root segments, we need to skip\n                  // rendering the route. This is because we don't support\n                  // revalidating the shells without the parameters present.\n                  if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    return\n                  }\n\n                  defaultMap[route.pathname] = {\n                    page: originalAppPath,\n                    _ssgPath: route.encodedPathname,\n                    _fallbackRouteParams: route.fallbackRouteParams,\n                    _isDynamicError: isDynamicError,\n                    _isAppDir: true,\n                    _isRoutePPREnabled: isRoutePPREnabled,\n                  }\n                })\n              })\n\n              // If the app does have dynamic IO enabled but does not have PPR\n              // enabled, then we need to perform a prospective render for all\n              // the dynamic pages to ensure that they won't error during\n              // rendering (due to a missing prelude).\n              for (const {\n                page,\n                originalAppPath,\n              } of prospectiveRenders.values()) {\n                defaultMap[page] = {\n                  page: originalAppPath,\n                  _ssgPath: page,\n                  _fallbackRouteParams: getParamKeys(page),\n                  // Prospective renders are only enabled for app pages.\n                  _isAppDir: true,\n                  // Prospective renders are only enabled when PPR is disabled.\n                  _isRoutePPREnabled: false,\n                  _isProspectiveRender: true,\n                  // Dynamic IO does not currently support `dynamic === 'error'`.\n                  _isDynamicError: false,\n                }\n              }\n\n              if (i18n) {\n                for (const page of [\n                  ...staticPages,\n                  ...ssgPages,\n                  ...(useStaticPages404 ? ['/404'] : []),\n                  ...(useDefaultStatic500 ? ['/500'] : []),\n                ]) {\n                  const isSsg = ssgPages.has(page)\n                  const isDynamic = isDynamicRoute(page)\n                  const isFallback = isSsg && ssgStaticFallbackPages.has(page)\n\n                  for (const locale of i18n.locales) {\n                    // skip fallback generation for SSG pages without fallback mode\n                    if (isSsg && isDynamic && !isFallback) continue\n                    const outputPath = `/${locale}${page === '/' ? '' : page}`\n\n                    defaultMap[outputPath] = {\n                      page: defaultMap[page]?.page || page,\n                      _locale: locale,\n                      _pagesFallback: isFallback,\n                    }\n                  }\n\n                  if (isSsg) {\n                    // remove non-locale prefixed variant from defaultMap\n                    delete defaultMap[page]\n                  }\n                }\n              }\n\n              return defaultMap\n            },\n          }\n\n          const outdir = path.join(distDir, 'export')\n          const exportResult = await exportApp(\n            dir,\n            {\n              nextConfig: exportConfig,\n              enabledDirectories,\n              silent: true,\n              buildExport: true,\n              debugOutput,\n              pages: combinedPages,\n              outdir,\n              statusMessage: 'Generating static pages',\n              numWorkers: getNumberOfWorkers(exportConfig),\n            },\n            nextBuildSpan\n          )\n\n          // If there was no result, there's nothing more to do.\n          if (!exportResult) return\n\n          const getCacheControl = (\n            exportPath: string,\n            defaultRevalidate: Revalidate = false\n          ): CacheControl => {\n            const cacheControl =\n              exportResult.byPath.get(exportPath)?.cacheControl\n\n            if (!cacheControl) {\n              return { revalidate: defaultRevalidate, expire: undefined }\n            }\n\n            if (\n              cacheControl.revalidate !== false &&\n              cacheControl.revalidate > 0 &&\n              cacheControl.expire === undefined\n            ) {\n              return {\n                revalidate: cacheControl.revalidate,\n                expire: config.expireTime,\n              }\n            }\n\n            return cacheControl\n          }\n\n          if (debugOutput || process.env.NEXT_SSG_FETCH_METRICS === '1') {\n            recordFetchMetrics(exportResult)\n          }\n\n          writeTurborepoAccessTraceResult({\n            distDir: config.distDir,\n            traces: [\n              turborepoAccessTraceResult,\n              ...exportResult.turborepoAccessTraceResults.values(),\n            ],\n          })\n\n          prerenderManifest.notFoundRoutes = Array.from(\n            exportResult.ssgNotFoundPaths\n          )\n\n          // remove server bundles that were exported\n          for (const page of staticPages) {\n            const serverBundle = getPagePath(page, distDir, undefined, false)\n            await fs.unlink(serverBundle)\n          }\n\n          staticPaths.forEach((prerenderedRoutes, originalAppPath) => {\n            const page = appNormalizedPaths.get(originalAppPath)\n            if (!page) throw new InvariantError('Page not found')\n\n            const appConfig = appDefaultConfigs.get(originalAppPath)\n            if (!appConfig) throw new InvariantError('App config not found')\n\n            let hasRevalidateZero =\n              appConfig.revalidate === 0 ||\n              getCacheControl(page).revalidate === 0\n\n            if (hasRevalidateZero && pageInfos.get(page)?.isStatic) {\n              // if the page was marked as being static, but it contains dynamic data\n              // (ie, in the case of a static generation bailout), then it should be marked dynamic\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                isStatic: false,\n                isSSG: false,\n              })\n            }\n\n            const isAppRouteHandler = isAppRouteRoute(originalAppPath)\n\n            // When this is an app page and PPR is enabled, the route supports\n            // partial pre-rendering.\n            const isRoutePPREnabled: true | undefined =\n              !isAppRouteHandler &&\n              checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                ? true\n                : undefined\n\n            const htmlBotsRegexString =\n              // The htmlLimitedBots has been converted to a string during loadConfig\n              config.htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING\n\n            // this flag is used to selectively bypass the static cache and invoke the lambda directly\n            // to enable server actions on static routes\n            const bypassFor: RouteHas[] = [\n              { type: 'header', key: ACTION_HEADER },\n              {\n                type: 'header',\n                key: 'content-type',\n                value: 'multipart/form-data;.*',\n              },\n              // If it's PPR rendered non-static page, bypass the PPR cache when streaming metadata is enabled.\n              // This will skip the postpone data for those bots requests and instead produce a dynamic render.\n              ...(isRoutePPREnabled\n                ? [\n                    {\n                      type: 'header',\n                      key: 'user-agent',\n                      value: htmlBotsRegexString,\n                    },\n                  ]\n                : []),\n            ]\n\n            // We should collect all the dynamic routes into a single array for\n            // this page. Including the full fallback route (the original\n            // route), any routes that were generated with unknown route params\n            // should be collected and included in the dynamic routes part\n            // of the manifest instead.\n            const routes: PrerenderedRoute[] = []\n            const dynamicRoutes: PrerenderedRoute[] = []\n\n            // Sort the outputted routes to ensure consistent output. Any route\n            // though that has unknown route params will be pulled and sorted\n            // independently. This is because the routes with unknown route\n            // params will contain the dynamic path parameters, some of which\n            // may conflict with the actual prerendered routes.\n            let unknownPrerenderRoutes: PrerenderedRoute[] = []\n            let knownPrerenderRoutes: PrerenderedRoute[] = []\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                unknownPrerenderRoutes.push(prerenderedRoute)\n              } else {\n                knownPrerenderRoutes.push(prerenderedRoute)\n              }\n            }\n\n            unknownPrerenderRoutes = getSortedRouteObjects(\n              unknownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n            knownPrerenderRoutes = getSortedRouteObjects(\n              knownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n\n            prerenderedRoutes = [\n              ...knownPrerenderRoutes,\n              ...unknownPrerenderRoutes,\n            ]\n\n            for (const prerenderedRoute of prerenderedRoutes) {\n              // TODO: check if still needed?\n              // Exclude the /_not-found route.\n              if (prerenderedRoute.pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n                continue\n              }\n\n              if (\n                isRoutePPREnabled &&\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                // If the route has unknown params, then we need to add it to\n                // the list of dynamic routes.\n                dynamicRoutes.push(prerenderedRoute)\n              } else {\n                // If the route doesn't have unknown params, then we need to\n                // add it to the list of routes.\n                routes.push(prerenderedRoute)\n              }\n            }\n\n            // Handle all the static routes.\n            for (const route of routes) {\n              if (isDynamicRoute(page) && route.pathname === page) continue\n              if (route.pathname === UNDERSCORE_NOT_FOUND_ROUTE) continue\n\n              const {\n                metadata = {},\n                hasEmptyPrelude,\n                hasPostponed,\n              } = exportResult.byPath.get(route.pathname) ?? {}\n\n              const cacheControl = getCacheControl(\n                route.pathname,\n                appConfig.revalidate\n              )\n\n              pageInfos.set(route.pathname, {\n                ...(pageInfos.get(route.pathname) as PageInfo),\n                hasPostponed,\n                hasEmptyPrelude,\n                initialCacheControl: cacheControl,\n              })\n\n              // update the page (eg /blog/[slug]) to also have the postpone metadata\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                hasPostponed,\n                hasEmptyPrelude,\n                initialCacheControl: cacheControl,\n              })\n\n              if (cacheControl.revalidate !== 0) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                let dataRoute: string | null\n                if (isAppRouteHandler) {\n                  dataRoute = null\n                } else {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | null | undefined\n                // While we may only write the `.rsc` when the route does not\n                // have PPR enabled, we still want to generate the route when\n                // deployed so it doesn't 404. If the app has PPR enabled, we\n                // should add this key.\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                const meta = collectMeta(metadata)\n\n                prerenderManifest.routes[route.pathname] = {\n                  initialStatus: meta.status,\n                  initialHeaders: meta.headers,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalPPR: isRoutePPREnabled,\n                  experimentalBypassFor: bypassFor,\n                  initialRevalidateSeconds: cacheControl.revalidate,\n                  initialExpireSeconds: cacheControl.expire,\n                  srcRoute: page,\n                  dataRoute,\n                  prefetchDataRoute,\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              } else {\n                hasRevalidateZero = true\n                // we might have determined during prerendering that this page\n                // used dynamic data\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isSSG: false,\n                  isStatic: false,\n                })\n              }\n            }\n\n            if (!hasRevalidateZero && isDynamicRoute(page)) {\n              // When PPR fallbacks aren't used, we need to include it here. If\n              // they are enabled, then it'll already be included in the\n              // prerendered routes.\n              if (!isRoutePPREnabled) {\n                dynamicRoutes.push({\n                  pathname: page,\n                  encodedPathname: page,\n                  fallbackRouteParams: undefined,\n                  fallbackMode:\n                    fallbackModes.get(originalAppPath) ??\n                    FallbackMode.NOT_FOUND,\n                  fallbackRootParams: undefined,\n                })\n              }\n\n              for (const route of dynamicRoutes) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                const metadata = exportResult.byPath.get(\n                  route.pathname\n                )?.metadata\n\n                const cacheControl = getCacheControl(route.pathname)\n\n                let dataRoute: string | null = null\n                if (!isAppRouteHandler) {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | undefined\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                if (!isAppRouteHandler && metadata?.segmentPaths) {\n                  const dynamicRoute = routesManifest.dynamicRoutes.find(\n                    (r) => r.page === page\n                  )\n                  if (!dynamicRoute) {\n                    throw new Error('Dynamic route not found')\n                  }\n\n                  dynamicRoute.prefetchSegmentDataRoutes = []\n                  for (const segmentPath of metadata.segmentPaths) {\n                    const result = buildPrefetchSegmentDataRoute(\n                      route.pathname,\n                      segmentPath\n                    )\n                    dynamicRoute.prefetchSegmentDataRoutes.push(result)\n                  }\n                }\n\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isDynamicAppRoute: true,\n                  // if PPR is turned on and the route contains a dynamic segment,\n                  // we assume it'll be partially prerendered\n                  hasPostponed: isRoutePPREnabled,\n                })\n\n                const fallbackMode =\n                  route.fallbackMode ?? FallbackMode.NOT_FOUND\n\n                // When the route is configured to serve a prerender, we should\n                // use the cache control from the export result. If it can't be\n                // found, mark that we should keep the shell forever\n                // (revalidate: `false` via `getCacheControl()`).\n                const fallbackCacheControl =\n                  isRoutePPREnabled && fallbackMode === FallbackMode.PRERENDER\n                    ? cacheControl\n                    : undefined\n\n                const fallback: Fallback = fallbackModeToFallbackField(\n                  fallbackMode,\n                  route.pathname\n                )\n\n                const meta =\n                  metadata &&\n                  isRoutePPREnabled &&\n                  fallbackMode === FallbackMode.PRERENDER\n                    ? collectMeta(metadata)\n                    : {}\n\n                prerenderManifest.dynamicRoutes[route.pathname] = {\n                  experimentalPPR: isRoutePPREnabled,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalBypassFor: bypassFor,\n                  routeRegex: normalizeRouteRegex(\n                    getNamedRouteRegex(route.pathname, {\n                      prefixRouteKeys: false,\n                    }).re.source\n                  ),\n                  dataRoute,\n                  fallback,\n                  fallbackRevalidate: fallbackCacheControl?.revalidate,\n                  fallbackExpire: fallbackCacheControl?.expire,\n                  fallbackStatus: meta.status,\n                  fallbackHeaders: meta.headers,\n                  fallbackRootParams: route.fallbackRootParams,\n                  fallbackSourceRoute: route.fallbackRouteParams?.length\n                    ? page\n                    : undefined,\n                  dataRouteRegex: !dataRoute\n                    ? null\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(dataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  prefetchDataRoute,\n                  prefetchDataRouteRegex: !prefetchDataRoute\n                    ? undefined\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(prefetchDataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              }\n            }\n          })\n\n          const moveExportedPage = async (\n            originPage: string,\n            page: string,\n            file: string,\n            isSsg: boolean,\n            ext: 'html' | 'json',\n            additionalSsgFile = false\n          ) => {\n            return staticGenerationSpan\n              .traceChild('move-exported-page')\n              .traceAsyncFn(async () => {\n                file = `${file}.${ext}`\n                const orig = path.join(outdir, file)\n                const pagePath = getPagePath(\n                  originPage,\n                  distDir,\n                  undefined,\n                  false\n                )\n\n                const relativeDest = path\n                  .relative(\n                    path.join(distDir, SERVER_DIRECTORY),\n                    path.join(\n                      path.join(\n                        pagePath,\n                        // strip leading / and then recurse number of nested dirs\n                        // to place from base folder\n                        originPage\n                          .slice(1)\n                          .split('/')\n                          .map(() => '..')\n                          .join('/')\n                      ),\n                      file\n                    )\n                  )\n                  .replace(/\\\\/g, '/')\n\n                if (\n                  !isSsg &&\n                  !(\n                    // don't add static status page to manifest if it's\n                    // the default generated version e.g. no pages/500\n                    (\n                      STATIC_STATUS_PAGES.includes(page) &&\n                      !usedStaticStatusPages.includes(page)\n                    )\n                  )\n                ) {\n                  pagesManifest[page] = relativeDest\n                }\n\n                const dest = path.join(distDir, SERVER_DIRECTORY, relativeDest)\n                const isNotFound =\n                  prerenderManifest.notFoundRoutes.includes(page)\n\n                // for SSG files with i18n the non-prerendered variants are\n                // output with the locale prefixed so don't attempt moving\n                // without the prefix\n                if ((!i18n || additionalSsgFile) && !isNotFound) {\n                  await fs.mkdir(path.dirname(dest), { recursive: true })\n                  await fs.rename(orig, dest)\n                } else if (i18n && !isSsg) {\n                  // this will be updated with the locale prefixed variant\n                  // since all files are output with the locale prefix\n                  delete pagesManifest[page]\n                }\n\n                if (i18n) {\n                  if (additionalSsgFile) return\n\n                  const localeExt = page === '/' ? path.extname(file) : ''\n                  const relativeDestNoPages = relativeDest.slice(\n                    'pages/'.length\n                  )\n\n                  for (const locale of i18n.locales) {\n                    const curPath = `/${locale}${page === '/' ? '' : page}`\n\n                    if (\n                      isSsg &&\n                      prerenderManifest.notFoundRoutes.includes(curPath)\n                    ) {\n                      continue\n                    }\n\n                    const updatedRelativeDest = path\n                      .join(\n                        'pages',\n                        locale + localeExt,\n                        // if it's the top-most index page we want it to be locale.EXT\n                        // instead of locale/index.html\n                        page === '/' ? '' : relativeDestNoPages\n                      )\n                      .replace(/\\\\/g, '/')\n\n                    const updatedOrig = path.join(\n                      outdir,\n                      locale + localeExt,\n                      page === '/' ? '' : file\n                    )\n                    const updatedDest = path.join(\n                      distDir,\n                      SERVER_DIRECTORY,\n                      updatedRelativeDest\n                    )\n\n                    if (!isSsg) {\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                    await fs.mkdir(path.dirname(updatedDest), {\n                      recursive: true,\n                    })\n                    await fs.rename(updatedOrig, updatedDest)\n                  }\n                }\n              })\n          }\n\n          async function moveExportedAppNotFoundTo404() {\n            return staticGenerationSpan\n              .traceChild('move-exported-app-not-found-')\n              .traceAsyncFn(async () => {\n                const orig = path.join(\n                  distDir,\n                  'server',\n                  'app',\n                  '_not-found.html'\n                )\n                const updatedRelativeDest = path\n                  .join('pages', '404.html')\n                  .replace(/\\\\/g, '/')\n\n                if (existsSync(orig)) {\n                  await fs.copyFile(\n                    orig,\n                    path.join(distDir, 'server', updatedRelativeDest)\n                  )\n                  pagesManifest['/404'] = updatedRelativeDest\n                }\n              })\n          }\n\n          // If there's /not-found inside app, we prefer it over the pages 404\n          if (hasStaticApp404) {\n            await moveExportedAppNotFoundTo404()\n          } else {\n            // Only move /404 to /404 when there is no custom 404 as in that case we don't know about the 404 page\n            if (!hasPages404 && !hasApp404 && useStaticPages404) {\n              await moveExportedPage('/_error', '/404', '/404', false, 'html')\n            }\n          }\n\n          if (useDefaultStatic500) {\n            await moveExportedPage('/_error', '/500', '/500', false, 'html')\n          }\n\n          for (const page of combinedPages) {\n            const isSsg = ssgPages.has(page)\n            const isStaticSsgFallback = ssgStaticFallbackPages.has(page)\n            const isDynamic = isDynamicRoute(page)\n            const hasAmp = hybridAmpPages.has(page)\n            const file = normalizePagePath(page)\n\n            const pageInfo = pageInfos.get(page)\n            const durationInfo = exportResult.byPage.get(page)\n            if (pageInfo && durationInfo) {\n              // Set Build Duration\n              if (pageInfo.ssgPageRoutes) {\n                pageInfo.ssgPageDurations = pageInfo.ssgPageRoutes.map(\n                  (pagePath) => {\n                    const duration = durationInfo.durationsByPath.get(pagePath)\n                    if (typeof duration === 'undefined') {\n                      throw new Error(\"Invariant: page wasn't built\")\n                    }\n\n                    return duration\n                  }\n                )\n              }\n              pageInfo.pageDuration = durationInfo.durationsByPath.get(page)\n            }\n\n            // The dynamic version of SSG pages are only prerendered if the\n            // fallback is enabled. Below, we handle the specific prerenders\n            // of these.\n            const hasHtmlOutput = !(isSsg && isDynamic && !isStaticSsgFallback)\n\n            if (hasHtmlOutput) {\n              await moveExportedPage(page, page, file, isSsg, 'html')\n            }\n\n            if (hasAmp && (!isSsg || (isSsg && !isDynamic))) {\n              const ampPage = `${file}.amp`\n              await moveExportedPage(page, ampPage, ampPage, isSsg, 'html')\n\n              if (isSsg) {\n                await moveExportedPage(page, ampPage, ampPage, isSsg, 'json')\n              }\n            }\n\n            if (isSsg) {\n              // For a non-dynamic SSG page, we must copy its data file\n              // from export, we already moved the HTML file above\n              if (!isDynamic) {\n                await moveExportedPage(page, page, file, isSsg, 'json')\n\n                if (i18n) {\n                  // TODO: do we want to show all locale variants in build output\n                  for (const locale of i18n.locales) {\n                    const localePage = `/${locale}${page === '/' ? '' : page}`\n\n                    const cacheControl = getCacheControl(localePage)\n\n                    prerenderManifest.routes[localePage] = {\n                      initialRevalidateSeconds: cacheControl.revalidate,\n                      initialExpireSeconds: cacheControl.expire,\n                      experimentalPPR: undefined,\n                      renderingMode: undefined,\n                      srcRoute: null,\n                      dataRoute: path.posix.join(\n                        '/_next/data',\n                        buildId,\n                        `${file}.json`\n                      ),\n                      prefetchDataRoute: undefined,\n                      allowHeader: ALLOWED_HEADERS,\n                    }\n                  }\n                } else {\n                  const cacheControl = getCacheControl(page)\n\n                  prerenderManifest.routes[page] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: null,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${file}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n                }\n                if (pageInfo) {\n                  pageInfo.initialCacheControl = getCacheControl(page)\n                }\n              } else {\n                // For a dynamic SSG page, we did not copy its data exports and only\n                // copy the fallback HTML file (if present).\n                // We must also copy specific versions of this page as defined by\n                // `getStaticPaths` (additionalSsgPaths).\n                for (const route of additionalPaths.get(page) ?? []) {\n                  const pageFile = normalizePagePath(route.pathname)\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'html',\n                    true\n                  )\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'json',\n                    true\n                  )\n\n                  if (hasAmp) {\n                    const ampPage = `${pageFile}.amp`\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'html',\n                      true\n                    )\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'json',\n                      true\n                    )\n                  }\n\n                  const cacheControl = getCacheControl(route.pathname)\n\n                  prerenderManifest.routes[route.pathname] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: page,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${normalizePagePath(route.pathname)}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n\n                  if (pageInfo) {\n                    pageInfo.initialCacheControl = cacheControl\n                  }\n                }\n              }\n            }\n          }\n\n          // remove temporary export folder\n          await fs.rm(outdir, { recursive: true, force: true })\n          await writeManifest(pagesManifestPath, pagesManifest)\n        })\n\n        // We need to write the manifest with rewrites after build as it might\n        // have been modified.\n        await nextBuildSpan\n          .traceChild('write-routes-manifest')\n          .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n      }\n\n      const postBuildSpinner = createSpinner('Finalizing page optimization')\n      let buildTracesSpinner = createSpinner(`Collecting build traces`)\n\n      // ensure the worker is not left hanging\n      worker.end()\n\n      const analysisEnd = process.hrtime(analysisBegin)\n      telemetry.record(\n        eventBuildOptimize(pagesPaths, {\n          durationInSeconds: analysisEnd[0],\n          staticPageCount: staticPages.size,\n          staticPropsPageCount: ssgPages.size,\n          serverPropsPageCount: serverPropsPages.size,\n          ssrPageCount:\n            pagesPaths.length -\n            (staticPages.size + ssgPages.size + serverPropsPages.size),\n          hasStatic404: useStaticPages404,\n          hasReportWebVitals:\n            namedExports?.includes('reportWebVitals') ?? false,\n          rewritesCount: combinedRewrites.length,\n          headersCount: headers.length,\n          redirectsCount: redirects.length - 1, // reduce one for trailing slash\n          headersWithHasCount: headers.filter((r: any) => !!r.has).length,\n          rewritesWithHasCount: combinedRewrites.filter((r: any) => !!r.has)\n            .length,\n          redirectsWithHasCount: redirects.filter((r: any) => !!r.has).length,\n          middlewareCount: hasMiddlewareFile ? 1 : 0,\n          totalAppPagesCount,\n          staticAppPagesCount,\n          serverAppPagesCount,\n          edgeRuntimeAppCount,\n          edgeRuntimePagesCount,\n        })\n      )\n\n      if (NextBuildContext.telemetryState) {\n        const events = eventBuildFeatureUsage(\n          NextBuildContext.telemetryState.usages\n        )\n        telemetry.record(events)\n        telemetry.record(\n          eventPackageUsedInGetServerSideProps(\n            NextBuildContext.telemetryState.packagesUsedInServerSideProps\n          )\n        )\n        const useCacheTracker = NextBuildContext.telemetryState.useCacheTracker\n\n        for (const [key, value] of Object.entries(useCacheTracker)) {\n          telemetry.record(\n            eventBuildFeatureUsage([\n              {\n                featureName: key as UseCacheTrackerKey,\n                invocationCount: value,\n              },\n            ])\n          )\n        }\n      }\n\n      if (ssgPages.size > 0 || appDir) {\n        tbdPrerenderRoutes.forEach((tbdRoute) => {\n          const normalizedRoute = normalizePagePath(tbdRoute)\n          const dataRoute = path.posix.join(\n            '/_next/data',\n            buildId,\n            `${normalizedRoute}.json`\n          )\n\n          prerenderManifest.dynamicRoutes[tbdRoute] = {\n            routeRegex: normalizeRouteRegex(\n              getNamedRouteRegex(tbdRoute, {\n                prefixRouteKeys: false,\n              }).re.source\n            ),\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            dataRoute,\n            fallback: ssgBlockingFallbackPages.has(tbdRoute)\n              ? null\n              : ssgStaticFallbackPages.has(tbdRoute)\n                ? `${normalizedRoute}.html`\n                : false,\n            fallbackRevalidate: undefined,\n            fallbackExpire: undefined,\n            fallbackSourceRoute: undefined,\n            fallbackRootParams: undefined,\n            dataRouteRegex: normalizeRouteRegex(\n              getNamedRouteRegex(dataRoute, {\n                prefixRouteKeys: true,\n                includeSuffix: true,\n                excludeOptionalTrailingSlash: true,\n              }).re.source\n            ),\n            // Pages does not have a prefetch data route.\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            allowHeader: ALLOWED_HEADERS,\n          }\n        })\n\n        NextBuildContext.previewModeId = previewProps.previewModeId\n        NextBuildContext.fetchCacheKeyPrefix =\n          config.experimental.fetchCacheKeyPrefix\n        NextBuildContext.allowedRevalidateHeaderKeys =\n          config.experimental.allowedRevalidateHeaderKeys\n\n        await writePrerenderManifest(distDir, prerenderManifest)\n        await writeClientSsgManifest(prerenderManifest, {\n          distDir,\n          buildId,\n          locales: config.i18n?.locales,\n        })\n      } else {\n        await writePrerenderManifest(distDir, {\n          version: 4,\n          routes: {},\n          dynamicRoutes: {},\n          preview: previewProps,\n          notFoundRoutes: [],\n        })\n      }\n\n      await writeImagesManifest(distDir, config)\n      await writeManifest(path.join(distDir, EXPORT_MARKER), {\n        version: 1,\n        hasExportPathMap: typeof config.exportPathMap === 'function',\n        exportTrailingSlash: config.trailingSlash === true,\n        isNextImageImported: isNextImageImported === true,\n      })\n      await fs.unlink(path.join(distDir, EXPORT_DETAIL)).catch((err) => {\n        if (err.code === 'ENOENT') {\n          return Promise.resolve()\n        }\n        return Promise.reject(err)\n      })\n\n      if (Boolean(config.experimental.nextScriptWorkers)) {\n        await nextBuildSpan\n          .traceChild('verify-partytown-setup')\n          .traceAsyncFn(async () => {\n            await verifyPartytownSetup(\n              dir,\n              path.join(distDir, CLIENT_STATIC_FILES_PATH)\n            )\n          })\n      }\n\n      await buildTracesPromise\n\n      if (buildTracesSpinner) {\n        buildTracesSpinner.stopAndPersist()\n        buildTracesSpinner = undefined\n      }\n\n      if (config.output === 'export') {\n        await writeFullyStaticExport(\n          config,\n          dir,\n          enabledDirectories,\n          configOutDir,\n          nextBuildSpan\n        )\n      }\n\n      if (config.output === 'standalone') {\n        await writeStandaloneDirectory(\n          nextBuildSpan,\n          distDir,\n          pageKeys,\n          denormalizedAppPages,\n          outputFileTracingRoot,\n          requiredServerFilesManifest,\n          middlewareManifest,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          staticPages,\n          loadedEnvFiles,\n          appDir\n        )\n      }\n\n      if (postBuildSpinner) postBuildSpinner.stopAndPersist()\n      console.log()\n\n      if (debugOutput) {\n        nextBuildSpan\n          .traceChild('print-custom-routes')\n          .traceFn(() => printCustomRoutes({ redirects, rewrites, headers }))\n      }\n\n      await nextBuildSpan.traceChild('print-tree-view').traceAsyncFn(() =>\n        printTreeView(pageKeys, pageInfos, {\n          distPath: distDir,\n          buildId: buildId,\n          pagesDir,\n          useStaticPages404,\n          pageExtensions: config.pageExtensions,\n          appBuildManifest,\n          buildManifest,\n          middlewareManifest,\n          gzipSize: config.experimental.gzipSize,\n        })\n      )\n\n      await nextBuildSpan\n        .traceChild('telemetry-flush')\n        .traceAsyncFn(() => telemetry.flush())\n\n      await shutdownPromise\n    })\n  } finally {\n    // Ensure we wait for lockfile patching if present\n    await lockfilePatchPromise.cur\n\n    // Ensure all traces are flushed before finishing the command\n    await flushAllTraces()\n    teardownTraceSubscriber()\n\n    if (traceUploadUrl && loadedConfig) {\n      uploadTrace({\n        traceUploadUrl,\n        mode: 'build',\n        projectDir: dir,\n        distDir: loadedConfig.distDir,\n        isTurboSession: turboNextBuild,\n        sync: true,\n      })\n    }\n  }\n}\n\nfunction durationToString(compilerDuration: number) {\n  let durationString\n  if (compilerDuration > 120) {\n    durationString = `${(compilerDuration / 60).toFixed(1)}min`\n  } else if (compilerDuration > 40) {\n    durationString = `${compilerDuration.toFixed(0)}s`\n  } else if (compilerDuration > 2) {\n    durationString = `${compilerDuration.toFixed(1)}s`\n  } else {\n    durationString = `${(compilerDuration * 1000).toFixed(0)}ms`\n  }\n  return durationString\n}\n"], "names": ["loadEnvConfig", "bold", "yellow", "crypto", "makeRe", "existsSync", "promises", "fs", "os", "Worker", "defaultConfig", "devalue", "findUp", "nanoid", "path", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "MIDDLEWARE_FILENAME", "PAGES_DIR_ALIAS", "INSTRUMENTATION_HOOK_FILENAME", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "NEXT_RESUME_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "MATCHED_PATH_HEADER", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "FileType", "fileExists", "findPagesDir", "loadCustomRoutes", "normalizeRouteRegex", "nonNullable", "recursiveDelete", "verifyPartytownSetup", "BUILD_ID_FILE", "BUILD_MANIFEST", "CLIENT_STATIC_FILES_PATH", "EXPORT_DETAIL", "EXPORT_MARKER", "IMAGES_MANIFEST", "PAGES_MANIFEST", "PHASE_PRODUCTION_BUILD", "PRERENDER_MANIFEST", "REACT_LOADABLE_MANIFEST", "ROUTES_MANIFEST", "SERVER_DIRECTORY", "SERVER_FILES_MANIFEST", "STATIC_STATUS_PAGES", "MIDDLEWARE_MANIFEST", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "APP_BUILD_MANIFEST", "RSC_MODULE_TYPES", "NEXT_FONT_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "UNDERSCORE_NOT_FOUND_ROUTE", "DYNAMIC_CSS_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "getSortedRoutes", "isDynamicRoute", "getSortedRouteObjects", "loadConfig", "normalizePagePath", "getPagePath", "ciEnvironment", "turborepoTraceAccess", "TurborepoAccessTraceResult", "writeTurborepoAccessTraceResult", "eventBuildOptimize", "eventCliSession", "eventBuildFeatureUsage", "eventNextPlugins", "EVENT_BUILD_FEATURE_USAGE", "eventPackageUsedInGetServerSideProps", "eventBuildCompleted", "Telemetry", "hadUnsupportedValue", "createPagesMapping", "getStaticInfoIncludingLayouts", "sortByPageExts", "PAGE_TYPES", "generateBuildId", "isWriteable", "Log", "createSpinner", "trace", "flushAllTraces", "setGlobal", "detectConflictingPaths", "computeFromManifest", "getJsPageSizeInKb", "printCustomRoutes", "printTreeView", "copyTracedFiles", "isReservedPage", "isAppBuiltinNotFoundPage", "collectRoutesUsingEdgeRuntime", "collectMeta", "writeBuildId", "normalizeLocalePath", "isError", "isEdgeRuntime", "recursiveCopy", "recursiveReadDir", "lockfilePatchPromise", "teardownTraceSubscriber", "getNamedRouteRegex", "getFilesInDir", "eventSwcPlugins", "normalizeAppPath", "ACTION_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "webpackBuild", "NextBuildContext", "normalizePathSep", "isAppRouteRoute", "createClientRouterFilter", "createValidFileMatcher", "startTypeChecking", "generateInterceptionRoutesRewrites", "buildDataRoute", "collectBuildTraces", "formatManifest", "recordFrameworkVersion", "updateBuildDiagnostics", "recordFetchMetrics", "getStartServerInfo", "logStartInfo", "hasCustomExportOutput", "buildCustomRoute", "traceMemoryUsage", "generateEncryptionKeyBase64", "uploadTrace", "checkIsAppPPREnabled", "checkIsRoutePPREnabled", "FallbackMode", "fallbackModeToFallbackField", "RenderingMode", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "formatNodeOptions", "getParsedNodeOptionsWithoutInspect", "InvariantError", "HTML_LIMITED_BOT_UA_RE_STRING", "buildPrefetchSegmentDataRoute", "turbopackBuild", "ALLOWED_HEADERS", "pageToRoute", "page", "routeRegex", "prefixRouteKeys", "regex", "re", "source", "routeKeys", "namedRegex", "getCacheDir", "distDir", "cacheDir", "join", "isCI", "hasNextSupport", "<PERSON><PERSON><PERSON>", "console", "log", "prefixes", "warn", "writeFileUtf8", "filePath", "content", "writeFile", "readFileUtf8", "readFile", "writeManifest", "manifest", "readManifest", "JSON", "parse", "writePrerenderManifest", "writeClientSsgManifest", "prerenderManifest", "buildId", "locales", "ssgPages", "Set", "Object", "entries", "routes", "filter", "srcRoute", "map", "route", "pathname", "keys", "dynamicRoutes", "sort", "clientSsgManifestContent", "writeFunctionsConfigManifest", "writeRequiredServerFilesManifest", "requiredServerFiles", "writeImagesManifest", "config", "images", "deviceSizes", "imageSizes", "sizes", "remotePatterns", "p", "protocol", "hostname", "port", "dot", "search", "localPatterns", "version", "STANDALONE_DIRECTORY", "writeStandaloneDirectory", "nextBuildSpan", "pageKeys", "denormalizedAppPages", "outputFileTracingRoot", "middlewareManifest", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "loadedEnvFiles", "appDir", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "pages", "file", "files", "reduce", "acc", "envFile", "includes", "push", "outputPath", "relative", "mkdir", "dirname", "recursive", "copyFile", "middlewareOutput", "overwrite", "originalServerApp", "getNumberOfWorkers", "experimental", "cpus", "memoryBasedWorkersCount", "Math", "max", "min", "floor", "freemem", "staticWorkerPath", "require", "resolve", "staticWorkerExposedMethods", "createStaticWorker", "progress", "nodeOptions", "logger", "numWorkers", "onActivity", "run", "onActivityAbort", "clear", "forkOptions", "env", "process", "NODE_OPTIONS", "enableWorkerThreads", "workerThreads", "exposedMethods", "writeFullyStaticExport", "dir", "enabledDirectories", "configOutDir", "exportApp", "default", "pagesWorker", "appWorker", "buildExport", "nextConfig", "silent", "outdir", "end", "getBuildId", "isGenerateMode", "build", "reactProductionProfiling", "debugOutput", "runLint", "noMangling", "appDirOnly", "turboNextBuild", "experimentalBuildMode", "traceUploadUrl", "isCompileMode", "loadedConfig", "undefined", "buildMode", "isTurboBuild", "String", "__NEXT_VERSION", "mappedPages", "traceFn", "turborepoAccessTraceResult", "NEXT_DEPLOYMENT_ID", "deploymentId", "customRoutes", "headers", "rewrites", "redirects", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "hasRewrites", "length", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "telemetry", "publicDir", "pagesDir", "app", "<PERSON><PERSON><PERSON>", "isBuild", "isSrcDir", "startsWith", "hasPublicDir", "record", "webpackVersion", "cliCommand", "has<PERSON>ow<PERSON><PERSON>", "cwd", "isCustomServer", "turboFlag", "then", "events", "envInfo", "experimentalFeatures", "networkUrl", "appUrl", "ignoreESLint", "Boolean", "eslint", "ignoreDuringBuilds", "shouldLint", "typeCheckingOptions", "distDirCreated", "err", "code", "Error", "cleanDistDir", "error", "flush", "exit", "buildLintEvent", "featureName", "invocationCount", "eventName", "payload", "validFile<PERSON><PERSON><PERSON>", "pageExtensions", "providedPagePaths", "NEXT_PRIVATE_PAGE_PATHS", "pagesPaths", "pathnameFilter", "isPageFile", "middlewareDetectionRegExp", "RegExp", "instrumentationHookDetectionRegExp", "rootDir", "rootPaths", "Array", "from", "some", "include", "test", "replace", "hasMiddlewareFile", "previewProps", "previewModeId", "randomBytes", "toString", "previewModeSigningKey", "previewModeEncryptionKey", "isDev", "pagesType", "PAGES", "pagePaths", "mappedAppPages", "providedAppPaths", "NEXT_PRIVATE_APP_PATHS", "appPaths", "absolutePath", "isAppRouterPage", "isRootNotFound", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "APP", "mappedRootPaths", "ROOT", "pagesPageKeys", "conflictingAppPagePaths", "appPageKeys", "appKey", "normalizedAppPageKey", "pagePath", "appPath", "add", "basePath", "totalAppPagesCount", "numConflictingAppPaths", "conflictingPublicFiles", "hasPages404", "hasApp404", "hasCustomErrorPage", "hasPublicUnderScoreNextDir", "hasPublicPageFile", "File", "numConflicting", "nestedReservedPages", "match", "restrictedRedirectPaths", "isAppDynamicIOEnabled", "dynamicIO", "isAuthInterruptsEnabled", "authInterrupts", "isAppPPREnabled", "ppr", "routesManifestPath", "routesManifest", "sortedRoutes", "staticRoutes", "pages404", "caseSensitive", "caseSensitiveRoutes", "r", "dataRoutes", "i18n", "rsc", "header", "<PERSON><PERSON><PERSON><PERSON>", "prefetch<PERSON><PERSON><PERSON>", "didPostponeHeader", "contentTypeHeader", "suffix", "prefetchSuffix", "prefetchSegmentHeader", "prefetchSegmentSuffix", "prefetchSegmentDirSuffix", "rewriteHeaders", "pathHeader", "query<PERSON>eader", "skipMiddlewareUrlNormalize", "chain", "clientRouterFilters", "clientRouterFilter", "nonInternalRedirects", "internal", "clientRouterFilterRedirects", "clientRouterFilterAllowedRate", "buildStage", "pagesManifestPath", "buildTraceContext", "buildTracesPromise", "useBuildWorker", "webpackBuildWorker", "webpack", "runServerAndEdgeInParallel", "parallelServerCompiles", "collectServerBuildTracesInParallel", "parallelServerBuildTraces", "setAttribute", "info", "buildOptions", "shutdownPromise", "Promise", "duration", "compilerDuration", "rest", "NEXT_TURBOPACK_USE_WORKER", "durationString", "durationToString", "event", "durationInSeconds", "round", "serverBuildPromise", "res", "buildTraceWorker", "edgeRuntimeRoutes", "Map", "hasSsrAmpPages", "catch", "edgeBuildPromise", "postCompileSpinner", "buildManifestPath", "appBuildManifestPath", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "ssgStaticFallbackPages", "ssgBlockingFallbackPages", "invalidPages", "hybridAmpPages", "serverPropsPages", "additionalPaths", "staticPaths", "prospectiveRenders", "appNormalizedPaths", "fallbackModes", "appDefaultConfigs", "pageInfos", "pagesManifest", "buildManifest", "appBuildManifest", "appPathRoutes", "appPathsManifest", "key", "NEXT_PHASE", "worker", "analysisBegin", "hrtime", "staticCheckSpan", "functionsConfigManifest", "functions", "customAppGetInitialProps", "namedExports", "isNextImageImported", "hasNonStaticErrorPage", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "runtimeEnvConfig", "sriEnabled", "sri", "algorithm", "nonStaticErrorPageSpan", "errorPageHasCustomGetInitialProps", "hasCustomGetInitialProps", "checkingApp", "errorPageStaticResult", "isPageStatic", "httpAgentOptions", "defaultLocale", "nextConfigOutput", "output", "pprConfig", "cacheLifeProfiles", "cacheLife", "appPageToCheck", "customAppGetInitialPropsPromise", "namedExportsPromise", "getDefinedNamedExports", "computedManifestData", "gzipSize", "actionManifest", "entriesWithAction", "id", "node", "entry", "workers", "edge", "all", "pageType", "checkPageSpan", "actualPage", "size", "totalSize", "isRoutePPREnabled", "isSSG", "isStatic", "isServerComponent", "isHybridAmp", "ssgPageRoutes", "find", "originalAppPath", "originalPath", "normalizedPath", "pageFilePath", "isInsideAppDir", "staticInfo", "runtime", "maxDuration", "pageRuntime", "client", "edgeInfo", "manifest<PERSON>ey", "isPageStaticSpan", "workerResult", "parentId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "isrFlushToDisk", "maxMemoryCacheSize", "cacheMaxMemorySize", "set", "warnOnce", "isDynamic", "prerenderedRoutes", "appConfig", "revalidate", "hasGenerateStaticParams", "encodedPathname", "fallbackRouteParams", "fallbackMode", "prerenderFallbackMode", "fallbackRootParams", "dynamic", "hasStaticProps", "isAmpOnly", "BLOCKING_STATIC_RENDER", "PRERENDER", "hasServerProps", "delete", "message", "initialCacheControl", "pageDuration", "ssgPageDurations", "hasEmptyPrelude", "errorPageResult", "nonStaticErrorPage", "returnValue", "stopAndPersist", "instrumentationHookEntryFiles", "TURBOPACK", "requiredServerFilesManifest", "normalizedCacheHandlers", "value", "serverFilesManifest", "configFile", "compress", "trustHostHeader", "isExperimentalCompile", "relativeAppDir", "ignore", "middlewareFile", "matchers", "middleware", "regexp", "originalSource", "useStaticPages404", "pg", "optimizeCss", "globOrig", "cssFilePaths", "reject", "features", "nextScriptWorkers", "feature", "notFoundRoutes", "preview", "tbdPrerenderRoutes", "usedStaticStatusPages", "for<PERSON>ach", "has", "hasPages500", "useDefaultStatic500", "combinedPages", "isApp404Static", "hasStaticApp404", "staticGenerationSpan", "exportConfig", "exportPathMap", "defaultMap", "_pagesFallback", "_ssgPath", "get", "isDynamicError", "_fallbackRouteParams", "_isDynamicError", "_isAppDir", "_isRoutePPREnabled", "values", "_isProspectiveRender", "isSsg", "<PERSON><PERSON><PERSON><PERSON>", "locale", "_locale", "exportResult", "statusMessage", "getCacheControl", "exportPath", "defaultRevalidate", "cacheControl", "by<PERSON><PERSON>", "expire", "expireTime", "NEXT_SSG_FETCH_METRICS", "traces", "turborepoAccessTraceResults", "ssgNotFoundPaths", "serverBundle", "unlink", "hasRevalidateZero", "isAppRouteHandler", "htmlBotsRegexString", "htmlLimitedBots", "bypassFor", "type", "unknown<PERSON>rerender<PERSON><PERSON><PERSON>", "knownPrerenderRoutes", "prerenderedRoute", "metadata", "hasPostponed", "normalizedRoute", "dataRoute", "posix", "prefetchDataRoute", "meta", "initialStatus", "status", "initialHeaders", "renderingMode", "PARTIALLY_STATIC", "STATIC", "experimentalPPR", "experimentalBypassFor", "initialRevalidateSeconds", "initialExpireSeconds", "allow<PERSON>eader", "NOT_FOUND", "segmentPaths", "dynamicRoute", "prefetchSegmentDataRoutes", "segmentPath", "result", "isDynamicAppRoute", "fallbackCacheControl", "fallbackRevalidate", "fallbackExpire", "fallback<PERSON><PERSON><PERSON>", "fallbackHeaders", "fallbackSourceRoute", "dataRouteRegex", "includeSuffix", "excludeOptionalTrailingSlash", "prefetchDataRouteRegex", "moveExportedPage", "originPage", "ext", "additionalSsgFile", "orig", "relativeDest", "slice", "split", "dest", "isNotFound", "rename", "localeExt", "extname", "relativeDestNoPages", "curPath", "updatedRelativeDest", "updatedOrig", "updatedDest", "moveExportedAppNotFoundTo404", "isStaticSsgFallback", "hasAmp", "pageInfo", "durationInfo", "byPage", "durationsByPath", "hasHtmlOutput", "ampPage", "localePage", "pageFile", "rm", "force", "postBuildSpinner", "buildTracesSpinner", "analysisEnd", "staticPageCount", "staticPropsPageCount", "serverPropsPageCount", "ssrPageCount", "hasStatic404", "hasReportWebVitals", "rewritesCount", "headersCount", "redirectsCount", "headersWithHasCount", "rewritesWithHasCount", "redirectsWithHasCount", "middlewareCount", "telemetryState", "usages", "packagesUsedInServerSideProps", "useCacheTracker", "tbdRoute", "fetchCacheKeyPrefix", "allowedRevalidateHeaderKeys", "hasExportPathMap", "exportTrailingSlash", "trailingSlash", "distPath", "cur", "mode", "projectDir", "isTurboSession", "sync", "toFixed"], "mappings": "AAOA,OAAO,mCAAkC;AAEzC,SAASA,aAAa,QAA6B,YAAW;AAC9D,SAASC,IAAI,EAAEC,MAAM,QAAQ,oBAAmB;AAChD,OAAOC,YAAY,SAAQ;AAC3B,SAASC,MAAM,QAAQ,+BAA8B;AACrD,SAASC,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,OAAOC,QAAQ,KAAI;AACnB,SAASC,MAAM,QAAQ,gBAAe;AACtC,SAASC,aAAa,QAAQ,0BAAyB;AACvD,OAAOC,aAAa,6BAA4B;AAChD,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,MAAM,QAAQ,sCAAqC;AAC5D,OAAOC,UAAU,OAAM;AACvB,SACEC,0CAA0C,EAC1CC,8BAA8B,EAC9BC,mBAAmB,EACnBC,eAAe,EACfC,6BAA6B,EAC7BC,mBAAmB,EACnBC,UAAU,EACVC,kBAAkB,EAClBC,2BAA2B,EAC3BC,0CAA0C,EAC1CC,sCAAsC,EACtCC,kCAAkC,EAClCC,mBAAmB,EACnBC,uBAAuB,EACvBC,kBAAkB,QACb,mBAAkB;AACzB,SAASC,QAAQ,EAAEC,UAAU,QAAQ,qBAAoB;AACzD,SAASC,YAAY,QAAQ,wBAAuB;AACpD,OAAOC,oBACLC,mBAAmB,QACd,4BAA2B;AAQlC,SAASC,WAAW,QAAQ,sBAAqB;AACjD,SAASC,eAAe,QAAQ,0BAAyB;AACzD,SAASC,oBAAoB,QAAQ,gCAA+B;AACpE,SACEC,aAAa,EACbC,cAAc,EACdC,wBAAwB,EACxBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,sBAAsB,EACtBC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,gBAAgB,EAChBC,qBAAqB,EACrBC,mBAAmB,EACnBC,mBAAmB,EACnBC,kBAAkB,EAClBC,wBAAwB,EACxBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,yBAAyB,EACzBC,kCAAkC,EAClCC,yBAAyB,EACzBC,yBAAyB,EACzBC,gCAAgC,EAChCC,0BAA0B,EAC1BC,oBAAoB,EACpBC,oCAAoC,QAC/B,0BAAyB;AAChC,SACEC,eAAe,EACfC,cAAc,EACdC,qBAAqB,QAChB,6BAA4B;AAEnC,OAAOC,gBAAgB,mBAAkB;AAEzC,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,WAAW,QAAQ,oBAAmB;AAC/C,YAAYC,mBAAmB,oBAAmB;AAClD,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,+BAA+B,QAC1B,2BAA0B;AAEjC,SACEC,kBAAkB,EAClBC,eAAe,EACfC,sBAAsB,EACtBC,gBAAgB,EAChBC,yBAAyB,EACzBC,oCAAoC,EACpCC,mBAAmB,QACd,sBAAqB;AAE5B,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SAASC,mBAAmB,QAAQ,kCAAiC;AACrE,SACEC,kBAAkB,EAClBC,6BAA6B,EAC7BC,cAAc,QACT,YAAW;AAClB,SAASC,UAAU,QAAQ,oBAAmB;AAC9C,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,YAAYC,SAAS,eAAc;AACnC,OAAOC,mBAAmB,YAAW;AACrC,SAASC,KAAK,EAAEC,cAAc,EAAEC,SAAS,QAAmB,WAAU;AACtE,SACEC,sBAAsB,EACtBC,mBAAmB,EACnBC,iBAAiB,EACjBC,iBAAiB,EACjBC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,wBAAwB,EACxBC,6BAA6B,EAC7BC,WAAW,QACN,UAAS;AAIhB,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,OAAOC,aAAa,kBAAiB;AAErC,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAuB;AACrD,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,oBAAoB,EAAEC,uBAAuB,QAAQ,QAAO;AACrE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,aAAa,QAAQ,0BAAyB;AACvD,SAASC,eAAe,QAAQ,kCAAiC;AACjE,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SACEC,aAAa,EACbC,2BAA2B,EAC3BC,UAAU,EACVC,uBAAuB,EACvBC,6BAA6B,EAC7BC,wBAAwB,EACxBC,mCAAmC,EACnCC,0BAA0B,EAC1BC,2BAA2B,QACtB,0CAAyC;AAChD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,gBAAgB,QAA0B,kBAAiB;AACpE,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,wBAAwB,QAAQ,qCAAoC;AAC7E,SAASC,sBAAsB,QAAQ,+BAA8B;AACrE,SAASC,iBAAiB,QAAQ,eAAc;AAChD,SAASC,kCAAkC,QAAQ,+CAA8C;AAEjG,SAASC,cAAc,QAAQ,8CAA6C;AAC5E,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,kBAAkB,QACb,mCAAkC;AACzC,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,6BAA4B;AAE7E,SAASC,qBAAqB,QAAQ,kBAAiB;AACvD,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,2BAA2B,QAAQ,+CAA8C;AAE1F,OAAOC,iBAAiB,wBAAuB;AAC/C,SACEC,oBAAoB,EACpBC,sBAAsB,QACjB,iCAAgC;AACvC,SAASC,YAAY,EAAEC,2BAA2B,QAAQ,kBAAiB;AAC3E,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,YAAY,QAAQ,oCAAmC;AAChE,SACEC,iBAAiB,EACjBC,kCAAkC,QAC7B,sBAAqB;AAC5B,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,6BAA6B,QAAQ,oCAAmC;AAEjF,SACEC,6BAA6B,QAExB,+DAA8D;AAErE,SAASC,cAAc,QAAQ,oBAAmB;AA2HlD;;;CAGC,GACD,MAAMC,kBAA4B;IAChC;IACAtI;IACAJ;IACAC;IACAE;IACAD;CACD;AAiGD,SAASyI,YAAYC,IAAY;IAC/B,MAAMC,aAAajD,mBAAmBgD,MAAM;QAC1CE,iBAAiB;IACnB;IACA,OAAO;QACLF;QACAG,OAAOpI,oBAAoBkI,WAAWG,EAAE,CAACC,MAAM;QAC/CC,WAAWL,WAAWK,SAAS;QAC/BC,YAAYN,WAAWM,UAAU;IACnC;AACF;AAEA,SAASC,YAAYC,OAAe;IAClC,MAAMC,WAAW/J,KAAKgK,IAAI,CAACF,SAAS;IACpC,IAAInG,cAAcsG,IAAI,IAAI,CAACtG,cAAcuG,cAAc,EAAE;QACvD,MAAMC,WAAW5K,WAAWwK;QAE5B,IAAI,CAACI,UAAU;YACb,kGAAkG;YAClG,sBAAsB;YACtBC,QAAQC,GAAG,CACT,GAAGvF,IAAIwF,QAAQ,CAACC,IAAI,CAAC,+HAA+H,CAAC;QAEzJ;IACF;IACA,OAAOR;AACT;AAEA,eAAeS,cAAcC,QAAgB,EAAEC,OAAe;IAC5D,MAAMjL,GAAGkL,SAAS,CAACF,UAAUC,SAAS;AACxC;AAEA,SAASE,aAAaH,QAAgB;IACpC,OAAOhL,GAAGoL,QAAQ,CAACJ,UAAU;AAC/B;AAEA,eAAeK,cACbL,QAAgB,EAChBM,QAAW;IAEX,MAAMP,cAAcC,UAAU7C,eAAemD;AAC/C;AAEA,eAAeC,aAA+BP,QAAgB;IAC5D,OAAOQ,KAAKC,KAAK,CAAC,MAAMN,aAAaH;AACvC;AAEA,eAAeU,uBACbrB,OAAe,EACfiB,QAAyC;IAEzC,MAAMD,cAAc9K,KAAKgK,IAAI,CAACF,SAAS9H,qBAAqB+I;AAC9D;AAEA,eAAeK,uBACbC,iBAAkD,EAClD,EACEC,OAAO,EACPxB,OAAO,EACPyB,OAAO,EAKR;IAED,MAAMC,WAAW,IAAIC,IACnB;WACKC,OAAOC,OAAO,CAACN,kBAAkBO,MAAM,CACxC,4BAA4B;SAC3BC,MAAM,CAAC,CAAC,GAAG,EAAEC,QAAQ,EAAE,CAAC,GAAKA,YAAY,MACzCC,GAAG,CAAC,CAAC,CAACC,MAAM,GAAKlG,oBAAoBkG,OAAOT,SAASU,QAAQ;WAC7DP,OAAOQ,IAAI,CAACb,kBAAkBc,aAAa;KAC/C,CAACC,IAAI;IAGR,MAAMC,2BAA2B,CAAC,oBAAoB,EAAExM,QACtD2L,UACA,iDAAiD,CAAC;IAEpD,MAAMhB,cACJxK,KAAKgK,IAAI,CAACF,SAASpI,0BAA0B4J,SAAS,oBACtDe;AAEJ;AAmBA,eAAeC,6BACbxC,OAAe,EACfiB,QAAiC;IAEjC,MAAMD,cACJ9K,KAAKgK,IAAI,CAACF,SAAS3H,kBAAkBa,4BACrC+H;AAEJ;AAWA,eAAewB,iCACbzC,OAAe,EACf0C,mBAAgD;IAEhD,MAAM1B,cACJ9K,KAAKgK,IAAI,CAACF,SAAS1H,wBACnBoK;AAEJ;AAEA,eAAeC,oBACb3C,OAAe,EACf4C,MAA0B;QAODA,gBAUrBA;IAfJ,MAAMC,SAAS;QAAE,GAAGD,OAAOC,MAAM;IAAC;IAClC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAE,GAAGF;IAClCA,OAAeG,KAAK,GAAG;WAAIF;WAAgBC;KAAW;IAExD,8DAA8D;IAC9DF,OAAOI,cAAc,GAAG,AAACL,CAAAA,CAAAA,2BAAAA,iBAAAA,OAAQC,MAAM,qBAAdD,eAAgBK,cAAc,KAAI,EAAE,AAAD,EAAGhB,GAAG,CAAC,CAACiB,IAAO,CAAA;YACzE,iEAAiE;YACjEC,UAAUD,EAAEC,QAAQ;YACpBC,UAAU5N,OAAO0N,EAAEE,QAAQ,EAAExD,MAAM;YACnCyD,MAAMH,EAAEG,IAAI;YACZlB,UAAU3M,OAAO0N,EAAEf,QAAQ,IAAI,MAAM;gBAAEmB,KAAK;YAAK,GAAG1D,MAAM;YAC1D2D,QAAQL,EAAEK,MAAM;QAClB,CAAA;IAEA,oEAAoE;IACpE,IAAIX,2BAAAA,kBAAAA,OAAQC,MAAM,qBAAdD,gBAAgBY,aAAa,EAAE;QACjCX,OAAOW,aAAa,GAAGZ,OAAOC,MAAM,CAACW,aAAa,CAACvB,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAC7D,gEAAgE;gBAChEf,UAAU3M,OAAO0N,EAAEf,QAAQ,IAAI,MAAM;oBAAEmB,KAAK;gBAAK,GAAG1D,MAAM;gBAC1D2D,QAAQL,EAAEK,MAAM;YAClB,CAAA;IACF;IAEA,MAAMvC,cAAc9K,KAAKgK,IAAI,CAACF,SAASjI,kBAAkB;QACvD0L,SAAS;QACTZ;IACF;AACF;AAEA,MAAMa,uBAAuB;AAC7B,eAAeC,yBACbC,aAAmB,EACnB5D,OAAe,EACf6D,QAAwD,EACxDC,oBAA0C,EAC1CC,qBAA6B,EAC7BrB,mBAAgD,EAChDsB,kBAAsC,EACtCC,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB,EACxBC,cAA8B,EAC9BC,MAA0B;IAE1B,MAAMT,cACHU,UAAU,CAAC,8BACXC,YAAY,CAAC;QACZ,MAAM7I,gBACJ,kFAAkF;QAClFgH,oBAAoB2B,MAAM,EAC1BrE,SACA6D,SAASW,KAAK,EACdV,sBACAC,uBACArB,oBAAoBE,MAAM,EAC1BoB,oBACAC,mBACAC,wBACAC;QAGF,KAAK,MAAMM,QAAQ;eACd/B,oBAAoBgC,KAAK;YAC5BxO,KAAKgK,IAAI,CAACwC,oBAAoBE,MAAM,CAAC5C,OAAO,EAAE1H;eAC3C8L,eAAeO,MAAM,CAAW,CAACC,KAAKC;gBACvC,IAAI;oBAAC;oBAAQ;iBAAkB,CAACC,QAAQ,CAACD,QAAQ3O,IAAI,GAAG;oBACtD0O,IAAIG,IAAI,CAACF,QAAQ3O,IAAI;gBACvB;gBACA,OAAO0O;YACT,GAAG,EAAE;SACN,CAAE;YACD,kFAAkF;YAClF,MAAMjE,WAAWzK,KAAKgK,IAAI,CAACwC,oBAAoB2B,MAAM,EAAEI;YACvD,MAAMO,aAAa9O,KAAKgK,IAAI,CAC1BF,SACA0D,sBACAxN,KAAK+O,QAAQ,CAAClB,uBAAuBpD;YAEvC,MAAMhL,GAAGuP,KAAK,CAAChP,KAAKiP,OAAO,CAACH,aAAa;gBACvCI,WAAW;YACb;YACA,MAAMzP,GAAG0P,QAAQ,CAAC1E,UAAUqE;QAC9B;QAEA,IAAIf,mBAAmB;YACrB,MAAMqB,mBAAmBpP,KAAKgK,IAAI,CAChCF,SACA0D,sBACAxN,KAAK+O,QAAQ,CAAClB,uBAAuB/D,UACrC3H,kBACA;YAGF,MAAM1C,GAAGuP,KAAK,CAAChP,KAAKiP,OAAO,CAACG,mBAAmB;gBAAEF,WAAW;YAAK;YACjE,MAAMzP,GAAG0P,QAAQ,CACfnP,KAAKgK,IAAI,CAACF,SAAS3H,kBAAkB,kBACrCiN;QAEJ;QAEA,MAAMnJ,cACJjG,KAAKgK,IAAI,CAACF,SAAS3H,kBAAkB,UACrCnC,KAAKgK,IAAI,CACPF,SACA0D,sBACAxN,KAAK+O,QAAQ,CAAClB,uBAAuB/D,UACrC3H,kBACA,UAEF;YAAEkN,WAAW;QAAK;QAEpB,IAAIlB,QAAQ;YACV,MAAMmB,oBAAoBtP,KAAKgK,IAAI,CAACF,SAAS3H,kBAAkB;YAC/D,IAAI5C,WAAW+P,oBAAoB;gBACjC,MAAMrJ,cACJqJ,mBACAtP,KAAKgK,IAAI,CACPF,SACA0D,sBACAxN,KAAK+O,QAAQ,CAAClB,uBAAuB/D,UACrC3H,kBACA,QAEF;oBAAEkN,WAAW;gBAAK;YAEtB;QACF;IACF;AACJ;AAEA,SAASE,mBAAmB7C,MAA0B;IACpD,IACEA,OAAO8C,YAAY,CAACC,IAAI,IACxB/C,OAAO8C,YAAY,CAACC,IAAI,KAAK7P,cAAc4P,YAAY,CAAEC,IAAI,EAC7D;QACA,OAAO/C,OAAO8C,YAAY,CAACC,IAAI;IACjC;IAEA,IAAI/C,OAAO8C,YAAY,CAACE,uBAAuB,EAAE;QAC/C,OAAOC,KAAKC,GAAG,CACbD,KAAKE,GAAG,CAACnD,OAAO8C,YAAY,CAACC,IAAI,IAAI,GAAGE,KAAKG,KAAK,CAACpQ,GAAGqQ,OAAO,KAAK,OAClE,iCAAiC;QACjC;IAEJ;IAEA,IAAIrD,OAAO8C,YAAY,CAACC,IAAI,EAAE;QAC5B,OAAO/C,OAAO8C,YAAY,CAACC,IAAI;IACjC;IAEA,qDAAqD;IACrD,OAAO;AACT;AAEA,MAAMO,mBAAmBC,QAAQC,OAAO,CAAC;AACzC,MAAMC,6BAA6B;IACjC;IACA;IACA;IACA;CACD;AAED,OAAO,SAASC,mBACd1D,MAA0B,EAC1B2D,QAGC;IAED,2DAA2D;IAC3D,2DAA2D;IAC3D,MAAMC,cAAcxH;IACpB,OAAOwH,WAAW,CAAC,qBAAqB;IACxC,OAAOA,WAAW,CAAC,qBAAqB;IAExC,OAAO,IAAI3Q,OAAOqQ,kBAAkB;QAClCO,QAAQzL;QACR0L,YAAYjB,mBAAmB7C;QAC/B+D,YAAY;YACVJ,4BAAAA,SAAUK,GAAG;QACf;QACAC,iBAAiB;YACfN,4BAAAA,SAAUO,KAAK;QACjB;QACAC,aAAa;YACXC,KAAK;gBAAE,GAAGC,QAAQD,GAAG;gBAAEE,cAAcnI,kBAAkByH;YAAa;QACtE;QACAW,qBAAqBvE,OAAO8C,YAAY,CAAC0B,aAAa;QACtDC,gBAAgBhB;IAClB;AACF;AAEA,eAAeiB,uBACb1E,MAA0B,EAC1B2E,GAAW,EACXC,kBAA0C,EAC1CC,YAAoB,EACpB7D,aAAmB;IAEnB,MAAM8D,YAAYvB,QAAQ,aACvBwB,OAAO;IAEV,MAAMC,cAActB,mBAAmB1D;IACvC,MAAMiF,YAAYvB,mBAAmB1D;IAErC,MAAM8E,UACJH,KACA;QACEO,aAAa;QACbC,YAAYnF;QACZ4E;QACAQ,QAAQ;QACRC,QAAQ/R,KAAKgK,IAAI,CAACqH,KAAKE;QACvBf,YAAYjB,mBAAmB7C;IACjC,GACAgB;IAGFgE,YAAYM,GAAG;IACfL,UAAUK,GAAG;AACf;AAEA,eAAeC,WACbC,cAAuB,EACvBpI,OAAe,EACf4D,aAAmB,EACnBhB,MAA0B;IAE1B,IAAIwF,gBAAgB;QAClB,OAAO,MAAMzS,GAAGoL,QAAQ,CAAC7K,KAAKgK,IAAI,CAACF,SAAS,aAAa;IAC3D;IACA,OAAO,MAAM4D,cACVU,UAAU,CAAC,oBACXC,YAAY,CAAC,IAAMzJ,gBAAgB8H,OAAO9H,eAAe,EAAE7E;AAChE;AAEA,eAAe,eAAeoS,MAC5Bd,GAAW,EACXe,2BAA2B,KAAK,EAChCC,cAAc,KAAK,EACnBC,UAAU,IAAI,EACdC,aAAa,KAAK,EAClBC,aAAa,KAAK,EAClBC,iBAAiB,KAAK,EACtBC,qBAAyD,EACzDC,cAAkC;IAElC,MAAMC,gBAAgBF,0BAA0B;IAChD,MAAMR,iBAAiBQ,0BAA0B;IAEjD,IAAIG;IACJ,IAAI;QACF,MAAMnF,gBAAgB1I,MAAM,cAAc8N,WAAW;YACnDC,WAAWL;YACXM,cAAcC,OAAOR;YACrBlF,SAASwD,QAAQD,GAAG,CAACoC,cAAc;QACrC;QAEA/L,iBAAiBuG,aAAa,GAAGA;QACjCvG,iBAAiBkK,GAAG,GAAGA;QACvBlK,iBAAiBqL,UAAU,GAAGA;QAC9BrL,iBAAiBiL,wBAAwB,GAAGA;QAC5CjL,iBAAiBoL,UAAU,GAAGA;QAE9B,MAAM7E,cAAcW,YAAY,CAAC;gBAoWX8E;YAnWpB,4EAA4E;YAC5E,MAAM,EAAEjF,cAAc,EAAE,GAAGR,cACxBU,UAAU,CAAC,eACXgF,OAAO,CAAC,IAAMlU,cAAcmS,KAAK,OAAOvM;YAC3CqC,iBAAiB+G,cAAc,GAAGA;YAElC,MAAMmF,6BAA6B,IAAIxP;YACvC,MAAM6I,SAA6B,MAAMgB,cACtCU,UAAU,CAAC,oBACXC,YAAY,CAAC,IACZzK,qBACE,IACEJ,WAAWzB,wBAAwBsP,KAAK;wBACtC,sCAAsC;wBACtCS,QAAQ;wBACRM;oBACF,IACFiB;YAGNR,eAAenG;YAEfqE,QAAQD,GAAG,CAACwC,kBAAkB,GAAG5G,OAAO6G,YAAY,IAAI;YACxDpM,iBAAiBuF,MAAM,GAAGA;YAE1B,IAAI6E,eAAe;YACnB,IAAIrJ,sBAAsBwE,SAAS;gBACjC6E,eAAe7E,OAAO5C,OAAO;gBAC7B4C,OAAO5C,OAAO,GAAG;YACnB;YACA,MAAMA,UAAU9J,KAAKgK,IAAI,CAACqH,KAAK3E,OAAO5C,OAAO;YAC7C3C,iBAAiB2C,OAAO,GAAGA;YAC3B5E,UAAU,SAASnD;YACnBmD,UAAU,WAAW4E;YAErB,MAAMwB,UAAU,MAAM2G,WACpBC,gBACApI,SACA4D,eACAhB;YAEFvF,iBAAiBmE,OAAO,GAAGA;YAE3B,MAAMkI,eAA6B,MAAM9F,cACtCU,UAAU,CAAC,sBACXC,YAAY,CAAC,IAAMlN,iBAAiBuL;YAEvC,MAAM,EAAE+G,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;YACzC,MAAMI,mBAA8B;mBAC/BF,SAASG,WAAW;mBACpBH,SAASI,UAAU;mBACnBJ,SAASK,QAAQ;aACrB;YACD,MAAMC,cAAcJ,iBAAiBK,MAAM,GAAG;YAC9C9M,iBAAiB6M,WAAW,GAAGA;YAC/B7M,iBAAiB+M,gBAAgB,GAAGxH,OAAOyH,iBAAiB;YAC5DhN,iBAAiBiN,iBAAiB,GAAG1H,OAAO2H,kBAAkB;YAE9D,MAAMtK,WAAWF,YAAYC;YAE7B,MAAMwK,YAAY,IAAIhQ,UAAU;gBAAEwF;YAAQ;YAE1C5E,UAAU,aAAaoP;YAEvB,MAAMC,YAAYvU,KAAKgK,IAAI,CAACqH,KAAK;YACjC,MAAM,EAAEmD,QAAQ,EAAErG,MAAM,EAAE,GAAGjN,aAAamQ;YAC1ClK,iBAAiBqN,QAAQ,GAAGA;YAC5BrN,iBAAiBgH,MAAM,GAAGA;YAE1B,MAAMmD,qBAA6C;gBACjDmD,KAAK,OAAOtG,WAAW;gBACvBG,OAAO,OAAOkG,aAAa;YAC7B;YAEA,mDAAmD;YACnD,wFAAwF;YACxF,MAAME,gBAAgB,MAAMrM,4BAA4B;gBACtDsM,SAAS;gBACT7K;YACF;YACA3C,iBAAiBuN,aAAa,GAAGA;YAEjC,MAAME,WAAW5U,KACd+O,QAAQ,CAACsC,KAAKmD,YAAYrG,UAAU,IACpC0G,UAAU,CAAC;YACd,MAAMC,eAAevV,WAAWgV;YAEhCD,UAAUS,MAAM,CACd/Q,gBAAgBqN,KAAK3E,QAAQ;gBAC3BsI,gBAAgB;gBAChBC,YAAY;gBACZL;gBACAM,YAAY,CAAC,CAAE,MAAMpV,OAAO,YAAY;oBAAEqV,KAAK9D;gBAAI;gBACnD+D,gBAAgB;gBAChBC,WAAW;gBACXb,UAAU,CAAC,CAACA;gBACZrG,QAAQ,CAAC,CAACA;YACZ;YAGFjK,iBAAiBlE,KAAKkQ,OAAO,CAACmB,MAAMiE,IAAI,CAAC,CAACC,SACxCjB,UAAUS,MAAM,CAACQ;YAGnBhP,gBAAgBvG,KAAKkQ,OAAO,CAACmB,MAAM3E,QAAQ4I,IAAI,CAAC,CAACC,SAC/CjB,UAAUS,MAAM,CAACQ;YAGnB,qDAAqD;YACrD,MAAM,EAAEC,OAAO,EAAEC,oBAAoB,EAAE,GAAG,MAAMzN,mBAC9CqJ,KACA;YAEFpJ,aAAa;gBACXyN,YAAY;gBACZC,QAAQ;gBACRH;gBACAC;YACF;YAEA,MAAMG,eAAeC,QAAQnJ,OAAOoJ,MAAM,CAACC,kBAAkB;YAC7D,MAAMC,aAAa,CAACJ,gBAAgBtD;YAEpC,MAAM2D,sBAA+D;gBACnE5E;gBACAlD;gBACAqG;gBACAlC;gBACA0D;gBACAJ;gBACAtB;gBACA5G;gBACAhB;gBACA3C;YACF;YAEA,MAAMmM,iBAAiB,MAAMxI,cAC1BU,UAAU,CAAC,mBACXC,YAAY,CAAC;gBACZ,IAAI;oBACF,MAAM5O,GAAGuP,KAAK,CAAClF,SAAS;wBAAEoF,WAAW;oBAAK;oBAC1C,OAAO;gBACT,EAAE,OAAOiH,KAAK;oBACZ,IAAIpQ,QAAQoQ,QAAQA,IAAIC,IAAI,KAAK,SAAS;wBACxC,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YAEF,IAAI,CAACD,kBAAkB,CAAE,MAAMrR,YAAYiF,UAAW;gBACpD,MAAM,qBAEL,CAFK,IAAIuM,MACR,iGADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI3J,OAAO4J,YAAY,IAAI,CAACpE,gBAAgB;gBAC1C,MAAM5Q,gBAAgBwI,SAAS;YACjC;YAEA,sEAAsE;YACtE,oEAAoE;YACpE,aAAa;YACb,IAAI,CAACqE,UAAU,CAACyE,eACd,MAAMpL,kBAAkByO;YAE1B,IAAI9H,UAAU,mBAAmBzB,QAAQ;gBACvC5H,IAAIyR,KAAK,CACP;gBAEF,MAAMjC,UAAUkC,KAAK;gBACrBzF,QAAQ0F,IAAI,CAAC;YACf;YAEA,MAAMC,iBAAyC;gBAC7CC,aAAa;gBACbC,iBAAiBZ,aAAa,IAAI;YACpC;YACA1B,UAAUS,MAAM,CAAC;gBACf8B,WAAW1S;gBACX2S,SAASJ;YACX;YAEA,MAAMK,mBAAmBxP,uBACvBmF,OAAOsK,cAAc,EACrB7I;YAGF,MAAM8I,oBAA8BhM,KAAKC,KAAK,CAC5C6F,QAAQD,GAAG,CAACoG,uBAAuB,IAAI;YAGzC,IAAIC,aAAatB,QAAQ9E,QAAQD,GAAG,CAACoG,uBAAuB,IACxDD,oBACA,CAACzE,cAAcgC,WACb,MAAM9G,cAAcU,UAAU,CAAC,iBAAiBC,YAAY,CAAC,IAC3DnI,iBAAiBsO,UAAU;oBACzB4C,gBAAgBL,iBAAiBM,UAAU;gBAC7C,MAEF,EAAE;YAER,MAAMC,4BAA4B,IAAIC,OACpC,CAAC,CAAC,EAAEpX,oBAAoB,MAAM,EAAEuM,OAAOsK,cAAc,CAAChN,IAAI,CAAC,KAAK,EAAE,CAAC;YAGrE,MAAMwN,qCAAqC,IAAID,OAC7C,CAAC,CAAC,EAAElX,8BAA8B,MAAM,EAAEqM,OAAOsK,cAAc,CAAChN,IAAI,CAClE,KACA,EAAE,CAAC;YAGP,MAAMyN,UAAUzX,KAAKgK,IAAI,CAAEwK,YAAYrG,QAAU;YACjD,MAAMS,WAAW;gBACf0I;gBACAE;aACD;YAED,MAAME,YAAYC,MAAMC,IAAI,CAAC,MAAMtR,cAAcmR,UAC9C5L,MAAM,CAAC,CAAC0C,OAASK,SAASiJ,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAACxJ,QACzDnC,IAAI,CAAC1H,eAAegI,OAAOsK,cAAc,GACzCjL,GAAG,CAAC,CAACwC,OAASvO,KAAKgK,IAAI,CAACyN,SAASlJ,MAAMyJ,OAAO,CAAC3G,KAAK;YAEvD,MAAMrD,yBAAyB0J,UAAUG,IAAI,CAAC,CAAC7K,IAC7CA,EAAE4B,QAAQ,CAACvO;YAEb,MAAM4X,oBAAoBP,UAAUG,IAAI,CAAC,CAAC7K,IACxCA,EAAE4B,QAAQ,CAACzO;YAGbgH,iBAAiB6G,sBAAsB,GAAGA;YAE1C,MAAMkK,eAAkC;gBACtCC,eAAe9Y,OAAO+Y,WAAW,CAAC,IAAIC,QAAQ,CAAC;gBAC/CC,uBAAuBjZ,OAAO+Y,WAAW,CAAC,IAAIC,QAAQ,CAAC;gBACvDE,0BAA0BlZ,OAAO+Y,WAAW,CAAC,IAAIC,QAAQ,CAAC;YAC5D;YACAlR,iBAAiB+Q,YAAY,GAAGA;YAEhC,MAAM/E,cAAc,MAAMzF,cACvBU,UAAU,CAAC,wBACXC,YAAY,CAAC,IACZ7J,mBAAmB;oBACjBgU,OAAO;oBACPxB,gBAAgBtK,OAAOsK,cAAc;oBACrCyB,WAAW9T,WAAW+T,KAAK;oBAC3BC,WAAWxB;oBACX3C;oBACArG;gBACF;YAEJhH,iBAAiBgM,WAAW,GAAGA;YAE/B,IAAIyF;YACJ,IAAIhL;YAEJ,IAAIO,QAAQ;gBACV,MAAM0K,mBAA6B5N,KAAKC,KAAK,CAC3C6F,QAAQD,GAAG,CAACgI,sBAAsB,IAAI;gBAGxC,IAAIC,WAAWlD,QAAQ9E,QAAQD,GAAG,CAACgI,sBAAsB,IACrDD,mBACA,MAAMnL,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC,IACZnI,iBAAiBiI,QAAQ;wBACvBiJ,gBAAgB,CAAC4B,eACfjC,iBAAiBkC,eAAe,CAACD,iBACjC,8DAA8D;4BAC9D,gCAAgC;4BAChCjC,iBAAiBmC,cAAc,CAACF;wBAClCG,kBAAkB,CAACC,OAASA,KAAKvE,UAAU,CAAC;oBAC9C;gBAGR+D,iBAAiB,MAAMlL,cACpBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZ7J,mBAAmB;wBACjBmU,WAAWI;wBACXP,OAAO;wBACPC,WAAW9T,WAAW0U,GAAG;wBACzBrC,gBAAgBtK,OAAOsK,cAAc;wBACrCxC;wBACArG;oBACF;gBAGJhH,iBAAiByR,cAAc,GAAGA;YACpC;YAEA,MAAMU,kBAAkB,MAAM9U,mBAAmB;gBAC/CgU,OAAO;gBACPxB,gBAAgBtK,OAAOsK,cAAc;gBACrC2B,WAAWjB;gBACXe,WAAW9T,WAAW4U,IAAI;gBAC1B/E,UAAUA;gBACVrG;YACF;YACAhH,iBAAiBmS,eAAe,GAAGA;YAEnC,MAAME,gBAAgB9N,OAAOQ,IAAI,CAACiH;YAElC,MAAMsG,0BAAiE,EAAE;YACzE,MAAMC,cAAc,IAAIjO;YACxB,IAAImN,gBAAgB;gBAClBhL,uBAAuBlC,OAAOQ,IAAI,CAAC0M;gBACnC,KAAK,MAAMe,UAAU/L,qBAAsB;oBACzC,MAAMgM,uBAAuBpT,iBAAiBmT;oBAC9C,MAAME,WAAW1G,WAAW,CAACyG,qBAAqB;oBAClD,IAAIC,UAAU;wBACZ,MAAMC,UAAUlB,cAAc,CAACe,OAAO;wBACtCF,wBAAwB5K,IAAI,CAAC;4BAC3BgL,SAAS7B,OAAO,CAAC,uBAAuB;4BACxC8B,QAAQ9B,OAAO,CAAC,yBAAyB;yBAC1C;oBACH;oBACA0B,YAAYK,GAAG,CAACH;gBAClB;YACF;YAEA,MAAMb,WAAWpB,MAAMC,IAAI,CAAC8B;YAC5B,2DAA2D;YAC3DhG,SAASG,WAAW,CAAChF,IAAI,IACpBpH,mCAAmCsR,UAAUrM,OAAOsN,QAAQ;YAGjE7S,iBAAiBuM,QAAQ,GAAGA;YAE5B,MAAMuG,qBAAqBlB,SAAS9E,MAAM;YAE1C,MAAMtG,WAAW;gBACfW,OAAOkL;gBACP/E,KAAKsE,SAAS9E,MAAM,GAAG,IAAI8E,WAAWjG;YACxC;YAEA,6DAA6D;YAC7D,IAAI,CAACL,gBAAgB;gBACnB,MAAMyH,yBAAyBT,wBAAwBxF,MAAM;gBAC7D,IAAI2E,kBAAkBsB,yBAAyB,GAAG;oBAChDpV,IAAIyR,KAAK,CACP,CAAC,6BAA6B,EAC5B2D,2BAA2B,IAAI,SAAS,SACzC,wDAAwD,CAAC;oBAE5D,KAAK,MAAM,CAACL,UAAUC,QAAQ,IAAIL,wBAAyB;wBACzD3U,IAAIyR,KAAK,CAAC,CAAC,GAAG,EAAEsD,SAAS,KAAK,EAAEC,QAAQ,CAAC,CAAC;oBAC5C;oBACA,MAAMxF,UAAUkC,KAAK;oBACrBzF,QAAQ0F,IAAI,CAAC;gBACf;YACF;YAEA,MAAM0D,yBAAmC,EAAE;YAC3C,MAAMC,eAAcjH,mBAAAA,WAAW,CAAC,OAAO,qBAAnBA,iBAAqB0B,UAAU,CAACzU;YACpD,MAAMia,YAAY,CAAC,EAACzB,kCAAAA,cAAgB,CAAC3V,iCAAiC;YACtE,MAAMqX,qBACJnH,WAAW,CAAC,UAAU,CAAC0B,UAAU,CAACzU;YAEpC,IAAI0U,cAAc;gBAChB,MAAMyF,6BAA6Bhb,WACjCS,KAAKgK,IAAI,CAACuK,WAAW;gBAEvB,IAAIgG,4BAA4B;oBAC9B,MAAM,qBAAyC,CAAzC,IAAIlE,MAAMnW,iCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwC;gBAChD;YACF;YAEA,MAAMwN,cACHU,UAAU,CAAC,6BACXC,YAAY,CAAC;gBACZ,iDAAiD;gBACjD,sDAAsD;gBACtD,IAAK,MAAMhF,QAAQ8J,YAAa;oBAC9B,MAAMqH,oBAAoB,MAAMvZ,WAC9BjB,KAAKgK,IAAI,CAACuK,WAAWlL,SAAS,MAAM,WAAWA,OAC/CrI,SAASyZ,IAAI;oBAEf,IAAID,mBAAmB;wBACrBL,uBAAuBtL,IAAI,CAACxF;oBAC9B;gBACF;gBAEA,MAAMqR,iBAAiBP,uBAAuBlG,MAAM;gBAEpD,IAAIyG,gBAAgB;oBAClB,MAAM,qBAML,CANK,IAAIrE,MACR,CAAC,gCAAgC,EAC/BqE,mBAAmB,IAAI,SAAS,SACjC,uEAAuE,EAAEP,uBAAuBnQ,IAAI,CACnG,OACC,GALC,qBAAA;+BAAA;oCAAA;sCAAA;oBAMN;gBACF;YACF;YAEF,MAAM2Q,sBAAsBhN,SAASW,KAAK,CAACzC,MAAM,CAAC,CAACxC;gBACjD,OACEA,KAAKuR,KAAK,CAAC,iCAAiC5a,KAAKiP,OAAO,CAAC5F,UAAU;YAEvE;YAEA,IAAIsR,oBAAoB1G,MAAM,EAAE;gBAC9BnP,IAAIyF,IAAI,CACN,CAAC,4FAA4F,CAAC,GAC5FoQ,oBAAoB3Q,IAAI,CAAC,QACzB,CAAC,6EAA6E,CAAC;YAErF;YAEA,MAAM6Q,0BAA0B;gBAAC;aAAS,CAAC9O,GAAG,CAAC,CAACiB,IAC9CN,OAAOsN,QAAQ,GAAG,GAAGtN,OAAOsN,QAAQ,GAAGhN,GAAG,GAAGA;YAG/C,MAAM8N,wBAAwBjF,QAAQnJ,OAAO8C,YAAY,CAACuL,SAAS;YACnE,MAAMC,0BAA0BnF,QAC9BnJ,OAAO8C,YAAY,CAACyL,cAAc;YAEpC,MAAMC,kBAAkB3S,qBAAqBmE,OAAO8C,YAAY,CAAC2L,GAAG;YAEpE,MAAMC,qBAAqBpb,KAAKgK,IAAI,CAACF,SAAS5H;YAC9C,MAAMmZ,iBAAiC3N,cACpCU,UAAU,CAAC,4BACXgF,OAAO,CAAC;gBACP,MAAMkI,eAAejY,gBAAgB;uBAChCsK,SAASW,KAAK;uBACbX,SAAS8G,GAAG,IAAI,EAAE;iBACvB;gBACD,MAAMtI,gBAAuD,EAAE;gBAC/D,MAAMoP,eAAqC,EAAE;gBAE7C,KAAK,MAAMvP,SAASsP,aAAc;oBAChC,IAAIhY,eAAe0I,QAAQ;wBACzBG,cAAc0C,IAAI,CAACzF,YAAY4C;oBACjC,OAAO,IAAI,CAACvG,eAAeuG,QAAQ;wBACjCuP,aAAa1M,IAAI,CAACzF,YAAY4C;oBAChC;gBACF;gBAEA,OAAO;oBACLuB,SAAS;oBACTiO,UAAU;oBACVC,eAAe,CAAC,CAAC/O,OAAO8C,YAAY,CAACkM,mBAAmB;oBACxD1B,UAAUtN,OAAOsN,QAAQ;oBACzBrG,WAAWA,UAAU5H,GAAG,CAAC,CAAC4P,IACxBxT,iBAAiB,YAAYwT,GAAGd;oBAElCpH,SAASA,QAAQ1H,GAAG,CAAC,CAAC4P,IAAMxT,iBAAiB,UAAUwT;oBACvDxP;oBACAoP;oBACAK,YAAY,EAAE;oBACdC,MAAMnP,OAAOmP,IAAI,IAAI/I;oBACrBgJ,KAAK;wBACHC,QAAQpV;wBACR,yFAAyF;wBACzF,4DAA4D;wBAC5DqV,YAAY,GAAGrV,WAAW,EAAE,EAAEE,8BAA8B,EAAE,EAAEH,4BAA4B,EAAE,EAAEK,qCAAqC;wBACrIkV,gBAAgBvV;wBAChBwV,mBAAmBpV;wBACnBqV,mBAAmBvV;wBACnBwV,QAAQ7b;wBACR8b,gBAAgB/b;wBAChBgc,uBAAuBvV;wBACvBwV,uBAAuBxb;wBACvByb,0BAA0B1b;oBAC5B;oBACA2b,gBAAgB;wBACdC,YAAY1V;wBACZ2V,aAAa1V;oBACf;oBACA2V,4BAA4BlQ,OAAOkQ,0BAA0B;oBAC7DzB,KAAKD,kBACD;wBACE2B,OAAO;4BACLpJ,SAAS;gCACP,CAACjT,mBAAmB,EAAE;4BACxB;wBACF;oBACF,IACAsS;gBACN;YACF;YAEF,IAAIY,SAASG,WAAW,CAACI,MAAM,KAAK,KAAKP,SAASK,QAAQ,CAACE,MAAM,KAAK,GAAG;gBACvEoH,eAAe3H,QAAQ,GAAGA,SAASI,UAAU,CAAC/H,GAAG,CAAC,CAAC4P,IACjDxT,iBAAiB,WAAWwT;YAEhC,OAAO;gBACLN,eAAe3H,QAAQ,GAAG;oBACxBG,aAAaH,SAASG,WAAW,CAAC9H,GAAG,CAAC,CAAC4P,IACrCxT,iBAAiB,WAAWwT;oBAE9B7H,YAAYJ,SAASI,UAAU,CAAC/H,GAAG,CAAC,CAAC4P,IACnCxT,iBAAiB,WAAWwT;oBAE9B5H,UAAUL,SAASK,QAAQ,CAAChI,GAAG,CAAC,CAAC4P,IAC/BxT,iBAAiB,WAAWwT;gBAEhC;YACF;YACA,IAAImB;YAIJ,IAAIpQ,OAAO8C,YAAY,CAACuN,kBAAkB,EAAE;gBAC1C,MAAMC,uBAAuB,AAACtQ,CAAAA,OAAO2H,kBAAkB,IAAI,EAAE,AAAD,EAAGxI,MAAM,CACnE,CAAC8P,IAAW,CAACA,EAAEsB,QAAQ;gBAEzBH,sBAAsBxV,yBACpB;uBAAIyR;iBAAS,EACbrM,OAAO8C,YAAY,CAAC0N,2BAA2B,GAC3CF,uBACA,EAAE,EACNtQ,OAAO8C,YAAY,CAAC2N,6BAA6B;gBAEnDhW,iBAAiB2V,mBAAmB,GAAGA;YACzC;YAEA,8EAA8E;YAC9E,uDAAuD;YACvD,MAAMtS,cACJxK,KAAKgK,IAAI,CAACF,SAAS,iBACnB;YAGF,yFAAyF;YACzF,MAAMjC,uBAAuBkJ,QAAQD,GAAG,CAACoC,cAAc;YACvD,MAAMpL,uBAAuB;gBAC3BsV,YAAY;YACd;YAEA,MAAMvP,wBAAwBnB,OAAOmB,qBAAqB,IAAIwD;YAE9D,MAAMgM,oBAAoBrd,KAAKgK,IAAI,CACjCF,SACA3H,kBACAL;YAGF,IAAIwb;YACJ,IAAIC,qBAA+CzK;YAEnD,uEAAuE;YACvE,4CAA4C;YAC5C,MAAM0K,iBACJ9Q,OAAO8C,YAAY,CAACiO,kBAAkB,IACrC/Q,OAAO8C,YAAY,CAACiO,kBAAkB,KAAK3K,aAC1C,CAACpG,OAAOgR,OAAO;YACnB,MAAMC,6BACJjR,OAAO8C,YAAY,CAACoO,sBAAsB;YAC5C,MAAMC,qCACJnR,OAAO8C,YAAY,CAACsO,yBAAyB,IAC5CpR,OAAO8C,YAAY,CAACsO,yBAAyB,KAAKhL,aACjDF;YAEJlF,cAAcqQ,YAAY,CACxB,6BACA9K,OAAO,CAAC,CAACvG,OAAOgR,OAAO;YAEzBhQ,cAAcqQ,YAAY,CAAC,oBAAoB9K,OAAOuK;YAEtD,IACE,CAACA,kBACAG,CAAAA,8BAA8BE,kCAAiC,GAChE;gBACA,MAAM,qBAEL,CAFK,IAAIxH,MACR,oMADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAvR,IAAIkZ,IAAI,CAAC;YACT5V,iBAAiB,kBAAkBsF;YAEnC,MAAM5F,uBAAuB;gBAC3BsV,YAAY;gBACZa,cAAc;oBACZT,gBAAgBvK,OAAOuK;gBACzB;YACF;YAEA,IAAIU,kBAAkBC,QAAQjO,OAAO;YACrC,IAAI,CAACgC,gBAAgB;gBACnB,IAAIO,gBAAgB;oBAClB,MAAM,EACJ2L,UAAUC,gBAAgB,EAC1BH,iBAAiBlR,CAAC,EAClB,GAAGsR,MACJ,GAAG,MAAMpV,eACR6H,QAAQD,GAAG,CAACyN,yBAAyB,KAAKzL,aACxC/B,QAAQD,GAAG,CAACyN,yBAAyB,KAAK;oBAE9CL,kBAAkBlR;oBAClB5E,iBAAiB,kBAAkBsF;oBAEnC4P,oBAAoBgB,KAAKhB,iBAAiB;oBAE1C,MAAMkB,iBAAiBC,iBAAiBJ;oBACxCvZ,IAAI4Z,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;oBAEtDlK,UAAUS,MAAM,CACd1Q,oBAAoB8S,YAAY;wBAC9BwH,mBAAmBhP,KAAKiP,KAAK,CAACP;wBAC9BpE;oBACF;gBAEJ,OAAO;oBACL,IACE0D,8BACAE,oCACA;wBACA,IAAIc,oBAAoB;wBAExB,MAAM7W,uBAAuB;4BAC3BsV,YAAY;wBACd;wBAEA,MAAMyB,qBAAqB3X,aAAasW,gBAAgB;4BACtD;yBACD,EAAElI,IAAI,CAAC,CAACwJ;4BACP1W,iBAAiB,+BAA+BsF;4BAChD4P,oBAAoBwB,IAAIxB,iBAAiB;4BACzCqB,qBAAqBG,IAAIV,QAAQ;4BAEjC,IAAIP,oCAAoC;gCACtC,MAAMkB,mBAAmB,IAAIpf,OAC3BsQ,QAAQC,OAAO,CAAC,2BAChB;oCACEM,YAAY;oCACZW,gBAAgB;wCAAC;qCAAqB;gCACxC;gCAGFoM,qBAAqBwB,iBAClBpX,kBAAkB,CAAC;oCAClB0J;oCACA3E;oCACA5C;oCACA,+CAA+C;oCAC/CkV,mBAAmBrZ,8BAA8B,IAAIsZ;oCACrDhR,aAAa,EAAE;oCACfiR,gBAAgB;oCAChB5B;oCACAzP;gCACF,GACCsR,KAAK,CAAC,CAAChJ;oCACN/L,QAAQmM,KAAK,CAACJ;oCACdpF,QAAQ0F,IAAI,CAAC;gCACf;4BACJ;wBACF;wBACA,IAAI,CAACkH,4BAA4B;4BAC/B,MAAMkB;4BACN,MAAM/W,uBAAuB;gCAC3BsV,YAAY;4BACd;wBACF;wBAEA,MAAMgC,mBAAmBlY,aAAasW,gBAAgB;4BACpD;yBACD,EAAElI,IAAI,CAAC,CAACwJ;4BACPH,qBAAqBG,IAAIV,QAAQ;4BACjChW,iBACE,oCACAsF;wBAEJ;wBACA,IAAIiQ,4BAA4B;4BAC9B,MAAMkB;4BACN,MAAM/W,uBAAuB;gCAC3BsV,YAAY;4BACd;wBACF;wBACA,MAAMgC;wBAEN,MAAMtX,uBAAuB;4BAC3BsV,YAAY;wBACd;wBAEA,MAAMlW,aAAasW,gBAAgB;4BAAC;yBAAS,EAAElI,IAAI,CAAC,CAACwJ;4BACnDH,qBAAqBG,IAAIV,QAAQ;4BACjChW,iBAAiB,+BAA+BsF;wBAClD;wBAEA,MAAM8Q,iBAAiBC,iBAAiBE;wBACxC7Z,IAAI4Z,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;wBAEtDlK,UAAUS,MAAM,CACd1Q,oBAAoB8S,YAAY;4BAC9BwH;4BACA1E;wBACF;oBAEJ,OAAO;wBACL,MAAM,EAAEmE,UAAUC,gBAAgB,EAAE,GAAGC,MAAM,GAAG,MAAMpX,aACpDsW,gBACA;wBAEFpV,iBAAiB,kBAAkBsF;wBAEnC4P,oBAAoBgB,KAAKhB,iBAAiB;wBAE1ChJ,UAAUS,MAAM,CACd1Q,oBAAoB8S,YAAY;4BAC9BwH,mBAAmBN;4BACnBpE;wBACF;oBAEJ;gBACF;YACF;YAEA,uDAAuD;YACvD,IAAI9L,UAAU,CAACyE,iBAAiB,CAACV,gBAAgB;gBAC/C,MAAMpK,uBAAuB;oBAC3BsV,YAAY;gBACd;gBACA,MAAM5V,kBAAkByO;gBACxB7N,iBAAiB,0BAA0BsF;YAC7C;YAEA,MAAM2R,qBAAqBta,cAAc;YAEzC,MAAMua,oBAAoBtf,KAAKgK,IAAI,CAACF,SAASrI;YAC7C,MAAM8d,uBAAuBvf,KAAKgK,IAAI,CAACF,SAASrH;YAEhD,IAAI+c,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,wBAAwB;YAC5B,MAAMnU,WAAW,IAAIC;YACrB,MAAMmU,yBAAyB,IAAInU;YACnC,MAAMoU,2BAA2B,IAAIpU;YACrC,MAAMwC,cAAc,IAAIxC;YACxB,MAAMqU,eAAe,IAAIrU;YACzB,MAAMsU,iBAAiB,IAAItU;YAC3B,MAAMuU,mBAAmB,IAAIvU;YAC7B,MAAMwU,kBAAkB,IAAIhB;YAC5B,MAAMiB,cAAc,IAAIjB;YACxB,MAAMkB,qBAAqB,IAAIlB;YAI/B,MAAMmB,qBAAqB,IAAInB;YAC/B,MAAMoB,gBAAgB,IAAIpB;YAC1B,MAAMqB,oBAAoB,IAAIrB;YAC9B,MAAMsB,YAAuB,IAAItB;YACjC,IAAIuB,gBAAgB,MAAMxV,aAA4BqS;YACtD,MAAMoD,gBAAgB,MAAMzV,aAA4BsU;YACxD,MAAMoB,mBAAmBvS,SACrB,MAAMnD,aAA+BuU,wBACrCzM;YAEJ,MAAM6N,gBAAwC,CAAC;YAE/C,IAAIxS,QAAQ;gBACV,MAAMyS,mBAAmB,MAAM5V,aAC7BhL,KAAKgK,IAAI,CAACF,SAAS3H,kBAAkBI;gBAGvC,IAAK,MAAMse,OAAOD,iBAAkB;oBAClCD,aAAa,CAACE,IAAI,GAAGra,iBAAiBqa;gBACxC;gBAEA,MAAM/V,cACJ9K,KAAKgK,IAAI,CAACF,SAAStH,2BACnBme;YAEJ;YAEA5P,QAAQD,GAAG,CAACgQ,UAAU,GAAG/e;YAEzB,MAAMgf,SAAS3Q,mBAAmB1D;YAElC,MAAMsU,gBAAgBjQ,QAAQkQ,MAAM;YACpC,MAAMC,kBAAkBxT,cAAcU,UAAU,CAAC;YAEjD,MAAM+S,0BAAmD;gBACvD5T,SAAS;gBACT6T,WAAW,CAAC;YACd;YAEA,MAAM,EACJC,wBAAwB,EACxBC,YAAY,EACZC,mBAAmB,EACnBrC,cAAc,EACdsC,qBAAqB,EACtB,GAAG,MAAMN,gBAAgB7S,YAAY,CAAC;oBAcV3B;gBAb3B,IAAIkG,eAAe;oBACjB,OAAO;wBACLyO,0BAA0B;wBAC1BC,cAAc,EAAE;wBAChBC,qBAAqB;wBACrBrC,gBAAgB,CAAC,CAAC1K;wBAClBgN,uBAAuB;oBACzB;gBACF;gBAEA,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE,GAChEjV;gBACF,MAAMkV,mBAAmB;oBAAEF;oBAAqBC;gBAAoB;gBACpE,MAAME,aAAahM,SAAQnJ,2BAAAA,OAAO8C,YAAY,CAACsS,GAAG,qBAAvBpV,yBAAyBqV,SAAS;gBAE7D,MAAMC,yBAAyBd,gBAAgB9S,UAAU,CACvD;gBAEF,MAAM6T,oCACJD,uBAAuB3T,YAAY,CACjC,UACEiM,sBACC,MAAMyG,OAAOmB,wBAAwB,CAAC;wBACrC7Y,MAAM;wBACNS;wBACA8X;wBACAO,aAAa;wBACbN;oBACF;gBAGN,MAAMO,wBAAwBJ,uBAAuB3T,YAAY,CAC/D;wBAWa3B,cACMA;2BAXjB4N,sBACAyG,OAAOsB,YAAY,CAAC;wBAClBhR;wBACAhI,MAAM;wBACNS;wBACA2X;wBACAG;wBACA7G,WAAWD;wBACXG,gBAAgBD;wBAChBsH,kBAAkB5V,OAAO4V,gBAAgB;wBACzC/W,OAAO,GAAEmB,eAAAA,OAAOmP,IAAI,qBAAXnP,aAAanB,OAAO;wBAC7BgX,aAAa,GAAE7V,gBAAAA,OAAOmP,IAAI,qBAAXnP,cAAa6V,aAAa;wBACzCC,kBAAkB9V,OAAO+V,MAAM;wBAC/BC,WAAWhW,OAAO8C,YAAY,CAAC2L,GAAG;wBAClCwH,mBAAmBjW,OAAO8C,YAAY,CAACoT,SAAS;wBAChDtX;wBACAuW;oBACF;;gBAGJ,MAAMgB,iBAAiB;gBAEvB,MAAMC,kCAAkC/B,OAAOmB,wBAAwB,CACrE;oBACE7Y,MAAMwZ;oBACN/Y;oBACA8X;oBACAO,aAAa;oBACbN;gBACF;gBAGF,MAAMkB,sBAAsBhC,OAAOiC,sBAAsB,CAAC;oBACxD3Z,MAAMwZ;oBACN/Y;oBACA8X;oBACAC;gBACF;gBAEA,wDAAwD;gBACxD,IAAIN;gBACJ,wDAAwD;gBACxD,IAAIrC,iBAAiB;gBAErB,MAAM+D,uBAAuB,MAAM7d,oBACjC;oBAAE+M,OAAOsO;oBAAehM,KAAKiM;gBAAiB,GAC9C5W,SACA4C,OAAO8C,YAAY,CAAC0T,QAAQ;gBAG9B,MAAMpV,qBAAyCmC,QAC7CjQ,KAAKgK,IAAI,CAACF,SAAS3H,kBAAkBG;gBAGvC,MAAM6gB,iBAAiBhV,SAClB8B,QACCjQ,KAAKgK,IAAI,CACPF,SACA3H,kBACAY,4BAA4B,YAGhC;gBACJ,MAAMqgB,oBAAoBD,iBAAiB,IAAI1X,QAAQ;gBACvD,IAAI0X,kBAAkBC,mBAAmB;oBACvC,IAAK,MAAMC,MAAMF,eAAeG,IAAI,CAAE;wBACpC,IAAK,MAAMC,SAASJ,eAAeG,IAAI,CAACD,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBrJ,GAAG,CAACwJ;wBACxB;oBACF;oBACA,IAAK,MAAMF,MAAMF,eAAeM,IAAI,CAAE;wBACpC,IAAK,MAAMF,SAASJ,eAAeM,IAAI,CAACJ,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBrJ,GAAG,CAACwJ;wBACxB;oBACF;gBACF;gBAEA,KAAK,MAAM1C,OAAOnV,OAAOQ,IAAI,CAAC4B,sCAAAA,mBAAoBsT,SAAS,EAAG;oBAC5D,IAAIP,IAAIhM,UAAU,CAAC,SAAS;wBAC1B8K;oBACF;gBACF;gBAEA,MAAMxB,QAAQuF,GAAG,CACfhY,OAAOC,OAAO,CAACgC,UACZc,MAAM,CACL,CAACC,KAAK,CAACmS,KAAKrS,MAAM;oBAChB,IAAI,CAACA,OAAO;wBACV,OAAOE;oBACT;oBAEA,MAAMiV,WAAW9C;oBAEjB,KAAK,MAAMxX,QAAQmF,MAAO;wBACxBE,IAAIG,IAAI,CAAC;4BAAE8U;4BAAUta;wBAAK;oBAC5B;oBAEA,OAAOqF;gBACT,GACA,EAAE,EAEH3C,GAAG,CAAC,CAAC,EAAE4X,QAAQ,EAAEta,IAAI,EAAE;oBACtB,MAAMua,gBAAgB1C,gBAAgB9S,UAAU,CAAC,cAAc;wBAC7D/E;oBACF;oBACA,OAAOua,cAAcvV,YAAY,CAAC;wBAChC,MAAMwV,aAAapgB,kBAAkB4F;wBACrC,MAAM,CAACya,MAAMC,UAAU,GAAG,MAAM1e,kBAC9Bse,UACAE,YACA/Z,SACA2W,eACAC,kBACAhU,OAAO8C,YAAY,CAAC0T,QAAQ,EAC5BD;wBAGF,IAAIe,oBAAoB;wBACxB,IAAIC,QAAQ;wBACZ,IAAIC,WAAW;wBACf,IAAIC,oBAAoB;wBACxB,IAAIC,cAAc;wBAClB,IAAIC,gBAAiC;wBACrC,IAAIxK,WAAW;wBAEf,IAAI8J,aAAa,SAAS;4BACxB9J,WACE1C,WAAWmN,IAAI,CAAC,CAACtX;gCACfA,IAAI5F,iBAAiB4F;gCACrB,OACEA,EAAE6H,UAAU,CAACgP,aAAa,QAC1B7W,EAAE6H,UAAU,CAACgP,aAAa;4BAE9B,MAAM;wBACV;wBACA,IAAIU;wBAEJ,IAAIZ,aAAa,SAAS/K,gBAAgB;4BACxC,KAAK,MAAM,CAAC4L,cAAcC,eAAe,IAAI/Y,OAAOC,OAAO,CACzDgV,eACC;gCACD,IAAI8D,mBAAmBpb,MAAM;oCAC3BwQ,WAAWjB,cAAc,CAAC4L,aAAa,CAACxM,OAAO,CAC7C,yBACA;oCAEFuM,kBAAkBC;oCAClB;gCACF;4BACF;wBACF;wBAEA,MAAME,eAAehf,yBAAyBmU,YAC1C5J,QAAQC,OAAO,CACb,iDAEFlQ,KAAKgK,IAAI,CACP,AAAC2Z,CAAAA,aAAa,UAAUnP,WAAWrG,MAAK,KAAM,IAC9C0L;wBAGN,MAAM8K,iBAAiBhB,aAAa;wBACpC,MAAMiB,aAAa/K,WACf,MAAMpV,8BAA8B;4BAClCkgB;4BACAD;4BACA1N,gBAAgBtK,OAAOsK,cAAc;4BACrC7I;4BACAzB;4BACA8L,OAAO;4BACP,yDAAyD;4BACzD,4DAA4D;4BAC5D,gEAAgE;4BAChEnP,MAAMsb,iBAAiBJ,kBAAmBlb;wBAC5C,KACAyJ;wBAEJ,8DAA8D;wBAC9D,oDAAoD;wBACpD,IACE,QAAO8R,8BAAAA,WAAYC,OAAO,MAAK,eAC/B,QAAOD,8BAAAA,WAAYE,WAAW,MAAK,aACnC;4BACA3D,wBAAwBC,SAAS,CAAC/X,KAAK,GAAG;gCACxCyb,WAAW,EAAEF,8BAAAA,WAAYE,WAAW;4BACtC;wBACF;wBAEA,MAAMC,cAAcjX,mBAAmBsT,SAAS,CAC9CmD,mBAAmBlb,KACpB,GACG,SACAub,8BAAAA,WAAYC,OAAO;wBAEvB,IAAI,CAACjS,eAAe;4BAClBuR,oBACER,aAAa,SACbiB,CAAAA,8BAAAA,WAAY9I,GAAG,MAAKpZ,iBAAiBsiB,MAAM;4BAE7C,IAAIrB,aAAa,SAAS,CAACle,eAAe4D,OAAO;gCAC/C,IAAI;oCACF,IAAI4b;oCAEJ,IAAIjf,cAAc+e,cAAc;wCAC9B,IAAIpB,aAAa,OAAO;4CACtBjE;wCACF,OAAO;4CACLC;wCACF;wCAEA,MAAMuF,cACJvB,aAAa,UAAUta,OAAOkb,mBAAmB;wCAEnDU,WAAWnX,mBAAmBsT,SAAS,CAAC8D,YAAY;oCACtD;oCAEA,IAAIC,mBACFvB,cAAcxV,UAAU,CAAC;oCAC3B,IAAIgX,eAAe,MAAMD,iBAAiB9W,YAAY,CACpD;4CASa3B,cACMA;wCATjB,OAAOqU,OAAOsB,YAAY,CAAC;4CACzBhR;4CACAhI;4CACAkb;4CACAza;4CACA2X;4CACAG;4CACAU,kBAAkB5V,OAAO4V,gBAAgB;4CACzC/W,OAAO,GAAEmB,eAAAA,OAAOmP,IAAI,qBAAXnP,aAAanB,OAAO;4CAC7BgX,aAAa,GAAE7V,gBAAAA,OAAOmP,IAAI,qBAAXnP,cAAa6V,aAAa;4CACzC8C,UAAUF,iBAAiBG,KAAK;4CAChCP;4CACAE;4CACAtB;4CACA5I,WAAWD;4CACXG,gBAAgBD;4CAChBuK,cAAc7Y,OAAO6Y,YAAY;4CACjCC,eAAe9Y,OAAO8C,YAAY,CAACgW,aAAa;4CAChDC,gBAAgB9hB,cAAcuG,cAAc,GACxC,QACAwC,OAAO8C,YAAY,CAACiW,cAAc;4CACtCC,oBAAoBhZ,OAAOiZ,kBAAkB;4CAC7CnD,kBAAkB9V,OAAO+V,MAAM;4CAC/BC,WAAWhW,OAAO8C,YAAY,CAAC2L,GAAG;4CAClCwH,mBAAmBjW,OAAO8C,YAAY,CAACoT,SAAS;4CAChDtX;4CACAuW;wCACF;oCACF;oCAGF,IAAI8B,aAAa,SAASY,iBAAiB;wCACzCnE,mBAAmBwF,GAAG,CAACrB,iBAAiBlb;wCACxC,0CAA0C;wCAC1C,IAAIrD,cAAc+e,cAAc;4CAC9Bb,WAAW;4CACXD,QAAQ;4CAERnf,IAAI+gB,QAAQ,CACV,CAAC,+EAA+E,CAAC;wCAErF,OAAO;4CACL,MAAMC,YAAYxiB,eAAe+F;4CAEjC,IACE,OAAO+b,aAAapB,iBAAiB,KAAK,WAC1C;gDACAA,oBAAoBoB,aAAapB,iBAAiB;4CACpD;4CAEA,oDAAoD;4CACpD,0CAA0C;4CAC1C,yBAAyB;4CACzB,IAAIoB,aAAapB,iBAAiB,EAAE;gDAClCC,QAAQ;gDACRC,WAAW;gDAEXhE,YAAY0F,GAAG,CAACrB,iBAAiB,EAAE;4CACrC,OAOK,IAAI7X,OAAO8C,YAAY,CAACuL,SAAS,IAAI+K,WAAW;gDACnD3F,mBAAmByF,GAAG,CAACrB,iBAAiB;oDACtClb;oDACAkb;gDACF;4CACF;4CAEA,IAAIa,aAAaW,iBAAiB,EAAE;gDAClC7F,YAAY0F,GAAG,CACbrB,iBACAa,aAAaW,iBAAiB;gDAEhC1B,gBAAgBe,aAAaW,iBAAiB,CAACha,GAAG,CAChD,CAACC,QAAUA,MAAMC,QAAQ;gDAE3BgY,QAAQ;4CACV;4CAEA,MAAM+B,YAAYZ,aAAaY,SAAS,IAAI,CAAC;4CAC7C,IAAIA,UAAUC,UAAU,KAAK,GAAG;gDAC9B,MAAMC,0BACJd,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC9R,MAAM,GAAG;gDAE1C,IACEvH,OAAO+V,MAAM,KAAK,YAClBqD,aACA,CAACI,yBACD;oDACA,MAAM,qBAEL,CAFK,IAAI7P,MACR,CAAC,MAAM,EAAEhN,KAAK,wFAAwF,CAAC,GADnG,qBAAA;+DAAA;oEAAA;sEAAA;oDAEN;gDACF;gDAEA,6BAA6B;gDAC7B,4BAA4B;gDAC5B,iEAAiE;gDACjE,8BAA8B;gDAC9B,IAAI,CAACyc,WAAW;oDACd5F,YAAY0F,GAAG,CAACrB,iBAAiB;wDAC/B;4DACEtY,UAAU5C;4DACV8c,iBAAiB9c;4DACjB+c,qBAAqBtT;4DACrBuT,cACEjB,aAAakB,qBAAqB;4DACpCC,oBAAoBzT;wDACtB;qDACD;oDACDoR,WAAW;gDACb,OAAO,IACL,CAACgC,2BACAF,CAAAA,UAAUQ,OAAO,KAAK,WACrBR,UAAUQ,OAAO,KAAK,cAAa,GACrC;oDACAtG,YAAY0F,GAAG,CAACrB,iBAAiB,EAAE;oDACnCL,WAAW;oDACXF,oBAAoB;gDACtB;4CACF;4CAEA,IAAIoB,aAAakB,qBAAqB,EAAE;gDACtCjG,cAAcuF,GAAG,CACfrB,iBACAa,aAAakB,qBAAqB;4CAEtC;4CAEAhG,kBAAkBsF,GAAG,CAACrB,iBAAiByB;wCACzC;oCACF,OAAO;wCACL,IAAIhgB,cAAc+e,cAAc;4CAC9B,IAAIK,aAAaqB,cAAc,EAAE;gDAC/Brc,QAAQG,IAAI,CACV,CAAC,kFAAkF,EAAElB,MAAM;4CAE/F;4CACA,mDAAmD;4CACnD,8CAA8C;4CAC9C+b,aAAalB,QAAQ,GAAG;4CACxBkB,aAAaqB,cAAc,GAAG;wCAChC;wCAEA,IACErB,aAAalB,QAAQ,KAAK,SACzBkB,CAAAA,aAAahB,WAAW,IAAIgB,aAAasB,SAAS,AAAD,GAClD;4CACAxH,iBAAiB;wCACnB;wCAEA,IAAIkG,aAAahB,WAAW,EAAE;4CAC5BA,cAAc;4CACdrE,eAAehG,GAAG,CAAC1Q;wCACrB;wCAEA,IAAI+b,aAAa7D,mBAAmB,EAAE;4CACpCA,sBAAsB;wCACxB;wCAEA,IAAI6D,aAAaqB,cAAc,EAAE;4CAC/Bjb,SAASuO,GAAG,CAAC1Q;4CACb4a,QAAQ;4CAER,IACEmB,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC9R,MAAM,GAAG,GACxC;gDACAgM,gBAAgB2F,GAAG,CACjBvc,MACA+b,aAAaW,iBAAiB;gDAEhC1B,gBAAgBe,aAAaW,iBAAiB,CAACha,GAAG,CAChD,CAACC,QAAUA,MAAMC,QAAQ;4CAE7B;4CAEA,IACEmZ,aAAakB,qBAAqB,KAClC7d,aAAake,sBAAsB,EACnC;gDACA9G,yBAAyB9F,GAAG,CAAC1Q;4CAC/B,OAAO,IACL+b,aAAakB,qBAAqB,KAClC7d,aAAame,SAAS,EACtB;gDACAhH,uBAAuB7F,GAAG,CAAC1Q;4CAC7B;wCACF,OAAO,IAAI+b,aAAayB,cAAc,EAAE;4CACtC7G,iBAAiBjG,GAAG,CAAC1Q;wCACvB,OAAO,IACL+b,aAAalB,QAAQ,IACrB,CAACC,qBACD,AAAC,MAAMrB,oCAAqC,OAC5C;4CACA7U,YAAY8L,GAAG,CAAC1Q;4CAChB6a,WAAW;wCACb,OAAO,IAAIC,mBAAmB;4CAC5B,2DAA2D;4CAC3D,gDAAgD;4CAChD3Y,SAASuO,GAAG,CAAC1Q;4CACb4a,QAAQ;wCACV;wCAEA,IAAI7J,eAAe/Q,SAAS,QAAQ;4CAClC,IACE,CAAC+b,aAAalB,QAAQ,IACtB,CAACkB,aAAaqB,cAAc,EAC5B;gDACA,MAAM,qBAEL,CAFK,IAAIpQ,MACR,CAAC,cAAc,EAAEpW,4CAA4C,GADzD,qBAAA;2DAAA;gEAAA;kEAAA;gDAEN;4CACF;4CACA,2DAA2D;4CAC3D,mCAAmC;4CACnC,IACE,AAAC,MAAM6iB,mCACP,CAACsC,aAAaqB,cAAc,EAC5B;gDACAxY,YAAY6Y,MAAM,CAACzd;4CACrB;wCACF;wCAEA,IACEhH,oBAAoBuM,QAAQ,CAACvF,SAC7B,CAAC+b,aAAalB,QAAQ,IACtB,CAACkB,aAAaqB,cAAc,EAC5B;4CACA,MAAM,qBAEL,CAFK,IAAIpQ,MACR,CAAC,OAAO,EAAEhN,KAAK,GAAG,EAAEpJ,4CAA4C,GAD5D,qBAAA;uDAAA;4DAAA;8DAAA;4CAEN;wCACF;oCACF;gCACF,EAAE,OAAOkW,KAAK;oCACZ,IACE,CAACpQ,QAAQoQ,QACTA,IAAI4Q,OAAO,KAAK,0BAEhB,MAAM5Q;oCACR2J,aAAa/F,GAAG,CAAC1Q;gCACnB;4BACF;4BAEA,IAAIsa,aAAa,OAAO;gCACtB,IAAIM,SAASC,UAAU;oCACrB1E;gCACF,OAAO;oCACLC;gCACF;4BACF;wBACF;wBAEAc,UAAUqF,GAAG,CAACvc,MAAM;4BAClBya;4BACAC;4BACAG;4BACAD;4BACAD;4BACAI;4BACAC;4BACA2C,qBAAqBlU;4BACrB+R,SAASE;4BACTkC,cAAcnU;4BACdoU,kBAAkBpU;4BAClBqU,iBAAiBrU;wBACnB;oBACF;gBACF;gBAGJ,IAAIvO,qBAAqB;oBACvBO,IAAIyR,KAAK,CACP,CAAC,0IAA0I,CAAC;oBAE9IxF,QAAQ0F,IAAI,CAAC;gBACf;gBAEA,MAAM2Q,kBAAkB,MAAMhF;gBAC9B,MAAMiF,qBACJ,AAAC,MAAMpF,qCACNmF,mBAAmBA,gBAAgBP,cAAc;gBAEpD,MAAMS,cAAc;oBAClBjG,0BAA0B,MAAMyB;oBAChCxB,cAAc,MAAMyB;oBACpBxB;oBACArC;oBACAsC,uBAAuB6F;gBACzB;gBAEA,OAAOC;YACT;YAEA,IAAIjI,oBAAoBA,mBAAmBkI,cAAc;YACzDnf,iBAAiB,iCAAiCsF;YAElD,IAAI2T,0BAA0B;gBAC5BjX,QAAQG,IAAI,CACVpL,KAAKC,OAAO,CAAC,SAAS,CAAC,KACrBA,OACE,CAAC,qJAAqJ,CAAC;gBAG7JgL,QAAQG,IAAI,CACV;YAEJ;YAEA,MAAM,EAAEgb,YAAY,EAAE,GAAG7Y;YAEzB,MAAM8a,gCAA0C,EAAE;YAClD,IAAIxZ,wBAAwB;gBAC1BwZ,8BAA8B3Y,IAAI,CAChC7O,KAAKgK,IAAI,CAAC7H,kBAAkB,GAAG9B,8BAA8B,GAAG,CAAC;gBAEnE,+DAA+D;gBAC/D,8FAA8F;gBAC9F,IACE,CAAC0Q,QAAQD,GAAG,CAAC2W,SAAS,IACrB/H,CAAAA,uBAAuBC,qBAAoB,GAC5C;oBACA6H,8BAA8B3Y,IAAI,CAChC7O,KAAKgK,IAAI,CACP7H,kBACA,CAAC,KAAK,EAAE9B,8BAA8B,GAAG,CAAC;gBAGhD;YACF;YAEA,MAAMqnB,8BAA8Bha,cACjCU,UAAU,CAAC,kCACXgF,OAAO,CAAC;gBACP,MAAMuU,0BAAkD,CAAC;gBAEzD,KAAK,MAAM,CAAC9G,KAAK+G,MAAM,IAAIlc,OAAOC,OAAO,CACvCe,OAAO8C,YAAY,CAACgW,aAAa,IAAI,CAAC,GACrC;oBACD,IAAI3E,OAAO+G,OAAO;wBAChBD,uBAAuB,CAAC9G,IAAI,GAAG7gB,KAAK+O,QAAQ,CAACjF,SAAS8d;oBACxD;gBACF;gBAEA,MAAMC,sBAAmD;oBACvDta,SAAS;oBACTb,QAAQ;wBACN,GAAGA,MAAM;wBACTob,YAAYhV;wBACZ,GAAInP,cAAcuG,cAAc,GAC5B;4BACE6d,UAAU;wBACZ,IACA,CAAC,CAAC;wBACNxC,cAAcA,eACVvlB,KAAK+O,QAAQ,CAACjF,SAASyb,gBACvB7Y,OAAO6Y,YAAY;wBACvB/V,cAAc;4BACZ,GAAG9C,OAAO8C,YAAY;4BACtBgW,eAAemC;4BACfK,iBAAiBrkB,cAAcuG,cAAc;4BAE7C,oGAAoG;4BACpG+d,uBAAuBrV;wBACzB;oBACF;oBACAzE,QAAQkD;oBACR6W,gBAAgBloB,KAAK+O,QAAQ,CAAClB,uBAAuBwD;oBACrD7C,OAAO;wBACLtM;wBACAlC,KAAK+O,QAAQ,CAACjF,SAASuT;wBACvB5b;wBACAO;wBACAhC,KAAKgK,IAAI,CAAC7H,kBAAkBa;wBAC5BhD,KAAKgK,IAAI,CAAC7H,kBAAkBG;wBAC5BtC,KAAKgK,IAAI,CAAC7H,kBAAkBU,4BAA4B;2BACpD,CAACkO,QAAQD,GAAG,CAAC2W,SAAS,GACtB;4BACEznB,KAAKgK,IAAI,CACP7H,kBACAW,qCAAqC;4BAEvCb;yBACD,GACD,EAAE;2BACFkM,SACA;+BACMzB,OAAO8C,YAAY,CAACsS,GAAG,GACvB;gCACE9hB,KAAKgK,IAAI,CACP7H,kBACAS,iCAAiC;gCAEnC5C,KAAKgK,IAAI,CACP7H,kBACAS,iCAAiC;6BAEpC,GACD,EAAE;4BACN5C,KAAKgK,IAAI,CAAC7H,kBAAkBI;4BAC5BvC,KAAKgK,IAAI,CAACxH;4BACVC;4BACAzC,KAAKgK,IAAI,CACP7H,kBACAY,4BAA4B;4BAE9B/C,KAAKgK,IAAI,CACP7H,kBACAY,4BAA4B;yBAE/B,GACD,EAAE;2BACFyR,YAAY,CAAC/B,iBACb;4BACEtP,uBAAuB;4BACvBnD,KAAKgK,IAAI,CAAC7H,kBAAkBgB,uBAAuB;yBACpD,GACD,EAAE;wBACN3B;wBACAxB,KAAKgK,IAAI,CAAC7H,kBAAkBQ,qBAAqB;wBACjD3C,KAAKgK,IAAI,CAAC7H,kBAAkBQ,qBAAqB;2BAC9C6kB;qBACJ,CACE3b,MAAM,CAACxK,aACP0K,GAAG,CAAC,CAACwC,OAASvO,KAAKgK,IAAI,CAAC0C,OAAO5C,OAAO,EAAEyE;oBAC3C4Z,QAAQ,EAAE;gBACZ;gBAEA,OAAON;YACT;YAEF,IAAI,CAAC3I,gBAAgB;gBACnBwI,4BAA4BS,MAAM,CAACtZ,IAAI,CACrC7O,KAAK+O,QAAQ,CACXsC,KACArR,KAAKgK,IAAI,CACPhK,KAAKiP,OAAO,CACVgB,QAAQC,OAAO,CACb,sDAGJ;YAIR;YAEA,MAAMkY,iBAAiB1Q,UAAU4M,IAAI,CAAC,CAACtX,IACrCA,EAAE4B,QAAQ,CAACzO;YAEb,IAAI4N,oBAAoB;YAExB,IAAIqa,gBAAgB;gBAClB,MAAMxD,aAAa,MAAMngB,8BAA8B;oBACrDkgB,gBAAgB;oBAChBD,cAAc1kB,KAAKgK,IAAI,CAACqH,KAAK+W;oBAC7B1b;oBACAyB;oBACA6I,gBAAgBtK,OAAOsK,cAAc;oBACrCwB,OAAO;oBACPnP,MAAM;gBACR;gBAEA,IAAIub,WAAWC,OAAO,KAAK,UAAU;wBAIvBD;oBAHZ7W,oBAAoB;oBACpBoT,wBAAwBC,SAAS,CAAC,eAAe,GAAG;wBAClDyD,SAASD,WAAWC,OAAO;wBAC3BwD,UAAUzD,EAAAA,yBAAAA,WAAW0D,UAAU,qBAArB1D,uBAAuByD,QAAQ,KAAI;4BAC3C;gCACEE,QAAQ;gCACRC,gBAAgB;4BAClB;yBACD;oBACH;oBAEA,IAAI/V,gBAAgB;wBAClB,MAAM3H,cACJ9K,KAAKgK,IAAI,CACPF,SACA,UACAwB,SACAlI,uCAEF+d,wBAAwBC,SAAS,CAAC,eAAe,CAACiH,QAAQ,IAAI,EAAE;oBAEpE;gBACF;YACF;YAEA,MAAM/b,6BAA6BxC,SAASqX;YAE5C,IAAI,CAACjP,kBAAkB,CAACqL,oBAAoB;gBAC1CA,qBAAqB5V,mBAAmB;oBACtC0J;oBACA3E;oBACA5C;oBACAkV,mBAAmBrZ,8BAA8B4a;oBACjDtS,aAAa;2BAAIA;qBAAY;oBAC7BP;oBACAwR;oBACA5B;oBACAzP;gBACF,GAAGsR,KAAK,CAAC,CAAChJ;oBACR/L,QAAQmM,KAAK,CAACJ;oBACdpF,QAAQ0F,IAAI,CAAC;gBACf;YACF;YAEA,IAAIuJ,iBAAiB8D,IAAI,GAAG,KAAKtY,SAASsY,IAAI,GAAG,GAAG;gBAClD,yDAAyD;gBACzD,+DAA+D;gBAC/DzI,eAAeO,UAAU,GAAGvY,gBAAgB;uBACvC2c;uBACAxU;iBACJ,EAAEO,GAAG,CAAC,CAAC1C;oBACN,OAAO3B,eAAe2B,MAAMiC;gBAC9B;YACF;YAEA,2DAA2D;YAC3D,MAAMoC,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMvD,cAAcsQ,oBAAoBC;YAExD,iHAAiH;YACjH,8DAA8D;YAC9D,MAAMoN,oBACJ,CAACpH,4BAA6B,CAAA,CAACG,yBAAyBpH,WAAU;YAEpE,IAAI0F,aAAagE,IAAI,GAAG,GAAG;gBACzB,MAAM3N,MAAM,qBAQX,CARW,IAAIE,MACd,CAAC,qCAAqC,EACpCyJ,aAAagE,IAAI,KAAK,IAAI,KAAK,IAChC,kDAAkD,EAAE;uBAAIhE;iBAAa,CACnE/T,GAAG,CAAC,CAAC2c,KAAO,CAAC,KAAK,EAAEA,IAAI,EACxB1e,IAAI,CACH,MACA,sFAAsF,CAAC,GAPjF,qBAAA;2BAAA;gCAAA;kCAAA;gBAQZ;gBACAmM,IAAIC,IAAI,GAAG;gBACX,MAAMD;YACR;YAEA,MAAMtQ,aAAaiE,SAASwB;YAE5B,IAAIoB,OAAO8C,YAAY,CAACmZ,WAAW,EAAE;gBACnC,MAAMC,WACJ3Y,QAAQ;gBAEV,MAAM4Y,eAAe,MAAM,IAAI1K,QAAkB,CAACjO,SAAS4Y;oBACzDF,SACE,YACA;wBAAEzT,KAAKnV,KAAKgK,IAAI,CAACF,SAAS;oBAAU,GACpC,CAACqM,KAAK3H;wBACJ,IAAI2H,KAAK;4BACP,OAAO2S,OAAO3S;wBAChB;wBACAjG,QAAQ1B;oBACV;gBAEJ;gBAEAkZ,4BAA4BlZ,KAAK,CAACK,IAAI,IACjCga,aAAa9c,GAAG,CAAC,CAACtB,WACnBzK,KAAKgK,IAAI,CAAC0C,OAAO5C,OAAO,EAAE,UAAUW;YAG1C;YAEA,MAAMse,WAAqC;gBACzC;oBACEpS,aAAa;oBACbC,iBAAiBlK,OAAO8C,YAAY,CAACuL,SAAS,GAAG,IAAI;gBACvD;gBACA;oBACEpE,aAAa;oBACbC,iBAAiBlK,OAAO8C,YAAY,CAACmZ,WAAW,GAAG,IAAI;gBACzD;gBACA;oBACEhS,aAAa;oBACbC,iBAAiBlK,OAAO8C,YAAY,CAACwZ,iBAAiB,GAAG,IAAI;gBAC/D;gBACA;oBACErS,aAAa;oBACbC,iBAAiBlK,OAAO8C,YAAY,CAAC2L,GAAG,GAAG,IAAI;gBACjD;aACD;YACD7G,UAAUS,MAAM,CACdgU,SAAShd,GAAG,CAAC,CAACkd;gBACZ,OAAO;oBACLpS,WAAW1S;oBACX2S,SAASmS;gBACX;YACF;YAGF,MAAM1c,iCACJzC,SACA4d;YAGF,MAAM5Z,qBAAyC,MAAM9C,aACnDhL,KAAKgK,IAAI,CAACF,SAAS3H,kBAAkBG;YAGvC,MAAM+I,oBAAuC;gBAC3CkC,SAAS;gBACT3B,QAAQ,CAAC;gBACTO,eAAe,CAAC;gBAChB+c,gBAAgB,EAAE;gBAClBC,SAASjR;YACX;YAEA,MAAMkR,qBAA+B,EAAE;YAEvC,MAAM,EAAEvN,IAAI,EAAE,GAAGnP;YAEjB,MAAM2c,wBAAwBhnB,oBAAoBwJ,MAAM,CACtD,CAACxC,OACC8J,WAAW,CAAC9J,KAAK,IACjB8J,WAAW,CAAC9J,KAAK,CAACwL,UAAU,CAAC;YAEjCwU,sBAAsBC,OAAO,CAAC,CAACjgB;gBAC7B,IAAI,CAACmC,SAAS+d,GAAG,CAAClgB,SAAS,CAACgY,0BAA0B;oBACpDpT,YAAY8L,GAAG,CAAC1Q;gBAClB;YACF;YAEA,MAAMmgB,cAAcH,sBAAsBza,QAAQ,CAAC;YACnD,MAAM6a,sBACJ,CAACD,eAAe,CAAChI,yBAAyB,CAACH;YAE7C,MAAMqI,gBAAgB;mBAAIzb;mBAAgBzC;aAAS;YACnD,MAAMme,iBAAiBzJ,YAAYqJ,GAAG,CAACtmB;YACvC,MAAM2mB,kBAAkBvP,aAAasP;YAErC,MAAM7hB,uBAAuB;gBAC3BsV,YAAY;YACd;YAEA,sDAAsD;YACtD,mBAAmB;YACnB,yBAAyB;YACzB,gCAAgC;YAChC,IACE,CAACxK,iBACA8W,CAAAA,cAAczV,MAAM,GAAG,KACtBwU,qBACAgB,uBACAtb,MAAK,GACP;gBACA,MAAM0b,uBACJnc,cAAcU,UAAU,CAAC;gBAC3B,MAAMyb,qBAAqBxb,YAAY,CAAC;oBACtClJ,uBACE;2BACKukB;2BACA/b,SAASW,KAAK,CAACzC,MAAM,CAAC,CAACxC,OAAS,CAACqgB,cAAc9a,QAAQ,CAACvF;qBAC5D,EACDmC,UACA,IAAIyT,IACFtH,MAAMC,IAAI,CAACqI,gBAAgBtU,OAAO,IAAII,GAAG,CACvC,CAAC,CAAC1C,MAAMuC,OAAO;wBACb,OAAO;4BAACvC;4BAAMuC,OAAOG,GAAG,CAAC,CAACC,QAAUA,MAAMC,QAAQ;yBAAE;oBACtD;oBAIN,MAAMuF,YAAYvB,QAAQ,aACvBwB,OAAO;oBAEV,MAAMqY,eAAmC;wBACvC,GAAGpd,MAAM;wBACT,sEAAsE;wBACtE,+BAA+B;wBAC/B,wEAAwE;wBACxE,6DAA6D;wBAC7Dqd,eAAe,CAACC;4BACd,+DAA+D;4BAC/D,iEAAiE;4BACjE,uEAAuE;4BACvE,UAAU;4BACV,EAAE;4BACF,6DAA6D;4BAC7Dxe,SAAS8d,OAAO,CAAC,CAACjgB;gCAChB,IAAI/F,eAAe+F,OAAO;oCACxB+f,mBAAmBva,IAAI,CAACxF;oCAExB,IAAIuW,uBAAuB2J,GAAG,CAAClgB,OAAO;wCACpC,iEAAiE;wCACjE,mBAAmB;wCACnB,IAAIwS,MAAM;4CACRmO,UAAU,CAAC,CAAC,CAAC,EAAEnO,KAAK0G,aAAa,GAAGlZ,MAAM,CAAC,GAAG;gDAC5CA;gDACA4gB,gBAAgB;4CAClB;wCACF,OAAO;4CACLD,UAAU,CAAC3gB,KAAK,GAAG;gDACjBA;gDACA4gB,gBAAgB;4CAClB;wCACF;oCACF,OAAO;wCACL,iEAAiE;wCACjE,iCAAiC;wCACjC,OAAOD,UAAU,CAAC3gB,KAAK;oCACzB;gCACF;4BACF;4BAEA,oEAAoE;4BACpE,cAAc;4BACd4W,gBAAgBqJ,OAAO,CAAC,CAAC1d,QAAQvC;gCAC/BuC,OAAO0d,OAAO,CAAC,CAACtd;oCACdge,UAAU,CAAChe,MAAMC,QAAQ,CAAC,GAAG;wCAC3B5C;wCACA6gB,UAAUle,MAAMma,eAAe;oCACjC;gCACF;4BACF;4BAEA,IAAIsC,mBAAmB;gCACrBuB,UAAU,CAAC,OAAO,GAAG;oCACnB3gB,MAAM+Q,cAAc,SAAS;gCAC/B;4BACF;4BAEA,IAAIqP,qBAAqB;gCACvBO,UAAU,CAAC,OAAO,GAAG;oCACnB3gB,MAAM;gCACR;4BACF;4BAEA,wDAAwD;4BACxD,gDAAgD;4BAChD6W,YAAYoJ,OAAO,CAAC,CAAC1d,QAAQ2Y;gCAC3B,MAAMyB,YAAY1F,kBAAkB6J,GAAG,CAAC5F;gCACxC,MAAM6F,iBAAiBpE,CAAAA,6BAAAA,UAAWQ,OAAO,MAAK;gCAE9C,MAAMxC,oBAAoBgC,YACtBxd,uBAAuBkE,OAAO8C,YAAY,CAAC2L,GAAG,EAAE6K,aAChDlT;gCAEJlH,OAAO0d,OAAO,CAAC,CAACtd;oCACd,8DAA8D;oCAC9D,wDAAwD;oCACxD,0DAA0D;oCAC1D,IACEA,MAAMua,kBAAkB,IACxBva,MAAMua,kBAAkB,CAACtS,MAAM,GAAG,GAClC;wCACA;oCACF;oCAEA+V,UAAU,CAAChe,MAAMC,QAAQ,CAAC,GAAG;wCAC3B5C,MAAMkb;wCACN2F,UAAUle,MAAMma,eAAe;wCAC/BkE,sBAAsBre,MAAMoa,mBAAmB;wCAC/CkE,iBAAiBF;wCACjBG,WAAW;wCACXC,oBAAoBxG;oCACtB;gCACF;4BACF;4BAEA,gEAAgE;4BAChE,gEAAgE;4BAChE,2DAA2D;4BAC3D,wCAAwC;4BACxC,KAAK,MAAM,EACT3a,IAAI,EACJkb,eAAe,EAChB,IAAIpE,mBAAmBsK,MAAM,GAAI;gCAChCT,UAAU,CAAC3gB,KAAK,GAAG;oCACjBA,MAAMkb;oCACN2F,UAAU7gB;oCACVghB,sBAAsBzhB,aAAaS;oCACnC,sDAAsD;oCACtDkhB,WAAW;oCACX,6DAA6D;oCAC7DC,oBAAoB;oCACpBE,sBAAsB;oCACtB,+DAA+D;oCAC/DJ,iBAAiB;gCACnB;4BACF;4BAEA,IAAIzO,MAAM;gCACR,KAAK,MAAMxS,QAAQ;uCACd4E;uCACAzC;uCACCid,oBAAoB;wCAAC;qCAAO,GAAG,EAAE;uCACjCgB,sBAAsB;wCAAC;qCAAO,GAAG,EAAE;iCACxC,CAAE;oCACD,MAAMkB,QAAQnf,SAAS+d,GAAG,CAAClgB;oCAC3B,MAAMyc,YAAYxiB,eAAe+F;oCACjC,MAAMuhB,aAAaD,SAAS/K,uBAAuB2J,GAAG,CAAClgB;oCAEvD,KAAK,MAAMwhB,UAAUhP,KAAKtQ,OAAO,CAAE;4CAMzBye;wCALR,+DAA+D;wCAC/D,IAAIW,SAAS7E,aAAa,CAAC8E,YAAY;wCACvC,MAAM9b,aAAa,CAAC,CAAC,EAAE+b,SAASxhB,SAAS,MAAM,KAAKA,MAAM;wCAE1D2gB,UAAU,CAAClb,WAAW,GAAG;4CACvBzF,MAAM2gB,EAAAA,mBAAAA,UAAU,CAAC3gB,KAAK,qBAAhB2gB,iBAAkB3gB,IAAI,KAAIA;4CAChCyhB,SAASD;4CACTZ,gBAAgBW;wCAClB;oCACF;oCAEA,IAAID,OAAO;wCACT,qDAAqD;wCACrD,OAAOX,UAAU,CAAC3gB,KAAK;oCACzB;gCACF;4BACF;4BAEA,OAAO2gB;wBACT;oBACF;oBAEA,MAAMjY,SAAS/R,KAAKgK,IAAI,CAACF,SAAS;oBAClC,MAAMihB,eAAe,MAAMvZ,UACzBH,KACA;wBACEQ,YAAYiY;wBACZxY;wBACAQ,QAAQ;wBACRF,aAAa;wBACbS;wBACA/D,OAAOob;wBACP3X;wBACAiZ,eAAe;wBACfxa,YAAYjB,mBAAmBua;oBACjC,GACApc;oBAGF,sDAAsD;oBACtD,IAAI,CAACqd,cAAc;oBAEnB,MAAME,kBAAkB,CACtBC,YACAC,oBAAgC,KAAK;4BAGnCJ;wBADF,MAAMK,gBACJL,2BAAAA,aAAaM,MAAM,CAAClB,GAAG,CAACe,gCAAxBH,yBAAqCK,YAAY;wBAEnD,IAAI,CAACA,cAAc;4BACjB,OAAO;gCAAEnF,YAAYkF;gCAAmBG,QAAQxY;4BAAU;wBAC5D;wBAEA,IACEsY,aAAanF,UAAU,KAAK,SAC5BmF,aAAanF,UAAU,GAAG,KAC1BmF,aAAaE,MAAM,KAAKxY,WACxB;4BACA,OAAO;gCACLmT,YAAYmF,aAAanF,UAAU;gCACnCqF,QAAQ5e,OAAO6e,UAAU;4BAC3B;wBACF;wBAEA,OAAOH;oBACT;oBAEA,IAAI/Y,eAAetB,QAAQD,GAAG,CAAC0a,sBAAsB,KAAK,KAAK;wBAC7DzjB,mBAAmBgjB;oBACrB;oBAEAjnB,gCAAgC;wBAC9BgG,SAAS4C,OAAO5C,OAAO;wBACvB2hB,QAAQ;4BACNpY;+BACG0X,aAAaW,2BAA2B,CAACjB,MAAM;yBACnD;oBACH;oBAEApf,kBAAkB6d,cAAc,GAAGvR,MAAMC,IAAI,CAC3CmT,aAAaY,gBAAgB;oBAG/B,2CAA2C;oBAC3C,KAAK,MAAMtiB,QAAQ4E,YAAa;wBAC9B,MAAM2d,eAAeloB,YAAY2F,MAAMS,SAASgJ,WAAW;wBAC3D,MAAMrT,GAAGosB,MAAM,CAACD;oBAClB;oBAEA1L,YAAYoJ,OAAO,CAAC,CAACvD,mBAAmBxB;4BAWbhE;wBAVzB,MAAMlX,OAAO+W,mBAAmB+J,GAAG,CAAC5F;wBACpC,IAAI,CAAClb,MAAM,MAAM,qBAAoC,CAApC,IAAIN,eAAe,mBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAmC;wBAEpD,MAAMid,YAAY1F,kBAAkB6J,GAAG,CAAC5F;wBACxC,IAAI,CAACyB,WAAW,MAAM,qBAA0C,CAA1C,IAAIjd,eAAe,yBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyC;wBAE/D,IAAI+iB,oBACF9F,UAAUC,UAAU,KAAK,KACzBgF,gBAAgB5hB,MAAM4c,UAAU,KAAK;wBAEvC,IAAI6F,uBAAqBvL,iBAAAA,UAAU4J,GAAG,CAAC9gB,0BAAdkX,eAAqB2D,QAAQ,GAAE;4BACtD,uEAAuE;4BACvE,qFAAqF;4BACrF3D,UAAUqF,GAAG,CAACvc,MAAM;gCAClB,GAAIkX,UAAU4J,GAAG,CAAC9gB,KAAK;gCACvB6a,UAAU;gCACVD,OAAO;4BACT;wBACF;wBAEA,MAAM8H,oBAAoB1kB,gBAAgBkd;wBAE1C,kEAAkE;wBAClE,yBAAyB;wBACzB,MAAMP,oBACJ,CAAC+H,qBACDvjB,uBAAuBkE,OAAO8C,YAAY,CAAC2L,GAAG,EAAE6K,aAC5C,OACAlT;wBAEN,MAAMkZ,sBACJ,uEAAuE;wBACvEtf,OAAOuf,eAAe,IAAIjjB;wBAE5B,0FAA0F;wBAC1F,4CAA4C;wBAC5C,MAAMkjB,YAAwB;4BAC5B;gCAAEC,MAAM;gCAAUtL,KAAKpa;4BAAc;4BACrC;gCACE0lB,MAAM;gCACNtL,KAAK;gCACL+G,OAAO;4BACT;4BACA,iGAAiG;4BACjG,iGAAiG;+BAC7F5D,oBACA;gCACE;oCACEmI,MAAM;oCACNtL,KAAK;oCACL+G,OAAOoE;gCACT;6BACD,GACD,EAAE;yBACP;wBAED,mEAAmE;wBACnE,6DAA6D;wBAC7D,mEAAmE;wBACnE,8DAA8D;wBAC9D,2BAA2B;wBAC3B,MAAMpgB,SAA6B,EAAE;wBACrC,MAAMO,gBAAoC,EAAE;wBAE5C,mEAAmE;wBACnE,iEAAiE;wBACjE,+DAA+D;wBAC/D,iEAAiE;wBACjE,mDAAmD;wBACnD,IAAIigB,yBAA6C,EAAE;wBACnD,IAAIC,uBAA2C,EAAE;wBACjD,KAAK,MAAMC,oBAAoBvG,kBAAmB;4BAChD,IACEuG,iBAAiBlG,mBAAmB,IACpCkG,iBAAiBlG,mBAAmB,CAACnS,MAAM,GAAG,GAC9C;gCACAmY,uBAAuBvd,IAAI,CAACyd;4BAC9B,OAAO;gCACLD,qBAAqBxd,IAAI,CAACyd;4BAC5B;wBACF;wBAEAF,yBAAyB7oB,sBACvB6oB,wBACA,CAACE,mBAAqBA,iBAAiBrgB,QAAQ;wBAEjDogB,uBAAuB9oB,sBACrB8oB,sBACA,CAACC,mBAAqBA,iBAAiBrgB,QAAQ;wBAGjD8Z,oBAAoB;+BACfsG;+BACAD;yBACJ;wBAED,KAAK,MAAME,oBAAoBvG,kBAAmB;4BAChD,+BAA+B;4BAC/B,iCAAiC;4BACjC,IAAIuG,iBAAiBrgB,QAAQ,KAAK/I,4BAA4B;gCAC5D;4BACF;4BAEA,IACE8gB,qBACAsI,iBAAiBlG,mBAAmB,IACpCkG,iBAAiBlG,mBAAmB,CAACnS,MAAM,GAAG,GAC9C;gCACA,6DAA6D;gCAC7D,8BAA8B;gCAC9B9H,cAAc0C,IAAI,CAACyd;4BACrB,OAAO;gCACL,4DAA4D;gCAC5D,gCAAgC;gCAChC1gB,OAAOiD,IAAI,CAACyd;4BACd;wBACF;wBAEA,gCAAgC;wBAChC,KAAK,MAAMtgB,SAASJ,OAAQ;4BAC1B,IAAItI,eAAe+F,SAAS2C,MAAMC,QAAQ,KAAK5C,MAAM;4BACrD,IAAI2C,MAAMC,QAAQ,KAAK/I,4BAA4B;4BAEnD,MAAM,EACJqpB,WAAW,CAAC,CAAC,EACbpF,eAAe,EACfqF,YAAY,EACb,GAAGzB,aAAaM,MAAM,CAAClB,GAAG,CAACne,MAAMC,QAAQ,KAAK,CAAC;4BAEhD,MAAMmf,eAAeH,gBACnBjf,MAAMC,QAAQ,EACd+Z,UAAUC,UAAU;4BAGtB1F,UAAUqF,GAAG,CAAC5Z,MAAMC,QAAQ,EAAE;gCAC5B,GAAIsU,UAAU4J,GAAG,CAACne,MAAMC,QAAQ,CAAC;gCACjCugB;gCACArF;gCACAH,qBAAqBoE;4BACvB;4BAEA,uEAAuE;4BACvE7K,UAAUqF,GAAG,CAACvc,MAAM;gCAClB,GAAIkX,UAAU4J,GAAG,CAAC9gB,KAAK;gCACvBmjB;gCACArF;gCACAH,qBAAqBoE;4BACvB;4BAEA,IAAIA,aAAanF,UAAU,KAAK,GAAG;gCACjC,MAAMwG,kBAAkBhpB,kBAAkBuI,MAAMC,QAAQ;gCAExD,IAAIygB;gCACJ,IAAIX,mBAAmB;oCACrBW,YAAY;gCACd,OAAO;oCACLA,YAAY1sB,KAAK2sB,KAAK,CAAC3iB,IAAI,CAAC,GAAGyiB,kBAAkBlsB,YAAY;gCAC/D;gCAEA,IAAIqsB;gCACJ,6DAA6D;gCAC7D,6DAA6D;gCAC7D,6DAA6D;gCAC7D,uBAAuB;gCACvB,IAAI,CAACb,qBAAqB7Q,iBAAiB;oCACzC0R,oBAAoB5sB,KAAK2sB,KAAK,CAAC3iB,IAAI,CACjC,GAAGyiB,kBAAkBnsB,qBAAqB;gCAE9C;gCAEA,MAAMusB,OAAOjnB,YAAY2mB;gCAEzBlhB,kBAAkBO,MAAM,CAACI,MAAMC,QAAQ,CAAC,GAAG;oCACzC6gB,eAAeD,KAAKE,MAAM;oCAC1BC,gBAAgBH,KAAKpZ,OAAO;oCAC5BwZ,eAAe/R,kBACX8I,oBACErb,cAAcukB,gBAAgB,GAC9BvkB,cAAcwkB,MAAM,GACtBra;oCACJsa,iBAAiBpJ;oCACjBqJ,uBAAuBnB;oCACvBoB,0BAA0BlC,aAAanF,UAAU;oCACjDsH,sBAAsBnC,aAAaE,MAAM;oCACzCxf,UAAUzC;oCACVqjB;oCACAE;oCACAY,aAAarkB;gCACf;4BACF,OAAO;gCACL2iB,oBAAoB;gCACpB,8DAA8D;gCAC9D,oBAAoB;gCACpBvL,UAAUqF,GAAG,CAAC5Z,MAAMC,QAAQ,EAAE;oCAC5B,GAAIsU,UAAU4J,GAAG,CAACne,MAAMC,QAAQ,CAAC;oCACjCgY,OAAO;oCACPC,UAAU;gCACZ;4BACF;wBACF;wBAEA,IAAI,CAAC4H,qBAAqBxoB,eAAe+F,OAAO;4BAC9C,iEAAiE;4BACjE,0DAA0D;4BAC1D,sBAAsB;4BACtB,IAAI,CAAC2a,mBAAmB;gCACtB7X,cAAc0C,IAAI,CAAC;oCACjB5C,UAAU5C;oCACV8c,iBAAiB9c;oCACjB+c,qBAAqBtT;oCACrBuT,cACEhG,cAAc8J,GAAG,CAAC5F,oBAClB9b,aAAaglB,SAAS;oCACxBlH,oBAAoBzT;gCACtB;4BACF;4BAEA,KAAK,MAAM9G,SAASG,cAAe;oCAGhB4e,0BAwFM/e;gCA1FvB,MAAMygB,kBAAkBhpB,kBAAkBuI,MAAMC,QAAQ;gCAExD,MAAMsgB,YAAWxB,2BAAAA,aAAaM,MAAM,CAAClB,GAAG,CACtCne,MAAMC,QAAQ,sBADC8e,yBAEdwB,QAAQ;gCAEX,MAAMnB,eAAeH,gBAAgBjf,MAAMC,QAAQ;gCAEnD,IAAIygB,YAA2B;gCAC/B,IAAI,CAACX,mBAAmB;oCACtBW,YAAY1sB,KAAK2sB,KAAK,CAAC3iB,IAAI,CAAC,GAAGyiB,kBAAkBlsB,YAAY;gCAC/D;gCAEA,IAAIqsB;gCACJ,IAAI,CAACb,qBAAqB7Q,iBAAiB;oCACzC0R,oBAAoB5sB,KAAK2sB,KAAK,CAAC3iB,IAAI,CACjC,GAAGyiB,kBAAkBnsB,qBAAqB;gCAE9C;gCAEA,IAAI,CAACyrB,sBAAqBQ,4BAAAA,SAAUmB,YAAY,GAAE;oCAChD,MAAMC,eAAetS,eAAelP,aAAa,CAACmY,IAAI,CACpD,CAAC3I,IAAMA,EAAEtS,IAAI,KAAKA;oCAEpB,IAAI,CAACskB,cAAc;wCACjB,MAAM,qBAAoC,CAApC,IAAItX,MAAM,4BAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAmC;oCAC3C;oCAEAsX,aAAaC,yBAAyB,GAAG,EAAE;oCAC3C,KAAK,MAAMC,eAAetB,SAASmB,YAAY,CAAE;wCAC/C,MAAMI,SAAS7kB,8BACb+C,MAAMC,QAAQ,EACd4hB;wCAEFF,aAAaC,yBAAyB,CAAC/e,IAAI,CAACif;oCAC9C;gCACF;gCAEAvN,UAAUqF,GAAG,CAAC5Z,MAAMC,QAAQ,EAAE;oCAC5B,GAAIsU,UAAU4J,GAAG,CAACne,MAAMC,QAAQ,CAAC;oCACjC8hB,mBAAmB;oCACnB,gEAAgE;oCAChE,2CAA2C;oCAC3CvB,cAAcxI;gCAChB;gCAEA,MAAMqC,eACJra,MAAMqa,YAAY,IAAI5d,aAAaglB,SAAS;gCAE9C,+DAA+D;gCAC/D,+DAA+D;gCAC/D,oDAAoD;gCACpD,iDAAiD;gCACjD,MAAMO,uBACJhK,qBAAqBqC,iBAAiB5d,aAAame,SAAS,GACxDwE,eACAtY;gCAEN,MAAMiB,WAAqBrL,4BACzB2d,cACAra,MAAMC,QAAQ;gCAGhB,MAAM4gB,OACJN,YACAvI,qBACAqC,iBAAiB5d,aAAame,SAAS,GACnChhB,YAAY2mB,YACZ,CAAC;gCAEPlhB,kBAAkBc,aAAa,CAACH,MAAMC,QAAQ,CAAC,GAAG;oCAChDmhB,iBAAiBpJ;oCACjBiJ,eAAe/R,kBACX8I,oBACErb,cAAcukB,gBAAgB,GAC9BvkB,cAAcwkB,MAAM,GACtBra;oCACJua,uBAAuBnB;oCACvB5iB,YAAYlI,oBACViF,mBAAmB2F,MAAMC,QAAQ,EAAE;wCACjC1C,iBAAiB;oCACnB,GAAGE,EAAE,CAACC,MAAM;oCAEdgjB;oCACA3Y;oCACAka,kBAAkB,EAAED,wCAAAA,qBAAsB/H,UAAU;oCACpDiI,cAAc,EAAEF,wCAAAA,qBAAsB1C,MAAM;oCAC5C6C,gBAAgBtB,KAAKE,MAAM;oCAC3BqB,iBAAiBvB,KAAKpZ,OAAO;oCAC7B8S,oBAAoBva,MAAMua,kBAAkB;oCAC5C8H,qBAAqBriB,EAAAA,6BAAAA,MAAMoa,mBAAmB,qBAAzBpa,2BAA2BiI,MAAM,IAClD5K,OACAyJ;oCACJwb,gBAAgB,CAAC5B,YACb,OACAtrB,oBACEiF,mBAAmBqmB,WAAW;wCAC5BnjB,iBAAiB;wCACjBglB,eAAe;wCACfC,8BAA8B;oCAChC,GAAG/kB,EAAE,CAACC,MAAM;oCAElBkjB;oCACA6B,wBAAwB,CAAC7B,oBACrB9Z,YACA1R,oBACEiF,mBAAmBumB,mBAAmB;wCACpCrjB,iBAAiB;wCACjBglB,eAAe;wCACfC,8BAA8B;oCAChC,GAAG/kB,EAAE,CAACC,MAAM;oCAElB8jB,aAAarkB;gCACf;4BACF;wBACF;oBACF;oBAEA,MAAMulB,mBAAmB,OACvBC,YACAtlB,MACAkF,MACAoc,OACAiE,KACAC,oBAAoB,KAAK;wBAEzB,OAAOhF,qBACJzb,UAAU,CAAC,sBACXC,YAAY,CAAC;4BACZE,OAAO,GAAGA,KAAK,CAAC,EAAEqgB,KAAK;4BACvB,MAAME,OAAO9uB,KAAKgK,IAAI,CAAC+H,QAAQxD;4BAC/B,MAAMsL,WAAWnW,YACfirB,YACA7kB,SACAgJ,WACA;4BAGF,MAAMic,eAAe/uB,KAClB+O,QAAQ,CACP/O,KAAKgK,IAAI,CAACF,SAAS3H,mBACnBnC,KAAKgK,IAAI,CACPhK,KAAKgK,IAAI,CACP6P,UACA,yDAAyD;4BACzD,4BAA4B;4BAC5B8U,WACGK,KAAK,CAAC,GACNC,KAAK,CAAC,KACNljB,GAAG,CAAC,IAAM,MACV/B,IAAI,CAAC,OAEVuE,OAGHyJ,OAAO,CAAC,OAAO;4BAElB,IACE,CAAC2S,SACD,CACE,mDAAmD;4BACnD,kDAAkD;4BAEhDtoB,CAAAA,oBAAoBuM,QAAQ,CAACvF,SAC7B,CAACggB,sBAAsBza,QAAQ,CAACvF,KAAI,GAGxC;gCACAmX,aAAa,CAACnX,KAAK,GAAG0lB;4BACxB;4BAEA,MAAMG,OAAOlvB,KAAKgK,IAAI,CAACF,SAAS3H,kBAAkB4sB;4BAClD,MAAMI,aACJ9jB,kBAAkB6d,cAAc,CAACta,QAAQ,CAACvF;4BAE5C,2DAA2D;4BAC3D,0DAA0D;4BAC1D,qBAAqB;4BACrB,IAAI,AAAC,CAAA,CAACwS,QAAQgT,iBAAgB,KAAM,CAACM,YAAY;gCAC/C,MAAM1vB,GAAGuP,KAAK,CAAChP,KAAKiP,OAAO,CAACigB,OAAO;oCAAEhgB,WAAW;gCAAK;gCACrD,MAAMzP,GAAG2vB,MAAM,CAACN,MAAMI;4BACxB,OAAO,IAAIrT,QAAQ,CAAC8O,OAAO;gCACzB,wDAAwD;gCACxD,oDAAoD;gCACpD,OAAOnK,aAAa,CAACnX,KAAK;4BAC5B;4BAEA,IAAIwS,MAAM;gCACR,IAAIgT,mBAAmB;gCAEvB,MAAMQ,YAAYhmB,SAAS,MAAMrJ,KAAKsvB,OAAO,CAAC/gB,QAAQ;gCACtD,MAAMghB,sBAAsBR,aAAaC,KAAK,CAC5C,SAAS/a,MAAM;gCAGjB,KAAK,MAAM4W,UAAUhP,KAAKtQ,OAAO,CAAE;oCACjC,MAAMikB,UAAU,CAAC,CAAC,EAAE3E,SAASxhB,SAAS,MAAM,KAAKA,MAAM;oCAEvD,IACEshB,SACAtf,kBAAkB6d,cAAc,CAACta,QAAQ,CAAC4gB,UAC1C;wCACA;oCACF;oCAEA,MAAMC,sBAAsBzvB,KACzBgK,IAAI,CACH,SACA6gB,SAASwE,WACT,8DAA8D;oCAC9D,+BAA+B;oCAC/BhmB,SAAS,MAAM,KAAKkmB,qBAErBvX,OAAO,CAAC,OAAO;oCAElB,MAAM0X,cAAc1vB,KAAKgK,IAAI,CAC3B+H,QACA8Y,SAASwE,WACThmB,SAAS,MAAM,KAAKkF;oCAEtB,MAAMohB,cAAc3vB,KAAKgK,IAAI,CAC3BF,SACA3H,kBACAstB;oCAGF,IAAI,CAAC9E,OAAO;wCACVnK,aAAa,CAACgP,QAAQ,GAAGC;oCAC3B;oCACA,MAAMhwB,GAAGuP,KAAK,CAAChP,KAAKiP,OAAO,CAAC0gB,cAAc;wCACxCzgB,WAAW;oCACb;oCACA,MAAMzP,GAAG2vB,MAAM,CAACM,aAAaC;gCAC/B;4BACF;wBACF;oBACJ;oBAEA,eAAeC;wBACb,OAAO/F,qBACJzb,UAAU,CAAC,gCACXC,YAAY,CAAC;4BACZ,MAAMygB,OAAO9uB,KAAKgK,IAAI,CACpBF,SACA,UACA,OACA;4BAEF,MAAM2lB,sBAAsBzvB,KACzBgK,IAAI,CAAC,SAAS,YACdgO,OAAO,CAAC,OAAO;4BAElB,IAAIzY,WAAWuvB,OAAO;gCACpB,MAAMrvB,GAAG0P,QAAQ,CACf2f,MACA9uB,KAAKgK,IAAI,CAACF,SAAS,UAAU2lB;gCAE/BjP,aAAa,CAAC,OAAO,GAAGiP;4BAC1B;wBACF;oBACJ;oBAEA,oEAAoE;oBACpE,IAAI7F,iBAAiB;wBACnB,MAAMgG;oBACR,OAAO;wBACL,sGAAsG;wBACtG,IAAI,CAACxV,eAAe,CAACC,aAAaoO,mBAAmB;4BACnD,MAAMiG,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;wBAC3D;oBACF;oBAEA,IAAIjF,qBAAqB;wBACvB,MAAMiF,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;oBAC3D;oBAEA,KAAK,MAAMrlB,QAAQqgB,cAAe;wBAChC,MAAMiB,QAAQnf,SAAS+d,GAAG,CAAClgB;wBAC3B,MAAMwmB,sBAAsBjQ,uBAAuB2J,GAAG,CAAClgB;wBACvD,MAAMyc,YAAYxiB,eAAe+F;wBACjC,MAAMymB,SAAS/P,eAAewJ,GAAG,CAAClgB;wBAClC,MAAMkF,OAAO9K,kBAAkB4F;wBAE/B,MAAM0mB,WAAWxP,UAAU4J,GAAG,CAAC9gB;wBAC/B,MAAM2mB,eAAejF,aAAakF,MAAM,CAAC9F,GAAG,CAAC9gB;wBAC7C,IAAI0mB,YAAYC,cAAc;4BAC5B,qBAAqB;4BACrB,IAAID,SAAS1L,aAAa,EAAE;gCAC1B0L,SAAS7I,gBAAgB,GAAG6I,SAAS1L,aAAa,CAACtY,GAAG,CACpD,CAAC8N;oCACC,MAAMuE,WAAW4R,aAAaE,eAAe,CAAC/F,GAAG,CAACtQ;oCAClD,IAAI,OAAOuE,aAAa,aAAa;wCACnC,MAAM,qBAAyC,CAAzC,IAAI/H,MAAM,iCAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAwC;oCAChD;oCAEA,OAAO+H;gCACT;4BAEJ;4BACA2R,SAAS9I,YAAY,GAAG+I,aAAaE,eAAe,CAAC/F,GAAG,CAAC9gB;wBAC3D;wBAEA,+DAA+D;wBAC/D,gEAAgE;wBAChE,YAAY;wBACZ,MAAM8mB,gBAAgB,CAAExF,CAAAA,SAAS7E,aAAa,CAAC+J,mBAAkB;wBAEjE,IAAIM,eAAe;4BACjB,MAAMzB,iBAAiBrlB,MAAMA,MAAMkF,MAAMoc,OAAO;wBAClD;wBAEA,IAAImF,UAAW,CAAA,CAACnF,SAAUA,SAAS,CAAC7E,SAAS,GAAI;4BAC/C,MAAMsK,UAAU,GAAG7hB,KAAK,IAAI,CAAC;4BAC7B,MAAMmgB,iBAAiBrlB,MAAM+mB,SAASA,SAASzF,OAAO;4BAEtD,IAAIA,OAAO;gCACT,MAAM+D,iBAAiBrlB,MAAM+mB,SAASA,SAASzF,OAAO;4BACxD;wBACF;wBAEA,IAAIA,OAAO;4BACT,yDAAyD;4BACzD,oDAAoD;4BACpD,IAAI,CAAC7E,WAAW;gCACd,MAAM4I,iBAAiBrlB,MAAMA,MAAMkF,MAAMoc,OAAO;gCAEhD,IAAI9O,MAAM;oCACR,+DAA+D;oCAC/D,KAAK,MAAMgP,UAAUhP,KAAKtQ,OAAO,CAAE;wCACjC,MAAM8kB,aAAa,CAAC,CAAC,EAAExF,SAASxhB,SAAS,MAAM,KAAKA,MAAM;wCAE1D,MAAM+hB,eAAeH,gBAAgBoF;wCAErChlB,kBAAkBO,MAAM,CAACykB,WAAW,GAAG;4CACrC/C,0BAA0BlC,aAAanF,UAAU;4CACjDsH,sBAAsBnC,aAAaE,MAAM;4CACzC8B,iBAAiBta;4CACjBma,eAAena;4CACfhH,UAAU;4CACV4gB,WAAW1sB,KAAK2sB,KAAK,CAAC3iB,IAAI,CACxB,eACAsB,SACA,GAAGiD,KAAK,KAAK,CAAC;4CAEhBqe,mBAAmB9Z;4CACnB0a,aAAarkB;wCACf;oCACF;gCACF,OAAO;oCACL,MAAMiiB,eAAeH,gBAAgB5hB;oCAErCgC,kBAAkBO,MAAM,CAACvC,KAAK,GAAG;wCAC/BikB,0BAA0BlC,aAAanF,UAAU;wCACjDsH,sBAAsBnC,aAAaE,MAAM;wCACzC8B,iBAAiBta;wCACjBma,eAAena;wCACfhH,UAAU;wCACV4gB,WAAW1sB,KAAK2sB,KAAK,CAAC3iB,IAAI,CACxB,eACAsB,SACA,GAAGiD,KAAK,KAAK,CAAC;wCAEhB,6CAA6C;wCAC7Cqe,mBAAmB9Z;wCACnB0a,aAAarkB;oCACf;gCACF;gCACA,IAAI4mB,UAAU;oCACZA,SAAS/I,mBAAmB,GAAGiE,gBAAgB5hB;gCACjD;4BACF,OAAO;gCACL,oEAAoE;gCACpE,4CAA4C;gCAC5C,iEAAiE;gCACjE,yCAAyC;gCACzC,KAAK,MAAM2C,SAASiU,gBAAgBkK,GAAG,CAAC9gB,SAAS,EAAE,CAAE;oCACnD,MAAMinB,WAAW7sB,kBAAkBuI,MAAMC,QAAQ;oCACjD,MAAMyiB,iBACJrlB,MACA2C,MAAMC,QAAQ,EACdqkB,UACA3F,OACA,QACA;oCAEF,MAAM+D,iBACJrlB,MACA2C,MAAMC,QAAQ,EACdqkB,UACA3F,OACA,QACA;oCAGF,IAAImF,QAAQ;wCACV,MAAMM,UAAU,GAAGE,SAAS,IAAI,CAAC;wCACjC,MAAM5B,iBACJrlB,MACA+mB,SACAA,SACAzF,OACA,QACA;wCAEF,MAAM+D,iBACJrlB,MACA+mB,SACAA,SACAzF,OACA,QACA;oCAEJ;oCAEA,MAAMS,eAAeH,gBAAgBjf,MAAMC,QAAQ;oCAEnDZ,kBAAkBO,MAAM,CAACI,MAAMC,QAAQ,CAAC,GAAG;wCACzCqhB,0BAA0BlC,aAAanF,UAAU;wCACjDsH,sBAAsBnC,aAAaE,MAAM;wCACzC8B,iBAAiBta;wCACjBma,eAAena;wCACfhH,UAAUzC;wCACVqjB,WAAW1sB,KAAK2sB,KAAK,CAAC3iB,IAAI,CACxB,eACAsB,SACA,GAAG7H,kBAAkBuI,MAAMC,QAAQ,EAAE,KAAK,CAAC;wCAE7C,6CAA6C;wCAC7C2gB,mBAAmB9Z;wCACnB0a,aAAarkB;oCACf;oCAEA,IAAI4mB,UAAU;wCACZA,SAAS/I,mBAAmB,GAAGoE;oCACjC;gCACF;4BACF;wBACF;oBACF;oBAEA,iCAAiC;oBACjC,MAAM3rB,GAAG8wB,EAAE,CAACxe,QAAQ;wBAAE7C,WAAW;wBAAMshB,OAAO;oBAAK;oBACnD,MAAM1lB,cAAcuS,mBAAmBmD;gBACzC;gBAEA,sEAAsE;gBACtE,sBAAsB;gBACtB,MAAM9S,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMvD,cAAcsQ,oBAAoBC;YAC1D;YAEA,MAAMoV,mBAAmB1rB,cAAc;YACvC,IAAI2rB,qBAAqB3rB,cAAc,CAAC,uBAAuB,CAAC;YAEhE,wCAAwC;YACxCgc,OAAO/O,GAAG;YAEV,MAAM2e,cAAc5f,QAAQkQ,MAAM,CAACD;YACnC1M,UAAUS,MAAM,CACdhR,mBAAmBoT,YAAY;gBAC7BwH,mBAAmBgS,WAAW,CAAC,EAAE;gBACjCC,iBAAiB3iB,YAAY6V,IAAI;gBACjC+M,sBAAsBrlB,SAASsY,IAAI;gBACnCgN,sBAAsB9Q,iBAAiB8D,IAAI;gBAC3CiN,cACE5Z,WAAWlD,MAAM,GAChBhG,CAAAA,YAAY6V,IAAI,GAAGtY,SAASsY,IAAI,GAAG9D,iBAAiB8D,IAAI,AAAD;gBAC1DkN,cAAcvI;gBACdwI,oBACE3P,CAAAA,gCAAAA,aAAc1S,QAAQ,CAAC,uBAAsB;gBAC/CsiB,eAAetd,iBAAiBK,MAAM;gBACtCkd,cAAc1d,QAAQQ,MAAM;gBAC5Bmd,gBAAgBzd,UAAUM,MAAM,GAAG;gBACnCod,qBAAqB5d,QAAQ5H,MAAM,CAAC,CAAC8P,IAAW,CAAC,CAACA,EAAE4N,GAAG,EAAEtV,MAAM;gBAC/Dqd,sBAAsB1d,iBAAiB/H,MAAM,CAAC,CAAC8P,IAAW,CAAC,CAACA,EAAE4N,GAAG,EAC9DtV,MAAM;gBACTsd,uBAAuB5d,UAAU9H,MAAM,CAAC,CAAC8P,IAAW,CAAC,CAACA,EAAE4N,GAAG,EAAEtV,MAAM;gBACnEud,iBAAiBvZ,oBAAoB,IAAI;gBACzCgC;gBACAuF;gBACAC;gBACAC;gBACAC;YACF;YAGF,IAAIxY,iBAAiBsqB,cAAc,EAAE;gBACnC,MAAMlc,SAAStR,uBACbkD,iBAAiBsqB,cAAc,CAACC,MAAM;gBAExCpd,UAAUS,MAAM,CAACQ;gBACjBjB,UAAUS,MAAM,CACd3Q,qCACE+C,iBAAiBsqB,cAAc,CAACE,6BAA6B;gBAGjE,MAAMC,kBAAkBzqB,iBAAiBsqB,cAAc,CAACG,eAAe;gBAEvE,KAAK,MAAM,CAAC/Q,KAAK+G,MAAM,IAAIlc,OAAOC,OAAO,CAACimB,iBAAkB;oBAC1Dtd,UAAUS,MAAM,CACd9Q,uBAAuB;wBACrB;4BACE0S,aAAakK;4BACbjK,iBAAiBgR;wBACnB;qBACD;gBAEL;YACF;YAEA,IAAIpc,SAASsY,IAAI,GAAG,KAAK3V,QAAQ;oBAmDpBzB;gBAlDX0c,mBAAmBE,OAAO,CAAC,CAACuI;oBAC1B,MAAMpF,kBAAkBhpB,kBAAkBouB;oBAC1C,MAAMnF,YAAY1sB,KAAK2sB,KAAK,CAAC3iB,IAAI,CAC/B,eACAsB,SACA,GAAGmhB,gBAAgB,KAAK,CAAC;oBAG3BphB,kBAAkBc,aAAa,CAAC0lB,SAAS,GAAG;wBAC1CvoB,YAAYlI,oBACViF,mBAAmBwrB,UAAU;4BAC3BtoB,iBAAiB;wBACnB,GAAGE,EAAE,CAACC,MAAM;wBAEd0jB,iBAAiBta;wBACjBma,eAAena;wBACf4Z;wBACA3Y,UAAU8L,yBAAyB0J,GAAG,CAACsI,YACnC,OACAjS,uBAAuB2J,GAAG,CAACsI,YACzB,GAAGpF,gBAAgB,KAAK,CAAC,GACzB;wBACNwB,oBAAoBnb;wBACpBob,gBAAgBpb;wBAChBub,qBAAqBvb;wBACrByT,oBAAoBzT;wBACpBwb,gBAAgBltB,oBACdiF,mBAAmBqmB,WAAW;4BAC5BnjB,iBAAiB;4BACjBglB,eAAe;4BACfC,8BAA8B;wBAChC,GAAG/kB,EAAE,CAACC,MAAM;wBAEd,6CAA6C;wBAC7CkjB,mBAAmB9Z;wBACnB2b,wBAAwB3b;wBACxB0a,aAAarkB;oBACf;gBACF;gBAEAhC,iBAAiBgR,aAAa,GAAGD,aAAaC,aAAa;gBAC3DhR,iBAAiB2qB,mBAAmB,GAClCplB,OAAO8C,YAAY,CAACsiB,mBAAmB;gBACzC3qB,iBAAiB4qB,2BAA2B,GAC1CrlB,OAAO8C,YAAY,CAACuiB,2BAA2B;gBAEjD,MAAM5mB,uBAAuBrB,SAASuB;gBACtC,MAAMD,uBAAuBC,mBAAmB;oBAC9CvB;oBACAwB;oBACAC,OAAO,GAAEmB,eAAAA,OAAOmP,IAAI,qBAAXnP,aAAanB,OAAO;gBAC/B;YACF,OAAO;gBACL,MAAMJ,uBAAuBrB,SAAS;oBACpCyD,SAAS;oBACT3B,QAAQ,CAAC;oBACTO,eAAe,CAAC;oBAChBgd,SAASjR;oBACTgR,gBAAgB,EAAE;gBACpB;YACF;YAEA,MAAMzc,oBAAoB3C,SAAS4C;YACnC,MAAM5B,cAAc9K,KAAKgK,IAAI,CAACF,SAASlI,gBAAgB;gBACrD2L,SAAS;gBACTykB,kBAAkB,OAAOtlB,OAAOqd,aAAa,KAAK;gBAClDkI,qBAAqBvlB,OAAOwlB,aAAa,KAAK;gBAC9C3Q,qBAAqBA,wBAAwB;YAC/C;YACA,MAAM9hB,GAAGosB,MAAM,CAAC7rB,KAAKgK,IAAI,CAACF,SAASnI,gBAAgBwd,KAAK,CAAC,CAAChJ;gBACxD,IAAIA,IAAIC,IAAI,KAAK,UAAU;oBACzB,OAAO+H,QAAQjO,OAAO;gBACxB;gBACA,OAAOiO,QAAQ2K,MAAM,CAAC3S;YACxB;YAEA,IAAIN,QAAQnJ,OAAO8C,YAAY,CAACwZ,iBAAiB,GAAG;gBAClD,MAAMtb,cACHU,UAAU,CAAC,0BACXC,YAAY,CAAC;oBACZ,MAAM9M,qBACJ8P,KACArR,KAAKgK,IAAI,CAACF,SAASpI;gBAEvB;YACJ;YAEA,MAAM6b;YAEN,IAAImT,oBAAoB;gBACtBA,mBAAmBnJ,cAAc;gBACjCmJ,qBAAqB5d;YACvB;YAEA,IAAIpG,OAAO+V,MAAM,KAAK,UAAU;gBAC9B,MAAMrR,uBACJ1E,QACA2E,KACAC,oBACAC,cACA7D;YAEJ;YAEA,IAAIhB,OAAO+V,MAAM,KAAK,cAAc;gBAClC,MAAMhV,yBACJC,eACA5D,SACA6D,UACAC,sBACAC,uBACA6Z,6BACA5Z,oBACAC,mBACAC,wBACAC,aACAC,gBACAC;YAEJ;YAEA,IAAIsiB,kBAAkBA,iBAAiBlJ,cAAc;YACrDnd,QAAQC,GAAG;YAEX,IAAIgI,aAAa;gBACf3E,cACGU,UAAU,CAAC,uBACXgF,OAAO,CAAC,IAAM9N,kBAAkB;wBAAEqO;wBAAWD;wBAAUD;oBAAQ;YACpE;YAEA,MAAM/F,cAAcU,UAAU,CAAC,mBAAmBC,YAAY,CAAC,IAC7D9I,cAAcoI,UAAU4S,WAAW;oBACjC4R,UAAUroB;oBACVwB,SAASA;oBACTkJ;oBACAiU;oBACAzR,gBAAgBtK,OAAOsK,cAAc;oBACrC0J;oBACAD;oBACA3S;oBACAoV,UAAUxW,OAAO8C,YAAY,CAAC0T,QAAQ;gBACxC;YAGF,MAAMxV,cACHU,UAAU,CAAC,mBACXC,YAAY,CAAC,IAAMiG,UAAUkC,KAAK;YAErC,MAAM0H;QACR;IACF,SAAU;QACR,kDAAkD;QAClD,MAAM/X,qBAAqBisB,GAAG;QAE9B,6DAA6D;QAC7D,MAAMntB;QACNmB;QAEA,IAAIuM,kBAAkBE,cAAc;YAClCvK,YAAY;gBACVqK;gBACA0f,MAAM;gBACNC,YAAYjhB;gBACZvH,SAAS+I,aAAa/I,OAAO;gBAC7ByoB,gBAAgB9f;gBAChB+f,MAAM;YACR;QACF;IACF;AACF;AAEA,SAAS/T,iBAAiBJ,gBAAwB;IAChD,IAAIG;IACJ,IAAIH,mBAAmB,KAAK;QAC1BG,iBAAiB,GAAG,AAACH,CAAAA,mBAAmB,EAAC,EAAGoU,OAAO,CAAC,GAAG,GAAG,CAAC;IAC7D,OAAO,IAAIpU,mBAAmB,IAAI;QAChCG,iBAAiB,GAAGH,iBAAiBoU,OAAO,CAAC,GAAG,CAAC,CAAC;IACpD,OAAO,IAAIpU,mBAAmB,GAAG;QAC/BG,iBAAiB,GAAGH,iBAAiBoU,OAAO,CAAC,GAAG,CAAC,CAAC;IACpD,OAAO;QACLjU,iBAAiB,GAAG,AAACH,CAAAA,mBAAmB,IAAG,EAAGoU,OAAO,CAAC,GAAG,EAAE,CAAC;IAC9D;IACA,OAAOjU;AACT"}