{"version": 3, "sources": ["../../../src/build/templates/pages.ts"], "sourcesContent": ["import { PagesRouteModule } from '../../server/route-modules/pages/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { hoist } from './helpers'\n\n// Import the app and document modules.\nimport * as document from 'VAR_MODULE_DOCUMENT'\nimport * as app from 'VAR_MODULE_APP'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\n\n// Re-export the component (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export methods.\nexport const getStaticProps = hoist(userland, 'getStaticProps')\nexport const getStaticPaths = hoist(userland, 'getStaticPaths')\nexport const getServerSideProps = hoist(userland, 'getServerSideProps')\nexport const config = hoist(userland, 'config')\nexport const reportWebVitals = hoist(userland, 'reportWebVitals')\n\n// Re-export legacy methods.\nexport const unstable_getStaticProps = hoist(\n  userland,\n  'unstable_getStaticProps'\n)\nexport const unstable_getStaticPaths = hoist(\n  userland,\n  'unstable_getStaticPaths'\n)\nexport const unstable_getStaticParams = hoist(\n  userland,\n  'unstable_getStaticParams'\n)\nexport const unstable_getServerProps = hoist(\n  userland,\n  'unstable_getServerProps'\n)\nexport const unstable_getServerSideProps = hoist(\n  userland,\n  'unstable_getServerSideProps'\n)\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesRouteModule({\n  definition: {\n    kind: RouteKind.PAGES,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  components: {\n    // default export might not exist when optimized for data only\n    App: app.default,\n    Document: document.default,\n  },\n  userland,\n})\n"], "names": ["PagesRouteModule", "RouteKind", "hoist", "document", "app", "userland", "getStaticProps", "getStaticPaths", "getServerSideProps", "config", "reportWebVitals", "unstable_getStaticProps", "unstable_getStaticPaths", "unstable_getStaticParams", "unstable_getServerProps", "unstable_getServerSideProps", "routeModule", "definition", "kind", "PAGES", "page", "pathname", "bundlePath", "filename", "components", "App", "default", "Document"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,KAAK,QAAQ,YAAW;AAEjC,uCAAuC;AACvC,YAAYC,cAAc,sBAAqB;AAC/C,YAAYC,SAAS,iBAAgB;AAErC,4BAA4B;AAC5B,YAAYC,cAAc,eAAc;AAExC,0DAA0D;AAC1D,eAAeH,MAAMG,UAAU,WAAU;AAEzC,qBAAqB;AACrB,OAAO,MAAMC,iBAAiBJ,MAAMG,UAAU,kBAAiB;AAC/D,OAAO,MAAME,iBAAiBL,MAAMG,UAAU,kBAAiB;AAC/D,OAAO,MAAMG,qBAAqBN,MAAMG,UAAU,sBAAqB;AACvE,OAAO,MAAMI,SAASP,MAAMG,UAAU,UAAS;AAC/C,OAAO,MAAMK,kBAAkBR,MAAMG,UAAU,mBAAkB;AAEjE,4BAA4B;AAC5B,OAAO,MAAMM,0BAA0BT,MACrCG,UACA,2BACD;AACD,OAAO,MAAMO,0BAA0BV,MACrCG,UACA,2BACD;AACD,OAAO,MAAMQ,2BAA2BX,MACtCG,UACA,4BACD;AACD,OAAO,MAAMS,0BAA0BZ,MACrCG,UACA,2BACD;AACD,OAAO,MAAMU,8BAA8Bb,MACzCG,UACA,+BACD;AAED,4DAA4D;AAC5D,OAAO,MAAMW,cAAc,IAAIhB,iBAAiB;IAC9CiB,YAAY;QACVC,MAAMjB,UAAUkB,KAAK;QACrBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAC,YAAY;QACV,8DAA8D;QAC9DC,KAAKrB,IAAIsB,OAAO;QAChBC,UAAUxB,SAASuB,OAAO;IAC5B;IACArB;AACF,GAAE"}