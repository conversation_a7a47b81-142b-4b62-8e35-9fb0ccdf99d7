{"version": 3, "sources": ["../../../src/build/templates/edge-app-route.ts"], "sourcesContent": ["import { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport type { NextConfigComplete } from '../../server/config-shared'\nimport { EdgeRouteModuleWrapper } from '../../server/web/edge-route-module-wrapper'\n\n// Import the userland code.\nimport * as module from 'VAR_USERLAND'\n\n// injected by the loader afterwards.\ndeclare const nextConfig: NextConfigComplete\n// INJECT:nextConfig\n\nconst maybeJSONParse = (str?: string) => (str ? JSON.parse(str) : undefined)\n\nconst rscManifest = self.__RSC_MANIFEST?.['VAR_PAGE']\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST)\n\nif (rscManifest && rscServerManifest) {\n  setReferenceManifestsSingleton({\n    page: 'VAR_PAGE',\n    clientReferenceManifest: rscManifest,\n    serverActionsManifest: rscServerManifest,\n    serverModuleMap: createServerModuleMap({\n      serverActionsManifest: rscServerManifest,\n    }),\n  })\n}\n\nexport const ComponentMod = module\n\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, { nextConfig })\n"], "names": ["self", "createServerModuleMap", "setReferenceManifestsSingleton", "EdgeRouteModuleWrapper", "module", "maybeJSONParse", "str", "JSON", "parse", "undefined", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "ComponentMod", "wrap", "routeModule", "nextConfig"], "mappings": "IAcoBA;AAdpB,SAASC,qBAAqB,QAAQ,uCAAsC;AAC5E,SAASC,8BAA8B,QAAQ,2CAA0C;AAEzF,SAASC,sBAAsB,QAAQ,6CAA4C;AAEnF,4BAA4B;AAC5B,YAAYC,YAAY,eAAc;AAItC,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,eAAcV,uBAAAA,KAAKW,cAAc,qBAAnBX,oBAAqB,CAAC,WAAW;AACrD,MAAMY,oBAAoBP,eAAeL,KAAKa,qBAAqB;AAEnE,IAAIH,eAAeE,mBAAmB;IACpCV,+BAA+B;QAC7BY,MAAM;QACNC,yBAAyBL;QACzBM,uBAAuBJ;QACvBK,iBAAiBhB,sBAAsB;YACrCe,uBAAuBJ;QACzB;IACF;AACF;AAEA,OAAO,MAAMM,eAAed,OAAM;AAElC,eAAeD,uBAAuBgB,IAAI,CAACf,OAAOgB,WAAW,EAAE;IAAEC;AAAW,GAAE"}