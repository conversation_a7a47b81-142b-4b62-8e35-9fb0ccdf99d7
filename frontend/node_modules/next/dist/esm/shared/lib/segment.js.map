{"version": 3, "sources": ["../../../src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["isGroupSegment", "segment", "endsWith", "isParallelRouteSegment", "startsWith", "addSearchParamsIfPageSegment", "searchParams", "isPageSegment", "includes", "PAGE_SEGMENT_KEY", "stringified<PERSON><PERSON>y", "JSON", "stringify", "DEFAULT_SEGMENT_KEY"], "mappings": "AAEA,OAAO,SAASA,eAAeC,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEA,OAAO,SAASC,uBAAuBF,OAAe;IACpD,OAAOA,QAAQG,UAAU,CAAC,QAAQH,YAAY;AAChD;AAEA,OAAO,SAASI,6BACdJ,OAAgB,EAChBK,YAA2D;IAE3D,MAAMC,gBAAgBN,QAAQO,QAAQ,CAACC;IAEvC,IAAIF,eAAe;QACjB,MAAMG,mBAAmBC,KAAKC,SAAS,CAACN;QACxC,OAAOI,qBAAqB,OACxBD,mBAAmB,MAAMC,mBACzBD;IACN;IAEA,OAAOR;AACT;AAEA,OAAO,MAAMQ,mBAAmB,WAAU;AAC1C,OAAO,MAAMI,sBAAsB,cAAa"}