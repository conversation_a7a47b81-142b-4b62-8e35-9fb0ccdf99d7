{"version": 3, "sources": ["../../../src/shared/lib/app-dynamic.tsx"], "sourcesContent": ["import type React from 'react'\nimport type { JSX } from 'react'\nimport Loadable from './lazy-dynamic/loadable'\n\nimport type {\n  LoadableGeneratedOptions,\n  DynamicOptionsLoadingProps,\n  Loader,\n  LoaderComponent,\n} from './lazy-dynamic/types'\n\nexport {\n  type LoadableGeneratedOptions,\n  type DynamicOptionsLoadingProps,\n  type Loader,\n  type LoaderComponent,\n}\n\nexport type DynamicOptions<P = {}> = LoadableGeneratedOptions & {\n  loading?: () => JSX.Element | null\n  loader?: Loader<P>\n  loadableGenerated?: LoadableGeneratedOptions\n  modules?: string[]\n  ssr?: boolean\n}\n\nexport type LoadableOptions<P = {}> = DynamicOptions<P>\n\nexport type LoadableFn<P = {}> = (\n  opts: LoadableOptions<P>\n) => React.ComponentType<P>\n\nexport type LoadableComponent<P = {}> = React.ComponentType<P>\n\nexport default function dynamic<P = {}>(\n  dynamicOptions: DynamicOptions<P> | Loader<P>,\n  options?: DynamicOptions<P>\n): React.ComponentType<P> {\n  const loadableOptions: LoadableOptions<P> = {}\n\n  if (typeof dynamicOptions === 'function') {\n    loadableOptions.loader = dynamicOptions\n  }\n\n  const mergedOptions = {\n    ...loadableOptions,\n    ...options,\n  }\n\n  return Loadable({\n    ...mergedOptions,\n    modules: mergedOptions.loadableGenerated?.modules,\n  })\n}\n"], "names": ["Loadable", "dynamic", "dynamicOptions", "options", "mergedOptions", "loadableOptions", "loader", "modules", "loadableGenerated"], "mappings": "AAEA,OAAOA,cAAc,0BAAyB;AAgC9C,eAAe,SAASC,QACtBC,cAA6C,EAC7CC,OAA2B;QAehBC;IAbX,MAAMC,kBAAsC,CAAC;IAE7C,IAAI,OAAOH,mBAAmB,YAAY;QACxCG,gBAAgBC,MAAM,GAAGJ;IAC3B;IAEA,MAAME,gBAAgB;QACpB,GAAGC,eAAe;QAClB,GAAGF,OAAO;IACZ;IAEA,OAAOH,SAAS;QACd,GAAGI,aAAa;QAChBG,OAAO,GAAEH,mCAAAA,cAAcI,iBAAiB,qBAA/BJ,iCAAiCG,OAAO;IACnD;AACF"}