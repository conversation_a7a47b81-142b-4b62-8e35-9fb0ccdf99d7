{"version": 3, "sources": ["../../../../src/shared/lib/router/action-queue.ts"], "sourcesContent": ["import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n} from '../../../client/components/router-reducer/router-reducer-types'\nimport { reducer } from '../../../client/components/router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../is-thenable'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // The rest of the current queue should still execute after this navigation.\n    // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n    newAction.next = actionQueue.pending.next\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nlet globalActionQueue: AppRouterActionQueue | null = null\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n  }\n\n  if (typeof window !== 'undefined') {\n    // The action queue is lazily created on hydration, but after that point\n    // it doesn't change. So we can store it in a global rather than pass\n    // it around everywhere via props/context.\n    if (globalActionQueue !== null) {\n      throw new Error(\n        'Internal Next.js Error: createMutableActionQueue was called more ' +\n          'than once'\n      )\n    }\n    globalActionQueue = actionQueue\n  }\n\n  return actionQueue\n}\n\nexport function getCurrentAppRouterState(): AppRouterState | null {\n  return globalActionQueue !== null ? globalActionQueue.state : null\n}\n"], "names": ["ACTION_REFRESH", "ACTION_SERVER_ACTION", "ACTION_NAVIGATE", "ACTION_RESTORE", "reducer", "startTransition", "isThenable", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "needsRefresh", "dispatch", "type", "origin", "window", "location", "prevState", "state", "payload", "actionResult", "handleResult", "nextState", "discarded", "resolve", "then", "err", "reject", "dispatchAction", "resolvers", "deferred<PERSON><PERSON><PERSON>", "Promise", "newAction", "last", "globalActionQueue", "createMutableActionQueue", "initialState", "result", "Error", "getCurrentAppRouterState"], "mappings": "AAAA,SAIEA,cAAc,EACdC,oBAAoB,EACpBC,eAAe,EACfC,cAAc,QACT,iEAAgE;AACvE,SAASC,OAAO,QAAQ,2DAA0D;AAClF,SAASC,eAAe,QAAQ,QAAO;AACvC,SAASC,UAAU,QAAQ,iBAAgB;AAqB3C,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF,OAAO;YACL,4DAA4D;YAC5D,IAAID,YAAYM,YAAY,EAAE;gBAC5BN,YAAYM,YAAY,GAAG;gBAC3BN,YAAYO,QAAQ,CAClB;oBACEC,MAAMhB;oBACNiB,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAR;YAEJ;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMW,YAAYZ,YAAYa,KAAK;IAEnCb,YAAYE,OAAO,GAAGG;IAEtB,MAAMS,UAAUT,OAAOS,OAAO;IAC9B,MAAMC,eAAef,YAAYK,MAAM,CAACO,WAAWE;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIZ,OAAOa,SAAS,EAAE;YACpB;QACF;QAEAlB,YAAYa,KAAK,GAAGI;QAEpBlB,oBAAoBC,aAAaC;QACjCI,OAAOc,OAAO,CAACF;IACjB;IAEA,8DAA8D;IAC9D,IAAInB,WAAWiB,eAAe;QAC5BA,aAAaK,IAAI,CAACJ,cAAc,CAACK;YAC/BtB,oBAAoBC,aAAaC;YACjCI,OAAOiB,MAAM,CAACD;QAChB;IACF,OAAO;QACLL,aAAaD;IACf;AACF;AAEA,SAASQ,eACPvB,WAAiC,EACjCc,OAAuB,EACvBb,QAA8B;IAE9B,IAAIuB,YAGA;QAAEL,SAASlB;QAAUqB,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIR,QAAQN,IAAI,KAAKb,gBAAgB;QACnC,6DAA6D;QAC7D,MAAM8B,kBAAkB,IAAIC,QAAwB,CAACP,SAASG;YAC5DE,YAAY;gBAAEL;gBAASG;YAAO;QAChC;QAEAzB,gBAAgB;YACd,oGAAoG;YACpG,iEAAiE;YACjEI,SAASwB;QACX;IACF;IAEA,MAAME,YAA6B;QACjCb;QACAX,MAAM;QACNgB,SAASK,UAAUL,OAAO;QAC1BG,QAAQE,UAAUF,MAAM;IAC1B;IAEA,8BAA8B;IAC9B,IAAItB,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAY4B,IAAI,GAAGD;QAEnBvB,UAAU;YACRJ;YACAK,QAAQsB;YACR1B;QACF;IACF,OAAO,IACLa,QAAQN,IAAI,KAAKd,mBACjBoB,QAAQN,IAAI,KAAKb,gBACjB;QACA,+EAA+E;QAC/E,oHAAoH;QACpHK,YAAYE,OAAO,CAACgB,SAAS,GAAG;QAEhC,4EAA4E;QAC5E,sIAAsI;QACtIS,UAAUxB,IAAI,GAAGH,YAAYE,OAAO,CAACC,IAAI;QAEzC,2GAA2G;QAC3G,IAAIH,YAAYE,OAAO,CAACY,OAAO,CAACN,IAAI,KAAKf,sBAAsB;YAC7DO,YAAYM,YAAY,GAAG;QAC7B;QAEAF,UAAU;YACRJ;YACAK,QAAQsB;YACR1B;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAY4B,IAAI,KAAK,MAAM;YAC7B5B,YAAY4B,IAAI,CAACzB,IAAI,GAAGwB;QAC1B;QACA3B,YAAY4B,IAAI,GAAGD;IACrB;AACF;AAEA,IAAIE,oBAAiD;AAErD,OAAO,SAASC,yBACdC,YAA4B;IAE5B,MAAM/B,cAAoC;QACxCa,OAAOkB;QACPxB,UAAU,CAACO,SAAyBb,WAClCsB,eAAevB,aAAac,SAASb;QACvCI,QAAQ,OAAOQ,OAAuBR;YACpC,MAAM2B,SAASpC,QAAQiB,OAAOR;YAC9B,OAAO2B;QACT;QACA9B,SAAS;QACT0B,MAAM;IACR;IAEA,IAAI,OAAOlB,WAAW,aAAa;QACjC,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1C,IAAImB,sBAAsB,MAAM;YAC9B,MAAM,qBAGL,CAHK,IAAII,MACR,sEACE,cAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACAJ,oBAAoB7B;IACtB;IAEA,OAAOA;AACT;AAEA,OAAO,SAASkC;IACd,OAAOL,sBAAsB,OAAOA,kBAAkBhB,KAAK,GAAG;AAChE"}