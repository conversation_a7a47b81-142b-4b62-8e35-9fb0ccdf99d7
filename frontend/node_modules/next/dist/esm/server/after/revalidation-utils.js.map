{"version": 3, "sources": ["../../../src/server/after/revalidation-utils.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\n/** Run a callback, and execute any *new* revalidations added during its runtime. */\nexport async function withExecuteRevalidates<T>(\n  store: WorkStore | undefined,\n  callback: () => Promise<T>\n): Promise<T> {\n  if (!store) {\n    return callback()\n  }\n  // If we executed any revalidates during the request, then we don't want to execute them again.\n  // save the state so we can check if anything changed after we're done running callbacks.\n  const savedRevalidationState = cloneRevalidationState(store)\n  try {\n    return await callback()\n  } finally {\n    // Check if we have any new revalidates, and if so, wait until they are all resolved.\n    const newRevalidates = diffRevalidationState(\n      savedRevalidationState,\n      cloneRevalidationState(store)\n    )\n    await executeRevalidates(store, newRevalidates)\n  }\n}\n\ntype RevalidationState = Required<\n  Pick<\n    WorkStore,\n    'revalidatedTags' | 'pendingRevalidates' | 'pendingRevalidateWrites'\n  >\n>\n\nfunction cloneRevalidationState(store: WorkStore): RevalidationState {\n  return {\n    revalidatedTags: store.revalidatedTags ? [...store.revalidatedTags] : [],\n    pendingRevalidates: { ...store.pendingRevalidates },\n    pendingRevalidateWrites: store.pendingRevalidateWrites\n      ? [...store.pendingRevalidateWrites]\n      : [],\n  }\n}\n\nfunction diffRevalidationState(\n  prev: RevalidationState,\n  curr: RevalidationState\n): RevalidationState {\n  const prevTags = new Set(prev.revalidatedTags)\n  const prevRevalidateWrites = new Set(prev.pendingRevalidateWrites)\n  return {\n    revalidatedTags: curr.revalidatedTags.filter((tag) => !prevTags.has(tag)),\n    pendingRevalidates: Object.fromEntries(\n      Object.entries(curr.pendingRevalidates).filter(\n        ([key]) => !(key in prev.pendingRevalidates)\n      )\n    ),\n    pendingRevalidateWrites: curr.pendingRevalidateWrites.filter(\n      (promise) => !prevRevalidateWrites.has(promise)\n    ),\n  }\n}\n\nasync function executeRevalidates(\n  workStore: WorkStore,\n  {\n    revalidatedTags,\n    pendingRevalidates,\n    pendingRevalidateWrites,\n  }: RevalidationState\n) {\n  return Promise.all([\n    workStore.incrementalCache?.revalidateTag(revalidatedTags),\n    ...Object.values(pendingRevalidates),\n    ...pendingRevalidateWrites,\n  ])\n}\n"], "names": ["withExecuteRevalidates", "store", "callback", "savedRevalidationState", "cloneRevalidationState", "newRevalidates", "diffRevalidationState", "executeRevalidates", "revalidatedTags", "pendingRevalidates", "pendingRevalidateWrites", "prev", "curr", "prevTags", "Set", "prevRevalidateWrites", "filter", "tag", "has", "Object", "fromEntries", "entries", "key", "promise", "workStore", "Promise", "all", "incrementalCache", "revalidateTag", "values"], "mappings": "AAEA,kFAAkF,GAClF,OAAO,eAAeA,uBACpBC,KAA4B,EAC5BC,QAA0B;IAE1B,IAAI,CAACD,OAAO;QACV,OAAOC;IACT;IACA,+FAA+F;IAC/F,yFAAyF;IACzF,MAAMC,yBAAyBC,uBAAuBH;IACtD,IAAI;QACF,OAAO,MAAMC;IACf,SAAU;QACR,qFAAqF;QACrF,MAAMG,iBAAiBC,sBACrBH,wBACAC,uBAAuBH;QAEzB,MAAMM,mBAAmBN,OAAOI;IAClC;AACF;AASA,SAASD,uBAAuBH,KAAgB;IAC9C,OAAO;QACLO,iBAAiBP,MAAMO,eAAe,GAAG;eAAIP,MAAMO,eAAe;SAAC,GAAG,EAAE;QACxEC,oBAAoB;YAAE,GAAGR,MAAMQ,kBAAkB;QAAC;QAClDC,yBAAyBT,MAAMS,uBAAuB,GAClD;eAAIT,MAAMS,uBAAuB;SAAC,GAClC,EAAE;IACR;AACF;AAEA,SAASJ,sBACPK,IAAuB,EACvBC,IAAuB;IAEvB,MAAMC,WAAW,IAAIC,IAAIH,KAAKH,eAAe;IAC7C,MAAMO,uBAAuB,IAAID,IAAIH,KAAKD,uBAAuB;IACjE,OAAO;QACLF,iBAAiBI,KAAKJ,eAAe,CAACQ,MAAM,CAAC,CAACC,MAAQ,CAACJ,SAASK,GAAG,CAACD;QACpER,oBAAoBU,OAAOC,WAAW,CACpCD,OAAOE,OAAO,CAACT,KAAKH,kBAAkB,EAAEO,MAAM,CAC5C,CAAC,CAACM,IAAI,GAAK,CAAEA,CAAAA,OAAOX,KAAKF,kBAAkB,AAAD;QAG9CC,yBAAyBE,KAAKF,uBAAuB,CAACM,MAAM,CAC1D,CAACO,UAAY,CAACR,qBAAqBG,GAAG,CAACK;IAE3C;AACF;AAEA,eAAehB,mBACbiB,SAAoB,EACpB,EACEhB,eAAe,EACfC,kBAAkB,EAClBC,uBAAuB,EACL;QAGlBc;IADF,OAAOC,QAAQC,GAAG,CAAC;SACjBF,8BAAAA,UAAUG,gBAAgB,qBAA1BH,4BAA4BI,aAAa,CAACpB;WACvCW,OAAOU,MAAM,CAACpB;WACdC;KACJ;AACH"}