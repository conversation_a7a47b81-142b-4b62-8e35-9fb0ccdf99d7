{"version": 3, "sources": ["../../../src/server/app-render/entry-base.ts"], "sourcesContent": ["// eslint-disable-next-line import/no-extraneous-dependencies\nexport {\n  createTemporaryReferenceSet,\n  renderToReadableStream,\n  decodeReply,\n  decodeAction,\n  decodeFormState,\n} from 'react-server-dom-webpack/server.edge'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport { unstable_prerender as prerender } from 'react-server-dom-webpack/static.edge'\n\nimport LayoutRouter from '../../client/components/layout-router'\nimport RenderFromTemplateContext from '../../client/components/render-from-template-context'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { actionAsyncStorage } from '../app-render/action-async-storage.external'\nimport { ClientPageRoot } from '../../client/components/client-page'\nimport { ClientSegmentRoot } from '../../client/components/client-segment'\nimport {\n  createServerSearchParamsForServerPage,\n  createPrerenderSearchParamsForClientPage,\n  createServerSearchParamsForMetadata,\n} from '../request/search-params'\nimport {\n  createServerParamsForServerSegment,\n  createServerParamsForMetadata,\n  createPrerenderParamsForClientSegment,\n} from '../request/params'\nimport * as serverHooks from '../../client/components/hooks-server-context'\nimport { HTTPAccessFallbackBoundary } from '../../client/components/http-access-fallback/error-boundary'\nimport { createMetadataComponents } from '../../lib/metadata/metadata'\nimport { patchFetch as _patchFetch } from '../lib/patch-fetch'\n// not being used but needs to be included in the client manifest for /_not-found\nimport '../../client/components/error-boundary'\nimport {\n  MetadataBoundary,\n  ViewportBoundary,\n  OutletBoundary,\n} from '../../client/components/metadata/metadata-boundary'\n\nimport { preloadStyle, preloadFont, preconnect } from './rsc/preloads'\nimport { Postpone } from './rsc/postpone'\nimport { taintObjectReference } from './rsc/taint'\nexport { collectSegmentData } from './collect-segment-data'\n\n// patchFetch makes use of APIs such as `React.unstable_postpone` which are only available\n// in the experimental channel of React, so export it from here so that it comes from the bundled runtime\nfunction patchFetch() {\n  return _patchFetch({\n    workAsyncStorage,\n    workUnitAsyncStorage,\n  })\n}\n\nexport {\n  LayoutRouter,\n  RenderFromTemplateContext,\n  workAsyncStorage,\n  workUnitAsyncStorage,\n  actionAsyncStorage,\n  createServerSearchParamsForServerPage,\n  createServerSearchParamsForMetadata,\n  createPrerenderSearchParamsForClientPage,\n  createServerParamsForServerSegment,\n  createServerParamsForMetadata,\n  createPrerenderParamsForClientSegment,\n  serverHooks,\n  preloadStyle,\n  preloadFont,\n  preconnect,\n  Postpone,\n  MetadataBoundary,\n  ViewportBoundary,\n  OutletBoundary,\n  taintObjectReference,\n  ClientPageRoot,\n  ClientSegmentRoot,\n  HTTPAccessFallbackBoundary,\n  patchFetch,\n  createMetadataComponents,\n}\n"], "names": ["createTemporaryReferenceSet", "renderToReadableStream", "decodeReply", "decodeAction", "decodeFormState", "unstable_prerender", "prerender", "LayoutRouter", "RenderFromTemplateContext", "workAsyncStorage", "workUnitAsyncStorage", "actionAsyncStorage", "ClientPageRoot", "ClientSegmentRoot", "createServerSearchParamsForServerPage", "createPrerenderSearchParamsForClientPage", "createServerSearchParamsForMetadata", "createServerParamsForServerSegment", "createServerParamsForMetadata", "createPrerenderParamsForClientSegment", "serverHooks", "HTTPAccessFallbackBoundary", "createMetadataComponents", "patchFetch", "_patchFetch", "MetadataBoundary", "ViewportBoundary", "OutletBoundary", "preloadStyle", "preloadFont", "preconnect", "Postpone", "taintObjectReference", "collectSegmentData"], "mappings": "AAAA,6DAA6D;AAC7D,SACEA,2BAA2B,EAC3BC,sBAAsB,EACtBC,WAAW,EACXC,YAAY,EACZC,eAAe,QACV,uCAAsC;AAE7C,6DAA6D;AAC7D,SAASC,sBAAsBC,SAAS,QAAQ,uCAAsC;AAEtF,OAAOC,kBAAkB,wCAAuC;AAChE,OAAOC,+BAA+B,uDAAsD;AAC5F,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,kBAAkB,QAAQ,8CAA6C;AAChF,SAASC,cAAc,QAAQ,sCAAqC;AACpE,SAASC,iBAAiB,QAAQ,yCAAwC;AAC1E,SACEC,qCAAqC,EACrCC,wCAAwC,EACxCC,mCAAmC,QAC9B,2BAA0B;AACjC,SACEC,kCAAkC,EAClCC,6BAA6B,EAC7BC,qCAAqC,QAChC,oBAAmB;AAC1B,YAAYC,iBAAiB,+CAA8C;AAC3E,SAASC,0BAA0B,QAAQ,8DAA6D;AACxG,SAASC,wBAAwB,QAAQ,8BAA6B;AACtE,SAASC,cAAcC,WAAW,QAAQ,qBAAoB;AAC9D,iFAAiF;AACjF,OAAO,yCAAwC;AAC/C,SACEC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,QACT,qDAAoD;AAE3D,SAASC,YAAY,EAAEC,WAAW,EAAEC,UAAU,QAAQ,iBAAgB;AACtE,SAASC,QAAQ,QAAQ,iBAAgB;AACzC,SAASC,oBAAoB,QAAQ,cAAa;AAClD,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,0FAA0F;AAC1F,yGAAyG;AACzG,SAASV;IACP,OAAOC,YAAY;QACjBf;QACAC;IACF;AACF;AAEA,SACEH,YAAY,EACZC,yBAAyB,EACzBC,gBAAgB,EAChBC,oBAAoB,EACpBC,kBAAkB,EAClBG,qCAAqC,EACrCE,mCAAmC,EACnCD,wCAAwC,EACxCE,kCAAkC,EAClCC,6BAA6B,EAC7BC,qCAAqC,EACrCC,WAAW,EACXQ,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRN,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,EACdK,oBAAoB,EACpBpB,cAAc,EACdC,iBAAiB,EACjBQ,0BAA0B,EAC1BE,UAAU,EACVD,wBAAwB,KACzB"}