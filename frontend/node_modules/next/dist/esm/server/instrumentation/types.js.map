{"version": 3, "sources": ["../../../src/server/instrumentation/types.ts"], "sourcesContent": ["export type RequestErrorContext = {\n  routerKind: 'Pages Router' | 'App Router'\n  routePath: string // the route file path, e.g. /app/blog/[dynamic]\n  routeType: 'render' | 'route' | 'action' | 'middleware'\n  renderSource?:\n    | 'react-server-components'\n    | 'react-server-components-payload'\n    | 'server-rendering'\n  revalidateReason: 'on-demand' | 'stale' | undefined\n  // TODO: other future instrumentation context\n}\n\nexport type InstrumentationOnRequestError = (\n  error: unknown,\n  errorRequest: Readonly<{\n    path: string\n    method: string\n    headers: NodeJS.Dict<string | string[]>\n  }>,\n  errorContext: Readonly<RequestErrorContext>\n) => void | Promise<void>\n\nexport type InstrumentationModule = {\n  register?(): void\n  onRequestError?: InstrumentationOnRequestError\n}\n\nexport namespace Instrumentation {\n  export type onRequestError = InstrumentationOnRequestError\n}\n"], "names": [], "mappings": "AA2BA,WAEC"}