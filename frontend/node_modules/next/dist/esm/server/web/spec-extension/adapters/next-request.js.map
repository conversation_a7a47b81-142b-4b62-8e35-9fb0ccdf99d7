{"version": 3, "sources": ["../../../../../src/server/web/spec-extension/adapters/next-request.ts"], "sourcesContent": ["import type { BaseNextRequest } from '../../../base-http'\nimport type { NodeNextRequest } from '../../../base-http/node'\nimport type { WebNextRequest } from '../../../base-http/web'\nimport type { Writable } from 'node:stream'\n\nimport { getRequestMeta } from '../../../request-meta'\nimport { fromNodeOutgoingHttpHeaders } from '../../utils'\nimport { NextRequest } from '../request'\nimport { isNodeNextRequest, isWebNextRequest } from '../../../base-http/helpers'\n\nexport const ResponseAbortedName = 'ResponseAborted'\nexport class ResponseAborted extends Error {\n  public readonly name = ResponseAbortedName\n}\n\n/**\n * Creates an AbortController tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * If the `close` event is fired before the `finish` event, then we'll send the\n * `abort` signal.\n */\nexport function createAbortController(response: Writable): AbortController {\n  const controller = new AbortController()\n\n  // If `finish` fires first, then `res.end()` has been called and the close is\n  // just us finishing the stream on our side. If `close` fires first, then we\n  // know the client disconnected before we finished.\n  response.once('close', () => {\n    if (response.writableFinished) return\n\n    controller.abort(new ResponseAborted())\n  })\n\n  return controller\n}\n\n/**\n * Creates an AbortSignal tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * This cannot be done with the request (IncomingMessage or Readable) because\n * the `abort` event will not fire if to data has been fully read (because that\n * will \"close\" the readable stream and nothing fires after that).\n */\nexport function signalFromNodeResponse(response: Writable): AbortSignal {\n  const { errored, destroyed } = response\n  if (errored || destroyed) {\n    return AbortSignal.abort(errored ?? new ResponseAborted())\n  }\n\n  const { signal } = createAbortController(response)\n  return signal\n}\n\nexport class NextRequestAdapter {\n  public static fromBaseNextRequest(\n    request: BaseNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromWebNextRequest(request)\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromNodeNextRequest(request, signal)\n    } else {\n      throw new Error('Invariant: Unsupported NextRequest type')\n    }\n  }\n\n  public static fromNodeNextRequest(\n    request: NodeNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: BodyInit | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD' && request.body) {\n      // @ts-expect-error - this is handled by undici, when streams/web land use it instead\n      body = request.body\n    }\n\n    let url: URL\n    if (request.url.startsWith('http')) {\n      url = new URL(request.url)\n    } else {\n      // Grab the full URL from the request metadata.\n      const base = getRequestMeta(request, 'initURL')\n      if (!base || !base.startsWith('http')) {\n        // Because the URL construction relies on the fact that the URL provided\n        // is absolute, we need to provide a base URL. We can't use the request\n        // URL because it's relative, so we use a dummy URL instead.\n        url = new URL(request.url, 'http://n')\n      } else {\n        url = new URL(request.url, base)\n      }\n    }\n\n    return new NextRequest(url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n\n  public static fromWebNextRequest(request: WebNextRequest): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: ReadableStream | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD') {\n      body = request.body\n    }\n\n    return new NextRequest(request.url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal: request.request.signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(request.request.signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n}\n"], "names": ["getRequestMeta", "fromNodeOutgoingHttpHeaders", "NextRequest", "isNodeNextRequest", "isWebNextRequest", "ResponseAbortedName", "ResponseAborted", "Error", "name", "createAbortController", "response", "controller", "AbortController", "once", "writableFinished", "abort", "signalFromNodeResponse", "errored", "destroyed", "AbortSignal", "signal", "NextRequestAdapter", "fromBaseNextRequest", "request", "process", "env", "NEXT_RUNTIME", "fromWebNextRequest", "fromNodeNextRequest", "body", "method", "url", "startsWith", "URL", "base", "headers", "duplex", "aborted"], "mappings": "AAKA,SAASA,cAAc,QAAQ,wBAAuB;AACtD,SAASC,2BAA2B,QAAQ,cAAa;AACzD,SAASC,WAAW,QAAQ,aAAY;AACxC,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,6BAA4B;AAEhF,OAAO,MAAMC,sBAAsB,kBAAiB;AACpD,OAAO,MAAMC,wBAAwBC;;QAA9B,qBACWC,OAAOH;;AACzB;AAEA;;;;;;CAMC,GACD,OAAO,SAASI,sBAAsBC,QAAkB;IACtD,MAAMC,aAAa,IAAIC;IAEvB,6EAA6E;IAC7E,4EAA4E;IAC5E,mDAAmD;IACnDF,SAASG,IAAI,CAAC,SAAS;QACrB,IAAIH,SAASI,gBAAgB,EAAE;QAE/BH,WAAWI,KAAK,CAAC,IAAIT;IACvB;IAEA,OAAOK;AACT;AAEA;;;;;;;CAOC,GACD,OAAO,SAASK,uBAAuBN,QAAkB;IACvD,MAAM,EAAEO,OAAO,EAAEC,SAAS,EAAE,GAAGR;IAC/B,IAAIO,WAAWC,WAAW;QACxB,OAAOC,YAAYJ,KAAK,CAACE,WAAW,IAAIX;IAC1C;IAEA,MAAM,EAAEc,MAAM,EAAE,GAAGX,sBAAsBC;IACzC,OAAOU;AACT;AAEA,OAAO,MAAMC;IACX,OAAcC,oBACZC,OAAwB,EACxBH,MAAmB,EACN;QACb,IACE,qEAAqE;QACrE,6DAA6D;QAC7DI,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BtB,iBAAiBmB,UACjB;YACA,OAAOF,mBAAmBM,kBAAkB,CAACJ;QAC/C,OAAO,IACL,qEAAqE;QACrE,6DAA6D;QAC7DC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BvB,kBAAkBoB,UAClB;YACA,OAAOF,mBAAmBO,mBAAmB,CAACL,SAASH;QACzD,OAAO;YACL,MAAM,qBAAoD,CAApD,IAAIb,MAAM,4CAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D;IACF;IAEA,OAAcqB,oBACZL,OAAwB,EACxBH,MAAmB,EACN;QACb,6CAA6C;QAC7C,IAAIS,OAAwB;QAC5B,IAAIN,QAAQO,MAAM,KAAK,SAASP,QAAQO,MAAM,KAAK,UAAUP,QAAQM,IAAI,EAAE;YACzE,qFAAqF;YACrFA,OAAON,QAAQM,IAAI;QACrB;QAEA,IAAIE;QACJ,IAAIR,QAAQQ,GAAG,CAACC,UAAU,CAAC,SAAS;YAClCD,MAAM,IAAIE,IAAIV,QAAQQ,GAAG;QAC3B,OAAO;YACL,+CAA+C;YAC/C,MAAMG,OAAOlC,eAAeuB,SAAS;YACrC,IAAI,CAACW,QAAQ,CAACA,KAAKF,UAAU,CAAC,SAAS;gBACrC,wEAAwE;gBACxE,uEAAuE;gBACvE,4DAA4D;gBAC5DD,MAAM,IAAIE,IAAIV,QAAQQ,GAAG,EAAE;YAC7B,OAAO;gBACLA,MAAM,IAAIE,IAAIV,QAAQQ,GAAG,EAAEG;YAC7B;QACF;QAEA,OAAO,IAAIhC,YAAY6B,KAAK;YAC1BD,QAAQP,QAAQO,MAAM;YACtBK,SAASlC,4BAA4BsB,QAAQY,OAAO;YACpDC,QAAQ;YACRhB;YACA,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIA,OAAOiB,OAAO,GACd,CAAC,IACD;gBACER;YACF,CAAC;QACP;IACF;IAEA,OAAcF,mBAAmBJ,OAAuB,EAAe;QACrE,6CAA6C;QAC7C,IAAIM,OAA8B;QAClC,IAAIN,QAAQO,MAAM,KAAK,SAASP,QAAQO,MAAM,KAAK,QAAQ;YACzDD,OAAON,QAAQM,IAAI;QACrB;QAEA,OAAO,IAAI3B,YAAYqB,QAAQQ,GAAG,EAAE;YAClCD,QAAQP,QAAQO,MAAM;YACtBK,SAASlC,4BAA4BsB,QAAQY,OAAO;YACpDC,QAAQ;YACRhB,QAAQG,QAAQA,OAAO,CAACH,MAAM;YAC9B,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIG,QAAQA,OAAO,CAACH,MAAM,CAACiB,OAAO,GAC9B,CAAC,IACD;gBACER;YACF,CAAC;QACP;IACF;AACF"}