{"version": 3, "sources": ["../../../../src/server/route-matcher-providers/helpers/cached-route-matcher-provider.ts"], "sourcesContent": ["import type { RouteMatcherProvider } from '../route-matcher-provider'\nimport type { RouteMatcher } from '../../route-matchers/route-matcher'\n\ninterface LoaderComparable<D> {\n  load(): Promise<D>\n  compare(left: D, right: D): boolean\n}\n\n/**\n * This will memoize the matchers if the loaded data is comparable.\n */\nexport abstract class CachedRouteMatcherProvider<\n  M extends RouteMatcher = RouteMatcher,\n  D = any,\n> implements RouteMatcherProvider<M>\n{\n  private data?: D\n  private cached: ReadonlyArray<M> = []\n\n  constructor(private readonly loader: LoaderComparable<D>) {}\n\n  protected abstract transform(data: D): Promise<ReadonlyArray<M>>\n\n  public async matchers(): Promise<readonly M[]> {\n    const data = await this.loader.load()\n    if (!data) return []\n\n    // Return the cached matchers if the data has not changed.\n    if (this.data && this.loader.compare(this.data, data)) return this.cached\n    this.data = data\n\n    // Transform the manifest into matchers.\n    const matchers = await this.transform(data)\n\n    // Cache the matchers.\n    this.cached = matchers\n\n    return matchers\n  }\n}\n"], "names": ["CachedRouteMatcherProvider", "constructor", "loader", "cached", "matchers", "data", "load", "compare", "transform"], "mappings": "AAQA;;CAEC,GACD,OAAO,MAAeA;IAQpBC,YAAY,AAAiBC,MAA2B,CAAE;aAA7BA,SAAAA;aAFrBC,SAA2B,EAAE;IAEsB;IAI3D,MAAaC,WAAkC;QAC7C,MAAMC,OAAO,MAAM,IAAI,CAACH,MAAM,CAACI,IAAI;QACnC,IAAI,CAACD,MAAM,OAAO,EAAE;QAEpB,0DAA0D;QAC1D,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,CAACH,MAAM,CAACK,OAAO,CAAC,IAAI,CAACF,IAAI,EAAEA,OAAO,OAAO,IAAI,CAACF,MAAM;QACzE,IAAI,CAACE,IAAI,GAAGA;QAEZ,wCAAwC;QACxC,MAAMD,WAAW,MAAM,IAAI,CAACI,SAAS,CAACH;QAEtC,sBAAsB;QACtB,IAAI,CAACF,MAAM,GAAGC;QAEd,OAAOA;IACT;AACF"}