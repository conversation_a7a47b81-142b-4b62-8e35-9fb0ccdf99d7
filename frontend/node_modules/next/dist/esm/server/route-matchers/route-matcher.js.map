{"version": 3, "sources": ["../../../src/server/route-matchers/route-matcher.ts"], "sourcesContent": ["import type { RouteMatch } from '../route-matches/route-match'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { Params } from '../request/params'\n\nimport { isDynamicRoute } from '../../shared/lib/router/utils'\nimport {\n  getRouteMatcher,\n  type RouteMatchFn,\n} from '../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\n\ntype RouteMatchResult = {\n  params?: Params\n}\n\nexport class RouteMatcher<D extends RouteDefinition = RouteDefinition> {\n  private readonly dynamic?: RouteMatchFn\n\n  /**\n   * When set, this is an array of all the other matchers that are duplicates of\n   * this one. This is used by the managers to warn the users about possible\n   * duplicate matches on routes.\n   */\n  public duplicated?: Array<RouteMatcher>\n\n  constructor(public readonly definition: D) {\n    if (isDynamicRoute(definition.pathname)) {\n      this.dynamic = getRouteMatcher(getRouteRegex(definition.pathname))\n    }\n  }\n\n  /**\n   * Identity returns the identity part of the matcher. This is used to compare\n   * a unique matcher to another. This is also used when sorting dynamic routes,\n   * so it must contain the pathname part.\n   */\n  public get identity(): string {\n    return this.definition.pathname\n  }\n\n  public get isDynamic() {\n    return this.dynamic !== undefined\n  }\n\n  public match(pathname: string): RouteMatch<D> | null {\n    const result = this.test(pathname)\n    if (!result) return null\n\n    return { definition: this.definition, params: result.params }\n  }\n\n  public test(pathname: string): RouteMatchResult | null {\n    if (this.dynamic) {\n      const params = this.dynamic(pathname)\n      if (!params) return null\n\n      return { params }\n    }\n\n    if (pathname === this.definition.pathname) {\n      return {}\n    }\n\n    return null\n  }\n}\n"], "names": ["isDynamicRoute", "getRouteMatcher", "getRouteRegex", "RouteMatcher", "constructor", "definition", "pathname", "dynamic", "identity", "isDynamic", "undefined", "match", "result", "test", "params"], "mappings": "AAIA,SAASA,cAAc,QAAQ,gCAA+B;AAC9D,SACEC,eAAe,QAEV,8CAA6C;AACpD,SAASC,aAAa,QAAQ,4CAA2C;AAMzE,OAAO,MAAMC;IAUXC,YAAY,AAAgBC,UAAa,CAAE;aAAfA,aAAAA;QAC1B,IAAIL,eAAeK,WAAWC,QAAQ,GAAG;YACvC,IAAI,CAACC,OAAO,GAAGN,gBAAgBC,cAAcG,WAAWC,QAAQ;QAClE;IACF;IAEA;;;;GAIC,GACD,IAAWE,WAAmB;QAC5B,OAAO,IAAI,CAACH,UAAU,CAACC,QAAQ;IACjC;IAEA,IAAWG,YAAY;QACrB,OAAO,IAAI,CAACF,OAAO,KAAKG;IAC1B;IAEOC,MAAML,QAAgB,EAAwB;QACnD,MAAMM,SAAS,IAAI,CAACC,IAAI,CAACP;QACzB,IAAI,CAACM,QAAQ,OAAO;QAEpB,OAAO;YAAEP,YAAY,IAAI,CAACA,UAAU;YAAES,QAAQF,OAAOE,MAAM;QAAC;IAC9D;IAEOD,KAAKP,QAAgB,EAA2B;QACrD,IAAI,IAAI,CAACC,OAAO,EAAE;YAChB,MAAMO,SAAS,IAAI,CAACP,OAAO,CAACD;YAC5B,IAAI,CAACQ,QAAQ,OAAO;YAEpB,OAAO;gBAAEA;YAAO;QAClB;QAEA,IAAIR,aAAa,IAAI,CAACD,UAAU,CAACC,QAAQ,EAAE;YACzC,OAAO,CAAC;QACV;QAEA,OAAO;IACT;AACF"}