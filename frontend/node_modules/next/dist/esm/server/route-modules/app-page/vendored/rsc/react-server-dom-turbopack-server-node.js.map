{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-node.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactServerDOMTurbopackServerNode\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMTurbopackServerNode"], "mappings": "AAAAA,OAAOC,OAAO,GAAGC,QAAQ,yBAAyBC,QAAQ,CACxD,YACD,CAACC,iCAAiC"}