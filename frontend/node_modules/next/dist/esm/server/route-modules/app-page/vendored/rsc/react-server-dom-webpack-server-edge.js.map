{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactServerDOMWebpackServerEdge\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMWebpackServerEdge"], "mappings": "AAAAA,OAAOC,OAAO,GAAGC,QAAQ,yBAAyBC,QAAQ,CACxD,YACD,CAACC,+BAA+B"}