{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/ssr/entrypoints.ts"], "sourcesContent": ["import * as React from 'react'\nimport * as ReactDOM from 'react-dom'\nimport * as ReactJsxDevRuntime from 'react/jsx-dev-runtime'\nimport * as ReactJsxRuntime from 'react/jsx-runtime'\nimport * as ReactCompilerRuntime from 'react/compiler-runtime'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as ReactDOMServerEdge from 'react-dom/server.edge'\n\nfunction getAltProxyForBindingsDEV(\n  type: 'Turbopack' | 'Webpack',\n  pkg:\n    | 'react-server-dom-turbopack/client.edge'\n    | 'react-server-dom-webpack/client.edge'\n) {\n  if (process.env.NODE_ENV === 'development') {\n    const altType = type === 'Turbopack' ? 'Webpack' : 'Turbopack'\n    const altPkg = pkg.replace(new RegExp(type, 'gi'), altType.toLowerCase())\n\n    return new Proxy(\n      {},\n      {\n        get(_, prop: string) {\n          throw new Error(\n            `Expected to use ${type} bindings (${pkg}) for React but the current process is referencing '${prop}' from the ${altType} bindings (${altPkg}). This is likely a bug in our integration of the Next.js server runtime.`\n          )\n        },\n      }\n    )\n  }\n}\n\nlet ReactServerDOMTurbopackClientEdge, ReactServerDOMWebpackClientEdge\nif (process.env.TURBOPACK) {\n  // eslint-disable-next-line import/no-extraneous-dependencies\n  ReactServerDOMTurbopackClientEdge = require('react-server-dom-turbopack/client.edge')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackClientEdge = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/client.edge'\n    )\n  }\n} else {\n  // eslint-disable-next-line import/no-extraneous-dependencies\n  ReactServerDOMWebpackClientEdge = require('react-server-dom-webpack/client.edge')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackClientEdge = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/client.edge'\n    )\n  }\n}\n\nexport {\n  React,\n  ReactJsxDevRuntime,\n  ReactJsxRuntime,\n  ReactCompilerRuntime,\n  ReactDOM,\n  ReactDOMServerEdge,\n  ReactServerDOMTurbopackClientEdge,\n  ReactServerDOMWebpackClientEdge,\n}\n"], "names": ["React", "ReactDOM", "ReactJsxDevRuntime", "ReactJsxRuntime", "ReactCompilerRuntime", "ReactDOMServerEdge", "getAltProxyForBindingsDEV", "type", "pkg", "process", "env", "NODE_ENV", "altType", "altPkg", "replace", "RegExp", "toLowerCase", "Proxy", "get", "_", "prop", "Error", "ReactServerDOMTurbopackClientEdge", "ReactServerDOMWebpackClientEdge", "TURBOPACK", "require"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,YAAYC,cAAc,YAAW;AACrC,YAAYC,wBAAwB,wBAAuB;AAC3D,YAAYC,qBAAqB,oBAAmB;AACpD,YAAYC,0BAA0B,yBAAwB;AAE9D,6DAA6D;AAC7D,YAAYC,wBAAwB,wBAAuB;AAE3D,SAASC,0BACPC,IAA6B,EAC7BC,GAE0C;IAE1C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAMC,UAAUL,SAAS,cAAc,YAAY;QACnD,MAAMM,SAASL,IAAIM,OAAO,CAAC,IAAIC,OAAOR,MAAM,OAAOK,QAAQI,WAAW;QAEtE,OAAO,IAAIC,MACT,CAAC,GACD;YACEC,KAAIC,CAAC,EAAEC,IAAY;gBACjB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,gBAAgB,EAAEd,KAAK,WAAW,EAAEC,IAAI,oDAAoD,EAAEY,KAAK,WAAW,EAAER,QAAQ,WAAW,EAAEC,OAAO,yEAAyE,CAAC,GADnN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IAEJ;AACF;AAEA,IAAIS,mCAAmCC;AACvC,IAAId,QAAQC,GAAG,CAACc,SAAS,EAAE;IACzB,6DAA6D;IAC7DF,oCAAoCG,QAAQ;IAC5C,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CY,kCAAkCjB,0BAChC,aACA;IAEJ;AACF,OAAO;IACL,6DAA6D;IAC7DiB,kCAAkCE,QAAQ;IAC1C,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CW,oCAAoChB,0BAClC,WACA;IAEJ;AACF;AAEA,SACEN,KAAK,EACLE,kBAAkB,EAClBC,eAAe,EACfC,oBAAoB,EACpBH,QAAQ,EACRI,kBAAkB,EAClBiB,iCAAiC,EACjCC,+BAA+B,KAChC"}