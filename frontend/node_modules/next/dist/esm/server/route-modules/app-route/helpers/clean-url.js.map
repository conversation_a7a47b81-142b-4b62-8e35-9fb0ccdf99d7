{"version": 3, "sources": ["../../../../../src/server/route-modules/app-route/helpers/clean-url.ts"], "sourcesContent": ["/**\n * Cleans a URL by stripping the protocol, host, and search params.\n *\n * @param urlString the url to clean\n * @returns the cleaned url\n */\n\nexport function cleanURL(url: string | URL): URL {\n  const u = new URL(url)\n  u.host = 'localhost:3000'\n  u.search = ''\n  u.protocol = 'http'\n  return u\n}\n"], "names": ["cleanURL", "url", "u", "URL", "host", "search", "protocol"], "mappings": "AAAA;;;;;CAKC,GAED,OAAO,SAASA,SAASC,GAAiB;IACxC,MAAMC,IAAI,IAAIC,IAAIF;IAClBC,EAAEE,IAAI,GAAG;IACTF,EAAEG,MAAM,GAAG;IACXH,EAAEI,QAAQ,GAAG;IACb,OAAOJ;AACT"}