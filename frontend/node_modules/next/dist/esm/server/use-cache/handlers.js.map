{"version": 3, "sources": ["../../../src/server/use-cache/handlers.ts"], "sourcesContent": ["import DefaultCacheHandler from '../lib/cache-handlers/default'\nimport type { <PERSON>ache<PERSON>andler } from '../lib/cache-handlers/types'\n\nconst debug = process.env.NEXT_PRIVATE_DEBUG_USE_CACHE\n  ? (message: string, ...args: any[]) => {\n      console.log(`use-cache[${process.pid}]: ${message}`, ...args)\n    }\n  : () => {}\n\nconst handlersSymbol = Symbol.for('@next/cache-handlers')\nconst handlersMapSymbol = Symbol.for('@next/cache-handlers-map')\nconst handlersSetSymbol = Symbol.for('@next/cache-handlers-set')\n\n/**\n * The reference to the cache handlers. We store the cache handlers on the\n * global object so that we can access the same instance across different\n * boundaries (such as different copies of the same module).\n */\nconst reference: typeof globalThis & {\n  [handlersSymbol]?: {\n    RemoteCache?: CacheHandler\n    DefaultCache?: CacheHandler\n  }\n  [handlersMapSymbol]?: Map<string, CacheHandler>\n  [handlersSetSymbol]?: Set<CacheHandler>\n} = globalThis\n\n/**\n * Initialize the cache handlers.\n * @returns `true` if the cache handlers were initialized, `false` if they were already initialized.\n */\nexport function initializeCacheHandlers(): boolean {\n  // If the cache handlers have already been initialized, don't do it again.\n  if (reference[handlersMapSymbol]) {\n    debug('cache handlers already initialized')\n    return false\n  }\n\n  debug('initializing cache handlers')\n  reference[handlersMapSymbol] = new Map<string, CacheHandler>()\n\n  // Initialize the cache from the symbol contents first.\n  if (reference[handlersSymbol]) {\n    let fallback: CacheHandler\n    if (reference[handlersSymbol].DefaultCache) {\n      debug('setting \"default\" cache handler from symbol')\n      fallback = reference[handlersSymbol].DefaultCache\n    } else {\n      debug('setting \"default\" cache handler from default')\n      fallback = DefaultCacheHandler\n    }\n\n    reference[handlersMapSymbol].set('default', fallback)\n\n    if (reference[handlersSymbol].RemoteCache) {\n      debug('setting \"remote\" cache handler from symbol')\n      reference[handlersMapSymbol].set(\n        'remote',\n        reference[handlersSymbol].RemoteCache\n      )\n    } else {\n      debug('setting \"remote\" cache handler from default')\n      reference[handlersMapSymbol].set('remote', fallback)\n    }\n  } else {\n    debug('setting \"default\" cache handler from default')\n    reference[handlersMapSymbol].set('default', DefaultCacheHandler)\n    debug('setting \"remote\" cache handler from default')\n    reference[handlersMapSymbol].set('remote', DefaultCacheHandler)\n  }\n\n  // Create a set of the cache handlers.\n  reference[handlersSetSymbol] = new Set(reference[handlersMapSymbol].values())\n\n  return true\n}\n\n/**\n * Get a cache handler by kind.\n * @param kind - The kind of cache handler to get.\n * @returns The cache handler, or `undefined` if it is not initialized or does not exist.\n */\nexport function getCacheHandler(kind: string): CacheHandler | undefined {\n  // This should never be called before initializeCacheHandlers.\n  if (!reference[handlersMapSymbol]) {\n    throw new Error('Cache handlers not initialized')\n  }\n\n  return reference[handlersMapSymbol].get(kind)\n}\n\n/**\n * Get an iterator over the cache handlers.\n * @returns An iterator over the cache handlers, or `undefined` if they are not initialized.\n */\nexport function getCacheHandlers(): SetIterator<CacheHandler> | undefined {\n  if (!reference[handlersSetSymbol]) {\n    return undefined\n  }\n\n  return reference[handlersSetSymbol].values()\n}\n\n/**\n * Set a cache handler by kind.\n * @param kind - The kind of cache handler to set.\n * @param cacheHandler - The cache handler to set.\n */\nexport function setCacheHandler(\n  kind: string,\n  cacheHandler: CacheHandler\n): void {\n  // This should never be called before initializeCacheHandlers.\n  if (!reference[handlersMapSymbol] || !reference[handlersSetSymbol]) {\n    throw new Error('Cache handlers not initialized')\n  }\n\n  debug('setting cache handler for \"%s\"', kind)\n  reference[handlersMapSymbol].set(kind, cacheHandler)\n  reference[handlersSetSymbol].add(cacheHandler)\n}\n"], "names": ["DefaultCache<PERSON>andler", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_USE_CACHE", "message", "args", "console", "log", "pid", "handlersSymbol", "Symbol", "for", "handlersMapSymbol", "handlersSetSymbol", "reference", "globalThis", "initializeCacheHandlers", "Map", "fallback", "DefaultCache", "set", "RemoteCache", "Set", "values", "getCache<PERSON><PERSON><PERSON>", "kind", "Error", "get", "getCacheHandlers", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cache<PERSON><PERSON><PERSON>", "add"], "mappings": "AAAA,OAAOA,yBAAyB,gCAA+B;AAG/D,MAAMC,QAAQC,QAAQC,GAAG,CAACC,4BAA4B,GAClD,CAACC,SAAiB,GAAGC;IACnBC,QAAQC,GAAG,CAAC,CAAC,UAAU,EAAEN,QAAQO,GAAG,CAAC,GAAG,EAAEJ,SAAS,KAAKC;AAC1D,IACA,KAAO;AAEX,MAAMI,iBAAiBC,OAAOC,GAAG,CAAC;AAClC,MAAMC,oBAAoBF,OAAOC,GAAG,CAAC;AACrC,MAAME,oBAAoBH,OAAOC,GAAG,CAAC;AAErC;;;;CAIC,GACD,MAAMG,YAOFC;AAEJ;;;CAGC,GACD,OAAO,SAASC;IACd,0EAA0E;IAC1E,IAAIF,SAAS,CAACF,kBAAkB,EAAE;QAChCZ,MAAM;QACN,OAAO;IACT;IAEAA,MAAM;IACNc,SAAS,CAACF,kBAAkB,GAAG,IAAIK;IAEnC,uDAAuD;IACvD,IAAIH,SAAS,CAACL,eAAe,EAAE;QAC7B,IAAIS;QACJ,IAAIJ,SAAS,CAACL,eAAe,CAACU,YAAY,EAAE;YAC1CnB,MAAM;YACNkB,WAAWJ,SAAS,CAACL,eAAe,CAACU,YAAY;QACnD,OAAO;YACLnB,MAAM;YACNkB,WAAWnB;QACb;QAEAe,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAAC,WAAWF;QAE5C,IAAIJ,SAAS,CAACL,eAAe,CAACY,WAAW,EAAE;YACzCrB,MAAM;YACNc,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAC9B,UACAN,SAAS,CAACL,eAAe,CAACY,WAAW;QAEzC,OAAO;YACLrB,MAAM;YACNc,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAAC,UAAUF;QAC7C;IACF,OAAO;QACLlB,MAAM;QACNc,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAAC,WAAWrB;QAC5CC,MAAM;QACNc,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAAC,UAAUrB;IAC7C;IAEA,sCAAsC;IACtCe,SAAS,CAACD,kBAAkB,GAAG,IAAIS,IAAIR,SAAS,CAACF,kBAAkB,CAACW,MAAM;IAE1E,OAAO;AACT;AAEA;;;;CAIC,GACD,OAAO,SAASC,gBAAgBC,IAAY;IAC1C,8DAA8D;IAC9D,IAAI,CAACX,SAAS,CAACF,kBAAkB,EAAE;QACjC,MAAM,qBAA2C,CAA3C,IAAIc,MAAM,mCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0C;IAClD;IAEA,OAAOZ,SAAS,CAACF,kBAAkB,CAACe,GAAG,CAACF;AAC1C;AAEA;;;CAGC,GACD,OAAO,SAASG;IACd,IAAI,CAACd,SAAS,CAACD,kBAAkB,EAAE;QACjC,OAAOgB;IACT;IAEA,OAAOf,SAAS,CAACD,kBAAkB,CAACU,MAAM;AAC5C;AAEA;;;;CAIC,GACD,OAAO,SAASO,gBACdL,IAAY,EACZM,YAA0B;IAE1B,8DAA8D;IAC9D,IAAI,CAACjB,SAAS,CAACF,kBAAkB,IAAI,CAACE,SAAS,CAACD,kBAAkB,EAAE;QAClE,MAAM,qBAA2C,CAA3C,IAAIa,MAAM,mCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0C;IAClD;IAEA1B,MAAM,kCAAkCyB;IACxCX,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAACK,MAAMM;IACvCjB,SAAS,CAACD,kBAAkB,CAACmB,GAAG,CAACD;AACnC"}