{"version": 3, "sources": ["../../src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  const hangingPromise = new Promise<T>((_, reject) => {\n    signal.addEventListener(\n      'abort',\n      () => {\n        reject(new HangingPromiseRejectionError(expression))\n      },\n      { once: true }\n    )\n  })\n  // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n  // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n  // your own promise out of it you'll need to ensure you handle the error when it rejects.\n  hangingPromise.catch(ignoreReject)\n  return hangingPromise\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "makeHangingPromise", "signal", "hanging<PERSON>romise", "Promise", "_", "reject", "addEventListener", "once", "catch", "ignoreReject"], "mappings": "AAAA,OAAO,SAASA,+BACdC,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAAY,AAAgBC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,QAFnRA,aAAAA,iBAFZL,SAASC;IAMzB;AACF;AAEA;;;;;;CAMC,GACD,OAAO,SAASK,mBACdC,MAAmB,EACnBF,UAAkB;IAElB,MAAMG,iBAAiB,IAAIC,QAAW,CAACC,GAAGC;QACxCJ,OAAOK,gBAAgB,CACrB,SACA;YACED,OAAO,IAAIT,6BAA6BG;QAC1C,GACA;YAAEQ,MAAM;QAAK;IAEjB;IACA,2GAA2G;IAC3G,6GAA6G;IAC7G,yFAAyF;IACzFL,eAAeM,KAAK,CAACC;IACrB,OAAOP;AACT;AAEA,SAASO,gBAAgB"}