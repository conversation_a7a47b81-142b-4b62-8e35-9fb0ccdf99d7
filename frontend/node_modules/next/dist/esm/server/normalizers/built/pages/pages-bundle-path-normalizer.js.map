{"version": 3, "sources": ["../../../../../src/server/normalizers/built/pages/pages-bundle-path-normalizer.ts"], "sourcesContent": ["import { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\nimport type { Normalizer } from '../../normalizer'\nimport { Normalizers } from '../../normalizers'\nimport { PrefixingNormalizer } from '../../prefixing-normalizer'\nimport { wrapNormalizerFn } from '../../wrap-normalizer-fn'\n\nexport class PagesBundlePathNormalizer extends Normalizers {\n  constructor() {\n    super([\n      // The bundle path should have the trailing `/index` stripped from\n      // it.\n      wrapNormalizerFn(normalizePagePath),\n      // The page should prefixed with `pages/`.\n      new PrefixingNormalizer('pages'),\n    ])\n  }\n\n  public normalize(page: string): string {\n    return super.normalize(page)\n  }\n}\n\nexport class DevPagesBundlePathNormalizer extends Normalizers {\n  constructor(pagesNormalizer: Normalizer) {\n    super([\n      // This should normalize the filename to a page.\n      pagesNormalizer,\n      // Normalize the app page to a pathname.\n      new PagesBundlePathNormalizer(),\n    ])\n  }\n\n  public normalize(filename: string): string {\n    return super.normalize(filename)\n  }\n}\n"], "names": ["normalizePagePath", "Normalizers", "PrefixingNormalizer", "wrapNormalizerFn", "PagesBundlePathNormalizer", "constructor", "normalize", "page", "DevPagesBundlePathNormalizer", "pagesNormalizer", "filename"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,uDAAsD;AAExF,SAASC,WAAW,QAAQ,oBAAmB;AAC/C,SAASC,mBAAmB,QAAQ,6BAA4B;AAChE,SAASC,gBAAgB,QAAQ,2BAA0B;AAE3D,OAAO,MAAMC,kCAAkCH;IAC7CI,aAAc;QACZ,KAAK,CAAC;YACJ,kEAAkE;YAClE,MAAM;YACNF,iBAAiBH;YACjB,0CAA0C;YAC1C,IAAIE,oBAAoB;SACzB;IACH;IAEOI,UAAUC,IAAY,EAAU;QACrC,OAAO,KAAK,CAACD,UAAUC;IACzB;AACF;AAEA,OAAO,MAAMC,qCAAqCP;IAChDI,YAAYI,eAA2B,CAAE;QACvC,KAAK,CAAC;YACJ,gDAAgD;YAChDA;YACA,wCAAwC;YACxC,IAAIL;SACL;IACH;IAEOE,UAAUI,QAAgB,EAAU;QACzC,OAAO,KAAK,CAACJ,UAAUI;IACzB;AACF"}