{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/utils.ts"], "sourcesContent": ["export const ipcForbiddenHeaders = [\n  'accept-encoding',\n  'keepalive',\n  'keep-alive',\n  'content-encoding',\n  'transfer-encoding',\n  // https://github.com/nodejs/undici/issues/1470\n  'connection',\n  // marked as unsupported by undici: https://github.com/nodejs/undici/blob/c83b084879fa0bb8e0469d31ec61428ac68160d5/lib/core/request.js#L354\n  'expect',\n]\n\nexport const actionsForbiddenHeaders = [\n  ...ipcForbiddenHeaders,\n  'content-length',\n  'set-cookie',\n]\n\nexport const filterReqHeaders = (\n  headers: Record<string, undefined | string | number | string[]>,\n  forbiddenHeaders: string[]\n) => {\n  // Some browsers are not matching spec and sending Content-Length: 0. This causes issues in undici\n  // https://github.com/nodejs/undici/issues/2046\n  if (headers['content-length'] && headers['content-length'] === '0') {\n    delete headers['content-length']\n  }\n\n  for (const [key, value] of Object.entries(headers)) {\n    if (\n      forbiddenHeaders.includes(key) ||\n      !(Array.isArray(value) || typeof value === 'string')\n    ) {\n      delete headers[key]\n    }\n  }\n  return headers as Record<string, undefined | string | string[]>\n}\n\n// These are headers that are only used internally and should\n// not be honored from the external request\nconst INTERNAL_HEADERS = [\n  'x-middleware-rewrite',\n  'x-middleware-redirect',\n  'x-middleware-set-cookie',\n  'x-middleware-skip',\n  'x-middleware-override-headers',\n  'x-middleware-next',\n  'x-now-route-matches',\n  'x-matched-path',\n]\n\nexport const filterInternalHeaders = (\n  headers: Record<string, undefined | string | string[]>\n) => {\n  for (const header in headers) {\n    if (INTERNAL_HEADERS.includes(header)) {\n      delete headers[header]\n    }\n  }\n}\n"], "names": ["ipcForbiddenHeaders", "actionsForbiddenHeaders", "filterReqHeaders", "headers", "forbiddenHeaders", "key", "value", "Object", "entries", "includes", "Array", "isArray", "INTERNAL_HEADERS", "filterInternalHeaders", "header"], "mappings": "AAAA,OAAO,MAAMA,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA,+CAA+C;IAC/C;IACA,2IAA2I;IAC3I;CACD,CAAA;AAED,OAAO,MAAMC,0BAA0B;OAClCD;IACH;IACA;CACD,CAAA;AAED,OAAO,MAAME,mBAAmB,CAC9BC,SACAC;IAEA,kGAAkG;IAClG,+CAA+C;IAC/C,IAAID,OAAO,CAAC,iBAAiB,IAAIA,OAAO,CAAC,iBAAiB,KAAK,KAAK;QAClE,OAAOA,OAAO,CAAC,iBAAiB;IAClC;IAEA,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,SAAU;QAClD,IACEC,iBAAiBK,QAAQ,CAACJ,QAC1B,CAAEK,CAAAA,MAAMC,OAAO,CAACL,UAAU,OAAOA,UAAU,QAAO,GAClD;YACA,OAAOH,OAAO,CAACE,IAAI;QACrB;IACF;IACA,OAAOF;AACT,EAAC;AAED,6DAA6D;AAC7D,2CAA2C;AAC3C,MAAMS,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,OAAO,MAAMC,wBAAwB,CACnCV;IAEA,IAAK,MAAMW,UAAUX,QAAS;QAC5B,IAAIS,iBAAiBH,QAAQ,CAACK,SAAS;YACrC,OAAOX,OAAO,CAACW,OAAO;QACxB;IACF;AACF,EAAC"}