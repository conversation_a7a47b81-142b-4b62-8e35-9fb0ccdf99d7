{"version": 3, "sources": ["../../../src/server/lib/clone-response.ts"], "sourcesContent": ["/**\n * Clones a response by teeing the body so we can return two independent\n * ReadableStreams from it. This avoids the bug in the undici library around\n * response cloning.\n *\n * After cloning, the original response's body will be consumed and closed.\n *\n * @see https://github.com/vercel/next.js/pull/73274\n *\n * @param original - The original response to clone.\n * @returns A tuple containing two independent clones of the original response.\n */\nexport function cloneResponse(original: Response): [Response, Response] {\n  // If the response has no body, then we can just return the original response\n  // twice because it's immutable.\n  if (!original.body) {\n    return [original, original]\n  }\n\n  const [body1, body2] = original.body.tee()\n\n  const cloned1 = new Response(body1, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned1, 'url', {\n    value: original.url,\n  })\n\n  const cloned2 = new Response(body2, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned2, 'url', {\n    value: original.url,\n  })\n\n  return [cloned1, cloned2]\n}\n"], "names": ["cloneResponse", "original", "body", "body1", "body2", "tee", "cloned1", "Response", "status", "statusText", "headers", "Object", "defineProperty", "value", "url", "cloned2"], "mappings": "AAAA;;;;;;;;;;;CAWC,GACD,OAAO,SAASA,cAAcC,QAAkB;IAC9C,6EAA6E;IAC7E,gCAAgC;IAChC,IAAI,CAACA,SAASC,IAAI,EAAE;QAClB,OAAO;YAACD;YAAUA;SAAS;IAC7B;IAEA,MAAM,CAACE,OAAOC,MAAM,GAAGH,SAASC,IAAI,CAACG,GAAG;IAExC,MAAMC,UAAU,IAAIC,SAASJ,OAAO;QAClCK,QAAQP,SAASO,MAAM;QACvBC,YAAYR,SAASQ,UAAU;QAC/BC,SAAST,SAASS,OAAO;IAC3B;IAEAC,OAAOC,cAAc,CAACN,SAAS,OAAO;QACpCO,OAAOZ,SAASa,GAAG;IACrB;IAEA,MAAMC,UAAU,IAAIR,SAASH,OAAO;QAClCI,QAAQP,SAASO,MAAM;QACvBC,YAAYR,SAASQ,UAAU;QAC/BC,SAAST,SAASS,OAAO;IAC3B;IAEAC,OAAOC,cAAc,CAACG,SAAS,OAAO;QACpCF,OAAOZ,SAASa,GAAG;IACrB;IAEA,OAAO;QAACR;QAASS;KAAQ;AAC3B"}