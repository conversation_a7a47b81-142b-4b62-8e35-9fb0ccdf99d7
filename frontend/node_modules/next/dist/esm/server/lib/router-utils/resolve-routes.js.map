{"version": 3, "sources": ["../../../../src/server/lib/router-utils/resolve-routes.ts"], "sourcesContent": ["import type { FsOutput } from './filesystem'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextConfigComplete } from '../../config-shared'\nimport type { RenderServer, initialize } from '../router-server'\nimport type { PatchMatcher } from '../../../shared/lib/router/utils/path-match'\nimport type { Redirect } from '../../../types'\nimport type { Header, Rewrite } from '../../../lib/load-custom-routes'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport type { NextUrlWithParsedQuery } from '../../request-meta'\n\nimport url from 'url'\nimport path from 'node:path'\nimport setupDebug from 'next/dist/compiled/debug'\nimport { getCloneableBody } from '../../body-streams'\nimport { filterReqHeaders, ipcForbiddenHeaders } from '../server-ipc/utils'\nimport { stringifyQuery } from '../../server-route-utils'\nimport { formatHostname } from '../format-hostname'\nimport { toNodeOutgoingHttpHeaders } from '../../web/utils'\nimport { isAbortError } from '../../pipe-readable'\nimport { getHostname } from '../../../shared/lib/get-hostname'\nimport { getRedirectStatus } from '../../../lib/redirect-status'\nimport { normalizeRepeatedSlashes } from '../../../shared/lib/utils'\nimport { getRelativeURL } from '../../../shared/lib/router/utils/relativize-url'\nimport { addPathPrefix } from '../../../shared/lib/router/utils/add-path-prefix'\nimport { pathHasPrefix } from '../../../shared/lib/router/utils/path-has-prefix'\nimport { detectDomainLocale } from '../../../shared/lib/i18n/detect-domain-locale'\nimport { normalizeLocalePath } from '../../../shared/lib/i18n/normalize-locale-path'\nimport { removePathPrefix } from '../../../shared/lib/router/utils/remove-path-prefix'\nimport { NextDataPathnameNormalizer } from '../../normalizers/request/next-data'\nimport { BasePathPathnameNormalizer } from '../../normalizers/request/base-path'\n\nimport { addRequestMeta } from '../../request-meta'\nimport {\n  compileNonPath,\n  matchHas,\n  parseDestination,\n  prepareDestination,\n} from '../../../shared/lib/router/utils/prepare-destination'\nimport type { TLSSocket } from 'tls'\nimport {\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  RSC_HEADER,\n} from '../../../client/components/app-router-headers'\nimport { getSelectedParams } from '../../../client/components/router-reducer/compute-changed-path'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport { parseAndValidateFlightRouterState } from '../../app-render/parse-and-validate-flight-router-state'\n\nconst debug = setupDebug('next:router-server:resolve-routes')\n\nexport function getResolveRoutes(\n  fsChecker: UnwrapPromise<\n    ReturnType<typeof import('./filesystem').setupFsCheck>\n  >,\n  config: NextConfigComplete,\n  opts: Parameters<typeof initialize>[0],\n  renderServer: RenderServer,\n  renderServerOpts: Parameters<RenderServer['initialize']>[0],\n  ensureMiddleware?: (url?: string) => Promise<void>\n) {\n  type Route = {\n    /**\n     * The path matcher to check if this route applies to this request.\n     */\n    match: PatchMatcher\n    check?: boolean\n    name?: string\n  } & Partial<Header> &\n    Partial<Redirect>\n\n  const routes: Route[] = [\n    // _next/data with middleware handling\n    { match: () => ({}), name: 'middleware_next_data' },\n\n    ...(opts.minimalMode ? [] : fsChecker.headers),\n    ...(opts.minimalMode ? [] : fsChecker.redirects),\n\n    // check middleware (using matchers)\n    { match: () => ({}), name: 'middleware' },\n\n    ...(opts.minimalMode ? [] : fsChecker.rewrites.beforeFiles),\n\n    // check middleware (using matchers)\n    { match: () => ({}), name: 'before_files_end' },\n\n    // we check exact matches on fs before continuing to\n    // after files rewrites\n    { match: () => ({}), name: 'check_fs' },\n\n    ...(opts.minimalMode ? [] : fsChecker.rewrites.afterFiles),\n\n    // we always do the check: true handling before continuing to\n    // fallback rewrites\n    {\n      check: true,\n      match: () => ({}),\n      name: 'after files check: true',\n    },\n\n    ...(opts.minimalMode ? [] : fsChecker.rewrites.fallback),\n  ]\n\n  async function resolveRoutes({\n    req,\n    res,\n    isUpgradeReq,\n    invokedOutputs,\n  }: {\n    req: IncomingMessage\n    res: ServerResponse\n    isUpgradeReq: boolean\n    signal: AbortSignal\n    invokedOutputs?: Set<string>\n  }): Promise<{\n    finished: boolean\n    statusCode?: number\n    bodyStream?: ReadableStream | null\n    resHeaders: Record<string, string | string[]>\n    parsedUrl: NextUrlWithParsedQuery\n    matchedOutput?: FsOutput | null\n  }> {\n    let finished = false\n    let resHeaders: Record<string, string | string[]> = {}\n    let matchedOutput: FsOutput | null = null\n    let parsedUrl = url.parse(req.url || '', true) as NextUrlWithParsedQuery\n    let didRewrite = false\n\n    const urlParts = (req.url || '').split('?', 1)\n    const urlNoQuery = urlParts[0]\n\n    // this normalizes repeated slashes in the path e.g. hello//world ->\n    // hello/world or backslashes to forward slashes, this does not\n    // handle trailing slash as that is handled the same as a next.config.js\n    // redirect\n    if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n      parsedUrl = url.parse(normalizeRepeatedSlashes(req.url!), true)\n      return {\n        parsedUrl,\n        resHeaders,\n        finished: true,\n        statusCode: 308,\n      }\n    }\n    // TODO: inherit this from higher up\n    const protocol =\n      (req?.socket as TLSSocket)?.encrypted ||\n      req.headers['x-forwarded-proto']?.includes('https')\n        ? 'https'\n        : 'http'\n\n    // When there are hostname and port we build an absolute URL\n    const initUrl = (config.experimental as any).trustHostHeader\n      ? `https://${req.headers.host || 'localhost'}${req.url}`\n      : opts.port\n        ? `${protocol}://${formatHostname(opts.hostname || 'localhost')}:${\n            opts.port\n          }${req.url}`\n        : req.url || ''\n\n    addRequestMeta(req, 'initURL', initUrl)\n    addRequestMeta(req, 'initQuery', { ...parsedUrl.query })\n    addRequestMeta(req, 'initProtocol', protocol)\n\n    if (!isUpgradeReq) {\n      addRequestMeta(req, 'clonableBody', getCloneableBody(req))\n    }\n\n    const maybeAddTrailingSlash = (pathname: string) => {\n      if (\n        config.trailingSlash &&\n        !config.skipMiddlewareUrlNormalize &&\n        !pathname.endsWith('/')\n      ) {\n        return `${pathname}/`\n      }\n      return pathname\n    }\n\n    let domainLocale: ReturnType<typeof detectDomainLocale> | undefined\n    let defaultLocale: string | undefined\n    let initialLocaleResult:\n      | ReturnType<typeof normalizeLocalePath>\n      | undefined = undefined\n\n    if (config.i18n) {\n      const hadTrailingSlash = parsedUrl.pathname?.endsWith('/')\n      const hadBasePath = pathHasPrefix(\n        parsedUrl.pathname || '',\n        config.basePath\n      )\n      initialLocaleResult = normalizeLocalePath(\n        removePathPrefix(parsedUrl.pathname || '/', config.basePath),\n        config.i18n.locales\n      )\n\n      domainLocale = detectDomainLocale(\n        config.i18n.domains,\n        getHostname(parsedUrl, req.headers)\n      )\n      defaultLocale = domainLocale?.defaultLocale || config.i18n.defaultLocale\n\n      addRequestMeta(req, 'defaultLocale', defaultLocale)\n      addRequestMeta(\n        req,\n        'locale',\n        initialLocaleResult.detectedLocale || defaultLocale\n      )\n\n      // ensure locale is present for resolving routes\n      if (\n        !initialLocaleResult.detectedLocale &&\n        !initialLocaleResult.pathname.startsWith('/_next/')\n      ) {\n        parsedUrl.pathname = addPathPrefix(\n          initialLocaleResult.pathname === '/'\n            ? `/${defaultLocale}`\n            : addPathPrefix(\n                initialLocaleResult.pathname || '',\n                `/${defaultLocale}`\n              ),\n          hadBasePath ? config.basePath : ''\n        )\n\n        if (hadTrailingSlash) {\n          parsedUrl.pathname = maybeAddTrailingSlash(parsedUrl.pathname)\n        }\n      }\n    }\n\n    const checkLocaleApi = (pathname: string) => {\n      if (\n        config.i18n &&\n        pathname === urlNoQuery &&\n        initialLocaleResult?.detectedLocale &&\n        pathHasPrefix(initialLocaleResult.pathname, '/api')\n      ) {\n        return true\n      }\n    }\n\n    async function checkTrue() {\n      const pathname = parsedUrl.pathname || ''\n\n      if (checkLocaleApi(pathname)) {\n        return\n      }\n      if (!invokedOutputs?.has(pathname)) {\n        const output = await fsChecker.getItem(pathname)\n\n        if (output) {\n          if (\n            config.useFileSystemPublicRoutes ||\n            didRewrite ||\n            (output.type !== 'appFile' && output.type !== 'pageFile')\n          ) {\n            return output\n          }\n        }\n      }\n      const dynamicRoutes = fsChecker.getDynamicRoutes()\n      let curPathname = parsedUrl.pathname\n\n      if (config.basePath) {\n        if (!pathHasPrefix(curPathname || '', config.basePath)) {\n          return\n        }\n        curPathname = curPathname?.substring(config.basePath.length) || '/'\n      }\n      const localeResult = fsChecker.handleLocale(curPathname || '')\n\n      for (const route of dynamicRoutes) {\n        // when resolving fallback: false the\n        // render worker may return a no-fallback response\n        // which signals we need to continue resolving.\n        // TODO: optimize this to collect static paths\n        // to use at the routing layer\n        if (invokedOutputs?.has(route.page)) {\n          continue\n        }\n        const params = route.match(localeResult.pathname)\n\n        if (params) {\n          const pageOutput = await fsChecker.getItem(\n            addPathPrefix(route.page, config.basePath || '')\n          )\n\n          // i18n locales aren't matched for app dir\n          if (\n            pageOutput?.type === 'appFile' &&\n            initialLocaleResult?.detectedLocale\n          ) {\n            continue\n          }\n\n          if (pageOutput && curPathname?.startsWith('/_next/data')) {\n            addRequestMeta(req, 'isNextDataReq', true)\n          }\n\n          if (config.useFileSystemPublicRoutes || didRewrite) {\n            return pageOutput\n          }\n        }\n      }\n    }\n\n    const normalizers = {\n      basePath:\n        config.basePath && config.basePath !== '/'\n          ? new BasePathPathnameNormalizer(config.basePath)\n          : undefined,\n      data: new NextDataPathnameNormalizer(fsChecker.buildId),\n    }\n\n    async function handleRoute(\n      route: (typeof routes)[0]\n    ): Promise<UnwrapPromise<ReturnType<typeof resolveRoutes>> | void> {\n      let curPathname = parsedUrl.pathname || '/'\n\n      if (config.i18n && route.internal) {\n        const hadTrailingSlash = curPathname.endsWith('/')\n\n        if (config.basePath) {\n          curPathname = removePathPrefix(curPathname, config.basePath)\n        }\n        const hadBasePath = curPathname !== parsedUrl.pathname\n\n        const localeResult = normalizeLocalePath(\n          curPathname,\n          config.i18n.locales\n        )\n        const isDefaultLocale = localeResult.detectedLocale === defaultLocale\n\n        if (isDefaultLocale) {\n          curPathname =\n            localeResult.pathname === '/' && hadBasePath\n              ? config.basePath\n              : addPathPrefix(\n                  localeResult.pathname,\n                  hadBasePath ? config.basePath : ''\n                )\n        } else if (hadBasePath) {\n          curPathname =\n            curPathname === '/'\n              ? config.basePath\n              : addPathPrefix(curPathname, config.basePath)\n        }\n\n        if ((isDefaultLocale || hadBasePath) && hadTrailingSlash) {\n          curPathname = maybeAddTrailingSlash(curPathname)\n        }\n      }\n      let params = route.match(curPathname)\n\n      if ((route.has || route.missing) && params) {\n        const hasParams = matchHas(\n          req,\n          parsedUrl.query,\n          route.has,\n          route.missing\n        )\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        if (\n          fsChecker.exportPathMapRoutes &&\n          route.name === 'before_files_end'\n        ) {\n          for (const exportPathMapRoute of fsChecker.exportPathMapRoutes) {\n            const result = await handleRoute(exportPathMapRoute)\n\n            if (result) {\n              return result\n            }\n          }\n        }\n\n        if (route.name === 'middleware_next_data' && parsedUrl.pathname) {\n          if (fsChecker.getMiddlewareMatchers()?.length) {\n            let normalized = parsedUrl.pathname\n\n            // Remove the base path if it exists.\n            const hadBasePath = normalizers.basePath?.match(parsedUrl.pathname)\n            if (hadBasePath && normalizers.basePath) {\n              normalized = normalizers.basePath.normalize(normalized, true)\n            }\n\n            let updated = false\n            if (normalizers.data.match(normalized)) {\n              updated = true\n              addRequestMeta(req, 'isNextDataReq', true)\n              normalized = normalizers.data.normalize(normalized, true)\n            }\n\n            if (config.i18n) {\n              const curLocaleResult = normalizeLocalePath(\n                normalized,\n                config.i18n.locales\n              )\n\n              if (curLocaleResult.detectedLocale) {\n                addRequestMeta(req, 'locale', curLocaleResult.detectedLocale)\n              }\n            }\n\n            // If we updated the pathname, and it had a base path, re-add the\n            // base path.\n            if (updated) {\n              if (hadBasePath) {\n                normalized = path.posix.join(config.basePath, normalized)\n              }\n\n              // Re-add the trailing slash (if required).\n              normalized = maybeAddTrailingSlash(normalized)\n\n              parsedUrl.pathname = normalized\n            }\n          }\n        }\n\n        if (route.name === 'check_fs') {\n          const pathname = parsedUrl.pathname || ''\n\n          if (invokedOutputs?.has(pathname) || checkLocaleApi(pathname)) {\n            return\n          }\n          const output = await fsChecker.getItem(pathname)\n\n          if (\n            output &&\n            !(\n              config.i18n &&\n              initialLocaleResult?.detectedLocale &&\n              pathHasPrefix(pathname, '/api')\n            )\n          ) {\n            if (\n              config.useFileSystemPublicRoutes ||\n              didRewrite ||\n              (output.type !== 'appFile' && output.type !== 'pageFile')\n            ) {\n              matchedOutput = output\n\n              if (output.locale) {\n                addRequestMeta(req, 'locale', output.locale)\n              }\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n                matchedOutput,\n              }\n            }\n          }\n        }\n\n        if (!opts.minimalMode && route.name === 'middleware') {\n          const match = fsChecker.getMiddlewareMatchers()\n          if (\n            // @ts-expect-error BaseNextRequest stuff\n            match?.(parsedUrl.pathname, req, parsedUrl.query)\n          ) {\n            if (ensureMiddleware) {\n              await ensureMiddleware(req.url)\n            }\n\n            const serverResult =\n              await renderServer?.initialize(renderServerOpts)\n\n            if (!serverResult) {\n              throw new Error(`Failed to initialize render server \"middleware\"`)\n            }\n\n            addRequestMeta(req, 'invokePath', '')\n            addRequestMeta(req, 'invokeOutput', '')\n            addRequestMeta(req, 'invokeQuery', {})\n            addRequestMeta(req, 'middlewareInvoke', true)\n            debug('invoking middleware', req.url, req.headers)\n\n            let middlewareRes: Response | undefined = undefined\n            let bodyStream: ReadableStream | undefined = undefined\n            try {\n              try {\n                await serverResult.requestHandler(req, res, parsedUrl)\n              } catch (err: any) {\n                if (!('result' in err) || !('response' in err.result)) {\n                  throw err\n                }\n                middlewareRes = err.result.response as Response\n                res.statusCode = middlewareRes.status\n\n                if (middlewareRes.body) {\n                  bodyStream = middlewareRes.body\n                } else if (middlewareRes.status) {\n                  bodyStream = new ReadableStream({\n                    start(controller) {\n                      controller.enqueue('')\n                      controller.close()\n                    },\n                  })\n                }\n              }\n            } catch (e) {\n              // If the client aborts before we can receive a response object\n              // (when the headers are flushed), then we can early exit without\n              // further processing.\n              if (isAbortError(e)) {\n                return {\n                  parsedUrl,\n                  resHeaders,\n                  finished: true,\n                }\n              }\n              throw e\n            }\n\n            if (res.closed || res.finished || !middlewareRes) {\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n              }\n            }\n\n            const middlewareHeaders = toNodeOutgoingHttpHeaders(\n              middlewareRes.headers\n            ) as Record<string, string | string[] | undefined>\n\n            debug('middleware res', middlewareRes.status, middlewareHeaders)\n\n            if (middlewareHeaders['x-middleware-override-headers']) {\n              const overriddenHeaders: Set<string> = new Set()\n              let overrideHeaders: string | string[] =\n                middlewareHeaders['x-middleware-override-headers']\n\n              if (typeof overrideHeaders === 'string') {\n                overrideHeaders = overrideHeaders.split(',')\n              }\n\n              for (const key of overrideHeaders) {\n                overriddenHeaders.add(key.trim())\n              }\n              delete middlewareHeaders['x-middleware-override-headers']\n\n              // Delete headers.\n              for (const key of Object.keys(req.headers)) {\n                if (!overriddenHeaders.has(key)) {\n                  delete req.headers[key]\n                }\n              }\n\n              // Update or add headers.\n              for (const key of overriddenHeaders.keys()) {\n                const valueKey = 'x-middleware-request-' + key\n                const newValue = middlewareHeaders[valueKey]\n                const oldValue = req.headers[key]\n\n                if (oldValue !== newValue) {\n                  req.headers[key] = newValue === null ? undefined : newValue\n                }\n                delete middlewareHeaders[valueKey]\n              }\n            }\n\n            if (\n              !middlewareHeaders['x-middleware-rewrite'] &&\n              !middlewareHeaders['x-middleware-next'] &&\n              !middlewareHeaders['location']\n            ) {\n              middlewareHeaders['x-middleware-refresh'] = '1'\n            }\n            delete middlewareHeaders['x-middleware-next']\n\n            for (const [key, value] of Object.entries({\n              ...filterReqHeaders(middlewareHeaders, ipcForbiddenHeaders),\n            })) {\n              if (\n                [\n                  'content-length',\n                  'x-middleware-rewrite',\n                  'x-middleware-redirect',\n                  'x-middleware-refresh',\n                ].includes(key)\n              ) {\n                continue\n              }\n\n              // for set-cookie, the header shouldn't be added to the response\n              // as it's only needed for the request to the middleware function.\n              if (key === 'x-middleware-set-cookie') {\n                req.headers[key] = value\n                continue\n              }\n\n              if (value) {\n                resHeaders[key] = value\n                req.headers[key] = value\n              }\n            }\n\n            if (middlewareHeaders['x-middleware-rewrite']) {\n              const value = middlewareHeaders['x-middleware-rewrite'] as string\n              const destination = getRelativeURL(value, initUrl)\n              resHeaders['x-middleware-rewrite'] = destination\n\n              parsedUrl = url.parse(destination, true)\n\n              if (parsedUrl.protocol) {\n                return {\n                  parsedUrl,\n                  resHeaders,\n                  finished: true,\n                }\n              }\n\n              if (config.i18n) {\n                const curLocaleResult = normalizeLocalePath(\n                  parsedUrl.pathname || '',\n                  config.i18n.locales\n                )\n\n                if (curLocaleResult.detectedLocale) {\n                  addRequestMeta(req, 'locale', curLocaleResult.detectedLocale)\n                }\n              }\n            }\n\n            if (middlewareHeaders['location']) {\n              const value = middlewareHeaders['location'] as string\n              const rel = getRelativeURL(value, initUrl)\n              resHeaders['location'] = rel\n              parsedUrl = url.parse(rel, true)\n\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n                statusCode: middlewareRes.status,\n              }\n            }\n\n            if (middlewareHeaders['x-middleware-refresh']) {\n              return {\n                parsedUrl,\n                resHeaders,\n                finished: true,\n                bodyStream,\n                statusCode: middlewareRes.status,\n              }\n            }\n          }\n        }\n\n        // handle redirect\n        if (\n          ('statusCode' in route || 'permanent' in route) &&\n          route.destination\n        ) {\n          const { parsedDestination } = prepareDestination({\n            appendParamsToQuery: false,\n            destination: route.destination,\n            params: params,\n            query: parsedUrl.query,\n          })\n\n          const { query } = parsedDestination\n          delete (parsedDestination as any).query\n\n          parsedDestination.search = stringifyQuery(req as any, query)\n\n          parsedDestination.pathname = normalizeRepeatedSlashes(\n            parsedDestination.pathname\n          )\n\n          return {\n            finished: true,\n            // @ts-expect-error custom ParsedUrl\n            parsedUrl: parsedDestination,\n            statusCode: getRedirectStatus(route),\n          }\n        }\n\n        // handle headers\n        if (route.headers) {\n          const hasParams = Object.keys(params).length > 0\n          for (const header of route.headers) {\n            let { key, value } = header\n            if (hasParams) {\n              key = compileNonPath(key, params)\n              value = compileNonPath(value, params)\n            }\n\n            if (key.toLowerCase() === 'set-cookie') {\n              if (!Array.isArray(resHeaders[key])) {\n                const val = resHeaders[key]\n                resHeaders[key] = typeof val === 'string' ? [val] : []\n              }\n              ;(resHeaders[key] as string[]).push(value)\n            } else {\n              resHeaders[key] = value\n            }\n          }\n        }\n\n        // handle rewrite\n        if (route.destination) {\n          let rewriteParams = params\n\n          try {\n            // An interception rewrite might reference a dynamic param for a route the user\n            // is currently on, which wouldn't be extractable from the matched route params.\n            // This attempts to extract the dynamic params from the provided router state.\n            if (isInterceptionRouteRewrite(route as Rewrite)) {\n              const stateHeader =\n                req.headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n\n              if (stateHeader) {\n                rewriteParams = {\n                  ...getSelectedParams(\n                    parseAndValidateFlightRouterState(stateHeader)\n                  ),\n                  ...params,\n                }\n              }\n            }\n          } catch (err) {\n            // this is a no-op -- we couldn't extract dynamic params from the provided router state,\n            // so we'll just use the params from the route matcher\n          }\n\n          // We extract the search params of the destination so we can set it on\n          // the response headers. We don't want to use the following\n          // `parsedDestination` as the query object is mutated.\n          const { search: destinationSearch, pathname: destinationPathname } =\n            parseDestination({\n              destination: route.destination,\n              params: rewriteParams,\n              query: parsedUrl.query,\n            })\n\n          const { parsedDestination } = prepareDestination({\n            appendParamsToQuery: true,\n            destination: route.destination,\n            params: rewriteParams,\n            query: parsedUrl.query,\n          })\n\n          if (parsedDestination.protocol) {\n            return {\n              // @ts-expect-error custom ParsedUrl\n              parsedUrl: parsedDestination,\n              finished: true,\n            }\n          }\n\n          // Set the rewrite headers only if this is a RSC request.\n          if (req.headers[RSC_HEADER.toLowerCase()] === '1') {\n            // We set the rewritten path and query headers on the response now\n            // that we know that the it's not an external rewrite.\n            if (parsedUrl.pathname !== destinationPathname) {\n              res.setHeader(NEXT_REWRITTEN_PATH_HEADER, destinationPathname)\n            }\n            if (destinationSearch) {\n              res.setHeader(\n                NEXT_REWRITTEN_QUERY_HEADER,\n                // remove the leading ? from the search\n                destinationSearch.slice(1)\n              )\n            }\n          }\n\n          if (config.i18n) {\n            const curLocaleResult = normalizeLocalePath(\n              removePathPrefix(parsedDestination.pathname, config.basePath),\n              config.i18n.locales\n            )\n\n            if (curLocaleResult.detectedLocale) {\n              addRequestMeta(req, 'locale', curLocaleResult.detectedLocale)\n            }\n          }\n          didRewrite = true\n          parsedUrl.pathname = parsedDestination.pathname\n          Object.assign(parsedUrl.query, parsedDestination.query)\n        }\n\n        // handle check: true\n        if (route.check) {\n          const output = await checkTrue()\n\n          if (output) {\n            return {\n              parsedUrl,\n              resHeaders,\n              finished: true,\n              matchedOutput: output,\n            }\n          }\n        }\n      }\n    }\n\n    for (const route of routes) {\n      const result = await handleRoute(route)\n      if (result) {\n        return result\n      }\n    }\n\n    return {\n      finished,\n      parsedUrl,\n      resHeaders,\n      matchedOutput,\n    }\n  }\n\n  return resolveRoutes\n}\n"], "names": ["url", "path", "setupDebug", "getCloneableBody", "filterReqHeaders", "ipcForbiddenHeaders", "stringifyQuery", "formatHostname", "toNodeOutgoingHttpHeaders", "isAbortError", "getHostname", "getRedirectStatus", "normalizeRepeatedSlashes", "getRelativeURL", "addPathPrefix", "pathHasPrefix", "detectDomainLocale", "normalizeLocalePath", "removePathPrefix", "NextDataPathnameNormalizer", "BasePathPathnameNormalizer", "addRequestMeta", "compileNonPath", "matchHas", "parseDestination", "prepareDestination", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "RSC_HEADER", "getSelectedParams", "isInterceptionRouteRewrite", "parseAndValidateFlightRouterState", "debug", "getResolveRoutes", "fs<PERSON><PERSON><PERSON>", "config", "opts", "renderServer", "renderServerOpts", "ensureMiddleware", "routes", "match", "name", "minimalMode", "headers", "redirects", "rewrites", "beforeFiles", "afterFiles", "check", "fallback", "resolveRoutes", "req", "res", "isUpgradeReq", "invokedOutputs", "finished", "resHeaders", "matchedOutput", "parsedUrl", "parse", "didRewrite", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statusCode", "protocol", "socket", "encrypted", "includes", "initUrl", "experimental", "trustHostHeader", "host", "port", "hostname", "query", "maybeAddTrailingSlash", "pathname", "trailingSlash", "skipMiddlewareUrlNormalize", "endsWith", "domainLocale", "defaultLocale", "initialLocaleResult", "undefined", "i18n", "hadTrailingSlash", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "basePath", "locales", "domains", "detectedLocale", "startsWith", "checkLocaleApi", "checkTrue", "has", "output", "getItem", "useFileSystemPublicRoutes", "type", "dynamicRoutes", "getDynamicRoutes", "curPathname", "substring", "length", "localeResult", "handleLocale", "route", "page", "params", "pageOutput", "normalizers", "data", "buildId", "handleRoute", "internal", "isDefaultLocale", "missing", "hasParams", "Object", "assign", "exportPathMapRoutes", "exportPathMapRoute", "result", "getMiddlewareMatchers", "normalized", "normalize", "updated", "curLocaleResult", "posix", "join", "locale", "serverResult", "initialize", "Error", "middlewareRes", "bodyStream", "requestHandler", "err", "response", "status", "body", "ReadableStream", "start", "controller", "enqueue", "close", "e", "closed", "middlewareHeaders", "overriddenHeaders", "Set", "overrideHeaders", "key", "add", "trim", "keys", "valueKey", "newValue", "oldValue", "value", "entries", "destination", "rel", "parsedDestination", "appendParamsToQuery", "search", "header", "toLowerCase", "Array", "isArray", "val", "push", "rewriteParams", "<PERSON><PERSON><PERSON><PERSON>", "destinationSearch", "destinationPathname", "<PERSON><PERSON><PERSON><PERSON>", "slice"], "mappings": "AAUA,OAAOA,SAAS,MAAK;AACrB,OAAOC,UAAU,YAAW;AAC5B,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,gBAAgB,QAAQ,qBAAoB;AACrD,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,sBAAqB;AAC3E,SAASC,cAAc,QAAQ,2BAA0B;AACzD,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kBAAiB;AAC3D,SAASC,YAAY,QAAQ,sBAAqB;AAClD,SAASC,WAAW,QAAQ,mCAAkC;AAC9D,SAASC,iBAAiB,QAAQ,+BAA8B;AAChE,SAASC,wBAAwB,QAAQ,4BAA2B;AACpE,SAASC,cAAc,QAAQ,kDAAiD;AAChF,SAASC,aAAa,QAAQ,mDAAkD;AAChF,SAASC,aAAa,QAAQ,mDAAkD;AAChF,SAASC,kBAAkB,QAAQ,gDAA+C;AAClF,SAASC,mBAAmB,QAAQ,iDAAgD;AACpF,SAASC,gBAAgB,QAAQ,sDAAqD;AACtF,SAASC,0BAA0B,QAAQ,sCAAqC;AAChF,SAASC,0BAA0B,QAAQ,sCAAqC;AAEhF,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SACEC,cAAc,EACdC,QAAQ,EACRC,gBAAgB,EAChBC,kBAAkB,QACb,uDAAsD;AAE7D,SACEC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,UAAU,QACL,gDAA+C;AACtD,SAASC,iBAAiB,QAAQ,iEAAgE;AAClG,SAASC,0BAA0B,QAAQ,qDAAoD;AAC/F,SAASC,iCAAiC,QAAQ,0DAAyD;AAE3G,MAAMC,QAAQ/B,WAAW;AAEzB,OAAO,SAASgC,iBACdC,SAEC,EACDC,MAA0B,EAC1BC,IAAsC,EACtCC,YAA0B,EAC1BC,gBAA2D,EAC3DC,gBAAkD;IAYlD,MAAMC,SAAkB;QACtB,sCAAsC;QACtC;YAAEC,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAuB;WAE9CN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUU,OAAO;WACzCR,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUW,SAAS;QAE/C,oCAAoC;QACpC;YAAEJ,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAa;WAEpCN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUY,QAAQ,CAACC,WAAW;QAE1D,oCAAoC;QACpC;YAAEN,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAmB;QAE9C,oDAAoD;QACpD,uBAAuB;QACvB;YAAED,OAAO,IAAO,CAAA,CAAC,CAAA;YAAIC,MAAM;QAAW;WAElCN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUY,QAAQ,CAACE,UAAU;QAEzD,6DAA6D;QAC7D,oBAAoB;QACpB;YACEC,OAAO;YACPR,OAAO,IAAO,CAAA,CAAC,CAAA;YACfC,MAAM;QACR;WAEIN,KAAKO,WAAW,GAAG,EAAE,GAAGT,UAAUY,QAAQ,CAACI,QAAQ;KACxD;IAED,eAAeC,cAAc,EAC3BC,GAAG,EACHC,GAAG,EACHC,YAAY,EACZC,cAAc,EAOf;YAgCIH,aACDA;QAzBF,IAAII,WAAW;QACf,IAAIC,aAAgD,CAAC;QACrD,IAAIC,gBAAiC;QACrC,IAAIC,YAAY5D,IAAI6D,KAAK,CAACR,IAAIrD,GAAG,IAAI,IAAI;QACzC,IAAI8D,aAAa;QAEjB,MAAMC,WAAW,AAACV,CAAAA,IAAIrD,GAAG,IAAI,EAAC,EAAGgE,KAAK,CAAC,KAAK;QAC5C,MAAMC,aAAaF,QAAQ,CAAC,EAAE;QAE9B,oEAAoE;QACpE,+DAA+D;QAC/D,wEAAwE;QACxE,WAAW;QACX,IAAIE,8BAAAA,WAAYvB,KAAK,CAAC,cAAc;YAClCkB,YAAY5D,IAAI6D,KAAK,CAACjD,yBAAyByC,IAAIrD,GAAG,GAAI;YAC1D,OAAO;gBACL4D;gBACAF;gBACAD,UAAU;gBACVS,YAAY;YACd;QACF;QACA,oCAAoC;QACpC,MAAMC,WACJ,CAACd,wBAAAA,cAAAA,IAAKe,MAAM,qBAAZ,AAACf,YAA2BgB,SAAS,OACrChB,+BAAAA,IAAIR,OAAO,CAAC,oBAAoB,qBAAhCQ,6BAAkCiB,QAAQ,CAAC,YACvC,UACA;QAEN,4DAA4D;QAC5D,MAAMC,UAAU,AAACnC,OAAOoC,YAAY,CAASC,eAAe,GACxD,CAAC,QAAQ,EAAEpB,IAAIR,OAAO,CAAC6B,IAAI,IAAI,cAAcrB,IAAIrD,GAAG,EAAE,GACtDqC,KAAKsC,IAAI,GACP,GAAGR,SAAS,GAAG,EAAE5D,eAAe8B,KAAKuC,QAAQ,IAAI,aAAa,CAAC,EAC7DvC,KAAKsC,IAAI,GACRtB,IAAIrD,GAAG,EAAE,GACZqD,IAAIrD,GAAG,IAAI;QAEjBqB,eAAegC,KAAK,WAAWkB;QAC/BlD,eAAegC,KAAK,aAAa;YAAE,GAAGO,UAAUiB,KAAK;QAAC;QACtDxD,eAAegC,KAAK,gBAAgBc;QAEpC,IAAI,CAACZ,cAAc;YACjBlC,eAAegC,KAAK,gBAAgBlD,iBAAiBkD;QACvD;QAEA,MAAMyB,wBAAwB,CAACC;YAC7B,IACE3C,OAAO4C,aAAa,IACpB,CAAC5C,OAAO6C,0BAA0B,IAClC,CAACF,SAASG,QAAQ,CAAC,MACnB;gBACA,OAAO,GAAGH,SAAS,CAAC,CAAC;YACvB;YACA,OAAOA;QACT;QAEA,IAAII;QACJ,IAAIC;QACJ,IAAIC,sBAEYC;QAEhB,IAAIlD,OAAOmD,IAAI,EAAE;gBACU3B;YAAzB,MAAM4B,oBAAmB5B,sBAAAA,UAAUmB,QAAQ,qBAAlBnB,oBAAoBsB,QAAQ,CAAC;YACtD,MAAMO,cAAc1E,cAClB6C,UAAUmB,QAAQ,IAAI,IACtB3C,OAAOsD,QAAQ;YAEjBL,sBAAsBpE,oBACpBC,iBAAiB0C,UAAUmB,QAAQ,IAAI,KAAK3C,OAAOsD,QAAQ,GAC3DtD,OAAOmD,IAAI,CAACI,OAAO;YAGrBR,eAAenE,mBACboB,OAAOmD,IAAI,CAACK,OAAO,EACnBlF,YAAYkD,WAAWP,IAAIR,OAAO;YAEpCuC,gBAAgBD,CAAAA,gCAAAA,aAAcC,aAAa,KAAIhD,OAAOmD,IAAI,CAACH,aAAa;YAExE/D,eAAegC,KAAK,iBAAiB+B;YACrC/D,eACEgC,KACA,UACAgC,oBAAoBQ,cAAc,IAAIT;YAGxC,gDAAgD;YAChD,IACE,CAACC,oBAAoBQ,cAAc,IACnC,CAACR,oBAAoBN,QAAQ,CAACe,UAAU,CAAC,YACzC;gBACAlC,UAAUmB,QAAQ,GAAGjE,cACnBuE,oBAAoBN,QAAQ,KAAK,MAC7B,CAAC,CAAC,EAAEK,eAAe,GACnBtE,cACEuE,oBAAoBN,QAAQ,IAAI,IAChC,CAAC,CAAC,EAAEK,eAAe,GAEzBK,cAAcrD,OAAOsD,QAAQ,GAAG;gBAGlC,IAAIF,kBAAkB;oBACpB5B,UAAUmB,QAAQ,GAAGD,sBAAsBlB,UAAUmB,QAAQ;gBAC/D;YACF;QACF;QAEA,MAAMgB,iBAAiB,CAAChB;YACtB,IACE3C,OAAOmD,IAAI,IACXR,aAAad,eACboB,uCAAAA,oBAAqBQ,cAAc,KACnC9E,cAAcsE,oBAAoBN,QAAQ,EAAE,SAC5C;gBACA,OAAO;YACT;QACF;QAEA,eAAeiB;YACb,MAAMjB,WAAWnB,UAAUmB,QAAQ,IAAI;YAEvC,IAAIgB,eAAehB,WAAW;gBAC5B;YACF;YACA,IAAI,EAACvB,kCAAAA,eAAgByC,GAAG,CAAClB,YAAW;gBAClC,MAAMmB,SAAS,MAAM/D,UAAUgE,OAAO,CAACpB;gBAEvC,IAAImB,QAAQ;oBACV,IACE9D,OAAOgE,yBAAyB,IAChCtC,cACCoC,OAAOG,IAAI,KAAK,aAAaH,OAAOG,IAAI,KAAK,YAC9C;wBACA,OAAOH;oBACT;gBACF;YACF;YACA,MAAMI,gBAAgBnE,UAAUoE,gBAAgB;YAChD,IAAIC,cAAc5C,UAAUmB,QAAQ;YAEpC,IAAI3C,OAAOsD,QAAQ,EAAE;gBACnB,IAAI,CAAC3E,cAAcyF,eAAe,IAAIpE,OAAOsD,QAAQ,GAAG;oBACtD;gBACF;gBACAc,cAAcA,CAAAA,+BAAAA,YAAaC,SAAS,CAACrE,OAAOsD,QAAQ,CAACgB,MAAM,MAAK;YAClE;YACA,MAAMC,eAAexE,UAAUyE,YAAY,CAACJ,eAAe;YAE3D,KAAK,MAAMK,SAASP,cAAe;gBACjC,qCAAqC;gBACrC,kDAAkD;gBAClD,+CAA+C;gBAC/C,8CAA8C;gBAC9C,8BAA8B;gBAC9B,IAAI9C,kCAAAA,eAAgByC,GAAG,CAACY,MAAMC,IAAI,GAAG;oBACnC;gBACF;gBACA,MAAMC,SAASF,MAAMnE,KAAK,CAACiE,aAAa5B,QAAQ;gBAEhD,IAAIgC,QAAQ;oBACV,MAAMC,aAAa,MAAM7E,UAAUgE,OAAO,CACxCrF,cAAc+F,MAAMC,IAAI,EAAE1E,OAAOsD,QAAQ,IAAI;oBAG/C,0CAA0C;oBAC1C,IACEsB,CAAAA,8BAAAA,WAAYX,IAAI,MAAK,cACrBhB,uCAAAA,oBAAqBQ,cAAc,GACnC;wBACA;oBACF;oBAEA,IAAImB,eAAcR,+BAAAA,YAAaV,UAAU,CAAC,iBAAgB;wBACxDzE,eAAegC,KAAK,iBAAiB;oBACvC;oBAEA,IAAIjB,OAAOgE,yBAAyB,IAAItC,YAAY;wBAClD,OAAOkD;oBACT;gBACF;YACF;QACF;QAEA,MAAMC,cAAc;YAClBvB,UACEtD,OAAOsD,QAAQ,IAAItD,OAAOsD,QAAQ,KAAK,MACnC,IAAItE,2BAA2BgB,OAAOsD,QAAQ,IAC9CJ;YACN4B,MAAM,IAAI/F,2BAA2BgB,UAAUgF,OAAO;QACxD;QAEA,eAAeC,YACbP,KAAyB;YAEzB,IAAIL,cAAc5C,UAAUmB,QAAQ,IAAI;YAExC,IAAI3C,OAAOmD,IAAI,IAAIsB,MAAMQ,QAAQ,EAAE;gBACjC,MAAM7B,mBAAmBgB,YAAYtB,QAAQ,CAAC;gBAE9C,IAAI9C,OAAOsD,QAAQ,EAAE;oBACnBc,cAActF,iBAAiBsF,aAAapE,OAAOsD,QAAQ;gBAC7D;gBACA,MAAMD,cAAce,gBAAgB5C,UAAUmB,QAAQ;gBAEtD,MAAM4B,eAAe1F,oBACnBuF,aACApE,OAAOmD,IAAI,CAACI,OAAO;gBAErB,MAAM2B,kBAAkBX,aAAad,cAAc,KAAKT;gBAExD,IAAIkC,iBAAiB;oBACnBd,cACEG,aAAa5B,QAAQ,KAAK,OAAOU,cAC7BrD,OAAOsD,QAAQ,GACf5E,cACE6F,aAAa5B,QAAQ,EACrBU,cAAcrD,OAAOsD,QAAQ,GAAG;gBAE1C,OAAO,IAAID,aAAa;oBACtBe,cACEA,gBAAgB,MACZpE,OAAOsD,QAAQ,GACf5E,cAAc0F,aAAapE,OAAOsD,QAAQ;gBAClD;gBAEA,IAAI,AAAC4B,CAAAA,mBAAmB7B,WAAU,KAAMD,kBAAkB;oBACxDgB,cAAc1B,sBAAsB0B;gBACtC;YACF;YACA,IAAIO,SAASF,MAAMnE,KAAK,CAAC8D;YAEzB,IAAI,AAACK,CAAAA,MAAMZ,GAAG,IAAIY,MAAMU,OAAO,AAAD,KAAMR,QAAQ;gBAC1C,MAAMS,YAAYjG,SAChB8B,KACAO,UAAUiB,KAAK,EACfgC,MAAMZ,GAAG,EACTY,MAAMU,OAAO;gBAEf,IAAIC,WAAW;oBACbC,OAAOC,MAAM,CAACX,QAAQS;gBACxB,OAAO;oBACLT,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,IACE5E,UAAUwF,mBAAmB,IAC7Bd,MAAMlE,IAAI,KAAK,oBACf;oBACA,KAAK,MAAMiF,sBAAsBzF,UAAUwF,mBAAmB,CAAE;wBAC9D,MAAME,SAAS,MAAMT,YAAYQ;wBAEjC,IAAIC,QAAQ;4BACV,OAAOA;wBACT;oBACF;gBACF;gBAEA,IAAIhB,MAAMlE,IAAI,KAAK,0BAA0BiB,UAAUmB,QAAQ,EAAE;wBAC3D5C;oBAAJ,KAAIA,mCAAAA,UAAU2F,qBAAqB,uBAA/B3F,iCAAmCuE,MAAM,EAAE;4BAIzBO;wBAHpB,IAAIc,aAAanE,UAAUmB,QAAQ;wBAEnC,qCAAqC;wBACrC,MAAMU,eAAcwB,wBAAAA,YAAYvB,QAAQ,qBAApBuB,sBAAsBvE,KAAK,CAACkB,UAAUmB,QAAQ;wBAClE,IAAIU,eAAewB,YAAYvB,QAAQ,EAAE;4BACvCqC,aAAad,YAAYvB,QAAQ,CAACsC,SAAS,CAACD,YAAY;wBAC1D;wBAEA,IAAIE,UAAU;wBACd,IAAIhB,YAAYC,IAAI,CAACxE,KAAK,CAACqF,aAAa;4BACtCE,UAAU;4BACV5G,eAAegC,KAAK,iBAAiB;4BACrC0E,aAAad,YAAYC,IAAI,CAACc,SAAS,CAACD,YAAY;wBACtD;wBAEA,IAAI3F,OAAOmD,IAAI,EAAE;4BACf,MAAM2C,kBAAkBjH,oBACtB8G,YACA3F,OAAOmD,IAAI,CAACI,OAAO;4BAGrB,IAAIuC,gBAAgBrC,cAAc,EAAE;gCAClCxE,eAAegC,KAAK,UAAU6E,gBAAgBrC,cAAc;4BAC9D;wBACF;wBAEA,iEAAiE;wBACjE,aAAa;wBACb,IAAIoC,SAAS;4BACX,IAAIxC,aAAa;gCACfsC,aAAa9H,KAAKkI,KAAK,CAACC,IAAI,CAAChG,OAAOsD,QAAQ,EAAEqC;4BAChD;4BAEA,2CAA2C;4BAC3CA,aAAajD,sBAAsBiD;4BAEnCnE,UAAUmB,QAAQ,GAAGgD;wBACvB;oBACF;gBACF;gBAEA,IAAIlB,MAAMlE,IAAI,KAAK,YAAY;oBAC7B,MAAMoC,WAAWnB,UAAUmB,QAAQ,IAAI;oBAEvC,IAAIvB,CAAAA,kCAAAA,eAAgByC,GAAG,CAAClB,cAAagB,eAAehB,WAAW;wBAC7D;oBACF;oBACA,MAAMmB,SAAS,MAAM/D,UAAUgE,OAAO,CAACpB;oBAEvC,IACEmB,UACA,CACE9D,CAAAA,OAAOmD,IAAI,KACXF,uCAAAA,oBAAqBQ,cAAc,KACnC9E,cAAcgE,UAAU,OAAM,GAEhC;wBACA,IACE3C,OAAOgE,yBAAyB,IAChCtC,cACCoC,OAAOG,IAAI,KAAK,aAAaH,OAAOG,IAAI,KAAK,YAC9C;4BACA1C,gBAAgBuC;4BAEhB,IAAIA,OAAOmC,MAAM,EAAE;gCACjBhH,eAAegC,KAAK,UAAU6C,OAAOmC,MAAM;4BAC7C;4BACA,OAAO;gCACLzE;gCACAF;gCACAD,UAAU;gCACVE;4BACF;wBACF;oBACF;gBACF;gBAEA,IAAI,CAACtB,KAAKO,WAAW,IAAIiE,MAAMlE,IAAI,KAAK,cAAc;oBACpD,MAAMD,QAAQP,UAAU2F,qBAAqB;oBAC7C,IACE,yCAAyC;oBACzCpF,yBAAAA,MAAQkB,UAAUmB,QAAQ,EAAE1B,KAAKO,UAAUiB,KAAK,GAChD;wBACA,IAAIrC,kBAAkB;4BACpB,MAAMA,iBAAiBa,IAAIrD,GAAG;wBAChC;wBAEA,MAAMsI,eACJ,OAAMhG,gCAAAA,aAAciG,UAAU,CAAChG;wBAEjC,IAAI,CAAC+F,cAAc;4BACjB,MAAM,qBAA4D,CAA5D,IAAIE,MAAM,CAAC,+CAA+C,CAAC,GAA3D,qBAAA;uCAAA;4CAAA;8CAAA;4BAA2D;wBACnE;wBAEAnH,eAAegC,KAAK,cAAc;wBAClChC,eAAegC,KAAK,gBAAgB;wBACpChC,eAAegC,KAAK,eAAe,CAAC;wBACpChC,eAAegC,KAAK,oBAAoB;wBACxCpB,MAAM,uBAAuBoB,IAAIrD,GAAG,EAAEqD,IAAIR,OAAO;wBAEjD,IAAI4F,gBAAsCnD;wBAC1C,IAAIoD,aAAyCpD;wBAC7C,IAAI;4BACF,IAAI;gCACF,MAAMgD,aAAaK,cAAc,CAACtF,KAAKC,KAAKM;4BAC9C,EAAE,OAAOgF,KAAU;gCACjB,IAAI,CAAE,CAAA,YAAYA,GAAE,KAAM,CAAE,CAAA,cAAcA,IAAIf,MAAM,AAAD,GAAI;oCACrD,MAAMe;gCACR;gCACAH,gBAAgBG,IAAIf,MAAM,CAACgB,QAAQ;gCACnCvF,IAAIY,UAAU,GAAGuE,cAAcK,MAAM;gCAErC,IAAIL,cAAcM,IAAI,EAAE;oCACtBL,aAAaD,cAAcM,IAAI;gCACjC,OAAO,IAAIN,cAAcK,MAAM,EAAE;oCAC/BJ,aAAa,IAAIM,eAAe;wCAC9BC,OAAMC,UAAU;4CACdA,WAAWC,OAAO,CAAC;4CACnBD,WAAWE,KAAK;wCAClB;oCACF;gCACF;4BACF;wBACF,EAAE,OAAOC,GAAG;4BACV,+DAA+D;4BAC/D,iEAAiE;4BACjE,sBAAsB;4BACtB,IAAI5I,aAAa4I,IAAI;gCACnB,OAAO;oCACLzF;oCACAF;oCACAD,UAAU;gCACZ;4BACF;4BACA,MAAM4F;wBACR;wBAEA,IAAI/F,IAAIgG,MAAM,IAAIhG,IAAIG,QAAQ,IAAI,CAACgF,eAAe;4BAChD,OAAO;gCACL7E;gCACAF;gCACAD,UAAU;4BACZ;wBACF;wBAEA,MAAM8F,oBAAoB/I,0BACxBiI,cAAc5F,OAAO;wBAGvBZ,MAAM,kBAAkBwG,cAAcK,MAAM,EAAES;wBAE9C,IAAIA,iBAAiB,CAAC,gCAAgC,EAAE;4BACtD,MAAMC,oBAAiC,IAAIC;4BAC3C,IAAIC,kBACFH,iBAAiB,CAAC,gCAAgC;4BAEpD,IAAI,OAAOG,oBAAoB,UAAU;gCACvCA,kBAAkBA,gBAAgB1F,KAAK,CAAC;4BAC1C;4BAEA,KAAK,MAAM2F,OAAOD,gBAAiB;gCACjCF,kBAAkBI,GAAG,CAACD,IAAIE,IAAI;4BAChC;4BACA,OAAON,iBAAiB,CAAC,gCAAgC;4BAEzD,kBAAkB;4BAClB,KAAK,MAAMI,OAAOlC,OAAOqC,IAAI,CAACzG,IAAIR,OAAO,EAAG;gCAC1C,IAAI,CAAC2G,kBAAkBvD,GAAG,CAAC0D,MAAM;oCAC/B,OAAOtG,IAAIR,OAAO,CAAC8G,IAAI;gCACzB;4BACF;4BAEA,yBAAyB;4BACzB,KAAK,MAAMA,OAAOH,kBAAkBM,IAAI,GAAI;gCAC1C,MAAMC,WAAW,0BAA0BJ;gCAC3C,MAAMK,WAAWT,iBAAiB,CAACQ,SAAS;gCAC5C,MAAME,WAAW5G,IAAIR,OAAO,CAAC8G,IAAI;gCAEjC,IAAIM,aAAaD,UAAU;oCACzB3G,IAAIR,OAAO,CAAC8G,IAAI,GAAGK,aAAa,OAAO1E,YAAY0E;gCACrD;gCACA,OAAOT,iBAAiB,CAACQ,SAAS;4BACpC;wBACF;wBAEA,IACE,CAACR,iBAAiB,CAAC,uBAAuB,IAC1C,CAACA,iBAAiB,CAAC,oBAAoB,IACvC,CAACA,iBAAiB,CAAC,WAAW,EAC9B;4BACAA,iBAAiB,CAAC,uBAAuB,GAAG;wBAC9C;wBACA,OAAOA,iBAAiB,CAAC,oBAAoB;wBAE7C,KAAK,MAAM,CAACI,KAAKO,MAAM,IAAIzC,OAAO0C,OAAO,CAAC;4BACxC,GAAG/J,iBAAiBmJ,mBAAmBlJ,oBAAoB;wBAC7D,GAAI;4BACF,IACE;gCACE;gCACA;gCACA;gCACA;6BACD,CAACiE,QAAQ,CAACqF,MACX;gCACA;4BACF;4BAEA,gEAAgE;4BAChE,kEAAkE;4BAClE,IAAIA,QAAQ,2BAA2B;gCACrCtG,IAAIR,OAAO,CAAC8G,IAAI,GAAGO;gCACnB;4BACF;4BAEA,IAAIA,OAAO;gCACTxG,UAAU,CAACiG,IAAI,GAAGO;gCAClB7G,IAAIR,OAAO,CAAC8G,IAAI,GAAGO;4BACrB;wBACF;wBAEA,IAAIX,iBAAiB,CAAC,uBAAuB,EAAE;4BAC7C,MAAMW,QAAQX,iBAAiB,CAAC,uBAAuB;4BACvD,MAAMa,cAAcvJ,eAAeqJ,OAAO3F;4BAC1Cb,UAAU,CAAC,uBAAuB,GAAG0G;4BAErCxG,YAAY5D,IAAI6D,KAAK,CAACuG,aAAa;4BAEnC,IAAIxG,UAAUO,QAAQ,EAAE;gCACtB,OAAO;oCACLP;oCACAF;oCACAD,UAAU;gCACZ;4BACF;4BAEA,IAAIrB,OAAOmD,IAAI,EAAE;gCACf,MAAM2C,kBAAkBjH,oBACtB2C,UAAUmB,QAAQ,IAAI,IACtB3C,OAAOmD,IAAI,CAACI,OAAO;gCAGrB,IAAIuC,gBAAgBrC,cAAc,EAAE;oCAClCxE,eAAegC,KAAK,UAAU6E,gBAAgBrC,cAAc;gCAC9D;4BACF;wBACF;wBAEA,IAAI0D,iBAAiB,CAAC,WAAW,EAAE;4BACjC,MAAMW,QAAQX,iBAAiB,CAAC,WAAW;4BAC3C,MAAMc,MAAMxJ,eAAeqJ,OAAO3F;4BAClCb,UAAU,CAAC,WAAW,GAAG2G;4BACzBzG,YAAY5D,IAAI6D,KAAK,CAACwG,KAAK;4BAE3B,OAAO;gCACLzG;gCACAF;gCACAD,UAAU;gCACVS,YAAYuE,cAAcK,MAAM;4BAClC;wBACF;wBAEA,IAAIS,iBAAiB,CAAC,uBAAuB,EAAE;4BAC7C,OAAO;gCACL3F;gCACAF;gCACAD,UAAU;gCACViF;gCACAxE,YAAYuE,cAAcK,MAAM;4BAClC;wBACF;oBACF;gBACF;gBAEA,kBAAkB;gBAClB,IACE,AAAC,CAAA,gBAAgBjC,SAAS,eAAeA,KAAI,KAC7CA,MAAMuD,WAAW,EACjB;oBACA,MAAM,EAAEE,iBAAiB,EAAE,GAAG7I,mBAAmB;wBAC/C8I,qBAAqB;wBACrBH,aAAavD,MAAMuD,WAAW;wBAC9BrD,QAAQA;wBACRlC,OAAOjB,UAAUiB,KAAK;oBACxB;oBAEA,MAAM,EAAEA,KAAK,EAAE,GAAGyF;oBAClB,OAAO,AAACA,kBAA0BzF,KAAK;oBAEvCyF,kBAAkBE,MAAM,GAAGlK,eAAe+C,KAAYwB;oBAEtDyF,kBAAkBvF,QAAQ,GAAGnE,yBAC3B0J,kBAAkBvF,QAAQ;oBAG5B,OAAO;wBACLtB,UAAU;wBACV,oCAAoC;wBACpCG,WAAW0G;wBACXpG,YAAYvD,kBAAkBkG;oBAChC;gBACF;gBAEA,iBAAiB;gBACjB,IAAIA,MAAMhE,OAAO,EAAE;oBACjB,MAAM2E,YAAYC,OAAOqC,IAAI,CAAC/C,QAAQL,MAAM,GAAG;oBAC/C,KAAK,MAAM+D,UAAU5D,MAAMhE,OAAO,CAAE;wBAClC,IAAI,EAAE8G,GAAG,EAAEO,KAAK,EAAE,GAAGO;wBACrB,IAAIjD,WAAW;4BACbmC,MAAMrI,eAAeqI,KAAK5C;4BAC1BmD,QAAQ5I,eAAe4I,OAAOnD;wBAChC;wBAEA,IAAI4C,IAAIe,WAAW,OAAO,cAAc;4BACtC,IAAI,CAACC,MAAMC,OAAO,CAAClH,UAAU,CAACiG,IAAI,GAAG;gCACnC,MAAMkB,MAAMnH,UAAU,CAACiG,IAAI;gCAC3BjG,UAAU,CAACiG,IAAI,GAAG,OAAOkB,QAAQ,WAAW;oCAACA;iCAAI,GAAG,EAAE;4BACxD;;4BACEnH,UAAU,CAACiG,IAAI,CAAcmB,IAAI,CAACZ;wBACtC,OAAO;4BACLxG,UAAU,CAACiG,IAAI,GAAGO;wBACpB;oBACF;gBACF;gBAEA,iBAAiB;gBACjB,IAAIrD,MAAMuD,WAAW,EAAE;oBACrB,IAAIW,gBAAgBhE;oBAEpB,IAAI;wBACF,+EAA+E;wBAC/E,gFAAgF;wBAChF,8EAA8E;wBAC9E,IAAIhF,2BAA2B8E,QAAmB;4BAChD,MAAMmE,cACJ3H,IAAIR,OAAO,CAACjB,8BAA8B8I,WAAW,GAAG;4BAE1D,IAAIM,aAAa;gCACfD,gBAAgB;oCACd,GAAGjJ,kBACDE,kCAAkCgJ,aACnC;oCACD,GAAGjE,MAAM;gCACX;4BACF;wBACF;oBACF,EAAE,OAAO6B,KAAK;oBACZ,wFAAwF;oBACxF,sDAAsD;oBACxD;oBAEA,sEAAsE;oBACtE,2DAA2D;oBAC3D,sDAAsD;oBACtD,MAAM,EAAE4B,QAAQS,iBAAiB,EAAElG,UAAUmG,mBAAmB,EAAE,GAChE1J,iBAAiB;wBACf4I,aAAavD,MAAMuD,WAAW;wBAC9BrD,QAAQgE;wBACRlG,OAAOjB,UAAUiB,KAAK;oBACxB;oBAEF,MAAM,EAAEyF,iBAAiB,EAAE,GAAG7I,mBAAmB;wBAC/C8I,qBAAqB;wBACrBH,aAAavD,MAAMuD,WAAW;wBAC9BrD,QAAQgE;wBACRlG,OAAOjB,UAAUiB,KAAK;oBACxB;oBAEA,IAAIyF,kBAAkBnG,QAAQ,EAAE;wBAC9B,OAAO;4BACL,oCAAoC;4BACpCP,WAAW0G;4BACX7G,UAAU;wBACZ;oBACF;oBAEA,yDAAyD;oBACzD,IAAIJ,IAAIR,OAAO,CAAChB,WAAW6I,WAAW,GAAG,KAAK,KAAK;wBACjD,kEAAkE;wBAClE,sDAAsD;wBACtD,IAAI9G,UAAUmB,QAAQ,KAAKmG,qBAAqB;4BAC9C5H,IAAI6H,SAAS,CAACzJ,4BAA4BwJ;wBAC5C;wBACA,IAAID,mBAAmB;4BACrB3H,IAAI6H,SAAS,CACXxJ,6BACA,uCAAuC;4BACvCsJ,kBAAkBG,KAAK,CAAC;wBAE5B;oBACF;oBAEA,IAAIhJ,OAAOmD,IAAI,EAAE;wBACf,MAAM2C,kBAAkBjH,oBACtBC,iBAAiBoJ,kBAAkBvF,QAAQ,EAAE3C,OAAOsD,QAAQ,GAC5DtD,OAAOmD,IAAI,CAACI,OAAO;wBAGrB,IAAIuC,gBAAgBrC,cAAc,EAAE;4BAClCxE,eAAegC,KAAK,UAAU6E,gBAAgBrC,cAAc;wBAC9D;oBACF;oBACA/B,aAAa;oBACbF,UAAUmB,QAAQ,GAAGuF,kBAAkBvF,QAAQ;oBAC/C0C,OAAOC,MAAM,CAAC9D,UAAUiB,KAAK,EAAEyF,kBAAkBzF,KAAK;gBACxD;gBAEA,qBAAqB;gBACrB,IAAIgC,MAAM3D,KAAK,EAAE;oBACf,MAAMgD,SAAS,MAAMF;oBAErB,IAAIE,QAAQ;wBACV,OAAO;4BACLtC;4BACAF;4BACAD,UAAU;4BACVE,eAAeuC;wBACjB;oBACF;gBACF;YACF;QACF;QAEA,KAAK,MAAMW,SAASpE,OAAQ;YAC1B,MAAMoF,SAAS,MAAMT,YAAYP;YACjC,IAAIgB,QAAQ;gBACV,OAAOA;YACT;QACF;QAEA,OAAO;YACLpE;YACAG;YACAF;YACAC;QACF;IACF;IAEA,OAAOP;AACT"}