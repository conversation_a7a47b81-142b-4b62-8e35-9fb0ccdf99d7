{"version": 3, "sources": ["../../../src/server/lib/app-info-log.ts"], "sourcesContent": ["import { loadEnvConfig } from '@next/env'\nimport * as Log from '../../build/output/log'\nimport { bold, purple } from '../../lib/picocolors'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_PRODUCTION_BUILD,\n} from '../../shared/lib/constants'\nimport loadConfig, {\n  getConfiguredExperimentalFeatures,\n  type ConfiguredExperimentalFeature,\n} from '../config'\n\nexport function logStartInfo({\n  networkUrl,\n  appUrl,\n  envInfo,\n  experimentalFeatures,\n  maxExperimentalFeatures = Infinity,\n}: {\n  networkUrl: string | null\n  appUrl: string | null\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n  maxExperimentalFeatures?: number\n}) {\n  Log.bootstrap(\n    `${bold(\n      purple(`${Log.prefixes.ready} Next.js ${process.env.__NEXT_VERSION}`)\n    )}${process.env.TURBOPACK ? ' (Turbopack)' : ''}`\n  )\n  if (appUrl) {\n    Log.bootstrap(`- Local:        ${appUrl}`)\n  }\n  if (networkUrl) {\n    Log.bootstrap(`- Network:      ${networkUrl}`)\n  }\n  if (envInfo?.length) Log.bootstrap(`- Environments: ${envInfo.join(', ')}`)\n\n  if (experimentalFeatures?.length) {\n    Log.bootstrap(`- Experiments (use with caution):`)\n    // only show a maximum number of flags\n    for (const exp of experimentalFeatures.slice(0, maxExperimentalFeatures)) {\n      const symbol =\n        exp.type === 'boolean'\n          ? exp.value === true\n            ? bold('✓')\n            : bold('⨯')\n          : '·'\n\n      const suffix = exp.type === 'number' ? `: ${exp.value}` : ''\n\n      Log.bootstrap(`  ${symbol} ${exp.name}${suffix}`)\n    }\n    /* indicate if there are more than the maximum shown no. flags */\n    if (experimentalFeatures.length > maxExperimentalFeatures) {\n      Log.bootstrap(`  · ...`)\n    }\n  }\n\n  // New line after the bootstrap info\n  Log.info('')\n}\n\nexport async function getStartServerInfo(\n  dir: string,\n  dev: boolean\n): Promise<{\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n}> {\n  let experimentalFeatures: ConfiguredExperimentalFeature[] = []\n  await loadConfig(\n    dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_BUILD,\n    dir,\n    {\n      onLoadUserConfig(userConfig) {\n        const configuredExperimentalFeatures =\n          getConfiguredExperimentalFeatures(userConfig.experimental)\n\n        experimentalFeatures = configuredExperimentalFeatures.sort(\n          ({ name: a }, { name: b }) => a.length - b.length\n        )\n      },\n    }\n  )\n\n  // we need to reset env if we are going to create\n  // the worker process with the esm loader so that the\n  // initial env state is correct\n  let envInfo: string[] = []\n  const { loadedEnvFiles } = loadEnvConfig(dir, true, console, false)\n  if (loadedEnvFiles.length > 0) {\n    envInfo = loadedEnvFiles.map((f) => f.path)\n  }\n\n  return {\n    envInfo,\n    experimentalFeatures,\n  }\n}\n"], "names": ["loadEnvConfig", "Log", "bold", "purple", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "loadConfig", "getConfiguredExperimentalFeatures", "logStartInfo", "networkUrl", "appUrl", "envInfo", "experimentalFeatures", "maxExperimentalFeatures", "Infinity", "bootstrap", "prefixes", "ready", "process", "env", "__NEXT_VERSION", "TURBOPACK", "length", "join", "exp", "slice", "symbol", "type", "value", "suffix", "name", "info", "getStartServerInfo", "dir", "dev", "onLoadUserConfig", "userConfig", "configuredExperimentalFeatures", "experimental", "sort", "a", "b", "loadedEnvFiles", "console", "map", "f", "path"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAW;AACzC,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,IAAI,EAAEC,MAAM,QAAQ,uBAAsB;AACnD,SACEC,wBAAwB,EACxBC,sBAAsB,QACjB,6BAA4B;AACnC,OAAOC,cACLC,iCAAiC,QAE5B,YAAW;AAElB,OAAO,SAASC,aAAa,EAC3BC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,oBAAoB,EACpBC,0BAA0BC,QAAQ,EAOnC;IACCb,IAAIc,SAAS,CACX,GAAGb,KACDC,OAAO,GAAGF,IAAIe,QAAQ,CAACC,KAAK,CAAC,SAAS,EAAEC,QAAQC,GAAG,CAACC,cAAc,EAAE,KAClEF,QAAQC,GAAG,CAACE,SAAS,GAAG,iBAAiB,IAAI;IAEnD,IAAIX,QAAQ;QACVT,IAAIc,SAAS,CAAC,CAAC,gBAAgB,EAAEL,QAAQ;IAC3C;IACA,IAAID,YAAY;QACdR,IAAIc,SAAS,CAAC,CAAC,gBAAgB,EAAEN,YAAY;IAC/C;IACA,IAAIE,2BAAAA,QAASW,MAAM,EAAErB,IAAIc,SAAS,CAAC,CAAC,gBAAgB,EAAEJ,QAAQY,IAAI,CAAC,OAAO;IAE1E,IAAIX,wCAAAA,qBAAsBU,MAAM,EAAE;QAChCrB,IAAIc,SAAS,CAAC,CAAC,iCAAiC,CAAC;QACjD,sCAAsC;QACtC,KAAK,MAAMS,OAAOZ,qBAAqBa,KAAK,CAAC,GAAGZ,yBAA0B;YACxE,MAAMa,SACJF,IAAIG,IAAI,KAAK,YACTH,IAAII,KAAK,KAAK,OACZ1B,KAAK,OACLA,KAAK,OACP;YAEN,MAAM2B,SAASL,IAAIG,IAAI,KAAK,WAAW,CAAC,EAAE,EAAEH,IAAII,KAAK,EAAE,GAAG;YAE1D3B,IAAIc,SAAS,CAAC,CAAC,EAAE,EAAEW,OAAO,CAAC,EAAEF,IAAIM,IAAI,GAAGD,QAAQ;QAClD;QACA,+DAA+D,GAC/D,IAAIjB,qBAAqBU,MAAM,GAAGT,yBAAyB;YACzDZ,IAAIc,SAAS,CAAC,CAAC,OAAO,CAAC;QACzB;IACF;IAEA,oCAAoC;IACpCd,IAAI8B,IAAI,CAAC;AACX;AAEA,OAAO,eAAeC,mBACpBC,GAAW,EACXC,GAAY;IAKZ,IAAItB,uBAAwD,EAAE;IAC9D,MAAMN,WACJ4B,MAAM9B,2BAA2BC,wBACjC4B,KACA;QACEE,kBAAiBC,UAAU;YACzB,MAAMC,iCACJ9B,kCAAkC6B,WAAWE,YAAY;YAE3D1B,uBAAuByB,+BAA+BE,IAAI,CACxD,CAAC,EAAET,MAAMU,CAAC,EAAE,EAAE,EAAEV,MAAMW,CAAC,EAAE,GAAKD,EAAElB,MAAM,GAAGmB,EAAEnB,MAAM;QAErD;IACF;IAGF,iDAAiD;IACjD,qDAAqD;IACrD,+BAA+B;IAC/B,IAAIX,UAAoB,EAAE;IAC1B,MAAM,EAAE+B,cAAc,EAAE,GAAG1C,cAAciC,KAAK,MAAMU,SAAS;IAC7D,IAAID,eAAepB,MAAM,GAAG,GAAG;QAC7BX,UAAU+B,eAAeE,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI;IAC5C;IAEA,OAAO;QACLnC;QACAC;IACF;AACF"}