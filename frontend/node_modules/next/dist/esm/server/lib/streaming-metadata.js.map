{"version": 3, "sources": ["../../../src/server/lib/streaming-metadata.ts"], "sourcesContent": ["import {\n  getBotType,\n  HTML_LIMITED_BOT_UA_RE_STRING,\n} from '../../shared/lib/router/utils/is-bot'\nimport type { BaseNextRequest } from '../base-http'\n\nexport function shouldServeStreamingMetadata(\n  userAgent: string,\n  htmlLimitedBots: string | undefined\n): boolean {\n  const blockingMetadataUARegex = new RegExp(\n    htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING,\n    'i'\n  )\n  return (\n    // When it's static generation, userAgents are not available - do not serve streaming metadata\n    !!userAgent && !blockingMetadataUARegex.test(userAgent)\n  )\n}\n\n// When the request UA is a html-limited bot, we should do a dynamic render.\n// In this case, postpone state is not sent.\nexport function isHtmlBotRequest(req: BaseNextRequest): boolean {\n  const ua = req.headers['user-agent'] || ''\n  const botType = getBotType(ua)\n\n  return botType === 'html'\n}\n"], "names": ["getBotType", "HTML_LIMITED_BOT_UA_RE_STRING", "shouldServeStreamingMetadata", "userAgent", "htmlLimitedBots", "blockingMetadataUARegex", "RegExp", "test", "isHtmlBotRequest", "req", "ua", "headers", "botType"], "mappings": "AAAA,SACEA,UAAU,EACVC,6BAA6B,QACxB,uCAAsC;AAG7C,OAAO,SAASC,6BACdC,SAAiB,EACjBC,eAAmC;IAEnC,MAAMC,0BAA0B,IAAIC,OAClCF,mBAAmBH,+BACnB;IAEF,OACE,8FAA8F;IAC9F,CAAC,CAACE,aAAa,CAACE,wBAAwBE,IAAI,CAACJ;AAEjD;AAEA,4EAA4E;AAC5E,4CAA4C;AAC5C,OAAO,SAASK,iBAAiBC,GAAoB;IACnD,MAAMC,KAAKD,IAAIE,OAAO,CAAC,aAAa,IAAI;IACxC,MAAMC,UAAUZ,WAAWU;IAE3B,OAAOE,YAAY;AACrB"}