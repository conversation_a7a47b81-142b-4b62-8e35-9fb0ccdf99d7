{"version": 3, "sources": ["../../../../src/server/lib/cache-handlers/default.ts"], "sourcesContent": ["/*\n  This is the default \"use cache\" handler it defaults\n  to an in memory store\n*/\nimport { LRUCache } from '../lru-cache'\nimport type { <PERSON>acheEntry, CacheHandler } from './types'\nimport {\n  isTagStale,\n  tagsManifest,\n} from '../incremental-cache/tags-manifest.external'\n\ntype PrivateCacheEntry = {\n  entry: CacheEntry\n\n  // For the default cache we store errored cache\n  // entries and allow them to be used up to 3 times\n  // after that we want to dispose it and try for fresh\n\n  // If an entry is errored we return no entry\n  // three times so that we retry hitting origin (MISS)\n  // and then if it still fails to set after the third we\n  // return the errored content and use expiration of\n  // Math.min(30, entry.expiration)\n  isErrored: boolean\n  errorRetryCount: number\n\n  // compute size on set since we need to read size\n  // of the ReadableStream for LRU evicting\n  size: number\n}\n\n// LRU cache default to max 50 MB but in future track\nconst memoryCache = new LRUCache<PrivateCacheEntry>(\n  50 * 1024 * 1024,\n  (entry) => entry.size\n)\nconst pendingSets = new Map<string, Promise<void>>()\n\nconst DefaultCacheHandler: CacheHandler = {\n  async get(cacheKey, softTags) {\n    await pendingSets.get(cacheKey)\n\n    const privateEntry = memoryCache.get(cacheKey)\n\n    if (!privateEntry) {\n      return undefined\n    }\n\n    const entry = privateEntry.entry\n    if (\n      performance.timeOrigin + performance.now() >\n      entry.timestamp + entry.revalidate * 1000\n    ) {\n      // In memory caches should expire after revalidate time because it is unlikely that\n      // a new entry will be able to be used before it is dropped from the cache.\n      return undefined\n    }\n\n    if (\n      isTagStale(entry.tags, entry.timestamp) ||\n      isTagStale(softTags, entry.timestamp)\n    ) {\n      return undefined\n    }\n    const [returnStream, newSaved] = entry.value.tee()\n    entry.value = newSaved\n\n    return {\n      ...entry,\n      value: returnStream,\n    }\n  },\n\n  async set(cacheKey, pendingEntry) {\n    let resolvePending: () => void = () => {}\n    const pendingPromise = new Promise<void>((resolve) => {\n      resolvePending = resolve\n    })\n    pendingSets.set(cacheKey, pendingPromise)\n\n    const entry = await pendingEntry\n\n    let size = 0\n\n    try {\n      const [value, clonedValue] = entry.value.tee()\n      entry.value = value\n      const reader = clonedValue.getReader()\n\n      for (let chunk; !(chunk = await reader.read()).done; ) {\n        size += Buffer.from(chunk.value).byteLength\n      }\n\n      memoryCache.set(cacheKey, {\n        entry,\n        isErrored: false,\n        errorRetryCount: 0,\n        size,\n      })\n    } catch {\n      // TODO: store partial buffer with error after we retry 3 times\n    } finally {\n      resolvePending()\n      pendingSets.delete(cacheKey)\n    }\n  },\n\n  async expireTags(...tags) {\n    for (const tag of tags) {\n      if (!tagsManifest.items[tag]) {\n        tagsManifest.items[tag] = {}\n      }\n      // TODO: use performance.now and update file-system-cache?\n      tagsManifest.items[tag].revalidatedAt = Date.now()\n    }\n  },\n\n  // This is only meant to invalidate in memory tags\n  // not meant to be propagated like expireTags would\n  // in multi-instance scenario\n  async receiveExpiredTags(...tags): Promise<void> {\n    return this.expireTags(...tags)\n  },\n}\n\nexport default DefaultCacheHandler\n"], "names": ["L<PERSON><PERSON><PERSON>", "isTagStale", "tagsManifest", "memoryCache", "entry", "size", "pendingSets", "Map", "DefaultCache<PERSON>andler", "get", "cache<PERSON>ey", "softTags", "privateEntry", "undefined", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "timestamp", "revalidate", "tags", "returnStream", "newSaved", "value", "tee", "set", "pendingEntry", "resolvePending", "pendingPromise", "Promise", "resolve", "clonedV<PERSON>ue", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunk", "read", "done", "<PERSON><PERSON><PERSON>", "from", "byteLength", "isErrored", "errorRetryCount", "delete", "expireTags", "tag", "items", "revalidatedAt", "Date", "receiveExpiredTags"], "mappings": "AAAA;;;AAGA,GACA,SAASA,QAAQ,QAAQ,eAAc;AAEvC,SACEC,UAAU,EACVC,YAAY,QACP,8CAA6C;AAsBpD,qDAAqD;AACrD,MAAMC,cAAc,IAAIH,SACtB,KAAK,OAAO,MACZ,CAACI,QAAUA,MAAMC,IAAI;AAEvB,MAAMC,cAAc,IAAIC;AAExB,MAAMC,sBAAoC;IACxC,MAAMC,KAAIC,QAAQ,EAAEC,QAAQ;QAC1B,MAAML,YAAYG,GAAG,CAACC;QAEtB,MAAME,eAAeT,YAAYM,GAAG,CAACC;QAErC,IAAI,CAACE,cAAc;YACjB,OAAOC;QACT;QAEA,MAAMT,QAAQQ,aAAaR,KAAK;QAChC,IACEU,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KACxCZ,MAAMa,SAAS,GAAGb,MAAMc,UAAU,GAAG,MACrC;YACA,mFAAmF;YACnF,2EAA2E;YAC3E,OAAOL;QACT;QAEA,IACEZ,WAAWG,MAAMe,IAAI,EAAEf,MAAMa,SAAS,KACtChB,WAAWU,UAAUP,MAAMa,SAAS,GACpC;YACA,OAAOJ;QACT;QACA,MAAM,CAACO,cAAcC,SAAS,GAAGjB,MAAMkB,KAAK,CAACC,GAAG;QAChDnB,MAAMkB,KAAK,GAAGD;QAEd,OAAO;YACL,GAAGjB,KAAK;YACRkB,OAAOF;QACT;IACF;IAEA,MAAMI,KAAId,QAAQ,EAAEe,YAAY;QAC9B,IAAIC,iBAA6B,KAAO;QACxC,MAAMC,iBAAiB,IAAIC,QAAc,CAACC;YACxCH,iBAAiBG;QACnB;QACAvB,YAAYkB,GAAG,CAACd,UAAUiB;QAE1B,MAAMvB,QAAQ,MAAMqB;QAEpB,IAAIpB,OAAO;QAEX,IAAI;YACF,MAAM,CAACiB,OAAOQ,YAAY,GAAG1B,MAAMkB,KAAK,CAACC,GAAG;YAC5CnB,MAAMkB,KAAK,GAAGA;YACd,MAAMS,SAASD,YAAYE,SAAS;YAEpC,IAAK,IAAIC,OAAO,CAAC,AAACA,CAAAA,QAAQ,MAAMF,OAAOG,IAAI,EAAC,EAAGC,IAAI,EAAI;gBACrD9B,QAAQ+B,OAAOC,IAAI,CAACJ,MAAMX,KAAK,EAAEgB,UAAU;YAC7C;YAEAnC,YAAYqB,GAAG,CAACd,UAAU;gBACxBN;gBACAmC,WAAW;gBACXC,iBAAiB;gBACjBnC;YACF;QACF,EAAE,OAAM;QACN,+DAA+D;QACjE,SAAU;YACRqB;YACApB,YAAYmC,MAAM,CAAC/B;QACrB;IACF;IAEA,MAAMgC,YAAW,GAAGvB,IAAI;QACtB,KAAK,MAAMwB,OAAOxB,KAAM;YACtB,IAAI,CAACjB,aAAa0C,KAAK,CAACD,IAAI,EAAE;gBAC5BzC,aAAa0C,KAAK,CAACD,IAAI,GAAG,CAAC;YAC7B;YACA,0DAA0D;YAC1DzC,aAAa0C,KAAK,CAACD,IAAI,CAACE,aAAa,GAAGC,KAAK9B,GAAG;QAClD;IACF;IAEA,kDAAkD;IAClD,mDAAmD;IACnD,6BAA6B;IAC7B,MAAM+B,oBAAmB,GAAG5B,IAAI;QAC9B,OAAO,IAAI,CAACuB,UAAU,IAAIvB;IAC5B;AACF;AAEA,eAAeX,oBAAmB"}