import { getNetworkHost } from '../../lib/get-network-host';
if (performance.getEntriesByName('next-start').length === 0) {
    performance.mark('next-start');
}
import '../next';
import '../require-hook';
import fs from 'fs';
import v8 from 'v8';
import path from 'path';
import http from 'http';
import https from 'https';
import os from 'os';
import Watchpack from 'next/dist/compiled/watchpack';
import * as Log from '../../build/output/log';
import setupDebug from 'next/dist/compiled/debug';
import { RESTART_EXIT_CODE, getFormattedDebugAddress, getNodeDebugType } from './utils';
import { formatHostname } from './format-hostname';
import { initialize } from './router-server';
import { CONFIG_FILES } from '../../shared/lib/constants';
import { getStartServerInfo, logStartInfo } from './app-info-log';
import { validateTurboNextConfig } from '../../lib/turbopack-warning';
import { trace, flushAllTraces } from '../../trace';
import { isPostpone } from './router-utils/is-postpone';
import { isIPv6 } from './is-ipv6';
import { AsyncCallbackSet } from './async-callback-set';
const debug = setupDebug('next:start-server');
let startServerSpan;
export async function getRequestHandlers({ dir, port, isDev, onDevServerCleanup, server, hostname, minimalMode, keepAliveTimeout, experimentalHttpsServer, quiet }) {
    return initialize({
        dir,
        port,
        hostname,
        onDevServerCleanup,
        dev: isDev,
        minimalMode,
        server,
        keepAliveTimeout,
        experimentalHttpsServer,
        startServerSpan,
        quiet
    });
}
export async function startServer(serverOptions) {
    const { dir, isDev, hostname, minimalMode, allowRetry, keepAliveTimeout, selfSignedCertificate } = serverOptions;
    let { port } = serverOptions;
    process.title = `next-server (v${"15.2.4"})`;
    let handlersReady = ()=>{};
    let handlersError = ()=>{};
    let handlersPromise = new Promise((resolve, reject)=>{
        handlersReady = resolve;
        handlersError = reject;
    });
    let requestHandler = async (req, res)=>{
        if (handlersPromise) {
            await handlersPromise;
            return requestHandler(req, res);
        }
        throw Object.defineProperty(new Error('Invariant request handler was not setup'), "__NEXT_ERROR_CODE", {
            value: "E287",
            enumerable: false,
            configurable: true
        });
    };
    let upgradeHandler = async (req, socket, head)=>{
        if (handlersPromise) {
            await handlersPromise;
            return upgradeHandler(req, socket, head);
        }
        throw Object.defineProperty(new Error('Invariant upgrade handler was not setup'), "__NEXT_ERROR_CODE", {
            value: "E290",
            enumerable: false,
            configurable: true
        });
    };
    let nextServer;
    // setup server listener as fast as possible
    if (selfSignedCertificate && !isDev) {
        throw Object.defineProperty(new Error('Using a self signed certificate is only supported with `next dev`.'), "__NEXT_ERROR_CODE", {
            value: "E128",
            enumerable: false,
            configurable: true
        });
    }
    async function requestListener(req, res) {
        try {
            if (handlersPromise) {
                await handlersPromise;
                handlersPromise = undefined;
            }
            await requestHandler(req, res);
        } catch (err) {
            res.statusCode = 500;
            res.end('Internal Server Error');
            Log.error(`Failed to handle request for ${req.url}`);
            console.error(err);
        } finally{
            if (isDev) {
                if (v8.getHeapStatistics().used_heap_size > 0.8 * v8.getHeapStatistics().heap_size_limit) {
                    Log.warn(`Server is approaching the used memory threshold, restarting...`);
                    trace('server-restart-close-to-memory-threshold', undefined, {
                        'memory.heapSizeLimit': String(v8.getHeapStatistics().heap_size_limit),
                        'memory.heapUsed': String(v8.getHeapStatistics().used_heap_size)
                    }).stop();
                    await flushAllTraces();
                    process.exit(RESTART_EXIT_CODE);
                }
            }
        }
    }
    const server = selfSignedCertificate ? https.createServer({
        key: fs.readFileSync(selfSignedCertificate.key),
        cert: fs.readFileSync(selfSignedCertificate.cert)
    }, requestListener) : http.createServer(requestListener);
    if (keepAliveTimeout) {
        server.keepAliveTimeout = keepAliveTimeout;
    }
    server.on('upgrade', async (req, socket, head)=>{
        try {
            await upgradeHandler(req, socket, head);
        } catch (err) {
            socket.destroy();
            Log.error(`Failed to handle request for ${req.url}`);
            console.error(err);
        }
    });
    let portRetryCount = 0;
    server.on('error', (err)=>{
        if (allowRetry && port && isDev && err.code === 'EADDRINUSE' && portRetryCount < 10) {
            Log.warn(`Port ${port} is in use, trying ${port + 1} instead.`);
            port += 1;
            portRetryCount += 1;
            server.listen(port, hostname);
        } else {
            Log.error(`Failed to start server`);
            console.error(err);
            process.exit(1);
        }
    });
    let cleanupListeners = isDev ? new AsyncCallbackSet() : undefined;
    await new Promise((resolve)=>{
        server.on('listening', async ()=>{
            const nodeDebugType = getNodeDebugType();
            const addr = server.address();
            const actualHostname = formatHostname(typeof addr === 'object' ? (addr == null ? void 0 : addr.address) || hostname || 'localhost' : addr);
            const formattedHostname = !hostname || actualHostname === '0.0.0.0' ? 'localhost' : actualHostname === '[::]' ? '[::1]' : formatHostname(hostname);
            port = typeof addr === 'object' ? (addr == null ? void 0 : addr.port) || port : port;
            const networkHostname = hostname ?? getNetworkHost(isIPv6(actualHostname) ? 'IPv6' : 'IPv4');
            const protocol = selfSignedCertificate ? 'https' : 'http';
            const networkUrl = networkHostname ? `${protocol}://${formatHostname(networkHostname)}:${port}` : null;
            const appUrl = `${protocol}://${formattedHostname}:${port}`;
            if (nodeDebugType) {
                const formattedDebugAddress = getFormattedDebugAddress();
                Log.info(`the --${nodeDebugType} option was detected, the Next.js router server should be inspected at ${formattedDebugAddress}.`);
            }
            // Store the selected port to:
            // - expose it to render workers
            // - re-use it for automatic dev server restarts with a randomly selected port
            process.env.PORT = port + '';
            process.env.__NEXT_PRIVATE_ORIGIN = appUrl;
            // Only load env and config in dev to for logging purposes
            let envInfo;
            let experimentalFeatures;
            if (isDev) {
                const startServerInfo = await getStartServerInfo(dir, isDev);
                envInfo = startServerInfo.envInfo;
                experimentalFeatures = startServerInfo.experimentalFeatures;
            }
            logStartInfo({
                networkUrl,
                appUrl,
                envInfo,
                experimentalFeatures,
                maxExperimentalFeatures: 3
            });
            Log.event(`Starting...`);
            try {
                let cleanupStarted = false;
                let closeUpgraded = null;
                const cleanup = ()=>{
                    if (cleanupStarted) {
                        // We can get duplicate signals, e.g. when `ctrl+c` is used in an
                        // interactive shell (i.e. bash, zsh), the shell will recursively
                        // send SIGINT to children. The parent `next-dev` process will also
                        // send us SIGINT.
                        return;
                    }
                    cleanupStarted = true;
                    (async ()=>{
                        debug('start-server process cleanup');
                        // first, stop accepting new connections and finish pending requests,
                        // because they might affect `nextServer.close()` (e.g. by scheduling an `after`)
                        await new Promise((res)=>{
                            server.close((err)=>{
                                if (err) console.error(err);
                                res();
                            });
                            if (isDev) {
                                server.closeAllConnections();
                                closeUpgraded == null ? void 0 : closeUpgraded();
                            }
                        });
                        // now that no new requests can come in, clean up the rest
                        await Promise.all([
                            nextServer == null ? void 0 : nextServer.close().catch(console.error),
                            cleanupListeners == null ? void 0 : cleanupListeners.runAll().catch(console.error)
                        ]);
                        debug('start-server process cleanup finished');
                        process.exit(0);
                    })();
                };
                const exception = (err)=>{
                    if (isPostpone(err)) {
                        // React postpones that are unhandled might end up logged here but they're
                        // not really errors. They're just part of rendering.
                        return;
                    }
                    // This is the render worker, we keep the process alive
                    console.error(err);
                };
                // Make sure commands gracefully respect termination signals (e.g. from Docker)
                // Allow the graceful termination to be manually configurable
                if (!process.env.NEXT_MANUAL_SIG_HANDLE) {
                    process.on('SIGINT', cleanup);
                    process.on('SIGTERM', cleanup);
                }
                process.on('rejectionHandled', ()=>{
                // It is ok to await a Promise late in Next.js as it allows for better
                // prefetching patterns to avoid waterfalls. We ignore loggining these.
                // We should've already errored in anyway unhandledRejection.
                });
                process.on('uncaughtException', exception);
                process.on('unhandledRejection', exception);
                const initResult = await getRequestHandlers({
                    dir,
                    port,
                    isDev,
                    onDevServerCleanup: cleanupListeners ? cleanupListeners.add.bind(cleanupListeners) : undefined,
                    server,
                    hostname,
                    minimalMode,
                    keepAliveTimeout,
                    experimentalHttpsServer: !!selfSignedCertificate
                });
                requestHandler = initResult.requestHandler;
                upgradeHandler = initResult.upgradeHandler;
                nextServer = initResult.server;
                closeUpgraded = initResult.closeUpgraded;
                const startServerProcessDuration = performance.mark('next-start-end') && performance.measure('next-start-duration', 'next-start', 'next-start-end').duration;
                handlersReady();
                const formatDurationText = startServerProcessDuration > 2000 ? `${Math.round(startServerProcessDuration / 100) / 10}s` : `${Math.round(startServerProcessDuration)}ms`;
                Log.event(`Ready in ${formatDurationText}`);
                if (process.env.TURBOPACK) {
                    await validateTurboNextConfig({
                        dir: serverOptions.dir,
                        isDev: true
                    });
                }
            } catch (err) {
                // fatal error if we can't setup
                handlersError();
                console.error(err);
                process.exit(1);
            }
            resolve();
        });
        server.listen(port, hostname);
    });
    if (isDev) {
        function watchConfigFiles(dirToWatch, onChange) {
            const wp = new Watchpack();
            wp.watch({
                files: CONFIG_FILES.map((file)=>path.join(dirToWatch, file))
            });
            wp.on('change', onChange);
        }
        watchConfigFiles(dir, async (filename)=>{
            if (process.env.__NEXT_DISABLE_MEMORY_WATCHER) {
                Log.info(`Detected change, manual restart required due to '__NEXT_DISABLE_MEMORY_WATCHER' usage`);
                return;
            }
            Log.warn(`Found a change in ${path.basename(filename)}. Restarting the server to apply the changes...`);
            process.exit(RESTART_EXIT_CODE);
        });
    }
}
if (process.env.NEXT_PRIVATE_WORKER && process.send) {
    process.addListener('message', async (msg)=>{
        if (msg && typeof msg === 'object' && msg.nextWorkerOptions && process.send) {
            startServerSpan = trace('start-dev-server', undefined, {
                cpus: String(os.cpus().length),
                platform: os.platform(),
                'memory.freeMem': String(os.freemem()),
                'memory.totalMem': String(os.totalmem()),
                'memory.heapSizeLimit': String(v8.getHeapStatistics().heap_size_limit)
            });
            await startServerSpan.traceAsyncFn(()=>startServer(msg.nextWorkerOptions));
            const memoryUsage = process.memoryUsage();
            startServerSpan.setAttribute('memory.rss', String(memoryUsage.rss));
            startServerSpan.setAttribute('memory.heapTotal', String(memoryUsage.heapTotal));
            startServerSpan.setAttribute('memory.heapUsed', String(memoryUsage.heapUsed));
            process.send({
                nextServerReady: true,
                port: process.env.PORT
            });
        }
    });
    process.send({
        nextWorkerReady: true
    });
}

//# sourceMappingURL=start-server.js.map