{"version": 3, "sources": ["../../../../src/server/typescript/rules/error.ts"], "sourcesContent": ["// This module provides intellisense for all components that has the `\"use client\"` directive.\n\nimport { NEXT_TS_ERRORS } from '../constant'\nimport { getTs } from '../utils'\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst errorEntry = {\n  getSemanticDiagnostics(\n    source: tsModule.SourceFile,\n    isClientEntry: boolean\n  ): tsModule.Diagnostic[] {\n    const isErrorFile = /[\\\\/]error\\.tsx?$/.test(source.fileName)\n    const isGlobalErrorFile = /[\\\\/]global-error\\.tsx?$/.test(source.fileName)\n\n    if (!isErrorFile && !isGlobalErrorFile) return []\n\n    const ts = getTs()\n\n    if (!isClientEntry) {\n      // Error components must be Client components\n      return [\n        {\n          file: source,\n          category: ts.DiagnosticCategory.Error,\n          code: NEXT_TS_ERRORS.INVALID_ERROR_COMPONENT,\n          messageText: `Error Components must be Client Components, please add the \"use client\" directive: https://nextjs.org/docs/app/api-reference/file-conventions/error`,\n          start: 0,\n          length: source.text.length,\n        },\n      ]\n    }\n    return []\n  },\n}\n\nexport default errorEntry\n"], "names": ["NEXT_TS_ERRORS", "getTs", "errorEntry", "getSemanticDiagnostics", "source", "isClientEntry", "isErrorFile", "test", "fileName", "isGlobalErrorFile", "ts", "file", "category", "DiagnosticCategory", "Error", "code", "INVALID_ERROR_COMPONENT", "messageText", "start", "length", "text"], "mappings": "AAAA,8FAA8F;AAE9F,SAASA,cAAc,QAAQ,cAAa;AAC5C,SAASC,KAAK,QAAQ,WAAU;AAGhC,MAAMC,aAAa;IACjBC,wBACEC,MAA2B,EAC3BC,aAAsB;QAEtB,MAAMC,cAAc,oBAAoBC,IAAI,CAACH,OAAOI,QAAQ;QAC5D,MAAMC,oBAAoB,2BAA2BF,IAAI,CAACH,OAAOI,QAAQ;QAEzE,IAAI,CAACF,eAAe,CAACG,mBAAmB,OAAO,EAAE;QAEjD,MAAMC,KAAKT;QAEX,IAAI,CAACI,eAAe;YAClB,6CAA6C;YAC7C,OAAO;gBACL;oBACEM,MAAMP;oBACNQ,UAAUF,GAAGG,kBAAkB,CAACC,KAAK;oBACrCC,MAAMf,eAAegB,uBAAuB;oBAC5CC,aAAa,CAAC,mJAAmJ,CAAC;oBAClKC,OAAO;oBACPC,QAAQf,OAAOgB,IAAI,CAACD,MAAM;gBAC5B;aACD;QACH;QACA,OAAO,EAAE;IACX;AACF;AAEA,eAAejB,WAAU"}