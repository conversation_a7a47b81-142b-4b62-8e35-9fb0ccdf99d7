{"version": 3, "sources": ["../../src/server/optimize-amp.ts"], "sourcesContent": ["export default async function optimize(\n  html: string,\n  config: any\n): Promise<string> {\n  let AmpOptimizer\n  try {\n    AmpOptimizer = require('next/dist/compiled/@ampproject/toolbox-optimizer')\n  } catch (_) {\n    return html\n  }\n  const optimizer = AmpOptimizer.create(config)\n  return optimizer.transformHtml(html, config)\n}\n"], "names": ["optimize", "html", "config", "AmpOptimizer", "require", "_", "optimizer", "create", "transformHtml"], "mappings": "AAAA,eAAe,eAAeA,SAC5BC,IAAY,EACZC,MAAW;IAEX,IAAIC;IACJ,IAAI;QACFA,eAAeC,QAAQ;IACzB,EAAE,OAAOC,GAAG;QACV,OAAOJ;IACT;IACA,MAAMK,YAAYH,aAAaI,MAAM,CAACL;IACtC,OAAOI,UAAUE,aAAa,CAACP,MAAMC;AACvC"}