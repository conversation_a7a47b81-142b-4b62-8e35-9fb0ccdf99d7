{"version": 3, "sources": ["../../../src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport type { NextRouter } from '../../shared/lib/router/router'\n\nimport React from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport {\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkInstance,\n} from '../components/links'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  router: NextRouter | AppRouterInstance,\n  href: string,\n  as: string,\n  replace?: boolean,\n  shallow?: boolean,\n  scroll?: boolean\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (isAnchorNodeName && isModifiedEvent(e)) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    // If the router is an NextRouter instance it will have `beforePopState`\n    const routerScroll = scroll ?? true\n    if ('beforePopState' in router) {\n      router[replace ? 'replace' : 'push'](href, as, {\n        shallow,\n        scroll: routerScroll,\n      })\n    } else {\n      router[replace ? 'replace' : 'push'](as || href, {\n        scroll: routerScroll,\n      })\n    }\n  }\n\n  React.startTransition(navigate)\n}\n\ntype LinkPropsReal = React.PropsWithChildren<\n  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> &\n    LinkProps\n>\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nconst Link = React.forwardRef<HTMLAnchorElement, LinkPropsReal>(\n  function LinkComponent(props, forwardedRef) {\n    let children: React.ReactNode\n\n    const {\n      href: hrefProp,\n      as: asProp,\n      children: childrenProp,\n      prefetch: prefetchProp = null,\n      passHref,\n      replace,\n      shallow,\n      scroll,\n      onClick,\n      onMouseEnter: onMouseEnterProp,\n      onTouchStart: onTouchStartProp,\n      legacyBehavior = false,\n      ...restProps\n    } = props\n\n    children = childrenProp\n\n    if (\n      legacyBehavior &&\n      (typeof children === 'string' || typeof children === 'number')\n    ) {\n      children = <a>{children}</a>\n    }\n\n    const router = React.useContext(AppRouterContext)\n\n    const prefetchEnabled = prefetchProp !== false\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */\n    const appPrefetchKind =\n      prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n    if (process.env.NODE_ENV !== 'production') {\n      function createPropError(args: {\n        key: string\n        expected: string\n        actual: string\n      }) {\n        return new Error(\n          `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n            (typeof window !== 'undefined'\n              ? \"\\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n\n      // TypeScript trick for type-guarding:\n      const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n        href: true,\n      } as const\n      const requiredProps: LinkPropsRequired[] = Object.keys(\n        requiredPropsGuard\n      ) as LinkPropsRequired[]\n      requiredProps.forEach((key: LinkPropsRequired) => {\n        if (key === 'href') {\n          if (\n            props[key] == null ||\n            (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n          ) {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: props[key] === null ? 'null' : typeof props[key],\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n\n      // TypeScript trick for type-guarding:\n      const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n        as: true,\n        replace: true,\n        scroll: true,\n        shallow: true,\n        passHref: true,\n        prefetch: true,\n        onClick: true,\n        onMouseEnter: true,\n        onTouchStart: true,\n        legacyBehavior: true,\n      } as const\n      const optionalProps: LinkPropsOptional[] = Object.keys(\n        optionalPropsGuard\n      ) as LinkPropsOptional[]\n      optionalProps.forEach((key: LinkPropsOptional) => {\n        const valType = typeof props[key]\n\n        if (key === 'as') {\n          if (props[key] && valType !== 'string' && valType !== 'object') {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'onClick' ||\n          key === 'onMouseEnter' ||\n          key === 'onTouchStart'\n        ) {\n          if (props[key] && valType !== 'function') {\n            throw createPropError({\n              key,\n              expected: '`function`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'replace' ||\n          key === 'scroll' ||\n          key === 'shallow' ||\n          key === 'passHref' ||\n          key === 'prefetch' ||\n          key === 'legacyBehavior'\n        ) {\n          if (props[key] != null && valType !== 'boolean') {\n            throw createPropError({\n              key,\n              expected: '`boolean`',\n              actual: valType,\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (props.locale) {\n        warnOnce(\n          'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n        )\n      }\n      if (!asProp) {\n        let href: string | undefined\n        if (typeof hrefProp === 'string') {\n          href = hrefProp\n        } else if (\n          typeof hrefProp === 'object' &&\n          typeof hrefProp.pathname === 'string'\n        ) {\n          href = hrefProp.pathname\n        }\n\n        if (href) {\n          const hasDynamicSegment = href\n            .split('/')\n            .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n          if (hasDynamicSegment) {\n            throw new Error(\n              `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n            )\n          }\n        }\n      }\n    }\n\n    const { href, as } = React.useMemo(() => {\n      const resolvedHref = formatStringOrUrl(hrefProp)\n      return {\n        href: resolvedHref,\n        as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n      }\n    }, [hrefProp, asProp])\n\n    // This will return the first child, if multiple are provided it will throw an error\n    let child: any\n    if (legacyBehavior) {\n      if (process.env.NODE_ENV === 'development') {\n        if (onClick) {\n          console.warn(\n            `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n          )\n        }\n        if (onMouseEnterProp) {\n          console.warn(\n            `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n          )\n        }\n        try {\n          child = React.Children.only(children)\n        } catch (err) {\n          if (!children) {\n            throw new Error(\n              `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n            )\n          }\n          throw new Error(\n            `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n              (typeof window !== 'undefined'\n                ? \" \\nOpen your browser's console to view the Component stack trace.\"\n                : '')\n          )\n        }\n      } else {\n        child = React.Children.only(children)\n      }\n    } else {\n      if (process.env.NODE_ENV === 'development') {\n        if ((children as any)?.type === 'a') {\n          throw new Error(\n            'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n          )\n        }\n      }\n    }\n\n    const childRef: any = legacyBehavior\n      ? child && typeof child === 'object' && child.ref\n      : forwardedRef\n\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = React.useCallback(\n      (element: HTMLAnchorElement | SVGAElement) => {\n        if (prefetchEnabled && router !== null) {\n          mountLinkInstance(element, href, router, appPrefetchKind)\n        }\n        return () => {\n          unmountLinkInstance(element)\n        }\n      },\n      [prefetchEnabled, href, router, appPrefetchKind]\n    )\n\n    const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n    const childProps: {\n      onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n      onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n      onClick: React.MouseEventHandler<HTMLAnchorElement>\n      href?: string\n      ref?: any\n    } = {\n      ref: mergedRef,\n      onClick(e) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (!e) {\n            throw new Error(\n              `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n            )\n          }\n        }\n\n        if (!legacyBehavior && typeof onClick === 'function') {\n          onClick(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onClick === 'function'\n        ) {\n          child.props.onClick(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (e.defaultPrevented) {\n          return\n        }\n\n        linkClicked(e, router, href, as, replace, shallow, scroll)\n      },\n      onMouseEnter(e) {\n        if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n          onMouseEnterProp(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onMouseEnter === 'function'\n        ) {\n          child.props.onMouseEnter(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n          return\n        }\n\n        onNavigationIntent(e.currentTarget as HTMLAnchorElement | SVGAElement)\n      },\n      onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n        ? undefined\n        : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n              onTouchStartProp(e)\n            }\n\n            if (\n              legacyBehavior &&\n              child.props &&\n              typeof child.props.onTouchStart === 'function'\n            ) {\n              child.props.onTouchStart(e)\n            }\n\n            if (!router) {\n              return\n            }\n\n            if (!prefetchEnabled) {\n              return\n            }\n\n            onNavigationIntent(\n              e.currentTarget as HTMLAnchorElement | SVGAElement\n            )\n          },\n    }\n\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if (isAbsoluteUrl(as)) {\n      childProps.href = as\n    } else if (\n      !legacyBehavior ||\n      passHref ||\n      (child.type === 'a' && !('href' in child.props))\n    ) {\n      childProps.href = addBasePath(as)\n    }\n\n    return legacyBehavior ? (\n      React.cloneElement(child, childProps)\n    ) : (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n)\n\nexport default Link\n"], "names": ["React", "formatUrl", "AppRouterContext", "PrefetchKind", "useMergedRef", "isAbsoluteUrl", "addBasePath", "warnOnce", "mountLinkInstance", "onNavigationIntent", "unmountLinkInstance", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "router", "href", "as", "replace", "shallow", "scroll", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "startTransition", "formatStringOrUrl", "urlObjOrString", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "useContext", "prefetchEnabled", "appPrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "ref", "observeLinkVisibilityOnMount", "useCallback", "element", "mergedRef", "childProps", "defaultPrevented", "__NEXT_LINK_NO_TOUCH_START", "undefined", "cloneElement"], "mappings": "AAAA;;AAIA,OAAOA,WAAW,QAAO;AAEzB,SAASC,SAAS,QAAQ,2CAA0C;AACpE,SAASC,gBAAgB,QAAQ,qDAAoD;AAErF,SAASC,YAAY,QAAQ,oDAAmD;AAChF,SAASC,YAAY,QAAQ,oBAAmB;AAChD,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,QAAQ,QAAQ,mCAAkC;AAC3D,SACEC,iBAAiB,EACjBC,kBAAkB,EAClBC,mBAAmB,QACd,sBAAqB;AA4L5B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACE,AAACD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVC,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB;IAEhB,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IAAID,oBAAoBrB,gBAAgBa,IAAI;QAC1C,8CAA8C;QAC9C;IACF;IAEAA,EAAEU,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeN,iBAAAA,SAAU;QAC/B,IAAI,oBAAoBL,QAAQ;YAC9BA,MAAM,CAACG,UAAU,YAAY,OAAO,CAACF,MAAMC,IAAI;gBAC7CE;gBACAC,QAAQM;YACV;QACF,OAAO;YACLX,MAAM,CAACG,UAAU,YAAY,OAAO,CAACD,MAAMD,MAAM;gBAC/CI,QAAQM;YACV;QACF;IACF;IAEApC,MAAMqC,eAAe,CAACF;AACxB;AAOA,SAASG,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOtC,UAAUsC;AACnB;AAEA;;;;;;;;;CASC,GACD,MAAMC,qBAAOxC,MAAMyC,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJnB,MAAMoB,QAAQ,EACdnB,IAAIoB,MAAM,EACVF,UAAUG,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACRvB,OAAO,EACPC,OAAO,EACPC,MAAM,EACNsB,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGf;IAEJE,WAAWG;IAEX,IACES,kBACC,CAAA,OAAOZ,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,KAACc;sBAAGd;;IACjB;IAEA,MAAMpB,SAASzB,MAAM4D,UAAU,CAAC1D;IAEhC,MAAM2D,kBAAkBX,iBAAiB;IACzC;;;;;KAKC,GACD,MAAMY,kBACJZ,iBAAiB,OAAO/C,aAAa4D,IAAI,GAAG5D,aAAa6D,IAAI;IAE/D,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAASC,gBAAgBC,IAIxB;YACC,OAAO,qBAKN,CALM,IAAIC,MACT,AAAC,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DjD,MAAM;QACR;QACA,MAAMkD,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACE5B,KAAK,CAAC4B,IAAI,IAAI,QACb,OAAO5B,KAAK,CAAC4B,IAAI,KAAK,YAAY,OAAO5B,KAAK,CAAC4B,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQ9B,KAAK,CAAC4B,IAAI,KAAK,OAAO,SAAS,OAAO5B,KAAK,CAAC4B,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DtD,IAAI;YACJC,SAAS;YACTE,QAAQ;YACRD,SAAS;YACTsB,UAAU;YACVF,UAAU;YACVG,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAMyB,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOxC,KAAK,CAAC4B,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAI5B,KAAK,CAAC4B,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAI5B,KAAK,CAAC4B,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAI5B,KAAK,CAAC4B,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAIxB,MAAMyC,MAAM,EAAE;YAChB7E,SACE;QAEJ;QACA,IAAI,CAACwC,QAAQ;YACX,IAAIrB;YACJ,IAAI,OAAOoB,aAAa,UAAU;gBAChCpB,OAAOoB;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAASuC,QAAQ,KAAK,UAC7B;gBACA3D,OAAOoB,SAASuC,QAAQ;YAC1B;YAEA,IAAI3D,MAAM;gBACR,MAAM4D,oBAAoB5D,KACvB6D,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,qBAEL,CAFK,IAAIhB,MACR,AAAC,mBAAiB5C,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAG3B,MAAM4F,OAAO,CAAC;QACjC,MAAMC,eAAevD,kBAAkBQ;QACvC,OAAO;YACLpB,MAAMmE;YACNlE,IAAIoB,SAAST,kBAAkBS,UAAU8C;QAC3C;IACF,GAAG;QAAC/C;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAI+C;IACJ,IAAIrC,gBAAgB;QAClB,IAAIQ,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAIf,SAAS;gBACX2C,QAAQC,IAAI,CACV,AAAC,oDAAoDlD,WAAS;YAElE;YACA,IAAIQ,kBAAkB;gBACpByC,QAAQC,IAAI,CACV,AAAC,yDAAyDlD,WAAS;YAEvE;YACA,IAAI;gBACFgD,QAAQ9F,MAAMiG,QAAQ,CAACC,IAAI,CAACrD;YAC9B,EAAE,OAAOsD,KAAK;gBACZ,IAAI,CAACtD,UAAU;oBACb,MAAM,qBAEL,CAFK,IAAIyB,MACR,AAAC,uDAAuDxB,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,qBAKL,CALK,IAAIwB,MACR,AAAC,6DAA6DxB,WAAS,8FACpE,CAAA,OAAO4B,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;YACLoB,QAAQ9F,MAAMiG,QAAQ,CAACC,IAAI,CAACrD;QAC9B;IACF,OAAO;QACL,IAAIoB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAACtB,4BAAD,AAACA,SAAkBuD,IAAI,MAAK,KAAK;gBACnC,MAAM,qBAEL,CAFK,IAAI9B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAM+B,WAAgB5C,iBAClBqC,SAAS,OAAOA,UAAU,YAAYA,MAAMQ,GAAG,GAC/C1D;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAM2D,+BAA+BvG,MAAMwG,WAAW,CACpD,CAACC;QACC,IAAI5C,mBAAmBpC,WAAW,MAAM;YACtCjB,kBAAkBiG,SAAS/E,MAAMD,QAAQqC;QAC3C;QACA,OAAO;YACLpD,oBAAoB+F;QACtB;IACF,GACA;QAAC5C;QAAiBnC;QAAMD;QAAQqC;KAAgB;IAGlD,MAAM4C,YAAYtG,aAAamG,8BAA8BF;IAE7D,MAAMM,aAMF;QACFL,KAAKI;QACLtD,SAAQ5B,CAAC;YACP,IAAIyC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAAC3C,GAAG;oBACN,MAAM,qBAEL,CAFK,IAAI8C,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACb,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQ5B;YACV;YAEA,IACEiC,kBACAqC,MAAMnD,KAAK,IACX,OAAOmD,MAAMnD,KAAK,CAACS,OAAO,KAAK,YAC/B;gBACA0C,MAAMnD,KAAK,CAACS,OAAO,CAAC5B;YACtB;YAEA,IAAI,CAACC,QAAQ;gBACX;YACF;YAEA,IAAID,EAAEoF,gBAAgB,EAAE;gBACtB;YACF;YAEArF,YAAYC,GAAGC,QAAQC,MAAMC,IAAIC,SAASC,SAASC;QACrD;QACAuB,cAAa7B,CAAC;YACZ,IAAI,CAACiC,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB9B;YACnB;YAEA,IACEiC,kBACAqC,MAAMnD,KAAK,IACX,OAAOmD,MAAMnD,KAAK,CAACU,YAAY,KAAK,YACpC;gBACAyC,MAAMnD,KAAK,CAACU,YAAY,CAAC7B;YAC3B;YAEA,IAAI,CAACC,QAAQ;gBACX;YACF;YAEA,IAAI,CAACoC,mBAAmBI,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;gBAC9D;YACF;YAEA1D,mBAAmBe,EAAEV,aAAa;QACpC;QACAyC,cAAcU,QAAQC,GAAG,CAAC2C,0BAA0B,GAChDC,YACA,SAASvD,aAAa/B,CAAC;YACrB,IAAI,CAACiC,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBhC;YACnB;YAEA,IACEiC,kBACAqC,MAAMnD,KAAK,IACX,OAAOmD,MAAMnD,KAAK,CAACY,YAAY,KAAK,YACpC;gBACAuC,MAAMnD,KAAK,CAACY,YAAY,CAAC/B;YAC3B;YAEA,IAAI,CAACC,QAAQ;gBACX;YACF;YAEA,IAAI,CAACoC,iBAAiB;gBACpB;YACF;YAEApD,mBACEe,EAAEV,aAAa;QAEnB;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAIT,cAAcsB,KAAK;QACrBgF,WAAWjF,IAAI,GAAGC;IACpB,OAAO,IACL,CAAC8B,kBACDN,YACC2C,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAMnD,KAAK,AAAD,GAC7C;QACAgE,WAAWjF,IAAI,GAAGpB,YAAYqB;IAChC;IAEA,OAAO8B,+BACLzD,MAAM+G,YAAY,CAACjB,OAAOa,4BAE1B,KAAChD;QAAG,GAAGD,SAAS;QAAG,GAAGiD,UAAU;kBAC7B9D;;AAGP;AAGF,eAAeL,KAAI"}