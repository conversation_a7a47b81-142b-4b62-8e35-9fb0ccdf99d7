{"version": 3, "sources": ["../../../src/client/tracing/tracer.ts"], "sourcesContent": ["import mitt from '../../shared/lib/mitt'\nimport type { MittEmitter } from '../../shared/lib/mitt'\n\nexport type SpanOptions = {\n  startTime?: number\n  attributes?: Record<string, unknown>\n}\n\nexport type SpanState =\n  | {\n      state: 'inprogress'\n    }\n  | {\n      state: 'ended'\n      endTime: number\n    }\n\ninterface ISpan {\n  name: string\n  startTime: number\n  attributes: Record<string, unknown>\n  state: SpanState\n  end(endTime?: number): void\n}\n\nclass Span implements ISpan {\n  name: string\n  startTime: number\n  onSpanEnd: (span: Span) => void\n  state: SpanState\n  attributes: Record<string, unknown>\n\n  constructor(\n    name: string,\n    options: SpanOptions,\n    onSpanEnd: (span: Span) => void\n  ) {\n    this.name = name\n    this.attributes = options.attributes ?? {}\n    this.startTime = options.startTime ?? Date.now()\n    this.onSpanEnd = onSpanEnd\n    this.state = { state: 'inprogress' }\n  }\n\n  end(endTime?: number) {\n    if (this.state.state === 'ended') {\n      throw new Error('Span has already ended')\n    }\n\n    this.state = {\n      state: 'ended',\n      endTime: endTime ?? Date.now(),\n    }\n\n    this.onSpanEnd(this)\n  }\n}\n\nclass Tracer {\n  _emitter: MittEmitter<string> = mitt()\n\n  private handleSpanEnd = (span: Span) => {\n    this._emitter.emit('spanend', span)\n  }\n\n  startSpan(name: string, options: SpanOptions) {\n    return new Span(name, options, this.handleSpanEnd)\n  }\n\n  onSpanEnd(cb: (span: ISpan) => void): () => void {\n    this._emitter.on('spanend', cb)\n    return () => {\n      this._emitter.off('spanend', cb)\n    }\n  }\n}\n\nexport type { ISpan as Span }\nexport default new Tracer()\n"], "names": ["mitt", "Span", "end", "endTime", "state", "Error", "Date", "now", "onSpanEnd", "constructor", "name", "options", "attributes", "startTime", "Tracer", "startSpan", "handleSpanEnd", "cb", "_emitter", "on", "off", "span", "emit"], "mappings": "AAAA,OAAOA,UAAU,wBAAuB;AAyBxC,MAAMC;IAmBJC,IAAIC,OAAgB,EAAE;QACpB,IAAI,IAAI,CAACC,KAAK,CAACA,KAAK,KAAK,SAAS;YAChC,MAAM,qBAAmC,CAAnC,IAAIC,MAAM,2BAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAkC;QAC1C;QAEA,IAAI,CAACD,KAAK,GAAG;YACXA,OAAO;YACPD,SAASA,kBAAAA,UAAWG,KAAKC,GAAG;QAC9B;QAEA,IAAI,CAACC,SAAS,CAAC,IAAI;IACrB;IAvBAC,YACEC,IAAY,EACZC,OAAoB,EACpBH,SAA+B,CAC/B;QACA,IAAI,CAACE,IAAI,GAAGA;YACMC;QAAlB,IAAI,CAACC,UAAU,GAAGD,CAAAA,sBAAAA,QAAQC,UAAU,YAAlBD,sBAAsB,CAAC;YACxBA;QAAjB,IAAI,CAACE,SAAS,GAAGF,CAAAA,qBAAAA,QAAQE,SAAS,YAAjBF,qBAAqBL,KAAKC,GAAG;QAC9C,IAAI,CAACC,SAAS,GAAGA;QACjB,IAAI,CAACJ,KAAK,GAAG;YAAEA,OAAO;QAAa;IACrC;AAcF;AAEA,MAAMU;IAOJC,UAAUL,IAAY,EAAEC,OAAoB,EAAE;QAC5C,OAAO,IAAIV,KAAKS,MAAMC,SAAS,IAAI,CAACK,aAAa;IACnD;IAEAR,UAAUS,EAAyB,EAAc;QAC/C,IAAI,CAACC,QAAQ,CAACC,EAAE,CAAC,WAAWF;QAC5B,OAAO;YACL,IAAI,CAACC,QAAQ,CAACE,GAAG,CAAC,WAAWH;QAC/B;IACF;;aAfAC,WAAgClB;aAExBgB,gBAAgB,CAACK;YACvB,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC,WAAWD;QAChC;;AAYF;AAGA,eAAe,IAAIP,SAAQ"}