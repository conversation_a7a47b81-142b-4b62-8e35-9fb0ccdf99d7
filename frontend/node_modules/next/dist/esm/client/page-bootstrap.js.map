{"version": 3, "sources": ["../../src/client/page-bootstrap.ts"], "sourcesContent": ["import { hydrate, router } from './'\nimport initOnDemandEntries from './dev/on-demand-entries-client'\nimport { devBuildIndicator } from './dev/dev-build-indicator/internal/dev-build-indicator'\nimport { displayContent } from './dev/fouc'\nimport {\n  connectHMR,\n  addMessageListener,\n} from './components/react-dev-overlay/pages/websocket'\nimport {\n  assign,\n  urlQueryToSearchParams,\n} from '../shared/lib/router/utils/querystring'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../server/dev/hot-reloader-types'\nimport { RuntimeErrorHandler } from './components/errors/runtime-error-handler'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from './components/react-dev-overlay/shared'\nimport { performFullReload } from './components/react-dev-overlay/pages/hot-reloader-client'\nimport { initializeDevBuildIndicatorForPageRouter } from './dev/dev-build-indicator/initialize-for-page-router'\n\nexport function pageBootstrap(assetPrefix: string) {\n  connectHMR({ assetPrefix, path: '/_next/webpack-hmr' })\n\n  return hydrate({ beforeRender: displayContent }).then(() => {\n    initOnDemandEntries()\n\n    initializeDevBuildIndicatorForPageRouter()\n\n    let reloading = false\n\n    addMessageListener((payload) => {\n      if (reloading) return\n      if ('action' in payload) {\n        switch (payload.action) {\n          case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n            const { stack, message } = JSON.parse(payload.errorJSON)\n            const error = new Error(message)\n            error.stack = stack\n            throw error\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n            reloading = true\n            window.location.reload()\n            break\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n            fetch(\n              `${assetPrefix}/_next/static/development/_devPagesManifest.json`\n            )\n              .then((res) => res.json())\n              .then((manifest) => {\n                window.__DEV_PAGES_MANIFEST = manifest\n              })\n              .catch((err) => {\n                console.log(`Failed to fetch devPagesManifest`, err)\n              })\n            break\n          }\n          default:\n            break\n        }\n      } else if ('event' in payload) {\n        switch (payload.event) {\n          case HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES: {\n            return window.location.reload()\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES: {\n            // This is used in `../server/dev/turbopack-utils.ts`.\n            const isOnErrorPage = window.next.router.pathname === '/_error'\n            // On the error page we want to reload the page when a page was changed\n            if (isOnErrorPage) {\n              if (RuntimeErrorHandler.hadRuntimeError) {\n                console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n              }\n              reloading = true\n              performFullReload(null)\n            }\n            break\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES: {\n            if (RuntimeErrorHandler.hadRuntimeError) {\n              console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n              performFullReload(null)\n            }\n\n            const { pages } = payload\n\n            // Make sure to reload when the dev-overlay is showing for an\n            // API route\n            // TODO: Fix `__NEXT_PAGE` type\n            if (pages.includes(router.query.__NEXT_PAGE as string)) {\n              return window.location.reload()\n            }\n\n            if (!router.clc && pages.includes(router.pathname)) {\n              console.log('Refreshing page data due to server-side change')\n              devBuildIndicator.show()\n              const clearIndicator = () => devBuildIndicator.hide()\n\n              router\n                .replace(\n                  router.pathname +\n                    '?' +\n                    String(\n                      assign(\n                        urlQueryToSearchParams(router.query),\n                        new URLSearchParams(location.search)\n                      )\n                    ),\n                  router.asPath,\n                  { scroll: false }\n                )\n                .catch(() => {\n                  // trigger hard reload when failing to refresh data\n                  // to show error overlay properly\n                  location.reload()\n                })\n                .finally(clearIndicator)\n            }\n            break\n          }\n          default:\n            break\n        }\n      }\n    })\n  })\n}\n"], "names": ["hydrate", "router", "initOnDemandEntries", "devBuildIndicator", "displayContent", "connectHMR", "addMessageListener", "assign", "urlQueryToSearchParams", "HMR_ACTIONS_SENT_TO_BROWSER", "RuntimeError<PERSON>andler", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "performFullReload", "initializeDevBuildIndicatorForPageRouter", "pageBootstrap", "assetPrefix", "path", "beforeRender", "then", "reloading", "payload", "action", "SERVER_ERROR", "stack", "message", "JSON", "parse", "errorJSON", "error", "Error", "RELOAD_PAGE", "window", "location", "reload", "DEV_PAGES_MANIFEST_UPDATE", "fetch", "res", "json", "manifest", "__DEV_PAGES_MANIFEST", "catch", "err", "console", "log", "event", "MIDDLEWARE_CHANGES", "CLIENT_CHANGES", "isOnErrorPage", "next", "pathname", "hadRuntimeError", "warn", "SERVER_ONLY_CHANGES", "pages", "includes", "query", "__NEXT_PAGE", "clc", "show", "clearIndicator", "hide", "replace", "String", "URLSearchParams", "search", "<PERSON><PERSON><PERSON>", "scroll", "finally"], "mappings": "AAAA,SAASA,OAAO,EAAEC,MAAM,QAAQ,KAAI;AACpC,OAAOC,yBAAyB,iCAAgC;AAChE,SAASC,iBAAiB,QAAQ,yDAAwD;AAC1F,SAASC,cAAc,QAAQ,aAAY;AAC3C,SACEC,UAAU,EACVC,kBAAkB,QACb,iDAAgD;AACvD,SACEC,MAAM,EACNC,sBAAsB,QACjB,yCAAwC;AAC/C,SAASC,2BAA2B,QAAQ,mCAAkC;AAC9E,SAASC,mBAAmB,QAAQ,4CAA2C;AAC/E,SAASC,oCAAoC,QAAQ,wCAAuC;AAC5F,SAASC,iBAAiB,QAAQ,2DAA0D;AAC5F,SAASC,wCAAwC,QAAQ,uDAAsD;AAE/G,OAAO,SAASC,cAAcC,WAAmB;IAC/CV,WAAW;QAAEU;QAAaC,MAAM;IAAqB;IAErD,OAAOhB,QAAQ;QAAEiB,cAAcb;IAAe,GAAGc,IAAI,CAAC;QACpDhB;QAEAW;QAEA,IAAIM,YAAY;QAEhBb,mBAAmB,CAACc;YAClB,IAAID,WAAW;YACf,IAAI,YAAYC,SAAS;gBACvB,OAAQA,QAAQC,MAAM;oBACpB,KAAKZ,4BAA4Ba,YAAY;wBAAE;4BAC7C,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAE,GAAGC,KAAKC,KAAK,CAACN,QAAQO,SAAS;4BACvD,MAAMC,QAAQ,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;uCAAA;4CAAA;8CAAA;4BAAiB;4BAC/BI,MAAML,KAAK,GAAGA;4BACd,MAAMK;wBACR;oBACA,KAAKnB,4BAA4BqB,WAAW;wBAAE;4BAC5CX,YAAY;4BACZY,OAAOC,QAAQ,CAACC,MAAM;4BACtB;wBACF;oBACA,KAAKxB,4BAA4ByB,yBAAyB;wBAAE;4BAC1DC,MACE,AAAC,KAAEpB,cAAY,oDAEdG,IAAI,CAAC,CAACkB,MAAQA,IAAIC,IAAI,IACtBnB,IAAI,CAAC,CAACoB;gCACLP,OAAOQ,oBAAoB,GAAGD;4BAChC,GACCE,KAAK,CAAC,CAACC;gCACNC,QAAQC,GAAG,CAAE,oCAAmCF;4BAClD;4BACF;wBACF;oBACA;wBACE;gBACJ;YACF,OAAO,IAAI,WAAWrB,SAAS;gBAC7B,OAAQA,QAAQwB,KAAK;oBACnB,KAAKnC,4BAA4BoC,kBAAkB;wBAAE;4BACnD,OAAOd,OAAOC,QAAQ,CAACC,MAAM;wBAC/B;oBACA,KAAKxB,4BAA4BqC,cAAc;wBAAE;4BAC/C,sDAAsD;4BACtD,MAAMC,gBAAgBhB,OAAOiB,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,KAAK;4BACtD,uEAAuE;4BACvE,IAAIF,eAAe;gCACjB,IAAIrC,oBAAoBwC,eAAe,EAAE;oCACvCR,QAAQS,IAAI,CAACxC;gCACf;gCACAQ,YAAY;gCACZP,kBAAkB;4BACpB;4BACA;wBACF;oBACA,KAAKH,4BAA4B2C,mBAAmB;wBAAE;4BACpD,IAAI1C,oBAAoBwC,eAAe,EAAE;gCACvCR,QAAQS,IAAI,CAACxC;gCACbC,kBAAkB;4BACpB;4BAEA,MAAM,EAAEyC,KAAK,EAAE,GAAGjC;4BAElB,6DAA6D;4BAC7D,YAAY;4BACZ,+BAA+B;4BAC/B,IAAIiC,MAAMC,QAAQ,CAACrD,OAAOsD,KAAK,CAACC,WAAW,GAAa;gCACtD,OAAOzB,OAAOC,QAAQ,CAACC,MAAM;4BAC/B;4BAEA,IAAI,CAAChC,OAAOwD,GAAG,IAAIJ,MAAMC,QAAQ,CAACrD,OAAOgD,QAAQ,GAAG;gCAClDP,QAAQC,GAAG,CAAC;gCACZxC,kBAAkBuD,IAAI;gCACtB,MAAMC,iBAAiB,IAAMxD,kBAAkByD,IAAI;gCAEnD3D,OACG4D,OAAO,CACN5D,OAAOgD,QAAQ,GACb,MACAa,OACEvD,OACEC,uBAAuBP,OAAOsD,KAAK,GACnC,IAAIQ,gBAAgB/B,SAASgC,MAAM,KAGzC/D,OAAOgE,MAAM,EACb;oCAAEC,QAAQ;gCAAM,GAEjB1B,KAAK,CAAC;oCACL,mDAAmD;oCACnD,iCAAiC;oCACjCR,SAASC,MAAM;gCACjB,GACCkC,OAAO,CAACR;4BACb;4BACA;wBACF;oBACA;wBACE;gBACJ;YACF;QACF;IACF;AACF"}