{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/icons/external.tsx"], "sourcesContent": ["export function ExternalIcon(props: React.SVGProps<SVGSVGElement>) {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        fill=\"currentColor\"\n        d=\"M11.5 9.75V11.25C11.5 11.3881 11.3881 11.5 11.25 11.5H4.75C4.61193 11.5 4.5 11.3881 4.5 11.25L4.5 4.75C4.5 4.61193 4.61193 4.5 4.75 4.5H6.25H7V3H6.25H4.75C3.7835 3 3 3.7835 3 4.75V11.25C3 12.2165 3.7835 13 4.75 13H11.25C12.2165 13 13 12.2165 13 11.25V9.75V9H11.5V9.75ZM8.5 3H9.25H12.2495C12.6637 3 12.9995 3.33579 12.9995 3.75V6.75V7.5H11.4995V6.75V5.56066L8.53033 8.52978L8 9.06011L6.93934 7.99945L7.46967 7.46912L10.4388 4.5H9.25H8.5V3Z\"\n      />\n    </svg>\n  )\n}\n\nexport function SourceMappingErrorIcon(props: React.SVGProps<SVGSVGElement>) {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      height=\"16\"\n      strokeLinejoin=\"round\"\n      viewBox=\"-4 -4 24 24\"\n      width=\"16\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M8.55846 2H7.44148L1.88975 13.5H14.1102L8.55846 2ZM9.90929 1.34788C9.65902 0.829456 9.13413 0.5 8.55846 0.5H7.44148C6.86581 0.5 6.34092 0.829454 6.09065 1.34787L0.192608 13.5653C-0.127943 14.2293 0.355835 15 1.09316 15H14.9068C15.6441 15 16.1279 14.2293 15.8073 13.5653L9.90929 1.34788ZM8.74997 4.75V5.5V8V8.75H7.24997V8V5.5V4.75H8.74997ZM7.99997 12C8.55226 12 8.99997 11.5523 8.99997 11C8.99997 10.4477 8.55226 10 7.99997 10C7.44769 10 6.99997 10.4477 6.99997 11C6.99997 11.5523 7.44769 12 7.99997 12Z\"\n        fill=\"currentColor\"\n      ></path>\n    </svg>\n  )\n}\n"], "names": ["ExternalIcon", "props", "svg", "xmlns", "width", "height", "viewBox", "fill", "path", "fillRule", "clipRule", "d", "SourceMappingErrorIcon", "strokeLinejoin"], "mappings": ";AAAA,OAAO,SAASA,aAAaC,KAAoC;IAC/D,qBACE,KAACC;QACCC,OAAM;QACNC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACJ,GAAGN,KAAK;kBAET,cAAA,KAACO;YACCC,UAAS;YACTC,UAAS;YACTH,MAAK;YACLI,GAAE;;;AAIV;AAEA,OAAO,SAASC,uBAAuBX,KAAoC;IACzE,qBACE,KAACC;QACCC,OAAM;QACNE,QAAO;QACPQ,gBAAe;QACfP,SAAQ;QACRF,OAAM;QACL,GAAGH,KAAK;kBAET,cAAA,KAACO;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFJ,MAAK;;;AAIb"}