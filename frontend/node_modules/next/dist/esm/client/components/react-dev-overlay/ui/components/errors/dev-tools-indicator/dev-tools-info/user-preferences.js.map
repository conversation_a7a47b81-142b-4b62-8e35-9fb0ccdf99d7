{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.tsx"], "sourcesContent": ["import { useState, type HTMLProps } from 'react'\nimport { css } from '../../../../../utils/css'\nimport EyeIcon from '../../../../icons/eye-icon'\nimport { STORAGE_KEY_POSITION, STORAGE_KEY_THEME } from '../../../../../shared'\nimport LightIcon from '../../../../icons/light-icon'\nimport DarkIcon from '../../../../icons/dark-icon'\nimport SystemIcon from '../../../../icons/system-icon'\nimport type { DevToolsInfoPropsCore } from './dev-tools-info'\nimport { DevToolsInfo } from './dev-tools-info'\nimport type { DevToolsIndicatorPosition } from '../dev-tools-indicator'\n\nfunction getInitialPreference() {\n  if (typeof localStorage === 'undefined') {\n    return 'system'\n  }\n\n  const theme = localStorage.getItem(STORAGE_KEY_THEME)\n  return theme === 'dark' || theme === 'light' ? theme : 'system'\n}\n\nexport function UserPreferences({\n  setPosition,\n  position,\n  hide,\n  ...props\n}: {\n  setPosition: (position: DevToolsIndicatorPosition) => void\n  position: DevToolsIndicatorPosition\n  hide: () => void\n} & DevToolsInfoPropsCore &\n  HTMLProps<HTMLDivElement>) {\n  // derive initial theme from system preference\n  const [theme, setTheme] = useState(getInitialPreference())\n\n  const handleThemeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const portal = document.querySelector('nextjs-portal')\n    if (!portal) return\n\n    setTheme(e.target.value)\n\n    if (e.target.value === 'system') {\n      portal.classList.remove('dark')\n      portal.classList.remove('light')\n      localStorage.removeItem(STORAGE_KEY_THEME)\n      return\n    }\n\n    if (e.target.value === 'dark') {\n      portal.classList.add('dark')\n      portal.classList.remove('light')\n      localStorage.setItem(STORAGE_KEY_THEME, 'dark')\n    } else {\n      portal.classList.remove('dark')\n      portal.classList.add('light')\n      localStorage.setItem(STORAGE_KEY_THEME, 'light')\n    }\n  }\n\n  function handlePositionChange(e: React.ChangeEvent<HTMLSelectElement>) {\n    setPosition(e.target.value as DevToolsIndicatorPosition)\n    localStorage.setItem(STORAGE_KEY_POSITION, e.target.value)\n  }\n\n  return (\n    <DevToolsInfo title=\"Preferences\" {...props}>\n      <div className=\"preferences-container\">\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"theme\">Theme</label>\n            <p className=\"preference-description\">\n              Select your theme preference.\n            </p>\n          </div>\n          <div className=\"preference-control-select\">\n            <div className=\"preference-icon\">\n              <ThemeIcon theme={theme as 'dark' | 'light' | 'system'} />\n            </div>\n            <select\n              id=\"theme\"\n              name=\"theme\"\n              className=\"select-button\"\n              value={theme}\n              onChange={handleThemeChange}\n            >\n              <option value=\"system\">System</option>\n              <option value=\"light\">Light</option>\n              <option value=\"dark\">Dark</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"position\">Position</label>\n            <p className=\"preference-description\">\n              Adjust the placement of your dev tools.\n            </p>\n          </div>\n          <div className=\"preference-control-select\">\n            <select\n              id=\"position\"\n              name=\"position\"\n              className=\"select-button\"\n              value={position}\n              onChange={handlePositionChange}\n            >\n              <option value=\"bottom-left\">Bottom Left</option>\n              <option value=\"bottom-right\">Bottom Right</option>\n              <option value=\"top-left\">Top Left</option>\n              <option value=\"top-right\">Top Right</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"hide-dev-tools\">\n              Hide Dev Tools for this session\n            </label>\n            <p className=\"preference-description\">\n              Hide Dev Tools until you restart your dev server, or 1 day.\n            </p>\n          </div>\n          <div className=\"preference-control\">\n            <button\n              id=\"hide-dev-tools\"\n              name=\"hide-dev-tools\"\n              data-hide-dev-tools\n              className=\"action-button\"\n              onClick={hide}\n            >\n              <div className=\"preference-icon\">\n                <EyeIcon />\n              </div>\n              <span>Hide</span>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label>Disable Dev Tools for this project</label>\n            <p className=\"preference-description\">\n              To disable this UI completely, set{' '}\n              <code className=\"dev-tools-info-code\">devIndicators: false</code>{' '}\n              in your <code className=\"dev-tools-info-code\">next.config</code>{' '}\n              file.\n            </p>\n          </div>\n        </div>\n      </div>\n    </DevToolsInfo>\n  )\n}\n\nfunction ThemeIcon({ theme }: { theme: 'dark' | 'light' | 'system' }) {\n  switch (theme) {\n    case 'system':\n      return <SystemIcon />\n    case 'dark':\n      return <DarkIcon />\n    case 'light':\n      return <LightIcon />\n    default:\n      return null\n  }\n}\n\nexport const DEV_TOOLS_INFO_USER_PREFERENCES_STYLES = css`\n  .preferences-container {\n    padding: 8px 6px;\n    width: 100%;\n  }\n\n  @media (min-width: 576px) {\n    .preferences-container {\n      width: 480px;\n    }\n  }\n\n  .preference-section:first-child {\n    padding-top: 0;\n  }\n\n  .preference-section {\n    padding: 12px 0;\n    border-bottom: 1px solid var(--color-gray-400);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 24px;\n  }\n\n  .preference-section:last-child {\n    border-bottom: none;\n  }\n\n  .preference-header {\n    margin-bottom: 0;\n    flex: 1;\n  }\n\n  .preference-header label {\n    font-size: var(--size-14);\n    font-weight: 500;\n    color: var(--color-gray-1000);\n    margin: 0;\n  }\n\n  .preference-description {\n    color: var(--color-gray-900);\n    font-size: var(--size-14);\n    margin: 0;\n  }\n\n  .preference-icon {\n    display: flex;\n    align-items: center;\n    width: 16px;\n    height: 16px;\n  }\n\n  .select-button,\n  .action-button {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-400);\n    border-radius: var(--rounded-lg);\n    font-weight: 400;\n    font-size: var(--size-14);\n    color: var(--color-gray-1000);\n    padding: 6px 8px;\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n  }\n\n  .preference-control-select {\n    padding: 6px 8px;\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    border-radius: var(--rounded-lg);\n    border: 1px solid var(--color-gray-400);\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n\n    &:focus-within {\n      outline: var(--focus-ring);\n    }\n  }\n\n  .preference-control-select select {\n    font-size: var(--size-14);\n    font-weight: 400;\n    border: none;\n    padding: 0 6px 0 0;\n    border-radius: 0;\n    outline: none;\n    background: none;\n  }\n\n  :global(.icon) {\n    width: 18px;\n    height: 18px;\n    color: #666;\n  }\n`\n"], "names": ["useState", "css", "EyeIcon", "STORAGE_KEY_POSITION", "STORAGE_KEY_THEME", "LightIcon", "DarkIcon", "SystemIcon", "DevToolsInfo", "getInitialPreference", "localStorage", "theme", "getItem", "UserPreferences", "setPosition", "position", "hide", "props", "setTheme", "handleThemeChange", "e", "portal", "document", "querySelector", "target", "value", "classList", "remove", "removeItem", "add", "setItem", "handlePositionChange", "title", "div", "className", "label", "htmlFor", "p", "ThemeIcon", "select", "id", "name", "onChange", "option", "button", "data-hide-dev-tools", "onClick", "span", "code", "DEV_TOOLS_INFO_USER_PREFERENCES_STYLES"], "mappings": ";;;;;;;;;;;AAAA,SAASA,QAAQ,QAAwB,QAAO;AAChD,SAASC,GAAG,QAAQ,2BAA0B;AAC9C,OAAOC,aAAa,6BAA4B;AAChD,SAASC,oBAAoB,EAAEC,iBAAiB,QAAQ,wBAAuB;AAC/E,OAAOC,eAAe,+BAA8B;AACpD,OAAOC,cAAc,8BAA6B;AAClD,OAAOC,gBAAgB,gCAA+B;AAEtD,SAASC,YAAY,QAAQ,mBAAkB;AAG/C,SAASC;IACP,IAAI,OAAOC,iBAAiB,aAAa;QACvC,OAAO;IACT;IAEA,MAAMC,QAAQD,aAAaE,OAAO,CAACR;IACnC,OAAOO,UAAU,UAAUA,UAAU,UAAUA,QAAQ;AACzD;AAEA,OAAO,SAASE,gBAAgB,KAUL;IAVK,IAAA,EAC9BC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJ,GAAGC,OAMsB,GAVK;IAW9B,8CAA8C;IAC9C,MAAM,CAACN,OAAOO,SAAS,GAAGlB,SAASS;IAEnC,MAAMU,oBAAoB,CAACC;QACzB,MAAMC,SAASC,SAASC,aAAa,CAAC;QACtC,IAAI,CAACF,QAAQ;QAEbH,SAASE,EAAEI,MAAM,CAACC,KAAK;QAEvB,IAAIL,EAAEI,MAAM,CAACC,KAAK,KAAK,UAAU;YAC/BJ,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBN,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBjB,aAAakB,UAAU,CAACxB;YACxB;QACF;QAEA,IAAIgB,EAAEI,MAAM,CAACC,KAAK,KAAK,QAAQ;YAC7BJ,OAAOK,SAAS,CAACG,GAAG,CAAC;YACrBR,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBjB,aAAaoB,OAAO,CAAC1B,mBAAmB;QAC1C,OAAO;YACLiB,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBN,OAAOK,SAAS,CAACG,GAAG,CAAC;YACrBnB,aAAaoB,OAAO,CAAC1B,mBAAmB;QAC1C;IACF;IAEA,SAAS2B,qBAAqBX,CAAuC;QACnEN,YAAYM,EAAEI,MAAM,CAACC,KAAK;QAC1Bf,aAAaoB,OAAO,CAAC3B,sBAAsBiB,EAAEI,MAAM,CAACC,KAAK;IAC3D;IAEA,qBACE,KAACjB;QAAawB,OAAM;QAAe,GAAGf,KAAK;kBACzC,cAAA,MAACgB;YAAIC,WAAU;;8BACb,MAACD;oBAAIC,WAAU;;sCACb,MAACD;4BAAIC,WAAU;;8CACb,KAACC;oCAAMC,SAAQ;8CAAQ;;8CACvB,KAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,MAACD;4BAAIC,WAAU;;8CACb,KAACD;oCAAIC,WAAU;8CACb,cAAA,KAACI;wCAAU3B,OAAOA;;;8CAEpB,MAAC4B;oCACCC,IAAG;oCACHC,MAAK;oCACLP,WAAU;oCACVT,OAAOd;oCACP+B,UAAUvB;;sDAEV,KAACwB;4CAAOlB,OAAM;sDAAS;;sDACvB,KAACkB;4CAAOlB,OAAM;sDAAQ;;sDACtB,KAACkB;4CAAOlB,OAAM;sDAAO;;;;;;;;8BAK3B,MAACQ;oBAAIC,WAAU;;sCACb,MAACD;4BAAIC,WAAU;;8CACb,KAACC;oCAAMC,SAAQ;8CAAW;;8CAC1B,KAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,KAACD;4BAAIC,WAAU;sCACb,cAAA,MAACK;gCACCC,IAAG;gCACHC,MAAK;gCACLP,WAAU;gCACVT,OAAOV;gCACP2B,UAAUX;;kDAEV,KAACY;wCAAOlB,OAAM;kDAAc;;kDAC5B,KAACkB;wCAAOlB,OAAM;kDAAe;;kDAC7B,KAACkB;wCAAOlB,OAAM;kDAAW;;kDACzB,KAACkB;wCAAOlB,OAAM;kDAAY;;;;;;;8BAKhC,MAACQ;oBAAIC,WAAU;;sCACb,MAACD;4BAAIC,WAAU;;8CACb,KAACC;oCAAMC,SAAQ;8CAAiB;;8CAGhC,KAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,KAACD;4BAAIC,WAAU;sCACb,cAAA,MAACU;gCACCJ,IAAG;gCACHC,MAAK;gCACLI,qBAAmB;gCACnBX,WAAU;gCACVY,SAAS9B;;kDAET,KAACiB;wCAAIC,WAAU;kDACb,cAAA,KAAChC;;kDAEH,KAAC6C;kDAAK;;;;;;;8BAKZ,KAACd;oBAAIC,WAAU;8BACb,cAAA,MAACD;wBAAIC,WAAU;;0CACb,KAACC;0CAAM;;0CACP,MAACE;gCAAEH,WAAU;;oCAAyB;oCACD;kDACnC,KAACc;wCAAKd,WAAU;kDAAsB;;oCAA4B;oCAAI;kDAC9D,KAACc;wCAAKd,WAAU;kDAAsB;;oCAAmB;oCAAI;;;;;;;;;AAQnF;AAEA,SAASI,UAAU,KAAiD;IAAjD,IAAA,EAAE3B,KAAK,EAA0C,GAAjD;IACjB,OAAQA;QACN,KAAK;YACH,qBAAO,KAACJ;QACV,KAAK;YACH,qBAAO,KAACD;QACV,KAAK;YACH,qBAAO,KAACD;QACV;YACE,OAAO;IACX;AACF;AAEA,OAAO,MAAM4C,yCAAyChD,uBAwGrD"}