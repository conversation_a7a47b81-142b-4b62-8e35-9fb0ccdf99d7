{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.tsx"], "sourcesContent": ["import type { OriginalStackFrame } from '../../../../utils/stack-frame'\nimport { useMemo, useState, useRef } from 'react'\nimport { CallStackFrame } from '../../call-stack-frame/call-stack-frame'\n\ninterface CallStackProps {\n  frames: OriginalStackFrame[]\n  dialogResizerRef: React.RefObject<HTMLDivElement | null>\n}\n\nexport function CallStack({ frames, dialogResizerRef }: CallStackProps) {\n  const initialDialogHeight = useRef<number>(NaN)\n  const [isIgnoreListOpen, setIsIgnoreListOpen] = useState(false)\n\n  const ignoredFramesTally = useMemo(() => {\n    return frames.reduce((tally, frame) => tally + (frame.ignored ? 1 : 0), 0)\n  }, [frames])\n\n  function onToggleIgnoreList() {\n    const dialog = dialogResizerRef?.current as HTMLElement\n\n    if (!dialog) {\n      return\n    }\n\n    const { height: currentHeight } = dialog?.getBoundingClientRect()\n\n    if (!initialDialogHeight.current) {\n      initialDialogHeight.current = currentHeight\n    }\n\n    if (isIgnoreListOpen) {\n      function onTransitionEnd() {\n        dialog.removeEventListener('transitionend', onTransitionEnd)\n        setIsIgnoreListOpen(false)\n      }\n      dialog.style.height = `${initialDialogHeight.current}px`\n      dialog.addEventListener('transitionend', onTransitionEnd)\n    } else {\n      setIsIgnoreListOpen(true)\n    }\n  }\n\n  return (\n    <div className=\"error-overlay-call-stack-container\">\n      <div className=\"error-overlay-call-stack-header\">\n        <p className=\"error-overlay-call-stack-title\">\n          Call Stack{' '}\n          <span className=\"error-overlay-call-stack-count\">\n            {frames.length}\n          </span>\n        </p>\n        {ignoredFramesTally > 0 && (\n          <button\n            data-expand-ignore-button={isIgnoreListOpen}\n            className=\"error-overlay-call-stack-ignored-list-toggle-button\"\n            onClick={onToggleIgnoreList}\n          >\n            {`${isIgnoreListOpen ? 'Hide' : 'Show'} ${ignoredFramesTally} ignore-listed frame(s)`}\n            <ChevronUpDown />\n          </button>\n        )}\n      </div>\n      {frames.map((frame, frameIndex) => {\n        return !frame.ignored || isIgnoreListOpen ? (\n          <CallStackFrame key={frameIndex} frame={frame} />\n        ) : null\n      })}\n    </div>\n  )\n}\n\nfunction ChevronUpDown() {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M8.70722 2.39641C8.3167 2.00588 7.68353 2.00588 7.29301 2.39641L4.46978 5.21963L3.93945 5.74996L5.00011 6.81062L5.53044 6.28029L8.00011 3.81062L10.4698 6.28029L11.0001 6.81062L12.0608 5.74996L11.5304 5.21963L8.70722 2.39641ZM5.53044 9.71963L5.00011 9.1893L3.93945 10.25L4.46978 10.7803L7.29301 13.6035C7.68353 13.994 8.3167 13.994 8.70722 13.6035L11.5304 10.7803L12.0608 10.25L11.0001 9.1893L10.4698 9.71963L8.00011 12.1893L5.53044 9.71963Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n\nexport const CALL_STACK_STYLES = `\n  .error-overlay-call-stack-container {\n    position: relative;\n    margin-top: 8px;\n  }\n\n  .error-overlay-call-stack-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    min-height: var(--size-28);\n    padding: 8px 8px 12px 4px;\n    width: 100%;\n  }\n\n  .error-overlay-call-stack-title {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 8px;\n\n    margin: 0;\n\n    color: var(--color-gray-1000);\n    font-size: var(--size-16);\n    font-weight: 500;\n  }\n\n  .error-overlay-call-stack-count {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    width: var(--size-20);\n    height: var(--size-20);\n    gap: 4px;\n\n    color: var(--color-gray-1000);\n    text-align: center;\n    font-size: var(--size-11);\n    font-weight: 500;\n    line-height: var(--size-16);\n\n    border-radius: var(--rounded-full);\n    background: var(--color-gray-300);\n  }\n\n  .error-overlay-call-stack-ignored-list-toggle-button {\n    all: unset;\n    display: flex;\n    align-items: center;\n    gap: 6px;\n    color: var(--color-gray-900);\n    font-size: var(--size-14);\n    line-height: var(--size-20);\n    border-radius: 6px;\n    padding: 4px 6px;\n    margin-right: -6px;\n    transition: background 150ms ease;\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n\n    &:focus {\n      outline: var(--focus-ring);\n    }\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n  }\n`\n"], "names": ["useMemo", "useState", "useRef", "CallStackFrame", "CallStack", "frames", "dialogResizerRef", "initialDialogHeight", "NaN", "isIgnoreListOpen", "setIsIgnoreListOpen", "ignoredFramesTally", "reduce", "tally", "frame", "ignored", "onToggleIgnoreList", "dialog", "current", "height", "currentHeight", "getBoundingClientRect", "onTransitionEnd", "removeEventListener", "style", "addEventListener", "div", "className", "p", "span", "length", "button", "data-expand-ignore-button", "onClick", "ChevronUpDown", "map", "frameIndex", "svg", "width", "viewBox", "fill", "xmlns", "path", "fillRule", "clipRule", "d", "CALL_STACK_STYLES"], "mappings": ";AACA,SAASA,OAAO,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,QAAO;AACjD,SAASC,cAAc,QAAQ,0CAAyC;AAOxE,OAAO,SAASC,UAAU,KAA4C;IAA5C,IAAA,EAAEC,MAAM,EAAEC,gBAAgB,EAAkB,GAA5C;IACxB,MAAMC,sBAAsBL,OAAeM;IAC3C,MAAM,CAACC,kBAAkBC,oBAAoB,GAAGT,SAAS;IAEzD,MAAMU,qBAAqBX,QAAQ;QACjC,OAAOK,OAAOO,MAAM,CAAC,CAACC,OAAOC,QAAUD,QAASC,CAAAA,MAAMC,OAAO,GAAG,IAAI,CAAA,GAAI;IAC1E,GAAG;QAACV;KAAO;IAEX,SAASW;QACP,MAAMC,SAASX,oCAAAA,iBAAkBY,OAAO;QAExC,IAAI,CAACD,QAAQ;YACX;QACF;QAEA,MAAM,EAAEE,QAAQC,aAAa,EAAE,GAAGH,0BAAAA,OAAQI,qBAAqB;QAE/D,IAAI,CAACd,oBAAoBW,OAAO,EAAE;YAChCX,oBAAoBW,OAAO,GAAGE;QAChC;QAEA,IAAIX,kBAAkB;YACpB,SAASa;gBACPL,OAAOM,mBAAmB,CAAC,iBAAiBD;gBAC5CZ,oBAAoB;YACtB;YACAO,OAAOO,KAAK,CAACL,MAAM,GAAG,AAAC,KAAEZ,oBAAoBW,OAAO,GAAC;YACrDD,OAAOQ,gBAAgB,CAAC,iBAAiBH;QAC3C,OAAO;YACLZ,oBAAoB;QACtB;IACF;IAEA,qBACE,MAACgB;QAAIC,WAAU;;0BACb,MAACD;gBAAIC,WAAU;;kCACb,MAACC;wBAAED,WAAU;;4BAAiC;4BACjC;0CACX,KAACE;gCAAKF,WAAU;0CACbtB,OAAOyB,MAAM;;;;oBAGjBnB,qBAAqB,mBACpB,MAACoB;wBACCC,6BAA2BvB;wBAC3BkB,WAAU;wBACVM,SAASjB;;4BAELP,CAAAA,mBAAmB,SAAS,MAAK,IAAE,MAAGE,qBAAmB;0CAC7D,KAACuB;;;;;YAIN7B,OAAO8B,GAAG,CAAC,CAACrB,OAAOsB;gBAClB,OAAO,CAACtB,MAAMC,OAAO,IAAIN,iCACvB,KAACN;oBAAgCW,OAAOA;mBAAnBsB,cACnB;YACN;;;AAGN;AAEA,SAASF;IACP,qBACE,KAACG;QACCC,OAAM;QACNnB,QAAO;QACPoB,SAAQ;QACRC,MAAK;QACLC,OAAM;kBAEN,cAAA,KAACC;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFL,MAAK;;;AAIb;AAEA,OAAO,MAAMM,oBAAqB,khDAyEjC"}