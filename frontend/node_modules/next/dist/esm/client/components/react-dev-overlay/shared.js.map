{"version": 3, "sources": ["../../../../src/client/components/react-dev-overlay/shared.ts"], "sourcesContent": ["import { useReducer } from 'react'\n\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport type { VersionInfo } from '../../../server/dev/parse-version-info'\nimport type { SupportedErrorEvent } from './ui/container/runtime-error/render-error'\nimport type { ComponentStackFrame } from './utils/parse-component-stack'\nimport type { DebugInfo } from './types'\nimport type { DevIndicatorServerState } from '../../../server/dev/dev-indicator-server-state'\n\ntype FastRefreshState =\n  /** No refresh in progress. */\n  | { type: 'idle' }\n  /** The refresh process has been triggered, but the new code has not been executed yet. */\n  | { type: 'pending'; errors: SupportedErrorEvent[] }\n\nexport interface OverlayState {\n  nextId: number\n  buildError: string | null\n  errors: SupportedErrorEvent[]\n  refreshState: FastRefreshState\n  rootLayoutMissingTags: typeof window.__next_root_layout_missing_tags\n  versionInfo: VersionInfo\n  notFound: boolean\n  staticIndicator: boolean\n  disableDevIndicator: boolean\n  debugInfo: DebugInfo\n  routerType: 'pages' | 'app'\n}\n\nexport const ACTION_STATIC_INDICATOR = 'static-indicator'\nexport const ACTION_BUILD_OK = 'build-ok'\nexport const ACTION_BUILD_ERROR = 'build-error'\nexport const ACTION_BEFORE_REFRESH = 'before-fast-refresh'\nexport const ACTION_REFRESH = 'fast-refresh'\nexport const ACTION_VERSION_INFO = 'version-info'\nexport const ACTION_UNHANDLED_ERROR = 'unhandled-error'\nexport const ACTION_UNHANDLED_REJECTION = 'unhandled-rejection'\nexport const ACTION_DEBUG_INFO = 'debug-info'\nexport const ACTION_DEV_INDICATOR = 'dev-indicator'\n\nexport const STORAGE_KEY_THEME = '__nextjs-dev-tools-theme'\nexport const STORAGE_KEY_POSITION = '__nextjs-dev-tools-position'\n\ninterface StaticIndicatorAction {\n  type: typeof ACTION_STATIC_INDICATOR\n  staticIndicator: boolean\n}\n\ninterface BuildOkAction {\n  type: typeof ACTION_BUILD_OK\n}\ninterface BuildErrorAction {\n  type: typeof ACTION_BUILD_ERROR\n  message: string\n}\ninterface BeforeFastRefreshAction {\n  type: typeof ACTION_BEFORE_REFRESH\n}\ninterface FastRefreshAction {\n  type: typeof ACTION_REFRESH\n}\n\nexport interface UnhandledErrorAction {\n  type: typeof ACTION_UNHANDLED_ERROR\n  reason: Error\n  frames: StackFrame[]\n  componentStackFrames?: ComponentStackFrame[]\n  warning?: [string, string, string]\n}\nexport interface UnhandledRejectionAction {\n  type: typeof ACTION_UNHANDLED_REJECTION\n  reason: Error\n  frames: StackFrame[]\n}\n\nexport interface DebugInfoAction {\n  type: typeof ACTION_DEBUG_INFO\n  debugInfo: any\n}\n\ninterface VersionInfoAction {\n  type: typeof ACTION_VERSION_INFO\n  versionInfo: VersionInfo\n}\n\ninterface DevIndicatorAction {\n  type: typeof ACTION_DEV_INDICATOR\n  devIndicator: DevIndicatorServerState\n}\n\nexport type BusEvent =\n  | BuildOkAction\n  | BuildErrorAction\n  | BeforeFastRefreshAction\n  | FastRefreshAction\n  | UnhandledErrorAction\n  | UnhandledRejectionAction\n  | VersionInfoAction\n  | StaticIndicatorAction\n  | DebugInfoAction\n  | DevIndicatorAction\n\nfunction pushErrorFilterDuplicates(\n  errors: SupportedErrorEvent[],\n  err: SupportedErrorEvent\n): SupportedErrorEvent[] {\n  return [\n    ...errors.filter((e) => {\n      // Filter out duplicate errors\n      return e.event.reason.stack !== err.event.reason.stack\n    }),\n    err,\n  ]\n}\n\nconst shouldDisableDevIndicator =\n  process.env.__NEXT_DEV_INDICATOR?.toString() === 'false'\n\nexport const INITIAL_OVERLAY_STATE: Omit<OverlayState, 'routerType'> = {\n  nextId: 1,\n  buildError: null,\n  errors: [],\n  notFound: false,\n  staticIndicator: false,\n  // To prevent flickering, set the initial state to disabled.\n  disableDevIndicator: true,\n  refreshState: { type: 'idle' },\n  rootLayoutMissingTags: [],\n  versionInfo: { installed: '0.0.0', staleness: 'unknown' },\n  debugInfo: { devtoolsFrontendUrl: undefined },\n}\n\nfunction getInitialState(\n  routerType: 'pages' | 'app'\n): OverlayState & { routerType: 'pages' | 'app' } {\n  return {\n    ...INITIAL_OVERLAY_STATE,\n    routerType,\n  }\n}\n\nexport function useErrorOverlayReducer(routerType: 'pages' | 'app') {\n  return useReducer((_state: OverlayState, action: BusEvent): OverlayState => {\n    switch (action.type) {\n      case ACTION_DEBUG_INFO: {\n        return { ..._state, debugInfo: action.debugInfo }\n      }\n      case ACTION_STATIC_INDICATOR: {\n        return { ..._state, staticIndicator: action.staticIndicator }\n      }\n      case ACTION_BUILD_OK: {\n        return { ..._state, buildError: null }\n      }\n      case ACTION_BUILD_ERROR: {\n        return { ..._state, buildError: action.message }\n      }\n      case ACTION_BEFORE_REFRESH: {\n        return { ..._state, refreshState: { type: 'pending', errors: [] } }\n      }\n      case ACTION_REFRESH: {\n        return {\n          ..._state,\n          buildError: null,\n          errors:\n            // Errors can come in during updates. In this case, UNHANDLED_ERROR\n            // and UNHANDLED_REJECTION events might be dispatched between the\n            // BEFORE_REFRESH and the REFRESH event. We want to keep those errors\n            // around until the next refresh. Otherwise we run into a race\n            // condition where those errors would be cleared on refresh completion\n            // before they can be displayed.\n            _state.refreshState.type === 'pending'\n              ? _state.refreshState.errors\n              : [],\n          refreshState: { type: 'idle' },\n        }\n      }\n      case ACTION_UNHANDLED_ERROR:\n      case ACTION_UNHANDLED_REJECTION: {\n        switch (_state.refreshState.type) {\n          case 'idle': {\n            return {\n              ..._state,\n              nextId: _state.nextId + 1,\n              errors: pushErrorFilterDuplicates(_state.errors, {\n                id: _state.nextId,\n                event: action,\n              }),\n            }\n          }\n          case 'pending': {\n            return {\n              ..._state,\n              nextId: _state.nextId + 1,\n              refreshState: {\n                ..._state.refreshState,\n                errors: pushErrorFilterDuplicates(_state.refreshState.errors, {\n                  id: _state.nextId,\n                  event: action,\n                }),\n              },\n            }\n          }\n          default:\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            const _: never = _state.refreshState\n            return _state\n        }\n      }\n      case ACTION_VERSION_INFO: {\n        return { ..._state, versionInfo: action.versionInfo }\n      }\n      case ACTION_DEV_INDICATOR: {\n        return {\n          ..._state,\n          disableDevIndicator:\n            shouldDisableDevIndicator || !!action.devIndicator.disabledUntil,\n        }\n      }\n      default: {\n        return _state\n      }\n    }\n  }, getInitialState(routerType))\n}\n\nexport const REACT_REFRESH_FULL_RELOAD_FROM_ERROR =\n  '[Fast Refresh] performing full reload because your application had an unrecoverable error'\n"], "names": ["process", "useReducer", "ACTION_STATIC_INDICATOR", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_BEFORE_REFRESH", "ACTION_REFRESH", "ACTION_VERSION_INFO", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_DEBUG_INFO", "ACTION_DEV_INDICATOR", "STORAGE_KEY_THEME", "STORAGE_KEY_POSITION", "pushErrorFilterDuplicates", "errors", "err", "filter", "e", "event", "reason", "stack", "shouldDisableDevIndicator", "env", "__NEXT_DEV_INDICATOR", "toString", "INITIAL_OVERLAY_STATE", "nextId", "buildError", "notFound", "staticIndicator", "disableDevIndicator", "refreshState", "type", "rootLayoutMissingTags", "versionInfo", "installed", "staleness", "debugInfo", "devtoolsFrontendUrl", "undefined", "getInitialState", "routerType", "useErrorOverlayReducer", "_state", "action", "message", "id", "_", "devIndicator", "disabledUntil", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR"], "mappings": "IAoHEA;AApHF,SAASC,UAAU,QAAQ,QAAO;AA6BlC,OAAO,MAAMC,0BAA0B,mBAAkB;AACzD,OAAO,MAAMC,kBAAkB,WAAU;AACzC,OAAO,MAAMC,qBAAqB,cAAa;AAC/C,OAAO,MAAMC,wBAAwB,sBAAqB;AAC1D,OAAO,MAAMC,iBAAiB,eAAc;AAC5C,OAAO,MAAMC,sBAAsB,eAAc;AACjD,OAAO,MAAMC,yBAAyB,kBAAiB;AACvD,OAAO,MAAMC,6BAA6B,sBAAqB;AAC/D,OAAO,MAAMC,oBAAoB,aAAY;AAC7C,OAAO,MAAMC,uBAAuB,gBAAe;AAEnD,OAAO,MAAMC,oBAAoB,2BAA0B;AAC3D,OAAO,MAAMC,uBAAuB,8BAA6B;AA6DjE,SAASC,0BACPC,MAA6B,EAC7BC,GAAwB;IAExB,OAAO;WACFD,OAAOE,MAAM,CAAC,CAACC;YAChB,8BAA8B;YAC9B,OAAOA,EAAEC,KAAK,CAACC,MAAM,CAACC,KAAK,KAAKL,IAAIG,KAAK,CAACC,MAAM,CAACC,KAAK;QACxD;QACAL;KACD;AACH;AAEA,MAAMM,4BACJtB,EAAAA,oCAAAA,QAAQuB,GAAG,CAACC,oBAAoB,qBAAhCxB,kCAAkCyB,QAAQ,QAAO;AAEnD,OAAO,MAAMC,wBAA0D;IACrEC,QAAQ;IACRC,YAAY;IACZb,QAAQ,EAAE;IACVc,UAAU;IACVC,iBAAiB;IACjB,4DAA4D;IAC5DC,qBAAqB;IACrBC,cAAc;QAAEC,MAAM;IAAO;IAC7BC,uBAAuB,EAAE;IACzBC,aAAa;QAAEC,WAAW;QAASC,WAAW;IAAU;IACxDC,WAAW;QAAEC,qBAAqBC;IAAU;AAC9C,EAAC;AAED,SAASC,gBACPC,UAA2B;IAE3B,OAAO;QACL,GAAGhB,qBAAqB;QACxBgB;IACF;AACF;AAEA,OAAO,SAASC,uBAAuBD,UAA2B;IAChE,OAAOzC,WAAW,CAAC2C,QAAsBC;QACvC,OAAQA,OAAOZ,IAAI;YACjB,KAAKvB;gBAAmB;oBACtB,OAAO;wBAAE,GAAGkC,MAAM;wBAAEN,WAAWO,OAAOP,SAAS;oBAAC;gBAClD;YACA,KAAKpC;gBAAyB;oBAC5B,OAAO;wBAAE,GAAG0C,MAAM;wBAAEd,iBAAiBe,OAAOf,eAAe;oBAAC;gBAC9D;YACA,KAAK3B;gBAAiB;oBACpB,OAAO;wBAAE,GAAGyC,MAAM;wBAAEhB,YAAY;oBAAK;gBACvC;YACA,KAAKxB;gBAAoB;oBACvB,OAAO;wBAAE,GAAGwC,MAAM;wBAAEhB,YAAYiB,OAAOC,OAAO;oBAAC;gBACjD;YACA,KAAKzC;gBAAuB;oBAC1B,OAAO;wBAAE,GAAGuC,MAAM;wBAAEZ,cAAc;4BAAEC,MAAM;4BAAWlB,QAAQ,EAAE;wBAAC;oBAAE;gBACpE;YACA,KAAKT;gBAAgB;oBACnB,OAAO;wBACL,GAAGsC,MAAM;wBACThB,YAAY;wBACZb,QACE,mEAAmE;wBACnE,iEAAiE;wBACjE,qEAAqE;wBACrE,8DAA8D;wBAC9D,sEAAsE;wBACtE,gCAAgC;wBAChC6B,OAAOZ,YAAY,CAACC,IAAI,KAAK,YACzBW,OAAOZ,YAAY,CAACjB,MAAM,GAC1B,EAAE;wBACRiB,cAAc;4BAAEC,MAAM;wBAAO;oBAC/B;gBACF;YACA,KAAKzB;YACL,KAAKC;gBAA4B;oBAC/B,OAAQmC,OAAOZ,YAAY,CAACC,IAAI;wBAC9B,KAAK;4BAAQ;gCACX,OAAO;oCACL,GAAGW,MAAM;oCACTjB,QAAQiB,OAAOjB,MAAM,GAAG;oCACxBZ,QAAQD,0BAA0B8B,OAAO7B,MAAM,EAAE;wCAC/CgC,IAAIH,OAAOjB,MAAM;wCACjBR,OAAO0B;oCACT;gCACF;4BACF;wBACA,KAAK;4BAAW;gCACd,OAAO;oCACL,GAAGD,MAAM;oCACTjB,QAAQiB,OAAOjB,MAAM,GAAG;oCACxBK,cAAc;wCACZ,GAAGY,OAAOZ,YAAY;wCACtBjB,QAAQD,0BAA0B8B,OAAOZ,YAAY,CAACjB,MAAM,EAAE;4CAC5DgC,IAAIH,OAAOjB,MAAM;4CACjBR,OAAO0B;wCACT;oCACF;gCACF;4BACF;wBACA;4BACE,6DAA6D;4BAC7D,MAAMG,IAAWJ,OAAOZ,YAAY;4BACpC,OAAOY;oBACX;gBACF;YACA,KAAKrC;gBAAqB;oBACxB,OAAO;wBAAE,GAAGqC,MAAM;wBAAET,aAAaU,OAAOV,WAAW;oBAAC;gBACtD;YACA,KAAKxB;gBAAsB;oBACzB,OAAO;wBACL,GAAGiC,MAAM;wBACTb,qBACET,6BAA6B,CAAC,CAACuB,OAAOI,YAAY,CAACC,aAAa;oBACpE;gBACF;YACA;gBAAS;oBACP,OAAON;gBACT;QACF;IACF,GAAGH,gBAAgBC;AACrB;AAEA,OAAO,MAAMS,uCACX,4FAA2F"}