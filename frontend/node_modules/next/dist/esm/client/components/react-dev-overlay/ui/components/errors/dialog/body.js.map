{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dialog/body.tsx"], "sourcesContent": ["import { DialogBody } from '../../dialog'\n\ntype ErrorOverlayDialogBodyProps = {\n  children?: React.ReactNode\n  onClose?: () => void\n}\n\nexport function ErrorOverlayDialogBody({\n  children,\n}: ErrorOverlayDialogBodyProps) {\n  return (\n    <DialogBody className=\"nextjs-container-errors-body\">{children}</DialogBody>\n  )\n}\n\nexport const DIALOG_BODY_STYLES = ``\n"], "names": ["DialogBody", "ErrorOverlayDialogBody", "children", "className", "DIALOG_BODY_STYLES"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAc;AAOzC,OAAO,SAASC,uBAAuB,KAET;IAFS,IAAA,EACrCC,QAAQ,EACoB,GAFS;IAGrC,qBACE,KAACF;QAAWG,WAAU;kBAAgCD;;AAE1D;AAEA,OAAO,MAAME,qBAAsB,GAAC"}