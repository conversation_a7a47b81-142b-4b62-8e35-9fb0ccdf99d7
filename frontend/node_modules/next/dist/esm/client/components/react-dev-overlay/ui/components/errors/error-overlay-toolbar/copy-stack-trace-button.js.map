{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/copy-stack-trace-button.tsx"], "sourcesContent": ["import { CopyButton } from '../../copy-button'\n\nexport function CopyStackTraceButton({ error }: { error: E<PERSON>r }) {\n  return (\n    <CopyButton\n      data-nextjs-data-runtime-error-copy-stack\n      className=\"copy-stack-trace-button\"\n      actionLabel=\"Copy Stack Trace\"\n      successLabel=\"Stack Trace Copied\"\n      content={error.stack || ''}\n      disabled={!error.stack}\n    />\n  )\n}\n"], "names": ["Copy<PERSON><PERSON><PERSON>", "CopyStackTraceButton", "error", "data-nextjs-data-runtime-error-copy-stack", "className", "actionLabel", "successLabel", "content", "stack", "disabled"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,oBAAmB;AAE9C,OAAO,SAASC,qBAAqB,KAA2B;IAA3B,IAAA,EAAEC,KAAK,EAAoB,GAA3B;IACnC,qBACE,KAACF;QACCG,2CAAyC;QACzCC,WAAU;QACVC,aAAY;QACZC,cAAa;QACbC,SAASL,MAAMM,KAAK,IAAI;QACxBC,UAAU,CAACP,MAAMM,KAAK;;AAG5B"}