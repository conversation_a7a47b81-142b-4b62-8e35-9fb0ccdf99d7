{"version": 3, "sources": ["../../../src/client/components/links.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../server/app-render/types'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { getCurrentAppRouterState } from '../../shared/lib/router/action-queue'\nimport { createPrefetchURL } from './app-router'\nimport { PrefetchKind } from './router-reducer/router-reducer-types'\nimport { getCurrentCacheVersion } from './segment-cache'\nimport { createCacheKey } from './segment-cache'\nimport {\n  type PrefetchTask,\n  PrefetchPriority,\n  schedulePrefetchTask as scheduleSegmentPrefetchTask,\n  cancelPrefetchTask,\n  bumpPrefetchTask,\n} from './segment-cache'\n\ntype LinkElement = HTMLAnchorElement | SVGAElement | HTMLFormElement\n\ntype LinkInstance = {\n  router: AppRouterInstance\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n  prefetchHref: string\n\n  isVisible: boolean\n  wasHoveredOrTouched: boolean\n\n  // The most recently initiated prefetch task. It may or may not have\n  // already completed.  The same prefetch task object can be reused across\n  // multiple prefetches of the same link.\n  prefetchTask: PrefetchTask | null\n\n  // The cache version at the time the task was initiated. This is used to\n  // determine if the cache was invalidated since the task was initiated.\n  cacheVersion: number\n}\n\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst links: WeakMap<LinkElement, LinkInstance> | Map<Element, LinkInstance> =\n  typeof WeakMap === 'function' ? new WeakMap() : new Map()\n\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst visibleLinks: Set<LinkInstance> = new Set()\n\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer: IntersectionObserver | null =\n  typeof IntersectionObserver === 'function'\n    ? new IntersectionObserver(handleIntersect, {\n        rootMargin: '200px',\n      })\n    : null\n\nexport function mountLinkInstance(\n  element: LinkElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n) {\n  let prefetchUrl: URL | null = null\n  try {\n    prefetchUrl = createPrefetchURL(href)\n    if (prefetchUrl === null) {\n      // We only track the link if it's prefetchable. For example, this excludes\n      // links to external URLs.\n      return\n    }\n  } catch {\n    // createPrefetchURL sometimes throws an error if an invalid URL is\n    // provided, though I'm not sure if it's actually necessary.\n    // TODO: Consider removing the throw from the inner function, or change it\n    // to reportError. Or maybe the error isn't even necessary for automatic\n    // prefetches, just navigations.\n    const reportErrorFn =\n      typeof reportError === 'function' ? reportError : console.error\n    reportErrorFn(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n    return\n  }\n\n  const instance: LinkInstance = {\n    prefetchHref: prefetchUrl.href,\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n  }\n  const existingInstance = links.get(element)\n  if (existingInstance !== undefined) {\n    // This shouldn't happen because each <Link> component should have its own\n    // anchor tag instance, but it's defensive coding to avoid a memory leak in\n    // case there's a logical error somewhere else.\n    unmountLinkInstance(element)\n  }\n  links.set(element, instance)\n  if (observer !== null) {\n    observer.observe(element)\n  }\n}\n\nexport function unmountLinkInstance(element: LinkElement) {\n  const instance = links.get(element)\n  if (instance !== undefined) {\n    links.delete(element)\n    visibleLinks.delete(instance)\n    const prefetchTask = instance.prefetchTask\n    if (prefetchTask !== null) {\n      cancelPrefetchTask(prefetchTask)\n    }\n  }\n  if (observer !== null) {\n    observer.unobserve(element)\n  }\n}\n\nfunction handleIntersect(entries: Array<IntersectionObserverEntry>) {\n  for (const entry of entries) {\n    // Some extremely old browsers or polyfills don't reliably support\n    // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n    // really. But whatever this is fine.)\n    const isVisible = entry.intersectionRatio > 0\n    onLinkVisibilityChanged(entry.target as HTMLAnchorElement, isVisible)\n  }\n}\n\nexport function onLinkVisibilityChanged(\n  element: LinkElement,\n  isVisible: boolean\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Prefetching on viewport is disabled in development for performance\n    // reasons, because it requires compiling the target page.\n    // TODO: Investigate re-enabling this.\n    return\n  }\n\n  const instance = links.get(element)\n  if (instance === undefined) {\n    return\n  }\n\n  instance.isVisible = isVisible\n  if (isVisible) {\n    visibleLinks.add(instance)\n  } else {\n    visibleLinks.delete(instance)\n  }\n  rescheduleLinkPrefetch(instance)\n}\n\nexport function onNavigationIntent(element: HTMLAnchorElement | SVGAElement) {\n  const instance = links.get(element)\n  if (instance === undefined) {\n    return\n  }\n  // Prefetch the link on hover/touchstart.\n  if (instance !== undefined) {\n    instance.wasHoveredOrTouched = true\n    rescheduleLinkPrefetch(instance)\n  }\n}\n\nfunction rescheduleLinkPrefetch(instance: LinkInstance) {\n  const existingPrefetchTask = instance.prefetchTask\n\n  if (!instance.isVisible) {\n    // Cancel any in-progress prefetch task. (If it already finished then this\n    // is a no-op.)\n    if (existingPrefetchTask !== null) {\n      cancelPrefetchTask(existingPrefetchTask)\n    }\n    // We don't need to reset the prefetchTask to null upon cancellation; an\n    // old task object can be rescheduled with bumpPrefetchTask. This is a\n    // micro-optimization but also makes the code simpler (don't need to\n    // worry about whether an old task object is stale).\n    return\n  }\n\n  if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // The old prefetch implementation does not have different priority levels.\n    // Just schedule a new prefetch task.\n    prefetchWithOldCacheImplementation(instance)\n    return\n  }\n\n  // In the Segment Cache implementation, we assign a higher priority level to\n  // links that were at one point hovered or touched. Since the queue is last-\n  // in-first-out, the highest priority Link is whichever one was hovered last.\n  //\n  // We also increase the relative priority of links whenever they re-enter the\n  // viewport, as if they were being scheduled for the first time.\n  const priority = instance.wasHoveredOrTouched\n    ? PrefetchPriority.Intent\n    : PrefetchPriority.Default\n  if (existingPrefetchTask === null) {\n    // Initiate a prefetch task.\n    const appRouterState = getCurrentAppRouterState()\n    if (appRouterState !== null) {\n      const nextUrl = appRouterState.nextUrl\n      const treeAtTimeOfPrefetch = appRouterState.tree\n      const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n      instance.prefetchTask = scheduleSegmentPrefetchTask(\n        cacheKey,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n      instance.cacheVersion = getCurrentCacheVersion()\n    }\n  } else {\n    // We already have an old task object that we can reschedule. This is\n    // effectively the same as canceling the old task and creating a new one.\n    bumpPrefetchTask(existingPrefetchTask, priority)\n  }\n}\n\nexport function pingVisibleLinks(\n  nextUrl: string | null,\n  tree: FlightRouterState\n) {\n  // For each currently visible link, cancel the existing prefetch task (if it\n  // exists) and schedule a new one. This is effectively the same as if all the\n  // visible links left and then re-entered the viewport.\n  //\n  // This is called when the Next-Url or the base tree changes, since those\n  // may affect the result of a prefetch task. It's also called after a\n  // cache invalidation.\n  const currentCacheVersion = getCurrentCacheVersion()\n  for (const instance of visibleLinks) {\n    const task = instance.prefetchTask\n    if (\n      task !== null &&\n      instance.cacheVersion === currentCacheVersion &&\n      task.key.nextUrl === nextUrl &&\n      task.treeAtTimeOfPrefetch === tree\n    ) {\n      // The cache has not been invalidated, and none of the inputs have\n      // changed. Bail out.\n      continue\n    }\n    // Something changed. Cancel the existing prefetch task and schedule a\n    // new one.\n    if (task !== null) {\n      cancelPrefetchTask(task)\n    }\n    const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n    const priority = instance.wasHoveredOrTouched\n      ? PrefetchPriority.Intent\n      : PrefetchPriority.Default\n    instance.prefetchTask = scheduleSegmentPrefetchTask(\n      cacheKey,\n      tree,\n      instance.kind === PrefetchKind.FULL,\n      priority\n    )\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nfunction prefetchWithOldCacheImplementation(instance: LinkInstance) {\n  // This is the path used when the Segment Cache is not enabled.\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return instance.router.prefetch(instance.prefetchHref, {\n      kind: instance.kind,\n    })\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n"], "names": ["getCurrentAppRouterState", "createPrefetchURL", "PrefetchKind", "getCurrentCacheVersion", "createCacheKey", "PrefetchPriority", "schedulePrefetchTask", "scheduleSegmentPrefetchTask", "cancelPrefetchTask", "bumpPrefetchTask", "links", "WeakMap", "Map", "visibleLinks", "Set", "observer", "IntersectionObserver", "handleIntersect", "rootMargin", "mountLinkInstance", "element", "href", "router", "kind", "prefetchUrl", "reportErrorFn", "reportError", "console", "error", "instance", "prefetchHref", "isVisible", "wasHoveredOrTouched", "prefetchTask", "cacheVersion", "existingInstance", "get", "undefined", "unmountLinkInstance", "set", "observe", "delete", "unobserve", "entries", "entry", "intersectionRatio", "onLinkVisibilityChanged", "target", "process", "env", "NODE_ENV", "add", "rescheduleLinkPrefetch", "onNavigationIntent", "existingPrefetchTask", "__NEXT_CLIENT_SEGMENT_CACHE", "prefetchWithOldCacheImplementation", "priority", "Intent", "<PERSON><PERSON><PERSON>", "appRouterState", "nextUrl", "treeAtTimeOfPrefetch", "tree", "cache<PERSON>ey", "FULL", "pingVisibleLinks", "currentCacheVersion", "task", "key", "window", "doPrefetch", "prefetch", "catch", "err"], "mappings": "AAEA,SAASA,wBAAwB,QAAQ,uCAAsC;AAC/E,SAASC,iBAAiB,QAAQ,eAAc;AAChD,SAASC,YAAY,QAAQ,wCAAuC;AACpE,SAASC,sBAAsB,QAAQ,kBAAiB;AACxD,SAASC,cAAc,QAAQ,kBAAiB;AAChD,SAEEC,gBAAgB,EAChBC,wBAAwBC,2BAA2B,EACnDC,kBAAkB,EAClBC,gBAAgB,QACX,kBAAiB;AAsBxB,2EAA2E;AAC3E,mEAAmE;AACnE,MAAMC,QACJ,OAAOC,YAAY,aAAa,IAAIA,YAAY,IAAIC;AAEtD,6EAA6E;AAC7E,4EAA4E;AAC5E,0EAA0E;AAC1E,iBAAiB;AACjB,MAAMC,eAAkC,IAAIC;AAE5C,0EAA0E;AAC1E,MAAMC,WACJ,OAAOC,yBAAyB,aAC5B,IAAIA,qBAAqBC,iBAAiB;IACxCC,YAAY;AACd,KACA;AAEN,OAAO,SAASC,kBACdC,OAAoB,EACpBC,IAAY,EACZC,MAAyB,EACzBC,IAA2C;IAE3C,IAAIC,cAA0B;IAC9B,IAAI;QACFA,cAAcvB,kBAAkBoB;QAChC,IAAIG,gBAAgB,MAAM;YACxB,0EAA0E;YAC1E,0BAA0B;YAC1B;QACF;IACF,EAAE,UAAM;QACN,mEAAmE;QACnE,4DAA4D;QAC5D,0EAA0E;QAC1E,wEAAwE;QACxE,gCAAgC;QAChC,MAAMC,gBACJ,OAAOC,gBAAgB,aAAaA,cAAcC,QAAQC,KAAK;QACjEH,cACE,AAAC,sBAAmBJ,OAAK;QAE3B;IACF;IAEA,MAAMQ,WAAyB;QAC7BC,cAAcN,YAAYH,IAAI;QAC9BC;QACAC;QACAQ,WAAW;QACXC,qBAAqB;QACrBC,cAAc;QACdC,cAAc,CAAC;IACjB;IACA,MAAMC,mBAAmBzB,MAAM0B,GAAG,CAAChB;IACnC,IAAIe,qBAAqBE,WAAW;QAClC,0EAA0E;QAC1E,2EAA2E;QAC3E,+CAA+C;QAC/CC,oBAAoBlB;IACtB;IACAV,MAAM6B,GAAG,CAACnB,SAASS;IACnB,IAAId,aAAa,MAAM;QACrBA,SAASyB,OAAO,CAACpB;IACnB;AACF;AAEA,OAAO,SAASkB,oBAAoBlB,OAAoB;IACtD,MAAMS,WAAWnB,MAAM0B,GAAG,CAAChB;IAC3B,IAAIS,aAAaQ,WAAW;QAC1B3B,MAAM+B,MAAM,CAACrB;QACbP,aAAa4B,MAAM,CAACZ;QACpB,MAAMI,eAAeJ,SAASI,YAAY;QAC1C,IAAIA,iBAAiB,MAAM;YACzBzB,mBAAmByB;QACrB;IACF;IACA,IAAIlB,aAAa,MAAM;QACrBA,SAAS2B,SAAS,CAACtB;IACrB;AACF;AAEA,SAASH,gBAAgB0B,OAAyC;IAChE,KAAK,MAAMC,SAASD,QAAS;QAC3B,kEAAkE;QAClE,yEAAyE;QACzE,sCAAsC;QACtC,MAAMZ,YAAYa,MAAMC,iBAAiB,GAAG;QAC5CC,wBAAwBF,MAAMG,MAAM,EAAuBhB;IAC7D;AACF;AAEA,OAAO,SAASe,wBACd1B,OAAoB,EACpBW,SAAkB;IAElB,IAAIiB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,qEAAqE;QACrE,0DAA0D;QAC1D,sCAAsC;QACtC;IACF;IAEA,MAAMrB,WAAWnB,MAAM0B,GAAG,CAAChB;IAC3B,IAAIS,aAAaQ,WAAW;QAC1B;IACF;IAEAR,SAASE,SAAS,GAAGA;IACrB,IAAIA,WAAW;QACblB,aAAasC,GAAG,CAACtB;IACnB,OAAO;QACLhB,aAAa4B,MAAM,CAACZ;IACtB;IACAuB,uBAAuBvB;AACzB;AAEA,OAAO,SAASwB,mBAAmBjC,OAAwC;IACzE,MAAMS,WAAWnB,MAAM0B,GAAG,CAAChB;IAC3B,IAAIS,aAAaQ,WAAW;QAC1B;IACF;IACA,yCAAyC;IACzC,IAAIR,aAAaQ,WAAW;QAC1BR,SAASG,mBAAmB,GAAG;QAC/BoB,uBAAuBvB;IACzB;AACF;AAEA,SAASuB,uBAAuBvB,QAAsB;IACpD,MAAMyB,uBAAuBzB,SAASI,YAAY;IAElD,IAAI,CAACJ,SAASE,SAAS,EAAE;QACvB,0EAA0E;QAC1E,eAAe;QACf,IAAIuB,yBAAyB,MAAM;YACjC9C,mBAAmB8C;QACrB;QACA,wEAAwE;QACxE,sEAAsE;QACtE,oEAAoE;QACpE,oDAAoD;QACpD;IACF;IAEA,IAAI,CAACN,QAAQC,GAAG,CAACM,2BAA2B,EAAE;QAC5C,2EAA2E;QAC3E,qCAAqC;QACrCC,mCAAmC3B;QACnC;IACF;IAEA,4EAA4E;IAC5E,4EAA4E;IAC5E,6EAA6E;IAC7E,EAAE;IACF,6EAA6E;IAC7E,gEAAgE;IAChE,MAAM4B,WAAW5B,SAASG,mBAAmB,GACzC3B,iBAAiBqD,MAAM,GACvBrD,iBAAiBsD,OAAO;IAC5B,IAAIL,yBAAyB,MAAM;QACjC,4BAA4B;QAC5B,MAAMM,iBAAiB5D;QACvB,IAAI4D,mBAAmB,MAAM;YAC3B,MAAMC,UAAUD,eAAeC,OAAO;YACtC,MAAMC,uBAAuBF,eAAeG,IAAI;YAChD,MAAMC,WAAW5D,eAAeyB,SAASC,YAAY,EAAE+B;YACvDhC,SAASI,YAAY,GAAG1B,4BACtByD,UACAF,sBACAjC,SAASN,IAAI,KAAKrB,aAAa+D,IAAI,EACnCR;YAEF5B,SAASK,YAAY,GAAG/B;QAC1B;IACF,OAAO;QACL,qEAAqE;QACrE,yEAAyE;QACzEM,iBAAiB6C,sBAAsBG;IACzC;AACF;AAEA,OAAO,SAASS,iBACdL,OAAsB,EACtBE,IAAuB;IAEvB,4EAA4E;IAC5E,6EAA6E;IAC7E,uDAAuD;IACvD,EAAE;IACF,yEAAyE;IACzE,qEAAqE;IACrE,sBAAsB;IACtB,MAAMI,sBAAsBhE;IAC5B,KAAK,MAAM0B,YAAYhB,aAAc;QACnC,MAAMuD,OAAOvC,SAASI,YAAY;QAClC,IACEmC,SAAS,QACTvC,SAASK,YAAY,KAAKiC,uBAC1BC,KAAKC,GAAG,CAACR,OAAO,KAAKA,WACrBO,KAAKN,oBAAoB,KAAKC,MAC9B;YAGA;QACF;QACA,sEAAsE;QACtE,WAAW;QACX,IAAIK,SAAS,MAAM;YACjB5D,mBAAmB4D;QACrB;QACA,MAAMJ,WAAW5D,eAAeyB,SAASC,YAAY,EAAE+B;QACvD,MAAMJ,WAAW5B,SAASG,mBAAmB,GACzC3B,iBAAiBqD,MAAM,GACvBrD,iBAAiBsD,OAAO;QAC5B9B,SAASI,YAAY,GAAG1B,4BACtByD,UACAD,MACAlC,SAASN,IAAI,KAAKrB,aAAa+D,IAAI,EACnCR;QAEF5B,SAASK,YAAY,GAAG/B;IAC1B;AACF;AAEA,SAASqD,mCAAmC3B,QAAsB;IAChE,+DAA+D;IAC/D,IAAI,OAAOyC,WAAW,aAAa;QACjC;IACF;IAEA,MAAMC,aAAa;QACjB,sDAAsD;QACtD,wFAAwF;QACxF,OAAO1C,SAASP,MAAM,CAACkD,QAAQ,CAAC3C,SAASC,YAAY,EAAE;YACrDP,MAAMM,SAASN,IAAI;QACrB;IACF;IAEA,kDAAkD;IAClD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDgD,aAAaE,KAAK,CAAC,CAACC;QAClB,IAAI1B,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMwB;QACR;IACF;AACF"}