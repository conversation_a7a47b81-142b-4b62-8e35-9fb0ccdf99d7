{"version": 3, "sources": ["../../../../src/client/components/segment-cache-impl/prefetch.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../../server/app-render/types'\nimport { createPrefetchURL } from '../app-router'\nimport { createCacheKey } from './cache-key'\nimport { schedulePrefetchTask } from './scheduler'\nimport { PrefetchPriority } from '../segment-cache'\n\n/**\n * Entrypoint for prefetching a URL into the Segment Cache.\n * @param href - The URL to prefetch. Typically this will come from a <Link>,\n * or router.prefetch. It must be validated before we attempt to prefetch it.\n * @param nextUrl - A special header used by the server for interception routes.\n * Roughly  corresponds to the current URL.\n * @param treeAtTimeOfPrefetch - The FlightRouterState at the time the prefetch\n * was requested. This is only used when PPR is disabled.\n * @param includeDynamicData - Whether to prefetch dynamic data, in addition to\n * static data. This is used by <Link prefetch={true}>.\n */\nexport function prefetch(\n  href: string,\n  nextUrl: string | null,\n  treeAtTimeOfPrefetch: FlightRouterState,\n  includeDynamicData: boolean\n) {\n  const url = createPrefetchURL(href)\n  if (url === null) {\n    // This href should not be prefetched.\n    return\n  }\n  const cacheKey = createCacheKey(url.href, nextUrl)\n  schedulePrefetchTask(\n    cacheKey,\n    treeAtTimeOfPrefetch,\n    includeDynamicData,\n    PrefetchPriority.Default\n  )\n}\n"], "names": ["createPrefetchURL", "createCacheKey", "schedulePrefetchTask", "PrefetchPriority", "prefetch", "href", "nextUrl", "treeAtTimeOfPrefetch", "includeDynamicData", "url", "cache<PERSON>ey", "<PERSON><PERSON><PERSON>"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,gBAAe;AACjD,SAASC,cAAc,QAAQ,cAAa;AAC5C,SAASC,oBAAoB,QAAQ,cAAa;AAClD,SAASC,gBAAgB,QAAQ,mBAAkB;AAEnD;;;;;;;;;;CAUC,GACD,OAAO,SAASC,SACdC,IAAY,EACZC,OAAsB,EACtBC,oBAAuC,EACvCC,kBAA2B;IAE3B,MAAMC,MAAMT,kBAAkBK;IAC9B,IAAII,QAAQ,MAAM;QAChB,sCAAsC;QACtC;IACF;IACA,MAAMC,WAAWT,eAAeQ,IAAIJ,IAAI,EAAEC;IAC1CJ,qBACEQ,UACAH,sBACAC,oBACAL,iBAAiBQ,OAAO;AAE5B"}