{"version": 3, "sources": ["../../../src/client/components/unauthorized-error.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from './http-access-fallback/error-fallback'\n\nexport default function Unauthorized() {\n  return (\n    <HTTPAccessErrorFallback\n      status={401}\n      message=\"You're not authorized to access this page.\"\n    />\n  )\n}\n"], "names": ["HTTPAccessErrorFallback", "Unauthorized", "status", "message"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,wCAAuC;AAE/E,eAAe,SAASC;IACtB,qBACE,KAACD;QACCE,QAAQ;QACRC,SAAQ;;AAGd"}