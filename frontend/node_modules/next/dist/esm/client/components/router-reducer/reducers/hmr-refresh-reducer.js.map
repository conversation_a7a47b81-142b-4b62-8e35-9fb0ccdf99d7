{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/hmr-refresh-reducer.ts"], "sourcesContent": ["import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  HmrRefreshAction,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\n\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(\n  state: ReadonlyReducerState,\n  action: HmrRefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [state.tree[0], state.tree[1], state.tree[2], 'refetch'],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n    isHmrRefresh: true,\n  })\n\n  return cache.lazyData.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n\n      for (const normalizedFlightData of flightData) {\n        const { tree: treePatch, isRootRender } = normalizedFlightData\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n        const applied = applyFlightData(\n          currentCache,\n          cache,\n          normalizedFlightData\n        )\n\n        if (applied) {\n          mutable.cache = cache\n          currentCache = cache\n        }\n\n        mutable.patchedTree = newTree\n        mutable.canonicalUrl = href\n\n        currentTree = newTree\n      }\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n\nfunction hmrRefreshReducerNoop(\n  state: ReadonlyReducerState,\n  _action: HmrRefreshAction\n): ReducerState {\n  return state\n}\n\nexport const hmrRefreshReducer =\n  process.env.NODE_ENV === 'production'\n    ? hmrRefreshReducerNoop\n    : hmrRefreshReducerImpl\n"], "names": ["fetchServerResponse", "createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "handleMutable", "applyFlightData", "createEmptyCacheNode", "handleSegmentMismatch", "hasInterceptionRouteInCurrentTree", "hmrRefreshReducerImpl", "state", "action", "origin", "mutable", "href", "canonicalUrl", "preserveCustomHistoryState", "cache", "includeNextUrl", "tree", "lazyData", "URL", "flightRouterState", "nextUrl", "isHmrRefresh", "then", "flightData", "canonicalUrlOverride", "pushRef", "pendingPush", "currentTree", "currentCache", "normalizedFlightData", "treePatch", "isRootRender", "console", "log", "newTree", "canonicalUrlOverrideHref", "undefined", "applied", "patchedTree", "hmrRefreshReducerNoop", "_action", "hmrRefreshReducer", "process", "env", "NODE_ENV"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,uBAAsB;AAEtD,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,qBAAqB,QAAQ,6BAA4B;AAClE,SAASC,iCAAiC,QAAQ,2CAA0C;AAE5F,wFAAwF;AACxF,SAASC,sBACPC,KAA2B,EAC3BC,MAAwB;IAExB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/BF,QAAQG,0BAA0B,GAAG;IAErC,MAAMC,QAAmBX;IACzB,sFAAsF;IACtF,sHAAsH;IACtH,MAAMY,iBAAiBV,kCAAkCE,MAAMS,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxCF,MAAMG,QAAQ,GAAGrB,oBAAoB,IAAIsB,IAAIP,MAAMF,SAAS;QAC1DU,mBAAmB;YAACZ,MAAMS,IAAI,CAAC,EAAE;YAAET,MAAMS,IAAI,CAAC,EAAE;YAAET,MAAMS,IAAI,CAAC,EAAE;YAAE;SAAU;QAC3EI,SAASL,iBAAiBR,MAAMa,OAAO,GAAG;QAC1CC,cAAc;IAChB;IAEA,OAAOP,MAAMG,QAAQ,CAACK,IAAI,CACxB;YAAC,EAAEC,UAAU,EAAEX,cAAcY,oBAAoB,EAAE;QACjD,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOvB,kBACLO,OACAG,SACAa,YACAhB,MAAMkB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/DZ,MAAMG,QAAQ,GAAG;QAEjB,IAAIU,cAAcpB,MAAMS,IAAI;QAC5B,IAAIY,eAAerB,MAAMO,KAAK;QAE9B,KAAK,MAAMe,wBAAwBN,WAAY;YAC7C,MAAM,EAAEP,MAAMc,SAAS,EAAEC,YAAY,EAAE,GAAGF;YAC1C,IAAI,CAACE,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO1B;YACT;YAEA,MAAM2B,UAAUpC,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJ6B,aACAG,WACAvB,MAAMK,YAAY;YAGpB,IAAIsB,YAAY,MAAM;gBACpB,OAAO9B,sBAAsBG,OAAOC,QAAQsB;YAC9C;YAEA,IAAI/B,4BAA4B4B,aAAaO,UAAU;gBACrD,OAAOlC,kBACLO,OACAG,SACAC,MACAJ,MAAMkB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMS,2BAA2BX,uBAC7B3B,kBAAkB2B,wBAClBY;YAEJ,IAAIZ,sBAAsB;gBACxBd,QAAQE,YAAY,GAAGuB;YACzB;YACA,MAAME,UAAUnC,gBACd0B,cACAd,OACAe;YAGF,IAAIQ,SAAS;gBACX3B,QAAQI,KAAK,GAAGA;gBAChBc,eAAed;YACjB;YAEAJ,QAAQ4B,WAAW,GAAGJ;YACtBxB,QAAQE,YAAY,GAAGD;YAEvBgB,cAAcO;QAChB;QACA,OAAOjC,cAAcM,OAAOG;IAC9B,GACA,IAAMH;AAEV;AAEA,SAASgC,sBACPhC,KAA2B,EAC3BiC,OAAyB;IAEzB,OAAOjC;AACT;AAEA,OAAO,MAAMkC,oBACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrBL,wBACAjC,sBAAqB"}