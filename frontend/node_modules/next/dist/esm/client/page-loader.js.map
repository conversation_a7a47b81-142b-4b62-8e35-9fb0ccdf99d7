{"version": 3, "sources": ["../../src/client/page-loader.ts"], "sourcesContent": ["import type { ComponentType } from 'react'\nimport type { RouteLoader } from './route-loader'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport { addBasePath } from './add-base-path'\nimport { interpolateAs } from '../shared/lib/router/utils/interpolate-as'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { addLocale } from './add-locale'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { parseRelativeUrl } from '../shared/lib/router/utils/parse-relative-url'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { createRouteLoader, getClientBuildManifest } from './route-loader'\nimport {\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\n\ndeclare global {\n  interface Window {\n    __DEV_MIDDLEWARE_MATCHERS?: MiddlewareMatcher[]\n    __DEV_PAGES_MANIFEST?: { pages: string[] }\n    __SSG_MANIFEST_CB?: () => void\n    __SSG_MANIFEST?: Set<string>\n  }\n}\n\nexport type StyleSheetTuple = { href: string; text: string }\nexport type GoodPageCache = {\n  page: ComponentType\n  mod: any\n  styleSheets: StyleSheetTuple[]\n}\n\nexport default class PageLoader {\n  private buildId: string\n  private assetPrefix: string\n  private promisedSsgManifest: Promise<Set<string>>\n  private promisedDevPagesManifest?: Promise<string[]>\n  private promisedMiddlewareMatchers?: Promise<MiddlewareMatcher[]>\n\n  public routeLoader: RouteLoader\n\n  constructor(buildId: string, assetPrefix: string) {\n    this.routeLoader = createRouteLoader(assetPrefix)\n\n    this.buildId = buildId\n    this.assetPrefix = assetPrefix\n\n    this.promisedSsgManifest = new Promise((resolve) => {\n      if (window.__SSG_MANIFEST) {\n        resolve(window.__SSG_MANIFEST)\n      } else {\n        window.__SSG_MANIFEST_CB = () => {\n          resolve(window.__SSG_MANIFEST!)\n        }\n      }\n    })\n  }\n\n  getPageList() {\n    if (process.env.NODE_ENV === 'production') {\n      return getClientBuildManifest().then((manifest) => manifest.sortedPages)\n    } else {\n      if (window.__DEV_PAGES_MANIFEST) {\n        return window.__DEV_PAGES_MANIFEST.pages\n      } else {\n        this.promisedDevPagesManifest ||= fetch(\n          `${this.assetPrefix}/_next/static/development/${DEV_CLIENT_PAGES_MANIFEST}`,\n          { credentials: 'same-origin' }\n        )\n          .then((res) => res.json())\n          .then((manifest: { pages: string[] }) => {\n            window.__DEV_PAGES_MANIFEST = manifest\n            return manifest.pages\n          })\n          .catch((err) => {\n            console.log(`Failed to fetch devPagesManifest:`, err)\n            throw new Error(\n              `Failed to fetch _devPagesManifest.json. Is something blocking that network request?\\n` +\n                'Read more: https://nextjs.org/docs/messages/failed-to-fetch-devpagesmanifest'\n            )\n          })\n        return this.promisedDevPagesManifest\n      }\n    }\n  }\n\n  getMiddleware() {\n    // Webpack production\n    if (\n      process.env.NODE_ENV === 'production' &&\n      process.env.__NEXT_MIDDLEWARE_MATCHERS\n    ) {\n      const middlewareMatchers = process.env.__NEXT_MIDDLEWARE_MATCHERS\n      window.__MIDDLEWARE_MATCHERS = middlewareMatchers\n        ? (middlewareMatchers as any as MiddlewareMatcher[])\n        : undefined\n      return window.__MIDDLEWARE_MATCHERS\n      // Turbopack production\n    } else if (process.env.NODE_ENV === 'production') {\n      if (window.__MIDDLEWARE_MATCHERS) {\n        return window.__MIDDLEWARE_MATCHERS\n      } else {\n        if (!this.promisedMiddlewareMatchers) {\n          // TODO: Decide what should happen when fetching fails instead of asserting\n          // @ts-ignore\n          this.promisedMiddlewareMatchers = fetch(\n            `${this.assetPrefix}/_next/static/${this.buildId}/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`,\n            { credentials: 'same-origin' }\n          )\n            .then((res) => res.json())\n            .then((matchers: MiddlewareMatcher[]) => {\n              window.__MIDDLEWARE_MATCHERS = matchers\n              return matchers\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch _devMiddlewareManifest`, err)\n            })\n        }\n        // TODO Remove this assertion as this could be undefined\n        return this.promisedMiddlewareMatchers!\n      }\n      // Development both Turbopack and Webpack\n    } else {\n      if (window.__DEV_MIDDLEWARE_MATCHERS) {\n        return window.__DEV_MIDDLEWARE_MATCHERS\n      } else {\n        if (!this.promisedMiddlewareMatchers) {\n          // TODO: Decide what should happen when fetching fails instead of asserting\n          // @ts-ignore\n          this.promisedMiddlewareMatchers = fetch(\n            `${this.assetPrefix}/_next/static/${this.buildId}/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`,\n            { credentials: 'same-origin' }\n          )\n            .then((res) => res.json())\n            .then((matchers: MiddlewareMatcher[]) => {\n              window.__DEV_MIDDLEWARE_MATCHERS = matchers\n              return matchers\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch _devMiddlewareManifest`, err)\n            })\n        }\n        // TODO Remove this assertion as this could be undefined\n        return this.promisedMiddlewareMatchers!\n      }\n    }\n  }\n\n  getDataHref(params: {\n    asPath: string\n    href: string\n    locale?: string | false\n    skipInterpolation?: boolean\n  }): string {\n    const { asPath, href, locale } = params\n    const { pathname: hrefPathname, query, search } = parseRelativeUrl(href)\n    const { pathname: asPathname } = parseRelativeUrl(asPath)\n    const route = removeTrailingSlash(hrefPathname)\n    if (route[0] !== '/') {\n      throw new Error(`Route name should start with a \"/\", got \"${route}\"`)\n    }\n\n    const getHrefForSlug = (path: string) => {\n      const dataRoute = getAssetPathFromRoute(\n        removeTrailingSlash(addLocale(path, locale)),\n        '.json'\n      )\n      return addBasePath(\n        `/_next/data/${this.buildId}${dataRoute}${search}`,\n        true\n      )\n    }\n\n    return getHrefForSlug(\n      params.skipInterpolation\n        ? asPathname\n        : isDynamicRoute(route)\n          ? interpolateAs(hrefPathname, asPathname, query).result\n          : route\n    )\n  }\n\n  _isSsg(\n    /** the route (file-system path) */\n    route: string\n  ): Promise<boolean> {\n    return this.promisedSsgManifest.then((manifest) => manifest.has(route))\n  }\n\n  loadPage(route: string): Promise<GoodPageCache> {\n    return this.routeLoader.loadRoute(route).then((res) => {\n      if ('component' in res) {\n        return {\n          page: res.component,\n          mod: res.exports,\n          styleSheets: res.styles.map((o) => ({\n            href: o.href,\n            text: o.content,\n          })),\n        }\n      }\n      throw res.error\n    })\n  }\n\n  prefetch(route: string): Promise<void> {\n    return this.routeLoader.prefetch(route)\n  }\n}\n"], "names": ["addBasePath", "interpolateAs", "getAssetPathFromRoute", "addLocale", "isDynamicRoute", "parseRelativeUrl", "removeTrailingSlash", "createRouteLoader", "getClientBuildManifest", "DEV_CLIENT_PAGES_MANIFEST", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "<PERSON><PERSON><PERSON><PERSON>", "getPageList", "process", "env", "NODE_ENV", "then", "manifest", "sortedPages", "window", "__DEV_PAGES_MANIFEST", "pages", "promisedDevPagesManifest", "fetch", "assetPrefix", "credentials", "res", "json", "catch", "err", "console", "log", "Error", "getMiddleware", "__NEXT_MIDDLEWARE_MATCHERS", "middlewareMatchers", "__MIDDLEWARE_MATCHERS", "undefined", "promisedMiddlewareMatchers", "buildId", "matchers", "__DEV_MIDDLEWARE_MATCHERS", "getDataHref", "params", "<PERSON><PERSON><PERSON>", "href", "locale", "pathname", "hrefPathname", "query", "search", "asPathname", "route", "getHrefForSlug", "path", "dataRoute", "skipInterpolation", "result", "_isSsg", "promisedSsgManifest", "has", "loadPage", "routeLoader", "loadRoute", "page", "component", "mod", "exports", "styleSheets", "styles", "map", "o", "text", "content", "error", "prefetch", "constructor", "Promise", "resolve", "__SSG_MANIFEST", "__SSG_MANIFEST_CB"], "mappings": "AAGA,SAASA,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,aAAa,QAAQ,4CAA2C;AACzE,OAAOC,2BAA2B,uDAAsD;AACxF,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,iBAAgB;AAC1E,SACEC,yBAAyB,EACzBC,8BAA8B,EAC9BC,oCAAoC,QAC/B,0BAAyB;AAkBjB,MAAMC;IA0BnBC,cAAc;QACZ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,OAAOR,yBAAyBS,IAAI,CAAC,CAACC,WAAaA,SAASC,WAAW;QACzE,OAAO;YACL,IAAIC,OAAOC,oBAAoB,EAAE;gBAC/B,OAAOD,OAAOC,oBAAoB,CAACC,KAAK;YAC1C,OAAO;gBACL,IAAI,CAACC,6BAAL,IAAI,CAACA,2BAA6BC,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,+BAA4BhB,2BAChD;oBAAEiB,aAAa;gBAAc,GAE5BT,IAAI,CAAC,CAACU,MAAQA,IAAIC,IAAI,IACtBX,IAAI,CAAC,CAACC;oBACLE,OAAOC,oBAAoB,GAAGH;oBAC9B,OAAOA,SAASI,KAAK;gBACvB,GACCO,KAAK,CAAC,CAACC;oBACNC,QAAQC,GAAG,CAAE,qCAAoCF;oBACjD,MAAM,qBAGL,CAHK,IAAIG,MACR,AAAC,0FACC,iFAFE,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;gBACF,OAAO,IAAI,CAACV,wBAAwB;YACtC;QACF;IACF;IAEAW,gBAAgB;QACd,qBAAqB;QACrB,IACEpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBF,QAAQC,GAAG,CAACoB,0BAA0B,EACtC;YACA,MAAMC,qBAAqBtB,QAAQC,GAAG,CAACoB,0BAA0B;YACjEf,OAAOiB,qBAAqB,GAAGD,qBAC1BA,qBACDE;YACJ,OAAOlB,OAAOiB,qBAAqB;QACnC,uBAAuB;QACzB,OAAO,IAAIvB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YAChD,IAAII,OAAOiB,qBAAqB,EAAE;gBAChC,OAAOjB,OAAOiB,qBAAqB;YACrC,OAAO;gBACL,IAAI,CAAC,IAAI,CAACE,0BAA0B,EAAE;oBACpC,2EAA2E;oBAC3E,aAAa;oBACb,IAAI,CAACA,0BAA0B,GAAGf,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,mBAAgB,IAAI,CAACe,OAAO,GAAC,MAAG7B,sCACpD;wBAAEe,aAAa;oBAAc,GAE5BT,IAAI,CAAC,CAACU,MAAQA,IAAIC,IAAI,IACtBX,IAAI,CAAC,CAACwB;wBACLrB,OAAOiB,qBAAqB,GAAGI;wBAC/B,OAAOA;oBACT,GACCZ,KAAK,CAAC,CAACC;wBACNC,QAAQC,GAAG,CAAE,0CAAyCF;oBACxD;gBACJ;gBACA,wDAAwD;gBACxD,OAAO,IAAI,CAACS,0BAA0B;YACxC;QACA,yCAAyC;QAC3C,OAAO;YACL,IAAInB,OAAOsB,yBAAyB,EAAE;gBACpC,OAAOtB,OAAOsB,yBAAyB;YACzC,OAAO;gBACL,IAAI,CAAC,IAAI,CAACH,0BAA0B,EAAE;oBACpC,2EAA2E;oBAC3E,aAAa;oBACb,IAAI,CAACA,0BAA0B,GAAGf,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,mBAAgB,IAAI,CAACe,OAAO,GAAC,MAAG9B,gCACpD;wBAAEgB,aAAa;oBAAc,GAE5BT,IAAI,CAAC,CAACU,MAAQA,IAAIC,IAAI,IACtBX,IAAI,CAAC,CAACwB;wBACLrB,OAAOsB,yBAAyB,GAAGD;wBACnC,OAAOA;oBACT,GACCZ,KAAK,CAAC,CAACC;wBACNC,QAAQC,GAAG,CAAE,0CAAyCF;oBACxD;gBACJ;gBACA,wDAAwD;gBACxD,OAAO,IAAI,CAACS,0BAA0B;YACxC;QACF;IACF;IAEAI,YAAYC,MAKX,EAAU;QACT,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE,GAAGH;QACjC,MAAM,EAAEI,UAAUC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAG9C,iBAAiByC;QACnE,MAAM,EAAEE,UAAUI,UAAU,EAAE,GAAG/C,iBAAiBwC;QAClD,MAAMQ,QAAQ/C,oBAAoB2C;QAClC,IAAII,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,MAAM,qBAA+D,CAA/D,IAAIpB,MAAM,AAAC,8CAA2CoB,QAAM,MAA5D,qBAAA;uBAAA;4BAAA;8BAAA;YAA8D;QACtE;QAEA,MAAMC,iBAAiB,CAACC;YACtB,MAAMC,YAAYtD,sBAChBI,oBAAoBH,UAAUoD,MAAMR,UACpC;YAEF,OAAO/C,YACL,AAAC,iBAAc,IAAI,CAACwC,OAAO,GAAGgB,YAAYL,QAC1C;QAEJ;QAEA,OAAOG,eACLV,OAAOa,iBAAiB,GACpBL,aACAhD,eAAeiD,SACbpD,cAAcgD,cAAcG,YAAYF,OAAOQ,MAAM,GACrDL;IAEV;IAEAM,OACE,iCAAiC,GACjCN,KAAa,EACK;QAClB,OAAO,IAAI,CAACO,mBAAmB,CAAC3C,IAAI,CAAC,CAACC,WAAaA,SAAS2C,GAAG,CAACR;IAClE;IAEAS,SAAST,KAAa,EAA0B;QAC9C,OAAO,IAAI,CAACU,WAAW,CAACC,SAAS,CAACX,OAAOpC,IAAI,CAAC,CAACU;YAC7C,IAAI,eAAeA,KAAK;gBACtB,OAAO;oBACLsC,MAAMtC,IAAIuC,SAAS;oBACnBC,KAAKxC,IAAIyC,OAAO;oBAChBC,aAAa1C,IAAI2C,MAAM,CAACC,GAAG,CAAC,CAACC,IAAO,CAAA;4BAClC1B,MAAM0B,EAAE1B,IAAI;4BACZ2B,MAAMD,EAAEE,OAAO;wBACjB,CAAA;gBACF;YACF;YACA,MAAM/C,IAAIgD,KAAK;QACjB;IACF;IAEAC,SAASvB,KAAa,EAAiB;QACrC,OAAO,IAAI,CAACU,WAAW,CAACa,QAAQ,CAACvB;IACnC;IAtKAwB,YAAYrC,OAAe,EAAEf,WAAmB,CAAE;QAChD,IAAI,CAACsC,WAAW,GAAGxD,kBAAkBkB;QAErC,IAAI,CAACe,OAAO,GAAGA;QACf,IAAI,CAACf,WAAW,GAAGA;QAEnB,IAAI,CAACmC,mBAAmB,GAAG,IAAIkB,QAAQ,CAACC;YACtC,IAAI3D,OAAO4D,cAAc,EAAE;gBACzBD,QAAQ3D,OAAO4D,cAAc;YAC/B,OAAO;gBACL5D,OAAO6D,iBAAiB,GAAG;oBACzBF,QAAQ3D,OAAO4D,cAAc;gBAC/B;YACF;QACF;IACF;AAwJF;AAhLA,SAAqBpE,wBAgLpB"}