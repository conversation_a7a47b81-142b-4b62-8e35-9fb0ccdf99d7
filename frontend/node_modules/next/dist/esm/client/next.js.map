{"version": 3, "sources": ["../../src/client/next.ts"], "sourcesContent": ["import './webpack'\nimport { initialize, hydrate, version, router, emitter } from './'\n\ndeclare global {\n  interface Window {\n    next: any\n  }\n}\n\nwindow.next = {\n  version,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n}\n\ninitialize({})\n  .then(() => hydrate())\n  .catch(console.error)\n"], "names": ["initialize", "hydrate", "version", "router", "emitter", "window", "next", "then", "catch", "console", "error"], "mappings": "AAAA,OAAO,YAAW;AAClB,SAASA,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,QAAQ,KAAI;AAQlEC,OAAOC,IAAI,GAAG;IACZJ;IACA,0DAA0D;IAC1D,IAAIC,UAAS;QACX,OAAOA;IACT;IACAC;AACF;AAEAJ,WAAW,CAAC,GACTO,IAAI,CAAC,IAAMN,WACXO,KAAK,CAACC,QAAQC,KAAK"}