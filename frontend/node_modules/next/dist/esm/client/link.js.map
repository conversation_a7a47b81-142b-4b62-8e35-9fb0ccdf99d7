{"version": 3, "sources": ["../../src/client/link.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  NextRouter,\n  PrefetchOptions as RouterPrefetchOptions,\n} from '../shared/lib/router/router'\n\nimport React from 'react'\nimport type { UrlObject } from 'url'\nimport { resolveHref } from './resolve-href'\nimport { isLocalURL } from '../shared/lib/router/utils/is-local-url'\nimport { formatUrl } from '../shared/lib/router/utils/format-url'\nimport { isAbsoluteUrl } from '../shared/lib/utils'\nimport { addLocale } from './add-locale'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport type { AppRouterInstance } from '../shared/lib/app-router-context.shared-runtime'\nimport { useIntersection } from './use-intersection'\nimport { getDomainLocale } from './get-domain-locale'\nimport { addBasePath } from './add-base-path'\nimport { useMergedRef } from './use-merged-ref'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype InternalLinkProps = {\n  /**\n   * The path or URL to navigate to. It can also be an object.\n   *\n   * @example https://nextjs.org/docs/api-reference/next/link#with-url-object\n   */\n  href: Url\n  /**\n   * Optional decorator for the path that will be shown in the browser URL bar. Before Next.js 9.5.3 this was used for dynamic routes, check our [previous docs](https://github.com/vercel/next.js/blob/v9.5.2/docs/api-reference/next/link.md#dynamic-routes) to see how it worked. Note: when this path differs from the one provided in `href` the previous `href`/`as` behavior is used as shown in the [previous docs](https://github.com/vercel/next.js/blob/v9.5.2/docs/api-reference/next/link.md#dynamic-routes).\n   */\n  as?: Url\n  /**\n   * Replace the current `history` state instead of adding a new url into the stack.\n   *\n   * @defaultValue `false`\n   */\n  replace?: boolean\n  /**\n   * Whether to override the default scroll behavior\n   *\n   * @example https://nextjs.org/docs/api-reference/next/link#disable-scrolling-to-the-top-of-the-page\n   *\n   * @defaultValue `true`\n   */\n  scroll?: boolean\n  /**\n   * Update the path of the current page without rerunning [`getStaticProps`](https://nextjs.org/docs/pages/building-your-application/data-fetching/get-static-props), [`getServerSideProps`](https://nextjs.org/docs/pages/building-your-application/data-fetching/get-server-side-props) or [`getInitialProps`](/docs/pages/api-reference/functions/get-initial-props).\n   *\n   * @defaultValue `false`\n   */\n  shallow?: boolean\n  /**\n   * Forces `Link` to send the `href` property to its child.\n   *\n   * @defaultValue `false`\n   */\n  passHref?: boolean\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`. Prefetching is only enabled in production.\n   *\n   * In App Router:\n   * - `null` (default): For statically generated pages, this will prefetch the full React Server Component data. For dynamic pages, this will prefetch up to the nearest route segment with a [`loading.js`](https://nextjs.org/docs/app/api-reference/file-conventions/loading) file. If there is no loading file, it will not fetch the full tree to avoid fetching too much data.\n   * - `true`: This will prefetch the full React Server Component data for all route segments, regardless of whether they contain a segment with `loading.js`.\n   * - `false`: This will not prefetch any data, even on hover.\n   *\n   * In Pages Router:\n   * - `true` (default): The full route & its data will be prefetched.\n   * - `false`: Prefetching will not happen when entering the viewport, but will still happen on hover.\n   * @defaultValue `true` (pages router) or `null` (app router)\n   */\n  prefetch?: boolean | null\n  /**\n   * The active locale is automatically prepended. `locale` allows for providing a different locale.\n   * When `false` `href` has to include the locale as the default behavior is disabled.\n   * Note: This is only available in the Pages Router.\n   */\n  locale?: string | false\n  /**\n   * Enable legacy link behavior.\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n  /**\n   * Optional event handler for when the mouse pointer is moved onto Link\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n  /**\n   * Optional event handler for when Link is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n  /**\n   * Optional event handler for when Link is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<InternalLinkProps>\n\nconst prefetched = new Set<string>()\n\ntype PrefetchOptions = RouterPrefetchOptions & {\n  /**\n   * bypassPrefetchedCheck will bypass the check to see if the `href` has\n   * already been fetched.\n   */\n  bypassPrefetchedCheck?: boolean\n}\n\nfunction prefetch(\n  router: NextRouter,\n  href: string,\n  as: string,\n  options: PrefetchOptions\n): void {\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    return\n  }\n\n  // We should only dedupe requests when experimental.optimisticClientCache is\n  // disabled.\n  if (!options.bypassPrefetchedCheck) {\n    const locale =\n      // Let the link's locale prop override the default router locale.\n      typeof options.locale !== 'undefined'\n        ? options.locale\n        : // Otherwise fallback to the router's locale.\n          'locale' in router\n          ? router.locale\n          : undefined\n\n    const prefetchedKey = href + '%' + as + '%' + locale\n\n    // If we've already fetched the key, then don't prefetch it again!\n    if (prefetched.has(prefetchedKey)) {\n      return\n    }\n\n    // Mark this URL as prefetched.\n    prefetched.add(prefetchedKey)\n  }\n\n  // Prefetch the JSON page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  router.prefetch(href, as, options).catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  router: NextRouter | AppRouterInstance,\n  href: string,\n  as: string,\n  replace?: boolean,\n  shallow?: boolean,\n  scroll?: boolean,\n  locale?: string | false\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (isAnchorNodeName && (isModifiedEvent(e) || !isLocalURL(href))) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    // If the router is an NextRouter instance it will have `beforePopState`\n    const routerScroll = scroll ?? true\n    if ('beforePopState' in router) {\n      router[replace ? 'replace' : 'push'](href, as, {\n        shallow,\n        locale,\n        scroll: routerScroll,\n      })\n    } else {\n      router[replace ? 'replace' : 'push'](as || href, {\n        scroll: routerScroll,\n      })\n    }\n  }\n\n  navigate()\n}\n\ntype LinkPropsReal = React.PropsWithChildren<\n  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> &\n    LinkProps\n>\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */\nconst Link = React.forwardRef<HTMLAnchorElement, LinkPropsReal>(\n  function LinkComponent(props, forwardedRef) {\n    let children: React.ReactNode\n\n    const {\n      href: hrefProp,\n      as: asProp,\n      children: childrenProp,\n      prefetch: prefetchProp = null,\n      passHref,\n      replace,\n      shallow,\n      scroll,\n      locale,\n      onClick,\n      onMouseEnter: onMouseEnterProp,\n      onTouchStart: onTouchStartProp,\n      legacyBehavior = false,\n      ...restProps\n    } = props\n\n    children = childrenProp\n\n    if (\n      legacyBehavior &&\n      (typeof children === 'string' || typeof children === 'number')\n    ) {\n      children = <a>{children}</a>\n    }\n\n    const router = React.useContext(RouterContext)\n\n    const prefetchEnabled = prefetchProp !== false\n\n    if (process.env.NODE_ENV !== 'production') {\n      function createPropError(args: {\n        key: string\n        expected: string\n        actual: string\n      }) {\n        return new Error(\n          `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n            (typeof window !== 'undefined'\n              ? // TODO: Remove this addendum if Owner Stacks are available\n                \"\\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n\n      // TypeScript trick for type-guarding:\n      const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n        href: true,\n      } as const\n      const requiredProps: LinkPropsRequired[] = Object.keys(\n        requiredPropsGuard\n      ) as LinkPropsRequired[]\n      requiredProps.forEach((key: LinkPropsRequired) => {\n        if (key === 'href') {\n          if (\n            props[key] == null ||\n            (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n          ) {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: props[key] === null ? 'null' : typeof props[key],\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n\n      // TypeScript trick for type-guarding:\n      const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n        as: true,\n        replace: true,\n        scroll: true,\n        shallow: true,\n        passHref: true,\n        prefetch: true,\n        locale: true,\n        onClick: true,\n        onMouseEnter: true,\n        onTouchStart: true,\n        legacyBehavior: true,\n      } as const\n      const optionalProps: LinkPropsOptional[] = Object.keys(\n        optionalPropsGuard\n      ) as LinkPropsOptional[]\n      optionalProps.forEach((key: LinkPropsOptional) => {\n        const valType = typeof props[key]\n\n        if (key === 'as') {\n          if (props[key] && valType !== 'string' && valType !== 'object') {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: valType,\n            })\n          }\n        } else if (key === 'locale') {\n          if (props[key] && valType !== 'string') {\n            throw createPropError({\n              key,\n              expected: '`string`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'onClick' ||\n          key === 'onMouseEnter' ||\n          key === 'onTouchStart'\n        ) {\n          if (props[key] && valType !== 'function') {\n            throw createPropError({\n              key,\n              expected: '`function`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'replace' ||\n          key === 'scroll' ||\n          key === 'shallow' ||\n          key === 'passHref' ||\n          key === 'prefetch' ||\n          key === 'legacyBehavior'\n        ) {\n          if (props[key] != null && valType !== 'boolean') {\n            throw createPropError({\n              key,\n              expected: '`boolean`',\n              actual: valType,\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n    }\n\n    const { href, as } = React.useMemo(() => {\n      if (!router) {\n        const resolvedHref = formatStringOrUrl(hrefProp)\n        return {\n          href: resolvedHref,\n          as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n        }\n      }\n\n      const [resolvedHref, resolvedAs] = resolveHref(router, hrefProp, true)\n\n      return {\n        href: resolvedHref,\n        as: asProp ? resolveHref(router, asProp) : resolvedAs || resolvedHref,\n      }\n    }, [router, hrefProp, asProp])\n\n    const previousHref = React.useRef<string>(href)\n    const previousAs = React.useRef<string>(as)\n\n    // This will return the first child, if multiple are provided it will throw an error\n    let child: any\n    if (legacyBehavior) {\n      if (process.env.NODE_ENV === 'development') {\n        if (onClick) {\n          console.warn(\n            `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n          )\n        }\n        if (onMouseEnterProp) {\n          console.warn(\n            `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n          )\n        }\n        try {\n          child = React.Children.only(children)\n        } catch (err) {\n          if (!children) {\n            throw new Error(\n              `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n            )\n          }\n          throw new Error(\n            `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n              (typeof window !== 'undefined'\n                ? \" \\nOpen your browser's console to view the Component stack trace.\"\n                : '')\n          )\n        }\n      } else {\n        child = React.Children.only(children)\n      }\n    } else {\n      if (process.env.NODE_ENV === 'development') {\n        if ((children as any)?.type === 'a') {\n          throw new Error(\n            'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n          )\n        }\n      }\n    }\n\n    const childRef: any = legacyBehavior\n      ? child && typeof child === 'object' && child.ref\n      : forwardedRef\n\n    const [setIntersectionRef, isVisible, resetVisible] = useIntersection({\n      rootMargin: '200px',\n    })\n\n    const setIntersectionWithResetRef = React.useCallback(\n      (el: Element | null) => {\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n          resetVisible()\n          previousAs.current = as\n          previousHref.current = href\n        }\n\n        setIntersectionRef(el)\n      },\n      [as, href, resetVisible, setIntersectionRef]\n    )\n\n    const setRef = useMergedRef(setIntersectionWithResetRef, childRef)\n\n    // Prefetch the URL if we haven't already and it's visible.\n    React.useEffect(() => {\n      // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n      if (process.env.NODE_ENV !== 'production') {\n        return\n      }\n\n      if (!router) {\n        return\n      }\n\n      // If we don't need to prefetch the URL, don't do prefetch.\n      if (!isVisible || !prefetchEnabled) {\n        return\n      }\n\n      // Prefetch the URL.\n      prefetch(router, href, as, { locale })\n    }, [as, href, isVisible, locale, prefetchEnabled, router?.locale, router])\n\n    const childProps: {\n      onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n      onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n      onClick: React.MouseEventHandler<HTMLAnchorElement>\n      href?: string\n      ref?: any\n    } = {\n      ref: setRef,\n      onClick(e) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (!e) {\n            throw new Error(\n              `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n            )\n          }\n        }\n\n        if (!legacyBehavior && typeof onClick === 'function') {\n          onClick(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onClick === 'function'\n        ) {\n          child.props.onClick(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (e.defaultPrevented) {\n          return\n        }\n\n        linkClicked(e, router, href, as, replace, shallow, scroll, locale)\n      },\n      onMouseEnter(e) {\n        if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n          onMouseEnterProp(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onMouseEnter === 'function'\n        ) {\n          child.props.onMouseEnter(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        prefetch(router, href, as, {\n          locale,\n          priority: true,\n          // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n          bypassPrefetchedCheck: true,\n        })\n      },\n      onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n        ? undefined\n        : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n              onTouchStartProp(e)\n            }\n\n            if (\n              legacyBehavior &&\n              child.props &&\n              typeof child.props.onTouchStart === 'function'\n            ) {\n              child.props.onTouchStart(e)\n            }\n\n            if (!router) {\n              return\n            }\n\n            prefetch(router, href, as, {\n              locale,\n              priority: true,\n              // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n              bypassPrefetchedCheck: true,\n            })\n          },\n    }\n\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if (isAbsoluteUrl(as)) {\n      childProps.href = as\n    } else if (\n      !legacyBehavior ||\n      passHref ||\n      (child.type === 'a' && !('href' in child.props))\n    ) {\n      const curLocale = typeof locale !== 'undefined' ? locale : router?.locale\n\n      // we only render domain locales if we are currently on a domain locale\n      // so that locale links are still visitable in development/preview envs\n      const localeDomain =\n        router?.isLocaleDomain &&\n        getDomainLocale(as, curLocale, router?.locales, router?.domainLocales)\n\n      childProps.href =\n        localeDomain ||\n        addBasePath(addLocale(as, curLocale, router?.defaultLocale))\n    }\n\n    return legacyBehavior ? (\n      React.cloneElement(child, childProps)\n    ) : (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n)\n\nexport default Link\n"], "names": ["React", "resolveHref", "isLocalURL", "formatUrl", "isAbsoluteUrl", "addLocale", "RouterContext", "useIntersection", "getDomainLocale", "addBasePath", "useMergedRef", "prefetched", "Set", "prefetch", "router", "href", "as", "options", "window", "bypassPrefetchedCheck", "locale", "undefined", "prefetched<PERSON><PERSON>", "has", "add", "catch", "err", "process", "env", "NODE_ENV", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "replace", "shallow", "scroll", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "formatStringOrUrl", "urlObjOrString", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "useContext", "prefetchEnabled", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "useMemo", "resolvedHref", "resolvedAs", "previousHref", "useRef", "previousAs", "child", "console", "warn", "Children", "only", "type", "childRef", "ref", "setIntersectionRef", "isVisible", "resetVisible", "rootMargin", "setIntersectionWithResetRef", "useCallback", "el", "current", "setRef", "useEffect", "childProps", "defaultPrevented", "priority", "__NEXT_LINK_NO_TOUCH_START", "cur<PERSON><PERSON><PERSON>", "localeDomain", "isLocaleDomain", "locales", "domainLocales", "defaultLocale", "cloneElement"], "mappings": "AAAA;;AAOA,OAAOA,WAAW,QAAO;AAEzB,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,UAAU,QAAQ,0CAAyC;AACpE,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SAASC,aAAa,QAAQ,sBAAqB;AACnD,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,aAAa,QAAQ,8CAA6C;AAE3E,SAASC,eAAe,QAAQ,qBAAoB;AACpD,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,YAAY,QAAQ,mBAAkB;AAmG/C,MAAMC,aAAa,IAAIC;AAUvB,SAASC,SACPC,MAAkB,EAClBC,IAAY,EACZC,EAAU,EACVC,OAAwB;IAExB,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IAEA,IAAI,CAAChB,WAAWa,OAAO;QACrB;IACF;IAEA,4EAA4E;IAC5E,YAAY;IACZ,IAAI,CAACE,QAAQE,qBAAqB,EAAE;QAClC,MAAMC,SACJ,iEAAiE;QACjE,OAAOH,QAAQG,MAAM,KAAK,cACtBH,QAAQG,MAAM,GAEd,YAAYN,SACVA,OAAOM,MAAM,GACbC;QAER,MAAMC,gBAAgBP,OAAO,MAAMC,KAAK,MAAMI;QAE9C,kEAAkE;QAClE,IAAIT,WAAWY,GAAG,CAACD,gBAAgB;YACjC;QACF;QAEA,+BAA+B;QAC/BX,WAAWa,GAAG,CAACF;IACjB;IAEA,uDAAuD;IACvD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDR,OAAOD,QAAQ,CAACE,MAAMC,IAAIC,SAASQ,KAAK,CAAC,CAACC;QACxC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMH;QACR;IACF;AACF;AAEA,SAASI,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACE,AAACD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnB7B,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACV4B,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB,EAChB1B,MAAuB;IAEvB,MAAM,EAAE2B,QAAQ,EAAE,GAAGJ,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMe,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IAAID,oBAAqBlB,CAAAA,gBAAgBa,MAAM,CAACzC,WAAWa,KAAI,GAAI;QACjE,8CAA8C;QAC9C;IACF;IAEA4B,EAAEO,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeN,iBAAAA,SAAU;QAC/B,IAAI,oBAAoBhC,QAAQ;YAC9BA,MAAM,CAAC8B,UAAU,YAAY,OAAO,CAAC7B,MAAMC,IAAI;gBAC7C6B;gBACAzB;gBACA0B,QAAQM;YACV;QACF,OAAO;YACLtC,MAAM,CAAC8B,UAAU,YAAY,OAAO,CAAC5B,MAAMD,MAAM;gBAC/C+B,QAAQM;YACV;QACF;IACF;IAEAD;AACF;AAOA,SAASE,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOnD,UAAUmD;AACnB;AAEA;;;;;;;CAOC,GACD,MAAMC,qBAAOvD,MAAMwD,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJ7C,MAAM8C,QAAQ,EACd7C,IAAI8C,MAAM,EACVF,UAAUG,YAAY,EACtBlD,UAAUmD,eAAe,IAAI,EAC7BC,QAAQ,EACRrB,OAAO,EACPC,OAAO,EACPC,MAAM,EACN1B,MAAM,EACN8C,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,KAACa;sBAAGb;;IACjB;IAEA,MAAM9C,SAASd,MAAM0E,UAAU,CAACpE;IAEhC,MAAMqE,kBAAkBX,iBAAiB;IAEzC,IAAIrC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAAS+C,gBAAgBC,IAIxB;YACC,OAAO,qBAMN,CANM,IAAIC,MACT,AAAC,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAO/D,WAAW,cAEf,qEACA,EAAC,IALF,qBAAA;uBAAA;4BAAA;8BAAA;YAMP;QACF;QAEA,sCAAsC;QACtC,MAAMgE,qBAAsD;YAC1DnE,MAAM;QACR;QACA,MAAMoE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACP;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACErB,KAAK,CAACqB,IAAI,IAAI,QACb,OAAOrB,KAAK,CAACqB,IAAI,KAAK,YAAY,OAAOrB,KAAK,CAACqB,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQvB,KAAK,CAACqB,IAAI,KAAK,OAAO,SAAS,OAAOrB,KAAK,CAACqB,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMQ,IAAWR;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMS,qBAAsD;YAC1DxE,IAAI;YACJ4B,SAAS;YACTE,QAAQ;YACRD,SAAS;YACToB,UAAU;YACVpD,UAAU;YACVO,QAAQ;YACR8C,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAMkB,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACP;YACrB,MAAMW,UAAU,OAAOhC,KAAK,CAACqB,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIrB,KAAK,CAACqB,IAAI,IAAIW,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IAAIX,QAAQ,UAAU;gBAC3B,IAAIrB,KAAK,CAACqB,IAAI,IAAIW,YAAY,UAAU;oBACtC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAIrB,KAAK,CAACqB,IAAI,IAAIW,YAAY,YAAY;oBACxC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAIrB,KAAK,CAACqB,IAAI,IAAI,QAAQW,YAAY,WAAW;oBAC/C,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWR;YACnB;QACF;IACF;IAEA,MAAM,EAAEhE,IAAI,EAAEC,EAAE,EAAE,GAAGhB,MAAM2F,OAAO,CAAC;QACjC,IAAI,CAAC7E,QAAQ;YACX,MAAM8E,eAAevC,kBAAkBQ;YACvC,OAAO;gBACL9C,MAAM6E;gBACN5E,IAAI8C,SAAST,kBAAkBS,UAAU8B;YAC3C;QACF;QAEA,MAAM,CAACA,cAAcC,WAAW,GAAG5F,YAAYa,QAAQ+C,UAAU;QAEjE,OAAO;YACL9C,MAAM6E;YACN5E,IAAI8C,SAAS7D,YAAYa,QAAQgD,UAAU+B,cAAcD;QAC3D;IACF,GAAG;QAAC9E;QAAQ+C;QAAUC;KAAO;IAE7B,MAAMgC,eAAe9F,MAAM+F,MAAM,CAAShF;IAC1C,MAAMiF,aAAahG,MAAM+F,MAAM,CAAS/E;IAExC,oFAAoF;IACpF,IAAIiF;IACJ,IAAI1B,gBAAgB;QAClB,IAAI5C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAIqC,SAAS;gBACXgC,QAAQC,IAAI,CACV,AAAC,oDAAoDtC,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpB8B,QAAQC,IAAI,CACV,AAAC,yDAAyDtC,WAAS;YAEvE;YACA,IAAI;gBACFoC,QAAQjG,MAAMoG,QAAQ,CAACC,IAAI,CAACzC;YAC9B,EAAE,OAAOlC,KAAK;gBACZ,IAAI,CAACkC,UAAU;oBACb,MAAM,qBAEL,CAFK,IAAIkB,MACR,AAAC,uDAAuDjB,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,qBAKL,CALK,IAAIiB,MACR,AAAC,6DAA6DjB,WAAS,8FACpE,CAAA,OAAO3C,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;YACL+E,QAAQjG,MAAMoG,QAAQ,CAACC,IAAI,CAACzC;QAC9B;IACF,OAAO;QACL,IAAIjC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAAC+B,4BAAD,AAACA,SAAkB0C,IAAI,MAAK,KAAK;gBACnC,MAAM,qBAEL,CAFK,IAAIxB,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMyB,WAAgBhC,iBAClB0B,SAAS,OAAOA,UAAU,YAAYA,MAAMO,GAAG,GAC/C7C;IAEJ,MAAM,CAAC8C,oBAAoBC,WAAWC,aAAa,GAAGpG,gBAAgB;QACpEqG,YAAY;IACd;IAEA,MAAMC,8BAA8B7G,MAAM8G,WAAW,CACnD,CAACC;QACC,4EAA4E;QAC5E,IAAIf,WAAWgB,OAAO,KAAKhG,MAAM8E,aAAakB,OAAO,KAAKjG,MAAM;YAC9D4F;YACAX,WAAWgB,OAAO,GAAGhG;YACrB8E,aAAakB,OAAO,GAAGjG;QACzB;QAEA0F,mBAAmBM;IACrB,GACA;QAAC/F;QAAID;QAAM4F;QAAcF;KAAmB;IAG9C,MAAMQ,SAASvG,aAAamG,6BAA6BN;IAEzD,2DAA2D;IAC3DvG,MAAMkH,SAAS,CAAC;QACd,gHAAgH;QAChH,IAAIvF,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,CAACf,QAAQ;YACX;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAAC4F,aAAa,CAAC/B,iBAAiB;YAClC;QACF;QAEA,oBAAoB;QACpB9D,SAASC,QAAQC,MAAMC,IAAI;YAAEI;QAAO;IACtC,GAAG;QAACJ;QAAID;QAAM2F;QAAWtF;QAAQuD;QAAiB7D,0BAAAA,OAAQM,MAAM;QAAEN;KAAO;IAEzE,MAAMqG,aAMF;QACFX,KAAKS;QACL/C,SAAQvB,CAAC;YACP,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAACc,GAAG;oBACN,MAAM,qBAEL,CAFK,IAAImC,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACP,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQvB;YACV;YAEA,IACE4B,kBACA0B,MAAMvC,KAAK,IACX,OAAOuC,MAAMvC,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACA+B,MAAMvC,KAAK,CAACQ,OAAO,CAACvB;YACtB;YAEA,IAAI,CAAC7B,QAAQ;gBACX;YACF;YAEA,IAAI6B,EAAEyE,gBAAgB,EAAE;gBACtB;YACF;YAEA1E,YAAYC,GAAG7B,QAAQC,MAAMC,IAAI4B,SAASC,SAASC,QAAQ1B;QAC7D;QACA+C,cAAaxB,CAAC;YACZ,IAAI,CAAC4B,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBzB;YACnB;YAEA,IACE4B,kBACA0B,MAAMvC,KAAK,IACX,OAAOuC,MAAMvC,KAAK,CAACS,YAAY,KAAK,YACpC;gBACA8B,MAAMvC,KAAK,CAACS,YAAY,CAACxB;YAC3B;YAEA,IAAI,CAAC7B,QAAQ;gBACX;YACF;YAEAD,SAASC,QAAQC,MAAMC,IAAI;gBACzBI;gBACAiG,UAAU;gBACV,gGAAgG;gBAChGlG,uBAAuB;YACzB;QACF;QACAkD,cAAc1C,QAAQC,GAAG,CAAC0F,0BAA0B,GAChDjG,YACA,SAASgD,aAAa1B,CAAC;YACrB,IAAI,CAAC4B,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiB3B;YACnB;YAEA,IACE4B,kBACA0B,MAAMvC,KAAK,IACX,OAAOuC,MAAMvC,KAAK,CAACW,YAAY,KAAK,YACpC;gBACA4B,MAAMvC,KAAK,CAACW,YAAY,CAAC1B;YAC3B;YAEA,IAAI,CAAC7B,QAAQ;gBACX;YACF;YAEAD,SAASC,QAAQC,MAAMC,IAAI;gBACzBI;gBACAiG,UAAU;gBACV,gGAAgG;gBAChGlG,uBAAuB;YACzB;QACF;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,oFAAoF;IACpF,IAAIf,cAAcY,KAAK;QACrBmG,WAAWpG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACuD,kBACDN,YACCgC,MAAMK,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUL,MAAMvC,KAAK,AAAD,GAC7C;QACA,MAAM6D,YAAY,OAAOnG,WAAW,cAAcA,SAASN,0BAAAA,OAAQM,MAAM;QAEzE,uEAAuE;QACvE,uEAAuE;QACvE,MAAMoG,eACJ1G,CAAAA,0BAAAA,OAAQ2G,cAAc,KACtBjH,gBAAgBQ,IAAIuG,WAAWzG,0BAAAA,OAAQ4G,OAAO,EAAE5G,0BAAAA,OAAQ6G,aAAa;QAEvER,WAAWpG,IAAI,GACbyG,gBACA/G,YAAYJ,UAAUW,IAAIuG,WAAWzG,0BAAAA,OAAQ8G,aAAa;IAC9D;IAEA,OAAOrD,+BACLvE,MAAM6H,YAAY,CAAC5B,OAAOkB,4BAE1B,KAAC1C;QAAG,GAAGD,SAAS;QAAG,GAAG2C,UAAU;kBAC7BvD;;AAGP;AAGF,eAAeL,KAAI"}