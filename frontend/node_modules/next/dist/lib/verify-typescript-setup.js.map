{"version": 3, "sources": ["../../src/lib/verify-typescript-setup.ts"], "sourcesContent": ["import { bold, cyan, red, yellow } from './picocolors'\nimport path from 'path'\n\nimport { hasNecessaryDependencies } from './has-necessary-dependencies'\nimport type { NecessaryDependencies } from './has-necessary-dependencies'\nimport semver from 'next/dist/compiled/semver'\nimport { CompileError } from './compile-error'\nimport * as log from '../build/output/log'\n\nimport { getTypeScriptIntent } from './typescript/getTypeScriptIntent'\nimport type { TypeCheckResult } from './typescript/runTypeCheck'\nimport { writeAppTypeDeclarations } from './typescript/writeAppTypeDeclarations'\nimport { writeConfigurationDefaults } from './typescript/writeConfigurationDefaults'\nimport { installDependencies } from './install-dependencies'\nimport { isCI } from '../server/ci-info'\nimport { missingDepsError } from './typescript/missingDependencyError'\n\nconst requiredPackages = [\n  {\n    file: 'typescript/lib/typescript.js',\n    pkg: 'typescript',\n    exportsRestrict: true,\n  },\n  {\n    file: '@types/react/index.d.ts',\n    pkg: '@types/react',\n    exportsRestrict: true,\n  },\n  {\n    file: '@types/node/index.d.ts',\n    pkg: '@types/node',\n    exportsRestrict: true,\n  },\n]\n\nexport async function verifyTypeScriptSetup({\n  dir,\n  distDir,\n  cacheDir,\n  intentDirs,\n  tsconfigPath,\n  typeCheckPreflight,\n  disableStaticImages,\n  hasAppDir,\n  hasPagesDir,\n}: {\n  dir: string\n  distDir: string\n  cacheDir?: string\n  tsconfigPath: string\n  intentDirs: string[]\n  typeCheckPreflight: boolean\n  disableStaticImages: boolean\n  hasAppDir: boolean\n  hasPagesDir: boolean\n}): Promise<{ result?: TypeCheckResult; version: string | null }> {\n  const resolvedTsConfigPath = path.join(dir, tsconfigPath)\n\n  try {\n    // Check if the project uses TypeScript:\n    const intent = await getTypeScriptIntent(dir, intentDirs, tsconfigPath)\n    if (!intent) {\n      return { version: null }\n    }\n\n    // Ensure TypeScript and necessary `@types/*` are installed:\n    let deps: NecessaryDependencies = await hasNecessaryDependencies(\n      dir,\n      requiredPackages\n    )\n\n    if (deps.missing?.length > 0) {\n      if (isCI) {\n        // we don't attempt auto install in CI to avoid side-effects\n        // and instead log the error for installing needed packages\n        missingDepsError(dir, deps.missing)\n      }\n      console.log(\n        bold(\n          yellow(\n            `It looks like you're trying to use TypeScript but do not have the required package(s) installed.`\n          )\n        ) +\n          '\\n' +\n          'Installing dependencies' +\n          '\\n\\n' +\n          bold(\n            'If you are not trying to use TypeScript, please remove the ' +\n              cyan('tsconfig.json') +\n              ' file from your package root (and any TypeScript files in your app and pages directories).'\n          ) +\n          '\\n'\n      )\n      await installDependencies(dir, deps.missing, true).catch((err) => {\n        if (err && typeof err === 'object' && 'command' in err) {\n          console.error(\n            `Failed to install required TypeScript dependencies, please install them manually to continue:\\n` +\n              (err as any).command +\n              '\\n'\n          )\n        }\n        throw err\n      })\n      deps = await hasNecessaryDependencies(dir, requiredPackages)\n    }\n\n    // Load TypeScript after we're sure it exists:\n    const tsPath = deps.resolved.get('typescript')!\n    const ts = (await Promise.resolve(\n      require(tsPath)\n    )) as typeof import('typescript')\n\n    if (semver.lt(ts.version, '4.5.2')) {\n      log.warn(\n        `Minimum recommended TypeScript version is v4.5.2, older versions can potentially be incompatible with Next.js. Detected: ${ts.version}`\n      )\n    }\n\n    // Reconfigure (or create) the user's `tsconfig.json` for them:\n    await writeConfigurationDefaults(\n      ts,\n      resolvedTsConfigPath,\n      intent.firstTimeSetup,\n      hasAppDir,\n      distDir,\n      hasPagesDir\n    )\n    // Write out the necessary `next-env.d.ts` file to correctly register\n    // Next.js' types:\n    await writeAppTypeDeclarations({\n      baseDir: dir,\n      imageImportsEnabled: !disableStaticImages,\n      hasPagesDir,\n      hasAppDir,\n    })\n\n    let result\n    if (typeCheckPreflight) {\n      const { runTypeCheck } = require('./typescript/runTypeCheck')\n\n      // Verify the project passes type-checking before we go to webpack phase:\n      result = await runTypeCheck(\n        ts,\n        dir,\n        distDir,\n        resolvedTsConfigPath,\n        cacheDir,\n        hasAppDir\n      )\n    }\n    return { result, version: ts.version }\n  } catch (err) {\n    // These are special errors that should not show a stack trace:\n    if (err instanceof CompileError) {\n      console.error(red('Failed to compile.\\n'))\n      console.error(err.message)\n      process.exit(1)\n    }\n\n    /**\n     * verifyTypeScriptSetup can be either invoked directly in the main thread (during next dev / next lint)\n     * or run in a worker (during next build). In the latter case, we need to print the error message, as the\n     * parent process will only receive an `Jest worker encountered 1 child process exceptions, exceeding retry limit`.\n     */\n\n    // we are in a worker, print the error message and exit the process\n    if (process.env.IS_NEXT_WORKER) {\n      if (err instanceof Error) {\n        console.error(err.message)\n      } else {\n        console.error(err)\n      }\n      process.exit(1)\n    }\n    // we are in the main thread, throw the error and it will be handled by the caller\n    throw err\n  }\n}\n"], "names": ["verifyTypeScriptSetup", "requiredPackages", "file", "pkg", "exportsRestrict", "dir", "distDir", "cacheDir", "intentDirs", "tsconfigPath", "typeCheckPreflight", "disableStaticImages", "hasAppDir", "hasPagesDir", "resolvedTsConfigPath", "path", "join", "deps", "intent", "getTypeScriptIntent", "version", "hasNecessaryDependencies", "missing", "length", "isCI", "missingDepsError", "console", "log", "bold", "yellow", "cyan", "installDependencies", "catch", "err", "error", "command", "tsPath", "resolved", "get", "ts", "Promise", "resolve", "require", "semver", "lt", "warn", "writeConfigurationDefaults", "firstTimeSetup", "writeAppTypeDeclarations", "baseDir", "imageImportsEnabled", "result", "runTypeCheck", "CompileError", "red", "message", "process", "exit", "env", "IS_NEXT_WORKER", "Error"], "mappings": ";;;;+BAmCsBA;;;eAAAA;;;4BAnCkB;6DACvB;0CAEwB;+DAEtB;8BACU;6DACR;qCAEe;0CAEK;4CACE;qCACP;wBACf;wCACY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,MAAMC,mBAAmB;IACvB;QACEC,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;CACD;AAEM,eAAeJ,sBAAsB,EAC1CK,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,SAAS,EACTC,WAAW,EAWZ;IACC,MAAMC,uBAAuBC,aAAI,CAACC,IAAI,CAACX,KAAKI;IAE5C,IAAI;YAaEQ;QAZJ,wCAAwC;QACxC,MAAMC,SAAS,MAAMC,IAAAA,wCAAmB,EAACd,KAAKG,YAAYC;QAC1D,IAAI,CAACS,QAAQ;YACX,OAAO;gBAAEE,SAAS;YAAK;QACzB;QAEA,4DAA4D;QAC5D,IAAIH,OAA8B,MAAMI,IAAAA,kDAAwB,EAC9DhB,KACAJ;QAGF,IAAIgB,EAAAA,gBAAAA,KAAKK,OAAO,qBAAZL,cAAcM,MAAM,IAAG,GAAG;YAC5B,IAAIC,YAAI,EAAE;gBACR,4DAA4D;gBAC5D,2DAA2D;gBAC3DC,IAAAA,wCAAgB,EAACpB,KAAKY,KAAKK,OAAO;YACpC;YACAI,QAAQC,GAAG,CACTC,IAAAA,gBAAI,EACFC,IAAAA,kBAAM,EACJ,CAAC,gGAAgG,CAAC,KAGpG,OACA,4BACA,SACAD,IAAAA,gBAAI,EACF,gEACEE,IAAAA,gBAAI,EAAC,mBACL,gGAEJ;YAEJ,MAAMC,IAAAA,wCAAmB,EAAC1B,KAAKY,KAAKK,OAAO,EAAE,MAAMU,KAAK,CAAC,CAACC;gBACxD,IAAIA,OAAO,OAAOA,QAAQ,YAAY,aAAaA,KAAK;oBACtDP,QAAQQ,KAAK,CACX,CAAC,+FAA+F,CAAC,GAC/F,AAACD,IAAYE,OAAO,GACpB;gBAEN;gBACA,MAAMF;YACR;YACAhB,OAAO,MAAMI,IAAAA,kDAAwB,EAAChB,KAAKJ;QAC7C;QAEA,8CAA8C;QAC9C,MAAMmC,SAASnB,KAAKoB,QAAQ,CAACC,GAAG,CAAC;QACjC,MAAMC,KAAM,MAAMC,QAAQC,OAAO,CAC/BC,QAAQN;QAGV,IAAIO,eAAM,CAACC,EAAE,CAACL,GAAGnB,OAAO,EAAE,UAAU;YAClCO,KAAIkB,IAAI,CACN,CAAC,yHAAyH,EAAEN,GAAGnB,OAAO,EAAE;QAE5I;QAEA,+DAA+D;QAC/D,MAAM0B,IAAAA,sDAA0B,EAC9BP,IACAzB,sBACAI,OAAO6B,cAAc,EACrBnC,WACAN,SACAO;QAEF,qEAAqE;QACrE,kBAAkB;QAClB,MAAMmC,IAAAA,kDAAwB,EAAC;YAC7BC,SAAS5C;YACT6C,qBAAqB,CAACvC;YACtBE;YACAD;QACF;QAEA,IAAIuC;QACJ,IAAIzC,oBAAoB;YACtB,MAAM,EAAE0C,YAAY,EAAE,GAAGV,QAAQ;YAEjC,yEAAyE;YACzES,SAAS,MAAMC,aACbb,IACAlC,KACAC,SACAQ,sBACAP,UACAK;QAEJ;QACA,OAAO;YAAEuC;YAAQ/B,SAASmB,GAAGnB,OAAO;QAAC;IACvC,EAAE,OAAOa,KAAK;QACZ,+DAA+D;QAC/D,IAAIA,eAAeoB,0BAAY,EAAE;YAC/B3B,QAAQQ,KAAK,CAACoB,IAAAA,eAAG,EAAC;YAClB5B,QAAQQ,KAAK,CAACD,IAAIsB,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QAEA;;;;KAIC,GAED,mEAAmE;QACnE,IAAID,QAAQE,GAAG,CAACC,cAAc,EAAE;YAC9B,IAAI1B,eAAe2B,OAAO;gBACxBlC,QAAQQ,KAAK,CAACD,IAAIsB,OAAO;YAC3B,OAAO;gBACL7B,QAAQQ,KAAK,CAACD;YAChB;YACAuB,QAAQC,IAAI,CAAC;QACf;QACA,kFAAkF;QAClF,MAAMxB;IACR;AACF"}