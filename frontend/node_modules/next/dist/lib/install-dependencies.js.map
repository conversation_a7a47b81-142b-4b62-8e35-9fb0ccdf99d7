{"version": 3, "sources": ["../../src/lib/install-dependencies.ts"], "sourcesContent": ["import { cyan } from './picocolors'\nimport path from 'path'\n\nimport type { MissingDependency } from './has-necessary-dependencies'\nimport { getPkgManager } from './helpers/get-pkg-manager'\nimport { install } from './helpers/install'\nimport { getOnline } from './helpers/get-online'\n\nexport type Dependencies = {\n  resolved: Map<string, string>\n}\n\nexport async function installDependencies(\n  baseDir: string,\n  deps: any,\n  dev: boolean = false\n) {\n  const packageManager = getPkgManager(baseDir)\n  const isOnline = await getOnline()\n\n  if (deps.length) {\n    console.log()\n    console.log(\n      `Installing ${\n        dev ? 'devDependencies' : 'dependencies'\n      } (${packageManager}):`\n    )\n    for (const dep of deps) {\n      console.log(`- ${cyan(dep.pkg)}`)\n    }\n    console.log()\n\n    await install(\n      path.resolve(baseDir),\n      deps.map((dep: MissingDependency) => dep.pkg),\n      { devDependencies: dev, isOnline, packageManager }\n    )\n    console.log()\n  }\n}\n"], "names": ["installDependencies", "baseDir", "deps", "dev", "packageManager", "getPkgManager", "isOnline", "getOnline", "length", "console", "log", "dep", "cyan", "pkg", "install", "path", "resolve", "map", "devDependencies"], "mappings": ";;;;+BAYsBA;;;eAAAA;;;4BAZD;6DACJ;+BAGa;yBACN;2BACE;;;;;;AAMnB,eAAeA,oBACpBC,OAAe,EACfC,IAAS,EACTC,MAAe,KAAK;IAEpB,MAAMC,iBAAiBC,IAAAA,4BAAa,EAACJ;IACrC,MAAMK,WAAW,MAAMC,IAAAA,oBAAS;IAEhC,IAAIL,KAAKM,MAAM,EAAE;QACfC,QAAQC,GAAG;QACXD,QAAQC,GAAG,CACT,CAAC,WAAW,EACVP,MAAM,oBAAoB,eAC3B,EAAE,EAAEC,eAAe,EAAE,CAAC;QAEzB,KAAK,MAAMO,OAAOT,KAAM;YACtBO,QAAQC,GAAG,CAAC,CAAC,EAAE,EAAEE,IAAAA,gBAAI,EAACD,IAAIE,GAAG,GAAG;QAClC;QACAJ,QAAQC,GAAG;QAEX,MAAMI,IAAAA,gBAAO,EACXC,aAAI,CAACC,OAAO,CAACf,UACbC,KAAKe,GAAG,CAAC,CAACN,MAA2BA,IAAIE,GAAG,GAC5C;YAAEK,iBAAiBf;YAAKG;YAAUF;QAAe;QAEnDK,QAAQC,GAAG;IACb;AACF"}