{"version": 3, "sources": ["../../src/lib/find-pages-dir.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\n\nexport function findDir(dir: string, name: 'pages' | 'app'): string | null {\n  // prioritize ./${name} over ./src/${name}\n  let curDir = path.join(dir, name)\n  if (fs.existsSync(curDir)) return curDir\n\n  curDir = path.join(dir, 'src', name)\n  if (fs.existsSync(curDir)) return curDir\n\n  return null\n}\n\nexport function findPagesDir(dir: string): {\n  pagesDir: string | undefined\n  appDir: string | undefined\n} {\n  const pagesDir = findDir(dir, 'pages') || undefined\n  const appDir = findDir(dir, 'app') || undefined\n\n  if (appDir == null && pagesDir == null) {\n    throw new Error(\n      \"> Couldn't find any `pages` or `app` directory. Please create one under the project root\"\n    )\n  }\n\n  return {\n    pagesDir,\n    appDir,\n  }\n}\n"], "names": ["findDir", "findPagesDir", "dir", "name", "curDir", "path", "join", "fs", "existsSync", "pagesDir", "undefined", "appDir", "Error"], "mappings": ";;;;;;;;;;;;;;;IAGgBA,OAAO;eAAPA;;IAWAC,YAAY;eAAZA;;;2DAdD;6DACE;;;;;;AAEV,SAASD,QAAQE,GAAW,EAAEC,IAAqB;IACxD,0CAA0C;IAC1C,IAAIC,SAASC,aAAI,CAACC,IAAI,CAACJ,KAAKC;IAC5B,IAAII,WAAE,CAACC,UAAU,CAACJ,SAAS,OAAOA;IAElCA,SAASC,aAAI,CAACC,IAAI,CAACJ,KAAK,OAAOC;IAC/B,IAAII,WAAE,CAACC,UAAU,CAACJ,SAAS,OAAOA;IAElC,OAAO;AACT;AAEO,SAASH,aAAaC,GAAW;IAItC,MAAMO,WAAWT,QAAQE,KAAK,YAAYQ;IAC1C,MAAMC,SAASX,QAAQE,KAAK,UAAUQ;IAEtC,IAAIC,UAAU,QAAQF,YAAY,MAAM;QACtC,MAAM,qBAEL,CAFK,IAAIG,MACR,6FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAO;QACLH;QACAE;IACF;AACF"}