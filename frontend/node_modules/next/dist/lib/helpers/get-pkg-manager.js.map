{"version": 3, "sources": ["../../../src/lib/helpers/get-pkg-manager.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport { execSync } from 'child_process'\n\nexport type PackageManager = 'npm' | 'pnpm' | 'yarn'\n\nexport function getPkgManager(baseDir: string): PackageManager {\n  try {\n    for (const { lockFile, packageManager } of [\n      { lockFile: 'yarn.lock', packageManager: 'yarn' },\n      { lockFile: 'pnpm-lock.yaml', packageManager: 'pnpm' },\n      { lockFile: 'package-lock.json', packageManager: 'npm' },\n    ]) {\n      if (fs.existsSync(path.join(baseDir, lockFile))) {\n        return packageManager as PackageManager\n      }\n    }\n    const userAgent = process.env.npm_config_user_agent\n    if (userAgent) {\n      if (userAgent.startsWith('yarn')) {\n        return 'yarn'\n      } else if (userAgent.startsWith('pnpm')) {\n        return 'pnpm'\n      }\n    }\n    try {\n      execSync('yarn --version', { stdio: 'ignore' })\n      return 'yarn'\n    } catch {\n      execSync('pnpm --version', { stdio: 'ignore' })\n      return 'pnpm'\n    }\n  } catch {\n    return 'npm'\n  }\n}\n"], "names": ["getPkgManager", "baseDir", "lockFile", "packageManager", "fs", "existsSync", "path", "join", "userAgent", "process", "env", "npm_config_user_agent", "startsWith", "execSync", "stdio"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;2DAND;6DACE;+BACQ;;;;;;AAIlB,SAASA,cAAcC,OAAe;IAC3C,IAAI;QACF,KAAK,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAE,IAAI;YACzC;gBAAED,UAAU;gBAAaC,gBAAgB;YAAO;YAChD;gBAAED,UAAU;gBAAkBC,gBAAgB;YAAO;YACrD;gBAAED,UAAU;gBAAqBC,gBAAgB;YAAM;SACxD,CAAE;YACD,IAAIC,WAAE,CAACC,UAAU,CAACC,aAAI,CAACC,IAAI,CAACN,SAASC,YAAY;gBAC/C,OAAOC;YACT;QACF;QACA,MAAMK,YAAYC,QAAQC,GAAG,CAACC,qBAAqB;QACnD,IAAIH,WAAW;YACb,IAAIA,UAAUI,UAAU,CAAC,SAAS;gBAChC,OAAO;YACT,OAAO,IAAIJ,UAAUI,UAAU,CAAC,SAAS;gBACvC,OAAO;YACT;QACF;QACA,IAAI;YACFC,IAAAA,uBAAQ,EAAC,kBAAkB;gBAAEC,OAAO;YAAS;YAC7C,OAAO;QACT,EAAE,OAAM;YACND,IAAAA,uBAAQ,EAAC,kBAAkB;gBAAEC,OAAO;YAAS;YAC7C,OAAO;QACT;IACF,EAAE,OAAM;QACN,OAAO;IACT;AACF"}