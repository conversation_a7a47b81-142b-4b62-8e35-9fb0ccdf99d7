{"version": 3, "sources": ["../../src/lib/has-necessary-dependencies.ts"], "sourcesContent": ["import { existsSync, promises as fs } from 'fs'\nimport { resolveFrom } from './resolve-from'\nimport { dirname, join, relative } from 'path'\n\nexport interface MissingDependency {\n  file: string\n  /**\n   * The package's package.json (e.g. require(`${pkg}/package.json`)) MUST resolve.\n   * If `exportsRestrict` is false, `${file}` MUST also resolve.\n   */\n  pkg: string\n  /**\n   * If true, the pkg's package.json needs to be resolvable.\n   * If true, will resolve `file` relative to the real path of the package.json.\n   *\n   * For example, `{ file: '@types/react/index.d.ts', pkg: '@types/react', exportsRestrict: true }`\n   * will try to resolve '@types/react/package.json' first and then assume `@types/react/index.d.ts`\n   * resolves to `path.join(dirname(resolvedPackageJsonPath), 'index.d.ts')`.\n   *\n   * If false, will resolve `file` relative to the baseDir.\n   * ForFor example, `{ file: '@types/react/index.d.ts', pkg: '@types/react', exportsRestrict: true }`\n   * will try to resolve `@types/react/index.d.ts` directly.\n   */\n  exportsRestrict: boolean\n}\n\nexport type NecessaryDependencies = {\n  resolved: Map<string, string>\n  missing: MissingDependency[]\n}\n\nexport async function hasNecessaryDependencies(\n  baseDir: string,\n  requiredPackages: MissingDependency[]\n): Promise<NecessaryDependencies> {\n  let resolutions = new Map<string, string>()\n  const missingPackages: MissingDependency[] = []\n\n  await Promise.all(\n    requiredPackages.map(async (p) => {\n      try {\n        const pkgPath = await fs.realpath(\n          resolveFrom(baseDir, `${p.pkg}/package.json`)\n        )\n        const pkgDir = dirname(pkgPath)\n\n        if (p.exportsRestrict) {\n          const fileNameToVerify = relative(p.pkg, p.file)\n          if (fileNameToVerify) {\n            const fileToVerify = join(pkgDir, fileNameToVerify)\n            if (existsSync(fileToVerify)) {\n              resolutions.set(p.pkg, fileToVerify)\n            } else {\n              return missingPackages.push(p)\n            }\n          } else {\n            resolutions.set(p.pkg, pkgPath)\n          }\n        } else {\n          resolutions.set(p.pkg, resolveFrom(baseDir, p.file))\n        }\n      } catch (_) {\n        return missingPackages.push(p)\n      }\n    })\n  )\n\n  return {\n    resolved: resolutions,\n    missing: missingPackages,\n  }\n}\n"], "names": ["hasNecessaryDependencies", "baseDir", "requiredPackages", "resolutions", "Map", "missingPackages", "Promise", "all", "map", "p", "pkgPath", "fs", "realpath", "resolveFrom", "pkg", "pkgDir", "dirname", "exportsRestrict", "fileNameToVerify", "relative", "file", "fileToVerify", "join", "existsSync", "set", "push", "_", "resolved", "missing"], "mappings": ";;;;+BA+BsBA;;;eAAAA;;;oBA/BqB;6BACf;sBACY;AA6BjC,eAAeA,yBACpBC,OAAe,EACfC,gBAAqC;IAErC,IAAIC,cAAc,IAAIC;IACtB,MAAMC,kBAAuC,EAAE;IAE/C,MAAMC,QAAQC,GAAG,CACfL,iBAAiBM,GAAG,CAAC,OAAOC;QAC1B,IAAI;YACF,MAAMC,UAAU,MAAMC,YAAE,CAACC,QAAQ,CAC/BC,IAAAA,wBAAW,EAACZ,SAAS,GAAGQ,EAAEK,GAAG,CAAC,aAAa,CAAC;YAE9C,MAAMC,SAASC,IAAAA,aAAO,EAACN;YAEvB,IAAID,EAAEQ,eAAe,EAAE;gBACrB,MAAMC,mBAAmBC,IAAAA,cAAQ,EAACV,EAAEK,GAAG,EAAEL,EAAEW,IAAI;gBAC/C,IAAIF,kBAAkB;oBACpB,MAAMG,eAAeC,IAAAA,UAAI,EAACP,QAAQG;oBAClC,IAAIK,IAAAA,cAAU,EAACF,eAAe;wBAC5BlB,YAAYqB,GAAG,CAACf,EAAEK,GAAG,EAAEO;oBACzB,OAAO;wBACL,OAAOhB,gBAAgBoB,IAAI,CAAChB;oBAC9B;gBACF,OAAO;oBACLN,YAAYqB,GAAG,CAACf,EAAEK,GAAG,EAAEJ;gBACzB;YACF,OAAO;gBACLP,YAAYqB,GAAG,CAACf,EAAEK,GAAG,EAAED,IAAAA,wBAAW,EAACZ,SAASQ,EAAEW,IAAI;YACpD;QACF,EAAE,OAAOM,GAAG;YACV,OAAOrB,gBAAgBoB,IAAI,CAAChB;QAC9B;IACF;IAGF,OAAO;QACLkB,UAAUxB;QACVyB,SAASvB;IACX;AACF"}