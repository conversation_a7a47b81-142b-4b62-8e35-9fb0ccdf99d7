{"version": 3, "sources": ["../../../../src/lib/metadata/types/twitter-types.ts"], "sourcesContent": ["// Reference: https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/markup\n\nimport type { AbsoluteTemplateString, TemplateString } from './metadata-types'\n\nexport type Twitter =\n  | TwitterSummary\n  | TwitterSummaryLargeImage\n  | TwitterPlayer\n  | TwitterApp\n  | TwitterMetadata\n\ntype TwitterMetadata = {\n  // defaults to card=\"summary\"\n  site?: string | undefined // username for account associated to the site itself\n  siteId?: string | undefined // id for account associated to the site itself\n  creator?: string | undefined // username for the account associated to the creator of the content on the site\n  creatorId?: string | undefined // id for the account associated to the creator of the content on the site\n  description?: string | undefined\n  title?: string | TemplateString | undefined\n  images?: TwitterImage | Array<TwitterImage> | undefined\n}\ntype TwitterSummary = TwitterMetadata & {\n  card: 'summary'\n}\ntype TwitterSummaryLargeImage = TwitterMetadata & {\n  card: 'summary_large_image'\n}\ntype TwitterPlayer = TwitterMetadata & {\n  card: 'player'\n  players: TwitterPlayerDescriptor | Array<TwitterPlayerDescriptor>\n}\ntype TwitterApp = TwitterMetadata & {\n  card: 'app'\n  app: TwitterAppDescriptor\n}\nexport type TwitterAppDescriptor = {\n  id: {\n    iphone?: string | number | undefined\n    ipad?: string | number | undefined\n    googleplay?: string | undefined\n  }\n  url?:\n    | {\n        iphone?: string | URL | undefined\n        ipad?: string | URL | undefined\n        googleplay?: string | URL | undefined\n      }\n    | undefined\n  name?: string | undefined\n}\n\ntype TwitterImage = string | TwitterImageDescriptor | URL\ntype TwitterImageDescriptor = {\n  url: string | URL\n  alt?: string | undefined\n  secureUrl?: string | URL | undefined\n  type?: string | undefined\n  width?: string | number | undefined\n  height?: string | number | undefined\n}\ntype TwitterPlayerDescriptor = {\n  playerUrl: string | URL\n  streamUrl: string | URL\n  width: number\n  height: number\n}\n\ntype ResolvedTwitterImage = {\n  url: string | URL\n  alt?: string | undefined\n  secureUrl?: string | URL | undefined\n  type?: string | undefined\n  width?: string | number | undefined\n  height?: string | number | undefined\n}\ntype ResolvedTwitterSummary = {\n  site: string | null\n  siteId: string | null\n  creator: string | null\n  creatorId: string | null\n  description: string | null\n  title: AbsoluteTemplateString\n  images?: Array<ResolvedTwitterImage> | undefined\n}\ntype ResolvedTwitterPlayer = ResolvedTwitterSummary & {\n  players: Array<TwitterPlayerDescriptor>\n}\ntype ResolvedTwitterApp = ResolvedTwitterSummary & { app: TwitterAppDescriptor }\n\nexport type ResolvedTwitterMetadata =\n  | ({ card: 'summary' } & ResolvedTwitterSummary)\n  | ({ card: 'summary_large_image' } & ResolvedTwitterSummary)\n  | ({ card: 'player' } & ResolvedTwitterPlayer)\n  | ({ card: 'app' } & ResolvedTwitterApp)\n"], "names": [], "mappings": "AAAA,8FAA8F"}