{"version": 3, "sources": ["../../../../src/lib/metadata/generate/opengraph.tsx"], "sourcesContent": ["import type { ResolvedMetadata } from '../types/metadata-interface'\nimport type { TwitterAppDescriptor } from '../types/twitter-types'\n\nimport { Meta, MetaFilter, MultiMeta } from './meta'\n\nexport function OpenGraphMetadata({\n  openGraph,\n}: {\n  openGraph: ResolvedMetadata['openGraph']\n}) {\n  if (!openGraph) {\n    return null\n  }\n\n  let typedOpenGraph\n  if ('type' in openGraph) {\n    const openGraphType = openGraph.type\n    switch (openGraphType) {\n      case 'website':\n        typedOpenGraph = [Meta({ property: 'og:type', content: 'website' })]\n        break\n      case 'article':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'article' }),\n          Meta({\n            property: 'article:published_time',\n            content: openGraph.publishedTime?.toString(),\n          }),\n          Meta({\n            property: 'article:modified_time',\n            content: openGraph.modifiedTime?.toString(),\n          }),\n          Meta({\n            property: 'article:expiration_time',\n            content: openGraph.expirationTime?.toString(),\n          }),\n          MultiMeta({\n            propertyPrefix: 'article:author',\n            contents: openGraph.authors,\n          }),\n          Meta({ property: 'article:section', content: openGraph.section }),\n          MultiMeta({\n            propertyPrefix: 'article:tag',\n            contents: openGraph.tags,\n          }),\n        ]\n        break\n      case 'book':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'book' }),\n          Meta({ property: 'book:isbn', content: openGraph.isbn }),\n          Meta({\n            property: 'book:release_date',\n            content: openGraph.releaseDate,\n          }),\n          MultiMeta({\n            propertyPrefix: 'book:author',\n            contents: openGraph.authors,\n          }),\n          MultiMeta({ propertyPrefix: 'book:tag', contents: openGraph.tags }),\n        ]\n        break\n      case 'profile':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'profile' }),\n          Meta({\n            property: 'profile:first_name',\n            content: openGraph.firstName,\n          }),\n          Meta({ property: 'profile:last_name', content: openGraph.lastName }),\n          Meta({ property: 'profile:username', content: openGraph.username }),\n          Meta({ property: 'profile:gender', content: openGraph.gender }),\n        ]\n        break\n      case 'music.song':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'music.song' }),\n          Meta({\n            property: 'music:duration',\n            content: openGraph.duration?.toString(),\n          }),\n          MultiMeta({\n            propertyPrefix: 'music:album',\n            contents: openGraph.albums,\n          }),\n          MultiMeta({\n            propertyPrefix: 'music:musician',\n            contents: openGraph.musicians,\n          }),\n        ]\n        break\n      case 'music.album':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'music.album' }),\n          MultiMeta({\n            propertyPrefix: 'music:song',\n            contents: openGraph.songs,\n          }),\n          MultiMeta({\n            propertyPrefix: 'music:musician',\n            contents: openGraph.musicians,\n          }),\n          Meta({\n            property: 'music:release_date',\n            content: openGraph.releaseDate,\n          }),\n        ]\n        break\n      case 'music.playlist':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'music.playlist' }),\n          MultiMeta({\n            propertyPrefix: 'music:song',\n            contents: openGraph.songs,\n          }),\n          MultiMeta({\n            propertyPrefix: 'music:creator',\n            contents: openGraph.creators,\n          }),\n        ]\n        break\n      case 'music.radio_station':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'music.radio_station' }),\n          MultiMeta({\n            propertyPrefix: 'music:creator',\n            contents: openGraph.creators,\n          }),\n        ]\n        break\n\n      case 'video.movie':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'video.movie' }),\n          MultiMeta({\n            propertyPrefix: 'video:actor',\n            contents: openGraph.actors,\n          }),\n          MultiMeta({\n            propertyPrefix: 'video:director',\n            contents: openGraph.directors,\n          }),\n          MultiMeta({\n            propertyPrefix: 'video:writer',\n            contents: openGraph.writers,\n          }),\n          Meta({ property: 'video:duration', content: openGraph.duration }),\n          Meta({\n            property: 'video:release_date',\n            content: openGraph.releaseDate,\n          }),\n          MultiMeta({ propertyPrefix: 'video:tag', contents: openGraph.tags }),\n        ]\n        break\n      case 'video.episode':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'video.episode' }),\n          MultiMeta({\n            propertyPrefix: 'video:actor',\n            contents: openGraph.actors,\n          }),\n          MultiMeta({\n            propertyPrefix: 'video:director',\n            contents: openGraph.directors,\n          }),\n          MultiMeta({\n            propertyPrefix: 'video:writer',\n            contents: openGraph.writers,\n          }),\n          Meta({ property: 'video:duration', content: openGraph.duration }),\n          Meta({\n            property: 'video:release_date',\n            content: openGraph.releaseDate,\n          }),\n          MultiMeta({ propertyPrefix: 'video:tag', contents: openGraph.tags }),\n          Meta({ property: 'video:series', content: openGraph.series }),\n        ]\n        break\n      case 'video.tv_show':\n        typedOpenGraph = [\n          Meta({ property: 'og:type', content: 'video.tv_show' }),\n        ]\n        break\n      case 'video.other':\n        typedOpenGraph = [Meta({ property: 'og:type', content: 'video.other' })]\n        break\n\n      default:\n        const _exhaustiveCheck: never = openGraphType\n        throw new Error(`Invalid OpenGraph type: ${_exhaustiveCheck}`)\n    }\n  }\n\n  return MetaFilter([\n    Meta({ property: 'og:determiner', content: openGraph.determiner }),\n    Meta({ property: 'og:title', content: openGraph.title?.absolute }),\n    Meta({ property: 'og:description', content: openGraph.description }),\n    Meta({ property: 'og:url', content: openGraph.url?.toString() }),\n    Meta({ property: 'og:site_name', content: openGraph.siteName }),\n    Meta({ property: 'og:locale', content: openGraph.locale }),\n    Meta({ property: 'og:country_name', content: openGraph.countryName }),\n    Meta({ property: 'og:ttl', content: openGraph.ttl?.toString() }),\n    MultiMeta({ propertyPrefix: 'og:image', contents: openGraph.images }),\n    MultiMeta({ propertyPrefix: 'og:video', contents: openGraph.videos }),\n    MultiMeta({ propertyPrefix: 'og:audio', contents: openGraph.audio }),\n    MultiMeta({ propertyPrefix: 'og:email', contents: openGraph.emails }),\n    MultiMeta({\n      propertyPrefix: 'og:phone_number',\n      contents: openGraph.phoneNumbers,\n    }),\n    MultiMeta({\n      propertyPrefix: 'og:fax_number',\n      contents: openGraph.faxNumbers,\n    }),\n    MultiMeta({\n      propertyPrefix: 'og:locale:alternate',\n      contents: openGraph.alternateLocale,\n    }),\n    ...(typedOpenGraph ? typedOpenGraph : []),\n  ])\n}\n\nfunction TwitterAppItem({\n  app,\n  type,\n}: {\n  app: TwitterAppDescriptor\n  type: 'iphone' | 'ipad' | 'googleplay'\n}) {\n  return [\n    Meta({ name: `twitter:app:name:${type}`, content: app.name }),\n    Meta({ name: `twitter:app:id:${type}`, content: app.id[type] }),\n    Meta({\n      name: `twitter:app:url:${type}`,\n      content: app.url?.[type]?.toString(),\n    }),\n  ]\n}\n\nexport function TwitterMetadata({\n  twitter,\n}: {\n  twitter: ResolvedMetadata['twitter']\n}) {\n  if (!twitter) return null\n  const { card } = twitter\n\n  return MetaFilter([\n    Meta({ name: 'twitter:card', content: card }),\n    Meta({ name: 'twitter:site', content: twitter.site }),\n    Meta({ name: 'twitter:site:id', content: twitter.siteId }),\n    Meta({ name: 'twitter:creator', content: twitter.creator }),\n    Meta({ name: 'twitter:creator:id', content: twitter.creatorId }),\n    Meta({ name: 'twitter:title', content: twitter.title?.absolute }),\n    Meta({ name: 'twitter:description', content: twitter.description }),\n    MultiMeta({ namePrefix: 'twitter:image', contents: twitter.images }),\n    ...(card === 'player'\n      ? twitter.players.flatMap((player) => [\n          Meta({\n            name: 'twitter:player',\n            content: player.playerUrl.toString(),\n          }),\n          Meta({\n            name: 'twitter:player:stream',\n            content: player.streamUrl.toString(),\n          }),\n          Meta({ name: 'twitter:player:width', content: player.width }),\n          Meta({ name: 'twitter:player:height', content: player.height }),\n        ])\n      : []),\n    ...(card === 'app'\n      ? [\n          TwitterAppItem({ app: twitter.app, type: 'iphone' }),\n          TwitterAppItem({ app: twitter.app, type: 'ipad' }),\n          TwitterAppItem({ app: twitter.app, type: 'googleplay' }),\n        ]\n      : []),\n  ])\n}\n\nexport function AppLinksMeta({\n  appLinks,\n}: {\n  appLinks: ResolvedMetadata['appLinks']\n}) {\n  if (!appLinks) return null\n  return MetaFilter([\n    MultiMeta({ propertyPrefix: 'al:ios', contents: appLinks.ios }),\n    MultiMeta({ propertyPrefix: 'al:iphone', contents: appLinks.iphone }),\n    MultiMeta({ propertyPrefix: 'al:ipad', contents: appLinks.ipad }),\n    MultiMeta({ propertyPrefix: 'al:android', contents: appLinks.android }),\n    MultiMeta({\n      propertyPrefix: 'al:windows_phone',\n      contents: appLinks.windows_phone,\n    }),\n    MultiMeta({ propertyPrefix: 'al:windows', contents: appLinks.windows }),\n    MultiMeta({\n      propertyPrefix: 'al:windows_universal',\n      contents: appLinks.windows_universal,\n    }),\n    MultiMeta({ propertyPrefix: 'al:web', contents: appLinks.web }),\n  ])\n}\n"], "names": ["AppLinksMeta", "OpenGraphMetadata", "TwitterMetadata", "openGraph", "typedOpenGraph", "openGraphType", "type", "Meta", "property", "content", "publishedTime", "toString", "modifiedTime", "expirationTime", "MultiMeta", "propertyPrefix", "contents", "authors", "section", "tags", "isbn", "releaseDate", "firstName", "lastName", "username", "gender", "duration", "albums", "musicians", "songs", "creators", "actors", "directors", "writers", "series", "_exhaustiveCheck", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "determiner", "title", "absolute", "description", "url", "siteName", "locale", "countryName", "ttl", "images", "videos", "audio", "emails", "phoneNumbers", "faxNumbers", "alternateLocale", "TwitterAppItem", "app", "name", "id", "twitter", "card", "site", "siteId", "creator", "creatorId", "namePrefix", "players", "flatMap", "player", "playerUrl", "streamUrl", "width", "height", "appLinks", "ios", "iphone", "ipad", "android", "windows_phone", "windows", "windows_universal", "web"], "mappings": ";;;;;;;;;;;;;;;;IAwRgBA,YAAY;eAAZA;;IAnRAC,iBAAiB;eAAjBA;;IA0OAC,eAAe;eAAfA;;;sBA5O4B;AAErC,SAASD,kBAAkB,EAChCE,SAAS,EAGV;QA0LyCA,kBAEFA,gBAIAA;IA/LtC,IAAI,CAACA,WAAW;QACd,OAAO;IACT;IAEA,IAAIC;IACJ,IAAI,UAAUD,WAAW;QACvB,MAAME,gBAAgBF,UAAUG,IAAI;QACpC,OAAQD;YACN,KAAK;gBACHD,iBAAiB;oBAACG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAU;iBAAG;gBACpE;YACF,KAAK;oBAKUN,0BAIAA,yBAIAA;gBAZbC,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAU;oBAC/CF,IAAAA,UAAI,EAAC;wBACHC,UAAU;wBACVC,OAAO,GAAEN,2BAAAA,UAAUO,aAAa,qBAAvBP,yBAAyBQ,QAAQ;oBAC5C;oBACAJ,IAAAA,UAAI,EAAC;wBACHC,UAAU;wBACVC,OAAO,GAAEN,0BAAAA,UAAUS,YAAY,qBAAtBT,wBAAwBQ,QAAQ;oBAC3C;oBACAJ,IAAAA,UAAI,EAAC;wBACHC,UAAU;wBACVC,OAAO,GAAEN,4BAAAA,UAAUU,cAAc,qBAAxBV,0BAA0BQ,QAAQ;oBAC7C;oBACAG,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAUc,OAAO;oBAC7B;oBACAV,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAmBC,SAASN,UAAUe,OAAO;oBAAC;oBAC/DJ,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAUgB,IAAI;oBAC1B;iBACD;gBACD;YACF,KAAK;gBACHf,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAO;oBAC5CF,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAaC,SAASN,UAAUiB,IAAI;oBAAC;oBACtDb,IAAAA,UAAI,EAAC;wBACHC,UAAU;wBACVC,SAASN,UAAUkB,WAAW;oBAChC;oBACAP,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAUc,OAAO;oBAC7B;oBACAH,IAAAA,eAAS,EAAC;wBAAEC,gBAAgB;wBAAYC,UAAUb,UAAUgB,IAAI;oBAAC;iBAClE;gBACD;YACF,KAAK;gBACHf,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAU;oBAC/CF,IAAAA,UAAI,EAAC;wBACHC,UAAU;wBACVC,SAASN,UAAUmB,SAAS;oBAC9B;oBACAf,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAqBC,SAASN,UAAUoB,QAAQ;oBAAC;oBAClEhB,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAoBC,SAASN,UAAUqB,QAAQ;oBAAC;oBACjEjB,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAkBC,SAASN,UAAUsB,MAAM;oBAAC;iBAC9D;gBACD;YACF,KAAK;oBAKUtB;gBAJbC,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAa;oBAClDF,IAAAA,UAAI,EAAC;wBACHC,UAAU;wBACVC,OAAO,GAAEN,sBAAAA,UAAUuB,QAAQ,qBAAlBvB,oBAAoBQ,QAAQ;oBACvC;oBACAG,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAUwB,MAAM;oBAC5B;oBACAb,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAUyB,SAAS;oBAC/B;iBACD;gBACD;YACF,KAAK;gBACHxB,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAc;oBACnDK,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU0B,KAAK;oBAC3B;oBACAf,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAUyB,SAAS;oBAC/B;oBACArB,IAAAA,UAAI,EAAC;wBACHC,UAAU;wBACVC,SAASN,UAAUkB,WAAW;oBAChC;iBACD;gBACD;YACF,KAAK;gBACHjB,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAiB;oBACtDK,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU0B,KAAK;oBAC3B;oBACAf,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU2B,QAAQ;oBAC9B;iBACD;gBACD;YACF,KAAK;gBACH1B,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAsB;oBAC3DK,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU2B,QAAQ;oBAC9B;iBACD;gBACD;YAEF,KAAK;gBACH1B,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAc;oBACnDK,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU4B,MAAM;oBAC5B;oBACAjB,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU6B,SAAS;oBAC/B;oBACAlB,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU8B,OAAO;oBAC7B;oBACA1B,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAkBC,SAASN,UAAUuB,QAAQ;oBAAC;oBAC/DnB,IAAAA,UAAI,EAAC;wBACHC,UAAU;wBACVC,SAASN,UAAUkB,WAAW;oBAChC;oBACAP,IAAAA,eAAS,EAAC;wBAAEC,gBAAgB;wBAAaC,UAAUb,UAAUgB,IAAI;oBAAC;iBACnE;gBACD;YACF,KAAK;gBACHf,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAgB;oBACrDK,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU4B,MAAM;oBAC5B;oBACAjB,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU6B,SAAS;oBAC/B;oBACAlB,IAAAA,eAAS,EAAC;wBACRC,gBAAgB;wBAChBC,UAAUb,UAAU8B,OAAO;oBAC7B;oBACA1B,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAkBC,SAASN,UAAUuB,QAAQ;oBAAC;oBAC/DnB,IAAAA,UAAI,EAAC;wBACHC,UAAU;wBACVC,SAASN,UAAUkB,WAAW;oBAChC;oBACAP,IAAAA,eAAS,EAAC;wBAAEC,gBAAgB;wBAAaC,UAAUb,UAAUgB,IAAI;oBAAC;oBAClEZ,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAgBC,SAASN,UAAU+B,MAAM;oBAAC;iBAC5D;gBACD;YACF,KAAK;gBACH9B,iBAAiB;oBACfG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAgB;iBACtD;gBACD;YACF,KAAK;gBACHL,iBAAiB;oBAACG,IAAAA,UAAI,EAAC;wBAAEC,UAAU;wBAAWC,SAAS;oBAAc;iBAAG;gBACxE;YAEF;gBACE,MAAM0B,mBAA0B9B;gBAChC,MAAM,qBAAwD,CAAxD,IAAI+B,MAAM,CAAC,wBAAwB,EAAED,kBAAkB,GAAvD,qBAAA;2BAAA;gCAAA;kCAAA;gBAAuD;QACjE;IACF;IAEA,OAAOE,IAAAA,gBAAU,EAAC;QAChB9B,IAAAA,UAAI,EAAC;YAAEC,UAAU;YAAiBC,SAASN,UAAUmC,UAAU;QAAC;QAChE/B,IAAAA,UAAI,EAAC;YAAEC,UAAU;YAAYC,OAAO,GAAEN,mBAAAA,UAAUoC,KAAK,qBAAfpC,iBAAiBqC,QAAQ;QAAC;QAChEjC,IAAAA,UAAI,EAAC;YAAEC,UAAU;YAAkBC,SAASN,UAAUsC,WAAW;QAAC;QAClElC,IAAAA,UAAI,EAAC;YAAEC,UAAU;YAAUC,OAAO,GAAEN,iBAAAA,UAAUuC,GAAG,qBAAbvC,eAAeQ,QAAQ;QAAG;QAC9DJ,IAAAA,UAAI,EAAC;YAAEC,UAAU;YAAgBC,SAASN,UAAUwC,QAAQ;QAAC;QAC7DpC,IAAAA,UAAI,EAAC;YAAEC,UAAU;YAAaC,SAASN,UAAUyC,MAAM;QAAC;QACxDrC,IAAAA,UAAI,EAAC;YAAEC,UAAU;YAAmBC,SAASN,UAAU0C,WAAW;QAAC;QACnEtC,IAAAA,UAAI,EAAC;YAAEC,UAAU;YAAUC,OAAO,GAAEN,iBAAAA,UAAU2C,GAAG,qBAAb3C,eAAeQ,QAAQ;QAAG;QAC9DG,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAYC,UAAUb,UAAU4C,MAAM;QAAC;QACnEjC,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAYC,UAAUb,UAAU6C,MAAM;QAAC;QACnElC,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAYC,UAAUb,UAAU8C,KAAK;QAAC;QAClEnC,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAYC,UAAUb,UAAU+C,MAAM;QAAC;QACnEpC,IAAAA,eAAS,EAAC;YACRC,gBAAgB;YAChBC,UAAUb,UAAUgD,YAAY;QAClC;QACArC,IAAAA,eAAS,EAAC;YACRC,gBAAgB;YAChBC,UAAUb,UAAUiD,UAAU;QAChC;QACAtC,IAAAA,eAAS,EAAC;YACRC,gBAAgB;YAChBC,UAAUb,UAAUkD,eAAe;QACrC;WACIjD,iBAAiBA,iBAAiB,EAAE;KACzC;AACH;AAEA,SAASkD,eAAe,EACtBC,GAAG,EACHjD,IAAI,EAIL;QAMciD,eAAAA;IALb,OAAO;QACLhD,IAAAA,UAAI,EAAC;YAAEiD,MAAM,CAAC,iBAAiB,EAAElD,MAAM;YAAEG,SAAS8C,IAAIC,IAAI;QAAC;QAC3DjD,IAAAA,UAAI,EAAC;YAAEiD,MAAM,CAAC,eAAe,EAAElD,MAAM;YAAEG,SAAS8C,IAAIE,EAAE,CAACnD,KAAK;QAAC;QAC7DC,IAAAA,UAAI,EAAC;YACHiD,MAAM,CAAC,gBAAgB,EAAElD,MAAM;YAC/BG,OAAO,GAAE8C,WAAAA,IAAIb,GAAG,sBAAPa,gBAAAA,QAAS,CAACjD,KAAK,qBAAfiD,cAAiB5C,QAAQ;QACpC;KACD;AACH;AAEO,SAAST,gBAAgB,EAC9BwD,OAAO,EAGR;QAU0CA;IATzC,IAAI,CAACA,SAAS,OAAO;IACrB,MAAM,EAAEC,IAAI,EAAE,GAAGD;IAEjB,OAAOrB,IAAAA,gBAAU,EAAC;QAChB9B,IAAAA,UAAI,EAAC;YAAEiD,MAAM;YAAgB/C,SAASkD;QAAK;QAC3CpD,IAAAA,UAAI,EAAC;YAAEiD,MAAM;YAAgB/C,SAASiD,QAAQE,IAAI;QAAC;QACnDrD,IAAAA,UAAI,EAAC;YAAEiD,MAAM;YAAmB/C,SAASiD,QAAQG,MAAM;QAAC;QACxDtD,IAAAA,UAAI,EAAC;YAAEiD,MAAM;YAAmB/C,SAASiD,QAAQI,OAAO;QAAC;QACzDvD,IAAAA,UAAI,EAAC;YAAEiD,MAAM;YAAsB/C,SAASiD,QAAQK,SAAS;QAAC;QAC9DxD,IAAAA,UAAI,EAAC;YAAEiD,MAAM;YAAiB/C,OAAO,GAAEiD,iBAAAA,QAAQnB,KAAK,qBAAbmB,eAAelB,QAAQ;QAAC;QAC/DjC,IAAAA,UAAI,EAAC;YAAEiD,MAAM;YAAuB/C,SAASiD,QAAQjB,WAAW;QAAC;QACjE3B,IAAAA,eAAS,EAAC;YAAEkD,YAAY;YAAiBhD,UAAU0C,QAAQX,MAAM;QAAC;WAC9DY,SAAS,WACTD,QAAQO,OAAO,CAACC,OAAO,CAAC,CAACC,SAAW;gBAClC5D,IAAAA,UAAI,EAAC;oBACHiD,MAAM;oBACN/C,SAAS0D,OAAOC,SAAS,CAACzD,QAAQ;gBACpC;gBACAJ,IAAAA,UAAI,EAAC;oBACHiD,MAAM;oBACN/C,SAAS0D,OAAOE,SAAS,CAAC1D,QAAQ;gBACpC;gBACAJ,IAAAA,UAAI,EAAC;oBAAEiD,MAAM;oBAAwB/C,SAAS0D,OAAOG,KAAK;gBAAC;gBAC3D/D,IAAAA,UAAI,EAAC;oBAAEiD,MAAM;oBAAyB/C,SAAS0D,OAAOI,MAAM;gBAAC;aAC9D,IACD,EAAE;WACFZ,SAAS,QACT;YACEL,eAAe;gBAAEC,KAAKG,QAAQH,GAAG;gBAAEjD,MAAM;YAAS;YAClDgD,eAAe;gBAAEC,KAAKG,QAAQH,GAAG;gBAAEjD,MAAM;YAAO;YAChDgD,eAAe;gBAAEC,KAAKG,QAAQH,GAAG;gBAAEjD,MAAM;YAAa;SACvD,GACD,EAAE;KACP;AACH;AAEO,SAASN,aAAa,EAC3BwE,QAAQ,EAGT;IACC,IAAI,CAACA,UAAU,OAAO;IACtB,OAAOnC,IAAAA,gBAAU,EAAC;QAChBvB,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAUC,UAAUwD,SAASC,GAAG;QAAC;QAC7D3D,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAaC,UAAUwD,SAASE,MAAM;QAAC;QACnE5D,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAWC,UAAUwD,SAASG,IAAI;QAAC;QAC/D7D,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAcC,UAAUwD,SAASI,OAAO;QAAC;QACrE9D,IAAAA,eAAS,EAAC;YACRC,gBAAgB;YAChBC,UAAUwD,SAASK,aAAa;QAClC;QACA/D,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAcC,UAAUwD,SAASM,OAAO;QAAC;QACrEhE,IAAAA,eAAS,EAAC;YACRC,gBAAgB;YAChBC,UAAUwD,SAASO,iBAAiB;QACtC;QACAjE,IAAAA,eAAS,EAAC;YAAEC,gBAAgB;YAAUC,UAAUwD,SAASQ,GAAG;QAAC;KAC9D;AACH"}