{"version": 3, "sources": ["../../../src/lib/metadata/constants.ts"], "sourcesContent": ["import type { ViewportLayout } from './types/extra-types'\nimport type { Icons } from './types/metadata-types'\n\nexport const ViewportMetaKeys: { [k in keyof ViewportLayout]: string } = {\n  width: 'width',\n  height: 'height',\n  initialScale: 'initial-scale',\n  minimumScale: 'minimum-scale',\n  maximumScale: 'maximum-scale',\n  viewportFit: 'viewport-fit',\n  userScalable: 'user-scalable',\n  interactiveWidget: 'interactive-widget',\n} as const\n\nexport const IconKeys: (keyof Icons)[] = ['icon', 'shortcut', 'apple', 'other']\n"], "names": ["IconKeys", "ViewportMetaKeys", "width", "height", "initialScale", "minimumScale", "maximumScale", "viewportFit", "userScalable", "interactiveWidget"], "mappings": ";;;;;;;;;;;;;;;IAcaA,QAAQ;eAARA;;IAXAC,gBAAgB;eAAhBA;;;AAAN,MAAMA,mBAA4D;IACvEC,OAAO;IACPC,QAAQ;IACRC,cAAc;IACdC,cAAc;IACdC,cAAc;IACdC,aAAa;IACbC,cAAc;IACdC,mBAAmB;AACrB;AAEO,MAAMT,WAA4B;IAAC;IAAQ;IAAY;IAAS;CAAQ"}