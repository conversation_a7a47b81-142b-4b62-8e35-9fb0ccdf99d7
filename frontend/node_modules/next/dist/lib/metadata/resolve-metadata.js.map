{"version": 3, "sources": ["../../../src/lib/metadata/resolve-metadata.ts"], "sourcesContent": ["import type {\n  <PERSON>ada<PERSON>,\n  ResolvedMetadata,\n  ResolvedViewport,\n  ResolvingMetadata,\n  ResolvingViewport,\n  Viewport,\n} from './types/metadata-interface'\nimport type { MetadataImageModule } from '../../build/webpack/loaders/metadata/types'\nimport type { GetDynamicParamFromSegment } from '../../server/app-render/app-render'\nimport type { Twitter } from './types/twitter-types'\nimport type { OpenGraph } from './types/opengraph-types'\nimport type { AppDirModules } from '../../build/webpack/loaders/next-app-loader'\nimport type { MetadataContext } from './types/resolvers'\nimport type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type {\n  AbsoluteTemplateString,\n  IconDescriptor,\n  ResolvedIcons,\n} from './types/metadata-types'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { StaticMetadata } from './types/icons'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport 'server-only'\n\nimport { cache } from 'react'\nimport {\n  createDefaultMetadata,\n  createDefaultViewport,\n} from './default-metadata'\nimport { resolveOpenGraph, resolveTwitter } from './resolvers/resolve-opengraph'\nimport { resolveTitle } from './resolvers/resolve-title'\nimport { resolveAsArrayOrUndefined } from './generate/utils'\nimport {\n  getComponentTypeModule,\n  getLayoutOrPageModule,\n} from '../../server/lib/app-dir-module'\nimport { interopDefault } from '../interop-default'\nimport {\n  resolveAlternates,\n  resolveAppleWebApp,\n  resolveAppLinks,\n  resolveRobots,\n  resolveThemeColor,\n  resolveVerification,\n  resolveItunes,\n  resolveFacebook,\n  resolvePagination,\n} from './resolvers/resolve-basics'\nimport { resolveIcons } from './resolvers/resolve-icons'\nimport { getTracer } from '../../server/lib/trace/tracer'\nimport { ResolveMetadataSpan } from '../../server/lib/trace/constants'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport * as Log from '../../build/output/log'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\nimport type {\n  Params,\n  CreateServerParamsForMetadata,\n} from '../../server/request/params'\n\ntype StaticIcons = Pick<ResolvedIcons, 'icon' | 'apple'>\n\ntype MetadataResolver = (\n  parent: ResolvingMetadata\n) => Metadata | Promise<Metadata>\ntype ViewportResolver = (\n  parent: ResolvingViewport\n) => Viewport | Promise<Viewport>\n\nexport type MetadataErrorType = 'not-found' | 'forbidden' | 'unauthorized'\n\nexport type MetadataItems = [\n  Metadata | MetadataResolver | null,\n  StaticMetadata,\n  Viewport | ViewportResolver | null,\n][]\n\ntype TitleTemplates = {\n  title: string | null\n  twitter: string | null\n  openGraph: string | null\n}\n\ntype BuildState = {\n  warnings: Set<string>\n}\n\ntype LayoutProps = {\n  params: { [key: string]: any }\n}\ntype PageProps = {\n  params: { [key: string]: any }\n  searchParams: { [key: string]: any }\n}\n\nfunction isFavicon(icon: IconDescriptor | undefined): boolean {\n  if (!icon) {\n    return false\n  }\n\n  // turbopack appends a hash to all images\n  return (\n    (icon.url === '/favicon.ico' ||\n      icon.url.toString().startsWith('/favicon.ico?')) &&\n    icon.type === 'image/x-icon'\n  )\n}\n\nfunction mergeStaticMetadata(\n  source: Metadata | null,\n  target: ResolvedMetadata,\n  staticFilesMetadata: StaticMetadata,\n  metadataContext: MetadataContext,\n  titleTemplates: TitleTemplates,\n  leafSegmentStaticIcons: StaticIcons\n) {\n  if (!staticFilesMetadata) return\n  const { icon, apple, openGraph, twitter, manifest } = staticFilesMetadata\n\n  // Keep updating the static icons in the most leaf node\n\n  if (icon) {\n    leafSegmentStaticIcons.icon = icon\n  }\n  if (apple) {\n    leafSegmentStaticIcons.apple = apple\n  }\n\n  // file based metadata is specified and current level metadata twitter.images is not specified\n  if (twitter && !source?.twitter?.hasOwnProperty('images')) {\n    const resolvedTwitter = resolveTwitter(\n      { ...target.twitter, images: twitter } as Twitter,\n      target.metadataBase,\n      { ...metadataContext, isStaticMetadataRouteFile: true },\n      titleTemplates.twitter\n    )\n    target.twitter = resolvedTwitter\n  }\n\n  // file based metadata is specified and current level metadata openGraph.images is not specified\n  if (openGraph && !source?.openGraph?.hasOwnProperty('images')) {\n    const resolvedOpenGraph = resolveOpenGraph(\n      { ...target.openGraph, images: openGraph } as OpenGraph,\n      target.metadataBase,\n      { ...metadataContext, isStaticMetadataRouteFile: true },\n      titleTemplates.openGraph\n    )\n    target.openGraph = resolvedOpenGraph\n  }\n  if (manifest) {\n    target.manifest = manifest\n  }\n\n  return target\n}\n\n// Merge the source metadata into the resolved target metadata.\nfunction mergeMetadata({\n  source,\n  target,\n  staticFilesMetadata,\n  titleTemplates,\n  metadataContext,\n  buildState,\n  leafSegmentStaticIcons,\n}: {\n  source: Metadata | null\n  target: ResolvedMetadata\n  staticFilesMetadata: StaticMetadata\n  titleTemplates: TitleTemplates\n  metadataContext: MetadataContext\n  buildState: BuildState\n  leafSegmentStaticIcons: StaticIcons\n}): void {\n  // If there's override metadata, prefer it otherwise fallback to the default metadata.\n  const metadataBase =\n    typeof source?.metadataBase !== 'undefined'\n      ? source.metadataBase\n      : target.metadataBase\n  for (const key_ in source) {\n    const key = key_ as keyof Metadata\n\n    switch (key) {\n      case 'title': {\n        target.title = resolveTitle(source.title, titleTemplates.title)\n        break\n      }\n      case 'alternates': {\n        target.alternates = resolveAlternates(\n          source.alternates,\n          metadataBase,\n          metadataContext\n        )\n        break\n      }\n      case 'openGraph': {\n        target.openGraph = resolveOpenGraph(\n          source.openGraph,\n          metadataBase,\n          metadataContext,\n          titleTemplates.openGraph\n        )\n        break\n      }\n      case 'twitter': {\n        target.twitter = resolveTwitter(\n          source.twitter,\n          metadataBase,\n          metadataContext,\n          titleTemplates.twitter\n        )\n        break\n      }\n      case 'facebook':\n        target.facebook = resolveFacebook(source.facebook)\n        break\n\n      case 'verification':\n        target.verification = resolveVerification(source.verification)\n        break\n\n      case 'icons': {\n        target.icons = resolveIcons(source.icons)\n        break\n      }\n      case 'appleWebApp':\n        target.appleWebApp = resolveAppleWebApp(source.appleWebApp)\n        break\n      case 'appLinks':\n        target.appLinks = resolveAppLinks(source.appLinks)\n        break\n      case 'robots': {\n        target.robots = resolveRobots(source.robots)\n        break\n      }\n      case 'archives':\n      case 'assets':\n      case 'bookmarks':\n      case 'keywords': {\n        target[key] = resolveAsArrayOrUndefined(source[key])\n        break\n      }\n      case 'authors': {\n        target[key] = resolveAsArrayOrUndefined(source.authors)\n        break\n      }\n      case 'itunes': {\n        target[key] = resolveItunes(\n          source.itunes,\n          metadataBase,\n          metadataContext\n        )\n        break\n      }\n      case 'pagination': {\n        target.pagination = resolvePagination(\n          source.pagination,\n          metadataBase,\n          metadataContext\n        )\n        break\n      }\n      // directly assign fields that fallback to null\n      case 'applicationName':\n      case 'description':\n      case 'generator':\n      case 'creator':\n      case 'publisher':\n      case 'category':\n      case 'classification':\n      case 'referrer':\n      case 'formatDetection':\n      case 'manifest':\n        // @ts-ignore TODO: support inferring\n        target[key] = source[key] || null\n        break\n      case 'other':\n        target.other = Object.assign({}, target.other, source.other)\n        break\n      case 'metadataBase':\n        target.metadataBase = metadataBase\n        break\n\n      default: {\n        if (\n          (key === 'viewport' ||\n            key === 'themeColor' ||\n            key === 'colorScheme') &&\n          source[key] != null\n        ) {\n          buildState.warnings.add(\n            `Unsupported metadata ${key} is configured in metadata export in ${metadataContext.pathname}. Please move it to viewport export instead.\\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport`\n          )\n        }\n        break\n      }\n    }\n  }\n  mergeStaticMetadata(\n    source,\n    target,\n    staticFilesMetadata,\n    metadataContext,\n    titleTemplates,\n    leafSegmentStaticIcons\n  )\n}\n\nfunction mergeViewport({\n  target,\n  source,\n}: {\n  target: ResolvedViewport\n  source: Viewport | null\n}): void {\n  if (!source) return\n  for (const key_ in source) {\n    const key = key_ as keyof Viewport\n\n    switch (key) {\n      case 'themeColor': {\n        target.themeColor = resolveThemeColor(source.themeColor)\n        break\n      }\n      case 'colorScheme':\n        target.colorScheme = source.colorScheme || null\n        break\n      default:\n        // always override the target with the source\n        // @ts-ignore viewport properties\n        target[key] = source[key]\n        break\n    }\n  }\n}\n\nfunction getDefinedViewport(\n  mod: any,\n  props: any,\n  tracingProps: { route: string }\n): Viewport | ViewportResolver | null {\n  if (typeof mod.generateViewport === 'function') {\n    const { route } = tracingProps\n    return (parent: ResolvingViewport) =>\n      getTracer().trace(\n        ResolveMetadataSpan.generateViewport,\n        {\n          spanName: `generateViewport ${route}`,\n          attributes: {\n            'next.page': route,\n          },\n        },\n        () => mod.generateViewport(props, parent)\n      )\n  }\n  return mod.viewport || null\n}\n\nfunction getDefinedMetadata(\n  mod: any,\n  props: any,\n  tracingProps: { route: string }\n): Metadata | MetadataResolver | null {\n  if (typeof mod.generateMetadata === 'function') {\n    const { route } = tracingProps\n    return (parent: ResolvingMetadata) =>\n      getTracer().trace(\n        ResolveMetadataSpan.generateMetadata,\n        {\n          spanName: `generateMetadata ${route}`,\n          attributes: {\n            'next.page': route,\n          },\n        },\n        () => mod.generateMetadata(props, parent)\n      )\n  }\n  return mod.metadata || null\n}\n\nasync function collectStaticImagesFiles(\n  metadata: AppDirModules['metadata'],\n  props: any,\n  type: keyof NonNullable<AppDirModules['metadata']>\n) {\n  if (!metadata?.[type]) return undefined\n\n  const iconPromises = metadata[type as 'icon' | 'apple'].map(\n    async (imageModule: (p: any) => Promise<MetadataImageModule[]>) =>\n      interopDefault(await imageModule(props))\n  )\n\n  return iconPromises?.length > 0\n    ? (await Promise.all(iconPromises))?.flat()\n    : undefined\n}\n\nasync function resolveStaticMetadata(\n  modules: AppDirModules,\n  props: any\n): Promise<StaticMetadata> {\n  const { metadata } = modules\n  if (!metadata) return null\n\n  const [icon, apple, openGraph, twitter] = await Promise.all([\n    collectStaticImagesFiles(metadata, props, 'icon'),\n    collectStaticImagesFiles(metadata, props, 'apple'),\n    collectStaticImagesFiles(metadata, props, 'openGraph'),\n    collectStaticImagesFiles(metadata, props, 'twitter'),\n  ])\n\n  const staticMetadata = {\n    icon,\n    apple,\n    openGraph,\n    twitter,\n    manifest: metadata.manifest,\n  }\n\n  return staticMetadata\n}\n\n// [layout.metadata, static files metadata] -> ... -> [page.metadata, static files metadata]\nasync function collectMetadata({\n  tree,\n  metadataItems,\n  errorMetadataItem,\n  props,\n  route,\n  errorConvention,\n}: {\n  tree: LoaderTree\n  metadataItems: MetadataItems\n  errorMetadataItem: MetadataItems[number]\n  props: any\n  route: string\n  errorConvention?: MetadataErrorType\n}) {\n  let mod\n  let modType\n  const hasErrorConventionComponent = Boolean(\n    errorConvention && tree[2][errorConvention]\n  )\n  if (errorConvention) {\n    mod = await getComponentTypeModule(tree, 'layout')\n    modType = errorConvention\n  } else {\n    const { mod: layoutOrPageMod, modType: layoutOrPageModType } =\n      await getLayoutOrPageModule(tree)\n    mod = layoutOrPageMod\n    modType = layoutOrPageModType\n  }\n\n  if (modType) {\n    route += `/${modType}`\n  }\n\n  const staticFilesMetadata = await resolveStaticMetadata(tree[2], props)\n  const metadataExport = mod ? getDefinedMetadata(mod, props, { route }) : null\n\n  const viewportExport = mod ? getDefinedViewport(mod, props, { route }) : null\n\n  metadataItems.push([metadataExport, staticFilesMetadata, viewportExport])\n\n  if (hasErrorConventionComponent && errorConvention) {\n    const errorMod = await getComponentTypeModule(tree, errorConvention)\n    const errorViewportExport = errorMod\n      ? getDefinedViewport(errorMod, props, { route })\n      : null\n    const errorMetadataExport = errorMod\n      ? getDefinedMetadata(errorMod, props, { route })\n      : null\n\n    errorMetadataItem[0] = errorMetadataExport\n    errorMetadataItem[1] = staticFilesMetadata\n    errorMetadataItem[2] = errorViewportExport\n  }\n}\n\nconst resolveMetadataItems = cache(async function (\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore\n) {\n  const parentParams = {}\n  const metadataItems: MetadataItems = []\n  const errorMetadataItem: MetadataItems[number] = [null, null, null]\n  const treePrefix = undefined\n  return resolveMetadataItemsImpl(\n    metadataItems,\n    tree,\n    treePrefix,\n    parentParams,\n    searchParams,\n    errorConvention,\n    errorMetadataItem,\n    getDynamicParamFromSegment,\n    createServerParamsForMetadata,\n    workStore\n  )\n})\n\nasync function resolveMetadataItemsImpl(\n  metadataItems: MetadataItems,\n  tree: LoaderTree,\n  /** Provided tree can be nested subtree, this argument says what is the path of such subtree */\n  treePrefix: undefined | string[],\n  parentParams: Params,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  errorMetadataItem: MetadataItems[number],\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore\n): Promise<MetadataItems> {\n  const [segment, parallelRoutes, { page }] = tree\n  const currentTreePrefix =\n    treePrefix && treePrefix.length ? [...treePrefix, segment] : [segment]\n  const isPage = typeof page !== 'undefined'\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n  /**\n   * Create object holding the parent params and current params\n   */\n  let currentParams = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const params = createServerParamsForMetadata(currentParams, workStore)\n\n  let layerProps: LayoutProps | PageProps\n  if (isPage) {\n    layerProps = {\n      params,\n      searchParams,\n    }\n  } else {\n    layerProps = {\n      params,\n    }\n  }\n\n  await collectMetadata({\n    tree,\n    metadataItems,\n    errorMetadataItem,\n    errorConvention,\n    props: layerProps,\n    route: currentTreePrefix\n      // __PAGE__ shouldn't be shown in a route\n      .filter((s) => s !== PAGE_SEGMENT_KEY)\n      .join('/'),\n  })\n\n  for (const key in parallelRoutes) {\n    const childTree = parallelRoutes[key]\n    await resolveMetadataItemsImpl(\n      metadataItems,\n      childTree,\n      currentTreePrefix,\n      currentParams,\n      searchParams,\n      errorConvention,\n      errorMetadataItem,\n      getDynamicParamFromSegment,\n      createServerParamsForMetadata,\n      workStore\n    )\n  }\n\n  if (Object.keys(parallelRoutes).length === 0 && errorConvention) {\n    // If there are no parallel routes, place error metadata as the last item.\n    // e.g. layout -> layout -> not-found\n    metadataItems.push(errorMetadataItem)\n  }\n\n  return metadataItems\n}\n\ntype WithTitle = { title?: AbsoluteTemplateString | null }\ntype WithDescription = { description?: string | null }\n\nconst isTitleTruthy = (title: AbsoluteTemplateString | null | undefined) =>\n  !!title?.absolute\nconst hasTitle = (metadata: WithTitle | null) => isTitleTruthy(metadata?.title)\n\nfunction inheritFromMetadata(\n  target: (WithTitle & WithDescription) | null,\n  metadata: ResolvedMetadata\n) {\n  if (target) {\n    if (!hasTitle(target) && hasTitle(metadata)) {\n      target.title = metadata.title\n    }\n    if (!target.description && metadata.description) {\n      target.description = metadata.description\n    }\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst commonOgKeys = ['title', 'description', 'images'] as const\nfunction postProcessMetadata(\n  metadata: ResolvedMetadata,\n  favicon: any,\n  titleTemplates: TitleTemplates,\n  metadataContext: MetadataContext\n): ResolvedMetadata {\n  const { openGraph, twitter } = metadata\n\n  if (openGraph) {\n    // If there's openGraph information but not configured in twitter,\n    // inherit them from openGraph metadata.\n    let autoFillProps: Partial<{\n      [Key in (typeof commonOgKeys)[number]]: NonNullable<\n        ResolvedMetadata['openGraph']\n      >[Key]\n    }> = {}\n    const hasTwTitle = hasTitle(twitter)\n    const hasTwDescription = twitter?.description\n    const hasTwImages = Boolean(\n      twitter?.hasOwnProperty('images') && twitter.images\n    )\n    if (!hasTwTitle) {\n      if (isTitleTruthy(openGraph.title)) {\n        autoFillProps.title = openGraph.title\n      } else if (metadata.title && isTitleTruthy(metadata.title)) {\n        autoFillProps.title = metadata.title\n      }\n    }\n    if (!hasTwDescription)\n      autoFillProps.description =\n        openGraph.description || metadata.description || undefined\n    if (!hasTwImages) autoFillProps.images = openGraph.images\n\n    if (Object.keys(autoFillProps).length > 0) {\n      const partialTwitter = resolveTwitter(\n        autoFillProps,\n        metadata.metadataBase,\n        metadataContext,\n        titleTemplates.twitter\n      )\n      if (metadata.twitter) {\n        metadata.twitter = Object.assign({}, metadata.twitter, {\n          ...(!hasTwTitle && { title: partialTwitter?.title }),\n          ...(!hasTwDescription && {\n            description: partialTwitter?.description,\n          }),\n          ...(!hasTwImages && { images: partialTwitter?.images }),\n        })\n      } else {\n        metadata.twitter = partialTwitter\n      }\n    }\n  }\n\n  // If there's no title and description configured in openGraph or twitter,\n  // use the title and description from metadata.\n  inheritFromMetadata(openGraph, metadata)\n  inheritFromMetadata(twitter, metadata)\n\n  if (favicon) {\n    if (!metadata.icons) {\n      metadata.icons = {\n        icon: [],\n        apple: [],\n      }\n    }\n\n    metadata.icons.icon.unshift(favicon)\n  }\n\n  return metadata\n}\n\ntype DataResolver<Data, ResolvedData> = (\n  parent: Promise<ResolvedData>\n) => Data | Promise<Data>\n\nfunction collectMetadataExportPreloading<Data, ResolvedData>(\n  results: (Data | Promise<Data>)[],\n  dynamicMetadataExportFn: DataResolver<Data, ResolvedData>,\n  resolvers: ((value: ResolvedData) => void)[]\n) {\n  const result = dynamicMetadataExportFn(\n    new Promise<any>((resolve) => {\n      resolvers.push(resolve)\n    })\n  )\n\n  if (result instanceof Promise) {\n    // since we eager execute generateMetadata and\n    // they can reject at anytime we need to ensure\n    // we attach the catch handler right away to\n    // prevent unhandled rejections crashing the process\n    result.catch((err) => {\n      return {\n        __nextError: err,\n      }\n    })\n  }\n  results.push(result)\n}\n\nasync function getMetadataFromExport<Data, ResolvedData>(\n  getPreloadMetadataExport: (\n    metadataItem: NonNullable<MetadataItems[number]>\n  ) => Data | DataResolver<Data, ResolvedData> | null,\n  dynamicMetadataResolveState: {\n    resolvers: ((value: ResolvedData) => void)[]\n    resolvingIndex: number\n  },\n  metadataItems: MetadataItems,\n  currentIndex: number,\n  resolvedMetadata: ResolvedData,\n  metadataResults: (Data | Promise<Data>)[]\n) {\n  const metadataExport = getPreloadMetadataExport(metadataItems[currentIndex])\n  const dynamicMetadataResolvers = dynamicMetadataResolveState.resolvers\n  let metadata: Data | null = null\n  if (typeof metadataExport === 'function') {\n    // Only preload at the beginning when resolves are empty\n    if (!dynamicMetadataResolvers.length) {\n      for (let j = currentIndex; j < metadataItems.length; j++) {\n        const preloadMetadataExport = getPreloadMetadataExport(metadataItems[j])\n        // call each `generateMetadata function concurrently and stash their resolver\n        if (typeof preloadMetadataExport === 'function') {\n          collectMetadataExportPreloading<Data, ResolvedData>(\n            metadataResults,\n            preloadMetadataExport as DataResolver<Data, ResolvedData>,\n            dynamicMetadataResolvers\n          )\n        }\n      }\n    }\n\n    const resolveParent =\n      dynamicMetadataResolvers[dynamicMetadataResolveState.resolvingIndex]\n    const metadataResult =\n      metadataResults[dynamicMetadataResolveState.resolvingIndex++]\n\n    // In dev we clone and freeze to prevent relying on mutating resolvedMetadata directly.\n    // In prod we just pass resolvedMetadata through without any copying.\n    const currentResolvedMetadata =\n      process.env.NODE_ENV === 'development'\n        ? Object.freeze(\n            require('./clone-metadata').cloneMetadata(resolvedMetadata)\n          )\n        : resolvedMetadata\n\n    // This resolve should unblock the generateMetadata function if it awaited the parent\n    // argument. If it didn't await the parent argument it might already have a value since it was\n    // called concurrently. Regardless we await the return value before continuing on to the next layer\n    resolveParent(currentResolvedMetadata)\n    metadata =\n      metadataResult instanceof Promise ? await metadataResult : metadataResult\n\n    if (metadata && typeof metadata === 'object' && '__nextError' in metadata) {\n      // re-throw caught metadata error from preloading\n      throw metadata['__nextError']\n    }\n  } else if (metadataExport !== null && typeof metadataExport === 'object') {\n    // This metadataExport is the object form\n    metadata = metadataExport\n  }\n\n  return metadata\n}\n\nexport async function accumulateMetadata(\n  metadataItems: MetadataItems,\n  metadataContext: MetadataContext\n): Promise<ResolvedMetadata> {\n  const resolvedMetadata = createDefaultMetadata()\n  const metadataResults: (Metadata | Promise<Metadata>)[] = []\n\n  let titleTemplates: TitleTemplates = {\n    title: null,\n    twitter: null,\n    openGraph: null,\n  }\n\n  // Loop over all metadata items again, merging synchronously any static object exports,\n  // awaiting any static promise exports, and resolving parent metadata and awaiting any generated metadata\n  const dynamicMetadataResolvers = {\n    resolvers: [],\n    resolvingIndex: 0,\n  }\n  const buildState = {\n    warnings: new Set<string>(),\n  }\n\n  let favicon\n\n  // Collect the static icons in the most leaf node,\n  // since we don't collect all the static metadata icons in the parent segments.\n  const leafSegmentStaticIcons = {\n    icon: [],\n    apple: [],\n  }\n  for (let i = 0; i < metadataItems.length; i++) {\n    const staticFilesMetadata = metadataItems[i][1]\n\n    // Treat favicon as special case, it should be the first icon in the list\n    // i <= 1 represents root layout, and if current page is also at root\n    if (i <= 1 && isFavicon(staticFilesMetadata?.icon?.[0])) {\n      const iconMod = staticFilesMetadata?.icon?.shift()\n      if (i === 0) favicon = iconMod\n    }\n\n    const metadata = await getMetadataFromExport<Metadata, ResolvedMetadata>(\n      (metadataItem) => metadataItem[0],\n      dynamicMetadataResolvers,\n      metadataItems,\n      i,\n      resolvedMetadata,\n      metadataResults\n    )\n\n    mergeMetadata({\n      target: resolvedMetadata,\n      source: metadata,\n      metadataContext,\n      staticFilesMetadata,\n      titleTemplates,\n      buildState,\n      leafSegmentStaticIcons,\n    })\n\n    // If the layout is the same layer with page, skip the leaf layout and leaf page\n    // The leaf layout and page are the last two items\n    if (i < metadataItems.length - 2) {\n      titleTemplates = {\n        title: resolvedMetadata.title?.template || null,\n        openGraph: resolvedMetadata.openGraph?.title.template || null,\n        twitter: resolvedMetadata.twitter?.title.template || null,\n      }\n    }\n  }\n\n  if (\n    leafSegmentStaticIcons.icon.length > 0 ||\n    leafSegmentStaticIcons.apple.length > 0\n  ) {\n    if (!resolvedMetadata.icons) {\n      resolvedMetadata.icons = {\n        icon: [],\n        apple: [],\n      }\n      if (leafSegmentStaticIcons.icon.length > 0) {\n        resolvedMetadata.icons.icon.unshift(...leafSegmentStaticIcons.icon)\n      }\n      if (leafSegmentStaticIcons.apple.length > 0) {\n        resolvedMetadata.icons.apple.unshift(...leafSegmentStaticIcons.apple)\n      }\n    }\n  }\n\n  // Only log warnings if there are any, and only once after the metadata resolving process is finished\n  if (buildState.warnings.size > 0) {\n    for (const warning of buildState.warnings) {\n      Log.warn(warning)\n    }\n  }\n\n  return postProcessMetadata(\n    resolvedMetadata,\n    favicon,\n    titleTemplates,\n    metadataContext\n  )\n}\n\nexport async function accumulateViewport(\n  metadataItems: MetadataItems\n): Promise<ResolvedViewport> {\n  const resolvedViewport: ResolvedViewport = createDefaultViewport()\n\n  const viewportResults: (Viewport | Promise<Viewport>)[] = []\n  const dynamicMetadataResolvers = {\n    resolvers: [],\n    resolvingIndex: 0,\n  }\n  for (let i = 0; i < metadataItems.length; i++) {\n    const viewport = await getMetadataFromExport<Viewport, ResolvedViewport>(\n      (metadataItem) => metadataItem[2],\n      dynamicMetadataResolvers,\n      metadataItems,\n      i,\n      resolvedViewport,\n      viewportResults\n    )\n\n    mergeViewport({\n      target: resolvedViewport,\n      source: viewport,\n    })\n  }\n  return resolvedViewport\n}\n\n// Exposed API for metadata component, that directly resolve the loader tree and related context as resolved metadata.\nexport async function resolveMetadata(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore,\n  metadataContext: MetadataContext\n): Promise<ResolvedMetadata> {\n  const metadataItems = await resolveMetadataItems(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    createServerParamsForMetadata,\n    workStore\n  )\n  return accumulateMetadata(metadataItems, metadataContext)\n}\n\n// Exposed API for viewport component, that directly resolve the loader tree and related context as resolved viewport.\nexport async function resolveViewport(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  createServerParamsForMetadata: CreateServerParamsForMetadata,\n  workStore: WorkStore\n): Promise<ResolvedViewport> {\n  const metadataItems = await resolveMetadataItems(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    createServerParamsForMetadata,\n    workStore\n  )\n  return accumulateViewport(metadataItems)\n}\n"], "names": ["accumulateMetadata", "accumulateViewport", "resolveMetadata", "resolveViewport", "isFavicon", "icon", "url", "toString", "startsWith", "type", "mergeStaticMetadata", "source", "target", "staticFilesMetadata", "metadataContext", "titleTemplates", "leafSegmentStaticIcons", "apple", "openGraph", "twitter", "manifest", "hasOwnProperty", "resolvedTwitter", "resolveTwitter", "images", "metadataBase", "isStaticMetadataRouteFile", "resolvedOpenGraph", "resolveOpenGraph", "mergeMetadata", "buildState", "key_", "key", "title", "resolveTitle", "alternates", "resolveAlternates", "facebook", "resolveFacebook", "verification", "resolveVerification", "icons", "resolveIcons", "appleWebApp", "resolveAppleWebApp", "appLinks", "resolveAppLinks", "robots", "resolveRobots", "resolveAsArrayOrUndefined", "authors", "resolveItunes", "itunes", "pagination", "resolvePagination", "other", "Object", "assign", "warnings", "add", "pathname", "mergeViewport", "themeColor", "resolveThemeColor", "colorScheme", "getDefinedViewport", "mod", "props", "tracingProps", "generateViewport", "route", "parent", "getTracer", "trace", "ResolveMetadataSpan", "spanName", "attributes", "viewport", "getDefinedMetadata", "generateMetadata", "metadata", "collectStaticImagesFiles", "undefined", "iconPromises", "map", "imageModule", "interopDefault", "length", "Promise", "all", "flat", "resolveStaticMetadata", "modules", "staticMetadata", "collectMetadata", "tree", "metadataItems", "errorMetadataItem", "errorConvention", "modType", "hasErrorConventionComponent", "Boolean", "getComponentTypeModule", "layoutOrPageMod", "layoutOrPageModType", "getLayoutOrPageModule", "metadataExport", "viewportExport", "push", "errorMod", "errorViewportExport", "errorMetadataExport", "resolveMetadataItems", "cache", "searchParams", "getDynamicParamFromSegment", "createServerParamsForMetadata", "workStore", "parentParams", "treePrefix", "resolveMetadataItemsImpl", "segment", "parallelRoutes", "page", "currentTreePrefix", "isPage", "segmentParam", "currentParams", "value", "param", "params", "layerProps", "filter", "s", "PAGE_SEGMENT_KEY", "join", "childTree", "keys", "isTitleTruthy", "absolute", "hasTitle", "inheritFromMetadata", "description", "commonOgKeys", "postProcessMetadata", "favicon", "autoFillProps", "hasTwTitle", "hasTwDescription", "hasTwImages", "partialTwitter", "unshift", "collectMetadataExportPreloading", "results", "dynamicMetadataExportFn", "resolvers", "result", "resolve", "catch", "err", "__nextError", "getMetadataFromExport", "getPreloadMetadataExport", "dynamicMetadataResolveState", "currentIndex", "resolvedMetadata", "metadataResults", "dynamicMetadataResolvers", "j", "preloadMetadataExport", "resolveParent", "resolvingIndex", "metadataResult", "currentResolvedMetadata", "process", "env", "NODE_ENV", "freeze", "require", "cloneMetadata", "createDefaultMetadata", "Set", "i", "iconMod", "shift", "metadataItem", "template", "size", "warning", "Log", "warn", "resolvedViewport", "createDefaultViewport", "viewportResults"], "mappings": ";;;;;;;;;;;;;;;;;IA0wBsBA,kBAAkB;eAAlBA;;IAwGAC,kBAAkB;eAAlBA;;IA6BAC,eAAe;eAAfA;;IAqBAC,eAAe;eAAfA;;;QA54Bf;uBAEe;iCAIf;kCAC0C;8BACpB;uBACa;8BAInC;gCACwB;+BAWxB;8BACsB;wBACH;2BACU;yBACH;6DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CrB,SAASC,UAAUC,IAAgC;IACjD,IAAI,CAACA,MAAM;QACT,OAAO;IACT;IAEA,yCAAyC;IACzC,OACE,AAACA,CAAAA,KAAKC,GAAG,KAAK,kBACZD,KAAKC,GAAG,CAACC,QAAQ,GAAGC,UAAU,CAAC,gBAAe,KAChDH,KAAKI,IAAI,KAAK;AAElB;AAEA,SAASC,oBACPC,MAAuB,EACvBC,MAAwB,EACxBC,mBAAmC,EACnCC,eAAgC,EAChCC,cAA8B,EAC9BC,sBAAmC;QAenBL,iBAWEA;IAxBlB,IAAI,CAACE,qBAAqB;IAC1B,MAAM,EAAER,IAAI,EAAEY,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE,GAAGP;IAEtD,uDAAuD;IAEvD,IAAIR,MAAM;QACRW,uBAAuBX,IAAI,GAAGA;IAChC;IACA,IAAIY,OAAO;QACTD,uBAAuBC,KAAK,GAAGA;IACjC;IAEA,8FAA8F;IAC9F,IAAIE,WAAW,EAACR,2BAAAA,kBAAAA,OAAQQ,OAAO,qBAAfR,gBAAiBU,cAAc,CAAC,YAAW;QACzD,MAAMC,kBAAkBC,IAAAA,gCAAc,EACpC;YAAE,GAAGX,OAAOO,OAAO;YAAEK,QAAQL;QAAQ,GACrCP,OAAOa,YAAY,EACnB;YAAE,GAAGX,eAAe;YAAEY,2BAA2B;QAAK,GACtDX,eAAeI,OAAO;QAExBP,OAAOO,OAAO,GAAGG;IACnB;IAEA,gGAAgG;IAChG,IAAIJ,aAAa,EAACP,2BAAAA,oBAAAA,OAAQO,SAAS,qBAAjBP,kBAAmBU,cAAc,CAAC,YAAW;QAC7D,MAAMM,oBAAoBC,IAAAA,kCAAgB,EACxC;YAAE,GAAGhB,OAAOM,SAAS;YAAEM,QAAQN;QAAU,GACzCN,OAAOa,YAAY,EACnB;YAAE,GAAGX,eAAe;YAAEY,2BAA2B;QAAK,GACtDX,eAAeG,SAAS;QAE1BN,OAAOM,SAAS,GAAGS;IACrB;IACA,IAAIP,UAAU;QACZR,OAAOQ,QAAQ,GAAGA;IACpB;IAEA,OAAOR;AACT;AAEA,+DAA+D;AAC/D,SAASiB,cAAc,EACrBlB,MAAM,EACNC,MAAM,EACNC,mBAAmB,EACnBE,cAAc,EACdD,eAAe,EACfgB,UAAU,EACVd,sBAAsB,EASvB;IACC,sFAAsF;IACtF,MAAMS,eACJ,QAAOd,0BAAAA,OAAQc,YAAY,MAAK,cAC5Bd,OAAOc,YAAY,GACnBb,OAAOa,YAAY;IACzB,IAAK,MAAMM,QAAQpB,OAAQ;QACzB,MAAMqB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAS;oBACZpB,OAAOqB,KAAK,GAAGC,IAAAA,0BAAY,EAACvB,OAAOsB,KAAK,EAAElB,eAAekB,KAAK;oBAC9D;gBACF;YACA,KAAK;gBAAc;oBACjBrB,OAAOuB,UAAU,GAAGC,IAAAA,gCAAiB,EACnCzB,OAAOwB,UAAU,EACjBV,cACAX;oBAEF;gBACF;YACA,KAAK;gBAAa;oBAChBF,OAAOM,SAAS,GAAGU,IAAAA,kCAAgB,EACjCjB,OAAOO,SAAS,EAChBO,cACAX,iBACAC,eAAeG,SAAS;oBAE1B;gBACF;YACA,KAAK;gBAAW;oBACdN,OAAOO,OAAO,GAAGI,IAAAA,gCAAc,EAC7BZ,OAAOQ,OAAO,EACdM,cACAX,iBACAC,eAAeI,OAAO;oBAExB;gBACF;YACA,KAAK;gBACHP,OAAOyB,QAAQ,GAAGC,IAAAA,8BAAe,EAAC3B,OAAO0B,QAAQ;gBACjD;YAEF,KAAK;gBACHzB,OAAO2B,YAAY,GAAGC,IAAAA,kCAAmB,EAAC7B,OAAO4B,YAAY;gBAC7D;YAEF,KAAK;gBAAS;oBACZ3B,OAAO6B,KAAK,GAAGC,IAAAA,0BAAY,EAAC/B,OAAO8B,KAAK;oBACxC;gBACF;YACA,KAAK;gBACH7B,OAAO+B,WAAW,GAAGC,IAAAA,iCAAkB,EAACjC,OAAOgC,WAAW;gBAC1D;YACF,KAAK;gBACH/B,OAAOiC,QAAQ,GAAGC,IAAAA,8BAAe,EAACnC,OAAOkC,QAAQ;gBACjD;YACF,KAAK;gBAAU;oBACbjC,OAAOmC,MAAM,GAAGC,IAAAA,4BAAa,EAACrC,OAAOoC,MAAM;oBAC3C;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY;oBACfnC,MAAM,CAACoB,IAAI,GAAGiB,IAAAA,gCAAyB,EAACtC,MAAM,CAACqB,IAAI;oBACnD;gBACF;YACA,KAAK;gBAAW;oBACdpB,MAAM,CAACoB,IAAI,GAAGiB,IAAAA,gCAAyB,EAACtC,OAAOuC,OAAO;oBACtD;gBACF;YACA,KAAK;gBAAU;oBACbtC,MAAM,CAACoB,IAAI,GAAGmB,IAAAA,4BAAa,EACzBxC,OAAOyC,MAAM,EACb3B,cACAX;oBAEF;gBACF;YACA,KAAK;gBAAc;oBACjBF,OAAOyC,UAAU,GAAGC,IAAAA,gCAAiB,EACnC3C,OAAO0C,UAAU,EACjB5B,cACAX;oBAEF;gBACF;YACA,+CAA+C;YAC/C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qCAAqC;gBACrCF,MAAM,CAACoB,IAAI,GAAGrB,MAAM,CAACqB,IAAI,IAAI;gBAC7B;YACF,KAAK;gBACHpB,OAAO2C,KAAK,GAAGC,OAAOC,MAAM,CAAC,CAAC,GAAG7C,OAAO2C,KAAK,EAAE5C,OAAO4C,KAAK;gBAC3D;YACF,KAAK;gBACH3C,OAAOa,YAAY,GAAGA;gBACtB;YAEF;gBAAS;oBACP,IACE,AAACO,CAAAA,QAAQ,cACPA,QAAQ,gBACRA,QAAQ,aAAY,KACtBrB,MAAM,CAACqB,IAAI,IAAI,MACf;wBACAF,WAAW4B,QAAQ,CAACC,GAAG,CACrB,CAAC,qBAAqB,EAAE3B,IAAI,qCAAqC,EAAElB,gBAAgB8C,QAAQ,CAAC,8HAA8H,CAAC;oBAE/N;oBACA;gBACF;QACF;IACF;IACAlD,oBACEC,QACAC,QACAC,qBACAC,iBACAC,gBACAC;AAEJ;AAEA,SAAS6C,cAAc,EACrBjD,MAAM,EACND,MAAM,EAIP;IACC,IAAI,CAACA,QAAQ;IACb,IAAK,MAAMoB,QAAQpB,OAAQ;QACzB,MAAMqB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAc;oBACjBpB,OAAOkD,UAAU,GAAGC,IAAAA,gCAAiB,EAACpD,OAAOmD,UAAU;oBACvD;gBACF;YACA,KAAK;gBACHlD,OAAOoD,WAAW,GAAGrD,OAAOqD,WAAW,IAAI;gBAC3C;YACF;gBACE,6CAA6C;gBAC7C,iCAAiC;gBACjCpD,MAAM,CAACoB,IAAI,GAAGrB,MAAM,CAACqB,IAAI;gBACzB;QACJ;IACF;AACF;AAEA,SAASiC,mBACPC,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,IAAI,OAAOF,IAAIG,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAEC,KAAK,EAAE,GAAGF;QAClB,OAAO,CAACG,SACNC,IAAAA,iBAAS,IAAGC,KAAK,CACfC,8BAAmB,CAACL,gBAAgB,EACpC;gBACEM,UAAU,CAAC,iBAAiB,EAAEL,OAAO;gBACrCM,YAAY;oBACV,aAAaN;gBACf;YACF,GACA,IAAMJ,IAAIG,gBAAgB,CAACF,OAAOI;IAExC;IACA,OAAOL,IAAIW,QAAQ,IAAI;AACzB;AAEA,SAASC,mBACPZ,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,IAAI,OAAOF,IAAIa,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAET,KAAK,EAAE,GAAGF;QAClB,OAAO,CAACG,SACNC,IAAAA,iBAAS,IAAGC,KAAK,CACfC,8BAAmB,CAACK,gBAAgB,EACpC;gBACEJ,UAAU,CAAC,iBAAiB,EAAEL,OAAO;gBACrCM,YAAY;oBACV,aAAaN;gBACf;YACF,GACA,IAAMJ,IAAIa,gBAAgB,CAACZ,OAAOI;IAExC;IACA,OAAOL,IAAIc,QAAQ,IAAI;AACzB;AAEA,eAAeC,yBACbD,QAAmC,EACnCb,KAAU,EACV1D,IAAkD;QAU7C;IARL,IAAI,EAACuE,4BAAAA,QAAU,CAACvE,KAAK,GAAE,OAAOyE;IAE9B,MAAMC,eAAeH,QAAQ,CAACvE,KAAyB,CAAC2E,GAAG,CACzD,OAAOC,cACLC,IAAAA,8BAAc,EAAC,MAAMD,YAAYlB;IAGrC,OAAOgB,CAAAA,gCAAAA,aAAcI,MAAM,IAAG,KACzB,QAAA,MAAMC,QAAQC,GAAG,CAACN,kCAAnB,AAAC,MAAkCO,IAAI,KACvCR;AACN;AAEA,eAAeS,sBACbC,OAAsB,EACtBzB,KAAU;IAEV,MAAM,EAAEa,QAAQ,EAAE,GAAGY;IACrB,IAAI,CAACZ,UAAU,OAAO;IAEtB,MAAM,CAAC3E,MAAMY,OAAOC,WAAWC,QAAQ,GAAG,MAAMqE,QAAQC,GAAG,CAAC;QAC1DR,yBAAyBD,UAAUb,OAAO;QAC1Cc,yBAAyBD,UAAUb,OAAO;QAC1Cc,yBAAyBD,UAAUb,OAAO;QAC1Cc,yBAAyBD,UAAUb,OAAO;KAC3C;IAED,MAAM0B,iBAAiB;QACrBxF;QACAY;QACAC;QACAC;QACAC,UAAU4D,SAAS5D,QAAQ;IAC7B;IAEA,OAAOyE;AACT;AAEA,4FAA4F;AAC5F,eAAeC,gBAAgB,EAC7BC,IAAI,EACJC,aAAa,EACbC,iBAAiB,EACjB9B,KAAK,EACLG,KAAK,EACL4B,eAAe,EAQhB;IACC,IAAIhC;IACJ,IAAIiC;IACJ,MAAMC,8BAA8BC,QAClCH,mBAAmBH,IAAI,CAAC,EAAE,CAACG,gBAAgB;IAE7C,IAAIA,iBAAiB;QACnBhC,MAAM,MAAMoC,IAAAA,oCAAsB,EAACP,MAAM;QACzCI,UAAUD;IACZ,OAAO;QACL,MAAM,EAAEhC,KAAKqC,eAAe,EAAEJ,SAASK,mBAAmB,EAAE,GAC1D,MAAMC,IAAAA,mCAAqB,EAACV;QAC9B7B,MAAMqC;QACNJ,UAAUK;IACZ;IAEA,IAAIL,SAAS;QACX7B,SAAS,CAAC,CAAC,EAAE6B,SAAS;IACxB;IAEA,MAAMtF,sBAAsB,MAAM8E,sBAAsBI,IAAI,CAAC,EAAE,EAAE5B;IACjE,MAAMuC,iBAAiBxC,MAAMY,mBAAmBZ,KAAKC,OAAO;QAAEG;IAAM,KAAK;IAEzE,MAAMqC,iBAAiBzC,MAAMD,mBAAmBC,KAAKC,OAAO;QAAEG;IAAM,KAAK;IAEzE0B,cAAcY,IAAI,CAAC;QAACF;QAAgB7F;QAAqB8F;KAAe;IAExE,IAAIP,+BAA+BF,iBAAiB;QAClD,MAAMW,WAAW,MAAMP,IAAAA,oCAAsB,EAACP,MAAMG;QACpD,MAAMY,sBAAsBD,WACxB5C,mBAAmB4C,UAAU1C,OAAO;YAAEG;QAAM,KAC5C;QACJ,MAAMyC,sBAAsBF,WACxB/B,mBAAmB+B,UAAU1C,OAAO;YAAEG;QAAM,KAC5C;QAEJ2B,iBAAiB,CAAC,EAAE,GAAGc;QACvBd,iBAAiB,CAAC,EAAE,GAAGpF;QACvBoF,iBAAiB,CAAC,EAAE,GAAGa;IACzB;AACF;AAEA,MAAME,uBAAuBC,IAAAA,YAAK,EAAC,eACjClB,IAAgB,EAChBmB,YAAqC,EACrChB,eAA8C,EAC9CiB,0BAAsD,EACtDC,6BAA4D,EAC5DC,SAAoB;IAEpB,MAAMC,eAAe,CAAC;IACtB,MAAMtB,gBAA+B,EAAE;IACvC,MAAMC,oBAA2C;QAAC;QAAM;QAAM;KAAK;IACnE,MAAMsB,aAAarC;IACnB,OAAOsC,yBACLxB,eACAD,MACAwB,YACAD,cACAJ,cACAhB,iBACAD,mBACAkB,4BACAC,+BACAC;AAEJ;AAEA,eAAeG,yBACbxB,aAA4B,EAC5BD,IAAgB,EAChB,6FAA6F,GAC7FwB,UAAgC,EAChCD,YAAoB,EACpBJ,YAAqC,EACrChB,eAA8C,EAC9CD,iBAAwC,EACxCkB,0BAAsD,EACtDC,6BAA4D,EAC5DC,SAAoB;IAEpB,MAAM,CAACI,SAASC,gBAAgB,EAAEC,IAAI,EAAE,CAAC,GAAG5B;IAC5C,MAAM6B,oBACJL,cAAcA,WAAWhC,MAAM,GAAG;WAAIgC;QAAYE;KAAQ,GAAG;QAACA;KAAQ;IACxE,MAAMI,SAAS,OAAOF,SAAS;IAE/B,iCAAiC;IACjC,MAAMG,eAAeX,2BAA2BM;IAChD;;GAEC,GACD,IAAIM,gBAAgBT;IACpB,IAAIQ,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGT,YAAY;YACf,CAACQ,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAME,SAASd,8BAA8BW,eAAeV;IAE5D,IAAIc;IACJ,IAAIN,QAAQ;QACVM,aAAa;YACXD;YACAhB;QACF;IACF,OAAO;QACLiB,aAAa;YACXD;QACF;IACF;IAEA,MAAMpC,gBAAgB;QACpBC;QACAC;QACAC;QACAC;QACA/B,OAAOgE;QACP7D,OAAOsD,iBACL,yCAAyC;SACxCQ,MAAM,CAAC,CAACC,IAAMA,MAAMC,yBAAgB,EACpCC,IAAI,CAAC;IACV;IAEA,IAAK,MAAMvG,OAAO0F,eAAgB;QAChC,MAAMc,YAAYd,cAAc,CAAC1F,IAAI;QACrC,MAAMwF,yBACJxB,eACAwC,WACAZ,mBACAG,eACAb,cACAhB,iBACAD,mBACAkB,4BACAC,+BACAC;IAEJ;IAEA,IAAI7D,OAAOiF,IAAI,CAACf,gBAAgBnC,MAAM,KAAK,KAAKW,iBAAiB;QAC/D,0EAA0E;QAC1E,qCAAqC;QACrCF,cAAcY,IAAI,CAACX;IACrB;IAEA,OAAOD;AACT;AAKA,MAAM0C,gBAAgB,CAACzG,QACrB,CAAC,EAACA,yBAAAA,MAAO0G,QAAQ;AACnB,MAAMC,WAAW,CAAC5D,WAA+B0D,cAAc1D,4BAAAA,SAAU/C,KAAK;AAE9E,SAAS4G,oBACPjI,MAA4C,EAC5CoE,QAA0B;IAE1B,IAAIpE,QAAQ;QACV,IAAI,CAACgI,SAAShI,WAAWgI,SAAS5D,WAAW;YAC3CpE,OAAOqB,KAAK,GAAG+C,SAAS/C,KAAK;QAC/B;QACA,IAAI,CAACrB,OAAOkI,WAAW,IAAI9D,SAAS8D,WAAW,EAAE;YAC/ClI,OAAOkI,WAAW,GAAG9D,SAAS8D,WAAW;QAC3C;IACF;AACF;AAEA,6DAA6D;AAC7D,MAAMC,eAAe;IAAC;IAAS;IAAe;CAAS;AACvD,SAASC,oBACPhE,QAA0B,EAC1BiE,OAAY,EACZlI,cAA8B,EAC9BD,eAAgC;IAEhC,MAAM,EAAEI,SAAS,EAAEC,OAAO,EAAE,GAAG6D;IAE/B,IAAI9D,WAAW;QACb,kEAAkE;QAClE,wCAAwC;QACxC,IAAIgI,gBAIC,CAAC;QACN,MAAMC,aAAaP,SAASzH;QAC5B,MAAMiI,mBAAmBjI,2BAAAA,QAAS2H,WAAW;QAC7C,MAAMO,cAAchD,QAClBlF,CAAAA,2BAAAA,QAASE,cAAc,CAAC,cAAaF,QAAQK,MAAM;QAErD,IAAI,CAAC2H,YAAY;YACf,IAAIT,cAAcxH,UAAUe,KAAK,GAAG;gBAClCiH,cAAcjH,KAAK,GAAGf,UAAUe,KAAK;YACvC,OAAO,IAAI+C,SAAS/C,KAAK,IAAIyG,cAAc1D,SAAS/C,KAAK,GAAG;gBAC1DiH,cAAcjH,KAAK,GAAG+C,SAAS/C,KAAK;YACtC;QACF;QACA,IAAI,CAACmH,kBACHF,cAAcJ,WAAW,GACvB5H,UAAU4H,WAAW,IAAI9D,SAAS8D,WAAW,IAAI5D;QACrD,IAAI,CAACmE,aAAaH,cAAc1H,MAAM,GAAGN,UAAUM,MAAM;QAEzD,IAAIgC,OAAOiF,IAAI,CAACS,eAAe3D,MAAM,GAAG,GAAG;YACzC,MAAM+D,iBAAiB/H,IAAAA,gCAAc,EACnC2H,eACAlE,SAASvD,YAAY,EACrBX,iBACAC,eAAeI,OAAO;YAExB,IAAI6D,SAAS7D,OAAO,EAAE;gBACpB6D,SAAS7D,OAAO,GAAGqC,OAAOC,MAAM,CAAC,CAAC,GAAGuB,SAAS7D,OAAO,EAAE;oBACrD,GAAI,CAACgI,cAAc;wBAAElH,KAAK,EAAEqH,kCAAAA,eAAgBrH,KAAK;oBAAC,CAAC;oBACnD,GAAI,CAACmH,oBAAoB;wBACvBN,WAAW,EAAEQ,kCAAAA,eAAgBR,WAAW;oBAC1C,CAAC;oBACD,GAAI,CAACO,eAAe;wBAAE7H,MAAM,EAAE8H,kCAAAA,eAAgB9H,MAAM;oBAAC,CAAC;gBACxD;YACF,OAAO;gBACLwD,SAAS7D,OAAO,GAAGmI;YACrB;QACF;IACF;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/CT,oBAAoB3H,WAAW8D;IAC/B6D,oBAAoB1H,SAAS6D;IAE7B,IAAIiE,SAAS;QACX,IAAI,CAACjE,SAASvC,KAAK,EAAE;YACnBuC,SAASvC,KAAK,GAAG;gBACfpC,MAAM,EAAE;gBACRY,OAAO,EAAE;YACX;QACF;QAEA+D,SAASvC,KAAK,CAACpC,IAAI,CAACkJ,OAAO,CAACN;IAC9B;IAEA,OAAOjE;AACT;AAMA,SAASwE,gCACPC,OAAiC,EACjCC,uBAAyD,EACzDC,SAA4C;IAE5C,MAAMC,SAASF,wBACb,IAAIlE,QAAa,CAACqE;QAChBF,UAAU/C,IAAI,CAACiD;IACjB;IAGF,IAAID,kBAAkBpE,SAAS;QAC7B,8CAA8C;QAC9C,+CAA+C;QAC/C,4CAA4C;QAC5C,oDAAoD;QACpDoE,OAAOE,KAAK,CAAC,CAACC;YACZ,OAAO;gBACLC,aAAaD;YACf;QACF;IACF;IACAN,QAAQ7C,IAAI,CAACgD;AACf;AAEA,eAAeK,sBACbC,wBAEmD,EACnDC,2BAGC,EACDnE,aAA4B,EAC5BoE,YAAoB,EACpBC,gBAA8B,EAC9BC,eAAyC;IAEzC,MAAM5D,iBAAiBwD,yBAAyBlE,aAAa,CAACoE,aAAa;IAC3E,MAAMG,2BAA2BJ,4BAA4BR,SAAS;IACtE,IAAI3E,WAAwB;IAC5B,IAAI,OAAO0B,mBAAmB,YAAY;QACxC,wDAAwD;QACxD,IAAI,CAAC6D,yBAAyBhF,MAAM,EAAE;YACpC,IAAK,IAAIiF,IAAIJ,cAAcI,IAAIxE,cAAcT,MAAM,EAAEiF,IAAK;gBACxD,MAAMC,wBAAwBP,yBAAyBlE,aAAa,CAACwE,EAAE;gBACvE,6EAA6E;gBAC7E,IAAI,OAAOC,0BAA0B,YAAY;oBAC/CjB,gCACEc,iBACAG,uBACAF;gBAEJ;YACF;QACF;QAEA,MAAMG,gBACJH,wBAAwB,CAACJ,4BAA4BQ,cAAc,CAAC;QACtE,MAAMC,iBACJN,eAAe,CAACH,4BAA4BQ,cAAc,GAAG;QAE/D,uFAAuF;QACvF,qEAAqE;QACrE,MAAME,0BACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrBxH,OAAOyH,MAAM,CACXC,QAAQ,oBAAoBC,aAAa,CAACd,qBAE5CA;QAEN,qFAAqF;QACrF,8FAA8F;QAC9F,mGAAmG;QACnGK,cAAcG;QACd7F,WACE4F,0BAA0BpF,UAAU,MAAMoF,iBAAiBA;QAE7D,IAAI5F,YAAY,OAAOA,aAAa,YAAY,iBAAiBA,UAAU;YACzE,iDAAiD;YACjD,MAAMA,QAAQ,CAAC,cAAc;QAC/B;IACF,OAAO,IAAI0B,mBAAmB,QAAQ,OAAOA,mBAAmB,UAAU;QACxE,yCAAyC;QACzC1B,WAAW0B;IACb;IAEA,OAAO1B;AACT;AAEO,eAAehF,mBACpBgG,aAA4B,EAC5BlF,eAAgC;IAEhC,MAAMuJ,mBAAmBe,IAAAA,sCAAqB;IAC9C,MAAMd,kBAAoD,EAAE;IAE5D,IAAIvJ,iBAAiC;QACnCkB,OAAO;QACPd,SAAS;QACTD,WAAW;IACb;IAEA,uFAAuF;IACvF,yGAAyG;IACzG,MAAMqJ,2BAA2B;QAC/BZ,WAAW,EAAE;QACbgB,gBAAgB;IAClB;IACA,MAAM7I,aAAa;QACjB4B,UAAU,IAAI2H;IAChB;IAEA,IAAIpC;IAEJ,kDAAkD;IAClD,+EAA+E;IAC/E,MAAMjI,yBAAyB;QAC7BX,MAAM,EAAE;QACRY,OAAO,EAAE;IACX;IACA,IAAK,IAAIqK,IAAI,GAAGA,IAAItF,cAAcT,MAAM,EAAE+F,IAAK;YAKrBzK;QAJxB,MAAMA,sBAAsBmF,aAAa,CAACsF,EAAE,CAAC,EAAE;QAE/C,yEAAyE;QACzE,qEAAqE;QACrE,IAAIA,KAAK,KAAKlL,UAAUS,wCAAAA,4BAAAA,oBAAqBR,IAAI,qBAAzBQ,yBAA2B,CAAC,EAAE,GAAG;gBACvCA;YAAhB,MAAM0K,UAAU1K,wCAAAA,6BAAAA,oBAAqBR,IAAI,qBAAzBQ,2BAA2B2K,KAAK;YAChD,IAAIF,MAAM,GAAGrC,UAAUsC;QACzB;QAEA,MAAMvG,WAAW,MAAMiF,sBACrB,CAACwB,eAAiBA,YAAY,CAAC,EAAE,EACjClB,0BACAvE,eACAsF,GACAjB,kBACAC;QAGFzI,cAAc;YACZjB,QAAQyJ;YACR1J,QAAQqE;YACRlE;YACAD;YACAE;YACAe;YACAd;QACF;QAEA,gFAAgF;QAChF,kDAAkD;QAClD,IAAIsK,IAAItF,cAAcT,MAAM,GAAG,GAAG;gBAEvB8E,yBACIA,6BACFA;YAHXtJ,iBAAiB;gBACfkB,OAAOoI,EAAAA,0BAAAA,iBAAiBpI,KAAK,qBAAtBoI,wBAAwBqB,QAAQ,KAAI;gBAC3CxK,WAAWmJ,EAAAA,8BAAAA,iBAAiBnJ,SAAS,qBAA1BmJ,4BAA4BpI,KAAK,CAACyJ,QAAQ,KAAI;gBACzDvK,SAASkJ,EAAAA,4BAAAA,iBAAiBlJ,OAAO,qBAAxBkJ,0BAA0BpI,KAAK,CAACyJ,QAAQ,KAAI;YACvD;QACF;IACF;IAEA,IACE1K,uBAAuBX,IAAI,CAACkF,MAAM,GAAG,KACrCvE,uBAAuBC,KAAK,CAACsE,MAAM,GAAG,GACtC;QACA,IAAI,CAAC8E,iBAAiB5H,KAAK,EAAE;YAC3B4H,iBAAiB5H,KAAK,GAAG;gBACvBpC,MAAM,EAAE;gBACRY,OAAO,EAAE;YACX;YACA,IAAID,uBAAuBX,IAAI,CAACkF,MAAM,GAAG,GAAG;gBAC1C8E,iBAAiB5H,KAAK,CAACpC,IAAI,CAACkJ,OAAO,IAAIvI,uBAAuBX,IAAI;YACpE;YACA,IAAIW,uBAAuBC,KAAK,CAACsE,MAAM,GAAG,GAAG;gBAC3C8E,iBAAiB5H,KAAK,CAACxB,KAAK,CAACsI,OAAO,IAAIvI,uBAAuBC,KAAK;YACtE;QACF;IACF;IAEA,qGAAqG;IACrG,IAAIa,WAAW4B,QAAQ,CAACiI,IAAI,GAAG,GAAG;QAChC,KAAK,MAAMC,WAAW9J,WAAW4B,QAAQ,CAAE;YACzCmI,KAAIC,IAAI,CAACF;QACX;IACF;IAEA,OAAO5C,oBACLqB,kBACApB,SACAlI,gBACAD;AAEJ;AAEO,eAAeb,mBACpB+F,aAA4B;IAE5B,MAAM+F,mBAAqCC,IAAAA,sCAAqB;IAEhE,MAAMC,kBAAoD,EAAE;IAC5D,MAAM1B,2BAA2B;QAC/BZ,WAAW,EAAE;QACbgB,gBAAgB;IAClB;IACA,IAAK,IAAIW,IAAI,GAAGA,IAAItF,cAAcT,MAAM,EAAE+F,IAAK;QAC7C,MAAMzG,WAAW,MAAMoF,sBACrB,CAACwB,eAAiBA,YAAY,CAAC,EAAE,EACjClB,0BACAvE,eACAsF,GACAS,kBACAE;QAGFpI,cAAc;YACZjD,QAAQmL;YACRpL,QAAQkE;QACV;IACF;IACA,OAAOkH;AACT;AAGO,eAAe7L,gBACpB6F,IAAgB,EAChBmB,YAAqC,EACrChB,eAA8C,EAC9CiB,0BAAsD,EACtDC,6BAA4D,EAC5DC,SAAoB,EACpBvG,eAAgC;IAEhC,MAAMkF,gBAAgB,MAAMgB,qBAC1BjB,MACAmB,cACAhB,iBACAiB,4BACAC,+BACAC;IAEF,OAAOrH,mBAAmBgG,eAAelF;AAC3C;AAGO,eAAeX,gBACpB4F,IAAgB,EAChBmB,YAAqC,EACrChB,eAA8C,EAC9CiB,0BAAsD,EACtDC,6BAA4D,EAC5DC,SAAoB;IAEpB,MAAMrB,gBAAgB,MAAMgB,qBAC1BjB,MACAmB,cACAhB,iBACAiB,4BACAC,+BACAC;IAEF,OAAOpH,mBAAmB+F;AAC5B"}