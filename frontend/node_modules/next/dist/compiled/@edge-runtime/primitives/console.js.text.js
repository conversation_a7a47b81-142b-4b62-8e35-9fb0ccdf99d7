module.exports = "\"use strict\";var Gr=Object.create,E=Object.defineProperty,Rr=Object.getOwnPropertyDescriptor,T=Object.getOwnPropertyNames,kr=Object.getPrototypeOf,Mr=Object.prototype.hasOwnProperty,a=(f,y)=>E(f,\"name\",{value:y,configurable:!0}),xr=(f,y)=>function(){return f&&(y=(0,f[T(f)[0]])(f=0)),y},Cr=(f,y)=>function(){return y||(0,f[T(f)[0]])((y={exports:{}}).exports,y),y.exports},Lr=(f,y)=>{for(var d in y)E(f,d,{get:y[d],enumerable:!0})},ir=(f,y,d,I)=>{if(y&&typeof y==\"object\"||typeof y==\"function\")for(let j of T(y))!Mr.call(f,j)&&j!==d&&E(f,j,{get:()=>y[j],enumerable:!(I=Rr(y,j))||I.enumerable});return f},vr=(f,y,d)=>(d=f!=null?Gr(kr(f)):{},ir(y||!f||!f.__esModule?E(d,\"default\",{value:f,enumerable:!0}):d,f)),Tr=f=>ir(E({},\"__esModule\",{value:!0}),f),cr=xr({\"<define:process>\"(){}}),Br=Cr({\"../format/dist/index.js\"(f,y){\"use strict\";cr();var d=Object.defineProperty,I=Object.getOwnPropertyDescriptor,j=Object.getOwnPropertyNames,ar=Object.prototype.hasOwnProperty,fr=a((r,s)=>{for(var i in s)d(r,i,{get:s[i],enumerable:!0})},\"__export\"),pr=a((r,s,i,l)=>{if(s&&typeof s==\"object\"||typeof s==\"function\")for(let u of j(s))!ar.call(r,u)&&u!==i&&d(r,u,{get:()=>s[u],enumerable:!(l=I(s,u))||l.enumerable});return r},\"__copyProps\"),lr=a(r=>pr(d({},\"__esModule\",{value:!0}),r),\"__toCommonJS\"),B={};fr(B,{createFormat:()=>J}),y.exports=lr(B);var yr=Reflect.getOwnPropertyDescriptor;function N(r,s){let i=yr(r,s);return i?i.get:void 0}a(N,\"GetOwnGetter\");var gr=Reflect.getPrototypeOf,ur=gr(Uint8Array),br=Array.prototype.filter,D=Array.prototype.push,mr=Date.prototype.getTime,dr=Date.prototype.toISOString,Sr=Object.getOwnPropertyDescriptors,F=Object.getOwnPropertyNames,Or=Object.getOwnPropertySymbols,_r=Object.keys,hr=Object.prototype.propertyIsEnumerable,jr=Object.prototype.toString,Pr=N(Map.prototype,\"size\"),wr=N(Set.prototype,\"size\"),Ar=String.prototype.includes,$r=Symbol.iterator,Er=Symbol.prototype.toString,Ir=N(ur.prototype,\"length\"),Nr=new Set([\"[object BigInt64Array]\",\"[object BigUint64Array]\",\"[object Float32Array]\",\"[object Float64Array]\",\"[object Int8Array]\",\"[object Int16Array]\",\"[object Int32Array]\",\"[object Uint8Array]\",\"[object Uint8ClampedArray]\",\"[object Uint16Array]\",\"[object Uint32Array]\"]);function R(r,s){let i=Array.isArray(r)||z(r)?new Set([...r.keys()].map(l=>l.toString())):void 0;return Object.entries(Sr(r)).filter(([l,u])=>!(i&&i.has(l)||s===1&&!u.enumerable)).map(([l])=>l)}a(R,\"getOwnNonIndexProperties\");var z=a(r=>g(r,\"object\")&&Nr.has(jr.call(r)),\"isTypedArray\");function g(r,s){return typeof r===s}a(g,\"kind\");var Dr=a(r=>{var s;return(s=r.constructor)==null?void 0:s.name},\"getConstructorName\"),P=a((r=\"\",s=\"\")=>`${r}${s} `,\"getPrefix\");function J(r={}){r.customInspectSymbol===void 0&&(r.customInspectSymbol=Symbol.for(\"edge-runtime.inspect.custom\")),r.formatError===void 0&&(r.formatError=n=>{var t;let p=(t=n.stack)!=null?t:Error.prototype.toString.call(n);return String(p)});let{formatError:s,customInspectSymbol:i}=r;function l(...n){let[t]=n;if(!g(t,\"string\"))return w(t,i)?l(t[i]({format:l})):n.map(o=>A(o,{customInspectSymbol:i})).join(\" \");let p=1,c=String(t).replace(/%[sjdOoif%]/g,o=>{if(o===\"%%\")return\"%\";if(p>=n.length)return o;switch(o){case\"%s\":{let e=n[p++];return w(e,i)?l(e[i]({format:l})):M(e)||x(e)||g(e,\"bigint\")?l(e):String(e)}case\"%j\":return Y(n[p++]);case\"%d\":{let e=n[p++];return g(e,\"bigint\")?l(e):String(Number(e))}case\"%O\":return A(n[p++],{customInspectSymbol:i});case\"%o\":return A(n[p++],{customInspectSymbol:i,showHidden:!0,depth:4});case\"%i\":{let e=n[p++];return g(e,\"bigint\")?l(e):String(parseInt(e,10))}case\"%f\":return String(parseFloat(n[p++]));default:return o}});for(let o=n[p];p<n.length;o=n[++p])o===null||!g(o,\"object\")?c+=\" \"+o:c+=\" \"+A(o);return c}a(l,\"format\");function u(n,t,p){if(w(t,i))return l(t[i]({format:l}));let c=U(t);if(c!==void 0)return c;if(n.seen.includes(t)){let o=1;return n.circular===void 0?(n.circular=new Map,n.circular.set(t,o)):(o=n.circular.get(t),o===void 0&&(o=n.circular.size+1,n.circular.set(t,o))),`[Circular *${o}]`}return h(n,t,p)}a(u,\"formatValue\");function h(n,t,p){let c=[],o=Dr(t),e=\"\",S=a(()=>[],\"formatter\"),m=[\"\",\"\"],O=!0,tr=n.showHidden?0:1;if($r in t)if(O=!1,Array.isArray(t)){let b=o!==\"Array\"?P(o,`(${t.length})`):\"\";if(c=R(t,tr),m=[`${b}[`,\"]\"],t.length===0&&c.length===0)return`${m[0]}]`;S=Q}else if(q(t)){let b=wr.call(t),_=P(o,`(${b})`);if(c=G(t,n.showHidden),S=Z,b===0&&c.length===0)return`${_}{}`;m=[`${_}{`,\"}\"]}else if(K(t)){let b=Pr.call(t),_=P(o,`(${b})`);if(c=G(t,n.showHidden),S=rr,b===0&&c.length===0)return`${_}{}`;m=[`${_}{`,\"}\"]}else if(z(t)){c=R(t,tr);let b=Ir.call(t);if(m=[`${P(o,`(${b})`)}[`,\"]\"],t.length===0&&c.length===0)return`${m[0]}]`;S=X.bind(null,b)}else O=!0;if(O)if(c=G(t,n.showHidden),m=[\"{\",\"}\"],o===void 0){if(c.length===0)return\"[Object: null prototype] {}\"}else if(o===\"Object\"){if(c.length===0)return\"{}\"}else if(g(t,\"function\")){if(e=`[Function${t.name?\": \"+t.name:\"\"}]`,c.length===0)return e}else if(k(t)){if(e=RegExp.prototype.toString.call(t),c.length===0)return e;e=\" \"+e}else if(M(t)){if(e=Number.isNaN(mr.call(t))?Date.prototype.toString.call(t):dr.call(t),c.length===0)return e;e=\" \"+e}else if(x(t))e=s(t),c=c.filter(b=>b!==\"name\");else if(w(t,n.customInspectSymbol)){if(e=l(t[n.customInspectSymbol]({format:l})),c.length===0)return e;e=\" \"+e}else m[0]=`${P(o)}{`;if(p&&p<0)return k(t)?RegExp.prototype.toString.call(t):\"[Object]\";n.seen.push(t);let er=new Set(c),nr=S(n,t,p,er,c);for(let b=0;b<c.length;b++)nr.push(L(n,t,p,er,c[b],!1));if(n.circular!==void 0){let b=n.circular.get(t);if(b!==void 0){let _=`<ref *${b}>`;e=e===\"\"?_:`${_} ${e}`}}return n.seen.pop(),W(nr,e,m)}a(h,\"formatRaw\");function A(n,t){return t=Object.assign({seen:[],depth:2},t),u(t,n,t.depth)}a(A,\"inspect\");function L(n,t,p,c,o,e){let S,m,O=Object.getOwnPropertyDescriptor(t,o)||{value:t[o]};return O.value!==void 0?m=u(n,O.value,p):O.get?m=O.set?\"[Getter/Setter]\":\"[Getter]\":O.set?m=\"[Setter]\":m=\"undefined\",e?m:(g(o,\"symbol\")?S=`[${Er.call(o)}]`:c.has(o)?S=o:S=\"[\"+o+\"]\",`${S}: ${m}`)}a(L,\"formatProperty\");function Q(n,t,p,c){let o=[];for(let e=0;e<t.length;++e)Object.prototype.hasOwnProperty.call(t,String(e))?o.push(L(n,t,p,c,String(e),!0)):o.push(\"\");return o}a(Q,\"formatArray\");function X(n,t,p,c){let o=new Array(n);for(let e=0;e<n;++e)o[e]=p.length>0&&g(p[0],\"number\")?String(p[e]):H(p[e]);if(t.showHidden)for(let e of[\"BYTES_PER_ELEMENT\",\"length\",\"byteLength\",\"byteOffset\",\"buffer\"]){let S=u(t,p[e],c);D.call(o,`[${String(e)}]: ${S}`)}return o}a(X,\"formatTypedArray\");function Z(n,t,p){let c=[];for(let o of t)D.call(c,u(n,o,p));return c}a(Z,\"formatSet\");function rr(n,t,p){let c=[];for(let{0:o,1:e}of t)c.push(`${u(n,o,p)} => ${u(n,e,p)}`);return c}return a(rr,\"formatMap\"),l}a(J,\"createFormat\");var H=a(r=>`${r}n`,\"formatBigInt\");function U(r){if(r===null)return\"null\";if(r===void 0)return\"undefined\";if(g(r,\"string\"))return`'${JSON.stringify(r).replace(/^\"|\"$/g,\"\").replace(/'/g,\"\\\\'\").replace(/\\\\\"/g,'\"')}'`;if(g(r,\"boolean\")||g(r,\"number\"))return\"\"+r;if(g(r,\"bigint\"))return H(r);if(g(r,\"symbol\"))return r.toString()}a(U,\"formatPrimitive\");function w(r,s){return r!==null&&g(r,\"object\")&&s in r&&g(r[s],\"function\")}a(w,\"hasCustomSymbol\");function k(r){return g(r,\"object\")&&Object.prototype.toString.call(r)===\"[object RegExp]\"}a(k,\"isRegExp\");function M(r){return g(r,\"object\")&&Object.prototype.toString.call(r)===\"[object Date]\"}a(M,\"isDate\");function x(r){return g(r,\"object\")&&(Object.prototype.toString.call(r)===\"[object Error]\"||r instanceof Error)}a(x,\"isError\");function K(r){return g(r,\"object\")&&Object.prototype.toString.call(r)===\"[object Map]\"}a(K,\"isMap\");function q(r){return g(r,\"object\")&&Object.prototype.toString.call(r)===\"[object Set]\"}a(q,\"isSet\");function V(r,s,i){let u=r.length+s;if(u+r.length>80)return!1;for(let h=0;h<r.length;h++)if(u+=r[h].length,u>80)return!1;return i===\"\"||!Ar.call(i,`\n`)}a(V,\"isBelowBreakLength\");function W(r,s,i){let l=r.length+i[0].length+s.length+10;return V(r,l,s)?((s?s+\" \":\"\")+i[0]+\" \"+r.join(\", \")+\" \"+i[1]).trim():(s?s+\" \":\"\")+i[0]+`\n  `+r.join(`,\n  `)+`\n`+i[1]}a(W,\"reduceToSingleString\");function Y(r){return Array.isArray(r)&&(r=r.map(s=>JSON.parse(JSON.stringify(s,C())))),JSON.stringify(r,C())}a(Y,\"safeStringify\");function C(){let r=new WeakSet;return(s,i)=>{if(i!==null&&g(i,\"object\")){if(r.has(i))return\"[Circular]\";r.add(i)}return i}}a(C,\"makeCircularReplacer\");function G(r,s=!1){let i,l=Or(r);if(s)i=F(r),l.length!==0&&D.apply(i,l);else{try{i=_r(r)}catch{i=F(r)}if(l.length!==0){let u=a(h=>hr.call(r,h),\"filter\");D.apply(i,br.call(l,u))}}return i}a(G,\"getKeys\")}}),sr={};Lr(sr,{console:()=>Wr});module.exports=Tr(sr);cr();var Fr=vr(Br()),$=(0,Fr.createFormat)(),zr=console.error.bind(console),Jr=console.log.bind(console),Hr=console.assert.bind(console),Ur=console.time.bind(console),Kr=console.timeEnd.bind(console),qr=console.timeLog.bind(console),Vr=console.trace.bind(console),or=a((...f)=>zr($(...f)),\"error\"),v=a((...f)=>Jr($(...f)),\"log\"),Wr={assert:(f,...y)=>Hr(f,$(...y)),count:console.count.bind(console),debug:v,dir:console.dir.bind(console),error:or,info:v,log:v,time:(...f)=>Ur($(...f)),timeEnd:(...f)=>Kr($(...f)),timeLog:qr,trace:Vr,warn:or};\n"