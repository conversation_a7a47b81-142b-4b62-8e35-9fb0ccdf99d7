{"version": 3, "file": "pages.runtime.prod.js", "mappings": "+EACA,IAAIA,EAAYC,OAAOC,cAAc,CACjCC,EAAmBF,OAAOG,wBAAwB,CAClDC,EAAoBJ,OAAOK,mBAAmB,CAC9CC,EAAeN,OAAOO,SAAS,CAACC,cAAc,CAgB9CC,EAAc,CAAC,EAWnB,SAASC,EAAgBC,CAAC,EACxB,IAAIC,EACJ,IAAMC,EAAQ,CACZ,SAAUF,GAAKA,EAAEG,IAAI,EAAI,CAAC,KAAK,EAAEH,EAAEG,IAAI,CAAC,CAAC,CACzC,YAAaH,GAAMA,CAAAA,EAAEI,OAAO,EAAIJ,IAAAA,EAAEI,OAAO,GAAW,CAAC,QAAQ,EAAE,CAAC,iBAAOJ,EAAEI,OAAO,CAAgB,IAAIC,KAAKL,EAAEI,OAAO,EAAIJ,EAAEI,OAAO,EAAEE,WAAW,GAAG,CAAC,CAChJ,WAAYN,GAAK,iBAAOA,EAAEO,MAAM,EAAiB,CAAC,QAAQ,EAAEP,EAAEO,MAAM,CAAC,CAAC,CACtE,WAAYP,GAAKA,EAAEQ,MAAM,EAAI,CAAC,OAAO,EAAER,EAAEQ,MAAM,CAAC,CAAC,CACjD,WAAYR,GAAKA,EAAES,MAAM,EAAI,SAC7B,aAAcT,GAAKA,EAAEU,QAAQ,EAAI,WACjC,aAAcV,GAAKA,EAAEW,QAAQ,EAAI,CAAC,SAAS,EAAEX,EAAEW,QAAQ,CAAC,CAAC,CACzD,gBAAiBX,GAAKA,EAAEY,WAAW,EAAI,cACvC,aAAcZ,GAAKA,EAAEa,QAAQ,EAAI,CAAC,SAAS,EAAEb,EAAEa,QAAQ,CAAC,CAAC,CAC1D,CAACC,MAAM,CAACC,SACHC,EAAc,CAAC,EAAEhB,EAAEiB,IAAI,CAAC,CAAC,EAAEC,mBAAmB,MAACjB,CAAAA,EAAKD,EAAEmB,KAAK,EAAYlB,EAAK,IAAI,CAAC,CACvF,OAAOC,IAAAA,EAAMkB,MAAM,CAASJ,EAAc,CAAC,EAAEA,EAAY,EAAE,EAAEd,EAAMmB,IAAI,CAAC,MAAM,CAAC,CAEjF,SAASC,EAAYC,CAAM,EACzB,IAAMC,EAAM,aAAa,EAAG,IAAIC,IAChC,IAAK,IAAMC,KAAQH,EAAOI,KAAK,CAAC,OAAQ,CACtC,GAAI,CAACD,EACH,SACF,IAAME,EAAUF,EAAKG,OAAO,CAAC,KAC7B,GAAID,KAAAA,EAAgB,CAClBJ,EAAIM,GAAG,CAACJ,EAAM,QACd,QACF,CACA,GAAM,CAACK,EAAKZ,EAAM,CAAG,CAACO,EAAKM,KAAK,CAAC,EAAGJ,GAAUF,EAAKM,KAAK,CAACJ,EAAU,GAAG,CACtE,GAAI,CACFJ,EAAIM,GAAG,CAACC,EAAKE,mBAAmBd,MAAAA,EAAgBA,EAAQ,QAC1D,CAAE,KAAM,CACR,CACF,CACA,OAAOK,CACT,CACA,SAASU,EAAeC,CAAS,MA8CVC,EAKAA,EAlDrB,GAAI,CAACD,EACH,OAEF,GAAM,CAAC,CAAClB,EAAME,EAAM,CAAE,GAAGkB,EAAW,CAAGf,EAAYa,GAC7C,CACJ3B,OAAAA,CAAM,CACNJ,QAAAA,CAAO,CACPkC,SAAAA,CAAQ,CACRC,OAAAA,CAAM,CACNpC,KAAAA,CAAI,CACJqC,SAAAA,CAAQ,CACR/B,OAAAA,CAAM,CACNG,YAAAA,CAAW,CACXC,SAAAA,CAAQ,CACT,CAAGxB,OAAOoD,WAAW,CACpBJ,EAAWb,GAAG,CAAC,CAAC,CAACO,EAAKW,EAAO,GAAK,CAChCX,EAAIY,WAAW,GAAGC,OAAO,CAAC,KAAM,IAChCF,EACD,GAeH,OAAOG,SAEQC,CAAC,EAChB,IAAMC,EAAO,CAAC,EACd,IAAK,IAAMhB,KAAOe,EACZA,CAAC,CAACf,EAAI,EACRgB,CAAAA,CAAI,CAAChB,EAAI,CAAGe,CAAC,CAACf,EAAI,EAGtB,OAAOgB,CACT,EAvBiB,CACb9B,KAAAA,EACAE,MAAOc,mBAAmBd,GAC1BX,OAAAA,EACA,GAAGJ,GAAW,CAAEA,QAAS,IAAIC,KAAKD,EAAS,CAAC,CAC5C,GAAGkC,GAAY,CAAE5B,SAAU,EAAK,CAAC,CACjC,GAAG,iBAAO6B,GAAuB,CAAEhC,OAAQyC,OAAOT,EAAQ,CAAC,CAC3DpC,KAAAA,EACA,GAAGqC,GAAY,CAAE7B,QAAQ,CAmBpBsC,EAAUC,QAAQ,CADzBd,EAASA,CADYA,EAjBsBI,GAkB3BG,WAAW,IACSP,EAAS,KAAK,CAnBG,CAAC,CACpD,GAAG3B,GAAU,CAAEA,OAAQ,EAAK,CAAC,CAC7B,GAAGI,GAAY,CAAEA,QAAQ,CAsBpBsC,EAASD,QAAQ,CADxBd,EAASA,CADYA,EApBsBvB,GAqB3B8B,WAAW,IACQP,EAAS,KAAK,CAtBI,CAAC,CACpD,GAAGxB,GAAe,CAAEA,YAAa,EAAK,CAAC,EAG3C,CA/EAwC,CAhBe,CAACC,EAAQC,KACtB,IAAK,IAAIrC,KAAQqC,EACflE,EAAUiE,EAAQpC,EAAM,CAAEsC,IAAKD,CAAG,CAACrC,EAAK,CAAEuC,WAAY,EAAK,EAC/D,GAaS1D,EAAa,CACpB2D,eAAgB,IAAMA,EACtBC,gBAAiB,IAAMA,EACvBpC,YAAa,IAAMA,EACnBY,eAAgB,IAAMA,EACtBnC,gBAAiB,IAAMA,CACzB,GACA4D,EAAOC,OAAO,CAnBI,EAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAQ,iBAAOA,GAAqB,mBAAOA,EAC7C,IAAK,IAAI/B,KAAOtC,EAAkBqE,GAC3BnE,EAAasE,IAAI,CAACJ,EAAI9B,IAAQA,KAHZgC,IAGYhC,GACjC3C,EAAUyE,EAAI9B,EAAK,CAAEwB,IAAK,IAAMO,CAAI,CAAC/B,EAAI,CAAEyB,WAAY,CAAEQ,CAAAA,EAAOzE,EAAiBuE,EAAM/B,EAAG,GAAMiC,EAAKR,UAAU,GAErH,OAAOK,CACT,GACwCzE,EAAU,CAAC,EAAG,aAAc,CAAE+B,MAAO,EAAK,GAWpDrB,GAkF9B,IAAImD,EAAY,CAAC,SAAU,MAAO,OAAO,CAKrCE,EAAW,CAAC,MAAO,SAAU,OAAO,CA0DpCM,EAAiB,MACnBS,YAAYC,CAAc,CAAE,CAE1B,IAAI,CAACC,OAAO,CAAG,aAAa,EAAG,IAAI3C,IACnC,IAAI,CAAC4C,QAAQ,CAAGF,EAChB,IAAMG,EAASH,EAAeZ,GAAG,CAAC,UAClC,GAAIe,EAEF,IAAK,GAAM,CAACrD,EAAME,EAAM,GADTG,EAAYgD,GAEzB,IAAI,CAACF,OAAO,CAACtC,GAAG,CAACb,EAAM,CAAEA,KAAAA,EAAME,MAAAA,CAAM,EAG3C,CACA,CAACoD,OAAOC,QAAQ,CAAC,EAAG,CAClB,OAAO,IAAI,CAACJ,OAAO,CAACG,OAAOC,QAAQ,CAAC,EACtC,CAIA,IAAIC,MAAO,CACT,OAAO,IAAI,CAACL,OAAO,CAACK,IAAI,CAE1BlB,IAAI,GAAGmB,CAAI,CAAE,CACX,IAAMzD,EAAO,iBAAOyD,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAACzD,IAAI,CACjE,OAAO,IAAI,CAACmD,OAAO,CAACb,GAAG,CAACtC,EAC1B,CACA0D,OAAO,GAAGD,CAAI,CAAE,CACd,IAAIzE,EACJ,IAAMqD,EAAMsB,MAAMd,IAAI,CAAC,IAAI,CAACM,OAAO,EACnC,GAAI,CAACM,EAAKtD,MAAM,CACd,OAAOkC,EAAI9B,GAAG,CAAC,CAAC,CAACqD,EAAG1D,EAAM,GAAKA,GAEjC,IAAMF,EAAO,iBAAOyD,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAG,MAACzE,CAAAA,EAAKyE,CAAI,CAAC,EAAE,EAAY,KAAK,EAAIzE,EAAGgB,IAAI,CAC9F,OAAOqC,EAAIxC,MAAM,CAAC,CAAC,CAACgE,EAAE,GAAKA,IAAM7D,GAAMO,GAAG,CAAC,CAAC,CAACqD,EAAG1D,EAAM,GAAKA,EAC7D,CACA4D,IAAI9D,CAAI,CAAE,CACR,OAAO,IAAI,CAACmD,OAAO,CAACW,GAAG,CAAC9D,EAC1B,CACAa,IAAI,GAAG4C,CAAI,CAAE,CACX,GAAM,CAACzD,EAAME,EAAM,CAAGuD,IAAAA,EAAKtD,MAAM,CAAS,CAACsD,CAAI,CAAC,EAAE,CAACzD,IAAI,CAAEyD,CAAI,CAAC,EAAE,CAACvD,KAAK,CAAC,CAAGuD,EACpElD,EAAM,IAAI,CAAC4C,OAAO,CAMxB,OALA5C,EAAIM,GAAG,CAACb,EAAM,CAAEA,KAAAA,EAAME,MAAAA,CAAM,GAC5B,IAAI,CAACkD,QAAQ,CAACvC,GAAG,CACf,SACA8C,MAAMd,IAAI,CAACtC,GAAKA,GAAG,CAAC,CAAC,CAACqD,EAAGnC,EAAO,GAAK3C,EAAgB2C,IAASrB,IAAI,CAAC,OAE9D,IAAI,CAKb2D,OAAOC,CAAK,CAAE,CACZ,IAAMzD,EAAM,IAAI,CAAC4C,OAAO,CAClBc,EAAS,MAAOC,OAAO,CAACF,GAA6BA,EAAMzD,GAAG,CAAC,GAAUA,EAAIwD,MAAM,CAAC/D,IAAnDO,EAAIwD,MAAM,CAACC,GAKlD,OAJA,IAAI,CAACZ,QAAQ,CAACvC,GAAG,CACf,SACA8C,MAAMd,IAAI,CAACtC,GAAKA,GAAG,CAAC,CAAC,CAACqD,EAAG1D,EAAM,GAAKpB,EAAgBoB,IAAQE,IAAI,CAAC,OAE5D6D,CACT,CAIAE,OAAQ,CAEN,OADA,IAAI,CAACJ,MAAM,CAACJ,MAAMd,IAAI,CAAC,IAAI,CAACM,OAAO,CAACiB,IAAI,KACjC,IAAI,CAKb,CAACd,OAAOe,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CAAC,eAAe,EAAEC,KAAKC,SAAS,CAACnG,OAAOoD,WAAW,CAAC,IAAI,CAAC2B,OAAO,GAAG,CAAC,CAE7EqB,UAAW,CACT,MAAO,IAAI,IAAI,CAACrB,OAAO,CAACsB,MAAM,GAAG,CAAClE,GAAG,CAAC,GAAO,CAAC,EAAEmE,EAAE1E,IAAI,CAAC,CAAC,EAAEC,mBAAmByE,EAAExE,KAAK,EAAE,CAAC,EAAEE,IAAI,CAAC,KAChG,CACF,EAGIqC,EAAkB,MACpBQ,YAAY0B,CAAe,CAAE,KAGvB3F,EAAI4F,EAAIC,CADZ,KAAI,CAAC1B,OAAO,CAAG,aAAa,EAAG,IAAI3C,IAEnC,IAAI,CAAC4C,QAAQ,CAAGuB,EAChB,IAAMzD,EAAY,MAAC2D,CAAAA,EAAK,MAACD,CAAAA,EAAK,MAAC5F,CAAAA,EAAK2F,EAAgBG,YAAY,EAAY,KAAK,EAAI9F,EAAGgE,IAAI,CAAC2B,EAAe,EAAaC,EAAKD,EAAgBrC,GAAG,CAAC,aAAY,EAAauC,EAAK,EAAE,CAElL,IAAK,IAAME,KADWpB,MAAMO,OAAO,CAAChD,GAAaA,EAAY8D,SA3IrCC,CAAa,EACvC,GAAI,CAACA,EACH,MAAO,EAAE,CACX,IAEIC,EACAC,EACAC,EACAC,EACAC,EANAC,EAAiB,EAAE,CACnBC,EAAM,EAMV,SAASC,IACP,KAAOD,EAAMP,EAAc9E,MAAM,EAAI,KAAKuF,IAAI,CAACT,EAAcU,MAAM,CAACH,KAClEA,GAAO,EAET,OAAOA,EAAMP,EAAc9E,MAAM,CAMnC,KAAOqF,EAAMP,EAAc9E,MAAM,EAAE,CAGjC,IAFA+E,EAAQM,EACRF,EAAwB,GACjBG,KAEL,GAAIN,MADJA,CAAAA,EAAKF,EAAcU,MAAM,CAACH,EAAG,EACb,CAKd,IAJAJ,EAAYI,EACZA,GAAO,EACPC,IACAJ,EAAYG,EACLA,EAAMP,EAAc9E,MAAM,EAZ9BgF,MADPA,CAAAA,EAAKF,EAAcU,MAAM,CAACH,EAAG,GACRL,MAAAA,GAAcA,MAAAA,GAa7BK,GAAO,CAELA,CAAAA,EAAMP,EAAc9E,MAAM,EAAI8E,MAAAA,EAAcU,MAAM,CAACH,IACrDF,EAAwB,GACxBE,EAAMH,EACNE,EAAeK,IAAI,CAACX,EAAcY,SAAS,CAACX,EAAOE,IACnDF,EAAQM,GAERA,EAAMJ,EAAY,CAEtB,MACEI,GAAO,EAGP,EAACF,GAAyBE,GAAOP,EAAc9E,MAAM,GACvDoF,EAAeK,IAAI,CAACX,EAAcY,SAAS,CAACX,EAAOD,EAAc9E,MAAM,EAE3E,CACA,OAAOoF,CACT,EAyFoFrE,GACtC,CACxC,IAAM4E,EAAS7E,EAAe8D,GAC1Be,GACF,IAAI,CAAC3C,OAAO,CAACtC,GAAG,CAACiF,EAAO9F,IAAI,CAAE8F,EAClC,CACF,CAIAxD,IAAI,GAAGmB,CAAI,CAAE,CACX,IAAM3C,EAAM,iBAAO2C,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAACzD,IAAI,CAChE,OAAO,IAAI,CAACmD,OAAO,CAACb,GAAG,CAACxB,EAC1B,CAIA4C,OAAO,GAAGD,CAAI,CAAE,CACd,IAAIzE,EACJ,IAAMqD,EAAMsB,MAAMd,IAAI,CAAC,IAAI,CAACM,OAAO,CAACsB,MAAM,IAC1C,GAAI,CAAChB,EAAKtD,MAAM,CACd,OAAOkC,EAET,IAAMvB,EAAM,iBAAO2C,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAG,MAACzE,CAAAA,EAAKyE,CAAI,CAAC,EAAE,EAAY,KAAK,EAAIzE,EAAGgB,IAAI,CAC7F,OAAOqC,EAAIxC,MAAM,CAAC,GAAOd,EAAEiB,IAAI,GAAKc,EACtC,CACAgD,IAAI9D,CAAI,CAAE,CACR,OAAO,IAAI,CAACmD,OAAO,CAACW,GAAG,CAAC9D,EAC1B,CAIAa,IAAI,GAAG4C,CAAI,CAAE,CACX,GAAM,CAACzD,EAAME,EAAOI,EAAO,CAAGmD,IAAAA,EAAKtD,MAAM,CAAS,CAACsD,CAAI,CAAC,EAAE,CAACzD,IAAI,CAAEyD,CAAI,CAAC,EAAE,CAACvD,KAAK,CAAEuD,CAAI,CAAC,EAAE,CAAC,CAAGA,EACrFlD,EAAM,IAAI,CAAC4C,OAAO,CAGxB,OAFA5C,EAAIM,GAAG,CAACb,EAAM+F,SAyBOzF,EAAS,CAAEN,KAAM,GAAIE,MAAO,EAAG,CAAC,EAUvD,MAT8B,UAA1B,OAAOI,EAAOnB,OAAO,EACvBmB,CAAAA,EAAOnB,OAAO,CAAG,IAAIC,KAAKkB,EAAOnB,OAAO,GAEtCmB,EAAOhB,MAAM,EACfgB,CAAAA,EAAOnB,OAAO,CAAG,IAAIC,KAAKA,KAAK4G,GAAG,GAAK1F,IAAAA,EAAOhB,MAAM,CAAM,EAExDgB,CAAAA,OAAAA,EAAOpB,IAAI,EAAaoB,KAAqB,IAArBA,EAAOpB,IAAI,GACrCoB,CAAAA,EAAOpB,IAAI,CAAG,GAAE,EAEXoB,CACT,EApCkC,CAAEN,KAAAA,EAAME,MAAAA,EAAO,GAAGI,CAAM,IACtDqB,SAiBasE,CAAG,CAAEC,CAAO,EAE3B,IAAK,GAAM,EAAGhG,EAAM,GADpBgG,EAAQnC,MAAM,CAAC,cACSkC,GAAK,CAC3B,IAAME,EAAarH,EAAgBoB,GACnCgG,EAAQE,MAAM,CAAC,aAAcD,EAC/B,CACF,EAvBY5F,EAAK,IAAI,CAAC6C,QAAQ,EACnB,IAAI,CAKbW,OAAO,GAAGN,CAAI,CAAE,CACd,GAAM,CAACzD,EAAMqG,EAAQ,CAAG,iBAAO5C,CAAI,CAAC,EAAE,CAAgB,CAACA,CAAI,CAAC,EAAE,CAAC,CAAG,CAACA,CAAI,CAAC,EAAE,CAACzD,IAAI,CAAEyD,CAAI,CAAC,EAAE,CAAC,CACzF,OAAO,IAAI,CAAC5C,GAAG,CAAC,CAAE,GAAGwF,CAAO,CAAErG,KAAAA,EAAME,MAAO,GAAIf,QAAS,aAAa,EAAG,IAAIC,KAAK,EAAG,EACtF,CACA,CAACkE,OAAOe,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CAAC,gBAAgB,EAAEC,KAAKC,SAAS,CAACnG,OAAOoD,WAAW,CAAC,IAAI,CAAC2B,OAAO,GAAG,CAAC,CAE9EqB,UAAW,CACT,MAAO,IAAI,IAAI,CAACrB,OAAO,CAACsB,MAAM,GAAG,CAAClE,GAAG,CAACzB,GAAiBsB,IAAI,CAAC,KAC9D,CACF,C,wCCvTA,CAAC,KAAK,YAA6C,cAA7B,OAAOkG,qBAAkCA,CAAAA,oBAAoBC,EAAE,CAACC,UAAU,GAAE,EAAE,IAAIC,EAAE,CAAC,EAAE,CAAC,KAC9G;;;;;CAKC,EAAEC,EAAEC,KAAK,CAAyI,SAAeF,CAAC,CAACC,CAAC,EAAE,GAAG,iBAAOD,EAAc,MAAM,UAAc,iCAAyF,IAAI,IAAxD5E,EAAE,CAAC,EAAkB+E,EAAEH,EAAE/F,KAAK,CAACmG,GAAOC,EAAEjD,CAA7B6C,GAAG,CAAC,GAA2BK,MAAM,EAAEC,EAAUC,EAAE,EAAEA,EAAEL,EAAEzG,MAAM,CAAC8G,IAAI,CAAC,IAAIC,EAAEN,CAAC,CAACK,EAAE,CAAKE,EAAED,EAAEtG,OAAO,CAAC,KAAK,IAAGuG,CAAAA,EAAE,IAAY,IAAIzC,EAAEwC,EAAEE,MAAM,CAAC,EAAED,GAAGE,IAAI,GAAOtI,EAAEmI,EAAEE,MAAM,CAAC,EAAED,EAAED,EAAE/G,MAAM,EAAEkH,IAAI,EAAM,MAAKtI,CAAC,CAAC,EAAE,EAAEA,CAAAA,EAAEA,EAAEgC,KAAK,CAAC,EAAE,GAAE,EAAKuG,KAAAA,GAAWzF,CAAC,CAAC6C,EAAE,EAAE7C,CAAAA,CAAC,CAAC6C,EAAE,CAAC6C,SAA8qCd,CAAC,CAACC,CAAC,EAAE,GAAG,CAAC,OAAOA,EAAED,EAAE,CAAC,MAAMC,EAAE,CAAC,OAAOD,CAAC,CAAC,EAA3sC1H,EAAE+H,EAAC,EAAE,CAAC,OAAOjF,CAAC,EAAtf6E,EAAEc,SAAS,CAA4e,SAAmBf,CAAC,CAACC,CAAC,CAACM,CAAC,EAAE,IAAIH,EAAEG,GAAG,CAAC,EAAMJ,EAAEC,EAAEY,MAAM,EAAE5F,EAAE,GAAG,mBAAO+E,EAAgB,MAAM,UAAc,4BAA4B,GAAG,CAAC/C,EAAE6B,IAAI,CAACe,GAAI,MAAM,UAAc,4BAA4B,IAAIK,EAAEF,EAAEF,GAAG,GAAGI,GAAG,CAACjD,EAAE6B,IAAI,CAACoB,GAAI,MAAM,UAAc,2BAA2B,IAAIG,EAAER,EAAE,IAAIK,EAAE,GAAG,MAAMD,EAAEvH,MAAM,CAAC,CAAC,IAAI4H,EAAEL,EAAEvH,MAAM,CAAC,EAAE,GAAGoI,MAAMR,IAAI,CAACS,SAAST,GAAI,MAAM,UAAc,4BAA4BD,GAAG,aAAaW,KAAKC,KAAK,CAACX,EAAE,CAAC,GAAGL,EAAEtH,MAAM,CAAC,CAAC,GAAG,CAACsE,EAAE6B,IAAI,CAACmB,EAAEtH,MAAM,EAAG,MAAM,UAAc,4BAA4B0H,GAAG,YAAYJ,EAAEtH,MAAM,CAAC,GAAGsH,EAAE3H,IAAI,CAAC,CAAC,GAAG,CAAC2E,EAAE6B,IAAI,CAACmB,EAAE3H,IAAI,EAAG,MAAM,UAAc,0BAA0B+H,GAAG,UAAUJ,EAAE3H,IAAI,CAAC,GAAG2H,EAAE1H,OAAO,CAAC,CAAC,GAAG,mBAAO0H,EAAE1H,OAAO,CAACE,WAAW,CAAe,MAAM,UAAc,6BAA6B4H,GAAG,aAAaJ,EAAE1H,OAAO,CAACE,WAAW,EAAE,CAA2D,GAAvDwH,EAAEpH,QAAQ,EAAEwH,CAAAA,GAAG,YAAW,EAAKJ,EAAErH,MAAM,EAAEyH,CAAAA,GAAG,UAAS,EAAKJ,EAAEnH,QAAQ,CAAyE,OAAjE,iBAAOmH,EAAEnH,QAAQ,CAAYmH,EAAEnH,QAAQ,CAACgC,WAAW,GAAGmF,EAAEnH,QAAQ,EAAW,IAAK,GAAsE,IAAI,SAArEuH,GAAG,oBAAoB,KAAM,KAAI,MAAMA,GAAG,iBAAiB,KAAgD,KAAI,OAAOA,GAAG,kBAAkB,KAAM,SAAQ,MAAM,UAAc,6BAA6B,CAAE,OAAOA,CAAC,EAAlmD,IAAID,EAAEhG,mBAAuBa,EAAE5B,mBAAuB4G,EAAE,MAAUhD,EAAE,uCAA0lD,KAAKnB,EAAOC,OAAO,CAAC8D,CAAC,I,4ECN1tD;;;;;;;;CAQC,EAGD,IAAIqB,EAAqBxE,OAAOe,GAAG,CAAC,8BAClC0D,EAAoBzE,OAAOe,GAAG,CAAC,gBAC/B2D,EAAsB1E,OAAOe,GAAG,CAAC,kBACjC4D,EAAyB3E,OAAOe,GAAG,CAAC,qBACpC6D,EAAsB5E,OAAOe,GAAG,CAAC,kBACnCf,OAAOe,GAAG,CAAC,kBACX,IAAI8D,EAAsB7E,OAAOe,GAAG,CAAC,kBACnC+D,EAAqB9E,OAAOe,GAAG,CAAC,iBAChCgE,EAAyB/E,OAAOe,GAAG,CAAC,qBACpCiE,EAAsBhF,OAAOe,GAAG,CAAC,kBACjCkE,EAA2BjF,OAAOe,GAAG,CAAC,uBACtCmE,EAAkBlF,OAAOe,GAAG,CAAC,cAC7BoE,EAAkBnF,OAAOe,GAAG,CAAC,cAC7BqE,EAAuBpF,OAAOe,GAAG,CAAC,mBAClCsE,EAA6BrF,OAAOe,GAAG,CAAC,yBACxCuE,EAAyBtF,OAAOe,GAAG,CAAC,0BACtC,SAASwE,EAAOC,CAAM,EACpB,GAAI,UAAa,OAAOA,GAAU,OAASA,EAAQ,CACjD,IAAIC,EAAWD,EAAOC,QAAQ,CAC9B,OAAQA,GACN,KAAKjB,EACH,OAAUgB,EAASA,EAAOE,IAAI,EAC5B,KAAKhB,EACL,KAAKE,EACL,KAAKD,EACL,KAAKK,EACL,KAAKC,EACL,KAAKI,EACH,OAAOG,CACT,SACE,OAAUA,EAASA,GAAUA,EAAOC,QAAQ,EAC1C,KAAKX,EACL,KAAKC,EACL,KAAKI,EACL,KAAKD,EAEL,KAAKL,EADH,OAAOW,CAGT,SACE,OAAOC,CACX,CACJ,CACF,KAAKhB,EACH,OAAOgB,CACX,CACF,CACF,CACApG,EAAQsG,eAAe,CAAGd,EAC1BxF,EAAQuG,eAAe,CAAGd,EAC1BzF,EAAQwG,OAAO,CAAGrB,EAClBnF,EAAQyG,UAAU,CAAGf,EACrB1F,EAAQ0G,QAAQ,CAAGrB,EACnBrF,EAAQ2G,IAAI,CAAGb,EACf9F,EAAQ4G,IAAI,CAAGf,EACf7F,EAAQ6G,MAAM,CAAGzB,EACjBpF,EAAQ8G,QAAQ,CAAGvB,EACnBvF,EAAQ+G,UAAU,CAAGzB,EACrBtF,EAAQgH,QAAQ,CAAGrB,EACnB3F,EAAQiH,YAAY,CAAGrB,EACvB5F,EAAQkH,iBAAiB,CAAG,SAAUf,CAAM,EAC1C,OAAOD,EAAOC,KAAYX,CAC5B,EACAxF,EAAQmH,iBAAiB,CAAG,SAAUhB,CAAM,EAC1C,OAAOD,EAAOC,KAAYV,CAC5B,EACAzF,EAAQoH,SAAS,CAAG,SAAUjB,CAAM,EAClC,MACE,UAAa,OAAOA,GACpB,OAASA,GACTA,EAAOC,QAAQ,GAAKjB,CAExB,EACAnF,EAAQqH,YAAY,CAAG,SAAUlB,CAAM,EACrC,OAAOD,EAAOC,KAAYT,CAC5B,EACA1F,EAAQsH,UAAU,CAAG,SAAUnB,CAAM,EACnC,OAAOD,EAAOC,KAAYd,CAC5B,EACArF,EAAQuH,MAAM,CAAG,SAAUpB,CAAM,EAC/B,OAAOD,EAAOC,KAAYL,CAC5B,EACA9F,EAAQwH,MAAM,CAAG,SAAUrB,CAAM,EAC/B,OAAOD,EAAOC,KAAYN,CAC5B,EACA7F,EAAQyH,QAAQ,CAAG,SAAUtB,CAAM,EACjC,OAAOD,EAAOC,KAAYf,CAC5B,EACApF,EAAQ0H,UAAU,CAAG,SAAUvB,CAAM,EACnC,OAAOD,EAAOC,KAAYZ,CAC5B,EACAvF,EAAQ2H,YAAY,CAAG,SAAUxB,CAAM,EACrC,OAAOD,EAAOC,KAAYb,CAC5B,EACAtF,EAAQ4H,UAAU,CAAG,SAAUzB,CAAM,EACnC,OAAOD,EAAOC,KAAYR,CAC5B,EACA3F,EAAQ6H,cAAc,CAAG,SAAU1B,CAAM,EACvC,OAAOD,EAAOC,KAAYP,CAC5B,EACA5F,EAAQ8H,kBAAkB,CAAG,SAAUzB,CAAI,EACzC,MAAO,UAAa,OAAOA,GACzB,YAAe,OAAOA,GACtBA,IAAShB,GACTgB,IAASd,GACTc,IAASf,GACTe,IAASV,GACTU,IAAST,GACTS,IAASN,GACR,UAAa,OAAOM,GACnB,OAASA,GACRA,CAAAA,EAAKD,QAAQ,GAAKN,GACjBO,EAAKD,QAAQ,GAAKP,GAClBQ,EAAKD,QAAQ,GAAKX,GAClBY,EAAKD,QAAQ,GAAKZ,GAClBa,EAAKD,QAAQ,GAAKV,GAClBW,EAAKD,QAAQ,GAAKH,GAClB,KAAK,IAAMI,EAAK0B,WAAW,CAGnC,EACA/H,EAAQkG,MAAM,CAAGA,C,4DChIfnG,CAAAA,EAAOC,OAAO,CAAG,EAAjB,sD,4CCHF,CAAC,KAAK,aAAa,IAAI8D,EAAE,CAAC,IAAIA,IAAIA,EAAE9D,OAAO,CAAC,CAAC,CAACgI,UAAUlE,EAAE,EAAK,CAAC,CAAC,CAAC,CAAC,GAAyN,OAA7M,wLAA0NA,EAAEa,KAAAA,EAAU,IAAK,EAAE,IAAI,CAACb,EAAEC,EAAE9C,KAAK,IAAM/B,EAAE+B,EAAE,IAAK6C,CAAAA,EAAE9D,OAAO,CAAC8D,GAAG,iBAAOA,EAAaA,EAAE9E,OAAO,CAACE,IAAI,IAAI4E,CAAC,CAAC,EAAMC,EAAE,CAAC,EAAE,SAASJ,EAAoB1C,CAAC,EAAE,IAAI/B,EAAE6E,CAAC,CAAC9C,EAAE,CAAC,GAAG/B,KAAIyF,IAAJzF,EAAe,OAAOA,EAAEc,OAAO,CAAC,IAAIkE,EAAEH,CAAC,CAAC9C,EAAE,CAAC,CAACjB,QAAQ,CAAC,CAAC,EAAMkB,EAAE,GAAK,GAAG,CAAC4C,CAAC,CAAC7C,EAAE,CAACiD,EAAEA,EAAElE,OAAO,CAAC2D,GAAqBzC,EAAE,EAAK,QAAQ,CAAIA,GAAE,OAAO6C,CAAC,CAAC9C,EAAE,CAAC,OAAOiD,EAAElE,OAAO,CAA6C2D,EAAoBC,EAAE,CAACC,UAAU,IAAI,IAAI5C,EAAE0C,EAAoB,IAAK5D,CAAAA,EAAOC,OAAO,CAACiB,CAAC,I,8DCkB9sBgH,E,kBAAxB,GAAM,CAAEC,IAAAA,CAAG,CAAEC,OAAAA,CAAM,CAAE,CAAGF,CAAAA,MAAAA,CAAAA,EAAAA,UAAS,EAATA,KAAAA,EAAAA,EAAYG,OAAO,GAAI,CAAC,EAE1CC,EACJH,GACA,CAACA,EAAII,QAAQ,EACZJ,CAAAA,EAAIK,WAAW,EAAKJ,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAQK,KAAK,GAAI,CAACN,EAAIO,EAAE,EAAIP,SAAAA,EAAIQ,IAAI,EAErDC,EAAe,CACnBC,EACAC,EACA7J,EACA8J,KAEA,IAAMvG,EAAQqG,EAAI1F,SAAS,CAAC,EAAG4F,GAAS9J,EAClC+J,EAAMH,EAAI1F,SAAS,CAAC4F,EAAQD,EAAMrL,MAAM,EACxCwL,EAAYD,EAAI9K,OAAO,CAAC4K,GAC9B,MAAO,CAACG,EACJzG,EAAQoG,EAAaI,EAAKF,EAAO7J,EAASgK,GAC1CzG,EAAQwG,CACd,EAEME,EAAY,CAACC,EAAcL,EAAe7J,EAAUkK,CAAI,GAC5D,EACO,IACL,IAAM1K,EAAS,GAAK2K,EACdL,EAAQtK,EAAOP,OAAO,CAAC4K,EAAOK,EAAK1L,MAAM,EAC/C,MAAO,CAACsL,EACJI,EAAOP,EAAanK,EAAQqK,EAAO7J,EAAS8J,GAASD,EACrDK,EAAO1K,EAASqK,CACtB,EAPqBO,OAWVC,EAAOJ,EAAU,UAAW,WAAY,mBAClCA,EAAU,UAAW,WAAY,mBAC9BA,EAAU,UAAW,YAClBA,EAAU,UAAW,YACvBA,EAAU,UAAW,YACtBA,EAAU,UAAW,YACdA,EAAU,UAAW,YAC7BA,EAAU,WAAY,YACpC,IAAMK,EAAML,EAAU,WAAY,YAC5BM,EAAQN,EAAU,WAAY,YAC9BO,EAASP,EAAU,WAAY,YACxBA,EAAU,WAAY,YACnC,IAAMQ,EAAUR,EAAU,WAAY,YACvBA,EAAU,yBAA0B,YACtCA,EAAU,WAAY,YACnC,IAAMS,EAAQT,EAAU,WAAY,YACvBA,EAAU,WAAY,YACnBA,EAAU,WAAY,YACxBA,EAAU,WAAY,YACpBA,EAAU,WAAY,YACrBA,EAAU,WAAY,YACxBA,EAAU,WAAY,YACnBA,EAAU,WAAY,YACzBA,EAAU,WAAY,YACrBA,EAAU,WAAY,YCxEtC,IAAMU,EAAW,CACtBC,KAAMF,EAAML,EAAK,MACjBQ,MAAOP,EAAID,EAAK,MAChBS,KAAMN,EAAOH,EAAK,MAClBU,MAAO,IACPC,KAAMN,EAAML,EAAK,MACjBY,MAAOV,EAAMF,EAAK,MAClBa,MAAOT,EAAQJ,EAAK,KACtB,EAEMc,EAAiB,CACrBC,IAAK,MACLN,KAAM,OACND,MAAO,OACT,EA0CO,SAASC,EAAK,GAAGO,CAAc,GACpCC,SAzCmBC,CAAiC,CAAE,GAAGF,CAAc,EAClEA,CAAAA,KAAAA,CAAO,CAAC,EAAE,EAAWA,KAAe1F,IAAf0F,CAAO,CAAC,EAAE,GAAmBA,IAAAA,EAAQ7M,MAAM,EACnE6M,EAAQG,KAAK,GAGf,IAAMC,EACJF,KAAcJ,EACVA,CAAc,CAACI,EAA0C,CACzD,MAEAG,EAASf,CAAQ,CAACY,EAAW,CAEZ,IAAnBF,EAAQ7M,MAAM,CAChBmN,OAAO,CAACF,EAAc,CAAC,IAInBJ,IAAAA,EAAQ7M,MAAM,EAAU,iBAAO6M,CAAO,CAAC,EAAE,CAC3CM,OAAO,CAACF,EAAc,CAAC,IAAMC,EAAS,IAAML,CAAO,CAAC,EAAE,EAEtDM,OAAO,CAACF,EAAc,CAAC,IAAMC,KAAWL,EAG9C,EAkBc,UAAWA,EACzB,CAkBsB,IC/Ef,MAOL/J,YAAYsK,CAAe,CAAEC,CAAoC,CAAE,CACjE,IAAI,CAACC,KAAK,CAAG,IAAIjN,IACjB,IAAI,CAACkN,KAAK,CAAG,IAAIlN,IACjB,IAAI,CAACmN,SAAS,CAAG,EACjB,IAAI,CAACJ,OAAO,CAAGA,EACf,IAAI,CAACC,aAAa,CAAGA,GAAmB,KAAK,EAC/C,CAEA3M,IAAIC,CAAmB,CAAEZ,CAAS,CAAQ,CACxC,GAAI,CAACY,GAAO,CAACZ,EAAO,OAEpB,IAAMsD,EAAO,IAAI,CAACgK,aAAa,CAACtN,GAEhC,GAAIsD,EAAO,IAAI,CAAC+J,OAAO,CAAE,CACvBD,QAAQb,IAAI,CAAC,oCACb,MACF,CAEI,IAAI,CAACgB,KAAK,CAAC3J,GAAG,CAAChD,IACjB,KAAI,CAAC6M,SAAS,EAAI,IAAI,CAACD,KAAK,CAACpL,GAAG,CAACxB,IAAQ,GAG3C,IAAI,CAAC2M,KAAK,CAAC5M,GAAG,CAACC,EAAKZ,GACpB,IAAI,CAACwN,KAAK,CAAC7M,GAAG,CAACC,EAAK0C,GACpB,IAAI,CAACmK,SAAS,EAAInK,EAElB,IAAI,CAACoK,KAAK,CAAC9M,EACb,CAEAgD,IAAIhD,CAAmB,CAAW,OAChC,EAAKA,IAEL,IAAI,CAAC8M,KAAK,CAAC9M,GACJhB,CAAAA,CAAQ,IAAI,CAAC2N,KAAK,CAACnL,GAAG,CAACxB,GAChC,CAEAwB,IAAIxB,CAAmB,CAAiB,CACtC,GAAI,CAACA,EAAK,OAEV,IAAMZ,EAAQ,IAAI,CAACuN,KAAK,CAACnL,GAAG,CAACxB,GAC7B,GAAIZ,KAAUoH,IAAVpH,EAKJ,OADA,IAAI,CAAC0N,KAAK,CAAC9M,GACJZ,CACT,CAEQ0N,MAAM9M,CAAW,CAAQ,CAC/B,IAAMZ,EAAQ,IAAI,CAACuN,KAAK,CAACnL,GAAG,CAACxB,EACfwG,MAAAA,IAAVpH,IACF,IAAI,CAACuN,KAAK,CAAC1J,MAAM,CAACjD,GAClB,IAAI,CAAC2M,KAAK,CAAC5M,GAAG,CAACC,EAAKZ,GACpB,IAAI,CAAC2N,gBAAgB,GAEzB,CAEQA,kBAAyB,CAC/B,KAAO,IAAI,CAACF,SAAS,CAAG,IAAI,CAACJ,OAAO,EAAI,IAAI,CAACE,KAAK,CAACjK,IAAI,CAAG,GACxD,IAAI,CAACsK,sBAAsB,EAE/B,CAEQA,wBAA+B,CACrC,IAAMC,EAAS,IAAI,CAACN,KAAK,CAACrJ,IAAI,GAAG4J,IAAI,GAAG9N,KAAK,CAC7C,GAAI6N,KAAWzG,IAAXyG,EAAsB,CACxB,IAAME,EAAU,IAAI,CAACP,KAAK,CAACpL,GAAG,CAACyL,IAAW,CAC1C,KAAI,CAACJ,SAAS,EAAIM,EAClB,IAAI,CAACR,KAAK,CAAC1J,MAAM,CAACgK,GAClB,IAAI,CAACL,KAAK,CAAC3J,MAAM,CAACgK,EACpB,CACF,CAEAG,OAAQ,CACN,IAAI,CAACT,KAAK,CAACtJ,KAAK,GAChB,IAAI,CAACuJ,KAAK,CAACvJ,KAAK,GAChB,IAAI,CAACwJ,SAAS,CAAG,CACnB,CAEAvJ,MAAO,CACL,MAAO,IAAI,IAAI,CAACqJ,KAAK,CAACrJ,IAAI,GAAG,CAG/B+J,OAAOrN,CAAW,CAAQ,CACpB,IAAI,CAAC2M,KAAK,CAAC3J,GAAG,CAAChD,KACjB,IAAI,CAAC6M,SAAS,EAAI,IAAI,CAACD,KAAK,CAACpL,GAAG,CAACxB,IAAQ,EACzC,IAAI,CAAC2M,KAAK,CAAC1J,MAAM,CAACjD,GAClB,IAAI,CAAC4M,KAAK,CAAC3J,MAAM,CAACjD,GAEtB,CAEAqD,OAAc,CACZ,IAAI,CAACsJ,KAAK,CAACtJ,KAAK,GAChB,IAAI,CAACuJ,KAAK,CAACvJ,KAAK,GAChB,IAAI,CAACwJ,SAAS,CAAG,CACnB,CAEA,IAAInK,MAAe,CACjB,OAAO,IAAI,CAACiK,KAAK,CAACjK,IAAI,CAGxB,IAAI4K,aAAsB,CACxB,OAAO,IAAI,CAACT,SAAS,CAEzB,EDhC2C,IAAQ,GAAWzN,EAAMC,MAAM,C,mKEzEnE,IAAMkO,EAA8B,yBAC9BC,EACX,sCA0BWC,EAAiB,QA8BjBC,EAAiC,sGAEjCC,EAAuC,0FAEvCC,EAA4B,yHAE5BC,EAA6C,0GAI7CC,EACX,6FACWC,EACX,iGAEWC,EACX,qGAGWC,EAA8B,2JAkBrCC,EAAuB,CAI3BC,OAAQ,SAKRC,sBAAuB,MAIvBC,oBAAqB,MAIrBC,cAAe,iBAIfC,QAAS,WAITC,QAAS,WAITC,WAAY,aAIZC,WAAY,aAIZC,UAAW,aAIXC,gBAAiB,oBAIjBC,gBAAiB,oBAIjBC,aAAc,iBAIdC,aAAc,gBAChB,EAKuB,EACrB,GAAGb,CAAoB,CACvBc,MAAO,CACLC,aAAc,CACZf,EAAqBE,qBAAqB,CAC1CF,EAAqBI,aAAa,CACnC,CACDY,WAAY,CACVhB,EAAqBE,qBAAqB,CAC1CF,EAAqBI,aAAa,CAClCJ,EAAqBQ,UAAU,CAC/BR,EAAqBO,UAAU,CAChC,CACDU,cAAe,CAEbjB,EAAqBK,OAAO,CAC5BL,EAAqBM,OAAO,CAC7B,CACDY,WAAY,CACVlB,EAAqBG,mBAAmB,CACxCH,EAAqBU,eAAe,CACrC,CACDS,QAAS,CACPnB,EAAqBE,qBAAqB,CAC1CF,EAAqBI,aAAa,CAClCJ,EAAqBG,mBAAmB,CACxCH,EAAqBU,eAAe,CACpCV,EAAqBC,MAAM,CAC3BD,EAAqBQ,UAAU,CAC/BR,EAAqBO,UAAU,CAChC,CACDa,SAAU,CAERpB,EAAqBE,qBAAqB,CAC1CF,EAAqBG,mBAAmB,CACxCH,EAAqBU,eAAe,CACpCV,EAAqBI,aAAa,CACnC,CAEL,E,sOCzHO,SAASiB,EACdC,CAAgD,CAChDC,CAA+B,EAK/B,IAAMrK,EAAUsK,EAAAA,CAAcA,CAAC3N,IAAI,CAACyN,EAAIpK,OAAO,EAS/C,MAAO,CAAEuK,qBANoBC,EADCpO,GAAG,CAAC+L,EAAAA,EAA2BA,IACdkC,EAAaG,aAAa,CAM1CC,wBAJCzK,EAAQpC,GAAG,CACzCwK,EAAAA,EAA0CA,CAGW,CACzD,C,sEAEO,IAAMsC,EAA+B,qBAC/BC,EAA6B,sBAI7BC,EAAsBxN,OAAOuN,GAC7BE,EAAyBzN,OAAOsN,GAEtC,SAASI,EACdC,CAAuB,CACvB5K,EAEI,CAAC,CAAC,EAEN,GAAI0K,KAA0BE,EAC5B,OAAOA,EAGT,GAAM,CAAEzJ,UAAAA,CAAS,CAAE,CACjB0J,EAAQ,mCACJC,EAAWF,EAAIG,SAAS,CAAC,cAuC/B,OAtCAH,EAAII,SAAS,CAAC,aAAc,IACtB,iBAAOF,EACP,CAACA,EAAS,CACVxN,MAAMO,OAAO,CAACiN,GACZA,EACA,EAAE,CACR3J,EAAUoJ,EAA8B,GAAI,CAI1CzR,QAAS,IAAIC,KAAK,GAClBK,SAAU,GACVC,SAAmD,OACnDF,OAAQuL,CAAAA,EACR7L,KAAM,IACN,GAAImH,KAAiBiB,IAAjBjB,EAAQnH,IAAI,CACX,CAAEA,KAAMmH,EAAQnH,IAAI,EACrBoI,KAAAA,CAAS,GAEfE,EAAUqJ,EAA4B,GAAI,CAIxC1R,QAAS,IAAIC,KAAK,GAClBK,SAAU,GACVC,SAAmD,OACnDF,OAAQuL,CAAAA,EACR7L,KAAM,IACN,GAAImH,KAAiBiB,IAAjBjB,EAAQnH,IAAI,CACX,CAAEA,KAAMmH,EAAQnH,IAAI,EACrBoI,KAAAA,CAAS,GAEhB,EAEDlJ,OAAOC,cAAc,CAAC4S,EAAKF,EAAwB,CACjD7Q,MAAO,GACPqC,WAAY,EACd,GACO0O,CACT,CAwCO,SAASK,EACd,CAAEhB,IAAAA,CAAG,CAAa,CAClBiB,CAAY,CACZC,CAAe,EAEf,IAAMC,EAAO,CAAEC,aAAc,GAAMnP,WAAY,EAAK,EAC9CoP,EAAY,CAAE,GAAGF,CAAI,CAAEG,SAAU,EAAK,EAE5CxT,OAAOC,cAAc,CAACiS,EAAKiB,EAAM,CAC/B,GAAGE,CAAI,CACPnP,IAAK,KACH,IAAMpC,EAAQsR,IAGd,OADApT,OAAOC,cAAc,CAACiS,EAAKiB,EAAM,CAAE,GAAGI,CAAS,CAAEzR,MAAAA,CAAM,GAChDA,CACT,EACAW,IAAK,IACHzC,OAAOC,cAAc,CAACiS,EAAKiB,EAAM,CAAE,GAAGI,CAAS,CAAEzR,MAAAA,CAAM,EACzD,CACF,EACF,C,4QCzMO,SAAS2R,EACdvB,CAAgD,CAChDW,CAAsC,CACtC5K,CAA0B,CAC1ByL,CAA2B,MAiBLC,EACGA,MAwCrBC,EAtDJ,GAAI3L,GAAWgK,CAAAA,EAAAA,EAAAA,EAAAA,EAA0BC,EAAKjK,GAASoK,oBAAoB,CACzE,MAAO,GAKT,GAAIK,EAAAA,EAAmBA,IAAIR,EACzB,OAAO,CAAY,CAACQ,EAAAA,EAAmBA,CAAC,CAG1C,IAAM5K,EAAUsK,EAAAA,CAAcA,CAAC3N,IAAI,CAACyN,EAAIpK,OAAO,EACzC6L,EAAU,IAAIvP,EAAAA,EAAcA,CAAC0D,GAE7BwK,EAAgB,MAAAqB,CAAAA,EAAAA,EAAQzP,GAAG,CAACsO,EAAAA,EAA4BA,CAAAA,EAAAA,KAAAA,EAAxCmB,EAA2C7R,KAAK,CAChE+R,EAAmB,MAAAF,CAAAA,EAAAA,EAAQzP,GAAG,CAACuO,EAAAA,EAA0BA,CAAAA,EAAAA,KAAAA,EAAtCkB,EAAyC7R,KAAK,CAGvE,GACEwQ,GACA,CAACuB,GACDvB,IAAkBrK,EAAQqK,aAAa,CACvC,CAIA,IAAMwB,EAAO,CAAC,EAKd,OAJA9T,OAAOC,cAAc,CAACiS,EAAKQ,EAAAA,EAAmBA,CAAE,CAC9C5Q,MAAOgS,EACP3P,WAAY,EACd,GACO2P,CACT,CAGA,GAAI,CAACxB,GAAiB,CAACuB,EACrB,MAAO,GAIT,GAAI,CAACvB,GAAiB,CAACuB,GAQnBvB,IAAkBrK,EAAQqK,aAAa,CAJzC,OAHKoB,GACHd,CAAAA,EAAAA,EAAAA,EAAAA,EAAiBC,GAEZ,GAcT,GAAI,CAGFe,EAAuBG,EADb,mCAC0BC,MAAM,CACxCH,EACA5L,EAAQgM,qBAAqB,CAEjC,CAAE,KAAM,CAGN,MADArB,CAAAA,EAAAA,EAAAA,EAAAA,EAAiBC,GACV,EACT,CAEA,GAAM,CAAEqB,kBAAAA,CAAiB,CAAE,CACzBpB,EAAQ,qCACJqB,EAAuBD,EAC3BE,OAAO3P,IAAI,CAACwD,EAAQoM,wBAAwB,EAC5CT,EAAqBE,IAAI,EAG3B,GAAI,CAEF,IAAMA,EAAO5N,KAAKqC,KAAK,CAAC4L,GAMxB,OAJAnU,OAAOC,cAAc,CAACiS,EAAKQ,EAAAA,EAAmBA,CAAE,CAC9C5Q,MAAOgS,EACP3P,WAAY,EACd,GACO2P,CACT,CAAE,KAAM,CACN,MAAO,EACT,CACF,C,6HCjHA,IAAM,EAA+BhB,QAAQ,U,0BCK7C,IAAMwB,EAAmB,cAQlB,SAASC,EAAkBC,CAAc,CAAEV,CAAY,EAC5D,IAAMW,EAAKC,IAAAA,WAAkB,CAPV,IAQbC,EAAOD,IAAAA,WAAkB,CANV,IASfhS,EAAMgS,IAAAA,UAAiB,CAC3BF,EACAG,EATsB,IALJ,GAiBlB,UAGIC,EAASF,IAAAA,cAAqB,CAACJ,EAAkB5R,EAAK+R,GACtDI,EAAYT,OAAOU,MAAM,CAAC,CAACF,EAAOG,MAAM,CAACjB,EAAM,QAASc,EAAOI,KAAK,GAAG,EAGvEC,EAAML,EAAOM,UAAU,GAE7B,OAAOd,OAAOU,MAAM,CAAC,CAKnBH,EACAF,EACAQ,EACAJ,EACD,EAAEzO,QAAQ,CAAC,MACd,CAEO,SAAS8N,EACdM,CAAc,CACdW,CAAqB,EAErB,IAAMC,EAAShB,OAAO3P,IAAI,CAAC0Q,EAAe,OAEpCR,EAAOS,EAAOzS,KAAK,CAAC,EAzCL,IA0Cf8R,EAAKW,EAAOzS,KAAK,CA1CF,GA4CnB0S,IAEIJ,EAAMG,EAAOzS,KAAK,CACtB0S,GACAA,IAEIR,EAAYO,EAAOzS,KAAK,CAC5B0S,IAII3S,EAAMgS,IAAAA,UAAiB,CAC3BF,EACAG,EAvDsB,IALJ,GA+DlB,UAGIW,EAAWZ,IAAAA,gBAAuB,CAACJ,EAAkB5R,EAAK+R,GAGhE,OAFAa,EAASC,UAAU,CAACN,GAEbK,EAASP,MAAM,CAACF,GAAaS,EAASN,KAAK,CAAC,OACrD,C,wGClEA,4BAAKQ,CAAc,E,ygBAAdA,C,EAAAA,GAAAA,CAAAA,GAeL,wBAAKC,CAAkB,E,iIAAlBA,C,EAAAA,GAAAA,CAAAA,GAKL,wBAAKC,CAAc,E,uMAAdA,C,EAAAA,GAAAA,CAAAA,GAOL,wBAAKC,CAAkB,E,y6CAAlBA,C,EAAAA,GAAAA,CAAAA,GAmCL,wBAAKC,CAAe,E,+CAAfA,C,EAAAA,GAAAA,CAAAA,GAIL,wBAAKC,CAAU,E,gOAAVA,C,EAAAA,GAAAA,CAAAA,GAQL,wBAAKC,CAAa,E,mLAAbA,C,EAAAA,GAAAA,CAAAA,GAOL,wBAAKC,CAAU,E,4CAAVA,C,EAAAA,GAAAA,CAAAA,GAIL,wBAAKC,CAAQ,E,sCAARA,C,EAAAA,GAAAA,CAAAA,GAIL,wBAAKC,CAAyB,E,uDAAzBA,C,EAAAA,GAAAA,CAAAA,GAIL,wBAAKC,CAAmB,E,mHAAnBA,C,EAAAA,GAAAA,CAAAA,GAKL,wBAAKC,CAAc,E,sCAAdA,C,EAAAA,GAAAA,CAAAA,E,6DC5GU,eAAeC,EAC5BC,CAAY,CACZC,CAAW,EAEX,IAAIC,EACJ,GAAI,CACFA,EAAezD,EAAQ,mDACzB,CAAE,MAAOtN,EAAG,CACV,OAAO6Q,CACT,CAEA,OAAOG,EADwBC,MAAM,CAACH,GACrBI,aAAa,CAACL,EAAMC,EACvC,C,6ECZO,SAASK,EAAe7U,CAAQ,EACrC,OAAOA,MAAAA,CACT,CCMA,eAAe8U,EACbC,CAAgB,CAChBC,CAAe,CACfC,CAQC,CACD,CAAEC,UAAAA,CAAS,CAAEC,UAAAA,CAAS,CAA8C,EAwCpE,IAAK,IAAMC,IAtC0C,CACnDvK,EACI,MAAO0J,IACL,IAAMc,EAAcrE,EAAAA,qCAAAA,CAAAA,CAMpB,OAJAuD,EAAO,MAAMc,EAAad,EAAMU,EAAWK,kBAAkB,EACzD,CAACL,EAAWM,iBAAiB,EAAIN,EAAWO,YAAY,EAC1D,MAAMP,EAAWO,YAAY,CAACjB,EAAMQ,GAE/BR,CACT,EACA,KACJ1J,CAAuCoK,EAAAA,EAAWQ,WAAW,EACzD,MAAOlB,IAGL,IAAMmB,EAAe,GADJ1E,CAAAA,EAAQ,WAAU,EACD,CAChC2E,QAAS,GACTC,mBAAoB,GACpB5W,KAAMiW,EAAWY,OAAO,CACxBC,WAAY,CAAC,EAAEb,EAAWc,WAAW,CAAC,OAAO,CAAC,CAC9CC,QAAS,QACTC,MAAO,GACPC,SACErL,QAAQF,GAAG,CAACwL,kBAAkB,EACW,OAC3C,GAAGlB,EAAWQ,WAAW,GAE3B,OAAO,MAAMC,EAAa7K,OAAO,CAAC0J,EACpC,EACA,KACJW,GAAaC,EACT,GACSZ,EAAK9S,OAAO,CAAC,cAAe,UAErC,KACL,CAAC9B,MAAM,CAACkV,GAGHO,GACFJ,CAAAA,EAAU,MAAMI,EAAcJ,EAAO,EAGzC,OAAOA,CACT,C,wLC3DO,OAAMoB,UAA6BC,MACxCtT,aAAc,CACZ,KAAK,CACH,qGAEJ,CAEA,OAAcuT,UAAW,CACvB,MAAM,IAAIF,CACZ,CACF,CAUO,MAAM9F,UAAuBiG,QAGlCxT,YAAYiD,CAA4B,CAAE,CAGxC,KAAK,GAEL,IAAI,CAACA,OAAO,CAAG,IAAIwQ,MAAMxQ,EAAS,CAChC5D,IAAIF,CAAM,CAAEmP,CAAI,CAAEoF,CAAQ,EAIxB,GAAI,iBAAOpF,EACT,OAAOqF,EAAAA,CAAcA,CAACtU,GAAG,CAACF,EAAQmP,EAAMoF,GAG1C,IAAME,EAAatF,EAAK7P,WAAW,GAK7BoV,EAAW1Y,OAAOgG,IAAI,CAAC8B,GAAS6Q,IAAI,CACxC,GAAOnQ,EAAElF,WAAW,KAAOmV,GAI7B,GAAI,KAAoB,IAAbC,EAGX,OAAOF,EAAAA,CAAcA,CAACtU,GAAG,CAACF,EAAQ0U,EAAUH,EAC9C,EACA9V,IAAIuB,CAAM,CAAEmP,CAAI,CAAErR,CAAK,CAAEyW,CAAQ,EAC/B,GAAI,iBAAOpF,EACT,OAAOqF,EAAAA,CAAcA,CAAC/V,GAAG,CAACuB,EAAQmP,EAAMrR,EAAOyW,GAGjD,IAAME,EAAatF,EAAK7P,WAAW,GAK7BoV,EAAW1Y,OAAOgG,IAAI,CAAC8B,GAAS6Q,IAAI,CACxC,GAAOnQ,EAAElF,WAAW,KAAOmV,GAI7B,OAAOD,EAAAA,CAAcA,CAAC/V,GAAG,CAACuB,EAAQ0U,GAAYvF,EAAMrR,EAAOyW,EAC7D,EACA7S,IAAI1B,CAAM,CAAEmP,CAAI,EACd,GAAI,iBAAOA,EAAmB,OAAOqF,EAAAA,CAAcA,CAAC9S,GAAG,CAAC1B,EAAQmP,GAEhE,IAAMsF,EAAatF,EAAK7P,WAAW,GAK7BoV,EAAW1Y,OAAOgG,IAAI,CAAC8B,GAAS6Q,IAAI,CACxC,GAAOnQ,EAAElF,WAAW,KAAOmV,UAI7B,KAAwB,IAAbC,GAGJF,EAAAA,CAAcA,CAAC9S,GAAG,CAAC1B,EAAQ0U,EACpC,EACAE,eAAe5U,CAAM,CAAEmP,CAAI,EACzB,GAAI,iBAAOA,EACT,OAAOqF,EAAAA,CAAcA,CAACI,cAAc,CAAC5U,EAAQmP,GAE/C,IAAMsF,EAAatF,EAAK7P,WAAW,GAK7BoV,EAAW1Y,OAAOgG,IAAI,CAAC8B,GAAS6Q,IAAI,CACxC,GAAOnQ,EAAElF,WAAW,KAAOmV,UAI7B,KAAwB,IAAbC,GAGJF,EAAAA,CAAcA,CAACI,cAAc,CAAC5U,EAAQ0U,EAC/C,CACF,EACF,CAMA,OAAcG,KAAK/Q,CAAgB,CAAmB,CACpD,OAAO,IAAIwQ,MAAuBxQ,EAAS,CACzC5D,IAAIF,CAAM,CAAEmP,CAAI,CAAEoF,CAAQ,EACxB,OAAQpF,GACN,IAAK,SACL,IAAK,SACL,IAAK,MACH,OAAO+E,EAAqBE,QAAQ,SAEpC,OAAOI,EAAAA,CAAcA,CAACtU,GAAG,CAACF,EAAQmP,EAAMoF,EAC5C,CACF,CACF,EACF,CASA,MAAczW,CAAwB,CAAU,QAC9C,MAAUgE,OAAO,CAAChE,GAAeA,EAAME,IAAI,CAAC,MAErCF,CACT,CAQA,OAAc2C,KAAKqD,CAAsC,CAAW,QAClE,aAAuBuQ,QAAgBvQ,EAEhC,IAAIsK,EAAetK,EAC5B,CAEOE,OAAOpG,CAAY,CAAEE,CAAa,CAAQ,CAC/C,IAAMgX,EAAW,IAAI,CAAChR,OAAO,CAAClG,EAAK,CACX,UAApB,OAAOkX,EACT,IAAI,CAAChR,OAAO,CAAClG,EAAK,CAAG,CAACkX,EAAUhX,EAAM,CAC7ByD,MAAMO,OAAO,CAACgT,GACvBA,EAAStR,IAAI,CAAC1F,GAEd,IAAI,CAACgG,OAAO,CAAClG,EAAK,CAAGE,CAEzB,CAEO6D,OAAO/D,CAAY,CAAQ,CAChC,OAAO,IAAI,CAACkG,OAAO,CAAClG,EAAK,CAGpBsC,IAAItC,CAAY,CAAiB,CACtC,IAAME,EAAQ,IAAI,CAACgG,OAAO,CAAClG,EAAK,QAChC,KAAqB,IAAVE,EAA8B,IAAI,CAACiX,KAAK,CAACjX,GAE7C,IACT,CAEO4D,IAAI9D,CAAY,CAAW,CAChC,OAAO,KAA8B,IAAvB,IAAI,CAACkG,OAAO,CAAClG,EAAK,CAG3Ba,IAAIb,CAAY,CAAEE,CAAa,CAAQ,CAC5C,IAAI,CAACgG,OAAO,CAAClG,EAAK,CAAGE,CACvB,CAEOkX,QACLC,CAAkE,CAClEC,CAAa,CACP,CACN,IAAK,GAAM,CAACtX,EAAME,EAAM,GAAI,IAAI,CAACqX,OAAO,GACtCF,EAAWrU,IAAI,CAACsU,EAASpX,EAAOF,EAAM,IAAI,CAE9C,CAEA,CAAQuX,SAA6C,CACnD,IAAK,IAAMzW,KAAO1C,OAAOgG,IAAI,CAAC,IAAI,CAAC8B,OAAO,EAAG,CAC3C,IAAMlG,EAAOc,EAAIY,WAAW,GAGtBxB,EAAQ,IAAI,CAACoC,GAAG,CAACtC,EAEvB,MAAM,CAACA,EAAME,EAAM,CAEvB,CAEA,CAAQkE,MAAgC,CACtC,IAAK,IAAMtD,KAAO1C,OAAOgG,IAAI,CAAC,IAAI,CAAC8B,OAAO,EAAG,CAC3C,IAAMlG,EAAOc,EAAIY,WAAW,EAC5B,OAAM1B,CACR,CACF,CAEA,CAAQyE,QAAkC,CACxC,IAAK,IAAM3D,KAAO1C,OAAOgG,IAAI,CAAC,IAAI,CAAC8B,OAAO,EAAG,CAG3C,IAAMhG,EAAQ,IAAI,CAACoC,GAAG,CAACxB,EAEvB,OAAMZ,CACR,CACF,CAEO,CAACoD,OAAOC,QAAQ,CAAC,EAAsC,CAC5D,OAAO,IAAI,CAACgU,OAAO,EACrB,CACF,C,oGCtOO,OAAMX,EACX,OAAOtU,IACLF,CAAS,CACTmP,CAAqB,CACrBoF,CAAiB,CACZ,CACL,IAAMzW,EAAQsX,QAAQlV,GAAG,CAACF,EAAQmP,EAAMoF,SACxC,YAAI,OAAOzW,EACFA,EAAMuX,IAAI,CAACrV,GAGblC,CACT,CAEA,OAAOW,IACLuB,CAAS,CACTmP,CAAqB,CACrBrR,CAAU,CACVyW,CAAa,CACJ,CACT,OAAOa,QAAQ3W,GAAG,CAACuB,EAAQmP,EAAMrR,EAAOyW,EAC1C,CAEA,OAAO7S,IAAsB1B,CAAS,CAAEmP,CAAqB,CAAW,CACtE,OAAOiG,QAAQ1T,GAAG,CAAC1B,EAAQmP,EAC7B,CAEA,OAAOyF,eACL5U,CAAS,CACTmP,CAAqB,CACZ,CACT,OAAOiG,QAAQR,cAAc,CAAC5U,EAAQmP,EACxC,CACF,C,iOCjBA7O,EAAOC,OAAO,CARqB,CACjC,YACA,UACA,aACA,WACA,YACD,E,8DCdD,IAAI+U,EAEJ,GAAI,CACFA,EAAiBxG,EAAQ,wBAC3B,CAAE,MAAO1E,EAAO,CACd,GACEA,qBAAAA,EAAMmL,IAAI,EACVnL,kCAAAA,EAAMmL,IAAI,CAEV,MAAMnL,EAIRkL,EAAiBxG,EAAQ,2BAC3B,CAEAxO,EAAOC,OAAO,CAAG+U,C,uCChBjBhV,CAAAA,EAAOC,OAAO,CAAGuO,QAAQ,oC,2BCAzBxO,CAAAA,EAAOC,OAAO,CAAGuO,QAAQ,W,qECAzBxO,CAAAA,EAAOC,OAAO,CAAGuO,QAAQ,mD,oDCAzBxO,CAAAA,EAAOC,OAAO,CAAGuO,QAAQ,kC,uBCAzBxO,CAAAA,EAAOC,OAAO,CAAGuO,QAAQ,O,6CCAzBxO,CAAAA,EAAOC,OAAO,CAAGuO,QAAQ,2B,0CCAzBxO,CAAAA,EAAOC,OAAO,CAAGuO,QAAQ,wB,GCCrB0G,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,KAAiBzQ,IAAjByQ,EACH,OAAOA,EAAapV,OAAO,CAG5B,IAAID,EAASkV,CAAwB,CAACE,EAAS,CAAG,CAGjDnV,QAAS,CAAC,CACX,EAMA,OAHAqV,CAAmB,CAACF,EAAS,CAACpV,EAAQA,EAAOC,OAAO,CAAEkV,GAG/CnV,EAAOC,OAAO,CCpBtBkV,EAAoBhU,CAAC,CAAG,IACvB,IAAI2N,EAAS9O,GAAUA,EAAOuV,UAAU,CACvC,IAAOvV,EAAO,OAAU,CACxB,IAAOA,EAER,OADAmV,EAAoBK,CAAC,CAAC1G,EAAQ,CAAE3K,EAAG2K,CAAO,GACnCA,CACR,ECNAqG,EAAoBK,CAAC,CAAG,CAACvV,EAASwV,KACjC,IAAI,IAAIrX,KAAOqX,EACXN,EAAoBjR,CAAC,CAACuR,EAAYrX,IAAQ,CAAC+W,EAAoBjR,CAAC,CAACjE,EAAS7B,IAC5E1C,OAAOC,cAAc,CAACsE,EAAS7B,EAAK,CAAEyB,WAAY,GAAMD,IAAK6V,CAAU,CAACrX,EAAI,EAG/E,ECPA+W,EAAoBjR,CAAC,CAAG,CAACwR,EAAK7G,IAAUnT,OAAOO,SAAS,CAACC,cAAc,CAACoE,IAAI,CAACoV,EAAK7G,GCClFsG,EAAoBnR,CAAC,CAAG,IACF,aAAlB,OAAOpD,QAA0BA,OAAO+U,WAAW,EACrDja,OAAOC,cAAc,CAACsE,EAASW,OAAO+U,WAAW,CAAE,CAAEnY,MAAO,QAAS,GAEtE9B,OAAOC,cAAc,CAACsE,EAAS,aAAc,CAAEzC,MAAO,EAAK,EAC5D,E,gCCqGI2R,EACApF,EACAuI,E,0/BC/EG,OAAesD,EAqBpBrV,YAAY,CAAEsV,SAAAA,CAAQ,CAAEJ,WAAAA,CAAU,CAA4B,CAAE,CAC9D,IAAI,CAACI,QAAQ,CAAGA,EAChB,IAAI,CAACJ,UAAU,CAAGA,CACpB,CACF,CCvDA,IAAM,EAA+BjH,QAAQ,qB,gDCA7C,IAAM,EAA+BA,QAAQ,S,4FCA7C,IAAM,EAA+BA,QAAQ,c,gGCMtC,IAAMsH,EAAiB,CAC5BC,OAAQ,SACRC,OAAQ,SACRC,WAAY,aACd,CAOGH,CAAAA,EAAeC,MAAM,CACrBD,EAAeE,MAAM,CACrBF,EAAeG,UAAU,CA0EgCrV,OADP,aAoB9C,IAAMsV,EAAsB,CAAC,OAAO,CChHpC,SAASC,EAAoB3Y,CAAU,EAC5C,OAAO9B,OAAOO,SAAS,CAAC6F,QAAQ,CAACxB,IAAI,CAAC9C,EACxC,CAEO,SAAS4Y,EAAc5Y,CAAU,EACtC,GAAI2Y,oBAAAA,EAAoB3Y,GACtB,MAAO,GAGT,IAAMvB,EAAYP,OAAO2a,cAAc,CAAC7Y,GAWxC,OAAOvB,OAAAA,GAAsBA,EAAUC,cAAc,CAAC,gBACxD,CChBA,IAAMoa,EAAwB,4BAEvB,OAAMC,UAA0B1C,MACrCtT,YAAYiW,CAAY,CAAEC,CAAc,CAAEja,CAAY,CAAE8N,CAAe,CAAE,CACvE,KAAK,CACH9N,EACI,CAAC,oBAAoB,EAAEA,EAAK,mBAAmB,EAAEia,EAAO,OAAO,EAAED,EAAK;AAAI,QAAQ,EAAElM,EAAQ,CAAC,CAC7F,CAAC,wCAAwC,EAAEmM,EAAO,OAAO,EAAED,EAAK;AAAI,QAAQ,EAAElM,EAAQ,CAAC,CAE/F,CACF,CAEO,SAASoM,EACdF,CAAY,CACZC,CAAc,CACdrN,CAAU,EAEV,GAAI,CAACgN,EAAchN,GACjB,MAAM,qBAOL,CAPK,IAAImN,EACRC,EACAC,EACA,GACA,CAAC,8CAA8C,EAAEA,EAAO,sCAAsC,EAAEN,EAC9F/M,GACA,IAAI,CAAC,EANH,qB,MAAA,O,WAAA,G,aAAA,EAON,GAGF,SAASuN,EAAMC,CAAyB,CAAEpZ,CAAU,CAAEhB,CAAY,EAChE,GAAIoa,EAAQxV,GAAG,CAAC5D,GACd,MAAM,qBAOL,CAPK,IAAI+Y,EACRC,EACAC,EACAja,EACA,CAAC,+DAA+D,EAC9Doa,EAAQhX,GAAG,CAACpC,IAAU,SACvB,IAAI,CAAC,EANF,qB,MAAA,O,WAAA,G,aAAA,EAON,GAGFoZ,EAAQzY,GAAG,CAACX,EAAOhB,EACrB,CA+FA,OAAOqa,SA7FEA,EACPC,CAAsB,CACtBtZ,CAAU,CACVhB,CAAY,EAEZ,IAAM8J,EAAO,OAAO9I,EACpB,GAEEA,OAAAA,GAMA8I,YAAAA,GACAA,WAAAA,GACAA,WAAAA,EAEA,MAAO,GAGT,GAAIA,cAAAA,EACF,MAAM,qBAKL,CALK,IAAIiQ,EACRC,EACAC,EACAja,EACA,mFAJI,qB,MAAA,O,WAAA,G,aAAA,EAKN,GAGF,GAAI4Z,EAAc5Y,GAAQ,CAGxB,GAFAmZ,EAAMG,EAAMtZ,EAAOhB,GAGjBd,OAAOmZ,OAAO,CAACrX,GAAOuZ,KAAK,CAAC,CAAC,CAAC3Y,EAAK4Y,EAAY,IAC7C,IAAMC,EAAWX,EAAsBtT,IAAI,CAAC5E,GACxC,CAAC,EAAE5B,EAAK,CAAC,EAAE4B,EAAI,CAAC,CAChB,CAAC,EAAE5B,EAAK,CAAC,EAAEoF,KAAKC,SAAS,CAACzD,GAAK,CAAC,CAAC,CAE/B8Y,EAAU,IAAIpZ,IAAIgZ,GACxB,OACED,EAAeK,EAAS9Y,EAAK6Y,IAC7BJ,EAAeK,EAASF,EAAaC,EAEzC,GAEA,MAAO,EAGT,OAAM,qBAKL,CALK,IAAIV,EACRC,EACAC,EACAja,EACA,mDAJI,qB,MAAA,O,WAAA,G,aAAA,EAKN,EACF,CAEA,GAAIyE,MAAMO,OAAO,CAAChE,GAAQ,CAGxB,GAFAmZ,EAAMG,EAAMtZ,EAAOhB,GAGjBgB,EAAMuZ,KAAK,CAAC,CAACC,EAAajO,IAEjB8N,EADS,IAAI/Y,IAAIgZ,GACOE,EAAa,CAAC,EAAExa,EAAK,CAAC,EAAEuM,EAAM,CAAC,CAAC,GAGjE,MAAO,EAGT,OAAM,qBAKL,CALK,IAAIwN,EACRC,EACAC,EACAja,EACA,kDAJI,qB,MAAA,O,WAAA,G,aAAA,EAKN,EACF,CAIA,MAAM,qBAWL,CAXK,IAAI+Z,EACRC,EACAC,EACAja,EACA,IACE8J,EACA,IACCA,CAAAA,WAAAA,EACG,CAAC,GAAG,EAAE5K,OAAOO,SAAS,CAAC6F,QAAQ,CAACxB,IAAI,CAAC9C,GAAO,EAAE,CAAC,CAC/C,EAAC,EACL,mFAVE,qB,MAAA,O,WAAA,G,aAAA,EAWN,EACF,EAEsB,IAAIM,IAAOsL,EAAO,GAC1C,CC5IO,IAAM+N,EAAsCC,IAAAA,aAAmB,CAAC,CAAC,GCA3DC,EAURD,IAAAA,aAAmB,CAAC,CAAC,GCNbE,EAAkBF,IAAAA,aAAmB,CAAmB,MCyB/DG,EAA0B,EAAE,CAC5BC,EAA4B,EAAE,CAGpC,SAASC,EAAKC,CAAW,EACvB,IAAIC,EAAUD,IAEVE,EAAa,CACfC,QAAS,GACTC,OAAQ,KACRhO,MAAO,IACT,EAcA,OAZA8N,EAAMD,OAAO,CAAGA,EACbI,IAAI,CAAC,IACJH,EAAMC,OAAO,CAAG,GAChBD,EAAME,MAAM,CAAGA,EACRA,IAERE,KAAK,CAAC,IAGL,MAFAJ,EAAMC,OAAO,CAAG,GAChBD,EAAM9N,KAAK,CAAGmO,EACRA,CACR,GAEKL,CACT,CAyGA,MAAMM,EAkBJP,SAAU,CACR,OAAO,IAAI,CAACQ,IAAI,CAACR,OAAO,CAG1BS,OAAQ,CACN,IAAI,CAACC,cAAc,GACnB,IAAI,CAACF,IAAI,CAAG,IAAI,CAACG,OAAO,CAAC,IAAI,CAACC,KAAK,CAACb,MAAM,EAE1C,IAAI,CAACc,MAAM,CAAG,CACZC,UAAW,GACXC,SAAU,EACZ,EAEA,GAAM,CAAEP,KAAM5J,CAAG,CAAEgK,MAAOxJ,CAAI,CAAE,CAAG,IAAI,CAEnCR,EAAIsJ,OAAO,GACa,UAAtB,OAAO9I,EAAK4J,KAAK,GACf5J,IAAAA,EAAK4J,KAAK,CACZ,IAAI,CAACH,MAAM,CAACC,SAAS,CAAG,GAExB,IAAI,CAACG,MAAM,CAAGC,WAAW,KACvB,IAAI,CAACC,OAAO,CAAC,CACXL,UAAW,EACb,EACF,EAAG1J,EAAK4J,KAAK,GAIW,UAAxB,OAAO5J,EAAKgK,OAAO,EACrB,KAAI,CAACC,QAAQ,CAAGH,WAAW,KACzB,IAAI,CAACC,OAAO,CAAC,CAAEJ,SAAU,EAAK,EAChC,EAAG3J,EAAKgK,OAAO,IAInB,IAAI,CAACZ,IAAI,CAACR,OAAO,CACdI,IAAI,CAAC,KACJ,IAAI,CAACe,OAAO,CAAC,CAAC,GACd,IAAI,CAACT,cAAc,EACrB,GACCL,KAAK,CAAC,IACL,IAAI,CAACc,OAAO,CAAC,CAAC,GACd,IAAI,CAACT,cAAc,EACrB,GACF,IAAI,CAACS,OAAO,CAAC,CAAC,EAChB,CAEAA,QAAQG,CAAY,CAAE,CACpB,IAAI,CAACT,MAAM,CAAG,CACZ,GAAG,IAAI,CAACA,MAAM,CACd1O,MAAO,IAAI,CAACqO,IAAI,CAACrO,KAAK,CACtBgO,OAAQ,IAAI,CAACK,IAAI,CAACL,MAAM,CACxBD,QAAS,IAAI,CAACM,IAAI,CAACN,OAAO,CAC1B,GAAGoB,CAAO,EAEZ,IAAI,CAACC,UAAU,CAACxE,OAAO,CAAC,GAAmByE,IAC7C,CAEAd,gBAAiB,CACfe,aAAa,IAAI,CAACR,MAAM,EACxBQ,aAAa,IAAI,CAACJ,QAAQ,CAC5B,CAEAK,iBAAkB,CAChB,OAAO,IAAI,CAACb,MAAM,CAGpBc,UAAUH,CAAa,CAAE,CAEvB,OADA,IAAI,CAACD,UAAU,CAACK,GAAG,CAACJ,GACb,KACL,IAAI,CAACD,UAAU,CAAC7X,MAAM,CAAC8X,EACzB,CACF,CAlFA5Y,YAAYiZ,CAAW,CAAEzK,CAAS,CAAE,CAClC,IAAI,CAACuJ,OAAO,CAAGkB,EACf,IAAI,CAACjB,KAAK,CAAGxJ,EACb,IAAI,CAACmK,UAAU,CAAG,IAAIO,IACtB,IAAI,CAACb,MAAM,CAAG,KACd,IAAI,CAACI,QAAQ,CAAG,KAEhB,IAAI,CAACZ,KAAK,EACZ,CA2EF,CAEA,SAASsB,EAAS3K,CAAS,EACzB,OAAO4K,SArMwBH,CAAW,CAAE7V,CAAY,EACxD,IAAIoL,EAAOrT,OAAOke,MAAM,CACtB,CACElC,OAAQ,KACRG,QAAS,KACTc,MAAO,IACPI,QAAS,KACTc,QAAS,KACTC,QAAS,IACX,EACAnW,GAIEoW,EAAoB,KACxB,SAASC,IACP,GAAI,CAACD,EAAc,CAEjB,IAAME,EAAM,IAAI/B,EAAqBsB,EAAQzK,GAC7CgL,EAAe,CACbV,gBAAiBY,EAAIZ,eAAe,CAACtE,IAAI,CAACkF,GAC1CX,UAAWW,EAAIX,SAAS,CAACvE,IAAI,CAACkF,GAC9B7B,MAAO6B,EAAI7B,KAAK,CAACrD,IAAI,CAACkF,GACtBtC,QAASsC,EAAItC,OAAO,CAAC5C,IAAI,CAACkF,EAC5B,CACF,CACA,OAAOF,EAAapC,OAAO,EAC7B,CAoCA,SAASuC,EAAkBC,CAAU,CAAEC,CAAQ,GAC7CC,WAXAL,IAEA,IAAMM,EAAUlD,IAAAA,UAAgB,CAACE,GAC7BgD,GAAWrZ,MAAMO,OAAO,CAACuN,EAAK+K,OAAO,GACvC/K,EAAK+K,OAAO,CAACpF,OAAO,CAAC,IACnB4F,EAAQC,EACV,EAEJ,IAKE,IAAM3C,EAAQ,wBAAmC,CAC/CmC,EAAaT,SAAS,CACtBS,EAAaV,eAAe,CAC5BU,EAAaV,eAAe,EAW9B,OARAjC,IAAAA,mBAAyB,CACvBgD,EACA,IAAO,EACLhC,MAAO2B,EAAa3B,KAAK,CAC3B,EACA,EAAE,EAGGhB,IAAAA,OAAa,CAAC,SAhHR1B,SAiHX,EAAUmC,OAAO,EAAID,EAAM9N,KAAK,CACvB,WAAP,EAAOsN,IAAAA,aAAmB,CAACrI,EAAK8I,OAAO,CAAE,CACvC2C,UAAW5C,EAAMC,OAAO,CACxBY,UAAWb,EAAMa,SAAS,CAC1BC,SAAUd,EAAMc,QAAQ,CACxB5O,MAAO8N,EAAM9N,KAAK,CAClBsO,MAAO2B,EAAa3B,KAAK,GAElBR,EAAME,MAAM,CACd,WAAP,EAAOV,IAAAA,aAAmB,CAzHzB1B,CADQA,EA0H0BkC,EAAME,MAAM,GAzHvCpC,EAAI+E,OAAO,CAAG/E,EAAI+E,OAAO,CAAG/E,EAyHcyE,GAE3C,IAEX,EAAG,CAACA,EAAOvC,EAAM,CACnB,CAKA,OArEEL,EAAiBrU,IAAI,CAAC8W,GAkExBE,EAAkB1G,OAAO,CAAG,IAAMwG,IAClCE,EAAkBQ,WAAW,CAAG,oBAEzB,WAAP,EAAOtD,IAAAA,UAAgB,CAAC8C,EAC1B,EAgGiCzC,EAAM1I,EACvC,CAEA,SAAS4L,EAAkBC,CAAiB,CAAEC,CAAS,EACrD,IAAIC,EAAW,EAAE,CAEjB,KAAOF,EAAand,MAAM,EAAE,CAC1B,IAAIuc,EAAOY,EAAaG,GAAG,GAC3BD,EAAS5X,IAAI,CAAC8W,EAAKa,GACrB,CAEA,OAAOG,QAAQrb,GAAG,CAACmb,GAAU/C,IAAI,CAAC,KAChC,GAAI6C,EAAand,MAAM,CACrB,OAAOkd,EAAkBC,EAAcC,EAE3C,EACF,CAEAnB,EAASuB,UAAU,CAAG,IACb,IAAID,QAAQ,CAACE,EAAqBC,KACvCR,EAAkBpD,GAAkBQ,IAAI,CAACmD,EAAqBC,EAChE,GAGFzB,EAAS0B,YAAY,CAAG,IAACP,KAAAA,IAAAA,GAAAA,CAAAA,EAA2B,EAAE,EAC7C,IAAIG,QAAc,IACvB,IAAMzM,EAAM,IAEH8M,IAGTV,EAAkBnD,EAAoBqD,GAAK9C,IAAI,CAACxJ,EAAKA,EACvD,IAaF,MAAemL,EC1SF4B,EAAgBlE,IAAAA,aAAmB,CAAoB,MCC7D,SAASmE,EAAmB/e,CAAY,EAC7C,OAAOA,EAAKgf,UAAU,CAAC,KAAOhf,EAAO,IAAIA,CAC3C,CCqBO,ICxBMif,EAA6B,CACxC,WACA,MACA,OACA,QACD,CCFKC,EAAa,gCAGbC,EAAoB,sBASnB,SAASC,EAAeC,CAAa,CAAEC,CAAsB,SAAtBA,KAAAA,IAAAA,GAAAA,CAAAA,EAAkB,EAAG,EDDvDlX,KAAAA,IAJRpI,EACGwB,KAAK,CAAC,KACNqW,IAAI,CAAC,GACJoH,EAA2BpH,IAAI,CAAC,GAAO0H,EAAQP,UAAU,CAACQ,MCI9DH,CAAAA,EAAQI,SDCwCzf,CAAY,EAC9D,IAAI0f,EACFC,EACAC,EAEF,IAAK,IAAML,KAAWvf,EAAKwB,KAAK,CAAC,KAE/B,GADAme,EAASV,EAA2BpH,IAAI,CAAC,GAAO0H,EAAQP,UAAU,CAACQ,IACvD,CACT,CAACE,EAAmBE,EAAiB,CAAG5f,EAAKwB,KAAK,CAACme,EAAQ,GAC5D,KACF,CAGF,GAAI,CAACD,GAAqB,CAACC,GAAU,CAACC,EACpC,MAAM,qBAEL,CAFK,MACJ,+BAA+B5f,EAAK,qFADhC,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAKF,OAFA0f,EEjBOX,EACLM,EAAM7d,KAAK,CAAC,KAAKqe,MAAM,CAAC,CAAC9J,EAAUwJ,EAAShT,EAAOuT,IAEjD,EAKA,MH3BGP,CAAO,CAAC,EAAE,EAAYA,EAAQQ,QAAQ,CAAC,MGgCtCR,MAAAA,CAAO,CAAC,EAAE,EAMZ,CAACA,SAAAA,GAAsBA,UAAAA,CAAkB,GACzChT,IAAUuT,EAAS7e,MAAM,CAAG,EAXrB8U,EAgBF,EAAY,IAAGwJ,EArBbxJ,EAsBR,KFPG4J,GACN,IAAK,MAGDC,EADEF,MAAAA,EACiB,IAAIE,EAEJF,EAAoB,IAAME,EAE/C,KACF,KAAK,OAEH,GAAIF,MAAAA,EACF,MAAM,qBAEL,CAFK,MACJ,+BAA+B1f,EAAK,gEADhC,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAEF4f,EAAmBF,EAChBle,KAAK,CAAC,KACNK,KAAK,CAAC,EAAG,IACTmS,MAAM,CAAC4L,GACP1e,IAAI,CAAC,KACR,KACF,KAAK,QAEH0e,EAAmB,IAAMA,EACzB,KACF,KAAK,WAGH,IAAMI,EAAyBN,EAAkBle,KAAK,CAAC,KACvD,GAAIwe,EAAuB/e,MAAM,EAAI,EACnC,MAAM,qBAEL,CAFK,MACJ,+BAA+BjB,EAAK,mEADhC,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF4f,EAAmBI,EAChBne,KAAK,CAAC,EAAG,IACTmS,MAAM,CAAC4L,GACP1e,IAAI,CAAC,KACR,KACF,SACE,MAAM,qBAAyC,CAAzC,MAAU,gCAAV,qB,MAAA,O,WAAA,G,aAAA,EAAwC,EAClD,CAEA,MAAO,CAAEwe,kBAAAA,EAAmBE,iBAAAA,CAAiB,CAC/C,ECpEgDP,GAAOO,gBAAgB,EAGrE,EACST,EAAkB3Y,IAAI,CAAC6Y,GAGzBH,EAAW1Y,IAAI,CAAC6Y,EACzB,CEqTO,SAASY,EAAkBC,CAA2B,EAC3D,MAAO,iBAAOA,EACVA,EACAA,EAAUhC,WAAW,EAAIgC,EAAUpf,IAAI,EAAI,SACjD,CAEO,SAASqf,EAAUpO,CAAmB,EAC3C,OAAOA,EAAIqO,QAAQ,EAAIrO,EAAIsO,WAAW,CAiBjC,eAAeC,EAIpBC,CAAgC,CAAEC,CAAM,EAUxC,IAAMzO,EAAMyO,EAAIzO,GAAG,EAAKyO,EAAIA,GAAG,EAAIA,EAAIA,GAAG,CAACzO,GAAG,CAE9C,GAAI,CAACwO,EAAIE,eAAe,QACtB,EAAQD,GAAG,EAAIA,EAAIN,SAAS,CAEnB,CACLQ,UAAW,MAAMJ,EAAoBE,EAAIN,SAAS,CAAEM,EAAIA,GAAG,CAC7D,EAEK,CAAC,EAGV,IAAM7C,EAAQ,MAAM4C,EAAIE,eAAe,CAACD,GAExC,GAAIzO,GAAOoO,EAAUpO,GACnB,OAAO4L,EAGT,GAAI,CAACA,EAIH,MAAM,qBAAkB,CAAlB,MAHU,IAAIsC,EAClBM,GACA,+DAA8D5C,EAAM,cAChE,qB,MAAA,O,WAAA,G,aAAA,EAAiB,GAazB,OAAOA,CACT,CAIEgD,aAFgB,OAAOC,aAGvB,CAAE,OAAQ,UAAW,mBAAmB,CAAWrG,KAAK,CACtD,GAAY,mBAAOqG,WAAW,CAAC3G,EAAO,CAInC,OAAM4G,UAAuBxJ,MAAO,CC9WpC,IAAMyJ,EAAcC,CAAAA,EAAAA,EAAAA,aAAAA,EAAqC3Y,KAAAA,GAKzD,SAAS4Y,IACd,IAAMlD,EAAUmD,CAAAA,EAAAA,EAAAA,UAAAA,EAAWH,GAE3B,GAAI,CAAChD,EACH,MAAM,qBAGL,CAHK,MACJ,qIADI,qB,MAAA,M,WAAA,G,aAAA,EAGN,GAGF,OAAOA,CACT,CC5DO,IAAMoD,GAAoB9c,OAAOe,GAAG,CAAC,2BAgMrC,SAASgc,GACd/P,CAAwB,CACxBxP,CAAO,EAEP,IAAMwf,EAAOhQ,CAAG,CAAC8P,GAAkB,EAAI,CAAC,EACxC,MAAO,iBAAOtf,EAAmBwf,CAAI,CAACxf,EAAI,CAAGwf,CAC/C,CCjNO,6BAAKC,CAAkB,E,kIAAlBA,C,MCEL,IAAMC,GAAqB,IAAIrE,IAAI,CAAC,IAAK,IAAK,IAAK,IAAK,IAAI,EAE5D,SAASsE,GAAkBlC,CAGjC,EACC,OACEA,EAAMmC,UAAU,EACfnC,CAAAA,EAAMoC,SAAS,CACZJ,GAAmBK,iBAAiB,CACpCL,GAAmBM,iBAAiB,CAE5C,C,gFCHA,SAASC,KAIT,CCXU,IAAIC,WAAW,CAAC,GAAI,IAAK,IAAK,IAAK,IAAI,EAEvC,IAAIA,WAAW,CAAC,GAAI,GAAI,IAAK,IAAK,IAAI,EAItC,IAAIA,WAAW,CAAC,GAAI,GAAI,IAAK,IAAK,GAAI,IAAK,GAAG,EAE9C,IAAIA,WAAW,CAAC,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAG,EAE9C,IAAIA,WAAW,CAAC,GAAI,GAAI,IAAK,IAAK,IAAK,IAAK,GAAG,EAEtC,IAAIA,WAAW,CAC5B,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,IAAK,GAC5D,EDML,IAAMC,GAAU,IAAIC,YAmDb,SAASC,GAAiBC,CAAa,EAC5C,OAAO,IAAIC,eAAe,CACxBlc,MAAMmc,CAAU,EACdA,EAAWC,OAAO,CAACH,GACnBE,EAAW7V,KAAK,EAClB,CACF,EACF,CAEO,eAAe+V,GACpBC,CAAkC,EAElC,IAAMC,EAASD,EAAOE,SAAS,GACzBC,EAAuB,EAAE,CAE/B,OAAa,CACX,GAAM,CAAEC,KAAAA,CAAI,CAAE1hB,MAAAA,CAAK,CAAE,CAAG,MAAMuhB,EAAOI,IAAI,GACzC,GAAID,EACF,MAGFD,EAAO/b,IAAI,CAAC1F,EACd,CAEA,OAAOsS,OAAOU,MAAM,CAACyO,EACvB,CAEO,eAAeG,GACpBN,CAAkC,CAClCO,CAAoB,EAEpB,IAAMC,EAAU,IAAIC,YAAY,QAAS,CAAEC,MAAO,EAAK,GACnD/gB,EAAS,GAEb,UAAW,IAAMggB,KAASK,EAAQ,CAChC,GAAIO,MAAAA,EAAAA,KAAAA,EAAAA,EAAQI,OAAO,CACjB,OAAOhhB,EAGTA,GAAU6gB,EAAQjb,MAAM,CAACoa,EAAO,CAAEK,OAAQ,EAAK,EACjD,CAIA,OAFArgB,EAAU6gB,EAAQjb,MAAM,EAG1B,CEjHO,SAASqb,GAAoB7D,CAAa,EAC/C,OAAOA,EAAM5c,OAAO,CAAC,MAAO,KAAO,GACrC,CCJO,SAAS0gB,GAAUnjB,CAAY,EACpC,IAAMojB,EAAYpjB,EAAK0B,OAAO,CAAC,KACzB2hB,EAAarjB,EAAK0B,OAAO,CAAC,KAC1B4hB,EAAWD,EAAa,IAAOD,CAAAA,EAAY,GAAKC,EAAaD,CAAQ,SAE3E,GAAgBA,EAAY,GACnB,CACLrN,SAAU/V,EAAK2G,SAAS,CAAC,EAAG2c,EAAWD,EAAaD,GACpDG,MAAOD,EACHtjB,EAAK2G,SAAS,CAAC0c,EAAYD,EAAY,GAAKA,EAAYhb,KAAAA,GACxD,GACJob,KAAMJ,EAAY,GAAKpjB,EAAK6B,KAAK,CAACuhB,GAAa,EACjD,EAGK,CAAErN,SAAU/V,EAAMujB,MAAO,GAAIC,KAAM,EAAG,CAC/C,CCfO,SAASC,GAAczjB,CAAY,CAAEmO,CAAe,EACzD,GAAI,CAACnO,EAAKgf,UAAU,CAAC,MAAQ,CAAC7Q,EAC5B,OAAOnO,EAGT,GAAM,CAAE+V,SAAAA,CAAQ,CAAEwN,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAGL,GAAUnjB,GAC5C,MAAO,GAAGmO,EAAS4H,EAAWwN,EAAQC,CACxC,CCNO,SAASE,GAAc1jB,CAAY,CAAE2jB,CAAe,EACzD,GAAI,CAAC3jB,EAAKgf,UAAU,CAAC,MAAQ,CAAC2E,EAC5B,OAAO3jB,EAGT,GAAM,CAAE+V,SAAAA,CAAQ,CAAEwN,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAGL,GAAUnjB,GAC5C,MAAO,GAAG+V,EAAW4N,EAASJ,EAAQC,CACxC,CCLO,SAASI,GAAc5jB,CAAY,CAAEmO,CAAc,EACxD,GAAI,iBAAOnO,EACT,MAAO,GAGT,GAAM,CAAE+V,SAAAA,CAAQ,CAAE,CAAGoN,GAAUnjB,GAC/B,OAAO+V,IAAa5H,GAAU4H,EAASiJ,UAAU,CAAC7Q,EAAS,IAC7D,CCNA,IAAMI,GAAQ,IAAIsV,QAWX,SAASC,GACd/N,CAAgB,CAChBgO,CAA2B,MAYvBC,EATJ,GAAI,CAACD,EAAS,MAAO,CAAEhO,SAAAA,CAAS,EAGhC,IAAIkO,EAAoB1V,GAAMnL,GAAG,CAAC2gB,GAC7BE,IACHA,EAAoBF,EAAQ1iB,GAAG,CAAC,GAAY6iB,EAAO1hB,WAAW,IAC9D+L,GAAM5M,GAAG,CAACoiB,EAASE,IAOrB,IAAMnE,EAAW/J,EAASvU,KAAK,CAAC,IAAK,GAIrC,GAAI,CAACse,CAAQ,CAAC,EAAE,CAAE,MAAO,CAAE/J,SAAAA,CAAS,EAGpC,IAAMwJ,EAAUO,CAAQ,CAAC,EAAE,CAACtd,WAAW,GAIjC+J,EAAQ0X,EAAkBviB,OAAO,CAAC6d,UACxC,EAAY,EAAU,CAAExJ,SAAAA,CAAS,GAGjCiO,EAAiBD,CAAO,CAACxX,EAAM,CAKxB,CAAEwJ,SAFTA,EAAWA,EAASlU,KAAK,CAACmiB,EAAe/iB,MAAM,CAAG,IAAM,IAErC+iB,eAAAA,CAAe,EACpC,CCvCA,IAAMG,GACJ,2FAEF,SAASC,GAASC,CAAiB,CAAEC,CAAmB,EACtD,OAAO,IAAIC,IACT1X,OAAOwX,GAAK5hB,OAAO,CAAC0hB,GAA0B,aAC9CG,GAAQzX,OAAOyX,GAAM7hB,OAAO,CAAC0hB,GAA0B,aAE3D,CAEA,IAAMK,GAAWpgB,OAAO,kBAEjB,OAAMqgB,GAeX1gB,YACE6I,CAAmB,CACnB8X,CAAmC,CACnCnS,CAAc,CACd,CACA,IAAI+R,EACAnd,CAGF,CAAuB,UAAvB,OAAQud,GAA2B,aAAcA,GACjD,iBAAOA,GAEPJ,EAAOI,EACPvd,EAAUoL,GAAQ,CAAC,GAEnBpL,EAAUoL,GAAQmS,GAAc,CAAC,EAGnC,IAAI,CAACF,GAAS,CAAG,CACfH,IAAKD,GAASxX,EAAO0X,GAAQnd,EAAQmd,IAAI,EACzCnd,QAASA,EACTwd,SAAU,EACZ,EAEA,IAAI,CAACC,OAAO,EACd,CAEQA,SAAU,C,IAcV,IAKJ,EACA,IAnBF,IAAMnX,EAAOoX,SCvBf9O,CAAgB,CAChB5O,CAAgB,MAE0BA,EAyCxBpC,EAzClB,GAAM,CAAE4f,SAAAA,CAAQ,CAAEG,KAAAA,CAAI,CAAEC,cAAAA,CAAa,CAAE,CAAG5d,MAAAA,CAAAA,EAAAA,EAAQ6d,UAAU,EAAlB7d,EAAsB,CAAC,EAC3DsG,EAAyB,CAC7BsI,SAAAA,EACAgP,cAAehP,MAAAA,EAAmBA,EAASgK,QAAQ,CAAC,KAAOgF,CAC7D,EAEIJ,GAAYf,GAAcnW,EAAKsI,QAAQ,CAAE4O,KAC3ClX,EAAKsI,QAAQ,CAAGkP,SCrDajlB,CAAY,CAAEmO,CAAc,EAa3D,GAAI,CAACyV,GAAc5jB,EAAMmO,GACvB,OAAOnO,EAIT,IAAMklB,EAAgBllB,EAAK6B,KAAK,CAACsM,EAAOlN,MAAM,SAG9C,EAAkB+d,UAAU,CAAC,KACpBkG,EAKF,IAAIA,CACb,EDyBqCzX,EAAKsI,QAAQ,CAAE4O,GAChDlX,EAAKkX,QAAQ,CAAGA,GAElB,IAAIQ,EAAuB1X,EAAKsI,QAAQ,CAExC,GACEtI,EAAKsI,QAAQ,CAACiJ,UAAU,CAAC,iBACzBvR,EAAKsI,QAAQ,CAACgK,QAAQ,CAAC,SACvB,CACA,IAAMqF,EAAQ3X,EAAKsI,QAAQ,CACxBtT,OAAO,CAAC,mBAAoB,IAC5BA,OAAO,CAAC,UAAW,IACnBjB,KAAK,CAAC,KAEH6jB,EAAUD,CAAK,CAAC,EAAE,CACxB3X,EAAK4X,OAAO,CAAGA,EACfF,EACEC,UAAAA,CAAK,CAAC,EAAE,CAAe,IAAIA,EAAMvjB,KAAK,CAAC,GAAGX,IAAI,CAAC,KAAS,IAIhC,KAAtBiG,EAAQme,SAAS,EACnB7X,CAAAA,EAAKsI,QAAQ,CAAGoP,CAAmB,CAEvC,CAIA,GAAIL,EAAM,CACR,IAAI/f,EAASoC,EAAQoe,YAAY,CAC7Bpe,EAAQoe,YAAY,CAACX,OAAO,CAACnX,EAAKsI,QAAQ,EAC1C+N,GAAoBrW,EAAKsI,QAAQ,CAAE+O,EAAKf,OAAO,CAEnDtW,CAAAA,EAAKyW,MAAM,CAAGnf,EAAOif,cAAc,CACnCvW,EAAKsI,QAAQ,CAAGhR,MAAAA,CAAAA,EAAAA,EAAOgR,QAAQ,EAAfhR,EAAmB0I,EAAKsI,QAAQ,CAE5C,CAAChR,EAAOif,cAAc,EAAIvW,EAAK4X,OAAO,EAKpCtgB,CAJJA,EAASoC,EAAQoe,YAAY,CACzBpe,EAAQoe,YAAY,CAACX,OAAO,CAACO,GAC7BrB,GAAoBqB,EAAsBL,EAAKf,OAAO,GAE/CC,cAAc,EACvBvW,CAAAA,EAAKyW,MAAM,CAAGnf,EAAOif,cAAc,CAGzC,CACA,OAAOvW,CACT,EDlCqC,IAAI,CAAC+W,GAAS,CAACH,GAAG,CAACtO,QAAQ,CAAE,CAC5DiP,WAAY,IAAI,CAACR,GAAS,CAACrd,OAAO,CAAC6d,UAAU,CAC7CM,UAAW,CAACzZ,QAAQF,GAAG,CAAC6Z,kCAAkC,CAC1DD,aAAc,IAAI,CAACf,GAAS,CAACrd,OAAO,CAACoe,YAAY,GAG7CE,EAAWC,SGzEnB9e,CAAoC,CACpCI,CAA6B,EAI7B,IAAIye,EACJ,GAAIze,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAS2e,IAAI,GAAI,CAAClhB,MAAMO,OAAO,CAACgC,EAAQ2e,IAAI,EAC9CF,EAAWze,EAAQ2e,IAAI,CAACrgB,QAAQ,GAAG9D,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,MAC9C,IAAIoF,EAAO6e,QAAQ,CAEnB,OADLA,EAAW7e,EAAO6e,QAAQ,CAG5B,OAAOA,EAASjjB,WAAW,EAC7B,EH6DM,IAAI,CAACgiB,GAAS,CAACH,GAAG,CAClB,IAAI,CAACG,GAAS,CAACrd,OAAO,CAACH,OAAO,CAEhC,KAAI,CAACwd,GAAS,CAACoB,YAAY,CAAG,IAAI,CAACpB,GAAS,CAACrd,OAAO,CAACoe,YAAY,CAC7D,IAAI,CAACf,GAAS,CAACrd,OAAO,CAACoe,YAAY,CAACM,kBAAkB,CAACJ,GACvDI,SIrFNC,CAAqC,CACrCL,CAAiB,CACjBzB,CAAuB,EAEvB,GAAK8B,EAML,IAAK,IAAMC,KAJP/B,GACFA,CAAAA,EAAiBA,EAAexhB,WAAW,EAAC,EAG3BsjB,GAAa,C,IAEPC,EAIrBA,EAHF,GACEN,IAFqB,OAAAM,CAAAA,EAAAA,EAAK1lB,MAAM,SAAX0lB,EAAavkB,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACgB,WAAW,EAAC,GAG/DwhB,IAAmB+B,EAAKC,aAAa,CAACxjB,WAAW,WACjDujB,CAAAA,EAAAA,EAAKhC,OAAO,SAAZgC,EAAcE,IAAI,CAAC,GAAY/B,EAAO1hB,WAAW,KAAOwhB,EAAc,EAEtE,OAAO+B,CAEX,CACF,EJgE2B,MACjB,OAAI,CAACvB,GAAS,CAACrd,OAAO,CAAC6d,UAAU,eAAjC,KAAmCF,IAAI,SAAvC,EAAyCoB,OAAO,CAChDT,GAGN,IAAMO,EACJ,cAAI,CAACxB,GAAS,CAACoB,YAAY,SAA3B,EAA6BI,aAAa,UAC1C,OAAI,CAACxB,GAAS,CAACrd,OAAO,CAAC6d,UAAU,eAAjC,KAAmCF,IAAI,SAAvC,EAAyCkB,aAAa,CAExD,KAAI,CAACxB,GAAS,CAACH,GAAG,CAACtO,QAAQ,CAAGtI,EAAKsI,QAAQ,CAC3C,IAAI,CAACyO,GAAS,CAACwB,aAAa,CAAGA,EAC/B,IAAI,CAACxB,GAAS,CAACG,QAAQ,CAAGlX,EAAKkX,QAAQ,EAAI,GAC3C,IAAI,CAACH,GAAS,CAACa,OAAO,CAAG5X,EAAK4X,OAAO,CACrC,IAAI,CAACb,GAAS,CAACN,MAAM,CAAGzW,EAAKyW,MAAM,EAAI8B,EACvC,IAAI,CAACxB,GAAS,CAACO,aAAa,CAAGtX,EAAKsX,aAAa,CAG3CoB,gBAAiB,KK9FY1Y,MACjCsI,EL8FF,OK9FEA,EAAWqQ,SCHfpmB,CAAY,CACZkkB,CAAuB,CACvB8B,CAAsB,CACtBK,CAAsB,EAItB,GAAI,CAACnC,GAAUA,IAAW8B,EAAe,OAAOhmB,EAEhD,IAAMsmB,EAAQtmB,EAAKwC,WAAW,SAI9B,CAAK6jB,IACCzC,GAAc0C,EAAO,SACrB1C,GAAc0C,EAAO,IAAIpC,EAAO1hB,WAAW,KADNxC,EAKpCyjB,GAAczjB,EAAM,IAAIkkB,EACjC,EDhBIzW,CAFmCA,EL+FL,CAC5BkX,SAAU,IAAI,CAACH,GAAS,CAACG,QAAQ,CACjCU,QAAS,IAAI,CAACb,GAAS,CAACa,OAAO,CAC/BW,cAAe,IAAK,CAACxB,GAAS,CAACrd,OAAO,CAACof,WAAW,CAE9Cne,KAAAA,EADA,IAAI,CAACoc,GAAS,CAACwB,aAAa,CAEhC9B,OAAQ,IAAI,CAACM,GAAS,CAACN,MAAM,CAC7BnO,SAAU,IAAI,CAACyO,GAAS,CAACH,GAAG,CAACtO,QAAQ,CACrCgP,cAAe,IAAI,CAACP,GAAS,CAACO,aAAa,GKrGxChP,QAAQ,CACbtI,EAAKyW,MAAM,CACXzW,EAAK4X,OAAO,CAAGjd,KAAAA,EAAYqF,EAAKuY,aAAa,CAC7CvY,EAAK4Y,YAAY,EAGf5Y,CAAAA,EAAK4X,OAAO,EAAI,CAAC5X,EAAKsX,aAAa,GACrChP,CAAAA,EAAWmN,GAAoBnN,EAAQ,EAGrCtI,EAAK4X,OAAO,EACdtP,CAAAA,EAAW2N,GACTD,GAAc1N,EAAU,eAAetI,EAAK4X,OAAO,EACnD5X,MAAAA,EAAKsI,QAAQ,CAAW,aAAe,QAAO,EAIlDA,EAAW0N,GAAc1N,EAAUtI,EAAKkX,QAAQ,EACzC,CAAClX,EAAK4X,OAAO,EAAI5X,EAAKsX,aAAa,CACtC,EAAUhF,QAAQ,CAAC,KAEjBhK,EADA2N,GAAc3N,EAAU,KAE1BmN,GAAoBnN,ELiFxB,CAEQyQ,cAAe,CACrB,OAAO,IAAI,CAAChC,GAAS,CAACH,GAAG,CAACoC,MAAM,CAGlC,IAAWpB,SAAU,CACnB,OAAO,IAAI,CAACb,GAAS,CAACa,OAAO,CAG/B,IAAWA,QAAQA,CAA2B,CAAE,CAC9C,IAAI,CAACb,GAAS,CAACa,OAAO,CAAGA,CAC3B,CAEA,IAAWnB,QAAS,CAClB,OAAO,IAAI,CAACM,GAAS,CAACN,MAAM,EAAI,EAClC,CAEA,IAAWA,OAAOA,CAAc,CAAE,C,IAG7B,IAFH,GACE,CAAC,IAAI,CAACM,GAAS,CAACN,MAAM,EACtB,QAAC,OAAI,CAACM,GAAS,CAACrd,OAAO,CAAC6d,UAAU,eAAjC,KAAmCF,IAAI,SAAvC,EAAyCf,OAAO,CAAChhB,QAAQ,CAACmhB,EAAM,EAEjE,MAAM,qBAEL,CAFK,UACJ,CAAC,8CAA8C,EAAEA,EAAO,CAAC,CAAC,EADtD,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAGF,KAAI,CAACM,GAAS,CAACN,MAAM,CAAGA,CAC1B,CAEA,IAAI8B,eAAgB,CAClB,OAAO,IAAI,CAACxB,GAAS,CAACwB,aAAa,CAGrC,IAAIJ,cAAe,CACjB,OAAO,IAAI,CAACpB,GAAS,CAACoB,YAAY,CAGpC,IAAIc,cAAe,CACjB,OAAO,IAAI,CAAClC,GAAS,CAACH,GAAG,CAACqC,YAAY,CAGxC,IAAIf,MAAO,CACT,OAAO,IAAI,CAACnB,GAAS,CAACH,GAAG,CAACsB,IAAI,CAGhC,IAAIA,KAAK3kB,CAAa,CAAE,CACtB,IAAI,CAACwjB,GAAS,CAACH,GAAG,CAACsB,IAAI,CAAG3kB,CAC5B,CAEA,IAAIykB,UAAW,CACb,OAAO,IAAI,CAACjB,GAAS,CAACH,GAAG,CAACoB,QAAQ,CAGpC,IAAIA,SAASzkB,CAAa,CAAE,CAC1B,IAAI,CAACwjB,GAAS,CAACH,GAAG,CAACoB,QAAQ,CAAGzkB,CAChC,CAEA,IAAI2lB,MAAO,CACT,OAAO,IAAI,CAACnC,GAAS,CAACH,GAAG,CAACsC,IAAI,CAGhC,IAAIA,KAAK3lB,CAAa,CAAE,CACtB,IAAI,CAACwjB,GAAS,CAACH,GAAG,CAACsC,IAAI,CAAG3lB,CAC5B,CAEA,IAAI4lB,UAAW,CACb,OAAO,IAAI,CAACpC,GAAS,CAACH,GAAG,CAACuC,QAAQ,CAGpC,IAAIA,SAAS5lB,CAAa,CAAE,CAC1B,IAAI,CAACwjB,GAAS,CAACH,GAAG,CAACuC,QAAQ,CAAG5lB,CAChC,CAEA,IAAI6lB,MAAO,CACT,IAAM9Q,EAAW,IAAI,CAACoQ,cAAc,GAC9BM,EAAS,IAAI,CAACD,YAAY,GAChC,MAAO,CAAC,EAAE,IAAI,CAACI,QAAQ,CAAC,EAAE,EAAE,IAAI,CAACjB,IAAI,CAAC,EAAE5P,EAAS,EAAE0Q,EAAO,EAAE,IAAI,CAACjD,IAAI,CAAC,CAAC,CAGzE,IAAIqD,KAAKxC,CAAW,CAAE,CACpB,IAAI,CAACG,GAAS,CAACH,GAAG,CAAGD,GAASC,GAC9B,IAAI,CAACO,OAAO,EACd,CAEA,IAAIkC,QAAS,CACX,OAAO,IAAI,CAACtC,GAAS,CAACH,GAAG,CAACyC,MAAM,CAGlC,IAAI/Q,UAAW,CACb,OAAO,IAAI,CAACyO,GAAS,CAACH,GAAG,CAACtO,QAAQ,CAGpC,IAAIA,SAAS/U,CAAa,CAAE,CAC1B,IAAI,CAACwjB,GAAS,CAACH,GAAG,CAACtO,QAAQ,CAAG/U,CAChC,CAEA,IAAIwiB,MAAO,CACT,OAAO,IAAI,CAACgB,GAAS,CAACH,GAAG,CAACb,IAAI,CAGhC,IAAIA,KAAKxiB,CAAa,CAAE,CACtB,IAAI,CAACwjB,GAAS,CAACH,GAAG,CAACb,IAAI,CAAGxiB,CAC5B,CAEA,IAAIylB,QAAS,CACX,OAAO,IAAI,CAACjC,GAAS,CAACH,GAAG,CAACoC,MAAM,CAGlC,IAAIA,OAAOzlB,CAAa,CAAE,CACxB,IAAI,CAACwjB,GAAS,CAACH,GAAG,CAACoC,MAAM,CAAGzlB,CAC9B,CAEA,IAAI+lB,UAAW,CACb,OAAO,IAAI,CAACvC,GAAS,CAACH,GAAG,CAAC0C,QAAQ,CAGpC,IAAIA,SAAS/lB,CAAa,CAAE,CAC1B,IAAI,CAACwjB,GAAS,CAACH,GAAG,CAAC0C,QAAQ,CAAG/lB,CAChC,CAEA,IAAIgmB,UAAW,CACb,OAAO,IAAI,CAACxC,GAAS,CAACH,GAAG,CAAC2C,QAAQ,CAGpC,IAAIA,SAAShmB,CAAa,CAAE,CAC1B,IAAI,CAACwjB,GAAS,CAACH,GAAG,CAAC2C,QAAQ,CAAGhmB,CAChC,CAEA,IAAI2jB,UAAW,CACb,OAAO,IAAI,CAACH,GAAS,CAACG,QAAQ,CAGhC,IAAIA,SAAS3jB,CAAa,CAAE,CAC1B,IAAI,CAACwjB,GAAS,CAACG,QAAQ,CAAG3jB,EAAMge,UAAU,CAAC,KAAOhe,EAAQ,CAAC,CAAC,EAAEA,EAAM,CAAC,CAGvEsE,UAAW,CACT,OAAO,IAAI,CAACuhB,IAAI,CAGlBI,QAAS,CACP,OAAO,IAAI,CAACJ,IAAI,CAGlB,CAACziB,OAAOe,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CACL0hB,KAAM,IAAI,CAACA,IAAI,CACfC,OAAQ,IAAI,CAACA,MAAM,CACnBF,SAAU,IAAI,CAACA,QAAQ,CACvBI,SAAU,IAAI,CAACA,QAAQ,CACvBD,SAAU,IAAI,CAACA,QAAQ,CACvBpB,KAAM,IAAI,CAACA,IAAI,CACfF,SAAU,IAAI,CAACA,QAAQ,CACvBkB,KAAM,IAAI,CAACA,IAAI,CACf5Q,SAAU,IAAI,CAACA,QAAQ,CACvB0Q,OAAQ,IAAI,CAACA,MAAM,CACnBC,aAAc,IAAI,CAACA,YAAY,CAC/BlD,KAAM,IAAI,CAACA,IAAI,CAEnB,CAEA0D,OAAQ,CACN,OAAO,IAAIzC,GAAQ5X,OAAO,IAAI,EAAG,IAAI,CAAC2X,GAAS,CAACrd,OAAO,CACzD,CACF,C,qDOpRyB/C,OAAO,oBAOC+iB,QAuC9B/iB,OAAOe,GAAG,CAAC,+BC1CP,IAAMiiB,GAAsB,iBAC5B,OAAMC,WAAwBhQ,M,kBAA9B,iBACWvW,IAAI,CAAGsmB,E,CACzB,CCPO,MAAME,GAKXvjB,aAAc,CACZ,IAAIwjB,EACA5I,CAGJ,KAAI,CAACxD,OAAO,CAAG,IAAIqD,QAAW,CAACzM,EAAKyV,KAClCD,EAAUxV,EACV4M,EAAS6I,CACX,GAIA,IAAI,CAACD,OAAO,CAAGA,EACf,IAAI,CAAC5I,MAAM,CAAGA,CAChB,CACF,CCvBA,IAAI8I,GAA2B,EAC3BC,GAA2B,EAC3BC,GAA2B,ECMxB,SAASC,GAAargB,CAAM,EACjC,MAAOA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAGzG,IAAI,IAAK,cAAgByG,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAGzG,IAAI,IAAKsmB,EACjD,CA6GO,eAAeS,GACpBC,CAAoC,CACpC/V,CAAmB,CACnBgW,CAAkC,EAElC,GAAI,CAEF,GAAM,CAAEC,QAAAA,CAAO,CAAEC,UAAAA,CAAS,CAAE,CAAGlW,EAC/B,GAAIiW,GAAWC,EAAW,OAI1B,IAAM9F,EAAa+F,SHhHeC,CAAkB,EACtD,IAAMhG,EAAa,IAAIiG,gBAWvB,OANAD,EAASE,IAAI,CAAC,QAAS,KACjBF,EAASG,gBAAgB,EAE7BnG,EAAWoG,KAAK,CAAC,IAAIlB,GACvB,GAEOlF,CACT,EGmG6CpQ,GAEnCyW,EAASC,SAxHjB1W,CAAmB,CACnBgW,CAAkC,EAElC,IAAIW,EAAU,GAIVC,EAAU,IAAIrB,GAClB,SAASsB,IACPD,EAAQpB,OAAO,EACjB,CACAxV,EAAI8W,EAAE,CAAC,QAASD,GAIhB7W,EAAIsW,IAAI,CAAC,QAAS,KAChBtW,EAAI+W,GAAG,CAAC,QAASF,GACjBD,EAAQpB,OAAO,EACjB,GAIA,IAAMnH,EAAW,IAAIkH,GAMrB,OALAvV,EAAIsW,IAAI,CAAC,SAAU,KACjBjI,EAASmH,OAAO,EAClB,GAGO,IAAIwB,eAA2B,CACpCC,MAAO,MAAO/G,IAIZ,GAAI,CAACyG,EAAS,CAGZ,GAFAA,EAAU,GAGR,gBAAiBhd,YACjBG,QAAQF,GAAG,CAACsd,4BAA4B,CACxC,CACA,IAAMC,EAAUC,SDbxBhiB,EAA+B,CAAC,CAAC,EAEjC,IAAM+hB,EACJzB,IAAAA,GACIrf,KAAAA,EACA,CACEqf,yBAAAA,GACAC,yBAAAA,GACAC,yBAAAA,EACF,EAQN,OANIxgB,EAAQ6H,KAAK,GACfyY,GAA2B,EAC3BC,GAA2B,EAC3BC,GAA2B,GAGtBuB,CACT,ICJcA,GACFtI,YAAYwI,OAAO,CACjB,CAAC,EAAEvd,QAAQF,GAAG,CAACsd,4BAA4B,CAAC,8BAA8B,CAAC,CAC3E,CACEjjB,MAAOkjB,EAAQzB,wBAAwB,CACvCjb,IACE0c,EAAQzB,wBAAwB,CAChCyB,EAAQxB,wBAAwB,EAI1C,CAEA3V,EAAIsX,YAAY,GAChBC,CAAAA,EAAAA,GAAAA,SAAAA,IAAY3b,KAAK,CACfkH,GAAAA,EAAkBA,CAAC0U,aAAa,CAChC,CACEC,SAAU,gBACZ,EACA,IAAMphB,KAAAA,EAEV,CAEA,GAAI,CACF,IAAMqhB,EAAK1X,EAAIiX,KAAK,CAAC/G,EAIjB,WAAWlQ,GAAO,mBAAOA,EAAI2X,KAAK,EACpC3X,EAAI2X,KAAK,GAKND,IACH,MAAMd,EAAQxN,OAAO,CAGrBwN,EAAU,IAAIrB,GAElB,CAAE,MAAO7L,EAAK,CAEZ,MADA1J,EAAIvF,GAAG,GACD,qBAA8D,CAA9D,MAAU,oCAAqC,CAAEmd,MAAOlO,CAAI,GAA5D,qB,MAAA,O,WAAA,G,aAAA,EAA6D,EACrE,CACF,EACA8M,MAAO,IACDxW,EAAIuW,gBAAgB,EAExBvW,EAAI6X,OAAO,CAACnO,EACd,EACAnP,MAAO,UAOL,GAJIyb,GACF,MAAMA,GAGJhW,EAAIuW,gBAAgB,CAGxB,OADAvW,EAAIvF,GAAG,GACA4T,EAASjF,OAAO,CAE3B,EACF,EAgB4CpJ,EAAKgW,EAE7C,OAAMD,EAAS+B,MAAM,CAACrB,EAAQ,CAAE3F,OAAQV,EAAWU,MAAM,EAC3D,CAAE,MAAOpH,EAAU,CAEjB,GAAImM,GAAanM,GAAM,MAEvB,OAAM,qBAAoD,CAApD,MAAU,0BAA2B,CAAEkO,MAAOlO,CAAI,GAAlD,qB,MAAA,O,WAAA,G,aAAA,EAAmD,EAC3D,CACF,CCvEe,MAAMqO,GA6BnB,OAAcC,WAAW/oB,CAAsB,CAAE,CAC/C,OAAO,IAAI8oB,GAAyC9oB,EAAO,CAAEgpB,SAAU,CAAC,CAAE,EAC5E,CAIAjmB,YACEokB,CAA8B,CAC9B,CAAE8B,YAAAA,CAAW,CAAEC,UAAAA,CAAS,CAAEF,SAAAA,CAAQ,CAAiC,CACnE,CACA,IAAI,CAAC7B,QAAQ,CAAGA,EAChB,IAAI,CAAC8B,WAAW,CAAGA,EACnB,IAAI,CAACD,QAAQ,CAAGA,EAChB,IAAI,CAACE,SAAS,CAAGA,CACnB,CAEOC,eAAeH,CAAkB,CAAE,CACxC9qB,OAAOke,MAAM,CAAC,IAAI,CAAC4M,QAAQ,CAAEA,EAC/B,CAMA,IAAWI,QAAkB,CAC3B,OAAO,WAAI,CAACjC,QAAQ,CAOtB,IAAWkC,WAAqB,CAC9B,MAAO,iBAAO,IAAI,CAAClC,QAAQ,CAKtBmC,kBAAkBhI,EAAS,EAAK,CAA4B,CACjE,GAAI,WAAI,CAAC6F,QAAQ,CACf,MAAM,qBAA0D,CAA1D,MAAU,iDAAV,qB,MAAA,O,WAAA,G,aAAA,EAAyD,GAGjE,GAAI,iBAAO,IAAI,CAACA,QAAQ,CAAe,CACrC,GAAI,CAAC7F,EACH,MAAM,qBAEL,CAFK,MACJ,8EADI,qB,MAAA,M,WAAA,G,aAAA,EAEN,GAGF,OAAOD,GAAe,IAAI,CAACyF,QAAQ,CACrC,CAEA,OAAOxU,OAAO3P,IAAI,CAAC,IAAI,CAACwkB,QAAQ,CAClC,CAWOoC,kBAAkBjI,EAAS,EAAK,CAA4B,CACjE,GAAI,WAAI,CAAC6F,QAAQ,CACf,MAAM,qBAA0D,CAA1D,MAAU,iDAAV,qB,MAAA,O,WAAA,G,aAAA,EAAyD,GAGjE,GAAI,iBAAO,IAAI,CAACA,QAAQ,CAAe,CACrC,GAAI,CAAC7F,EACH,MAAM,qBAEL,CAFK,MACJ,8EADI,qB,MAAA,M,WAAA,G,aAAA,EAEN,GAGF,OAAOM,GAAe,IAAI,CAACkF,QAAQ,CACrC,CAEA,OAAO,IAAI,CAACK,QAAQ,CAOtB,IAAYL,UAAuC,CACjD,GAAI,WAAI,CAACK,QAAQ,CACf,MAAM,qBAAyD,CAAzD,MAAU,gDAAV,qB,MAAA,M,WAAA,G,aAAA,EAAwD,GAEhE,GAAI,iBAAO,IAAI,CAACA,QAAQ,CACtB,MAAM,qBAA2D,CAA3D,MAAU,kDAAV,qB,MAAA,O,WAAA,G,aAAA,EAA0D,UAGlE,OAAWqC,QAAQ,CAAC,IAAI,CAACrC,QAAQ,EACxBnG,GAAiB,IAAI,CAACmG,QAAQ,EAInC1jB,MAAMO,OAAO,CAAC,IAAI,CAACmjB,QAAQ,EACtBsC,SpBjLX,GAAGC,CAA4B,EAI/B,GAAIA,IAAAA,EAAQzpB,MAAM,CAChB,MAAM,qBAAiE,CAAjE,MAAU,wDAAV,qB,MAAA,O,WAAA,G,aAAA,EAAgE,GAIxE,GAAIypB,IAAAA,EAAQzpB,MAAM,CAChB,OAAOypB,CAAO,CAAC,EAAE,CAGnB,GAAM,CAAE5C,SAAAA,CAAQ,CAAEpV,SAAAA,CAAQ,CAAE,CAAG,IAAIiY,gBAI/BxP,EAAUuP,CAAO,CAAC,EAAE,CAACb,MAAM,CAACnX,EAAU,CAAEkY,aAAc,EAAK,GAE3D9iB,EAAI,EACR,KAAOA,EAAI4iB,EAAQzpB,MAAM,CAAG,EAAG6G,IAAK,CAClC,IAAM+iB,EAAaH,CAAO,CAAC5iB,EAAE,CAC7BqT,EAAUA,EAAQI,IAAI,CAAC,IACrBsP,EAAWhB,MAAM,CAACnX,EAAU,CAAEkY,aAAc,EAAK,GAErD,CAIA,IAAME,EAAaJ,CAAO,CAAC5iB,EAAE,CAO7B,MAFAqT,CAJAA,EAAUA,EAAQI,IAAI,CAAC,IAAMuP,EAAWjB,MAAM,CAACnX,GAAS,EAIhD8I,KAAK,CAACoG,IAEPkG,CACT,KoB4I6B,IAAI,CAACK,QAAQ,EAG/B,IAAI,CAACA,QAAQ,CAWtB,MAAaL,CAAoC,CAAE,KpBxJpBzb,MoB8JzB0e,EALJ,GAAI,WAAI,CAAC5C,QAAQ,CACf,MAAM,qBAAkE,CAAlE,MAAU,yDAAV,qB,MAAA,O,WAAA,G,aAAA,EAAiE,EAKrE,CAAyB,UAAzB,OAAO,IAAI,CAACA,QAAQ,CACtB4C,EAAY,EpBhKe1e,EoBgKG,IAAI,CAAC8b,QAAQ,CpB/JxC,IAAIjG,eAAe,CACxBlc,MAAMmc,CAAU,EACdA,EAAWC,OAAO,CAACN,GAAQvZ,MAAM,CAAC8D,IAClC8V,EAAW7V,KAAK,EAClB,CACF,IoB0JiD,CACpC7H,MAAMO,OAAO,CAAC,IAAI,CAACmjB,QAAQ,EACpC4C,EAAY,IAAI,CAAC5C,QAAQ,CAChB7U,OAAOkX,QAAQ,CAAC,IAAI,CAACrC,QAAQ,EACtC4C,EAAY,CAAC/I,GAAiB,IAAI,CAACmG,QAAQ,EAAE,CAE7C4C,EAAY,CAAC,IAAI,CAAC5C,QAAQ,CAAC,CAI7B4C,EAAUrkB,IAAI,CAACohB,GAGf,IAAI,CAACK,QAAQ,CAAG4C,CAClB,CASA,MAAalB,OAAOnX,CAAoC,CAAiB,CACvE,GAAI,CACF,MAAM,IAAI,CAACoV,QAAQ,CAAC+B,MAAM,CAACnX,EAAU,CAKnCkY,aAAc,EAChB,GAII,IAAI,CAACV,SAAS,EAAE,MAAM,IAAI,CAACA,SAAS,CAGxC,MAAMxX,EAASpG,KAAK,EACtB,CAAE,MAAOmP,EAAK,CAIZ,GAAImM,GAAanM,GAAM,CAErB,MAAM/I,EAAS6V,KAAK,CAAC9M,GAErB,MACF,CAKA,MAAMA,CACR,CACF,CAQA,MAAaoM,mBAAmB9V,CAAmB,CAAE,CACnD,MAAM8V,GAAmB,IAAI,CAACC,QAAQ,CAAE/V,EAAK,IAAI,CAACmY,SAAS,CAC7D,CACF,CChSO,IAAMc,GACXpQ,IAAAA,aAAmB,CC4HkC,CACrDqQ,YAAa,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,CAC1DC,WAAY,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC/ClrB,KAAM,eACNkb,OAAQ,UACRiQ,WAAY,GACZjF,QAAS,EAAE,CACXkF,oBAAqB,GACrBC,gBAAiB,GACjBC,QAAS,CAAC,aAAa,CACvBC,oBAAqB,GACrBC,sBAAwB,gDACxBC,uBAAwB,aACxBC,cAAetjB,KAAAA,EACfujB,eAAgB,EAAE,CAClBC,UAAWxjB,KAAAA,EACXyjB,YAAa,EACf,G,wEC9IA,IAAMC,GAAuB,CCqBO,ODrBe,CECtCC,GAAsBhL,CAAAA,EAAAA,EAAAA,aAAAA,EAAsC,MAC5DiL,GAAkBjL,CAAAA,EAAAA,EAAAA,aAAAA,EAA6B,MAC/CkL,GAAoBlL,CAAAA,EAAAA,EAAAA,aAAAA,EAA6B,MCNxDmL,GAAc,sBACdC,GAAkB,uBAEjB,SAASC,GAAmB/f,CAAW,SAE5C,GAAgB7F,IAAI,CAAC6F,GACZA,EAAI5J,OAAO,CAAC0pB,GAAiB,QAE/B9f,CACT,CC8EA,IAAMggB,GAAoB,2CAmC1B,SAASC,GAAsBC,CAAa,EAC1C,IAAMC,EAAWD,EAAMvN,UAAU,CAAC,MAAQuN,EAAMxM,QAAQ,CAAC,KACrDyM,GACFD,CAAAA,EAAQA,EAAM1qB,KAAK,CAAC,EAAG,GAAE,EAE3B,IAAM4qB,EAASF,EAAMvN,UAAU,CAAC,OAIhC,OAHIyN,GACFF,CAAAA,EAAQA,EAAM1qB,KAAK,CAAC,EAAC,EAEhB,CAAED,IAAK2qB,EAAOE,OAAAA,EAAQD,SAAAA,CAAS,CACxC,CCjEO,SAASE,GAA+B,CAO7C,EAP6C,IAC7CC,SAAAA,CAAQ,CACRC,OAAAA,CAAM,CACN,GAAGjP,EAIH,CAP6C,EAQvCC,EAAMiP,CAAAA,EAAAA,EAAAA,MAAAA,EAAOlP,EAAMmP,YAAY,EAC/B9rB,EAAQ+rB,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,SAkChB1I,EA9BJ,IAAMyI,EAAelP,EAAIoP,OAAO,CAOhC,GANIF,GACFlP,CAAAA,EAAIoP,OAAO,CAAG,EAAI,EAKhB5N,EAAewN,EAAO7W,QAAQ,IAK5B6W,EAAOK,UAAU,EASjBH,GAAgB,CAACF,EAAOM,OAAO,EARjC,OAAO,KAkBX,GAAI,CACF7I,EAAM,IAAIE,IAAIqI,EAAOO,MAAM,CAAE,WAC/B,CAAE,MAAOzoB,EAAG,CAEV,MAAO,GACT,CAEA,OAAO2f,EAAItO,QAAQ,EAClB,CAAC6W,EAAOO,MAAM,CAAEP,EAAOK,UAAU,CAAEL,EAAOM,OAAO,CAAEN,EAAO7W,QAAQ,CAAC,EAEtE,MACE,WADF,EACE,UAACiW,GAAgBoB,QAAQ,EAACpsB,MAAOA,E,SAC9B2rB,C,EAGP,CCyBO,IAAMU,GAAmBzS,IAAAA,aAAmB,CACjD,MAEW0S,GAAsB1S,IAAAA,aAAmB,CAK5C,MAEG2S,GAA4B3S,IAAAA,aAAmB,CAKzD,MAEU4S,GAAkB5S,IAAAA,aAAmB,CAAkB,MASvD6S,GAAqB7S,IAAAA,aAAmB,CAAc,IAAIqC,KClLjEyQ,GAActpB,OAAOe,GAAG,CAAC,erD+GzBwoB,GAAU,kBAYhB,SAASC,KAGP,MAAM,qBAAkB,CAAlB,MADJ,uJACI,qB,MAAA,O,WAAA,G,aAAA,EAAiB,EACzB,CAEA,eAAeC,GAAeC,CAA2B,EACvD,IAAMC,EAAe,MAAMC,IAAAA,sBAA0C,CAACF,GAEtE,OADA,MAAMC,EAAaE,QAAQ,CACpBrL,GAAemL,EACxB,CAnBEpb,EACEX,EAAAA,4DAAAA,CAAAA,CACFzE,EAAOyE,EAAAA,kCAAAA,EAAAA,CACP8D,EAAkB9D,EAAAA,qCAAAA,CAAAA,OAkBdkc,GAgBJnqB,YACEgS,CAAgB,CAChBwN,CAAqB,CACrB4K,CAAU,CACV,CAAElB,WAAAA,CAAU,CAA2B,CACvCC,CAAgB,CAChBvI,CAAgB,CAChBT,CAAe,CACfH,CAA2B,CAC3BiC,CAAsB,CACtBoI,CAAuC,CACvCC,CAAmB,CACnBC,CAAwB,CACxB,CACA,IAAI,CAACjP,KAAK,CAAGtJ,EAAStT,OAAO,CAAC,MAAO,KAAO,IAC5C,IAAI,CAACsT,QAAQ,CAAGA,EAChB,IAAI,CAACwN,KAAK,CAAGA,EACb,IAAI,CAAC4J,MAAM,CAAGgB,EACd,IAAI,CAAClB,UAAU,CAAGA,EAClB,IAAI,CAACtI,QAAQ,CAAGA,EAChB,IAAI,CAACT,MAAM,CAAGA,EACd,IAAI,CAACH,OAAO,CAAGA,EACf,IAAI,CAACiC,aAAa,CAAGA,EACrB,IAAI,CAACkH,OAAO,CAAGA,EACf,IAAI,CAACkB,aAAa,CAAGA,EACrB,IAAI,CAACC,SAAS,CAAG,CAAC,CAACA,EACnB,IAAI,CAACC,cAAc,CAAG,CAAC,CAACA,CAC1B,CAEA5nB,MAAY,CACVknB,IACF,CACAnrB,SAAe,CACbmrB,IACF,CACAW,QAAS,CACPX,IACF,CACAY,MAAO,CACLZ,IACF,CACAa,SAAgB,CACdb,IACF,CACAc,UAAgB,CACdd,IACF,CACAe,gBAAiB,CACff,IACF,CACF,CA0BA,SAASgB,GACPrO,CAAY,CACZL,CAA4B,CAC5BvC,CAAU,EAEV,MAAO,WAAP,EAAO,UAAC4C,EAAAA,CAAIL,UAAWA,EAAY,GAAGvC,CAAK,EAC7C,CAgHA,IAAMkR,GAAiB,CACrBC,EACAC,KAEA,IAAMC,EAAe,CAAC,QAAQ,EAAEF,EAAWG,iBAAiB,GAAG,MAAM,CAAC,CAEtE,MACE,CAAC,qCAAqC,EAAEH,EAEvC;AAC2B;AAAE;AAIb;AAAE,4BALc,EAAEC,EAAY7tB,IAAI,CAAC,MACnD;AAAG,4CAA2C,EAAE8tB,EAAa,CAH8E,EAOhJ,SAASE,GACPC,CAAkB,CAClB/d,CAAoB,CACpB6I,CAA+C,EAE/C,GAAM,CAAEmV,YAAAA,CAAW,CAAE3N,UAAAA,CAAS,CAAED,WAAAA,CAAU,CAAEmD,SAAAA,CAAQ,CAAE,CAAGwK,EACrDE,EAAmB,EAAE,CAEnBC,EAAgB,KAAsB,IAAf9N,EACvB+N,EAAe,KAAqB,IAAd9N,CAExB8N,CAAAA,GAAgBD,EAClBD,EAAO3oB,IAAI,CAAC,yDACH6oB,GAAgB,kBAAO9N,EAChC4N,EAAO3oB,IAAI,CAAC,yCACH4oB,GAAiB,CAAChO,GAAmB1c,GAAG,CAAC4c,IAClD6N,EAAO3oB,IAAI,CACT,CAAC,wCAAwC,EAAE,IAAI4a,GAAmB,CAACpgB,IAAI,CACrE,OACC,EAGP,IAAMsuB,EAAkB,OAAOJ,CAEP,YAApBI,GACFH,EAAO3oB,IAAI,CACT,CAAC,8CAA8C,EAAE8oB,EAAgB,CAAC,EAItE,IAAMC,EAAe,OAAO9K,EAQ5B,GANqB,cAAjB8K,GAAgCA,YAAAA,GAClCJ,EAAO3oB,IAAI,CACT,CAAC,sDAAsD,EAAE+oB,EAAa,CAAC,EAIvEJ,EAAOpuB,MAAM,CAAG,EAClB,MAAM,qBAKL,CALK,MACJ,CAAC,sCAAsC,EAAEgZ,EAAO,KAAK,EAAE7I,EAAIiT,GAAG;AAAG,CAAC,CAChEgL,EAAOnuB,IAAI,CAAC,SADd,gFADI,qB,MAAA,O,WAAA,G,aAAA,EAKN,EAEJ,CAqCO,eAAewuB,GACpBte,CAAoB,CACpBW,CAAmB,CACnBgE,CAAgB,CAChBwN,CAAyB,CACzBtN,CAAmD,CACnD0Z,CAAsB,CACtBC,CAAiC,CACjCC,CAAiC,MsD3bH7oB,EtDma9ByU,MAwPIqU,EA+KAnS,EAhLA0Q,EAzQA0B,EA6CJ3d,CAAAA,EAAAA,EAAAA,EAAAA,EAAY,CAAEhB,IAAKA,CAAW,EAAG,WsD9bHpK,EtD8b8BoK,EAAIpK,OAAO,CsD3bhE,WACL,GAAM,CAAE5F,OAAAA,CAAM,CAAE,CAAG4F,EAEnB,GAAI,CAAC5F,EACH,MAAO,CAAC,EAGV,GAAM,CAAEqG,MAAOuoB,CAAa,CAAE,CAAGhe,EAAQ,mCACzC,OAAOge,EAAcvrB,MAAMO,OAAO,CAAC5D,GAAUA,EAAOF,IAAI,CAAC,MAAQE,EACnE,ItDobA,IAAM4oB,EAAsC,CAAC,EAK7C,GAHAA,EAASiG,gBAAgB,CACvB,EAAYC,GAAG,EAAIja,EAAWga,gBAAgB,EAAK,GAEjDha,EAAWia,GAAG,EAAI,CAAClG,EAASiG,gBAAgB,CAAE,CAChD,IAAME,EAAY,CAAC/e,EAAIpK,OAAO,CAAC,aAAa,EAAI,EAAC,EAAGxE,WAAW,GAC3D2tB,EAAUptB,QAAQ,CAAC,WAAa,CAACotB,EAAUptB,QAAQ,CAAC,WAMtDinB,CAAAA,EAASiG,gBAAgB,CAAG,CAAC,IAAI,EAAE/vB,KAAK4G,GAAG,GAAG,CAAC,CAEnD,CAGI8oB,EAAcQ,YAAY,EAC5BpG,CAAAA,EAASiG,gBAAgB,EAAI,CAAC,EAAEjG,EAASiG,gBAAgB,CAAG,IAAM,IAAI,IAAI,EACxEL,EAAcQ,YAAY,EAC1B,EAIJ7M,EAAQrkB,OAAOke,MAAM,CAAC,CAAC,EAAGmG,GAE1B,GAAM,CACJ9H,IAAAA,CAAG,CACHyU,IAAAA,EAAM,EAAK,CACXG,QAAAA,EAAU,EAAE,CACZC,WAAAA,EAAa,CAAC,CAAC,CACfC,cAAAA,CAAa,CACbC,sBAAAA,CAAqB,CACrBC,WAAAA,CAAU,CACVC,eAAAA,CAAc,CACdC,eAAAA,CAAc,CACdC,mBAAAA,CAAkB,CAClBC,kBAAAA,CAAiB,CACjBC,OAAAA,CAAM,CACNzf,aAAAA,CAAY,CACZsT,SAAAA,CAAQ,CACRoM,OAAAA,CAAM,CACNC,QAASC,EAAa,CACtBC,sBAAAA,EAAqB,CACrBC,WAAAA,EAAU,CACX,CAAGlb,EACE,CAAEsK,IAAAA,EAAG,CAAE,CAAGoP,EAEVM,GAAmBjG,EAASiG,gBAAgB,CAE9CmB,GAAWzB,EAAMyB,QAAQ,CAEzBlR,GACFjK,EAAWiK,SAAS,CAGhB+M,GAAa4C,EAAc5C,UAAU,EAAI,GACzCoE,GAAkBxB,EAAcyB,6BAA6B,EAGnEC,S8C9fmChO,CAAyB,EAC5D,IAAK,IAAMziB,KAAQgrB,GACjB,OAAOvI,CAAK,CAACziB,EAAK,E9C4fCyiB,GAErB,IAAMiO,GAAQ,CAAC,CAACd,EACVe,GAAiBD,IAASvb,EAAWyb,UAAU,CAC/CC,GACJpR,GAAIE,eAAe,GAAK,GAAamR,mBAAmB,CAEpDC,GAAyB,CAAC,CAAE3R,CAAAA,MAAAA,GAAAA,KAAAA,EAAD,GAAoBO,eAAe,EAC9DqR,GAAkB5R,MAAAA,GAAAA,KAAAA,EAAD,GAAoB6R,qBAAqB,CAE1DC,GAAgB5S,EAAerJ,GAE/Bkc,GACJlc,YAAAA,GACA,GAAmB0K,eAAe,GAChC,GAAmBmR,mBAAmB,CAGxC3b,EAAWyb,UAAU,EACrBG,IACA,CAACI,IAED1kB,EACE,CAAC,kCAAkC,EAAEwI,EAGlC;AAKY,oEAL0D,CAHzB,EAOpD,IAAI+W,GACF,CAAC+E,IACDF,IACA,CAACH,IACD,CAACZ,EAcH,GARI9D,IAAgB,CAACoD,GAAOgB,KAC1Bnf,EAAII,SAAS,CACX,gBACA+f,SuD/hBgC,CACpCC,WAAAA,CAAU,CACVC,OAAAA,CAAM,CACO,EACb,IAAMC,EACJ,iBAAOF,GACPC,KAAWhqB,IAAXgqB,GACAD,EAAaC,EACT,CAAC,yBAAyB,EAAEA,EAASD,EAAW,CAAC,CACjD,UAEN,IAAIA,EACK,0DACE,iBAAOA,EACT,CAAC,SAAS,EAAEA,EAAW,EAAEE,EAAU,CAAC,CAGtC,CAAC,SAAS,EAAEhjB,EAAAA,EAAcA,CAAC,EAAEgjB,EAAU,CAAC,EvD8gBrB,CAAEF,WAAY,GAAOC,OAAQjB,EAAW,IAEhErE,GAAe,IAGb+E,IAA0BL,GAC5B,MAAM,qBAA0D,CAA1D,MAAUliB,EAAAA,EAA8BA,CAAG,CAAC,CAAC,EAAEyG,EAAS,CAAC,EAAzD,qB,MAAA,O,WAAA,G,aAAA,EAAyD,GAGjE,GAAI8b,IAA0BjB,EAC5B,MAAM,qBAAgE,CAAhE,MAAUrhB,EAAAA,EAAoCA,CAAG,CAAC,CAAC,EAAEwG,EAAS,CAAC,EAA/D,qB,MAAA,O,WAAA,G,aAAA,EAA+D,GAGvE,GAAI6a,GAAsBY,GACxB,MAAM,qBAAqD,CAArD,MAAUhiB,EAAAA,EAAyBA,CAAG,CAAC,CAAC,EAAEuG,EAAS,CAAC,EAApD,qB,MAAA,O,WAAA,G,aAAA,EAAoD,GAG5D,GAAI6a,GAAsB3a,WAAAA,EAAWqc,gBAAgB,CACnD,MAAM,qBAEL,CAFK,MACJ,6IADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAI3B,GAAkB,CAACqB,GACrB,MAAM,qBAGL,CAHK,MACJ,CAAC,uEAAuE,EAAEjc,EACvE;AAAI,4EAA0E,CADK,EADlF,qB,MAAA,O,WAAA,G,aAAA,EAGN,GAGF,GAAI,GAAoB,CAACyb,GACvB,MAAM,qBAEL,CAFK,MACJ,CAAC,qDAAqD,EAAEzb,EAAS,qDAAqD,CAAC,EADnH,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAIyb,IAASQ,IAAiB,CAACrB,EAC7B,MAAM,qBAGL,CAHK,MACJ,CAAC,qEAAqE,EAAE5a,EACrE;AAAI,wEAAsE,CADO,EADhF,qB,MAAA,O,WAAA,G,aAAA,EAGN,GAGF,IAAIoX,GAAiBlX,EAAWsc,cAAc,EAAKnhB,EAAIiT,GAAG,CAE1D,GAAI6L,EAAK,CACP,GAAM,CAAE3kB,mBAAAA,CAAkB,CAAE,CAAGyG,EAAQ,qCACvC,GAAI,CAACzG,EAAmB2U,IACtB,MAAM,qBAEL,CAFK,MACJ,CAAC,sDAAsD,EAAEnK,EAAS,CAAC,CAAC,EADhE,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAI,CAACxK,EAAmBgV,IACtB,MAAM,qBAEL,CAFK,MACJ,gEADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAI,CAAChV,EAAmB6lB,IACtB,MAAM,qBAEL,CAFK,MACJ,qEADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAmBF,GAhBItE,CAAAA,IAAgBG,EAAS,IAE3B1J,EAAQ,CACN,GAAIA,EAAMiP,GAAG,CACT,CACEA,IAAKjP,EAAMiP,GAAG,EAEhB,CAAC,CAAC,EAERrF,GAAS,CAAC,EAAEpX,EAAS,EAEnB3E,EAAIiT,GAAG,CAAEtE,QAAQ,CAAC,MAAQhK,MAAAA,GAAoB,CAACic,GAAgB,IAAM,IACrE,CACF5gB,EAAIiT,GAAG,CAAGtO,GAGRA,SAAAA,GAAwB8b,CAAAA,IAA0BjB,CAAiB,EACrE,MAAM,qBAEL,CAFK,MACJ,CAAC,cAAc,EAAEnhB,EAAAA,EAA0CA,CAAC,CAAC,EADzD,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAEF,GACEiK,EAAoB3W,QAAQ,CAACgT,IAC5B8b,CAAAA,IAA0BjB,CAAiB,EAE5C,MAAM,qBAEL,CAFK,MACJ,CAAC,OAAO,EAAE7a,EAAS,GAAG,EAAEtG,EAAAA,EAA0CA,CAAC,CAAC,EADhE,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGEwG,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAYwc,YAAY,GAC1Bxc,EAAWwc,YAAY,CAACtF,GAAQqE,EAAAA,MAAS1E,IAAsB,KAEnE,CAEA,IAAK,IAAMgC,IAAc,CACvB,iBACA,qBACA,iBACD,CACC,GAAK5O,MAAAA,GAAAA,KAAAA,EAAD,EAAoB,CAAC4O,EAAW,CAClC,MAAM,qBAEL,CAFK,MACJ,CAAC,KAAK,EAAE/Y,EAAS,CAAC,EAAE+Y,EAAW,CAAC,EAAEjf,EAAAA,EAA2BA,CAAC,CAAC,EAD3D,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAIJ,OAAMqN,EAASuB,UAAU,GAMtB+S,CAAAA,IAASZ,CAAiB,GAC3B,CAAC3D,IAED5b,GAWAgd,CAAAA,EAAYyB,CAAgB,IAN5BA,CAAAA,EAAcnd,EACZvB,EACAW,EACAV,EACA,CAAC,CAAC4E,EAAWrD,kBAAkB,EAED,EAUlC,IAAMga,GAAS,IAAIsB,GACjBnY,EACAwN,EACA4J,GACA,CACEF,WAAYA,EACd,EAZoB,CAAC,CACrB2D,CAAAA,GACAiB,IACC,CAACF,IAA6B,CAACH,IAChCN,EAAoB,EAUpBvM,EACA1O,EAAWiO,MAAM,CACjBjO,EAAW8N,OAAO,CAClB9N,EAAW+P,aAAa,CACxB/P,EAAWmY,aAAa,CACxBC,EACAlN,GAAe/P,EAAK,mBAGhBshB,ImD/rBJlE,OACEmE,GAAYnE,IAAI,EAClB,EACAC,UACEkE,GAAYlE,OAAO,EACrB,EACAmE,UACED,GAAYpE,MAAM,EACpB,EACAsE,aAAc,EACdnsB,KAAKmgB,CAAI,CAAE,OAAEiM,OAAAA,CAAM,CAAE,CAAV,WAAa,CAAC,EAAd,EACJH,GAAYjsB,IAAI,CAACmgB,EAAMze,KAAAA,EAAW,CAAE0qB,OAAAA,CAAO,EAClD,EACArwB,QAAQokB,CAAI,CAAE,OAAEiM,OAAAA,CAAM,CAAE,CAAV,WAAa,CAAC,EAAd,EACPH,GAAYlwB,OAAO,CAACokB,EAAMze,KAAAA,EAAW,CAAE0qB,OAAAA,CAAO,EACrD,EACApE,SAAS7H,CAAI,EACN8L,GAAYjE,QAAQ,CAAC7H,EAC5B,GnD+qBEkM,GAAoB,CAAC,EACnBC,GAAmBC,CAAAA,EAAAA,EAAAA,mBAAAA,IACnBC,GAAW,CACfC,SAAU7C,CAAmB,IAAnBA,EAAWkC,GAAG,CACxBlP,SAAU1iB,CAAAA,CAAQ2iB,EAAMiP,GAAG,CAC3BY,OAAQ9C,WAAAA,EAAWkC,GAAG,EAIlBtc,GAAmDmd,SwDztB/B,OAC1BF,SAAAA,EAAW,EAAK,CAChBC,OAAAA,EAAS,EAAK,CACd9P,SAAAA,EAAW,EAAK,CACjB,CAJ2B,WAIxB,CAAC,EAJuB,EAK1B,OAAO6P,GAAaC,GAAU9P,CAChC,ExDmtBuE4P,IACjEI,GAAsBC,SyD7sBArd,CAAiB,EAAjBA,KAAAA,IAAAA,GAAAA,CAAAA,EAAY,EAAI,EAC1C,IAAMod,EAAO,CAAC,W,EAAA,UAAClS,OAAAA,CAAKoS,QAAQ,O,EAAY,WAAa,CAMrD,OALKtd,GACHod,EAAK5sB,IAAI,CACP,WADO,EACP,UAAC0a,OAAAA,CAAKtgB,KAAK,WAAWkV,QAAQ,oB,EAAyB,aAGpDsd,CACT,EzDqsBwCpd,IAChCud,GAAiC,EAAE,CAErCC,GAAsB,CAAC,EACvB5B,IACF4B,CAAAA,GAAeC,iBAAiB,CAAG,EAAE,CAClC3f,MAAM,CAAC8d,MACPnxB,MAAM,CAAC,GAAiBizB,sBAAAA,EAAOjW,KAAK,CAACkW,QAAQ,EAC7CxyB,GAAG,CAAC,GAAiBuyB,EAAOjW,KAAK,GAGtC,IAAMmW,GAAe,CAAC,CAAEnH,SAAAA,CAAQ,CAA6B,GAC3D,WAD2D,EAC3D,UAACU,GAAiBD,QAAQ,EAACpsB,MAAO0xB,G,SAChC,uBAAC3G,GAAoBqB,QAAQ,EAACpsB,MmDzrBlC,GAAYksB,OAAO,EAAKN,GAAOrJ,KAAK,CO3C7B,IAAIgB,IP+CiBqI,GAAOO,MAAM,CO/ClB,YAAYzG,YAAY,CP4CtC,IAAIqN,gB,SnDyrBP,uBAACrH,GAA8BA,CAC7BE,OAAQA,GACRE,aAAcA,G,SAEd,uBAACb,GAAkBmB,QAAQ,EAACpsB,MAAOgzB,SmDtrB3CpH,CAAqE,EAErE,GAAI,CAACA,EAAOM,OAAO,EAAI,CAACN,EAAOrJ,KAAK,CAClC,OAAO,KAET,IAAM0Q,EAAqB,CAAC,EAG5B,IAAK,IAAMryB,KADE1C,OAAOgG,IAAI,CAACgvB,SDmIzBC,CAAuB,CACvB,OACEC,cAAAA,EAAgB,EAAK,CACrBC,cAAAA,EAAgB,EAAK,CACrBC,6BAAAA,EAA+B,EAAK,CACf,CAAG,CAAC,EAErB,CAAEC,mBAAAA,CAAkB,CAAEC,OAAAA,CAAM,CAAE,CAAGC,SA/DvCpV,CAAa,CACb+U,CAAsB,CACtBC,CAAsB,EAEtB,IAAMG,EAAyC,CAAC,EAC5CE,EAAa,EAEX5U,EAAqB,EAAE,CAC7B,IAAK,IAAMP,KAAW2D,GAAoB7D,GAAOxd,KAAK,CAAC,GAAGL,KAAK,CAAC,KAAM,CACpE,IAAMmzB,EAAc1V,EAA2BpH,IAAI,CAAC,GAClD0H,EAAQP,UAAU,CAACQ,IAEfoV,EAAerV,EAAQsV,KAAK,CAACxI,IAEnC,GAAIsI,GAAeC,GAAgBA,CAAY,CAAC,EAAE,CAAE,CAClD,GAAM,CAAEhzB,IAAAA,CAAG,CAAE4qB,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAE,CAAGH,GAAsBsI,CAAY,CAAC,EAAE,CACvEJ,CAAAA,CAAM,CAAC5yB,EAAI,CAAG,CAAE0E,IAAKouB,IAAcjI,OAAAA,EAAQD,SAAAA,CAAS,EACpD1M,EAASpZ,IAAI,CAAC,IAAI0lB,GAAmBuI,GAAa,WACpD,MAAO,GAAIC,GAAgBA,CAAY,CAAC,EAAE,CAAE,CAC1C,GAAM,CAAEhzB,IAAAA,CAAG,CAAE6qB,OAAAA,CAAM,CAAED,SAAAA,CAAQ,CAAE,CAAGF,GAAsBsI,CAAY,CAAC,EAAE,CACvEJ,CAAAA,CAAM,CAAC5yB,EAAI,CAAG,CAAE0E,IAAKouB,IAAcjI,OAAAA,EAAQD,SAAAA,CAAS,EAEhD6H,GAAiBO,CAAY,CAAC,EAAE,EAClC9U,EAASpZ,IAAI,CAAC,IAAI0lB,GAAmBwI,CAAY,CAAC,EAAE,GAGtD,IAAIhtB,EAAI6kB,EAAUD,EAAW,cAAgB,SAAY,YAGrD6H,GAAiBO,CAAY,CAAC,EAAE,EAClChtB,CAAAA,EAAIA,EAAEjB,SAAS,CAAC,EAAC,EAGnBmZ,EAASpZ,IAAI,CAACkB,EAChB,MACEkY,EAASpZ,IAAI,CAAC,IAAI0lB,GAAmB7M,IAInC6U,GAAiBQ,GAAgBA,CAAY,CAAC,EAAE,EAClD9U,EAASpZ,IAAI,CAAC0lB,GAAmBwI,CAAY,CAAC,EAAE,EAEpD,CAEA,MAAO,CACLL,mBAAoBzU,EAAS5e,IAAI,CAAC,IAClCszB,OAAAA,CACF,CACF,EAgBIL,EACAC,EACAC,GAGES,EAAKP,EAKT,OAJKD,GACHQ,CAAAA,GAAM,QAAO,EAGR,CACLA,GAAI,OAAW,IAAIA,EAAG,KACtBN,OAAQA,CACV,CACF,EC1JmC5H,EAAO7W,QAAQ,EACZye,MAAM,EAExCP,CAAU,CAACryB,EAAI,CAAGgrB,EAAOrJ,KAAK,CAAC3hB,EAAI,CAErC,OAAOqyB,CACT,EnD0qBgErH,I,SACpD,uBAAC9N,EAAcsO,QAAQ,EAACpsB,MAAO4rB,G,SAC7B,uBAACjS,EAAgByS,QAAQ,EAACpsB,MAAOkyB,G,SAC/B,uBAACrY,EAAmBuS,QAAQ,EAC1BpsB,MAAO,CACL+zB,WAAY,IACVzB,GAAOlY,CACT,EACA4Z,cAAe,IACbjC,GAAekC,CACjB,EACAA,QAASvB,GACTwB,iBAAkB,IAAIjY,GACxB,E,SAEA,uBAACnC,EAAgBsS,QAAQ,EACvBpsB,MAAO,GACLyyB,GAAqB/sB,IAAI,CAACqX,G,SAG5B,uBAACoX,EAAAA,aAAaA,CAAAA,CAACC,SAAUpC,G,SACvB,uBAAChI,GAAmBoC,QAAQ,EAACpsB,MAAO+vB,E,SACjCpE,C,qBAmBjB0I,GAAO,IAAM,KACbC,GAED,CAAC,CAAE3I,SAAAA,CAAQ,CAAE,GAEd,WADF,EACE,uB,UAEE,W,EAAA,UAAC0I,GAAAA,CAAAA,GACD,W,EAAA,UAACvB,GAAAA,C,SACC,oC,UAEG5D,EACC,WADDA,EACC,uB,UACGvD,EACD,W,EAAA,UAAC0I,GAAAA,CAAAA,G,GAGH1I,EAGF,W,EAAA,UAAC0I,GAAAA,CAAAA,G,QAOL7U,GAAM,CACV/E,IAAAA,EACArK,IAAK0b,GAAe1kB,KAAAA,EAAYgJ,EAChCW,IAAK+a,GAAe1kB,KAAAA,EAAY2J,EAChCgE,SAAAA,EACAwN,MAAAA,EACA4J,OAAAA,GACAjJ,OAAQjO,EAAWiO,MAAM,CACzBH,QAAS9N,EAAW8N,OAAO,CAC3BiC,cAAe/P,EAAW+P,aAAa,CACvCuP,QAAS,GAEL,WADF,EACE,UAACD,GAAAA,C,SACE1G,GAAerO,GA/TAL,GA+TsB,CAAE,GAAGvC,CAAK,CAAEiP,OAAAA,EAAO,E,GAI/D4I,uBAAwB,MACtBC,EACAtuB,EAA8B,CAAC,CAAC,IAMhC,GAAM,CAAEoO,KAAAA,CAAI,CAAE+d,KAAMoC,CAAc,CAAE,CAAG,MAAMD,EAAOE,UAAU,CAAC,CAC7DC,WALiB,GACV,GAAgB,WAAfjY,EAAe,UAACkY,EAAAA,CAAS,GAAGlY,CAAK,EAK3C,GACMmY,EAAS9C,GAAiB8C,MAAM,CAAC,CAAEC,MAAO5uB,EAAQ4uB,KAAK,GAE7D,OADA/C,GAAiBtJ,KAAK,GACf,CAAEnU,KAAAA,EAAM+d,KAAMoC,EAAgBI,OAAAA,CAAO,CAC9C,CACF,EAGMpE,GACJ,CAACF,IAAUvb,CAAAA,EAAWyb,UAAU,EAAKxB,GAAQpD,CAAAA,IAAgBG,EAAS,GAElE+I,GAAwB,KAC5B,IAAMF,EAAS9C,GAAiB8C,MAAM,GAEtC,OADA9C,GAAiBtJ,KAAK,GACf,WAAP,EAAO,sB,SAAGoM,C,EACZ,EAiBA,GAfAnY,EAAQ,MAAM2C,EAAoBC,GAAK,CACrCgV,QAAS/U,GAAI+U,OAAO,CACpBrV,UAAAA,GACA0M,OAAAA,GACApM,IAAAA,EACF,GAEKgR,CAAAA,IAASZ,CAAiB,GAAMvC,GACnC1Q,CAAAA,EAAMsY,WAAW,CAAG,EAAG,EAGrBzE,IACF7T,CAAAA,EKtwB2B,OLswBL,CAAG,EAAG,EAG1B6T,IAAS,CAACvE,GAAY,KACpBja,EAkHAmf,EAhHJ,GAAI,CACFnf,EAAO,MAAMsW,CAAAA,EAAAA,GAAAA,SAAAA,IAAY3b,KAAK,CAC5BoH,GAAAA,EAAUA,CAAC2b,cAAc,CACzB,CACElH,SAAU,CAAC,eAAe,EAAEzT,EAAS,CAAC,CACtC7T,WAAY,CACV,aAAc6T,CAChB,CACF,EACA,IACE2a,EAAe,CACb,GAAIsB,GAAgB,CAAElB,OAAAA,CAAO,EAAI1oB,KAAAA,CAAS,CAC1C,GAAIimB,EACA,CAAE6H,UAAW,GAAMC,QAAS,GAAMrG,YAAaA,CAAY,EAC3D1nB,KAAAA,CAAS,CACb2b,QAAS,IAAK9N,EAAW8N,OAAO,EAAI,EAAE,CAAE,CACxCG,OAAQjO,EAAWiO,MAAM,CACzB8B,cAAe/P,EAAW+P,aAAa,CACvCoQ,iBAAkBngB,EAAW1E,oBAAoB,CAC7C,YACAkgB,GACE,QACA,OACR,GAEN,CAAE,MAAO4E,EAAuB,CAM9B,MAHIA,GAAoBA,WAAAA,EAAiB5d,IAAI,EAC3C,OAAO4d,EAAiB5d,IAAI,CAExB4d,CACR,CAEA,GAAIrjB,MAAAA,EACF,MAAM,qBAAgC,CAAhC,MAAUtD,EAAAA,EAAqBA,EAA/B,qB,MAAA,O,WAAA,G,aAAA,EAA+B,GAGvC,IAAMqf,EAAc7vB,OAAOgG,IAAI,CAAC8N,GAAMrS,MAAM,CAC1C,GACEiB,eAAAA,GACAA,UAAAA,GACAA,aAAAA,GACAA,aAAAA,GAGJ,GAAImtB,EAAYhsB,QAAQ,CAAC,uBACvB,MAAM,qBAA2C,CAA3C,MAAU6M,EAAAA,EAAgCA,EAA1C,qB,MAAA,O,WAAA,G,aAAA,EAA0C,GAGlD,GAAImf,EAAY9tB,MAAM,CACpB,MAAM,qBAAwD,CAAxD,MAAU4tB,GAAe,iBAAkBE,IAA3C,qB,MAAA,O,WAAA,G,aAAA,EAAuD,GAgB/D,GAAI,aAAc/b,GAAQA,EAAKsjB,QAAQ,CAAE,CACvC,GAAIvgB,SAAAA,EACF,MAAM,qBAEL,CAFK,MACJ,4FADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAGFiU,CAAAA,EAASuM,UAAU,CAAG,EACxB,CAEA,GACE,aAAcvjB,GACdA,EAAKmc,QAAQ,EACb,iBAAOnc,EAAKmc,QAAQ,CACpB,CAGA,GAFAD,GAAoBlc,EAAKmc,QAAQ,CAAc/d,EAAK,kBAEhDqgB,GACF,MAAM,qBAGL,CAHK,MACJ,CAAC,0EAA0E,EAAErgB,EAAIiT,GAAG,CAAC;AAAG,mFAAC,EADrF,qB,MAAA,O,WAAA,G,aAAA,EAGN,EAGArR,CAAAA,EAAa2K,KAAK,CAAG,CACrB6Y,aAAcxjB,EAAKmc,QAAQ,CAACC,WAAW,CACvCqH,oBAAqBlV,GAAkBvO,EAAKmc,QAAQ,CACtD,EACsC,SAA3Bnc,EAAKmc,QAAQ,CAACxK,QAAQ,EAC7B3R,CAAAA,EAAa2K,KAAK,CAAC+Y,sBAAsB,CAAG1jB,EAAKmc,QAAQ,CAACxK,QAAQ,EAEtEqF,EAAS2M,UAAU,CAAG,EACxB,CAEA,GACE,CAACzG,GAAOuB,EAAa,GACrB,CAACzH,EAASuM,UAAU,EACpB,CAACrc,EAAoBnE,EAAU,iBAAkB,EAAc4H,KAAK,EAGpE,MAAM,qBAEL,CAFK,MACJ,6EADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAIF,GAAI,eAAgB3K,EAAM,CACxB,GAAIA,EAAKmf,UAAU,EAAIlc,WAAAA,EAAWqc,gBAAgB,CAChD,MAAM,qBAEL,CAFK,MACJ,8HADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAEF,GAAI,iBAAOtf,EAAKmf,UAAU,EACxB,GAAKtvB,OAAO+zB,SAAS,CAAC5jB,EAAKmf,UAAU,GAO9B,GAAInf,EAAKmf,UAAU,EAAI,EAC5B,MAAM,qBAIL,CAJK,MACJ,CAAC,qEAAqE,EAAE/gB,EAAIiT,GAAG,CAAC;AAAsH;AACpM;AAAyF,kEADyG,CAAC,EADjM,qB,MAAA,O,WAAA,G,aAAA,EAIN,EAEIrR,CAAAA,EAAKmf,UAAU,CAAG,SAEpB/jB,QAAQb,IAAI,CACV,CAAC,oEAAoE,EAAE6D,EAAIiT,GAAG,CAAC;AAAqC,gHAAF,CAAC,EAKvH8N,EAAanf,EAAKmf,UAAU,MArB5B,MAAM,qBAKL,CALK,MACJ,CAAC,6EAA6E,EAAE/gB,EAAIiT,GAAG,CAAC,0BAA0B,EAAErR,EAAKmf,UAAU,CAChI;AAAoB,2BAAS,EAAEzpB,KAAKmuB,IAAI,CACvC7jB,EAAKmf,UAAU,EACf,yDAAyD,CAH0F,EADnJ,qB,MAAA,O,WAAA,G,aAAA,EAKN,QAkBG,GAAInf,CAAoB,IAApBA,EAAKmf,UAAU,CAIxBA,EAAa,OACR,GACLnf,CAAoB,IAApBA,EAAKmf,UAAU,EACf,KAA2B,IAApBnf,EAAKmf,UAAU,CAGtBA,EAAa,QAEb,MAAM,qBAIL,CAJK,MACJ,CAAC,8HAA8H,EAAE/sB,KAAKC,SAAS,CAC7I2N,EAAKmf,UAAU,EACf,MAAM,EAAE/gB,EAAIiT,GAAG,CAAC,CAAC,EAHf,qB,MAAA,O,WAAA,G,aAAA,EAIN,EAEJ,MAEE8N,EAAa,GAcf,GAXAxU,EAAM+C,SAAS,CAAGxhB,OAAOke,MAAM,CAC7B,CAAC,EACDO,EAAM+C,SAAS,CACf,UAAW1N,EAAOA,EAAK2K,KAAK,CAAGvV,KAAAA,GAIjC4hB,EAAS8M,YAAY,CAAG,CAAE3E,WAAAA,EAAYC,OAAQhqB,KAAAA,CAAU,EACxD4hB,EAAS+M,QAAQ,CAAGpZ,EAGhBqM,EAASuM,UAAU,CACrB,OAAO,IAAIzM,GAAa,KAAM,CAAEE,SAAAA,CAAS,EAE7C,CAMA,GAJI4G,GACFjT,CAAAA,EKn8B2B,OLm8BL,CAAG,EAAG,EAG1BiT,GAAsB,CAAC3D,GAAY,KACjCja,EAIJ,IAAIgkB,EAAkB,GAyBtB,GAAI,CACFhkB,EAAO,MAAMsW,CAAAA,EAAAA,GAAAA,SAAAA,IAAY3b,KAAK,CAC5BoH,GAAAA,EAAUA,CAAC6b,kBAAkB,CAC7B,CACEpH,SAAU,CAAC,mBAAmB,EAAEzT,EAAS,CAAC,CAC1C7T,WAAY,CACV,aAAc6T,CAChB,CACF,EACA,SACE6a,EAAmB,CACjBxf,IAAKA,EAGLW,IAxCSA,EAyCTwR,MAAAA,EACA0T,YAAahhB,EAAWghB,WAAW,CACnC,GAAIjF,GAAgB,CAAElB,OAAAA,CAAO,EAAI1oB,KAAAA,CAAS,CAC1C,GAAI0nB,CAAgB,IAAhBA,EACA,CAAEoG,UAAW,GAAMC,QAAS,GAAMrG,YAAaA,CAAY,EAC3D1nB,KAAAA,CAAS,CAIb2b,QAAS,IAAK9N,EAAW8N,OAAO,EAAI,EAAE,CAAE,CACxCG,OAAQjO,EAAWiO,MAAM,CACzB8B,cAAe/P,EAAW+P,aAAa,IAI7CgE,EAAS8M,YAAY,CAAG,CAAE3E,WAAY,EAAGC,OAAQhqB,KAAAA,CAAU,CAC7D,CAAE,MAAO8uB,EAA2B,CASlC,K2D9lCa,UAAf,O3DylCYA,G2DzlCezb,O3DylCfyb,G2DzlC+B,S3DylC/BA,G2DzlCgD,Y3DylChDA,GACRA,WAAAA,EAAqBze,IAAI,EAEzB,OAAOye,EAAqBze,IAAI,CAE5Bye,CACR,CAEA,GAAIlkB,MAAAA,EACF,MAAM,qBAAiC,CAAjC,MAAUrD,EAAAA,EAAsBA,EAAhC,qB,MAAA,O,WAAA,G,aAAA,EAAgC,EAGpC,GAAcgO,KAAK,YAAYa,SACjCwY,CAAAA,EAAkB,EAAG,EAGvB,IAAMjI,EAAc7vB,OAAOgG,IAAI,CAAC8N,GAAMrS,MAAM,CAC1C,GAASiB,UAAAA,GAAmBA,aAAAA,GAAsBA,aAAAA,GAGpD,GAAI,EAAcu1B,iBAAiB,CACjC,MAAM,qBAEL,CAFK,MACJ,CAAC,2FAA2F,EAAEphB,EAAS,CAAC,EADpG,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAEF,GAAI,EAAcqhB,iBAAiB,CACjC,MAAM,qBAEL,CAFK,MACJ,CAAC,2FAA2F,EAAErhB,EAAS,CAAC,EADpG,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAIgZ,EAAY9tB,MAAM,CACpB,MAAM,qBAA4D,CAA5D,MAAU4tB,GAAe,qBAAsBE,IAA/C,qB,MAAA,O,WAAA,G,aAAA,EAA2D,GAGnE,GAAI,aAAc/b,GAAQA,EAAKsjB,QAAQ,CAAE,CACvC,GAAIvgB,SAAAA,EACF,MAAM,qBAEL,CAFK,MACJ,4FADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAIF,OADAiU,EAASuM,UAAU,CAAG,GACf,IAAIzM,GAAa,KAAM,CAAEE,SAAAA,CAAS,EAC3C,CAkBA,GAhBI,aAAchX,GAAQ,iBAAOA,EAAKmc,QAAQ,GAC5CD,GAAoBlc,EAAKmc,QAAQ,CAAc/d,EAAK,sBAClD4B,EAAa2K,KAAK,CAAG,CACrB6Y,aAAcxjB,EAAKmc,QAAQ,CAACC,WAAW,CACvCqH,oBAAqBlV,GAAkBvO,EAAKmc,QAAQ,CACtD,EACsC,SAA3Bnc,EAAKmc,QAAQ,CAACxK,QAAQ,EAC7B3R,CAAAA,EAAa2K,KAAK,CAAC+Y,sBAAsB,CAAG1jB,EAAKmc,QAAQ,CAACxK,QAAQ,EAEtEqF,EAAS2M,UAAU,CAAG,IAGpBK,GACAhkB,CAAAA,EAAa2K,KAAK,CAAG,MAAM,EAAcA,KAAK,EAIhD,CAACuS,GAAOuB,EAAa,GACrB,CAACvX,EAAoBnE,EAAU,qBAAsB,EAAc4H,KAAK,EAGxE,MAAM,qBAEL,CAFK,MACJ,iFADI,qB,MAAA,M,WAAA,G,aAAA,EAEN,EAGFA,CAAAA,EAAM+C,SAAS,CAAGxhB,OAAOke,MAAM,CAAC,CAAC,EAAGO,EAAM+C,SAAS,CAAE,EAAc/C,KAAK,EACxEqM,EAAS+M,QAAQ,CAAGpZ,CACtB,CAgBA,GAAI,GAAsB,CAAC6T,IAAUxH,EAAS2M,UAAU,CACtD,OAAO,IAAI7M,GAAa1kB,KAAKC,SAAS,CAACsY,GAAQ,CAC7CqM,SAAAA,CACF,GAUF,GALIiD,IACFtP,CAAAA,EAAM+C,SAAS,CAAG,CAAC,GAIjBP,EAAUpO,IAAQ,CAACyf,GAAO,OAAO,IAAI1H,GAAa,KAAM,CAAEE,SAAAA,CAAS,GAIvE,IAAIqN,GAAwB9G,EAC5B,GAAIzD,IAAgBkF,GAAe,K4D1sC/BsF,E5D2sCF,IAAMtd,E4D1sCDsd,CADHA,ECNGt3B,CCOF,SAA2Bga,CAAY,EAC5C,IAAMud,EACJ,iBAAiB/wB,IAAI,CAACwT,IAAS,CAACoF,EAAepF,GAC3C,SAASA,EACTA,MAAAA,EACE,SACA+E,EAAmB/E,EAEc,EACvC,GAAM,CAAEwd,MAAAA,CAAK,CAAE,CAAGxlB,EAAQ,QACpBylB,EAAeD,EAAME,SAAS,CAACH,GACrC,GAAIE,IAAiBF,EACnB,MAAM,IAAI1W,EACR,yCAAyC0W,EAAW,IAAGE,EAG7D,CAEA,OAAOF,CACT,G9DurCuDxhB,G6DjtCzCtT,OAAO,CAAC,MAAO,MDOduc,UAAU,CAAC,YAAc,CAACI,EAAekY,GAClDA,EAAMz1B,KAAK,CAAC,GACZy1B,WAAAA,EACEA,EACA,I5D0sCAtd,KAAQqd,GAAsBM,KAAK,EACrCN,CAAAA,GAAwB,CACtB,GAAGA,EAAqB,CACxBM,MAAO,CACL,GAAGN,GAAsBM,KAAK,CAC9B,CAAC3d,EAAK,CAAE,IACHqd,GAAsBM,KAAK,CAAC3d,EAAK,IACjCqd,GAAsBO,gBAAgB,CAACj3B,MAAM,CAAC,GAC/CqH,EAAEjF,QAAQ,CAAC,mBAEd,EAEH60B,iBAAkBP,GAAsBO,gBAAgB,CAACj3B,MAAM,CAC7D,GAAO,CAACqH,EAAEjF,QAAQ,CAAC,kBAEvB,EAEJ,CAEA,IAAM80B,GAAO,CAAC,CAAElL,SAAAA,CAAQ,CAA6B,GAC5CzW,GAAYyW,EAAW,WAAXA,EAAW,UAACmL,MAAAA,CAAIC,GAAG,S,SAAUpL,C,GAG5CqL,GAAiB,cAkHjBC,EAqCAnC,EAlIJ,eAAeoC,EACbC,CAGiC,EAEjC,IAAMxC,EAAyB,MAC7BxuB,EAA8B,CAAC,CAAC,IAEhC,GAAIqZ,GAAI/E,GAAG,EAAIgV,EAWb,OATI0H,GACFA,EAAY5X,GAAKL,IAQZ,CAAE3K,KALI,MAAMsY,GACjB,WADiBA,EACjB,UAACgK,GAAAA,C,SACC,uBAACpH,EAAAA,CAAAA,E,IAGU6C,KAAAA,EAAK,EAGtB,GAAIpD,GAAQvS,CAAAA,EAAMiP,MAAM,EAAIjP,EAAMuC,SAAS,EACzC,MAAM,qBAEL,CAFK,MACJ,0IADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAM,CAAEK,IAAK6X,CAAW,CAAElY,UAAWmY,CAAiB,CAAE,CAhlC1D,mBAilCsBlxB,EAhlCjB,CACLoZ,IA+kC+BA,GA9kC/BL,UAAW/Y,EA8kCyB+Y,GA7kCtC,EAGK,CACLK,IAAKpZ,EAAQyuB,UAAU,CAAGzuB,EAAQyuB,UAAU,CAykCXrV,IAAAA,GAxkCjCL,UAAW/Y,EAAQmxB,gBAAgB,CAC/BnxB,EAAQmxB,gBAAgB,CAukCUpY,IAAAA,EArkCxC,EAukCYoC,EAAS,MAAM6V,EAAYC,EAAaC,GAI9C,OAHA,MAAM/V,EAAO2L,QAAQ,CAGd,CAAE1Y,KAFI,MAAMqN,GAAeN,GAEnBgR,KAAAA,EAAK,CACtB,EACMiF,EAAc,CAAE,GAAG/X,EAAG,CAAEmV,WAAAA,CAAW,EACnC6C,EAAiC,MAAMlY,EAC3C8Q,GACAmH,GAGF,GAAIpY,EAAUpO,IAAQ,CAACyf,GAAO,OAAO,KAErC,GAAI,CAACgH,GAAY,iBAAOA,EAASjjB,IAAI,CAInC,MAAM,qBAAkB,CAAlB,MAHU,CAAC,CAAC,EAAE0K,EAClBmR,IACA,+FAA+F,CAAC,EAC5F,qB,MAAA,O,WAAA,G,aAAA,EAAiB,GAGzB,MAAO,CAAEoH,SAAAA,EAAUD,YAAAA,CAAY,CACjC,CAtE4D,GKzrC3B,yBL2rCT,CAsExB,IAAME,EAAgB,CAACC,EAAeC,KACpC,IAAMP,EAAcM,GAAQnY,GACtB8X,EAAoBM,GAAczY,GAExC,OAAOM,GAAI/E,GAAG,EAAIgV,EAChB,WADgBA,EAChB,UAACoH,GAAAA,C,SACC,uBAACpH,EAAAA,CAAAA,E,GAGH,W,EAAA,UAACoH,GAAAA,C,SACC,uBAACvC,GAAAA,C,SACE1G,GAAewJ,EAAaC,EAAmB,CAC9C,GAAG1a,CAAK,CACRiP,OAAAA,EACF,E,IAIR,EAGMuL,EAAc,MAClBC,EACAC,KAEA,IAAMriB,EAAUyiB,EAAcL,EAAaC,GAC3C,OAAO,MAAMO,SuBrqCuB,CACxCpgB,eAAAA,CAAc,CACdsV,QAAAA,CAAO,CACP+K,cAAAA,CAAa,CAKd,EACC,MAAOvP,CAAAA,EAAAA,GAAAA,SAAAA,IAAY3b,KAAK,CAACqH,GAAAA,EAAaA,CAAC8jB,sBAAsB,CAAE,SAC7DtgB,EAAesgB,sBAAsB,CAAChL,EAAS+K,GAEnD,EvBypC6C,CACrCrgB,eAAgBwV,IAChBF,QAAS9X,CACX,EACF,EAEM+iB,EACmC,CAAC,CAAC3H,GAAS3Q,eAAe,CAQ7D,CAACuY,EAA0BhjB,EAAQ,CAAG,MAAMwI,QAAQrb,GAAG,CAAC,CAC5D0qB,GAAemI,MACd,WACC,GAAI+C,EAA4B,CAE9B,GAAId,OADJA,CAAAA,EAA0B,MAAMC,EAAyBC,EAAW,EAC9B,OAAO,KAC7C,GAAM,CAAEK,SAAAA,CAAQ,CAAE,CAAGP,EACrB,OAAOO,EAASjjB,IAAI,CACf,CACL0iB,EAA0B,CAAC,EAC3B,IAAM3V,EAAS,MAAM6V,EAAY5X,GAAKL,IAEtC,OADA,MAAMoC,EAAO2L,QAAQ,CACdrL,GAAeN,EACxB,CACF,KACD,EAED,GAAItM,OAAAA,EACF,OAAO,KAMT,GAAM,CAAEwiB,SAAAA,CAAQ,CAAE,CAAG,GAAoC,CAAC,EAkB1D,OARIO,GACFjD,EAAS0C,EAAS1C,MAAM,CACxBxC,GAAOkF,EAASlF,IAAI,GAEpBwC,EAAS9C,GAAiB8C,MAAM,GAChC9C,GAAiBtJ,KAAK,IAGjB,CACLuP,YAtBkBD,EAA2BhjB,EAuB7CkjB,gBAnBsB,GAIb,WAAP,EAAO,UAAC9H,GAAAA,CAAU,GAAG+H,CAAS,CAAG,GAAGX,CAAQ,GAgB9ClF,KAAAA,GACA8F,SAAU,EAAE,CACZtD,OAAAA,CACF,CACF,EAEAxM,CAAAA,EAAAA,GAAAA,SAAAA,IAAY+P,oBAAoB,CAAC,aAAcpjB,EAAW+D,IAAI,EAC9D,IAAMsf,GAAiB,MAAMhQ,CAAAA,EAAAA,GAAAA,SAAAA,IAAY3b,KAAK,CAC5CoH,GAAAA,EAAUA,CAACijB,cAAc,CACzB,CACExO,SAAU,CAAC,qBAAqB,EAAEvT,EAAW+D,IAAI,CAAC,CAAC,CACnD9X,WAAY,CACV,aAAc+T,EAAW+D,IAAI,CAEjC,EACA,SAAYge,MAEd,GAAI,CAACsB,GACH,OAAO,IAAIxP,GAAa,KAAM,CAAEE,SAAAA,CAAS,GAG3C,IAAMuP,GAAoB,IAAItc,IACxBuc,GAAiB,IAAIvc,IAE3B,IAAK,IAAMwc,KAAOhG,GAAsB,CACtC,IAAMiG,EAAelJ,CAAqB,CAACiJ,EAAI,CAE3CC,IACFH,GAAkBxc,GAAG,CAAC2c,EAAa3B,EAAE,EACrC2B,EAAaC,KAAK,CAACzhB,OAAO,CAAC,IACzBshB,GAAezc,GAAG,CAACgJ,EACrB,GAEJ,CAEA,IAAM5P,GAAY+c,GAASE,MAAM,CAG3B,CACJrc,YAAAA,EAAW,CACXiP,cAAAA,EAAa,CACb4T,wBAAAA,EAAuB,CACvBxL,cAAAA,EAAa,CACblK,OAAAA,EAAM,CACNH,QAAAA,EAAO,CACP8V,cAAAA,EAAa,CACd,CAAG5jB,EACEkjB,GAAuB,CAC3BW,cAAe,CACbnc,MAAAA,EACA3D,KAAMjE,EACNwN,MAAAA,EACA8B,QAASuK,EAAcvK,OAAO,CAC9BtO,YAAaA,KAAAA,GAAqB3O,KAAAA,EAAY2O,GAC9C8iB,cAAAA,GACAnI,WAAYA,CAAe,IAAfA,IAA6BtpB,KAAAA,EACzC2xB,WAAYjN,CAAiB,IAAjBA,IAA+B1kB,KAAAA,EAC3C6kB,WAAAA,GACAiE,sBAAAA,GACA8I,WACET,IAAAA,GAAkBj1B,IAAI,CAClB8D,KAAAA,EACA3D,MAAMd,IAAI,CAAC41B,IACjB9d,IAAKxF,EAAWwF,GAAG,EA1iCvBA,EA0iC8CxF,EAAWwF,GAAG,CAriC5D,GAvBIsU,EACF,SAGAA,EAASkK,CqDzZU,CAACvM,GAAY,ErDyZA,SAG3B,CACL5sB,KAAM2a,EAAI3a,IAAI,CACdivB,OAAAA,EACAjiB,QAASosB,KAAUze,EAAI3N,OAAO,EAC9BqsB,MAAO1e,EAAI0e,KAAK,CAChBC,OAAQ,EAAaA,MAAM,GAetB,CACLt5B,KAAM,yBACNgN,QAAS,+BACT0T,WAAY,GACd,GA6hCgEpZ,KAAAA,EAC5DiyB,IAAK,EAAE3J,GAAwBtoB,KAAAA,EAC/BkyB,KAAM,EAAE1J,GAA4BxoB,KAAAA,EACpCmyB,aAAc3K,EAAc2K,YAAY,CACxCC,IAAK3I,EAAAA,IAAgCzpB,KAAAA,EACrCqyB,OAAQ,CAAC9I,IAAmCvpB,KAAAA,EAC5C8b,OAAAA,GACAH,QAAAA,GACAiC,cAAAA,GACAoI,cAAAA,GACAC,UAAWA,CAAc,IAAdA,GAA4BjmB,KAAAA,EACvCipB,gBAAiBA,IAAmBnB,EAAMmB,GAAkBjpB,KAAAA,CAC9D,EACAsyB,eAAgBzkB,EAAWykB,cAAc,CACzCnK,cAAe8G,GACfsD,sBA1CoE,CAAC,EA2CrEC,gBAAiBhO,GAAOO,MAAM,CAC9B0N,cACE,CAAC5kB,EAAWoa,OAAO,EAAIlP,GAAe/P,EAAK,kBACvC,CAAC,EAAE6E,EAAW4kB,aAAa,EAAI,GAAG,CAAC,EAAE5kB,EAAWiO,MAAM,CAAC,CAAC,CACxDjO,EAAW4kB,aAAa,CAC9BxK,QAAAA,EACAna,UAAAA,GACA4kB,cAAe,CAAC,CAAC5K,EACjB/Z,UAAAA,GACAqjB,eAAgB/0B,MAAMd,IAAI,CAAC61B,IAC3BuB,mBAAoB,IAAI9d,IAAIhH,EAAW8kB,kBAAkB,EAAI,EAAE,EAC/DhkB,YAAAA,GAEAikB,mBACEnvB,EACemvB,kBACF5yB,CACf6yB,mBAAoB3K,EAAW2K,kBAAkB,CACjDhL,iBAAAA,GACA8C,aAAAA,GACA7O,OAAAA,GACA0V,wBAAAA,GACAtG,KAAMgG,GAAehG,IAAI,CACzB8F,SAAUE,GAAeF,QAAQ,CACjCtD,OAAQwD,GAAexD,MAAM,CAC7BoF,YAAajlB,EAAWilB,WAAW,CACnCzkB,YAAaR,EAAWQ,WAAW,CACnC6b,iBAAkBrc,EAAWqc,gBAAgB,CAC7C6I,kBAAmBllB,EAAWklB,iBAAiB,CAC/CnK,QAASC,GACTmK,mBAAoBnlB,EAAWmlB,kBAAkB,CACjDC,iBAAkBplB,EAAWolB,gBAAgB,CAC7CC,gCACErlB,EAAWslB,YAAY,CAACC,mBAAmB,EAGzCC,GACJ,WADIA,EACJ,UAAC9gB,EAAgByS,QAAQ,EAACpsB,MAAOkyB,G,SAC/B,uBAACpS,EAAYsM,QAAQ,EAACpsB,MAAOm4B,G,SAC1BG,GAAeJ,eAAe,CAACC,G,KAKhCuC,GAAe,MAAMpS,CAAAA,EAAAA,GAAAA,SAAAA,IAAY3b,KAAK,CAC1CoH,GAAAA,EAAUA,CAAC8Y,cAAc,CACzB,SAAYA,GAAe4N,KA0BvB,CAACE,GAAoBC,GAAmB,CAAGF,GAAal6B,KAAK,CACjE,8EACA,GAGE2M,GAAS,GACRutB,GAAa1c,UAAU,CAAC2O,KAC3Bxf,CAAAA,IAAUwf,EAAM,EAElBxf,IAAUwtB,GACNzlB,IACF/H,CAAAA,IAAU,8BAAuB,EAGnC,IAAM6H,GAAU7H,GAASmrB,GAAeL,WAAW,CAAG2C,GAOtD,OAAO,IAAI9R,GALW,MAAMhU,EAAgBC,EAAUC,GAASC,EAAY,CACzEC,UAAAA,GACAC,UAAAA,EACF,GAEuC,CAAE6T,SAAAA,CAAS,EACpD,CAYO,IAAM6R,GAA4B,CACvCzqB,EACAW,EACAgE,EACAwN,EACAtN,EACA2Z,EACAC,IAEOH,GACLte,EACAW,EACAgE,EACAwN,EACAtN,EACAA,EACA2Z,EACAC,G+DnlDSiM,GAAAA,WAAAA,EACXlhB,IAAAA,aAAmB,CAAgC,MAE9C,SAASmhB,GAAsBpf,CAA+B,EACnE,IAAMqf,EAAgC/a,CAAAA,EAAAA,EAAAA,UAAAA,EAAW6a,IAE7CE,GACFA,EAA8Brf,EAElC,CCsGO,MAAMsf,WAAyB7iB,EAMpCrV,YAAYoD,CAAgC,CAAE,CAC5C,KAAK,CAACA,GAEN,IAAI,CAAC+0B,UAAU,CAAG/0B,EAAQ+0B,UAAU,CAG/BC,OACL/qB,CAAoB,CACpBW,CAAmB,CACnB+L,CAAiC,CACV,CACvB,OAAO4R,GACLte,EACAW,EACA+L,EAAQ9D,IAAI,CACZ8D,EAAQyF,KAAK,CACbzF,EAAQ7H,UAAU,CAClB,CACEsK,IAAK,IAAI,CAAC2b,UAAU,CAAC3b,GAAG,CACxB6Q,SAAU,IAAI,CAAC8K,UAAU,CAAC9K,QAAQ,EAEpCtT,EAAQ8R,aAAa,CACrB9R,EAAQ+R,aAAa,CAEzB,CACF,CAEA,IAAMuM,GAAW,CACfC,SAAUC,CACZ,EAKA,GAAeL,E", "sources": ["webpack://next/./dist/compiled/@edge-runtime/cookies/index.js", "webpack://next/./dist/compiled/cookie/index.js", "webpack://next/./dist/compiled/react-is/cjs/react-is.production.js", "webpack://next/./dist/compiled/react-is/index.js", "webpack://next/./dist/compiled/strip-ansi/index.js", "webpack://next/./dist/src/lib/picocolors.ts", "webpack://next/./dist/src/build/output/log.ts", "webpack://next/./dist/src/server/lib/lru-cache.ts", "webpack://next/./dist/src/lib/constants.ts", "webpack://next/./dist/src/server/api-utils/index.ts", "webpack://next/./dist/src/server/api-utils/node/try-get-preview-data.ts", "webpack://next/external node-commonjs \"crypto\"", "webpack://next/./dist/src/server/crypto-utils.ts", "webpack://next/./dist/src/server/lib/trace/constants.ts", "webpack://next/./dist/src/server/optimize-amp.ts", "webpack://next/./dist/src/lib/non-nullable.ts", "webpack://next/./dist/src/server/post-process.ts", "webpack://next/./dist/src/server/web/spec-extension/adapters/headers.ts", "webpack://next/./dist/src/server/web/spec-extension/adapters/reflect.ts", "webpack://next/./dist/src/shared/lib/modern-browserslist-target.js", "webpack://next/./src/server/ReactDOMServerPages.js", "webpack://next/external commonjs \"next/dist/server/lib/trace/tracer\"", "webpack://next/external commonjs2 \"critters\"", "webpack://next/external commonjs2 \"next/dist/compiled/@ampproject/toolbox-optimizer\"", "webpack://next/external commonjs2 \"next/dist/compiled/jsonwebtoken\"", "webpack://next/external node-commonjs \"path\"", "webpack://next/external commonjs2 \"react-dom/server.browser\"", "webpack://next/external commonjs2 \"react-dom/server.edge\"", "webpack://next/webpack/bootstrap", "webpack://next/webpack/runtime/compat get default export", "webpack://next/webpack/runtime/define property getters", "webpack://next/webpack/runtime/hasOwnProperty shorthand", "webpack://next/webpack/runtime/make namespace object", "webpack://next/./dist/src/server/render.tsx", "webpack://next/./dist/src/server/route-modules/route-module.ts", "webpack://next/external commonjs2 \"react/jsx-runtime\"", "webpack://next/external commonjs2 \"react\"", "webpack://next/external commonjs2 \"styled-jsx\"", "webpack://next/./dist/src/shared/lib/constants.ts", "webpack://next/./dist/src/shared/lib/is-plain-object.ts", "webpack://next/./dist/src/lib/is-serializable-props.ts", "webpack://next/./dist/src/shared/lib/amp-context.shared-runtime.ts", "webpack://next/./dist/src/shared/lib/head-manager-context.shared-runtime.ts", "webpack://next/./dist/src/shared/lib/loadable-context.shared-runtime.ts", "webpack://next/./dist/src/shared/lib/loadable.shared-runtime.tsx", "webpack://next/./dist/src/shared/lib/router-context.shared-runtime.ts", "webpack://next/./dist/src/shared/lib/page-path/ensure-leading-slash.ts", "webpack://next/./dist/src/shared/lib/segment.ts", "webpack://next/./dist/src/shared/lib/router/utils/interception-routes.ts", "webpack://next/./dist/src/shared/lib/router/utils/is-dynamic.ts", "webpack://next/./dist/src/shared/lib/router/utils/app-paths.ts", "webpack://next/./dist/src/shared/lib/utils.ts", "webpack://next/./dist/src/shared/lib/html-context.shared-runtime.ts", "webpack://next/./dist/src/server/request-meta.ts", "webpack://next/./dist/src/client/components/redirect-status-code.ts", "webpack://next/./dist/src/lib/redirect-status.ts", "webpack://next/./dist/src/server/stream-utils/node-web-streams-helper.ts", "webpack://next/./dist/src/server/stream-utils/encodedTags.ts", "webpack://next/./dist/src/shared/lib/router/utils/remove-trailing-slash.ts", "webpack://next/./dist/src/shared/lib/router/utils/parse-path.ts", "webpack://next/./dist/src/shared/lib/router/utils/add-path-prefix.ts", "webpack://next/./dist/src/shared/lib/router/utils/add-path-suffix.ts", "webpack://next/./dist/src/shared/lib/router/utils/path-has-prefix.ts", "webpack://next/./dist/src/shared/lib/i18n/normalize-locale-path.ts", "webpack://next/./dist/src/server/web/next-url.ts", "webpack://next/./dist/src/shared/lib/router/utils/get-next-pathname-info.ts", "webpack://next/./dist/src/shared/lib/router/utils/remove-path-prefix.ts", "webpack://next/./dist/src/shared/lib/get-hostname.ts", "webpack://next/./dist/src/shared/lib/i18n/detect-domain-locale.ts", "webpack://next/./dist/src/shared/lib/router/utils/format-next-pathname-info.ts", "webpack://next/./dist/src/shared/lib/router/utils/add-locale.ts", "webpack://next/./dist/src/server/web/spec-extension/request.ts", "webpack://next/./dist/src/server/web/spec-extension/adapters/next-request.ts", "webpack://next/./dist/src/lib/detached-promise.ts", "webpack://next/./dist/src/server/client-component-renderer-logger.ts", "webpack://next/./dist/src/server/pipe-readable.ts", "webpack://next/./dist/src/server/render-result.ts", "webpack://next/./dist/src/shared/lib/image-config-context.shared-runtime.ts", "webpack://next/./dist/src/shared/lib/image-config.ts", "webpack://next/./dist/src/server/internal-utils.ts", "webpack://next/./dist/src/client/components/app-router-headers.ts", "webpack://next/./dist/src/shared/lib/hooks-client-context.shared-runtime.ts", "webpack://next/./dist/src/shared/lib/escape-regexp.ts", "webpack://next/./dist/src/shared/lib/router/utils/route-regex.ts", "webpack://next/./dist/src/shared/lib/router/adapters.tsx", "webpack://next/./dist/src/shared/lib/app-router-context.shared-runtime.ts", "webpack://next/./dist/src/shared/lib/error-source.ts", "webpack://next/./dist/src/server/api-utils/get-cookie-parser.ts", "webpack://next/./dist/src/server/lib/cache-control.ts", "webpack://next/./dist/src/shared/lib/amp-mode.ts", "webpack://next/./dist/src/shared/lib/head.tsx", "webpack://next/./dist/src/shared/lib/router/utils/as-path-to-search-params.ts", "webpack://next/./dist/src/lib/is-error.ts", "webpack://next/./dist/src/shared/lib/page-path/denormalize-page-path.ts", "webpack://next/./dist/src/shared/lib/page-path/normalize-path-sep.ts", "webpack://next/./dist/src/shared/lib/page-path/normalize-page-path.ts", "webpack://next/./dist/src/shared/lib/server-inserted-html.shared-runtime.tsx", "webpack://next/./dist/src/server/route-modules/pages/module.ts"], "sourcesContent": ["\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  RequestCookies: () => RequestCookies,\n  ResponseCookies: () => ResponseCookies,\n  parseCookie: () => parseCookie,\n  parseSetCookie: () => parseSetCookie,\n  stringifyCookie: () => stringifyCookie\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/serialize.ts\nfunction stringifyCookie(c) {\n  var _a;\n  const attrs = [\n    \"path\" in c && c.path && `Path=${c.path}`,\n    \"expires\" in c && (c.expires || c.expires === 0) && `Expires=${(typeof c.expires === \"number\" ? new Date(c.expires) : c.expires).toUTCString()}`,\n    \"maxAge\" in c && typeof c.maxAge === \"number\" && `Max-Age=${c.maxAge}`,\n    \"domain\" in c && c.domain && `Domain=${c.domain}`,\n    \"secure\" in c && c.secure && \"Secure\",\n    \"httpOnly\" in c && c.httpOnly && \"HttpOnly\",\n    \"sameSite\" in c && c.sameSite && `SameSite=${c.sameSite}`,\n    \"partitioned\" in c && c.partitioned && \"Partitioned\",\n    \"priority\" in c && c.priority && `Priority=${c.priority}`\n  ].filter(Boolean);\n  const stringified = `${c.name}=${encodeURIComponent((_a = c.value) != null ? _a : \"\")}`;\n  return attrs.length === 0 ? stringified : `${stringified}; ${attrs.join(\"; \")}`;\n}\nfunction parseCookie(cookie) {\n  const map = /* @__PURE__ */ new Map();\n  for (const pair of cookie.split(/; */)) {\n    if (!pair)\n      continue;\n    const splitAt = pair.indexOf(\"=\");\n    if (splitAt === -1) {\n      map.set(pair, \"true\");\n      continue;\n    }\n    const [key, value] = [pair.slice(0, splitAt), pair.slice(splitAt + 1)];\n    try {\n      map.set(key, decodeURIComponent(value != null ? value : \"true\"));\n    } catch {\n    }\n  }\n  return map;\n}\nfunction parseSetCookie(setCookie) {\n  if (!setCookie) {\n    return void 0;\n  }\n  const [[name, value], ...attributes] = parseCookie(setCookie);\n  const {\n    domain,\n    expires,\n    httponly,\n    maxage,\n    path,\n    samesite,\n    secure,\n    partitioned,\n    priority\n  } = Object.fromEntries(\n    attributes.map(([key, value2]) => [\n      key.toLowerCase().replace(/-/g, \"\"),\n      value2\n    ])\n  );\n  const cookie = {\n    name,\n    value: decodeURIComponent(value),\n    domain,\n    ...expires && { expires: new Date(expires) },\n    ...httponly && { httpOnly: true },\n    ...typeof maxage === \"string\" && { maxAge: Number(maxage) },\n    path,\n    ...samesite && { sameSite: parseSameSite(samesite) },\n    ...secure && { secure: true },\n    ...priority && { priority: parsePriority(priority) },\n    ...partitioned && { partitioned: true }\n  };\n  return compact(cookie);\n}\nfunction compact(t) {\n  const newT = {};\n  for (const key in t) {\n    if (t[key]) {\n      newT[key] = t[key];\n    }\n  }\n  return newT;\n}\nvar SAME_SITE = [\"strict\", \"lax\", \"none\"];\nfunction parseSameSite(string) {\n  string = string.toLowerCase();\n  return SAME_SITE.includes(string) ? string : void 0;\n}\nvar PRIORITY = [\"low\", \"medium\", \"high\"];\nfunction parsePriority(string) {\n  string = string.toLowerCase();\n  return PRIORITY.includes(string) ? string : void 0;\n}\nfunction splitCookiesString(cookiesString) {\n  if (!cookiesString)\n    return [];\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        lastComma = pos;\n        pos += 1;\n        skipWhitespace();\n        nextStart = pos;\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          cookiesSeparatorFound = true;\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n  return cookiesStrings;\n}\n\n// src/request-cookies.ts\nvar RequestCookies = class {\n  constructor(requestHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    this._headers = requestHeaders;\n    const header = requestHeaders.get(\"cookie\");\n    if (header) {\n      const parsed = parseCookie(header);\n      for (const [name, value] of parsed) {\n        this._parsed.set(name, { name, value });\n      }\n    }\n  }\n  [Symbol.iterator]() {\n    return this._parsed[Symbol.iterator]();\n  }\n  /**\n   * The amount of cookies received from the client\n   */\n  get size() {\n    return this._parsed.size;\n  }\n  get(...args) {\n    const name = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(name);\n  }\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed);\n    if (!args.length) {\n      return all.map(([_, value]) => value);\n    }\n    const name = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter(([n]) => n === name).map(([_, value]) => value);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  set(...args) {\n    const [name, value] = args.length === 1 ? [args[0].name, args[0].value] : args;\n    const map = this._parsed;\n    map.set(name, { name, value });\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value2]) => stringifyCookie(value2)).join(\"; \")\n    );\n    return this;\n  }\n  /**\n   * Delete the cookies matching the passed name or names in the request.\n   */\n  delete(names) {\n    const map = this._parsed;\n    const result = !Array.isArray(names) ? map.delete(names) : names.map((name) => map.delete(name));\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value]) => stringifyCookie(value)).join(\"; \")\n    );\n    return result;\n  }\n  /**\n   * Delete all the cookies in the cookies in the request.\n   */\n  clear() {\n    this.delete(Array.from(this._parsed.keys()));\n    return this;\n  }\n  /**\n   * Format the cookies in the request as a string for logging\n   */\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map((v) => `${v.name}=${encodeURIComponent(v.value)}`).join(\"; \");\n  }\n};\n\n// src/response-cookies.ts\nvar ResponseCookies = class {\n  constructor(responseHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    var _a, _b, _c;\n    this._headers = responseHeaders;\n    const setCookie = (_c = (_b = (_a = responseHeaders.getSetCookie) == null ? void 0 : _a.call(responseHeaders)) != null ? _b : responseHeaders.get(\"set-cookie\")) != null ? _c : [];\n    const cookieStrings = Array.isArray(setCookie) ? setCookie : splitCookiesString(setCookie);\n    for (const cookieString of cookieStrings) {\n      const parsed = parseSetCookie(cookieString);\n      if (parsed)\n        this._parsed.set(parsed.name, parsed);\n    }\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-get CookieStore#get} without the Promise.\n   */\n  get(...args) {\n    const key = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(key);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-getAll CookieStore#getAll} without the Promise.\n   */\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed.values());\n    if (!args.length) {\n      return all;\n    }\n    const key = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter((c) => c.name === key);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-set CookieStore#set} without the Promise.\n   */\n  set(...args) {\n    const [name, value, cookie] = args.length === 1 ? [args[0].name, args[0].value, args[0]] : args;\n    const map = this._parsed;\n    map.set(name, normalizeCookie({ name, value, ...cookie }));\n    replace(map, this._headers);\n    return this;\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-delete CookieStore#delete} without the Promise.\n   */\n  delete(...args) {\n    const [name, options] = typeof args[0] === \"string\" ? [args[0]] : [args[0].name, args[0]];\n    return this.set({ ...options, name, value: \"\", expires: /* @__PURE__ */ new Date(0) });\n  }\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map(stringifyCookie).join(\"; \");\n  }\n};\nfunction replace(bag, headers) {\n  headers.delete(\"set-cookie\");\n  for (const [, value] of bag) {\n    const serialized = stringifyCookie(value);\n    headers.append(\"set-cookie\", serialized);\n  }\n}\nfunction normalizeCookie(cookie = { name: \"\", value: \"\" }) {\n  if (typeof cookie.expires === \"number\") {\n    cookie.expires = new Date(cookie.expires);\n  }\n  if (cookie.maxAge) {\n    cookie.expires = new Date(Date.now() + cookie.maxAge * 1e3);\n  }\n  if (cookie.path === null || cookie.path === void 0) {\n    cookie.path = \"/\";\n  }\n  return cookie;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  RequestCookies,\n  ResponseCookies,\n  parseCookie,\n  parseSetCookie,\n  stringifyCookie\n});\n", "(()=>{\"use strict\";if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var e={};(()=>{var r=e;\n/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */r.parse=parse;r.serialize=serialize;var i=decodeURIComponent;var t=encodeURIComponent;var a=/; */;var n=/^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;function parse(e,r){if(typeof e!==\"string\"){throw new TypeError(\"argument str must be a string\")}var t={};var n=r||{};var o=e.split(a);var s=n.decode||i;for(var p=0;p<o.length;p++){var f=o[p];var u=f.indexOf(\"=\");if(u<0){continue}var v=f.substr(0,u).trim();var c=f.substr(++u,f.length).trim();if('\"'==c[0]){c=c.slice(1,-1)}if(undefined==t[v]){t[v]=tryDecode(c,s)}}return t}function serialize(e,r,i){var a=i||{};var o=a.encode||t;if(typeof o!==\"function\"){throw new TypeError(\"option encode is invalid\")}if(!n.test(e)){throw new TypeError(\"argument name is invalid\")}var s=o(r);if(s&&!n.test(s)){throw new TypeError(\"argument val is invalid\")}var p=e+\"=\"+s;if(null!=a.maxAge){var f=a.maxAge-0;if(isNaN(f)||!isFinite(f)){throw new TypeError(\"option maxAge is invalid\")}p+=\"; Max-Age=\"+Math.floor(f)}if(a.domain){if(!n.test(a.domain)){throw new TypeError(\"option domain is invalid\")}p+=\"; Domain=\"+a.domain}if(a.path){if(!n.test(a.path)){throw new TypeError(\"option path is invalid\")}p+=\"; Path=\"+a.path}if(a.expires){if(typeof a.expires.toUTCString!==\"function\"){throw new TypeError(\"option expires is invalid\")}p+=\"; Expires=\"+a.expires.toUTCString()}if(a.httpOnly){p+=\"; HttpOnly\"}if(a.secure){p+=\"; Secure\"}if(a.sameSite){var u=typeof a.sameSite===\"string\"?a.sameSite.toLowerCase():a.sameSite;switch(u){case true:p+=\"; SameSite=Strict\";break;case\"lax\":p+=\"; SameSite=Lax\";break;case\"strict\":p+=\"; SameSite=Strict\";break;case\"none\":p+=\"; SameSite=None\";break;default:throw new TypeError(\"option sameSite is invalid\")}}return p}function tryDecode(e,r){try{return r(e)}catch(r){return e}}})();module.exports=e})();", "/**\n * @license React\n * react-is.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n  REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n  REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\nSymbol.for(\"react.provider\");\nvar REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n  REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n  REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n  REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n  REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n  REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n  REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n  REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n  REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"),\n  REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\nfunction typeOf(object) {\n  if (\"object\" === typeof object && null !== object) {\n    var $$typeof = object.$$typeof;\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        switch (((object = object.type), object)) {\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n          case REACT_SUSPENSE_LIST_TYPE:\n          case REACT_VIEW_TRANSITION_TYPE:\n            return object;\n          default:\n            switch (((object = object && object.$$typeof), object)) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n                return object;\n              case REACT_CONSUMER_TYPE:\n                return object;\n              default:\n                return $$typeof;\n            }\n        }\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n}\nexports.ContextConsumer = REACT_CONSUMER_TYPE;\nexports.ContextProvider = REACT_CONTEXT_TYPE;\nexports.Element = REACT_ELEMENT_TYPE;\nexports.ForwardRef = REACT_FORWARD_REF_TYPE;\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.Lazy = REACT_LAZY_TYPE;\nexports.Memo = REACT_MEMO_TYPE;\nexports.Portal = REACT_PORTAL_TYPE;\nexports.Profiler = REACT_PROFILER_TYPE;\nexports.StrictMode = REACT_STRICT_MODE_TYPE;\nexports.Suspense = REACT_SUSPENSE_TYPE;\nexports.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\nexports.isContextConsumer = function (object) {\n  return typeOf(object) === REACT_CONSUMER_TYPE;\n};\nexports.isContextProvider = function (object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n};\nexports.isElement = function (object) {\n  return (\n    \"object\" === typeof object &&\n    null !== object &&\n    object.$$typeof === REACT_ELEMENT_TYPE\n  );\n};\nexports.isForwardRef = function (object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n};\nexports.isFragment = function (object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n};\nexports.isLazy = function (object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n};\nexports.isMemo = function (object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n};\nexports.isPortal = function (object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n};\nexports.isProfiler = function (object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n};\nexports.isStrictMode = function (object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n};\nexports.isSuspense = function (object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n};\nexports.isSuspenseList = function (object) {\n  return typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n};\nexports.isValidElementType = function (type) {\n  return \"string\" === typeof type ||\n    \"function\" === typeof type ||\n    type === REACT_FRAGMENT_TYPE ||\n    type === REACT_PROFILER_TYPE ||\n    type === REACT_STRICT_MODE_TYPE ||\n    type === REACT_SUSPENSE_TYPE ||\n    type === REACT_SUSPENSE_LIST_TYPE ||\n    type === REACT_OFFSCREEN_TYPE ||\n    (\"object\" === typeof type &&\n      null !== type &&\n      (type.$$typeof === REACT_LAZY_TYPE ||\n        type.$$typeof === REACT_MEMO_TYPE ||\n        type.$$typeof === REACT_CONTEXT_TYPE ||\n        type.$$typeof === REACT_CONSUMER_TYPE ||\n        type.$$typeof === REACT_FORWARD_REF_TYPE ||\n        type.$$typeof === REACT_CLIENT_REFERENCE ||\n        void 0 !== type.getModuleId))\n    ? !0\n    : !1;\n};\nexports.typeOf = typeOf;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "(()=>{\"use strict\";var e={511:e=>{e.exports=({onlyFirst:e=false}={})=>{const r=[\"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\",\"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-ntqry=><~]))\"].join(\"|\");return new RegExp(r,e?undefined:\"g\")}},532:(e,r,_)=>{const t=_(511);e.exports=e=>typeof e===\"string\"?e.replace(t(),\"\"):e}};var r={};function __nccwpck_require__(_){var t=r[_];if(t!==undefined){return t.exports}var a=r[_]={exports:{}};var n=true;try{e[_](a,a.exports,__nccwpck_require__);n=false}finally{if(n)delete r[_]}return a.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var _=__nccwpck_require__(532);module.exports=_})();", "// ISC License\n\n// Copyright (c) 2021 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>\n\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n//\n// https://github.com/ale<PERSON><PERSON><PERSON>ov/picocolors/blob/b6261487e7b81aaab2440e397a356732cad9e342/picocolors.js#L1\n\nconst { env, stdout } = globalThis?.process ?? {}\n\nconst enabled =\n  env &&\n  !env.NO_COLOR &&\n  (env.FORCE_COLOR || (stdout?.isTTY && !env.CI && env.TERM !== 'dumb'))\n\nconst replaceClose = (\n  str: string,\n  close: string,\n  replace: string,\n  index: number\n): string => {\n  const start = str.substring(0, index) + replace\n  const end = str.substring(index + close.length)\n  const nextIndex = end.indexOf(close)\n  return ~nextIndex\n    ? start + replaceClose(end, close, replace, nextIndex)\n    : start + end\n}\n\nconst formatter = (open: string, close: string, replace = open) => {\n  if (!enabled) return String\n  return (input: string) => {\n    const string = '' + input\n    const index = string.indexOf(close, open.length)\n    return ~index\n      ? open + replaceClose(string, close, replace, index) + close\n      : open + string + close\n  }\n}\n\nexport const reset = enabled ? (s: string) => `\\x1b[0m${s}\\x1b[0m` : String\nexport const bold = formatter('\\x1b[1m', '\\x1b[22m', '\\x1b[22m\\x1b[1m')\nexport const dim = formatter('\\x1b[2m', '\\x1b[22m', '\\x1b[22m\\x1b[2m')\nexport const italic = formatter('\\x1b[3m', '\\x1b[23m')\nexport const underline = formatter('\\x1b[4m', '\\x1b[24m')\nexport const inverse = formatter('\\x1b[7m', '\\x1b[27m')\nexport const hidden = formatter('\\x1b[8m', '\\x1b[28m')\nexport const strikethrough = formatter('\\x1b[9m', '\\x1b[29m')\nexport const black = formatter('\\x1b[30m', '\\x1b[39m')\nexport const red = formatter('\\x1b[31m', '\\x1b[39m')\nexport const green = formatter('\\x1b[32m', '\\x1b[39m')\nexport const yellow = formatter('\\x1b[33m', '\\x1b[39m')\nexport const blue = formatter('\\x1b[34m', '\\x1b[39m')\nexport const magenta = formatter('\\x1b[35m', '\\x1b[39m')\nexport const purple = formatter('\\x1b[38;2;173;127;168m', '\\x1b[39m')\nexport const cyan = formatter('\\x1b[36m', '\\x1b[39m')\nexport const white = formatter('\\x1b[37m', '\\x1b[39m')\nexport const gray = formatter('\\x1b[90m', '\\x1b[39m')\nexport const bgBlack = formatter('\\x1b[40m', '\\x1b[49m')\nexport const bgRed = formatter('\\x1b[41m', '\\x1b[49m')\nexport const bgGreen = formatter('\\x1b[42m', '\\x1b[49m')\nexport const bgYellow = formatter('\\x1b[43m', '\\x1b[49m')\nexport const bgBlue = formatter('\\x1b[44m', '\\x1b[49m')\nexport const bgMagenta = formatter('\\x1b[45m', '\\x1b[49m')\nexport const bgCyan = formatter('\\x1b[46m', '\\x1b[49m')\nexport const bgWhite = formatter('\\x1b[47m', '\\x1b[49m')\n", "import { bold, green, magenta, red, yellow, white } from '../../lib/picocolors'\nimport { LRUCache } from '../../server/lib/lru-cache'\n\nexport const prefixes = {\n  wait: white(bold('○')),\n  error: red(bold('⨯')),\n  warn: yellow(bold('⚠')),\n  ready: '▲', // no color\n  info: white(bold(' ')),\n  event: green(bold('✓')),\n  trace: magenta(bold('»')),\n} as const\n\nconst LOGGING_METHOD = {\n  log: 'log',\n  warn: 'warn',\n  error: 'error',\n} as const\n\nfunction prefixedLog(prefixType: keyof typeof prefixes, ...message: any[]) {\n  if ((message[0] === '' || message[0] === undefined) && message.length === 1) {\n    message.shift()\n  }\n\n  const consoleMethod: keyof typeof LOGGING_METHOD =\n    prefixType in LOGGING_METHOD\n      ? LOGGING_METHOD[prefixType as keyof typeof LOGGING_METHOD]\n      : 'log'\n\n  const prefix = prefixes[prefixType]\n  // If there's no message, don't print the prefix but a new line\n  if (message.length === 0) {\n    console[consoleMethod]('')\n  } else {\n    // Ensure if there's ANSI escape codes it's concatenated into one string.\n    // Chrome DevTool can only handle color if it's in one string.\n    if (message.length === 1 && typeof message[0] === 'string') {\n      console[consoleMethod](' ' + prefix + ' ' + message[0])\n    } else {\n      console[consoleMethod](' ' + prefix, ...message)\n    }\n  }\n}\n\nexport function bootstrap(...message: string[]) {\n  // logging format: ' <prefix> <message>'\n  // e.g. ' ✓ Compiled successfully'\n  // Add spaces to align with the indent of other logs\n  console.log('   ' + message.join(' '))\n}\n\nexport function wait(...message: any[]) {\n  prefixedLog('wait', ...message)\n}\n\nexport function error(...message: any[]) {\n  prefixedLog('error', ...message)\n}\n\nexport function warn(...message: any[]) {\n  prefixedLog('warn', ...message)\n}\n\nexport function ready(...message: any[]) {\n  prefixedLog('ready', ...message)\n}\n\nexport function info(...message: any[]) {\n  prefixedLog('info', ...message)\n}\n\nexport function event(...message: any[]) {\n  prefixedLog('event', ...message)\n}\n\nexport function trace(...message: any[]) {\n  prefixedLog('trace', ...message)\n}\n\nconst warnOnceCache = new LRUCache<string>(10_000, (value) => value.length)\nexport function warnOnce(...message: any[]) {\n  const key = message.join(' ')\n  if (!warnOnceCache.has(key)) {\n    warnOnceCache.set(key, key)\n    warn(...message)\n  }\n}\n", "export class LRUCache<T> {\n  private cache: Map<string, T>\n  private sizes: Map<string, number>\n  private totalSize: number\n  private maxSize: number\n  private calculateSize: (value: T) => number\n\n  constructor(maxSize: number, calculateSize?: (value: T) => number) {\n    this.cache = new Map()\n    this.sizes = new Map()\n    this.totalSize = 0\n    this.maxSize = maxSize\n    this.calculateSize = calculateSize || (() => 1)\n  }\n\n  set(key?: string | null, value?: T): void {\n    if (!key || !value) return\n\n    const size = this.calculateSize(value)\n\n    if (size > this.maxSize) {\n      console.warn('Single item size exceeds maxSize')\n      return\n    }\n\n    if (this.cache.has(key)) {\n      this.totalSize -= this.sizes.get(key) || 0\n    }\n\n    this.cache.set(key, value)\n    this.sizes.set(key, size)\n    this.totalSize += size\n\n    this.touch(key)\n  }\n\n  has(key?: string | null): boolean {\n    if (!key) return false\n\n    this.touch(key)\n    return Boolean(this.cache.get(key))\n  }\n\n  get(key?: string | null): T | undefined {\n    if (!key) return\n\n    const value = this.cache.get(key)\n    if (value === undefined) {\n      return undefined\n    }\n\n    this.touch(key)\n    return value\n  }\n\n  private touch(key: string): void {\n    const value = this.cache.get(key)\n    if (value !== undefined) {\n      this.cache.delete(key)\n      this.cache.set(key, value)\n      this.evictIfNecessary()\n    }\n  }\n\n  private evictIfNecessary(): void {\n    while (this.totalSize > this.maxSize && this.cache.size > 0) {\n      this.evictLeastRecentlyUsed()\n    }\n  }\n\n  private evictLeastRecentlyUsed(): void {\n    const lruKey = this.cache.keys().next().value\n    if (lruKey !== undefined) {\n      const lruSize = this.sizes.get(lruKey) || 0\n      this.totalSize -= lruSize\n      this.cache.delete(lruKey)\n      this.sizes.delete(lruKey)\n    }\n  }\n\n  reset() {\n    this.cache.clear()\n    this.sizes.clear()\n    this.totalSize = 0\n  }\n\n  keys() {\n    return [...this.cache.keys()]\n  }\n\n  remove(key: string): void {\n    if (this.cache.has(key)) {\n      this.totalSize -= this.sizes.get(key) || 0\n      this.cache.delete(key)\n      this.sizes.delete(key)\n    }\n  }\n\n  clear(): void {\n    this.cache.clear()\n    this.sizes.clear()\n    this.totalSize = 0\n  }\n\n  get size(): number {\n    return this.cache.size\n  }\n\n  get currentSize(): number {\n    return this.totalSize\n  }\n}\n", "import type { ServerRuntime } from '../types'\n\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n", "import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { NextApiResponse } from '../../shared/lib/utils'\n\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../lib/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NodeSpan } from '../lib/trace/constants'\n\nexport type NextApiRequestCookies = Partial<{ [key: string]: string }>\nexport type NextApiRequestQuery = Partial<{ [key: string]: string | string[] }>\n\nexport type __ApiPreviewProps = {\n  previewModeId: string\n  previewModeEncryptionKey: string\n  previewModeSigningKey: string\n}\n\nexport function wrapApiHandler<T extends (...args: any[]) => any>(\n  page: string,\n  handler: T\n): T {\n  return ((...args) => {\n    getTracer().setRootSpanAttribute('next.route', page)\n    // Call API route method\n    return getTracer().trace(\n      NodeSpan.runHandler,\n      {\n        spanName: `executing api route (pages) ${page}`,\n      },\n      () => handler(...args)\n    )\n  }) as T\n}\n\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */\nexport function sendStatusCode(\n  res: NextApiResponse,\n  statusCode: number\n): NextApiResponse<any> {\n  res.statusCode = statusCode\n  return res\n}\n\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */\nexport function redirect(\n  res: NextApiResponse,\n  statusOrUrl: string | number,\n  url?: string\n): NextApiResponse<any> {\n  if (typeof statusOrUrl === 'string') {\n    url = statusOrUrl\n    statusOrUrl = 307\n  }\n  if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n    throw new Error(\n      `Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`\n    )\n  }\n  res.writeHead(statusOrUrl, { Location: url })\n  res.write(url)\n  res.end()\n  return res\n}\n\nexport function checkIsOnDemandRevalidate(\n  req: Request | IncomingMessage | BaseNextRequest,\n  previewProps: __ApiPreviewProps\n): {\n  isOnDemandRevalidate: boolean\n  revalidateOnlyGenerated: boolean\n} {\n  const headers = HeadersAdapter.from(req.headers)\n\n  const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER)\n  const isOnDemandRevalidate = previewModeId === previewProps.previewModeId\n\n  const revalidateOnlyGenerated = headers.has(\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER\n  )\n\n  return { isOnDemandRevalidate, revalidateOnlyGenerated }\n}\n\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`\n\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024\n\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA)\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS)\n\nexport function clearPreviewData<T>(\n  res: NextApiResponse<T>,\n  options: {\n    path?: string\n  } = {}\n): NextApiResponse<T> {\n  if (SYMBOL_CLEARED_COOKIES in res) {\n    return res\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n\n  Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n    value: true,\n    enumerable: false,\n  })\n  return res\n}\n\n/**\n * Custom error class\n */\nexport class ApiError extends Error {\n  readonly statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n    this.statusCode = statusCode\n  }\n}\n\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */\nexport function sendError(\n  res: NextApiResponse,\n  statusCode: number,\n  message: string\n): void {\n  res.statusCode = statusCode\n  res.statusMessage = message\n  res.end(message)\n}\n\ninterface LazyProps {\n  req: IncomingMessage\n}\n\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */\nexport function setLazyProp<T>(\n  { req }: LazyProps,\n  prop: string,\n  getter: () => T\n): void {\n  const opts = { configurable: true, enumerable: true }\n  const optsReset = { ...opts, writable: true }\n\n  Object.defineProperty(req, prop, {\n    ...opts,\n    get: () => {\n      const value = getter()\n      // we set the property on the object to avoid recalculating it\n      Object.defineProperty(req, prop, { ...optsReset, value })\n      return value\n    },\n    set: (value) => {\n      Object.defineProperty(req, prop, { ...optsReset, value })\n    },\n  })\n}\n", "import type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextApiResponse } from '../../../shared/lib/utils'\nimport { checkIsOnDemandRevalidate } from '../.'\nimport type { __ApiPreviewProps } from '../.'\nimport type { BaseNextRequest, BaseNextResponse } from '../../base-http'\nimport type { PreviewData } from '../../../types'\n\nimport {\n  clearPreviewData,\n  COOKIE_NAME_PRERENDER_BYPASS,\n  COOKIE_NAME_PRERENDER_DATA,\n  SYMBOL_PREVIEW_DATA,\n} from '../index'\nimport { RequestCookies } from '../../web/spec-extension/cookies'\nimport { HeadersAdapter } from '../../web/spec-extension/adapters/headers'\n\nexport function tryGetPreviewData(\n  req: IncomingMessage | BaseNextRequest | Request,\n  res: ServerResponse | BaseNextResponse,\n  options: __ApiPreviewProps,\n  multiZoneDraftMode: boolean\n): PreviewData {\n  // if an On-Demand revalidation is being done preview mode\n  // is disabled\n  if (options && checkIsOnDemandRevalidate(req, options).isOnDemandRevalidate) {\n    return false\n  }\n\n  // Read cached preview data if present\n  // TODO: use request metadata instead of a symbol\n  if (SYMBOL_PREVIEW_DATA in req) {\n    return (req as any)[SYMBOL_PREVIEW_DATA] as any\n  }\n\n  const headers = HeadersAdapter.from(req.headers)\n  const cookies = new RequestCookies(headers)\n\n  const previewModeId = cookies.get(COOKIE_NAME_PRERENDER_BYPASS)?.value\n  const tokenPreviewData = cookies.get(COOKIE_NAME_PRERENDER_DATA)?.value\n\n  // Case: preview mode cookie set but data cookie is not set\n  if (\n    previewModeId &&\n    !tokenPreviewData &&\n    previewModeId === options.previewModeId\n  ) {\n    // This is \"Draft Mode\" which doesn't use\n    // previewData, so we return an empty object\n    // for backwards compat with \"Preview Mode\".\n    const data = {}\n    Object.defineProperty(req, SYMBOL_PREVIEW_DATA, {\n      value: data,\n      enumerable: false,\n    })\n    return data\n  }\n\n  // Case: neither cookie is set.\n  if (!previewModeId && !tokenPreviewData) {\n    return false\n  }\n\n  // Case: one cookie is set, but not the other.\n  if (!previewModeId || !tokenPreviewData) {\n    if (!multiZoneDraftMode) {\n      clearPreviewData(res as NextApiResponse)\n    }\n    return false\n  }\n\n  // Case: preview session is for an old build.\n  if (previewModeId !== options.previewModeId) {\n    if (!multiZoneDraftMode) {\n      clearPreviewData(res as NextApiResponse)\n    }\n    return false\n  }\n\n  let encryptedPreviewData: {\n    data: string\n  }\n  try {\n    const jsonwebtoken =\n      require('next/dist/compiled/jsonwebtoken') as typeof import('next/dist/compiled/jsonwebtoken')\n    encryptedPreviewData = jsonwebtoken.verify(\n      tokenPreviewData,\n      options.previewModeSigningKey\n    ) as typeof encryptedPreviewData\n  } catch {\n    // TODO: warn\n    clearPreviewData(res as NextApiResponse)\n    return false\n  }\n\n  const { decryptWithSecret } =\n    require('../../crypto-utils') as typeof import('../../crypto-utils')\n  const decryptedPreviewData = decryptWithSecret(\n    Buffer.from(options.previewModeEncryptionKey),\n    encryptedPreviewData.data\n  )\n\n  try {\n    // TODO: strict runtime type checking\n    const data = JSON.parse(decryptedPreviewData)\n    // Cache lookup\n    Object.defineProperty(req, SYMBOL_PREVIEW_DATA, {\n      value: data,\n      enumerable: false,\n    })\n    return data\n  } catch {\n    return false\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"crypto\");", "import crypto from 'crypto'\n\n// Background:\n// https://security.stackexchange.com/questions/184305/why-would-i-ever-use-aes-256-cbc-if-aes-256-gcm-is-more-secure\n\nconst CIPHER_ALGORITHM = `aes-256-gcm`,\n  CIPHER_KEY_LENGTH = 32, // https://stackoverflow.com/a/28307668/4397028\n  CIPHER_IV_LENGTH = 16, // https://stackoverflow.com/a/28307668/4397028\n  CIPHER_TAG_LENGTH = 16,\n  CIPHER_SALT_LENGTH = 64\n\nconst PBKDF2_ITERATIONS = 100_000 // https://support.1password.com/pbkdf2/\n\nexport function encryptWithSecret(secret: Buffer, data: string): string {\n  const iv = crypto.randomBytes(CIPHER_IV_LENGTH)\n  const salt = crypto.randomBytes(CIPHER_SALT_LENGTH)\n\n  // https://nodejs.org/api/crypto.html#crypto_crypto_pbkdf2sync_password_salt_iterations_keylen_digest\n  const key = crypto.pbkdf2Sync(\n    secret,\n    salt,\n    PBKDF2_ITERATIONS,\n    CIPHER_KEY_LENGTH,\n    `sha512`\n  )\n\n  const cipher = crypto.createCipheriv(CIPHER_ALGORITHM, key, iv)\n  const encrypted = Buffer.concat([cipher.update(data, `utf8`), cipher.final()])\n\n  // https://nodejs.org/api/crypto.html#crypto_cipher_getauthtag\n  const tag = cipher.getAuthTag()\n\n  return Buffer.concat([\n    // Data as required by:\n    // Salt for Key: https://nodejs.org/api/crypto.html#crypto_crypto_pbkdf2sync_password_salt_iterations_keylen_digest\n    // IV: https://nodejs.org/api/crypto.html#crypto_class_decipher\n    // Tag: https://nodejs.org/api/crypto.html#crypto_decipher_setauthtag_buffer\n    salt,\n    iv,\n    tag,\n    encrypted,\n  ]).toString(`hex`)\n}\n\nexport function decryptWithSecret(\n  secret: Buffer,\n  encryptedData: string\n): string {\n  const buffer = Buffer.from(encryptedData, `hex`)\n\n  const salt = buffer.slice(0, CIPHER_SALT_LENGTH)\n  const iv = buffer.slice(\n    CIPHER_SALT_LENGTH,\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH\n  )\n  const tag = buffer.slice(\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH,\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH + CIPHER_TAG_LENGTH\n  )\n  const encrypted = buffer.slice(\n    CIPHER_SALT_LENGTH + CIPHER_IV_LENGTH + CIPHER_TAG_LENGTH\n  )\n\n  // https://nodejs.org/api/crypto.html#crypto_crypto_pbkdf2sync_password_salt_iterations_keylen_digest\n  const key = crypto.pbkdf2Sync(\n    secret,\n    salt,\n    PBKDF2_ITERATIONS,\n    CIPHER_KEY_LENGTH,\n    `sha512`\n  )\n\n  const decipher = crypto.createDecipheriv(CIPHER_ALGORITHM, key, iv)\n  decipher.setAuthTag(tag)\n\n  return decipher.update(encrypted) + decipher.final(`utf8`)\n}\n", "/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\n\nenum BaseServerSpan {\n  handleRequest = 'BaseServer.handleRequest',\n  run = 'BaseServer.run',\n  pipe = 'BaseServer.pipe',\n  getStaticHTML = 'BaseServer.getStaticHTML',\n  render = 'BaseServer.render',\n  renderToResponseWithComponents = 'BaseServer.renderToResponseWithComponents',\n  renderToResponse = 'BaseServer.renderToResponse',\n  renderToHTML = 'BaseServer.renderToHTML',\n  renderError = 'BaseServer.renderError',\n  renderErrorToResponse = 'BaseServer.renderErrorToResponse',\n  renderErrorToHTML = 'BaseServer.renderErrorToHTML',\n  render404 = 'BaseServer.render404',\n}\n\nenum LoadComponentsSpan {\n  loadDefaultErrorComponents = 'LoadComponents.loadDefaultErrorComponents',\n  loadComponents = 'LoadComponents.loadComponents',\n}\n\nenum NextServerSpan {\n  getRequestHandler = 'NextServer.getRequestHandler',\n  getServer = 'NextServer.getServer',\n  getServerRequestHandler = 'NextServer.getServerRequestHandler',\n  createServer = 'createServer.createServer',\n}\n\nenum NextNodeServerSpan {\n  compression = 'NextNodeServer.compression',\n  getBuildId = 'NextNodeServer.getBuildId',\n  createComponentTree = 'NextNodeServer.createComponentTree',\n  clientComponentLoading = 'NextNodeServer.clientComponentLoading',\n  getLayoutOrPageModule = 'NextNodeServer.getLayoutOrPageModule',\n  generateStaticRoutes = 'NextNodeServer.generateStaticRoutes',\n  generateFsStaticRoutes = 'NextNodeServer.generateFsStaticRoutes',\n  generatePublicRoutes = 'NextNodeServer.generatePublicRoutes',\n  generateImageRoutes = 'NextNodeServer.generateImageRoutes.route',\n  sendRenderResult = 'NextNodeServer.sendRenderResult',\n  proxyRequest = 'NextNodeServer.proxyRequest',\n  runApi = 'NextNodeServer.runApi',\n  render = 'NextNodeServer.render',\n  renderHTML = 'NextNodeServer.renderHTML',\n  imageOptimizer = 'NextNodeServer.imageOptimizer',\n  getPagePath = 'NextNodeServer.getPagePath',\n  getRoutesManifest = 'NextNodeServer.getRoutesManifest',\n  findPageComponents = 'NextNodeServer.findPageComponents',\n  getFontManifest = 'NextNodeServer.getFontManifest',\n  getServerComponentManifest = 'NextNodeServer.getServerComponentManifest',\n  getRequestHandler = 'NextNodeServer.getRequestHandler',\n  renderToHTML = 'NextNodeServer.renderToHTML',\n  renderError = 'NextNodeServer.renderError',\n  renderErrorToHTML = 'NextNodeServer.renderErrorToHTML',\n  render404 = 'NextNodeServer.render404',\n  startResponse = 'NextNodeServer.startResponse',\n\n  // nested inner span, does not require parent scope name\n  route = 'route',\n  onProxyReq = 'onProxyReq',\n  apiResolver = 'apiResolver',\n  internalFetch = 'internalFetch',\n}\n\nenum StartServerSpan {\n  startServer = 'startServer.startServer',\n}\n\nenum RenderSpan {\n  getServerSideProps = 'Render.getServerSideProps',\n  getStaticProps = 'Render.getStaticProps',\n  renderToString = 'Render.renderToString',\n  renderDocument = 'Render.renderDocument',\n  createBodyResult = 'Render.createBodyResult',\n}\n\nenum AppRenderSpan {\n  renderToString = 'AppRender.renderToString',\n  renderToReadableStream = 'AppRender.renderToReadableStream',\n  getBodyResult = 'AppRender.getBodyResult',\n  fetch = 'AppRender.fetch',\n}\n\nenum RouterSpan {\n  executeRoute = 'Router.executeRoute',\n}\n\nenum NodeSpan {\n  runHandler = 'Node.runHandler',\n}\n\nenum AppRouteRouteHandlersSpan {\n  runHandler = 'AppRouteRouteHandlers.runHandler',\n}\n\nenum ResolveMetadataSpan {\n  generateMetadata = 'ResolveMetadata.generateMetadata',\n  generateViewport = 'ResolveMetadata.generateViewport',\n}\n\nenum MiddlewareSpan {\n  execute = 'Middleware.execute',\n}\n\ntype SpanTypes =\n  | `${BaseServerSpan}`\n  | `${LoadComponentsSpan}`\n  | `${NextServerSpan}`\n  | `${StartServerSpan}`\n  | `${NextNodeServerSpan}`\n  | `${RenderSpan}`\n  | `${RouterSpan}`\n  | `${AppRenderSpan}`\n  | `${NodeSpan}`\n  | `${AppRouteRouteHandlersSpan}`\n  | `${ResolveMetadataSpan}`\n  | `${MiddlewareSpan}`\n\n// This list is used to filter out spans that are not relevant to the user\nexport const NextVanillaSpanAllowlist = [\n  MiddlewareSpan.execute,\n  BaseServerSpan.handleRequest,\n  RenderSpan.getServerSideProps,\n  RenderSpan.getStaticProps,\n  AppRenderSpan.fetch,\n  AppRenderSpan.getBodyResult,\n  RenderSpan.renderDocument,\n  NodeSpan.runHandler,\n  AppRouteRouteHandlersSpan.runHandler,\n  ResolveMetadataSpan.generateMetadata,\n  ResolveMetadataSpan.generateViewport,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.getLayoutOrPageModule,\n  NextNodeServerSpan.startResponse,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\n// These Spans are allowed to be always logged\n// when the otel log prefix env is set\nexport const LogSpanAllowList = [\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\nexport {\n  BaseServerSpan,\n  LoadComponentsSpan,\n  NextServerSpan,\n  NextNodeServerSpan,\n  StartServerSpan,\n  RenderSpan,\n  RouterSpan,\n  AppRenderSpan,\n  NodeSpan,\n  AppRouteRouteHandlersSpan,\n  ResolveMetadataSpan,\n  MiddlewareSpan,\n}\n\nexport type { SpanTypes }\n", "export default async function optimize(\n  html: string,\n  config: any\n): Promise<string> {\n  let AmpOptimizer\n  try {\n    AmpOptimizer = require('next/dist/compiled/@ampproject/toolbox-optimizer')\n  } catch (_) {\n    return html\n  }\n  const optimizer = AmpOptimizer.create(config)\n  return optimizer.transformHtml(html, config)\n}\n", "export function nonNullable<T>(value: T): value is NonNullable<T> {\n  return value !== null && value !== undefined\n}\n", "import type { RenderOpts } from './render'\n\nimport { nonNullable } from '../lib/non-nullable'\n\ntype PostProcessorFunction =\n  | ((html: string) => Promise<string>)\n  | ((html: string) => string)\n\nasync function postProcessHTML(\n  pathname: string,\n  content: string,\n  renderOpts: Pick<\n    RenderOpts,\n    | 'ampOptimizerConfig'\n    | 'ampValidator'\n    | 'ampSkipValidation'\n    | 'optimizeCss'\n    | 'distDir'\n    | 'assetPrefix'\n  >,\n  { inAmpMode, hybridAmp }: { inAmpMode: boolean; hybridAmp: boolean }\n) {\n  const postProcessors: Array<PostProcessorFunction> = [\n    process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n      ? async (html: string) => {\n          const optimizeAmp = require('./optimize-amp')\n            .default as typeof import('./optimize-amp').default\n          html = await optimizeAmp!(html, renderOpts.ampOptimizerConfig)\n          if (!renderOpts.ampSkipValidation && renderOpts.ampValidator) {\n            await renderOpts.ampValidator(html, pathname)\n          }\n          return html\n        }\n      : null,\n    process.env.NEXT_RUNTIME !== 'edge' && renderOpts.optimizeCss\n      ? async (html: string) => {\n          // eslint-disable-next-line import/no-extraneous-dependencies\n          const Critters = require('critters')\n          const cssOptimizer = new Critters({\n            ssrMode: true,\n            reduceInlineStyles: false,\n            path: renderOpts.distDir,\n            publicPath: `${renderOpts.assetPrefix}/_next/`,\n            preload: 'media',\n            fonts: false,\n            logLevel:\n              process.env.CRITTERS_LOG_LEVEL ||\n              (process.env.NODE_ENV === 'production' ? 'warn' : 'info'),\n            ...renderOpts.optimizeCss,\n          })\n          return await cssOptimizer.process(html)\n        }\n      : null,\n    inAmpMode || hybridAmp\n      ? (html: string) => {\n          return html.replace(/&amp;amp=1/g, '&amp=1')\n        }\n      : null,\n  ].filter(nonNullable)\n\n  for (const postProcessor of postProcessors) {\n    if (postProcessor) {\n      content = await postProcessor(content)\n    }\n  }\n  return content\n}\n\nexport { postProcessHTML }\n", "import type { IncomingHttpHeaders } from 'http'\n\nimport { ReflectAdapter } from './reflect'\n\n/**\n * @internal\n */\nexport class ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      'Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyHeadersError()\n  }\n}\n\nexport type ReadonlyHeaders = Headers & {\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  append(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  set(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  delete(...args: any[]): void\n}\nexport class HeadersAdapter extends Headers {\n  private readonly headers: IncomingHttpHeaders\n\n  constructor(headers: IncomingHttpHeaders) {\n    // We've already overridden the methods that would be called, so we're just\n    // calling the super constructor to ensure that the instanceof check works.\n    super()\n\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        // Because this is just an object, we expect that all \"get\" operations\n        // are for properties. If it's a \"get\" for a symbol, we'll just return\n        // the symbol.\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return undefined.\n        if (typeof original === 'undefined') return\n\n        // If the original casing exists, return the value.\n        return ReflectAdapter.get(target, original, receiver)\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.set(target, prop, value, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, use the prop as the key.\n        return ReflectAdapter.set(target, original ?? prop, value, receiver)\n      },\n      has(target, prop) {\n        if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return false.\n        if (typeof original === 'undefined') return false\n\n        // If the original casing exists, return true.\n        return ReflectAdapter.has(target, original)\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === 'symbol')\n          return ReflectAdapter.deleteProperty(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return true.\n        if (typeof original === 'undefined') return true\n\n        // If the original casing exists, delete the property.\n        return ReflectAdapter.deleteProperty(target, original)\n      },\n    })\n  }\n\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  public static seal(headers: Headers): ReadonlyHeaders {\n    return new Proxy<ReadonlyHeaders>(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'append':\n          case 'delete':\n          case 'set':\n            return ReadonlyHeadersError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  private merge(value: string | string[]): string {\n    if (Array.isArray(value)) return value.join(', ')\n\n    return value\n  }\n\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  public static from(headers: IncomingHttpHeaders | Headers): Headers {\n    if (headers instanceof Headers) return headers\n\n    return new HeadersAdapter(headers)\n  }\n\n  public append(name: string, value: string): void {\n    const existing = this.headers[name]\n    if (typeof existing === 'string') {\n      this.headers[name] = [existing, value]\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      this.headers[name] = value\n    }\n  }\n\n  public delete(name: string): void {\n    delete this.headers[name]\n  }\n\n  public get(name: string): string | null {\n    const value = this.headers[name]\n    if (typeof value !== 'undefined') return this.merge(value)\n\n    return null\n  }\n\n  public has(name: string): boolean {\n    return typeof this.headers[name] !== 'undefined'\n  }\n\n  public set(name: string, value: string): void {\n    this.headers[name] = value\n  }\n\n  public forEach(\n    callbackfn: (value: string, name: string, parent: Headers) => void,\n    thisArg?: any\n  ): void {\n    for (const [name, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name, this)\n    }\n  }\n\n  public *entries(): HeadersIterator<[string, string]> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(name) as string\n\n      yield [name, value] as [string, string]\n    }\n  }\n\n  public *keys(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      yield name\n    }\n  }\n\n  public *values(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(key) as string\n\n      yield value\n    }\n  }\n\n  public [Symbol.iterator](): HeadersIterator<[string, string]> {\n    return this.entries()\n  }\n}\n", "export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n", "// Note: This file is JS because it's used by the taskfile-swc.js file, which is JS.\n// Keep file changes in sync with the corresponding `.d.ts` files.\n/**\n * These are the browser versions that support all of the following:\n * static import: https://caniuse.com/es6-module\n * dynamic import: https://caniuse.com/es6-module-dynamic-import\n * import.meta: https://caniuse.com/mdn-javascript_operators_import_meta\n */\nconst MODERN_BROWSERSLIST_TARGET = [\n  'chrome 64',\n  'edge 79',\n  'firefox 67',\n  'opera 51',\n  'safari 12',\n]\n\nmodule.exports = MODERN_BROWSERSLIST_TARGET\n", "let ReactDOMServer\n\ntry {\n  ReactDOMServer = require('react-dom/server.edge')\n} catch (error) {\n  if (\n    error.code !== 'MODULE_NOT_FOUND' &&\n    error.code !== 'ERR_PACKAGE_PATH_NOT_EXPORTED'\n  ) {\n    throw error\n  }\n  // In React versions without react-dom/server.edge, the browser build works in Node.js.\n  // The Node.js build does not support renderToReadableStream.\n  ReactDOMServer = require('react-dom/server.browser')\n}\n\nmodule.exports = ReactDOMServer\n", "module.exports = require(\"next/dist/server/lib/trace/tracer\");", "module.exports = require(\"critters\");", "module.exports = require(\"next/dist/compiled/@ampproject/toolbox-optimizer\");", "module.exports = require(\"next/dist/compiled/jsonwebtoken\");", "module.exports = require(\"path\");", "module.exports = require(\"react-dom/server.browser\");", "module.exports = require(\"react-dom/server.edge\");", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import type { IncomingMessage, ServerResponse } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { NextRouter } from '../shared/lib/router/router'\nimport type { HtmlProps } from '../shared/lib/html-context.shared-runtime'\nimport type { DomainLocale } from './config'\nimport type {\n  AppType,\n  DocumentInitialProps,\n  DocumentType,\n  DocumentProps,\n  DocumentContext,\n  NextComponentType,\n  RenderPage,\n  RenderPageResult,\n} from '../shared/lib/utils'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport type { Redirect } from '../lib/load-custom-routes'\nimport {\n  type NextApiRequestCookies,\n  type __ApiPreviewProps,\n  setLazyProp,\n} from './api-utils'\nimport { getCookieParser } from './api-utils/get-cookie-parser'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type {\n  GetServerSideProps,\n  GetStaticProps,\n  PreviewData,\n  ServerRuntime,\n  SizeLimit,\n} from '../types'\nimport type { UnwrapPromise } from '../lib/coalesced-function'\nimport type { ReactReadableStream } from './stream-utils/node-web-streams-helper'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type { PagesModule } from './route-modules/pages/module'\nimport type { ComponentsEnhancer } from '../shared/lib/utils'\nimport type { NextParsedUrlQuery } from './request-meta'\nimport type { Revalidate } from './lib/cache-control'\nimport type { COMPILER_NAMES } from '../shared/lib/constants'\n\nimport React, { type JSX } from 'react'\nimport ReactDOMServerPages from 'next/dist/server/ReactDOMServerPages'\nimport { StyleRegistry, createStyleRegistry } from 'styled-jsx'\nimport {\n  GSP_NO_RETURNED_VALUE,\n  GSSP_COMPONENT_MEMBER_ERROR,\n  GSSP_NO_RETURNED_VALUE,\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  UNSTABLE_REVALIDATE_RENAME_ERROR,\n} from '../lib/constants'\nimport {\n  NEXT_BUILTIN_DOCUMENT,\n  SERVER_PROPS_ID,\n  STATIC_PROPS_ID,\n  STATIC_STATUS_PAGES,\n} from '../shared/lib/constants'\nimport { isSerializableProps } from '../lib/is-serializable-props'\nimport { isInAmpMode } from '../shared/lib/amp-mode'\nimport { AmpStateContext } from '../shared/lib/amp-context.shared-runtime'\nimport { defaultHead } from '../shared/lib/head'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport Loadable from '../shared/lib/loadable.shared-runtime'\nimport { LoadableContext } from '../shared/lib/loadable-context.shared-runtime'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport {\n  getDisplayName,\n  isResSent,\n  loadGetInitialProps,\n} from '../shared/lib/utils'\nimport { HtmlContext } from '../shared/lib/html-context.shared-runtime'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { getRequestMeta } from './request-meta'\nimport { allowedStatusCodes, getRedirectStatus } from '../lib/redirect-status'\nimport RenderResult, { type PagesRenderResultMetadata } from './render-result'\nimport isError from '../lib/is-error'\nimport {\n  streamToString,\n  renderToInitialFizzStream,\n} from './stream-utils/node-web-streams-helper'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { stripInternalQueries } from './internal-utils'\nimport {\n  adaptForAppRouterInstance,\n  adaptForPathParams,\n  adaptForSearchParams,\n  PathnameContextProviderAdapter,\n} from '../shared/lib/router/adapters'\nimport { AppRouterContext } from '../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathParamsContext,\n} from '../shared/lib/hooks-client-context.shared-runtime'\nimport { getTracer } from './lib/trace/tracer'\nimport { RenderSpan } from './lib/trace/constants'\nimport { ReflectAdapter } from './web/spec-extension/adapters/reflect'\nimport { getCacheControlHeader } from './lib/cache-control'\nimport { getErrorSource } from '../shared/lib/error-source'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport type { PagesDevOverlayType } from '../client/components/react-dev-overlay/pages/pages-dev-overlay'\n\nlet tryGetPreviewData: typeof import('./api-utils/node/try-get-preview-data').tryGetPreviewData\nlet warn: typeof import('../build/output/log').warn\nlet postProcessHTML: typeof import('./post-process').postProcessHTML\n\nconst DOCTYPE = '<!DOCTYPE html>'\n\nif (process.env.NEXT_RUNTIME !== 'edge') {\n  tryGetPreviewData =\n    require('./api-utils/node/try-get-preview-data').tryGetPreviewData\n  warn = require('../build/output/log').warn\n  postProcessHTML = require('./post-process').postProcessHTML\n} else {\n  warn = console.warn.bind(console)\n  postProcessHTML = async (_pathname: string, html: string) => html\n}\n\nfunction noRouter() {\n  const message =\n    'No router instance found. you should only use \"next/router\" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance'\n  throw new Error(message)\n}\n\nasync function renderToString(element: React.ReactElement) {\n  const renderStream = await ReactDOMServerPages.renderToReadableStream(element)\n  await renderStream.allReady\n  return streamToString(renderStream)\n}\n\nclass ServerRouter implements NextRouter {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  events: any\n  isFallback: boolean\n  locale?: string\n  isReady: boolean\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  isPreview: boolean\n  isLocaleDomain: boolean\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    { isFallback }: { isFallback: boolean },\n    isReady: boolean,\n    basePath: string,\n    locale?: string,\n    locales?: readonly string[],\n    defaultLocale?: string,\n    domainLocales?: readonly DomainLocale[],\n    isPreview?: boolean,\n    isLocaleDomain?: boolean\n  ) {\n    this.route = pathname.replace(/\\/$/, '') || '/'\n    this.pathname = pathname\n    this.query = query\n    this.asPath = as\n    this.isFallback = isFallback\n    this.basePath = basePath\n    this.locale = locale\n    this.locales = locales\n    this.defaultLocale = defaultLocale\n    this.isReady = isReady\n    this.domainLocales = domainLocales\n    this.isPreview = !!isPreview\n    this.isLocaleDomain = !!isLocaleDomain\n  }\n\n  push(): any {\n    noRouter()\n  }\n  replace(): any {\n    noRouter()\n  }\n  reload() {\n    noRouter()\n  }\n  back() {\n    noRouter()\n  }\n  forward(): void {\n    noRouter()\n  }\n  prefetch(): any {\n    noRouter()\n  }\n  beforePopState() {\n    noRouter()\n  }\n}\n\nfunction enhanceComponents(\n  options: ComponentsEnhancer,\n  App: AppType,\n  Component: NextComponentType\n): {\n  App: AppType\n  Component: NextComponentType\n} {\n  // For backwards compatibility\n  if (typeof options === 'function') {\n    return {\n      App,\n      Component: options(Component),\n    }\n  }\n\n  return {\n    App: options.enhanceApp ? options.enhanceApp(App) : App,\n    Component: options.enhanceComponent\n      ? options.enhanceComponent(Component)\n      : Component,\n  }\n}\n\nfunction renderPageTree(\n  App: AppType,\n  Component: NextComponentType,\n  props: any\n) {\n  return <App Component={Component} {...props} />\n}\n\nexport type RenderOptsPartial = {\n  canonicalBase: string\n  runtimeConfig?: { [key: string]: any }\n  assetPrefix?: string\n  err?: Error | null\n  nextExport?: boolean\n  dev?: boolean\n  ampPath?: string\n  ErrorDebug?: PagesDevOverlayType\n  ampValidator?: (html: string, pathname: string) => Promise<void>\n  ampSkipValidation?: boolean\n  ampOptimizerConfig?: { [key: string]: any }\n  isNextDataRequest?: boolean\n  params?: ParsedUrlQuery\n  previewProps: __ApiPreviewProps | undefined\n  basePath: string\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  optimizeCss: any\n  nextConfigOutput?: 'standalone' | 'export'\n  nextScriptWorkers: any\n  assetQueryString?: string\n  resolvedUrl?: string\n  resolvedAsPath?: string\n  setIsrStatus?: (key: string, value: boolean | null) => void\n  clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  nextFontManifest?: DeepReadonly<NextFontManifest>\n  distDir?: string\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  disableOptimizedLoading?: boolean\n  supportsDynamicResponse: boolean\n  botType?: 'dom' | 'html' | undefined\n  serveStreamingMetadata?: boolean\n  runtime?: ServerRuntime\n  serverComponents?: boolean\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n  crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined\n  images: ImageConfigComplete\n  largePageDataBytes?: number\n  isOnDemandRevalidate?: boolean\n  strictNextHead: boolean\n  isServerAction?: boolean\n  isExperimentalCompile?: boolean\n  isPrefetch?: boolean\n  expireTime?: number\n  experimental: {\n    clientTraceMetadata?: string[]\n  }\n}\n\nexport type RenderOpts = LoadComponentsReturnType<PagesModule> &\n  RenderOptsPartial\n\n/**\n * Shared context used for all page renders.\n */\nexport type PagesSharedContext = {\n  /**\n   * Used to facilitate caching of page bundles, we send it to the client so\n   * that pageloader knows where to load bundles.\n   */\n  buildId: string\n\n  /**\n   * The deployment ID if the user is deploying to a platform that provides one.\n   */\n  deploymentId: string | undefined\n\n  /**\n   * True if the user is using a custom server.\n   */\n  customServer: true | undefined\n}\n\n/**\n * The context for the given request.\n */\nexport type PagesRenderContext = {\n  /**\n   * Whether this should be rendered as a fallback page.\n   */\n  isFallback: boolean\n\n  /**\n   * Whether this is in draft mode.\n   */\n  isDraftMode: boolean | undefined\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage: string | undefined\n}\n\n/**\n * RenderOptsExtra is being used to split away functionality that's within the\n * renderOpts. Eventually we can have more explicit render options for each\n * route kind.\n */\nexport type RenderOptsExtra = {\n  App: AppType\n  Document: DocumentType\n}\n\nconst invalidKeysMsg = (\n  methodName: 'getServerSideProps' | 'getStaticProps',\n  invalidKeys: string[]\n) => {\n  const docsPathname = `invalid-${methodName.toLocaleLowerCase()}-value`\n\n  return (\n    `Additional keys were returned from \\`${methodName}\\`. Properties intended for your component must be nested under the \\`props\\` key, e.g.:` +\n    `\\n\\n\\treturn { props: { title: 'My Title', content: '...' } }` +\n    `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.` +\n    `\\nRead more: https://nextjs.org/docs/messages/${docsPathname}`\n  )\n}\n\nfunction checkRedirectValues(\n  redirect: Redirect,\n  req: IncomingMessage,\n  method: 'getStaticProps' | 'getServerSideProps'\n) {\n  const { destination, permanent, statusCode, basePath } = redirect\n  let errors: string[] = []\n\n  const hasStatusCode = typeof statusCode !== 'undefined'\n  const hasPermanent = typeof permanent !== 'undefined'\n\n  if (hasPermanent && hasStatusCode) {\n    errors.push(`\\`permanent\\` and \\`statusCode\\` can not both be provided`)\n  } else if (hasPermanent && typeof permanent !== 'boolean') {\n    errors.push(`\\`permanent\\` must be \\`true\\` or \\`false\\``)\n  } else if (hasStatusCode && !allowedStatusCodes.has(statusCode!)) {\n    errors.push(\n      `\\`statusCode\\` must undefined or one of ${[...allowedStatusCodes].join(\n        ', '\n      )}`\n    )\n  }\n  const destinationType = typeof destination\n\n  if (destinationType !== 'string') {\n    errors.push(\n      `\\`destination\\` should be string but received ${destinationType}`\n    )\n  }\n\n  const basePathType = typeof basePath\n\n  if (basePathType !== 'undefined' && basePathType !== 'boolean') {\n    errors.push(\n      `\\`basePath\\` should be undefined or a false, received ${basePathType}`\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new Error(\n      `Invalid redirect object returned from ${method} for ${req.url}\\n` +\n        errors.join(' and ') +\n        '\\n' +\n        `See more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp`\n    )\n  }\n}\n\nexport function errorToJSON(err: Error) {\n  let source: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer =\n    'server'\n\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    source = getErrorSource(err) || 'server'\n  }\n\n  return {\n    name: err.name,\n    source,\n    message: stripAnsi(err.message),\n    stack: err.stack,\n    digest: (err as any).digest,\n  }\n}\n\nfunction serializeError(\n  dev: boolean | undefined,\n  err: Error\n): Error & {\n  statusCode?: number\n  source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n} {\n  if (dev) {\n    return errorToJSON(err)\n  }\n\n  return {\n    name: 'Internal Server Error.',\n    message: '500 - Internal Server Error.',\n    statusCode: 500,\n  }\n}\n\nexport async function renderToHTMLImpl(\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: NextParsedUrlQuery,\n  renderOpts: Omit<RenderOpts, keyof RenderOptsExtra>,\n  extra: RenderOptsExtra,\n  sharedContext: PagesSharedContext,\n  renderContext: PagesRenderContext\n): Promise<RenderResult> {\n  // Adds support for reading `cookies` in `getServerSideProps` when SSR.\n  setLazyProp({ req: req as any }, 'cookies', getCookieParser(req.headers))\n\n  const metadata: PagesRenderResultMetadata = {}\n\n  metadata.assetQueryString =\n    (renderOpts.dev && renderOpts.assetQueryString) || ''\n\n  if (renderOpts.dev && !metadata.assetQueryString) {\n    const userAgent = (req.headers['user-agent'] || '').toLowerCase()\n    if (userAgent.includes('safari') && !userAgent.includes('chrome')) {\n      // In dev we invalidate the cache by appending a timestamp to the resource URL.\n      // This is a workaround to fix https://github.com/vercel/next.js/issues/5860\n      // TODO: remove this workaround when https://bugs.webkit.org/show_bug.cgi?id=187726 is fixed.\n      // Note: The workaround breaks breakpoints on reload since the script url always changes,\n      // so we only apply it to Safari.\n      metadata.assetQueryString = `?ts=${Date.now()}`\n    }\n  }\n\n  // if deploymentId is provided we append it to all asset requests\n  if (sharedContext.deploymentId) {\n    metadata.assetQueryString += `${metadata.assetQueryString ? '&' : '?'}dpl=${\n      sharedContext.deploymentId\n    }`\n  }\n\n  // don't modify original query object\n  query = Object.assign({}, query)\n\n  const {\n    err,\n    dev = false,\n    ampPath = '',\n    pageConfig = {},\n    buildManifest,\n    reactLoadableManifest,\n    ErrorDebug,\n    getStaticProps,\n    getStaticPaths,\n    getServerSideProps,\n    isNextDataRequest,\n    params,\n    previewProps,\n    basePath,\n    images,\n    runtime: globalRuntime,\n    isExperimentalCompile,\n    expireTime,\n  } = renderOpts\n  const { App } = extra\n\n  const assetQueryString = metadata.assetQueryString\n\n  let Document = extra.Document\n\n  let Component: React.ComponentType<{}> | ((props: any) => JSX.Element) =\n    renderOpts.Component\n  const OriginComponent = Component\n\n  const isFallback = renderContext.isFallback ?? false\n  const notFoundSrcPage = renderContext.developmentNotFoundSourcePage\n\n  // next internal queries should be stripped out\n  stripInternalQueries(query)\n\n  const isSSG = !!getStaticProps\n  const isBuildTimeSSG = isSSG && renderOpts.nextExport\n  const defaultAppGetInitialProps =\n    App.getInitialProps === (App as any).origGetInitialProps\n\n  const hasPageGetInitialProps = !!(Component as any)?.getInitialProps\n  const hasPageScripts = (Component as any)?.unstable_scriptLoader\n\n  const pageIsDynamic = isDynamicRoute(pathname)\n\n  const defaultErrorGetInitialProps =\n    pathname === '/_error' &&\n    (Component as any).getInitialProps ===\n      (Component as any).origGetInitialProps\n\n  if (\n    renderOpts.nextExport &&\n    hasPageGetInitialProps &&\n    !defaultErrorGetInitialProps\n  ) {\n    warn(\n      `Detected getInitialProps on page '${pathname}'` +\n        ` while running export. It's recommended to use getStaticProps` +\n        ` which has a more correct behavior for static exporting.` +\n        `\\nRead more: https://nextjs.org/docs/messages/get-initial-props-export`\n    )\n  }\n\n  let isAutoExport =\n    !hasPageGetInitialProps &&\n    defaultAppGetInitialProps &&\n    !isSSG &&\n    !getServerSideProps\n\n  // if we are running from experimental compile and the page\n  // would normally be automatically statically optimized\n  // ensure we set cache header so it's not rendered on-demand\n  // every request\n  if (isAutoExport && !dev && isExperimentalCompile) {\n    res.setHeader(\n      'Cache-Control',\n      getCacheControlHeader({ revalidate: false, expire: expireTime })\n    )\n    isAutoExport = false\n  }\n\n  if (hasPageGetInitialProps && isSSG) {\n    throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (hasPageGetInitialProps && getServerSideProps) {\n    throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && isSSG) {\n    throw new Error(SERVER_PROPS_SSG_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && renderOpts.nextConfigOutput === 'export') {\n    throw new Error(\n      'getServerSideProps cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n    )\n  }\n\n  if (getStaticPaths && !pageIsDynamic) {\n    throw new Error(\n      `getStaticPaths is only allowed for dynamic SSG pages and was found on '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`\n    )\n  }\n\n  if (!!getStaticPaths && !isSSG) {\n    throw new Error(\n      `getStaticPaths was added without a getStaticProps in ${pathname}. Without getStaticProps, getStaticPaths does nothing`\n    )\n  }\n\n  if (isSSG && pageIsDynamic && !getStaticPaths) {\n    throw new Error(\n      `getStaticPaths is required for dynamic SSG pages and is missing for '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n    )\n  }\n\n  let asPath: string = renderOpts.resolvedAsPath || (req.url as string)\n\n  if (dev) {\n    const { isValidElementType } = require('next/dist/compiled/react-is')\n    if (!isValidElementType(Component)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"${pathname}\"`\n      )\n    }\n\n    if (!isValidElementType(App)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_app\"`\n      )\n    }\n\n    if (!isValidElementType(Document)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_document\"`\n      )\n    }\n\n    if (isAutoExport || isFallback) {\n      // remove query values except ones that will be set during export\n      query = {\n        ...(query.amp\n          ? {\n              amp: query.amp,\n            }\n          : {}),\n      }\n      asPath = `${pathname}${\n        // ensure trailing slash is present for non-dynamic auto-export pages\n        req.url!.endsWith('/') && pathname !== '/' && !pageIsDynamic ? '/' : ''\n      }`\n      req.url = pathname\n    }\n\n    if (pathname === '/404' && (hasPageGetInitialProps || getServerSideProps)) {\n      throw new Error(\n        `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n    if (\n      STATIC_STATUS_PAGES.includes(pathname) &&\n      (hasPageGetInitialProps || getServerSideProps)\n    ) {\n      throw new Error(\n        `\\`pages${pathname}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n\n    if (renderOpts?.setIsrStatus) {\n      renderOpts.setIsrStatus(asPath, isSSG || isAutoExport ? true : null)\n    }\n  }\n\n  for (const methodName of [\n    'getStaticProps',\n    'getServerSideProps',\n    'getStaticPaths',\n  ]) {\n    if ((Component as any)?.[methodName]) {\n      throw new Error(\n        `page ${pathname} ${methodName} ${GSSP_COMPONENT_MEMBER_ERROR}`\n      )\n    }\n  }\n\n  await Loadable.preloadAll() // Make sure all dynamic imports are loaded\n\n  let isPreview: boolean | undefined = undefined\n  let previewData: PreviewData\n\n  if (\n    (isSSG || getServerSideProps) &&\n    !isFallback &&\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    previewProps\n  ) {\n    // Reads of this are cached on the `req` object, so this should resolve\n    // instantly. There's no need to pass this data down from a previous\n    // invoke.\n    previewData = tryGetPreviewData(\n      req,\n      res,\n      previewProps,\n      !!renderOpts.multiZoneDraftMode\n    )\n    isPreview = previewData !== false\n  }\n\n  // url will always be set\n  const routerIsReady = !!(\n    getServerSideProps ||\n    hasPageGetInitialProps ||\n    (!defaultAppGetInitialProps && !isSSG) ||\n    isExperimentalCompile\n  )\n  const router = new ServerRouter(\n    pathname,\n    query,\n    asPath,\n    {\n      isFallback: isFallback,\n    },\n    routerIsReady,\n    basePath,\n    renderOpts.locale,\n    renderOpts.locales,\n    renderOpts.defaultLocale,\n    renderOpts.domainLocales,\n    isPreview,\n    getRequestMeta(req, 'isLocaleDomain')\n  )\n\n  const appRouter = adaptForAppRouterInstance(router)\n\n  let scriptLoader: any = {}\n  const jsxStyleRegistry = createStyleRegistry()\n  const ampState = {\n    ampFirst: pageConfig.amp === true,\n    hasQuery: Boolean(query.amp),\n    hybrid: pageConfig.amp === 'hybrid',\n  }\n\n  // Disable AMP under the web environment\n  const inAmpMode = process.env.NEXT_RUNTIME !== 'edge' && isInAmpMode(ampState)\n  let head: JSX.Element[] = defaultHead(inAmpMode)\n  const reactLoadableModules: string[] = []\n\n  let initialScripts: any = {}\n  if (hasPageScripts) {\n    initialScripts.beforeInteractive = []\n      .concat(hasPageScripts())\n      .filter((script: any) => script.props.strategy === 'beforeInteractive')\n      .map((script: any) => script.props)\n  }\n\n  const AppContainer = ({ children }: { children: JSX.Element }) => (\n    <AppRouterContext.Provider value={appRouter}>\n      <SearchParamsContext.Provider value={adaptForSearchParams(router)}>\n        <PathnameContextProviderAdapter\n          router={router}\n          isAutoExport={isAutoExport}\n        >\n          <PathParamsContext.Provider value={adaptForPathParams(router)}>\n            <RouterContext.Provider value={router}>\n              <AmpStateContext.Provider value={ampState}>\n                <HeadManagerContext.Provider\n                  value={{\n                    updateHead: (state) => {\n                      head = state\n                    },\n                    updateScripts: (scripts) => {\n                      scriptLoader = scripts\n                    },\n                    scripts: initialScripts,\n                    mountedInstances: new Set(),\n                  }}\n                >\n                  <LoadableContext.Provider\n                    value={(moduleName) =>\n                      reactLoadableModules.push(moduleName)\n                    }\n                  >\n                    <StyleRegistry registry={jsxStyleRegistry}>\n                      <ImageConfigContext.Provider value={images}>\n                        {children}\n                      </ImageConfigContext.Provider>\n                    </StyleRegistry>\n                  </LoadableContext.Provider>\n                </HeadManagerContext.Provider>\n              </AmpStateContext.Provider>\n            </RouterContext.Provider>\n          </PathParamsContext.Provider>\n        </PathnameContextProviderAdapter>\n      </SearchParamsContext.Provider>\n    </AppRouterContext.Provider>\n  )\n\n  // The `useId` API uses the path indexes to generate an ID for each node.\n  // To guarantee the match of hydration, we need to ensure that the structure\n  // of wrapper nodes is isomorphic in server and client.\n  // TODO: With `enhanceApp` and `enhanceComponents` options, this approach may\n  // not be useful.\n  // https://github.com/facebook/react/pull/22644\n  const Noop = () => null\n  const AppContainerWithIsomorphicFiberStructure: React.FC<{\n    children: JSX.Element\n  }> = ({ children }) => {\n    return (\n      <>\n        {/* <Head/> */}\n        <Noop />\n        <AppContainer>\n          <>\n            {/* <ReactDevOverlay/> */}\n            {dev ? (\n              <>\n                {children}\n                <Noop />\n              </>\n            ) : (\n              children\n            )}\n            {/* <RouteAnnouncer/> */}\n            <Noop />\n          </>\n        </AppContainer>\n      </>\n    )\n  }\n\n  const ctx = {\n    err,\n    req: isAutoExport ? undefined : req,\n    res: isAutoExport ? undefined : res,\n    pathname,\n    query,\n    asPath,\n    locale: renderOpts.locale,\n    locales: renderOpts.locales,\n    defaultLocale: renderOpts.defaultLocale,\n    AppTree: (props: any) => {\n      return (\n        <AppContainerWithIsomorphicFiberStructure>\n          {renderPageTree(App, OriginComponent, { ...props, router })}\n        </AppContainerWithIsomorphicFiberStructure>\n      )\n    },\n    defaultGetInitialProps: async (\n      docCtx: DocumentContext,\n      options: { nonce?: string } = {}\n    ): Promise<DocumentInitialProps> => {\n      const enhanceApp = (AppComp: any) => {\n        return (props: any) => <AppComp {...props} />\n      }\n\n      const { html, head: renderPageHead } = await docCtx.renderPage({\n        enhanceApp,\n      })\n      const styles = jsxStyleRegistry.styles({ nonce: options.nonce })\n      jsxStyleRegistry.flush()\n      return { html, head: renderPageHead, styles }\n    },\n  }\n  let props: any\n\n  const nextExport =\n    !isSSG && (renderOpts.nextExport || (dev && (isAutoExport || isFallback)))\n\n  const styledJsxInsertedHTML = () => {\n    const styles = jsxStyleRegistry.styles()\n    jsxStyleRegistry.flush()\n    return <>{styles}</>\n  }\n\n  props = await loadGetInitialProps(App, {\n    AppTree: ctx.AppTree,\n    Component,\n    router,\n    ctx,\n  })\n\n  if ((isSSG || getServerSideProps) && isPreview) {\n    props.__N_PREVIEW = true\n  }\n\n  if (isSSG) {\n    props[STATIC_PROPS_ID] = true\n  }\n\n  if (isSSG && !isFallback) {\n    let data: Readonly<UnwrapPromise<ReturnType<GetStaticProps>>>\n\n    try {\n      data = await getTracer().trace(\n        RenderSpan.getStaticProps,\n        {\n          spanName: `getStaticProps ${pathname}`,\n          attributes: {\n            'next.route': pathname,\n          },\n        },\n        () =>\n          getStaticProps({\n            ...(pageIsDynamic ? { params } : undefined),\n            ...(isPreview\n              ? { draftMode: true, preview: true, previewData: previewData }\n              : undefined),\n            locales: [...(renderOpts.locales ?? [])],\n            locale: renderOpts.locale,\n            defaultLocale: renderOpts.defaultLocale,\n            revalidateReason: renderOpts.isOnDemandRevalidate\n              ? 'on-demand'\n              : isBuildTimeSSG\n                ? 'build'\n                : 'stale',\n          })\n      )\n    } catch (staticPropsError: any) {\n      // remove not found error code to prevent triggering legacy\n      // 404 rendering\n      if (staticPropsError && staticPropsError.code === 'ENOENT') {\n        delete staticPropsError.code\n      }\n      throw staticPropsError\n    }\n\n    if (data == null) {\n      throw new Error(GSP_NO_RETURNED_VALUE)\n    }\n\n    const invalidKeys = Object.keys(data).filter(\n      (key) =>\n        key !== 'revalidate' &&\n        key !== 'props' &&\n        key !== 'redirect' &&\n        key !== 'notFound'\n    )\n\n    if (invalidKeys.includes('unstable_revalidate')) {\n      throw new Error(UNSTABLE_REVALIDATE_RENAME_ERROR)\n    }\n\n    if (invalidKeys.length) {\n      throw new Error(invalidKeysMsg('getStaticProps', invalidKeys))\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (\n        typeof (data as any).notFound !== 'undefined' &&\n        typeof (data as any).redirect !== 'undefined'\n      ) {\n        throw new Error(\n          `\\`redirect\\` and \\`notFound\\` can not both be returned from ${\n            isSSG ? 'getStaticProps' : 'getServerSideProps'\n          } at the same time. Page: ${pathname}\\nSee more info here: https://nextjs.org/docs/messages/gssp-mixed-not-found-redirect`\n        )\n      }\n    }\n\n    if ('notFound' in data && data.notFound) {\n      if (pathname === '/404') {\n        throw new Error(\n          `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n        )\n      }\n\n      metadata.isNotFound = true\n    }\n\n    if (\n      'redirect' in data &&\n      data.redirect &&\n      typeof data.redirect === 'object'\n    ) {\n      checkRedirectValues(data.redirect as Redirect, req, 'getStaticProps')\n\n      if (isBuildTimeSSG) {\n        throw new Error(\n          `\\`redirect\\` can not be returned from getStaticProps during prerendering (${req.url})\\n` +\n            `See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`\n        )\n      }\n\n      ;(data as any).props = {\n        __N_REDIRECT: data.redirect.destination,\n        __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n      }\n      if (typeof data.redirect.basePath !== 'undefined') {\n        ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n      }\n      metadata.isRedirect = true\n    }\n\n    if (\n      (dev || isBuildTimeSSG) &&\n      !metadata.isNotFound &&\n      !isSerializableProps(pathname, 'getStaticProps', (data as any).props)\n    ) {\n      // this fn should throw an error instead of ever returning `false`\n      throw new Error(\n        'invariant: getStaticProps did not return valid props. Please report this.'\n      )\n    }\n\n    let revalidate: Revalidate\n    if ('revalidate' in data) {\n      if (data.revalidate && renderOpts.nextConfigOutput === 'export') {\n        throw new Error(\n          'ISR cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n        )\n      }\n      if (typeof data.revalidate === 'number') {\n        if (!Number.isInteger(data.revalidate)) {\n          throw new Error(\n            `A page's revalidate option must be seconds expressed as a natural number for ${req.url}. Mixed numbers, such as '${data.revalidate}', cannot be used.` +\n              `\\nTry changing the value to '${Math.ceil(\n                data.revalidate\n              )}' or using \\`Math.ceil()\\` if you're computing the value.`\n          )\n        } else if (data.revalidate <= 0) {\n          throw new Error(\n            `A page's revalidate option can not be less than or equal to zero for ${req.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.` +\n              `\\n\\nTo never revalidate, you can set revalidate to \\`false\\` (only ran once at build-time).` +\n              `\\nTo revalidate as soon as possible, you can set the value to \\`1\\`.`\n          )\n        } else {\n          if (data.revalidate > 31536000) {\n            // if it's greater than a year for some reason error\n            console.warn(\n              `Warning: A page's revalidate option was set to more than a year for ${req.url}. This may have been done in error.` +\n                `\\nTo only run getStaticProps at build-time and not revalidate at runtime, you can set \\`revalidate\\` to \\`false\\`!`\n            )\n          }\n\n          revalidate = data.revalidate\n        }\n      } else if (data.revalidate === true) {\n        // When enabled, revalidate after 1 second. This value is optimal for\n        // the most up-to-date page possible, but without a 1-to-1\n        // request-refresh ratio.\n        revalidate = 1\n      } else if (\n        data.revalidate === false ||\n        typeof data.revalidate === 'undefined'\n      ) {\n        // By default, we never revalidate.\n        revalidate = false\n      } else {\n        throw new Error(\n          `A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(\n            data.revalidate\n          )}' for ${req.url}`\n        )\n      }\n    } else {\n      // By default, we never revalidate.\n      revalidate = false\n    }\n\n    props.pageProps = Object.assign(\n      {},\n      props.pageProps,\n      'props' in data ? data.props : undefined\n    )\n\n    // pass up cache control and props for export\n    metadata.cacheControl = { revalidate, expire: undefined }\n    metadata.pageData = props\n\n    // this must come after revalidate is added to renderResultMeta\n    if (metadata.isNotFound) {\n      return new RenderResult(null, { metadata })\n    }\n  }\n\n  if (getServerSideProps) {\n    props[SERVER_PROPS_ID] = true\n  }\n\n  if (getServerSideProps && !isFallback) {\n    let data: UnwrapPromise<ReturnType<GetServerSideProps>>\n\n    let canAccessRes = true\n    let resOrProxy = res\n    let deferredContent = false\n    if (process.env.NODE_ENV !== 'production') {\n      resOrProxy = new Proxy<ServerResponse>(res, {\n        get: function (obj, prop) {\n          if (!canAccessRes) {\n            const message =\n              `You should not access 'res' after getServerSideProps resolves.` +\n              `\\nRead more: https://nextjs.org/docs/messages/gssp-no-mutating-res`\n\n            if (deferredContent) {\n              throw new Error(message)\n            } else {\n              warn(message)\n            }\n          }\n\n          if (typeof prop === 'symbol') {\n            return ReflectAdapter.get(obj, prop, res)\n          }\n\n          return ReflectAdapter.get(obj, prop, res)\n        },\n      })\n    }\n\n    try {\n      data = await getTracer().trace(\n        RenderSpan.getServerSideProps,\n        {\n          spanName: `getServerSideProps ${pathname}`,\n          attributes: {\n            'next.route': pathname,\n          },\n        },\n        async () =>\n          getServerSideProps({\n            req: req as IncomingMessage & {\n              cookies: NextApiRequestCookies\n            },\n            res: resOrProxy,\n            query,\n            resolvedUrl: renderOpts.resolvedUrl as string,\n            ...(pageIsDynamic ? { params } : undefined),\n            ...(previewData !== false\n              ? { draftMode: true, preview: true, previewData: previewData }\n              : undefined),\n            // We create a copy here to avoid having the types of\n            // `getServerSideProps` change. This ensures that users can't\n            // mutate this array and have it poison the reference.\n            locales: [...(renderOpts.locales ?? [])],\n            locale: renderOpts.locale,\n            defaultLocale: renderOpts.defaultLocale,\n          })\n      )\n      canAccessRes = false\n      metadata.cacheControl = { revalidate: 0, expire: undefined }\n    } catch (serverSidePropsError: any) {\n      // remove not found error code to prevent triggering legacy\n      // 404 rendering\n      if (\n        isError(serverSidePropsError) &&\n        serverSidePropsError.code === 'ENOENT'\n      ) {\n        delete serverSidePropsError.code\n      }\n      throw serverSidePropsError\n    }\n\n    if (data == null) {\n      throw new Error(GSSP_NO_RETURNED_VALUE)\n    }\n\n    if ((data as any).props instanceof Promise) {\n      deferredContent = true\n    }\n\n    const invalidKeys = Object.keys(data).filter(\n      (key) => key !== 'props' && key !== 'redirect' && key !== 'notFound'\n    )\n\n    if ((data as any).unstable_notFound) {\n      throw new Error(\n        `unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${pathname}`\n      )\n    }\n    if ((data as any).unstable_redirect) {\n      throw new Error(\n        `unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${pathname}`\n      )\n    }\n\n    if (invalidKeys.length) {\n      throw new Error(invalidKeysMsg('getServerSideProps', invalidKeys))\n    }\n\n    if ('notFound' in data && data.notFound) {\n      if (pathname === '/404') {\n        throw new Error(\n          `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n        )\n      }\n\n      metadata.isNotFound = true\n      return new RenderResult(null, { metadata })\n    }\n\n    if ('redirect' in data && typeof data.redirect === 'object') {\n      checkRedirectValues(data.redirect as Redirect, req, 'getServerSideProps')\n      ;(data as any).props = {\n        __N_REDIRECT: data.redirect.destination,\n        __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n      }\n      if (typeof data.redirect.basePath !== 'undefined') {\n        ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n      }\n      metadata.isRedirect = true\n    }\n\n    if (deferredContent) {\n      ;(data as any).props = await (data as any).props\n    }\n\n    if (\n      (dev || isBuildTimeSSG) &&\n      !isSerializableProps(pathname, 'getServerSideProps', (data as any).props)\n    ) {\n      // this fn should throw an error instead of ever returning `false`\n      throw new Error(\n        'invariant: getServerSideProps did not return valid props. Please report this.'\n      )\n    }\n\n    props.pageProps = Object.assign({}, props.pageProps, (data as any).props)\n    metadata.pageData = props\n  }\n\n  if (\n    !isSSG && // we only show this warning for legacy pages\n    !getServerSideProps &&\n    process.env.NODE_ENV !== 'production' &&\n    Object.keys(props?.pageProps || {}).includes('url')\n  ) {\n    console.warn(\n      `The prop \\`url\\` is a reserved prop in Next.js for legacy reasons and will be overridden on page ${pathname}\\n` +\n        `See more info here: https://nextjs.org/docs/messages/reserved-page-prop`\n    )\n  }\n\n  // Avoid rendering page un-necessarily for getServerSideProps data request\n  // and getServerSideProps/getStaticProps redirects\n  if ((isNextDataRequest && !isSSG) || metadata.isRedirect) {\n    return new RenderResult(JSON.stringify(props), {\n      metadata,\n    })\n  }\n\n  // We don't call getStaticProps or getServerSideProps while generating\n  // the fallback so make sure to set pageProps to an empty object\n  if (isFallback) {\n    props.pageProps = {}\n  }\n\n  // the response might be finished on the getInitialProps call\n  if (isResSent(res) && !isSSG) return new RenderResult(null, { metadata })\n\n  // we preload the buildManifest for auto-export dynamic pages\n  // to speed up hydrating query values\n  let filteredBuildManifest = buildManifest\n  if (isAutoExport && pageIsDynamic) {\n    const page = denormalizePagePath(normalizePagePath(pathname))\n    // This code would be much cleaner using `immer` and directly pushing into\n    // the result from `getPageFiles`, we could maybe consider that in the\n    // future.\n    if (page in filteredBuildManifest.pages) {\n      filteredBuildManifest = {\n        ...filteredBuildManifest,\n        pages: {\n          ...filteredBuildManifest.pages,\n          [page]: [\n            ...filteredBuildManifest.pages[page],\n            ...filteredBuildManifest.lowPriorityFiles.filter((f) =>\n              f.includes('_buildManifest')\n            ),\n          ],\n        },\n        lowPriorityFiles: filteredBuildManifest.lowPriorityFiles.filter(\n          (f) => !f.includes('_buildManifest')\n        ),\n      }\n    }\n  }\n\n  const Body = ({ children }: { children: JSX.Element }) => {\n    return inAmpMode ? children : <div id=\"__next\">{children}</div>\n  }\n\n  const renderDocument = async () => {\n    // For `Document`, there are two cases that we don't support:\n    // 1. Using `Document.getInitialProps` in the Edge runtime.\n    // 2. Using the class component `Document` with concurrent features.\n\n    const BuiltinFunctionalDocument: DocumentType | undefined = (\n      Document as any\n    )[NEXT_BUILTIN_DOCUMENT]\n\n    if (process.env.NEXT_RUNTIME === 'edge' && Document.getInitialProps) {\n      // In the Edge runtime, `Document.getInitialProps` isn't supported.\n      // We throw an error here if it's customized.\n      if (BuiltinFunctionalDocument) {\n        Document = BuiltinFunctionalDocument\n      } else {\n        throw new Error(\n          '`getInitialProps` in Document component is not supported with the Edge Runtime.'\n        )\n      }\n    }\n\n    async function loadDocumentInitialProps(\n      renderShell: (\n        _App: AppType,\n        _Component: NextComponentType\n      ) => Promise<ReactReadableStream>\n    ) {\n      const renderPage: RenderPage = async (\n        options: ComponentsEnhancer = {}\n      ): Promise<RenderPageResult> => {\n        if (ctx.err && ErrorDebug) {\n          // Always start rendering the shell even if there's an error.\n          if (renderShell) {\n            renderShell(App, Component)\n          }\n\n          const html = await renderToString(\n            <Body>\n              <ErrorDebug />\n            </Body>\n          )\n          return { html, head }\n        }\n\n        if (dev && (props.router || props.Component)) {\n          throw new Error(\n            `'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props`\n          )\n        }\n\n        const { App: EnhancedApp, Component: EnhancedComponent } =\n          enhanceComponents(options, App, Component)\n\n        const stream = await renderShell(EnhancedApp, EnhancedComponent)\n        await stream.allReady\n        const html = await streamToString(stream)\n\n        return { html, head }\n      }\n      const documentCtx = { ...ctx, renderPage }\n      const docProps: DocumentInitialProps = await loadGetInitialProps(\n        Document,\n        documentCtx\n      )\n      // the response might be finished on the getInitialProps call\n      if (isResSent(res) && !isSSG) return null\n\n      if (!docProps || typeof docProps.html !== 'string') {\n        const message = `\"${getDisplayName(\n          Document\n        )}.getInitialProps()\" should resolve to an object with a \"html\" prop set with a valid html string`\n        throw new Error(message)\n      }\n\n      return { docProps, documentCtx }\n    }\n\n    const renderContent = (_App: AppType, _Component: NextComponentType) => {\n      const EnhancedApp = _App || App\n      const EnhancedComponent = _Component || Component\n\n      return ctx.err && ErrorDebug ? (\n        <Body>\n          <ErrorDebug />\n        </Body>\n      ) : (\n        <Body>\n          <AppContainerWithIsomorphicFiberStructure>\n            {renderPageTree(EnhancedApp, EnhancedComponent, {\n              ...props,\n              router,\n            })}\n          </AppContainerWithIsomorphicFiberStructure>\n        </Body>\n      )\n    }\n\n    // Always using react concurrent rendering mode with required react version 18.x\n    const renderShell = async (\n      EnhancedApp: AppType,\n      EnhancedComponent: NextComponentType\n    ) => {\n      const content = renderContent(EnhancedApp, EnhancedComponent)\n      return await renderToInitialFizzStream({\n        ReactDOMServer: ReactDOMServerPages,\n        element: content,\n      })\n    }\n\n    const hasDocumentGetInitialProps =\n      process.env.NEXT_RUNTIME !== 'edge' && !!Document.getInitialProps\n\n    // If it has getInitialProps, we will render the shell in `renderPage`.\n    // Otherwise we do it right now.\n    let documentInitialPropsRes:\n      | {}\n      | Awaited<ReturnType<typeof loadDocumentInitialProps>>\n\n    const [rawStyledJsxInsertedHTML, content] = await Promise.all([\n      renderToString(styledJsxInsertedHTML()),\n      (async () => {\n        if (hasDocumentGetInitialProps) {\n          documentInitialPropsRes = await loadDocumentInitialProps(renderShell)\n          if (documentInitialPropsRes === null) return null\n          const { docProps } = documentInitialPropsRes as any\n          return docProps.html\n        } else {\n          documentInitialPropsRes = {}\n          const stream = await renderShell(App, Component)\n          await stream.allReady\n          return streamToString(stream)\n        }\n      })(),\n    ])\n\n    if (content === null) {\n      return null\n    }\n\n    const contentHTML = rawStyledJsxInsertedHTML + content\n\n    // @ts-ignore: documentInitialPropsRes is set\n    const { docProps } = (documentInitialPropsRes as any) || {}\n    const documentElement = (htmlProps: any) => {\n      if (process.env.NEXT_RUNTIME === 'edge') {\n        return (Document as any)()\n      } else {\n        return <Document {...htmlProps} {...docProps} />\n      }\n    }\n\n    let styles\n    if (hasDocumentGetInitialProps) {\n      styles = docProps.styles\n      head = docProps.head\n    } else {\n      styles = jsxStyleRegistry.styles()\n      jsxStyleRegistry.flush()\n    }\n\n    return {\n      contentHTML,\n      documentElement,\n      head,\n      headTags: [],\n      styles,\n    }\n  }\n\n  getTracer().setRootSpanAttribute('next.route', renderOpts.page)\n  const documentResult = await getTracer().trace(\n    RenderSpan.renderDocument,\n    {\n      spanName: `render route (pages) ${renderOpts.page}`,\n      attributes: {\n        'next.route': renderOpts.page,\n      },\n    },\n    async () => renderDocument()\n  )\n  if (!documentResult) {\n    return new RenderResult(null, { metadata })\n  }\n\n  const dynamicImportsIds = new Set<string | number>()\n  const dynamicImports = new Set<string>()\n\n  for (const mod of reactLoadableModules) {\n    const manifestItem = reactLoadableManifest[mod]\n\n    if (manifestItem) {\n      dynamicImportsIds.add(manifestItem.id)\n      manifestItem.files.forEach((item) => {\n        dynamicImports.add(item)\n      })\n    }\n  }\n\n  const hybridAmp = ampState.hybrid\n  const docComponentsRendered: DocumentProps['docComponentsRendered'] = {}\n\n  const {\n    assetPrefix,\n    defaultLocale,\n    disableOptimizedLoading,\n    domainLocales,\n    locale,\n    locales,\n    runtimeConfig,\n  } = renderOpts\n  const htmlProps: HtmlProps = {\n    __NEXT_DATA__: {\n      props, // The result of getInitialProps\n      page: pathname, // The rendered page\n      query, // querystring parsed / passed by the user\n      buildId: sharedContext.buildId,\n      assetPrefix: assetPrefix === '' ? undefined : assetPrefix, // send assetPrefix to the client side when configured, otherwise don't sent in the resulting HTML\n      runtimeConfig, // runtimeConfig if provided, otherwise don't sent in the resulting HTML\n      nextExport: nextExport === true ? true : undefined, // If this is a page exported by `next export`\n      autoExport: isAutoExport === true ? true : undefined, // If this is an auto exported page\n      isFallback,\n      isExperimentalCompile,\n      dynamicIds:\n        dynamicImportsIds.size === 0\n          ? undefined\n          : Array.from(dynamicImportsIds),\n      err: renderOpts.err ? serializeError(dev, renderOpts.err) : undefined, // Error if one happened, otherwise don't sent in the resulting HTML\n      gsp: !!getStaticProps ? true : undefined, // whether the page is getStaticProps\n      gssp: !!getServerSideProps ? true : undefined, // whether the page is getServerSideProps\n      customServer: sharedContext.customServer,\n      gip: hasPageGetInitialProps ? true : undefined, // whether the page has getInitialProps\n      appGip: !defaultAppGetInitialProps ? true : undefined, // whether the _app has getInitialProps\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview: isPreview === true ? true : undefined,\n      notFoundSrcPage: notFoundSrcPage && dev ? notFoundSrcPage : undefined,\n    },\n    strictNextHead: renderOpts.strictNextHead,\n    buildManifest: filteredBuildManifest,\n    docComponentsRendered,\n    dangerousAsPath: router.asPath,\n    canonicalBase:\n      !renderOpts.ampPath && getRequestMeta(req, 'didStripLocale')\n        ? `${renderOpts.canonicalBase || ''}/${renderOpts.locale}`\n        : renderOpts.canonicalBase,\n    ampPath,\n    inAmpMode,\n    isDevelopment: !!dev,\n    hybridAmp,\n    dynamicImports: Array.from(dynamicImports),\n    dynamicCssManifest: new Set(renderOpts.dynamicCssManifest || []),\n    assetPrefix,\n    // Only enabled in production as development mode has features relying on HMR (style injection for example)\n    unstable_runtimeJS:\n      process.env.NODE_ENV === 'production'\n        ? pageConfig.unstable_runtimeJS\n        : undefined,\n    unstable_JsPreload: pageConfig.unstable_JsPreload,\n    assetQueryString,\n    scriptLoader,\n    locale,\n    disableOptimizedLoading,\n    head: documentResult.head,\n    headTags: documentResult.headTags,\n    styles: documentResult.styles,\n    crossOrigin: renderOpts.crossOrigin,\n    optimizeCss: renderOpts.optimizeCss,\n    nextConfigOutput: renderOpts.nextConfigOutput,\n    nextScriptWorkers: renderOpts.nextScriptWorkers,\n    runtime: globalRuntime,\n    largePageDataBytes: renderOpts.largePageDataBytes,\n    nextFontManifest: renderOpts.nextFontManifest,\n    experimentalClientTraceMetadata:\n      renderOpts.experimental.clientTraceMetadata,\n  }\n\n  const document = (\n    <AmpStateContext.Provider value={ampState}>\n      <HtmlContext.Provider value={htmlProps}>\n        {documentResult.documentElement(htmlProps)}\n      </HtmlContext.Provider>\n    </AmpStateContext.Provider>\n  )\n\n  const documentHTML = await getTracer().trace(\n    RenderSpan.renderToString,\n    async () => renderToString(document)\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    const nonRenderedComponents = []\n    const expectedDocComponents = ['Main', 'Head', 'NextScript', 'Html']\n\n    for (const comp of expectedDocComponents) {\n      if (!(docComponentsRendered as any)[comp]) {\n        nonRenderedComponents.push(comp)\n      }\n    }\n\n    if (nonRenderedComponents.length) {\n      const missingComponentList = nonRenderedComponents\n        .map((e) => `<${e} />`)\n        .join(', ')\n      const plural = nonRenderedComponents.length !== 1 ? 's' : ''\n      console.warn(\n        `Your custom Document (pages/_document) did not render all the required subcomponent${plural}.\\n` +\n          `Missing component${plural}: ${missingComponentList}\\n` +\n          'Read how to fix here: https://nextjs.org/docs/messages/missing-document-component'\n      )\n    }\n  }\n\n  const [renderTargetPrefix, renderTargetSuffix] = documentHTML.split(\n    '<next-js-internal-body-render-target></next-js-internal-body-render-target>',\n    2\n  )\n\n  let prefix = ''\n  if (!documentHTML.startsWith(DOCTYPE)) {\n    prefix += DOCTYPE\n  }\n  prefix += renderTargetPrefix\n  if (inAmpMode) {\n    prefix += '<!-- __NEXT_DATA__ -->'\n  }\n\n  const content = prefix + documentResult.contentHTML + renderTargetSuffix\n\n  const optimizedHtml = await postProcessHTML(pathname, content, renderOpts, {\n    inAmpMode,\n    hybridAmp,\n  })\n\n  return new RenderResult(optimizedHtml, { metadata })\n}\n\nexport type PagesRender = (\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  sharedContext: PagesSharedContext,\n  renderContext: PagesRenderContext\n) => Promise<RenderResult>\n\nexport const renderToHTML: PagesRender = (\n  req,\n  res,\n  pathname,\n  query,\n  renderOpts,\n  sharedContext,\n  renderContext\n) => {\n  return renderToHTMLImpl(\n    req,\n    res,\n    pathname,\n    query,\n    renderOpts,\n    renderOpts,\n    sharedContext,\n    renderContext\n  )\n}\n", "import type { RouteDefinition } from '../route-definitions/route-definition'\n\n/**\n * RouteModuleOptions is the options that are passed to the route module, other\n * route modules should extend this class to add specific options for their\n * route.\n */\nexport interface RouteModuleOptions<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  readonly definition: Readonly<D>\n  readonly userland: Readonly<U>\n}\n\n/**\n * RouteHandlerContext is the base context for a route handler.\n */\nexport interface RouteModuleHandleContext {\n  /**\n   * Any matched parameters for the request. This is only defined for dynamic\n   * routes.\n   */\n  params: Record<string, string | string[] | undefined> | undefined\n}\n\n/**\n * RouteModule is the base class for all route modules. This class should be\n * extended by all route modules.\n */\nexport abstract class RouteModule<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  /**\n   * The userland module. This is the module that is exported from the user's\n   * code. This is marked as readonly to ensure that the module is not mutated\n   * because the module (when compiled) only provides getters.\n   */\n  public readonly userland: Readonly<U>\n\n  /**\n   * The definition of the route.\n   */\n  public readonly definition: Readonly<D>\n\n  /**\n   * The shared modules that are exposed and required for the route module.\n   */\n  public static readonly sharedModules: any\n\n  constructor({ userland, definition }: RouteModuleOptions<D, U>) {\n    this.userland = userland\n    this.definition = definition\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"react/jsx-runtime\");", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"react\");", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"styled-jsx\");", "import MODERN_BROWSERSLIST_TARGET from './modern-browserslist-target'\n\nexport { MODERN_BROWSERSLIST_TARGET }\n\nexport type ValueOf<T> = Required<T>[keyof T]\n\nexport const COMPILER_NAMES = {\n  client: 'client',\n  server: 'server',\n  edgeServer: 'edge-server',\n} as const\n\nexport type CompilerNameValues = ValueOf<typeof COMPILER_NAMES>\n\nexport const COMPILER_INDEXES: {\n  [compilerKey in CompilerNameValues]: number\n} = {\n  [COMPILER_NAMES.client]: 0,\n  [COMPILER_NAMES.server]: 1,\n  [COMPILER_NAMES.edgeServer]: 2,\n} as const\n\nexport const UNDERSCORE_NOT_FOUND_ROUTE = '/_not-found'\nexport const UNDERSCORE_NOT_FOUND_ROUTE_ENTRY = `${UNDERSCORE_NOT_FOUND_ROUTE}/page`\nexport const PHASE_EXPORT = 'phase-export'\nexport const PHASE_PRODUCTION_BUILD = 'phase-production-build'\nexport const PHASE_PRODUCTION_SERVER = 'phase-production-server'\nexport const PHASE_DEVELOPMENT_SERVER = 'phase-development-server'\nexport const PHASE_TEST = 'phase-test'\nexport const PHASE_INFO = 'phase-info'\nexport const PAGES_MANIFEST = 'pages-manifest.json'\nexport const WEBPACK_STATS = 'webpack-stats.json'\nexport const APP_PATHS_MANIFEST = 'app-paths-manifest.json'\nexport const APP_PATH_ROUTES_MANIFEST = 'app-path-routes-manifest.json'\nexport const BUILD_MANIFEST = 'build-manifest.json'\nexport const APP_BUILD_MANIFEST = 'app-build-manifest.json'\nexport const FUNCTIONS_CONFIG_MANIFEST = 'functions-config-manifest.json'\nexport const SUBRESOURCE_INTEGRITY_MANIFEST = 'subresource-integrity-manifest'\nexport const NEXT_FONT_MANIFEST = 'next-font-manifest'\nexport const EXPORT_MARKER = 'export-marker.json'\nexport const EXPORT_DETAIL = 'export-detail.json'\nexport const PRERENDER_MANIFEST = 'prerender-manifest.json'\nexport const ROUTES_MANIFEST = 'routes-manifest.json'\nexport const IMAGES_MANIFEST = 'images-manifest.json'\nexport const SERVER_FILES_MANIFEST = 'required-server-files.json'\nexport const DEV_CLIENT_PAGES_MANIFEST = '_devPagesManifest.json'\nexport const MIDDLEWARE_MANIFEST = 'middleware-manifest.json'\nexport const TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST =\n  '_clientMiddlewareManifest.json'\nexport const DEV_CLIENT_MIDDLEWARE_MANIFEST = '_devMiddlewareManifest.json'\nexport const REACT_LOADABLE_MANIFEST = 'react-loadable-manifest.json'\nexport const SERVER_DIRECTORY = 'server'\nexport const CONFIG_FILES = [\n  'next.config.js',\n  'next.config.mjs',\n  'next.config.ts',\n]\nexport const BUILD_ID_FILE = 'BUILD_ID'\nexport const BLOCKED_PAGES = ['/_document', '/_app', '/_error']\nexport const CLIENT_PUBLIC_FILES_PATH = 'public'\nexport const CLIENT_STATIC_FILES_PATH = 'static'\nexport const STRING_LITERAL_DROP_BUNDLE = '__NEXT_DROP_CLIENT_FILE__'\nexport const NEXT_BUILTIN_DOCUMENT = '__NEXT_BUILTIN_DOCUMENT__'\nexport const BARREL_OPTIMIZATION_PREFIX = '__barrel_optimize__'\n\n// server/[entry]/page_client-reference-manifest.js\nexport const CLIENT_REFERENCE_MANIFEST = 'client-reference-manifest'\n// server/server-reference-manifest\nexport const SERVER_REFERENCE_MANIFEST = 'server-reference-manifest'\n// server/middleware-build-manifest.js\nexport const MIDDLEWARE_BUILD_MANIFEST = 'middleware-build-manifest'\n// server/middleware-react-loadable-manifest.js\nexport const MIDDLEWARE_REACT_LOADABLE_MANIFEST =\n  'middleware-react-loadable-manifest'\n// server/interception-route-rewrite-manifest.js\nexport const INTERCEPTION_ROUTE_REWRITE_MANIFEST =\n  'interception-route-rewrite-manifest'\n// server/dynamic-css-manifest.js\nexport const DYNAMIC_CSS_MANIFEST = 'dynamic-css-manifest'\n\n// static/runtime/main.js\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN = `main`\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN_APP = `${CLIENT_STATIC_FILES_RUNTIME_MAIN}-app`\n// next internal client components chunk for layouts\nexport const APP_CLIENT_INTERNALS = 'app-pages-internals'\n// static/runtime/react-refresh.js\nexport const CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH = `react-refresh`\n// static/runtime/amp.js\nexport const CLIENT_STATIC_FILES_RUNTIME_AMP = `amp`\n// static/runtime/webpack.js\nexport const CLIENT_STATIC_FILES_RUNTIME_WEBPACK = `webpack`\n// static/runtime/polyfills.js\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS = 'polyfills'\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL = Symbol(\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS\n)\nexport const DEFAULT_RUNTIME_WEBPACK = 'webpack-runtime'\nexport const EDGE_RUNTIME_WEBPACK = 'edge-runtime-webpack'\nexport const STATIC_PROPS_ID = '__N_SSG'\nexport const SERVER_PROPS_ID = '__N_SSP'\nexport const DEFAULT_SERIF_FONT = {\n  name: 'Times New Roman',\n  xAvgCharWidth: 821,\n  azAvgWidth: 854.3953488372093,\n  unitsPerEm: 2048,\n}\nexport const DEFAULT_SANS_SERIF_FONT = {\n  name: 'Arial',\n  xAvgCharWidth: 904,\n  azAvgWidth: 934.5116279069767,\n  unitsPerEm: 2048,\n}\nexport const STATIC_STATUS_PAGES = ['/500']\nexport const TRACE_OUTPUT_VERSION = 1\n// in `MB`\nexport const TURBO_TRACE_DEFAULT_MEMORY_LIMIT = 6000\n\nexport const RSC_MODULE_TYPES = {\n  client: 'client',\n  server: 'server',\n} as const\n\n// comparing\n// https://nextjs.org/docs/api-reference/edge-runtime\n// with\n// https://nodejs.org/docs/latest/api/globals.html\nexport const EDGE_UNSUPPORTED_NODE_APIS = [\n  'clearImmediate',\n  'setImmediate',\n  'BroadcastChannel',\n  'ByteLengthQueuingStrategy',\n  'CompressionStream',\n  'CountQueuingStrategy',\n  'DecompressionStream',\n  'DomException',\n  'MessageChannel',\n  'MessageEvent',\n  'MessagePort',\n  'ReadableByteStreamController',\n  'ReadableStreamBYOBRequest',\n  'ReadableStreamDefaultController',\n  'TransformStreamDefaultController',\n  'WritableStreamDefaultController',\n]\n\nexport const SYSTEM_ENTRYPOINTS = new Set<string>([\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n])\n", "export function getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nexport function isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\n}\n", "import {\n  isPlainObject,\n  getObjectClassLabel,\n} from '../shared/lib/is-plain-object'\n\nconst regexpPlainIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport class SerializableError extends Error {\n  constructor(page: string, method: string, path: string, message: string) {\n    super(\n      path\n        ? `Error serializing \\`${path}\\` returned from \\`${method}\\` in \"${page}\".\\nReason: ${message}`\n        : `Error serializing props returned from \\`${method}\\` in \"${page}\".\\nReason: ${message}`\n    )\n  }\n}\n\nexport function isSerializableProps(\n  page: string,\n  method: string,\n  input: any\n): true {\n  if (!isPlainObject(input)) {\n    throw new SerializableError(\n      page,\n      method,\n      '',\n      `Props must be returned as a plain object from ${method}: \\`{ props: { ... } }\\` (received: \\`${getObjectClassLabel(\n        input\n      )}\\`).`\n    )\n  }\n\n  function visit(visited: Map<any, string>, value: any, path: string) {\n    if (visited.has(value)) {\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        `Circular references cannot be expressed in JSON (references: \\`${\n          visited.get(value) || '(self)'\n        }\\`).`\n      )\n    }\n\n    visited.set(value, path)\n  }\n\n  function isSerializable(\n    refs: Map<any, string>,\n    value: any,\n    path: string\n  ): true {\n    const type = typeof value\n    if (\n      // `null` can be serialized, but not `undefined`.\n      value === null ||\n      // n.b. `bigint`, `function`, `symbol`, and `undefined` cannot be\n      // serialized.\n      //\n      // `object` is special-cased below, as it may represent `null`, an Array,\n      // a plain object, a class, et al.\n      type === 'boolean' ||\n      type === 'number' ||\n      type === 'string'\n    ) {\n      return true\n    }\n\n    if (type === 'undefined') {\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        '`undefined` cannot be serialized as JSON. Please use `null` or omit this value.'\n      )\n    }\n\n    if (isPlainObject(value)) {\n      visit(refs, value, path)\n\n      if (\n        Object.entries(value).every(([key, nestedValue]) => {\n          const nextPath = regexpPlainIdentifier.test(key)\n            ? `${path}.${key}`\n            : `${path}[${JSON.stringify(key)}]`\n\n          const newRefs = new Map(refs)\n          return (\n            isSerializable(newRefs, key, nextPath) &&\n            isSerializable(newRefs, nestedValue, nextPath)\n          )\n        })\n      ) {\n        return true\n      }\n\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        `invariant: Unknown error encountered in Object.`\n      )\n    }\n\n    if (Array.isArray(value)) {\n      visit(refs, value, path)\n\n      if (\n        value.every((nestedValue, index) => {\n          const newRefs = new Map(refs)\n          return isSerializable(newRefs, nestedValue, `${path}[${index}]`)\n        })\n      ) {\n        return true\n      }\n\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        `invariant: Unknown error encountered in Array.`\n      )\n    }\n\n    // None of these can be expressed as JSON:\n    // const type: \"bigint\" | \"symbol\" | \"object\" | \"function\"\n    throw new SerializableError(\n      page,\n      method,\n      path,\n      '`' +\n        type +\n        '`' +\n        (type === 'object'\n          ? ` (\"${Object.prototype.toString.call(value)}\")`\n          : '') +\n        ' cannot be serialized as JSON. Please only return JSON serializable data types.'\n    )\n  }\n\n  return isSerializable(new Map(), input, '')\n}\n", "import React from 'react'\n\nexport const AmpStateContext: React.Context<any> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  AmpStateContext.displayName = 'AmpStateContext'\n}\n", "import React from 'react'\n\nexport const HeadManagerContext: React.Context<{\n  updateHead?: (state: any) => void\n  mountedInstances?: any\n  updateScripts?: (state: any) => void\n  scripts?: any\n  getIsSsr?: () => boolean\n\n  // Used in app directory, to render script tags as server components.\n  appDir?: boolean\n  nonce?: string\n}> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  HeadManagerContext.displayName = 'HeadManagerContext'\n}\n", "'use client'\n\nimport React from 'react'\n\ntype CaptureFn = (moduleName: string) => void\n\nexport const LoadableContext = React.createContext<CaptureFn | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  LoadableContext.displayName = 'LoadableContext'\n}\n", "// TODO: Remove use of `any` type.\n/**\n@copyright (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE\n*/\n// https://github.com/jamiebuilds/react-loadable/blob/v5.5.0/src/index.js\n// Modified to be compatible with webpack 4 / Next.js\n\nimport React from 'react'\nimport { LoadableContext } from './loadable-context.shared-runtime'\n\nfunction resolve(obj: any) {\n  return obj && obj.default ? obj.default : obj\n}\n\nconst ALL_INITIALIZERS: any[] = []\nconst READY_INITIALIZERS: any[] = []\nlet initialized = false\n\nfunction load(loader: any) {\n  let promise = loader()\n\n  let state: any = {\n    loading: true,\n    loaded: null,\n    error: null,\n  }\n\n  state.promise = promise\n    .then((loaded: any) => {\n      state.loading = false\n      state.loaded = loaded\n      return loaded\n    })\n    .catch((err: any) => {\n      state.loading = false\n      state.error = err\n      throw err\n    })\n\n  return state\n}\n\nfunction createLoadableComponent(loadFn: any, options: any) {\n  let opts = Object.assign(\n    {\n      loader: null,\n      loading: null,\n      delay: 200,\n      timeout: null,\n      webpack: null,\n      modules: null,\n    },\n    options\n  )\n\n  /** @type LoadableSubscription */\n  let subscription: any = null\n  function init() {\n    if (!subscription) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      const sub = new LoadableSubscription(loadFn, opts)\n      subscription = {\n        getCurrentValue: sub.getCurrentValue.bind(sub),\n        subscribe: sub.subscribe.bind(sub),\n        retry: sub.retry.bind(sub),\n        promise: sub.promise.bind(sub),\n      }\n    }\n    return subscription.promise()\n  }\n\n  // Server only\n  if (typeof window === 'undefined') {\n    ALL_INITIALIZERS.push(init)\n  }\n\n  // Client only\n  if (!initialized && typeof window !== 'undefined') {\n    // require.resolveWeak check is needed for environments that don't have it available like Jest\n    const moduleIds =\n      opts.webpack && typeof (require as any).resolveWeak === 'function'\n        ? opts.webpack()\n        : opts.modules\n    if (moduleIds) {\n      READY_INITIALIZERS.push((ids: any) => {\n        for (const moduleId of moduleIds) {\n          if (ids.includes(moduleId)) {\n            return init()\n          }\n        }\n      })\n    }\n  }\n\n  function useLoadableModule() {\n    init()\n\n    const context = React.useContext(LoadableContext)\n    if (context && Array.isArray(opts.modules)) {\n      opts.modules.forEach((moduleName: any) => {\n        context(moduleName)\n      })\n    }\n  }\n\n  function LoadableComponent(props: any, ref: any) {\n    useLoadableModule()\n\n    const state = (React as any).useSyncExternalStore(\n      subscription.subscribe,\n      subscription.getCurrentValue,\n      subscription.getCurrentValue\n    )\n\n    React.useImperativeHandle(\n      ref,\n      () => ({\n        retry: subscription.retry,\n      }),\n      []\n    )\n\n    return React.useMemo(() => {\n      if (state.loading || state.error) {\n        return React.createElement(opts.loading, {\n          isLoading: state.loading,\n          pastDelay: state.pastDelay,\n          timedOut: state.timedOut,\n          error: state.error,\n          retry: subscription.retry,\n        })\n      } else if (state.loaded) {\n        return React.createElement(resolve(state.loaded), props)\n      } else {\n        return null\n      }\n    }, [props, state])\n  }\n\n  LoadableComponent.preload = () => init()\n  LoadableComponent.displayName = 'LoadableComponent'\n\n  return React.forwardRef(LoadableComponent)\n}\n\nclass LoadableSubscription {\n  _loadFn: any\n  _opts: any\n  _callbacks: any\n  _delay: any\n  _timeout: any\n  _res: any\n  _state: any\n  constructor(loadFn: any, opts: any) {\n    this._loadFn = loadFn\n    this._opts = opts\n    this._callbacks = new Set()\n    this._delay = null\n    this._timeout = null\n\n    this.retry()\n  }\n\n  promise() {\n    return this._res.promise\n  }\n\n  retry() {\n    this._clearTimeouts()\n    this._res = this._loadFn(this._opts.loader)\n\n    this._state = {\n      pastDelay: false,\n      timedOut: false,\n    }\n\n    const { _res: res, _opts: opts } = this\n\n    if (res.loading) {\n      if (typeof opts.delay === 'number') {\n        if (opts.delay === 0) {\n          this._state.pastDelay = true\n        } else {\n          this._delay = setTimeout(() => {\n            this._update({\n              pastDelay: true,\n            })\n          }, opts.delay)\n        }\n      }\n\n      if (typeof opts.timeout === 'number') {\n        this._timeout = setTimeout(() => {\n          this._update({ timedOut: true })\n        }, opts.timeout)\n      }\n    }\n\n    this._res.promise\n      .then(() => {\n        this._update({})\n        this._clearTimeouts()\n      })\n      .catch((_err: any) => {\n        this._update({})\n        this._clearTimeouts()\n      })\n    this._update({})\n  }\n\n  _update(partial: any) {\n    this._state = {\n      ...this._state,\n      error: this._res.error,\n      loaded: this._res.loaded,\n      loading: this._res.loading,\n      ...partial,\n    }\n    this._callbacks.forEach((callback: any) => callback())\n  }\n\n  _clearTimeouts() {\n    clearTimeout(this._delay)\n    clearTimeout(this._timeout)\n  }\n\n  getCurrentValue() {\n    return this._state\n  }\n\n  subscribe(callback: any) {\n    this._callbacks.add(callback)\n    return () => {\n      this._callbacks.delete(callback)\n    }\n  }\n}\n\nfunction Loadable(opts: any) {\n  return createLoadableComponent(load, opts)\n}\n\nfunction flushInitializers(initializers: any, ids?: any): any {\n  let promises = []\n\n  while (initializers.length) {\n    let init = initializers.pop()\n    promises.push(init(ids))\n  }\n\n  return Promise.all(promises).then(() => {\n    if (initializers.length) {\n      return flushInitializers(initializers, ids)\n    }\n  })\n}\n\nLoadable.preloadAll = () => {\n  return new Promise((resolveInitializers, reject) => {\n    flushInitializers(ALL_INITIALIZERS).then(resolveInitializers, reject)\n  })\n}\n\nLoadable.preloadReady = (ids: (string | number)[] = []): Promise<void> => {\n  return new Promise<void>((resolvePreload) => {\n    const res = () => {\n      initialized = true\n      return resolvePreload()\n    }\n    // We always will resolve, errors should be handled within loading UIs.\n    flushInitializers(READY_INITIALIZERS, ids).then(res, res)\n  })\n}\n\ndeclare global {\n  interface Window {\n    __NEXT_PRELOADREADY?: (ids?: (string | number)[]) => Promise<void>\n  }\n}\n\nif (typeof window !== 'undefined') {\n  window.__NEXT_PRELOADREADY = Loadable.preloadReady\n}\n\nexport default Loadable\n", "import React from 'react'\nimport type { NextRouter } from './router/router'\n\nexport const RouterContext = React.createContext<NextRouter | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  RouterContext.displayName = 'RouterContext'\n}\n", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n", "import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n", "import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n", "import {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\n\n// Identify /.*[param].*/ in route string\nconst TEST_ROUTE = /\\/[^/]*\\[[^/]+\\][^/]*(?=\\/|$)/\n\n// Identify /[param]/ in route string\nconst TEST_STRICT_ROUTE = /\\/\\[[^/]+\\](?=\\/|$)/\n\n/**\n * Check if a route is dynamic.\n *\n * @param route - The route to check.\n * @param strict - Whether to use strict mode which prohibits segments with prefixes/suffixes (default: true).\n * @returns Whether the route is dynamic.\n */\nexport function isDynamicRoute(route: string, strict: boolean = true): boolean {\n  if (isInterceptionRouteAppPath(route)) {\n    route = extractInterceptionRouteInformation(route).interceptedRoute\n  }\n\n  if (strict) {\n    return TEST_STRICT_ROUTE.test(route)\n  }\n\n  return TEST_ROUTE.test(route)\n}\n", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n", "import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n", "import type { BuildManifest } from '../../server/get-page-files'\nimport type { ServerRuntime } from '../../types'\nimport type { NEXT_DATA } from './utils'\nimport type { NextFontManifest } from '../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { DeepReadonly } from './deep-readonly'\n\nimport { createContext, useContext, type JSX } from 'react'\n\nexport type HtmlProps = {\n  __NEXT_DATA__: NEXT_DATA\n  strictNextHead: boolean\n  dangerousAsPath: string\n  docComponentsRendered: {\n    Html?: boolean\n    Main?: boolean\n    Head?: boolean\n    NextScript?: boolean\n  }\n  buildManifest: BuildManifest\n  ampPath: string\n  inAmpMode: boolean\n  hybridAmp: boolean\n  isDevelopment: boolean\n  dynamicImports: string[]\n  /**\n   * This manifest is only needed for Pages dir, Production, Webpack\n   * @see https://github.com/vercel/next.js/pull/72959\n   */\n  dynamicCssManifest: Set<string>\n  assetPrefix?: string\n  canonicalBase: string\n  headTags: any[]\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  assetQueryString: string\n  scriptLoader: {\n    afterInteractive?: string[]\n    beforeInteractive?: any[]\n    worker?: any[]\n  }\n  locale?: string\n  disableOptimizedLoading?: boolean\n  styles?: React.ReactElement[] | Iterable<React.ReactNode>\n  head?: Array<JSX.Element | null>\n  crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined\n  optimizeCss?: any\n  nextConfigOutput?: 'standalone' | 'export'\n  nextScriptWorkers?: boolean\n  runtime?: ServerRuntime\n  hasConcurrentFeatures?: boolean\n  largePageDataBytes?: number\n  nextFontManifest?: DeepReadonly<NextFontManifest>\n  experimentalClientTraceMetadata?: string[]\n}\n\nexport const HtmlContext = createContext<HtmlProps | undefined>(undefined)\nif (process.env.NODE_ENV !== 'production') {\n  HtmlContext.displayName = 'HtmlContext'\n}\n\nexport function useHtmlContext() {\n  const context = useContext(HtmlContext)\n\n  if (!context) {\n    throw new Error(\n      `<Html> should not be imported outside of pages/_document.\\n` +\n        'Read more: https://nextjs.org/docs/messages/no-document-import-in-page'\n    )\n  }\n\n  return context\n}\n", "/* eslint-disable no-redeclare */\nimport type { IncomingMessage } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { BaseNextRequest } from './base-http'\nimport type { CloneableBody } from './body-streams'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport type { ServerComponentsHmrCache } from './response-cache'\n\n// FIXME: (wyattjoh) this is a temporary solution to allow us to pass data between bundled modules\nexport const NEXT_REQUEST_META = Symbol.for('NextInternalRequestMeta')\n\nexport type NextIncomingMessage = (BaseNextRequest | IncomingMessage) & {\n  [NEXT_REQUEST_META]?: RequestMeta\n}\n\nexport interface RequestMeta {\n  /**\n   * The query that was used to make the request.\n   */\n  initQuery?: ParsedUrlQuery\n\n  /**\n   * The URL that was used to make the request.\n   */\n  initURL?: string\n\n  /**\n   * The protocol that was used to make the request.\n   */\n  initProtocol?: string\n\n  /**\n   * The body that was read from the request. This is used to allow the body to\n   * be read multiple times.\n   */\n  clonableBody?: CloneableBody\n\n  /**\n   * True when the request matched a locale domain that was configured in the\n   * next.config.js file.\n   */\n  isLocaleDomain?: boolean\n\n  /**\n   * True when the request had locale information stripped from the pathname\n   * part of the URL.\n   */\n  didStripLocale?: boolean\n\n  /**\n   * If the request had it's URL rewritten, this is the URL it was rewritten to.\n   */\n  rewroteURL?: string\n\n  /**\n   * The cookies that were added by middleware and were added to the response.\n   */\n  middlewareCookie?: string[]\n\n  /**\n   * The match on the request for a given route.\n   */\n  match?: RouteMatch\n\n  /**\n   * The incremental cache to use for the request.\n   */\n  incrementalCache?: any\n\n  /**\n   * The server components HMR cache, only for dev.\n   */\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  /**\n   * Equals the segment path that was used for the prefetch RSC request.\n   */\n  segmentPrefetchRSCRequest?: string\n\n  /**\n   * True when the request is for the prefetch flight data.\n   */\n  isPrefetchRSCRequest?: true\n\n  /**\n   * True when the request is for the flight data.\n   */\n  isRSCRequest?: true\n\n  /**\n   * True when the request is for the `/_next/data` route using the pages\n   * router.\n   */\n  isNextDataReq?: true\n\n  /**\n   * Postponed state to use for resumption. If present it's assumed that the\n   * request is for a page that has postponed (there are no guarantees that the\n   * page actually has postponed though as it would incur an additional cache\n   * lookup).\n   */\n  postponed?: string\n\n  /**\n   * If provided, this will be called when a response cache entry was generated\n   * or looked up in the cache.\n   */\n  onCacheEntry?: (\n    cacheEntry: any,\n    requestMeta: any\n  ) => Promise<boolean | void> | boolean | void\n\n  /**\n   * The previous revalidate before rendering 404 page for notFound: true\n   */\n  notFoundRevalidate?: number | false\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage?: string\n\n  /**\n   * The path we routed to and should be invoked\n   */\n  invokePath?: string\n\n  /**\n   * The specific page output we should be matching\n   */\n  invokeOutput?: string\n\n  /**\n   * The status we are invoking the request with from routing\n   */\n  invokeStatus?: number\n\n  /**\n   * The routing error we are invoking with\n   */\n  invokeError?: Error\n\n  /**\n   * The query parsed for the invocation\n   */\n  invokeQuery?: Record<string, undefined | string | string[]>\n\n  /**\n   * Whether the request is a middleware invocation\n   */\n  middlewareInvoke?: boolean\n\n  /**\n   * Whether the default route matches were set on the request during routing.\n   */\n  didSetDefaultRouteMatches?: boolean\n\n  /**\n   * Whether the request is for the custom error page.\n   */\n  customErrorRender?: true\n\n  /**\n   * Whether to bubble up the NoFallbackError to the caller when a 404 is\n   * returned.\n   */\n  bubbleNoFallback?: true\n\n  /**\n   * True when the request had locale information inferred from the default\n   * locale.\n   */\n  localeInferredFromDefault?: true\n\n  /**\n   * The locale that was inferred or explicitly set for the request.\n   */\n  locale?: string\n\n  /**\n   * The default locale that was inferred or explicitly set for the request.\n   */\n  defaultLocale?: string\n}\n\n/**\n * Gets the request metadata. If no key is provided, the entire metadata object\n * is returned.\n *\n * @param req the request to get the metadata from\n * @param key the key to get from the metadata (optional)\n * @returns the value for the key or the entire metadata object\n */\nexport function getRequestMeta(\n  req: NextIncomingMessage,\n  key?: undefined\n): RequestMeta\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key: K\n): RequestMeta[K]\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key?: K\n): RequestMeta | RequestMeta[K] {\n  const meta = req[NEXT_REQUEST_META] || {}\n  return typeof key === 'string' ? meta[key] : meta\n}\n\n/**\n * Sets the request metadata.\n *\n * @param req the request to set the metadata on\n * @param meta the metadata to set\n * @returns the mutated request metadata\n */\nexport function setRequestMeta(req: NextIncomingMessage, meta: RequestMeta) {\n  req[NEXT_REQUEST_META] = meta\n  return meta\n}\n\n/**\n * Adds a value to the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to set\n * @param value the value to set\n * @returns the mutated request metadata\n */\nexport function addRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K,\n  value: RequestMeta[K]\n) {\n  const meta = getRequestMeta(request)\n  meta[key] = value\n  return setRequestMeta(request, meta)\n}\n\n/**\n * Removes a key from the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to remove\n * @returns the mutated request metadata\n */\nexport function removeRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K\n) {\n  const meta = getRequestMeta(request)\n  delete meta[key]\n  return setRequestMeta(request, meta)\n}\n\ntype NextQueryMetadata = {\n  /**\n   * The `_rsc` query parameter used for cache busting to ensure that the RSC\n   * requests do not get cached by the browser explicitly.\n   */\n  [NEXT_RSC_UNION_QUERY]?: string\n}\n\nexport type NextParsedUrlQuery = ParsedUrlQuery &\n  NextQueryMetadata & {\n    amp?: '1'\n  }\n\nexport interface NextUrlWithParsedQuery extends UrlWithParsedQuery {\n  query: NextParsedUrlQuery\n}\n", "export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n", "import { RedirectStatusCode } from '../client/components/redirect-status-code'\n\nexport const allowedStatusCodes = new Set([301, 302, 303, 307, 308])\n\nexport function getRedirectStatus(route: {\n  statusCode?: number\n  permanent?: boolean\n}): number {\n  return (\n    route.statusCode ||\n    (route.permanent\n      ? RedirectStatusCode.PermanentRedirect\n      : RedirectStatusCode.TemporaryRedirect)\n  )\n}\n\n// for redirects we restrict matching /_next and for all routes\n// we add an optional trailing slash at the end for easier\n// configuring between trailingSlash: true/false\nexport function modifyRouteRegex(regex: string, restrictedPaths?: string[]) {\n  if (restrictedPaths) {\n    regex = regex.replace(\n      /\\^/,\n      `^(?!${restrictedPaths\n        .map((path) => path.replace(/\\//g, '\\\\/'))\n        .join('|')})`\n    )\n  }\n  regex = regex.replace(/\\$$/, '(?:\\\\/)?$')\n  return regex\n}\n", "import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encodedTags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: typeof import('react-dom/server.edge')\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: typeof window.__next_root_layout_missing_tags = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<script>self.__next_root_layout_missing_tags=${JSON.stringify(\n            missingTags\n          )}</script>`\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Insert generated metadata\n    createHeadInsertionTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to head\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to body\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n", "export const ENCODED_TAGS = {\n  // opening tags do not have the closing `>` since they can contain other attributes such as `<body className=''>`\n  OPENING: {\n    // <html\n    HTML: new Uint8Array([60, 104, 116, 109, 108]),\n    // <body\n    BODY: new Uint8Array([60, 98, 111, 100, 121]),\n  },\n  CLOSED: {\n    // </head>\n    HEAD: new Uint8Array([60, 47, 104, 101, 97, 100, 62]),\n    // </body>\n    BODY: new Uint8Array([60, 47, 98, 111, 100, 121, 62]),\n    // </html>\n    HTML: new Uint8Array([60, 47, 104, 116, 109, 108, 62]),\n    // </body></html>\n    BODY_AND_HTML: new Uint8Array([\n      60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62,\n    ]),\n  },\n} as const\n", "/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n", "/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Similarly to `addPathPrefix`, this function adds a suffix at the end on the\n * provided path. It also works only for paths ensuring the argument starts\n * with a slash.\n */\nexport function addPathSuffix(path: string, suffix?: string) {\n  if (!path.startsWith('/') || !suffix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${pathname}${suffix}${query}${hash}`\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n", "export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n", "import type { OutgoingHttpHeaders } from 'http'\nimport type { DomainLocale, I18NConfig } from '../config-shared'\nimport type { I18NProvider } from '../lib/i18n-provider'\n\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { formatNextPathnameInfo } from '../../shared/lib/router/utils/format-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\n\ninterface Options {\n  base?: string | URL\n  headers?: OutgoingHttpHeaders\n  forceLocale?: boolean\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  i18nProvider?: I18NProvider\n}\n\nconst REGEX_LOCALHOST_HOSTNAME =\n  /(?!^https?:\\/\\/)(127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\\[::1\\]|localhost)/\n\nfunction parseURL(url: string | URL, base?: string | URL) {\n  return new URL(\n    String(url).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost'),\n    base && String(base).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost')\n  )\n}\n\nconst Internal = Symbol('NextURLInternal')\n\nexport class NextURL {\n  private [Internal]: {\n    basePath: string\n    buildId?: string\n    flightSearchParameters?: Record<string, string>\n    defaultLocale?: string\n    domainLocale?: DomainLocale\n    locale?: string\n    options: Options\n    trailingSlash?: boolean\n    url: URL\n  }\n\n  constructor(input: string | URL, base?: string | URL, opts?: Options)\n  constructor(input: string | URL, opts?: Options)\n  constructor(\n    input: string | URL,\n    baseOrOpts?: string | URL | Options,\n    opts?: Options\n  ) {\n    let base: undefined | string | URL\n    let options: Options\n\n    if (\n      (typeof baseOrOpts === 'object' && 'pathname' in baseOrOpts) ||\n      typeof baseOrOpts === 'string'\n    ) {\n      base = baseOrOpts\n      options = opts || {}\n    } else {\n      options = opts || baseOrOpts || {}\n    }\n\n    this[Internal] = {\n      url: parseURL(input, base ?? options.base),\n      options: options,\n      basePath: '',\n    }\n\n    this.analyze()\n  }\n\n  private analyze() {\n    const info = getNextPathnameInfo(this[Internal].url.pathname, {\n      nextConfig: this[Internal].options.nextConfig,\n      parseData: !process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,\n      i18nProvider: this[Internal].options.i18nProvider,\n    })\n\n    const hostname = getHostname(\n      this[Internal].url,\n      this[Internal].options.headers\n    )\n    this[Internal].domainLocale = this[Internal].options.i18nProvider\n      ? this[Internal].options.i18nProvider.detectDomainLocale(hostname)\n      : detectDomainLocale(\n          this[Internal].options.nextConfig?.i18n?.domains,\n          hostname\n        )\n\n    const defaultLocale =\n      this[Internal].domainLocale?.defaultLocale ||\n      this[Internal].options.nextConfig?.i18n?.defaultLocale\n\n    this[Internal].url.pathname = info.pathname\n    this[Internal].defaultLocale = defaultLocale\n    this[Internal].basePath = info.basePath ?? ''\n    this[Internal].buildId = info.buildId\n    this[Internal].locale = info.locale ?? defaultLocale\n    this[Internal].trailingSlash = info.trailingSlash\n  }\n\n  private formatPathname() {\n    return formatNextPathnameInfo({\n      basePath: this[Internal].basePath,\n      buildId: this[Internal].buildId,\n      defaultLocale: !this[Internal].options.forceLocale\n        ? this[Internal].defaultLocale\n        : undefined,\n      locale: this[Internal].locale,\n      pathname: this[Internal].url.pathname,\n      trailingSlash: this[Internal].trailingSlash,\n    })\n  }\n\n  private formatSearch() {\n    return this[Internal].url.search\n  }\n\n  public get buildId() {\n    return this[Internal].buildId\n  }\n\n  public set buildId(buildId: string | undefined) {\n    this[Internal].buildId = buildId\n  }\n\n  public get locale() {\n    return this[Internal].locale ?? ''\n  }\n\n  public set locale(locale: string) {\n    if (\n      !this[Internal].locale ||\n      !this[Internal].options.nextConfig?.i18n?.locales.includes(locale)\n    ) {\n      throw new TypeError(\n        `The NextURL configuration includes no locale \"${locale}\"`\n      )\n    }\n\n    this[Internal].locale = locale\n  }\n\n  get defaultLocale() {\n    return this[Internal].defaultLocale\n  }\n\n  get domainLocale() {\n    return this[Internal].domainLocale\n  }\n\n  get searchParams() {\n    return this[Internal].url.searchParams\n  }\n\n  get host() {\n    return this[Internal].url.host\n  }\n\n  set host(value: string) {\n    this[Internal].url.host = value\n  }\n\n  get hostname() {\n    return this[Internal].url.hostname\n  }\n\n  set hostname(value: string) {\n    this[Internal].url.hostname = value\n  }\n\n  get port() {\n    return this[Internal].url.port\n  }\n\n  set port(value: string) {\n    this[Internal].url.port = value\n  }\n\n  get protocol() {\n    return this[Internal].url.protocol\n  }\n\n  set protocol(value: string) {\n    this[Internal].url.protocol = value\n  }\n\n  get href() {\n    const pathname = this.formatPathname()\n    const search = this.formatSearch()\n    return `${this.protocol}//${this.host}${pathname}${search}${this.hash}`\n  }\n\n  set href(url: string) {\n    this[Internal].url = parseURL(url)\n    this.analyze()\n  }\n\n  get origin() {\n    return this[Internal].url.origin\n  }\n\n  get pathname() {\n    return this[Internal].url.pathname\n  }\n\n  set pathname(value: string) {\n    this[Internal].url.pathname = value\n  }\n\n  get hash() {\n    return this[Internal].url.hash\n  }\n\n  set hash(value: string) {\n    this[Internal].url.hash = value\n  }\n\n  get search() {\n    return this[Internal].url.search\n  }\n\n  set search(value: string) {\n    this[Internal].url.search = value\n  }\n\n  get password() {\n    return this[Internal].url.password\n  }\n\n  set password(value: string) {\n    this[Internal].url.password = value\n  }\n\n  get username() {\n    return this[Internal].url.username\n  }\n\n  set username(value: string) {\n    this[Internal].url.username = value\n  }\n\n  get basePath() {\n    return this[Internal].basePath\n  }\n\n  set basePath(value: string) {\n    this[Internal].basePath = value.startsWith('/') ? value : `/${value}`\n  }\n\n  toString() {\n    return this.href\n  }\n\n  toJSON() {\n    return this.href\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      href: this.href,\n      origin: this.origin,\n      protocol: this.protocol,\n      username: this.username,\n      password: this.password,\n      host: this.host,\n      hostname: this.hostname,\n      port: this.port,\n      pathname: this.pathname,\n      search: this.search,\n      searchParams: this.searchParams,\n      hash: this.hash,\n    }\n  }\n\n  clone() {\n    return new NextURL(String(this), this[Internal].options)\n  }\n}\n", "import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n", "import { pathHasPrefix } from './path-has-prefix'\n\n/**\n * Given a path and a prefix it will remove the prefix when it exists in the\n * given path. It ensures it matches exactly without containing extra chars\n * and if the prefix is not there it will be noop.\n *\n * @param path The path to remove the prefix from.\n * @param prefix The prefix to be removed.\n */\nexport function removePathPrefix(path: string, prefix: string): string {\n  // If the path doesn't start with the prefix we can return it as is. This\n  // protects us from situations where the prefix is a substring of the path\n  // prefix such as:\n  //\n  // For prefix: /blog\n  //\n  //   /blog -> true\n  //   /blog/ -> true\n  //   /blog/1 -> true\n  //   /blogging -> false\n  //   /blogging/ -> false\n  //   /blogging/1 -> false\n  if (!pathHasPrefix(path, prefix)) {\n    return path\n  }\n\n  // Remove the prefix from the path via slicing.\n  const withoutPrefix = path.slice(prefix.length)\n\n  // If the path without the prefix starts with a `/` we can return it as is.\n  if (withoutPrefix.startsWith('/')) {\n    return withoutPrefix\n  }\n\n  // If the path without the prefix doesn't start with a `/` we need to add it\n  // back to the path to make sure it's a valid path.\n  return `/${withoutPrefix}`\n}\n", "import type { OutgoingHttpHeaders } from 'http'\n\n/**\n * Takes an object with a hostname property (like a parsed URL) and some\n * headers that may contain Host and returns the preferred hostname.\n * @param parsed An object containing a hostname property.\n * @param headers A dictionary with headers containing a `host`.\n */\nexport function getHostname(\n  parsed: { hostname?: string | null },\n  headers?: OutgoingHttpHeaders\n): string | undefined {\n  // Get the hostname from the headers if it exists, otherwise use the parsed\n  // hostname.\n  let hostname: string\n  if (headers?.host && !Array.isArray(headers.host)) {\n    hostname = headers.host.toString().split(':', 1)[0]\n  } else if (parsed.hostname) {\n    hostname = parsed.hostname\n  } else return\n\n  return hostname.toLowerCase()\n}\n", "import type { DomainLocale } from '../../../server/config-shared'\n\nexport function detectDomainLocale(\n  domainItems?: readonly DomainLocale[],\n  hostname?: string,\n  detectedLocale?: string\n) {\n  if (!domainItems) return\n\n  if (detectedLocale) {\n    detectedLocale = detectedLocale.toLowerCase()\n  }\n\n  for (const item of domainItems) {\n    // remove port if present\n    const domainHostname = item.domain?.split(':', 1)[0].toLowerCase()\n    if (\n      hostname === domainHostname ||\n      detectedLocale === item.defaultLocale.toLowerCase() ||\n      item.locales?.some((locale) => locale.toLowerCase() === detectedLocale)\n    ) {\n      return item\n    }\n  }\n}\n", "import type { NextPathnameInfo } from './get-next-pathname-info'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { addPathPrefix } from './add-path-prefix'\nimport { addPathSuffix } from './add-path-suffix'\nimport { addLocale } from './add-locale'\n\ninterface ExtendedInfo extends NextPathnameInfo {\n  defaultLocale?: string\n  ignorePrefix?: boolean\n}\n\nexport function formatNextPathnameInfo(info: ExtendedInfo) {\n  let pathname = addLocale(\n    info.pathname,\n    info.locale,\n    info.buildId ? undefined : info.defaultLocale,\n    info.ignorePrefix\n  )\n\n  if (info.buildId || !info.trailingSlash) {\n    pathname = removeTrailingSlash(pathname)\n  }\n\n  if (info.buildId) {\n    pathname = addPathSuffix(\n      addPathPrefix(pathname, `/_next/data/${info.buildId}`),\n      info.pathname === '/' ? 'index.json' : '.json'\n    )\n  }\n\n  pathname = addPathPrefix(pathname, info.basePath)\n  return !info.buildId && info.trailingSlash\n    ? !pathname.endsWith('/')\n      ? addPathSuffix(pathname, '/')\n      : pathname\n    : removeTrailingSlash(pathname)\n}\n", "import { addPathPrefix } from './add-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\n\n/**\n * For a given path and a locale, if the locale is given, it will prefix the\n * locale. The path shouldn't be an API path. If a default locale is given the\n * prefix will be omitted if the locale is already the default locale.\n */\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string,\n  ignorePrefix?: boolean\n) {\n  // If no locale was given or the locale is the default locale, we don't need\n  // to prefix the path.\n  if (!locale || locale === defaultLocale) return path\n\n  const lower = path.toLowerCase()\n\n  // If the path is an API path or the path already has the locale prefix, we\n  // don't need to prefix the path.\n  if (!ignorePrefix) {\n    if (pathHasPrefix(lower, '/api')) return path\n    if (pathHasPrefix(lower, `/${locale.toLowerCase()}`)) return path\n  }\n\n  // Add the locale prefix to the path.\n  return addPathPrefix(path, `/${locale}`)\n}\n", "import type { I18NConfig } from '../../config-shared'\nimport { NextURL } from '../next-url'\nimport { toNodeOutgoingHttpHeaders, validateURL } from '../utils'\nimport { RemovedUAError, RemovedPageError } from '../error'\nimport { RequestCookies } from './cookies'\n\nexport const INTERNALS = Symbol('internal request')\n\n/**\n * This class extends the [Web `Request` API](https://developer.mozilla.org/docs/Web/API/Request) with additional convenience methods.\n *\n * Read more: [Next.js Docs: `NextRequest`](https://nextjs.org/docs/app/api-reference/functions/next-request)\n */\nexport class NextRequest extends Request {\n  [INTERNALS]: {\n    cookies: RequestCookies\n    url: string\n    nextUrl: NextURL\n  }\n\n  constructor(input: URL | RequestInfo, init: RequestInit = {}) {\n    const url =\n      typeof input !== 'string' && 'url' in input ? input.url : String(input)\n\n    validateURL(url)\n\n    // node Request instance requires duplex option when a body\n    // is present or it errors, we don't handle this for\n    // Request being passed in since it would have already\n    // errored if this wasn't configured\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      if (init.body && init.duplex !== 'half') {\n        init.duplex = 'half'\n      }\n    }\n\n    if (input instanceof Request) super(input, init)\n    else super(url, init)\n\n    const nextUrl = new NextURL(url, {\n      headers: toNodeOutgoingHttpHeaders(this.headers),\n      nextConfig: init.nextConfig,\n    })\n    this[INTERNALS] = {\n      cookies: new RequestCookies(this.headers),\n      nextUrl,\n      url: process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n        ? url\n        : nextUrl.toString(),\n    }\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      cookies: this.cookies,\n      nextUrl: this.nextUrl,\n      url: this.url,\n      // rest of props come from Request\n      bodyUsed: this.bodyUsed,\n      cache: this.cache,\n      credentials: this.credentials,\n      destination: this.destination,\n      headers: Object.fromEntries(this.headers),\n      integrity: this.integrity,\n      keepalive: this.keepalive,\n      method: this.method,\n      mode: this.mode,\n      redirect: this.redirect,\n      referrer: this.referrer,\n      referrerPolicy: this.referrerPolicy,\n      signal: this.signal,\n    }\n  }\n\n  public get cookies() {\n    return this[INTERNALS].cookies\n  }\n\n  public get nextUrl() {\n    return this[INTERNALS].nextUrl\n  }\n\n  /**\n   * @deprecated\n   * `page` has been deprecated in favour of `URLPattern`.\n   * Read more: https://nextjs.org/docs/messages/middleware-request-page\n   */\n  public get page() {\n    throw new RemovedPageError()\n  }\n\n  /**\n   * @deprecated\n   * `ua` has been removed in favour of \\`userAgent\\` function.\n   * Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n   */\n  public get ua() {\n    throw new RemovedUAError()\n  }\n\n  public get url() {\n    return this[INTERNALS].url\n  }\n}\n\nexport interface RequestInit extends globalThis.RequestInit {\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  signal?: AbortSignal\n  // see https://github.com/whatwg/fetch/pull/1457\n  duplex?: 'half'\n}\n", "import type { BaseNextRequest } from '../../../base-http'\nimport type { NodeNextRequest } from '../../../base-http/node'\nimport type { WebNextRequest } from '../../../base-http/web'\nimport type { Writable } from 'node:stream'\n\nimport { getRequestMeta } from '../../../request-meta'\nimport { fromNodeOutgoingHttpHeaders } from '../../utils'\nimport { NextRequest } from '../request'\nimport { isNodeNextRequest, isWebNextRequest } from '../../../base-http/helpers'\n\nexport const ResponseAbortedName = 'ResponseAborted'\nexport class ResponseAborted extends Error {\n  public readonly name = ResponseAbortedName\n}\n\n/**\n * Creates an AbortController tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * If the `close` event is fired before the `finish` event, then we'll send the\n * `abort` signal.\n */\nexport function createAbortController(response: Writable): AbortController {\n  const controller = new AbortController()\n\n  // If `finish` fires first, then `res.end()` has been called and the close is\n  // just us finishing the stream on our side. If `close` fires first, then we\n  // know the client disconnected before we finished.\n  response.once('close', () => {\n    if (response.writableFinished) return\n\n    controller.abort(new ResponseAborted())\n  })\n\n  return controller\n}\n\n/**\n * Creates an AbortSignal tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * This cannot be done with the request (IncomingMessage or Readable) because\n * the `abort` event will not fire if to data has been fully read (because that\n * will \"close\" the readable stream and nothing fires after that).\n */\nexport function signalFromNodeResponse(response: Writable): AbortSignal {\n  const { errored, destroyed } = response\n  if (errored || destroyed) {\n    return AbortSignal.abort(errored ?? new ResponseAborted())\n  }\n\n  const { signal } = createAbortController(response)\n  return signal\n}\n\nexport class NextRequestAdapter {\n  public static fromBaseNextRequest(\n    request: BaseNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromWebNextRequest(request)\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromNodeNextRequest(request, signal)\n    } else {\n      throw new Error('Invariant: Unsupported NextRequest type')\n    }\n  }\n\n  public static fromNodeNextRequest(\n    request: NodeNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: BodyInit | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD' && request.body) {\n      // @ts-expect-error - this is handled by undici, when streams/web land use it instead\n      body = request.body\n    }\n\n    let url: URL\n    if (request.url.startsWith('http')) {\n      url = new URL(request.url)\n    } else {\n      // Grab the full URL from the request metadata.\n      const base = getRequestMeta(request, 'initURL')\n      if (!base || !base.startsWith('http')) {\n        // Because the URL construction relies on the fact that the URL provided\n        // is absolute, we need to provide a base URL. We can't use the request\n        // URL because it's relative, so we use a dummy URL instead.\n        url = new URL(request.url, 'http://n')\n      } else {\n        url = new URL(request.url, base)\n      }\n    }\n\n    return new NextRequest(url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n\n  public static fromWebNextRequest(request: WebNextRequest): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: ReadableStream | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD') {\n      body = request.body\n    }\n\n    return new NextRequest(request.url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal: request.request.signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(request.request.signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n}\n", "/**\n * A `Promise.withResolvers` implementation that exposes the `resolve` and\n * `reject` functions on a `Promise`.\n *\n * @see https://tc39.es/proposal-promise-with-resolvers/\n */\nexport class DetachedPromise<T = any> {\n  public readonly resolve: (value: T | PromiseLike<T>) => void\n  public readonly reject: (reason: any) => void\n  public readonly promise: Promise<T>\n\n  constructor() {\n    let resolve: (value: T | PromiseLike<T>) => void\n    let reject: (reason: any) => void\n\n    // Create the promise and assign the resolvers to the object.\n    this.promise = new Promise<T>((res, rej) => {\n      resolve = res\n      reject = rej\n    })\n\n    // We know that resolvers is defined because the Promise constructor runs\n    // synchronously.\n    this.resolve = resolve!\n    this.reject = reject!\n  }\n}\n", "import type { AppPageModule } from './route-modules/app-page/module'\n\n// Combined load times for loading client components\nlet clientComponentLoadStart = 0\nlet clientComponentLoadTimes = 0\nlet clientComponentLoadCount = 0\n\nexport function wrapClientComponentLoader(\n  ComponentMod: AppPageModule\n): AppPageModule['__next_app__'] {\n  if (!('performance' in globalThis)) {\n    return ComponentMod.__next_app__\n  }\n\n  return {\n    require: (...args) => {\n      const startTime = performance.now()\n\n      if (clientComponentLoadStart === 0) {\n        clientComponentLoadStart = startTime\n      }\n\n      try {\n        clientComponentLoadCount += 1\n        return ComponentMod.__next_app__.require(...args)\n      } finally {\n        clientComponentLoadTimes += performance.now() - startTime\n      }\n    },\n    loadChunk: (...args) => {\n      const startTime = performance.now()\n      const result = ComponentMod.__next_app__.loadChunk(...args)\n      // Avoid wrapping `loadChunk`'s result in an extra promise in case something like React depends on its identity.\n      // We only need to know when it's settled.\n      result.finally(() => {\n        clientComponentLoadTimes += performance.now() - startTime\n      })\n      return result\n    },\n  }\n}\n\nexport function getClientComponentLoaderMetrics(\n  options: { reset?: boolean } = {}\n) {\n  const metrics =\n    clientComponentLoadStart === 0\n      ? undefined\n      : {\n          clientComponentLoadStart,\n          clientComponentLoadTimes,\n          clientComponentLoadCount,\n        }\n\n  if (options.reset) {\n    clientComponentLoadStart = 0\n    clientComponentLoadTimes = 0\n    clientComponentLoadCount = 0\n  }\n\n  return metrics\n}\n", "import type { ServerResponse } from 'node:http'\n\nimport {\n  ResponseAbortedName,\n  createAbortController,\n} from './web/spec-extension/adapters/next-request'\nimport { DetachedPromise } from '../lib/detached-promise'\nimport { getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { getClientComponentLoaderMetrics } from './client-component-renderer-logger'\n\nexport function isAbortError(e: any): e is Error & { name: 'AbortError' } {\n  return e?.name === 'AbortError' || e?.name === ResponseAbortedName\n}\n\nfunction createWriterFromResponse(\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n): WritableStream<Uint8Array> {\n  let started = false\n\n  // Create a promise that will resolve once the response has drained. See\n  // https://nodejs.org/api/stream.html#stream_event_drain\n  let drained = new DetachedPromise<void>()\n  function onDrain() {\n    drained.resolve()\n  }\n  res.on('drain', onDrain)\n\n  // If the finish event fires, it means we shouldn't block and wait for the\n  // drain event.\n  res.once('close', () => {\n    res.off('drain', onDrain)\n    drained.resolve()\n  })\n\n  // Create a promise that will resolve once the response has finished. See\n  // https://nodejs.org/api/http.html#event-finish_1\n  const finished = new DetachedPromise<void>()\n  res.once('finish', () => {\n    finished.resolve()\n  })\n\n  // Create a writable stream that will write to the response.\n  return new WritableStream<Uint8Array>({\n    write: async (chunk) => {\n      // You'd think we'd want to use `start` instead of placing this in `write`\n      // but this ensures that we don't actually flush the headers until we've\n      // started writing chunks.\n      if (!started) {\n        started = true\n\n        if (\n          'performance' in globalThis &&\n          process.env.NEXT_OTEL_PERFORMANCE_PREFIX\n        ) {\n          const metrics = getClientComponentLoaderMetrics()\n          if (metrics) {\n            performance.measure(\n              `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,\n              {\n                start: metrics.clientComponentLoadStart,\n                end:\n                  metrics.clientComponentLoadStart +\n                  metrics.clientComponentLoadTimes,\n              }\n            )\n          }\n        }\n\n        res.flushHeaders()\n        getTracer().trace(\n          NextNodeServerSpan.startResponse,\n          {\n            spanName: 'start response',\n          },\n          () => undefined\n        )\n      }\n\n      try {\n        const ok = res.write(chunk)\n\n        // Added by the `compression` middleware, this is a function that will\n        // flush the partially-compressed response to the client.\n        if ('flush' in res && typeof res.flush === 'function') {\n          res.flush()\n        }\n\n        // If the write returns false, it means there's some backpressure, so\n        // wait until it's streamed before continuing.\n        if (!ok) {\n          await drained.promise\n\n          // Reset the drained promise so that we can wait for the next drain event.\n          drained = new DetachedPromise<void>()\n        }\n      } catch (err) {\n        res.end()\n        throw new Error('failed to write chunk to response', { cause: err })\n      }\n    },\n    abort: (err) => {\n      if (res.writableFinished) return\n\n      res.destroy(err)\n    },\n    close: async () => {\n      // if a waitUntil promise was passed, wait for it to resolve before\n      // ending the response.\n      if (waitUntilForEnd) {\n        await waitUntilForEnd\n      }\n\n      if (res.writableFinished) return\n\n      res.end()\n      return finished.promise\n    },\n  })\n}\n\nexport async function pipeToNodeResponse(\n  readable: ReadableStream<Uint8Array>,\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n) {\n  try {\n    // If the response has already errored, then just return now.\n    const { errored, destroyed } = res\n    if (errored || destroyed) return\n\n    // Create a new AbortController so that we can abort the readable if the\n    // client disconnects.\n    const controller = createAbortController(res)\n\n    const writer = createWriterFromResponse(res, waitUntilForEnd)\n\n    await readable.pipeTo(writer, { signal: controller.signal })\n  } catch (err: any) {\n    // If this isn't related to an abort error, re-throw it.\n    if (isAbortError(err)) return\n\n    throw new Error('failed to pipe response', { cause: err })\n  }\n}\n", "import type { OutgoingHttpHeaders, ServerResponse } from 'http'\nimport type { CacheControl } from './lib/cache-control'\nimport type { FetchMetrics } from './base-http'\n\nimport {\n  chainStreams,\n  streamFromBuffer,\n  streamFromString,\n  streamToBuffer,\n  streamToString,\n} from './stream-utils/node-web-streams-helper'\nimport { isAbortError, pipeToNodeResponse } from './pipe-readable'\nimport type { RenderResumeDataCache } from './resume-data-cache/resume-data-cache'\n\ntype ContentTypeOption = string | undefined\n\nexport type AppPageRenderResultMetadata = {\n  flightData?: Buffer\n  cacheControl?: CacheControl\n  staticBailoutInfo?: {\n    stack?: string\n    description?: string\n  }\n\n  /**\n   * The postponed state if the render had postponed and needs to be resumed.\n   */\n  postponed?: string\n\n  /**\n   * The headers to set on the response that were added by the render.\n   */\n  headers?: OutgoingHttpHeaders\n  fetchTags?: string\n  fetchMetrics?: FetchMetrics\n\n  segmentData?: Map<string, Buffer>\n\n  /**\n   * In development, the cache is warmed up before the render. This is attached\n   * to the metadata so that it can be used during the render.\n   */\n  devRenderResumeDataCache?: RenderResumeDataCache\n}\n\nexport type PagesRenderResultMetadata = {\n  pageData?: any\n  cacheControl?: CacheControl\n  assetQueryString?: string\n  isNotFound?: boolean\n  isRedirect?: boolean\n}\n\nexport type StaticRenderResultMetadata = {}\n\nexport type RenderResultMetadata = AppPageRenderResultMetadata &\n  PagesRenderResultMetadata &\n  StaticRenderResultMetadata\n\nexport type RenderResultResponse =\n  | ReadableStream<Uint8Array>[]\n  | ReadableStream<Uint8Array>\n  | string\n  | Buffer\n  | null\n\nexport type RenderResultOptions<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> = {\n  contentType?: ContentTypeOption\n  waitUntil?: Promise<unknown>\n  metadata: Metadata\n}\n\nexport default class RenderResult<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> {\n  /**\n   * The detected content type for the response. This is used to set the\n   * `Content-Type` header.\n   */\n  public readonly contentType: ContentTypeOption\n\n  /**\n   * The metadata for the response. This is used to set the revalidation times\n   * and other metadata.\n   */\n  public readonly metadata: Readonly<Metadata>\n\n  /**\n   * The response itself. This can be a string, a stream, or null. If it's a\n   * string, then it's a static response. If it's a stream, then it's a\n   * dynamic response. If it's null, then the response was not found or was\n   * already sent.\n   */\n  private response: RenderResultResponse\n\n  /**\n   * Creates a new RenderResult instance from a static response.\n   *\n   * @param value the static response value\n   * @returns a new RenderResult instance\n   */\n  public static fromStatic(value: string | Buffer) {\n    return new RenderResult<StaticRenderResultMetadata>(value, { metadata: {} })\n  }\n\n  private readonly waitUntil?: Promise<unknown>\n\n  constructor(\n    response: RenderResultResponse,\n    { contentType, waitUntil, metadata }: RenderResultOptions<Metadata>\n  ) {\n    this.response = response\n    this.contentType = contentType\n    this.metadata = metadata\n    this.waitUntil = waitUntil\n  }\n\n  public assignMetadata(metadata: Metadata) {\n    Object.assign(this.metadata, metadata)\n  }\n\n  /**\n   * Returns true if the response is null. It can be null if the response was\n   * not found or was already sent.\n   */\n  public get isNull(): boolean {\n    return this.response === null\n  }\n\n  /**\n   * Returns false if the response is a string. It can be a string if the page\n   * was prerendered. If it's not, then it was generated dynamically.\n   */\n  public get isDynamic(): boolean {\n    return typeof this.response !== 'string'\n  }\n\n  public toUnchunkedBuffer(stream?: false): Buffer\n  public toUnchunkedBuffer(stream: true): Promise<Buffer>\n  public toUnchunkedBuffer(stream = false): Promise<Buffer> | Buffer {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToBuffer(this.readable)\n    }\n\n    return Buffer.from(this.response)\n  }\n\n  /**\n   * Returns the response if it is a string. If the page was dynamic, this will\n   * return a promise if the `stream` option is true, or it will throw an error.\n   *\n   * @param stream Whether or not to return a promise if the response is dynamic\n   * @returns The response as a string\n   */\n  public toUnchunkedString(stream?: false): string\n  public toUnchunkedString(stream: true): Promise<string>\n  public toUnchunkedString(stream = false): Promise<string> | string {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToString(this.readable)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Returns the response if it is a stream, or throws an error if it is a\n   * string.\n   */\n  private get readable(): ReadableStream<Uint8Array> {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be streamed')\n    }\n    if (typeof this.response === 'string') {\n      throw new Error('Invariant: static responses cannot be streamed')\n    }\n\n    if (Buffer.isBuffer(this.response)) {\n      return streamFromBuffer(this.response)\n    }\n\n    // If the response is an array of streams, then chain them together.\n    if (Array.isArray(this.response)) {\n      return chainStreams(...this.response)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Chains a new stream to the response. This will convert the response to an\n   * array of streams if it is not already one and will add the new stream to\n   * the end. When this response is piped, all of the streams will be piped\n   * one after the other.\n   *\n   * @param readable The new stream to chain\n   */\n  public chain(readable: ReadableStream<Uint8Array>) {\n    if (this.response === null) {\n      throw new Error('Invariant: response is null. This is a bug in Next.js')\n    }\n\n    // If the response is not an array of streams already, make it one.\n    let responses: ReadableStream<Uint8Array>[]\n    if (typeof this.response === 'string') {\n      responses = [streamFromString(this.response)]\n    } else if (Array.isArray(this.response)) {\n      responses = this.response\n    } else if (Buffer.isBuffer(this.response)) {\n      responses = [streamFromBuffer(this.response)]\n    } else {\n      responses = [this.response]\n    }\n\n    // Add the new stream to the array.\n    responses.push(readable)\n\n    // Update the response.\n    this.response = responses\n  }\n\n  /**\n   * Pipes the response to a writable stream. This will close/cancel the\n   * writable stream if an error is encountered. If this doesn't throw, then\n   * the writable stream will be closed or aborted.\n   *\n   * @param writable Writable stream to pipe the response to\n   */\n  public async pipeTo(writable: WritableStream<Uint8Array>): Promise<void> {\n    try {\n      await this.readable.pipeTo(writable, {\n        // We want to close the writable stream ourselves so that we can wait\n        // for the waitUntil promise to resolve before closing it. If an error\n        // is encountered, we'll abort the writable stream if we swallowed the\n        // error.\n        preventClose: true,\n      })\n\n      // If there is a waitUntil promise, wait for it to resolve before\n      // closing the writable stream.\n      if (this.waitUntil) await this.waitUntil\n\n      // Close the writable stream.\n      await writable.close()\n    } catch (err) {\n      // If this is an abort error, we should abort the writable stream (as we\n      // took ownership of it when we started piping). We don't need to re-throw\n      // because we handled the error.\n      if (isAbortError(err)) {\n        // Abort the writable stream if an error is encountered.\n        await writable.abort(err)\n\n        return\n      }\n\n      // We're not aborting the writer here as when this method throws it's not\n      // clear as to how so the caller should assume it's their responsibility\n      // to clean up the writer.\n      throw err\n    }\n  }\n\n  /**\n   * Pipes the response to a node response. This will close/cancel the node\n   * response if an error is encountered.\n   *\n   * @param res\n   */\n  public async pipeToNodeResponse(res: ServerResponse) {\n    await pipeToNodeResponse(this.readable, res, this.waitUntil)\n  }\n}\n", "import React from 'react'\nimport type { ImageConfigComplete } from './image-config'\nimport { imageConfigDefault } from './image-config'\n\nexport const ImageConfigContext =\n  React.createContext<ImageConfigComplete>(imageConfigDefault)\n\nif (process.env.NODE_ENV !== 'production') {\n  ImageConfigContext.displayName = 'ImageConfigContext'\n}\n", "export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentSecurityPolicy: string\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: RemotePattern[]\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n", "import type { NextParsedUrlQuery } from './request-meta'\n\nimport { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\n\nconst INTERNAL_QUERY_NAMES = [NEXT_RSC_UNION_QUERY] as const\n\nexport function stripInternalQueries(query: NextParsedUrlQuery) {\n  for (const name of INTERNAL_QUERY_NAMES) {\n    delete query[name]\n  }\n}\n\nexport function stripInternalSearchParams<T extends string | URL>(url: T): T {\n  const isStringUrl = typeof url === 'string'\n  const instance = isStringUrl ? new URL(url) : (url as URL)\n\n  instance.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  return (isStringUrl ? instance.toString() : instance) as T\n}\n", "export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n", "'use client'\n\nimport { createContext } from 'react'\nimport type { Params } from '../../server/request/params'\n\nexport const SearchParamsContext = createContext<URLSearchParams | null>(null)\nexport const PathnameContext = createContext<string | null>(null)\nexport const PathParamsContext = createContext<Params | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  SearchParamsContext.displayName = 'SearchParamsContext'\n  PathnameContext.displayName = 'PathnameContext'\n  PathParamsContext.displayName = 'PathParamsContext'\n}\n", "// regexp is based on https://github.com/sindresorhus/escape-string-regexp\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g\n\nexport function escapeStringRegexp(str: string) {\n  // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n  if (reHasRegExp.test(str)) {\n    return str.replace(reReplaceRegExp, '\\\\$&')\n  }\n  return str\n}\n", "import {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../../../lib/constants'\nimport { INTERCEPTION_ROUTE_MARKERS } from './interception-routes'\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { removeTrailingSlash } from './remove-trailing-slash'\n\nexport interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\nexport interface RouteRegex {\n  groups: { [groupName: string]: Group }\n  re: RegExp\n}\n\ntype GetNamedRouteRegexOptions = {\n  /**\n   * Whether to prefix the route keys with the NEXT_INTERCEPTION_MARKER_PREFIX\n   * or NEXT_QUERY_PARAM_PREFIX. This is only relevant when creating the\n   * routes-manifest during the build.\n   */\n  prefixRouteKeys: boolean\n\n  /**\n   * Whether to include the suffix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n\n  /**\n   * Whether to backtrack duplicate keys. This is only relevant when creating\n   * the routes-manifest during the build.\n   */\n  backreferenceDuplicateKeys?: boolean\n}\n\ntype GetRouteRegexOptions = {\n  /**\n   * Whether to include extra parts in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   * of adding this option.\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n}\n\n/**\n * Regular expression pattern used to match route parameters.\n * Matches both single parameters and parameter groups.\n * Examples:\n *   - `[[...slug]]` matches parameter group with key 'slug', repeat: true, optional: true\n *   - `[...slug]` matches parameter group with key 'slug', repeat: true, optional: false\n *   - `[[foo]]` matches parameter with key 'foo', repeat: false, optional: true\n *   - `[bar]` matches parameter with key 'bar', repeat: false, optional: false\n */\nconst PARAMETER_PATTERN = /^([^[]*)\\[((?:\\[[^\\]]*\\])|[^\\]]+)\\](.*)$/\n\n/**\n * Parses a given parameter from a route to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[[...slug]]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[[foo]]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `[bar]` -> `{ key: 'bar', repeat: false, optional: false }`\n *   - `fizz` -> `{ key: 'fizz', repeat: false, optional: false }`\n * @param param - The parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nexport function parseParameter(param: string) {\n  const match = param.match(PARAMETER_PATTERN)\n\n  if (!match) {\n    return parseMatchedParameter(param)\n  }\n\n  return parseMatchedParameter(match[2])\n}\n\n/**\n * Parses a matched parameter from the PARAMETER_PATTERN regex to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `...slug` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[foo]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `bar` -> `{ key: 'bar', repeat: false, optional: false }`\n * @param param - The matched parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nfunction parseMatchedParameter(param: string) {\n  const optional = param.startsWith('[') && param.endsWith(']')\n  if (optional) {\n    param = param.slice(1, -1)\n  }\n  const repeat = param.startsWith('...')\n  if (repeat) {\n    param = param.slice(3)\n  }\n  return { key: param, repeat, optional }\n}\n\nfunction getParametrizedRoute(\n  route: string,\n  includeSuffix: boolean,\n  includePrefix: boolean\n) {\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const markerMatch = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n      segment.startsWith(m)\n    )\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (markerMatch && paramMatches && paramMatches[2]) {\n      const { key, optional, repeat } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n      segments.push(`/${escapeStringRegexp(markerMatch)}([^/]+?)`)\n    } else if (paramMatches && paramMatches[2]) {\n      const { key, repeat, optional } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    parameterizedRoute: segments.join(''),\n    groups,\n  }\n}\n\n/**\n * From a normalized route this function generates a regular expression and\n * a corresponding groups object intended to be used to store matching groups\n * from the regular expression.\n */\nexport function getRouteRegex(\n  normalizedRoute: string,\n  {\n    includeSuffix = false,\n    includePrefix = false,\n    excludeOptionalTrailingSlash = false,\n  }: GetRouteRegexOptions = {}\n): RouteRegex {\n  const { parameterizedRoute, groups } = getParametrizedRoute(\n    normalizedRoute,\n    includeSuffix,\n    includePrefix\n  )\n\n  let re = parameterizedRoute\n  if (!excludeOptionalTrailingSlash) {\n    re += '(?:/)?'\n  }\n\n  return {\n    re: new RegExp(`^${re}$`),\n    groups: groups,\n  }\n}\n\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */\nfunction buildGetSafeRouteKey() {\n  let i = 0\n\n  return () => {\n    let routeKey = ''\n    let j = ++i\n    while (j > 0) {\n      routeKey += String.fromCharCode(97 + ((j - 1) % 26))\n      j = Math.floor((j - 1) / 26)\n    }\n    return routeKey\n  }\n}\n\nfunction getSafeKeyFromSegment({\n  interceptionMarker,\n  getSafeRouteKey,\n  segment,\n  routeKeys,\n  keyPrefix,\n  backreferenceDuplicateKeys,\n}: {\n  interceptionMarker?: string\n  getSafeRouteKey: () => string\n  segment: string\n  routeKeys: Record<string, string>\n  keyPrefix?: string\n  backreferenceDuplicateKeys: boolean\n}) {\n  const { key, optional, repeat } = parseMatchedParameter(segment)\n\n  // replace any non-word characters since they can break\n  // the named regex\n  let cleanedKey = key.replace(/\\W/g, '')\n\n  if (keyPrefix) {\n    cleanedKey = `${keyPrefix}${cleanedKey}`\n  }\n  let invalidKey = false\n\n  // check if the key is still invalid and fallback to using a known\n  // safe key\n  if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n    invalidKey = true\n  }\n  if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n    invalidKey = true\n  }\n\n  if (invalidKey) {\n    cleanedKey = getSafeRouteKey()\n  }\n\n  const duplicateKey = cleanedKey in routeKeys\n\n  if (keyPrefix) {\n    routeKeys[cleanedKey] = `${keyPrefix}${key}`\n  } else {\n    routeKeys[cleanedKey] = key\n  }\n\n  // if the segment has an interception marker, make sure that's part of the regex pattern\n  // this is to ensure that the route with the interception marker doesn't incorrectly match\n  // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n  const interceptionPrefix = interceptionMarker\n    ? escapeStringRegexp(interceptionMarker)\n    : ''\n\n  let pattern: string\n  if (duplicateKey && backreferenceDuplicateKeys) {\n    // Use a backreference to the key to ensure that the key is the same value\n    // in each of the placeholders.\n    pattern = `\\\\k<${cleanedKey}>`\n  } else if (repeat) {\n    pattern = `(?<${cleanedKey}>.+?)`\n  } else {\n    pattern = `(?<${cleanedKey}>[^/]+?)`\n  }\n\n  return optional\n    ? `(?:/${interceptionPrefix}${pattern})?`\n    : `/${interceptionPrefix}${pattern}`\n}\n\nfunction getNamedParametrizedRoute(\n  route: string,\n  prefixRouteKeys: boolean,\n  includeSuffix: boolean,\n  includePrefix: boolean,\n  backreferenceDuplicateKeys: boolean\n) {\n  const getSafeRouteKey = buildGetSafeRouteKey()\n  const routeKeys: { [named: string]: string } = {}\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const hasInterceptionMarker = INTERCEPTION_ROUTE_MARKERS.some((m) =>\n      segment.startsWith(m)\n    )\n\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (hasInterceptionMarker && paramMatches && paramMatches[2]) {\n      // If there's an interception marker, add it to the segments.\n      segments.push(\n        getSafeKeyFromSegment({\n          getSafeRouteKey,\n          interceptionMarker: paramMatches[1],\n          segment: paramMatches[2],\n          routeKeys,\n          keyPrefix: prefixRouteKeys\n            ? NEXT_INTERCEPTION_MARKER_PREFIX\n            : undefined,\n          backreferenceDuplicateKeys,\n        })\n      )\n    } else if (paramMatches && paramMatches[2]) {\n      // If there's a prefix, add it to the segments if it's enabled.\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = getSafeKeyFromSegment({\n        getSafeRouteKey,\n        segment: paramMatches[2],\n        routeKeys,\n        keyPrefix: prefixRouteKeys ? NEXT_QUERY_PARAM_PREFIX : undefined,\n        backreferenceDuplicateKeys,\n      })\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    namedParameterizedRoute: segments.join(''),\n    routeKeys,\n  }\n}\n\n/**\n * This function extends `getRouteRegex` generating also a named regexp where\n * each group is named along with a routeKeys object that indexes the assigned\n * named group with its corresponding key. When the routeKeys need to be\n * prefixed to uniquely identify internally the \"prefixRouteKey\" arg should\n * be \"true\" currently this is only the case when creating the routes-manifest\n * during the build\n */\nexport function getNamedRouteRegex(\n  normalizedRoute: string,\n  options: GetNamedRouteRegexOptions\n) {\n  const result = getNamedParametrizedRoute(\n    normalizedRoute,\n    options.prefixRouteKeys,\n    options.includeSuffix ?? false,\n    options.includePrefix ?? false,\n    options.backreferenceDuplicateKeys ?? false\n  )\n\n  let namedRegex = result.namedParameterizedRoute\n  if (!options.excludeOptionalTrailingSlash) {\n    namedRegex += '(?:/)?'\n  }\n\n  return {\n    ...getRouteRegex(normalizedRoute, options),\n    namedRegex: `^${namedRegex}$`,\n    routeKeys: result.routeKeys,\n  }\n}\n\n/**\n * Generates a named regexp.\n * This is intended to be using for build time only.\n */\nexport function getNamedMiddlewareRegex(\n  normalizedRoute: string,\n  options: {\n    catchAll?: boolean\n  }\n) {\n  const { parameterizedRoute } = getParametrizedRoute(\n    normalizedRoute,\n    false,\n    false\n  )\n  const { catchAll = true } = options\n  if (parameterizedRoute === '/') {\n    let catchAllRegex = catchAll ? '.*' : ''\n    return {\n      namedRegex: `^/${catchAllRegex}$`,\n    }\n  }\n\n  const { namedParameterizedRoute } = getNamedParametrizedRoute(\n    normalizedRoute,\n    false,\n    false,\n    false,\n    false\n  )\n  let catchAllGroupedRegex = catchAll ? '(?:(/.*)?)' : ''\n  return {\n    namedRegex: `^${namedParameterizedRoute}${catchAllGroupedRegex}$`,\n  }\n}\n", "import type { AppRouterInstance } from '../app-router-context.shared-runtime'\nimport type { Params } from '../../../server/request/params'\nimport type { NextRouter } from './router'\n\nimport React, { useMemo, useRef } from 'react'\nimport { PathnameContext } from '../hooks-client-context.shared-runtime'\nimport { isDynamicRoute } from './utils'\nimport { asPathToSearchParams } from './utils/as-path-to-search-params'\nimport { getRouteRegex } from './utils/route-regex'\n\n/** It adapts a Pages Router (`NextRouter`) to the App Router Instance. */\nexport function adaptForAppRouterInstance(\n  pagesRouter: NextRouter\n): AppRouterInstance {\n  return {\n    back() {\n      pagesRouter.back()\n    },\n    forward() {\n      pagesRouter.forward()\n    },\n    refresh() {\n      pagesRouter.reload()\n    },\n    hmrRefresh() {},\n    push(href, { scroll } = {}) {\n      void pagesRouter.push(href, undefined, { scroll })\n    },\n    replace(href, { scroll } = {}) {\n      void pagesRouter.replace(href, undefined, { scroll })\n    },\n    prefetch(href) {\n      void pagesRouter.prefetch(href)\n    },\n  }\n}\n\n/**\n * adaptForSearchParams transforms the ParsedURLQuery into URLSearchParams.\n *\n * @param router the router that contains the query.\n * @returns the search params in the URLSearchParams format\n */\nexport function adaptForSearchParams(\n  router: Pick<NextRouter, 'isReady' | 'query' | 'asPath'>\n): URLSearchParams {\n  if (!router.isReady || !router.query) {\n    return new URLSearchParams()\n  }\n\n  return asPathToSearchParams(router.asPath)\n}\n\nexport function adaptForPathParams(\n  router: Pick<NextRouter, 'isReady' | 'pathname' | 'query' | 'asPath'>\n): Params | null {\n  if (!router.isReady || !router.query) {\n    return null\n  }\n  const pathParams: Params = {}\n  const routeRegex = getRouteRegex(router.pathname)\n  const keys = Object.keys(routeRegex.groups)\n  for (const key of keys) {\n    pathParams[key] = router.query[key]!\n  }\n  return pathParams\n}\n\nexport function PathnameContextProviderAdapter({\n  children,\n  router,\n  ...props\n}: React.PropsWithChildren<{\n  router: Pick<NextRouter, 'pathname' | 'asPath' | 'isReady' | 'isFallback'>\n  isAutoExport: boolean\n}>) {\n  const ref = useRef(props.isAutoExport)\n  const value = useMemo(() => {\n    // isAutoExport is only ever `true` on the first render from the server,\n    // so reset it to `false` after we read it for the first time as `true`. If\n    // we don't use the value, then we don't need it.\n    const isAutoExport = ref.current\n    if (isAutoExport) {\n      ref.current = false\n    }\n\n    // When the route is a dynamic route, we need to do more processing to\n    // determine if we need to stop showing the pathname.\n    if (isDynamicRoute(router.pathname)) {\n      // When the router is rendering the fallback page, it can't possibly know\n      // the path, so return `null` here. Read more about fallback pages over\n      // at:\n      // https://nextjs.org/docs/api-reference/data-fetching/get-static-paths#fallback-pages\n      if (router.isFallback) {\n        return null\n      }\n\n      // When `isAutoExport` is true, meaning this is a page page has been\n      // automatically statically optimized, and the router is not ready, then\n      // we can't know the pathname yet. Read more about automatic static\n      // optimization at:\n      // https://nextjs.org/docs/advanced-features/automatic-static-optimization\n      if (isAutoExport && !router.isReady) {\n        return null\n      }\n    }\n\n    // The `router.asPath` contains the pathname seen by the browser (including\n    // any query strings), so it should have that stripped. Read more about the\n    // `asPath` option over at:\n    // https://nextjs.org/docs/api-reference/next/router#router-object\n    let url: URL\n    try {\n      url = new URL(router.asPath, 'http://f')\n    } catch (_) {\n      // fallback to / for invalid asPath values e.g. //\n      return '/'\n    }\n\n    return url.pathname\n  }, [router.asPath, router.isFallback, router.isReady, router.pathname])\n\n  return (\n    <PathnameContext.Provider value={value}>\n      {children}\n    </PathnameContext.Provider>\n  )\n}\n", "'use client'\n\nimport type { FetchServerResponseResult } from '../../client/components/router-reducer/fetch-server-response'\nimport type {\n  FocusAndScrollRef,\n  PrefetchKind,\n  RouterChangeByServerResponse,\n} from '../../client/components/router-reducer/router-reducer-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport React from 'react'\n\nexport type ChildSegmentMap = Map<string, CacheNode>\n\n/**\n * Cache node used in app-router / layout-router.\n */\nexport type CacheNode = ReadyCacheNode | LazyCacheNode\n\nexport type LoadingModuleData =\n  | [React.JSX.Element, React.ReactNode, React.ReactNode]\n  | null\n\n/** viewport metadata node */\nexport type HeadData = React.ReactNode\n\nexport type LazyCacheNode = {\n  /**\n   * When rsc is null, this is a lazily-initialized cache node.\n   *\n   * If the app attempts to render it, it triggers a lazy data fetch,\n   * postpones the render, and schedules an update to a new tree.\n   *\n   * TODO: This mechanism should not be used when PPR is enabled, though it\n   * currently is in some cases until we've implemented partial\n   * segment fetching.\n   */\n  rsc: null\n\n  /**\n   * A prefetched version of the segment data. See explanation in corresponding\n   * field of ReadyCacheNode (below).\n   *\n   * Since LazyCacheNode mostly only exists in the non-PPR implementation, this\n   * will usually be null, but it could have been cloned from a previous\n   * CacheNode that was created by the PPR implementation. Eventually we want\n   * to migrate everything away from LazyCacheNode entirely.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * A pending response for the lazy data fetch. If this is not present\n   * during render, it is lazily created.\n   */\n  lazyData: Promise<FetchServerResponseResult> | null\n\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  /**\n   * Child parallel routes.\n   */\n  parallelRoutes: Map<string, ChildSegmentMap>\n}\n\nexport type ReadyCacheNode = {\n  /**\n   * When rsc is not null, it represents the RSC data for the\n   * corresponding segment.\n   *\n   * `null` is a valid React Node but because segment data is always a\n   * <LayoutRouter> component, we can use `null` to represent empty.\n   *\n   * TODO: For additional type safety, update this type to\n   * Exclude<React.ReactNode, null>. Need to update createEmptyCacheNode to\n   * accept rsc as an argument, or just inline the callers.\n   */\n  rsc: React.ReactNode\n\n  /**\n   * Represents a static version of the segment that can be shown immediately,\n   * and may or may not contain dynamic holes. It's prefetched before a\n   * navigation occurs.\n   *\n   * During rendering, we will choose whether to render `rsc` or `prefetchRsc`\n   * with `useDeferredValue`. As with the `rsc` field, a value of `null` means\n   * no value was provided. In this case, the LayoutRouter will go straight to\n   * rendering the `rsc` value; if that one is also missing, it will suspend and\n   * trigger a lazy fetch.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * There should never be a lazy data request in this case.\n   */\n  lazyData: null\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  parallelRoutes: Map<string, ChildSegmentMap>\n}\n\nexport interface NavigateOptions {\n  scroll?: boolean\n}\n\nexport interface PrefetchOptions {\n  kind: PrefetchKind\n}\n\nexport interface AppRouterInstance {\n  /**\n   * Navigate to the previous history entry.\n   */\n  back(): void\n  /**\n   * Navigate to the next history entry.\n   */\n  forward(): void\n  /**\n   * Refresh the current page.\n   */\n  refresh(): void\n  /**\n   * Refresh the current page. Use in development only.\n   * @internal\n   */\n  hmrRefresh(): void\n  /**\n   * Navigate to the provided href.\n   * Pushes a new history entry.\n   */\n  push(href: string, options?: NavigateOptions): void\n  /**\n   * Navigate to the provided href.\n   * Replaces the current history entry.\n   */\n  replace(href: string, options?: NavigateOptions): void\n  /**\n   * Prefetch the provided href.\n   */\n  prefetch(href: string, options?: PrefetchOptions): void\n}\n\nexport const AppRouterContext = React.createContext<AppRouterInstance | null>(\n  null\n)\nexport const LayoutRouterContext = React.createContext<{\n  parentTree: FlightRouterState\n  parentCacheNode: CacheNode\n  parentSegmentPath: FlightSegmentPath | null\n  url: string\n} | null>(null)\n\nexport const GlobalLayoutRouterContext = React.createContext<{\n  tree: FlightRouterState\n  changeByServerResponse: RouterChangeByServerResponse\n  focusAndScrollRef: FocusAndScrollRef\n  nextUrl: string | null\n}>(null as any)\n\nexport const TemplateContext = React.createContext<React.ReactNode>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  AppRouterContext.displayName = 'AppRouterContext'\n  LayoutRouterContext.displayName = 'LayoutRouterContext'\n  GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext'\n  TemplateContext.displayName = 'TemplateContext'\n}\n\nexport const MissingSlotContext = React.createContext<Set<string>>(new Set())\n", "const symbolError = Symbol.for('NextjsError')\n\nexport function getErrorSource(error: Error): 'server' | 'edge-server' | null {\n  return (error as any)[symbolError] || null\n}\n\nexport type ErrorSourceType = 'edge-server' | 'server'\n\nexport function decorateServerError(error: Error, type: ErrorSourceType) {\n  Object.defineProperty(error, symbolError, {\n    writable: false,\n    enumerable: false,\n    configurable: false,\n    value: type,\n  })\n}\n", "import type { NextApiRequestCookies } from '.'\n\n/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */\n\nexport function getCookieParser(headers: {\n  [key: string]: string | string[] | null | undefined\n}): () => NextApiRequestCookies {\n  return function parseCookie(): NextApiRequestCookies {\n    const { cookie } = headers\n\n    if (!cookie) {\n      return {}\n    }\n\n    const { parse: parseCookieFn } = require('next/dist/compiled/cookie')\n    return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie)\n  }\n}\n", "import { CACHE_ONE_YEAR } from '../../lib/constants'\n\n/**\n * The revalidate option used internally for pages. A value of `false` means\n * that the page should not be revalidated. A number means that the page\n * should be revalidated after the given number of seconds (this also includes\n * `1` which means to revalidate after 1 second). A value of `0` is not a valid\n * value for this option.\n */\nexport type Revalidate = number | false\n\nexport interface CacheControl {\n  revalidate: Revalidate\n  expire: number | undefined\n}\n\nexport function getCacheControlHeader({\n  revalidate,\n  expire,\n}: CacheControl): string {\n  const swrHeader =\n    typeof revalidate === 'number' &&\n    expire !== undefined &&\n    revalidate < expire\n      ? `, stale-while-revalidate=${expire - revalidate}`\n      : ''\n\n  if (revalidate === 0) {\n    return 'private, no-cache, no-store, max-age=0, must-revalidate'\n  } else if (typeof revalidate === 'number') {\n    return `s-maxage=${revalidate}${swrHeader}`\n  }\n\n  return `s-maxage=${CACHE_ONE_YEAR}${swrHeader}`\n}\n", "export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (\n        process.env.NODE_ENV !== 'development' &&\n        process.env.__NEXT_OPTIMIZE_FONTS &&\n        !inAmpMode\n      ) {\n        if (\n          c.type === 'link' &&\n          c.props['href'] &&\n          // TODO(prateekbh@): Replace this with const from `constants` when the tree shaking works.\n          ['https://fonts.googleapis.com/css', 'https://use.typekit.net/'].some(\n            (url) => c.props['href'].startsWith(url)\n          )\n        ) {\n          const newProps = { ...(c.props || {}) }\n          newProps['data-href'] = newProps['href']\n          newProps['href'] = undefined\n\n          // Add this attribute to make it easy to identify optimized tags\n          newProps['data-optimized-fonts'] = true\n\n          return React.cloneElement(c, newProps)\n        }\n      }\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n", "// Convert router.asPath to a URLSearchParams object\n// example: /dynamic/[slug]?foo=bar -> { foo: 'bar' }\nexport function asPathToSearchParams(asPath: string): URLSearchParams {\n  return new URL(asPath, 'http://n').searchParams\n}\n", "import { isPlainObject } from '../shared/lib/is-plain-object'\n\n// We allow some additional attached properties for Next.js errors\nexport interface NextError extends Error {\n  type?: string\n  page?: string\n  code?: string | number\n  cancelled?: boolean\n  digest?: number\n}\n\n/**\n * Checks whether the given value is a NextError.\n * This can be used to print a more detailed error message with properties like `code` & `digest`.\n */\nexport default function isError(err: unknown): err is NextError {\n  return (\n    typeof err === 'object' && err !== null && 'name' in err && 'message' in err\n  )\n}\n\nfunction safeStringify(obj: any) {\n  const seen = new WeakSet()\n\n  return JSON.stringify(obj, (_key, value) => {\n    // If value is an object and already seen, replace with \"[Circular]\"\n    if (typeof value === 'object' && value !== null) {\n      if (seen.has(value)) {\n        return '[Circular]'\n      }\n      seen.add(value)\n    }\n    return value\n  })\n}\n\nexport function getProperError(err: unknown): Error {\n  if (isError(err)) {\n    return err\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // provide better error for case where `throw undefined`\n    // is called in development\n    if (typeof err === 'undefined') {\n      return new Error(\n        'An undefined error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n\n    if (err === null) {\n      return new Error(\n        'A null error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n  }\n\n  return new Error(isPlainObject(err) ? safeStringify(err) : err + '')\n}\n", "import { isDynamicRoute } from '../router/utils'\nimport { normalizePathSep } from './normalize-path-sep'\n\n/**\n * Performs the opposite transformation of `normalizePagePath`. Note that\n * this function is not idempotent either in cases where there are multiple\n * leading `/index` for the page. Examples:\n *  - `/index` -> `/`\n *  - `/index/foo` -> `/foo`\n *  - `/index/index` -> `/index`\n */\nexport function denormalizePagePath(page: string) {\n  let _page = normalizePathSep(page)\n  return _page.startsWith('/index/') && !isDynamicRoute(_page)\n    ? _page.slice(6)\n    : _page !== '/index'\n      ? _page\n      : '/'\n}\n", "/**\n * For a given page path, this function ensures that there is no backslash\n * escaping slashes in the path. Example:\n *  - `foo\\/bar\\/baz` -> `foo/bar/baz`\n */\nexport function normalizePathSep(path: string): string {\n  return path.replace(/\\\\/g, '/')\n}\n", "import { ensureLeadingSlash } from './ensure-leading-slash'\nimport { isDynamicRoute } from '../router/utils'\nimport { NormalizeError } from '../utils'\n\n/**\n * Takes a page and transforms it into its file counterpart ensuring that the\n * output is normalized. Note this function is not idempotent because a page\n * `/index` can be referencing `/index/index.js` and `/index/index` could be\n * referencing `/index/index/index.js`. Examples:\n *  - `/` -> `/index`\n *  - `/index/foo` -> `/index/index/foo`\n *  - `/index` -> `/index/index`\n */\nexport function normalizePagePath(page: string): string {\n  const normalized =\n    /^\\/index(\\/|$)/.test(page) && !isDynamicRoute(page)\n      ? `/index${page}`\n      : page === '/'\n        ? '/index'\n        : ensureLeadingSlash(page)\n\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    const { posix } = require('path')\n    const resolvedPage = posix.normalize(normalized)\n    if (resolvedPage !== normalized) {\n      throw new NormalizeError(\n        `Requested and resolved page mismatch: ${normalized} ${resolvedPage}`\n      )\n    }\n  }\n\n  return normalized\n}\n", "'use client'\n\nimport React, { useContext } from 'react'\n\nexport type ServerInsertedHTMLHook = (callbacks: () => React.ReactNode) => void\n\n// Use `React.createContext` to avoid errors from the RSC checks because\n// it can't be imported directly in Server Components:\n//\n//   import { createContext } from 'react'\n//\n// More info: https://github.com/vercel/next.js/pull/40686\nexport const ServerInsertedHTMLContext =\n  React.createContext<ServerInsertedHTMLHook | null>(null as any)\n\nexport function useServerInsertedHTML(callback: () => React.ReactNode): void {\n  const addInsertedServerHTMLCallback = useContext(ServerInsertedHTMLContext)\n  // Should have no effects on client where there's no flush effects provider\n  if (addInsertedServerHTMLCallback) {\n    addInsertedServerHTMLCallback(callback)\n  }\n}\n", "import type { IncomingMessage, ServerResponse } from 'http'\nimport type {\n  GetServerSideProps,\n  GetStaticPaths,\n  GetStaticProps,\n  NextComponentType,\n  PageConfig,\n} from '../../../types'\nimport type { PagesRouteDefinition } from '../../route-definitions/pages-route-definition'\nimport type { NextParsedUrlQuery } from '../../request-meta'\nimport type {\n  PagesRenderContext,\n  PagesSharedContext,\n  RenderOpts,\n} from '../../render'\nimport type RenderResult from '../../render-result'\nimport type { AppType, DocumentType } from '../../../shared/lib/utils'\n\nimport {\n  RouteModule,\n  type RouteModuleHandleContext,\n  type RouteModuleOptions,\n} from '../route-module'\nimport { renderToHTMLImpl, renderToHTML } from '../../render'\nimport * as vendoredContexts from './vendored/contexts/entrypoints'\n\n/**\n * The PagesModule is the type of the module exported by the bundled pages\n * module.\n */\nexport type PagesModule = typeof import('../../../build/templates/pages')\n\n/**\n * The userland module for a page. This is the module that is exported from the\n * page file that contains the page component, page config, and any page data\n * fetching functions.\n */\nexport type PagesUserlandModule = {\n  /**\n   * The exported page component.\n   */\n  readonly default: NextComponentType\n\n  /**\n   * The exported page config.\n   */\n  readonly config?: PageConfig\n\n  /**\n   * The exported `getStaticProps` function.\n   */\n  readonly getStaticProps?: GetStaticProps\n\n  /**\n   * The exported `getStaticPaths` function.\n   */\n  readonly getStaticPaths?: GetStaticPaths\n\n  /**\n   * The exported `getServerSideProps` function.\n   */\n  readonly getServerSideProps?: GetServerSideProps\n}\n\n/**\n * The components that are used to render a page. These aren't tied to the\n * specific page being rendered, but rather are the components that are used to\n * render all pages.\n */\ntype PagesComponents = {\n  /**\n   * The `App` component. This could be exported by a user's custom `_app` page\n   * file, or it could be the default `App` component.\n   */\n  readonly App: AppType\n\n  /**\n   * The `Document` component. This could be exported by a user's custom\n   * `_document` page file, or it could be the default `Document` component.\n   */\n  readonly Document: DocumentType\n}\n\nexport interface PagesRouteModuleOptions\n  extends RouteModuleOptions<PagesRouteDefinition, PagesUserlandModule> {\n  readonly components: PagesComponents\n}\n\n/**\n * AppRouteRouteHandlerContext is the context that is passed to the route\n * handler for app routes.\n */\nexport interface PagesRouteHandlerContext extends RouteModuleHandleContext {\n  /**\n   * The page for the given route.\n   */\n  page: string\n\n  /**\n   * The parsed URL query for the given request.\n   */\n  query: NextParsedUrlQuery\n\n  /**\n   * The shared context used for all page renders.\n   */\n  sharedContext: PagesSharedContext\n\n  /**\n   * The context for the given request.\n   */\n  renderContext: PagesRenderContext\n\n  /**\n   * The arguments for the given request.\n  /**\n   * The RenderOpts for the given request which include the specific modules to\n   * use for rendering.\n   */\n  // TODO: (wyattjoh) break this out into smaller parts, it currently includes the userland components\n  renderOpts: Omit<RenderOpts, 'Document' | 'App'>\n}\n\nexport class PagesRouteModule extends RouteModule<\n  PagesRouteDefinition,\n  PagesUserlandModule\n> {\n  private readonly components: PagesComponents\n\n  constructor(options: PagesRouteModuleOptions) {\n    super(options)\n\n    this.components = options.components\n  }\n\n  public render(\n    req: IncomingMessage,\n    res: ServerResponse,\n    context: PagesRouteHandlerContext\n  ): Promise<RenderResult> {\n    return renderToHTMLImpl(\n      req,\n      res,\n      context.page,\n      context.query,\n      context.renderOpts,\n      {\n        App: this.components.App,\n        Document: this.components.Document,\n      },\n      context.sharedContext,\n      context.renderContext\n    )\n  }\n}\n\nconst vendored = {\n  contexts: vendoredContexts,\n}\n\n// needed for the static build\nexport { renderToHTML, vendored }\n\nexport default PagesRouteModule\n"], "names": ["__defProp", "Object", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__hasOwnProp", "prototype", "hasOwnProperty", "src_exports", "string<PERSON><PERSON><PERSON><PERSON>", "c", "_a", "attrs", "path", "expires", "Date", "toUTCString", "maxAge", "domain", "secure", "httpOnly", "sameSite", "partitioned", "priority", "filter", "Boolean", "stringified", "name", "encodeURIComponent", "value", "length", "join", "parse<PERSON><PERSON><PERSON>", "cookie", "map", "Map", "pair", "split", "splitAt", "indexOf", "set", "key", "slice", "decodeURIComponent", "parseSetCookie", "<PERSON><PERSON><PERSON><PERSON>", "string", "attributes", "httponly", "maxage", "samesite", "fromEntries", "value2", "toLowerCase", "replace", "compact", "t", "newT", "Number", "SAME_SITE", "includes", "PRIORITY", "__export", "target", "all", "get", "enumerable", "RequestCookies", "ResponseCookies", "module", "exports", "to", "from", "except", "desc", "call", "constructor", "requestHeaders", "_parsed", "_headers", "header", "Symbol", "iterator", "size", "args", "getAll", "Array", "_", "n", "has", "delete", "names", "result", "isArray", "clear", "keys", "for", "JSON", "stringify", "toString", "values", "v", "responseHeaders", "_b", "_c", "getSetCookie", "cookieString", "splitCookiesString", "cookiesString", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "cookiesStrings", "pos", "skipWhitespace", "test", "char<PERSON>t", "push", "substring", "parsed", "normalizeCookie", "now", "bag", "headers", "serialized", "append", "options", "__nccwpck_require__", "ab", "__dirname", "e", "r", "parse", "o", "a", "s", "decode", "i", "p", "f", "u", "substr", "trim", "undefined", "tryDecode", "serialize", "encode", "isNaN", "isFinite", "Math", "floor", "REACT_ELEMENT_TYPE", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_CONSUMER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_OFFSCREEN_TYPE", "REACT_VIEW_TRANSITION_TYPE", "REACT_CLIENT_REFERENCE", "typeOf", "object", "$$typeof", "type", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Fragment", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "SuspenseList", "isContextConsumer", "isContextProvider", "isElement", "isForwardRef", "isFragment", "isLazy", "isMemo", "isPortal", "isProfiler", "isStrictMode", "isSuspense", "isSuspenseList", "isValidElementType", "getModuleId", "<PERSON><PERSON><PERSON><PERSON>", "globalThis", "env", "stdout", "process", "enabled", "NO_COLOR", "FORCE_COLOR", "isTTY", "CI", "TERM", "replaceClose", "str", "close", "index", "end", "nextIndex", "formatter", "open", "input", "String", "bold", "red", "green", "yellow", "magenta", "white", "prefixes", "wait", "error", "warn", "ready", "info", "event", "trace", "LOGGING_METHOD", "log", "message", "prefixedLog", "prefixType", "shift", "consoleMethod", "prefix", "console", "maxSize", "calculateSize", "cache", "sizes", "totalSize", "touch", "evictIfNecessary", "evictLeastRecentlyUsed", "lruKey", "next", "lruSize", "reset", "remove", "currentSize", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "CACHE_ONE_YEAR", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "GSP_NO_RETURNED_VALUE", "GSSP_NO_RETURNED_VALUE", "UNSTABLE_REVALIDATE_RENAME_ERROR", "GSSP_COMPONENT_MEMBER_ERROR", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "checkIsOnDemandRevalidate", "req", "previewProps", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isOnDemandRevalidate", "previewModeId", "revalidateOnlyGenerated", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "SYMBOL_PREVIEW_DATA", "SYMBOL_CLEARED_COOKIES", "clearPreviewData", "res", "require", "previous", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setLazyProp", "prop", "getter", "opts", "configurable", "optsReset", "writable", "tryGetPreviewData", "multiZoneDraftMode", "cookies", "encryptedPreviewData", "tokenPreviewData", "data", "jsonwebtoken", "verify", "previewModeSigningKey", "decryptWithSecret", "decryptedPreviewData", "<PERSON><PERSON><PERSON>", "previewModeEncryptionKey", "CIPHER_ALGORITHM", "encryptWithSecret", "secret", "iv", "crypto", "salt", "cipher", "encrypted", "concat", "update", "final", "tag", "getAuthTag", "encryptedData", "buffer", "CIPHER_SALT_LENGTH", "decipher", "setAuthTag", "BaseServerSpan", "LoadComponentsSpan", "NextServerSpan", "NextNodeServerSpan", "StartServerSpan", "RenderSpan", "AppRenderSpan", "RouterSpan", "NodeSpan", "AppRouteRouteHandlersSpan", "ResolveMetadataSpan", "MiddlewareSpan", "optimize", "html", "config", "AmpOptimizer", "optimizer", "create", "transformHtml", "nonNullable", "postProcessHTML", "pathname", "content", "renderOpts", "inAmpMode", "hybridAmp", "postProcessor", "optimizeAmp", "ampOptimizerConfig", "ampSkipValidation", "ampValidator", "optimizeCss", "cssOptimizer", "ssrMode", "reduceInlineStyles", "distDir", "publicPath", "assetPrefix", "preload", "fonts", "logLevel", "CRITTERS_LOG_LEVEL", "ReadonlyHeadersError", "Error", "callable", "Headers", "Proxy", "receiver", "ReflectAdapter", "lowercased", "original", "find", "deleteProperty", "seal", "existing", "merge", "for<PERSON>ach", "callbackfn", "thisArg", "entries", "Reflect", "bind", "ReactDOMServer", "code", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "__esModule", "d", "definition", "obj", "toStringTag", "RouteModule", "userland", "COMPILER_NAMES", "client", "server", "edgeServer", "STATIC_STATUS_PAGES", "getObjectClassLabel", "isPlainObject", "getPrototypeOf", "regexpPlainIdentifier", "SerializableError", "page", "method", "isSerializableProps", "visit", "visited", "isSerializable", "refs", "every", "nestedV<PERSON>ue", "nextPath", "newRefs", "AmpStateContext", "React", "HeadManagerContext", "LoadableContext", "ALL_INITIALIZERS", "READY_INITIALIZERS", "load", "loader", "promise", "state", "loading", "loaded", "then", "catch", "err", "LoadableSubscription", "_res", "retry", "_clearTimeouts", "_loadFn", "_opts", "_state", "past<PERSON>elay", "timedOut", "delay", "_delay", "setTimeout", "_update", "timeout", "_timeout", "partial", "_callbacks", "callback", "clearTimeout", "getCurrentValue", "subscribe", "add", "loadFn", "Set", "Loadable", "createLoadableComponent", "assign", "webpack", "modules", "subscription", "init", "sub", "LoadableComponent", "props", "ref", "useLoadableModule", "context", "moduleName", "isLoading", "default", "displayName", "flushInitializers", "initializers", "ids", "promises", "pop", "Promise", "preloadAll", "resolveInitializers", "reject", "preloadReady", "resolvePreload", "RouterContext", "ensureLeadingSlash", "startsWith", "INTERCEPTION_ROUTE_MARKERS", "TEST_ROUTE", "TEST_STRICT_ROUTE", "isDynamicRoute", "route", "strict", "segment", "m", "extractInterceptionRouteInformation", "interceptingRoute", "marker", "interceptedRoute", "reduce", "segments", "endsWith", "splitInterceptingRoute", "getDisplayName", "Component", "isResSent", "finished", "headersSent", "loadGetInitialProps", "App", "ctx", "getInitialProps", "pageProps", "SP", "performance", "NormalizeError", "HtmlContext", "createContext", "useHtmlContext", "useContext", "NEXT_REQUEST_META", "getRequestMeta", "meta", "RedirectStatusCode", "allowedStatusCodes", "getRedirectStatus", "statusCode", "permanent", "PermanentRedirect", "TemporaryRedirect", "voidCatch", "Uint8Array", "encoder", "TextEncoder", "streamFromBuffer", "chunk", "ReadableStream", "controller", "enqueue", "streamToBuffer", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "read", "streamToString", "signal", "decoder", "TextDecoder", "fatal", "aborted", "removeTrailingSlash", "parsePath", "hashIndex", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "query", "hash", "addPathPrefix", "addPathSuffix", "suffix", "pathHasPrefix", "WeakMap", "normalizeLocalePath", "locales", "detectedLocale", "lowercasedLocales", "locale", "REGEX_LOCALHOST_HOSTNAME", "parseURL", "url", "base", "URL", "Internal", "NextURL", "baseOrOpts", "basePath", "analyze", "getNextPathnameInfo", "i18n", "trailingSlash", "nextConfig", "removePathPrefix", "withoutPrefix", "pathnameNoDataPrefix", "paths", "buildId", "parseData", "i18nProvider", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "hostname", "getHostname", "host", "domainLocale", "detectDomainLocale", "domainItems", "item", "defaultLocale", "some", "domains", "formatPathname", "addLocale", "ignorePrefix", "lower", "forceLocale", "formatSearch", "search", "searchParams", "port", "protocol", "href", "origin", "password", "username", "toJSON", "clone", "Request", "ResponseAbortedName", "ResponseAborted", "Detached<PERSON>romise", "resolve", "rej", "clientComponentLoadStart", "clientComponentLoadTimes", "clientComponentLoadCount", "isAbortError", "pipeToNodeResponse", "readable", "waitUntilForEnd", "errored", "destroyed", "createAbortController", "response", "AbortController", "once", "writableFinished", "abort", "writer", "createWriterFromResponse", "started", "drained", "onDrain", "on", "off", "WritableStream", "write", "NEXT_OTEL_PERFORMANCE_PREFIX", "metrics", "getClientComponentLoaderMetrics", "measure", "flushHeaders", "getTracer", "startResponse", "spanName", "ok", "flush", "cause", "destroy", "pipeTo", "RenderResult", "fromStatic", "metadata", "contentType", "waitUntil", "assignMetadata", "isNull", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toUnchunkedString", "<PERSON><PERSON><PERSON><PERSON>", "chainStreams", "streams", "TransformStream", "preventClose", "nextStream", "lastStream", "responses", "ImageConfigContext", "deviceSizes", "imageSizes", "loaderFile", "disableStaticImages", "minimumCacheTTL", "formats", "dangerouslyAllowSVG", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "remotePatterns", "qualities", "unoptimized", "INTERNAL_QUERY_NAMES", "SearchParamsContext", "PathnameContext", "PathParamsContext", "reHasRegExp", "reReplaceRegExp", "escapeStringRegexp", "PARAMETER_PATTERN", "parseMatchedParameter", "param", "optional", "repeat", "PathnameContextProviderAdapter", "children", "router", "useRef", "isAutoExport", "useMemo", "current", "<PERSON><PERSON><PERSON><PERSON>", "isReady", "<PERSON><PERSON><PERSON>", "Provider", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "MissingSlotContext", "symbolError", "DOCTYPE", "noRouter", "renderToString", "element", "renderStream", "ReactDOMServerPages", "allReady", "ServerRouter", "as", "domainLocales", "isPreview", "isLocaleDomain", "reload", "back", "forward", "prefetch", "beforePopState", "renderPageTree", "invalidKeysMsg", "methodName", "<PERSON><PERSON><PERSON><PERSON>", "docsPathname", "toLocaleLowerCase", "checkRedirectValues", "redirect", "destination", "errors", "hasStatusCode", "hasPermanent", "destinationType", "basePathType", "renderToHTMLImpl", "extra", "sharedContext", "renderContext", "previewData", "source", "parseCookieFn", "assetQueryString", "dev", "userAgent", "deploymentId", "ampPath", "pageConfig", "buildManifest", "reactLoadableManifest", "ErrorDebug", "getStaticProps", "getStaticPaths", "getServerSideProps", "isNextDataRequest", "params", "images", "runtime", "globalRuntime", "isExperimentalCompile", "expireTime", "Document", "notFoundSrcPage", "developmentNotFoundSourcePage", "stripInternalQueries", "isSSG", "isBuildTimeSSG", "nextExport", "defaultAppGetInitialProps", "origGetInitialProps", "hasPageGetInitialProps", "hasPageScripts", "unstable_scriptLoader", "pageIsDynamic", "defaultErrorGetInitialProps", "getCacheControlHeader", "revalidate", "expire", "swr<PERSON><PERSON><PERSON>", "nextConfigOutput", "resolvedAsPath", "amp", "setIsrStatus", "appRouter", "pagesRouter", "refresh", "hmrRefresh", "scroll", "<PERSON><PERSON><PERSON><PERSON>", "jsxStyleRegistry", "createStyleRegistry", "ampState", "ampFirs<PERSON>", "hybrid", "isInAmpMode", "head", "defaultHead", "charSet", "reactLoadableModules", "initialScripts", "beforeInteractive", "script", "strategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "URLSearchParams", "adaptForPathParams", "pathParams", "routeRegex", "normalizedRoute", "includeSuffix", "includePrefix", "excludeOptionalTrailingSlash", "parameterizedRoute", "groups", "getParametrizedRoute", "groupIndex", "markerMatch", "paramMatch<PERSON>", "match", "re", "updateHead", "updateScripts", "scripts", "mountedInstances", "StyleRegistry", "registry", "Noop", "AppContainerWithIsomorphicFiberStructure", "AppTree", "defaultGetInitialProps", "docCtx", "renderPageHead", "renderPage", "enhanceApp", "AppComp", "styles", "nonce", "styledJsxInsertedHTML", "__N_PREVIEW", "draftMode", "preview", "revalidateReason", "staticPropsError", "notFound", "isNotFound", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "isRedirect", "isInteger", "ceil", "cacheControl", "pageData", "deferred<PERSON><PERSON>nt", "resolvedUrl", "serverSidePropsError", "unstable_notFound", "unstable_redirect", "filteredBuildManifest", "_page", "normalized", "posix", "resolvedPage", "normalize", "pages", "lowPriorityFiles", "Body", "div", "id", "renderDocument", "documentInitialPropsRes", "loadDocumentInitialProps", "renderShell", "EnhancedApp", "EnhancedComponent", "enhanceComponent", "documentCtx", "docProps", "renderContent", "_App", "_Component", "renderToInitialFizzStream", "streamOptions", "renderToReadableStream", "hasDocumentGetInitialProps", "rawStyledJsxInsertedHTML", "contentHTML", "documentElement", "htmlProps", "headTags", "setRootSpanAttribute", "documentResult", "dynamicImportsIds", "dynamicImports", "mod", "manifestItem", "files", "disableOptimizedLoading", "runtimeConfig", "__NEXT_DATA__", "autoExport", "dynamicIds", "getErrorSource", "stripAnsi", "stack", "digest", "gsp", "gssp", "customServer", "gip", "appGip", "strictNextHead", "doc<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "dangerousAsPath", "canonicalBase", "isDevelopment", "dynamicCssManifest", "unstable_runtimeJS", "unstable_JsPreload", "crossOrigin", "nextScriptWorkers", "largePageDataBytes", "nextFontManifest", "experimentalClientTraceMetadata", "experimental", "clientTraceMetadata", "document", "documentHTML", "renderTargetPrefix", "renderTargetSuffix", "renderToHTML", "ServerInsertedHTMLContext", "useServerInsertedHTML", "addInsertedServerHTMLCallback", "PagesRouteModule", "components", "render", "vendored", "contexts", "vendoredContexts"], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97]}