{"version": 3, "sources": ["../../../src/server/typescript/index.ts"], "sourcesContent": ["/**\n * This is a TypeScript language service plugin for Next.js app directory,\n * it provides the following features:\n *\n * - Warns about disallowed React APIs in server components.\n * - Warns about disallowed layout and page exports.\n * - Autocompletion for entry configurations.\n * - Hover hint and docs for entry configurations.\n */\n\nimport {\n  init,\n  getEntryInfo,\n  isAppEntryFile,\n  isDefaultFunctionExport,\n  isPositionInsideNode,\n  getSource,\n  isInsideApp,\n} from './utils'\nimport { NEXT_TS_ERRORS } from './constant'\n\nimport entryConfig from './rules/config'\nimport serverLayer from './rules/server'\nimport entryDefault from './rules/entry'\nimport clientBoundary from './rules/client-boundary'\nimport serverBoundary from './rules/server-boundary'\nimport metadata from './rules/metadata'\nimport errorEntry from './rules/error'\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nexport const createTSPlugin: tsModule.server.PluginModuleFactory = ({\n  typescript: ts,\n}) => {\n  function create(info: tsModule.server.PluginCreateInfo) {\n    init({\n      ts,\n      info,\n    })\n\n    // Set up decorator object\n    const proxy = Object.create(null)\n    for (let k of Object.keys(info.languageService)) {\n      const x = (info.languageService as any)[k]\n      proxy[k] = (...args: Array<{}>) => x.apply(info.languageService, args)\n    }\n\n    // Get plugin options\n    // config is the plugin options from the user's tsconfig.json\n    // e.g. { \"plugins\": [{ \"name\": \"next\", \"enabled\": true }] }\n    // config will be { \"name\": \"next\", \"enabled\": true }\n    // The default user config is { \"name\": \"next\" }\n    const isPluginEnabled = info.config.enabled ?? true\n\n    if (!isPluginEnabled) {\n      return proxy\n    }\n\n    // Auto completion\n    proxy.getCompletionsAtPosition = (\n      fileName: string,\n      position: number,\n      options: any\n    ) => {\n      let prior = info.languageService.getCompletionsAtPosition(\n        fileName,\n        position,\n        options\n      ) || {\n        isGlobalCompletion: false,\n        isMemberCompletion: false,\n        isNewIdentifierLocation: false,\n        entries: [],\n      }\n      if (!isAppEntryFile(fileName)) return prior\n\n      // If it's a server entry.\n      const entryInfo = getEntryInfo(fileName)\n      if (!entryInfo.client) {\n        // Remove specified entries from completion list\n        prior.entries = serverLayer.filterCompletionsAtPosition(prior.entries)\n\n        // Provide autocompletion for metadata fields\n        prior = metadata.filterCompletionsAtPosition(\n          fileName,\n          position,\n          options,\n          prior\n        )\n      }\n\n      // Add auto completions for export configs.\n      entryConfig.addCompletionsAtPosition(fileName, position, prior)\n\n      const source = getSource(fileName)\n      if (!source) return prior\n\n      ts.forEachChild(source!, (node) => {\n        // Auto completion for default export function's props.\n        if (\n          isPositionInsideNode(position, node) &&\n          isDefaultFunctionExport(node)\n        ) {\n          prior.entries.push(\n            ...entryDefault.getCompletionsAtPosition(\n              fileName,\n              node as tsModule.FunctionDeclaration,\n              position\n            )\n          )\n        }\n      })\n\n      return prior\n    }\n\n    // Show auto completion details\n    proxy.getCompletionEntryDetails = (\n      fileName: string,\n      position: number,\n      entryName: string,\n      formatOptions: tsModule.FormatCodeOptions,\n      source: string,\n      preferences: tsModule.UserPreferences,\n      data: tsModule.CompletionEntryData\n    ) => {\n      const entryCompletionEntryDetails = entryConfig.getCompletionEntryDetails(\n        entryName,\n        data\n      )\n      if (entryCompletionEntryDetails) return entryCompletionEntryDetails\n\n      const metadataCompletionEntryDetails = metadata.getCompletionEntryDetails(\n        fileName,\n        position,\n        entryName,\n        formatOptions,\n        source,\n        preferences,\n        data\n      )\n      if (metadataCompletionEntryDetails) return metadataCompletionEntryDetails\n\n      return info.languageService.getCompletionEntryDetails(\n        fileName,\n        position,\n        entryName,\n        formatOptions,\n        source,\n        preferences,\n        data\n      )\n    }\n\n    // Quick info\n    proxy.getQuickInfoAtPosition = (fileName: string, position: number) => {\n      const prior = info.languageService.getQuickInfoAtPosition(\n        fileName,\n        position\n      )\n      if (!isAppEntryFile(fileName)) return prior\n\n      // Remove type suggestions for disallowed APIs in server components.\n      const entryInfo = getEntryInfo(fileName)\n      if (!entryInfo.client) {\n        const definitions = info.languageService.getDefinitionAtPosition(\n          fileName,\n          position\n        )\n        if (\n          definitions &&\n          serverLayer.hasDisallowedReactAPIDefinition(definitions)\n        ) {\n          return\n        }\n\n        const metadataInfo = metadata.getQuickInfoAtPosition(fileName, position)\n        if (metadataInfo) return metadataInfo\n      }\n\n      const overridden = entryConfig.getQuickInfoAtPosition(fileName, position)\n      if (overridden) return overridden\n\n      return prior\n    }\n\n    // Show errors for disallowed imports\n    proxy.getSemanticDiagnostics = (fileName: string) => {\n      const prior = info.languageService.getSemanticDiagnostics(fileName)\n      const source = getSource(fileName)\n      if (!source) return prior\n\n      let isClientEntry = false\n      let isServerEntry = false\n      const isAppEntry = isAppEntryFile(fileName)\n\n      try {\n        const entryInfo = getEntryInfo(fileName, true)\n        isClientEntry = entryInfo.client\n        isServerEntry = entryInfo.server\n      } catch (e: any) {\n        prior.push({\n          file: source,\n          category: ts.DiagnosticCategory.Error,\n          code: NEXT_TS_ERRORS.MISPLACED_ENTRY_DIRECTIVE,\n          ...e,\n        })\n        isClientEntry = false\n        isServerEntry = false\n      }\n\n      if (isInsideApp(fileName)) {\n        const errorDiagnostic = errorEntry.getSemanticDiagnostics(\n          source!,\n          isClientEntry\n        )\n        prior.push(...errorDiagnostic)\n      }\n\n      ts.forEachChild(source!, (node) => {\n        if (ts.isImportDeclaration(node)) {\n          // import ...\n          if (isAppEntry) {\n            if (!isClientEntry || isServerEntry) {\n              // Check if it has valid imports in the server layer\n              const diagnostics =\n                serverLayer.getSemanticDiagnosticsForImportDeclaration(\n                  source,\n                  node\n                )\n              prior.push(...diagnostics)\n            }\n          }\n        } else if (\n          ts.isVariableStatement(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          // export const ...\n          if (isAppEntry) {\n            // Check if it has correct option exports\n            const diagnostics =\n              entryConfig.getSemanticDiagnosticsForExportVariableStatement(\n                source,\n                node\n              )\n            const metadataDiagnostics = isClientEntry\n              ? metadata.getSemanticDiagnosticsForExportVariableStatementInClientEntry(\n                  fileName,\n                  node\n                )\n              : metadata.getSemanticDiagnosticsForExportVariableStatement(\n                  fileName,\n                  node\n                )\n            prior.push(...diagnostics, ...metadataDiagnostics)\n          }\n\n          if (isClientEntry) {\n            prior.push(\n              ...clientBoundary.getSemanticDiagnosticsForExportVariableStatement(\n                source,\n                node\n              )\n            )\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForExportVariableStatement(\n                source,\n                node\n              )\n            )\n          }\n        } else if (isDefaultFunctionExport(node)) {\n          // export default function ...\n          if (isAppEntry) {\n            const diagnostics = entryDefault.getSemanticDiagnostics(\n              fileName,\n              source,\n              node\n            )\n            prior.push(...diagnostics)\n          }\n\n          if (isClientEntry) {\n            prior.push(\n              ...clientBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n        } else if (\n          ts.isFunctionDeclaration(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          // export function ...\n          if (isAppEntry) {\n            const metadataDiagnostics = isClientEntry\n              ? metadata.getSemanticDiagnosticsForExportVariableStatementInClientEntry(\n                  fileName,\n                  node\n                )\n              : metadata.getSemanticDiagnosticsForExportVariableStatement(\n                  fileName,\n                  node\n                )\n            prior.push(...metadataDiagnostics)\n          }\n\n          if (isClientEntry) {\n            prior.push(\n              ...clientBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n        } else if (ts.isExportDeclaration(node)) {\n          // export { ... }\n          if (isAppEntry) {\n            const metadataDiagnostics = isClientEntry\n              ? metadata.getSemanticDiagnosticsForExportDeclarationInClientEntry(\n                  fileName,\n                  node\n                )\n              : metadata.getSemanticDiagnosticsForExportDeclaration(\n                  fileName,\n                  node\n                )\n            prior.push(...metadataDiagnostics)\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForExportDeclaration(\n                source,\n                node\n              )\n            )\n          }\n        }\n      })\n\n      return prior\n    }\n\n    // Get definition and link for specific node\n    proxy.getDefinitionAndBoundSpan = (fileName: string, position: number) => {\n      const entryInfo = getEntryInfo(fileName)\n      if (isAppEntryFile(fileName) && !entryInfo.client) {\n        const metadataDefinition = metadata.getDefinitionAndBoundSpan(\n          fileName,\n          position\n        )\n        if (metadataDefinition) return metadataDefinition\n      }\n\n      return info.languageService.getDefinitionAndBoundSpan(fileName, position)\n    }\n\n    return proxy\n  }\n\n  return { create }\n}\n"], "names": ["createTSPlugin", "typescript", "ts", "create", "info", "init", "proxy", "Object", "k", "keys", "languageService", "x", "args", "apply", "isPluginEnabled", "config", "enabled", "getCompletionsAtPosition", "fileName", "position", "options", "prior", "isGlobalCompletion", "isMemberCompletion", "isNewIdentifierLocation", "entries", "isAppEntryFile", "entryInfo", "getEntryInfo", "client", "serverLayer", "filterCompletionsAtPosition", "metadata", "entryConfig", "addCompletionsAtPosition", "source", "getSource", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "isPositionInsideNode", "isDefaultFunctionExport", "push", "<PERSON><PERSON><PERSON><PERSON>", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "data", "entryCompletionEntryDetails", "metadataCompletionEntryDetails", "getQuickInfoAtPosition", "definitions", "getDefinitionAtPosition", "hasDisallowedReactAPIDefinition", "metadataInfo", "overridden", "getSemanticDiagnostics", "isClientEntry", "isServerEntry", "isAppEntry", "server", "e", "file", "category", "DiagnosticCategory", "Error", "code", "NEXT_TS_ERRORS", "MISPLACED_ENTRY_DIRECTIVE", "isInsideApp", "errorDiagnostic", "errorEntry", "isImportDeclaration", "diagnostics", "getSemanticDiagnosticsForImportDeclaration", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "getSemanticDiagnosticsForExportVariableStatement", "metadataDiagnostics", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "clientBoundary", "serverBoundary", "getSemanticDiagnosticsForFunctionExport", "isFunctionDeclaration", "isExportDeclaration", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "getSemanticDiagnosticsForExportDeclaration", "getDefinitionAndBoundSpan", "metadataDefinition"], "mappings": "AAAA;;;;;;;;CAQC;;;;+BAsBYA;;;eAAAA;;;uBAZN;0BACwB;+DAEP;+DACA;8DACC;uEACE;uEACA;iEACN;8DACE;;;;;;AAGhB,MAAMA,iBAAsD,CAAC,EAClEC,YAAYC,EAAE,EACf;IACC,SAASC,OAAOC,IAAsC;QACpDC,IAAAA,WAAI,EAAC;YACHH;YACAE;QACF;QAEA,0BAA0B;QAC1B,MAAME,QAAQC,OAAOJ,MAAM,CAAC;QAC5B,KAAK,IAAIK,KAAKD,OAAOE,IAAI,CAACL,KAAKM,eAAe,EAAG;YAC/C,MAAMC,IAAI,AAACP,KAAKM,eAAe,AAAQ,CAACF,EAAE;YAC1CF,KAAK,CAACE,EAAE,GAAG,CAAC,GAAGI,OAAoBD,EAAEE,KAAK,CAACT,KAAKM,eAAe,EAAEE;QACnE;QAEA,qBAAqB;QACrB,6DAA6D;QAC7D,4DAA4D;QAC5D,qDAAqD;QACrD,gDAAgD;QAChD,MAAME,kBAAkBV,KAAKW,MAAM,CAACC,OAAO,IAAI;QAE/C,IAAI,CAACF,iBAAiB;YACpB,OAAOR;QACT;QAEA,kBAAkB;QAClBA,MAAMW,wBAAwB,GAAG,CAC/BC,UACAC,UACAC;YAEA,IAAIC,QAAQjB,KAAKM,eAAe,CAACO,wBAAwB,CACvDC,UACAC,UACAC,YACG;gBACHE,oBAAoB;gBACpBC,oBAAoB;gBACpBC,yBAAyB;gBACzBC,SAAS,EAAE;YACb;YACA,IAAI,CAACC,IAAAA,qBAAc,EAACR,WAAW,OAAOG;YAEtC,0BAA0B;YAC1B,MAAMM,YAAYC,IAAAA,mBAAY,EAACV;YAC/B,IAAI,CAACS,UAAUE,MAAM,EAAE;gBACrB,gDAAgD;gBAChDR,MAAMI,OAAO,GAAGK,eAAW,CAACC,2BAA2B,CAACV,MAAMI,OAAO;gBAErE,6CAA6C;gBAC7CJ,QAAQW,iBAAQ,CAACD,2BAA2B,CAC1Cb,UACAC,UACAC,SACAC;YAEJ;YAEA,2CAA2C;YAC3CY,eAAW,CAACC,wBAAwB,CAAChB,UAAUC,UAAUE;YAEzD,MAAMc,SAASC,IAAAA,gBAAS,EAAClB;YACzB,IAAI,CAACiB,QAAQ,OAAOd;YAEpBnB,GAAGmC,YAAY,CAACF,QAAS,CAACG;gBACxB,uDAAuD;gBACvD,IACEC,IAAAA,2BAAoB,EAACpB,UAAUmB,SAC/BE,IAAAA,8BAAuB,EAACF,OACxB;oBACAjB,MAAMI,OAAO,CAACgB,IAAI,IACbC,cAAY,CAACzB,wBAAwB,CACtCC,UACAoB,MACAnB;gBAGN;YACF;YAEA,OAAOE;QACT;QAEA,+BAA+B;QAC/Bf,MAAMqC,yBAAyB,GAAG,CAChCzB,UACAC,UACAyB,WACAC,eACAV,QACAW,aACAC;YAEA,MAAMC,8BAA8Bf,eAAW,CAACU,yBAAyB,CACvEC,WACAG;YAEF,IAAIC,6BAA6B,OAAOA;YAExC,MAAMC,iCAAiCjB,iBAAQ,CAACW,yBAAyB,CACvEzB,UACAC,UACAyB,WACAC,eACAV,QACAW,aACAC;YAEF,IAAIE,gCAAgC,OAAOA;YAE3C,OAAO7C,KAAKM,eAAe,CAACiC,yBAAyB,CACnDzB,UACAC,UACAyB,WACAC,eACAV,QACAW,aACAC;QAEJ;QAEA,aAAa;QACbzC,MAAM4C,sBAAsB,GAAG,CAAChC,UAAkBC;YAChD,MAAME,QAAQjB,KAAKM,eAAe,CAACwC,sBAAsB,CACvDhC,UACAC;YAEF,IAAI,CAACO,IAAAA,qBAAc,EAACR,WAAW,OAAOG;YAEtC,oEAAoE;YACpE,MAAMM,YAAYC,IAAAA,mBAAY,EAACV;YAC/B,IAAI,CAACS,UAAUE,MAAM,EAAE;gBACrB,MAAMsB,cAAc/C,KAAKM,eAAe,CAAC0C,uBAAuB,CAC9DlC,UACAC;gBAEF,IACEgC,eACArB,eAAW,CAACuB,+BAA+B,CAACF,cAC5C;oBACA;gBACF;gBAEA,MAAMG,eAAetB,iBAAQ,CAACkB,sBAAsB,CAAChC,UAAUC;gBAC/D,IAAImC,cAAc,OAAOA;YAC3B;YAEA,MAAMC,aAAatB,eAAW,CAACiB,sBAAsB,CAAChC,UAAUC;YAChE,IAAIoC,YAAY,OAAOA;YAEvB,OAAOlC;QACT;QAEA,qCAAqC;QACrCf,MAAMkD,sBAAsB,GAAG,CAACtC;YAC9B,MAAMG,QAAQjB,KAAKM,eAAe,CAAC8C,sBAAsB,CAACtC;YAC1D,MAAMiB,SAASC,IAAAA,gBAAS,EAAClB;YACzB,IAAI,CAACiB,QAAQ,OAAOd;YAEpB,IAAIoC,gBAAgB;YACpB,IAAIC,gBAAgB;YACpB,MAAMC,aAAajC,IAAAA,qBAAc,EAACR;YAElC,IAAI;gBACF,MAAMS,YAAYC,IAAAA,mBAAY,EAACV,UAAU;gBACzCuC,gBAAgB9B,UAAUE,MAAM;gBAChC6B,gBAAgB/B,UAAUiC,MAAM;YAClC,EAAE,OAAOC,GAAQ;gBACfxC,MAAMoB,IAAI,CAAC;oBACTqB,MAAM3B;oBACN4B,UAAU7D,GAAG8D,kBAAkB,CAACC,KAAK;oBACrCC,MAAMC,wBAAc,CAACC,yBAAyB;oBAC9C,GAAGP,CAAC;gBACN;gBACAJ,gBAAgB;gBAChBC,gBAAgB;YAClB;YAEA,IAAIW,IAAAA,kBAAW,EAACnD,WAAW;gBACzB,MAAMoD,kBAAkBC,cAAU,CAACf,sBAAsB,CACvDrB,QACAsB;gBAEFpC,MAAMoB,IAAI,IAAI6B;YAChB;YAEApE,GAAGmC,YAAY,CAACF,QAAS,CAACG;oBAgBtBA,iBAqEAA;gBApFF,IAAIpC,GAAGsE,mBAAmB,CAAClC,OAAO;oBAChC,aAAa;oBACb,IAAIqB,YAAY;wBACd,IAAI,CAACF,iBAAiBC,eAAe;4BACnC,oDAAoD;4BACpD,MAAMe,cACJ3C,eAAW,CAAC4C,0CAA0C,CACpDvC,QACAG;4BAEJjB,MAAMoB,IAAI,IAAIgC;wBAChB;oBACF;gBACF,OAAO,IACLvE,GAAGyE,mBAAmB,CAACrC,WACvBA,kBAAAA,KAAKsC,SAAS,qBAAdtC,gBAAgBuC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK7E,GAAG8E,UAAU,CAACC,aAAa,IAClE;oBACA,mBAAmB;oBACnB,IAAItB,YAAY;wBACd,yCAAyC;wBACzC,MAAMc,cACJxC,eAAW,CAACiD,gDAAgD,CAC1D/C,QACAG;wBAEJ,MAAM6C,sBAAsB1B,gBACxBzB,iBAAQ,CAACoD,6DAA6D,CACpElE,UACAoB,QAEFN,iBAAQ,CAACkD,gDAAgD,CACvDhE,UACAoB;wBAENjB,MAAMoB,IAAI,IAAIgC,gBAAgBU;oBAChC;oBAEA,IAAI1B,eAAe;wBACjBpC,MAAMoB,IAAI,IACL4C,uBAAc,CAACH,gDAAgD,CAChE/C,QACAG;oBAGN;oBAEA,IAAIoB,eAAe;wBACjBrC,MAAMoB,IAAI,IACL6C,uBAAc,CAACJ,gDAAgD,CAChE/C,QACAG;oBAGN;gBACF,OAAO,IAAIE,IAAAA,8BAAuB,EAACF,OAAO;oBACxC,8BAA8B;oBAC9B,IAAIqB,YAAY;wBACd,MAAMc,cAAc/B,cAAY,CAACc,sBAAsB,CACrDtC,UACAiB,QACAG;wBAEFjB,MAAMoB,IAAI,IAAIgC;oBAChB;oBAEA,IAAIhB,eAAe;wBACjBpC,MAAMoB,IAAI,IACL4C,uBAAc,CAACE,uCAAuC,CACvDpD,QACAG;oBAGN;oBAEA,IAAIoB,eAAe;wBACjBrC,MAAMoB,IAAI,IACL6C,uBAAc,CAACC,uCAAuC,CACvDpD,QACAG;oBAGN;gBACF,OAAO,IACLpC,GAAGsF,qBAAqB,CAAClD,WACzBA,mBAAAA,KAAKsC,SAAS,qBAAdtC,iBAAgBuC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAK7E,GAAG8E,UAAU,CAACC,aAAa,IAClE;oBACA,sBAAsB;oBACtB,IAAItB,YAAY;wBACd,MAAMwB,sBAAsB1B,gBACxBzB,iBAAQ,CAACoD,6DAA6D,CACpElE,UACAoB,QAEFN,iBAAQ,CAACkD,gDAAgD,CACvDhE,UACAoB;wBAENjB,MAAMoB,IAAI,IAAI0C;oBAChB;oBAEA,IAAI1B,eAAe;wBACjBpC,MAAMoB,IAAI,IACL4C,uBAAc,CAACE,uCAAuC,CACvDpD,QACAG;oBAGN;oBAEA,IAAIoB,eAAe;wBACjBrC,MAAMoB,IAAI,IACL6C,uBAAc,CAACC,uCAAuC,CACvDpD,QACAG;oBAGN;gBACF,OAAO,IAAIpC,GAAGuF,mBAAmB,CAACnD,OAAO;oBACvC,iBAAiB;oBACjB,IAAIqB,YAAY;wBACd,MAAMwB,sBAAsB1B,gBACxBzB,iBAAQ,CAAC0D,uDAAuD,CAC9DxE,UACAoB,QAEFN,iBAAQ,CAAC2D,0CAA0C,CACjDzE,UACAoB;wBAENjB,MAAMoB,IAAI,IAAI0C;oBAChB;oBAEA,IAAIzB,eAAe;wBACjBrC,MAAMoB,IAAI,IACL6C,uBAAc,CAACK,0CAA0C,CAC1DxD,QACAG;oBAGN;gBACF;YACF;YAEA,OAAOjB;QACT;QAEA,4CAA4C;QAC5Cf,MAAMsF,yBAAyB,GAAG,CAAC1E,UAAkBC;YACnD,MAAMQ,YAAYC,IAAAA,mBAAY,EAACV;YAC/B,IAAIQ,IAAAA,qBAAc,EAACR,aAAa,CAACS,UAAUE,MAAM,EAAE;gBACjD,MAAMgE,qBAAqB7D,iBAAQ,CAAC4D,yBAAyB,CAC3D1E,UACAC;gBAEF,IAAI0E,oBAAoB,OAAOA;YACjC;YAEA,OAAOzF,KAAKM,eAAe,CAACkF,yBAAyB,CAAC1E,UAAUC;QAClE;QAEA,OAAOb;IACT;IAEA,OAAO;QAAEH;IAAO;AAClB"}