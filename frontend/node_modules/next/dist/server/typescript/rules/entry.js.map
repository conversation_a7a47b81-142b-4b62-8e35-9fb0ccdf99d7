{"version": 3, "sources": ["../../../../src/server/typescript/rules/entry.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs'\n\nimport {\n  ALLOWED_LAYOUT_PROPS,\n  ALLOWED_PAGE_PROPS,\n  NEXT_TS_ERRORS,\n} from '../constant'\nimport { getTs, isPageFile, isPositionInsideNode } from '../utils'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst entry = {\n  // Give auto completion for the component's props\n  getCompletionsAtPosition(\n    fileName: string,\n    node: tsModule.FunctionDeclaration,\n    position: number\n  ) {\n    const ts = getTs()\n    const entries: tsModule.CompletionEntry[] = []\n\n    // Default export function might not accept parameters\n    const paramNode = node.parameters?.[0] as\n      | tsModule.ParameterDeclaration\n      | undefined\n\n    if (paramNode && isPositionInsideNode(position, paramNode)) {\n      const props = paramNode?.name\n      if (props && ts.isObjectBindingPattern(props)) {\n        let validProps = []\n        let validPropsWithType = []\n        let type: string\n\n        if (isPageFile(fileName)) {\n          // For page entries (page.js), it can only have `params` and `searchParams`\n          // as the prop names.\n          validProps = ALLOWED_PAGE_PROPS\n          validPropsWithType = ALLOWED_PAGE_PROPS\n          type = 'page'\n        } else {\n          // For layout entires, check if it has any named slots.\n          const currentDir = path.dirname(fileName)\n          const items = fs.readdirSync(currentDir, {\n            withFileTypes: true,\n          })\n          const slots = []\n          for (const item of items) {\n            if (item.isDirectory() && item.name.startsWith('@')) {\n              slots.push(item.name.slice(1))\n            }\n          }\n          validProps = ALLOWED_LAYOUT_PROPS.concat(slots)\n          validPropsWithType = ALLOWED_LAYOUT_PROPS.concat(\n            slots.map((s) => `${s}: React.ReactNode`)\n          )\n          type = 'layout'\n        }\n\n        // Auto completion for props\n        for (const element of props.elements) {\n          if (isPositionInsideNode(position, element)) {\n            const nameNode = element.propertyName || element.name\n\n            if (isPositionInsideNode(position, nameNode)) {\n              for (const name of validProps) {\n                entries.push({\n                  name,\n                  insertText: name,\n                  sortText: '_' + name,\n                  kind: ts.ScriptElementKind.memberVariableElement,\n                  kindModifiers: ts.ScriptElementKindModifier.none,\n                  labelDetails: {\n                    description: `Next.js ${type} prop`,\n                  },\n                } as tsModule.CompletionEntry)\n              }\n            }\n\n            break\n          }\n        }\n\n        // Auto completion for types\n        if (paramNode.type && ts.isTypeLiteralNode(paramNode.type)) {\n          for (const member of paramNode.type.members) {\n            if (isPositionInsideNode(position, member)) {\n              for (const name of validPropsWithType) {\n                entries.push({\n                  name,\n                  insertText: name,\n                  sortText: '_' + name,\n                  kind: ts.ScriptElementKind.memberVariableElement,\n                  kindModifiers: ts.ScriptElementKindModifier.none,\n                  labelDetails: {\n                    description: `Next.js ${type} prop type`,\n                  },\n                } as tsModule.CompletionEntry)\n              }\n\n              break\n            }\n          }\n        }\n      }\n    }\n\n    return entries\n  },\n\n  // Give error diagnostics for the component\n  getSemanticDiagnostics(\n    fileName: string,\n    source: tsModule.SourceFile,\n    node: tsModule.FunctionDeclaration\n  ) {\n    const ts = getTs()\n\n    let validProps = []\n    let type: string\n\n    if (isPageFile(fileName)) {\n      // For page entries (page.js), it can only have `params` and `searchParams`\n      // as the prop names.\n      validProps = ALLOWED_PAGE_PROPS\n      type = 'page'\n    } else {\n      // For layout entires, check if it has any named slots.\n      const currentDir = path.dirname(fileName)\n      const items = fs.readdirSync(currentDir, { withFileTypes: true })\n      const slots = []\n      for (const item of items) {\n        if (item.isDirectory() && item.name.startsWith('@')) {\n          slots.push(item.name.slice(1))\n        }\n      }\n      validProps = ALLOWED_LAYOUT_PROPS.concat(slots)\n      type = 'layout'\n    }\n\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    const props = node.parameters?.[0]?.name\n    if (props && ts.isObjectBindingPattern(props)) {\n      for (const prop of props.elements) {\n        const propName = (prop.propertyName || prop.name).getText()\n        if (!validProps.includes(propName)) {\n          diagnostics.push({\n            file: source,\n            category: ts.DiagnosticCategory.Error,\n            code: NEXT_TS_ERRORS.INVALID_PAGE_PROP,\n            messageText: `\"${propName}\" is not a valid ${type} prop.`,\n            start: prop.getStart(),\n            length: prop.getWidth(),\n          })\n        }\n      }\n    }\n\n    return diagnostics\n  },\n}\n\nexport default entry\n"], "names": ["entry", "getCompletionsAtPosition", "fileName", "node", "position", "ts", "getTs", "entries", "paramNode", "parameters", "isPositionInsideNode", "props", "name", "isObjectBindingPattern", "validProps", "validPropsWithType", "type", "isPageFile", "ALLOWED_PAGE_PROPS", "currentDir", "path", "dirname", "items", "fs", "readdirSync", "withFileTypes", "slots", "item", "isDirectory", "startsWith", "push", "slice", "ALLOWED_LAYOUT_PROPS", "concat", "map", "s", "element", "elements", "nameNode", "propertyName", "insertText", "sortText", "kind", "ScriptElementKind", "memberVariableElement", "kindModifiers", "ScriptElementKindModifier", "none", "labelDetails", "description", "isTypeLiteralNode", "member", "members", "getSemanticDiagnostics", "source", "diagnostics", "prop", "propName", "getText", "includes", "file", "category", "DiagnosticCategory", "Error", "code", "NEXT_TS_ERRORS", "INVALID_PAGE_PROP", "messageText", "start", "getStart", "length", "getWidth"], "mappings": ";;;;+BAmKA;;;eAAA;;;6DAnKiB;2DACF;0BAMR;uBACiD;;;;;;AAIxD,MAAMA,QAAQ;IACZ,iDAAiD;IACjDC,0BACEC,QAAgB,EAChBC,IAAkC,EAClCC,QAAgB;YAMED;QAJlB,MAAME,KAAKC,IAAAA,YAAK;QAChB,MAAMC,UAAsC,EAAE;QAE9C,sDAAsD;QACtD,MAAMC,aAAYL,mBAAAA,KAAKM,UAAU,qBAAfN,gBAAiB,CAAC,EAAE;QAItC,IAAIK,aAAaE,IAAAA,2BAAoB,EAACN,UAAUI,YAAY;YAC1D,MAAMG,QAAQH,6BAAAA,UAAWI,IAAI;YAC7B,IAAID,SAASN,GAAGQ,sBAAsB,CAACF,QAAQ;gBAC7C,IAAIG,aAAa,EAAE;gBACnB,IAAIC,qBAAqB,EAAE;gBAC3B,IAAIC;gBAEJ,IAAIC,IAAAA,iBAAU,EAACf,WAAW;oBACxB,2EAA2E;oBAC3E,qBAAqB;oBACrBY,aAAaI,4BAAkB;oBAC/BH,qBAAqBG,4BAAkB;oBACvCF,OAAO;gBACT,OAAO;oBACL,uDAAuD;oBACvD,MAAMG,aAAaC,aAAI,CAACC,OAAO,CAACnB;oBAChC,MAAMoB,QAAQC,WAAE,CAACC,WAAW,CAACL,YAAY;wBACvCM,eAAe;oBACjB;oBACA,MAAMC,QAAQ,EAAE;oBAChB,KAAK,MAAMC,QAAQL,MAAO;wBACxB,IAAIK,KAAKC,WAAW,MAAMD,KAAKf,IAAI,CAACiB,UAAU,CAAC,MAAM;4BACnDH,MAAMI,IAAI,CAACH,KAAKf,IAAI,CAACmB,KAAK,CAAC;wBAC7B;oBACF;oBACAjB,aAAakB,8BAAoB,CAACC,MAAM,CAACP;oBACzCX,qBAAqBiB,8BAAoB,CAACC,MAAM,CAC9CP,MAAMQ,GAAG,CAAC,CAACC,IAAM,GAAGA,EAAE,iBAAiB,CAAC;oBAE1CnB,OAAO;gBACT;gBAEA,4BAA4B;gBAC5B,KAAK,MAAMoB,WAAWzB,MAAM0B,QAAQ,CAAE;oBACpC,IAAI3B,IAAAA,2BAAoB,EAACN,UAAUgC,UAAU;wBAC3C,MAAME,WAAWF,QAAQG,YAAY,IAAIH,QAAQxB,IAAI;wBAErD,IAAIF,IAAAA,2BAAoB,EAACN,UAAUkC,WAAW;4BAC5C,KAAK,MAAM1B,QAAQE,WAAY;gCAC7BP,QAAQuB,IAAI,CAAC;oCACXlB;oCACA4B,YAAY5B;oCACZ6B,UAAU,MAAM7B;oCAChB8B,MAAMrC,GAAGsC,iBAAiB,CAACC,qBAAqB;oCAChDC,eAAexC,GAAGyC,yBAAyB,CAACC,IAAI;oCAChDC,cAAc;wCACZC,aAAa,CAAC,QAAQ,EAAEjC,KAAK,KAAK,CAAC;oCACrC;gCACF;4BACF;wBACF;wBAEA;oBACF;gBACF;gBAEA,4BAA4B;gBAC5B,IAAIR,UAAUQ,IAAI,IAAIX,GAAG6C,iBAAiB,CAAC1C,UAAUQ,IAAI,GAAG;oBAC1D,KAAK,MAAMmC,UAAU3C,UAAUQ,IAAI,CAACoC,OAAO,CAAE;wBAC3C,IAAI1C,IAAAA,2BAAoB,EAACN,UAAU+C,SAAS;4BAC1C,KAAK,MAAMvC,QAAQG,mBAAoB;gCACrCR,QAAQuB,IAAI,CAAC;oCACXlB;oCACA4B,YAAY5B;oCACZ6B,UAAU,MAAM7B;oCAChB8B,MAAMrC,GAAGsC,iBAAiB,CAACC,qBAAqB;oCAChDC,eAAexC,GAAGyC,yBAAyB,CAACC,IAAI;oCAChDC,cAAc;wCACZC,aAAa,CAAC,QAAQ,EAAEjC,KAAK,UAAU,CAAC;oCAC1C;gCACF;4BACF;4BAEA;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOT;IACT;IAEA,2CAA2C;IAC3C8C,wBACEnD,QAAgB,EAChBoD,MAA2B,EAC3BnD,IAAkC;YA4BpBA,mBAAAA;QA1Bd,MAAME,KAAKC,IAAAA,YAAK;QAEhB,IAAIQ,aAAa,EAAE;QACnB,IAAIE;QAEJ,IAAIC,IAAAA,iBAAU,EAACf,WAAW;YACxB,2EAA2E;YAC3E,qBAAqB;YACrBY,aAAaI,4BAAkB;YAC/BF,OAAO;QACT,OAAO;YACL,uDAAuD;YACvD,MAAMG,aAAaC,aAAI,CAACC,OAAO,CAACnB;YAChC,MAAMoB,QAAQC,WAAE,CAACC,WAAW,CAACL,YAAY;gBAAEM,eAAe;YAAK;YAC/D,MAAMC,QAAQ,EAAE;YAChB,KAAK,MAAMC,QAAQL,MAAO;gBACxB,IAAIK,KAAKC,WAAW,MAAMD,KAAKf,IAAI,CAACiB,UAAU,CAAC,MAAM;oBACnDH,MAAMI,IAAI,CAACH,KAAKf,IAAI,CAACmB,KAAK,CAAC;gBAC7B;YACF;YACAjB,aAAakB,8BAAoB,CAACC,MAAM,CAACP;YACzCV,OAAO;QACT;QAEA,MAAMuC,cAAqC,EAAE;QAE7C,MAAM5C,SAAQR,mBAAAA,KAAKM,UAAU,sBAAfN,oBAAAA,gBAAiB,CAAC,EAAE,qBAApBA,kBAAsBS,IAAI;QACxC,IAAID,SAASN,GAAGQ,sBAAsB,CAACF,QAAQ;YAC7C,KAAK,MAAM6C,QAAQ7C,MAAM0B,QAAQ,CAAE;gBACjC,MAAMoB,WAAW,AAACD,CAAAA,KAAKjB,YAAY,IAAIiB,KAAK5C,IAAI,AAAD,EAAG8C,OAAO;gBACzD,IAAI,CAAC5C,WAAW6C,QAAQ,CAACF,WAAW;oBAClCF,YAAYzB,IAAI,CAAC;wBACf8B,MAAMN;wBACNO,UAAUxD,GAAGyD,kBAAkB,CAACC,KAAK;wBACrCC,MAAMC,wBAAc,CAACC,iBAAiB;wBACtCC,aAAa,CAAC,CAAC,EAAEV,SAAS,iBAAiB,EAAEzC,KAAK,MAAM,CAAC;wBACzDoD,OAAOZ,KAAKa,QAAQ;wBACpBC,QAAQd,KAAKe,QAAQ;oBACvB;gBACF;YACF;QACF;QAEA,OAAOhB;IACT;AACF;MAEA,WAAevD"}