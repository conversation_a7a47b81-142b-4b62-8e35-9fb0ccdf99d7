{"version": 3, "sources": ["../../../src/server/route-modules/checks.ts"], "sourcesContent": ["import type { AppRouteRouteModule } from './app-route/module'\nimport type { AppPageRouteModule } from './app-page/module'\nimport type { PagesRouteModule } from './pages/module'\nimport type { PagesAPIRouteModule } from './pages-api/module'\n\nimport type { RouteModule } from './route-module'\n\nimport { RouteKind } from '../route-kind'\n\nexport function isAppRouteRouteModule(\n  routeModule: RouteModule\n): routeModule is AppRouteRouteModule {\n  return routeModule.definition.kind === RouteKind.APP_ROUTE\n}\n\nexport function isAppPageRouteModule(\n  routeModule: RouteModule\n): routeModule is AppPageRouteModule {\n  return routeModule.definition.kind === RouteKind.APP_PAGE\n}\n\nexport function isPagesRouteModule(\n  routeModule: RouteModule\n): routeModule is PagesRouteModule {\n  return routeModule.definition.kind === RouteKind.PAGES\n}\n\nexport function isPagesAPIRouteModule(\n  routeModule: RouteModule\n): routeModule is PagesAPIRouteModule {\n  return routeModule.definition.kind === RouteKind.PAGES_API\n}\n"], "names": ["isAppPageRouteModule", "isAppRouteRouteModule", "isPagesAPIRouteModule", "isPagesRouteModule", "routeModule", "definition", "kind", "RouteKind", "APP_ROUTE", "APP_PAGE", "PAGES", "PAGES_API"], "mappings": ";;;;;;;;;;;;;;;;;IAegBA,oBAAoB;eAApBA;;IANAC,qBAAqB;eAArBA;;IAkBAC,qBAAqB;eAArBA;;IANAC,kBAAkB;eAAlBA;;;2BAdU;AAEnB,SAASF,sBACdG,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACC,SAAS;AAC5D;AAEO,SAASR,qBACdI,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACE,QAAQ;AAC3D;AAEO,SAASN,mBACdC,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACG,KAAK;AACxD;AAEO,SAASR,sBACdE,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACI,SAAS;AAC5D"}