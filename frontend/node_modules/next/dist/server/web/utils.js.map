{"version": 3, "sources": ["../../../src/server/web/utils.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../lib/constants'\n\n/**\n * Converts a Node.js IncomingHttpHeaders object to a Headers object. Any\n * headers with multiple values will be joined with a comma and space. Any\n * headers that have an undefined value will be ignored and others will be\n * coerced to strings.\n *\n * @param nodeHeaders the headers object to convert\n * @returns the converted headers object\n */\nexport function fromNodeOutgoingHttpHeaders(\n  nodeHeaders: OutgoingHttpHeaders\n): Headers {\n  const headers = new Headers()\n  for (let [key, value] of Object.entries(nodeHeaders)) {\n    const values = Array.isArray(value) ? value : [value]\n    for (let v of values) {\n      if (typeof v === 'undefined') continue\n      if (typeof v === 'number') {\n        v = v.toString()\n      }\n\n      headers.append(key, v)\n    }\n  }\n  return headers\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n  \n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nexport function splitCookiesString(cookiesString: string) {\n  var cookiesStrings = []\n  var pos = 0\n  var start\n  var ch\n  var lastComma\n  var nextStart\n  var cookiesSeparatorFound\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1\n    }\n    return pos < cookiesString.length\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos)\n\n    return ch !== '=' && ch !== ';' && ch !== ','\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos\n    cookiesSeparatorFound = false\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos)\n      if (ch === ',') {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos\n        pos += 1\n\n        skipWhitespace()\n        nextStart = pos\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === '=') {\n          // we found cookies separator\n          cookiesSeparatorFound = true\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart\n          cookiesStrings.push(cookiesString.substring(start, lastComma))\n          start = pos\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1\n        }\n      } else {\n        pos += 1\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length))\n    }\n  }\n\n  return cookiesStrings\n}\n\n/**\n * Converts a Headers object to a Node.js OutgoingHttpHeaders object. This is\n * required to support the set-cookie header, which may have multiple values.\n *\n * @param headers the headers object to convert\n * @returns the converted headers object\n */\nexport function toNodeOutgoingHttpHeaders(\n  headers: Headers\n): OutgoingHttpHeaders {\n  const nodeHeaders: OutgoingHttpHeaders = {}\n  const cookies: string[] = []\n  if (headers) {\n    for (const [key, value] of headers.entries()) {\n      if (key.toLowerCase() === 'set-cookie') {\n        // We may have gotten a comma joined string of cookies, or multiple\n        // set-cookie headers. We need to merge them into one header array\n        // to represent all the cookies.\n        cookies.push(...splitCookiesString(value))\n        nodeHeaders[key] = cookies.length === 1 ? cookies[0] : cookies\n      } else {\n        nodeHeaders[key] = value\n      }\n    }\n  }\n  return nodeHeaders\n}\n\n/**\n * Validate the correctness of a user-provided URL.\n */\nexport function validateURL(url: string | URL): string {\n  try {\n    return String(new URL(String(url)))\n  } catch (error: any) {\n    throw new Error(\n      `URL is malformed \"${String(\n        url\n      )}\". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,\n      { cause: error }\n    )\n  }\n}\n\n/**\n * Normalizes `nxtP` and `nxtI` query param values to remove the prefix.\n * This function does not mutate the input key.\n */\nexport function normalizeNextQueryParam(key: string): null | string {\n  const prefixes = [NEXT_QUERY_PARAM_PREFIX, NEXT_INTERCEPTION_MARKER_PREFIX]\n  for (const prefix of prefixes) {\n    if (key !== prefix && key.startsWith(prefix)) {\n      return key.substring(prefix.length)\n    }\n  }\n  return null\n}\n"], "names": ["fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "splitCookiesString", "toNodeOutgoingHttpHeaders", "validateURL", "nodeHeaders", "headers", "Headers", "key", "value", "Object", "entries", "values", "Array", "isArray", "v", "toString", "append", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "length", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "cookies", "toLowerCase", "url", "String", "URL", "error", "Error", "cause", "prefixes", "NEXT_QUERY_PARAM_PREFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "prefix", "startsWith"], "mappings": ";;;;;;;;;;;;;;;;;;IAegBA,2BAA2B;eAA3BA;;IA8IAC,uBAAuB;eAAvBA;;IAlHAC,kBAAkB;eAAlBA;;IAyEAC,yBAAyB;eAAzBA;;IAwBAC,WAAW;eAAXA;;;2BAxIT;AAWA,SAASJ,4BACdK,WAAgC;IAEhC,MAAMC,UAAU,IAAIC;IACpB,KAAK,IAAI,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACN,aAAc;QACpD,MAAMO,SAASC,MAAMC,OAAO,CAACL,SAASA,QAAQ;YAACA;SAAM;QACrD,KAAK,IAAIM,KAAKH,OAAQ;YACpB,IAAI,OAAOG,MAAM,aAAa;YAC9B,IAAI,OAAOA,MAAM,UAAU;gBACzBA,IAAIA,EAAEC,QAAQ;YAChB;YAEAV,QAAQW,MAAM,CAACT,KAAKO;QACtB;IACF;IACA,OAAOT;AACT;AAYO,SAASJ,mBAAmBgB,aAAqB;IACtD,IAAIC,iBAAiB,EAAE;IACvB,IAAIC,MAAM;IACV,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IAEJ,SAASC;QACP,MAAON,MAAMF,cAAcS,MAAM,IAAI,KAAKC,IAAI,CAACV,cAAcW,MAAM,CAACT,MAAO;YACzEA,OAAO;QACT;QACA,OAAOA,MAAMF,cAAcS,MAAM;IACnC;IAEA,SAASG;QACPR,KAAKJ,cAAcW,MAAM,CAACT;QAE1B,OAAOE,OAAO,OAAOA,OAAO,OAAOA,OAAO;IAC5C;IAEA,MAAOF,MAAMF,cAAcS,MAAM,CAAE;QACjCN,QAAQD;QACRK,wBAAwB;QAExB,MAAOC,iBAAkB;YACvBJ,KAAKJ,cAAcW,MAAM,CAACT;YAC1B,IAAIE,OAAO,KAAK;gBACd,uEAAuE;gBACvEC,YAAYH;gBACZA,OAAO;gBAEPM;gBACAF,YAAYJ;gBAEZ,MAAOA,MAAMF,cAAcS,MAAM,IAAIG,iBAAkB;oBACrDV,OAAO;gBACT;gBAEA,8BAA8B;gBAC9B,IAAIA,MAAMF,cAAcS,MAAM,IAAIT,cAAcW,MAAM,CAACT,SAAS,KAAK;oBACnE,6BAA6B;oBAC7BK,wBAAwB;oBACxB,2DAA2D;oBAC3DL,MAAMI;oBACNL,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOE;oBACnDF,QAAQD;gBACV,OAAO;oBACL,uCAAuC;oBACvC,8BAA8B;oBAC9BA,MAAMG,YAAY;gBACpB;YACF,OAAO;gBACLH,OAAO;YACT;QACF;QAEA,IAAI,CAACK,yBAAyBL,OAAOF,cAAcS,MAAM,EAAE;YACzDR,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOH,cAAcS,MAAM;QACzE;IACF;IAEA,OAAOR;AACT;AASO,SAAShB,0BACdG,OAAgB;IAEhB,MAAMD,cAAmC,CAAC;IAC1C,MAAM4B,UAAoB,EAAE;IAC5B,IAAI3B,SAAS;QACX,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIH,QAAQK,OAAO,GAAI;YAC5C,IAAIH,IAAI0B,WAAW,OAAO,cAAc;gBACtC,mEAAmE;gBACnE,kEAAkE;gBAClE,gCAAgC;gBAChCD,QAAQF,IAAI,IAAI7B,mBAAmBO;gBACnCJ,WAAW,CAACG,IAAI,GAAGyB,QAAQN,MAAM,KAAK,IAAIM,OAAO,CAAC,EAAE,GAAGA;YACzD,OAAO;gBACL5B,WAAW,CAACG,IAAI,GAAGC;YACrB;QACF;IACF;IACA,OAAOJ;AACT;AAKO,SAASD,YAAY+B,GAAiB;IAC3C,IAAI;QACF,OAAOC,OAAO,IAAIC,IAAID,OAAOD;IAC/B,EAAE,OAAOG,OAAY;QACnB,MAAM,qBAKL,CALK,IAAIC,MACR,CAAC,kBAAkB,EAAEH,OACnBD,KACA,4FAA4F,CAAC,EAC/F;YAAEK,OAAOF;QAAM,IAJX,qBAAA;mBAAA;wBAAA;0BAAA;QAKN;IACF;AACF;AAMO,SAASrC,wBAAwBO,GAAW;IACjD,MAAMiC,WAAW;QAACC,kCAAuB;QAAEC,0CAA+B;KAAC;IAC3E,KAAK,MAAMC,UAAUH,SAAU;QAC7B,IAAIjC,QAAQoC,UAAUpC,IAAIqC,UAAU,CAACD,SAAS;YAC5C,OAAOpC,IAAIwB,SAAS,CAACY,OAAOjB,MAAM;QACpC;IACF;IACA,OAAO;AACT"}