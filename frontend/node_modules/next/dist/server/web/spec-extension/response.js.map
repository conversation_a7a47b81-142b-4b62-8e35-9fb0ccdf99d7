{"version": 3, "sources": ["../../../../src/server/web/spec-extension/response.ts"], "sourcesContent": ["import { stringify<PERSON><PERSON>ie } from '../../web/spec-extension/cookies'\nimport type { I18NConfig } from '../../config-shared'\nimport { NextURL } from '../next-url'\nimport { toNodeOutgoingHttpHeaders, validateURL } from '../utils'\nimport { ReflectAdapter } from './adapters/reflect'\n\nimport { ResponseCookies } from './cookies'\n\nconst INTERNALS = Symbol('internal response')\nconst REDIRECTS = new Set([301, 302, 303, 307, 308])\n\nfunction handleMiddlewareField(\n  init: MiddlewareResponseInit | undefined,\n  headers: Headers\n) {\n  if (init?.request?.headers) {\n    if (!(init.request.headers instanceof Headers)) {\n      throw new Error('request.headers must be an instance of Headers')\n    }\n\n    const keys = []\n    for (const [key, value] of init.request.headers) {\n      headers.set('x-middleware-request-' + key, value)\n      keys.push(key)\n    }\n\n    headers.set('x-middleware-override-headers', keys.join(','))\n  }\n}\n\n/**\n * This class extends the [Web `Response` API](https://developer.mozilla.org/docs/Web/API/Response) with additional convenience methods.\n *\n * Read more: [Next.js Docs: `NextResponse`](https://nextjs.org/docs/app/api-reference/functions/next-response)\n */\nexport class NextResponse<Body = unknown> extends Response {\n  [INTERNALS]: {\n    cookies: ResponseCookies\n    url?: NextURL\n    body?: Body\n  }\n\n  constructor(body?: BodyInit | null, init: ResponseInit = {}) {\n    super(body, init)\n\n    const headers = this.headers\n    const cookies = new ResponseCookies(headers)\n\n    const cookiesProxy = new Proxy(cookies, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'delete':\n          case 'set': {\n            return (...args: [string, string]) => {\n              const result = Reflect.apply(target[prop], target, args)\n              const newHeaders = new Headers(headers)\n\n              if (result instanceof ResponseCookies) {\n                headers.set(\n                  'x-middleware-set-cookie',\n                  result\n                    .getAll()\n                    .map((cookie) => stringifyCookie(cookie))\n                    .join(',')\n                )\n              }\n\n              handleMiddlewareField(init, newHeaders)\n              return result\n            }\n          }\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n\n    this[INTERNALS] = {\n      cookies: cookiesProxy,\n      url: init.url\n        ? new NextURL(init.url, {\n            headers: toNodeOutgoingHttpHeaders(headers),\n            nextConfig: init.nextConfig,\n          })\n        : undefined,\n    }\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      cookies: this.cookies,\n      url: this.url,\n      // rest of props come from Response\n      body: this.body,\n      bodyUsed: this.bodyUsed,\n      headers: Object.fromEntries(this.headers),\n      ok: this.ok,\n      redirected: this.redirected,\n      status: this.status,\n      statusText: this.statusText,\n      type: this.type,\n    }\n  }\n\n  public get cookies() {\n    return this[INTERNALS].cookies\n  }\n\n  static json<JsonBody>(\n    body: JsonBody,\n    init?: ResponseInit\n  ): NextResponse<JsonBody> {\n    const response: Response = Response.json(body, init)\n    return new NextResponse(response.body, response)\n  }\n\n  static redirect(url: string | NextURL | URL, init?: number | ResponseInit) {\n    const status = typeof init === 'number' ? init : init?.status ?? 307\n    if (!REDIRECTS.has(status)) {\n      throw new RangeError(\n        'Failed to execute \"redirect\" on \"response\": Invalid status code'\n      )\n    }\n    const initObj = typeof init === 'object' ? init : {}\n    const headers = new Headers(initObj?.headers)\n    headers.set('Location', validateURL(url))\n\n    return new NextResponse(null, {\n      ...initObj,\n      headers,\n      status,\n    })\n  }\n\n  static rewrite(\n    destination: string | NextURL | URL,\n    init?: MiddlewareResponseInit\n  ) {\n    const headers = new Headers(init?.headers)\n    headers.set('x-middleware-rewrite', validateURL(destination))\n\n    handleMiddlewareField(init, headers)\n    return new NextResponse(null, { ...init, headers })\n  }\n\n  static next(init?: MiddlewareResponseInit) {\n    const headers = new Headers(init?.headers)\n    headers.set('x-middleware-next', '1')\n\n    handleMiddlewareField(init, headers)\n    return new NextResponse(null, { ...init, headers })\n  }\n}\n\ninterface ResponseInit extends globalThis.ResponseInit {\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig\n    trailingSlash?: boolean\n  }\n  url?: string\n}\n\ninterface ModifiedRequest {\n  /**\n   * If this is set, the request headers will be overridden with this value.\n   */\n  headers?: Headers\n}\n\ninterface MiddlewareResponseInit extends globalThis.ResponseInit {\n  /**\n   * These fields will override the request from clients.\n   */\n  request?: ModifiedRequest\n}\n"], "names": ["NextResponse", "INTERNALS", "Symbol", "REDIRECTS", "Set", "handleMiddlewareField", "init", "headers", "request", "Headers", "Error", "keys", "key", "value", "set", "push", "join", "Response", "constructor", "body", "cookies", "ResponseCookies", "cookiesProxy", "Proxy", "get", "target", "prop", "receiver", "args", "result", "Reflect", "apply", "newHeaders", "getAll", "map", "cookie", "string<PERSON><PERSON><PERSON><PERSON>", "ReflectAdapter", "url", "NextURL", "toNodeOutgoingHttpHeaders", "nextConfig", "undefined", "for", "bodyUsed", "Object", "fromEntries", "ok", "redirected", "status", "statusText", "type", "json", "response", "redirect", "has", "RangeError", "initObj", "validateURL", "rewrite", "destination", "next"], "mappings": ";;;;+BAmCaA;;;eAAAA;;;yBAnCmB;yBAER;uBAC+B;yBACxB;0BAEC;AAEhC,MAAMC,YAAYC,OAAO;AACzB,MAAMC,YAAY,IAAIC,IAAI;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAEnD,SAASC,sBACPC,IAAwC,EACxCC,OAAgB;QAEZD;IAAJ,IAAIA,yBAAAA,gBAAAA,KAAME,OAAO,qBAAbF,cAAeC,OAAO,EAAE;QAC1B,IAAI,CAAED,CAAAA,KAAKE,OAAO,CAACD,OAAO,YAAYE,OAAM,GAAI;YAC9C,MAAM,qBAA2D,CAA3D,IAAIC,MAAM,mDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEA,MAAMC,OAAO,EAAE;QACf,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIP,KAAKE,OAAO,CAACD,OAAO,CAAE;YAC/CA,QAAQO,GAAG,CAAC,0BAA0BF,KAAKC;YAC3CF,KAAKI,IAAI,CAACH;QACZ;QAEAL,QAAQO,GAAG,CAAC,iCAAiCH,KAAKK,IAAI,CAAC;IACzD;AACF;AAOO,MAAMhB,qBAAqCiB;IAOhDC,YAAYC,IAAsB,EAAEb,OAAqB,CAAC,CAAC,CAAE;QAC3D,KAAK,CAACa,MAAMb;QAEZ,MAAMC,UAAU,IAAI,CAACA,OAAO;QAC5B,MAAMa,UAAU,IAAIC,yBAAe,CAACd;QAEpC,MAAMe,eAAe,IAAIC,MAAMH,SAAS;YACtCI,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;wBAAO;4BACV,OAAO,CAAC,GAAGE;gCACT,MAAMC,SAASC,QAAQC,KAAK,CAACN,MAAM,CAACC,KAAK,EAAED,QAAQG;gCACnD,MAAMI,aAAa,IAAIvB,QAAQF;gCAE/B,IAAIsB,kBAAkBR,yBAAe,EAAE;oCACrCd,QAAQO,GAAG,CACT,2BACAe,OACGI,MAAM,GACNC,GAAG,CAAC,CAACC,SAAWC,IAAAA,wBAAe,EAACD,SAChCnB,IAAI,CAAC;gCAEZ;gCAEAX,sBAAsBC,MAAM0B;gCAC5B,OAAOH;4BACT;wBACF;oBACA;wBACE,OAAOQ,uBAAc,CAACb,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;QAEA,IAAI,CAAC1B,UAAU,GAAG;YAChBmB,SAASE;YACTgB,KAAKhC,KAAKgC,GAAG,GACT,IAAIC,gBAAO,CAACjC,KAAKgC,GAAG,EAAE;gBACpB/B,SAASiC,IAAAA,gCAAyB,EAACjC;gBACnCkC,YAAYnC,KAAKmC,UAAU;YAC7B,KACAC;QACN;IACF;IAEA,CAACxC,OAAOyC,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLvB,SAAS,IAAI,CAACA,OAAO;YACrBkB,KAAK,IAAI,CAACA,GAAG;YACb,mCAAmC;YACnCnB,MAAM,IAAI,CAACA,IAAI;YACfyB,UAAU,IAAI,CAACA,QAAQ;YACvBrC,SAASsC,OAAOC,WAAW,CAAC,IAAI,CAACvC,OAAO;YACxCwC,IAAI,IAAI,CAACA,EAAE;YACXC,YAAY,IAAI,CAACA,UAAU;YAC3BC,QAAQ,IAAI,CAACA,MAAM;YACnBC,YAAY,IAAI,CAACA,UAAU;YAC3BC,MAAM,IAAI,CAACA,IAAI;QACjB;IACF;IAEA,IAAW/B,UAAU;QACnB,OAAO,IAAI,CAACnB,UAAU,CAACmB,OAAO;IAChC;IAEA,OAAOgC,KACLjC,IAAc,EACdb,IAAmB,EACK;QACxB,MAAM+C,WAAqBpC,SAASmC,IAAI,CAACjC,MAAMb;QAC/C,OAAO,IAAIN,aAAaqD,SAASlC,IAAI,EAAEkC;IACzC;IAEA,OAAOC,SAAShB,GAA2B,EAAEhC,IAA4B,EAAE;QACzE,MAAM2C,SAAS,OAAO3C,SAAS,WAAWA,OAAOA,CAAAA,wBAAAA,KAAM2C,MAAM,KAAI;QACjE,IAAI,CAAC9C,UAAUoD,GAAG,CAACN,SAAS;YAC1B,MAAM,qBAEL,CAFK,IAAIO,WACR,oEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMC,UAAU,OAAOnD,SAAS,WAAWA,OAAO,CAAC;QACnD,MAAMC,UAAU,IAAIE,QAAQgD,2BAAAA,QAASlD,OAAO;QAC5CA,QAAQO,GAAG,CAAC,YAAY4C,IAAAA,kBAAW,EAACpB;QAEpC,OAAO,IAAItC,aAAa,MAAM;YAC5B,GAAGyD,OAAO;YACVlD;YACA0C;QACF;IACF;IAEA,OAAOU,QACLC,WAAmC,EACnCtD,IAA6B,EAC7B;QACA,MAAMC,UAAU,IAAIE,QAAQH,wBAAAA,KAAMC,OAAO;QACzCA,QAAQO,GAAG,CAAC,wBAAwB4C,IAAAA,kBAAW,EAACE;QAEhDvD,sBAAsBC,MAAMC;QAC5B,OAAO,IAAIP,aAAa,MAAM;YAAE,GAAGM,IAAI;YAAEC;QAAQ;IACnD;IAEA,OAAOsD,KAAKvD,IAA6B,EAAE;QACzC,MAAMC,UAAU,IAAIE,QAAQH,wBAAAA,KAAMC,OAAO;QACzCA,QAAQO,GAAG,CAAC,qBAAqB;QAEjCT,sBAAsBC,MAAMC;QAC5B,OAAO,IAAIP,aAAa,MAAM;YAAE,GAAGM,IAAI;YAAEC;QAAQ;IACnD;AACF"}