{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-page-normalizer.ts"], "sourcesContent": ["import { PAGE_TYPES } from '../../../../lib/page-types'\nimport { AbsoluteFilenameNormalizer } from '../../absolute-filename-normalizer'\n\n/**\n * DevAppPageNormalizer is a normalizer that is used to normalize a pathname\n * to a page in the `app` directory.\n */\nexport class DevAppPageNormalizer extends AbsoluteFilenameNormalizer {\n  constructor(appDir: string, extensions: ReadonlyArray<string>) {\n    super(appDir, extensions, PAGE_TYPES.APP)\n  }\n}\n"], "names": ["DevAppPageNormalizer", "AbsoluteFilenameNormalizer", "constructor", "appDir", "extensions", "PAGE_TYPES", "APP"], "mappings": ";;;;+BAOaA;;;eAAAA;;;2BAPc;4CACgB;AAMpC,MAAMA,6BAA6BC,sDAA0B;IAClEC,YAAYC,MAAc,EAAEC,UAAiC,CAAE;QAC7D,KAAK,CAACD,QAAQC,YAAYC,qBAAU,CAACC,GAAG;IAC1C;AACF"}