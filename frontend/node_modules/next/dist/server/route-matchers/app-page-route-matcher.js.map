{"version": 3, "sources": ["../../../src/server/route-matchers/app-page-route-matcher.ts"], "sourcesContent": ["import { RouteMatcher } from './route-matcher'\nimport type { AppPageRouteDefinition } from '../route-definitions/app-page-route-definition'\n\nexport class AppPageRouteMatcher extends RouteMatcher<AppPageRouteDefinition> {\n  public get identity(): string {\n    return `${this.definition.pathname}?__nextPage=${this.definition.page}`\n  }\n}\n"], "names": ["AppPageRouteMatcher", "RouteMatcher", "identity", "definition", "pathname", "page"], "mappings": ";;;;+BAGaA;;;eAAAA;;;8BAHgB;AAGtB,MAAMA,4BAA4BC,0BAAY;IACnD,IAAWC,WAAmB;QAC5B,OAAO,GAAG,IAAI,CAACC,UAAU,CAACC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAACD,UAAU,CAACE,IAAI,EAAE;IACzE;AACF"}