{"version": 3, "sources": ["../../../../src/server/app-render/rsc/postpone.ts"], "sourcesContent": ["/*\n\nFiles in the rsc directory are meant to be packaged as part of the RSC graph using next-app-loader.\n\n*/\n\n// When postpone is available in canary React we can switch to importing it directly\nexport { Postpone } from '../dynamic-rendering'\n"], "names": ["Postpone"], "mappings": "AAAA;;;;AAIA,GAEA,oFAAoF;;;;;+BAC3EA;;;eAAAA,0BAAQ;;;kCAAQ"}