/*

Files in the rsc directory are meant to be packaged as part of the RSC graph using next-app-loader.

*/ // When postpone is available in canary React we can switch to importing it directly
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "Postpone", {
    enumerable: true,
    get: function() {
        return _dynamicrendering.Postpone;
    }
});
const _dynamicrendering = require("../dynamic-rendering");

//# sourceMappingURL=postpone.js.map