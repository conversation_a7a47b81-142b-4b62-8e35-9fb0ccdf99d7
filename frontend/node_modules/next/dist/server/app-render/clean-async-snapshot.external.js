// Share the instance module in the next-shared layer
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "runInCleanSnapshot", {
    enumerable: true,
    get: function() {
        return _cleanasyncsnapshotinstance.runInCleanSnapshot;
    }
});
const _cleanasyncsnapshotinstance = require("./clean-async-snapshot-instance");

//# sourceMappingURL=clean-async-snapshot.external.js.map