{"version": 3, "sources": ["../../../src/server/app-render/action-utils.ts"], "sourcesContent": ["import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport { workAsyncStorage } from './work-async-storage.external'\n\n// This function creates a Flight-acceptable server module map proxy from our\n// Server Reference Manifest similar to our client module map.\n// This is because our manifest contains a lot of internal Next.js data that\n// are relevant to the runtime, workers, etc. that <PERSON>act doesn't need to know.\nexport function createServerModuleMap({\n  serverActionsManifest,\n}: {\n  serverActionsManifest: ActionManifest\n}) {\n  return new Proxy(\n    {},\n    {\n      get: (_, id: string) => {\n        const workers =\n          serverActionsManifest[\n            process.env.NEXT_RUNTIME === 'edge' ? 'edge' : 'node'\n          ][id].workers\n\n        const workStore = workAsyncStorage.getStore()\n\n        let workerEntry:\n          | { moduleId: string | number; async: boolean }\n          | undefined\n\n        if (workStore) {\n          workerEntry = workers[normalizeWorkerPageName(workStore.page)]\n        } else {\n          // If there's no work store defined, we can assume that a server\n          // module map is needed during module evaluation, e.g. to create a\n          // server action using a higher-order function. Therefore it should be\n          // safe to return any entry from the manifest that matches the action\n          // ID. They all refer to the same module ID, which must also exist in\n          // the current page bundle. TODO: This is currently not guaranteed in\n          // Turbopack, and needs to be fixed.\n          workerEntry = Object.values(workers).at(0)\n        }\n\n        if (!workerEntry) {\n          return undefined\n        }\n\n        const { moduleId, async } = workerEntry\n\n        return { id: moduleId, name: id, chunks: [], async }\n      },\n    }\n  )\n}\n\n/**\n * Checks if the requested action has a worker for the current page.\n * If not, it returns the first worker that has a handler for the action.\n */\nexport function selectWorkerForForwarding(\n  actionId: string,\n  pageName: string,\n  serverActionsManifest: ActionManifest\n) {\n  const workers =\n    serverActionsManifest[\n      process.env.NEXT_RUNTIME === 'edge' ? 'edge' : 'node'\n    ][actionId]?.workers\n  const workerName = normalizeWorkerPageName(pageName)\n\n  // no workers, nothing to forward to\n  if (!workers) return\n\n  // if there is a worker for this page, no need to forward it.\n  if (workers[workerName]) {\n    return\n  }\n\n  // otherwise, grab the first worker that has a handler for this action id\n  return denormalizeWorkerPageName(Object.keys(workers)[0])\n}\n\n/**\n * The flight entry loader keys actions by bundlePath.\n * bundlePath corresponds with the relative path (including 'app') to the page entrypoint.\n */\nfunction normalizeWorkerPageName(pageName: string) {\n  if (pathHasPrefix(pageName, 'app')) {\n    return pageName\n  }\n\n  return 'app' + pageName\n}\n\n/**\n * Converts a bundlePath (relative path to the entrypoint) to a routable page name\n */\nfunction denormalizeWorkerPageName(bundlePath: string) {\n  return normalizeAppPath(removePathPrefix(bundlePath, 'app'))\n}\n"], "names": ["createServerModuleMap", "selectWorkerForForwarding", "serverActionsManifest", "Proxy", "get", "_", "id", "workers", "process", "env", "NEXT_RUNTIME", "workStore", "workAsyncStorage", "getStore", "workerEntry", "normalizeWorkerPageName", "page", "Object", "values", "at", "undefined", "moduleId", "async", "name", "chunks", "actionId", "pageName", "worker<PERSON>ame", "denormalizeWorkerPageName", "keys", "pathHasPrefix", "bundlePath", "normalizeAppPath", "removePathPrefix"], "mappings": ";;;;;;;;;;;;;;;IAUgBA,qBAAqB;eAArBA;;IAiDAC,yBAAyB;eAAzBA;;;0BA1DiB;+BACH;kCACG;0CACA;AAM1B,SAASD,sBAAsB,EACpCE,qBAAqB,EAGtB;IACC,OAAO,IAAIC,MACT,CAAC,GACD;QACEC,KAAK,CAACC,GAAGC;YACP,MAAMC,UACJL,qBAAqB,CACnBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,SAAS,OAChD,CAACJ,GAAG,CAACC,OAAO;YAEf,MAAMI,YAAYC,0CAAgB,CAACC,QAAQ;YAE3C,IAAIC;YAIJ,IAAIH,WAAW;gBACbG,cAAcP,OAAO,CAACQ,wBAAwBJ,UAAUK,IAAI,EAAE;YAChE,OAAO;gBACL,gEAAgE;gBAChE,kEAAkE;gBAClE,sEAAsE;gBACtE,qEAAqE;gBACrE,qEAAqE;gBACrE,qEAAqE;gBACrE,oCAAoC;gBACpCF,cAAcG,OAAOC,MAAM,CAACX,SAASY,EAAE,CAAC;YAC1C;YAEA,IAAI,CAACL,aAAa;gBAChB,OAAOM;YACT;YAEA,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGR;YAE5B,OAAO;gBAAER,IAAIe;gBAAUE,MAAMjB;gBAAIkB,QAAQ,EAAE;gBAAEF;YAAM;QACrD;IACF;AAEJ;AAMO,SAASrB,0BACdwB,QAAgB,EAChBC,QAAgB,EAChBxB,qBAAqC;QAGnCA;IADF,MAAMK,WACJL,mCAAAA,qBAAqB,CACnBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,SAAS,OAChD,CAACe,SAAS,qBAFXvB,iCAEaK,OAAO;IACtB,MAAMoB,aAAaZ,wBAAwBW;IAE3C,oCAAoC;IACpC,IAAI,CAACnB,SAAS;IAEd,6DAA6D;IAC7D,IAAIA,OAAO,CAACoB,WAAW,EAAE;QACvB;IACF;IAEA,yEAAyE;IACzE,OAAOC,0BAA0BX,OAAOY,IAAI,CAACtB,QAAQ,CAAC,EAAE;AAC1D;AAEA;;;CAGC,GACD,SAASQ,wBAAwBW,QAAgB;IAC/C,IAAII,IAAAA,4BAAa,EAACJ,UAAU,QAAQ;QAClC,OAAOA;IACT;IAEA,OAAO,QAAQA;AACjB;AAEA;;CAEC,GACD,SAASE,0BAA0BG,UAAkB;IACnD,OAAOC,IAAAA,0BAAgB,EAACC,IAAAA,kCAAgB,EAACF,YAAY;AACvD"}