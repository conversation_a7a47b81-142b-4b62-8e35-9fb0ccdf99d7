{"version": 3, "sources": ["../../src/server/get-route-from-entrypoint.ts"], "sourcesContent": ["import getAppRouteFromEntrypoint from './get-app-route-from-entrypoint'\nimport matchBundle from './match-bundle'\n\n// matches pages/:page*.js\nconst SERVER_ROUTE_NAME_REGEX = /^pages[/\\\\](.*)$/\n\n// matches static/pages/:page*.js\nconst BROWSER_ROUTE_NAME_REGEX = /^static[/\\\\]pages[/\\\\](.*)$/\n\nexport default function getRouteFromEntrypoint(\n  entryFile: string,\n  app?: boolean\n): string | null {\n  let pagePath = matchBundle(SERVER_ROUTE_NAME_REGEX, entryFile)\n\n  if (pagePath) {\n    return pagePath\n  }\n\n  if (app) {\n    pagePath = getAppRouteFromEntrypoint(entryFile)\n    if (pagePath) return pagePath\n  }\n\n  // Potentially the passed item is a browser bundle so we try to match that also\n  return matchBundle(BROWSER_ROUTE_NAME_REGEX, entryFile)\n}\n"], "names": ["getRouteFromEntrypoint", "SERVER_ROUTE_NAME_REGEX", "BROWSER_ROUTE_NAME_REGEX", "entryFile", "app", "pagePath", "matchBundle", "getAppRouteFromEntrypoint"], "mappings": ";;;;+BASA;;;eAAwBA;;;kFATc;oEACd;;;;;;AAExB,0BAA0B;AAC1B,MAAMC,0BAA0B;AAEhC,iCAAiC;AACjC,MAAMC,2BAA2B;AAElB,SAASF,uBACtBG,SAAiB,EACjBC,GAAa;IAEb,IAAIC,WAAWC,IAAAA,oBAAW,EAACL,yBAAyBE;IAEpD,IAAIE,UAAU;QACZ,OAAOA;IACT;IAEA,IAAID,KAAK;QACPC,WAAWE,IAAAA,kCAAyB,EAACJ;QACrC,IAAIE,UAAU,OAAOA;IACvB;IAEA,+EAA+E;IAC/E,OAAOC,IAAAA,oBAAW,EAACJ,0BAA0BC;AAC/C"}