{"version": 3, "sources": ["../../../../src/server/route-matcher-providers/dev/dev-pages-api-route-matcher-provider.ts"], "sourcesContent": ["import type { <PERSON><PERSON>eader } from './helpers/file-reader/file-reader'\nimport {\n  PagesAPILocaleRouteMatcher,\n  PagesAPIRouteMatcher,\n} from '../../route-matchers/pages-api-route-matcher'\nimport { RouteKind } from '../../route-kind'\nimport path from 'path'\nimport type { LocaleRouteNormalizer } from '../../normalizers/locale-route-normalizer'\nimport { FileCacheRouteMatcherProvider } from './file-cache-route-matcher-provider'\nimport { DevPagesNormalizers } from '../../normalizers/built/pages'\n\nexport class DevPagesAPIRouteMatcherProvider extends FileCacheRouteMatcherProvider<PagesAPIRouteMatcher> {\n  private readonly expression: RegExp\n  private readonly normalizers: DevPagesNormalizers\n\n  constructor(\n    private readonly pagesDir: string,\n    private readonly extensions: ReadonlyArray<string>,\n    reader: FileReader,\n    private readonly localeNormalizer?: LocaleRouteNormalizer\n  ) {\n    super(pagesDir, reader)\n\n    // Match any route file that ends with `/${filename}.${extension}` under the\n    // pages directory.\n    this.expression = new RegExp(`\\\\.(?:${extensions.join('|')})$`)\n\n    this.normalizers = new DevPagesNormalizers(pagesDir, extensions)\n  }\n\n  private test(filename: string): boolean {\n    // If the file does not end in the correct extension it's not a match.\n    if (!this.expression.test(filename)) return false\n\n    // Pages API routes must exist in the pages directory with the `/api/`\n    // prefix. The pathnames being tested here though are the full filenames,\n    // so we need to include the pages directory.\n\n    // TODO: could path separator normalization be needed here?\n    if (filename.startsWith(path.join(this.pagesDir, '/api/'))) return true\n\n    for (const extension of this.extensions) {\n      // We can also match if we have `pages/api.${extension}`, so check to\n      // see if it's a match.\n      if (filename === path.join(this.pagesDir, `api.${extension}`)) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  protected async transform(\n    files: ReadonlyArray<string>\n  ): Promise<ReadonlyArray<PagesAPIRouteMatcher>> {\n    const matchers: Array<PagesAPIRouteMatcher> = []\n    for (const filename of files) {\n      // If the file isn't a match for this matcher, then skip it.\n      if (!this.test(filename)) continue\n\n      const pathname = this.normalizers.pathname.normalize(filename)\n      const page = this.normalizers.page.normalize(filename)\n      const bundlePath = this.normalizers.bundlePath.normalize(filename)\n\n      if (this.localeNormalizer) {\n        matchers.push(\n          new PagesAPILocaleRouteMatcher({\n            kind: RouteKind.PAGES_API,\n            pathname,\n            page,\n            bundlePath,\n            filename,\n            i18n: {},\n          })\n        )\n      } else {\n        matchers.push(\n          new PagesAPIRouteMatcher({\n            kind: RouteKind.PAGES_API,\n            pathname,\n            page,\n            bundlePath,\n            filename,\n          })\n        )\n      }\n    }\n\n    return matchers\n  }\n}\n"], "names": ["DevPagesAPIRouteMatcherProvider", "FileCacheRouteMatcherProvider", "constructor", "pagesDir", "extensions", "reader", "localeNormalizer", "expression", "RegExp", "join", "normalizers", "DevPagesNormalizers", "test", "filename", "startsWith", "path", "extension", "transform", "files", "matchers", "pathname", "normalize", "page", "bundlePath", "push", "PagesAPILocaleRouteMatcher", "kind", "RouteKind", "PAGES_API", "i18n", "PagesAPIRouteMatcher"], "mappings": ";;;;+BAWaA;;;eAAAA;;;sCAPN;2BACmB;6DACT;+CAE6B;uBACV;;;;;;AAE7B,MAAMA,wCAAwCC,4DAA6B;IAIhFC,YACE,AAAiBC,QAAgB,EACjC,AAAiBC,UAAiC,EAClDC,MAAkB,EAClB,AAAiBC,gBAAwC,CACzD;QACA,KAAK,CAACH,UAAUE,cALCF,WAAAA,eACAC,aAAAA,iBAEAE,mBAAAA;QAIjB,4EAA4E;QAC5E,mBAAmB;QACnB,IAAI,CAACC,UAAU,GAAG,IAAIC,OAAO,CAAC,MAAM,EAAEJ,WAAWK,IAAI,CAAC,KAAK,EAAE,CAAC;QAE9D,IAAI,CAACC,WAAW,GAAG,IAAIC,0BAAmB,CAACR,UAAUC;IACvD;IAEQQ,KAAKC,QAAgB,EAAW;QACtC,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAACN,UAAU,CAACK,IAAI,CAACC,WAAW,OAAO;QAE5C,sEAAsE;QACtE,yEAAyE;QACzE,6CAA6C;QAE7C,2DAA2D;QAC3D,IAAIA,SAASC,UAAU,CAACC,aAAI,CAACN,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE,WAAW,OAAO;QAEnE,KAAK,MAAMa,aAAa,IAAI,CAACZ,UAAU,CAAE;YACvC,qEAAqE;YACrE,uBAAuB;YACvB,IAAIS,aAAaE,aAAI,CAACN,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE,CAAC,IAAI,EAAEa,WAAW,GAAG;gBAC7D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAgBC,UACdC,KAA4B,EACkB;QAC9C,MAAMC,WAAwC,EAAE;QAChD,KAAK,MAAMN,YAAYK,MAAO;YAC5B,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAACN,IAAI,CAACC,WAAW;YAE1B,MAAMO,WAAW,IAAI,CAACV,WAAW,CAACU,QAAQ,CAACC,SAAS,CAACR;YACrD,MAAMS,OAAO,IAAI,CAACZ,WAAW,CAACY,IAAI,CAACD,SAAS,CAACR;YAC7C,MAAMU,aAAa,IAAI,CAACb,WAAW,CAACa,UAAU,CAACF,SAAS,CAACR;YAEzD,IAAI,IAAI,CAACP,gBAAgB,EAAE;gBACzBa,SAASK,IAAI,CACX,IAAIC,gDAA0B,CAAC;oBAC7BC,MAAMC,oBAAS,CAACC,SAAS;oBACzBR;oBACAE;oBACAC;oBACAV;oBACAgB,MAAM,CAAC;gBACT;YAEJ,OAAO;gBACLV,SAASK,IAAI,CACX,IAAIM,0CAAoB,CAAC;oBACvBJ,MAAMC,oBAAS,CAACC,SAAS;oBACzBR;oBACAE;oBACAC;oBACAV;gBACF;YAEJ;QACF;QAEA,OAAOM;IACT;AACF"}