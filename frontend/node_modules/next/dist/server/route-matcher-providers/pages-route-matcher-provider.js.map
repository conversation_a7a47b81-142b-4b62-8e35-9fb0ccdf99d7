{"version": 3, "sources": ["../../../src/server/route-matcher-providers/pages-route-matcher-provider.ts"], "sourcesContent": ["import { isAPIRoute } from '../../lib/is-api-route'\nimport { BLOCKED_PAGES, PAGES_MANIFEST } from '../../shared/lib/constants'\nimport { RouteKind } from '../route-kind'\nimport {\n  PagesLocaleRouteMatcher,\n  PagesRouteMatcher,\n} from '../route-matchers/pages-route-matcher'\nimport type {\n  <PERSON>ife<PERSON>,\n  ManifestLoader,\n} from './helpers/manifest-loaders/manifest-loader'\nimport { ManifestRouteMatcherProvider } from './manifest-route-matcher-provider'\nimport type { I18NProvider } from '../lib/i18n-provider'\nimport { PagesNormalizers } from '../normalizers/built/pages'\n\nexport class PagesRouteMatcherProvider extends ManifestRouteMatcherProvider<PagesRouteMatcher> {\n  private readonly normalizers: PagesNormalizers\n\n  constructor(\n    distDir: string,\n    manifestLoader: ManifestLoader,\n    private readonly i18nProvider?: I18NProvider\n  ) {\n    super(PAGES_MANIFEST, manifestLoader)\n\n    this.normalizers = new PagesNormalizers(distDir)\n  }\n\n  protected async transform(\n    manifest: Manifest\n  ): Promise<ReadonlyArray<PagesRouteMatcher>> {\n    // This matcher is only for Pages routes, not Pages API routes which are\n    // included in this manifest.\n    const pathnames = Object.keys(manifest)\n      .filter((pathname) => !isAPIRoute(pathname))\n      // Remove any blocked pages (page that can't be routed to, like error or\n      // internal pages).\n      .filter((pathname) => {\n        const normalized =\n          this.i18nProvider?.analyze(pathname).pathname ?? pathname\n\n        // Skip any blocked pages.\n        if (BLOCKED_PAGES.includes(normalized)) return false\n\n        return true\n      })\n\n    const matchers: Array<PagesRouteMatcher> = []\n    for (const page of pathnames) {\n      if (this.i18nProvider) {\n        // Match the locale on the page name, or default to the default locale.\n        const { detectedLocale, pathname } = this.i18nProvider.analyze(page)\n\n        matchers.push(\n          new PagesLocaleRouteMatcher({\n            kind: RouteKind.PAGES,\n            pathname,\n            page,\n            bundlePath: this.normalizers.bundlePath.normalize(page),\n            filename: this.normalizers.filename.normalize(manifest[page]),\n            i18n: {\n              locale: detectedLocale,\n            },\n          })\n        )\n      } else {\n        matchers.push(\n          new PagesRouteMatcher({\n            kind: RouteKind.PAGES,\n            // In `pages/`, the page is the same as the pathname.\n            pathname: page,\n            page,\n            bundlePath: this.normalizers.bundlePath.normalize(page),\n            filename: this.normalizers.filename.normalize(manifest[page]),\n          })\n        )\n      }\n    }\n\n    return matchers\n  }\n}\n"], "names": ["PagesRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "i18nProvider", "PAGES_MANIFEST", "normalizers", "PagesNormalizers", "transform", "manifest", "pathnames", "Object", "keys", "filter", "pathname", "isAPIRoute", "normalized", "analyze", "BLOCKED_PAGES", "includes", "matchers", "page", "detectedLocale", "push", "PagesLocaleRouteMatcher", "kind", "RouteKind", "PAGES", "bundlePath", "normalize", "filename", "i18n", "locale", "PagesRouteMatcher"], "mappings": ";;;;+BAeaA;;;eAAAA;;;4BAfc;2BACmB;2BACpB;mCAInB;8CAKsC;uBAEZ;AAE1B,MAAMA,kCAAkCC,0DAA4B;IAGzEC,YACEC,OAAe,EACfC,cAA8B,EAC9B,AAAiBC,YAA2B,CAC5C;QACA,KAAK,CAACC,yBAAc,EAAEF,sBAFLC,eAAAA;QAIjB,IAAI,CAACE,WAAW,GAAG,IAAIC,uBAAgB,CAACL;IAC1C;IAEA,MAAgBM,UACdC,QAAkB,EACyB;QAC3C,wEAAwE;QACxE,6BAA6B;QAC7B,MAAMC,YAAYC,OAAOC,IAAI,CAACH,UAC3BI,MAAM,CAAC,CAACC,WAAa,CAACC,IAAAA,sBAAU,EAACD,UAClC,wEAAwE;QACxE,mBAAmB;SAClBD,MAAM,CAAC,CAACC;gBAEL;YADF,MAAME,aACJ,EAAA,qBAAA,IAAI,CAACZ,YAAY,qBAAjB,mBAAmBa,OAAO,CAACH,UAAUA,QAAQ,KAAIA;YAEnD,0BAA0B;YAC1B,IAAII,wBAAa,CAACC,QAAQ,CAACH,aAAa,OAAO;YAE/C,OAAO;QACT;QAEF,MAAMI,WAAqC,EAAE;QAC7C,KAAK,MAAMC,QAAQX,UAAW;YAC5B,IAAI,IAAI,CAACN,YAAY,EAAE;gBACrB,uEAAuE;gBACvE,MAAM,EAAEkB,cAAc,EAAER,QAAQ,EAAE,GAAG,IAAI,CAACV,YAAY,CAACa,OAAO,CAACI;gBAE/DD,SAASG,IAAI,CACX,IAAIC,0CAAuB,CAAC;oBAC1BC,MAAMC,oBAAS,CAACC,KAAK;oBACrBb;oBACAO;oBACAO,YAAY,IAAI,CAACtB,WAAW,CAACsB,UAAU,CAACC,SAAS,CAACR;oBAClDS,UAAU,IAAI,CAACxB,WAAW,CAACwB,QAAQ,CAACD,SAAS,CAACpB,QAAQ,CAACY,KAAK;oBAC5DU,MAAM;wBACJC,QAAQV;oBACV;gBACF;YAEJ,OAAO;gBACLF,SAASG,IAAI,CACX,IAAIU,oCAAiB,CAAC;oBACpBR,MAAMC,oBAAS,CAACC,KAAK;oBACrB,qDAAqD;oBACrDb,UAAUO;oBACVA;oBACAO,YAAY,IAAI,CAACtB,WAAW,CAACsB,UAAU,CAACC,SAAS,CAACR;oBAClDS,UAAU,IAAI,CAACxB,WAAW,CAACwB,QAAQ,CAACD,SAAS,CAACpB,QAAQ,CAACY,KAAK;gBAC9D;YAEJ;QACF;QAEA,OAAOD;IACT;AACF"}