{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../config-shared'\n\nimport '../require-hook'\nimport '../node-environment'\n\nimport { reduceAppConfig } from '../../build/utils'\nimport { collectSegments } from '../../build/segment-config/app/app-segments'\nimport type { StaticPathsResult } from '../../build/static-paths/types'\nimport { loadComponents } from '../load-components'\nimport { setHttpClientAndAgentOptions } from '../setup-http-agent-env'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport { isAppPageRouteModule } from '../route-modules/checks'\nimport {\n  checkIsRoutePPREnabled,\n  type ExperimentalPPRConfig,\n} from '../lib/experimental/ppr'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { collectRootParamKeys } from '../../build/segment-config/app/collect-root-param-keys'\nimport { buildAppStaticPaths } from '../../build/static-paths/app'\nimport { buildPagesStaticPaths } from '../../build/static-paths/pages'\nimport { createIncrementalCache } from '../../export/helpers/create-incremental-cache'\n\ntype RuntimeConfig = {\n  pprConfig: ExperimentalPPRConfig | undefined\n  configFileName: string\n  publicRuntimeConfig: { [key: string]: any }\n  serverRuntimeConfig: { [key: string]: any }\n  dynamicIO: boolean\n}\n\n// we call getStaticPaths in a separate process to ensure\n// side-effects aren't relied on in dev that will break\n// during a production build\nexport async function loadStaticPaths({\n  dir,\n  distDir,\n  pathname,\n  config,\n  httpAgentOptions,\n  locales,\n  defaultLocale,\n  isAppPath,\n  page,\n  isrFlushToDisk,\n  fetchCacheKeyPrefix,\n  maxMemoryCacheSize,\n  requestHeaders,\n  cacheHandler,\n  cacheHandlers,\n  cacheLifeProfiles,\n  nextConfigOutput,\n  buildId,\n  authInterrupts,\n  sriEnabled,\n}: {\n  dir: string\n  distDir: string\n  pathname: string\n  config: RuntimeConfig\n  httpAgentOptions: NextConfigComplete['httpAgentOptions']\n  locales?: readonly string[]\n  defaultLocale?: string\n  isAppPath: boolean\n  page: string\n  isrFlushToDisk?: boolean\n  fetchCacheKeyPrefix?: string\n  maxMemoryCacheSize?: number\n  requestHeaders: IncrementalCache['requestHeaders']\n  cacheHandler?: string\n  cacheHandlers?: NextConfigComplete['experimental']['cacheHandlers']\n  cacheLifeProfiles?: {\n    [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n  }\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  buildId: string\n  authInterrupts: boolean\n  sriEnabled: boolean\n}): Promise<Partial<StaticPathsResult>> {\n  // this needs to be initialized before loadComponents otherwise\n  // \"use cache\" could be missing it's cache handlers\n  await createIncrementalCache({\n    dir,\n    distDir,\n    cacheHandler,\n    cacheHandlers,\n    requestHeaders,\n    fetchCacheKeyPrefix,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  // update work memory runtime-config\n  require('../../shared/lib/runtime-config.external').setConfig(config)\n  setHttpClientAndAgentOptions({\n    httpAgentOptions,\n  })\n\n  const components = await loadComponents({\n    distDir,\n    // In `pages/`, the page is the same as the pathname.\n    page: page || pathname,\n    isAppPath,\n    isDev: true,\n    sriEnabled,\n  })\n\n  if (isAppPath) {\n    const segments = await collectSegments(components)\n\n    const isRoutePPREnabled =\n      isAppPageRouteModule(components.routeModule) &&\n      checkIsRoutePPREnabled(config.pprConfig, reduceAppConfig(segments))\n\n    const rootParamKeys = collectRootParamKeys(components)\n\n    return buildAppStaticPaths({\n      dir,\n      page: pathname,\n      dynamicIO: config.dynamicIO,\n      segments,\n      distDir,\n      requestHeaders,\n      cacheHandler,\n      cacheLifeProfiles,\n      isrFlushToDisk,\n      fetchCacheKeyPrefix,\n      maxMemoryCacheSize,\n      ComponentMod: components.ComponentMod,\n      nextConfigOutput,\n      isRoutePPREnabled,\n      buildId,\n      authInterrupts,\n      rootParamKeys,\n    })\n  } else if (!components.getStaticPaths) {\n    // We shouldn't get to this point since the worker should only be called for\n    // SSG pages with getStaticPaths.\n    throw new InvariantError(\n      `Failed to load page with getStaticPaths for ${pathname}`\n    )\n  }\n\n  return buildPagesStaticPaths({\n    page: pathname,\n    getStaticPaths: components.getStaticPaths,\n    configFileName: config.configFileName,\n    locales,\n    defaultLocale,\n  })\n}\n"], "names": ["loadStaticPaths", "dir", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheLifeProfiles", "nextConfigOutput", "buildId", "authInterrupts", "sriEnabled", "createIncrementalCache", "flushToDisk", "cacheMaxMemorySize", "require", "setConfig", "setHttpClientAndAgentOptions", "components", "loadComponents", "isDev", "segments", "collectSegments", "isRoutePPREnabled", "isAppPageRouteModule", "routeModule", "checkIsRoutePPREnabled", "pprConfig", "reduceAppConfig", "rootParamKeys", "collectRootParamKeys", "buildAppStaticPaths", "dynamicIO", "ComponentMod", "getStaticPaths", "InvariantError", "buildPagesStaticPaths", "configFileName"], "mappings": ";;;;+BAiCsBA;;;eAAAA;;;QA/Bf;QACA;uBAEyB;6BACA;gCAED;mCACc;wBAER;qBAI9B;gCACwB;sCACM;qBACD;uBACE;wCACC;AAahC,eAAeA,gBAAgB,EACpCC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,aAAa,EACbC,iBAAiB,EACjBC,gBAAgB,EAChBC,OAAO,EACPC,cAAc,EACdC,UAAU,EAwBX;IACC,+DAA+D;IAC/D,mDAAmD;IACnD,MAAMC,IAAAA,8CAAsB,EAAC;QAC3BpB;QACAC;QACAY;QACAC;QACAF;QACAF;QACAW,aAAaZ;QACba,oBAAoBX;IACtB;IAEA,oCAAoC;IACpCY,QAAQ,4CAA4CC,SAAS,CAACrB;IAC9DsB,IAAAA,+CAA4B,EAAC;QAC3BrB;IACF;IAEA,MAAMsB,aAAa,MAAMC,IAAAA,8BAAc,EAAC;QACtC1B;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;QACAqB,OAAO;QACPT;IACF;IAEA,IAAIZ,WAAW;QACb,MAAMsB,WAAW,MAAMC,IAAAA,4BAAe,EAACJ;QAEvC,MAAMK,oBACJC,IAAAA,4BAAoB,EAACN,WAAWO,WAAW,KAC3CC,IAAAA,2BAAsB,EAAC/B,OAAOgC,SAAS,EAAEC,IAAAA,sBAAe,EAACP;QAE3D,MAAMQ,gBAAgBC,IAAAA,0CAAoB,EAACZ;QAE3C,OAAOa,IAAAA,wBAAmB,EAAC;YACzBvC;YACAQ,MAAMN;YACNsC,WAAWrC,OAAOqC,SAAS;YAC3BX;YACA5B;YACAW;YACAC;YACAE;YACAN;YACAC;YACAC;YACA8B,cAAcf,WAAWe,YAAY;YACrCzB;YACAe;YACAd;YACAC;YACAmB;QACF;IACF,OAAO,IAAI,CAACX,WAAWgB,cAAc,EAAE;QACrC,4EAA4E;QAC5E,iCAAiC;QACjC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,CAAC,4CAA4C,EAAEzC,UAAU,GADrD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAO0C,IAAAA,4BAAqB,EAAC;QAC3BpC,MAAMN;QACNwC,gBAAgBhB,WAAWgB,cAAc;QACzCG,gBAAgB1C,OAAO0C,cAAc;QACrCxC;QACAC;IACF;AACF"}