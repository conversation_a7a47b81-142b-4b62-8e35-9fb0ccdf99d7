{"version": 3, "sources": ["../../../src/server/dev/hot-reloader-webpack.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../config-shared'\nimport type { CustomRoutes } from '../../lib/load-custom-routes'\nimport type { Duplex } from 'stream'\nimport type { Telemetry } from '../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlObject } from 'url'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\n\nimport { webpack, StringXor } from 'next/dist/compiled/webpack/webpack'\nimport {\n  getOverlayMiddleware,\n  getSourceMapMiddleware,\n} from '../../client/components/react-dev-overlay/server/middleware-webpack'\nimport { WebpackHotMiddleware } from './hot-middleware'\nimport { join, relative, isAbsolute, posix } from 'path'\nimport {\n  createEntrypoints,\n  createPagesMapping,\n  finalizeEntrypoint,\n  getClientEntry,\n  getEdgeServerEntry,\n  getAppEntry,\n  runDependingOnPageType,\n  getStaticInfoIncludingLayouts,\n  getInstrumentationEntry,\n} from '../../build/entries'\nimport { watchCompilers } from '../../build/output'\nimport * as Log from '../../build/output/log'\nimport getBaseWebpackConfig, {\n  loadProjectInfo,\n} from '../../build/webpack-config'\nimport { APP_DIR_ALIAS, WEBPACK_LAYERS } from '../../lib/constants'\nimport { recursiveDelete } from '../../lib/recursive-delete'\nimport {\n  BLOCKED_PAGES,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  COMPILER_NAMES,\n  RSC_MODULE_TYPES,\n} from '../../shared/lib/constants'\nimport type { __ApiPreviewProps } from '../api-utils'\nimport { getPathMatch } from '../../shared/lib/router/utils/path-match'\nimport { findPageFile } from '../lib/find-page-file'\nimport {\n  BUILDING,\n  getEntries,\n  EntryTypes,\n  getInvalidator,\n  onDemandEntryHandler,\n} from './on-demand-entry-handler'\nimport { denormalizePagePath } from '../../shared/lib/page-path/denormalize-page-path'\nimport { normalizePathSep } from '../../shared/lib/page-path/normalize-path-sep'\nimport getRouteFromEntrypoint from '../get-route-from-entrypoint'\nimport {\n  difference,\n  isInstrumentationHookFile,\n  isMiddlewareFile,\n  isMiddlewareFilename,\n} from '../../build/utils'\nimport { DecodeError } from '../../shared/lib/utils'\nimport { type Span, trace } from '../../trace'\nimport { getProperError } from '../../lib/is-error'\nimport ws from 'next/dist/compiled/ws'\nimport { existsSync, promises as fs } from 'fs'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\nimport { parseVersionInfo } from './parse-version-info'\nimport type { VersionInfo } from './parse-version-info'\nimport { isAPIRoute } from '../../lib/is-api-route'\nimport { getRouteLoaderEntry } from '../../build/webpack/loaders/next-route-loader'\nimport {\n  isInternalComponent,\n  isNonRoutePagesPage,\n} from '../../lib/is-internal-component'\nimport { RouteKind } from '../route-kind'\nimport {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type NextJsHotReloaderInterface,\n} from './hot-reloader-types'\nimport type { HMR_ACTION_TYPES } from './hot-reloader-types'\nimport type { WebpackError } from 'webpack'\nimport { PAGE_TYPES } from '../../lib/page-types'\nimport { FAST_REFRESH_RUNTIME_RELOAD } from './messages'\nimport { getNodeDebugType } from '../lib/utils'\nimport { getNextErrorFeedbackMiddleware } from '../../client/components/react-dev-overlay/server/get-next-error-feedback-middleware'\nimport { getDevOverlayFontMiddleware } from '../../client/components/react-dev-overlay/font/get-dev-overlay-font-middleware'\nimport { getDisableDevIndicatorMiddleware } from './dev-indicator-middleware'\n\nconst MILLISECONDS_IN_NANOSECOND = BigInt(1_000_000)\n\nfunction diff(a: Set<any>, b: Set<any>) {\n  return new Set([...a].filter((v) => !b.has(v)))\n}\n\nconst wsServer = new ws.Server({ noServer: true })\n\nexport async function renderScriptError(\n  res: ServerResponse,\n  error: Error,\n  { verbose = true } = {}\n): Promise<{ finished: true | undefined }> {\n  // Asks CDNs and others to not to cache the errored page\n  res.setHeader(\n    'Cache-Control',\n    'no-cache, no-store, max-age=0, must-revalidate'\n  )\n\n  if ((error as any).code === 'ENOENT') {\n    return { finished: undefined }\n  }\n\n  if (verbose) {\n    console.error(error.stack)\n  }\n  res.statusCode = 500\n  res.end('500 - Internal Error')\n  return { finished: true }\n}\n\nfunction addCorsSupport(req: IncomingMessage, res: ServerResponse) {\n  // Only rewrite CORS handling when URL matches a hot-reloader middleware\n  if (!req.url!.startsWith('/__next')) {\n    return { preflight: false }\n  }\n\n  if (!req.headers.origin) {\n    return { preflight: false }\n  }\n\n  res.setHeader('Access-Control-Allow-Origin', req.headers.origin)\n  res.setHeader('Access-Control-Allow-Methods', 'OPTIONS, GET')\n  // Based on https://github.com/primus/access-control/blob/4cf1bc0e54b086c91e6aa44fb14966fa5ef7549c/index.js#L158\n  if (req.headers['access-control-request-headers']) {\n    res.setHeader(\n      'Access-Control-Allow-Headers',\n      req.headers['access-control-request-headers'] as string\n    )\n  }\n\n  if (req.method === 'OPTIONS') {\n    res.writeHead(200)\n    res.end()\n    return { preflight: true }\n  }\n\n  return { preflight: false }\n}\n\nexport const matchNextPageBundleRequest = getPathMatch(\n  '/_next/static/chunks/pages/:path*.js(\\\\.map|)'\n)\n\n// Iteratively look up the issuer till it ends up at the root\nfunction findEntryModule(\n  module: webpack.Module,\n  compilation: webpack.Compilation\n): any {\n  for (;;) {\n    const issuer = compilation.moduleGraph.getIssuer(module)\n    if (!issuer) return module\n    module = issuer\n  }\n}\n\nfunction erroredPages(compilation: webpack.Compilation) {\n  const failedPages: { [page: string]: WebpackError[] } = {}\n  for (const error of compilation.errors) {\n    if (!error.module) {\n      continue\n    }\n\n    const entryModule = findEntryModule(error.module, compilation)\n    const { name } = entryModule\n    if (!name) {\n      continue\n    }\n\n    // Only pages have to be reloaded\n    const enhancedName = getRouteFromEntrypoint(name)\n\n    if (!enhancedName) {\n      continue\n    }\n\n    if (!failedPages[enhancedName]) {\n      failedPages[enhancedName] = []\n    }\n\n    failedPages[enhancedName].push(error)\n  }\n\n  return failedPages\n}\n\nexport async function getVersionInfo(): Promise<VersionInfo> {\n  let installed = '0.0.0'\n\n  try {\n    installed = require('next/package.json').version\n\n    let res\n\n    try {\n      // use NPM registry regardless user using Yarn\n      res = await fetch('https://registry.npmjs.org/-/package/next/dist-tags')\n    } catch {\n      // ignore fetch errors\n    }\n\n    if (!res || !res.ok) return { installed, staleness: 'unknown' }\n\n    const { latest, canary } = await res.json()\n\n    return parseVersionInfo({ installed, latest, canary })\n  } catch (e: any) {\n    console.error(e)\n    return { installed, staleness: 'unknown' }\n  }\n}\n\nexport default class HotReloaderWebpack implements NextJsHotReloaderInterface {\n  private hasAmpEntrypoints: boolean\n  private hasAppRouterEntrypoints: boolean\n  private hasPagesRouterEntrypoints: boolean\n  private dir: string\n  private buildId: string\n  private encryptionKey: string\n  private middlewares: ((\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ) => Promise<void>)[]\n  private pagesDir?: string\n  private distDir: string\n  private webpackHotMiddleware?: WebpackHotMiddleware\n  private config: NextConfigComplete\n  private clientStats: webpack.Stats | null\n  private clientError: Error | null = null\n  private serverError: Error | null = null\n  private hmrServerError: Error | null = null\n  private serverPrevDocumentHash: string | null\n  private serverChunkNames?: Set<string>\n  private prevChunkNames?: Set<any>\n  private onDemandEntries?: ReturnType<typeof onDemandEntryHandler>\n  private previewProps: __ApiPreviewProps\n  private watcher: any\n  private rewrites: CustomRoutes['rewrites']\n  private fallbackWatcher: any\n  private hotReloaderSpan: Span\n  private pagesMapping: { [key: string]: string } = {}\n  private appDir?: string\n  private telemetry: Telemetry\n  private resetFetch: () => void\n  private versionInfo: VersionInfo = {\n    staleness: 'unknown',\n    installed: '0.0.0',\n  }\n  private devtoolsFrontendUrl: string | undefined\n  private reloadAfterInvalidation: boolean = false\n\n  public serverStats: webpack.Stats | null\n  public edgeServerStats: webpack.Stats | null\n  public multiCompiler?: webpack.MultiCompiler\n  public activeWebpackConfigs?: Array<\n    UnwrapPromise<ReturnType<typeof getBaseWebpackConfig>>\n  >\n\n  constructor(\n    dir: string,\n    {\n      config,\n      pagesDir,\n      distDir,\n      buildId,\n      encryptionKey,\n      previewProps,\n      rewrites,\n      appDir,\n      telemetry,\n      resetFetch,\n    }: {\n      config: NextConfigComplete\n      pagesDir?: string\n      distDir: string\n      buildId: string\n      encryptionKey: string\n      previewProps: __ApiPreviewProps\n      rewrites: CustomRoutes['rewrites']\n      appDir?: string\n      telemetry: Telemetry\n      resetFetch: () => void\n    }\n  ) {\n    this.hasAmpEntrypoints = false\n    this.hasAppRouterEntrypoints = false\n    this.hasPagesRouterEntrypoints = false\n    this.buildId = buildId\n    this.encryptionKey = encryptionKey\n    this.dir = dir\n    this.middlewares = []\n    this.pagesDir = pagesDir\n    this.appDir = appDir\n    this.distDir = distDir\n    this.clientStats = null\n    this.serverStats = null\n    this.edgeServerStats = null\n    this.serverPrevDocumentHash = null\n    this.telemetry = telemetry\n    this.resetFetch = resetFetch\n\n    this.config = config\n    this.previewProps = previewProps\n    this.rewrites = rewrites\n    this.hotReloaderSpan = trace('hot-reloader', undefined, {\n      version: process.env.__NEXT_VERSION as string,\n    })\n    // Ensure the hotReloaderSpan is flushed immediately as it's the parentSpan for all processing\n    // of the current `next dev` invocation.\n    this.hotReloaderSpan.stop()\n  }\n\n  public async run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlObject\n  ): Promise<{ finished?: true }> {\n    // Usually CORS support is not needed for the hot-reloader (this is dev only feature)\n    // With when the app runs for multi-zones support behind a proxy,\n    // the current page is trying to access this URL via assetPrefix.\n    // That's when the CORS support is needed.\n    const { preflight } = addCorsSupport(req, res)\n    if (preflight) {\n      return {}\n    }\n\n    // When a request comes in that is a page bundle, e.g. /_next/static/<buildid>/pages/index.js\n    // we have to compile the page using on-demand-entries, this middleware will handle doing that\n    // by adding the page to on-demand-entries, waiting till it's done\n    // and then the bundle will be served like usual by the actual route in server/index.js\n    const handlePageBundleRequest = async (\n      pageBundleRes: ServerResponse,\n      parsedPageBundleUrl: UrlObject\n    ): Promise<{ finished?: true }> => {\n      const { pathname } = parsedPageBundleUrl\n      if (!pathname) return {}\n\n      const params = matchNextPageBundleRequest(pathname)\n      if (!params) return {}\n\n      let decodedPagePath: string\n\n      try {\n        decodedPagePath = `/${params.path\n          .map((param: string) => decodeURIComponent(param))\n          .join('/')}`\n      } catch (_) {\n        throw new DecodeError('failed to decode param')\n      }\n\n      const page = denormalizePagePath(decodedPagePath)\n\n      if (page === '/_error' || BLOCKED_PAGES.indexOf(page) === -1) {\n        try {\n          await this.ensurePage({ page, clientOnly: true, url: req.url })\n        } catch (error) {\n          return await renderScriptError(pageBundleRes, getProperError(error))\n        }\n\n        const errors = await this.getCompilationErrors(page)\n        if (errors.length > 0) {\n          return await renderScriptError(pageBundleRes, errors[0], {\n            verbose: false,\n          })\n        }\n      }\n\n      return {}\n    }\n\n    const { finished } = await handlePageBundleRequest(res, parsedUrl)\n\n    for (const middleware of this.middlewares) {\n      let calledNext = false\n\n      await middleware(req, res, () => {\n        calledNext = true\n      })\n\n      if (!calledNext) {\n        return { finished: true }\n      }\n    }\n\n    return { finished }\n  }\n\n  public setHmrServerError(error: Error | null): void {\n    this.hmrServerError = error\n  }\n\n  public clearHmrServerError(): void {\n    if (this.hmrServerError) {\n      this.setHmrServerError(null)\n      this.send({\n        action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n        data: 'clear hmr server error',\n      })\n    }\n  }\n\n  protected async refreshServerComponents(hash: string): Promise<void> {\n    this.send({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n      hash,\n      // TODO: granular reloading of changes\n      // entrypoints: serverComponentChanges,\n    })\n  }\n\n  public onHMR(\n    req: IncomingMessage,\n    _socket: Duplex,\n    head: Buffer,\n    callback: (client: ws.WebSocket) => void\n  ) {\n    wsServer.handleUpgrade(req, req.socket, head, (client) => {\n      this.webpackHotMiddleware?.onHMR(client)\n      this.onDemandEntries?.onHMR(client, () => this.hmrServerError)\n      callback(client)\n\n      client.addEventListener('message', ({ data }) => {\n        data = typeof data !== 'string' ? data.toString() : data\n\n        try {\n          const payload = JSON.parse(data)\n\n          let traceChild:\n            | {\n                name: string\n                startTime?: bigint\n                endTime?: bigint\n                attrs?: Record<string, number | string | undefined | string[]>\n              }\n            | undefined\n\n          switch (payload.event) {\n            case 'span-end': {\n              traceChild = {\n                name: payload.spanName,\n                startTime:\n                  BigInt(Math.floor(payload.startTime)) *\n                  MILLISECONDS_IN_NANOSECOND,\n                attrs: payload.attributes,\n                endTime:\n                  BigInt(Math.floor(payload.endTime)) *\n                  MILLISECONDS_IN_NANOSECOND,\n              }\n              break\n            }\n            case 'client-hmr-latency': {\n              traceChild = {\n                name: payload.event,\n                startTime:\n                  BigInt(payload.startTime) * MILLISECONDS_IN_NANOSECOND,\n                endTime: BigInt(payload.endTime) * MILLISECONDS_IN_NANOSECOND,\n                attrs: {\n                  updatedModules: payload.updatedModules.map((m: string) =>\n                    m\n                      .replace(`(${WEBPACK_LAYERS.appPagesBrowser})/`, '')\n                      .replace(/^\\.\\//, '[project]/')\n                  ),\n                  page: payload.page,\n                  isPageHidden: payload.isPageHidden,\n                },\n              }\n              break\n            }\n            case 'client-reload-page':\n            case 'client-success': {\n              traceChild = {\n                name: payload.event,\n              }\n              break\n            }\n            case 'client-error': {\n              traceChild = {\n                name: payload.event,\n                attrs: { errorCount: payload.errorCount },\n              }\n              break\n            }\n            case 'client-warning': {\n              traceChild = {\n                name: payload.event,\n                attrs: { warningCount: payload.warningCount },\n              }\n              break\n            }\n            case 'client-removed-page':\n            case 'client-added-page': {\n              traceChild = {\n                name: payload.event,\n                attrs: { page: payload.page || '' },\n              }\n              break\n            }\n            case 'client-full-reload': {\n              const { event, stackTrace, hadRuntimeError } = payload\n\n              traceChild = {\n                name: event,\n                attrs: { stackTrace: stackTrace ?? '' },\n              }\n\n              if (hadRuntimeError) {\n                Log.warn(FAST_REFRESH_RUNTIME_RELOAD)\n                break\n              }\n\n              let fileMessage = ''\n              if (stackTrace) {\n                const file = /Aborted because (.+) is not accepted/.exec(\n                  stackTrace\n                )?.[1]\n                if (file) {\n                  // `file` is filepath in `pages/` but it can be a webpack url.\n                  // If it's a webpack loader URL, it will include the app-pages layer\n                  if (file.startsWith(`(${WEBPACK_LAYERS.appPagesBrowser})/`)) {\n                    const fileUrl = new URL(file, 'file://')\n                    const cwd = process.cwd()\n                    const modules = fileUrl.searchParams\n                      .getAll('modules')\n                      .map((filepath) => filepath.slice(cwd.length + 1))\n                      .filter(\n                        (filepath) => !filepath.startsWith('node_modules')\n                      )\n\n                    if (modules.length > 0) {\n                      fileMessage = ` when ${modules.join(', ')} changed`\n                    }\n                  } else if (\n                    // Handle known webpack layers\n                    file.startsWith(`(${WEBPACK_LAYERS.pagesDirBrowser})/`)\n                  ) {\n                    const cleanedFilePath = file.slice(\n                      `(${WEBPACK_LAYERS.pagesDirBrowser})/`.length\n                    )\n\n                    fileMessage = ` when ${cleanedFilePath} changed`\n                  } else {\n                    fileMessage = ` when ${file} changed`\n                  }\n                }\n              }\n\n              Log.warn(\n                `Fast Refresh had to perform a full reload${fileMessage}. Read more: https://nextjs.org/docs/messages/fast-refresh-reload`\n              )\n              break\n            }\n            default: {\n              break\n            }\n          }\n\n          if (traceChild) {\n            this.hotReloaderSpan.manualTraceChild(\n              traceChild.name,\n              traceChild.startTime,\n              traceChild.endTime,\n              { ...traceChild.attrs, clientId: payload.id }\n            )\n          }\n        } catch (_) {\n          // invalid WebSocket message\n        }\n      })\n    })\n  }\n\n  private async clean(span: Span): Promise<void> {\n    return span\n      .traceChild('clean')\n      .traceAsyncFn(() =>\n        recursiveDelete(join(this.dir, this.config.distDir), /^cache/)\n      )\n  }\n\n  private async getWebpackConfig(span: Span) {\n    const webpackConfigSpan = span.traceChild('get-webpack-config')\n\n    const pageExtensions = this.config.pageExtensions\n\n    return webpackConfigSpan.traceAsyncFn(async () => {\n      const pagePaths = !this.pagesDir\n        ? ([] as (string | null)[])\n        : await webpackConfigSpan\n            .traceChild('get-page-paths')\n            .traceAsyncFn(() =>\n              Promise.all([\n                findPageFile(this.pagesDir!, '/_app', pageExtensions, false),\n                findPageFile(\n                  this.pagesDir!,\n                  '/_document',\n                  pageExtensions,\n                  false\n                ),\n              ])\n            )\n\n      this.pagesMapping = await webpackConfigSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: true,\n            pageExtensions: this.config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagePaths.filter(\n              (i: string | null): i is string => typeof i === 'string'\n            ),\n            pagesDir: this.pagesDir,\n            appDir: this.appDir,\n          })\n        )\n\n      const entrypoints = await webpackConfigSpan\n        .traceChild('create-entrypoints')\n        .traceAsyncFn(() =>\n          createEntrypoints({\n            appDir: this.appDir,\n            buildId: this.buildId,\n            config: this.config,\n            envFiles: [],\n            isDev: true,\n            pages: this.pagesMapping,\n            pagesDir: this.pagesDir,\n            previewMode: this.previewProps,\n            rootDir: this.dir,\n            pageExtensions: this.config.pageExtensions,\n          })\n        )\n\n      const commonWebpackOptions = {\n        dev: true,\n        buildId: this.buildId,\n        encryptionKey: this.encryptionKey,\n        config: this.config,\n        pagesDir: this.pagesDir,\n        rewrites: this.rewrites,\n        originalRewrites: this.config._originalRewrites,\n        originalRedirects: this.config._originalRedirects,\n        runWebpackSpan: this.hotReloaderSpan,\n        appDir: this.appDir,\n      }\n\n      return webpackConfigSpan\n        .traceChild('generate-webpack-config')\n        .traceAsyncFn(async () => {\n          const info = await loadProjectInfo({\n            dir: this.dir,\n            config: commonWebpackOptions.config,\n            dev: true,\n          })\n          return Promise.all([\n            // order is important here\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.client,\n              entrypoints: entrypoints.client,\n              ...info,\n            }),\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.server,\n              entrypoints: entrypoints.server,\n              ...info,\n            }),\n            getBaseWebpackConfig(this.dir, {\n              ...commonWebpackOptions,\n              compilerType: COMPILER_NAMES.edgeServer,\n              entrypoints: entrypoints.edgeServer,\n              ...info,\n            }),\n          ])\n        })\n    })\n  }\n\n  public async buildFallbackError(): Promise<void> {\n    if (this.fallbackWatcher) return\n\n    const info = await loadProjectInfo({\n      dir: this.dir,\n      config: this.config,\n      dev: true,\n    })\n    const fallbackConfig = await getBaseWebpackConfig(this.dir, {\n      runWebpackSpan: this.hotReloaderSpan,\n      dev: true,\n      compilerType: COMPILER_NAMES.client,\n      config: this.config,\n      buildId: this.buildId,\n      encryptionKey: this.encryptionKey,\n      appDir: this.appDir,\n      pagesDir: this.pagesDir,\n      rewrites: {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      },\n      originalRewrites: {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      },\n      originalRedirects: [],\n      isDevFallback: true,\n      entrypoints: (\n        await createEntrypoints({\n          appDir: this.appDir,\n          buildId: this.buildId,\n          config: this.config,\n          envFiles: [],\n          isDev: true,\n          pages: {\n            '/_app': 'next/dist/pages/_app',\n            '/_error': 'next/dist/pages/_error',\n          },\n          pagesDir: this.pagesDir,\n          previewMode: this.previewProps,\n          rootDir: this.dir,\n          pageExtensions: this.config.pageExtensions,\n        })\n      ).client,\n      ...info,\n    })\n    const fallbackCompiler = webpack(fallbackConfig)\n\n    this.fallbackWatcher = await new Promise((resolve) => {\n      let bootedFallbackCompiler = false\n      fallbackCompiler.watch(\n        // @ts-ignore webpack supports an array of watchOptions when using a multiCompiler\n        fallbackConfig.watchOptions,\n        // Errors are handled separately\n        (_err: any) => {\n          if (!bootedFallbackCompiler) {\n            bootedFallbackCompiler = true\n            resolve(true)\n          }\n        }\n      )\n    })\n  }\n\n  private async tracedGetVersionInfo(span: Span) {\n    const versionInfoSpan = span.traceChild('get-version-info')\n    return versionInfoSpan.traceAsyncFn<VersionInfo>(async () =>\n      getVersionInfo()\n    )\n  }\n\n  public async start(): Promise<void> {\n    const startSpan = this.hotReloaderSpan.traceChild('start')\n    startSpan.stop() // Stop immediately to create an artificial parent span\n\n    this.versionInfo = await this.tracedGetVersionInfo(startSpan)\n\n    const nodeDebugType = getNodeDebugType()\n    if (nodeDebugType && !this.devtoolsFrontendUrl) {\n      const debugPort = process.debugPort\n      let debugInfo\n      try {\n        // It requires to use 127.0.0.1 instead of localhost for server-side fetching.\n        const debugInfoList = await fetch(\n          `http://127.0.0.1:${debugPort}/json/list`\n        ).then((res) => res.json())\n        // There will be only one item for current process, so always get the first item.\n        debugInfo = debugInfoList[0]\n      } catch {}\n      if (debugInfo) {\n        this.devtoolsFrontendUrl = debugInfo.devtoolsFrontendUrl\n      }\n    }\n\n    await this.clean(startSpan)\n    // Ensure distDir exists before writing package.json\n    await fs.mkdir(this.distDir, { recursive: true })\n\n    const distPackageJsonPath = join(this.distDir, 'package.json')\n    // Ensure commonjs handling is used for files in the distDir (generally .next)\n    // Files outside of the distDir can be \"type\": \"module\"\n    await fs.writeFile(distPackageJsonPath, '{\"type\": \"commonjs\"}')\n\n    this.activeWebpackConfigs = await this.getWebpackConfig(startSpan)\n\n    for (const config of this.activeWebpackConfigs) {\n      const defaultEntry = config.entry\n      config.entry = async (...args) => {\n        const outputPath = this.multiCompiler?.outputPath || ''\n        const entries = getEntries(outputPath)\n        // @ts-ignore entry is always a function\n        const entrypoints = await defaultEntry(...args)\n        const isClientCompilation = config.name === COMPILER_NAMES.client\n        const isNodeServerCompilation = config.name === COMPILER_NAMES.server\n        const isEdgeServerCompilation =\n          config.name === COMPILER_NAMES.edgeServer\n\n        await Promise.all(\n          Object.keys(entries).map(async (entryKey) => {\n            const entryData = entries[entryKey]\n            const { bundlePath, dispose } = entryData\n\n            const result =\n              /^(client|server|edge-server)@(app|pages|root)@(.*)/g.exec(\n                entryKey\n              )\n            const [, key /* pageType */, , page] = result! // this match should always happen\n\n            if (key === COMPILER_NAMES.client && !isClientCompilation) return\n            if (key === COMPILER_NAMES.server && !isNodeServerCompilation)\n              return\n            if (key === COMPILER_NAMES.edgeServer && !isEdgeServerCompilation)\n              return\n\n            const isEntry = entryData.type === EntryTypes.ENTRY\n            const isChildEntry = entryData.type === EntryTypes.CHILD_ENTRY\n\n            // Check if the page was removed or disposed and remove it\n            if (isEntry) {\n              const pageExists =\n                !dispose && existsSync(entryData.absolutePagePath)\n              if (!pageExists) {\n                delete entries[entryKey]\n                return\n              }\n            }\n\n            // For child entries, if it has an entry file and it's gone, remove it\n            if (isChildEntry) {\n              if (entryData.absoluteEntryFilePath) {\n                const pageExists =\n                  !dispose && existsSync(entryData.absoluteEntryFilePath)\n                if (!pageExists) {\n                  delete entries[entryKey]\n                  return\n                }\n              }\n            }\n\n            // Ensure _error is considered a `pages` page.\n            if (page === '/_error') {\n              this.hasPagesRouterEntrypoints = true\n            }\n\n            const hasAppDir = !!this.appDir\n            const isAppPath = hasAppDir && bundlePath.startsWith('app/')\n            const staticInfo = isEntry\n              ? await getStaticInfoIncludingLayouts({\n                  isInsideAppDir: isAppPath,\n                  pageExtensions: this.config.pageExtensions,\n                  pageFilePath: entryData.absolutePagePath,\n                  appDir: this.appDir,\n                  config: this.config,\n                  isDev: true,\n                  page,\n                })\n              : undefined\n\n            if (staticInfo?.type === PAGE_TYPES.PAGES) {\n              if (\n                staticInfo.config?.config?.amp === true ||\n                staticInfo.config?.config?.amp === 'hybrid'\n              ) {\n                this.hasAmpEntrypoints = true\n              }\n            }\n\n            const isServerComponent =\n              isAppPath && staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n            const pageType: PAGE_TYPES = entryData.bundlePath.startsWith(\n              'pages/'\n            )\n              ? PAGE_TYPES.PAGES\n              : entryData.bundlePath.startsWith('app/')\n                ? PAGE_TYPES.APP\n                : PAGE_TYPES.ROOT\n\n            if (pageType === 'pages') {\n              this.hasPagesRouterEntrypoints = true\n            }\n            if (pageType === 'app') {\n              this.hasAppRouterEntrypoints = true\n            }\n\n            const isInstrumentation =\n              isInstrumentationHookFile(page) && pageType === PAGE_TYPES.ROOT\n\n            let pageRuntime = staticInfo?.runtime\n\n            if (\n              isMiddlewareFile(page) &&\n              !this.config.experimental.nodeMiddleware &&\n              pageRuntime === 'nodejs'\n            ) {\n              Log.warn(\n                'nodejs runtime support for middleware requires experimental.nodeMiddleware be enabled in your next.config'\n              )\n              pageRuntime = 'edge'\n            }\n\n            runDependingOnPageType({\n              page,\n              pageRuntime,\n              pageType,\n              onEdgeServer: () => {\n                // TODO-APP: verify if child entry should support.\n                if (!isEdgeServerCompilation || !isEntry) return\n                entries[entryKey].status = BUILDING\n\n                if (isInstrumentation) {\n                  const normalizedBundlePath = bundlePath.replace('src/', '')\n                  entrypoints[normalizedBundlePath] = finalizeEntrypoint({\n                    compilerType: COMPILER_NAMES.edgeServer,\n                    name: normalizedBundlePath,\n                    value: getInstrumentationEntry({\n                      absolutePagePath: entryData.absolutePagePath,\n                      isEdgeServer: true,\n                      isDev: true,\n                    }),\n                    isServerComponent: true,\n                    hasAppDir,\n                  })\n                  return\n                }\n                const appDirLoader = isAppPath\n                  ? getAppEntry({\n                      name: bundlePath,\n                      page,\n                      appPaths: entryData.appPaths,\n                      pagePath: posix.join(\n                        APP_DIR_ALIAS,\n                        relative(\n                          this.appDir!,\n                          entryData.absolutePagePath\n                        ).replace(/\\\\/g, '/')\n                      ),\n                      appDir: this.appDir!,\n                      pageExtensions: this.config.pageExtensions,\n                      rootDir: this.dir,\n                      isDev: true,\n                      tsconfigPath: this.config.typescript.tsconfigPath,\n                      basePath: this.config.basePath,\n                      assetPrefix: this.config.assetPrefix,\n                      nextConfigOutput: this.config.output,\n                      preferredRegion: staticInfo?.preferredRegion,\n                      middlewareConfig: Buffer.from(\n                        JSON.stringify(staticInfo?.middleware || {})\n                      ).toString('base64'),\n                    }).import\n                  : undefined\n\n                entrypoints[bundlePath] = finalizeEntrypoint({\n                  compilerType: COMPILER_NAMES.edgeServer,\n                  name: bundlePath,\n                  value: getEdgeServerEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    rootDir: this.dir,\n                    buildId: this.buildId,\n                    bundlePath,\n                    config: this.config,\n                    isDev: true,\n                    page,\n                    pages: this.pagesMapping,\n                    isServerComponent,\n                    appDirLoader,\n                    pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n                    preferredRegion: staticInfo?.preferredRegion,\n                  }),\n                  hasAppDir,\n                })\n              },\n              onClient: () => {\n                if (!isClientCompilation) return\n                if (isChildEntry) {\n                  entries[entryKey].status = BUILDING\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    name: bundlePath,\n                    compilerType: COMPILER_NAMES.client,\n                    value: entryData.request,\n                    hasAppDir,\n                  })\n                } else {\n                  entries[entryKey].status = BUILDING\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    name: bundlePath,\n                    compilerType: COMPILER_NAMES.client,\n                    value: getClientEntry({\n                      absolutePagePath: entryData.absolutePagePath,\n                      page,\n                    }),\n                    hasAppDir,\n                  })\n                }\n              },\n              onServer: () => {\n                // TODO-APP: verify if child entry should support.\n                if (!isNodeServerCompilation || !isEntry) return\n                entries[entryKey].status = BUILDING\n                let relativeRequest = relative(\n                  config.context!,\n                  entryData.absolutePagePath\n                )\n                if (\n                  !isAbsolute(relativeRequest) &&\n                  !relativeRequest.startsWith('../')\n                ) {\n                  relativeRequest = `./${relativeRequest}`\n                }\n\n                let value: { import: string; layer?: string } | string\n                if (isInstrumentation) {\n                  value = getInstrumentationEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    isEdgeServer: false,\n                    isDev: true,\n                  })\n                  entrypoints[bundlePath] = finalizeEntrypoint({\n                    compilerType: COMPILER_NAMES.server,\n                    name: bundlePath,\n                    isServerComponent: true,\n                    value,\n                    hasAppDir,\n                  })\n                } else if (isMiddlewareFile(page)) {\n                  value = getEdgeServerEntry({\n                    absolutePagePath: entryData.absolutePagePath,\n                    rootDir: this.dir,\n                    buildId: this.buildId,\n                    bundlePath,\n                    config: this.config,\n                    isDev: true,\n                    page,\n                    pages: this.pagesMapping,\n                    isServerComponent,\n                    pagesType: PAGE_TYPES.PAGES,\n                    preferredRegion: staticInfo?.preferredRegion,\n                  })\n                } else if (isAppPath) {\n                  value = getAppEntry({\n                    name: bundlePath,\n                    page,\n                    appPaths: entryData.appPaths,\n                    pagePath: posix.join(\n                      APP_DIR_ALIAS,\n                      relative(\n                        this.appDir!,\n                        entryData.absolutePagePath\n                      ).replace(/\\\\/g, '/')\n                    ),\n                    appDir: this.appDir!,\n                    pageExtensions: this.config.pageExtensions,\n                    rootDir: this.dir,\n                    isDev: true,\n                    tsconfigPath: this.config.typescript.tsconfigPath,\n                    basePath: this.config.basePath,\n                    assetPrefix: this.config.assetPrefix,\n                    nextConfigOutput: this.config.output,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: Buffer.from(\n                      JSON.stringify(staticInfo?.middleware || {})\n                    ).toString('base64'),\n                  })\n                } else if (isAPIRoute(page)) {\n                  value = getRouteLoaderEntry({\n                    kind: RouteKind.PAGES_API,\n                    page,\n                    absolutePagePath: relativeRequest,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: staticInfo?.middleware || {},\n                  })\n                } else if (\n                  !isMiddlewareFile(page) &&\n                  !isInternalComponent(relativeRequest) &&\n                  !isNonRoutePagesPage(page) &&\n                  !isInstrumentation\n                ) {\n                  value = getRouteLoaderEntry({\n                    kind: RouteKind.PAGES,\n                    page,\n                    pages: this.pagesMapping,\n                    absolutePagePath: relativeRequest,\n                    preferredRegion: staticInfo?.preferredRegion,\n                    middlewareConfig: staticInfo?.middleware ?? {},\n                  })\n                } else {\n                  value = relativeRequest\n                }\n\n                entrypoints[bundlePath] = finalizeEntrypoint({\n                  compilerType: COMPILER_NAMES.server,\n                  name: bundlePath,\n                  isServerComponent,\n                  value,\n                  hasAppDir,\n                })\n              },\n            })\n          })\n        )\n\n        if (!this.hasAmpEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_AMP]\n        }\n        if (!this.hasPagesRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_MAIN]\n          delete entrypoints['pages/_app']\n          delete entrypoints['pages/_error']\n          delete entrypoints['/_error']\n          delete entrypoints['pages/_document']\n        }\n        // Remove React Refresh entrypoint chunk as `app` doesn't require it.\n        if (!this.hasAmpEntrypoints && !this.hasPagesRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]\n        }\n        if (!this.hasAppRouterEntrypoints) {\n          delete entrypoints[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]\n        }\n\n        return entrypoints\n      }\n    }\n\n    // Enable building of client compilation before server compilation in development\n    // @ts-ignore webpack 5\n    this.activeWebpackConfigs.parallelism = 1\n\n    this.multiCompiler = webpack(\n      this.activeWebpackConfigs\n    ) as unknown as webpack.MultiCompiler\n\n    // Copy over the filesystem so that it is shared between all compilers.\n    const inputFileSystem = this.multiCompiler.compilers[0].inputFileSystem\n    for (const compiler of this.multiCompiler.compilers) {\n      compiler.inputFileSystem = inputFileSystem\n      // This is set for the initial compile. After that Watching class in webpack adds it.\n      compiler.fsStartTime = Date.now()\n      // Ensure NodeEnvironmentPlugin doesn't purge the inputFileSystem. Purging is handled in `done` below.\n      compiler.hooks.beforeRun.intercept({\n        register(tapInfo: any) {\n          if (tapInfo.name === 'NodeEnvironmentPlugin') {\n            return null\n          }\n          return tapInfo\n        },\n      })\n    }\n\n    this.multiCompiler.hooks.done.tap('NextjsHotReloader', () => {\n      inputFileSystem?.purge?.()\n    })\n    watchCompilers(\n      this.multiCompiler.compilers[0],\n      this.multiCompiler.compilers[1],\n      this.multiCompiler.compilers[2]\n    )\n\n    // Watch for changes to client/server page files so we can tell when just\n    // the server file changes and trigger a reload for GS(S)P pages\n    const changedClientPages = new Set<string>()\n    const changedServerPages = new Set<string>()\n    const changedEdgeServerPages = new Set<string>()\n\n    const changedServerComponentPages = new Set<string>()\n    const changedCSSImportPages = new Set<string>()\n\n    const prevClientPageHashes = new Map<string, string>()\n    const prevServerPageHashes = new Map<string, string>()\n    const prevEdgeServerPageHashes = new Map<string, string>()\n    const prevCSSImportModuleHashes = new Map<string, string>()\n\n    const pageExtensionRegex = new RegExp(\n      `\\\\.(?:${this.config.pageExtensions.join('|')})$`\n    )\n\n    const trackPageChanges =\n      (\n        pageHashMap: Map<string, string>,\n        changedItems: Set<string>,\n        serverComponentChangedItems?: Set<string>\n      ) =>\n      (stats: webpack.Compilation) => {\n        try {\n          stats.entrypoints.forEach((entry, key) => {\n            if (\n              key.startsWith('pages/') ||\n              key.startsWith('app/') ||\n              isMiddlewareFilename(key)\n            ) {\n              // TODO this doesn't handle on demand loaded chunks\n              entry.chunks.forEach((chunk) => {\n                if (chunk.id === key) {\n                  const modsIterable: any =\n                    stats.chunkGraph.getChunkModulesIterable(chunk)\n\n                  let hasCSSModuleChanges = false\n                  let chunksHash = new StringXor()\n                  let chunksHashServerLayer = new StringXor()\n\n                  modsIterable.forEach((mod: any) => {\n                    if (\n                      mod.resource &&\n                      mod.resource.replace(/\\\\/g, '/').includes(key) &&\n                      // Shouldn't match CSS modules, etc.\n                      pageExtensionRegex.test(mod.resource)\n                    ) {\n                      // use original source to calculate hash since mod.hash\n                      // includes the source map in development which changes\n                      // every time for both server and client so we calculate\n                      // the hash without the source map for the page module\n                      const hash = require('crypto')\n                        .createHash('sha1')\n                        .update(mod.originalSource().buffer())\n                        .digest()\n                        .toString('hex')\n\n                      if (\n                        mod.layer === WEBPACK_LAYERS.reactServerComponents &&\n                        mod?.buildInfo?.rsc?.type !== 'client'\n                      ) {\n                        chunksHashServerLayer.add(hash)\n                      }\n\n                      chunksHash.add(hash)\n                    } else {\n                      // for non-pages we can use the module hash directly\n                      const hash = stats.chunkGraph.getModuleHash(\n                        mod,\n                        chunk.runtime\n                      )\n\n                      if (\n                        mod.layer === WEBPACK_LAYERS.reactServerComponents &&\n                        mod?.buildInfo?.rsc?.type !== 'client'\n                      ) {\n                        chunksHashServerLayer.add(hash)\n                      }\n\n                      chunksHash.add(hash)\n\n                      // Both CSS import changes from server and client\n                      // components are tracked.\n                      if (\n                        key.startsWith('app/') &&\n                        /\\.(css|scss|sass)$/.test(mod.resource || '')\n                      ) {\n                        const resourceKey = mod.layer + ':' + mod.resource\n                        const prevHash =\n                          prevCSSImportModuleHashes.get(resourceKey)\n                        if (prevHash && prevHash !== hash) {\n                          hasCSSModuleChanges = true\n                        }\n                        prevCSSImportModuleHashes.set(resourceKey, hash)\n                      }\n                    }\n                  })\n\n                  const prevHash = pageHashMap.get(key)\n                  const curHash = chunksHash.toString()\n                  if (prevHash && prevHash !== curHash) {\n                    changedItems.add(key)\n                  }\n                  pageHashMap.set(key, curHash)\n\n                  if (serverComponentChangedItems) {\n                    const serverKey =\n                      WEBPACK_LAYERS.reactServerComponents + ':' + key\n                    const prevServerHash = pageHashMap.get(serverKey)\n                    const curServerHash = chunksHashServerLayer.toString()\n                    if (prevServerHash && prevServerHash !== curServerHash) {\n                      serverComponentChangedItems.add(key)\n                    }\n                    pageHashMap.set(serverKey, curServerHash)\n                  }\n\n                  if (hasCSSModuleChanges) {\n                    changedCSSImportPages.add(key)\n                  }\n                }\n              })\n            }\n          })\n        } catch (err) {\n          console.error(err)\n        }\n      }\n\n    this.multiCompiler.compilers[0].hooks.emit.tap(\n      'NextjsHotReloaderForClient',\n      trackPageChanges(prevClientPageHashes, changedClientPages)\n    )\n    this.multiCompiler.compilers[1].hooks.emit.tap(\n      'NextjsHotReloaderForServer',\n      trackPageChanges(\n        prevServerPageHashes,\n        changedServerPages,\n        changedServerComponentPages\n      )\n    )\n    this.multiCompiler.compilers[2].hooks.emit.tap(\n      'NextjsHotReloaderForServer',\n      trackPageChanges(\n        prevEdgeServerPageHashes,\n        changedEdgeServerPages,\n        changedServerComponentPages\n      )\n    )\n\n    // This plugin watches for changes to _document.js and notifies the client side that it should reload the page\n    this.multiCompiler.compilers[1].hooks.failed.tap(\n      'NextjsHotReloaderForServer',\n      (err: Error) => {\n        this.serverError = err\n        this.serverStats = null\n        this.serverChunkNames = undefined\n      }\n    )\n\n    this.multiCompiler.compilers[2].hooks.done.tap(\n      'NextjsHotReloaderForServer',\n      (stats) => {\n        this.serverError = null\n        this.edgeServerStats = stats\n      }\n    )\n\n    this.multiCompiler.compilers[1].hooks.done.tap(\n      'NextjsHotReloaderForServer',\n      (stats) => {\n        this.serverError = null\n        this.serverStats = stats\n\n        if (!this.pagesDir) {\n          return\n        }\n\n        const { compilation } = stats\n\n        // We only watch `_document` for changes on the server compilation\n        // the rest of the files will be triggered by the client compilation\n        const documentChunk = compilation.namedChunks.get('pages/_document')\n        // If the document chunk can't be found we do nothing\n        if (!documentChunk) {\n          return\n        }\n\n        // Initial value\n        if (this.serverPrevDocumentHash === null) {\n          this.serverPrevDocumentHash = documentChunk.hash || null\n          return\n        }\n\n        // If _document.js didn't change we don't trigger a reload.\n        if (documentChunk.hash === this.serverPrevDocumentHash) {\n          return\n        }\n\n        // As document chunk will change if new app pages are joined,\n        // since react bundle is different it will effect the chunk hash.\n        // So we diff the chunk changes, if there's only new app page chunk joins,\n        // then we don't trigger a reload by checking pages/_document chunk change.\n        if (this.appDir) {\n          const chunkNames = new Set(compilation.namedChunks.keys())\n          const diffChunkNames = difference<string>(\n            this.serverChunkNames || new Set(),\n            chunkNames\n          )\n\n          if (\n            diffChunkNames.length === 0 ||\n            diffChunkNames.every((chunkName) => chunkName.startsWith('app/'))\n          ) {\n            return\n          }\n          this.serverChunkNames = chunkNames\n        }\n\n        this.serverPrevDocumentHash = documentChunk.hash || null\n\n        // Notify reload to reload the page, as _document.js was changed (different hash)\n        this.send({\n          action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n          data: '_document has changed',\n        })\n      }\n    )\n\n    this.multiCompiler.hooks.done.tap('NextjsHotReloaderForServer', (stats) => {\n      const reloadAfterInvalidation = this.reloadAfterInvalidation\n      this.reloadAfterInvalidation = false\n\n      const serverOnlyChanges = difference<string>(\n        changedServerPages,\n        changedClientPages\n      )\n\n      const edgeServerOnlyChanges = difference<string>(\n        changedEdgeServerPages,\n        changedClientPages\n      )\n\n      const pageChanges = serverOnlyChanges\n        .concat(edgeServerOnlyChanges)\n        .filter((key) => key.startsWith('pages/'))\n\n      const middlewareChanges = [\n        ...Array.from(changedEdgeServerPages),\n        ...Array.from(changedServerPages),\n      ].filter((name) => isMiddlewareFilename(name))\n\n      if (middlewareChanges.length > 0) {\n        this.send({\n          event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES,\n        })\n      }\n\n      if (pageChanges.length > 0) {\n        this.send({\n          event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES,\n          pages: serverOnlyChanges.map((pg) =>\n            denormalizePagePath(pg.slice('pages'.length))\n          ),\n        })\n      }\n\n      if (\n        changedServerComponentPages.size ||\n        changedCSSImportPages.size ||\n        reloadAfterInvalidation\n      ) {\n        this.resetFetch()\n        this.refreshServerComponents(stats.hash)\n      }\n\n      changedClientPages.clear()\n      changedServerPages.clear()\n      changedEdgeServerPages.clear()\n      changedServerComponentPages.clear()\n      changedCSSImportPages.clear()\n    })\n\n    this.multiCompiler.compilers[0].hooks.failed.tap(\n      'NextjsHotReloaderForClient',\n      (err: Error) => {\n        this.clientError = err\n        this.clientStats = null\n      }\n    )\n    this.multiCompiler.compilers[0].hooks.done.tap(\n      'NextjsHotReloaderForClient',\n      (stats) => {\n        this.clientError = null\n        this.clientStats = stats\n\n        const { compilation } = stats\n        const chunkNames = new Set(\n          [...compilation.namedChunks.keys()].filter(\n            (name) => !!getRouteFromEntrypoint(name)\n          )\n        )\n\n        if (this.prevChunkNames) {\n          // detect chunks which have to be replaced with a new template\n          // e.g, pages/index.js <-> pages/_error.js\n          const addedPages = diff(chunkNames, this.prevChunkNames!)\n          const removedPages = diff(this.prevChunkNames!, chunkNames)\n\n          if (addedPages.size > 0) {\n            for (const addedPage of addedPages) {\n              const page = getRouteFromEntrypoint(addedPage)\n              this.send({\n                action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n                data: [page],\n              })\n            }\n          }\n\n          if (removedPages.size > 0) {\n            for (const removedPage of removedPages) {\n              const page = getRouteFromEntrypoint(removedPage)\n              this.send({\n                action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n                data: [page],\n              })\n            }\n          }\n        }\n\n        this.prevChunkNames = chunkNames\n      }\n    )\n\n    this.webpackHotMiddleware = new WebpackHotMiddleware(\n      this.multiCompiler.compilers,\n      this.versionInfo,\n      this.devtoolsFrontendUrl\n    )\n\n    let booted = false\n\n    this.watcher = await new Promise((resolve) => {\n      const watcher = this.multiCompiler?.watch(\n        // @ts-ignore webpack supports an array of watchOptions when using a multiCompiler\n        this.activeWebpackConfigs.map((config) => config.watchOptions!),\n        // Errors are handled separately\n        (_err: any) => {\n          if (!booted) {\n            booted = true\n            resolve(watcher)\n          }\n        }\n      )\n    })\n\n    this.onDemandEntries = onDemandEntryHandler({\n      hotReloader: this,\n      multiCompiler: this.multiCompiler,\n      pagesDir: this.pagesDir,\n      appDir: this.appDir,\n      rootDir: this.dir,\n      nextConfig: this.config,\n      ...(this.config.onDemandEntries as {\n        maxInactiveAge: number\n        pagesBufferLength: number\n      }),\n    })\n\n    this.middlewares = [\n      getOverlayMiddleware({\n        rootDirectory: this.dir,\n        clientStats: () => this.clientStats,\n        serverStats: () => this.serverStats,\n        edgeServerStats: () => this.edgeServerStats,\n      }),\n      getSourceMapMiddleware({\n        clientStats: () => this.clientStats,\n        serverStats: () => this.serverStats,\n        edgeServerStats: () => this.edgeServerStats,\n      }),\n      getNextErrorFeedbackMiddleware(this.telemetry),\n      getDevOverlayFontMiddleware(),\n      getDisableDevIndicatorMiddleware(),\n    ]\n  }\n\n  public invalidate(\n    { reloadAfterInvalidation }: { reloadAfterInvalidation: boolean } = {\n      reloadAfterInvalidation: false,\n    }\n  ) {\n    // Cache the `reloadAfterInvalidation` flag, and use it to reload the page when compilation is done\n    this.reloadAfterInvalidation = reloadAfterInvalidation\n    const outputPath = this.multiCompiler?.outputPath\n    if (outputPath) {\n      getInvalidator(outputPath)?.invalidate()\n    }\n  }\n\n  public async getCompilationErrors(page: string) {\n    const getErrors = ({ compilation }: webpack.Stats) => {\n      const failedPages = erroredPages(compilation)\n      const normalizedPage = normalizePathSep(page)\n      // If there is an error related to the requesting page we display it instead of the first error\n      return failedPages[normalizedPage]?.length > 0\n        ? failedPages[normalizedPage]\n        : compilation.errors\n    }\n\n    if (this.clientError) {\n      return [this.clientError]\n    } else if (this.serverError) {\n      return [this.serverError]\n    } else if (this.clientStats?.hasErrors()) {\n      return getErrors(this.clientStats)\n    } else if (this.serverStats?.hasErrors()) {\n      return getErrors(this.serverStats)\n    } else if (this.edgeServerStats?.hasErrors()) {\n      return getErrors(this.edgeServerStats)\n    } else {\n      return []\n    }\n  }\n\n  public send(action: HMR_ACTION_TYPES): void {\n    this.webpackHotMiddleware!.publish(action)\n  }\n\n  public async ensurePage({\n    page,\n    clientOnly,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    isApp?: boolean\n    definition?: RouteDefinition\n    url?: string\n  }): Promise<void> {\n    return this.hotReloaderSpan\n      .traceChild('ensure-page', {\n        inputPage: page,\n      })\n      .traceAsyncFn(async () => {\n        // Make sure we don't re-build or dispose prebuilt pages\n        if (page !== '/_error' && BLOCKED_PAGES.indexOf(page) !== -1) {\n          return\n        }\n        const error = clientOnly\n          ? this.clientError\n          : this.serverError || this.clientError\n        if (error) {\n          throw error\n        }\n\n        return this.onDemandEntries?.ensurePage({\n          page,\n          appPaths,\n          definition,\n          isApp,\n          url,\n        })\n      })\n  }\n\n  public close() {\n    this.webpackHotMiddleware?.close()\n  }\n}\n"], "names": ["HotReloaderWebpack", "getVersionInfo", "matchNextPageBundleRequest", "renderScriptError", "MILLISECONDS_IN_NANOSECOND", "BigInt", "diff", "a", "b", "Set", "filter", "v", "has", "wsServer", "ws", "Server", "noServer", "res", "error", "verbose", "<PERSON><PERSON><PERSON><PERSON>", "code", "finished", "undefined", "console", "stack", "statusCode", "end", "addCorsSupport", "req", "url", "startsWith", "preflight", "headers", "origin", "method", "writeHead", "getPathMatch", "findEntryModule", "module", "compilation", "issuer", "moduleGraph", "get<PERSON><PERSON><PERSON>", "erroredPages", "failedPages", "errors", "entryModule", "name", "enhancedName", "getRouteFromEntrypoint", "push", "installed", "require", "version", "fetch", "ok", "staleness", "latest", "canary", "json", "parseVersionInfo", "e", "constructor", "dir", "config", "pagesDir", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "rewrites", "appDir", "telemetry", "resetFetch", "clientError", "serverError", "hmrServerError", "pagesMapping", "versionInfo", "reloadAfterInvalidation", "hasAmpEntrypoints", "hasAppRouterEntrypoints", "hasPagesRouterEntrypoints", "middlewares", "clientStats", "serverStats", "edgeServerStats", "serverPrevDocumentHash", "hotReloaderSpan", "trace", "process", "env", "__NEXT_VERSION", "stop", "run", "parsedUrl", "handlePageBundleRequest", "pageBundleRes", "parsedPageBundleUrl", "pathname", "params", "decodedPagePath", "path", "map", "param", "decodeURIComponent", "join", "_", "DecodeError", "page", "denormalizePagePath", "BLOCKED_PAGES", "indexOf", "ensurePage", "clientOnly", "getProperError", "getCompilationErrors", "length", "middleware", "calledNext", "setHmrServerError", "clearHmrServerError", "send", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "RELOAD_PAGE", "data", "refreshServerComponents", "hash", "SERVER_COMPONENT_CHANGES", "onHMR", "_socket", "head", "callback", "handleUpgrade", "socket", "client", "webpackHotMiddleware", "onDemandEntries", "addEventListener", "toString", "payload", "JSON", "parse", "<PERSON><PERSON><PERSON><PERSON>", "event", "spanName", "startTime", "Math", "floor", "attrs", "attributes", "endTime", "updatedModules", "m", "replace", "WEBPACK_LAYERS", "appPagesBrowser", "isPageHidden", "errorCount", "warningCount", "stackTrace", "hadRuntimeError", "Log", "warn", "FAST_REFRESH_RUNTIME_RELOAD", "fileMessage", "file", "exec", "fileUrl", "URL", "cwd", "modules", "searchParams", "getAll", "filepath", "slice", "pagesDirBrowser", "cleanedFile<PERSON>ath", "manualTraceChild", "clientId", "id", "clean", "span", "traceAsyncFn", "recursiveDelete", "getWebpackConfig", "webpackConfigSpan", "pageExtensions", "pagePaths", "Promise", "all", "findPageFile", "createPagesMapping", "isDev", "pagesType", "PAGE_TYPES", "PAGES", "i", "entrypoints", "createEntrypoints", "envFiles", "pages", "previewMode", "rootDir", "commonWebpackOptions", "dev", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "runWebpackSpan", "info", "loadProjectInfo", "getBaseWebpackConfig", "compilerType", "COMPILER_NAMES", "server", "edgeServer", "buildFallbackError", "fallback<PERSON><PERSON><PERSON>", "fallbackConfig", "beforeFiles", "afterFiles", "fallback", "isDev<PERSON><PERSON><PERSON>", "fallbackCompiler", "webpack", "resolve", "bootedFallbackCompiler", "watch", "watchOptions", "_err", "tracedGetVersionInfo", "versionInfoSpan", "start", "startSpan", "nodeDebugType", "getNodeDebugType", "devtoolsFrontendUrl", "debugPort", "debugInfo", "debugInfoList", "then", "fs", "mkdir", "recursive", "distPackageJsonPath", "writeFile", "activeWebpackConfigs", "defaultEntry", "entry", "args", "outputPath", "multiCompiler", "entries", "getEntries", "isClientCompilation", "isNodeServerCompilation", "isEdgeServerCompilation", "Object", "keys", "<PERSON><PERSON><PERSON>", "entryData", "bundlePath", "dispose", "result", "key", "isEntry", "type", "EntryTypes", "ENTRY", "isChildEntry", "CHILD_ENTRY", "pageExists", "existsSync", "absolutePagePath", "absoluteEntryFilePath", "hasAppDir", "isAppPath", "staticInfo", "getStaticInfoIncludingLayouts", "isInsideAppDir", "pageFilePath", "amp", "isServerComponent", "rsc", "RSC_MODULE_TYPES", "pageType", "APP", "ROOT", "isInstrumentation", "isInstrumentationHookFile", "pageRuntime", "runtime", "isMiddlewareFile", "experimental", "nodeMiddleware", "runDependingOnPageType", "onEdgeServer", "status", "BUILDING", "normalizedBundlePath", "finalizeEntrypoint", "value", "getInstrumentationEntry", "isEdgeServer", "appDirLoader", "getAppEntry", "appPaths", "pagePath", "posix", "APP_DIR_ALIAS", "relative", "tsconfigPath", "typescript", "basePath", "assetPrefix", "nextConfigOutput", "output", "preferredRegion", "middlewareConfig", "<PERSON><PERSON><PERSON>", "from", "stringify", "import", "getEdgeServerEntry", "onClient", "request", "getClientEntry", "onServer", "relativeRequest", "context", "isAbsolute", "isAPIRoute", "getRouteLoaderEntry", "kind", "RouteKind", "PAGES_API", "isInternalComponent", "isNonRoutePagesPage", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "parallelism", "inputFileSystem", "compilers", "compiler", "fsStartTime", "Date", "now", "hooks", "beforeRun", "intercept", "register", "tapInfo", "done", "tap", "purge", "watchCompilers", "changedClientPages", "changedServerPages", "changedEdgeServerPages", "changedServerComponentPages", "changedCSSImportPages", "prevClientPageHashes", "Map", "prevServerPageHashes", "prevEdgeServerPageHashes", "prevCSSImportModuleHashes", "pageExtensionRegex", "RegExp", "trackPageChanges", "pageHashMap", "changedItems", "serverComponentChangedItems", "stats", "for<PERSON>ach", "isMiddlewareFilename", "chunks", "chunk", "modsIterable", "chunkGraph", "getChunkModulesIterable", "hasCSSModuleChanges", "chunksHash", "StringXor", "chunksHashServerLayer", "mod", "resource", "includes", "test", "createHash", "update", "originalSource", "buffer", "digest", "layer", "reactServerComponents", "buildInfo", "add", "getModuleHash", "resourceKey", "prevHash", "get", "set", "curHash", "server<PERSON>ey", "prevServerHash", "curServerHash", "err", "emit", "failed", "serverChunkNames", "documentChunk", "namedChunks", "chunkNames", "diffChunkNames", "difference", "every", "chunkName", "serverOnlyChanges", "edgeServerOnlyChanges", "pageChanges", "concat", "middlewareChanges", "Array", "MIDDLEWARE_CHANGES", "SERVER_ONLY_CHANGES", "pg", "size", "clear", "prevChunkNames", "addedPages", "removedPages", "addedPage", "ADDED_PAGE", "removedPage", "REMOVED_PAGE", "WebpackHotMiddleware", "booted", "watcher", "onDemandEntryHandler", "hotReloader", "nextConfig", "getOverlayMiddleware", "rootDirectory", "getSourceMapMiddleware", "getNextErrorFeedbackMiddleware", "getDevOverlayFontMiddleware", "getDisableDevIndicatorMiddleware", "invalidate", "getInvalidator", "getErrors", "normalizedPage", "normalizePathSep", "hasErrors", "publish", "definition", "isApp", "inputPage", "close"], "mappings": ";;;;;;;;;;;;;;;;;IA6NA,OA64CC;eA74CoBA;;IA1BCC,cAAc;eAAdA;;IA9CTC,0BAA0B;eAA1BA;;IApDSC,iBAAiB;eAAjBA;;;yBAzFa;mCAI5B;+BAC8B;sBACa;yBAW3C;wBACwB;6DACV;uEAGd;2BACuC;iCACd;4BASzB;2BAEsB;8BACA;sCAOtB;qCAC6B;kCACH;+EACE;uBAM5B;wBACqB;uBACK;yBACF;2DAChB;oBAC4B;kCAEV;4BAEN;iCACS;qCAI7B;2BACmB;kCAInB;2BAGoB;0BACiB;wBACX;gDACc;6CACH;wCACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjD,MAAMC,6BAA6BC,OAAO;AAE1C,SAASC,KAAKC,CAAW,EAAEC,CAAW;IACpC,OAAO,IAAIC,IAAI;WAAIF;KAAE,CAACG,MAAM,CAAC,CAACC,IAAM,CAACH,EAAEI,GAAG,CAACD;AAC7C;AAEA,MAAME,WAAW,IAAIC,WAAE,CAACC,MAAM,CAAC;IAAEC,UAAU;AAAK;AAEzC,eAAeb,kBACpBc,GAAmB,EACnBC,KAAY,EACZ,EAAEC,UAAU,IAAI,EAAE,GAAG,CAAC,CAAC;IAEvB,wDAAwD;IACxDF,IAAIG,SAAS,CACX,iBACA;IAGF,IAAI,AAACF,MAAcG,IAAI,KAAK,UAAU;QACpC,OAAO;YAAEC,UAAUC;QAAU;IAC/B;IAEA,IAAIJ,SAAS;QACXK,QAAQN,KAAK,CAACA,MAAMO,KAAK;IAC3B;IACAR,IAAIS,UAAU,GAAG;IACjBT,IAAIU,GAAG,CAAC;IACR,OAAO;QAAEL,UAAU;IAAK;AAC1B;AAEA,SAASM,eAAeC,GAAoB,EAAEZ,GAAmB;IAC/D,wEAAwE;IACxE,IAAI,CAACY,IAAIC,GAAG,CAAEC,UAAU,CAAC,YAAY;QACnC,OAAO;YAAEC,WAAW;QAAM;IAC5B;IAEA,IAAI,CAACH,IAAII,OAAO,CAACC,MAAM,EAAE;QACvB,OAAO;YAAEF,WAAW;QAAM;IAC5B;IAEAf,IAAIG,SAAS,CAAC,+BAA+BS,IAAII,OAAO,CAACC,MAAM;IAC/DjB,IAAIG,SAAS,CAAC,gCAAgC;IAC9C,gHAAgH;IAChH,IAAIS,IAAII,OAAO,CAAC,iCAAiC,EAAE;QACjDhB,IAAIG,SAAS,CACX,gCACAS,IAAII,OAAO,CAAC,iCAAiC;IAEjD;IAEA,IAAIJ,IAAIM,MAAM,KAAK,WAAW;QAC5BlB,IAAImB,SAAS,CAAC;QACdnB,IAAIU,GAAG;QACP,OAAO;YAAEK,WAAW;QAAK;IAC3B;IAEA,OAAO;QAAEA,WAAW;IAAM;AAC5B;AAEO,MAAM9B,6BAA6BmC,IAAAA,uBAAY,EACpD;AAGF,6DAA6D;AAC7D,SAASC,gBACPC,OAAsB,EACtBC,WAAgC;IAEhC,OAAS;QACP,MAAMC,SAASD,YAAYE,WAAW,CAACC,SAAS,CAACJ;QACjD,IAAI,CAACE,QAAQ,OAAOF;QACpBA,UAASE;IACX;AACF;AAEA,SAASG,aAAaJ,WAAgC;IACpD,MAAMK,cAAkD,CAAC;IACzD,KAAK,MAAM3B,SAASsB,YAAYM,MAAM,CAAE;QACtC,IAAI,CAAC5B,MAAMqB,MAAM,EAAE;YACjB;QACF;QAEA,MAAMQ,cAAcT,gBAAgBpB,MAAMqB,MAAM,EAAEC;QAClD,MAAM,EAAEQ,IAAI,EAAE,GAAGD;QACjB,IAAI,CAACC,MAAM;YACT;QACF;QAEA,iCAAiC;QACjC,MAAMC,eAAeC,IAAAA,+BAAsB,EAACF;QAE5C,IAAI,CAACC,cAAc;YACjB;QACF;QAEA,IAAI,CAACJ,WAAW,CAACI,aAAa,EAAE;YAC9BJ,WAAW,CAACI,aAAa,GAAG,EAAE;QAChC;QAEAJ,WAAW,CAACI,aAAa,CAACE,IAAI,CAACjC;IACjC;IAEA,OAAO2B;AACT;AAEO,eAAe5C;IACpB,IAAImD,YAAY;IAEhB,IAAI;QACFA,YAAYC,QAAQ,qBAAqBC,OAAO;QAEhD,IAAIrC;QAEJ,IAAI;YACF,8CAA8C;YAC9CA,MAAM,MAAMsC,MAAM;QACpB,EAAE,OAAM;QACN,sBAAsB;QACxB;QAEA,IAAI,CAACtC,OAAO,CAACA,IAAIuC,EAAE,EAAE,OAAO;YAAEJ;YAAWK,WAAW;QAAU;QAE9D,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,GAAG,MAAM1C,IAAI2C,IAAI;QAEzC,OAAOC,IAAAA,kCAAgB,EAAC;YAAET;YAAWM;YAAQC;QAAO;IACtD,EAAE,OAAOG,GAAQ;QACftC,QAAQN,KAAK,CAAC4C;QACd,OAAO;YAAEV;YAAWK,WAAW;QAAU;IAC3C;AACF;AAEe,MAAMzD;IA+CnB+D,YACEC,GAAW,EACX,EACEC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,aAAa,EACbC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTC,UAAU,EAYX,CACD;aAvDMC,cAA4B;aAC5BC,cAA4B;aAC5BC,iBAA+B;aAU/BC,eAA0C,CAAC;aAI3CC,cAA2B;YACjCtB,WAAW;YACXL,WAAW;QACb;aAEQ4B,0BAAmC;QAmCzC,IAAI,CAACC,iBAAiB,GAAG;QACzB,IAAI,CAACC,uBAAuB,GAAG;QAC/B,IAAI,CAACC,yBAAyB,GAAG;QACjC,IAAI,CAACf,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACL,GAAG,GAAGA;QACX,IAAI,CAACoB,WAAW,GAAG,EAAE;QACrB,IAAI,CAAClB,QAAQ,GAAGA;QAChB,IAAI,CAACM,MAAM,GAAGA;QACd,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACkB,WAAW,GAAG;QACnB,IAAI,CAACC,WAAW,GAAG;QACnB,IAAI,CAACC,eAAe,GAAG;QACvB,IAAI,CAACC,sBAAsB,GAAG;QAC9B,IAAI,CAACf,SAAS,GAAGA;QACjB,IAAI,CAACC,UAAU,GAAGA;QAElB,IAAI,CAACT,MAAM,GAAGA;QACd,IAAI,CAACK,YAAY,GAAGA;QACpB,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACkB,eAAe,GAAGC,IAAAA,YAAK,EAAC,gBAAgBnE,WAAW;YACtD+B,SAASqC,QAAQC,GAAG,CAACC,cAAc;QACrC;QACA,8FAA8F;QAC9F,wCAAwC;QACxC,IAAI,CAACJ,eAAe,CAACK,IAAI;IAC3B;IAEA,MAAaC,IACXlE,GAAoB,EACpBZ,GAAmB,EACnB+E,SAAoB,EACU;QAC9B,qFAAqF;QACrF,iEAAiE;QACjE,iEAAiE;QACjE,0CAA0C;QAC1C,MAAM,EAAEhE,SAAS,EAAE,GAAGJ,eAAeC,KAAKZ;QAC1C,IAAIe,WAAW;YACb,OAAO,CAAC;QACV;QAEA,6FAA6F;QAC7F,8FAA8F;QAC9F,kEAAkE;QAClE,uFAAuF;QACvF,MAAMiE,0BAA0B,OAC9BC,eACAC;YAEA,MAAM,EAAEC,QAAQ,EAAE,GAAGD;YACrB,IAAI,CAACC,UAAU,OAAO,CAAC;YAEvB,MAAMC,SAASnG,2BAA2BkG;YAC1C,IAAI,CAACC,QAAQ,OAAO,CAAC;YAErB,IAAIC;YAEJ,IAAI;gBACFA,kBAAkB,CAAC,CAAC,EAAED,OAAOE,IAAI,CAC9BC,GAAG,CAAC,CAACC,QAAkBC,mBAAmBD,QAC1CE,IAAI,CAAC,MAAM;YAChB,EAAE,OAAOC,GAAG;gBACV,MAAM,qBAAyC,CAAzC,IAAIC,mBAAW,CAAC,2BAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;YAEA,MAAMC,OAAOC,IAAAA,wCAAmB,EAACT;YAEjC,IAAIQ,SAAS,aAAaE,yBAAa,CAACC,OAAO,CAACH,UAAU,CAAC,GAAG;gBAC5D,IAAI;oBACF,MAAM,IAAI,CAACI,UAAU,CAAC;wBAAEJ;wBAAMK,YAAY;wBAAMrF,KAAKD,IAAIC,GAAG;oBAAC;gBAC/D,EAAE,OAAOZ,OAAO;oBACd,OAAO,MAAMf,kBAAkB+F,eAAekB,IAAAA,uBAAc,EAAClG;gBAC/D;gBAEA,MAAM4B,SAAS,MAAM,IAAI,CAACuE,oBAAoB,CAACP;gBAC/C,IAAIhE,OAAOwE,MAAM,GAAG,GAAG;oBACrB,OAAO,MAAMnH,kBAAkB+F,eAAepD,MAAM,CAAC,EAAE,EAAE;wBACvD3B,SAAS;oBACX;gBACF;YACF;YAEA,OAAO,CAAC;QACV;QAEA,MAAM,EAAEG,QAAQ,EAAE,GAAG,MAAM2E,wBAAwBhF,KAAK+E;QAExD,KAAK,MAAMuB,cAAc,IAAI,CAACnC,WAAW,CAAE;YACzC,IAAIoC,aAAa;YAEjB,MAAMD,WAAW1F,KAAKZ,KAAK;gBACzBuG,aAAa;YACf;YAEA,IAAI,CAACA,YAAY;gBACf,OAAO;oBAAElG,UAAU;gBAAK;YAC1B;QACF;QAEA,OAAO;YAAEA;QAAS;IACpB;IAEOmG,kBAAkBvG,KAAmB,EAAQ;QAClD,IAAI,CAAC2D,cAAc,GAAG3D;IACxB;IAEOwG,sBAA4B;QACjC,IAAI,IAAI,CAAC7C,cAAc,EAAE;YACvB,IAAI,CAAC4C,iBAAiB,CAAC;YACvB,IAAI,CAACE,IAAI,CAAC;gBACRC,QAAQC,6CAA2B,CAACC,WAAW;gBAC/CC,MAAM;YACR;QACF;IACF;IAEA,MAAgBC,wBAAwBC,IAAY,EAAiB;QACnE,IAAI,CAACN,IAAI,CAAC;YACRC,QAAQC,6CAA2B,CAACK,wBAAwB;YAC5DD;QAGF;IACF;IAEOE,MACLtG,GAAoB,EACpBuG,OAAe,EACfC,IAAY,EACZC,QAAwC,EACxC;QACAzH,SAAS0H,aAAa,CAAC1G,KAAKA,IAAI2G,MAAM,EAAEH,MAAM,CAACI;gBAC7C,4BACA;aADA,6BAAA,IAAI,CAACC,oBAAoB,qBAAzB,2BAA2BP,KAAK,CAACM;aACjC,wBAAA,IAAI,CAACE,eAAe,qBAApB,sBAAsBR,KAAK,CAACM,QAAQ,IAAM,IAAI,CAAC5D,cAAc;YAC7DyD,SAASG;YAETA,OAAOG,gBAAgB,CAAC,WAAW,CAAC,EAAEb,IAAI,EAAE;gBAC1CA,OAAO,OAAOA,SAAS,WAAWA,KAAKc,QAAQ,KAAKd;gBAEpD,IAAI;oBACF,MAAMe,UAAUC,KAAKC,KAAK,CAACjB;oBAE3B,IAAIkB;oBASJ,OAAQH,QAAQI,KAAK;wBACnB,KAAK;4BAAY;gCACfD,aAAa;oCACXjG,MAAM8F,QAAQK,QAAQ;oCACtBC,WACE/I,OAAOgJ,KAAKC,KAAK,CAACR,QAAQM,SAAS,KACnChJ;oCACFmJ,OAAOT,QAAQU,UAAU;oCACzBC,SACEpJ,OAAOgJ,KAAKC,KAAK,CAACR,QAAQW,OAAO,KACjCrJ;gCACJ;gCACA;4BACF;wBACA,KAAK;4BAAsB;gCACzB6I,aAAa;oCACXjG,MAAM8F,QAAQI,KAAK;oCACnBE,WACE/I,OAAOyI,QAAQM,SAAS,IAAIhJ;oCAC9BqJ,SAASpJ,OAAOyI,QAAQW,OAAO,IAAIrJ;oCACnCmJ,OAAO;wCACLG,gBAAgBZ,QAAQY,cAAc,CAAClD,GAAG,CAAC,CAACmD,IAC1CA,EACGC,OAAO,CAAC,CAAC,CAAC,EAAEC,yBAAc,CAACC,eAAe,CAAC,EAAE,CAAC,EAAE,IAChDF,OAAO,CAAC,SAAS;wCAEtB9C,MAAMgC,QAAQhC,IAAI;wCAClBiD,cAAcjB,QAAQiB,YAAY;oCACpC;gCACF;gCACA;4BACF;wBACA,KAAK;wBACL,KAAK;4BAAkB;gCACrBd,aAAa;oCACXjG,MAAM8F,QAAQI,KAAK;gCACrB;gCACA;4BACF;wBACA,KAAK;4BAAgB;gCACnBD,aAAa;oCACXjG,MAAM8F,QAAQI,KAAK;oCACnBK,OAAO;wCAAES,YAAYlB,QAAQkB,UAAU;oCAAC;gCAC1C;gCACA;4BACF;wBACA,KAAK;4BAAkB;gCACrBf,aAAa;oCACXjG,MAAM8F,QAAQI,KAAK;oCACnBK,OAAO;wCAAEU,cAAcnB,QAAQmB,YAAY;oCAAC;gCAC9C;gCACA;4BACF;wBACA,KAAK;wBACL,KAAK;4BAAqB;gCACxBhB,aAAa;oCACXjG,MAAM8F,QAAQI,KAAK;oCACnBK,OAAO;wCAAEzC,MAAMgC,QAAQhC,IAAI,IAAI;oCAAG;gCACpC;gCACA;4BACF;wBACA,KAAK;4BAAsB;gCACzB,MAAM,EAAEoC,KAAK,EAAEgB,UAAU,EAAEC,eAAe,EAAE,GAAGrB;gCAE/CG,aAAa;oCACXjG,MAAMkG;oCACNK,OAAO;wCAAEW,YAAYA,cAAc;oCAAG;gCACxC;gCAEA,IAAIC,iBAAiB;oCACnBC,KAAIC,IAAI,CAACC,qCAA2B;oCACpC;gCACF;gCAEA,IAAIC,cAAc;gCAClB,IAAIL,YAAY;wCACD;oCAAb,MAAMM,QAAO,QAAA,uCAAuCC,IAAI,CACtDP,gCADW,KAEV,CAAC,EAAE;oCACN,IAAIM,MAAM;wCACR,8DAA8D;wCAC9D,oEAAoE;wCACpE,IAAIA,KAAKzI,UAAU,CAAC,CAAC,CAAC,EAAE8H,yBAAc,CAACC,eAAe,CAAC,EAAE,CAAC,GAAG;4CAC3D,MAAMY,UAAU,IAAIC,IAAIH,MAAM;4CAC9B,MAAMI,MAAMjF,QAAQiF,GAAG;4CACvB,MAAMC,UAAUH,QAAQI,YAAY,CACjCC,MAAM,CAAC,WACPvE,GAAG,CAAC,CAACwE,WAAaA,SAASC,KAAK,CAACL,IAAItD,MAAM,GAAG,IAC9C5G,MAAM,CACL,CAACsK,WAAa,CAACA,SAASjJ,UAAU,CAAC;4CAGvC,IAAI8I,QAAQvD,MAAM,GAAG,GAAG;gDACtBiD,cAAc,CAAC,MAAM,EAAEM,QAAQlE,IAAI,CAAC,MAAM,QAAQ,CAAC;4CACrD;wCACF,OAAO,IACL,8BAA8B;wCAC9B6D,KAAKzI,UAAU,CAAC,CAAC,CAAC,EAAE8H,yBAAc,CAACqB,eAAe,CAAC,EAAE,CAAC,GACtD;4CACA,MAAMC,kBAAkBX,KAAKS,KAAK,CAChC,CAAC,CAAC,EAAEpB,yBAAc,CAACqB,eAAe,CAAC,EAAE,CAAC,CAAC5D,MAAM;4CAG/CiD,cAAc,CAAC,MAAM,EAAEY,gBAAgB,QAAQ,CAAC;wCAClD,OAAO;4CACLZ,cAAc,CAAC,MAAM,EAAEC,KAAK,QAAQ,CAAC;wCACvC;oCACF;gCACF;gCAEAJ,KAAIC,IAAI,CACN,CAAC,yCAAyC,EAAEE,YAAY,iEAAiE,CAAC;gCAE5H;4BACF;wBACA;4BAAS;gCACP;4BACF;oBACF;oBAEA,IAAItB,YAAY;wBACd,IAAI,CAACxD,eAAe,CAAC2F,gBAAgB,CACnCnC,WAAWjG,IAAI,EACfiG,WAAWG,SAAS,EACpBH,WAAWQ,OAAO,EAClB;4BAAE,GAAGR,WAAWM,KAAK;4BAAE8B,UAAUvC,QAAQwC,EAAE;wBAAC;oBAEhD;gBACF,EAAE,OAAO1E,GAAG;gBACV,4BAA4B;gBAC9B;YACF;QACF;IACF;IAEA,MAAc2E,MAAMC,IAAU,EAAiB;QAC7C,OAAOA,KACJvC,UAAU,CAAC,SACXwC,YAAY,CAAC,IACZC,IAAAA,gCAAe,EAAC/E,IAAAA,UAAI,EAAC,IAAI,CAAC3C,GAAG,EAAE,IAAI,CAACC,MAAM,CAACE,OAAO,GAAG;IAE3D;IAEA,MAAcwH,iBAAiBH,IAAU,EAAE;QACzC,MAAMI,oBAAoBJ,KAAKvC,UAAU,CAAC;QAE1C,MAAM4C,iBAAiB,IAAI,CAAC5H,MAAM,CAAC4H,cAAc;QAEjD,OAAOD,kBAAkBH,YAAY,CAAC;YACpC,MAAMK,YAAY,CAAC,IAAI,CAAC5H,QAAQ,GAC3B,EAAE,GACH,MAAM0H,kBACH3C,UAAU,CAAC,kBACXwC,YAAY,CAAC,IACZM,QAAQC,GAAG,CAAC;oBACVC,IAAAA,0BAAY,EAAC,IAAI,CAAC/H,QAAQ,EAAG,SAAS2H,gBAAgB;oBACtDI,IAAAA,0BAAY,EACV,IAAI,CAAC/H,QAAQ,EACb,cACA2H,gBACA;iBAEH;YAGT,IAAI,CAAC/G,YAAY,GAAG,MAAM8G,kBACvB3C,UAAU,CAAC,wBACXwC,YAAY,CAAC,IACZS,IAAAA,2BAAkB,EAAC;oBACjBC,OAAO;oBACPN,gBAAgB,IAAI,CAAC5H,MAAM,CAAC4H,cAAc;oBAC1CO,WAAWC,qBAAU,CAACC,KAAK;oBAC3BR,WAAWA,UAAUpL,MAAM,CACzB,CAAC6L,IAAkC,OAAOA,MAAM;oBAElDrI,UAAU,IAAI,CAACA,QAAQ;oBACvBM,QAAQ,IAAI,CAACA,MAAM;gBACrB;YAGJ,MAAMgI,cAAc,MAAMZ,kBACvB3C,UAAU,CAAC,sBACXwC,YAAY,CAAC,IACZgB,IAAAA,0BAAiB,EAAC;oBAChBjI,QAAQ,IAAI,CAACA,MAAM;oBACnBJ,SAAS,IAAI,CAACA,OAAO;oBACrBH,QAAQ,IAAI,CAACA,MAAM;oBACnByI,UAAU,EAAE;oBACZP,OAAO;oBACPQ,OAAO,IAAI,CAAC7H,YAAY;oBACxBZ,UAAU,IAAI,CAACA,QAAQ;oBACvB0I,aAAa,IAAI,CAACtI,YAAY;oBAC9BuI,SAAS,IAAI,CAAC7I,GAAG;oBACjB6H,gBAAgB,IAAI,CAAC5H,MAAM,CAAC4H,cAAc;gBAC5C;YAGJ,MAAMiB,uBAAuB;gBAC3BC,KAAK;gBACL3I,SAAS,IAAI,CAACA,OAAO;gBACrBC,eAAe,IAAI,CAACA,aAAa;gBACjCJ,QAAQ,IAAI,CAACA,MAAM;gBACnBC,UAAU,IAAI,CAACA,QAAQ;gBACvBK,UAAU,IAAI,CAACA,QAAQ;gBACvByI,kBAAkB,IAAI,CAAC/I,MAAM,CAACgJ,iBAAiB;gBAC/CC,mBAAmB,IAAI,CAACjJ,MAAM,CAACkJ,kBAAkB;gBACjDC,gBAAgB,IAAI,CAAC3H,eAAe;gBACpCjB,QAAQ,IAAI,CAACA,MAAM;YACrB;YAEA,OAAOoH,kBACJ3C,UAAU,CAAC,2BACXwC,YAAY,CAAC;gBACZ,MAAM4B,OAAO,MAAMC,IAAAA,8BAAe,EAAC;oBACjCtJ,KAAK,IAAI,CAACA,GAAG;oBACbC,QAAQ6I,qBAAqB7I,MAAM;oBACnC8I,KAAK;gBACP;gBACA,OAAOhB,QAAQC,GAAG,CAAC;oBACjB,0BAA0B;oBAC1BuB,IAAAA,sBAAoB,EAAC,IAAI,CAACvJ,GAAG,EAAE;wBAC7B,GAAG8I,oBAAoB;wBACvBU,cAAcC,0BAAc,CAAChF,MAAM;wBACnC+D,aAAaA,YAAY/D,MAAM;wBAC/B,GAAG4E,IAAI;oBACT;oBACAE,IAAAA,sBAAoB,EAAC,IAAI,CAACvJ,GAAG,EAAE;wBAC7B,GAAG8I,oBAAoB;wBACvBU,cAAcC,0BAAc,CAACC,MAAM;wBACnClB,aAAaA,YAAYkB,MAAM;wBAC/B,GAAGL,IAAI;oBACT;oBACAE,IAAAA,sBAAoB,EAAC,IAAI,CAACvJ,GAAG,EAAE;wBAC7B,GAAG8I,oBAAoB;wBACvBU,cAAcC,0BAAc,CAACE,UAAU;wBACvCnB,aAAaA,YAAYmB,UAAU;wBACnC,GAAGN,IAAI;oBACT;iBACD;YACH;QACJ;IACF;IAEA,MAAaO,qBAAoC;QAC/C,IAAI,IAAI,CAACC,eAAe,EAAE;QAE1B,MAAMR,OAAO,MAAMC,IAAAA,8BAAe,EAAC;YACjCtJ,KAAK,IAAI,CAACA,GAAG;YACbC,QAAQ,IAAI,CAACA,MAAM;YACnB8I,KAAK;QACP;QACA,MAAMe,iBAAiB,MAAMP,IAAAA,sBAAoB,EAAC,IAAI,CAACvJ,GAAG,EAAE;YAC1DoJ,gBAAgB,IAAI,CAAC3H,eAAe;YACpCsH,KAAK;YACLS,cAAcC,0BAAc,CAAChF,MAAM;YACnCxE,QAAQ,IAAI,CAACA,MAAM;YACnBG,SAAS,IAAI,CAACA,OAAO;YACrBC,eAAe,IAAI,CAACA,aAAa;YACjCG,QAAQ,IAAI,CAACA,MAAM;YACnBN,UAAU,IAAI,CAACA,QAAQ;YACvBK,UAAU;gBACRwJ,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACAjB,kBAAkB;gBAChBe,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACAf,mBAAmB,EAAE;YACrBgB,eAAe;YACf1B,aAAa,AACX,CAAA,MAAMC,IAAAA,0BAAiB,EAAC;gBACtBjI,QAAQ,IAAI,CAACA,MAAM;gBACnBJ,SAAS,IAAI,CAACA,OAAO;gBACrBH,QAAQ,IAAI,CAACA,MAAM;gBACnByI,UAAU,EAAE;gBACZP,OAAO;gBACPQ,OAAO;oBACL,SAAS;oBACT,WAAW;gBACb;gBACAzI,UAAU,IAAI,CAACA,QAAQ;gBACvB0I,aAAa,IAAI,CAACtI,YAAY;gBAC9BuI,SAAS,IAAI,CAAC7I,GAAG;gBACjB6H,gBAAgB,IAAI,CAAC5H,MAAM,CAAC4H,cAAc;YAC5C,EAAC,EACDpD,MAAM;YACR,GAAG4E,IAAI;QACT;QACA,MAAMc,mBAAmBC,IAAAA,gBAAO,EAACN;QAEjC,IAAI,CAACD,eAAe,GAAG,MAAM,IAAI9B,QAAQ,CAACsC;YACxC,IAAIC,yBAAyB;YAC7BH,iBAAiBI,KAAK,CACpB,kFAAkF;YAClFT,eAAeU,YAAY,EAC3B,gCAAgC;YAChC,CAACC;gBACC,IAAI,CAACH,wBAAwB;oBAC3BA,yBAAyB;oBACzBD,QAAQ;gBACV;YACF;QAEJ;IACF;IAEA,MAAcK,qBAAqBlD,IAAU,EAAE;QAC7C,MAAMmD,kBAAkBnD,KAAKvC,UAAU,CAAC;QACxC,OAAO0F,gBAAgBlD,YAAY,CAAc,UAC/CxL;IAEJ;IAEA,MAAa2O,QAAuB;QAClC,MAAMC,YAAY,IAAI,CAACpJ,eAAe,CAACwD,UAAU,CAAC;QAClD4F,UAAU/I,IAAI,GAAG,uDAAuD;;QAExE,IAAI,CAACf,WAAW,GAAG,MAAM,IAAI,CAAC2J,oBAAoB,CAACG;QAEnD,MAAMC,gBAAgBC,IAAAA,wBAAgB;QACtC,IAAID,iBAAiB,CAAC,IAAI,CAACE,mBAAmB,EAAE;YAC9C,MAAMC,YAAYtJ,QAAQsJ,SAAS;YACnC,IAAIC;YACJ,IAAI;gBACF,8EAA8E;gBAC9E,MAAMC,gBAAgB,MAAM5L,MAC1B,CAAC,iBAAiB,EAAE0L,UAAU,UAAU,CAAC,EACzCG,IAAI,CAAC,CAACnO,MAAQA,IAAI2C,IAAI;gBACxB,iFAAiF;gBACjFsL,YAAYC,aAAa,CAAC,EAAE;YAC9B,EAAE,OAAM,CAAC;YACT,IAAID,WAAW;gBACb,IAAI,CAACF,mBAAmB,GAAGE,UAAUF,mBAAmB;YAC1D;QACF;QAEA,MAAM,IAAI,CAACzD,KAAK,CAACsD;QACjB,oDAAoD;QACpD,MAAMQ,YAAE,CAACC,KAAK,CAAC,IAAI,CAACnL,OAAO,EAAE;YAAEoL,WAAW;QAAK;QAE/C,MAAMC,sBAAsB7I,IAAAA,UAAI,EAAC,IAAI,CAACxC,OAAO,EAAE;QAC/C,8EAA8E;QAC9E,uDAAuD;QACvD,MAAMkL,YAAE,CAACI,SAAS,CAACD,qBAAqB;QAExC,IAAI,CAACE,oBAAoB,GAAG,MAAM,IAAI,CAAC/D,gBAAgB,CAACkD;QAExD,KAAK,MAAM5K,UAAU,IAAI,CAACyL,oBAAoB,CAAE;YAC9C,MAAMC,eAAe1L,OAAO2L,KAAK;YACjC3L,OAAO2L,KAAK,GAAG,OAAO,GAAGC;oBACJ;gBAAnB,MAAMC,aAAa,EAAA,sBAAA,IAAI,CAACC,aAAa,qBAAlB,oBAAoBD,UAAU,KAAI;gBACrD,MAAME,UAAUC,IAAAA,gCAAU,EAACH;gBAC3B,wCAAwC;gBACxC,MAAMtD,cAAc,MAAMmD,gBAAgBE;gBAC1C,MAAMK,sBAAsBjM,OAAOjB,IAAI,KAAKyK,0BAAc,CAAChF,MAAM;gBACjE,MAAM0H,0BAA0BlM,OAAOjB,IAAI,KAAKyK,0BAAc,CAACC,MAAM;gBACrE,MAAM0C,0BACJnM,OAAOjB,IAAI,KAAKyK,0BAAc,CAACE,UAAU;gBAE3C,MAAM5B,QAAQC,GAAG,CACfqE,OAAOC,IAAI,CAACN,SAASxJ,GAAG,CAAC,OAAO+J;oBAC9B,MAAMC,YAAYR,OAAO,CAACO,SAAS;oBACnC,MAAM,EAAEE,UAAU,EAAEC,OAAO,EAAE,GAAGF;oBAEhC,MAAMG,SACJ,sDAAsDlG,IAAI,CACxD8F;oBAEJ,MAAM,GAAGK,IAAI,YAAY,OAAM9J,KAAK,GAAG6J,MAAQ,kCAAkC;;oBAEjF,IAAIC,QAAQnD,0BAAc,CAAChF,MAAM,IAAI,CAACyH,qBAAqB;oBAC3D,IAAIU,QAAQnD,0BAAc,CAACC,MAAM,IAAI,CAACyC,yBACpC;oBACF,IAAIS,QAAQnD,0BAAc,CAACE,UAAU,IAAI,CAACyC,yBACxC;oBAEF,MAAMS,UAAUL,UAAUM,IAAI,KAAKC,gCAAU,CAACC,KAAK;oBACnD,MAAMC,eAAeT,UAAUM,IAAI,KAAKC,gCAAU,CAACG,WAAW;oBAE9D,0DAA0D;oBAC1D,IAAIL,SAAS;wBACX,MAAMM,aACJ,CAACT,WAAWU,IAAAA,cAAU,EAACZ,UAAUa,gBAAgB;wBACnD,IAAI,CAACF,YAAY;4BACf,OAAOnB,OAAO,CAACO,SAAS;4BACxB;wBACF;oBACF;oBAEA,sEAAsE;oBACtE,IAAIU,cAAc;wBAChB,IAAIT,UAAUc,qBAAqB,EAAE;4BACnC,MAAMH,aACJ,CAACT,WAAWU,IAAAA,cAAU,EAACZ,UAAUc,qBAAqB;4BACxD,IAAI,CAACH,YAAY;gCACf,OAAOnB,OAAO,CAACO,SAAS;gCACxB;4BACF;wBACF;oBACF;oBAEA,8CAA8C;oBAC9C,IAAIzJ,SAAS,WAAW;wBACtB,IAAI,CAAC3B,yBAAyB,GAAG;oBACnC;oBAEA,MAAMoM,YAAY,CAAC,CAAC,IAAI,CAAC/M,MAAM;oBAC/B,MAAMgN,YAAYD,aAAad,WAAW1O,UAAU,CAAC;oBACrD,MAAM0P,aAAaZ,UACf,MAAMa,IAAAA,sCAA6B,EAAC;wBAClCC,gBAAgBH;wBAChB3F,gBAAgB,IAAI,CAAC5H,MAAM,CAAC4H,cAAc;wBAC1C+F,cAAcpB,UAAUa,gBAAgB;wBACxC7M,QAAQ,IAAI,CAACA,MAAM;wBACnBP,QAAQ,IAAI,CAACA,MAAM;wBACnBkI,OAAO;wBACPrF;oBACF,KACAvF;oBAEJ,IAAIkQ,CAAAA,8BAAAA,WAAYX,IAAI,MAAKzE,qBAAU,CAACC,KAAK,EAAE;4BAEvCmF,2BAAAA,oBACAA,4BAAAA;wBAFF,IACEA,EAAAA,qBAAAA,WAAWxN,MAAM,sBAAjBwN,4BAAAA,mBAAmBxN,MAAM,qBAAzBwN,0BAA2BI,GAAG,MAAK,QACnCJ,EAAAA,sBAAAA,WAAWxN,MAAM,sBAAjBwN,6BAAAA,oBAAmBxN,MAAM,qBAAzBwN,2BAA2BI,GAAG,MAAK,UACnC;4BACA,IAAI,CAAC5M,iBAAiB,GAAG;wBAC3B;oBACF;oBAEA,MAAM6M,oBACJN,aAAaC,CAAAA,8BAAAA,WAAYM,GAAG,MAAKC,4BAAgB,CAACvJ,MAAM;oBAE1D,MAAMwJ,WAAuBzB,UAAUC,UAAU,CAAC1O,UAAU,CAC1D,YAEEsK,qBAAU,CAACC,KAAK,GAChBkE,UAAUC,UAAU,CAAC1O,UAAU,CAAC,UAC9BsK,qBAAU,CAAC6F,GAAG,GACd7F,qBAAU,CAAC8F,IAAI;oBAErB,IAAIF,aAAa,SAAS;wBACxB,IAAI,CAAC9M,yBAAyB,GAAG;oBACnC;oBACA,IAAI8M,aAAa,OAAO;wBACtB,IAAI,CAAC/M,uBAAuB,GAAG;oBACjC;oBAEA,MAAMkN,oBACJC,IAAAA,gCAAyB,EAACvL,SAASmL,aAAa5F,qBAAU,CAAC8F,IAAI;oBAEjE,IAAIG,cAAcb,8BAAAA,WAAYc,OAAO;oBAErC,IACEC,IAAAA,uBAAgB,EAAC1L,SACjB,CAAC,IAAI,CAAC7C,MAAM,CAACwO,YAAY,CAACC,cAAc,IACxCJ,gBAAgB,UAChB;wBACAlI,KAAIC,IAAI,CACN;wBAEFiI,cAAc;oBAChB;oBAEAK,IAAAA,+BAAsB,EAAC;wBACrB7L;wBACAwL;wBACAL;wBACAW,cAAc;4BACZ,kDAAkD;4BAClD,IAAI,CAACxC,2BAA2B,CAACS,SAAS;4BAC1Cb,OAAO,CAACO,SAAS,CAACsC,MAAM,GAAGC,8BAAQ;4BAEnC,IAAIV,mBAAmB;gCACrB,MAAMW,uBAAuBtC,WAAW7G,OAAO,CAAC,QAAQ;gCACxD4C,WAAW,CAACuG,qBAAqB,GAAGC,IAAAA,2BAAkB,EAAC;oCACrDxF,cAAcC,0BAAc,CAACE,UAAU;oCACvC3K,MAAM+P;oCACNE,OAAOC,IAAAA,gCAAuB,EAAC;wCAC7B7B,kBAAkBb,UAAUa,gBAAgB;wCAC5C8B,cAAc;wCACdhH,OAAO;oCACT;oCACA2F,mBAAmB;oCACnBP;gCACF;gCACA;4BACF;4BACA,MAAM6B,eAAe5B,YACjB6B,IAAAA,oBAAW,EAAC;gCACVrQ,MAAMyN;gCACN3J;gCACAwM,UAAU9C,UAAU8C,QAAQ;gCAC5BC,UAAUC,WAAK,CAAC7M,IAAI,CAClB8M,wBAAa,EACbC,IAAAA,cAAQ,EACN,IAAI,CAAClP,MAAM,EACXgM,UAAUa,gBAAgB,EAC1BzH,OAAO,CAAC,OAAO;gCAEnBpF,QAAQ,IAAI,CAACA,MAAM;gCACnBqH,gBAAgB,IAAI,CAAC5H,MAAM,CAAC4H,cAAc;gCAC1CgB,SAAS,IAAI,CAAC7I,GAAG;gCACjBmI,OAAO;gCACPwH,cAAc,IAAI,CAAC1P,MAAM,CAAC2P,UAAU,CAACD,YAAY;gCACjDE,UAAU,IAAI,CAAC5P,MAAM,CAAC4P,QAAQ;gCAC9BC,aAAa,IAAI,CAAC7P,MAAM,CAAC6P,WAAW;gCACpCC,kBAAkB,IAAI,CAAC9P,MAAM,CAAC+P,MAAM;gCACpCC,eAAe,EAAExC,8BAAAA,WAAYwC,eAAe;gCAC5CC,kBAAkBC,OAAOC,IAAI,CAC3BrL,KAAKsL,SAAS,CAAC5C,CAAAA,8BAAAA,WAAYlK,UAAU,KAAI,CAAC,IAC1CsB,QAAQ,CAAC;4BACb,GAAGyL,MAAM,GACT/S;4BAEJiL,WAAW,CAACiE,WAAW,GAAGuC,IAAAA,2BAAkB,EAAC;gCAC3CxF,cAAcC,0BAAc,CAACE,UAAU;gCACvC3K,MAAMyN;gCACNwC,OAAOsB,IAAAA,2BAAkB,EAAC;oCACxBlD,kBAAkBb,UAAUa,gBAAgB;oCAC5CxE,SAAS,IAAI,CAAC7I,GAAG;oCACjBI,SAAS,IAAI,CAACA,OAAO;oCACrBqM;oCACAxM,QAAQ,IAAI,CAACA,MAAM;oCACnBkI,OAAO;oCACPrF;oCACA6F,OAAO,IAAI,CAAC7H,YAAY;oCACxBgN;oCACAsB;oCACAhH,WAAWoF,YAAYnF,qBAAU,CAAC6F,GAAG,GAAG7F,qBAAU,CAACC,KAAK;oCACxD2H,eAAe,EAAExC,8BAAAA,WAAYwC,eAAe;gCAC9C;gCACA1C;4BACF;wBACF;wBACAiD,UAAU;4BACR,IAAI,CAACtE,qBAAqB;4BAC1B,IAAIe,cAAc;gCAChBjB,OAAO,CAACO,SAAS,CAACsC,MAAM,GAAGC,8BAAQ;gCACnCtG,WAAW,CAACiE,WAAW,GAAGuC,IAAAA,2BAAkB,EAAC;oCAC3ChQ,MAAMyN;oCACNjD,cAAcC,0BAAc,CAAChF,MAAM;oCACnCwK,OAAOzC,UAAUiE,OAAO;oCACxBlD;gCACF;4BACF,OAAO;gCACLvB,OAAO,CAACO,SAAS,CAACsC,MAAM,GAAGC,8BAAQ;gCACnCtG,WAAW,CAACiE,WAAW,GAAGuC,IAAAA,2BAAkB,EAAC;oCAC3ChQ,MAAMyN;oCACNjD,cAAcC,0BAAc,CAAChF,MAAM;oCACnCwK,OAAOyB,IAAAA,uBAAc,EAAC;wCACpBrD,kBAAkBb,UAAUa,gBAAgB;wCAC5CvK;oCACF;oCACAyK;gCACF;4BACF;wBACF;wBACAoD,UAAU;4BACR,kDAAkD;4BAClD,IAAI,CAACxE,2BAA2B,CAACU,SAAS;4BAC1Cb,OAAO,CAACO,SAAS,CAACsC,MAAM,GAAGC,8BAAQ;4BACnC,IAAI8B,kBAAkBlB,IAAAA,cAAQ,EAC5BzP,OAAO4Q,OAAO,EACdrE,UAAUa,gBAAgB;4BAE5B,IACE,CAACyD,IAAAA,gBAAU,EAACF,oBACZ,CAACA,gBAAgB7S,UAAU,CAAC,QAC5B;gCACA6S,kBAAkB,CAAC,EAAE,EAAEA,iBAAiB;4BAC1C;4BAEA,IAAI3B;4BACJ,IAAIb,mBAAmB;gCACrBa,QAAQC,IAAAA,gCAAuB,EAAC;oCAC9B7B,kBAAkBb,UAAUa,gBAAgB;oCAC5C8B,cAAc;oCACdhH,OAAO;gCACT;gCACAK,WAAW,CAACiE,WAAW,GAAGuC,IAAAA,2BAAkB,EAAC;oCAC3CxF,cAAcC,0BAAc,CAACC,MAAM;oCACnC1K,MAAMyN;oCACNqB,mBAAmB;oCACnBmB;oCACA1B;gCACF;4BACF,OAAO,IAAIiB,IAAAA,uBAAgB,EAAC1L,OAAO;gCACjCmM,QAAQsB,IAAAA,2BAAkB,EAAC;oCACzBlD,kBAAkBb,UAAUa,gBAAgB;oCAC5CxE,SAAS,IAAI,CAAC7I,GAAG;oCACjBI,SAAS,IAAI,CAACA,OAAO;oCACrBqM;oCACAxM,QAAQ,IAAI,CAACA,MAAM;oCACnBkI,OAAO;oCACPrF;oCACA6F,OAAO,IAAI,CAAC7H,YAAY;oCACxBgN;oCACA1F,WAAWC,qBAAU,CAACC,KAAK;oCAC3B2H,eAAe,EAAExC,8BAAAA,WAAYwC,eAAe;gCAC9C;4BACF,OAAO,IAAIzC,WAAW;gCACpByB,QAAQI,IAAAA,oBAAW,EAAC;oCAClBrQ,MAAMyN;oCACN3J;oCACAwM,UAAU9C,UAAU8C,QAAQ;oCAC5BC,UAAUC,WAAK,CAAC7M,IAAI,CAClB8M,wBAAa,EACbC,IAAAA,cAAQ,EACN,IAAI,CAAClP,MAAM,EACXgM,UAAUa,gBAAgB,EAC1BzH,OAAO,CAAC,OAAO;oCAEnBpF,QAAQ,IAAI,CAACA,MAAM;oCACnBqH,gBAAgB,IAAI,CAAC5H,MAAM,CAAC4H,cAAc;oCAC1CgB,SAAS,IAAI,CAAC7I,GAAG;oCACjBmI,OAAO;oCACPwH,cAAc,IAAI,CAAC1P,MAAM,CAAC2P,UAAU,CAACD,YAAY;oCACjDE,UAAU,IAAI,CAAC5P,MAAM,CAAC4P,QAAQ;oCAC9BC,aAAa,IAAI,CAAC7P,MAAM,CAAC6P,WAAW;oCACpCC,kBAAkB,IAAI,CAAC9P,MAAM,CAAC+P,MAAM;oCACpCC,eAAe,EAAExC,8BAAAA,WAAYwC,eAAe;oCAC5CC,kBAAkBC,OAAOC,IAAI,CAC3BrL,KAAKsL,SAAS,CAAC5C,CAAAA,8BAAAA,WAAYlK,UAAU,KAAI,CAAC,IAC1CsB,QAAQ,CAAC;gCACb;4BACF,OAAO,IAAIkM,IAAAA,sBAAU,EAACjO,OAAO;gCAC3BmM,QAAQ+B,IAAAA,oCAAmB,EAAC;oCAC1BC,MAAMC,oBAAS,CAACC,SAAS;oCACzBrO;oCACAuK,kBAAkBuD;oCAClBX,eAAe,EAAExC,8BAAAA,WAAYwC,eAAe;oCAC5CC,kBAAkBzC,CAAAA,8BAAAA,WAAYlK,UAAU,KAAI,CAAC;gCAC/C;4BACF,OAAO,IACL,CAACiL,IAAAA,uBAAgB,EAAC1L,SAClB,CAACsO,IAAAA,wCAAmB,EAACR,oBACrB,CAACS,IAAAA,wCAAmB,EAACvO,SACrB,CAACsL,mBACD;gCACAa,QAAQ+B,IAAAA,oCAAmB,EAAC;oCAC1BC,MAAMC,oBAAS,CAAC5I,KAAK;oCACrBxF;oCACA6F,OAAO,IAAI,CAAC7H,YAAY;oCACxBuM,kBAAkBuD;oCAClBX,eAAe,EAAExC,8BAAAA,WAAYwC,eAAe;oCAC5CC,kBAAkBzC,CAAAA,8BAAAA,WAAYlK,UAAU,KAAI,CAAC;gCAC/C;4BACF,OAAO;gCACL0L,QAAQ2B;4BACV;4BAEApI,WAAW,CAACiE,WAAW,GAAGuC,IAAAA,2BAAkB,EAAC;gCAC3CxF,cAAcC,0BAAc,CAACC,MAAM;gCACnC1K,MAAMyN;gCACNqB;gCACAmB;gCACA1B;4BACF;wBACF;oBACF;gBACF;gBAGF,IAAI,CAAC,IAAI,CAACtM,iBAAiB,EAAE;oBAC3B,OAAOuH,WAAW,CAAC8I,2CAA+B,CAAC;gBACrD;gBACA,IAAI,CAAC,IAAI,CAACnQ,yBAAyB,EAAE;oBACnC,OAAOqH,WAAW,CAAC+I,4CAAgC,CAAC;oBACpD,OAAO/I,WAAW,CAAC,aAAa;oBAChC,OAAOA,WAAW,CAAC,eAAe;oBAClC,OAAOA,WAAW,CAAC,UAAU;oBAC7B,OAAOA,WAAW,CAAC,kBAAkB;gBACvC;gBACA,qEAAqE;gBACrE,IAAI,CAAC,IAAI,CAACvH,iBAAiB,IAAI,CAAC,IAAI,CAACE,yBAAyB,EAAE;oBAC9D,OAAOqH,WAAW,CAACgJ,qDAAyC,CAAC;gBAC/D;gBACA,IAAI,CAAC,IAAI,CAACtQ,uBAAuB,EAAE;oBACjC,OAAOsH,WAAW,CAACiJ,gDAAoC,CAAC;gBAC1D;gBAEA,OAAOjJ;YACT;QACF;QAEA,iFAAiF;QACjF,uBAAuB;QACvB,IAAI,CAACkD,oBAAoB,CAACgG,WAAW,GAAG;QAExC,IAAI,CAAC3F,aAAa,GAAG3B,IAAAA,gBAAO,EAC1B,IAAI,CAACsB,oBAAoB;QAG3B,uEAAuE;QACvE,MAAMiG,kBAAkB,IAAI,CAAC5F,aAAa,CAAC6F,SAAS,CAAC,EAAE,CAACD,eAAe;QACvE,KAAK,MAAME,YAAY,IAAI,CAAC9F,aAAa,CAAC6F,SAAS,CAAE;YACnDC,SAASF,eAAe,GAAGA;YAC3B,qFAAqF;YACrFE,SAASC,WAAW,GAAGC,KAAKC,GAAG;YAC/B,sGAAsG;YACtGH,SAASI,KAAK,CAACC,SAAS,CAACC,SAAS,CAAC;gBACjCC,UAASC,OAAY;oBACnB,IAAIA,QAAQrT,IAAI,KAAK,yBAAyB;wBAC5C,OAAO;oBACT;oBACA,OAAOqT;gBACT;YACF;QACF;QAEA,IAAI,CAACtG,aAAa,CAACkG,KAAK,CAACK,IAAI,CAACC,GAAG,CAAC,qBAAqB;gBACrDZ;YAAAA,oCAAAA,yBAAAA,gBAAiBa,KAAK,qBAAtBb,4BAAAA;QACF;QACAc,IAAAA,sBAAc,EACZ,IAAI,CAAC1G,aAAa,CAAC6F,SAAS,CAAC,EAAE,EAC/B,IAAI,CAAC7F,aAAa,CAAC6F,SAAS,CAAC,EAAE,EAC/B,IAAI,CAAC7F,aAAa,CAAC6F,SAAS,CAAC,EAAE;QAGjC,yEAAyE;QACzE,gEAAgE;QAChE,MAAMc,qBAAqB,IAAIjW;QAC/B,MAAMkW,qBAAqB,IAAIlW;QAC/B,MAAMmW,yBAAyB,IAAInW;QAEnC,MAAMoW,8BAA8B,IAAIpW;QACxC,MAAMqW,wBAAwB,IAAIrW;QAElC,MAAMsW,uBAAuB,IAAIC;QACjC,MAAMC,uBAAuB,IAAID;QACjC,MAAME,2BAA2B,IAAIF;QACrC,MAAMG,4BAA4B,IAAIH;QAEtC,MAAMI,qBAAqB,IAAIC,OAC7B,CAAC,MAAM,EAAE,IAAI,CAACpT,MAAM,CAAC4H,cAAc,CAAClF,IAAI,CAAC,KAAK,EAAE,CAAC;QAGnD,MAAM2Q,mBACJ,CACEC,aACAC,cACAC,8BAEF,CAACC;gBACC,IAAI;oBACFA,MAAMlL,WAAW,CAACmL,OAAO,CAAC,CAAC/H,OAAOgB;wBAChC,IACEA,IAAI7O,UAAU,CAAC,aACf6O,IAAI7O,UAAU,CAAC,WACf6V,IAAAA,2BAAoB,EAAChH,MACrB;4BACA,mDAAmD;4BACnDhB,MAAMiI,MAAM,CAACF,OAAO,CAAC,CAACG;gCACpB,IAAIA,MAAMxM,EAAE,KAAKsF,KAAK;oCACpB,MAAMmH,eACJL,MAAMM,UAAU,CAACC,uBAAuB,CAACH;oCAE3C,IAAII,sBAAsB;oCAC1B,IAAIC,aAAa,IAAIC,kBAAS;oCAC9B,IAAIC,wBAAwB,IAAID,kBAAS;oCAEzCL,aAAaJ,OAAO,CAAC,CAACW;wCACpB,IACEA,IAAIC,QAAQ,IACZD,IAAIC,QAAQ,CAAC3O,OAAO,CAAC,OAAO,KAAK4O,QAAQ,CAAC5H,QAC1C,oCAAoC;wCACpCwG,mBAAmBqB,IAAI,CAACH,IAAIC,QAAQ,GACpC;gDAaED,oBAAAA;4CAZF,uDAAuD;4CACvD,uDAAuD;4CACvD,wDAAwD;4CACxD,sDAAsD;4CACtD,MAAMrQ,OAAO5E,QAAQ,UAClBqV,UAAU,CAAC,QACXC,MAAM,CAACL,IAAIM,cAAc,GAAGC,MAAM,IAClCC,MAAM,GACNjQ,QAAQ,CAAC;4CAEZ,IACEyP,IAAIS,KAAK,KAAKlP,yBAAc,CAACmP,qBAAqB,IAClDV,CAAAA,wBAAAA,iBAAAA,IAAKW,SAAS,sBAAdX,qBAAAA,eAAgBvG,GAAG,qBAAnBuG,mBAAqBxH,IAAI,MAAK,UAC9B;gDACAuH,sBAAsBa,GAAG,CAACjR;4CAC5B;4CAEAkQ,WAAWe,GAAG,CAACjR;wCACjB,OAAO;gDASHqQ,qBAAAA;4CARF,oDAAoD;4CACpD,MAAMrQ,OAAOyP,MAAMM,UAAU,CAACmB,aAAa,CACzCb,KACAR,MAAMvF,OAAO;4CAGf,IACE+F,IAAIS,KAAK,KAAKlP,yBAAc,CAACmP,qBAAqB,IAClDV,CAAAA,wBAAAA,kBAAAA,IAAKW,SAAS,sBAAdX,sBAAAA,gBAAgBvG,GAAG,qBAAnBuG,oBAAqBxH,IAAI,MAAK,UAC9B;gDACAuH,sBAAsBa,GAAG,CAACjR;4CAC5B;4CAEAkQ,WAAWe,GAAG,CAACjR;4CAEf,iDAAiD;4CACjD,0BAA0B;4CAC1B,IACE2I,IAAI7O,UAAU,CAAC,WACf,qBAAqB0W,IAAI,CAACH,IAAIC,QAAQ,IAAI,KAC1C;gDACA,MAAMa,cAAcd,IAAIS,KAAK,GAAG,MAAMT,IAAIC,QAAQ;gDAClD,MAAMc,WACJlC,0BAA0BmC,GAAG,CAACF;gDAChC,IAAIC,YAAYA,aAAapR,MAAM;oDACjCiQ,sBAAsB;gDACxB;gDACAf,0BAA0BoC,GAAG,CAACH,aAAanR;4CAC7C;wCACF;oCACF;oCAEA,MAAMoR,WAAW9B,YAAY+B,GAAG,CAAC1I;oCACjC,MAAM4I,UAAUrB,WAAWtP,QAAQ;oCACnC,IAAIwQ,YAAYA,aAAaG,SAAS;wCACpChC,aAAa0B,GAAG,CAACtI;oCACnB;oCACA2G,YAAYgC,GAAG,CAAC3I,KAAK4I;oCAErB,IAAI/B,6BAA6B;wCAC/B,MAAMgC,YACJ5P,yBAAc,CAACmP,qBAAqB,GAAG,MAAMpI;wCAC/C,MAAM8I,iBAAiBnC,YAAY+B,GAAG,CAACG;wCACvC,MAAME,gBAAgBtB,sBAAsBxP,QAAQ;wCACpD,IAAI6Q,kBAAkBA,mBAAmBC,eAAe;4CACtDlC,4BAA4ByB,GAAG,CAACtI;wCAClC;wCACA2G,YAAYgC,GAAG,CAACE,WAAWE;oCAC7B;oCAEA,IAAIzB,qBAAqB;wCACvBpB,sBAAsBoC,GAAG,CAACtI;oCAC5B;gCACF;4BACF;wBACF;oBACF;gBACF,EAAE,OAAOgJ,KAAK;oBACZpY,QAAQN,KAAK,CAAC0Y;gBAChB;YACF;QAEF,IAAI,CAAC7J,aAAa,CAAC6F,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC4D,IAAI,CAACtD,GAAG,CAC5C,8BACAe,iBAAiBP,sBAAsBL;QAEzC,IAAI,CAAC3G,aAAa,CAAC6F,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC4D,IAAI,CAACtD,GAAG,CAC5C,8BACAe,iBACEL,sBACAN,oBACAE;QAGJ,IAAI,CAAC9G,aAAa,CAAC6F,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC4D,IAAI,CAACtD,GAAG,CAC5C,8BACAe,iBACEJ,0BACAN,wBACAC;QAIJ,8GAA8G;QAC9G,IAAI,CAAC9G,aAAa,CAAC6F,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC6D,MAAM,CAACvD,GAAG,CAC9C,8BACA,CAACqD;YACC,IAAI,CAAChV,WAAW,GAAGgV;YACnB,IAAI,CAACtU,WAAW,GAAG;YACnB,IAAI,CAACyU,gBAAgB,GAAGxY;QAC1B;QAGF,IAAI,CAACwO,aAAa,CAAC6F,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACmB;YACC,IAAI,CAAC9S,WAAW,GAAG;YACnB,IAAI,CAACW,eAAe,GAAGmS;QACzB;QAGF,IAAI,CAAC3H,aAAa,CAAC6F,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACmB;YACC,IAAI,CAAC9S,WAAW,GAAG;YACnB,IAAI,CAACU,WAAW,GAAGoS;YAEnB,IAAI,CAAC,IAAI,CAACxT,QAAQ,EAAE;gBAClB;YACF;YAEA,MAAM,EAAE1B,WAAW,EAAE,GAAGkV;YAExB,kEAAkE;YAClE,oEAAoE;YACpE,MAAMsC,gBAAgBxX,YAAYyX,WAAW,CAACX,GAAG,CAAC;YAClD,qDAAqD;YACrD,IAAI,CAACU,eAAe;gBAClB;YACF;YAEA,gBAAgB;YAChB,IAAI,IAAI,CAACxU,sBAAsB,KAAK,MAAM;gBACxC,IAAI,CAACA,sBAAsB,GAAGwU,cAAc/R,IAAI,IAAI;gBACpD;YACF;YAEA,2DAA2D;YAC3D,IAAI+R,cAAc/R,IAAI,KAAK,IAAI,CAACzC,sBAAsB,EAAE;gBACtD;YACF;YAEA,6DAA6D;YAC7D,iEAAiE;YACjE,0EAA0E;YAC1E,2EAA2E;YAC3E,IAAI,IAAI,CAAChB,MAAM,EAAE;gBACf,MAAM0V,aAAa,IAAIzZ,IAAI+B,YAAYyX,WAAW,CAAC3J,IAAI;gBACvD,MAAM6J,iBAAiBC,IAAAA,iBAAU,EAC/B,IAAI,CAACL,gBAAgB,IAAI,IAAItZ,OAC7ByZ;gBAGF,IACEC,eAAe7S,MAAM,KAAK,KAC1B6S,eAAeE,KAAK,CAAC,CAACC,YAAcA,UAAUvY,UAAU,CAAC,UACzD;oBACA;gBACF;gBACA,IAAI,CAACgY,gBAAgB,GAAGG;YAC1B;YAEA,IAAI,CAAC1U,sBAAsB,GAAGwU,cAAc/R,IAAI,IAAI;YAEpD,iFAAiF;YACjF,IAAI,CAACN,IAAI,CAAC;gBACRC,QAAQC,6CAA2B,CAACC,WAAW;gBAC/CC,MAAM;YACR;QACF;QAGF,IAAI,CAACgI,aAAa,CAACkG,KAAK,CAACK,IAAI,CAACC,GAAG,CAAC,8BAA8B,CAACmB;YAC/D,MAAM1S,0BAA0B,IAAI,CAACA,uBAAuB;YAC5D,IAAI,CAACA,uBAAuB,GAAG;YAE/B,MAAMuV,oBAAoBH,IAAAA,iBAAU,EAClCzD,oBACAD;YAGF,MAAM8D,wBAAwBJ,IAAAA,iBAAU,EACtCxD,wBACAF;YAGF,MAAM+D,cAAcF,kBACjBG,MAAM,CAACF,uBACP9Z,MAAM,CAAC,CAACkQ,MAAQA,IAAI7O,UAAU,CAAC;YAElC,MAAM4Y,oBAAoB;mBACrBC,MAAMxG,IAAI,CAACwC;mBACXgE,MAAMxG,IAAI,CAACuC;aACf,CAACjW,MAAM,CAAC,CAACsC,OAAS4U,IAAAA,2BAAoB,EAAC5U;YAExC,IAAI2X,kBAAkBrT,MAAM,GAAG,GAAG;gBAChC,IAAI,CAACK,IAAI,CAAC;oBACRuB,OAAOrB,6CAA2B,CAACgT,kBAAkB;gBACvD;YACF;YAEA,IAAIJ,YAAYnT,MAAM,GAAG,GAAG;gBAC1B,IAAI,CAACK,IAAI,CAAC;oBACRuB,OAAOrB,6CAA2B,CAACiT,mBAAmB;oBACtDnO,OAAO4N,kBAAkB/T,GAAG,CAAC,CAACuU,KAC5BhU,IAAAA,wCAAmB,EAACgU,GAAG9P,KAAK,CAAC,QAAQ3D,MAAM;gBAE/C;YACF;YAEA,IACEuP,4BAA4BmE,IAAI,IAChClE,sBAAsBkE,IAAI,IAC1BhW,yBACA;gBACA,IAAI,CAACN,UAAU;gBACf,IAAI,CAACsD,uBAAuB,CAAC0P,MAAMzP,IAAI;YACzC;YAEAyO,mBAAmBuE,KAAK;YACxBtE,mBAAmBsE,KAAK;YACxBrE,uBAAuBqE,KAAK;YAC5BpE,4BAA4BoE,KAAK;YACjCnE,sBAAsBmE,KAAK;QAC7B;QAEA,IAAI,CAAClL,aAAa,CAAC6F,SAAS,CAAC,EAAE,CAACK,KAAK,CAAC6D,MAAM,CAACvD,GAAG,CAC9C,8BACA,CAACqD;YACC,IAAI,CAACjV,WAAW,GAAGiV;YACnB,IAAI,CAACvU,WAAW,GAAG;QACrB;QAEF,IAAI,CAAC0K,aAAa,CAAC6F,SAAS,CAAC,EAAE,CAACK,KAAK,CAACK,IAAI,CAACC,GAAG,CAC5C,8BACA,CAACmB;YACC,IAAI,CAAC/S,WAAW,GAAG;YACnB,IAAI,CAACU,WAAW,GAAGqS;YAEnB,MAAM,EAAElV,WAAW,EAAE,GAAGkV;YACxB,MAAMwC,aAAa,IAAIzZ,IACrB;mBAAI+B,YAAYyX,WAAW,CAAC3J,IAAI;aAAG,CAAC5P,MAAM,CACxC,CAACsC,OAAS,CAAC,CAACE,IAAAA,+BAAsB,EAACF;YAIvC,IAAI,IAAI,CAACkY,cAAc,EAAE;gBACvB,8DAA8D;gBAC9D,0CAA0C;gBAC1C,MAAMC,aAAa7a,KAAK4Z,YAAY,IAAI,CAACgB,cAAc;gBACvD,MAAME,eAAe9a,KAAK,IAAI,CAAC4a,cAAc,EAAGhB;gBAEhD,IAAIiB,WAAWH,IAAI,GAAG,GAAG;oBACvB,KAAK,MAAMK,aAAaF,WAAY;wBAClC,MAAMrU,OAAO5D,IAAAA,+BAAsB,EAACmY;wBACpC,IAAI,CAAC1T,IAAI,CAAC;4BACRC,QAAQC,6CAA2B,CAACyT,UAAU;4BAC9CvT,MAAM;gCAACjB;6BAAK;wBACd;oBACF;gBACF;gBAEA,IAAIsU,aAAaJ,IAAI,GAAG,GAAG;oBACzB,KAAK,MAAMO,eAAeH,aAAc;wBACtC,MAAMtU,OAAO5D,IAAAA,+BAAsB,EAACqY;wBACpC,IAAI,CAAC5T,IAAI,CAAC;4BACRC,QAAQC,6CAA2B,CAAC2T,YAAY;4BAChDzT,MAAM;gCAACjB;6BAAK;wBACd;oBACF;gBACF;YACF;YAEA,IAAI,CAACoU,cAAc,GAAGhB;QACxB;QAGF,IAAI,CAACxR,oBAAoB,GAAG,IAAI+S,mCAAoB,CAClD,IAAI,CAAC1L,aAAa,CAAC6F,SAAS,EAC5B,IAAI,CAAC7Q,WAAW,EAChB,IAAI,CAACiK,mBAAmB;QAG1B,IAAI0M,SAAS;QAEb,IAAI,CAACC,OAAO,GAAG,MAAM,IAAI5P,QAAQ,CAACsC;gBAChB;YAAhB,MAAMsN,WAAU,sBAAA,IAAI,CAAC5L,aAAa,qBAAlB,oBAAoBxB,KAAK,CACvC,kFAAkF;YAClF,IAAI,CAACmB,oBAAoB,CAAClJ,GAAG,CAAC,CAACvC,SAAWA,OAAOuK,YAAY,GAC7D,gCAAgC;YAChC,CAACC;gBACC,IAAI,CAACiN,QAAQ;oBACXA,SAAS;oBACTrN,QAAQsN;gBACV;YACF;QAEJ;QAEA,IAAI,CAAChT,eAAe,GAAGiT,IAAAA,0CAAoB,EAAC;YAC1CC,aAAa,IAAI;YACjB9L,eAAe,IAAI,CAACA,aAAa;YACjC7L,UAAU,IAAI,CAACA,QAAQ;YACvBM,QAAQ,IAAI,CAACA,MAAM;YACnBqI,SAAS,IAAI,CAAC7I,GAAG;YACjB8X,YAAY,IAAI,CAAC7X,MAAM;YACvB,GAAI,IAAI,CAACA,MAAM,CAAC0E,eAAe;QAIjC;QAEA,IAAI,CAACvD,WAAW,GAAG;YACjB2W,IAAAA,uCAAoB,EAAC;gBACnBC,eAAe,IAAI,CAAChY,GAAG;gBACvBqB,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;YAC7C;YACA0W,IAAAA,yCAAsB,EAAC;gBACrB5W,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,aAAa,IAAM,IAAI,CAACA,WAAW;gBACnCC,iBAAiB,IAAM,IAAI,CAACA,eAAe;YAC7C;YACA2W,IAAAA,8DAA8B,EAAC,IAAI,CAACzX,SAAS;YAC7C0X,IAAAA,wDAA2B;YAC3BC,IAAAA,wDAAgC;SACjC;IACH;IAEOC,WACL,EAAErX,uBAAuB,EAAwC,GAAG;QAClEA,yBAAyB;IAC3B,CAAC,EACD;YAGmB;QAFnB,mGAAmG;QACnG,IAAI,CAACA,uBAAuB,GAAGA;QAC/B,MAAM8K,cAAa,sBAAA,IAAI,CAACC,aAAa,qBAAlB,oBAAoBD,UAAU;QACjD,IAAIA,YAAY;gBACdwM;aAAAA,kBAAAA,IAAAA,oCAAc,EAACxM,gCAAfwM,gBAA4BD,UAAU;QACxC;IACF;IAEA,MAAahV,qBAAqBP,IAAY,EAAE;YAcnC,mBAEA,mBAEA;QAjBX,MAAMyV,YAAY,CAAC,EAAE/Z,WAAW,EAAiB;gBAIxCK;YAHP,MAAMA,cAAcD,aAAaJ;YACjC,MAAMga,iBAAiBC,IAAAA,kCAAgB,EAAC3V;YACxC,+FAA+F;YAC/F,OAAOjE,EAAAA,8BAAAA,WAAW,CAAC2Z,eAAe,qBAA3B3Z,4BAA6ByE,MAAM,IAAG,IACzCzE,WAAW,CAAC2Z,eAAe,GAC3Bha,YAAYM,MAAM;QACxB;QAEA,IAAI,IAAI,CAAC6B,WAAW,EAAE;YACpB,OAAO;gBAAC,IAAI,CAACA,WAAW;aAAC;QAC3B,OAAO,IAAI,IAAI,CAACC,WAAW,EAAE;YAC3B,OAAO;gBAAC,IAAI,CAACA,WAAW;aAAC;QAC3B,OAAO,KAAI,oBAAA,IAAI,CAACS,WAAW,qBAAhB,kBAAkBqX,SAAS,IAAI;YACxC,OAAOH,UAAU,IAAI,CAAClX,WAAW;QACnC,OAAO,KAAI,oBAAA,IAAI,CAACC,WAAW,qBAAhB,kBAAkBoX,SAAS,IAAI;YACxC,OAAOH,UAAU,IAAI,CAACjX,WAAW;QACnC,OAAO,KAAI,wBAAA,IAAI,CAACC,eAAe,qBAApB,sBAAsBmX,SAAS,IAAI;YAC5C,OAAOH,UAAU,IAAI,CAAChX,eAAe;QACvC,OAAO;YACL,OAAO,EAAE;QACX;IACF;IAEOoC,KAAKC,MAAwB,EAAQ;QAC1C,IAAI,CAACc,oBAAoB,CAAEiU,OAAO,CAAC/U;IACrC;IAEA,MAAaV,WAAW,EACtBJ,IAAI,EACJK,UAAU,EACVmM,QAAQ,EACRsJ,UAAU,EACVC,KAAK,EACL/a,GAAG,EAQJ,EAAiB;QAChB,OAAO,IAAI,CAAC2D,eAAe,CACxBwD,UAAU,CAAC,eAAe;YACzB6T,WAAWhW;QACb,GACC2E,YAAY,CAAC;gBAYL;YAXP,wDAAwD;YACxD,IAAI3E,SAAS,aAAaE,yBAAa,CAACC,OAAO,CAACH,UAAU,CAAC,GAAG;gBAC5D;YACF;YACA,MAAM5F,QAAQiG,aACV,IAAI,CAACxC,WAAW,GAChB,IAAI,CAACC,WAAW,IAAI,IAAI,CAACD,WAAW;YACxC,IAAIzD,OAAO;gBACT,MAAMA;YACR;YAEA,QAAO,wBAAA,IAAI,CAACyH,eAAe,qBAApB,sBAAsBzB,UAAU,CAAC;gBACtCJ;gBACAwM;gBACAsJ;gBACAC;gBACA/a;YACF;QACF;IACJ;IAEOib,QAAQ;YACb;SAAA,6BAAA,IAAI,CAACrU,oBAAoB,qBAAzB,2BAA2BqU,KAAK;IAClC;AACF"}