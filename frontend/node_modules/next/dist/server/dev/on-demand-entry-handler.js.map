{"version": 3, "sources": ["../../../src/server/dev/on-demand-entry-handler.ts"], "sourcesContent": ["import type ws from 'next/dist/compiled/ws'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { NextConfigComplete } from '../config-shared'\nimport type {\n  DynamicParamTypesShort,\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../app-render/types'\nimport type { CompilerNameValues } from '../../shared/lib/constants'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type HotReloaderWebpack from './hot-reloader-webpack'\n\nimport createDebug from 'next/dist/compiled/debug'\nimport { EventEmitter } from 'events'\nimport { findPageFile } from '../lib/find-page-file'\nimport {\n  getStaticInfoIncludingLayouts,\n  runDependingOnPageType,\n} from '../../build/entries'\nimport { join, posix } from 'path'\nimport { normalizePathSep } from '../../shared/lib/page-path/normalize-path-sep'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { ensureLeadingSlash } from '../../shared/lib/page-path/ensure-leading-slash'\nimport { removePagePathTail } from '../../shared/lib/page-path/remove-page-path-tail'\nimport { reportTrigger } from '../../build/output'\nimport getRouteFromEntrypoint from '../get-route-from-entrypoint'\nimport {\n  isInstrumentationHookFile,\n  isInstrumentationHookFilename,\n  isMiddlewareFile,\n  isMiddlewareFilename,\n} from '../../build/utils'\nimport { PageNotFoundError, stringifyError } from '../../shared/lib/utils'\nimport {\n  COMPILER_INDEXES,\n  COMPILER_NAMES,\n  RSC_MODULE_TYPES,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../../shared/lib/constants'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from './hot-reloader-types'\nimport { isAppPageRouteDefinition } from '../route-definitions/app-page-route-definition'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport { Batcher } from '../../lib/batcher'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { PAGE_TYPES } from '../../lib/page-types'\nimport { getNextFlightSegmentPath } from '../../client/flight-data-helpers'\n\nconst debug = createDebug('next:on-demand-entry-handler')\n\n/**\n * Returns object keys with type inferred from the object key\n */\nconst keys = Object.keys as <T>(o: T) => Extract<keyof T, string>[]\n\nconst COMPILER_KEYS = keys(COMPILER_INDEXES)\n\nfunction treePathToEntrypoint(\n  segmentPath: FlightSegmentPath,\n  parentPath?: string\n): string {\n  const [parallelRouteKey, segment] = segmentPath\n\n  // TODO-APP: modify this path to cover parallelRouteKey convention\n  const path =\n    (parentPath ? parentPath + '/' : '') +\n    (parallelRouteKey !== 'children' && !segment.startsWith('@')\n      ? `@${parallelRouteKey}/`\n      : '') +\n    (segment === '' ? 'page' : segment)\n\n  // Last segment\n  if (segmentPath.length === 2) {\n    return path\n  }\n\n  const childSegmentPath = getNextFlightSegmentPath(segmentPath)\n  return treePathToEntrypoint(childSegmentPath, path)\n}\n\nfunction convertDynamicParamTypeToSyntax(\n  dynamicParamTypeShort: DynamicParamTypesShort,\n  param: string\n) {\n  switch (dynamicParamTypeShort) {\n    case 'c':\n    case 'ci':\n      return `[...${param}]`\n    case 'oc':\n      return `[[...${param}]]`\n    case 'd':\n    case 'di':\n      return `[${param}]`\n    default:\n      throw new Error('Unknown dynamic param type')\n  }\n}\n\n/**\n * format: {compiler type}@{page type}@{page path}\n * e.g. client@pages@/index\n * e.g. server@app@app/page\n *\n * This guarantees the uniqueness for each page, to avoid conflicts between app/ and pages/\n */\n\nexport function getEntryKey(\n  compilerType: CompilerNameValues,\n  pageBundleType: PAGE_TYPES,\n  page: string\n) {\n  // TODO: handle the /children slot better\n  // this is a quick hack to handle when children is provided as children/page instead of /page\n  const pageKey = page.replace(/(@[^/]+)\\/children/g, '$1')\n  return `${compilerType}@${pageBundleType}@${pageKey}`\n}\n\nfunction getPageBundleType(pageBundlePath: string): PAGE_TYPES {\n  // Handle special case for /_error\n  if (pageBundlePath === '/_error') return PAGE_TYPES.PAGES\n  if (isMiddlewareFilename(pageBundlePath)) return PAGE_TYPES.ROOT\n  return pageBundlePath.startsWith('pages/')\n    ? PAGE_TYPES.PAGES\n    : pageBundlePath.startsWith('app/')\n      ? PAGE_TYPES.APP\n      : PAGE_TYPES.ROOT\n}\n\nfunction getEntrypointsFromTree(\n  tree: FlightRouterState,\n  isFirst: boolean,\n  parentPath: string[] = []\n) {\n  const [segment, parallelRoutes] = tree\n\n  const currentSegment = Array.isArray(segment)\n    ? convertDynamicParamTypeToSyntax(segment[2], segment[0])\n    : segment\n\n  const isPageSegment = currentSegment.startsWith(PAGE_SEGMENT_KEY)\n\n  const currentPath = [...parentPath, isPageSegment ? '' : currentSegment]\n\n  if (!isFirst && isPageSegment) {\n    // TODO get rid of '' at the start of tree\n    return [treePathToEntrypoint(currentPath.slice(1))]\n  }\n\n  return Object.keys(parallelRoutes).reduce(\n    (paths: string[], key: string): string[] => {\n      const childTree = parallelRoutes[key]\n      const childPages = getEntrypointsFromTree(childTree, false, [\n        ...currentPath,\n        key,\n      ])\n      return [...paths, ...childPages]\n    },\n    []\n  )\n}\n\nexport const ADDED = Symbol('added')\nexport const BUILDING = Symbol('building')\nexport const BUILT = Symbol('built')\n\ninterface EntryType {\n  /**\n   * Tells if a page is scheduled to be disposed.\n   */\n  dispose?: boolean\n  /**\n   * Timestamp with the last time the page was active.\n   */\n  lastActiveTime?: number\n  /**\n   * Page build status.\n   */\n  status?: typeof ADDED | typeof BUILDING | typeof BUILT\n\n  /**\n   * Path to the page file relative to the dist folder with no extension.\n   * For example: `pages/about/index`\n   */\n  bundlePath: string\n\n  /**\n   * Webpack request to create a dependency for.\n   */\n  request: string\n}\n\n// Shadowing check in ESLint does not account for enum\n// eslint-disable-next-line no-shadow\nexport const enum EntryTypes {\n  ENTRY,\n  CHILD_ENTRY,\n}\ninterface Entry extends EntryType {\n  type: EntryTypes.ENTRY\n  /**\n   * The absolute page to the page file. Used for detecting if the file was removed. For example:\n   * `/Users/<USER>/project/pages/about/index.js`\n   */\n  absolutePagePath: string\n  /**\n   * All parallel pages that match the same entry, for example:\n   * ['/parallel/@bar/nested/@a/page', '/parallel/@bar/nested/@b/page', '/parallel/@foo/nested/@a/page', '/parallel/@foo/nested/@b/page']\n   */\n  appPaths: ReadonlyArray<string> | null\n}\n\ninterface ChildEntry extends EntryType {\n  type: EntryTypes.CHILD_ENTRY\n  /**\n   * Which parent entries use this childEntry.\n   */\n  parentEntries: Set<string>\n  /**\n   * The absolute page to the entry file. Used for detecting if the file was removed. For example:\n   * `/Users/<USER>/project/app/about/layout.js`\n   */\n  absoluteEntryFilePath?: string\n}\n\nconst entriesMap: Map<\n  string,\n  {\n    /**\n     * The key composed of the compiler name and the page. For example:\n     * `edge-server/about`\n     */\n    [entryName: string]: Entry | ChildEntry\n  }\n> = new Map()\n\n// remove /server from end of output for server compiler\nconst normalizeOutputPath = (dir: string) => dir.replace(/[/\\\\]server$/, '')\n\nexport const getEntries = (\n  dir: string\n): NonNullable<ReturnType<(typeof entriesMap)['get']>> => {\n  dir = normalizeOutputPath(dir)\n  const entries = entriesMap.get(dir) || {}\n  entriesMap.set(dir, entries)\n  return entries\n}\n\nconst invalidators: Map<string, Invalidator> = new Map()\n\nexport const getInvalidator = (dir: string) => {\n  dir = normalizeOutputPath(dir)\n  return invalidators.get(dir)\n}\n\nconst doneCallbacks: EventEmitter = new EventEmitter()\nconst lastClientAccessPages = ['']\nconst lastServerAccessPagesForAppDir = ['']\n\ntype BuildingTracker = Set<CompilerNameValues>\ntype RebuildTracker = Set<CompilerNameValues>\n\n// Make sure only one invalidation happens at a time\n// Otherwise, webpack hash gets changed and it'll force the client to reload.\nclass Invalidator {\n  private multiCompiler: webpack.MultiCompiler\n\n  private building: BuildingTracker = new Set()\n  private rebuildAgain: RebuildTracker = new Set()\n\n  constructor(multiCompiler: webpack.MultiCompiler) {\n    this.multiCompiler = multiCompiler\n  }\n\n  public shouldRebuildAll() {\n    return this.rebuildAgain.size > 0\n  }\n\n  invalidate(compilerKeys: typeof COMPILER_KEYS = COMPILER_KEYS): void {\n    for (const key of compilerKeys) {\n      // If there's a current build is processing, we won't abort it by invalidating.\n      // (If aborted, it'll cause a client side hard reload)\n      // But let it to invalidate just after the completion.\n      // So, it can re-build the queued pages at once.\n\n      if (this.building.has(key)) {\n        this.rebuildAgain.add(key)\n        continue\n      }\n\n      this.building.add(key)\n      this.multiCompiler.compilers[COMPILER_INDEXES[key]].watching?.invalidate()\n    }\n  }\n\n  public startBuilding(compilerKey: keyof typeof COMPILER_INDEXES) {\n    this.building.add(compilerKey)\n  }\n\n  public doneBuilding(compilerKeys: typeof COMPILER_KEYS = []) {\n    const rebuild: typeof COMPILER_KEYS = []\n    for (const key of compilerKeys) {\n      this.building.delete(key)\n\n      if (this.rebuildAgain.has(key)) {\n        rebuild.push(key)\n        this.rebuildAgain.delete(key)\n      }\n    }\n\n    if (rebuild.length > 0) {\n      this.invalidate(rebuild)\n    }\n  }\n\n  public willRebuild(compilerKey: keyof typeof COMPILER_INDEXES) {\n    return this.rebuildAgain.has(compilerKey)\n  }\n}\n\nfunction disposeInactiveEntries(\n  entries: NonNullable<ReturnType<(typeof entriesMap)['get']>>,\n  maxInactiveAge: number\n) {\n  Object.keys(entries).forEach((entryKey) => {\n    const entryData = entries[entryKey]\n    const { lastActiveTime, status, dispose, bundlePath } = entryData\n\n    // TODO-APP: implement disposing of CHILD_ENTRY\n    if (entryData.type === EntryTypes.CHILD_ENTRY) {\n      return\n    }\n\n    // For the root middleware and the instrumentation hook files,\n    // we don't dispose them periodically as it's needed for every request.\n    if (\n      isMiddlewareFilename(bundlePath) ||\n      isInstrumentationHookFilename(bundlePath)\n    ) {\n      return\n    }\n\n    if (dispose)\n      // Skip pages already scheduled for disposing\n      return\n\n    // This means this entry is currently building or just added\n    // We don't need to dispose those entries.\n    if (status !== BUILT) return\n\n    // We should not build the last accessed page even we didn't get any pings\n    // Sometimes, it's possible our XHR ping to wait before completing other requests.\n    // In that case, we should not dispose the current viewing page\n    if (\n      lastClientAccessPages.includes(entryKey) ||\n      lastServerAccessPagesForAppDir.includes(entryKey)\n    )\n      return\n\n    if (lastActiveTime && Date.now() - lastActiveTime > maxInactiveAge) {\n      entries[entryKey].dispose = true\n    }\n  })\n}\n\n// Normalize both app paths and page paths\nfunction tryToNormalizePagePath(page: string) {\n  try {\n    return normalizePagePath(page)\n  } catch (err) {\n    console.error(err)\n    throw new PageNotFoundError(page)\n  }\n}\n\ninterface PagePathData {\n  filename: string\n  bundlePath: string\n  page: string\n}\n\n/**\n * Attempts to find a page file path from the given pages absolute directory,\n * a page and allowed extensions. If the page can't be found it will throw an\n * error. It defaults the `/_error` page to Next.js internal error page.\n *\n * @param rootDir Absolute path to the project root.\n * @param page The page normalized (it will be denormalized).\n * @param extensions Array of page extensions.\n * @param pagesDir Absolute path to the pages folder with trailing `/pages`.\n * @param appDir Absolute path to the app folder with trailing `/app`.\n */\nexport async function findPagePathData(\n  rootDir: string,\n  page: string,\n  extensions: string[],\n  pagesDir?: string,\n  appDir?: string\n): Promise<PagePathData> {\n  const normalizedPagePath = tryToNormalizePagePath(page)\n  let pagePath: string | null = null\n\n  const isInstrumentation = isInstrumentationHookFile(normalizedPagePath)\n  if (isMiddlewareFile(normalizedPagePath) || isInstrumentation) {\n    pagePath = await findPageFile(\n      rootDir,\n      normalizedPagePath,\n      extensions,\n      false\n    )\n\n    if (!pagePath) {\n      throw new PageNotFoundError(normalizedPagePath)\n    }\n\n    const pageUrl = ensureLeadingSlash(\n      removePagePathTail(normalizePathSep(pagePath), {\n        extensions,\n      })\n    )\n\n    let bundlePath = normalizedPagePath\n    let pageKey = posix.normalize(pageUrl)\n\n    if (isInstrumentation || isMiddlewareFile(normalizedPagePath)) {\n      bundlePath = bundlePath.replace('/src', '')\n      pageKey = page.replace('/src', '')\n    }\n\n    return {\n      filename: join(rootDir, pagePath),\n      bundlePath: bundlePath.slice(1),\n      page: pageKey,\n    }\n  }\n\n  // Check appDir first falling back to pagesDir\n  if (appDir) {\n    if (page === UNDERSCORE_NOT_FOUND_ROUTE_ENTRY) {\n      const notFoundPath = await findPageFile(\n        appDir,\n        'not-found',\n        extensions,\n        true\n      )\n      if (notFoundPath) {\n        return {\n          filename: join(appDir, notFoundPath),\n          bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n          page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n        }\n      }\n\n      return {\n        filename: require.resolve(\n          'next/dist/client/components/not-found-error'\n        ),\n        bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n        page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n      }\n    }\n    pagePath = await findPageFile(appDir, normalizedPagePath, extensions, true)\n    if (pagePath) {\n      const pageUrl = ensureLeadingSlash(\n        removePagePathTail(normalizePathSep(pagePath), {\n          keepIndex: true,\n          extensions,\n        })\n      )\n\n      return {\n        filename: join(appDir, pagePath),\n        bundlePath: posix.join('app', pageUrl),\n        page: posix.normalize(pageUrl),\n      }\n    }\n  }\n\n  if (!pagePath && pagesDir) {\n    pagePath = await findPageFile(\n      pagesDir,\n      normalizedPagePath,\n      extensions,\n      false\n    )\n  }\n\n  if (pagePath !== null && pagesDir) {\n    const pageUrl = ensureLeadingSlash(\n      removePagePathTail(normalizePathSep(pagePath), {\n        extensions,\n      })\n    )\n\n    return {\n      filename: join(pagesDir, pagePath),\n      bundlePath: posix.join('pages', normalizePagePath(pageUrl)),\n      page: posix.normalize(pageUrl),\n    }\n  }\n\n  if (page === '/_error') {\n    return {\n      filename: require.resolve('next/dist/pages/_error'),\n      bundlePath: page,\n      page: normalizePathSep(page),\n    }\n  } else {\n    throw new PageNotFoundError(normalizedPagePath)\n  }\n}\n\nexport function onDemandEntryHandler({\n  hotReloader,\n  maxInactiveAge,\n  multiCompiler,\n  nextConfig,\n  pagesBufferLength,\n  pagesDir,\n  rootDir,\n  appDir,\n}: {\n  hotReloader: HotReloaderWebpack\n  maxInactiveAge: number\n  multiCompiler: webpack.MultiCompiler\n  nextConfig: NextConfigComplete\n  pagesBufferLength: number\n  pagesDir?: string\n  rootDir: string\n  appDir?: string\n}) {\n  const hasAppDir = !!appDir\n  let curInvalidator: Invalidator = getInvalidator(\n    multiCompiler.outputPath\n  ) as any\n  const curEntries = getEntries(multiCompiler.outputPath) as any\n\n  if (!curInvalidator) {\n    curInvalidator = new Invalidator(multiCompiler)\n    invalidators.set(multiCompiler.outputPath, curInvalidator)\n  }\n\n  const startBuilding = (compilation: webpack.Compilation) => {\n    const compilationName = compilation.name as any as CompilerNameValues\n    curInvalidator.startBuilding(compilationName)\n  }\n  for (const compiler of multiCompiler.compilers) {\n    compiler.hooks.make.tap('NextJsOnDemandEntries', startBuilding)\n  }\n\n  function getPagePathsFromEntrypoints(\n    type: CompilerNameValues,\n    entrypoints: Map<string, { name?: string | null }>\n  ) {\n    const pagePaths: string[] = []\n    for (const entrypoint of entrypoints.values()) {\n      const page = getRouteFromEntrypoint(entrypoint.name!, hasAppDir)\n\n      if (page) {\n        const pageBundleType = entrypoint.name?.startsWith('app/')\n          ? PAGE_TYPES.APP\n          : PAGE_TYPES.PAGES\n        pagePaths.push(getEntryKey(type, pageBundleType, page))\n      } else if (\n        isMiddlewareFilename(entrypoint.name) ||\n        isInstrumentationHookFilename(entrypoint.name)\n      ) {\n        pagePaths.push(\n          getEntryKey(type, PAGE_TYPES.ROOT, `/${entrypoint.name}`)\n        )\n      }\n    }\n    return pagePaths\n  }\n\n  for (const compiler of multiCompiler.compilers) {\n    compiler.hooks.done.tap('NextJsOnDemandEntries', () =>\n      getInvalidator(compiler.outputPath)?.doneBuilding([\n        compiler.name as keyof typeof COMPILER_INDEXES,\n      ])\n    )\n  }\n\n  multiCompiler.hooks.done.tap('NextJsOnDemandEntries', (multiStats) => {\n    const [clientStats, serverStats, edgeServerStats] = multiStats.stats\n    const entryNames = [\n      ...getPagePathsFromEntrypoints(\n        COMPILER_NAMES.client,\n        clientStats.compilation.entrypoints\n      ),\n      ...getPagePathsFromEntrypoints(\n        COMPILER_NAMES.server,\n        serverStats.compilation.entrypoints\n      ),\n      ...(edgeServerStats\n        ? getPagePathsFromEntrypoints(\n            COMPILER_NAMES.edgeServer,\n            edgeServerStats.compilation.entrypoints\n          )\n        : []),\n    ]\n\n    for (const name of entryNames) {\n      const entry = curEntries[name]\n      if (!entry) {\n        continue\n      }\n\n      if (entry.status !== BUILDING) {\n        continue\n      }\n\n      entry.status = BUILT\n      doneCallbacks.emit(name)\n    }\n\n    getInvalidator(multiCompiler.outputPath)?.doneBuilding([...COMPILER_KEYS])\n  })\n\n  const pingIntervalTime = Math.max(1000, Math.min(5000, maxInactiveAge))\n\n  setInterval(function () {\n    disposeInactiveEntries(curEntries, maxInactiveAge)\n  }, pingIntervalTime + 1000).unref()\n\n  function handleAppDirPing(tree: FlightRouterState): void {\n    const pages = getEntrypointsFromTree(tree, true)\n\n    for (const page of pages) {\n      for (const compilerType of [\n        COMPILER_NAMES.client,\n        COMPILER_NAMES.server,\n        COMPILER_NAMES.edgeServer,\n      ]) {\n        const entryKey = getEntryKey(compilerType, PAGE_TYPES.APP, `/${page}`)\n        const entryInfo = curEntries[entryKey]\n\n        // If there's no entry, it may have been invalidated and needs to be re-built.\n        if (!entryInfo) {\n          // if (page !== lastEntry) client pings, but there's no entry for page\n          continue\n        }\n\n        // We don't need to maintain active state of anything other than BUILT entries\n        if (entryInfo.status !== BUILT) continue\n\n        // If there's an entryInfo\n        if (!lastServerAccessPagesForAppDir.includes(entryKey)) {\n          lastServerAccessPagesForAppDir.unshift(entryKey)\n\n          // Maintain the buffer max length\n          // TODO: verify that the current pageKey is not at the end of the array as multiple entrypoints can exist\n          if (lastServerAccessPagesForAppDir.length > pagesBufferLength) {\n            lastServerAccessPagesForAppDir.pop()\n          }\n        }\n        entryInfo.lastActiveTime = Date.now()\n        entryInfo.dispose = false\n      }\n    }\n  }\n\n  function handlePing(pg: string): void {\n    const page = normalizePathSep(pg)\n    for (const compilerType of [\n      COMPILER_NAMES.client,\n      COMPILER_NAMES.server,\n      COMPILER_NAMES.edgeServer,\n    ]) {\n      const entryKey = getEntryKey(compilerType, PAGE_TYPES.PAGES, page)\n      const entryInfo = curEntries[entryKey]\n\n      // If there's no entry, it may have been invalidated and needs to be re-built.\n      if (!entryInfo) {\n        // if (page !== lastEntry) client pings, but there's no entry for page\n        if (compilerType === COMPILER_NAMES.client) {\n          return\n        }\n        continue\n      }\n\n      // We don't need to maintain active state of anything other than BUILT entries\n      if (entryInfo.status !== BUILT) continue\n\n      // If there's an entryInfo\n      if (!lastClientAccessPages.includes(entryKey)) {\n        lastClientAccessPages.unshift(entryKey)\n\n        // Maintain the buffer max length\n        if (lastClientAccessPages.length > pagesBufferLength) {\n          lastClientAccessPages.pop()\n        }\n      }\n      entryInfo.lastActiveTime = Date.now()\n      entryInfo.dispose = false\n    }\n    return\n  }\n\n  async function ensurePageImpl({\n    page,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    appPaths: ReadonlyArray<string> | null\n    definition: RouteDefinition | undefined\n    isApp: boolean | undefined\n    url?: string\n  }): Promise<void> {\n    const stalledTime = 60\n    const stalledEnsureTimeout = setTimeout(() => {\n      debug(\n        `Ensuring ${page} has taken longer than ${stalledTime}s, if this continues to stall this may be a bug`\n      )\n    }, stalledTime * 1000)\n\n    try {\n      let route: Pick<RouteDefinition, 'filename' | 'bundlePath' | 'page'>\n      if (definition) {\n        route = definition\n      } else {\n        route = await findPagePathData(\n          rootDir,\n          page,\n          nextConfig.pageExtensions,\n          pagesDir,\n          appDir\n        )\n      }\n\n      const isInsideAppDir = !!appDir && route.filename.startsWith(appDir)\n\n      if (typeof isApp === 'boolean' && isApp !== isInsideAppDir) {\n        Error.stackTraceLimit = 15\n        throw new Error(\n          `Ensure bailed, found path \"${\n            route.page\n          }\" does not match ensure type (${isApp ? 'app' : 'pages'})`\n        )\n      }\n\n      const pageBundleType = getPageBundleType(route.bundlePath)\n      const addEntry = (\n        compilerType: CompilerNameValues\n      ): {\n        entryKey: string\n        newEntry: boolean\n        shouldInvalidate: boolean\n      } => {\n        const entryKey = getEntryKey(compilerType, pageBundleType, route.page)\n        if (\n          curEntries[entryKey] &&\n          // there can be an overlap in the entryKey for the instrumentation hook file and a page named the same\n          // this is a quick fix to support this scenario by overwriting the instrumentation hook entry, since we only use it one time\n          // any changes to the instrumentation hook file will require a restart of the dev server anyway\n          !isInstrumentationHookFilename(curEntries[entryKey].bundlePath)\n        ) {\n          curEntries[entryKey].dispose = false\n          curEntries[entryKey].lastActiveTime = Date.now()\n          if (curEntries[entryKey].status === BUILT) {\n            return {\n              entryKey,\n              newEntry: false,\n              shouldInvalidate: false,\n            }\n          }\n\n          return {\n            entryKey,\n            newEntry: false,\n            shouldInvalidate: true,\n          }\n        }\n\n        curEntries[entryKey] = {\n          type: EntryTypes.ENTRY,\n          appPaths,\n          absolutePagePath: route.filename,\n          request: route.filename,\n          bundlePath: route.bundlePath,\n          dispose: false,\n          lastActiveTime: Date.now(),\n          status: ADDED,\n        }\n        return {\n          entryKey: entryKey,\n          newEntry: true,\n          shouldInvalidate: true,\n        }\n      }\n\n      const staticInfo = await getStaticInfoIncludingLayouts({\n        page,\n        pageFilePath: route.filename,\n        isInsideAppDir,\n        pageExtensions: nextConfig.pageExtensions,\n        isDev: true,\n        config: nextConfig,\n        appDir,\n      })\n\n      const added = new Map<CompilerNameValues, ReturnType<typeof addEntry>>()\n      const isServerComponent =\n        isInsideAppDir && staticInfo.rsc !== RSC_MODULE_TYPES.client\n\n      let pageRuntime = staticInfo.runtime\n\n      if (isMiddlewareFile(page) && !nextConfig.experimental.nodeMiddleware) {\n        pageRuntime = 'edge'\n      }\n\n      runDependingOnPageType({\n        page: route.page,\n        pageRuntime,\n        pageType: pageBundleType,\n        onClient: () => {\n          // Skip adding the client entry for app / Server Components.\n          if (isServerComponent || isInsideAppDir) {\n            return\n          }\n          added.set(COMPILER_NAMES.client, addEntry(COMPILER_NAMES.client))\n        },\n        onServer: () => {\n          added.set(COMPILER_NAMES.server, addEntry(COMPILER_NAMES.server))\n          const edgeServerEntry = getEntryKey(\n            COMPILER_NAMES.edgeServer,\n            pageBundleType,\n            route.page\n          )\n          if (\n            curEntries[edgeServerEntry] &&\n            !isInstrumentationHookFile(route.page)\n          ) {\n            // Runtime switched from edge to server\n            delete curEntries[edgeServerEntry]\n          }\n        },\n        onEdgeServer: () => {\n          added.set(\n            COMPILER_NAMES.edgeServer,\n            addEntry(COMPILER_NAMES.edgeServer)\n          )\n          const serverEntry = getEntryKey(\n            COMPILER_NAMES.server,\n            pageBundleType,\n            route.page\n          )\n          if (\n            curEntries[serverEntry] &&\n            !isInstrumentationHookFile(route.page)\n          ) {\n            // Runtime switched from server to edge\n            delete curEntries[serverEntry]\n          }\n        },\n      })\n\n      const addedValues = [...added.values()]\n      const entriesThatShouldBeInvalidated = [...added.entries()].filter(\n        ([, entry]) => entry.shouldInvalidate\n      )\n      const hasNewEntry = addedValues.some((entry) => entry.newEntry)\n\n      if (hasNewEntry) {\n        const routePage = isApp ? route.page : normalizeAppPath(route.page)\n        reportTrigger(routePage, url)\n      }\n\n      if (entriesThatShouldBeInvalidated.length > 0) {\n        const invalidatePromise = Promise.all(\n          entriesThatShouldBeInvalidated.map(([compilerKey, { entryKey }]) => {\n            return new Promise<void>((resolve, reject) => {\n              doneCallbacks.once(entryKey, (err: Error) => {\n                if (err) {\n                  return reject(err)\n                }\n\n                // If the invalidation also triggers a rebuild, we need to\n                // wait for that additional build to prevent race conditions.\n                const needsRebuild = curInvalidator.willRebuild(compilerKey)\n                if (needsRebuild) {\n                  doneCallbacks.once(entryKey, (rebuildErr: Error) => {\n                    if (rebuildErr) {\n                      return reject(rebuildErr)\n                    }\n                    resolve()\n                  })\n                } else {\n                  resolve()\n                }\n              })\n            })\n          })\n        )\n\n        curInvalidator.invalidate([...added.keys()])\n        await invalidatePromise\n      }\n    } finally {\n      clearTimeout(stalledEnsureTimeout)\n    }\n  }\n\n  type EnsurePageOptions = {\n    page: string\n    appPaths?: ReadonlyArray<string> | null\n    definition?: RouteDefinition\n    isApp?: boolean\n    url?: string\n  }\n\n  // Make sure that we won't have multiple invalidations ongoing concurrently.\n  const batcher = Batcher.create<EnsurePageOptions, void, string>({\n    // The cache key here is composed of the elements that affect the\n    // compilation, namely, the page, whether it's client only, and whether\n    // it's an app page. This ensures that we don't have multiple compilations\n    // for the same page happening concurrently.\n    //\n    // We don't include the whole match because it contains match specific\n    // parameters (like route params) that would just bust this cache. Any\n    // details that would possibly bust the cache should be listed here.\n    cacheKeyFn: (options) => JSON.stringify(options),\n    // Schedule the invocation of the ensurePageImpl function on the next tick.\n    schedulerFn: scheduleOnNextTick,\n  })\n\n  return {\n    async ensurePage({\n      page,\n      appPaths = null,\n      definition,\n      isApp,\n      url,\n    }: EnsurePageOptions) {\n      // If the route is actually an app page route, then we should have access\n      // to the app route definition, and therefore, the appPaths from it.\n      if (!appPaths && definition && isAppPageRouteDefinition(definition)) {\n        appPaths = definition.appPaths\n      }\n\n      // Wrap the invocation of the ensurePageImpl function in the pending\n      // wrapper, which will ensure that we don't have multiple compilations\n      // for the same page happening concurrently.\n      return batcher.batch({ page, appPaths, definition, isApp }, async () => {\n        await ensurePageImpl({\n          page,\n          appPaths,\n          definition,\n          isApp,\n          url,\n        })\n      })\n    },\n    onHMR(client: ws, getHmrServerError: () => Error | null) {\n      let bufferedHmrServerError: Error | null = null\n\n      client.addEventListener('close', () => {\n        bufferedHmrServerError = null\n      })\n      client.addEventListener('message', ({ data }) => {\n        try {\n          const error = getHmrServerError()\n\n          // New error occurred: buffered error is flushed and new error occurred\n          if (!bufferedHmrServerError && error) {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR,\n              errorJSON: stringifyError(error),\n            })\n            bufferedHmrServerError = null\n          }\n\n          const parsedData = JSON.parse(\n            typeof data !== 'string' ? data.toString() : data\n          )\n\n          if (parsedData.event === 'ping') {\n            if (parsedData.appDirRoute) {\n              handleAppDirPing(parsedData.tree)\n            } else {\n              handlePing(parsedData.page)\n            }\n          }\n        } catch {}\n      })\n    },\n  }\n}\n"], "names": ["ADDED", "BUILDING", "BUILT", "EntryTypes", "findPagePathData", "getEntries", "getEntry<PERSON>ey", "getInvalidator", "onDemandEntryHandler", "debug", "createDebug", "keys", "Object", "COMPILER_KEYS", "COMPILER_INDEXES", "treePathToEntrypoint", "segmentPath", "parentPath", "parallelRouteKey", "segment", "path", "startsWith", "length", "childSegment<PERSON>ath", "getNextFlightSegmentPath", "convertDynamicParamTypeToSyntax", "dynamicParamTypeShort", "param", "Error", "compilerType", "pageBundleType", "page", "page<PERSON><PERSON>", "replace", "getPageBundleType", "pageBundlePath", "PAGE_TYPES", "PAGES", "isMiddlewareFilename", "ROOT", "APP", "getEntrypointsFromTree", "tree", "<PERSON><PERSON><PERSON><PERSON>", "parallelRoutes", "currentSegment", "Array", "isArray", "isPageSegment", "PAGE_SEGMENT_KEY", "currentPath", "slice", "reduce", "paths", "key", "childTree", "childPages", "Symbol", "entriesMap", "Map", "normalizeOutputPath", "dir", "entries", "get", "set", "invalidators", "doneCallbacks", "EventEmitter", "lastClientAccessPages", "lastServerAccessPagesForAppDir", "Invalidator", "constructor", "multiCompiler", "building", "Set", "rebuildAgain", "shouldRebuildAll", "size", "invalidate", "compilerKeys", "has", "add", "compilers", "watching", "startBuilding", "<PERSON><PERSON><PERSON>", "doneBuilding", "rebuild", "delete", "push", "willRebuild", "disposeInactiveEntries", "maxInactiveAge", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "entryData", "lastActiveTime", "status", "dispose", "bundlePath", "type", "isInstrumentationHookFilename", "includes", "Date", "now", "tryToNormalizePagePath", "normalizePagePath", "err", "console", "error", "PageNotFoundError", "rootDir", "extensions", "pagesDir", "appDir", "normalizedPagePath", "pagePath", "isInstrumentation", "isInstrumentationHookFile", "isMiddlewareFile", "findPageFile", "pageUrl", "ensureLeadingSlash", "removePagePathTail", "normalizePathSep", "posix", "normalize", "filename", "join", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "notFoundPath", "require", "resolve", "keepIndex", "hotReloader", "nextConfig", "pagesBufferLength", "hasAppDir", "curInvalidator", "outputPath", "curEntries", "compilation", "compilationName", "name", "compiler", "hooks", "make", "tap", "getPagePathsFromEntrypoints", "entrypoints", "pagePaths", "entrypoint", "values", "getRouteFromEntrypoint", "done", "multiStats", "clientStats", "serverStats", "edgeServerStats", "stats", "entryNames", "COMPILER_NAMES", "client", "server", "edgeServer", "entry", "emit", "pingIntervalTime", "Math", "max", "min", "setInterval", "unref", "handleAppDirPing", "pages", "entryInfo", "unshift", "pop", "handlePing", "pg", "ensurePageImpl", "appPaths", "definition", "isApp", "url", "stalledTime", "stalledEnsureTimeout", "setTimeout", "route", "pageExtensions", "isInsideAppDir", "stackTraceLimit", "addEntry", "newEntry", "shouldInvalidate", "absolutePagePath", "request", "staticInfo", "getStaticInfoIncludingLayouts", "pageFilePath", "isDev", "config", "added", "isServerComponent", "rsc", "RSC_MODULE_TYPES", "pageRuntime", "runtime", "experimental", "nodeMiddleware", "runDependingOnPageType", "pageType", "onClient", "onServer", "edgeServerEntry", "onEdgeServer", "serverEntry", "addedV<PERSON>ues", "entriesThatShouldBeInvalidated", "filter", "hasNewEntry", "some", "routePage", "normalizeAppPath", "reportTrigger", "invalidate<PERSON><PERSON><PERSON>", "Promise", "all", "map", "reject", "once", "needsRebuild", "rebuildErr", "clearTimeout", "batcher", "<PERSON><PERSON>", "create", "cacheKeyFn", "options", "JSON", "stringify", "schedulerFn", "scheduleOnNextTick", "ensurePage", "isAppPageRouteDefinition", "batch", "onHMR", "getHmrServerError", "bufferedHmrServerError", "addEventListener", "data", "send", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "SERVER_ERROR", "errorJSON", "stringifyError", "parsedData", "parse", "toString", "event", "appDirRoute"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAiKaA,KAAK;eAALA;;IACAC,QAAQ;eAARA;;IACAC,KAAK;eAALA;;IA8BKC,UAAU;eAAVA;;IAsMIC,gBAAgB;eAAhBA;;IAzJTC,UAAU;eAAVA;;IApIGC,WAAW;eAAXA;;IA+IHC,cAAc;eAAdA;;IAsQGC,oBAAoB;eAApBA;;;8DAnfQ;wBACK;8BACA;yBAItB;sBACqB;kCACK;mCACC;oCACC;oCACA;wBACL;+EACK;uBAM5B;wBAC2C;2BAM3C;yBAC0B;kCACW;wCACH;2BACN;yBACX;0BACS;2BACN;mCACc;;;;;;AAEzC,MAAMC,QAAQC,IAAAA,cAAW,EAAC;AAE1B;;CAEC,GACD,MAAMC,OAAOC,OAAOD,IAAI;AAExB,MAAME,gBAAgBF,KAAKG,2BAAgB;AAE3C,SAASC,qBACPC,WAA8B,EAC9BC,UAAmB;IAEnB,MAAM,CAACC,kBAAkBC,QAAQ,GAAGH;IAEpC,kEAAkE;IAClE,MAAMI,OACJ,AAACH,CAAAA,aAAaA,aAAa,MAAM,EAAC,IACjCC,CAAAA,qBAAqB,cAAc,CAACC,QAAQE,UAAU,CAAC,OACpD,CAAC,CAAC,EAAEH,iBAAiB,CAAC,CAAC,GACvB,EAAC,IACJC,CAAAA,YAAY,KAAK,SAASA,OAAM;IAEnC,eAAe;IACf,IAAIH,YAAYM,MAAM,KAAK,GAAG;QAC5B,OAAOF;IACT;IAEA,MAAMG,mBAAmBC,IAAAA,2CAAwB,EAACR;IAClD,OAAOD,qBAAqBQ,kBAAkBH;AAChD;AAEA,SAASK,gCACPC,qBAA6C,EAC7CC,KAAa;IAEb,OAAQD;QACN,KAAK;QACL,KAAK;YACH,OAAO,CAAC,IAAI,EAAEC,MAAM,CAAC,CAAC;QACxB,KAAK;YACH,OAAO,CAAC,KAAK,EAAEA,MAAM,EAAE,CAAC;QAC1B,KAAK;QACL,KAAK;YACH,OAAO,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC;QACrB;YACE,MAAM,qBAAuC,CAAvC,IAAIC,MAAM,+BAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAsC;IAChD;AACF;AAUO,SAAStB,YACduB,YAAgC,EAChCC,cAA0B,EAC1BC,IAAY;IAEZ,yCAAyC;IACzC,6FAA6F;IAC7F,MAAMC,UAAUD,KAAKE,OAAO,CAAC,uBAAuB;IACpD,OAAO,GAAGJ,aAAa,CAAC,EAAEC,eAAe,CAAC,EAAEE,SAAS;AACvD;AAEA,SAASE,kBAAkBC,cAAsB;IAC/C,kCAAkC;IAClC,IAAIA,mBAAmB,WAAW,OAAOC,qBAAU,CAACC,KAAK;IACzD,IAAIC,IAAAA,2BAAoB,EAACH,iBAAiB,OAAOC,qBAAU,CAACG,IAAI;IAChE,OAAOJ,eAAed,UAAU,CAAC,YAC7Be,qBAAU,CAACC,KAAK,GAChBF,eAAed,UAAU,CAAC,UACxBe,qBAAU,CAACI,GAAG,GACdJ,qBAAU,CAACG,IAAI;AACvB;AAEA,SAASE,uBACPC,IAAuB,EACvBC,OAAgB,EAChB1B,aAAuB,EAAE;IAEzB,MAAM,CAACE,SAASyB,eAAe,GAAGF;IAElC,MAAMG,iBAAiBC,MAAMC,OAAO,CAAC5B,WACjCM,gCAAgCN,OAAO,CAAC,EAAE,EAAEA,OAAO,CAAC,EAAE,IACtDA;IAEJ,MAAM6B,gBAAgBH,eAAexB,UAAU,CAAC4B,yBAAgB;IAEhE,MAAMC,cAAc;WAAIjC;QAAY+B,gBAAgB,KAAKH;KAAe;IAExE,IAAI,CAACF,WAAWK,eAAe;QAC7B,0CAA0C;QAC1C,OAAO;YAACjC,qBAAqBmC,YAAYC,KAAK,CAAC;SAAI;IACrD;IAEA,OAAOvC,OAAOD,IAAI,CAACiC,gBAAgBQ,MAAM,CACvC,CAACC,OAAiBC;QAChB,MAAMC,YAAYX,cAAc,CAACU,IAAI;QACrC,MAAME,aAAaf,uBAAuBc,WAAW,OAAO;eACvDL;YACHI;SACD;QACD,OAAO;eAAID;eAAUG;SAAW;IAClC,GACA,EAAE;AAEN;AAEO,MAAMxD,QAAQyD,OAAO;AACrB,MAAMxD,WAAWwD,OAAO;AACxB,MAAMvD,QAAQuD,OAAO;AA8BrB,IAAA,AAAWtD,oCAAAA;;;WAAAA;;AA+BlB,MAAMuD,aASF,IAAIC;AAER,wDAAwD;AACxD,MAAMC,sBAAsB,CAACC,MAAgBA,IAAI5B,OAAO,CAAC,gBAAgB;AAElE,MAAM5B,aAAa,CACxBwD;IAEAA,MAAMD,oBAAoBC;IAC1B,MAAMC,UAAUJ,WAAWK,GAAG,CAACF,QAAQ,CAAC;IACxCH,WAAWM,GAAG,CAACH,KAAKC;IACpB,OAAOA;AACT;AAEA,MAAMG,eAAyC,IAAIN;AAE5C,MAAMpD,iBAAiB,CAACsD;IAC7BA,MAAMD,oBAAoBC;IAC1B,OAAOI,aAAaF,GAAG,CAACF;AAC1B;AAEA,MAAMK,gBAA8B,IAAIC,oBAAY;AACpD,MAAMC,wBAAwB;IAAC;CAAG;AAClC,MAAMC,iCAAiC;IAAC;CAAG;AAK3C,oDAAoD;AACpD,6EAA6E;AAC7E,MAAMC;IAMJC,YAAYC,aAAoC,CAAE;aAH1CC,WAA4B,IAAIC;aAChCC,eAA+B,IAAID;QAGzC,IAAI,CAACF,aAAa,GAAGA;IACvB;IAEOI,mBAAmB;QACxB,OAAO,IAAI,CAACD,YAAY,CAACE,IAAI,GAAG;IAClC;IAEAC,WAAWC,eAAqClE,aAAa,EAAQ;QACnE,KAAK,MAAMyC,OAAOyB,aAAc;gBAY9B;YAXA,+EAA+E;YAC/E,sDAAsD;YACtD,sDAAsD;YACtD,gDAAgD;YAEhD,IAAI,IAAI,CAACN,QAAQ,CAACO,GAAG,CAAC1B,MAAM;gBAC1B,IAAI,CAACqB,YAAY,CAACM,GAAG,CAAC3B;gBACtB;YACF;YAEA,IAAI,CAACmB,QAAQ,CAACQ,GAAG,CAAC3B;aAClB,8DAAA,IAAI,CAACkB,aAAa,CAACU,SAAS,CAACpE,2BAAgB,CAACwC,IAAI,CAAC,CAAC6B,QAAQ,qBAA5D,4DAA8DL,UAAU;QAC1E;IACF;IAEOM,cAAcC,WAA0C,EAAE;QAC/D,IAAI,CAACZ,QAAQ,CAACQ,GAAG,CAACI;IACpB;IAEOC,aAAaP,eAAqC,EAAE,EAAE;QAC3D,MAAMQ,UAAgC,EAAE;QACxC,KAAK,MAAMjC,OAAOyB,aAAc;YAC9B,IAAI,CAACN,QAAQ,CAACe,MAAM,CAAClC;YAErB,IAAI,IAAI,CAACqB,YAAY,CAACK,GAAG,CAAC1B,MAAM;gBAC9BiC,QAAQE,IAAI,CAACnC;gBACb,IAAI,CAACqB,YAAY,CAACa,MAAM,CAAClC;YAC3B;QACF;QAEA,IAAIiC,QAAQjE,MAAM,GAAG,GAAG;YACtB,IAAI,CAACwD,UAAU,CAACS;QAClB;IACF;IAEOG,YAAYL,WAA0C,EAAE;QAC7D,OAAO,IAAI,CAACV,YAAY,CAACK,GAAG,CAACK;IAC/B;AACF;AAEA,SAASM,uBACP7B,OAA4D,EAC5D8B,cAAsB;IAEtBhF,OAAOD,IAAI,CAACmD,SAAS+B,OAAO,CAAC,CAACC;QAC5B,MAAMC,YAAYjC,OAAO,CAACgC,SAAS;QACnC,MAAM,EAAEE,cAAc,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAE,GAAGJ;QAExD,+CAA+C;QAC/C,IAAIA,UAAUK,IAAI,QAA6B;YAC7C;QACF;QAEA,8DAA8D;QAC9D,uEAAuE;QACvE,IACE9D,IAAAA,2BAAoB,EAAC6D,eACrBE,IAAAA,oCAA6B,EAACF,aAC9B;YACA;QACF;QAEA,IAAID,SACF,6CAA6C;QAC7C;QAEF,4DAA4D;QAC5D,0CAA0C;QAC1C,IAAID,WAAW/F,OAAO;QAEtB,0EAA0E;QAC1E,kFAAkF;QAClF,+DAA+D;QAC/D,IACEkE,sBAAsBkC,QAAQ,CAACR,aAC/BzB,+BAA+BiC,QAAQ,CAACR,WAExC;QAEF,IAAIE,kBAAkBO,KAAKC,GAAG,KAAKR,iBAAiBJ,gBAAgB;YAClE9B,OAAO,CAACgC,SAAS,CAACI,OAAO,GAAG;QAC9B;IACF;AACF;AAEA,0CAA0C;AAC1C,SAASO,uBAAuB1E,IAAY;IAC1C,IAAI;QACF,OAAO2E,IAAAA,oCAAiB,EAAC3E;IAC3B,EAAE,OAAO4E,KAAK;QACZC,QAAQC,KAAK,CAACF;QACd,MAAM,IAAIG,yBAAiB,CAAC/E;IAC9B;AACF;AAmBO,eAAe3B,iBACpB2G,OAAe,EACfhF,IAAY,EACZiF,UAAoB,EACpBC,QAAiB,EACjBC,MAAe;IAEf,MAAMC,qBAAqBV,uBAAuB1E;IAClD,IAAIqF,WAA0B;IAE9B,MAAMC,oBAAoBC,IAAAA,gCAAyB,EAACH;IACpD,IAAII,IAAAA,uBAAgB,EAACJ,uBAAuBE,mBAAmB;QAC7DD,WAAW,MAAMI,IAAAA,0BAAY,EAC3BT,SACAI,oBACAH,YACA;QAGF,IAAI,CAACI,UAAU;YACb,MAAM,IAAIN,yBAAiB,CAACK;QAC9B;QAEA,MAAMM,UAAUC,IAAAA,sCAAkB,EAChCC,IAAAA,sCAAkB,EAACC,IAAAA,kCAAgB,EAACR,WAAW;YAC7CJ;QACF;QAGF,IAAIb,aAAagB;QACjB,IAAInF,UAAU6F,WAAK,CAACC,SAAS,CAACL;QAE9B,IAAIJ,qBAAqBE,IAAAA,uBAAgB,EAACJ,qBAAqB;YAC7DhB,aAAaA,WAAWlE,OAAO,CAAC,QAAQ;YACxCD,UAAUD,KAAKE,OAAO,CAAC,QAAQ;QACjC;QAEA,OAAO;YACL8F,UAAUC,IAAAA,UAAI,EAACjB,SAASK;YACxBjB,YAAYA,WAAWhD,KAAK,CAAC;YAC7BpB,MAAMC;QACR;IACF;IAEA,8CAA8C;IAC9C,IAAIkF,QAAQ;QACV,IAAInF,SAASkG,2CAAgC,EAAE;YAC7C,MAAMC,eAAe,MAAMV,IAAAA,0BAAY,EACrCN,QACA,aACAF,YACA;YAEF,IAAIkB,cAAc;gBAChB,OAAO;oBACLH,UAAUC,IAAAA,UAAI,EAACd,QAAQgB;oBACvB/B,YAAY,CAAC,GAAG,EAAE8B,2CAAgC,EAAE;oBACpDlG,MAAMkG,2CAAgC;gBACxC;YACF;YAEA,OAAO;gBACLF,UAAUI,QAAQC,OAAO,CACvB;gBAEFjC,YAAY,CAAC,GAAG,EAAE8B,2CAAgC,EAAE;gBACpDlG,MAAMkG,2CAAgC;YACxC;QACF;QACAb,WAAW,MAAMI,IAAAA,0BAAY,EAACN,QAAQC,oBAAoBH,YAAY;QACtE,IAAII,UAAU;YACZ,MAAMK,UAAUC,IAAAA,sCAAkB,EAChCC,IAAAA,sCAAkB,EAACC,IAAAA,kCAAgB,EAACR,WAAW;gBAC7CiB,WAAW;gBACXrB;YACF;YAGF,OAAO;gBACLe,UAAUC,IAAAA,UAAI,EAACd,QAAQE;gBACvBjB,YAAY0B,WAAK,CAACG,IAAI,CAAC,OAAOP;gBAC9B1F,MAAM8F,WAAK,CAACC,SAAS,CAACL;YACxB;QACF;IACF;IAEA,IAAI,CAACL,YAAYH,UAAU;QACzBG,WAAW,MAAMI,IAAAA,0BAAY,EAC3BP,UACAE,oBACAH,YACA;IAEJ;IAEA,IAAII,aAAa,QAAQH,UAAU;QACjC,MAAMQ,UAAUC,IAAAA,sCAAkB,EAChCC,IAAAA,sCAAkB,EAACC,IAAAA,kCAAgB,EAACR,WAAW;YAC7CJ;QACF;QAGF,OAAO;YACLe,UAAUC,IAAAA,UAAI,EAACf,UAAUG;YACzBjB,YAAY0B,WAAK,CAACG,IAAI,CAAC,SAAStB,IAAAA,oCAAiB,EAACe;YAClD1F,MAAM8F,WAAK,CAACC,SAAS,CAACL;QACxB;IACF;IAEA,IAAI1F,SAAS,WAAW;QACtB,OAAO;YACLgG,UAAUI,QAAQC,OAAO,CAAC;YAC1BjC,YAAYpE;YACZA,MAAM6F,IAAAA,kCAAgB,EAAC7F;QACzB;IACF,OAAO;QACL,MAAM,IAAI+E,yBAAiB,CAACK;IAC9B;AACF;AAEO,SAAS3G,qBAAqB,EACnC8H,WAAW,EACX1C,cAAc,EACdpB,aAAa,EACb+D,UAAU,EACVC,iBAAiB,EACjBvB,QAAQ,EACRF,OAAO,EACPG,MAAM,EAUP;IACC,MAAMuB,YAAY,CAAC,CAACvB;IACpB,IAAIwB,iBAA8BnI,eAChCiE,cAAcmE,UAAU;IAE1B,MAAMC,aAAavI,WAAWmE,cAAcmE,UAAU;IAEtD,IAAI,CAACD,gBAAgB;QACnBA,iBAAiB,IAAIpE,YAAYE;QACjCP,aAAaD,GAAG,CAACQ,cAAcmE,UAAU,EAAED;IAC7C;IAEA,MAAMtD,gBAAgB,CAACyD;QACrB,MAAMC,kBAAkBD,YAAYE,IAAI;QACxCL,eAAetD,aAAa,CAAC0D;IAC/B;IACA,KAAK,MAAME,YAAYxE,cAAcU,SAAS,CAAE;QAC9C8D,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,yBAAyB/D;IACnD;IAEA,SAASgE,4BACPhD,IAAwB,EACxBiD,WAAkD;QAElD,MAAMC,YAAsB,EAAE;QAC9B,KAAK,MAAMC,cAAcF,YAAYG,MAAM,GAAI;YAC7C,MAAMzH,OAAO0H,IAAAA,+BAAsB,EAACF,WAAWR,IAAI,EAAGN;YAEtD,IAAI1G,MAAM;oBACewH;gBAAvB,MAAMzH,iBAAiByH,EAAAA,mBAAAA,WAAWR,IAAI,qBAAfQ,iBAAiBlI,UAAU,CAAC,WAC/Ce,qBAAU,CAACI,GAAG,GACdJ,qBAAU,CAACC,KAAK;gBACpBiH,UAAU7D,IAAI,CAACnF,YAAY8F,MAAMtE,gBAAgBC;YACnD,OAAO,IACLO,IAAAA,2BAAoB,EAACiH,WAAWR,IAAI,KACpC1C,IAAAA,oCAA6B,EAACkD,WAAWR,IAAI,GAC7C;gBACAO,UAAU7D,IAAI,CACZnF,YAAY8F,MAAMhE,qBAAU,CAACG,IAAI,EAAE,CAAC,CAAC,EAAEgH,WAAWR,IAAI,EAAE;YAE5D;QACF;QACA,OAAOO;IACT;IAEA,KAAK,MAAMN,YAAYxE,cAAcU,SAAS,CAAE;QAC9C8D,SAASC,KAAK,CAACS,IAAI,CAACP,GAAG,CAAC,yBAAyB;gBAC/C5I;oBAAAA,kBAAAA,eAAeyI,SAASL,UAAU,sBAAlCpI,gBAAqC+E,YAAY,CAAC;gBAChD0D,SAASD,IAAI;aACd;;IAEL;IAEAvE,cAAcyE,KAAK,CAACS,IAAI,CAACP,GAAG,CAAC,yBAAyB,CAACQ;YAiCrDpJ;QAhCA,MAAM,CAACqJ,aAAaC,aAAaC,gBAAgB,GAAGH,WAAWI,KAAK;QACpE,MAAMC,aAAa;eACdZ,4BACDa,yBAAc,CAACC,MAAM,EACrBN,YAAYf,WAAW,CAACQ,WAAW;eAElCD,4BACDa,yBAAc,CAACE,MAAM,EACrBN,YAAYhB,WAAW,CAACQ,WAAW;eAEjCS,kBACAV,4BACEa,yBAAc,CAACG,UAAU,EACzBN,gBAAgBjB,WAAW,CAACQ,WAAW,IAEzC,EAAE;SACP;QAED,KAAK,MAAMN,QAAQiB,WAAY;YAC7B,MAAMK,QAAQzB,UAAU,CAACG,KAAK;YAC9B,IAAI,CAACsB,OAAO;gBACV;YACF;YAEA,IAAIA,MAAMpE,MAAM,KAAKhG,UAAU;gBAC7B;YACF;YAEAoK,MAAMpE,MAAM,GAAG/F;YACfgE,cAAcoG,IAAI,CAACvB;QACrB;SAEAxI,kBAAAA,eAAeiE,cAAcmE,UAAU,sBAAvCpI,gBAA0C+E,YAAY,CAAC;eAAIzE;SAAc;IAC3E;IAEA,MAAM0J,mBAAmBC,KAAKC,GAAG,CAAC,MAAMD,KAAKE,GAAG,CAAC,MAAM9E;IAEvD+E,YAAY;QACVhF,uBAAuBiD,YAAYhD;IACrC,GAAG2E,mBAAmB,MAAMK,KAAK;IAEjC,SAASC,iBAAiBnI,IAAuB;QAC/C,MAAMoI,QAAQrI,uBAAuBC,MAAM;QAE3C,KAAK,MAAMX,QAAQ+I,MAAO;YACxB,KAAK,MAAMjJ,gBAAgB;gBACzBoI,yBAAc,CAACC,MAAM;gBACrBD,yBAAc,CAACE,MAAM;gBACrBF,yBAAc,CAACG,UAAU;aAC1B,CAAE;gBACD,MAAMtE,WAAWxF,YAAYuB,cAAcO,qBAAU,CAACI,GAAG,EAAE,CAAC,CAAC,EAAET,MAAM;gBACrE,MAAMgJ,YAAYnC,UAAU,CAAC9C,SAAS;gBAEtC,8EAA8E;gBAC9E,IAAI,CAACiF,WAAW;oBAEd;gBACF;gBAEA,8EAA8E;gBAC9E,IAAIA,UAAU9E,MAAM,KAAK/F,OAAO;gBAEhC,0BAA0B;gBAC1B,IAAI,CAACmE,+BAA+BiC,QAAQ,CAACR,WAAW;oBACtDzB,+BAA+B2G,OAAO,CAAClF;oBAEvC,iCAAiC;oBACjC,yGAAyG;oBACzG,IAAIzB,+BAA+B/C,MAAM,GAAGkH,mBAAmB;wBAC7DnE,+BAA+B4G,GAAG;oBACpC;gBACF;gBACAF,UAAU/E,cAAc,GAAGO,KAAKC,GAAG;gBACnCuE,UAAU7E,OAAO,GAAG;YACtB;QACF;IACF;IAEA,SAASgF,WAAWC,EAAU;QAC5B,MAAMpJ,OAAO6F,IAAAA,kCAAgB,EAACuD;QAC9B,KAAK,MAAMtJ,gBAAgB;YACzBoI,yBAAc,CAACC,MAAM;YACrBD,yBAAc,CAACE,MAAM;YACrBF,yBAAc,CAACG,UAAU;SAC1B,CAAE;YACD,MAAMtE,WAAWxF,YAAYuB,cAAcO,qBAAU,CAACC,KAAK,EAAEN;YAC7D,MAAMgJ,YAAYnC,UAAU,CAAC9C,SAAS;YAEtC,8EAA8E;YAC9E,IAAI,CAACiF,WAAW;gBACd,sEAAsE;gBACtE,IAAIlJ,iBAAiBoI,yBAAc,CAACC,MAAM,EAAE;oBAC1C;gBACF;gBACA;YACF;YAEA,8EAA8E;YAC9E,IAAIa,UAAU9E,MAAM,KAAK/F,OAAO;YAEhC,0BAA0B;YAC1B,IAAI,CAACkE,sBAAsBkC,QAAQ,CAACR,WAAW;gBAC7C1B,sBAAsB4G,OAAO,CAAClF;gBAE9B,iCAAiC;gBACjC,IAAI1B,sBAAsB9C,MAAM,GAAGkH,mBAAmB;oBACpDpE,sBAAsB6G,GAAG;gBAC3B;YACF;YACAF,UAAU/E,cAAc,GAAGO,KAAKC,GAAG;YACnCuE,UAAU7E,OAAO,GAAG;QACtB;QACA;IACF;IAEA,eAAekF,eAAe,EAC5BrJ,IAAI,EACJsJ,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,GAAG,EAOJ;QACC,MAAMC,cAAc;QACpB,MAAMC,uBAAuBC,WAAW;YACtClL,MACE,CAAC,SAAS,EAAEsB,KAAK,uBAAuB,EAAE0J,YAAY,+CAA+C,CAAC;QAE1G,GAAGA,cAAc;QAEjB,IAAI;YACF,IAAIG;YACJ,IAAIN,YAAY;gBACdM,QAAQN;YACV,OAAO;gBACLM,QAAQ,MAAMxL,iBACZ2G,SACAhF,MACAwG,WAAWsD,cAAc,EACzB5E,UACAC;YAEJ;YAEA,MAAM4E,iBAAiB,CAAC,CAAC5E,UAAU0E,MAAM7D,QAAQ,CAAC1G,UAAU,CAAC6F;YAE7D,IAAI,OAAOqE,UAAU,aAAaA,UAAUO,gBAAgB;gBAC1DlK,MAAMmK,eAAe,GAAG;gBACxB,MAAM,qBAIL,CAJK,IAAInK,MACR,CAAC,2BAA2B,EAC1BgK,MAAM7J,IAAI,CACX,8BAA8B,EAAEwJ,QAAQ,QAAQ,QAAQ,CAAC,CAAC,GAHvD,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;YAEA,MAAMzJ,iBAAiBI,kBAAkB0J,MAAMzF,UAAU;YACzD,MAAM6F,WAAW,CACfnK;gBAMA,MAAMiE,WAAWxF,YAAYuB,cAAcC,gBAAgB8J,MAAM7J,IAAI;gBACrE,IACE6G,UAAU,CAAC9C,SAAS,IACpB,sGAAsG;gBACtG,4HAA4H;gBAC5H,+FAA+F;gBAC/F,CAACO,IAAAA,oCAA6B,EAACuC,UAAU,CAAC9C,SAAS,CAACK,UAAU,GAC9D;oBACAyC,UAAU,CAAC9C,SAAS,CAACI,OAAO,GAAG;oBAC/B0C,UAAU,CAAC9C,SAAS,CAACE,cAAc,GAAGO,KAAKC,GAAG;oBAC9C,IAAIoC,UAAU,CAAC9C,SAAS,CAACG,MAAM,KAAK/F,OAAO;wBACzC,OAAO;4BACL4F;4BACAmG,UAAU;4BACVC,kBAAkB;wBACpB;oBACF;oBAEA,OAAO;wBACLpG;wBACAmG,UAAU;wBACVC,kBAAkB;oBACpB;gBACF;gBAEAtD,UAAU,CAAC9C,SAAS,GAAG;oBACrBM,IAAI;oBACJiF;oBACAc,kBAAkBP,MAAM7D,QAAQ;oBAChCqE,SAASR,MAAM7D,QAAQ;oBACvB5B,YAAYyF,MAAMzF,UAAU;oBAC5BD,SAAS;oBACTF,gBAAgBO,KAAKC,GAAG;oBACxBP,QAAQjG;gBACV;gBACA,OAAO;oBACL8F,UAAUA;oBACVmG,UAAU;oBACVC,kBAAkB;gBACpB;YACF;YAEA,MAAMG,aAAa,MAAMC,IAAAA,sCAA6B,EAAC;gBACrDvK;gBACAwK,cAAcX,MAAM7D,QAAQ;gBAC5B+D;gBACAD,gBAAgBtD,WAAWsD,cAAc;gBACzCW,OAAO;gBACPC,QAAQlE;gBACRrB;YACF;YAEA,MAAMwF,QAAQ,IAAI/I;YAClB,MAAMgJ,oBACJb,kBAAkBO,WAAWO,GAAG,KAAKC,2BAAgB,CAAC3C,MAAM;YAE9D,IAAI4C,cAAcT,WAAWU,OAAO;YAEpC,IAAIxF,IAAAA,uBAAgB,EAACxF,SAAS,CAACwG,WAAWyE,YAAY,CAACC,cAAc,EAAE;gBACrEH,cAAc;YAChB;YAEAI,IAAAA,+BAAsB,EAAC;gBACrBnL,MAAM6J,MAAM7J,IAAI;gBAChB+K;gBACAK,UAAUrL;gBACVsL,UAAU;oBACR,4DAA4D;oBAC5D,IAAIT,qBAAqBb,gBAAgB;wBACvC;oBACF;oBACAY,MAAM1I,GAAG,CAACiG,yBAAc,CAACC,MAAM,EAAE8B,SAAS/B,yBAAc,CAACC,MAAM;gBACjE;gBACAmD,UAAU;oBACRX,MAAM1I,GAAG,CAACiG,yBAAc,CAACE,MAAM,EAAE6B,SAAS/B,yBAAc,CAACE,MAAM;oBAC/D,MAAMmD,kBAAkBhN,YACtB2J,yBAAc,CAACG,UAAU,EACzBtI,gBACA8J,MAAM7J,IAAI;oBAEZ,IACE6G,UAAU,CAAC0E,gBAAgB,IAC3B,CAAChG,IAAAA,gCAAyB,EAACsE,MAAM7J,IAAI,GACrC;wBACA,uCAAuC;wBACvC,OAAO6G,UAAU,CAAC0E,gBAAgB;oBACpC;gBACF;gBACAC,cAAc;oBACZb,MAAM1I,GAAG,CACPiG,yBAAc,CAACG,UAAU,EACzB4B,SAAS/B,yBAAc,CAACG,UAAU;oBAEpC,MAAMoD,cAAclN,YAClB2J,yBAAc,CAACE,MAAM,EACrBrI,gBACA8J,MAAM7J,IAAI;oBAEZ,IACE6G,UAAU,CAAC4E,YAAY,IACvB,CAAClG,IAAAA,gCAAyB,EAACsE,MAAM7J,IAAI,GACrC;wBACA,uCAAuC;wBACvC,OAAO6G,UAAU,CAAC4E,YAAY;oBAChC;gBACF;YACF;YAEA,MAAMC,cAAc;mBAAIf,MAAMlD,MAAM;aAAG;YACvC,MAAMkE,iCAAiC;mBAAIhB,MAAM5I,OAAO;aAAG,CAAC6J,MAAM,CAChE,CAAC,GAAGtD,MAAM,GAAKA,MAAM6B,gBAAgB;YAEvC,MAAM0B,cAAcH,YAAYI,IAAI,CAAC,CAACxD,QAAUA,MAAM4B,QAAQ;YAE9D,IAAI2B,aAAa;gBACf,MAAME,YAAYvC,QAAQK,MAAM7J,IAAI,GAAGgM,IAAAA,0BAAgB,EAACnC,MAAM7J,IAAI;gBAClEiM,IAAAA,qBAAa,EAACF,WAAWtC;YAC3B;YAEA,IAAIkC,+BAA+BpM,MAAM,GAAG,GAAG;gBAC7C,MAAM2M,oBAAoBC,QAAQC,GAAG,CACnCT,+BAA+BU,GAAG,CAAC,CAAC,CAAC/I,aAAa,EAAES,QAAQ,EAAE,CAAC;oBAC7D,OAAO,IAAIoI,QAAc,CAAC9F,SAASiG;wBACjCnK,cAAcoK,IAAI,CAACxI,UAAU,CAACa;4BAC5B,IAAIA,KAAK;gCACP,OAAO0H,OAAO1H;4BAChB;4BAEA,0DAA0D;4BAC1D,6DAA6D;4BAC7D,MAAM4H,eAAe7F,eAAehD,WAAW,CAACL;4BAChD,IAAIkJ,cAAc;gCAChBrK,cAAcoK,IAAI,CAACxI,UAAU,CAAC0I;oCAC5B,IAAIA,YAAY;wCACd,OAAOH,OAAOG;oCAChB;oCACApG;gCACF;4BACF,OAAO;gCACLA;4BACF;wBACF;oBACF;gBACF;gBAGFM,eAAe5D,UAAU,CAAC;uBAAI4H,MAAM/L,IAAI;iBAAG;gBAC3C,MAAMsN;YACR;QACF,SAAU;YACRQ,aAAa/C;QACf;IACF;IAUA,4EAA4E;IAC5E,MAAMgD,UAAUC,gBAAO,CAACC,MAAM,CAAkC;QAC9D,iEAAiE;QACjE,uEAAuE;QACvE,0EAA0E;QAC1E,4CAA4C;QAC5C,EAAE;QACF,sEAAsE;QACtE,sEAAsE;QACtE,oEAAoE;QACpEC,YAAY,CAACC,UAAYC,KAAKC,SAAS,CAACF;QACxC,2EAA2E;QAC3EG,aAAaC,6BAAkB;IACjC;IAEA,OAAO;QACL,MAAMC,YAAW,EACfpN,IAAI,EACJsJ,WAAW,IAAI,EACfC,UAAU,EACVC,KAAK,EACLC,GAAG,EACe;YAClB,yEAAyE;YACzE,oEAAoE;YACpE,IAAI,CAACH,YAAYC,cAAc8D,IAAAA,gDAAwB,EAAC9D,aAAa;gBACnED,WAAWC,WAAWD,QAAQ;YAChC;YAEA,oEAAoE;YACpE,sEAAsE;YACtE,4CAA4C;YAC5C,OAAOqD,QAAQW,KAAK,CAAC;gBAAEtN;gBAAMsJ;gBAAUC;gBAAYC;YAAM,GAAG;gBAC1D,MAAMH,eAAe;oBACnBrJ;oBACAsJ;oBACAC;oBACAC;oBACAC;gBACF;YACF;QACF;QACA8D,OAAMpF,MAAU,EAAEqF,iBAAqC;YACrD,IAAIC,yBAAuC;YAE3CtF,OAAOuF,gBAAgB,CAAC,SAAS;gBAC/BD,yBAAyB;YAC3B;YACAtF,OAAOuF,gBAAgB,CAAC,WAAW,CAAC,EAAEC,IAAI,EAAE;gBAC1C,IAAI;oBACF,MAAM7I,QAAQ0I;oBAEd,uEAAuE;oBACvE,IAAI,CAACC,0BAA0B3I,OAAO;wBACpCyB,YAAYqH,IAAI,CAAC;4BACfC,QAAQC,6CAA2B,CAACC,YAAY;4BAChDC,WAAWC,IAAAA,sBAAc,EAACnJ;wBAC5B;wBACA2I,yBAAyB;oBAC3B;oBAEA,MAAMS,aAAalB,KAAKmB,KAAK,CAC3B,OAAOR,SAAS,WAAWA,KAAKS,QAAQ,KAAKT;oBAG/C,IAAIO,WAAWG,KAAK,KAAK,QAAQ;wBAC/B,IAAIH,WAAWI,WAAW,EAAE;4BAC1BxF,iBAAiBoF,WAAWvN,IAAI;wBAClC,OAAO;4BACLwI,WAAW+E,WAAWlO,IAAI;wBAC5B;oBACF;gBACF,EAAE,OAAM,CAAC;YACX;QACF;IACF;AACF"}