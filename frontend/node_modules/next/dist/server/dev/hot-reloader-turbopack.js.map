{"version": 3, "sources": ["../../../src/server/dev/hot-reloader-turbopack.ts"], "sourcesContent": ["import type { Socket } from 'net'\nimport { mkdir, writeFile } from 'fs/promises'\nimport { join, extname } from 'path'\nimport { pathToFileURL } from 'url'\n\nimport ws from 'next/dist/compiled/ws'\n\nimport type { OutputState } from '../../build/output/store'\nimport { store as consoleStore } from '../../build/output/store'\nimport type {\n  CompilationError,\n  HMR_ACTION_TYPES,\n  NextJsHotReloaderInterface,\n  ReloadPageAction,\n  SyncAction,\n  TurbopackConnectedAction,\n} from './hot-reloader-types'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from './hot-reloader-types'\nimport type {\n  Update as TurbopackUpdate,\n  Endpoint,\n  WrittenEndpoint,\n  TurbopackResult,\n  Project,\n  Entrypoints,\n} from '../../build/swc/types'\nimport { createDefineEnv } from '../../build/swc'\nimport * as Log from '../../build/output/log'\nimport {\n  getVersionInfo,\n  matchNextPageBundleRequest,\n} from './hot-reloader-webpack'\nimport { BLOCKED_PAGES } from '../../shared/lib/constants'\nimport {\n  getOverlayMiddleware,\n  getSourceMapMiddleware,\n} from '../../client/components/react-dev-overlay/server/middleware-turbopack'\nimport { PageNotFoundError } from '../../shared/lib/utils'\nimport { debounce } from '../utils'\nimport { deleteAppClientCache, deleteCache } from './require-cache'\nimport {\n  clearAllModuleContexts,\n  clearModuleContext,\n} from '../lib/render-server'\nimport { denormalizePagePath } from '../../shared/lib/page-path/denormalize-page-path'\nimport { trace } from '../../trace'\nimport {\n  AssetMapper,\n  type ChangeSubscriptions,\n  type ClientState,\n  handleEntrypoints,\n  handlePagesErrorRoute,\n  handleRouteType,\n  hasEntrypointForKey,\n  msToNs,\n  type ReadyIds,\n  type SendHmr,\n  type StartBuilding,\n  processTopLevelIssues,\n  printNonFatalIssue,\n  normalizedPageToTurbopackStructureRoute,\n} from './turbopack-utils'\nimport {\n  propagateServerField,\n  type ServerFields,\n  type SetupOpts,\n} from '../lib/router-utils/setup-dev-bundler'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { findPagePathData } from './on-demand-entry-handler'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport {\n  type EntryKey,\n  getEntryKey,\n  splitEntryKey,\n} from '../../shared/lib/turbopack/entry-key'\nimport { FAST_REFRESH_RUNTIME_RELOAD } from './messages'\nimport { generateEncryptionKeyBase64 } from '../app-render/encryption-utils-server'\nimport { isAppPageRouteDefinition } from '../route-definitions/app-page-route-definition'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { getNodeDebugType } from '../lib/utils'\nimport { isMetadataRouteFile } from '../../lib/metadata/is-metadata-route'\nimport {\n  setBundlerFindSourceMapImplementation,\n  type ModernSourceMapPayload,\n} from '../patch-error-inspect'\nimport { getNextErrorFeedbackMiddleware } from '../../client/components/react-dev-overlay/server/get-next-error-feedback-middleware'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isWellKnownError,\n  processIssues,\n  renderStyledStringToErrorAnsi,\n  type EntryIssuesMap,\n  type TopLevelIssuesMap,\n} from '../../shared/lib/turbopack/utils'\nimport { getDevOverlayFontMiddleware } from '../../client/components/react-dev-overlay/font/get-dev-overlay-font-middleware'\nimport { devIndicatorServerState } from './dev-indicator-server-state'\nimport { getDisableDevIndicatorMiddleware } from './dev-indicator-middleware'\n// import { getSupportedBrowsers } from '../../build/utils'\n\nconst wsServer = new ws.Server({ noServer: true })\nconst isTestMode = !!(\n  process.env.NEXT_TEST_MODE ||\n  process.env.__NEXT_TEST_MODE ||\n  process.env.DEBUG\n)\n\nconst sessionId = Math.floor(Number.MAX_SAFE_INTEGER * Math.random())\n\n/**\n * Replaces turbopack:///[project] with the specified project in the `source` field.\n */\nfunction rewriteTurbopackSources(\n  projectRoot: string,\n  sourceMap: ModernSourceMapPayload\n): void {\n  if ('sections' in sourceMap) {\n    for (const section of sourceMap.sections) {\n      rewriteTurbopackSources(projectRoot, section.map)\n    }\n  } else {\n    for (let i = 0; i < sourceMap.sources.length; i++) {\n      sourceMap.sources[i] = pathToFileURL(\n        join(\n          projectRoot,\n          sourceMap.sources[i].replace(/turbopack:\\/\\/\\/\\[project\\]/, '')\n        )\n      ).toString()\n    }\n  }\n}\n\nfunction getSourceMapFromTurbopack(\n  project: Project,\n  projectRoot: string,\n  sourceURL: string\n): ModernSourceMapPayload | undefined {\n  let sourceMapJson: string | null = null\n\n  try {\n    sourceMapJson = project.getSourceMapSync(sourceURL)\n  } catch (err) {}\n\n  if (sourceMapJson === null) {\n    return undefined\n  } else {\n    const payload: ModernSourceMapPayload = JSON.parse(sourceMapJson)\n    // The sourcemap from Turbopack is not yet written to disk so its `sources`\n    // are not absolute paths yet. We need to rewrite them to be absolute paths.\n    rewriteTurbopackSources(projectRoot, payload)\n    return payload\n  }\n}\n\nexport async function createHotReloaderTurbopack(\n  opts: SetupOpts,\n  serverFields: ServerFields,\n  distDir: string,\n  resetFetch: () => void\n): Promise<NextJsHotReloaderInterface> {\n  const dev = true\n  const buildId = 'development'\n  const { nextConfig, dir: projectPath } = opts\n\n  const { loadBindings } =\n    require('../../build/swc') as typeof import('../../build/swc')\n\n  let bindings = await loadBindings()\n\n  // For the debugging purpose, check if createNext or equivalent next instance setup in test cases\n  // works correctly. Normally `run-test` hides output so only will be visible when `--debug` flag is used.\n  if (process.env.TURBOPACK && isTestMode) {\n    require('console').log('Creating turbopack project', {\n      dir: projectPath,\n      testMode: isTestMode,\n    })\n  }\n\n  const hasRewrites =\n    opts.fsChecker.rewrites.afterFiles.length > 0 ||\n    opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n    opts.fsChecker.rewrites.fallback.length > 0\n\n  const hotReloaderSpan = trace('hot-reloader', undefined, {\n    version: process.env.__NEXT_VERSION as string,\n  })\n  // Ensure the hotReloaderSpan is flushed immediately as it's the parentSpan for all processing\n  // of the current `next dev` invocation.\n  hotReloaderSpan.stop()\n\n  const encryptionKey = await generateEncryptionKeyBase64({\n    isBuild: false,\n    distDir,\n  })\n\n  // TODO: Implement\n  let clientRouterFilters: any\n  if (nextConfig.experimental.clientRouterFilter) {\n    // TODO this need to be set correctly for persistent caching to work\n  }\n\n  // const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  const supportedBrowsers = [\n    'last 1 Chrome versions, last 1 Firefox versions, last 1 Safari versions, last 1 Edge versions',\n  ]\n\n  const project = await bindings.turbo.createProject(\n    {\n      projectPath: projectPath,\n      rootPath:\n        opts.nextConfig.experimental.turbo?.root ||\n        opts.nextConfig.outputFileTracingRoot ||\n        projectPath,\n      distDir,\n      nextConfig: opts.nextConfig,\n      jsConfig: await getTurbopackJsConfig(projectPath, nextConfig),\n      watch: {\n        enable: dev,\n        pollIntervalMs: nextConfig.watchOptions?.pollIntervalMs,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters,\n        config: nextConfig,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix: opts.nextConfig.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // TODO: Implement\n        middlewareMatchers: undefined,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps: opts.fsChecker.prerenderManifest.preview,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling: false,\n    },\n    {\n      persistentCaching: isPersistentCachingEnabled(opts.nextConfig),\n      memoryLimit: opts.nextConfig.experimental.turbo?.memoryLimit,\n    }\n  )\n  setBundlerFindSourceMapImplementation(\n    getSourceMapFromTurbopack.bind(null, project, projectPath)\n  )\n  opts.onDevServerCleanup?.(async () => {\n    setBundlerFindSourceMapImplementation(() => undefined)\n    await project.onExit()\n  })\n  const entrypointsSubscription = project.entrypointsSubscribe()\n\n  const currentWrittenEntrypoints: Map<EntryKey, WrittenEndpoint> = new Map()\n  const currentEntrypoints: Entrypoints = {\n    global: {\n      app: undefined,\n      document: undefined,\n      error: undefined,\n\n      middleware: undefined,\n      instrumentation: undefined,\n    },\n\n    page: new Map(),\n    app: new Map(),\n  }\n\n  const currentTopLevelIssues: TopLevelIssuesMap = new Map()\n  const currentEntryIssues: EntryIssuesMap = new Map()\n\n  const manifestLoader = new TurbopackManifestLoader({\n    buildId,\n    distDir,\n    encryptionKey,\n  })\n\n  // Dev specific\n  const changeSubscriptions: ChangeSubscriptions = new Map()\n  const serverPathState = new Map<string, string>()\n  const readyIds: ReadyIds = new Set()\n  let currentEntriesHandlingResolve: ((value?: unknown) => void) | undefined\n  let currentEntriesHandling = new Promise(\n    (resolve) => (currentEntriesHandlingResolve = resolve)\n  )\n\n  const assetMapper = new AssetMapper()\n\n  function clearRequireCache(\n    key: EntryKey,\n    writtenEndpoint: WrittenEndpoint,\n    {\n      force,\n    }: {\n      // Always clear the cache, don't check if files have changed\n      force?: boolean\n    } = {}\n  ): void {\n    if (force) {\n      for (const { path, contentHash } of writtenEndpoint.serverPaths) {\n        serverPathState.set(path, contentHash)\n      }\n    } else {\n      // Figure out if the server files have changed\n      let hasChange = false\n      for (const { path, contentHash } of writtenEndpoint.serverPaths) {\n        // We ignore source maps\n        if (path.endsWith('.map')) continue\n        const localKey = `${key}:${path}`\n        const localHash = serverPathState.get(localKey)\n        const globalHash = serverPathState.get(path)\n        if (\n          (localHash && localHash !== contentHash) ||\n          (globalHash && globalHash !== contentHash)\n        ) {\n          hasChange = true\n          serverPathState.set(key, contentHash)\n          serverPathState.set(path, contentHash)\n        } else {\n          if (!localHash) {\n            serverPathState.set(key, contentHash)\n          }\n          if (!globalHash) {\n            serverPathState.set(path, contentHash)\n          }\n        }\n      }\n\n      if (!hasChange) {\n        return\n      }\n    }\n\n    resetFetch()\n\n    const hasAppPaths = writtenEndpoint.serverPaths.some(({ path: p }) =>\n      p.startsWith('server/app')\n    )\n\n    if (hasAppPaths) {\n      deleteAppClientCache()\n    }\n\n    const serverPaths = writtenEndpoint.serverPaths.map(({ path: p }) =>\n      join(distDir, p)\n    )\n\n    for (const file of serverPaths) {\n      clearModuleContext(file)\n      deleteCache(file)\n    }\n\n    return\n  }\n\n  const buildingIds = new Set()\n\n  const startBuilding: StartBuilding = (id, requestUrl, forceRebuild) => {\n    if (!forceRebuild && readyIds.has(id)) {\n      return () => {}\n    }\n    if (buildingIds.size === 0) {\n      consoleStore.setState(\n        {\n          loading: true,\n          trigger: id,\n          url: requestUrl,\n        } as OutputState,\n        true\n      )\n    }\n    buildingIds.add(id)\n    return function finishBuilding() {\n      if (buildingIds.size === 0) {\n        return\n      }\n      readyIds.add(id)\n      buildingIds.delete(id)\n      if (buildingIds.size === 0) {\n        hmrEventHappened = false\n        consoleStore.setState(\n          {\n            loading: false,\n          } as OutputState,\n          true\n        )\n      }\n    }\n  }\n\n  let hmrEventHappened = false\n  let hmrHash = 0\n\n  const clients = new Set<ws>()\n  const clientStates = new WeakMap<ws, ClientState>()\n\n  function sendToClient(client: ws, payload: HMR_ACTION_TYPES) {\n    client.send(JSON.stringify(payload))\n  }\n\n  function sendEnqueuedMessages() {\n    for (const [, issueMap] of currentEntryIssues) {\n      if (\n        [...issueMap.values()].filter((i) => i.severity !== 'warning').length >\n        0\n      ) {\n        // During compilation errors we want to delay the HMR events until errors are fixed\n        return\n      }\n    }\n\n    for (const client of clients) {\n      const state = clientStates.get(client)\n      if (!state) {\n        continue\n      }\n\n      for (const [, issueMap] of state.clientIssues) {\n        if (\n          [...issueMap.values()].filter((i) => i.severity !== 'warning')\n            .length > 0\n        ) {\n          // During compilation errors we want to delay the HMR events until errors are fixed\n          return\n        }\n      }\n\n      for (const payload of state.hmrPayloads.values()) {\n        sendToClient(client, payload)\n      }\n      state.hmrPayloads.clear()\n\n      if (state.turbopackUpdates.length > 0) {\n        sendToClient(client, {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n          data: state.turbopackUpdates,\n        })\n        state.turbopackUpdates.length = 0\n      }\n    }\n  }\n  const sendEnqueuedMessagesDebounce = debounce(sendEnqueuedMessages, 2)\n\n  const sendHmr: SendHmr = (id: string, payload: HMR_ACTION_TYPES) => {\n    for (const client of clients) {\n      clientStates.get(client)?.hmrPayloads.set(id, payload)\n    }\n\n    hmrEventHappened = true\n    sendEnqueuedMessagesDebounce()\n  }\n\n  function sendTurbopackMessage(payload: TurbopackUpdate) {\n    // TODO(PACK-2049): For some reason we end up emitting hundreds of issues messages on bigger apps,\n    //   a lot of which are duplicates.\n    //   They are currently not handled on the client at all, so might as well not send them for now.\n    payload.diagnostics = []\n    payload.issues = []\n\n    for (const client of clients) {\n      clientStates.get(client)?.turbopackUpdates.push(payload)\n    }\n\n    hmrEventHappened = true\n    sendEnqueuedMessagesDebounce()\n  }\n\n  async function subscribeToChanges(\n    key: EntryKey,\n    includeIssues: boolean,\n    endpoint: Endpoint,\n    makePayload: (\n      change: TurbopackResult,\n      hash: string\n    ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void,\n    onError?: (\n      error: Error\n    ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void\n  ) {\n    if (changeSubscriptions.has(key)) {\n      return\n    }\n\n    const { side } = splitEntryKey(key)\n\n    const changedPromise = endpoint[`${side}Changed`](includeIssues)\n    changeSubscriptions.set(key, changedPromise)\n    try {\n      const changed = await changedPromise\n\n      for await (const change of changed) {\n        processIssues(currentEntryIssues, key, change, false, true)\n        // TODO: Get an actual content hash from Turbopack.\n        const payload = await makePayload(change, String(++hmrHash))\n        if (payload) {\n          sendHmr(key, payload)\n        }\n      }\n    } catch (e) {\n      changeSubscriptions.delete(key)\n      const payload = await onError?.(e as Error)\n      if (payload) {\n        sendHmr(key, payload)\n      }\n      return\n    }\n    changeSubscriptions.delete(key)\n  }\n\n  async function unsubscribeFromChanges(key: EntryKey) {\n    const subscription = await changeSubscriptions.get(key)\n    if (subscription) {\n      await subscription.return?.()\n      changeSubscriptions.delete(key)\n    }\n    currentEntryIssues.delete(key)\n  }\n\n  async function subscribeToHmrEvents(client: ws, id: string) {\n    const key = getEntryKey('assets', 'client', id)\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      // maybe throw an error / force the client to reload?\n      return\n    }\n\n    const state = clientStates.get(client)\n    if (!state || state.subscriptions.has(id)) {\n      return\n    }\n\n    const subscription = project!.hmrEvents(id)\n    state.subscriptions.set(id, subscription)\n\n    // The subscription will always emit once, which is the initial\n    // computation. This is not a change, so swallow it.\n    try {\n      await subscription.next()\n\n      for await (const data of subscription) {\n        processIssues(state.clientIssues, key, data, false, true)\n        if (data.type !== 'issues') {\n          sendTurbopackMessage(data)\n        }\n      }\n    } catch (e) {\n      // The client might be using an HMR session from a previous server, tell them\n      // to fully reload the page to resolve the issue. We can't use\n      // `hotReloader.send` since that would force every connected client to\n      // reload, only this client is out of date.\n      const reloadAction: ReloadPageAction = {\n        action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n        data: `error in HMR event subscription for ${id}: ${e}`,\n      }\n      sendToClient(client, reloadAction)\n      client.close()\n      return\n    }\n  }\n\n  function unsubscribeFromHmrEvents(client: ws, id: string) {\n    const state = clientStates.get(client)\n    if (!state) {\n      return\n    }\n\n    const subscription = state.subscriptions.get(id)\n    subscription?.return!()\n\n    const key = getEntryKey('assets', 'client', id)\n    state.clientIssues.delete(key)\n  }\n\n  async function handleEntrypointsSubscription() {\n    for await (const entrypoints of entrypointsSubscription) {\n      if (!currentEntriesHandlingResolve) {\n        currentEntriesHandling = new Promise(\n          // eslint-disable-next-line no-loop-func\n          (resolve) => (currentEntriesHandlingResolve = resolve)\n        )\n      }\n\n      processTopLevelIssues(currentTopLevelIssues, entrypoints)\n\n      await handleEntrypoints({\n        entrypoints,\n\n        currentEntrypoints,\n\n        currentEntryIssues,\n        manifestLoader,\n        devRewrites: opts.fsChecker.rewrites,\n        productionRewrites: undefined,\n        logErrors: true,\n\n        dev: {\n          assetMapper,\n          changeSubscriptions,\n          clients,\n          clientStates,\n          serverFields,\n\n          hooks: {\n            handleWrittenEndpoint: (id, result) => {\n              currentWrittenEntrypoints.set(id, result)\n              clearRequireCache(id, result)\n            },\n            propagateServerField: propagateServerField.bind(null, opts),\n            sendHmr,\n            startBuilding,\n            subscribeToChanges,\n            unsubscribeFromChanges,\n            unsubscribeFromHmrEvents,\n          },\n        },\n      })\n\n      currentEntriesHandlingResolve!()\n      currentEntriesHandlingResolve = undefined\n    }\n  }\n\n  await mkdir(join(distDir, 'server'), { recursive: true })\n  await mkdir(join(distDir, 'static', buildId), { recursive: true })\n  await writeFile(\n    join(distDir, 'package.json'),\n    JSON.stringify(\n      {\n        type: 'commonjs',\n      },\n      null,\n      2\n    )\n  )\n\n  const middlewares = [\n    getOverlayMiddleware(project, projectPath),\n    getSourceMapMiddleware(project),\n    getNextErrorFeedbackMiddleware(opts.telemetry),\n    getDevOverlayFontMiddleware(),\n    getDisableDevIndicatorMiddleware(),\n  ]\n\n  const versionInfoPromise = getVersionInfo()\n\n  let devtoolsFrontendUrl: string | undefined\n  const nodeDebugType = getNodeDebugType()\n  if (nodeDebugType) {\n    const debugPort = process.debugPort\n    let debugInfo\n    try {\n      // It requires to use 127.0.0.1 instead of localhost for server-side fetching.\n      const debugInfoList = await fetch(\n        `http://127.0.0.1:${debugPort}/json/list`\n      ).then((res) => res.json())\n      debugInfo = debugInfoList[0]\n    } catch {}\n    if (debugInfo) {\n      devtoolsFrontendUrl = debugInfo.devtoolsFrontendUrl\n    }\n  }\n\n  const hotReloader: NextJsHotReloaderInterface = {\n    turbopackProject: project,\n    activeWebpackConfigs: undefined,\n    serverStats: null,\n    edgeServerStats: null,\n    async run(req, res, _parsedUrl) {\n      // intercept page chunks request and ensure them with turbopack\n      if (req.url?.startsWith('/_next/static/chunks/pages/')) {\n        const params = matchNextPageBundleRequest(req.url)\n\n        if (params) {\n          const decodedPagePath = `/${params.path\n            .map((param: string) => decodeURIComponent(param))\n            .join('/')}`\n\n          const denormalizedPagePath = denormalizePagePath(decodedPagePath)\n\n          await hotReloader\n            .ensurePage({\n              page: denormalizedPagePath,\n              clientOnly: false,\n              definition: undefined,\n              url: req.url,\n            })\n            .catch(console.error)\n        }\n      }\n\n      for (const middleware of middlewares) {\n        let calledNext = false\n\n        await middleware(req, res, () => {\n          calledNext = true\n        })\n\n        if (!calledNext) {\n          return { finished: true }\n        }\n      }\n\n      // Request was not finished.\n      return { finished: undefined }\n    },\n\n    // TODO: Figure out if socket type can match the NextJsHotReloaderInterface\n    onHMR(req, socket: Socket, head, onUpgrade) {\n      wsServer.handleUpgrade(req, socket, head, (client) => {\n        onUpgrade(client)\n        const clientIssues: EntryIssuesMap = new Map()\n        const subscriptions: Map<string, AsyncIterator<any>> = new Map()\n\n        clients.add(client)\n        clientStates.set(client, {\n          clientIssues,\n          hmrPayloads: new Map(),\n          turbopackUpdates: [],\n          subscriptions,\n        })\n\n        client.on('close', () => {\n          // Remove active subscriptions\n          for (const subscription of subscriptions.values()) {\n            subscription.return?.()\n          }\n          clientStates.delete(client)\n          clients.delete(client)\n        })\n\n        client.addEventListener('message', ({ data }) => {\n          const parsedData = JSON.parse(\n            typeof data !== 'string' ? data.toString() : data\n          )\n\n          // Next.js messages\n          switch (parsedData.event) {\n            case 'span-end': {\n              hotReloaderSpan.manualTraceChild(\n                parsedData.spanName,\n                msToNs(parsedData.startTime),\n                msToNs(parsedData.endTime),\n                parsedData.attributes\n              )\n              break\n            }\n            case 'client-hmr-latency': // { id, startTime, endTime, page, updatedModules, isPageHidden }\n              hotReloaderSpan.manualTraceChild(\n                parsedData.event,\n                msToNs(parsedData.startTime),\n                msToNs(parsedData.endTime),\n                {\n                  updatedModules: parsedData.updatedModules,\n                  page: parsedData.page,\n                  isPageHidden: parsedData.isPageHidden,\n                }\n              )\n              break\n            case 'client-error': // { errorCount, clientId }\n            case 'client-warning': // { warningCount, clientId }\n            case 'client-success': // { clientId }\n            case 'server-component-reload-page': // { clientId }\n            case 'client-reload-page': // { clientId }\n            case 'client-removed-page': // { page }\n            case 'client-full-reload': // { stackTrace, hadRuntimeError }\n              const { hadRuntimeError, dependencyChain } = parsedData\n              if (hadRuntimeError) {\n                Log.warn(FAST_REFRESH_RUNTIME_RELOAD)\n              }\n              if (\n                Array.isArray(dependencyChain) &&\n                typeof dependencyChain[0] === 'string'\n              ) {\n                const cleanedModulePath = dependencyChain[0]\n                  .replace(/^\\[project\\]/, '.')\n                  .replace(/ \\[.*\\] \\(.*\\)$/, '')\n                Log.warn(\n                  `Fast Refresh had to perform a full reload when ${cleanedModulePath} changed. Read more: https://nextjs.org/docs/messages/fast-refresh-reload`\n                )\n              }\n              break\n            case 'client-added-page':\n              // TODO\n              break\n\n            default:\n              // Might be a Turbopack message...\n              if (!parsedData.type) {\n                throw new Error(`unrecognized HMR message \"${data}\"`)\n              }\n          }\n\n          // Turbopack messages\n          switch (parsedData.type) {\n            case 'turbopack-subscribe':\n              subscribeToHmrEvents(client, parsedData.path)\n              break\n\n            case 'turbopack-unsubscribe':\n              unsubscribeFromHmrEvents(client, parsedData.path)\n              break\n\n            default:\n              if (!parsedData.event) {\n                throw new Error(`unrecognized Turbopack HMR message \"${data}\"`)\n              }\n          }\n        })\n\n        const turbopackConnected: TurbopackConnectedAction = {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n          data: { sessionId },\n        }\n        sendToClient(client, turbopackConnected)\n\n        const errors: CompilationError[] = []\n\n        for (const entryIssues of currentEntryIssues.values()) {\n          for (const issue of entryIssues.values()) {\n            if (issue.severity !== 'warning') {\n              errors.push({\n                message: formatIssue(issue),\n              })\n            } else {\n              printNonFatalIssue(issue)\n            }\n          }\n        }\n\n        if (devIndicatorServerState.disabledUntil < Date.now()) {\n          devIndicatorServerState.disabledUntil = 0\n        }\n\n        ;(async function () {\n          const versionInfo = await versionInfoPromise\n\n          const sync: SyncAction = {\n            action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC,\n            errors,\n            warnings: [],\n            hash: '',\n            versionInfo,\n            debug: {\n              devtoolsFrontendUrl,\n            },\n            devIndicator: devIndicatorServerState,\n          }\n\n          sendToClient(client, sync)\n        })()\n      })\n    },\n\n    send(action) {\n      const payload = JSON.stringify(action)\n      for (const client of clients) {\n        client.send(payload)\n      }\n    },\n\n    setHmrServerError(_error) {\n      // Not implemented yet.\n    },\n    clearHmrServerError() {\n      // Not implemented yet.\n    },\n    async start() {},\n    async getCompilationErrors(page) {\n      const appEntryKey = getEntryKey('app', 'server', page)\n      const pagesEntryKey = getEntryKey('pages', 'server', page)\n\n      const topLevelIssues = currentTopLevelIssues.values()\n\n      const thisEntryIssues =\n        currentEntryIssues.get(appEntryKey) ??\n        currentEntryIssues.get(pagesEntryKey)\n\n      if (thisEntryIssues !== undefined && thisEntryIssues.size > 0) {\n        // If there is an error related to the requesting page we display it instead of the first error\n        return [...topLevelIssues, ...thisEntryIssues.values()]\n          .map((issue) => {\n            const formattedIssue = formatIssue(issue)\n            if (issue.severity === 'warning') {\n              printNonFatalIssue(issue)\n              return null\n            } else if (isWellKnownError(issue)) {\n              Log.error(formattedIssue)\n            }\n\n            return new Error(formattedIssue)\n          })\n          .filter((error) => error !== null)\n      }\n\n      // Otherwise, return all errors across pages\n      const errors = []\n      for (const issue of topLevelIssues) {\n        if (issue.severity !== 'warning') {\n          errors.push(new Error(formatIssue(issue)))\n        }\n      }\n      for (const entryIssues of currentEntryIssues.values()) {\n        for (const issue of entryIssues.values()) {\n          if (issue.severity !== 'warning') {\n            const message = formatIssue(issue)\n            errors.push(new Error(message))\n          } else {\n            printNonFatalIssue(issue)\n          }\n        }\n      }\n      return errors\n    },\n    async invalidate({\n      // .env files or tsconfig/jsconfig change\n      reloadAfterInvalidation,\n    }) {\n      if (reloadAfterInvalidation) {\n        for (const [key, entrypoint] of currentWrittenEntrypoints) {\n          clearRequireCache(key, entrypoint, { force: true })\n        }\n\n        await clearAllModuleContexts()\n        this.send({\n          action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n          hash: String(++hmrHash),\n        })\n      }\n    },\n    async buildFallbackError() {\n      // Not implemented yet.\n    },\n    async ensurePage({\n      page: inputPage,\n      // Unused parameters\n      // clientOnly,\n      appPaths,\n      definition,\n      isApp,\n      url: requestUrl,\n    }) {\n      return hotReloaderSpan\n        .traceChild('ensure-page', {\n          inputPage,\n        })\n        .traceAsyncFn(async () => {\n          if (BLOCKED_PAGES.includes(inputPage) && inputPage !== '/_error') {\n            return\n          }\n\n          await currentEntriesHandling\n\n          // TODO We shouldn't look into the filesystem again. This should use the information from entrypoints\n          let routeDef: Pick<\n            RouteDefinition,\n            'filename' | 'bundlePath' | 'page'\n          > =\n            definition ??\n            (await findPagePathData(\n              projectPath,\n              inputPage,\n              nextConfig.pageExtensions,\n              opts.pagesDir,\n              opts.appDir\n            ))\n\n          // If the route is actually an app page route, then we should have access\n          // to the app route definition, and therefore, the appPaths from it.\n          if (!appPaths && definition && isAppPageRouteDefinition(definition)) {\n            appPaths = definition.appPaths\n          }\n\n          let page = routeDef.page\n          if (appPaths) {\n            const normalizedPage = normalizeAppPath(page)\n\n            // filter out paths that are not exact matches (e.g. catchall)\n            const matchingAppPaths = appPaths.filter(\n              (path) => normalizeAppPath(path) === normalizedPage\n            )\n\n            // the last item in the array is the root page, if there are parallel routes\n            page = matchingAppPaths[matchingAppPaths.length - 1]\n          }\n\n          const pathname = definition?.pathname ?? inputPage\n\n          if (page === '/_error') {\n            let finishBuilding = startBuilding(pathname, requestUrl, false)\n            try {\n              await handlePagesErrorRoute({\n                currentEntryIssues,\n                entrypoints: currentEntrypoints,\n                manifestLoader,\n                devRewrites: opts.fsChecker.rewrites,\n                productionRewrites: undefined,\n                logErrors: true,\n                hooks: {\n                  subscribeToChanges,\n                  handleWrittenEndpoint: (id, result) => {\n                    clearRequireCache(id, result)\n                    currentWrittenEntrypoints.set(id, result)\n                    assetMapper.setPathsForKey(id, result.clientPaths)\n                  },\n                },\n              })\n            } finally {\n              finishBuilding()\n            }\n            return\n          }\n\n          const isInsideAppDir = routeDef.bundlePath.startsWith('app/')\n          const isEntryMetadataRouteFile = isMetadataRouteFile(\n            routeDef.filename.replace(opts.appDir || '', ''),\n            nextConfig.pageExtensions,\n            true\n          )\n          const normalizedAppPage = isEntryMetadataRouteFile\n            ? normalizedPageToTurbopackStructureRoute(\n                page,\n                extname(routeDef.filename)\n              )\n            : page\n\n          const route = isInsideAppDir\n            ? currentEntrypoints.app.get(normalizedAppPage)\n            : currentEntrypoints.page.get(page)\n\n          if (!route) {\n            // TODO: why is this entry missing in turbopack?\n            if (page === '/middleware') return\n            if (page === '/src/middleware') return\n            if (page === '/instrumentation') return\n            if (page === '/src/instrumentation') return\n\n            throw new PageNotFoundError(`route not found ${page}`)\n          }\n\n          // We don't throw on ensureOpts.isApp === true for page-api\n          // since this can happen when app pages make\n          // api requests to page API routes.\n          if (isApp && route.type === 'page') {\n            throw new Error(`mis-matched route type: isApp && page for ${page}`)\n          }\n\n          const finishBuilding = startBuilding(pathname, requestUrl, false)\n          try {\n            await handleRouteType({\n              dev,\n              page,\n              pathname,\n              route,\n              currentEntryIssues,\n              entrypoints: currentEntrypoints,\n              manifestLoader,\n              readyIds,\n              devRewrites: opts.fsChecker.rewrites,\n              productionRewrites: undefined,\n              logErrors: true,\n\n              hooks: {\n                subscribeToChanges,\n                handleWrittenEndpoint: (id, result) => {\n                  currentWrittenEntrypoints.set(id, result)\n                  clearRequireCache(id, result)\n                  assetMapper.setPathsForKey(id, result.clientPaths)\n                },\n              },\n            })\n          } finally {\n            finishBuilding()\n          }\n        })\n    },\n    close() {\n      for (const wsClient of clients) {\n        // it's okay to not cleanly close these websocket connections, this is dev\n        wsClient.terminate()\n      }\n      clients.clear()\n    },\n  }\n\n  handleEntrypointsSubscription().catch((err) => {\n    console.error(err)\n    process.exit(1)\n  })\n\n  // Write empty manifests\n  await currentEntriesHandling\n  await manifestLoader.writeManifests({\n    devRewrites: opts.fsChecker.rewrites,\n    productionRewrites: undefined,\n    entrypoints: currentEntrypoints,\n  })\n\n  async function handleProjectUpdates() {\n    for await (const updateMessage of project.updateInfoSubscribe(30)) {\n      switch (updateMessage.updateType) {\n        case 'start': {\n          hotReloader.send({ action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING })\n          break\n        }\n        case 'end': {\n          sendEnqueuedMessages()\n\n          function addErrors(\n            errorsMap: Map<string, CompilationError>,\n            issues: EntryIssuesMap\n          ) {\n            for (const issueMap of issues.values()) {\n              for (const [key, issue] of issueMap) {\n                if (issue.severity === 'warning') continue\n                if (errorsMap.has(key)) continue\n\n                const message = formatIssue(issue)\n\n                errorsMap.set(key, {\n                  message,\n                  details: issue.detail\n                    ? renderStyledStringToErrorAnsi(issue.detail)\n                    : undefined,\n                })\n              }\n            }\n          }\n\n          const errors = new Map<string, CompilationError>()\n          addErrors(errors, currentEntryIssues)\n\n          for (const client of clients) {\n            const state = clientStates.get(client)\n            if (!state) {\n              continue\n            }\n\n            const clientErrors = new Map(errors)\n            addErrors(clientErrors, state.clientIssues)\n\n            sendToClient(client, {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT,\n              hash: String(++hmrHash),\n              errors: [...clientErrors.values()],\n              warnings: [],\n            })\n          }\n\n          if (hmrEventHappened) {\n            const time = updateMessage.value.duration\n            const timeMessage =\n              time > 2000 ? `${Math.round(time / 100) / 10}s` : `${time}ms`\n            Log.event(`Compiled in ${timeMessage}`)\n            hmrEventHappened = false\n          }\n          break\n        }\n        default:\n      }\n    }\n  }\n\n  handleProjectUpdates().catch((err) => {\n    console.error(err)\n    process.exit(1)\n  })\n\n  return hotReloader\n}\n"], "names": ["createHotReloaderTurbopack", "wsServer", "ws", "Server", "noServer", "isTestMode", "process", "env", "NEXT_TEST_MODE", "__NEXT_TEST_MODE", "DEBUG", "sessionId", "Math", "floor", "Number", "MAX_SAFE_INTEGER", "random", "rewriteTurbopackSources", "projectRoot", "sourceMap", "section", "sections", "map", "i", "sources", "length", "pathToFileURL", "join", "replace", "toString", "getSourceMapFromTurbopack", "project", "sourceURL", "sourceMapJson", "getSourceMapSync", "err", "undefined", "payload", "JSON", "parse", "opts", "serverFields", "distDir", "resetFetch", "nextConfig", "dev", "buildId", "dir", "projectPath", "loadBindings", "require", "bindings", "TURBOPACK", "log", "testMode", "hasRewrites", "fs<PERSON><PERSON><PERSON>", "rewrites", "afterFiles", "beforeFiles", "fallback", "hotReloaderSpan", "trace", "version", "__NEXT_VERSION", "stop", "<PERSON><PERSON><PERSON>", "generateEncryptionKeyBase64", "isBuild", "clientRouterFilters", "experimental", "clientRouterFilter", "supportedBrowsers", "turbo", "createProject", "rootPath", "root", "outputFileTracingRoot", "jsConfig", "getTurbopackJsConfig", "watch", "enable", "pollIntervalMs", "watchOptions", "defineEnv", "createDefineEnv", "isTurbopack", "config", "fetchCacheKeyPrefix", "middlewareMatchers", "previewProps", "prerenderManifest", "preview", "browserslistQuery", "noMangling", "persistentCaching", "isPersistentCachingEnabled", "memoryLimit", "setBundlerFindSourceMapImplementation", "bind", "onDevServerCleanup", "onExit", "entrypointsSubscription", "entrypointsSubscribe", "currentWrittenEntrypoints", "Map", "currentEntrypoints", "global", "app", "document", "error", "middleware", "instrumentation", "page", "currentTopLevelIssues", "currentEntryIssues", "manifest<PERSON><PERSON>der", "TurbopackManifestLoader", "changeSubscriptions", "serverPathState", "readyIds", "Set", "currentEntriesHandlingResolve", "currentEntriesHandling", "Promise", "resolve", "assetMapper", "AssetMapper", "clearRequireCache", "key", "writtenEndpoint", "force", "path", "contentHash", "serverPaths", "set", "hasChange", "endsWith", "localKey", "localHash", "get", "globalHash", "hasAppPaths", "some", "p", "startsWith", "deleteAppClientCache", "file", "clearModuleContext", "deleteCache", "buildingIds", "startBuilding", "id", "requestUrl", "forceRebuild", "has", "size", "consoleStore", "setState", "loading", "trigger", "url", "add", "finishBuilding", "delete", "hmrEventHappened", "hmrHash", "clients", "clientStates", "WeakMap", "sendToClient", "client", "send", "stringify", "sendEnqueuedMessages", "issueMap", "values", "filter", "severity", "state", "clientIssues", "hmrPayloads", "clear", "turbopackUpdates", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "TURBOPACK_MESSAGE", "data", "sendEnqueuedMessagesDebounce", "debounce", "sendHmr", "sendTurbopackMessage", "diagnostics", "issues", "push", "subscribeToChanges", "includeIssues", "endpoint", "makePayload", "onError", "side", "splitEntryKey", "changedPromise", "changed", "change", "processIssues", "String", "e", "unsubscribeFromChanges", "subscription", "return", "subscribeToHmrEvents", "getEntry<PERSON>ey", "hasEntrypointForKey", "subscriptions", "hmrEvents", "next", "type", "reloadAction", "RELOAD_PAGE", "close", "unsubscribeFromHmrEvents", "handleEntrypointsSubscription", "entrypoints", "processTopLevelIssues", "handleEntrypoints", "devRewrites", "productionRewrites", "logErrors", "hooks", "handleWrittenEndpoint", "result", "propagateServerField", "mkdir", "recursive", "writeFile", "middlewares", "getOverlayMiddleware", "getSourceMapMiddleware", "getNextErrorFeedbackMiddleware", "telemetry", "getDevOverlayFontMiddleware", "getDisableDevIndicatorMiddleware", "versionInfoPromise", "getVersionInfo", "devtoolsFrontendUrl", "nodeDebugType", "getNodeDebugType", "debugPort", "debugInfo", "debugInfoList", "fetch", "then", "res", "json", "hotReloader", "turbopackProject", "activeWebpackConfigs", "serverStats", "edgeServerStats", "run", "req", "_parsedUrl", "params", "matchNextPageBundleRequest", "decodedPagePath", "param", "decodeURIComponent", "denormalizedPagePath", "denormalizePagePath", "ensurePage", "clientOnly", "definition", "catch", "console", "calledNext", "finished", "onHMR", "socket", "head", "onUpgrade", "handleUpgrade", "on", "addEventListener", "parsedData", "event", "manualTraceChild", "spanName", "msToNs", "startTime", "endTime", "attributes", "updatedModules", "isPageHidden", "hadRuntimeError", "dependency<PERSON><PERSON>n", "Log", "warn", "FAST_REFRESH_RUNTIME_RELOAD", "Array", "isArray", "cleanedModulePath", "Error", "turbopackConnected", "TURBOPACK_CONNECTED", "errors", "entryIssues", "issue", "message", "formatIssue", "printNonFatalIssue", "devIndicatorServerState", "disabledUntil", "Date", "now", "versionInfo", "sync", "SYNC", "warnings", "hash", "debug", "devIndicator", "setHmrServerError", "_error", "clearHmrServerError", "start", "getCompilationErrors", "appEntry<PERSON>ey", "pagesEntry<PERSON>ey", "topLevelIssues", "thisEntryIssues", "formattedIssue", "isWellKnownError", "invalidate", "reloadAfterInvalidation", "entrypoint", "clearAllModuleContexts", "SERVER_COMPONENT_CHANGES", "buildFallbackError", "inputPage", "appPaths", "isApp", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "BLOCKED_PAGES", "includes", "routeDef", "findPagePathData", "pageExtensions", "pagesDir", "appDir", "isAppPageRouteDefinition", "normalizedPage", "normalizeAppPath", "matchingAppPaths", "pathname", "handlePagesErrorRoute", "setPathsFor<PERSON>ey", "clientPaths", "isInsideAppDir", "bundlePath", "isEntryMetadataRouteFile", "isMetadataRouteFile", "filename", "normalizedAppPage", "normalizedPageToTurbopackStructureRoute", "extname", "route", "PageNotFoundError", "handleRouteType", "wsClient", "terminate", "exit", "writeManifests", "handleProjectUpdates", "updateMessage", "updateInfoSubscribe", "updateType", "BUILDING", "addErrors", "errorsMap", "details", "detail", "renderStyledStringToErrorAnsi", "clientErrors", "BUILT", "time", "value", "duration", "timeMessage", "round"], "mappings": ";;;;+BA2JsBA;;;eAAAA;;;0BA1JW;sBACH;qBACA;2DAEf;uBAGuB;kCASM;qBASZ;6DACX;oCAId;2BACuB;qCAIvB;uBAC2B;wBACT;8BACyB;8BAI3C;qCAC6B;uBACd;gCAgBf;iCAKA;gCACiC;sCACP;0BAM1B;0BACqC;uCACA;wCACH;0BACR;wBACA;iCACG;mCAI7B;gDACwC;wBAUxC;6CACqC;yCACJ;wCACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACjD,2DAA2D;AAE3D,MAAMC,WAAW,IAAIC,WAAE,CAACC,MAAM,CAAC;IAAEC,UAAU;AAAK;AAChD,MAAMC,aAAa,CAAC,CAClBC,CAAAA,QAAQC,GAAG,CAACC,cAAc,IAC1BF,QAAQC,GAAG,CAACE,gBAAgB,IAC5BH,QAAQC,GAAG,CAACG,KAAK,AAAD;AAGlB,MAAMC,YAAYC,KAAKC,KAAK,CAACC,OAAOC,gBAAgB,GAAGH,KAAKI,MAAM;AAElE;;CAEC,GACD,SAASC,wBACPC,WAAmB,EACnBC,SAAiC;IAEjC,IAAI,cAAcA,WAAW;QAC3B,KAAK,MAAMC,WAAWD,UAAUE,QAAQ,CAAE;YACxCJ,wBAAwBC,aAAaE,QAAQE,GAAG;QAClD;IACF,OAAO;QACL,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,UAAUK,OAAO,CAACC,MAAM,EAAEF,IAAK;YACjDJ,UAAUK,OAAO,CAACD,EAAE,GAAGG,IAAAA,kBAAa,EAClCC,IAAAA,UAAI,EACFT,aACAC,UAAUK,OAAO,CAACD,EAAE,CAACK,OAAO,CAAC,+BAA+B,MAE9DC,QAAQ;QACZ;IACF;AACF;AAEA,SAASC,0BACPC,OAAgB,EAChBb,WAAmB,EACnBc,SAAiB;IAEjB,IAAIC,gBAA+B;IAEnC,IAAI;QACFA,gBAAgBF,QAAQG,gBAAgB,CAACF;IAC3C,EAAE,OAAOG,KAAK,CAAC;IAEf,IAAIF,kBAAkB,MAAM;QAC1B,OAAOG;IACT,OAAO;QACL,MAAMC,UAAkCC,KAAKC,KAAK,CAACN;QACnD,2EAA2E;QAC3E,4EAA4E;QAC5EhB,wBAAwBC,aAAamB;QACrC,OAAOA;IACT;AACF;AAEO,eAAerC,2BACpBwC,IAAe,EACfC,YAA0B,EAC1BC,OAAe,EACfC,UAAsB;QAoDhBH,qCAQgBI,0BAuBLJ;IAjFjB,MAAMK,MAAM;IACZ,MAAMC,UAAU;IAChB,MAAM,EAAEF,UAAU,EAAEG,KAAKC,WAAW,EAAE,GAAGR;IAEzC,MAAM,EAAES,YAAY,EAAE,GACpBC,QAAQ;IAEV,IAAIC,WAAW,MAAMF;IAErB,iGAAiG;IACjG,yGAAyG;IACzG,IAAI3C,QAAQC,GAAG,CAAC6C,SAAS,IAAI/C,YAAY;QACvC6C,QAAQ,WAAWG,GAAG,CAAC,8BAA8B;YACnDN,KAAKC;YACLM,UAAUjD;QACZ;IACF;IAEA,MAAMkD,cACJf,KAAKgB,SAAS,CAACC,QAAQ,CAACC,UAAU,CAACjC,MAAM,GAAG,KAC5Ce,KAAKgB,SAAS,CAACC,QAAQ,CAACE,WAAW,CAAClC,MAAM,GAAG,KAC7Ce,KAAKgB,SAAS,CAACC,QAAQ,CAACG,QAAQ,CAACnC,MAAM,GAAG;IAE5C,MAAMoC,kBAAkBC,IAAAA,YAAK,EAAC,gBAAgB1B,WAAW;QACvD2B,SAASzD,QAAQC,GAAG,CAACyD,cAAc;IACrC;IACA,8FAA8F;IAC9F,wCAAwC;IACxCH,gBAAgBI,IAAI;IAEpB,MAAMC,gBAAgB,MAAMC,IAAAA,kDAA2B,EAAC;QACtDC,SAAS;QACT1B;IACF;IAEA,kBAAkB;IAClB,IAAI2B;IACJ,IAAIzB,WAAW0B,YAAY,CAACC,kBAAkB,EAAE;IAC9C,oEAAoE;IACtE;IAEA,iEAAiE;IACjE,MAAMC,oBAAoB;QACxB;KACD;IAED,MAAMzC,UAAU,MAAMoB,SAASsB,KAAK,CAACC,aAAa,CAChD;QACE1B,aAAaA;QACb2B,UACEnC,EAAAA,sCAAAA,KAAKI,UAAU,CAAC0B,YAAY,CAACG,KAAK,qBAAlCjC,oCAAoCoC,IAAI,KACxCpC,KAAKI,UAAU,CAACiC,qBAAqB,IACrC7B;QACFN;QACAE,YAAYJ,KAAKI,UAAU;QAC3BkC,UAAU,MAAMC,IAAAA,4BAAoB,EAAC/B,aAAaJ;QAClDoC,OAAO;YACLC,QAAQpC;YACRqC,cAAc,GAAEtC,2BAAAA,WAAWuC,YAAY,qBAAvBvC,yBAAyBsC,cAAc;QACzD;QACArC;QACAtC,KAAKD,QAAQC,GAAG;QAChB6E,WAAWC,IAAAA,oBAAe,EAAC;YACzBC,aAAa;YACbjB;YACAkB,QAAQ3C;YACRC;YACAH;YACA8C,qBAAqBhD,KAAKI,UAAU,CAAC0B,YAAY,CAACkB,mBAAmB;YACrEjC;YACA,kBAAkB;YAClBkC,oBAAoBrD;QACtB;QACAU;QACAoB;QACAwB,cAAclD,KAAKgB,SAAS,CAACmC,iBAAiB,CAACC,OAAO;QACtDC,mBAAmBrB,kBAAkB7C,IAAI,CAAC;QAC1CmE,YAAY;IACd,GACA;QACEC,mBAAmBC,IAAAA,kCAA0B,EAACxD,KAAKI,UAAU;QAC7DqD,WAAW,GAAEzD,uCAAAA,KAAKI,UAAU,CAAC0B,YAAY,CAACG,KAAK,qBAAlCjC,qCAAoCyD,WAAW;IAC9D;IAEFC,IAAAA,wDAAqC,EACnCpE,0BAA0BqE,IAAI,CAAC,MAAMpE,SAASiB;IAEhDR,KAAK4D,kBAAkB,oBAAvB5D,KAAK4D,kBAAkB,MAAvB5D,MAA0B;QACxB0D,IAAAA,wDAAqC,EAAC,IAAM9D;QAC5C,MAAML,QAAQsE,MAAM;IACtB;IACA,MAAMC,0BAA0BvE,QAAQwE,oBAAoB;IAE5D,MAAMC,4BAA4D,IAAIC;IACtE,MAAMC,qBAAkC;QACtCC,QAAQ;YACNC,KAAKxE;YACLyE,UAAUzE;YACV0E,OAAO1E;YAEP2E,YAAY3E;YACZ4E,iBAAiB5E;QACnB;QAEA6E,MAAM,IAAIR;QACVG,KAAK,IAAIH;IACX;IAEA,MAAMS,wBAA2C,IAAIT;IACrD,MAAMU,qBAAqC,IAAIV;IAE/C,MAAMW,iBAAiB,IAAIC,uCAAuB,CAAC;QACjDvE;QACAJ;QACAwB;IACF;IAEA,eAAe;IACf,MAAMoD,sBAA2C,IAAIb;IACrD,MAAMc,kBAAkB,IAAId;IAC5B,MAAMe,WAAqB,IAAIC;IAC/B,IAAIC;IACJ,IAAIC,yBAAyB,IAAIC,QAC/B,CAACC,UAAaH,gCAAgCG;IAGhD,MAAMC,cAAc,IAAIC,2BAAW;IAEnC,SAASC,kBACPC,GAAa,EACbC,eAAgC,EAChC,EACEC,KAAK,EAIN,GAAG,CAAC,CAAC;QAEN,IAAIA,OAAO;YACT,KAAK,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE,IAAIH,gBAAgBI,WAAW,CAAE;gBAC/Df,gBAAgBgB,GAAG,CAACH,MAAMC;YAC5B;QACF,OAAO;YACL,8CAA8C;YAC9C,IAAIG,YAAY;YAChB,KAAK,MAAM,EAAEJ,IAAI,EAAEC,WAAW,EAAE,IAAIH,gBAAgBI,WAAW,CAAE;gBAC/D,wBAAwB;gBACxB,IAAIF,KAAKK,QAAQ,CAAC,SAAS;gBAC3B,MAAMC,WAAW,GAAGT,IAAI,CAAC,EAAEG,MAAM;gBACjC,MAAMO,YAAYpB,gBAAgBqB,GAAG,CAACF;gBACtC,MAAMG,aAAatB,gBAAgBqB,GAAG,CAACR;gBACvC,IACE,AAACO,aAAaA,cAAcN,eAC3BQ,cAAcA,eAAeR,aAC9B;oBACAG,YAAY;oBACZjB,gBAAgBgB,GAAG,CAACN,KAAKI;oBACzBd,gBAAgBgB,GAAG,CAACH,MAAMC;gBAC5B,OAAO;oBACL,IAAI,CAACM,WAAW;wBACdpB,gBAAgBgB,GAAG,CAACN,KAAKI;oBAC3B;oBACA,IAAI,CAACQ,YAAY;wBACftB,gBAAgBgB,GAAG,CAACH,MAAMC;oBAC5B;gBACF;YACF;YAEA,IAAI,CAACG,WAAW;gBACd;YACF;QACF;QAEA7F;QAEA,MAAMmG,cAAcZ,gBAAgBI,WAAW,CAACS,IAAI,CAAC,CAAC,EAAEX,MAAMY,CAAC,EAAE,GAC/DA,EAAEC,UAAU,CAAC;QAGf,IAAIH,aAAa;YACfI,IAAAA,kCAAoB;QACtB;QAEA,MAAMZ,cAAcJ,gBAAgBI,WAAW,CAAChH,GAAG,CAAC,CAAC,EAAE8G,MAAMY,CAAC,EAAE,GAC9DrH,IAAAA,UAAI,EAACe,SAASsG;QAGhB,KAAK,MAAMG,QAAQb,YAAa;YAC9Bc,IAAAA,gCAAkB,EAACD;YACnBE,IAAAA,yBAAW,EAACF;QACd;QAEA;IACF;IAEA,MAAMG,cAAc,IAAI7B;IAExB,MAAM8B,gBAA+B,CAACC,IAAIC,YAAYC;QACpD,IAAI,CAACA,gBAAgBlC,SAASmC,GAAG,CAACH,KAAK;YACrC,OAAO,KAAO;QAChB;QACA,IAAIF,YAAYM,IAAI,KAAK,GAAG;YAC1BC,YAAY,CAACC,QAAQ,CACnB;gBACEC,SAAS;gBACTC,SAASR;gBACTS,KAAKR;YACP,GACA;QAEJ;QACAH,YAAYY,GAAG,CAACV;QAChB,OAAO,SAASW;YACd,IAAIb,YAAYM,IAAI,KAAK,GAAG;gBAC1B;YACF;YACApC,SAAS0C,GAAG,CAACV;YACbF,YAAYc,MAAM,CAACZ;YACnB,IAAIF,YAAYM,IAAI,KAAK,GAAG;gBAC1BS,mBAAmB;gBACnBR,YAAY,CAACC,QAAQ,CACnB;oBACEC,SAAS;gBACX,GACA;YAEJ;QACF;IACF;IAEA,IAAIM,mBAAmB;IACvB,IAAIC,UAAU;IAEd,MAAMC,UAAU,IAAI9C;IACpB,MAAM+C,eAAe,IAAIC;IAEzB,SAASC,aAAaC,MAAU,EAAEtI,OAAyB;QACzDsI,OAAOC,IAAI,CAACtI,KAAKuI,SAAS,CAACxI;IAC7B;IAEA,SAASyI;QACP,KAAK,MAAM,GAAGC,SAAS,IAAI5D,mBAAoB;YAC7C,IACE;mBAAI4D,SAASC,MAAM;aAAG,CAACC,MAAM,CAAC,CAAC1J,IAAMA,EAAE2J,QAAQ,KAAK,WAAWzJ,MAAM,GACrE,GACA;gBACA,mFAAmF;gBACnF;YACF;QACF;QAEA,KAAK,MAAMkJ,UAAUJ,QAAS;YAC5B,MAAMY,QAAQX,aAAa5B,GAAG,CAAC+B;YAC/B,IAAI,CAACQ,OAAO;gBACV;YACF;YAEA,KAAK,MAAM,GAAGJ,SAAS,IAAII,MAAMC,YAAY,CAAE;gBAC7C,IACE;uBAAIL,SAASC,MAAM;iBAAG,CAACC,MAAM,CAAC,CAAC1J,IAAMA,EAAE2J,QAAQ,KAAK,WACjDzJ,MAAM,GAAG,GACZ;oBACA,mFAAmF;oBACnF;gBACF;YACF;YAEA,KAAK,MAAMY,WAAW8I,MAAME,WAAW,CAACL,MAAM,GAAI;gBAChDN,aAAaC,QAAQtI;YACvB;YACA8I,MAAME,WAAW,CAACC,KAAK;YAEvB,IAAIH,MAAMI,gBAAgB,CAAC9J,MAAM,GAAG,GAAG;gBACrCiJ,aAAaC,QAAQ;oBACnBa,QAAQC,6CAA2B,CAACC,iBAAiB;oBACrDC,MAAMR,MAAMI,gBAAgB;gBAC9B;gBACAJ,MAAMI,gBAAgB,CAAC9J,MAAM,GAAG;YAClC;QACF;IACF;IACA,MAAMmK,+BAA+BC,IAAAA,gBAAQ,EAACf,sBAAsB;IAEpE,MAAMgB,UAAmB,CAACtC,IAAYnH;QACpC,KAAK,MAAMsI,UAAUJ,QAAS;gBAC5BC;aAAAA,oBAAAA,aAAa5B,GAAG,CAAC+B,4BAAjBH,kBAA0Ba,WAAW,CAAC9C,GAAG,CAACiB,IAAInH;QAChD;QAEAgI,mBAAmB;QACnBuB;IACF;IAEA,SAASG,qBAAqB1J,OAAwB;QACpD,kGAAkG;QAClG,mCAAmC;QACnC,iGAAiG;QACjGA,QAAQ2J,WAAW,GAAG,EAAE;QACxB3J,QAAQ4J,MAAM,GAAG,EAAE;QAEnB,KAAK,MAAMtB,UAAUJ,QAAS;gBAC5BC;aAAAA,oBAAAA,aAAa5B,GAAG,CAAC+B,4BAAjBH,kBAA0Be,gBAAgB,CAACW,IAAI,CAAC7J;QAClD;QAEAgI,mBAAmB;QACnBuB;IACF;IAEA,eAAeO,mBACblE,GAAa,EACbmE,aAAsB,EACtBC,QAAkB,EAClBC,WAGwD,EACxDC,OAEwD;QAExD,IAAIjF,oBAAoBqC,GAAG,CAAC1B,MAAM;YAChC;QACF;QAEA,MAAM,EAAEuE,IAAI,EAAE,GAAGC,IAAAA,uBAAa,EAACxE;QAE/B,MAAMyE,iBAAiBL,QAAQ,CAAC,GAAGG,KAAK,OAAO,CAAC,CAAC,CAACJ;QAClD9E,oBAAoBiB,GAAG,CAACN,KAAKyE;QAC7B,IAAI;YACF,MAAMC,UAAU,MAAMD;YAEtB,WAAW,MAAME,UAAUD,QAAS;gBAClCE,IAAAA,qBAAa,EAAC1F,oBAAoBc,KAAK2E,QAAQ,OAAO;gBACtD,mDAAmD;gBACnD,MAAMvK,UAAU,MAAMiK,YAAYM,QAAQE,OAAO,EAAExC;gBACnD,IAAIjI,SAAS;oBACXyJ,QAAQ7D,KAAK5F;gBACf;YACF;QACF,EAAE,OAAO0K,GAAG;YACVzF,oBAAoB8C,MAAM,CAACnC;YAC3B,MAAM5F,UAAU,OAAMkK,2BAAAA,QAAUQ;YAChC,IAAI1K,SAAS;gBACXyJ,QAAQ7D,KAAK5F;YACf;YACA;QACF;QACAiF,oBAAoB8C,MAAM,CAACnC;IAC7B;IAEA,eAAe+E,uBAAuB/E,GAAa;QACjD,MAAMgF,eAAe,MAAM3F,oBAAoBsB,GAAG,CAACX;QACnD,IAAIgF,cAAc;YAChB,OAAMA,aAAaC,MAAM,oBAAnBD,aAAaC,MAAM,MAAnBD;YACN3F,oBAAoB8C,MAAM,CAACnC;QAC7B;QACAd,mBAAmBiD,MAAM,CAACnC;IAC5B;IAEA,eAAekF,qBAAqBxC,MAAU,EAAEnB,EAAU;QACxD,MAAMvB,MAAMmF,IAAAA,qBAAW,EAAC,UAAU,UAAU5D;QAC5C,IAAI,CAAC6D,IAAAA,mCAAmB,EAAC3G,oBAAoBuB,KAAKH,cAAc;YAC9D,qDAAqD;YACrD;QACF;QAEA,MAAMqD,QAAQX,aAAa5B,GAAG,CAAC+B;QAC/B,IAAI,CAACQ,SAASA,MAAMmC,aAAa,CAAC3D,GAAG,CAACH,KAAK;YACzC;QACF;QAEA,MAAMyD,eAAelL,QAASwL,SAAS,CAAC/D;QACxC2B,MAAMmC,aAAa,CAAC/E,GAAG,CAACiB,IAAIyD;QAE5B,+DAA+D;QAC/D,oDAAoD;QACpD,IAAI;YACF,MAAMA,aAAaO,IAAI;YAEvB,WAAW,MAAM7B,QAAQsB,aAAc;gBACrCJ,IAAAA,qBAAa,EAAC1B,MAAMC,YAAY,EAAEnD,KAAK0D,MAAM,OAAO;gBACpD,IAAIA,KAAK8B,IAAI,KAAK,UAAU;oBAC1B1B,qBAAqBJ;gBACvB;YACF;QACF,EAAE,OAAOoB,GAAG;YACV,6EAA6E;YAC7E,8DAA8D;YAC9D,sEAAsE;YACtE,2CAA2C;YAC3C,MAAMW,eAAiC;gBACrClC,QAAQC,6CAA2B,CAACkC,WAAW;gBAC/ChC,MAAM,CAAC,oCAAoC,EAAEnC,GAAG,EAAE,EAAEuD,GAAG;YACzD;YACArC,aAAaC,QAAQ+C;YACrB/C,OAAOiD,KAAK;YACZ;QACF;IACF;IAEA,SAASC,yBAAyBlD,MAAU,EAAEnB,EAAU;QACtD,MAAM2B,QAAQX,aAAa5B,GAAG,CAAC+B;QAC/B,IAAI,CAACQ,OAAO;YACV;QACF;QAEA,MAAM8B,eAAe9B,MAAMmC,aAAa,CAAC1E,GAAG,CAACY;QAC7CyD,gCAAAA,aAAcC,MAAM;QAEpB,MAAMjF,MAAMmF,IAAAA,qBAAW,EAAC,UAAU,UAAU5D;QAC5C2B,MAAMC,YAAY,CAAChB,MAAM,CAACnC;IAC5B;IAEA,eAAe6F;QACb,WAAW,MAAMC,eAAezH,wBAAyB;YACvD,IAAI,CAACoB,+BAA+B;gBAClCC,yBAAyB,IAAIC,QAC3B,wCAAwC;gBACxC,CAACC,UAAaH,gCAAgCG;YAElD;YAEAmG,IAAAA,qCAAqB,EAAC9G,uBAAuB6G;YAE7C,MAAME,IAAAA,iCAAiB,EAAC;gBACtBF;gBAEArH;gBAEAS;gBACAC;gBACA8G,aAAa1L,KAAKgB,SAAS,CAACC,QAAQ;gBACpC0K,oBAAoB/L;gBACpBgM,WAAW;gBAEXvL,KAAK;oBACHiF;oBACAR;oBACAiD;oBACAC;oBACA/H;oBAEA4L,OAAO;wBACLC,uBAAuB,CAAC9E,IAAI+E;4BAC1B/H,0BAA0B+B,GAAG,CAACiB,IAAI+E;4BAClCvG,kBAAkBwB,IAAI+E;wBACxB;wBACAC,sBAAsBA,qCAAoB,CAACrI,IAAI,CAAC,MAAM3D;wBACtDsJ;wBACAvC;wBACA4C;wBACAa;wBACAa;oBACF;gBACF;YACF;YAEAnG;YACAA,gCAAgCtF;QAClC;IACF;IAEA,MAAMqM,IAAAA,eAAK,EAAC9M,IAAAA,UAAI,EAACe,SAAS,WAAW;QAAEgM,WAAW;IAAK;IACvD,MAAMD,IAAAA,eAAK,EAAC9M,IAAAA,UAAI,EAACe,SAAS,UAAUI,UAAU;QAAE4L,WAAW;IAAK;IAChE,MAAMC,IAAAA,mBAAS,EACbhN,IAAAA,UAAI,EAACe,SAAS,iBACdJ,KAAKuI,SAAS,CACZ;QACE4C,MAAM;IACR,GACA,MACA;IAIJ,MAAMmB,cAAc;QAClBC,IAAAA,yCAAoB,EAAC9M,SAASiB;QAC9B8L,IAAAA,2CAAsB,EAAC/M;QACvBgN,IAAAA,8DAA8B,EAACvM,KAAKwM,SAAS;QAC7CC,IAAAA,wDAA2B;QAC3BC,IAAAA,wDAAgC;KACjC;IAED,MAAMC,qBAAqBC,IAAAA,kCAAc;IAEzC,IAAIC;IACJ,MAAMC,gBAAgBC,IAAAA,wBAAgB;IACtC,IAAID,eAAe;QACjB,MAAME,YAAYlP,QAAQkP,SAAS;QACnC,IAAIC;QACJ,IAAI;YACF,8EAA8E;YAC9E,MAAMC,gBAAgB,MAAMC,MAC1B,CAAC,iBAAiB,EAAEH,UAAU,UAAU,CAAC,EACzCI,IAAI,CAAC,CAACC,MAAQA,IAAIC,IAAI;YACxBL,YAAYC,aAAa,CAAC,EAAE;QAC9B,EAAE,OAAM,CAAC;QACT,IAAID,WAAW;YACbJ,sBAAsBI,UAAUJ,mBAAmB;QACrD;IACF;IAEA,MAAMU,cAA0C;QAC9CC,kBAAkBjO;QAClBkO,sBAAsB7N;QACtB8N,aAAa;QACbC,iBAAiB;QACjB,MAAMC,KAAIC,GAAG,EAAER,GAAG,EAAES,UAAU;gBAExBD;YADJ,+DAA+D;YAC/D,KAAIA,WAAAA,IAAIpG,GAAG,qBAAPoG,SAASpH,UAAU,CAAC,gCAAgC;gBACtD,MAAMsH,SAASC,IAAAA,8CAA0B,EAACH,IAAIpG,GAAG;gBAEjD,IAAIsG,QAAQ;oBACV,MAAME,kBAAkB,CAAC,CAAC,EAAEF,OAAOnI,IAAI,CACpC9G,GAAG,CAAC,CAACoP,QAAkBC,mBAAmBD,QAC1C/O,IAAI,CAAC,MAAM;oBAEd,MAAMiP,uBAAuBC,IAAAA,wCAAmB,EAACJ;oBAEjD,MAAMV,YACHe,UAAU,CAAC;wBACV7J,MAAM2J;wBACNG,YAAY;wBACZC,YAAY5O;wBACZ6H,KAAKoG,IAAIpG,GAAG;oBACd,GACCgH,KAAK,CAACC,QAAQpK,KAAK;gBACxB;YACF;YAEA,KAAK,MAAMC,cAAc6H,YAAa;gBACpC,IAAIuC,aAAa;gBAEjB,MAAMpK,WAAWsJ,KAAKR,KAAK;oBACzBsB,aAAa;gBACf;gBAEA,IAAI,CAACA,YAAY;oBACf,OAAO;wBAAEC,UAAU;oBAAK;gBAC1B;YACF;YAEA,4BAA4B;YAC5B,OAAO;gBAAEA,UAAUhP;YAAU;QAC/B;QAEA,2EAA2E;QAC3EiP,OAAMhB,GAAG,EAAEiB,MAAc,EAAEC,IAAI,EAAEC,SAAS;YACxCvR,SAASwR,aAAa,CAACpB,KAAKiB,QAAQC,MAAM,CAAC5G;gBACzC6G,UAAU7G;gBACV,MAAMS,eAA+B,IAAI3E;gBACzC,MAAM6G,gBAAiD,IAAI7G;gBAE3D8D,QAAQL,GAAG,CAACS;gBACZH,aAAajC,GAAG,CAACoC,QAAQ;oBACvBS;oBACAC,aAAa,IAAI5E;oBACjB8E,kBAAkB,EAAE;oBACpB+B;gBACF;gBAEA3C,OAAO+G,EAAE,CAAC,SAAS;oBACjB,8BAA8B;oBAC9B,KAAK,MAAMzE,gBAAgBK,cAActC,MAAM,GAAI;wBACjDiC,aAAaC,MAAM,oBAAnBD,aAAaC,MAAM,MAAnBD;oBACF;oBACAzC,aAAaJ,MAAM,CAACO;oBACpBJ,QAAQH,MAAM,CAACO;gBACjB;gBAEAA,OAAOgH,gBAAgB,CAAC,WAAW,CAAC,EAAEhG,IAAI,EAAE;oBAC1C,MAAMiG,aAAatP,KAAKC,KAAK,CAC3B,OAAOoJ,SAAS,WAAWA,KAAK9J,QAAQ,KAAK8J;oBAG/C,mBAAmB;oBACnB,OAAQiG,WAAWC,KAAK;wBACtB,KAAK;4BAAY;gCACfhO,gBAAgBiO,gBAAgB,CAC9BF,WAAWG,QAAQ,EACnBC,IAAAA,sBAAM,EAACJ,WAAWK,SAAS,GAC3BD,IAAAA,sBAAM,EAACJ,WAAWM,OAAO,GACzBN,WAAWO,UAAU;gCAEvB;4BACF;wBACA,KAAK;4BACHtO,gBAAgBiO,gBAAgB,CAC9BF,WAAWC,KAAK,EAChBG,IAAAA,sBAAM,EAACJ,WAAWK,SAAS,GAC3BD,IAAAA,sBAAM,EAACJ,WAAWM,OAAO,GACzB;gCACEE,gBAAgBR,WAAWQ,cAAc;gCACzCnL,MAAM2K,WAAW3K,IAAI;gCACrBoL,cAAcT,WAAWS,YAAY;4BACvC;4BAEF;wBACF,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BACH,MAAM,EAAEC,eAAe,EAAEC,eAAe,EAAE,GAAGX;4BAC7C,IAAIU,iBAAiB;gCACnBE,KAAIC,IAAI,CAACC,qCAA2B;4BACtC;4BACA,IACEC,MAAMC,OAAO,CAACL,oBACd,OAAOA,eAAe,CAAC,EAAE,KAAK,UAC9B;gCACA,MAAMM,oBAAoBN,eAAe,CAAC,EAAE,CACzC3Q,OAAO,CAAC,gBAAgB,KACxBA,OAAO,CAAC,mBAAmB;gCAC9B4Q,KAAIC,IAAI,CACN,CAAC,+CAA+C,EAAEI,kBAAkB,yEAAyE,CAAC;4BAElJ;4BACA;wBACF,KAAK;4BAEH;wBAEF;4BACE,kCAAkC;4BAClC,IAAI,CAACjB,WAAWnE,IAAI,EAAE;gCACpB,MAAM,qBAA+C,CAA/C,IAAIqF,MAAM,CAAC,0BAA0B,EAAEnH,KAAK,CAAC,CAAC,GAA9C,qBAAA;2CAAA;gDAAA;kDAAA;gCAA8C;4BACtD;oBACJ;oBAEA,qBAAqB;oBACrB,OAAQiG,WAAWnE,IAAI;wBACrB,KAAK;4BACHN,qBAAqBxC,QAAQiH,WAAWxJ,IAAI;4BAC5C;wBAEF,KAAK;4BACHyF,yBAAyBlD,QAAQiH,WAAWxJ,IAAI;4BAChD;wBAEF;4BACE,IAAI,CAACwJ,WAAWC,KAAK,EAAE;gCACrB,MAAM,qBAAyD,CAAzD,IAAIiB,MAAM,CAAC,oCAAoC,EAAEnH,KAAK,CAAC,CAAC,GAAxD,qBAAA;2CAAA;gDAAA;kDAAA;gCAAwD;4BAChE;oBACJ;gBACF;gBAEA,MAAMoH,qBAA+C;oBACnDvH,QAAQC,6CAA2B,CAACuH,mBAAmB;oBACvDrH,MAAM;wBAAEhL;oBAAU;gBACpB;gBACA+J,aAAaC,QAAQoI;gBAErB,MAAME,SAA6B,EAAE;gBAErC,KAAK,MAAMC,eAAe/L,mBAAmB6D,MAAM,GAAI;oBACrD,KAAK,MAAMmI,SAASD,YAAYlI,MAAM,GAAI;wBACxC,IAAImI,MAAMjI,QAAQ,KAAK,WAAW;4BAChC+H,OAAO/G,IAAI,CAAC;gCACVkH,SAASC,IAAAA,mBAAW,EAACF;4BACvB;wBACF,OAAO;4BACLG,IAAAA,kCAAkB,EAACH;wBACrB;oBACF;gBACF;gBAEA,IAAII,gDAAuB,CAACC,aAAa,GAAGC,KAAKC,GAAG,IAAI;oBACtDH,gDAAuB,CAACC,aAAa,GAAG;gBAC1C;;gBAEE,CAAA;oBACA,MAAMG,cAAc,MAAMxE;oBAE1B,MAAMyE,OAAmB;wBACvBpI,QAAQC,6CAA2B,CAACoI,IAAI;wBACxCZ;wBACAa,UAAU,EAAE;wBACZC,MAAM;wBACNJ;wBACAK,OAAO;4BACL3E;wBACF;wBACA4E,cAAcV,gDAAuB;oBACvC;oBAEA7I,aAAaC,QAAQiJ;gBACvB,CAAA;YACF;QACF;QAEAhJ,MAAKY,MAAM;YACT,MAAMnJ,UAAUC,KAAKuI,SAAS,CAACW;YAC/B,KAAK,MAAMb,UAAUJ,QAAS;gBAC5BI,OAAOC,IAAI,CAACvI;YACd;QACF;QAEA6R,mBAAkBC,MAAM;QACtB,uBAAuB;QACzB;QACAC;QACE,uBAAuB;QACzB;QACA,MAAMC,UAAS;QACf,MAAMC,sBAAqBrN,IAAI;YAC7B,MAAMsN,cAAcnH,IAAAA,qBAAW,EAAC,OAAO,UAAUnG;YACjD,MAAMuN,gBAAgBpH,IAAAA,qBAAW,EAAC,SAAS,UAAUnG;YAErD,MAAMwN,iBAAiBvN,sBAAsB8D,MAAM;YAEnD,MAAM0J,kBACJvN,mBAAmByB,GAAG,CAAC2L,gBACvBpN,mBAAmByB,GAAG,CAAC4L;YAEzB,IAAIE,oBAAoBtS,aAAasS,gBAAgB9K,IAAI,GAAG,GAAG;gBAC7D,+FAA+F;gBAC/F,OAAO;uBAAI6K;uBAAmBC,gBAAgB1J,MAAM;iBAAG,CACpD1J,GAAG,CAAC,CAAC6R;oBACJ,MAAMwB,iBAAiBtB,IAAAA,mBAAW,EAACF;oBACnC,IAAIA,MAAMjI,QAAQ,KAAK,WAAW;wBAChCoI,IAAAA,kCAAkB,EAACH;wBACnB,OAAO;oBACT,OAAO,IAAIyB,IAAAA,wBAAgB,EAACzB,QAAQ;wBAClCX,KAAI1L,KAAK,CAAC6N;oBACZ;oBAEA,OAAO,qBAAyB,CAAzB,IAAI7B,MAAM6B,iBAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwB;gBACjC,GACC1J,MAAM,CAAC,CAACnE,QAAUA,UAAU;YACjC;YAEA,4CAA4C;YAC5C,MAAMmM,SAAS,EAAE;YACjB,KAAK,MAAME,SAASsB,eAAgB;gBAClC,IAAItB,MAAMjI,QAAQ,KAAK,WAAW;oBAChC+H,OAAO/G,IAAI,CAAC,qBAA6B,CAA7B,IAAI4G,MAAMO,IAAAA,mBAAW,EAACF,SAAtB,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4B;gBAC1C;YACF;YACA,KAAK,MAAMD,eAAe/L,mBAAmB6D,MAAM,GAAI;gBACrD,KAAK,MAAMmI,SAASD,YAAYlI,MAAM,GAAI;oBACxC,IAAImI,MAAMjI,QAAQ,KAAK,WAAW;wBAChC,MAAMkI,UAAUC,IAAAA,mBAAW,EAACF;wBAC5BF,OAAO/G,IAAI,CAAC,qBAAkB,CAAlB,IAAI4G,MAAMM,UAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAiB;oBAC/B,OAAO;wBACLE,IAAAA,kCAAkB,EAACH;oBACrB;gBACF;YACF;YACA,OAAOF;QACT;QACA,MAAM4B,YAAW,EACf,yCAAyC;QACzCC,uBAAuB,EACxB;YACC,IAAIA,yBAAyB;gBAC3B,KAAK,MAAM,CAAC7M,KAAK8M,WAAW,IAAIvO,0BAA2B;oBACzDwB,kBAAkBC,KAAK8M,YAAY;wBAAE5M,OAAO;oBAAK;gBACnD;gBAEA,MAAM6M,IAAAA,oCAAsB;gBAC5B,IAAI,CAACpK,IAAI,CAAC;oBACRY,QAAQC,6CAA2B,CAACwJ,wBAAwB;oBAC5DlB,MAAMjH,OAAO,EAAExC;gBACjB;YACF;QACF;QACA,MAAM4K;QACJ,uBAAuB;QACzB;QACA,MAAMpE,YAAW,EACf7J,MAAMkO,SAAS,EACf,oBAAoB;QACpB,cAAc;QACdC,QAAQ,EACRpE,UAAU,EACVqE,KAAK,EACLpL,KAAKR,UAAU,EAChB;YACC,OAAO5F,gBACJyR,UAAU,CAAC,eAAe;gBACzBH;YACF,GACCI,YAAY,CAAC;gBACZ,IAAIC,wBAAa,CAACC,QAAQ,CAACN,cAAcA,cAAc,WAAW;oBAChE;gBACF;gBAEA,MAAMxN;gBAEN,qGAAqG;gBACrG,IAAI+N,WAIF1E,cACC,MAAM2E,IAAAA,sCAAgB,EACrB3S,aACAmS,WACAvS,WAAWgT,cAAc,EACzBpT,KAAKqT,QAAQ,EACbrT,KAAKsT,MAAM;gBAGf,yEAAyE;gBACzE,oEAAoE;gBACpE,IAAI,CAACV,YAAYpE,cAAc+E,IAAAA,gDAAwB,EAAC/E,aAAa;oBACnEoE,WAAWpE,WAAWoE,QAAQ;gBAChC;gBAEA,IAAInO,OAAOyO,SAASzO,IAAI;gBACxB,IAAImO,UAAU;oBACZ,MAAMY,iBAAiBC,IAAAA,0BAAgB,EAAChP;oBAExC,8DAA8D;oBAC9D,MAAMiP,mBAAmBd,SAASnK,MAAM,CACtC,CAAC7C,OAAS6N,IAAAA,0BAAgB,EAAC7N,UAAU4N;oBAGvC,4EAA4E;oBAC5E/O,OAAOiP,gBAAgB,CAACA,iBAAiBzU,MAAM,GAAG,EAAE;gBACtD;gBAEA,MAAM0U,WAAWnF,CAAAA,8BAAAA,WAAYmF,QAAQ,KAAIhB;gBAEzC,IAAIlO,SAAS,WAAW;oBACtB,IAAIkD,iBAAiBZ,cAAc4M,UAAU1M,YAAY;oBACzD,IAAI;wBACF,MAAM2M,IAAAA,qCAAqB,EAAC;4BAC1BjP;4BACA4G,aAAarH;4BACbU;4BACA8G,aAAa1L,KAAKgB,SAAS,CAACC,QAAQ;4BACpC0K,oBAAoB/L;4BACpBgM,WAAW;4BACXC,OAAO;gCACLlC;gCACAmC,uBAAuB,CAAC9E,IAAI+E;oCAC1BvG,kBAAkBwB,IAAI+E;oCACtB/H,0BAA0B+B,GAAG,CAACiB,IAAI+E;oCAClCzG,YAAYuO,cAAc,CAAC7M,IAAI+E,OAAO+H,WAAW;gCACnD;4BACF;wBACF;oBACF,SAAU;wBACRnM;oBACF;oBACA;gBACF;gBAEA,MAAMoM,iBAAiBb,SAASc,UAAU,CAACvN,UAAU,CAAC;gBACtD,MAAMwN,2BAA2BC,IAAAA,oCAAmB,EAClDhB,SAASiB,QAAQ,CAAC/U,OAAO,CAACY,KAAKsT,MAAM,IAAI,IAAI,KAC7ClT,WAAWgT,cAAc,EACzB;gBAEF,MAAMgB,oBAAoBH,2BACtBI,IAAAA,uDAAuC,EACrC5P,MACA6P,IAAAA,aAAO,EAACpB,SAASiB,QAAQ,KAE3B1P;gBAEJ,MAAM8P,QAAQR,iBACV7P,mBAAmBE,GAAG,CAACgC,GAAG,CAACgO,qBAC3BlQ,mBAAmBO,IAAI,CAAC2B,GAAG,CAAC3B;gBAEhC,IAAI,CAAC8P,OAAO;oBACV,gDAAgD;oBAChD,IAAI9P,SAAS,eAAe;oBAC5B,IAAIA,SAAS,mBAAmB;oBAChC,IAAIA,SAAS,oBAAoB;oBACjC,IAAIA,SAAS,wBAAwB;oBAErC,MAAM,IAAI+P,wBAAiB,CAAC,CAAC,gBAAgB,EAAE/P,MAAM;gBACvD;gBAEA,2DAA2D;gBAC3D,4CAA4C;gBAC5C,mCAAmC;gBACnC,IAAIoO,SAAS0B,MAAMtJ,IAAI,KAAK,QAAQ;oBAClC,MAAM,qBAA8D,CAA9D,IAAIqF,MAAM,CAAC,0CAA0C,EAAE7L,MAAM,GAA7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA6D;gBACrE;gBAEA,MAAMkD,iBAAiBZ,cAAc4M,UAAU1M,YAAY;gBAC3D,IAAI;oBACF,MAAMwN,IAAAA,+BAAe,EAAC;wBACpBpU;wBACAoE;wBACAkP;wBACAY;wBACA5P;wBACA4G,aAAarH;wBACbU;wBACAI;wBACA0G,aAAa1L,KAAKgB,SAAS,CAACC,QAAQ;wBACpC0K,oBAAoB/L;wBACpBgM,WAAW;wBAEXC,OAAO;4BACLlC;4BACAmC,uBAAuB,CAAC9E,IAAI+E;gCAC1B/H,0BAA0B+B,GAAG,CAACiB,IAAI+E;gCAClCvG,kBAAkBwB,IAAI+E;gCACtBzG,YAAYuO,cAAc,CAAC7M,IAAI+E,OAAO+H,WAAW;4BACnD;wBACF;oBACF;gBACF,SAAU;oBACRnM;gBACF;YACF;QACJ;QACAyD;YACE,KAAK,MAAMsJ,YAAY3M,QAAS;gBAC9B,0EAA0E;gBAC1E2M,SAASC,SAAS;YACpB;YACA5M,QAAQe,KAAK;QACf;IACF;IAEAwC,gCAAgCmD,KAAK,CAAC,CAAC9O;QACrC+O,QAAQpK,KAAK,CAAC3E;QACd7B,QAAQ8W,IAAI,CAAC;IACf;IAEA,wBAAwB;IACxB,MAAMzP;IACN,MAAMP,eAAeiQ,cAAc,CAAC;QAClCnJ,aAAa1L,KAAKgB,SAAS,CAACC,QAAQ;QACpC0K,oBAAoB/L;QACpB2L,aAAarH;IACf;IAEA,eAAe4Q;QACb,WAAW,MAAMC,iBAAiBxV,QAAQyV,mBAAmB,CAAC,IAAK;YACjE,OAAQD,cAAcE,UAAU;gBAC9B,KAAK;oBAAS;wBACZ1H,YAAYnF,IAAI,CAAC;4BAAEY,QAAQC,6CAA2B,CAACiM,QAAQ;wBAAC;wBAChE;oBACF;gBACA,KAAK;oBAAO;wBACV5M;wBAEA,SAAS6M,UACPC,SAAwC,EACxC3L,MAAsB;4BAEtB,KAAK,MAAMlB,YAAYkB,OAAOjB,MAAM,GAAI;gCACtC,KAAK,MAAM,CAAC/C,KAAKkL,MAAM,IAAIpI,SAAU;oCACnC,IAAIoI,MAAMjI,QAAQ,KAAK,WAAW;oCAClC,IAAI0M,UAAUjO,GAAG,CAAC1B,MAAM;oCAExB,MAAMmL,UAAUC,IAAAA,mBAAW,EAACF;oCAE5ByE,UAAUrP,GAAG,CAACN,KAAK;wCACjBmL;wCACAyE,SAAS1E,MAAM2E,MAAM,GACjBC,IAAAA,qCAA6B,EAAC5E,MAAM2E,MAAM,IAC1C1V;oCACN;gCACF;4BACF;wBACF;wBAEA,MAAM6Q,SAAS,IAAIxM;wBACnBkR,UAAU1E,QAAQ9L;wBAElB,KAAK,MAAMwD,UAAUJ,QAAS;4BAC5B,MAAMY,QAAQX,aAAa5B,GAAG,CAAC+B;4BAC/B,IAAI,CAACQ,OAAO;gCACV;4BACF;4BAEA,MAAM6M,eAAe,IAAIvR,IAAIwM;4BAC7B0E,UAAUK,cAAc7M,MAAMC,YAAY;4BAE1CV,aAAaC,QAAQ;gCACnBa,QAAQC,6CAA2B,CAACwM,KAAK;gCACzClE,MAAMjH,OAAO,EAAExC;gCACf2I,QAAQ;uCAAI+E,aAAahN,MAAM;iCAAG;gCAClC8I,UAAU,EAAE;4BACd;wBACF;wBAEA,IAAIzJ,kBAAkB;4BACpB,MAAM6N,OAAOX,cAAcY,KAAK,CAACC,QAAQ;4BACzC,MAAMC,cACJH,OAAO,OAAO,GAAGtX,KAAK0X,KAAK,CAACJ,OAAO,OAAO,GAAG,CAAC,CAAC,GAAG,GAAGA,KAAK,EAAE,CAAC;4BAC/D1F,KAAIX,KAAK,CAAC,CAAC,YAAY,EAAEwG,aAAa;4BACtChO,mBAAmB;wBACrB;wBACA;oBACF;gBACA;YACF;QACF;IACF;IAEAiN,uBAAuBrG,KAAK,CAAC,CAAC9O;QAC5B+O,QAAQpK,KAAK,CAAC3E;QACd7B,QAAQ8W,IAAI,CAAC;IACf;IAEA,OAAOrH;AACT"}