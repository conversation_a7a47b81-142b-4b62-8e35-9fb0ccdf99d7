{"version": 3, "sources": ["../../../src/server/dev/extract-modules-from-turbopack-message.ts"], "sourcesContent": ["import type { Update as TurbopackUpdate } from '../../build/swc/types'\n\nexport function extractModulesFromTurbopackMessage(\n  data: TurbopackUpdate | TurbopackUpdate[]\n): Set<string> {\n  const updatedModules: Set<string> = new Set()\n\n  const updates = Array.isArray(data) ? data : [data]\n  for (const update of updates) {\n    // TODO this won't capture changes to CSS since they don't result in a \"merged\" update\n    if (\n      update.type !== 'partial' ||\n      update.instruction.type !== 'ChunkListUpdate' ||\n      update.instruction.merged === undefined\n    ) {\n      continue\n    }\n\n    for (const mergedUpdate of update.instruction.merged) {\n      for (const name of Object.keys(mergedUpdate.entries)) {\n        const res = /(.*)\\s+\\[.*/.exec(name)\n        if (res === null) {\n          console.error(\n            '[Turbopack HMR] Expected module to match pattern: ' + name\n          )\n          continue\n        }\n\n        updatedModules.add(res[1])\n      }\n    }\n  }\n\n  return updatedModules\n}\n"], "names": ["extractModulesFromTurbopackMessage", "data", "updatedModules", "Set", "updates", "Array", "isArray", "update", "type", "instruction", "merged", "undefined", "mergedUpdate", "name", "Object", "keys", "entries", "res", "exec", "console", "error", "add"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,mCACdC,IAAyC;IAEzC,MAAMC,iBAA8B,IAAIC;IAExC,MAAMC,UAAUC,MAAMC,OAAO,CAACL,QAAQA,OAAO;QAACA;KAAK;IACnD,KAAK,MAAMM,UAAUH,QAAS;QAC5B,sFAAsF;QACtF,IACEG,OAAOC,IAAI,KAAK,aAChBD,OAAOE,WAAW,CAACD,IAAI,KAAK,qBAC5BD,OAAOE,WAAW,CAACC,MAAM,KAAKC,WAC9B;YACA;QACF;QAEA,KAAK,MAAMC,gBAAgBL,OAAOE,WAAW,CAACC,MAAM,CAAE;YACpD,KAAK,MAAMG,QAAQC,OAAOC,IAAI,CAACH,aAAaI,OAAO,EAAG;gBACpD,MAAMC,MAAM,cAAcC,IAAI,CAACL;gBAC/B,IAAII,QAAQ,MAAM;oBAChBE,QAAQC,KAAK,CACX,uDAAuDP;oBAEzD;gBACF;gBAEAX,eAAemB,GAAG,CAACJ,GAAG,CAAC,EAAE;YAC3B;QACF;IACF;IAEA,OAAOf;AACT"}