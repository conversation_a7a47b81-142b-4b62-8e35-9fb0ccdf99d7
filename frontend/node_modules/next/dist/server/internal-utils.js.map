{"version": 3, "sources": ["../../src/server/internal-utils.ts"], "sourcesContent": ["import type { NextParsedUrlQuery } from './request-meta'\n\nimport { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\n\nconst INTERNAL_QUERY_NAMES = [NEXT_RSC_UNION_QUERY] as const\n\nexport function stripInternalQueries(query: NextParsedUrlQuery) {\n  for (const name of INTERNAL_QUERY_NAMES) {\n    delete query[name]\n  }\n}\n\nexport function stripInternalSearchParams<T extends string | URL>(url: T): T {\n  const isStringUrl = typeof url === 'string'\n  const instance = isStringUrl ? new URL(url) : (url as URL)\n\n  instance.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  return (isStringUrl ? instance.toString() : instance) as T\n}\n"], "names": ["stripInternalQueries", "stripInternalSearchParams", "INTERNAL_QUERY_NAMES", "NEXT_RSC_UNION_QUERY", "query", "name", "url", "isStringUrl", "instance", "URL", "searchParams", "delete", "toString"], "mappings": ";;;;;;;;;;;;;;;IAMgBA,oBAAoB;eAApBA;;IAMAC,yBAAyB;eAAzBA;;;kCAVqB;AAErC,MAAMC,uBAAuB;IAACC,sCAAoB;CAAC;AAE5C,SAASH,qBAAqBI,KAAyB;IAC5D,KAAK,MAAMC,QAAQH,qBAAsB;QACvC,OAAOE,KAAK,CAACC,KAAK;IACpB;AACF;AAEO,SAASJ,0BAAkDK,GAAM;IACtE,MAAMC,cAAc,OAAOD,QAAQ;IACnC,MAAME,WAAWD,cAAc,IAAIE,IAAIH,OAAQA;IAE/CE,SAASE,YAAY,CAACC,MAAM,CAACR,sCAAoB;IAEjD,OAAQI,cAAcC,SAASI,QAAQ,KAAKJ;AAC9C"}