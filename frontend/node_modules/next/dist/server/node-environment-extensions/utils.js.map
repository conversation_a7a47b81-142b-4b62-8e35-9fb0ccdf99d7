{"version": 3, "sources": ["../../../src/server/node-environment-extensions/utils.tsx"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortOnSynchronousPlatformIOAccess,\n  trackSynchronousPlatformIOAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\ntype ApiType = 'time' | 'random' | 'crypto'\n\nexport function io(expression: string, type: ApiType) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender') {\n      const workStore = workAsyncStorage.getStore()\n      if (workStore) {\n        let message: string\n        switch (type) {\n          case 'time':\n            message = `Route \"${workStore.route}\" used ${expression} instead of using \\`performance\\` or without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-current-time`\n            break\n          case 'random':\n            message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-random`\n            break\n          case 'crypto':\n            message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-crypto`\n            break\n          default:\n            throw new InvariantError(\n              'Unknown expression type in abortOnSynchronousPlatformIOAccess.'\n            )\n        }\n        const errorWithStack = new Error(message)\n\n        abortOnSynchronousPlatformIOAccess(\n          workStore.route,\n          expression,\n          errorWithStack,\n          workUnitStore\n        )\n      }\n    } else if (\n      workUnitStore.type === 'request' &&\n      workUnitStore.prerenderPhase === true\n    ) {\n      const requestStore = workUnitStore\n      trackSynchronousPlatformIOAccessInDev(requestStore)\n    }\n  }\n}\n"], "names": ["io", "expression", "type", "workUnitStore", "workUnitAsyncStorage", "getStore", "workStore", "workAsyncStorage", "message", "route", "InvariantError", "errorWithStack", "Error", "abortOnSynchronousPlatformIOAccess", "prerenderPhase", "requestStore", "trackSynchronousPlatformIOAccessInDev"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;0CAViB;8CACI;kCAI9B;gCACwB;AAIxB,SAASA,GAAGC,UAAkB,EAAEC,IAAa;IAClD,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,IAAIA,cAAcD,IAAI,KAAK,aAAa;YACtC,MAAMI,YAAYC,0CAAgB,CAACF,QAAQ;YAC3C,IAAIC,WAAW;gBACb,IAAIE;gBACJ,OAAQN;oBACN,KAAK;wBACHM,UAAU,CAAC,OAAO,EAAEF,UAAUG,KAAK,CAAC,OAAO,EAAER,WAAW,mLAAmL,CAAC;wBAC5O;oBACF,KAAK;wBACHO,UAAU,CAAC,OAAO,EAAEF,UAAUG,KAAK,CAAC,OAAO,EAAER,WAAW,wKAAwK,CAAC;wBACjO;oBACF,KAAK;wBACHO,UAAU,CAAC,OAAO,EAAEF,UAAUG,KAAK,CAAC,OAAO,EAAER,WAAW,wKAAwK,CAAC;wBACjO;oBACF;wBACE,MAAM,qBAEL,CAFK,IAAIS,8BAAc,CACtB,mEADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;gBACJ;gBACA,MAAMC,iBAAiB,qBAAkB,CAAlB,IAAIC,MAAMJ,UAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiB;gBAExCK,IAAAA,oDAAkC,EAChCP,UAAUG,KAAK,EACfR,YACAU,gBACAR;YAEJ;QACF,OAAO,IACLA,cAAcD,IAAI,KAAK,aACvBC,cAAcW,cAAc,KAAK,MACjC;YACA,MAAMC,eAAeZ;YACrBa,IAAAA,uDAAqC,EAACD;QACxC;IACF;AACF"}