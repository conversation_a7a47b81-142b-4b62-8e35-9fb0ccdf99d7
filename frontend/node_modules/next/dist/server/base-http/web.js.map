{"version": 3, "sources": ["../../../src/server/base-http/web.ts"], "sourcesContent": ["import type { IncomingHttpHeaders, OutgoingHttpHeaders } from 'http'\nimport type { FetchMetrics } from './index'\n\nimport { toNodeOutgoingHttpHeaders } from '../web/utils'\nimport { BaseNextRequest, BaseNextResponse } from './index'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport type { NextRequestHint } from '../web/adapter'\nimport { CloseController, trackBodyConsumed } from '../web/web-on-close'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nexport class WebNextRequest extends BaseNextRequest<ReadableStream | null> {\n  public request: Request\n  public headers: IncomingHttpHeaders\n  public fetchMetrics: FetchMetrics | undefined\n\n  constructor(request: NextRequestHint) {\n    const url = new URL(request.url)\n\n    super(\n      request.method,\n      url.href.slice(url.origin.length),\n      request.clone().body\n    )\n    this.request = request\n    this.fetchMetrics = request.fetchMetrics\n\n    this.headers = {}\n    for (const [name, value] of request.headers.entries()) {\n      this.headers[name] = value\n    }\n  }\n\n  async parseBody(_limit: string | number): Promise<any> {\n    throw new Error('parseBody is not implemented in the web runtime')\n  }\n}\n\nexport class WebNextResponse extends BaseNextResponse<WritableStream> {\n  private headers = new Headers()\n  private textBody: string | undefined = undefined\n\n  private closeController = new CloseController()\n\n  public statusCode: number | undefined\n  public statusMessage: string | undefined\n\n  constructor(public transformStream = new TransformStream()) {\n    super(transformStream.writable)\n  }\n\n  setHeader(name: string, value: string | string[]): this {\n    this.headers.delete(name)\n    for (const val of Array.isArray(value) ? value : [value]) {\n      this.headers.append(name, val)\n    }\n    return this\n  }\n\n  removeHeader(name: string): this {\n    this.headers.delete(name)\n    return this\n  }\n\n  getHeaderValues(name: string): string[] | undefined {\n    // https://developer.mozilla.org/docs/Web/API/Headers/get#example\n    return this.getHeader(name)\n      ?.split(',')\n      .map((v) => v.trimStart())\n  }\n\n  getHeader(name: string): string | undefined {\n    return this.headers.get(name) ?? undefined\n  }\n\n  getHeaders(): OutgoingHttpHeaders {\n    return toNodeOutgoingHttpHeaders(this.headers)\n  }\n\n  hasHeader(name: string): boolean {\n    return this.headers.has(name)\n  }\n\n  appendHeader(name: string, value: string): this {\n    this.headers.append(name, value)\n    return this\n  }\n\n  body(value: string) {\n    this.textBody = value\n    return this\n  }\n\n  private readonly sendPromise = new DetachedPromise<void>()\n\n  private _sent = false\n  public send() {\n    this.sendPromise.resolve()\n    this._sent = true\n  }\n\n  get sent() {\n    return this._sent\n  }\n\n  public async toResponse() {\n    // If we haven't called `send` yet, wait for it to be called.\n    if (!this.sent) await this.sendPromise.promise\n\n    const body = this.textBody ?? this.transformStream.readable\n\n    let bodyInit: BodyInit = body\n\n    // if the response is streaming, onClose() can still be called after this point.\n    const canAddListenersLater = typeof bodyInit !== 'string'\n    const shouldTrackBody = canAddListenersLater\n      ? true\n      : this.closeController.listeners > 0\n\n    if (shouldTrackBody) {\n      bodyInit = trackBodyConsumed(body, () => {\n        this.closeController.dispatchClose()\n      })\n    }\n\n    return new Response(bodyInit, {\n      headers: this.headers,\n      status: this.statusCode,\n      statusText: this.statusMessage,\n    })\n  }\n\n  public onClose(callback: () => void) {\n    if (this.closeController.isClosed) {\n      throw new InvariantError(\n        'Cannot call onClose on a WebNextResponse that is already closed'\n      )\n    }\n    return this.closeController.onClose(callback)\n  }\n}\n"], "names": ["WebNextRequest", "WebNextResponse", "BaseNextRequest", "constructor", "request", "url", "URL", "method", "href", "slice", "origin", "length", "clone", "body", "fetchMetrics", "headers", "name", "value", "entries", "parseBody", "_limit", "Error", "BaseNextResponse", "transformStream", "TransformStream", "writable", "Headers", "textBody", "undefined", "closeController", "CloseController", "sendPromise", "Detached<PERSON>romise", "_sent", "<PERSON><PERSON><PERSON><PERSON>", "delete", "val", "Array", "isArray", "append", "removeHeader", "getHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON>", "split", "map", "v", "trimStart", "get", "getHeaders", "toNodeOutgoingHttpHeaders", "<PERSON><PERSON><PERSON><PERSON>", "has", "append<PERSON><PERSON>er", "send", "resolve", "sent", "toResponse", "promise", "readable", "bodyInit", "canAddListenersLater", "shouldTrackBody", "listeners", "trackBodyConsumed", "dispatchClose", "Response", "status", "statusCode", "statusText", "statusMessage", "onClose", "callback", "isClosed", "InvariantError"], "mappings": ";;;;;;;;;;;;;;;IAUaA,cAAc;eAAdA;;IA2BAC,eAAe;eAAfA;;;uBAlC6B;uBACQ;iCAClB;4BAEmB;gCACpB;AAExB,MAAMD,uBAAuBE,sBAAe;IAKjDC,YAAYC,OAAwB,CAAE;QACpC,MAAMC,MAAM,IAAIC,IAAIF,QAAQC,GAAG;QAE/B,KAAK,CACHD,QAAQG,MAAM,EACdF,IAAIG,IAAI,CAACC,KAAK,CAACJ,IAAIK,MAAM,CAACC,MAAM,GAChCP,QAAQQ,KAAK,GAAGC,IAAI;QAEtB,IAAI,CAACT,OAAO,GAAGA;QACf,IAAI,CAACU,YAAY,GAAGV,QAAQU,YAAY;QAExC,IAAI,CAACC,OAAO,GAAG,CAAC;QAChB,KAAK,MAAM,CAACC,MAAMC,MAAM,IAAIb,QAAQW,OAAO,CAACG,OAAO,GAAI;YACrD,IAAI,CAACH,OAAO,CAACC,KAAK,GAAGC;QACvB;IACF;IAEA,MAAME,UAAUC,MAAuB,EAAgB;QACrD,MAAM,qBAA4D,CAA5D,IAAIC,MAAM,oDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA2D;IACnE;AACF;AAEO,MAAMpB,wBAAwBqB,uBAAgB;IASnDnB,YAAY,AAAOoB,kBAAkB,IAAIC,iBAAiB,CAAE;QAC1D,KAAK,CAACD,gBAAgBE,QAAQ,QADbF,kBAAAA,sBARXR,UAAU,IAAIW,gBACdC,WAA+BC,gBAE/BC,kBAAkB,IAAIC,2BAAe,SAmD5BC,cAAc,IAAIC,gCAAe,SAE1CC,QAAQ;IA9ChB;IAEAC,UAAUlB,IAAY,EAAEC,KAAwB,EAAQ;QACtD,IAAI,CAACF,OAAO,CAACoB,MAAM,CAACnB;QACpB,KAAK,MAAMoB,OAAOC,MAAMC,OAAO,CAACrB,SAASA,QAAQ;YAACA;SAAM,CAAE;YACxD,IAAI,CAACF,OAAO,CAACwB,MAAM,CAACvB,MAAMoB;QAC5B;QACA,OAAO,IAAI;IACb;IAEAI,aAAaxB,IAAY,EAAQ;QAC/B,IAAI,CAACD,OAAO,CAACoB,MAAM,CAACnB;QACpB,OAAO,IAAI;IACb;IAEAyB,gBAAgBzB,IAAY,EAAwB;YAE3C;QADP,iEAAiE;QACjE,QAAO,kBAAA,IAAI,CAAC0B,SAAS,CAAC1B,0BAAf,gBACH2B,KAAK,CAAC,KACPC,GAAG,CAAC,CAACC,IAAMA,EAAEC,SAAS;IAC3B;IAEAJ,UAAU1B,IAAY,EAAsB;QAC1C,OAAO,IAAI,CAACD,OAAO,CAACgC,GAAG,CAAC/B,SAASY;IACnC;IAEAoB,aAAkC;QAChC,OAAOC,IAAAA,gCAAyB,EAAC,IAAI,CAAClC,OAAO;IAC/C;IAEAmC,UAAUlC,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACD,OAAO,CAACoC,GAAG,CAACnC;IAC1B;IAEAoC,aAAapC,IAAY,EAAEC,KAAa,EAAQ;QAC9C,IAAI,CAACF,OAAO,CAACwB,MAAM,CAACvB,MAAMC;QAC1B,OAAO,IAAI;IACb;IAEAJ,KAAKI,KAAa,EAAE;QAClB,IAAI,CAACU,QAAQ,GAAGV;QAChB,OAAO,IAAI;IACb;IAKOoC,OAAO;QACZ,IAAI,CAACtB,WAAW,CAACuB,OAAO;QACxB,IAAI,CAACrB,KAAK,GAAG;IACf;IAEA,IAAIsB,OAAO;QACT,OAAO,IAAI,CAACtB,KAAK;IACnB;IAEA,MAAauB,aAAa;QACxB,6DAA6D;QAC7D,IAAI,CAAC,IAAI,CAACD,IAAI,EAAE,MAAM,IAAI,CAACxB,WAAW,CAAC0B,OAAO;QAE9C,MAAM5C,OAAO,IAAI,CAACc,QAAQ,IAAI,IAAI,CAACJ,eAAe,CAACmC,QAAQ;QAE3D,IAAIC,WAAqB9C;QAEzB,gFAAgF;QAChF,MAAM+C,uBAAuB,OAAOD,aAAa;QACjD,MAAME,kBAAkBD,uBACpB,OACA,IAAI,CAAC/B,eAAe,CAACiC,SAAS,GAAG;QAErC,IAAID,iBAAiB;YACnBF,WAAWI,IAAAA,6BAAiB,EAAClD,MAAM;gBACjC,IAAI,CAACgB,eAAe,CAACmC,aAAa;YACpC;QACF;QAEA,OAAO,IAAIC,SAASN,UAAU;YAC5B5C,SAAS,IAAI,CAACA,OAAO;YACrBmD,QAAQ,IAAI,CAACC,UAAU;YACvBC,YAAY,IAAI,CAACC,aAAa;QAChC;IACF;IAEOC,QAAQC,QAAoB,EAAE;QACnC,IAAI,IAAI,CAAC1C,eAAe,CAAC2C,QAAQ,EAAE;YACjC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,oEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAO,IAAI,CAAC5C,eAAe,CAACyC,OAAO,CAACC;IACtC;AACF"}