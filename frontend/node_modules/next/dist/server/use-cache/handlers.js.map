{"version": 3, "sources": ["../../../src/server/use-cache/handlers.ts"], "sourcesContent": ["import DefaultCacheHandler from '../lib/cache-handlers/default'\nimport type { <PERSON>ache<PERSON>andler } from '../lib/cache-handlers/types'\n\nconst debug = process.env.NEXT_PRIVATE_DEBUG_USE_CACHE\n  ? (message: string, ...args: any[]) => {\n      console.log(`use-cache[${process.pid}]: ${message}`, ...args)\n    }\n  : () => {}\n\nconst handlersSymbol = Symbol.for('@next/cache-handlers')\nconst handlersMapSymbol = Symbol.for('@next/cache-handlers-map')\nconst handlersSetSymbol = Symbol.for('@next/cache-handlers-set')\n\n/**\n * The reference to the cache handlers. We store the cache handlers on the\n * global object so that we can access the same instance across different\n * boundaries (such as different copies of the same module).\n */\nconst reference: typeof globalThis & {\n  [handlersSymbol]?: {\n    RemoteCache?: CacheHandler\n    DefaultCache?: CacheHandler\n  }\n  [handlersMapSymbol]?: Map<string, CacheHandler>\n  [handlersSetSymbol]?: Set<CacheHandler>\n} = globalThis\n\n/**\n * Initialize the cache handlers.\n * @returns `true` if the cache handlers were initialized, `false` if they were already initialized.\n */\nexport function initializeCacheHandlers(): boolean {\n  // If the cache handlers have already been initialized, don't do it again.\n  if (reference[handlersMapSymbol]) {\n    debug('cache handlers already initialized')\n    return false\n  }\n\n  debug('initializing cache handlers')\n  reference[handlersMapSymbol] = new Map<string, CacheHandler>()\n\n  // Initialize the cache from the symbol contents first.\n  if (reference[handlersSymbol]) {\n    let fallback: CacheHandler\n    if (reference[handlersSymbol].DefaultCache) {\n      debug('setting \"default\" cache handler from symbol')\n      fallback = reference[handlersSymbol].DefaultCache\n    } else {\n      debug('setting \"default\" cache handler from default')\n      fallback = DefaultCacheHandler\n    }\n\n    reference[handlersMapSymbol].set('default', fallback)\n\n    if (reference[handlersSymbol].RemoteCache) {\n      debug('setting \"remote\" cache handler from symbol')\n      reference[handlersMapSymbol].set(\n        'remote',\n        reference[handlersSymbol].RemoteCache\n      )\n    } else {\n      debug('setting \"remote\" cache handler from default')\n      reference[handlersMapSymbol].set('remote', fallback)\n    }\n  } else {\n    debug('setting \"default\" cache handler from default')\n    reference[handlersMapSymbol].set('default', DefaultCacheHandler)\n    debug('setting \"remote\" cache handler from default')\n    reference[handlersMapSymbol].set('remote', DefaultCacheHandler)\n  }\n\n  // Create a set of the cache handlers.\n  reference[handlersSetSymbol] = new Set(reference[handlersMapSymbol].values())\n\n  return true\n}\n\n/**\n * Get a cache handler by kind.\n * @param kind - The kind of cache handler to get.\n * @returns The cache handler, or `undefined` if it is not initialized or does not exist.\n */\nexport function getCacheHandler(kind: string): CacheHandler | undefined {\n  // This should never be called before initializeCacheHandlers.\n  if (!reference[handlersMapSymbol]) {\n    throw new Error('Cache handlers not initialized')\n  }\n\n  return reference[handlersMapSymbol].get(kind)\n}\n\n/**\n * Get an iterator over the cache handlers.\n * @returns An iterator over the cache handlers, or `undefined` if they are not initialized.\n */\nexport function getCacheHandlers(): SetIterator<CacheHandler> | undefined {\n  if (!reference[handlersSetSymbol]) {\n    return undefined\n  }\n\n  return reference[handlersSetSymbol].values()\n}\n\n/**\n * Set a cache handler by kind.\n * @param kind - The kind of cache handler to set.\n * @param cacheHandler - The cache handler to set.\n */\nexport function setCacheHandler(\n  kind: string,\n  cacheHandler: CacheHandler\n): void {\n  // This should never be called before initializeCacheHandlers.\n  if (!reference[handlersMapSymbol] || !reference[handlersSetSymbol]) {\n    throw new Error('Cache handlers not initialized')\n  }\n\n  debug('setting cache handler for \"%s\"', kind)\n  reference[handlersMapSymbol].set(kind, cacheHandler)\n  reference[handlersSetSymbol].add(cacheHandler)\n}\n"], "names": ["getCache<PERSON><PERSON><PERSON>", "getCacheHandlers", "initializeCacheHandlers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_USE_CACHE", "message", "args", "console", "log", "pid", "handlersSymbol", "Symbol", "for", "handlersMapSymbol", "handlersSetSymbol", "reference", "globalThis", "Map", "fallback", "DefaultCache", "DefaultCache<PERSON>andler", "set", "RemoteCache", "Set", "values", "kind", "Error", "get", "undefined", "cache<PERSON><PERSON><PERSON>", "add"], "mappings": ";;;;;;;;;;;;;;;;;IAkFgBA,eAAe;eAAfA;;IAaAC,gBAAgB;eAAhBA;;IAhEAC,uBAAuB;eAAvBA;;IA6EAC,eAAe;eAAfA;;;gEA5GgB;;;;;;AAGhC,MAAMC,QAAQC,QAAQC,GAAG,CAACC,4BAA4B,GAClD,CAACC,SAAiB,GAAGC;IACnBC,QAAQC,GAAG,CAAC,CAAC,UAAU,EAAEN,QAAQO,GAAG,CAAC,GAAG,EAAEJ,SAAS,KAAKC;AAC1D,IACA,KAAO;AAEX,MAAMI,iBAAiBC,OAAOC,GAAG,CAAC;AAClC,MAAMC,oBAAoBF,OAAOC,GAAG,CAAC;AACrC,MAAME,oBAAoBH,OAAOC,GAAG,CAAC;AAErC;;;;CAIC,GACD,MAAMG,YAOFC;AAMG,SAASjB;IACd,0EAA0E;IAC1E,IAAIgB,SAAS,CAACF,kBAAkB,EAAE;QAChCZ,MAAM;QACN,OAAO;IACT;IAEAA,MAAM;IACNc,SAAS,CAACF,kBAAkB,GAAG,IAAII;IAEnC,uDAAuD;IACvD,IAAIF,SAAS,CAACL,eAAe,EAAE;QAC7B,IAAIQ;QACJ,IAAIH,SAAS,CAACL,eAAe,CAACS,YAAY,EAAE;YAC1ClB,MAAM;YACNiB,WAAWH,SAAS,CAACL,eAAe,CAACS,YAAY;QACnD,OAAO;YACLlB,MAAM;YACNiB,WAAWE,gBAAmB;QAChC;QAEAL,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAAC,WAAWH;QAE5C,IAAIH,SAAS,CAACL,eAAe,CAACY,WAAW,EAAE;YACzCrB,MAAM;YACNc,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAC9B,UACAN,SAAS,CAACL,eAAe,CAACY,WAAW;QAEzC,OAAO;YACLrB,MAAM;YACNc,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAAC,UAAUH;QAC7C;IACF,OAAO;QACLjB,MAAM;QACNc,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAAC,WAAWD,gBAAmB;QAC/DnB,MAAM;QACNc,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAAC,UAAUD,gBAAmB;IAChE;IAEA,sCAAsC;IACtCL,SAAS,CAACD,kBAAkB,GAAG,IAAIS,IAAIR,SAAS,CAACF,kBAAkB,CAACW,MAAM;IAE1E,OAAO;AACT;AAOO,SAAS3B,gBAAgB4B,IAAY;IAC1C,8DAA8D;IAC9D,IAAI,CAACV,SAAS,CAACF,kBAAkB,EAAE;QACjC,MAAM,qBAA2C,CAA3C,IAAIa,MAAM,mCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0C;IAClD;IAEA,OAAOX,SAAS,CAACF,kBAAkB,CAACc,GAAG,CAACF;AAC1C;AAMO,SAAS3B;IACd,IAAI,CAACiB,SAAS,CAACD,kBAAkB,EAAE;QACjC,OAAOc;IACT;IAEA,OAAOb,SAAS,CAACD,kBAAkB,CAACU,MAAM;AAC5C;AAOO,SAASxB,gBACdyB,IAAY,EACZI,YAA0B;IAE1B,8DAA8D;IAC9D,IAAI,CAACd,SAAS,CAACF,kBAAkB,IAAI,CAACE,SAAS,CAACD,kBAAkB,EAAE;QAClE,MAAM,qBAA2C,CAA3C,IAAIY,MAAM,mCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0C;IAClD;IAEAzB,MAAM,kCAAkCwB;IACxCV,SAAS,CAACF,kBAAkB,CAACQ,GAAG,CAACI,MAAMI;IACvCd,SAAS,CAACD,kBAAkB,CAACgB,GAAG,CAACD;AACnC"}