{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/tags-manifest.external.ts"], "sourcesContent": ["type TagsManifest = {\n  items: { [tag: string]: { revalidatedAt?: number } }\n}\n\n// we share tags manifest between \"use cache\" handlers and\n// previous file-system-cache\nexport const tagsManifest: TagsManifest = {\n  items: {},\n}\n\nexport const isTagStale = (tags: string[], timestamp: number) => {\n  for (const tag of tags) {\n    const tagEntry = tagsManifest.items[tag]\n    if (\n      typeof tagEntry?.revalidatedAt === 'number' &&\n      // TODO: use performance.now and update file-system-cache?\n      tagEntry.revalidatedAt >= timestamp\n    ) {\n      return true\n    }\n  }\n  return false\n}\n"], "names": ["isTagStale", "tagsManifest", "items", "tags", "timestamp", "tag", "tagEntry", "revalidatedAt"], "mappings": ";;;;;;;;;;;;;;;IAUaA,UAAU;eAAVA;;IAJAC,YAAY;eAAZA;;;AAAN,MAAMA,eAA6B;IACxCC,OAAO,CAAC;AACV;AAEO,MAAMF,aAAa,CAACG,MAAgBC;IACzC,KAAK,MAAMC,OAAOF,KAAM;QACtB,MAAMG,WAAWL,aAAaC,KAAK,CAACG,IAAI;QACxC,IACE,QAAOC,4BAAAA,SAAUC,aAAa,MAAK,YACnC,0DAA0D;QAC1DD,SAASC,aAAa,IAAIH,WAC1B;YACA,OAAO;QACT;IACF;IACA,OAAO;AACT"}