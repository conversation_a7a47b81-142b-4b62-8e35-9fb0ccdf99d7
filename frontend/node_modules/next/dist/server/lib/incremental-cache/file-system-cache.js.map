{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/file-system-cache.ts"], "sourcesContent": ["import type { RouteMetadata } from '../../../export/routes/types'\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheHandlerContext, CacheHandlerValue } from './'\nimport type { CacheFs } from '../../../shared/lib/utils'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchValue,\n  type IncrementalCacheValue,\n  type SetIncrementalFetchCacheContext,\n  type SetIncrementalResponseCacheContext,\n} from '../../response-cache'\n\nimport { LRUCache } from '../lru-cache'\nimport path from '../../../shared/lib/isomorphic/path'\nimport {\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_DATA_SUFFIX,\n  NEXT_META_SUFFIX,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n} from '../../../lib/constants'\nimport { tagsManifest } from './tags-manifest.external'\nimport { MultiFileWriter } from '../../../lib/multi-file-writer'\n\ntype FileSystemCacheContext = Omit<\n  CacheHandlerContext,\n  'fs' | 'serverDistDir'\n> & {\n  fs: CacheFs\n  serverDistDir: string\n}\n\nlet memoryCache: LRUCache<CacheHandlerValue> | undefined\n\nexport default class FileSystemCache implements CacheHandler {\n  private fs: FileSystemCacheContext['fs']\n  private flushToDisk?: FileSystemCacheContext['flushToDisk']\n  private serverDistDir: FileSystemCacheContext['serverDistDir']\n  private revalidatedTags: string[]\n  private debug: boolean\n\n  constructor(ctx: FileSystemCacheContext) {\n    this.fs = ctx.fs\n    this.flushToDisk = ctx.flushToDisk\n    this.serverDistDir = ctx.serverDistDir\n    this.revalidatedTags = ctx.revalidatedTags\n    this.debug = !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n\n    if (ctx.maxMemoryCacheSize) {\n      if (!memoryCache) {\n        if (this.debug) {\n          console.log('using memory store for fetch cache')\n        }\n\n        memoryCache = new LRUCache(ctx.maxMemoryCacheSize, function length({\n          value,\n        }) {\n          if (!value) {\n            return 25\n          } else if (value.kind === CachedRouteKind.REDIRECT) {\n            return JSON.stringify(value.props).length\n          } else if (value.kind === CachedRouteKind.IMAGE) {\n            throw new Error('invariant image should not be incremental-cache')\n          } else if (value.kind === CachedRouteKind.FETCH) {\n            return JSON.stringify(value.data || '').length\n          } else if (value.kind === CachedRouteKind.APP_ROUTE) {\n            return value.body.length\n          }\n          // rough estimate of size of cache value\n          return (\n            value.html.length +\n            (JSON.stringify(\n              value.kind === CachedRouteKind.APP_PAGE\n                ? value.rscData\n                : value.pageData\n            )?.length || 0)\n          )\n        })\n      }\n    } else if (this.debug) {\n      console.log('not using memory store for fetch cache')\n    }\n  }\n\n  public resetRequestCache(): void {}\n\n  public async revalidateTag(\n    ...args: Parameters<CacheHandler['revalidateTag']>\n  ) {\n    let [tags] = args\n    tags = typeof tags === 'string' ? [tags] : tags\n\n    if (this.debug) {\n      console.log('revalidateTag', tags)\n    }\n\n    if (tags.length === 0) {\n      return\n    }\n\n    for (const tag of tags) {\n      const data = tagsManifest.items[tag] || {}\n      data.revalidatedAt = Date.now()\n      tagsManifest.items[tag] = data\n    }\n  }\n\n  public async get(...args: Parameters<CacheHandler['get']>) {\n    const [key, ctx] = args\n    const { kind } = ctx\n\n    let data = memoryCache?.get(key)\n\n    if (this.debug) {\n      if (kind === IncrementalCacheKind.FETCH) {\n        console.log('get', key, ctx.tags, kind, !!data)\n      } else {\n        console.log('get', key, kind, !!data)\n      }\n    }\n\n    // let's check the disk for seed data\n    if (!data && process.env.NEXT_RUNTIME !== 'edge') {\n      if (kind === IncrementalCacheKind.APP_ROUTE) {\n        try {\n          const filePath = this.getFilePath(\n            `${key}.body`,\n            IncrementalCacheKind.APP_ROUTE\n          )\n          const fileData = await this.fs.readFile(filePath)\n          const { mtime } = await this.fs.stat(filePath)\n\n          const meta = JSON.parse(\n            await this.fs.readFile(\n              filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n              'utf8'\n            )\n          )\n\n          const cacheEntry: CacheHandlerValue = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_ROUTE,\n              body: fileData,\n              headers: meta.headers,\n              status: meta.status,\n            },\n          }\n          return cacheEntry\n        } catch {\n          return null\n        }\n      }\n\n      try {\n        const filePath = this.getFilePath(\n          kind === IncrementalCacheKind.FETCH ? key : `${key}.html`,\n          kind\n        )\n\n        const fileData = await this.fs.readFile(filePath, 'utf8')\n        const { mtime } = await this.fs.stat(filePath)\n\n        if (kind === IncrementalCacheKind.FETCH) {\n          const { tags, fetchIdx, fetchUrl } = ctx\n\n          if (!this.flushToDisk) return null\n\n          const lastModified = mtime.getTime()\n          const parsedData: CachedFetchValue = JSON.parse(fileData)\n          data = {\n            lastModified,\n            value: parsedData,\n          }\n\n          if (data.value?.kind === CachedRouteKind.FETCH) {\n            const storedTags = data.value?.tags\n\n            // update stored tags if a new one is being added\n            // TODO: remove this when we can send the tags\n            // via header on GET same as SET\n            if (!tags?.every((tag) => storedTags?.includes(tag))) {\n              if (this.debug) {\n                console.log('tags vs storedTags mismatch', tags, storedTags)\n              }\n              await this.set(key, data.value, {\n                fetchCache: true,\n                tags,\n                fetchIdx,\n                fetchUrl,\n              })\n            }\n          }\n        } else if (kind === IncrementalCacheKind.APP_PAGE) {\n          // We try to load the metadata file, but if it fails, we don't\n          // error. We also don't load it if this is a fallback.\n          let meta: RouteMetadata | undefined\n          try {\n            meta = JSON.parse(\n              await this.fs.readFile(\n                filePath.replace(/\\.html$/, NEXT_META_SUFFIX),\n                'utf8'\n              )\n            )\n          } catch {}\n\n          let maybeSegmentData: Map<string, Buffer> | undefined\n          if (meta?.segmentPaths) {\n            // Collect all the segment data for this page.\n            // TODO: To optimize file system reads, we should consider creating\n            // separate cache entries for each segment, rather than storing them\n            // all on the page's entry. Though the behavior is\n            // identical regardless.\n            const segmentData: Map<string, Buffer> = new Map()\n            maybeSegmentData = segmentData\n            const segmentsDir = key + RSC_SEGMENTS_DIR_SUFFIX\n            await Promise.all(\n              meta.segmentPaths.map(async (segmentPath: string) => {\n                const segmentDataFilePath = this.getFilePath(\n                  segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX,\n                  IncrementalCacheKind.APP_PAGE\n                )\n                try {\n                  segmentData.set(\n                    segmentPath,\n                    await this.fs.readFile(segmentDataFilePath)\n                  )\n                } catch {\n                  // This shouldn't happen, but if for some reason we fail to\n                  // load a segment from the filesystem, treat it the same as if\n                  // the segment is dynamic and does not have a prefetch.\n                }\n              })\n            )\n          }\n\n          let rscData: Buffer | undefined\n          if (!ctx.isFallback) {\n            rscData = await this.fs.readFile(\n              this.getFilePath(\n                `${key}${ctx.isRoutePPREnabled ? RSC_PREFETCH_SUFFIX : RSC_SUFFIX}`,\n                IncrementalCacheKind.APP_PAGE\n              )\n            )\n          }\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.APP_PAGE,\n              html: fileData,\n              rscData,\n              postponed: meta?.postponed,\n              headers: meta?.headers,\n              status: meta?.status,\n              segmentData: maybeSegmentData,\n            },\n          }\n        } else if (kind === IncrementalCacheKind.PAGES) {\n          let meta: RouteMetadata | undefined\n          let pageData: string | object = {}\n\n          if (!ctx.isFallback) {\n            pageData = JSON.parse(\n              await this.fs.readFile(\n                this.getFilePath(\n                  `${key}${NEXT_DATA_SUFFIX}`,\n                  IncrementalCacheKind.PAGES\n                ),\n                'utf8'\n              )\n            )\n          }\n\n          data = {\n            lastModified: mtime.getTime(),\n            value: {\n              kind: CachedRouteKind.PAGES,\n              html: fileData,\n              pageData,\n              headers: meta?.headers,\n              status: meta?.status,\n            },\n          }\n        } else {\n          throw new Error(\n            `Invariant: Unexpected route kind ${kind} in file system cache.`\n          )\n        }\n\n        if (data) {\n          memoryCache?.set(key, data)\n        }\n      } catch {\n        return null\n      }\n    }\n\n    if (\n      data?.value?.kind === CachedRouteKind.APP_PAGE ||\n      data?.value?.kind === CachedRouteKind.PAGES\n    ) {\n      let cacheTags: undefined | string[]\n      const tagsHeader = data.value.headers?.[NEXT_CACHE_TAGS_HEADER]\n\n      if (typeof tagsHeader === 'string') {\n        cacheTags = tagsHeader.split(',')\n      }\n\n      if (cacheTags?.length) {\n        const isStale = cacheTags.some((tag) => {\n          return (\n            tagsManifest?.items[tag]?.revalidatedAt &&\n            tagsManifest?.items[tag].revalidatedAt >=\n              (data?.lastModified || Date.now())\n          )\n        })\n\n        // we trigger a blocking validation if an ISR page\n        // had a tag revalidated, if we want to be a background\n        // revalidation instead we return data.lastModified = -1\n        if (isStale) {\n          return null\n        }\n      }\n    } else if (data?.value?.kind === CachedRouteKind.FETCH) {\n      const combinedTags =\n        ctx.kind === IncrementalCacheKind.FETCH\n          ? [...(ctx.tags || []), ...(ctx.softTags || [])]\n          : []\n\n      const wasRevalidated = combinedTags.some((tag) => {\n        if (this.revalidatedTags.includes(tag)) {\n          return true\n        }\n\n        return (\n          tagsManifest?.items[tag]?.revalidatedAt &&\n          tagsManifest?.items[tag].revalidatedAt >=\n            (data?.lastModified || Date.now())\n        )\n      })\n      // When revalidate tag is called we don't return\n      // stale data so it's updated right away\n      if (wasRevalidated) {\n        data = undefined\n      }\n    }\n\n    return data ?? null\n  }\n\n  public async set(\n    key: string,\n    data: IncrementalCacheValue | null,\n    ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ) {\n    memoryCache?.set(key, {\n      value: data,\n      lastModified: Date.now(),\n    })\n\n    if (this.debug) {\n      console.log('set', key)\n    }\n\n    if (!this.flushToDisk || !data) return\n\n    // Create a new writer that will prepare to write all the files to disk\n    // after their containing directory is created.\n    const writer = new MultiFileWriter(this.fs)\n\n    if (data.kind === CachedRouteKind.APP_ROUTE) {\n      const filePath = this.getFilePath(\n        `${key}.body`,\n        IncrementalCacheKind.APP_ROUTE\n      )\n\n      writer.append(filePath, data.body)\n\n      const meta: RouteMetadata = {\n        headers: data.headers,\n        status: data.status,\n        postponed: undefined,\n        segmentPaths: undefined,\n      }\n\n      writer.append(\n        filePath.replace(/\\.body$/, NEXT_META_SUFFIX),\n        JSON.stringify(meta, null, 2)\n      )\n    } else if (\n      data.kind === CachedRouteKind.PAGES ||\n      data.kind === CachedRouteKind.APP_PAGE\n    ) {\n      const isAppPath = data.kind === CachedRouteKind.APP_PAGE\n      const htmlPath = this.getFilePath(\n        `${key}.html`,\n        isAppPath ? IncrementalCacheKind.APP_PAGE : IncrementalCacheKind.PAGES\n      )\n\n      writer.append(htmlPath, data.html)\n\n      // Fallbacks don't generate a data file.\n      if (!ctx.fetchCache && !ctx.isFallback) {\n        writer.append(\n          this.getFilePath(\n            `${key}${\n              isAppPath\n                ? ctx.isRoutePPREnabled\n                  ? RSC_PREFETCH_SUFFIX\n                  : RSC_SUFFIX\n                : NEXT_DATA_SUFFIX\n            }`,\n            isAppPath\n              ? IncrementalCacheKind.APP_PAGE\n              : IncrementalCacheKind.PAGES\n          ),\n          isAppPath ? data.rscData! : JSON.stringify(data.pageData)\n        )\n      }\n\n      if (data?.kind === CachedRouteKind.APP_PAGE) {\n        let segmentPaths: string[] | undefined\n        if (data.segmentData) {\n          segmentPaths = []\n          const segmentsDir = htmlPath.replace(\n            /\\.html$/,\n            RSC_SEGMENTS_DIR_SUFFIX\n          )\n\n          for (const [segmentPath, buffer] of data.segmentData) {\n            segmentPaths.push(segmentPath)\n            const segmentDataFilePath =\n              segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX\n            writer.append(segmentDataFilePath, buffer)\n          }\n        }\n\n        const meta: RouteMetadata = {\n          headers: data.headers,\n          status: data.status,\n          postponed: data.postponed,\n          segmentPaths,\n        }\n\n        writer.append(\n          htmlPath.replace(/\\.html$/, NEXT_META_SUFFIX),\n          JSON.stringify(meta)\n        )\n      }\n    } else if (data.kind === CachedRouteKind.FETCH) {\n      const filePath = this.getFilePath(key, IncrementalCacheKind.FETCH)\n      writer.append(\n        filePath,\n        JSON.stringify({\n          ...data,\n          tags: ctx.fetchCache ? ctx.tags : [],\n        })\n      )\n    }\n\n    // Wait for all FS operations to complete.\n    await writer.wait()\n  }\n\n  private getFilePath(pathname: string, kind: IncrementalCacheKind): string {\n    switch (kind) {\n      case IncrementalCacheKind.FETCH:\n        // we store in .next/cache/fetch-cache so it can be persisted\n        // across deploys\n        return path.join(\n          this.serverDistDir,\n          '..',\n          'cache',\n          'fetch-cache',\n          pathname\n        )\n      case IncrementalCacheKind.PAGES:\n        return path.join(this.serverDistDir, 'pages', pathname)\n      case IncrementalCacheKind.IMAGE:\n      case IncrementalCacheKind.APP_PAGE:\n      case IncrementalCacheKind.APP_ROUTE:\n        return path.join(this.serverDistDir, 'app', pathname)\n      default:\n        throw new Error(`Unexpected file path kind: ${kind}`)\n    }\n  }\n}\n"], "names": ["FileSystemCache", "memoryCache", "constructor", "ctx", "fs", "flushToDisk", "serverDistDir", "revalidatedTags", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "maxMemoryCacheSize", "console", "log", "L<PERSON><PERSON><PERSON>", "length", "value", "JSON", "kind", "CachedRouteKind", "REDIRECT", "stringify", "props", "IMAGE", "Error", "FETCH", "data", "APP_ROUTE", "body", "html", "APP_PAGE", "rscData", "pageData", "resetRequestCache", "revalidateTag", "args", "tags", "tag", "tagsManifest", "items", "revalidatedAt", "Date", "now", "get", "key", "IncrementalCacheKind", "NEXT_RUNTIME", "filePath", "getFilePath", "fileData", "readFile", "mtime", "stat", "meta", "parse", "replace", "NEXT_META_SUFFIX", "cacheEntry", "lastModified", "getTime", "headers", "status", "fetchIdx", "fetchUrl", "parsedData", "storedTags", "every", "includes", "set", "fetchCache", "maybeSegmentData", "segmentPaths", "segmentData", "Map", "segmentsDir", "RSC_SEGMENTS_DIR_SUFFIX", "Promise", "all", "map", "segmentPath", "segmentDataFilePath", "RSC_SEGMENT_SUFFIX", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "postponed", "PAGES", "NEXT_DATA_SUFFIX", "cacheTags", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_CACHE_TAGS_HEADER", "split", "isStale", "some", "combinedTags", "softTags", "wasRevalidated", "undefined", "writer", "MultiFileWriter", "append", "isAppPath", "htmlPath", "buffer", "push", "wait", "pathname", "path", "join"], "mappings": ";;;;+BAoCA;;;eAAqBA;;;+BA1Bd;0BAEkB;6DACR;2BASV;sCACsB;iCACG;;;;;;AAUhC,IAAIC;AAEW,MAAMD;IAOnBE,YAAYC,GAA2B,CAAE;QACvC,IAAI,CAACC,EAAE,GAAGD,IAAIC,EAAE;QAChB,IAAI,CAACC,WAAW,GAAGF,IAAIE,WAAW;QAClC,IAAI,CAACC,aAAa,GAAGH,IAAIG,aAAa;QACtC,IAAI,CAACC,eAAe,GAAGJ,IAAII,eAAe;QAC1C,IAAI,CAACC,KAAK,GAAG,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;QAEnD,IAAIR,IAAIS,kBAAkB,EAAE;YAC1B,IAAI,CAACX,aAAa;gBAChB,IAAI,IAAI,CAACO,KAAK,EAAE;oBACdK,QAAQC,GAAG,CAAC;gBACd;gBAEAb,cAAc,IAAIc,kBAAQ,CAACZ,IAAIS,kBAAkB,EAAE,SAASI,OAAO,EACjEC,KAAK,EACN;wBAeIC;oBAdH,IAAI,CAACD,OAAO;wBACV,OAAO;oBACT,OAAO,IAAIA,MAAME,IAAI,KAAKC,8BAAe,CAACC,QAAQ,EAAE;wBAClD,OAAOH,KAAKI,SAAS,CAACL,MAAMM,KAAK,EAAEP,MAAM;oBAC3C,OAAO,IAAIC,MAAME,IAAI,KAAKC,8BAAe,CAACI,KAAK,EAAE;wBAC/C,MAAM,qBAA4D,CAA5D,IAAIC,MAAM,oDAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAA2D;oBACnE,OAAO,IAAIR,MAAME,IAAI,KAAKC,8BAAe,CAACM,KAAK,EAAE;wBAC/C,OAAOR,KAAKI,SAAS,CAACL,MAAMU,IAAI,IAAI,IAAIX,MAAM;oBAChD,OAAO,IAAIC,MAAME,IAAI,KAAKC,8BAAe,CAACQ,SAAS,EAAE;wBACnD,OAAOX,MAAMY,IAAI,CAACb,MAAM;oBAC1B;oBACA,wCAAwC;oBACxC,OACEC,MAAMa,IAAI,CAACd,MAAM,GAChBE,CAAAA,EAAAA,kBAAAA,KAAKI,SAAS,CACbL,MAAME,IAAI,KAAKC,8BAAe,CAACW,QAAQ,GACnCd,MAAMe,OAAO,GACbf,MAAMgB,QAAQ,sBAHnBf,gBAIEF,MAAM,KAAI,CAAA;gBAEjB;YACF;QACF,OAAO,IAAI,IAAI,CAACR,KAAK,EAAE;YACrBK,QAAQC,GAAG,CAAC;QACd;IACF;IAEOoB,oBAA0B,CAAC;IAElC,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAE3C,IAAI,IAAI,CAAC7B,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,iBAAiBuB;QAC/B;QAEA,IAAIA,KAAKrB,MAAM,KAAK,GAAG;YACrB;QACF;QAEA,KAAK,MAAMsB,OAAOD,KAAM;YACtB,MAAMV,OAAOY,kCAAY,CAACC,KAAK,CAACF,IAAI,IAAI,CAAC;YACzCX,KAAKc,aAAa,GAAGC,KAAKC,GAAG;YAC7BJ,kCAAY,CAACC,KAAK,CAACF,IAAI,GAAGX;QAC5B;IACF;IAEA,MAAaiB,IAAI,GAAGR,IAAqC,EAAE;YAgMvDT,aACAA,cAyBSA;QAzNX,MAAM,CAACkB,KAAK1C,IAAI,GAAGiC;QACnB,MAAM,EAAEjB,IAAI,EAAE,GAAGhB;QAEjB,IAAIwB,OAAO1B,+BAAAA,YAAa2C,GAAG,CAACC;QAE5B,IAAI,IAAI,CAACrC,KAAK,EAAE;YACd,IAAIW,SAAS2B,mCAAoB,CAACpB,KAAK,EAAE;gBACvCb,QAAQC,GAAG,CAAC,OAAO+B,KAAK1C,IAAIkC,IAAI,EAAElB,MAAM,CAAC,CAACQ;YAC5C,OAAO;gBACLd,QAAQC,GAAG,CAAC,OAAO+B,KAAK1B,MAAM,CAAC,CAACQ;YAClC;QACF;QAEA,qCAAqC;QACrC,IAAI,CAACA,QAAQlB,QAAQC,GAAG,CAACqC,YAAY,KAAK,QAAQ;YAChD,IAAI5B,SAAS2B,mCAAoB,CAAClB,SAAS,EAAE;gBAC3C,IAAI;oBACF,MAAMoB,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGJ,IAAI,KAAK,CAAC,EACbC,mCAAoB,CAAClB,SAAS;oBAEhC,MAAMsB,WAAW,MAAM,IAAI,CAAC9C,EAAE,CAAC+C,QAAQ,CAACH;oBACxC,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAChD,EAAE,CAACiD,IAAI,CAACL;oBAErC,MAAMM,OAAOpC,KAAKqC,KAAK,CACrB,MAAM,IAAI,CAACnD,EAAE,CAAC+C,QAAQ,CACpBH,SAASQ,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;oBAIJ,MAAMC,aAAgC;wBACpCC,cAAcP,MAAMQ,OAAO;wBAC3B3C,OAAO;4BACLE,MAAMC,8BAAe,CAACQ,SAAS;4BAC/BC,MAAMqB;4BACNW,SAASP,KAAKO,OAAO;4BACrBC,QAAQR,KAAKQ,MAAM;wBACrB;oBACF;oBACA,OAAOJ;gBACT,EAAE,OAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI;gBACF,MAAMV,WAAW,IAAI,CAACC,WAAW,CAC/B9B,SAAS2B,mCAAoB,CAACpB,KAAK,GAAGmB,MAAM,GAAGA,IAAI,KAAK,CAAC,EACzD1B;gBAGF,MAAM+B,WAAW,MAAM,IAAI,CAAC9C,EAAE,CAAC+C,QAAQ,CAACH,UAAU;gBAClD,MAAM,EAAEI,KAAK,EAAE,GAAG,MAAM,IAAI,CAAChD,EAAE,CAACiD,IAAI,CAACL;gBAErC,IAAI7B,SAAS2B,mCAAoB,CAACpB,KAAK,EAAE;wBAYnCC;oBAXJ,MAAM,EAAEU,IAAI,EAAE0B,QAAQ,EAAEC,QAAQ,EAAE,GAAG7D;oBAErC,IAAI,CAAC,IAAI,CAACE,WAAW,EAAE,OAAO;oBAE9B,MAAMsD,eAAeP,MAAMQ,OAAO;oBAClC,MAAMK,aAA+B/C,KAAKqC,KAAK,CAACL;oBAChDvB,OAAO;wBACLgC;wBACA1C,OAAOgD;oBACT;oBAEA,IAAItC,EAAAA,eAAAA,KAAKV,KAAK,qBAAVU,aAAYR,IAAI,MAAKC,8BAAe,CAACM,KAAK,EAAE;4BAC3BC;wBAAnB,MAAMuC,cAAavC,eAAAA,KAAKV,KAAK,qBAAVU,aAAYU,IAAI;wBAEnC,iDAAiD;wBACjD,8CAA8C;wBAC9C,gCAAgC;wBAChC,IAAI,EAACA,wBAAAA,KAAM8B,KAAK,CAAC,CAAC7B,MAAQ4B,8BAAAA,WAAYE,QAAQ,CAAC9B,QAAO;4BACpD,IAAI,IAAI,CAAC9B,KAAK,EAAE;gCACdK,QAAQC,GAAG,CAAC,+BAA+BuB,MAAM6B;4BACnD;4BACA,MAAM,IAAI,CAACG,GAAG,CAACxB,KAAKlB,KAAKV,KAAK,EAAE;gCAC9BqD,YAAY;gCACZjC;gCACA0B;gCACAC;4BACF;wBACF;oBACF;gBACF,OAAO,IAAI7C,SAAS2B,mCAAoB,CAACf,QAAQ,EAAE;oBACjD,8DAA8D;oBAC9D,sDAAsD;oBACtD,IAAIuB;oBACJ,IAAI;wBACFA,OAAOpC,KAAKqC,KAAK,CACf,MAAM,IAAI,CAACnD,EAAE,CAAC+C,QAAQ,CACpBH,SAASQ,OAAO,CAAC,WAAWC,2BAAgB,GAC5C;oBAGN,EAAE,OAAM,CAAC;oBAET,IAAIc;oBACJ,IAAIjB,wBAAAA,KAAMkB,YAAY,EAAE;wBACtB,8CAA8C;wBAC9C,mEAAmE;wBACnE,oEAAoE;wBACpE,kDAAkD;wBAClD,wBAAwB;wBACxB,MAAMC,cAAmC,IAAIC;wBAC7CH,mBAAmBE;wBACnB,MAAME,cAAc9B,MAAM+B,kCAAuB;wBACjD,MAAMC,QAAQC,GAAG,CACfxB,KAAKkB,YAAY,CAACO,GAAG,CAAC,OAAOC;4BAC3B,MAAMC,sBAAsB,IAAI,CAAChC,WAAW,CAC1C0B,cAAcK,cAAcE,6BAAkB,EAC9CpC,mCAAoB,CAACf,QAAQ;4BAE/B,IAAI;gCACF0C,YAAYJ,GAAG,CACbW,aACA,MAAM,IAAI,CAAC5E,EAAE,CAAC+C,QAAQ,CAAC8B;4BAE3B,EAAE,OAAM;4BACN,2DAA2D;4BAC3D,8DAA8D;4BAC9D,uDAAuD;4BACzD;wBACF;oBAEJ;oBAEA,IAAIjD;oBACJ,IAAI,CAAC7B,IAAIgF,UAAU,EAAE;wBACnBnD,UAAU,MAAM,IAAI,CAAC5B,EAAE,CAAC+C,QAAQ,CAC9B,IAAI,CAACF,WAAW,CACd,GAAGJ,MAAM1C,IAAIiF,iBAAiB,GAAGC,8BAAmB,GAAGC,qBAAU,EAAE,EACnExC,mCAAoB,CAACf,QAAQ;oBAGnC;oBAEAJ,OAAO;wBACLgC,cAAcP,MAAMQ,OAAO;wBAC3B3C,OAAO;4BACLE,MAAMC,8BAAe,CAACW,QAAQ;4BAC9BD,MAAMoB;4BACNlB;4BACAuD,SAAS,EAAEjC,wBAAAA,KAAMiC,SAAS;4BAC1B1B,OAAO,EAAEP,wBAAAA,KAAMO,OAAO;4BACtBC,MAAM,EAAER,wBAAAA,KAAMQ,MAAM;4BACpBW,aAAaF;wBACf;oBACF;gBACF,OAAO,IAAIpD,SAAS2B,mCAAoB,CAAC0C,KAAK,EAAE;oBAC9C,IAAIlC;oBACJ,IAAIrB,WAA4B,CAAC;oBAEjC,IAAI,CAAC9B,IAAIgF,UAAU,EAAE;wBACnBlD,WAAWf,KAAKqC,KAAK,CACnB,MAAM,IAAI,CAACnD,EAAE,CAAC+C,QAAQ,CACpB,IAAI,CAACF,WAAW,CACd,GAAGJ,MAAM4C,2BAAgB,EAAE,EAC3B3C,mCAAoB,CAAC0C,KAAK,GAE5B;oBAGN;oBAEA7D,OAAO;wBACLgC,cAAcP,MAAMQ,OAAO;wBAC3B3C,OAAO;4BACLE,MAAMC,8BAAe,CAACoE,KAAK;4BAC3B1D,MAAMoB;4BACNjB;4BACA4B,OAAO,EAAEP,wBAAAA,KAAMO,OAAO;4BACtBC,MAAM,EAAER,wBAAAA,KAAMQ,MAAM;wBACtB;oBACF;gBACF,OAAO;oBACL,MAAM,qBAEL,CAFK,IAAIrC,MACR,CAAC,iCAAiC,EAAEN,KAAK,sBAAsB,CAAC,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIQ,MAAM;oBACR1B,+BAAAA,YAAaoE,GAAG,CAACxB,KAAKlB;gBACxB;YACF,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,IACEA,CAAAA,yBAAAA,cAAAA,KAAMV,KAAK,qBAAXU,YAAaR,IAAI,MAAKC,8BAAe,CAACW,QAAQ,IAC9CJ,CAAAA,yBAAAA,eAAAA,KAAMV,KAAK,qBAAXU,aAAaR,IAAI,MAAKC,8BAAe,CAACoE,KAAK,EAC3C;gBAEmB7D;YADnB,IAAI+D;YACJ,MAAMC,cAAahE,sBAAAA,KAAKV,KAAK,CAAC4C,OAAO,qBAAlBlC,mBAAoB,CAACiE,iCAAsB,CAAC;YAE/D,IAAI,OAAOD,eAAe,UAAU;gBAClCD,YAAYC,WAAWE,KAAK,CAAC;YAC/B;YAEA,IAAIH,6BAAAA,UAAW1E,MAAM,EAAE;gBACrB,MAAM8E,UAAUJ,UAAUK,IAAI,CAAC,CAACzD;wBAE5BC;oBADF,OACEA,CAAAA,kCAAY,qBAAZA,0BAAAA,kCAAY,CAAEC,KAAK,CAACF,IAAI,qBAAxBC,wBAA0BE,aAAa,KACvCF,CAAAA,kCAAY,oBAAZA,kCAAY,CAAEC,KAAK,CAACF,IAAI,CAACG,aAAa,KACnCd,CAAAA,CAAAA,wBAAAA,KAAMgC,YAAY,KAAIjB,KAAKC,GAAG,EAAC;gBAEtC;gBAEA,kDAAkD;gBAClD,uDAAuD;gBACvD,wDAAwD;gBACxD,IAAImD,SAAS;oBACX,OAAO;gBACT;YACF;QACF,OAAO,IAAInE,CAAAA,yBAAAA,eAAAA,KAAMV,KAAK,qBAAXU,aAAaR,IAAI,MAAKC,8BAAe,CAACM,KAAK,EAAE;YACtD,MAAMsE,eACJ7F,IAAIgB,IAAI,KAAK2B,mCAAoB,CAACpB,KAAK,GACnC;mBAAKvB,IAAIkC,IAAI,IAAI,EAAE;mBAAOlC,IAAI8F,QAAQ,IAAI,EAAE;aAAE,GAC9C,EAAE;YAER,MAAMC,iBAAiBF,aAAaD,IAAI,CAAC,CAACzD;oBAMtCC;gBALF,IAAI,IAAI,CAAChC,eAAe,CAAC6D,QAAQ,CAAC9B,MAAM;oBACtC,OAAO;gBACT;gBAEA,OACEC,CAAAA,kCAAY,qBAAZA,0BAAAA,kCAAY,CAAEC,KAAK,CAACF,IAAI,qBAAxBC,wBAA0BE,aAAa,KACvCF,CAAAA,kCAAY,oBAAZA,kCAAY,CAAEC,KAAK,CAACF,IAAI,CAACG,aAAa,KACnCd,CAAAA,CAAAA,wBAAAA,KAAMgC,YAAY,KAAIjB,KAAKC,GAAG,EAAC;YAEtC;YACA,gDAAgD;YAChD,wCAAwC;YACxC,IAAIuD,gBAAgB;gBAClBvE,OAAOwE;YACT;QACF;QAEA,OAAOxE,QAAQ;IACjB;IAEA,MAAa0C,IACXxB,GAAW,EACXlB,IAAkC,EAClCxB,GAAyE,EACzE;QACAF,+BAAAA,YAAaoE,GAAG,CAACxB,KAAK;YACpB5B,OAAOU;YACPgC,cAAcjB,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAACnC,KAAK,EAAE;YACdK,QAAQC,GAAG,CAAC,OAAO+B;QACrB;QAEA,IAAI,CAAC,IAAI,CAACxC,WAAW,IAAI,CAACsB,MAAM;QAEhC,uEAAuE;QACvE,+CAA+C;QAC/C,MAAMyE,SAAS,IAAIC,gCAAe,CAAC,IAAI,CAACjG,EAAE;QAE1C,IAAIuB,KAAKR,IAAI,KAAKC,8BAAe,CAACQ,SAAS,EAAE;YAC3C,MAAMoB,WAAW,IAAI,CAACC,WAAW,CAC/B,GAAGJ,IAAI,KAAK,CAAC,EACbC,mCAAoB,CAAClB,SAAS;YAGhCwE,OAAOE,MAAM,CAACtD,UAAUrB,KAAKE,IAAI;YAEjC,MAAMyB,OAAsB;gBAC1BO,SAASlC,KAAKkC,OAAO;gBACrBC,QAAQnC,KAAKmC,MAAM;gBACnByB,WAAWY;gBACX3B,cAAc2B;YAChB;YAEAC,OAAOE,MAAM,CACXtD,SAASQ,OAAO,CAAC,WAAWC,2BAAgB,GAC5CvC,KAAKI,SAAS,CAACgC,MAAM,MAAM;QAE/B,OAAO,IACL3B,KAAKR,IAAI,KAAKC,8BAAe,CAACoE,KAAK,IACnC7D,KAAKR,IAAI,KAAKC,8BAAe,CAACW,QAAQ,EACtC;YACA,MAAMwE,YAAY5E,KAAKR,IAAI,KAAKC,8BAAe,CAACW,QAAQ;YACxD,MAAMyE,WAAW,IAAI,CAACvD,WAAW,CAC/B,GAAGJ,IAAI,KAAK,CAAC,EACb0D,YAAYzD,mCAAoB,CAACf,QAAQ,GAAGe,mCAAoB,CAAC0C,KAAK;YAGxEY,OAAOE,MAAM,CAACE,UAAU7E,KAAKG,IAAI;YAEjC,wCAAwC;YACxC,IAAI,CAAC3B,IAAImE,UAAU,IAAI,CAACnE,IAAIgF,UAAU,EAAE;gBACtCiB,OAAOE,MAAM,CACX,IAAI,CAACrD,WAAW,CACd,GAAGJ,MACD0D,YACIpG,IAAIiF,iBAAiB,GACnBC,8BAAmB,GACnBC,qBAAU,GACZG,2BAAgB,EACpB,EACFc,YACIzD,mCAAoB,CAACf,QAAQ,GAC7Be,mCAAoB,CAAC0C,KAAK,GAEhCe,YAAY5E,KAAKK,OAAO,GAAId,KAAKI,SAAS,CAACK,KAAKM,QAAQ;YAE5D;YAEA,IAAIN,CAAAA,wBAAAA,KAAMR,IAAI,MAAKC,8BAAe,CAACW,QAAQ,EAAE;gBAC3C,IAAIyC;gBACJ,IAAI7C,KAAK8C,WAAW,EAAE;oBACpBD,eAAe,EAAE;oBACjB,MAAMG,cAAc6B,SAAShD,OAAO,CAClC,WACAoB,kCAAuB;oBAGzB,KAAK,MAAM,CAACI,aAAayB,OAAO,IAAI9E,KAAK8C,WAAW,CAAE;wBACpDD,aAAakC,IAAI,CAAC1B;wBAClB,MAAMC,sBACJN,cAAcK,cAAcE,6BAAkB;wBAChDkB,OAAOE,MAAM,CAACrB,qBAAqBwB;oBACrC;gBACF;gBAEA,MAAMnD,OAAsB;oBAC1BO,SAASlC,KAAKkC,OAAO;oBACrBC,QAAQnC,KAAKmC,MAAM;oBACnByB,WAAW5D,KAAK4D,SAAS;oBACzBf;gBACF;gBAEA4B,OAAOE,MAAM,CACXE,SAAShD,OAAO,CAAC,WAAWC,2BAAgB,GAC5CvC,KAAKI,SAAS,CAACgC;YAEnB;QACF,OAAO,IAAI3B,KAAKR,IAAI,KAAKC,8BAAe,CAACM,KAAK,EAAE;YAC9C,MAAMsB,WAAW,IAAI,CAACC,WAAW,CAACJ,KAAKC,mCAAoB,CAACpB,KAAK;YACjE0E,OAAOE,MAAM,CACXtD,UACA9B,KAAKI,SAAS,CAAC;gBACb,GAAGK,IAAI;gBACPU,MAAMlC,IAAImE,UAAU,GAAGnE,IAAIkC,IAAI,GAAG,EAAE;YACtC;QAEJ;QAEA,0CAA0C;QAC1C,MAAM+D,OAAOO,IAAI;IACnB;IAEQ1D,YAAY2D,QAAgB,EAAEzF,IAA0B,EAAU;QACxE,OAAQA;YACN,KAAK2B,mCAAoB,CAACpB,KAAK;gBAC7B,6DAA6D;gBAC7D,iBAAiB;gBACjB,OAAOmF,aAAI,CAACC,IAAI,CACd,IAAI,CAACxG,aAAa,EAClB,MACA,SACA,eACAsG;YAEJ,KAAK9D,mCAAoB,CAAC0C,KAAK;gBAC7B,OAAOqB,aAAI,CAACC,IAAI,CAAC,IAAI,CAACxG,aAAa,EAAE,SAASsG;YAChD,KAAK9D,mCAAoB,CAACtB,KAAK;YAC/B,KAAKsB,mCAAoB,CAACf,QAAQ;YAClC,KAAKe,mCAAoB,CAAClB,SAAS;gBACjC,OAAOiF,aAAI,CAACC,IAAI,CAAC,IAAI,CAACxG,aAAa,EAAE,OAAOsG;YAC9C;gBACE,MAAM,qBAA+C,CAA/C,IAAInF,MAAM,CAAC,2BAA2B,EAAEN,MAAM,GAA9C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;QACxD;IACF;AACF"}