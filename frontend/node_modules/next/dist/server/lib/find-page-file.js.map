{"version": 3, "sources": ["../../../src/server/lib/find-page-file.ts"], "sourcesContent": ["import { fileExists } from '../../lib/file-exists'\nimport { getPagePaths } from '../../shared/lib/page-path/get-page-paths'\nimport { nonNullable } from '../../lib/non-nullable'\nimport { join, sep, normalize } from 'path'\nimport { promises as fsPromises } from 'fs'\nimport { warn } from '../../build/output/log'\nimport { cyan } from '../../lib/picocolors'\nimport { isMetadataRouteFile } from '../../lib/metadata/is-metadata-route'\nimport type { PageExtensions } from '../../build/page-extensions-type'\n\nasync function isTrueCasePagePath(pagePath: string, pagesDir: string) {\n  const pageSegments = normalize(pagePath).split(sep).filter(Boolean)\n  const segmentExistsPromises = pageSegments.map(async (segment, i) => {\n    const segmentParentDir = join(pagesDir, ...pageSegments.slice(0, i))\n    const parentDirEntries = await fsPromises.readdir(segmentParentDir)\n    return parentDirEntries.includes(segment)\n  })\n\n  return (await Promise.all(segmentExistsPromises)).every(<PERSON><PERSON>an)\n}\n\n/**\n * Finds a page file with the given parameters. If the page is duplicated with\n * multiple extensions it will throw, otherwise it will return the *relative*\n * path to the page file or null if it is not found.\n *\n * @param pagesDir Absolute path to the pages folder with trailing `/pages`.\n * @param normalizedPagePath The page normalized (it will be denormalized).\n * @param pageExtensions Array of page extensions.\n */\nexport async function findPageFile(\n  pagesDir: string,\n  normalizedPagePath: string,\n  pageExtensions: PageExtensions,\n  isAppDir: boolean\n): Promise<string | null> {\n  const pagePaths = getPagePaths(normalizedPagePath, pageExtensions, isAppDir)\n  const [existingPath, ...others] = (\n    await Promise.all(\n      pagePaths.map(async (path) => {\n        const filePath = join(pagesDir, path)\n        try {\n          return (await fileExists(filePath)) ? path : null\n        } catch (err: any) {\n          if (!err?.code?.includes('ENOTDIR')) throw err\n        }\n        return null\n      })\n    )\n  ).filter(nonNullable)\n\n  if (!existingPath) {\n    return null\n  }\n\n  if (!(await isTrueCasePagePath(existingPath, pagesDir))) {\n    return null\n  }\n\n  if (others.length > 0) {\n    warn(\n      `Duplicate page detected. ${cyan(join('pages', existingPath))} and ${cyan(\n        join('pages', others[0])\n      )} both resolve to ${cyan(normalizedPagePath)}.`\n    )\n  }\n\n  return existingPath\n}\n\n/**\n *\n * createValidFileMatcher receives configured page extensions and return helpers to determine:\n * `isLayoutsLeafPage`: if a file is a valid page file or routes file under app directory\n * `isTrackedFiles`: if it's a tracked file for webpack watcher\n *\n */\nexport function createValidFileMatcher(\n  pageExtensions: PageExtensions,\n  appDirPath: string | undefined\n) {\n  const getExtensionRegexString = (extensions: string[]) =>\n    `(?:${extensions.join('|')})`\n\n  const validExtensionFileRegex = new RegExp(\n    '\\\\.' + getExtensionRegexString(pageExtensions) + '$'\n  )\n  const leafOnlyPageFileRegex = new RegExp(\n    `(^(page|route)|[\\\\\\\\/](page|route))\\\\.${getExtensionRegexString(\n      pageExtensions\n    )}$`\n  )\n  const rootNotFoundFileRegex = new RegExp(\n    `^not-found\\\\.${getExtensionRegexString(pageExtensions)}$`\n  )\n  /** TODO-METADATA: support other metadata routes\n   *  regex for:\n   *\n   * /robots.txt|<ext>\n   * /sitemap.xml|<ext>\n   * /favicon.ico\n   * /manifest.json|<ext>\n   * <route>/icon.png|jpg|<ext>\n   * <route>/apple-touch-icon.png|jpg|<ext>\n   *\n   */\n\n  /**\n   * Match the file if it's a metadata route file, static: if the file is a static metadata file.\n   * It needs to be a file which doesn't match the custom metadata routes e.g. `app/robots.txt/route.js`\n   */\n  function isMetadataFile(filePath: string) {\n    const appDirRelativePath = appDirPath\n      ? filePath.replace(appDirPath, '')\n      : filePath\n\n    return isMetadataRouteFile(appDirRelativePath, pageExtensions, true)\n  }\n\n  // Determine if the file is leaf node page file or route file under layouts,\n  // 'page.<extension>' | 'route.<extension>'\n  function isAppRouterPage(filePath: string) {\n    return leafOnlyPageFileRegex.test(filePath) || isMetadataFile(filePath)\n  }\n\n  function isPageFile(filePath: string) {\n    return validExtensionFileRegex.test(filePath) || isMetadataFile(filePath)\n  }\n\n  function isRootNotFound(filePath: string) {\n    if (!appDirPath) {\n      return false\n    }\n    if (!filePath.startsWith(appDirPath + sep)) {\n      return false\n    }\n    const rest = filePath.slice(appDirPath.length + 1)\n    return rootNotFoundFileRegex.test(rest)\n  }\n\n  return {\n    isPageFile,\n    isAppRouterPage,\n    isMetadataFile,\n    isRootNotFound,\n  }\n}\n"], "names": ["createValidFileMatcher", "findPageFile", "isTrueCasePagePath", "pagePath", "pagesDir", "pageSegments", "normalize", "split", "sep", "filter", "Boolean", "segmentExistsPromises", "map", "segment", "i", "segmentParentDir", "join", "slice", "parentDirEntries", "fsPromises", "readdir", "includes", "Promise", "all", "every", "normalizedPagePath", "pageExtensions", "isAppDir", "pagePaths", "getPagePaths", "existingPath", "others", "path", "filePath", "fileExists", "err", "code", "nonNullable", "length", "warn", "cyan", "appDirPath", "getExtensionRegexString", "extensions", "validExtensionFileRegex", "RegExp", "leafOnlyPageFileRegex", "rootNotFoundFileRegex", "isMetadataFile", "appDirRelativePath", "replace", "isMetadataRouteFile", "isAppRouterPage", "test", "isPageFile", "isRootNotFound", "startsWith", "rest"], "mappings": ";;;;;;;;;;;;;;;IA6EgBA,sBAAsB;eAAtBA;;IA/CMC,YAAY;eAAZA;;;4BA9BK;8BACE;6BACD;sBACS;oBACE;qBAClB;4BACA;iCACe;AAGpC,eAAeC,mBAAmBC,QAAgB,EAAEC,QAAgB;IAClE,MAAMC,eAAeC,IAAAA,eAAS,EAACH,UAAUI,KAAK,CAACC,SAAG,EAAEC,MAAM,CAACC;IAC3D,MAAMC,wBAAwBN,aAAaO,GAAG,CAAC,OAAOC,SAASC;QAC7D,MAAMC,mBAAmBC,IAAAA,UAAI,EAACZ,aAAaC,aAAaY,KAAK,CAAC,GAAGH;QACjE,MAAMI,mBAAmB,MAAMC,YAAU,CAACC,OAAO,CAACL;QAClD,OAAOG,iBAAiBG,QAAQ,CAACR;IACnC;IAEA,OAAO,AAAC,CAAA,MAAMS,QAAQC,GAAG,CAACZ,sBAAqB,EAAGa,KAAK,CAACd;AAC1D;AAWO,eAAeT,aACpBG,QAAgB,EAChBqB,kBAA0B,EAC1BC,cAA8B,EAC9BC,QAAiB;IAEjB,MAAMC,YAAYC,IAAAA,0BAAY,EAACJ,oBAAoBC,gBAAgBC;IACnE,MAAM,CAACG,cAAc,GAAGC,OAAO,GAAG,AAChC,CAAA,MAAMT,QAAQC,GAAG,CACfK,UAAUhB,GAAG,CAAC,OAAOoB;QACnB,MAAMC,WAAWjB,IAAAA,UAAI,EAACZ,UAAU4B;QAChC,IAAI;YACF,OAAO,AAAC,MAAME,IAAAA,sBAAU,EAACD,YAAaD,OAAO;QAC/C,EAAE,OAAOG,KAAU;gBACZA;YAAL,IAAI,EAACA,wBAAAA,YAAAA,IAAKC,IAAI,qBAATD,UAAWd,QAAQ,CAAC,aAAY,MAAMc;QAC7C;QACA,OAAO;IACT,GACF,EACA1B,MAAM,CAAC4B,wBAAW;IAEpB,IAAI,CAACP,cAAc;QACjB,OAAO;IACT;IAEA,IAAI,CAAE,MAAM5B,mBAAmB4B,cAAc1B,WAAY;QACvD,OAAO;IACT;IAEA,IAAI2B,OAAOO,MAAM,GAAG,GAAG;QACrBC,IAAAA,SAAI,EACF,CAAC,yBAAyB,EAAEC,IAAAA,gBAAI,EAACxB,IAAAA,UAAI,EAAC,SAASc,eAAe,KAAK,EAAEU,IAAAA,gBAAI,EACvExB,IAAAA,UAAI,EAAC,SAASe,MAAM,CAAC,EAAE,GACvB,iBAAiB,EAAES,IAAAA,gBAAI,EAACf,oBAAoB,CAAC,CAAC;IAEpD;IAEA,OAAOK;AACT;AASO,SAAS9B,uBACd0B,cAA8B,EAC9Be,UAA8B;IAE9B,MAAMC,0BAA0B,CAACC,aAC/B,CAAC,GAAG,EAAEA,WAAW3B,IAAI,CAAC,KAAK,CAAC,CAAC;IAE/B,MAAM4B,0BAA0B,IAAIC,OAClC,QAAQH,wBAAwBhB,kBAAkB;IAEpD,MAAMoB,wBAAwB,IAAID,OAChC,CAAC,sCAAsC,EAAEH,wBACvChB,gBACA,CAAC,CAAC;IAEN,MAAMqB,wBAAwB,IAAIF,OAChC,CAAC,aAAa,EAAEH,wBAAwBhB,gBAAgB,CAAC,CAAC;IAE5D;;;;;;;;;;GAUC,GAED;;;GAGC,GACD,SAASsB,eAAef,QAAgB;QACtC,MAAMgB,qBAAqBR,aACvBR,SAASiB,OAAO,CAACT,YAAY,MAC7BR;QAEJ,OAAOkB,IAAAA,oCAAmB,EAACF,oBAAoBvB,gBAAgB;IACjE;IAEA,4EAA4E;IAC5E,2CAA2C;IAC3C,SAAS0B,gBAAgBnB,QAAgB;QACvC,OAAOa,sBAAsBO,IAAI,CAACpB,aAAae,eAAef;IAChE;IAEA,SAASqB,WAAWrB,QAAgB;QAClC,OAAOW,wBAAwBS,IAAI,CAACpB,aAAae,eAAef;IAClE;IAEA,SAASsB,eAAetB,QAAgB;QACtC,IAAI,CAACQ,YAAY;YACf,OAAO;QACT;QACA,IAAI,CAACR,SAASuB,UAAU,CAACf,aAAajC,SAAG,GAAG;YAC1C,OAAO;QACT;QACA,MAAMiD,OAAOxB,SAAShB,KAAK,CAACwB,WAAWH,MAAM,GAAG;QAChD,OAAOS,sBAAsBM,IAAI,CAACI;IACpC;IAEA,OAAO;QACLH;QACAF;QACAJ;QACAO;IACF;AACF"}