{"version": 3, "sources": ["../../../../src/server/lib/module-loader/node-module-loader.ts"], "sourcesContent": ["import type { ModuleLoader } from './module-loader'\n\n/**\n * Loads a module using `await require(id)`.\n */\nexport class NodeModuleLoader implements ModuleLoader {\n  public async load<M>(id: string): Promise<M> {\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      // Need to `await` to cover the case that route is marked ESM modules by ESM escalation.\n      return await (process.env.NEXT_MINIMAL\n        ? // @ts-ignore\n          __non_webpack_require__(id)\n        : require(id))\n    }\n\n    throw new Error('NodeModuleLoader is not supported in edge runtime.')\n  }\n}\n"], "names": ["NodeModuleLoader", "load", "id", "process", "env", "NEXT_RUNTIME", "NEXT_MINIMAL", "__non_webpack_require__", "require", "Error"], "mappings": ";;;;+BAKaA;;;eAAAA;;;AAAN,MAAMA;IACX,MAAaC,KAAQC,EAAU,EAAc;QAC3C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YACvC,wFAAwF;YACxF,OAAO,MAAOF,CAAAA,QAAQC,GAAG,CAACE,YAAY,GAElCC,wBAAwBL,MACxBM,QAAQN,GAAE;QAChB;QAEA,MAAM,qBAA+D,CAA/D,IAAIO,MAAM,uDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA8D;IACtE;AACF"}