{"version": 3, "sources": ["../../src/server/server-utils.ts"], "sourcesContent": ["import type { Rewrite } from '../lib/load-custom-routes'\nimport type { RouteMatchFn } from '../shared/lib/router/utils/route-matcher'\nimport type { NextConfig } from './config'\nimport type { BaseNextRequest } from './base-http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { getPathMatch } from '../shared/lib/router/utils/path-match'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport {\n  matchHas,\n  prepareDestination,\n} from '../shared/lib/router/utils/prepare-destination'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { normalizeRscURL } from '../shared/lib/router/utils/app-paths'\nimport {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../lib/constants'\nimport { normalizeNextQueryParam } from './web/utils'\n\nexport function normalizeVercelUrl(\n  req: BaseNextRequest,\n  paramKeys: string[],\n  defaultRouteRegex: ReturnType<typeof getNamedRouteRegex> | undefined\n) {\n  // make sure to normalize req.url on Vercel to strip dynamic and rewrite\n  // params from the query which are added during routing\n  const _parsedUrl = parseUrl(req.url!, true)\n  delete (_parsedUrl as any).search\n\n  for (const key of Object.keys(_parsedUrl.query)) {\n    const isNextQueryPrefix =\n      key !== NEXT_QUERY_PARAM_PREFIX && key.startsWith(NEXT_QUERY_PARAM_PREFIX)\n\n    const isNextInterceptionMarkerPrefix =\n      key !== NEXT_INTERCEPTION_MARKER_PREFIX &&\n      key.startsWith(NEXT_INTERCEPTION_MARKER_PREFIX)\n\n    if (\n      isNextQueryPrefix ||\n      isNextInterceptionMarkerPrefix ||\n      paramKeys.includes(key) ||\n      (defaultRouteRegex && Object.keys(defaultRouteRegex.groups).includes(key))\n    ) {\n      delete _parsedUrl.query[key]\n    }\n  }\n\n  req.url = formatUrl(_parsedUrl)\n}\n\nexport function interpolateDynamicPath(\n  pathname: string,\n  params: ParsedUrlQuery,\n  defaultRouteRegex?: ReturnType<typeof getNamedRouteRegex> | undefined\n) {\n  if (!defaultRouteRegex) return pathname\n\n  for (const param of Object.keys(defaultRouteRegex.groups)) {\n    const { optional, repeat } = defaultRouteRegex.groups[param]\n    let builtParam = `[${repeat ? '...' : ''}${param}]`\n\n    if (optional) {\n      builtParam = `[${builtParam}]`\n    }\n\n    let paramValue: string\n    const value = params[param]\n\n    if (Array.isArray(value)) {\n      paramValue = value.map((v) => v && encodeURIComponent(v)).join('/')\n    } else if (value) {\n      paramValue = encodeURIComponent(value)\n    } else {\n      paramValue = ''\n    }\n\n    pathname = pathname.replaceAll(builtParam, paramValue)\n  }\n\n  return pathname\n}\n\nexport function normalizeDynamicRouteParams(\n  query: ParsedUrlQuery,\n  defaultRouteRegex: ReturnType<typeof getNamedRouteRegex>,\n  defaultRouteMatches: ParsedUrlQuery,\n  ignoreMissingOptional: boolean\n) {\n  let hasValidParams = true\n  let params: ParsedUrlQuery = {}\n\n  for (const key of Object.keys(defaultRouteRegex.groups)) {\n    let value: string | string[] | undefined = query[key]\n\n    if (typeof value === 'string') {\n      value = normalizeRscURL(value)\n    } else if (Array.isArray(value)) {\n      value = value.map(normalizeRscURL)\n    }\n\n    // if the value matches the default value we can't rely\n    // on the parsed params, this is used to signal if we need\n    // to parse x-now-route-matches or not\n    const defaultValue = defaultRouteMatches![key]\n    const isOptional = defaultRouteRegex!.groups[key].optional\n\n    const isDefaultValue = Array.isArray(defaultValue)\n      ? defaultValue.some((defaultVal) => {\n          return Array.isArray(value)\n            ? value.some((val) => val.includes(defaultVal))\n            : value?.includes(defaultVal)\n        })\n      : value?.includes(defaultValue as string)\n\n    if (\n      isDefaultValue ||\n      (typeof value === 'undefined' && !(isOptional && ignoreMissingOptional))\n    ) {\n      return { params: {}, hasValidParams: false }\n    }\n\n    // non-provided optional values should be undefined so normalize\n    // them to undefined\n    if (\n      isOptional &&\n      (!value ||\n        (Array.isArray(value) &&\n          value.length === 1 &&\n          // fallback optional catch-all SSG pages have\n          // [[...paramName]] for the root path on Vercel\n          (value[0] === 'index' || value[0] === `[[...${key}]]`)))\n    ) {\n      value = undefined\n      delete query[key]\n    }\n\n    // query values from the proxy aren't already split into arrays\n    // so make sure to normalize catch-all values\n    if (\n      value &&\n      typeof value === 'string' &&\n      defaultRouteRegex!.groups[key].repeat\n    ) {\n      value = value.split('/')\n    }\n\n    if (value) {\n      params[key] = value\n    }\n  }\n\n  return {\n    params,\n    hasValidParams,\n  }\n}\n\nexport function getUtils({\n  page,\n  i18n,\n  basePath,\n  rewrites,\n  pageIsDynamic,\n  trailingSlash,\n  caseSensitive,\n}: {\n  page: string\n  i18n?: NextConfig['i18n']\n  basePath: string\n  rewrites: {\n    fallback?: ReadonlyArray<Rewrite>\n    afterFiles?: ReadonlyArray<Rewrite>\n    beforeFiles?: ReadonlyArray<Rewrite>\n  }\n  pageIsDynamic: boolean\n  trailingSlash?: boolean\n  caseSensitive: boolean\n}) {\n  let defaultRouteRegex: ReturnType<typeof getNamedRouteRegex> | undefined\n  let dynamicRouteMatcher: RouteMatchFn | undefined\n  let defaultRouteMatches: ParsedUrlQuery | undefined\n\n  if (pageIsDynamic) {\n    defaultRouteRegex = getNamedRouteRegex(page, {\n      prefixRouteKeys: false,\n    })\n    dynamicRouteMatcher = getRouteMatcher(defaultRouteRegex)\n    defaultRouteMatches = dynamicRouteMatcher(page) as ParsedUrlQuery\n  }\n\n  function handleRewrites(req: BaseNextRequest, parsedUrl: UrlWithParsedQuery) {\n    const rewriteParams = {}\n    let fsPathname = parsedUrl.pathname\n\n    const matchesPage = () => {\n      const fsPathnameNoSlash = removeTrailingSlash(fsPathname || '')\n      return (\n        fsPathnameNoSlash === removeTrailingSlash(page) ||\n        dynamicRouteMatcher?.(fsPathnameNoSlash)\n      )\n    }\n\n    const checkRewrite = (rewrite: Rewrite): boolean => {\n      const matcher = getPathMatch(\n        rewrite.source + (trailingSlash ? '(/)?' : ''),\n        {\n          removeUnnamedParams: true,\n          strict: true,\n          sensitive: !!caseSensitive,\n        }\n      )\n\n      if (!parsedUrl.pathname) return false\n\n      let params = matcher(parsedUrl.pathname)\n\n      if ((rewrite.has || rewrite.missing) && params) {\n        const hasParams = matchHas(\n          req,\n          parsedUrl.query,\n          rewrite.has,\n          rewrite.missing\n        )\n\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        const { parsedDestination, destQuery } = prepareDestination({\n          appendParamsToQuery: true,\n          destination: rewrite.destination,\n          params: params,\n          query: parsedUrl.query,\n        })\n\n        // if the rewrite destination is external break rewrite chain\n        if (parsedDestination.protocol) {\n          return true\n        }\n\n        Object.assign(rewriteParams, destQuery, params)\n        Object.assign(parsedUrl.query, parsedDestination.query)\n        delete (parsedDestination as any).query\n\n        Object.assign(parsedUrl, parsedDestination)\n\n        fsPathname = parsedUrl.pathname\n        if (!fsPathname) return false\n\n        if (basePath) {\n          fsPathname = fsPathname.replace(new RegExp(`^${basePath}`), '') || '/'\n        }\n\n        if (i18n) {\n          const result = normalizeLocalePath(fsPathname, i18n.locales)\n          fsPathname = result.pathname\n          parsedUrl.query.nextInternalLocale =\n            result.detectedLocale || params.nextInternalLocale\n        }\n\n        if (fsPathname === page) {\n          return true\n        }\n\n        if (pageIsDynamic && dynamicRouteMatcher) {\n          const dynamicParams = dynamicRouteMatcher(fsPathname)\n          if (dynamicParams) {\n            parsedUrl.query = {\n              ...parsedUrl.query,\n              ...dynamicParams,\n            }\n            return true\n          }\n        }\n      }\n      return false\n    }\n\n    for (const rewrite of rewrites.beforeFiles || []) {\n      checkRewrite(rewrite)\n    }\n\n    if (fsPathname !== page) {\n      let finished = false\n\n      for (const rewrite of rewrites.afterFiles || []) {\n        finished = checkRewrite(rewrite)\n        if (finished) break\n      }\n\n      if (!finished && !matchesPage()) {\n        for (const rewrite of rewrites.fallback || []) {\n          finished = checkRewrite(rewrite)\n          if (finished) break\n        }\n      }\n    }\n    return rewriteParams\n  }\n\n  function getParamsFromRouteMatches(routeMatchesHeader: string) {\n    // If we don't have a default route regex, we can't get params from route\n    // matches\n    if (!defaultRouteRegex) return null\n\n    const { groups, routeKeys } = defaultRouteRegex\n\n    const matcher = getRouteMatcher({\n      re: {\n        // Simulate a RegExp match from the \\`req.url\\` input\n        exec: (str: string) => {\n          // Normalize all the prefixed query params.\n          const obj: Record<string, string> = Object.fromEntries(\n            new URLSearchParams(str)\n          )\n          for (const [key, value] of Object.entries(obj)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            obj[normalizedKey] = value\n            delete obj[key]\n          }\n\n          // Use all the named route keys.\n          const result = {} as RegExpExecArray\n          for (const keyName of Object.keys(routeKeys)) {\n            const paramName = routeKeys[keyName]\n\n            // If this param name is not a valid parameter name, then skip it.\n            if (!paramName) continue\n\n            const group = groups[paramName]\n            const value = obj[keyName]\n\n            // When we're missing a required param, we can't match the route.\n            if (!group.optional && !value) return null\n\n            result[group.pos] = value\n          }\n\n          return result\n        },\n      },\n      groups,\n    })\n\n    const routeMatches = matcher(routeMatchesHeader)\n    if (!routeMatches) return null\n\n    return routeMatches\n  }\n\n  return {\n    handleRewrites,\n    defaultRouteRegex,\n    dynamicRouteMatcher,\n    defaultRouteMatches,\n    getParamsFromRouteMatches,\n    /**\n     * Normalize dynamic route params.\n     *\n     * @param query - The query params to normalize.\n     * @param ignoreMissingOptional - Whether to ignore missing optional params.\n     * @returns The normalized params and whether they are valid.\n     */\n    normalizeDynamicRouteParams: (\n      query: ParsedUrlQuery,\n      ignoreMissingOptional: boolean\n    ) => {\n      if (!defaultRouteRegex || !defaultRouteMatches) {\n        return { params: {}, hasValidParams: false }\n      }\n\n      return normalizeDynamicRouteParams(\n        query,\n        defaultRouteRegex,\n        defaultRouteMatches,\n        ignoreMissingOptional\n      )\n    },\n    normalizeVercelUrl: (req: BaseNextRequest, paramKeys: string[]) =>\n      normalizeVercelUrl(req, paramKeys, defaultRouteRegex),\n    interpolateDynamicPath: (\n      pathname: string,\n      params: Record<string, undefined | string | string[]>\n    ) => interpolateDynamicPath(pathname, params, defaultRouteRegex),\n  }\n}\n"], "names": ["getUtils", "interpolateDynamicPath", "normalizeDynamicRouteParams", "normalizeVercelUrl", "req", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultRouteRegex", "_parsedUrl", "parseUrl", "url", "search", "key", "Object", "keys", "query", "isNextQueryPrefix", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "isNextInterceptionMarkerPrefix", "NEXT_INTERCEPTION_MARKER_PREFIX", "includes", "groups", "formatUrl", "pathname", "params", "param", "optional", "repeat", "builtParam", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "replaceAll", "defaultRouteMatches", "ignoreMissingOptional", "hasValidParams", "normalizeRscURL", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "val", "length", "undefined", "split", "page", "i18n", "basePath", "rewrites", "pageIsDynamic", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "getNamedRouteRegex", "prefixRouteKeys", "getRouteMatcher", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "removeTrailingSlash", "checkRewrite", "rewrite", "matcher", "getPathMatch", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "matchHas", "assign", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prepareDestination", "appendParamsToQuery", "destination", "protocol", "replace", "RegExp", "result", "normalizeLocalePath", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "routeMatchesHeader", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "entries", "normalizedKey", "normalizeNextQueryParam", "keyName", "paramName", "group", "pos", "routeMatches"], "mappings": ";;;;;;;;;;;;;;;;;IAkKgBA,QAAQ;eAARA;;IA3GAC,sBAAsB;eAAtBA;;IAgCAC,2BAA2B;eAA3BA;;IA/DAC,kBAAkB;eAAlBA;;;qBAjBuC;qCACnB;2BACP;4BACM;8BACH;oCAIzB;qCAC6B;0BACJ;2BAIzB;uBACiC;AAEjC,SAASA,mBACdC,GAAoB,EACpBC,SAAmB,EACnBC,iBAAoE;IAEpE,wEAAwE;IACxE,uDAAuD;IACvD,MAAMC,aAAaC,IAAAA,UAAQ,EAACJ,IAAIK,GAAG,EAAG;IACtC,OAAO,AAACF,WAAmBG,MAAM;IAEjC,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACN,WAAWO,KAAK,EAAG;QAC/C,MAAMC,oBACJJ,QAAQK,kCAAuB,IAAIL,IAAIM,UAAU,CAACD,kCAAuB;QAE3E,MAAME,iCACJP,QAAQQ,0CAA+B,IACvCR,IAAIM,UAAU,CAACE,0CAA+B;QAEhD,IACEJ,qBACAG,kCACAb,UAAUe,QAAQ,CAACT,QAClBL,qBAAqBM,OAAOC,IAAI,CAACP,kBAAkBe,MAAM,EAAED,QAAQ,CAACT,MACrE;YACA,OAAOJ,WAAWO,KAAK,CAACH,IAAI;QAC9B;IACF;IAEAP,IAAIK,GAAG,GAAGa,IAAAA,WAAS,EAACf;AACtB;AAEO,SAASN,uBACdsB,QAAgB,EAChBC,MAAsB,EACtBlB,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOiB;IAE/B,KAAK,MAAME,SAASb,OAAOC,IAAI,CAACP,kBAAkBe,MAAM,EAAG;QACzD,MAAM,EAAEK,QAAQ,EAAEC,MAAM,EAAE,GAAGrB,kBAAkBe,MAAM,CAACI,MAAM;QAC5D,IAAIG,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,KAAKF,MAAM,CAAC,CAAC;QAEnD,IAAIC,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,IAAIC;QACJ,MAAMC,QAAQN,MAAM,CAACC,MAAM;QAE3B,IAAIM,MAAMC,OAAO,CAACF,QAAQ;YACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;QACjE,OAAO,IAAIN,OAAO;YAChBD,aAAaM,mBAAmBL;QAClC,OAAO;YACLD,aAAa;QACf;QAEAN,WAAWA,SAASc,UAAU,CAACT,YAAYC;IAC7C;IAEA,OAAON;AACT;AAEO,SAASrB,4BACdY,KAAqB,EACrBR,iBAAwD,EACxDgC,mBAAmC,EACnCC,qBAA8B;IAE9B,IAAIC,iBAAiB;IACrB,IAAIhB,SAAyB,CAAC;IAE9B,KAAK,MAAMb,OAAOC,OAAOC,IAAI,CAACP,kBAAkBe,MAAM,EAAG;QACvD,IAAIS,QAAuChB,KAAK,CAACH,IAAI;QAErD,IAAI,OAAOmB,UAAU,UAAU;YAC7BA,QAAQW,IAAAA,yBAAe,EAACX;QAC1B,OAAO,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YAC/BA,QAAQA,MAAMG,GAAG,CAACQ,yBAAe;QACnC;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAMC,eAAeJ,mBAAoB,CAAC3B,IAAI;QAC9C,MAAMgC,aAAarC,kBAAmBe,MAAM,CAACV,IAAI,CAACe,QAAQ;QAE1D,MAAMkB,iBAAiBb,MAAMC,OAAO,CAACU,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOf,MAAMC,OAAO,CAACF,SACjBA,MAAMe,IAAI,CAAC,CAACE,MAAQA,IAAI3B,QAAQ,CAAC0B,eACjChB,yBAAAA,MAAOV,QAAQ,CAAC0B;QACtB,KACAhB,yBAAAA,MAAOV,QAAQ,CAACsB;QAEpB,IACEE,kBACC,OAAOd,UAAU,eAAe,CAAEa,CAAAA,cAAcJ,qBAAoB,GACrE;YACA,OAAO;gBAAEf,QAAQ,CAAC;gBAAGgB,gBAAgB;YAAM;QAC7C;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEG,cACC,CAAA,CAACb,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMkB,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9ClB,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAEnB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;YACAmB,QAAQmB;YACR,OAAOnC,KAAK,CAACH,IAAI;QACnB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEmB,SACA,OAAOA,UAAU,YACjBxB,kBAAmBe,MAAM,CAACV,IAAI,CAACgB,MAAM,EACrC;YACAG,QAAQA,MAAMoB,KAAK,CAAC;QACtB;QAEA,IAAIpB,OAAO;YACTN,MAAM,CAACb,IAAI,GAAGmB;QAChB;IACF;IAEA,OAAO;QACLN;QACAgB;IACF;AACF;AAEO,SAASxC,SAAS,EACvBmD,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,aAAa,EACbC,aAAa,EACbC,aAAa,EAad;IACC,IAAInD;IACJ,IAAIoD;IACJ,IAAIpB;IAEJ,IAAIiB,eAAe;QACjBjD,oBAAoBqD,IAAAA,8BAAkB,EAACR,MAAM;YAC3CS,iBAAiB;QACnB;QACAF,sBAAsBG,IAAAA,6BAAe,EAACvD;QACtCgC,sBAAsBoB,oBAAoBP;IAC5C;IAEA,SAASW,eAAe1D,GAAoB,EAAE2D,SAA6B;QACzE,MAAMC,gBAAgB,CAAC;QACvB,IAAIC,aAAaF,UAAUxC,QAAQ;QAEnC,MAAM2C,cAAc;YAClB,MAAMC,oBAAoBC,IAAAA,wCAAmB,EAACH,cAAc;YAC5D,OACEE,sBAAsBC,IAAAA,wCAAmB,EAACjB,UAC1CO,uCAAAA,oBAAsBS;QAE1B;QAEA,MAAME,eAAe,CAACC;YACpB,MAAMC,UAAUC,IAAAA,uBAAY,EAC1BF,QAAQG,MAAM,GAAIjB,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEkB,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAACnB;YACf;YAGF,IAAI,CAACM,UAAUxC,QAAQ,EAAE,OAAO;YAEhC,IAAIC,SAAS+C,QAAQR,UAAUxC,QAAQ;YAEvC,IAAI,AAAC+C,CAAAA,QAAQO,GAAG,IAAIP,QAAQQ,OAAO,AAAD,KAAMtD,QAAQ;gBAC9C,MAAMuD,YAAYC,IAAAA,4BAAQ,EACxB5E,KACA2D,UAAUjD,KAAK,EACfwD,QAAQO,GAAG,EACXP,QAAQQ,OAAO;gBAGjB,IAAIC,WAAW;oBACbnE,OAAOqE,MAAM,CAACzD,QAAQuD;gBACxB,OAAO;oBACLvD,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,MAAM,EAAE0D,iBAAiB,EAAEC,SAAS,EAAE,GAAGC,IAAAA,sCAAkB,EAAC;oBAC1DC,qBAAqB;oBACrBC,aAAahB,QAAQgB,WAAW;oBAChC9D,QAAQA;oBACRV,OAAOiD,UAAUjD,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAIoE,kBAAkBK,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEA3E,OAAOqE,MAAM,CAACjB,eAAemB,WAAW3D;gBACxCZ,OAAOqE,MAAM,CAAClB,UAAUjD,KAAK,EAAEoE,kBAAkBpE,KAAK;gBACtD,OAAO,AAACoE,kBAA0BpE,KAAK;gBAEvCF,OAAOqE,MAAM,CAAClB,WAAWmB;gBAEzBjB,aAAaF,UAAUxC,QAAQ;gBAC/B,IAAI,CAAC0C,YAAY,OAAO;gBAExB,IAAIZ,UAAU;oBACZY,aAAaA,WAAWuB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEpC,UAAU,GAAG,OAAO;gBACrE;gBAEA,IAAID,MAAM;oBACR,MAAMsC,SAASC,IAAAA,wCAAmB,EAAC1B,YAAYb,KAAKwC,OAAO;oBAC3D3B,aAAayB,OAAOnE,QAAQ;oBAC5BwC,UAAUjD,KAAK,CAAC+E,kBAAkB,GAChCH,OAAOI,cAAc,IAAItE,OAAOqE,kBAAkB;gBACtD;gBAEA,IAAI5B,eAAed,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAII,iBAAiBG,qBAAqB;oBACxC,MAAMqC,gBAAgBrC,oBAAoBO;oBAC1C,IAAI8B,eAAe;wBACjBhC,UAAUjD,KAAK,GAAG;4BAChB,GAAGiD,UAAUjD,KAAK;4BAClB,GAAGiF,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMzB,WAAWhB,SAAS0C,WAAW,IAAI,EAAE,CAAE;YAChD3B,aAAaC;QACf;QAEA,IAAIL,eAAed,MAAM;YACvB,IAAI8C,WAAW;YAEf,KAAK,MAAM3B,WAAWhB,SAAS4C,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAW5B,aAAaC;gBACxB,IAAI2B,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC/B,eAAe;gBAC/B,KAAK,MAAMI,WAAWhB,SAAS6C,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAW5B,aAAaC;oBACxB,IAAI2B,UAAU;gBAChB;YACF;QACF;QACA,OAAOjC;IACT;IAEA,SAASoC,0BAA0BC,kBAA0B;QAC3D,yEAAyE;QACzE,UAAU;QACV,IAAI,CAAC/F,mBAAmB,OAAO;QAE/B,MAAM,EAAEe,MAAM,EAAEiF,SAAS,EAAE,GAAGhG;QAE9B,MAAMiE,UAAUV,IAAAA,6BAAe,EAAC;YAC9B0C,IAAI;gBACF,qDAAqD;gBACrDC,MAAM,CAACC;oBACL,2CAA2C;oBAC3C,MAAMC,MAA8B9F,OAAO+F,WAAW,CACpD,IAAIC,gBAAgBH;oBAEtB,KAAK,MAAM,CAAC9F,KAAKmB,MAAM,IAAIlB,OAAOiG,OAAO,CAACH,KAAM;wBAC9C,MAAMI,gBAAgBC,IAAAA,8BAAuB,EAACpG;wBAC9C,IAAI,CAACmG,eAAe;wBAEpBJ,GAAG,CAACI,cAAc,GAAGhF;wBACrB,OAAO4E,GAAG,CAAC/F,IAAI;oBACjB;oBAEA,gCAAgC;oBAChC,MAAM+E,SAAS,CAAC;oBAChB,KAAK,MAAMsB,WAAWpG,OAAOC,IAAI,CAACyF,WAAY;wBAC5C,MAAMW,YAAYX,SAAS,CAACU,QAAQ;wBAEpC,kEAAkE;wBAClE,IAAI,CAACC,WAAW;wBAEhB,MAAMC,QAAQ7F,MAAM,CAAC4F,UAAU;wBAC/B,MAAMnF,QAAQ4E,GAAG,CAACM,QAAQ;wBAE1B,iEAAiE;wBACjE,IAAI,CAACE,MAAMxF,QAAQ,IAAI,CAACI,OAAO,OAAO;wBAEtC4D,MAAM,CAACwB,MAAMC,GAAG,CAAC,GAAGrF;oBACtB;oBAEA,OAAO4D;gBACT;YACF;YACArE;QACF;QAEA,MAAM+F,eAAe7C,QAAQ8B;QAC7B,IAAI,CAACe,cAAc,OAAO;QAE1B,OAAOA;IACT;IAEA,OAAO;QACLtD;QACAxD;QACAoD;QACApB;QACA8D;QACA;;;;;;KAMC,GACDlG,6BAA6B,CAC3BY,OACAyB;YAEA,IAAI,CAACjC,qBAAqB,CAACgC,qBAAqB;gBAC9C,OAAO;oBAAEd,QAAQ,CAAC;oBAAGgB,gBAAgB;gBAAM;YAC7C;YAEA,OAAOtC,4BACLY,OACAR,mBACAgC,qBACAC;QAEJ;QACApC,oBAAoB,CAACC,KAAsBC,YACzCF,mBAAmBC,KAAKC,WAAWC;QACrCL,wBAAwB,CACtBsB,UACAC,SACGvB,uBAAuBsB,UAAUC,QAAQlB;IAChD;AACF"}