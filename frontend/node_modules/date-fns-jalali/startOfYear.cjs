"use strict";
exports.startOfYear = startOfYear;
var _index = require("./toDate.cjs");

var _index2 = require("./_core/getFullYear.cjs");
var _index3 = require("./_core/setFullYear.cjs");

/**
 * The {@link startOfYear} function options.
 */

/**
 * @name startOfYear
 * @category Year Helpers
 * @summary Return the start of a year for the given date.
 *
 * @description
 * Return the start of a year for the given date.
 * The result will be in the local timezone.
 *
 * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).
 * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.
 *
 * @param date - The original date
 * @param options - The options
 *
 * @returns The start of a year
 *
 * @example
 * // The start of a year for 2 September 2014 11:55:00:
 * const result = startOfYear(new Date(2014, 8, 2, 11, 55, 00))
 * //=> Wed Jan 01 2014 00:00:00
 */
function startOfYear(date, options) {
  const date_ = (0, _index.toDate)(date, options?.in);
  (0, _index3.setFullYear)(date_, (0, _index2.getFullYear)(date_), 0, 1);
  date_.setHours(0, 0, 0, 0);
  return date_;
}
