import { toDate } from "./toDate.js";

import { isLeapYear as coreIsLeapYear } from "./_core/isLeapYear.js";
import { getFullYear as coreGetFullYear } from "./_core/getFullYear.js";

/**
 * @name isLeapYear
 * @category Year Helpers
 * @summary Is the given date in the leap year?
 *
 * @description
 * Is the given date in the leap year?
 *
 * @param date - The date to check
 * @param options - The options object
 *
 * @returns The date is in the leap year
 *
 * @example
 * // Is 1 September 2012 in the leap year?
 * const result = isLeapYear(new Date(2012, 8, 1))
 * //=> true
 */
export function isLeapYear(date, options) {
  const _date = toDate(date, options?.in);
  const year = coreGetFullYear(_date);
  return coreIsLeapYear(year);
}

// Fallback for modularized imports:
export default isLeapYear;
