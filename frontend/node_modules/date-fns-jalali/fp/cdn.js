(() => {
function _createForOfIteratorHelper(o, allowArrayLike) {var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];if (!it) {if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {if (it) o = it;var i = 0;var F = function F() {};return { s: F, n: function n() {if (i >= o.length) return { done: true };return { done: false, value: o[i++] };}, e: function e(_e) {throw _e;}, f: F };}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}var normalCompletion = true,didErr = false,err;return { s: function s() {it = it.call(o);}, n: function n() {var step = it.next();normalCompletion = step.done;return step;}, e: function e(_e2) {didErr = true;err = _e2;}, f: function f() {try {if (!normalCompletion && it.return != null) it.return();} finally {if (didErr) throw err;}} };}function _callSuper(t, o, e) {return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));}function _possibleConstructorReturn(self, call) {if (call && (_typeof(call) === "object" || typeof call === "function")) {return call;} else if (call !== void 0) {throw new TypeError("Derived constructors may only return object or undefined");}return _assertThisInitialized(self);}function _assertThisInitialized(self) {if (self === void 0) {throw new ReferenceError("this hasn't been initialised - super() hasn't been called");}return self;}function _getPrototypeOf(o) {_getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {return o.__proto__ || Object.getPrototypeOf(o);};return _getPrototypeOf(o);}function _inherits(subClass, superClass) {if (typeof superClass !== "function" && superClass !== null) {throw new TypeError("Super expression must either be null or a function");}subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } });Object.defineProperty(subClass, "prototype", { writable: false });if (superClass) _setPrototypeOf(subClass, superClass);}function _classCallCheck(instance, Constructor) {if (!(instance instanceof Constructor)) {throw new TypeError("Cannot call a class as a function");}}function _defineProperties(target, props) {for (var i = 0; i < props.length; i++) {var descriptor = props[i];descriptor.enumerable = descriptor.enumerable || false;descriptor.configurable = true;if ("value" in descriptor) descriptor.writable = true;Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);}}function _createClass(Constructor, protoProps, staticProps) {if (protoProps) _defineProperties(Constructor.prototype, protoProps);if (staticProps) _defineProperties(Constructor, staticProps);Object.defineProperty(Constructor, "prototype", { writable: false });return Constructor;}function _toArray(arr) {return _arrayWithHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableRest();}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, "string");return "symbol" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if ("object" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || "default");if ("object" != _typeof(i)) return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return ("string" === r ? String : Number)(t);}function _typeof(o) {"@babel/helpers - typeof";return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;}, _typeof(o);}function _toConsumableArray(arr) {return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();}function _nonIterableSpread() {throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}function _iterableToArray(iter) {if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);}function _arrayWithoutHoles(arr) {if (Array.isArray(arr)) return _arrayLikeToArray(arr);}function _construct(t, e, r) {if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);var o = [null];o.push.apply(o, e);var p = new (t.bind.apply(t, o))();return r && _setPrototypeOf(p, r.prototype), p;}function _isNativeReflectConstruct() {try {var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));} catch (t) {}return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {return !!t;})();}function _setPrototypeOf(o, p) {_setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {o.__proto__ = p;return o;};return _setPrototypeOf(o, p);}function _slicedToArray(arr, i) {return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();}function _nonIterableRest() {throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");}function _unsupportedIterableToArray(o, minLen) {if (!o) return;if (typeof o === "string") return _arrayLikeToArray(o, minLen);var n = Object.prototype.toString.call(o).slice(8, -1);if (n === "Object" && o.constructor) n = o.constructor.name;if (n === "Map" || n === "Set") return Array.from(o);if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);}function _arrayLikeToArray(arr, len) {if (len == null || len > arr.length) len = arr.length;for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];return arr2;}function _iterableToArrayLimit(r, l) {var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];if (null != t) {var e,n,i,u,a = [],f = !0,o = !1;try {if (i = (t = t.call(r)).next, 0 === l) {if (Object(t) !== t) return;f = !1;} else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);} catch (r) {o = !0, n = r;} finally {try {if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;} finally {if (o) throw n;}}return a;}}function _arrayWithHoles(arr) {if (Array.isArray(arr)) return arr;}var __defProp = Object.defineProperty;
var __export = function __export(target, all) {
  for (var name in all)
  __defProp(target, name, {
    get: all[name],
    enumerable: true,
    configurable: true,
    set: function set(newValue) {return all[name] = function () {return newValue;};}
  });
};

// ../../../../../../tmp/date-fns-jalali/fp.js
var exports_fp = {};
__export(exports_fp, {
  yearsToQuarters: function yearsToQuarters() {return yearsToQuarters3;},
  yearsToMonths: function yearsToMonths() {return yearsToMonths3;},
  yearsToDays: function yearsToDays() {return yearsToDays3;},
  weeksToDays: function weeksToDays() {return weeksToDays3;},
  transpose: function transpose() {return transpose4;},
  toDate: function toDate() {return toDate108;},
  subYearsWithOptions: function subYearsWithOptions() {return _subYearsWithOptions;},
  subYears: function subYears() {return subYears3;},
  subWithOptions: function subWithOptions() {return _subWithOptions;},
  subWeeksWithOptions: function subWeeksWithOptions() {return _subWeeksWithOptions;},
  subWeeks: function subWeeks() {return subWeeks3;},
  subSecondsWithOptions: function subSecondsWithOptions() {return _subSecondsWithOptions;},
  subSeconds: function subSeconds() {return subSeconds3;},
  subQuartersWithOptions: function subQuartersWithOptions() {return _subQuartersWithOptions;},
  subQuarters: function subQuarters() {return subQuarters3;},
  subMonthsWithOptions: function subMonthsWithOptions() {return _subMonthsWithOptions;},
  subMonths: function subMonths() {return subMonths4;},
  subMinutesWithOptions: function subMinutesWithOptions() {return _subMinutesWithOptions;},
  subMinutes: function subMinutes() {return subMinutes3;},
  subMillisecondsWithOptions: function subMillisecondsWithOptions() {return _subMillisecondsWithOptions;},
  subMilliseconds: function subMilliseconds() {return subMilliseconds3;},
  subISOWeekYearsWithOptions: function subISOWeekYearsWithOptions() {return _subISOWeekYearsWithOptions;},
  subISOWeekYears: function subISOWeekYears() {return subISOWeekYears4;},
  subHoursWithOptions: function subHoursWithOptions() {return _subHoursWithOptions;},
  subHours: function subHours() {return subHours3;},
  subDaysWithOptions: function subDaysWithOptions() {return _subDaysWithOptions;},
  subDays: function subDays() {return subDays5;},
  subBusinessDaysWithOptions: function subBusinessDaysWithOptions() {return _subBusinessDaysWithOptions;},
  subBusinessDays: function subBusinessDays() {return subBusinessDays3;},
  sub: function sub() {return sub3;},
  startOfYearWithOptions: function startOfYearWithOptions() {return _startOfYearWithOptions;},
  startOfYear: function startOfYear() {return startOfYear5;},
  startOfWeekYearWithOptions: function startOfWeekYearWithOptions() {return _startOfWeekYearWithOptions;},
  startOfWeekYear: function startOfWeekYear() {return startOfWeekYear5;},
  startOfWeekWithOptions: function startOfWeekWithOptions() {return _startOfWeekWithOptions;},
  startOfWeek: function startOfWeek() {return startOfWeek12;},
  startOfSecondWithOptions: function startOfSecondWithOptions() {return _startOfSecondWithOptions;},
  startOfSecond: function startOfSecond() {return startOfSecond4;},
  startOfQuarterWithOptions: function startOfQuarterWithOptions() {return _startOfQuarterWithOptions;},
  startOfQuarter: function startOfQuarter() {return startOfQuarter5;},
  startOfMonthWithOptions: function startOfMonthWithOptions() {return _startOfMonthWithOptions;},
  startOfMonth: function startOfMonth() {return startOfMonth6;},
  startOfMinuteWithOptions: function startOfMinuteWithOptions() {return _startOfMinuteWithOptions;},
  startOfMinute: function startOfMinute() {return startOfMinute4;},
  startOfISOWeekYearWithOptions: function startOfISOWeekYearWithOptions() {return _startOfISOWeekYearWithOptions;},
  startOfISOWeekYear: function startOfISOWeekYear() {return startOfISOWeekYear7;},
  startOfISOWeekWithOptions: function startOfISOWeekWithOptions() {return _startOfISOWeekWithOptions;},
  startOfISOWeek: function startOfISOWeek() {return startOfISOWeek11;},
  startOfHourWithOptions: function startOfHourWithOptions() {return _startOfHourWithOptions;},
  startOfHour: function startOfHour() {return startOfHour4;},
  startOfDecadeWithOptions: function startOfDecadeWithOptions() {return _startOfDecadeWithOptions;},
  startOfDecade: function startOfDecade() {return startOfDecade3;},
  startOfDayWithOptions: function startOfDayWithOptions() {return _startOfDayWithOptions;},
  startOfDay: function startOfDay() {return startOfDay5;},
  setYearWithOptions: function setYearWithOptions() {return _setYearWithOptions;},
  setYear: function setYear() {return setYear3;},
  setWithOptions: function setWithOptions() {return _setWithOptions;},
  setWeekYearWithOptions: function setWeekYearWithOptions() {return _setWeekYearWithOptions;},
  setWeekYear: function setWeekYear() {return setWeekYear3;},
  setWeekWithOptions: function setWeekWithOptions() {return _setWeekWithOptions;},
  setWeek: function setWeek() {return setWeek4;},
  setSecondsWithOptions: function setSecondsWithOptions() {return _setSecondsWithOptions;},
  setSeconds: function setSeconds() {return setSeconds3;},
  setQuarterWithOptions: function setQuarterWithOptions() {return _setQuarterWithOptions;},
  setQuarter: function setQuarter() {return setQuarter3;},
  setMonthWithOptions: function setMonthWithOptions() {return _setMonthWithOptions;},
  setMonth: function setMonth() {return setMonth19;},
  setMinutesWithOptions: function setMinutesWithOptions() {return _setMinutesWithOptions;},
  setMinutes: function setMinutes() {return setMinutes3;},
  setMillisecondsWithOptions: function setMillisecondsWithOptions() {return _setMillisecondsWithOptions;},
  setMilliseconds: function setMilliseconds() {return setMilliseconds3;},
  setISOWeekYearWithOptions: function setISOWeekYearWithOptions() {return _setISOWeekYearWithOptions;},
  setISOWeekYear: function setISOWeekYear() {return setISOWeekYear4;},
  setISOWeekWithOptions: function setISOWeekWithOptions() {return _setISOWeekWithOptions;},
  setISOWeek: function setISOWeek() {return setISOWeek4;},
  setISODayWithOptions: function setISODayWithOptions() {return _setISODayWithOptions;},
  setISODay: function setISODay() {return setISODay4;},
  setHoursWithOptions: function setHoursWithOptions() {return _setHoursWithOptions;},
  setHours: function setHours() {return setHours3;},
  setDayWithOptions: function setDayWithOptions() {return _setDayWithOptions;},
  setDayOfYearWithOptions: function setDayOfYearWithOptions() {return _setDayOfYearWithOptions;},
  setDayOfYear: function setDayOfYear() {return setDayOfYear3;},
  setDay: function setDay() {return setDay6;},
  setDateWithOptions: function setDateWithOptions() {return _setDateWithOptions;},
  setDate: function setDate() {return setDate18;},
  set: function set() {return set3;},
  secondsToMinutes: function secondsToMinutes() {return secondsToMinutes3;},
  secondsToMilliseconds: function secondsToMilliseconds() {return secondsToMilliseconds3;},
  secondsToHours: function secondsToHours() {return secondsToHours3;},
  roundToNearestMinutesWithOptions: function roundToNearestMinutesWithOptions() {return _roundToNearestMinutesWithOptions;},
  roundToNearestMinutes: function roundToNearestMinutes() {return roundToNearestMinutes3;},
  roundToNearestHoursWithOptions: function roundToNearestHoursWithOptions() {return _roundToNearestHoursWithOptions;},
  roundToNearestHours: function roundToNearestHours() {return roundToNearestHours3;},
  quartersToYears: function quartersToYears() {return quartersToYears3;},
  quartersToMonths: function quartersToMonths() {return quartersToMonths3;},
  previousWednesdayWithOptions: function previousWednesdayWithOptions() {return _previousWednesdayWithOptions;},
  previousWednesday: function previousWednesday() {return previousWednesday3;},
  previousTuesdayWithOptions: function previousTuesdayWithOptions() {return _previousTuesdayWithOptions;},
  previousTuesday: function previousTuesday() {return previousTuesday3;},
  previousThursdayWithOptions: function previousThursdayWithOptions() {return _previousThursdayWithOptions;},
  previousThursday: function previousThursday() {return previousThursday3;},
  previousSundayWithOptions: function previousSundayWithOptions() {return _previousSundayWithOptions;},
  previousSunday: function previousSunday() {return previousSunday3;},
  previousSaturdayWithOptions: function previousSaturdayWithOptions() {return _previousSaturdayWithOptions;},
  previousSaturday: function previousSaturday() {return previousSaturday3;},
  previousMondayWithOptions: function previousMondayWithOptions() {return _previousMondayWithOptions;},
  previousMonday: function previousMonday() {return previousMonday3;},
  previousFridayWithOptions: function previousFridayWithOptions() {return _previousFridayWithOptions;},
  previousFriday: function previousFriday() {return previousFriday3;},
  previousDayWithOptions: function previousDayWithOptions() {return _previousDayWithOptions;},
  previousDay: function previousDay() {return previousDay3;},
  parseWithOptions: function parseWithOptions() {return _parseWithOptions;},
  parseJSONWithOptions: function parseJSONWithOptions() {return _parseJSONWithOptions;},
  parseJSON: function parseJSON() {return parseJSON3;},
  parseISOWithOptions: function parseISOWithOptions() {return _parseISOWithOptions;},
  parseISO: function parseISO() {return parseISO3;},
  parse: function parse() {return parse4;},
  nextWednesdayWithOptions: function nextWednesdayWithOptions() {return _nextWednesdayWithOptions;},
  nextWednesday: function nextWednesday() {return nextWednesday3;},
  nextTuesdayWithOptions: function nextTuesdayWithOptions() {return _nextTuesdayWithOptions;},
  nextTuesday: function nextTuesday() {return nextTuesday3;},
  nextThursdayWithOptions: function nextThursdayWithOptions() {return _nextThursdayWithOptions;},
  nextThursday: function nextThursday() {return nextThursday3;},
  nextSundayWithOptions: function nextSundayWithOptions() {return _nextSundayWithOptions;},
  nextSunday: function nextSunday() {return nextSunday3;},
  nextSaturdayWithOptions: function nextSaturdayWithOptions() {return _nextSaturdayWithOptions;},
  nextSaturday: function nextSaturday() {return nextSaturday3;},
  nextMondayWithOptions: function nextMondayWithOptions() {return _nextMondayWithOptions;},
  nextMonday: function nextMonday() {return nextMonday3;},
  nextFridayWithOptions: function nextFridayWithOptions() {return _nextFridayWithOptions;},
  nextFriday: function nextFriday() {return nextFriday3;},
  nextDayWithOptions: function nextDayWithOptions() {return _nextDayWithOptions;},
  nextDay: function nextDay() {return nextDay3;},
  monthsToYears: function monthsToYears() {return monthsToYears3;},
  monthsToQuarters: function monthsToQuarters() {return monthsToQuarters3;},
  minutesToSeconds: function minutesToSeconds() {return minutesToSeconds3;},
  minutesToMilliseconds: function minutesToMilliseconds() {return minutesToMilliseconds3;},
  minutesToHours: function minutesToHours() {return minutesToHours3;},
  minWithOptions: function minWithOptions() {return _minWithOptions;},
  min: function min() {return min4;},
  millisecondsToSeconds: function millisecondsToSeconds() {return millisecondsToSeconds3;},
  millisecondsToMinutes: function millisecondsToMinutes() {return millisecondsToMinutes3;},
  millisecondsToHours: function millisecondsToHours() {return millisecondsToHours3;},
  milliseconds: function milliseconds() {return milliseconds3;},
  maxWithOptions: function maxWithOptions() {return _maxWithOptions;},
  max: function max() {return max4;},
  lightFormat: function lightFormat() {return lightFormat3;},
  lastDayOfYearWithOptions: function lastDayOfYearWithOptions() {return _lastDayOfYearWithOptions;},
  lastDayOfYear: function lastDayOfYear() {return lastDayOfYear3;},
  lastDayOfWeekWithOptions: function lastDayOfWeekWithOptions() {return _lastDayOfWeekWithOptions;},
  lastDayOfWeek: function lastDayOfWeek() {return lastDayOfWeek4;},
  lastDayOfQuarterWithOptions: function lastDayOfQuarterWithOptions() {return _lastDayOfQuarterWithOptions;},
  lastDayOfQuarter: function lastDayOfQuarter() {return lastDayOfQuarter3;},
  lastDayOfMonthWithOptions: function lastDayOfMonthWithOptions() {return _lastDayOfMonthWithOptions;},
  lastDayOfMonth: function lastDayOfMonth() {return lastDayOfMonth4;},
  lastDayOfISOWeekYearWithOptions: function lastDayOfISOWeekYearWithOptions() {return _lastDayOfISOWeekYearWithOptions;},
  lastDayOfISOWeekYear: function lastDayOfISOWeekYear() {return lastDayOfISOWeekYear3;},
  lastDayOfISOWeekWithOptions: function lastDayOfISOWeekWithOptions() {return _lastDayOfISOWeekWithOptions;},
  lastDayOfISOWeek: function lastDayOfISOWeek() {return lastDayOfISOWeek3;},
  lastDayOfDecadeWithOptions: function lastDayOfDecadeWithOptions() {return _lastDayOfDecadeWithOptions;},
  lastDayOfDecade: function lastDayOfDecade() {return lastDayOfDecade3;},
  isWithinIntervalWithOptions: function isWithinIntervalWithOptions() {return _isWithinIntervalWithOptions;},
  isWithinInterval: function isWithinInterval() {return isWithinInterval3;},
  isWeekendWithOptions: function isWeekendWithOptions() {return _isWeekendWithOptions;},
  isWeekend: function isWeekend() {return isWeekend6;},
  isWednesdayWithOptions: function isWednesdayWithOptions() {return _isWednesdayWithOptions;},
  isWednesday: function isWednesday() {return isWednesday3;},
  isValid: function isValid() {return isValid9;},
  isTuesdayWithOptions: function isTuesdayWithOptions() {return _isTuesdayWithOptions;},
  isTuesday: function isTuesday() {return isTuesday3;},
  isThursdayWithOptions: function isThursdayWithOptions() {return _isThursdayWithOptions;},
  isThursday: function isThursday() {return isThursday3;},
  isSundayWithOptions: function isSundayWithOptions() {return _isSundayWithOptions;},
  isSunday: function isSunday() {return isSunday3;},
  isSaturdayWithOptions: function isSaturdayWithOptions() {return _isSaturdayWithOptions;},
  isSaturday: function isSaturday() {return isSaturday3;},
  isSameYearWithOptions: function isSameYearWithOptions() {return _isSameYearWithOptions;},
  isSameYear: function isSameYear() {return isSameYear3;},
  isSameWeekWithOptions: function isSameWeekWithOptions() {return _isSameWeekWithOptions;},
  isSameWeek: function isSameWeek() {return isSameWeek4;},
  isSameSecond: function isSameSecond() {return isSameSecond3;},
  isSameQuarterWithOptions: function isSameQuarterWithOptions() {return _isSameQuarterWithOptions;},
  isSameQuarter: function isSameQuarter() {return isSameQuarter3;},
  isSameMonthWithOptions: function isSameMonthWithOptions() {return _isSameMonthWithOptions;},
  isSameMonth: function isSameMonth() {return isSameMonth3;},
  isSameMinute: function isSameMinute() {return isSameMinute3;},
  isSameISOWeekYearWithOptions: function isSameISOWeekYearWithOptions() {return _isSameISOWeekYearWithOptions;},
  isSameISOWeekYear: function isSameISOWeekYear() {return isSameISOWeekYear3;},
  isSameISOWeekWithOptions: function isSameISOWeekWithOptions() {return _isSameISOWeekWithOptions;},
  isSameISOWeek: function isSameISOWeek() {return isSameISOWeek3;},
  isSameHourWithOptions: function isSameHourWithOptions() {return _isSameHourWithOptions;},
  isSameHour: function isSameHour() {return isSameHour3;},
  isSameDayWithOptions: function isSameDayWithOptions() {return _isSameDayWithOptions;},
  isSameDay: function isSameDay() {return isSameDay4;},
  isMondayWithOptions: function isMondayWithOptions() {return _isMondayWithOptions;},
  isMonday: function isMonday() {return isMonday3;},
  isMatchWithOptions: function isMatchWithOptions() {return _isMatchWithOptions;},
  isMatch: function isMatch() {return isMatch3;},
  isLeapYearWithOptions: function isLeapYearWithOptions() {return _isLeapYearWithOptions;},
  isLeapYear: function isLeapYear() {return isLeapYear6;},
  isLastDayOfMonthWithOptions: function isLastDayOfMonthWithOptions() {return _isLastDayOfMonthWithOptions;},
  isLastDayOfMonth: function isLastDayOfMonth() {return isLastDayOfMonth4;},
  isFridayWithOptions: function isFridayWithOptions() {return _isFridayWithOptions;},
  isFriday: function isFriday() {return isFriday4;},
  isFirstDayOfMonthWithOptions: function isFirstDayOfMonthWithOptions() {return _isFirstDayOfMonthWithOptions;},
  isFirstDayOfMonth: function isFirstDayOfMonth() {return isFirstDayOfMonth3;},
  isExists: function isExists() {return isExists3;},
  isEqual: function isEqual() {return isEqual3;},
  isDate: function isDate() {return isDate4;},
  isBefore: function isBefore() {return isBefore3;},
  isAfter: function isAfter() {return isAfter3;},
  intlFormatDistanceWithOptions: function intlFormatDistanceWithOptions() {return _intlFormatDistanceWithOptions;},
  intlFormatDistance: function intlFormatDistance() {return intlFormatDistance3;},
  intlFormat: function intlFormat() {return intlFormat3;},
  intervalWithOptions: function intervalWithOptions() {return _intervalWithOptions;},
  intervalToDurationWithOptions: function intervalToDurationWithOptions() {return _intervalToDurationWithOptions;},
  intervalToDuration: function intervalToDuration() {return intervalToDuration3;},
  interval: function interval() {return interval3;},
  hoursToSeconds: function hoursToSeconds() {return hoursToSeconds3;},
  hoursToMinutes: function hoursToMinutes() {return hoursToMinutes3;},
  hoursToMilliseconds: function hoursToMilliseconds() {return hoursToMilliseconds3;},
  getYearWithOptions: function getYearWithOptions() {return _getYearWithOptions;},
  getYear: function getYear() {return getYear3;},
  getWeeksInMonthWithOptions: function getWeeksInMonthWithOptions() {return _getWeeksInMonthWithOptions;},
  getWeeksInMonth: function getWeeksInMonth() {return getWeeksInMonth3;},
  getWeekYearWithOptions: function getWeekYearWithOptions() {return _getWeekYearWithOptions;},
  getWeekYear: function getWeekYear() {return getWeekYear5;},
  getWeekWithOptions: function getWeekWithOptions() {return _getWeekWithOptions;},
  getWeekOfMonthWithOptions: function getWeekOfMonthWithOptions() {return _getWeekOfMonthWithOptions;},
  getWeekOfMonth: function getWeekOfMonth() {return getWeekOfMonth3;},
  getWeek: function getWeek() {return getWeek4;},
  getUnixTime: function getUnixTime() {return getUnixTime3;},
  getTime: function getTime() {return getTime3;},
  getSeconds: function getSeconds() {return getSeconds3;},
  getQuarterWithOptions: function getQuarterWithOptions() {return _getQuarterWithOptions;},
  getQuarter: function getQuarter() {return getQuarter4;},
  getOverlappingDaysInIntervals: function getOverlappingDaysInIntervals() {return getOverlappingDaysInIntervals3;},
  getMonthWithOptions: function getMonthWithOptions() {return _getMonthWithOptions;},
  getMonth: function getMonth() {return getMonth17;},
  getMinutesWithOptions: function getMinutesWithOptions() {return _getMinutesWithOptions;},
  getMinutes: function getMinutes() {return getMinutes3;},
  getMilliseconds: function getMilliseconds() {return getMilliseconds3;},
  getISOWeeksInYearWithOptions: function getISOWeeksInYearWithOptions() {return _getISOWeeksInYearWithOptions;},
  getISOWeeksInYear: function getISOWeeksInYear() {return getISOWeeksInYear3;},
  getISOWeekYearWithOptions: function getISOWeekYearWithOptions() {return _getISOWeekYearWithOptions;},
  getISOWeekYear: function getISOWeekYear() {return getISOWeekYear8;},
  getISOWeekWithOptions: function getISOWeekWithOptions() {return _getISOWeekWithOptions;},
  getISOWeek: function getISOWeek() {return getISOWeek4;},
  getISODayWithOptions: function getISODayWithOptions() {return _getISODayWithOptions;},
  getISODay: function getISODay() {return getISODay3;},
  getHoursWithOptions: function getHoursWithOptions() {return _getHoursWithOptions;},
  getHours: function getHours() {return getHours3;},
  getDecadeWithOptions: function getDecadeWithOptions() {return _getDecadeWithOptions;},
  getDecade: function getDecade() {return getDecade3;},
  getDaysInYearWithOptions: function getDaysInYearWithOptions() {return _getDaysInYearWithOptions;},
  getDaysInYear: function getDaysInYear() {return getDaysInYear3;},
  getDaysInMonthWithOptions: function getDaysInMonthWithOptions() {return _getDaysInMonthWithOptions;},
  getDaysInMonth: function getDaysInMonth() {return getDaysInMonth3;},
  getDayWithOptions: function getDayWithOptions() {return _getDayWithOptions;},
  getDayOfYearWithOptions: function getDayOfYearWithOptions() {return _getDayOfYearWithOptions;},
  getDayOfYear: function getDayOfYear() {return getDayOfYear4;},
  getDay: function getDay() {return getDay3;},
  getDateWithOptions: function getDateWithOptions() {return _getDateWithOptions;},
  getDate: function getDate() {return getDate15;},
  fromUnixTimeWithOptions: function fromUnixTimeWithOptions() {return _fromUnixTimeWithOptions;},
  fromUnixTime: function fromUnixTime() {return fromUnixTime3;},
  formatWithOptions: function formatWithOptions() {return _formatWithOptions;},
  formatRelativeWithOptions: function formatRelativeWithOptions() {return _formatRelativeWithOptions;},
  formatRelative: function formatRelative() {return formatRelative5;},
  formatRFC7231: function formatRFC7231() {return formatRFC72313;},
  formatRFC3339WithOptions: function formatRFC3339WithOptions() {return _formatRFC3339WithOptions;},
  formatRFC3339: function formatRFC3339() {return formatRFC33393;},
  formatISOWithOptions: function formatISOWithOptions() {return _formatISOWithOptions;},
  formatISODuration: function formatISODuration() {return formatISODuration3;},
  formatISO9075WithOptions: function formatISO9075WithOptions() {return _formatISO9075WithOptions;},
  formatISO9075: function formatISO9075() {return formatISO90753;},
  formatISO: function formatISO() {return formatISO3;},
  formatDurationWithOptions: function formatDurationWithOptions() {return _formatDurationWithOptions;},
  formatDuration: function formatDuration() {return formatDuration3;},
  formatDistanceWithOptions: function formatDistanceWithOptions() {return _formatDistanceWithOptions;},
  formatDistanceStrictWithOptions: function formatDistanceStrictWithOptions() {return _formatDistanceStrictWithOptions;},
  formatDistanceStrict: function formatDistanceStrict() {return formatDistanceStrict3;},
  formatDistance: function formatDistance() {return formatDistance5;},
  format: function format() {return format3;},
  endOfYearWithOptions: function endOfYearWithOptions() {return _endOfYearWithOptions;},
  endOfYear: function endOfYear() {return endOfYear4;},
  endOfWeekWithOptions: function endOfWeekWithOptions() {return _endOfWeekWithOptions;},
  endOfWeek: function endOfWeek() {return endOfWeek4;},
  endOfSecondWithOptions: function endOfSecondWithOptions() {return _endOfSecondWithOptions;},
  endOfSecond: function endOfSecond() {return endOfSecond3;},
  endOfQuarterWithOptions: function endOfQuarterWithOptions() {return _endOfQuarterWithOptions;},
  endOfQuarter: function endOfQuarter() {return endOfQuarter3;},
  endOfMonthWithOptions: function endOfMonthWithOptions() {return _endOfMonthWithOptions;},
  endOfMonth: function endOfMonth() {return endOfMonth5;},
  endOfMinuteWithOptions: function endOfMinuteWithOptions() {return _endOfMinuteWithOptions;},
  endOfMinute: function endOfMinute() {return endOfMinute3;},
  endOfISOWeekYearWithOptions: function endOfISOWeekYearWithOptions() {return _endOfISOWeekYearWithOptions;},
  endOfISOWeekYear: function endOfISOWeekYear() {return endOfISOWeekYear3;},
  endOfISOWeekWithOptions: function endOfISOWeekWithOptions() {return _endOfISOWeekWithOptions;},
  endOfISOWeek: function endOfISOWeek() {return endOfISOWeek3;},
  endOfHourWithOptions: function endOfHourWithOptions() {return _endOfHourWithOptions;},
  endOfHour: function endOfHour() {return endOfHour3;},
  endOfDecadeWithOptions: function endOfDecadeWithOptions() {return _endOfDecadeWithOptions;},
  endOfDecade: function endOfDecade() {return endOfDecade3;},
  endOfDayWithOptions: function endOfDayWithOptions() {return _endOfDayWithOptions;},
  endOfDay: function endOfDay() {return endOfDay4;},
  eachYearOfIntervalWithOptions: function eachYearOfIntervalWithOptions() {return _eachYearOfIntervalWithOptions;},
  eachYearOfInterval: function eachYearOfInterval() {return eachYearOfInterval3;},
  eachWeekendOfYearWithOptions: function eachWeekendOfYearWithOptions() {return _eachWeekendOfYearWithOptions;},
  eachWeekendOfYear: function eachWeekendOfYear() {return eachWeekendOfYear3;},
  eachWeekendOfMonthWithOptions: function eachWeekendOfMonthWithOptions() {return _eachWeekendOfMonthWithOptions;},
  eachWeekendOfMonth: function eachWeekendOfMonth() {return eachWeekendOfMonth3;},
  eachWeekendOfIntervalWithOptions: function eachWeekendOfIntervalWithOptions() {return _eachWeekendOfIntervalWithOptions;},
  eachWeekendOfInterval: function eachWeekendOfInterval() {return eachWeekendOfInterval3;},
  eachWeekOfIntervalWithOptions: function eachWeekOfIntervalWithOptions() {return _eachWeekOfIntervalWithOptions;},
  eachWeekOfInterval: function eachWeekOfInterval() {return eachWeekOfInterval3;},
  eachQuarterOfIntervalWithOptions: function eachQuarterOfIntervalWithOptions() {return _eachQuarterOfIntervalWithOptions;},
  eachQuarterOfInterval: function eachQuarterOfInterval() {return eachQuarterOfInterval3;},
  eachMonthOfIntervalWithOptions: function eachMonthOfIntervalWithOptions() {return _eachMonthOfIntervalWithOptions;},
  eachMonthOfInterval: function eachMonthOfInterval() {return eachMonthOfInterval3;},
  eachMinuteOfIntervalWithOptions: function eachMinuteOfIntervalWithOptions() {return _eachMinuteOfIntervalWithOptions;},
  eachMinuteOfInterval: function eachMinuteOfInterval() {return eachMinuteOfInterval3;},
  eachHourOfIntervalWithOptions: function eachHourOfIntervalWithOptions() {return _eachHourOfIntervalWithOptions;},
  eachHourOfInterval: function eachHourOfInterval() {return eachHourOfInterval3;},
  eachDayOfIntervalWithOptions: function eachDayOfIntervalWithOptions() {return _eachDayOfIntervalWithOptions;},
  eachDayOfInterval: function eachDayOfInterval() {return eachDayOfInterval3;},
  differenceInYearsWithOptions: function differenceInYearsWithOptions() {return _differenceInYearsWithOptions;},
  differenceInYears: function differenceInYears() {return differenceInYears3;},
  differenceInWeeksWithOptions: function differenceInWeeksWithOptions() {return _differenceInWeeksWithOptions;},
  differenceInWeeks: function differenceInWeeks() {return differenceInWeeks3;},
  differenceInSecondsWithOptions: function differenceInSecondsWithOptions() {return _differenceInSecondsWithOptions;},
  differenceInSeconds: function differenceInSeconds() {return differenceInSeconds3;},
  differenceInQuartersWithOptions: function differenceInQuartersWithOptions() {return _differenceInQuartersWithOptions;},
  differenceInQuarters: function differenceInQuarters() {return differenceInQuarters3;},
  differenceInMonthsWithOptions: function differenceInMonthsWithOptions() {return _differenceInMonthsWithOptions;},
  differenceInMonths: function differenceInMonths() {return differenceInMonths3;},
  differenceInMinutesWithOptions: function differenceInMinutesWithOptions() {return _differenceInMinutesWithOptions;},
  differenceInMinutes: function differenceInMinutes() {return differenceInMinutes3;},
  differenceInMilliseconds: function differenceInMilliseconds() {return differenceInMilliseconds3;},
  differenceInISOWeekYearsWithOptions: function differenceInISOWeekYearsWithOptions() {return _differenceInISOWeekYearsWithOptions;},
  differenceInISOWeekYears: function differenceInISOWeekYears() {return differenceInISOWeekYears3;},
  differenceInHoursWithOptions: function differenceInHoursWithOptions() {return _differenceInHoursWithOptions;},
  differenceInHours: function differenceInHours() {return differenceInHours3;},
  differenceInDaysWithOptions: function differenceInDaysWithOptions() {return _differenceInDaysWithOptions;},
  differenceInDays: function differenceInDays() {return differenceInDays3;},
  differenceInCalendarYearsWithOptions: function differenceInCalendarYearsWithOptions() {return _differenceInCalendarYearsWithOptions;},
  differenceInCalendarYears: function differenceInCalendarYears() {return differenceInCalendarYears3;},
  differenceInCalendarWeeksWithOptions: function differenceInCalendarWeeksWithOptions() {return _differenceInCalendarWeeksWithOptions;},
  differenceInCalendarWeeks: function differenceInCalendarWeeks() {return differenceInCalendarWeeks3;},
  differenceInCalendarQuartersWithOptions: function differenceInCalendarQuartersWithOptions() {return _differenceInCalendarQuartersWithOptions;},
  differenceInCalendarQuarters: function differenceInCalendarQuarters() {return differenceInCalendarQuarters3;},
  differenceInCalendarMonthsWithOptions: function differenceInCalendarMonthsWithOptions() {return _differenceInCalendarMonthsWithOptions;},
  differenceInCalendarMonths: function differenceInCalendarMonths() {return differenceInCalendarMonths3;},
  differenceInCalendarISOWeeksWithOptions: function differenceInCalendarISOWeeksWithOptions() {return _differenceInCalendarISOWeeksWithOptions;},
  differenceInCalendarISOWeeks: function differenceInCalendarISOWeeks() {return differenceInCalendarISOWeeks3;},
  differenceInCalendarISOWeekYearsWithOptions: function differenceInCalendarISOWeekYearsWithOptions() {return _differenceInCalendarISOWeekYearsWithOptions;},
  differenceInCalendarISOWeekYears: function differenceInCalendarISOWeekYears() {return differenceInCalendarISOWeekYears3;},
  differenceInCalendarDaysWithOptions: function differenceInCalendarDaysWithOptions() {return _differenceInCalendarDaysWithOptions;},
  differenceInCalendarDays: function differenceInCalendarDays() {return differenceInCalendarDays5;},
  differenceInBusinessDaysWithOptions: function differenceInBusinessDaysWithOptions() {return _differenceInBusinessDaysWithOptions;},
  differenceInBusinessDays: function differenceInBusinessDays() {return differenceInBusinessDays3;},
  daysToWeeks: function daysToWeeks() {return daysToWeeks3;},
  constructFrom: function constructFrom() {return constructFrom16;},
  compareDesc: function compareDesc() {return compareDesc3;},
  compareAsc: function compareAsc() {return compareAsc3;},
  closestToWithOptions: function closestToWithOptions() {return _closestToWithOptions;},
  closestTo: function closestTo() {return closestTo3;},
  closestIndexTo: function closestIndexTo() {return closestIndexTo3;},
  clampWithOptions: function clampWithOptions() {return _clampWithOptions;},
  clamp: function clamp() {return clamp3;},
  areIntervalsOverlappingWithOptions: function areIntervalsOverlappingWithOptions() {return _areIntervalsOverlappingWithOptions;},
  areIntervalsOverlapping: function areIntervalsOverlapping() {return areIntervalsOverlapping3;},
  addYearsWithOptions: function addYearsWithOptions() {return _addYearsWithOptions;},
  addYears: function addYears() {return addYears3;},
  addWithOptions: function addWithOptions() {return _addWithOptions;},
  addWeeksWithOptions: function addWeeksWithOptions() {return _addWeeksWithOptions;},
  addWeeks: function addWeeks() {return addWeeks3;},
  addSecondsWithOptions: function addSecondsWithOptions() {return _addSecondsWithOptions;},
  addSeconds: function addSeconds() {return addSeconds3;},
  addQuartersWithOptions: function addQuartersWithOptions() {return _addQuartersWithOptions;},
  addQuarters: function addQuarters() {return addQuarters3;},
  addMonthsWithOptions: function addMonthsWithOptions() {return _addMonthsWithOptions;},
  addMonths: function addMonths() {return addMonths4;},
  addMinutesWithOptions: function addMinutesWithOptions() {return _addMinutesWithOptions;},
  addMinutes: function addMinutes() {return addMinutes3;},
  addMillisecondsWithOptions: function addMillisecondsWithOptions() {return _addMillisecondsWithOptions;},
  addMilliseconds: function addMilliseconds() {return addMilliseconds4;},
  addISOWeekYearsWithOptions: function addISOWeekYearsWithOptions() {return _addISOWeekYearsWithOptions;},
  addISOWeekYears: function addISOWeekYears() {return addISOWeekYears3;},
  addHoursWithOptions: function addHoursWithOptions() {return _addHoursWithOptions;},
  addHours: function addHours() {return addHours3;},
  addDaysWithOptions: function addDaysWithOptions() {return _addDaysWithOptions;},
  addDays: function addDays() {return addDays4;},
  addBusinessDaysWithOptions: function addBusinessDaysWithOptions() {return _addBusinessDaysWithOptions;},
  addBusinessDays: function addBusinessDays() {return addBusinessDays3;},
  add: function add() {return add3;}
});

// ../../../../../../tmp/date-fns-jalali/constants.js
var daysInWeek = 7;
var daysInYear = 365.2425;
var maxTime = Math.pow(10, 8) * 24 * 60 * 60 * 1000;
var minTime = -maxTime;
var millisecondsInWeek = 604800000;
var millisecondsInDay = 86400000;
var millisecondsInMinute = 60000;
var millisecondsInHour = 3600000;
var millisecondsInSecond = 1000;
var minutesInYear = 525600;
var minutesInMonth = 43200;
var minutesInDay = 1440;
var minutesInHour = 60;
var monthsInQuarter = 3;
var monthsInYear = 12;
var quartersInYear = 4;
var secondsInHour = 3600;
var secondsInMinute = 60;
var secondsInDay = secondsInHour * 24;
var secondsInWeek = secondsInDay * 7;
var secondsInYear = secondsInDay * daysInYear;
var secondsInMonth = secondsInYear / 12;
var secondsInQuarter = secondsInMonth * 3;
var constructFromSymbol = Symbol.for("constructDateFrom");

// ../../../../../../tmp/date-fns-jalali/_lib/jalali.js
function toJalali(gy, gm, gd) {
  return d2j(g2d(gy, gm, gd));
}
function toGregorian(jy, jm, jd) {
  return d2g(j2d(jy, jm, jd));
}
function isLeapJalaliYear(jy) {
  if (jy === -3) {
    return false;
  }
  var m = mod(25 * jy + 11, 33);
  return m < 8 && m >= -1 || m <= -27;
}
function j2d(jy, jm, jd) {
  var _normalizeMonth = normalizeMonth(jy, jm),_normalizeMonth2 = _slicedToArray(_normalizeMonth, 2),ny = _normalizeMonth2[0],nm = _normalizeMonth2[1];
  jy = ny;
  jm = nm;
  var month = jm - 1;
  var year = jy;
  var day = jd;
  var julianDay = PERSIAN_EPOCH - 1 + 365 * (year - 1) + div(8 * year + 21, 33);
  if (month != 0) {
    julianDay += PERSIAN_NUM_DAYS[month];
  }
  return julianDay + day;
}
function d2j(julianDay) {
  if (isNaN(julianDay)) {
    return { jy: NaN, jm: NaN, jd: NaN };
  }
  var month, dayOfYear;
  var daysSinceEpoch = julianDay - PERSIAN_EPOCH;
  var year = 1 + div(33 * daysSinceEpoch + 3, 12053);
  dayOfYear = daysSinceEpoch - (365 * (year - 1) + div(8 * year + 21, 33));
  if (dayOfYear < 0) {
    year--;
    dayOfYear = daysSinceEpoch - (365 * (year - 1) + div(8 * year + 21, 33));
  }
  if (dayOfYear < 216) {
    month = div(dayOfYear, 31);
  } else {
    month = div(dayOfYear - 6, 30);
  }
  var dayOfMonth = dayOfYear - PERSIAN_NUM_DAYS[month] + 1;
  dayOfYear++;
  var jy = year;
  var jm = month + 1;
  var jd = dayOfMonth;
  return { jy: jy, jm: jm, jd: jd };
}
function g2d(gy, gm, gd) {
  var _normalizeMonth3 = normalizeMonth(gy, gm),_normalizeMonth4 = _slicedToArray(_normalizeMonth3, 2),ny = _normalizeMonth4[0],nm = _normalizeMonth4[1];
  gy = ny;
  gm = nm;
  return div(1461 * (gy + 4800 + div(gm - 14, 12)), 4) + div(367 * (gm - 2 - 12 * div(gm - 14, 12)), 12) - div(3 * div(gy + 4900 + div(gm - 14, 12), 100), 4) + gd - 32075;
}
function d2g(jdn) {
  if (isNaN(jdn)) {
    return { gy: NaN, gm: NaN, gd: NaN };
  }
  var L = jdn + 68569;
  var n = div(4 * L, 146097);
  L = L - div(146097 * n + 3, 4);
  var i = div(4000 * (L + 1), 1461001);
  L = L - div(1461 * i, 4) + 31;
  var j = div(80 * L, 2447);
  var gd = L - div(2447 * j, 80);
  L = div(j, 11);
  var gm = j + 2 - 12 * L;
  var gy = 100 * (n - 49) + i + L;
  return { gy: gy, gm: gm, gd: gd };
}
function normalizeMonth(year, month) {
  month = month - 1;
  if (month < 0) {
    var old_month = month;
    month = pmod(month, 12);
    year -= div(month - old_month, 12);
  }
  if (month > 11) {
    year += div(month, 12);
    month = mod(month, 12);
  }
  return [year, month + 1];
}
function div(a, b) {
  return ~~(a / b);
}
function mod(a, b) {
  return a - ~~(a / b) * b;
}
function pmod(a, b) {
  return mod(mod(a, b) + b, b);
}
var PERSIAN_EPOCH = 1948320;
var PERSIAN_NUM_DAYS = [
0,
31,
62,
93,
124,
155,
186,
216,
246,
276,
306,
336];


// ../../../../../../tmp/date-fns-jalali/_core/newDate.js
function newDate() {for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {args[_key] = arguments[_key];}
  if (args.length > 1) {
    var year = args[0],month = args[1],_args$ = args[2],day = _args$ === void 0 ? 1 : _args$,rest = args.slice(3);
    var g = toGregorian(year, month + 1, day);
    return _construct(Date, [g.gy, g.gm - 1, g.gd].concat(_toConsumableArray(rest)));
  }
  return _construct(Date, args);
}

// ../../../../../../tmp/date-fns-jalali/constructFrom.js
function constructFrom(date, value) {
  if (typeof date === "function")
  return date(value);
  if (date && _typeof(date) === "object" && constructFromSymbol in date)
  return date[constructFromSymbol](value);
  if (date instanceof Date)
  return new date.constructor(value);
  return newDate(value);
}

// ../../../../../../tmp/date-fns-jalali/toDate.js
function toDate(argument, context) {
  return constructFrom(context || argument, argument);
}

// ../../../../../../tmp/date-fns-jalali/_core/getDate.js
function getDate(cleanDate) {
  var gd = cleanDate.getDate();
  var gm = cleanDate.getMonth() + 1;
  var gy = cleanDate.getFullYear();
  return toJalali(gy, gm, gd).jd;
}

// ../../../../../../tmp/date-fns-jalali/_core/setDate.js
function setDate(cleanDate) {
  var gd = cleanDate.getDate();
  var gm = cleanDate.getMonth() + 1;
  var gy = cleanDate.getFullYear();
  var j = toJalali(gy, gm, gd);for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {args[_key2 - 1] = arguments[_key2];}
  var date = args[0];
  var g = toGregorian(j.jy, j.jm, date);
  return cleanDate.setFullYear(g.gy, g.gm - 1, g.gd);
}

// ../../../../../../tmp/date-fns-jalali/addDays.js
function addDays(date, amount, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  if (isNaN(amount))
  return constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, NaN);
  if (!amount)
  return _date;
  setDate(_date, getDate(_date) + amount);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/_core/getMonth.js
function getMonth(cleanDate) {
  var gd = cleanDate.getDate();
  var gm = cleanDate.getMonth() + 1;
  var gy = cleanDate.getFullYear();
  return toJalali(gy, gm, gd).jm - 1;
}

// ../../../../../../tmp/date-fns-jalali/_core/setMonth.js
function setMonth(cleanDate) {
  var gd = cleanDate.getDate();
  var gm = cleanDate.getMonth() + 1;
  var gy = cleanDate.getFullYear();
  var j = toJalali(gy, gm, gd);for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {args[_key3 - 1] = arguments[_key3];}
  var month = args[0],_args$2 = args[1],date = _args$2 === void 0 ? j.jd : _args$2;
  var g = toGregorian(j.jy, month + 1, date);
  return cleanDate.setFullYear(g.gy, g.gm - 1, g.gd);
}

// ../../../../../../tmp/date-fns-jalali/_core/getFullYear.js
function getFullYear(cleanDate) {
  var gd = cleanDate.getDate();
  var gm = cleanDate.getMonth() + 1;
  var gy = cleanDate.getFullYear();
  return toJalali(gy, gm, gd).jy;
}

// ../../../../../../tmp/date-fns-jalali/_core/setFullYear.js
function setFullYear(cleanDate) {
  var gd = cleanDate.getDate();
  var gm = cleanDate.getMonth() + 1;
  var gy = cleanDate.getFullYear();
  var j = toJalali(gy, gm, gd);for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {args[_key4 - 1] = arguments[_key4];}
  var year = args[0],_args$3 = args[1],month = _args$3 === void 0 ? j.jm - 1 : _args$3,_args$4 = args[2],date = _args$4 === void 0 ? j.jd : _args$4;
  var g = toGregorian(year, month + 1, date);
  return cleanDate.setFullYear(g.gy, g.gm - 1, g.gd);
}

// ../../../../../../tmp/date-fns-jalali/addMonths.js
function addMonths(date, amount, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  if (isNaN(amount))
  return constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, NaN);
  if (!amount) {
    return _date;
  }
  var dayOfMonth = getDate(_date);
  var endOfDesiredMonth = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, _date.getTime());
  setMonth(endOfDesiredMonth, getMonth(_date) + amount + 1, 0);
  var daysInMonth = getDate(endOfDesiredMonth);
  if (dayOfMonth >= daysInMonth) {
    return endOfDesiredMonth;
  } else {
    setFullYear(_date, getFullYear(endOfDesiredMonth), getMonth(endOfDesiredMonth), dayOfMonth);
    return _date;
  }
}

// ../../../../../../tmp/date-fns-jalali/add.js
function add(date, duration, options) {
  var _duration$years =







    duration.years,years = _duration$years === void 0 ? 0 : _duration$years,_duration$months = duration.months,months = _duration$months === void 0 ? 0 : _duration$months,_duration$weeks = duration.weeks,weeks = _duration$weeks === void 0 ? 0 : _duration$weeks,_duration$days = duration.days,days = _duration$days === void 0 ? 0 : _duration$days,_duration$hours = duration.hours,hours = _duration$hours === void 0 ? 0 : _duration$hours,_duration$minutes = duration.minutes,minutes = _duration$minutes === void 0 ? 0 : _duration$minutes,_duration$seconds = duration.seconds,seconds = _duration$seconds === void 0 ? 0 : _duration$seconds;
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var dateWithMonths = months || years ? addMonths(_date, months + years * 12) : _date;
  var dateWithDays = days || weeks ? addDays(dateWithMonths, days + weeks * 7) : dateWithMonths;
  var minutesToAdd = minutes + hours * 60;
  var secondsToAdd = seconds + minutesToAdd * 60;
  var msToAdd = secondsToAdd * 1000;
  return constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, +dateWithDays + msToAdd);
}

// ../../../../../../tmp/date-fns-jalali/fp/_lib/convertToFP.js
function convertToFP(fn, arity) {var curriedArgs = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];
  return curriedArgs.length >= arity ? fn.apply(void 0, _toConsumableArray(curriedArgs.slice(0, arity).reverse())) : function () {for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {args[_key5] = arguments[_key5];}return convertToFP(fn, arity, curriedArgs.concat(args));};
}

// ../../../../../../tmp/date-fns-jalali/fp/add.js
var add3 = convertToFP(add, 2);
// ../../../../../../tmp/date-fns-jalali/isFriday.js
function isFriday(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay() === 5;
}

// ../../../../../../tmp/date-fns-jalali/isWeekend.js
function isWeekend(date, options) {
  var day = toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay();
  return day === 5;
}

// ../../../../../../tmp/date-fns-jalali/addBusinessDays.js
function addBusinessDays(date, amount, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var startedOnWeekend = isWeekend(_date, options);
  if (isNaN(amount))
  return constructFrom(options === null || options === void 0 ? void 0 : options.in, NaN);
  var hours = _date.getHours();
  var sign = amount < 0 ? -1 : 1;
  var fullWeeks = Math.trunc(amount / 6);
  setDate(_date, getDate(_date) + fullWeeks * 7);
  var restDays = Math.abs(amount % 6);
  while (restDays > 0) {
    setDate(_date, getDate(_date) + sign);
    if (!isWeekend(_date, options))
    restDays -= 1;
  }
  if (startedOnWeekend && isWeekend(_date, options) && amount !== 0) {
    if (isFriday(_date, options))
    setDate(_date, getDate(_date) + (sign < 0 ? 1 : -2));
  }
  _date.setHours(hours);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/addBusinessDays.js
var addBusinessDays3 = convertToFP(addBusinessDays, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addBusinessDaysWithOptions.js
var _addBusinessDaysWithOptions = convertToFP(addBusinessDays, 3);
// ../../../../../../tmp/date-fns-jalali/fp/addDays.js
var addDays4 = convertToFP(addDays, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addDaysWithOptions.js
var _addDaysWithOptions = convertToFP(addDays, 3);
// ../../../../../../tmp/date-fns-jalali/addMilliseconds.js
function addMilliseconds(date, amount, options) {
  return constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, +toDate(date) + amount);
}

// ../../../../../../tmp/date-fns-jalali/addHours.js
function addHours(date, amount, options) {
  return addMilliseconds(date, amount * millisecondsInHour, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/addHours.js
var addHours3 = convertToFP(addHours, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addHoursWithOptions.js
var _addHoursWithOptions = convertToFP(addHours, 3);
// ../../../../../../tmp/date-fns-jalali/_lib/defaultOptions.js
function getDefaultOptions() {
  return defaultOptions;
}
function setDefaultOptions(newOptions) {
  defaultOptions = newOptions;
}
var defaultOptions = {};

// ../../../../../../tmp/date-fns-jalali/startOfWeek.js
function startOfWeek(date, options) {var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _defaultOptions3$loca;
  var defaultOptions3 = getDefaultOptions();
  var weekStartsOn = (_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 || (_options$locale = options.locale) === null || _options$locale === void 0 || (_options$locale = _options$locale.options) === null || _options$locale === void 0 ? void 0 : _options$locale.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions3.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions3$loca = defaultOptions3.locale) === null || _defaultOptions3$loca === void 0 || (_defaultOptions3$loca = _defaultOptions3$loca.options) === null || _defaultOptions3$loca === void 0 ? void 0 : _defaultOptions3$loca.weekStartsOn) !== null && _ref !== void 0 ? _ref : 6;
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var day = _date.getDay();
  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
  setDate(_date, getDate(_date) - diff);
  _date.setHours(0, 0, 0, 0);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/startOfISOWeek.js
function startOfISOWeek(date, options) {
  return startOfWeek(date, _objectSpread(_objectSpread({}, options), {}, { weekStartsOn: 1 }));
}

// ../../../../../../tmp/date-fns-jalali/getISOWeekYear.js
function getISOWeekYear(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = _date.getFullYear();
  var fourthOfJanuaryOfNextYear = constructFrom(_date, 0);
  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);
  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);
  var startOfNextYear = startOfISOWeek(fourthOfJanuaryOfNextYear);
  var fourthOfJanuaryOfThisYear = constructFrom(_date, 0);
  fourthOfJanuaryOfThisYear.setFullYear(year, 0, 4);
  fourthOfJanuaryOfThisYear.setHours(0, 0, 0, 0);
  var startOfThisYear = startOfISOWeek(fourthOfJanuaryOfThisYear);
  if (_date.getTime() >= startOfNextYear.getTime()) {
    return year + 1;
  } else if (_date.getTime() >= startOfThisYear.getTime()) {
    return year;
  } else {
    return year - 1;
  }
}

// ../../../../../../tmp/date-fns-jalali/_lib/getTimezoneOffsetInMilliseconds.js
function getTimezoneOffsetInMilliseconds(date) {
  var _date = toDate(date);
  var utcDate = new Date(Date.UTC(_date.getFullYear(), _date.getMonth(), _date.getDate(), _date.getHours(), _date.getMinutes(), _date.getSeconds(), _date.getMilliseconds()));
  utcDate.setUTCFullYear(_date.getFullYear());
  return +date - +utcDate;
}

// ../../../../../../tmp/date-fns-jalali/_lib/normalizeDates.js
function normalizeDates(context) {for (var _len6 = arguments.length, dates = new Array(_len6 > 1 ? _len6 - 1 : 0), _key6 = 1; _key6 < _len6; _key6++) {dates[_key6 - 1] = arguments[_key6];}
  var normalize = constructFrom.bind(null, context || dates.find(function (date) {return _typeof(date) === "object";}));
  return dates.map(normalize);
}

// ../../../../../../tmp/date-fns-jalali/startOfDay.js
function startOfDay(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setHours(0, 0, 0, 0);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/differenceInCalendarDays.js
function differenceInCalendarDays(laterDate, earlierDate, options) {
  var _normalizeDates = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates2 = _slicedToArray(_normalizeDates, 2),laterDate_ = _normalizeDates2[0],earlierDate_ = _normalizeDates2[1];
  var laterStartOfDay = startOfDay(laterDate_);
  var earlierStartOfDay = startOfDay(earlierDate_);
  var laterTimestamp = +laterStartOfDay - getTimezoneOffsetInMilliseconds(laterStartOfDay);
  var earlierTimestamp = +earlierStartOfDay - getTimezoneOffsetInMilliseconds(earlierStartOfDay);
  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInDay);
}

// ../../../../../../tmp/date-fns-jalali/startOfISOWeekYear.js
function startOfISOWeekYear(date, options) {
  var year = getISOWeekYear(date, options);
  var fourthOfJanuary = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);
  fourthOfJanuary.setFullYear(year, 0, 4);
  fourthOfJanuary.setHours(0, 0, 0, 0);
  return startOfISOWeek(fourthOfJanuary);
}

// ../../../../../../tmp/date-fns-jalali/setISOWeekYear.js
function setISOWeekYear(date, weekYear, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var diff = differenceInCalendarDays(_date, startOfISOWeekYear(_date, options));
  var fourthOfJanuary = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);
  fourthOfJanuary.setFullYear(weekYear, 0, 4);
  fourthOfJanuary.setHours(0, 0, 0, 0);
  _date = startOfISOWeekYear(fourthOfJanuary);
  _date.setDate(_date.getDate() + diff);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/addISOWeekYears.js
function addISOWeekYears(date, amount, options) {
  return setISOWeekYear(date, getISOWeekYear(date, options) + amount, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/addISOWeekYears.js
var addISOWeekYears3 = convertToFP(addISOWeekYears, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addISOWeekYearsWithOptions.js
var _addISOWeekYearsWithOptions = convertToFP(addISOWeekYears, 3);
// ../../../../../../tmp/date-fns-jalali/fp/addMilliseconds.js
var addMilliseconds4 = convertToFP(addMilliseconds, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addMillisecondsWithOptions.js
var _addMillisecondsWithOptions = convertToFP(addMilliseconds, 3);
// ../../../../../../tmp/date-fns-jalali/addMinutes.js
function addMinutes(date, amount, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setTime(_date.getTime() + amount * millisecondsInMinute);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/addMinutes.js
var addMinutes3 = convertToFP(addMinutes, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addMinutesWithOptions.js
var _addMinutesWithOptions = convertToFP(addMinutes, 3);
// ../../../../../../tmp/date-fns-jalali/fp/addMonths.js
var addMonths4 = convertToFP(addMonths, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addMonthsWithOptions.js
var _addMonthsWithOptions = convertToFP(addMonths, 3);
// ../../../../../../tmp/date-fns-jalali/addQuarters.js
function addQuarters(date, amount, options) {
  return addMonths(date, amount * 3, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/addQuarters.js
var addQuarters3 = convertToFP(addQuarters, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addQuartersWithOptions.js
var _addQuartersWithOptions = convertToFP(addQuarters, 3);
// ../../../../../../tmp/date-fns-jalali/addSeconds.js
function addSeconds(date, amount, options) {
  return addMilliseconds(date, amount * 1000, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/addSeconds.js
var addSeconds3 = convertToFP(addSeconds, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addSecondsWithOptions.js
var _addSecondsWithOptions = convertToFP(addSeconds, 3);
// ../../../../../../tmp/date-fns-jalali/addWeeks.js
function addWeeks(date, amount, options) {
  return addDays(date, amount * 7, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/addWeeks.js
var addWeeks3 = convertToFP(addWeeks, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addWeeksWithOptions.js
var _addWeeksWithOptions = convertToFP(addWeeks, 3);
// ../../../../../../tmp/date-fns-jalali/fp/addWithOptions.js
var _addWithOptions = convertToFP(add, 3);
// ../../../../../../tmp/date-fns-jalali/addYears.js
function addYears(date, amount, options) {
  return addMonths(date, amount * 12, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/addYears.js
var addYears3 = convertToFP(addYears, 2);
// ../../../../../../tmp/date-fns-jalali/fp/addYearsWithOptions.js
var _addYearsWithOptions = convertToFP(addYears, 3);
// ../../../../../../tmp/date-fns-jalali/areIntervalsOverlapping.js
function areIntervalsOverlapping(intervalLeft, intervalRight, options) {
  var _sort = [
    +toDate(intervalLeft.start, options === null || options === void 0 ? void 0 : options.in),
    +toDate(intervalLeft.end, options === null || options === void 0 ? void 0 : options.in)].
    sort(function (a, b) {return a - b;}),_sort2 = _slicedToArray(_sort, 2),leftStartTime = _sort2[0],leftEndTime = _sort2[1];
  var _sort3 = [
    +toDate(intervalRight.start, options === null || options === void 0 ? void 0 : options.in),
    +toDate(intervalRight.end, options === null || options === void 0 ? void 0 : options.in)].
    sort(function (a, b) {return a - b;}),_sort4 = _slicedToArray(_sort3, 2),rightStartTime = _sort4[0],rightEndTime = _sort4[1];
  if (options !== null && options !== void 0 && options.inclusive)
  return leftStartTime <= rightEndTime && rightStartTime <= leftEndTime;
  return leftStartTime < rightEndTime && rightStartTime < leftEndTime;
}

// ../../../../../../tmp/date-fns-jalali/fp/areIntervalsOverlapping.js
var areIntervalsOverlapping3 = convertToFP(areIntervalsOverlapping, 2);
// ../../../../../../tmp/date-fns-jalali/fp/areIntervalsOverlappingWithOptions.js
var _areIntervalsOverlappingWithOptions = convertToFP(areIntervalsOverlapping, 3);
// ../../../../../../tmp/date-fns-jalali/max.js
function max(dates, options) {
  var result;
  var context = options === null || options === void 0 ? void 0 : options.in;
  dates.forEach(function (date) {
    if (!context && _typeof(date) === "object")
    context = constructFrom.bind(null, date);
    var date_ = toDate(date, context);
    if (!result || result < date_ || isNaN(+date_))
    result = date_;
  });
  return constructFrom(context, result || NaN);
}

// ../../../../../../tmp/date-fns-jalali/min.js
function min(dates, options) {
  var result;
  var context = options === null || options === void 0 ? void 0 : options.in;
  dates.forEach(function (date) {
    if (!context && _typeof(date) === "object")
    context = constructFrom.bind(null, date);
    var date_ = toDate(date, context);
    if (!result || result > date_ || isNaN(+date_))
    result = date_;
  });
  return constructFrom(context, result || NaN);
}

// ../../../../../../tmp/date-fns-jalali/clamp.js
function clamp(date, interval, options) {
  var _normalizeDates3 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, date, interval.start, interval.end),_normalizeDates4 = _slicedToArray(_normalizeDates3, 3),date_ = _normalizeDates4[0],start = _normalizeDates4[1],end = _normalizeDates4[2];
  return min([max([date_, start], options), end], options);
}

// ../../../../../../tmp/date-fns-jalali/fp/clamp.js
var clamp3 = convertToFP(clamp, 2);
// ../../../../../../tmp/date-fns-jalali/fp/clampWithOptions.js
var _clampWithOptions = convertToFP(clamp, 3);
// ../../../../../../tmp/date-fns-jalali/closestIndexTo.js
function closestIndexTo(dateToCompare, dates) {
  var timeToCompare = +toDate(dateToCompare);
  if (isNaN(timeToCompare))
  return NaN;
  var result;
  var minDistance;
  dates.forEach(function (date, index) {
    var date_ = toDate(date);
    if (isNaN(+date_)) {
      result = NaN;
      minDistance = NaN;
      return;
    }
    var distance = Math.abs(timeToCompare - +date_);
    if (result == null || distance < minDistance) {
      result = index;
      minDistance = distance;
    }
  });
  return result;
}

// ../../../../../../tmp/date-fns-jalali/fp/closestIndexTo.js
var closestIndexTo3 = convertToFP(closestIndexTo, 2);
// ../../../../../../tmp/date-fns-jalali/closestTo.js
function closestTo(dateToCompare, dates, options) {
  var _normalizeDates5 = normalizeDates.apply(void 0, [options === null || options === void 0 ? void 0 : options.in, dateToCompare].concat(_toConsumableArray(dates))),_normalizeDates6 = _toArray(_normalizeDates5),dateToCompare_ = _normalizeDates6[0],dates_ = _normalizeDates6.slice(1);
  var index = closestIndexTo(dateToCompare_, dates_);
  if (typeof index === "number" && isNaN(index))
  return constructFrom(dateToCompare_, NaN);
  if (index !== undefined)
  return dates_[index];
}

// ../../../../../../tmp/date-fns-jalali/fp/closestTo.js
var closestTo3 = convertToFP(closestTo, 2);
// ../../../../../../tmp/date-fns-jalali/fp/closestToWithOptions.js
var _closestToWithOptions = convertToFP(closestTo, 3);
// ../../../../../../tmp/date-fns-jalali/compareAsc.js
function compareAsc(dateLeft, dateRight) {
  var diff = +toDate(dateLeft) - +toDate(dateRight);
  if (diff < 0)
  return -1;else
  if (diff > 0)
  return 1;
  return diff;
}

// ../../../../../../tmp/date-fns-jalali/fp/compareAsc.js
var compareAsc3 = convertToFP(compareAsc, 2);
// ../../../../../../tmp/date-fns-jalali/compareDesc.js
function compareDesc(dateLeft, dateRight) {
  var diff = +toDate(dateLeft) - +toDate(dateRight);
  if (diff > 0)
  return -1;else
  if (diff < 0)
  return 1;
  return diff;
}

// ../../../../../../tmp/date-fns-jalali/fp/compareDesc.js
var compareDesc3 = convertToFP(compareDesc, 2);
// ../../../../../../tmp/date-fns-jalali/fp/constructFrom.js
var constructFrom16 = convertToFP(constructFrom, 2);
// ../../../../../../tmp/date-fns-jalali/daysToWeeks.js
function daysToWeeks(days) {
  var result = Math.trunc(days / daysInWeek);
  return result === 0 ? 0 : result;
}

// ../../../../../../tmp/date-fns-jalali/fp/daysToWeeks.js
var daysToWeeks3 = convertToFP(daysToWeeks, 1);
// ../../../../../../tmp/date-fns-jalali/isSameDay.js
function isSameDay(laterDate, earlierDate, options) {
  var _normalizeDates7 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates8 = _slicedToArray(_normalizeDates7, 2),dateLeft_ = _normalizeDates8[0],dateRight_ = _normalizeDates8[1];
  return +startOfDay(dateLeft_) === +startOfDay(dateRight_);
}

// ../../../../../../tmp/date-fns-jalali/isDate.js
function isDate(value) {
  return value instanceof Date || _typeof(value) === "object" && Object.prototype.toString.call(value) === "[object Date]";
}

// ../../../../../../tmp/date-fns-jalali/isValid.js
function isValid(date) {
  return !(!isDate(date) && typeof date !== "number" || isNaN(+toDate(date)));
}

// ../../../../../../tmp/date-fns-jalali/differenceInBusinessDays.js
function differenceInBusinessDays(laterDate, earlierDate, options) {
  var _normalizeDates9 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates10 = _slicedToArray(_normalizeDates9, 2),laterDate_ = _normalizeDates10[0],earlierDate_ = _normalizeDates10[1];
  if (!isValid(laterDate_) || !isValid(earlierDate_))
  return NaN;
  var diff = differenceInCalendarDays(laterDate_, earlierDate_);
  var sign = diff < 0 ? -1 : 1;
  var weeks = Math.trunc(diff / 7);
  var result = weeks * 6;
  var movingDate = addDays(earlierDate_, weeks * 7);
  while (!isSameDay(laterDate_, movingDate)) {
    result += isWeekend(movingDate, options) ? 0 : sign;
    movingDate = addDays(movingDate, sign);
  }
  return result === 0 ? 0 : result;
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInBusinessDays.js
var differenceInBusinessDays3 = convertToFP(differenceInBusinessDays, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInBusinessDaysWithOptions.js
var _differenceInBusinessDaysWithOptions = convertToFP(differenceInBusinessDays, 3);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarDays.js
var differenceInCalendarDays5 = convertToFP(differenceInCalendarDays, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarDaysWithOptions.js
var _differenceInCalendarDaysWithOptions = convertToFP(differenceInCalendarDays, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInCalendarISOWeekYears.js
function differenceInCalendarISOWeekYears(laterDate, earlierDate, options) {
  var _normalizeDates11 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates12 = _slicedToArray(_normalizeDates11, 2),laterDate_ = _normalizeDates12[0],earlierDate_ = _normalizeDates12[1];
  return getISOWeekYear(laterDate_, options) - getISOWeekYear(earlierDate_, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarISOWeekYears.js
var differenceInCalendarISOWeekYears3 = convertToFP(differenceInCalendarISOWeekYears, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarISOWeekYearsWithOptions.js
var _differenceInCalendarISOWeekYearsWithOptions = convertToFP(differenceInCalendarISOWeekYears, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInCalendarISOWeeks.js
function differenceInCalendarISOWeeks(laterDate, earlierDate, options) {
  var _normalizeDates13 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates14 = _slicedToArray(_normalizeDates13, 2),laterDate_ = _normalizeDates14[0],earlierDate_ = _normalizeDates14[1];
  var startOfISOWeekLeft = startOfISOWeek(laterDate_);
  var startOfISOWeekRight = startOfISOWeek(earlierDate_);
  var timestampLeft = +startOfISOWeekLeft - getTimezoneOffsetInMilliseconds(startOfISOWeekLeft);
  var timestampRight = +startOfISOWeekRight - getTimezoneOffsetInMilliseconds(startOfISOWeekRight);
  return Math.round((timestampLeft - timestampRight) / millisecondsInWeek);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarISOWeeks.js
var differenceInCalendarISOWeeks3 = convertToFP(differenceInCalendarISOWeeks, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarISOWeeksWithOptions.js
var _differenceInCalendarISOWeeksWithOptions = convertToFP(differenceInCalendarISOWeeks, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInCalendarMonths.js
function differenceInCalendarMonths(laterDate, earlierDate, options) {
  var _normalizeDates15 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates16 = _slicedToArray(_normalizeDates15, 2),laterDate_ = _normalizeDates16[0],earlierDate_ = _normalizeDates16[1];
  var yearsDiff = getFullYear(laterDate_) - getFullYear(earlierDate_);
  var monthsDiff = getMonth(laterDate_) - getMonth(earlierDate_);
  return yearsDiff * 12 + monthsDiff;
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarMonths.js
var differenceInCalendarMonths3 = convertToFP(differenceInCalendarMonths, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarMonthsWithOptions.js
var _differenceInCalendarMonthsWithOptions = convertToFP(differenceInCalendarMonths, 3);
// ../../../../../../tmp/date-fns-jalali/getQuarter.js
function getQuarter(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var quarter = Math.trunc(getMonth(_date) / 3) + 1;
  return quarter;
}

// ../../../../../../tmp/date-fns-jalali/differenceInCalendarQuarters.js
function differenceInCalendarQuarters(laterDate, earlierDate, options) {
  var _normalizeDates17 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates18 = _slicedToArray(_normalizeDates17, 2),laterDate_ = _normalizeDates18[0],earlierDate_ = _normalizeDates18[1];
  var yearsDiff = getFullYear(laterDate_) - getFullYear(earlierDate_);
  var quartersDiff = getQuarter(laterDate_) - getQuarter(earlierDate_);
  return yearsDiff * 4 + quartersDiff;
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarQuarters.js
var differenceInCalendarQuarters3 = convertToFP(differenceInCalendarQuarters, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarQuartersWithOptions.js
var _differenceInCalendarQuartersWithOptions = convertToFP(differenceInCalendarQuarters, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInCalendarWeeks.js
function differenceInCalendarWeeks(laterDate, earlierDate, options) {
  var _normalizeDates19 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates20 = _slicedToArray(_normalizeDates19, 2),laterDate_ = _normalizeDates20[0],earlierDate_ = _normalizeDates20[1];
  var laterStartOfWeek = startOfWeek(laterDate_, options);
  var earlierStartOfWeek = startOfWeek(earlierDate_, options);
  var laterTimestamp = +laterStartOfWeek - getTimezoneOffsetInMilliseconds(laterStartOfWeek);
  var earlierTimestamp = +earlierStartOfWeek - getTimezoneOffsetInMilliseconds(earlierStartOfWeek);
  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInWeek);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarWeeks.js
var differenceInCalendarWeeks3 = convertToFP(differenceInCalendarWeeks, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarWeeksWithOptions.js
var _differenceInCalendarWeeksWithOptions = convertToFP(differenceInCalendarWeeks, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInCalendarYears.js
function differenceInCalendarYears(laterDate, earlierDate, options) {
  var _normalizeDates21 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates22 = _slicedToArray(_normalizeDates21, 2),laterDate_ = _normalizeDates22[0],earlierDate_ = _normalizeDates22[1];
  return getFullYear(laterDate_) - getFullYear(earlierDate_);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarYears.js
var differenceInCalendarYears3 = convertToFP(differenceInCalendarYears, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInCalendarYearsWithOptions.js
var _differenceInCalendarYearsWithOptions = convertToFP(differenceInCalendarYears, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInDays.js
function differenceInDays(laterDate, earlierDate, options) {
  var _normalizeDates23 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates24 = _slicedToArray(_normalizeDates23, 2),laterDate_ = _normalizeDates24[0],earlierDate_ = _normalizeDates24[1];
  var sign = compareLocalAsc(laterDate_, earlierDate_);
  var difference = Math.abs(differenceInCalendarDays(laterDate_, earlierDate_));
  setDate(laterDate_, getDate(laterDate_) - sign * difference);
  var isLastDayNotFull = Number(compareLocalAsc(laterDate_, earlierDate_) === -sign);
  var result = sign * (difference - isLastDayNotFull);
  return result === 0 ? 0 : result;
}
function compareLocalAsc(laterDate, earlierDate) {
  var diff = getFullYear(laterDate) - getFullYear(earlierDate) || getMonth(laterDate) - getMonth(earlierDate) || getDate(laterDate) - getDate(earlierDate) || laterDate.getHours() - earlierDate.getHours() || laterDate.getMinutes() - earlierDate.getMinutes() || laterDate.getSeconds() - earlierDate.getSeconds() || laterDate.getMilliseconds() - earlierDate.getMilliseconds();
  if (diff < 0)
  return -1;
  if (diff > 0)
  return 1;
  return diff;
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInDays.js
var differenceInDays3 = convertToFP(differenceInDays, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInDaysWithOptions.js
var _differenceInDaysWithOptions = convertToFP(differenceInDays, 3);
// ../../../../../../tmp/date-fns-jalali/_lib/getRoundingMethod.js
function getRoundingMethod(method) {
  return function (number) {
    var round = method ? Math[method] : Math.trunc;
    var result = round(number);
    return result === 0 ? 0 : result;
  };
}

// ../../../../../../tmp/date-fns-jalali/differenceInHours.js
function differenceInHours(laterDate, earlierDate, options) {
  var _normalizeDates25 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates26 = _slicedToArray(_normalizeDates25, 2),laterDate_ = _normalizeDates26[0],earlierDate_ = _normalizeDates26[1];
  var diff = (+laterDate_ - +earlierDate_) / millisecondsInHour;
  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInHours.js
var differenceInHours3 = convertToFP(differenceInHours, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInHoursWithOptions.js
var _differenceInHoursWithOptions = convertToFP(differenceInHours, 3);
// ../../../../../../tmp/date-fns-jalali/subISOWeekYears.js
function subISOWeekYears(date, amount, options) {
  return addISOWeekYears(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/differenceInISOWeekYears.js
function differenceInISOWeekYears(laterDate, earlierDate, options) {
  var _normalizeDates27 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates28 = _slicedToArray(_normalizeDates27, 2),laterDate_ = _normalizeDates28[0],earlierDate_ = _normalizeDates28[1];
  var sign = compareAsc(laterDate_, earlierDate_);
  var diff = Math.abs(differenceInCalendarISOWeekYears(laterDate_, earlierDate_, options));
  var adjustedDate = subISOWeekYears(laterDate_, sign * diff, options);
  var isLastISOWeekYearNotFull = Number(compareAsc(adjustedDate, earlierDate_) === -sign);
  var result = sign * (diff - isLastISOWeekYearNotFull);
  return result === 0 ? 0 : result;
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInISOWeekYears.js
var differenceInISOWeekYears3 = convertToFP(differenceInISOWeekYears, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInISOWeekYearsWithOptions.js
var _differenceInISOWeekYearsWithOptions = convertToFP(differenceInISOWeekYears, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInMilliseconds.js
function differenceInMilliseconds(laterDate, earlierDate) {
  return +toDate(laterDate) - +toDate(earlierDate);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInMilliseconds.js
var differenceInMilliseconds3 = convertToFP(differenceInMilliseconds, 2);
// ../../../../../../tmp/date-fns-jalali/differenceInMinutes.js
function differenceInMinutes(dateLeft, dateRight, options) {
  var diff = differenceInMilliseconds(dateLeft, dateRight) / millisecondsInMinute;
  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInMinutes.js
var differenceInMinutes3 = convertToFP(differenceInMinutes, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInMinutesWithOptions.js
var _differenceInMinutesWithOptions = convertToFP(differenceInMinutes, 3);
// ../../../../../../tmp/date-fns-jalali/endOfDay.js
function endOfDay(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setHours(23, 59, 59, 999);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/endOfMonth.js
function endOfMonth(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var month = getMonth(_date);
  setFullYear(_date, getFullYear(_date), month + 1, 0);
  _date.setHours(23, 59, 59, 999);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/isLastDayOfMonth.js
function isLastDayOfMonth(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  return +endOfDay(_date, options) === +endOfMonth(_date, options);
}

// ../../../../../../tmp/date-fns-jalali/differenceInMonths.js
function differenceInMonths(laterDate, earlierDate, options) {
  var _normalizeDates29 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, laterDate, earlierDate),_normalizeDates30 = _slicedToArray(_normalizeDates29, 3),laterDate_ = _normalizeDates30[0],workingLaterDate = _normalizeDates30[1],earlierDate_ = _normalizeDates30[2];
  var sign = compareAsc(workingLaterDate, earlierDate_);
  var difference = Math.abs(differenceInCalendarMonths(workingLaterDate, earlierDate_));
  if (difference < 1)
  return 0;
  if (getMonth(workingLaterDate) === 1 && getDate(workingLaterDate) > 27)
  setDate(workingLaterDate, 30);
  setMonth(workingLaterDate, getMonth(workingLaterDate) - sign * difference);
  var isLastMonthNotFull = compareAsc(workingLaterDate, earlierDate_) === -sign;
  if (isLastDayOfMonth(laterDate_) && difference === 1 && compareAsc(laterDate_, earlierDate_) === 1) {
    isLastMonthNotFull = false;
  }
  var result = sign * (difference - +isLastMonthNotFull);
  return result === 0 ? 0 : result;
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInMonths.js
var differenceInMonths3 = convertToFP(differenceInMonths, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInMonthsWithOptions.js
var _differenceInMonthsWithOptions = convertToFP(differenceInMonths, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInQuarters.js
function differenceInQuarters(laterDate, earlierDate, options) {
  var diff = differenceInMonths(laterDate, earlierDate, options) / 3;
  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInQuarters.js
var differenceInQuarters3 = convertToFP(differenceInQuarters, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInQuartersWithOptions.js
var _differenceInQuartersWithOptions = convertToFP(differenceInQuarters, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInSeconds.js
function differenceInSeconds(laterDate, earlierDate, options) {
  var diff = differenceInMilliseconds(laterDate, earlierDate) / 1000;
  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInSeconds.js
var differenceInSeconds3 = convertToFP(differenceInSeconds, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInSecondsWithOptions.js
var _differenceInSecondsWithOptions = convertToFP(differenceInSeconds, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInWeeks.js
function differenceInWeeks(laterDate, earlierDate, options) {
  var diff = differenceInDays(laterDate, earlierDate, options) / 7;
  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInWeeks.js
var differenceInWeeks3 = convertToFP(differenceInWeeks, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInWeeksWithOptions.js
var _differenceInWeeksWithOptions = convertToFP(differenceInWeeks, 3);
// ../../../../../../tmp/date-fns-jalali/differenceInYears.js
function differenceInYears(laterDate, earlierDate, options) {
  var _normalizeDates31 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates32 = _slicedToArray(_normalizeDates31, 2),laterDate_ = _normalizeDates32[0],earlierDate_ = _normalizeDates32[1];
  var sign = compareAsc(laterDate_, earlierDate_);
  var diff = Math.abs(differenceInCalendarYears(laterDate_, earlierDate_));
  setFullYear(laterDate_, 1399);
  setFullYear(earlierDate_, 1399);
  var partial = compareAsc(laterDate_, earlierDate_) === -sign;
  var result = sign * (diff - +partial);
  return result === 0 ? 0 : result;
}

// ../../../../../../tmp/date-fns-jalali/fp/differenceInYears.js
var differenceInYears3 = convertToFP(differenceInYears, 2);
// ../../../../../../tmp/date-fns-jalali/fp/differenceInYearsWithOptions.js
var _differenceInYearsWithOptions = convertToFP(differenceInYears, 3);
// ../../../../../../tmp/date-fns-jalali/_lib/normalizeInterval.js
function normalizeInterval(context, interval) {
  var _normalizeDates33 = normalizeDates(context, interval.start, interval.end),_normalizeDates34 = _slicedToArray(_normalizeDates33, 2),start = _normalizeDates34[0],end = _normalizeDates34[1];
  return { start: start, end: end };
}

// ../../../../../../tmp/date-fns-jalali/eachDayOfInterval.js
function eachDayOfInterval(interval, options) {var _options$step;
  var _normalizeInterval = normalizeInterval(options === null || options === void 0 ? void 0 : options.in, interval),start = _normalizeInterval.start,end = _normalizeInterval.end;
  var reversed = +start > +end;
  var endTime = reversed ? +start : +end;
  var date = reversed ? end : start;
  date.setHours(0, 0, 0, 0);
  var step = (_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1;
  if (!step)
  return [];
  if (step < 0) {
    step = -step;
    reversed = !reversed;
  }
  var dates = [];
  while (+date <= endTime) {
    dates.push(constructFrom(start, date));
    setDate(date, getDate(date) + step);
    date.setHours(0, 0, 0, 0);
  }
  return reversed ? dates.reverse() : dates;
}

// ../../../../../../tmp/date-fns-jalali/fp/eachDayOfInterval.js
var eachDayOfInterval3 = convertToFP(eachDayOfInterval, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachDayOfIntervalWithOptions.js
var _eachDayOfIntervalWithOptions = convertToFP(eachDayOfInterval, 2);
// ../../../../../../tmp/date-fns-jalali/eachHourOfInterval.js
function eachHourOfInterval(interval, options) {var _options$step2;
  var _normalizeInterval2 = normalizeInterval(options === null || options === void 0 ? void 0 : options.in, interval),start = _normalizeInterval2.start,end = _normalizeInterval2.end;
  var reversed = +start > +end;
  var endTime = reversed ? +start : +end;
  var date = reversed ? end : start;
  date.setMinutes(0, 0, 0);
  var step = (_options$step2 = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step2 !== void 0 ? _options$step2 : 1;
  if (!step)
  return [];
  if (step < 0) {
    step = -step;
    reversed = !reversed;
  }
  var dates = [];
  while (+date <= endTime) {
    dates.push(constructFrom(start, date));
    date.setHours(date.getHours() + step);
  }
  return reversed ? dates.reverse() : dates;
}

// ../../../../../../tmp/date-fns-jalali/fp/eachHourOfInterval.js
var eachHourOfInterval3 = convertToFP(eachHourOfInterval, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachHourOfIntervalWithOptions.js
var _eachHourOfIntervalWithOptions = convertToFP(eachHourOfInterval, 2);
// ../../../../../../tmp/date-fns-jalali/eachMinuteOfInterval.js
function eachMinuteOfInterval(interval, options) {var _options$step3;
  var _normalizeInterval3 = normalizeInterval(options === null || options === void 0 ? void 0 : options.in, interval),start = _normalizeInterval3.start,end = _normalizeInterval3.end;
  start.setSeconds(0, 0);
  var reversed = +start > +end;
  var endTime = reversed ? +start : +end;
  var date = reversed ? end : start;
  var step = (_options$step3 = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step3 !== void 0 ? _options$step3 : 1;
  if (!step)
  return [];
  if (step < 0) {
    step = -step;
    reversed = !reversed;
  }
  var dates = [];
  while (+date <= endTime) {
    dates.push(constructFrom(start, date));
    date = addMinutes(date, step);
  }
  return reversed ? dates.reverse() : dates;
}

// ../../../../../../tmp/date-fns-jalali/fp/eachMinuteOfInterval.js
var eachMinuteOfInterval3 = convertToFP(eachMinuteOfInterval, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachMinuteOfIntervalWithOptions.js
var _eachMinuteOfIntervalWithOptions = convertToFP(eachMinuteOfInterval, 2);
// ../../../../../../tmp/date-fns-jalali/eachMonthOfInterval.js
function eachMonthOfInterval(interval, options) {var _options$step4;
  var _normalizeInterval4 = normalizeInterval(options === null || options === void 0 ? void 0 : options.in, interval),start = _normalizeInterval4.start,end = _normalizeInterval4.end;
  var reversed = +start > +end;
  var endTime = reversed ? +start : +end;
  var date = reversed ? end : start;
  date.setHours(0, 0, 0, 0);
  setDate(date, 1);
  var step = (_options$step4 = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step4 !== void 0 ? _options$step4 : 1;
  if (!step)
  return [];
  if (step < 0) {
    step = -step;
    reversed = !reversed;
  }
  var dates = [];
  while (+date <= endTime) {
    dates.push(constructFrom(start, date));
    setMonth(date, getMonth(date) + step);
  }
  return reversed ? dates.reverse() : dates;
}

// ../../../../../../tmp/date-fns-jalali/fp/eachMonthOfInterval.js
var eachMonthOfInterval3 = convertToFP(eachMonthOfInterval, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachMonthOfIntervalWithOptions.js
var _eachMonthOfIntervalWithOptions = convertToFP(eachMonthOfInterval, 2);
// ../../../../../../tmp/date-fns-jalali/startOfQuarter.js
function startOfQuarter(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var currentMonth = getMonth(_date);
  var month = currentMonth - currentMonth % 3;
  setMonth(_date, month, 1);
  _date.setHours(0, 0, 0, 0);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/eachQuarterOfInterval.js
function eachQuarterOfInterval(interval, options) {var _options$step5;
  var _normalizeInterval5 = normalizeInterval(options === null || options === void 0 ? void 0 : options.in, interval),start = _normalizeInterval5.start,end = _normalizeInterval5.end;
  var reversed = +start > +end;
  var endTime = reversed ? +startOfQuarter(start) : +startOfQuarter(end);
  var date = reversed ? startOfQuarter(end) : startOfQuarter(start);
  var step = (_options$step5 = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step5 !== void 0 ? _options$step5 : 1;
  if (!step)
  return [];
  if (step < 0) {
    step = -step;
    reversed = !reversed;
  }
  var dates = [];
  while (+date <= endTime) {
    dates.push(constructFrom(start, date));
    date = addQuarters(date, step);
  }
  return reversed ? dates.reverse() : dates;
}

// ../../../../../../tmp/date-fns-jalali/fp/eachQuarterOfInterval.js
var eachQuarterOfInterval3 = convertToFP(eachQuarterOfInterval, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachQuarterOfIntervalWithOptions.js
var _eachQuarterOfIntervalWithOptions = convertToFP(eachQuarterOfInterval, 2);
// ../../../../../../tmp/date-fns-jalali/eachWeekOfInterval.js
function eachWeekOfInterval(interval, options) {var _options$step6;
  var _normalizeInterval6 = normalizeInterval(options === null || options === void 0 ? void 0 : options.in, interval),start = _normalizeInterval6.start,end = _normalizeInterval6.end;
  var reversed = +start > +end;
  var startDateWeek = reversed ? startOfWeek(end, options) : startOfWeek(start, options);
  var endDateWeek = reversed ? startOfWeek(start, options) : startOfWeek(end, options);
  startDateWeek.setHours(15);
  endDateWeek.setHours(15);
  var endTime = +endDateWeek.getTime();
  var currentDate = startDateWeek;
  var step = (_options$step6 = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step6 !== void 0 ? _options$step6 : 1;
  if (!step)
  return [];
  if (step < 0) {
    step = -step;
    reversed = !reversed;
  }
  var dates = [];
  while (+currentDate <= endTime) {
    currentDate.setHours(0);
    dates.push(constructFrom(start, currentDate));
    currentDate = addWeeks(currentDate, step);
    currentDate.setHours(15);
  }
  return reversed ? dates.reverse() : dates;
}

// ../../../../../../tmp/date-fns-jalali/fp/eachWeekOfInterval.js
var eachWeekOfInterval3 = convertToFP(eachWeekOfInterval, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachWeekOfIntervalWithOptions.js
var _eachWeekOfIntervalWithOptions = convertToFP(eachWeekOfInterval, 2);
// ../../../../../../tmp/date-fns-jalali/eachWeekendOfInterval.js
function eachWeekendOfInterval(interval, options) {
  var _normalizeInterval7 = normalizeInterval(options === null || options === void 0 ? void 0 : options.in, interval),start = _normalizeInterval7.start,end = _normalizeInterval7.end;
  var dateInterval = eachDayOfInterval({ start: start, end: end }, options);
  var weekends = [];
  var index = 0;
  while (index < dateInterval.length) {
    var date = dateInterval[index++];
    if (isWeekend(date))
    weekends.push(constructFrom(start, date));
  }
  return weekends;
}

// ../../../../../../tmp/date-fns-jalali/fp/eachWeekendOfInterval.js
var eachWeekendOfInterval3 = convertToFP(eachWeekendOfInterval, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachWeekendOfIntervalWithOptions.js
var _eachWeekendOfIntervalWithOptions = convertToFP(eachWeekendOfInterval, 2);
// ../../../../../../tmp/date-fns-jalali/startOfMonth.js
function startOfMonth(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  setDate(_date, 1);
  _date.setHours(0, 0, 0, 0);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/eachWeekendOfMonth.js
function eachWeekendOfMonth(date, options) {
  var start = startOfMonth(date, options);
  var end = endOfMonth(date, options);
  return eachWeekendOfInterval({ start: start, end: end }, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/eachWeekendOfMonth.js
var eachWeekendOfMonth3 = convertToFP(eachWeekendOfMonth, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachWeekendOfMonthWithOptions.js
var _eachWeekendOfMonthWithOptions = convertToFP(eachWeekendOfMonth, 2);
// ../../../../../../tmp/date-fns-jalali/endOfYear.js
function endOfYear(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(_date);
  setFullYear(_date, year + 1, 0, 0);
  _date.setHours(23, 59, 59, 999);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/startOfYear.js
function startOfYear(date, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  setFullYear(date_, getFullYear(date_), 0, 1);
  date_.setHours(0, 0, 0, 0);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/eachWeekendOfYear.js
function eachWeekendOfYear(date, options) {
  var start = startOfYear(date, options);
  var end = endOfYear(date, options);
  return eachWeekendOfInterval({ start: start, end: end }, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/eachWeekendOfYear.js
var eachWeekendOfYear3 = convertToFP(eachWeekendOfYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachWeekendOfYearWithOptions.js
var _eachWeekendOfYearWithOptions = convertToFP(eachWeekendOfYear, 2);
// ../../../../../../tmp/date-fns-jalali/eachYearOfInterval.js
function eachYearOfInterval(interval, options) {var _options$step7;
  var _normalizeInterval8 = normalizeInterval(options === null || options === void 0 ? void 0 : options.in, interval),start = _normalizeInterval8.start,end = _normalizeInterval8.end;
  var reversed = +start > +end;
  var endTime = reversed ? +start : +end;
  var date = reversed ? end : start;
  date.setHours(0, 0, 0, 0);
  setMonth(date, 0, 1);
  var step = (_options$step7 = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step7 !== void 0 ? _options$step7 : 1;
  if (!step)
  return [];
  if (step < 0) {
    step = -step;
    reversed = !reversed;
  }
  var dates = [];
  while (+date <= endTime) {
    dates.push(constructFrom(start, date));
    setFullYear(date, getFullYear(date) + step);
  }
  return reversed ? dates.reverse() : dates;
}

// ../../../../../../tmp/date-fns-jalali/fp/eachYearOfInterval.js
var eachYearOfInterval3 = convertToFP(eachYearOfInterval, 1);
// ../../../../../../tmp/date-fns-jalali/fp/eachYearOfIntervalWithOptions.js
var _eachYearOfIntervalWithOptions = convertToFP(eachYearOfInterval, 2);
// ../../../../../../tmp/date-fns-jalali/fp/endOfDay.js
var endOfDay4 = convertToFP(endOfDay, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfDayWithOptions.js
var _endOfDayWithOptions = convertToFP(endOfDay, 2);
// ../../../../../../tmp/date-fns-jalali/endOfDecade.js
function endOfDecade(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(_date);
  var decade = 9 + Math.floor(year / 10) * 10;
  setFullYear(_date, decade + 1, 0, 0);
  _date.setHours(23, 59, 59, 999);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/endOfDecade.js
var endOfDecade3 = convertToFP(endOfDecade, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfDecadeWithOptions.js
var _endOfDecadeWithOptions = convertToFP(endOfDecade, 2);
// ../../../../../../tmp/date-fns-jalali/endOfHour.js
function endOfHour(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setMinutes(59, 59, 999);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/endOfHour.js
var endOfHour3 = convertToFP(endOfHour, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfHourWithOptions.js
var _endOfHourWithOptions = convertToFP(endOfHour, 2);
// ../../../../../../tmp/date-fns-jalali/endOfWeek.js
function endOfWeek(date, options) {var _ref4, _ref5, _ref6, _options$weekStartsOn2, _options$locale2, _defaultOptions4$loca;
  var defaultOptions4 = getDefaultOptions();
  var weekStartsOn = (_ref4 = (_ref5 = (_ref6 = (_options$weekStartsOn2 = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn2 !== void 0 ? _options$weekStartsOn2 : options === null || options === void 0 || (_options$locale2 = options.locale) === null || _options$locale2 === void 0 || (_options$locale2 = _options$locale2.options) === null || _options$locale2 === void 0 ? void 0 : _options$locale2.weekStartsOn) !== null && _ref6 !== void 0 ? _ref6 : defaultOptions4.weekStartsOn) !== null && _ref5 !== void 0 ? _ref5 : (_defaultOptions4$loca = defaultOptions4.locale) === null || _defaultOptions4$loca === void 0 || (_defaultOptions4$loca = _defaultOptions4$loca.options) === null || _defaultOptions4$loca === void 0 ? void 0 : _defaultOptions4$loca.weekStartsOn) !== null && _ref4 !== void 0 ? _ref4 : 6;
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var day = _date.getDay();
  var diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);
  setDate(_date, getDate(_date) + diff);
  _date.setHours(23, 59, 59, 999);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/endOfISOWeek.js
function endOfISOWeek(date, options) {
  return endOfWeek(date, _objectSpread(_objectSpread({}, options), {}, { weekStartsOn: 1 }));
}

// ../../../../../../tmp/date-fns-jalali/fp/endOfISOWeek.js
var endOfISOWeek3 = convertToFP(endOfISOWeek, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfISOWeekWithOptions.js
var _endOfISOWeekWithOptions = convertToFP(endOfISOWeek, 2);
// ../../../../../../tmp/date-fns-jalali/endOfISOWeekYear.js
function endOfISOWeekYear(date, options) {
  var year = getISOWeekYear(date, options);
  var fourthOfJanuaryOfNextYear = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);
  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);
  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);
  var _date = startOfISOWeek(fourthOfJanuaryOfNextYear, options);
  _date.setMilliseconds(_date.getMilliseconds() - 1);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/endOfISOWeekYear.js
var endOfISOWeekYear3 = convertToFP(endOfISOWeekYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfISOWeekYearWithOptions.js
var _endOfISOWeekYearWithOptions = convertToFP(endOfISOWeekYear, 2);
// ../../../../../../tmp/date-fns-jalali/endOfMinute.js
function endOfMinute(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setSeconds(59, 999);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/endOfMinute.js
var endOfMinute3 = convertToFP(endOfMinute, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfMinuteWithOptions.js
var _endOfMinuteWithOptions = convertToFP(endOfMinute, 2);
// ../../../../../../tmp/date-fns-jalali/fp/endOfMonth.js
var endOfMonth5 = convertToFP(endOfMonth, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfMonthWithOptions.js
var _endOfMonthWithOptions = convertToFP(endOfMonth, 2);
// ../../../../../../tmp/date-fns-jalali/endOfQuarter.js
function endOfQuarter(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var currentMonth = getMonth(_date);
  var month = currentMonth - currentMonth % 3 + 3;
  setMonth(_date, month, 0);
  _date.setHours(23, 59, 59, 999);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/endOfQuarter.js
var endOfQuarter3 = convertToFP(endOfQuarter, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfQuarterWithOptions.js
var _endOfQuarterWithOptions = convertToFP(endOfQuarter, 2);
// ../../../../../../tmp/date-fns-jalali/endOfSecond.js
function endOfSecond(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setMilliseconds(999);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/endOfSecond.js
var endOfSecond3 = convertToFP(endOfSecond, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfSecondWithOptions.js
var _endOfSecondWithOptions = convertToFP(endOfSecond, 2);
// ../../../../../../tmp/date-fns-jalali/fp/endOfWeek.js
var endOfWeek4 = convertToFP(endOfWeek, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfWeekWithOptions.js
var _endOfWeekWithOptions = convertToFP(endOfWeek, 2);
// ../../../../../../tmp/date-fns-jalali/fp/endOfYear.js
var endOfYear4 = convertToFP(endOfYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/endOfYearWithOptions.js
var _endOfYearWithOptions = convertToFP(endOfYear, 2);
// ../../../../../../tmp/date-fns-jalali/locale/fa-IR/_lib/formatDistance.js
var formatDistanceLocale = {
  lessThanXSeconds: {
    one: "\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062B\u0627\u0646\u06CC\u0647",
    other: "\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062B\u0627\u0646\u06CC\u0647"
  },
  xSeconds: {
    one: "1 \u062B\u0627\u0646\u06CC\u0647",
    other: "{{count}} \u062B\u0627\u0646\u06CC\u0647"
  },
  halfAMinute: "\u0646\u06CC\u0645 \u062F\u0642\u06CC\u0642\u0647",
  lessThanXMinutes: {
    one: "\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062F\u0642\u06CC\u0642\u0647",
    other: "\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062F\u0642\u06CC\u0642\u0647"
  },
  xMinutes: {
    one: "1 \u062F\u0642\u06CC\u0642\u0647",
    other: "{{count}} \u062F\u0642\u06CC\u0642\u0647"
  },
  aboutXHours: {
    one: "\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0639\u062A",
    other: "\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0639\u062A"
  },
  xHours: {
    one: "1 \u0633\u0627\u0639\u062A",
    other: "{{count}} \u0633\u0627\u0639\u062A"
  },
  xDays: {
    one: "1 \u0631\u0648\u0632",
    other: "{{count}} \u0631\u0648\u0632"
  },
  aboutXWeeks: {
    one: "\u062D\u062F\u0648\u062F 1 \u0647\u0641\u062A\u0647",
    other: "\u062D\u062F\u0648\u062F {{count}} \u0647\u0641\u062A\u0647"
  },
  xWeeks: {
    one: "1 \u0647\u0641\u062A\u0647",
    other: "{{count}} \u0647\u0641\u062A\u0647"
  },
  aboutXMonths: {
    one: "\u062D\u062F\u0648\u062F 1 \u0645\u0627\u0647",
    other: "\u062D\u062F\u0648\u062F {{count}} \u0645\u0627\u0647"
  },
  xMonths: {
    one: "1 \u0645\u0627\u0647",
    other: "{{count}} \u0645\u0627\u0647"
  },
  aboutXYears: {
    one: "\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0644",
    other: "\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0644"
  },
  xYears: {
    one: "1 \u0633\u0627\u0644",
    other: "{{count}} \u0633\u0627\u0644"
  },
  overXYears: {
    one: "\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 1 \u0633\u0627\u0644",
    other: "\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 {{count}} \u0633\u0627\u0644"
  },
  almostXYears: {
    one: "\u0646\u0632\u062F\u06CC\u06A9 1 \u0633\u0627\u0644",
    other: "\u0646\u0632\u062F\u06CC\u06A9 {{count}} \u0633\u0627\u0644"
  }
};
var formatDistance = function formatDistance(token, count, options) {
  var result;
  var tokenValue = formatDistanceLocale[token];
  if (typeof tokenValue === "string") {
    result = tokenValue;
  } else if (count === 1) {
    result = tokenValue.one;
  } else {
    result = tokenValue.other.replace("{{count}}", count.toString());
  }
  if (options !== null && options !== void 0 && options.addSuffix) {
    if (options.comparison && options.comparison > 0) {
      return "\u062F\u0631 " + result;
    } else {
      return result + " \u0642\u0628\u0644";
    }
  }
  return result;
};

// ../../../../../../tmp/date-fns-jalali/locale/_lib/buildFormatLongFn.js
function buildFormatLongFn(args) {
  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    var width = options.width ? String(options.width) : args.defaultWidth;
    var format = args.formats[width] || args.formats[args.defaultWidth];
    return format;
  };
}

// ../../../../../../tmp/date-fns-jalali/locale/fa-IR/_lib/formatLong.js
var dateFormats = {
  full: "EEEE do MMMM y",
  long: "do MMMM y",
  medium: "d MMM y",
  short: "yyyy/MM/dd"
};
var timeFormats = {
  full: "h:mm:ss a zzzz",
  long: "h:mm:ss a z",
  medium: "h:mm:ss a",
  short: "h:mm a"
};
var dateTimeFormats = {
  full: "{{date}} '\u062F\u0631' {{time}}",
  long: "{{date}} '\u062F\u0631' {{time}}",
  medium: "{{date}}, {{time}}",
  short: "{{date}}, {{time}}"
};
var formatLong = {
  date: buildFormatLongFn({
    formats: dateFormats,
    defaultWidth: "full"
  }),
  time: buildFormatLongFn({
    formats: timeFormats,
    defaultWidth: "full"
  }),
  dateTime: buildFormatLongFn({
    formats: dateTimeFormats,
    defaultWidth: "full"
  })
};

// ../../../../../../tmp/date-fns-jalali/locale/fa-IR/_lib/formatRelative.js
var formatRelativeLocale = {
  lastWeek: "eeee '\u06AF\u0630\u0634\u062A\u0647 \u062F\u0631' p",
  yesterday: "'\u062F\u06CC\u0631\u0648\u0632 \u062F\u0631' p",
  today: "'\u0627\u0645\u0631\u0648\u0632 \u062F\u0631' p",
  tomorrow: "'\u0641\u0631\u062F\u0627 \u062F\u0631' p",
  nextWeek: "eeee '\u062F\u0631' p",
  other: "P"
};
var formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};

// ../../../../../../tmp/date-fns-jalali/locale/_lib/buildLocalizeFn.js
function buildLocalizeFn(args) {
  return function (value, options) {
    var context = options !== null && options !== void 0 && options.context ? String(options.context) : "standalone";
    var valuesArray;
    if (context === "formatting" && args.formattingValues) {
      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;
      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
    } else {
      var _defaultWidth = args.defaultWidth;
      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;
      valuesArray = args.values[_width] || args.values[_defaultWidth];
    }
    var index = args.argumentCallback ? args.argumentCallback(value) : value;
    return valuesArray[index];
  };
}

// ../../../../../../tmp/date-fns-jalali/locale/fa-IR/_lib/localize.js
var eraValues = {
  narrow: ["\u0642", "\u0628"],
  abbreviated: ["\u0642.\u0647.", "\u0628.\u0647."],
  wide: ["\u0642\u0628\u0644 \u0627\u0632 \u0647\u062C\u0631\u062A", "\u0628\u0639\u062F \u0627\u0632 \u0647\u062C\u0631\u062A"]
};
var quarterValues = {
  narrow: ["1", "2", "3", "4"],
  abbreviated: ["\u0633\u200C\u06451", "\u0633\u200C\u06452", "\u0633\u200C\u06453", "\u0633\u200C\u06454"],
  wide: ["\u0633\u0647\u200C\u0645\u0627\u0647\u0647 1", "\u0633\u0647\u200C\u0645\u0627\u0647\u0647 2", "\u0633\u0647\u200C\u0645\u0627\u0647\u0647 3", "\u0633\u0647\u200C\u0645\u0627\u0647\u0647 4"]
};
var monthValues = {
  narrow: [
  "\u0641\u0631",
  "\u0627\u0631",
  "\u062E\u0631",
  "\u062A\u06CC",
  "\u0645\u0631",
  "\u0634\u0647",
  "\u0645\u0647",
  "\u0622\u0628",
  "\u0622\u0630",
  "\u062F\u06CC",
  "\u0628\u0647",
  "\u0627\u0633"],

  abbreviated: [
  "\u0641\u0631\u0648",
  "\u0627\u0631\u062F",
  "\u062E\u0631\u062F",
  "\u062A\u06CC\u0631",
  "\u0645\u0631\u062F",
  "\u0634\u0647\u0631",
  "\u0645\u0647\u0631",
  "\u0622\u0628\u0627",
  "\u0622\u0630\u0631",
  "\u062F\u06CC",
  "\u0628\u0647\u0645",
  "\u0627\u0633\u0641"],

  wide: [
  "\u0641\u0631\u0648\u0631\u062F\u06CC\u0646",
  "\u0627\u0631\u062F\u06CC\u0628\u0647\u0634\u062A",
  "\u062E\u0631\u062F\u0627\u062F",
  "\u062A\u06CC\u0631",
  "\u0645\u0631\u062F\u0627\u062F",
  "\u0634\u0647\u0631\u06CC\u0648\u0631",
  "\u0645\u0647\u0631",
  "\u0622\u0628\u0627\u0646",
  "\u0622\u0630\u0631",
  "\u062F\u06CC",
  "\u0628\u0647\u0645\u0646",
  "\u0627\u0633\u0641\u0646\u062F"]

};
var dayValues = {
  narrow: ["\u06CC", "\u062F", "\u0633", "\u0686", "\u067E", "\u062C", "\u0634"],
  short: ["1\u0634", "2\u0634", "3\u0634", "4\u0634", "5\u0634", "\u062C", "\u0634"],
  abbreviated: [
  "\u06CC\u06A9\u200C\u0634\u0646\u0628\u0647",
  "\u062F\u0648\u0634\u0646\u0628\u0647",
  "\u0633\u0647\u200C\u0634\u0646\u0628\u0647",
  "\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647",
  "\u067E\u0646\u062C\u200C\u0634\u0646\u0628\u0647",
  "\u062C\u0645\u0639\u0647",
  "\u0634\u0646\u0628\u0647"],

  wide: [
  "\u06CC\u06A9\u200C\u0634\u0646\u0628\u0647",
  "\u062F\u0648\u0634\u0646\u0628\u0647",
  "\u0633\u0647\u200C\u0634\u0646\u0628\u0647",
  "\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647",
  "\u067E\u0646\u062C\u200C\u0634\u0646\u0628\u0647",
  "\u062C\u0645\u0639\u0647",
  "\u0634\u0646\u0628\u0647"]

};
var dayPeriodValues = {
  narrow: {
    am: "\u0642",
    pm: "\u0628",
    midnight: "\u0646",
    noon: "\u0638",
    morning: "\u0635",
    afternoon: "\u0628.\u0638.",
    evening: "\u0639",
    night: "\u0634"
  },
  abbreviated: {
    am: "\u0642.\u0638.",
    pm: "\u0628.\u0638.",
    midnight: "\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",
    noon: "\u0638\u0647\u0631",
    morning: "\u0635\u0628\u062D",
    afternoon: "\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",
    evening: "\u0639\u0635\u0631",
    night: "\u0634\u0628"
  },
  wide: {
    am: "\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",
    pm: "\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",
    midnight: "\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",
    noon: "\u0638\u0647\u0631",
    morning: "\u0635\u0628\u062D",
    afternoon: "\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",
    evening: "\u0639\u0635\u0631",
    night: "\u0634\u0628"
  }
};
var formattingDayPeriodValues = {
  narrow: {
    am: "\u0642",
    pm: "\u0628",
    midnight: "\u0646",
    noon: "\u0638",
    morning: "\u0635",
    afternoon: "\u0628.\u0638.",
    evening: "\u0639",
    night: "\u0634"
  },
  abbreviated: {
    am: "\u0642.\u0638.",
    pm: "\u0628.\u0638.",
    midnight: "\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",
    noon: "\u0638\u0647\u0631",
    morning: "\u0635\u0628\u062D",
    afternoon: "\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",
    evening: "\u0639\u0635\u0631",
    night: "\u0634\u0628"
  },
  wide: {
    am: "\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",
    pm: "\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",
    midnight: "\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",
    noon: "\u0638\u0647\u0631",
    morning: "\u0635\u0628\u062D",
    afternoon: "\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",
    evening: "\u0639\u0635\u0631",
    night: "\u0634\u0628"
  }
};
var ordinalNumber = function ordinalNumber(dirtyNumber, _options) {
  var number = Number(dirtyNumber);
  return number + "-\u0627\u0645";
};
var localize = {
  ordinalNumber: ordinalNumber,
  era: buildLocalizeFn({
    values: eraValues,
    defaultWidth: "wide"
  }),
  quarter: buildLocalizeFn({
    values: quarterValues,
    defaultWidth: "wide",
    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}
  }),
  month: buildLocalizeFn({
    values: monthValues,
    defaultWidth: "wide"
  }),
  day: buildLocalizeFn({
    values: dayValues,
    defaultWidth: "wide"
  }),
  dayPeriod: buildLocalizeFn({
    values: dayPeriodValues,
    defaultWidth: "wide",
    formattingValues: formattingDayPeriodValues,
    defaultFormattingWidth: "wide"
  })
};

// ../../../../../../tmp/date-fns-jalali/locale/_lib/buildMatchFn.js
function buildMatchFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var width = options.width;
    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
    var matchResult = string.match(matchPattern);
    if (!matchResult) {
      return null;
    }
    var matchedString = matchResult[0];
    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});
    var value;
    value = args.valueCallback ? args.valueCallback(key) : key;
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}
function findKey(object, predicate) {
  for (var key in object) {
    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {
      return key;
    }
  }
  return;
}
function findIndex(array, predicate) {
  for (var key = 0; key < array.length; key++) {
    if (predicate(array[key])) {
      return key;
    }
  }
  return;
}

// ../../../../../../tmp/date-fns-jalali/locale/_lib/buildMatchPatternFn.js
function buildMatchPatternFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var matchResult = string.match(args.matchPattern);
    if (!matchResult)
    return null;
    var matchedString = matchResult[0];
    var parseResult = string.match(args.parsePattern);
    if (!parseResult)
    return null;
    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}

// ../../../../../../tmp/date-fns-jalali/locale/fa-IR/_lib/match.js
var matchOrdinalNumberPattern = /^(\d+)(-?ام)?/i;
var parseOrdinalNumberPattern = /\d+/i;
var matchEraPatterns = {
  narrow: /^(ق|ب)/i,
  abbreviated: /^(ق\.?\s?ه\.?|ب\.?\s?ه\.?|ه\.?)/i,
  wide: /^(قبل از هجرت|هجری شمسی|بعد از هجرت)/i
};
var parseEraPatterns = {
  any: [/^قبل/i, /^بعد/i]
};
var matchQuarterPatterns = {
  narrow: /^[1234]/i,
  abbreviated: /^(ف|Q|س‌م)[1234]/i,
  wide: /^(فصل|quarter|سه‌ماهه) [1234](-ام|ام)?/i
};
var parseQuarterPatterns = {
  any: [/1/i, /2/i, /3/i, /4/i]
};
var matchMonthPatterns = {
  narrow: /^(فر|ار|خر|تی|مر|شه|مه|آب|آذ|دی|به|اس)/i,
  abbreviated: /^(فرو|ارد|خرد|تیر|مرد|شهر|مهر|آبا|آذر|دی|بهم|اسف)/i,
  wide: /^(فروردین|اردیبهشت|خرداد|تیر|مرداد|شهریور|مهر|آبان|آذر|دی|بهمن|اسفند)/i
};
var parseMonthPatterns = {
  narrow: [
  /^فر/i,
  /^ار/i,
  /^خر/i,
  /^تی/i,
  /^مر/i,
  /^شه/i,
  /^مه/i,
  /^آب/i,
  /^آذ/i,
  /^دی/i,
  /^به/i,
  /^اس/i],

  any: [
  /^فر/i,
  /^ار/i,
  /^خر/i,
  /^تی/i,
  /^مر/i,
  /^شه/i,
  /^مه/i,
  /^آب/i,
  /^آذ/i,
  /^دی/i,
  /^به/i,
  /^اس/i]

};
var matchDayPatterns = {
  narrow: /^[شیدسچپج]/i,
  short: /^(ش|ج|1ش|2ش|3ش|4ش|5ش)/i,
  abbreviated: /^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i,
  wide: /^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i
};
var parseDayPatterns = {
  narrow: [/^ی/i, /^دو/i, /^س/i, /^چ/i, /^پ/i, /^ج/i, /^ش/i],
  any: [
  /^(ی|1ش|یکشنبه)/i,
  /^(د|2ش|دوشنبه)/i,
  /^(س|3ش|سه‌شنبه)/i,
  /^(چ|4ش|چهارشنبه)/i,
  /^(پ|5ش|پنجشنبه)/i,
  /^(ج|جمعه)/i,
  /^(ش|شنبه)/i]

};
var matchDayPeriodPatterns = {
  narrow: /^(ب|ق|ن|ظ|ص|ب.ظ.|ع|ش)/i,
  any: /^(ق.ظ.|ب.ظ.|قبل‌ازظهر|نیمه‌شب|ظهر|صبح|بعدازظهر|عصر|شب)/i
};
var parseDayPeriodPatterns = {
  any: {
    am: /^(ق|ق.ظ.|قبل‌ازظهر)/i,
    pm: /^(ب|ب.ظ.|بعدازظهر)/i,
    midnight: /^(‌نیمه‌شب|ن)/i,
    noon: /^(ظ|ظهر)/i,
    morning: /^(ص|صبح)/i,
    afternoon: /^(ب|ب.ظ.|بعدازظهر)/i,
    evening: /^(ع|عصر)/i,
    night: /^(ش|شب)/i
  }
};
var match = {
  ordinalNumber: buildMatchPatternFn({
    matchPattern: matchOrdinalNumberPattern,
    parsePattern: parseOrdinalNumberPattern,
    valueCallback: function valueCallback(value) {return parseInt(value, 10);}
  }),
  era: buildMatchFn({
    matchPatterns: matchEraPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseEraPatterns,
    defaultParseWidth: "any"
  }),
  quarter: buildMatchFn({
    matchPatterns: matchQuarterPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseQuarterPatterns,
    defaultParseWidth: "any",
    valueCallback: function valueCallback(index) {return index + 1;}
  }),
  month: buildMatchFn({
    matchPatterns: matchMonthPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseMonthPatterns,
    defaultParseWidth: "any"
  }),
  day: buildMatchFn({
    matchPatterns: matchDayPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseDayPatterns,
    defaultParseWidth: "any"
  }),
  dayPeriod: buildMatchFn({
    matchPatterns: matchDayPeriodPatterns,
    defaultMatchWidth: "any",
    parsePatterns: parseDayPeriodPatterns,
    defaultParseWidth: "any"
  })
};

// ../../../../../../tmp/date-fns-jalali/locale/fa-IR.js
var faIR = {
  code: "fa-IR",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 6,
    firstWeekContainsDate: 1
  }
};
// ../../../../../../tmp/date-fns-jalali/getDayOfYear.js
function getDayOfYear(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var diff = differenceInCalendarDays(_date, startOfYear(_date));
  var dayOfYear = diff + 1;
  return dayOfYear;
}

// ../../../../../../tmp/date-fns-jalali/getISOWeek.js
function getISOWeek(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var diff = +startOfISOWeek(_date) - +startOfISOWeekYear(_date);
  return Math.round(diff / millisecondsInWeek) + 1;
}

// ../../../../../../tmp/date-fns-jalali/getWeekYear.js
function getWeekYear(date, options) {var _ref7, _ref8, _ref9, _options$firstWeekCon, _options$locale3, _defaultOptions5$loca;
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(_date);
  var defaultOptions5 = getDefaultOptions();
  var firstWeekContainsDate = (_ref7 = (_ref8 = (_ref9 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 || (_options$locale3 = options.locale) === null || _options$locale3 === void 0 || (_options$locale3 = _options$locale3.options) === null || _options$locale3 === void 0 ? void 0 : _options$locale3.firstWeekContainsDate) !== null && _ref9 !== void 0 ? _ref9 : defaultOptions5.firstWeekContainsDate) !== null && _ref8 !== void 0 ? _ref8 : (_defaultOptions5$loca = defaultOptions5.locale) === null || _defaultOptions5$loca === void 0 || (_defaultOptions5$loca = _defaultOptions5$loca.options) === null || _defaultOptions5$loca === void 0 ? void 0 : _defaultOptions5$loca.firstWeekContainsDate) !== null && _ref7 !== void 0 ? _ref7 : 1;
  var firstWeekOfNextYear = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);
  setFullYear(firstWeekOfNextYear, year + 1, 0, firstWeekContainsDate);
  firstWeekOfNextYear.setHours(0, 0, 0, 0);
  var startOfNextYear = startOfWeek(firstWeekOfNextYear, options);
  var firstWeekOfThisYear = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);
  setFullYear(firstWeekOfThisYear, year, 0, firstWeekContainsDate);
  firstWeekOfThisYear.setHours(0, 0, 0, 0);
  var startOfThisYear = startOfWeek(firstWeekOfThisYear, options);
  if (+_date >= +startOfNextYear) {
    return year + 1;
  } else if (+_date >= +startOfThisYear) {
    return year;
  } else {
    return year - 1;
  }
}

// ../../../../../../tmp/date-fns-jalali/startOfWeekYear.js
function startOfWeekYear(date, options) {var _ref10, _ref11, _ref12, _options$firstWeekCon2, _options$locale4, _defaultOptions6$loca;
  var defaultOptions6 = getDefaultOptions();
  var firstWeekContainsDate = (_ref10 = (_ref11 = (_ref12 = (_options$firstWeekCon2 = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon2 !== void 0 ? _options$firstWeekCon2 : options === null || options === void 0 || (_options$locale4 = options.locale) === null || _options$locale4 === void 0 || (_options$locale4 = _options$locale4.options) === null || _options$locale4 === void 0 ? void 0 : _options$locale4.firstWeekContainsDate) !== null && _ref12 !== void 0 ? _ref12 : defaultOptions6.firstWeekContainsDate) !== null && _ref11 !== void 0 ? _ref11 : (_defaultOptions6$loca = defaultOptions6.locale) === null || _defaultOptions6$loca === void 0 || (_defaultOptions6$loca = _defaultOptions6$loca.options) === null || _defaultOptions6$loca === void 0 ? void 0 : _defaultOptions6$loca.firstWeekContainsDate) !== null && _ref10 !== void 0 ? _ref10 : 1;
  var year = getWeekYear(date, options);
  var firstWeek = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);
  setFullYear(firstWeek, year, 0, firstWeekContainsDate);
  firstWeek.setHours(0, 0, 0, 0);
  var _date = startOfWeek(firstWeek, options);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/getWeek.js
function getWeek(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var diff = +startOfWeek(_date, options) - +startOfWeekYear(_date, options);
  return Math.round(diff / millisecondsInWeek) + 1;
}

// ../../../../../../tmp/date-fns-jalali/_lib/addLeadingZeros.js
function addLeadingZeros(number, targetLength) {
  var sign = number < 0 ? "-" : "";
  var output = Math.abs(number).toString().padStart(targetLength, "0");
  return sign + output;
}

// ../../../../../../tmp/date-fns-jalali/_lib/format/lightFormatters.js
var lightFormatters = {
  y: function y(date, token) {
    var signedYear = getFullYear(date);
    var year = signedYear > 0 ? signedYear : 1 - signedYear;
    return addLeadingZeros(token === "yy" ? year % 100 : year, token.length);
  },
  M: function M(date, token) {
    var month = getMonth(date);
    return token === "M" ? String(month + 1) : addLeadingZeros(month + 1, 2);
  },
  d: function d(date, token) {
    return addLeadingZeros(getDate(date), token.length);
  },
  a: function a(date, token) {
    var dayPeriodEnumValue = date.getHours() / 12 >= 1 ? "pm" : "am";
    switch (token) {
      case "a":
      case "aa":
        return dayPeriodEnumValue.toUpperCase();
      case "aaa":
        return dayPeriodEnumValue;
      case "aaaaa":
        return dayPeriodEnumValue[0];
      case "aaaa":
      default:
        return dayPeriodEnumValue === "am" ? "a.m." : "p.m.";
    }
  },
  h: function h(date, token) {
    return addLeadingZeros(date.getHours() % 12 || 12, token.length);
  },
  H: function H(date, token) {
    return addLeadingZeros(date.getHours(), token.length);
  },
  m: function m(date, token) {
    return addLeadingZeros(date.getMinutes(), token.length);
  },
  s: function s(date, token) {
    return addLeadingZeros(date.getSeconds(), token.length);
  },
  S: function S(date, token) {
    var numberOfDigits = token.length;
    var milliseconds = date.getMilliseconds();
    var fractionalSeconds = Math.trunc(milliseconds * Math.pow(10, numberOfDigits - 3));
    return addLeadingZeros(fractionalSeconds, token.length);
  }
};

// ../../../../../../tmp/date-fns-jalali/_lib/format/formatters.js
function formatTimezoneShort(offset) {var delimiter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : "";
  var sign = offset > 0 ? "-" : "+";
  var absOffset = Math.abs(offset);
  var hours = Math.trunc(absOffset / 60);
  var minutes = absOffset % 60;
  if (minutes === 0) {
    return sign + String(hours);
  }
  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);
}
function formatTimezoneWithOptionalMinutes(offset, delimiter) {
  if (offset % 60 === 0) {
    var sign = offset > 0 ? "-" : "+";
    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);
  }
  return formatTimezone(offset, delimiter);
}
function formatTimezone(offset) {var delimiter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : "";
  var sign = offset > 0 ? "-" : "+";
  var absOffset = Math.abs(offset);
  var hours = addLeadingZeros(Math.trunc(absOffset / 60), 2);
  var minutes = addLeadingZeros(absOffset % 60, 2);
  return sign + hours + delimiter + minutes;
}
var dayPeriodEnum = {
  am: "am",
  pm: "pm",
  midnight: "midnight",
  noon: "noon",
  morning: "morning",
  afternoon: "afternoon",
  evening: "evening",
  night: "night"
};
var formatters = {
  G: function G(date, token, localize3) {
    var era = getFullYear(date) > 0 ? 1 : 0;
    switch (token) {
      case "G":
      case "GG":
      case "GGG":
        return localize3.era(era, { width: "abbreviated" });
      case "GGGGG":
        return localize3.era(era, { width: "narrow" });
      case "GGGG":
      default:
        return localize3.era(era, { width: "wide" });
    }
  },
  y: function y(date, token, localize3) {
    if (token === "yo") {
      var signedYear = getFullYear(date);
      var year = signedYear > 0 ? signedYear : 1 - signedYear;
      return localize3.ordinalNumber(year, { unit: "year" });
    }
    return lightFormatters.y(date, token);
  },
  Y: function Y(date, token, localize3, options) {
    var signedWeekYear = getWeekYear(date, options);
    var weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;
    if (token === "YY") {
      var twoDigitYear = weekYear % 100;
      return addLeadingZeros(twoDigitYear, 2);
    }
    if (token === "Yo") {
      return localize3.ordinalNumber(weekYear, { unit: "year" });
    }
    return addLeadingZeros(weekYear, token.length);
  },
  R: function R(date, token) {
    var isoWeekYear = getISOWeekYear(date);
    return addLeadingZeros(isoWeekYear, token.length);
  },
  u: function u(date, token) {
    var year = getFullYear(date);
    return addLeadingZeros(year, token.length);
  },
  Q: function Q(date, token, localize3) {
    var quarter = Math.ceil((getMonth(date) + 1) / 3);
    switch (token) {
      case "Q":
        return String(quarter);
      case "QQ":
        return addLeadingZeros(quarter, 2);
      case "Qo":
        return localize3.ordinalNumber(quarter, { unit: "quarter" });
      case "QQQ":
        return localize3.quarter(quarter, {
          width: "abbreviated",
          context: "formatting"
        });
      case "QQQQQ":
        return localize3.quarter(quarter, {
          width: "narrow",
          context: "formatting"
        });
      case "QQQQ":
      default:
        return localize3.quarter(quarter, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  q: function q(date, token, localize3) {
    var quarter = Math.ceil((getMonth(date) + 1) / 3);
    switch (token) {
      case "q":
        return String(quarter);
      case "qq":
        return addLeadingZeros(quarter, 2);
      case "qo":
        return localize3.ordinalNumber(quarter, { unit: "quarter" });
      case "qqq":
        return localize3.quarter(quarter, {
          width: "abbreviated",
          context: "standalone"
        });
      case "qqqqq":
        return localize3.quarter(quarter, {
          width: "narrow",
          context: "standalone"
        });
      case "qqqq":
      default:
        return localize3.quarter(quarter, {
          width: "wide",
          context: "standalone"
        });
    }
  },
  M: function M(date, token, localize3) {
    var month = getMonth(date);
    switch (token) {
      case "M":
      case "MM":
        return lightFormatters.M(date, token);
      case "Mo":
        return localize3.ordinalNumber(month + 1, { unit: "month" });
      case "MMM":
        return localize3.month(month, {
          width: "abbreviated",
          context: "formatting"
        });
      case "MMMMM":
        return localize3.month(month, {
          width: "narrow",
          context: "formatting"
        });
      case "MMMM":
      default:
        return localize3.month(month, { width: "wide", context: "formatting" });
    }
  },
  L: function L(date, token, localize3) {
    var month = getMonth(date);
    switch (token) {
      case "L":
        return String(month + 1);
      case "LL":
        return addLeadingZeros(month + 1, 2);
      case "Lo":
        return localize3.ordinalNumber(month + 1, { unit: "month" });
      case "LLL":
        return localize3.month(month, {
          width: "abbreviated",
          context: "standalone"
        });
      case "LLLLL":
        return localize3.month(month, {
          width: "narrow",
          context: "standalone"
        });
      case "LLLL":
      default:
        return localize3.month(month, { width: "wide", context: "standalone" });
    }
  },
  w: function w(date, token, localize3, options) {
    var week = getWeek(date, options);
    if (token === "wo") {
      return localize3.ordinalNumber(week, { unit: "week" });
    }
    return addLeadingZeros(week, token.length);
  },
  I: function I(date, token, localize3) {
    var isoWeek = getISOWeek(date);
    if (token === "Io") {
      return localize3.ordinalNumber(isoWeek, { unit: "week" });
    }
    return addLeadingZeros(isoWeek, token.length);
  },
  d: function d(date, token, localize3) {
    if (token === "do") {
      return localize3.ordinalNumber(getDate(date), { unit: "date" });
    }
    return lightFormatters.d(date, token);
  },
  D: function D(date, token, localize3) {
    var dayOfYear = getDayOfYear(date);
    if (token === "Do") {
      return localize3.ordinalNumber(dayOfYear, { unit: "dayOfYear" });
    }
    return addLeadingZeros(dayOfYear, token.length);
  },
  E: function E(date, token, localize3) {
    var dayOfWeek = date.getDay();
    switch (token) {
      case "E":
      case "EE":
      case "EEE":
        return localize3.day(dayOfWeek, {
          width: "abbreviated",
          context: "formatting"
        });
      case "EEEEE":
        return localize3.day(dayOfWeek, {
          width: "narrow",
          context: "formatting"
        });
      case "EEEEEE":
        return localize3.day(dayOfWeek, {
          width: "short",
          context: "formatting"
        });
      case "EEEE":
      default:
        return localize3.day(dayOfWeek, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  e: function e(date, token, localize3, options) {
    var dayOfWeek = date.getDay();
    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;
    switch (token) {
      case "e":
        return String(localDayOfWeek);
      case "ee":
        return addLeadingZeros(localDayOfWeek, 2);
      case "eo":
        return localize3.ordinalNumber(localDayOfWeek, { unit: "day" });
      case "eee":
        return localize3.day(dayOfWeek, {
          width: "abbreviated",
          context: "formatting"
        });
      case "eeeee":
        return localize3.day(dayOfWeek, {
          width: "narrow",
          context: "formatting"
        });
      case "eeeeee":
        return localize3.day(dayOfWeek, {
          width: "short",
          context: "formatting"
        });
      case "eeee":
      default:
        return localize3.day(dayOfWeek, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  c: function c(date, token, localize3, options) {
    var dayOfWeek = date.getDay();
    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;
    switch (token) {
      case "c":
        return String(localDayOfWeek);
      case "cc":
        return addLeadingZeros(localDayOfWeek, token.length);
      case "co":
        return localize3.ordinalNumber(localDayOfWeek, { unit: "day" });
      case "ccc":
        return localize3.day(dayOfWeek, {
          width: "abbreviated",
          context: "standalone"
        });
      case "ccccc":
        return localize3.day(dayOfWeek, {
          width: "narrow",
          context: "standalone"
        });
      case "cccccc":
        return localize3.day(dayOfWeek, {
          width: "short",
          context: "standalone"
        });
      case "cccc":
      default:
        return localize3.day(dayOfWeek, {
          width: "wide",
          context: "standalone"
        });
    }
  },
  i: function i(date, token, localize3) {
    var dayOfWeek = date.getDay();
    var isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
    switch (token) {
      case "i":
        return String(isoDayOfWeek);
      case "ii":
        return addLeadingZeros(isoDayOfWeek, token.length);
      case "io":
        return localize3.ordinalNumber(isoDayOfWeek, { unit: "day" });
      case "iii":
        return localize3.day(dayOfWeek, {
          width: "abbreviated",
          context: "formatting"
        });
      case "iiiii":
        return localize3.day(dayOfWeek, {
          width: "narrow",
          context: "formatting"
        });
      case "iiiiii":
        return localize3.day(dayOfWeek, {
          width: "short",
          context: "formatting"
        });
      case "iiii":
      default:
        return localize3.day(dayOfWeek, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  a: function a(date, token, localize3) {
    var hours = date.getHours();
    var dayPeriodEnumValue = hours / 12 >= 1 ? "pm" : "am";
    switch (token) {
      case "a":
      case "aa":
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        });
      case "aaa":
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        }).toLowerCase();
      case "aaaaa":
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "narrow",
          context: "formatting"
        });
      case "aaaa":
      default:
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  b: function b(date, token, localize3) {
    var hours = date.getHours();
    var dayPeriodEnumValue;
    if (hours === 12) {
      dayPeriodEnumValue = dayPeriodEnum.noon;
    } else if (hours === 0) {
      dayPeriodEnumValue = dayPeriodEnum.midnight;
    } else {
      dayPeriodEnumValue = hours / 12 >= 1 ? "pm" : "am";
    }
    switch (token) {
      case "b":
      case "bb":
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        });
      case "bbb":
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        }).toLowerCase();
      case "bbbbb":
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "narrow",
          context: "formatting"
        });
      case "bbbb":
      default:
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  B: function B(date, token, localize3) {
    var hours = date.getHours();
    var dayPeriodEnumValue;
    if (hours >= 17) {
      dayPeriodEnumValue = dayPeriodEnum.evening;
    } else if (hours >= 12) {
      dayPeriodEnumValue = dayPeriodEnum.afternoon;
    } else if (hours >= 4) {
      dayPeriodEnumValue = dayPeriodEnum.morning;
    } else {
      dayPeriodEnumValue = dayPeriodEnum.night;
    }
    switch (token) {
      case "B":
      case "BB":
      case "BBB":
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "abbreviated",
          context: "formatting"
        });
      case "BBBBB":
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "narrow",
          context: "formatting"
        });
      case "BBBB":
      default:
        return localize3.dayPeriod(dayPeriodEnumValue, {
          width: "wide",
          context: "formatting"
        });
    }
  },
  h: function h(date, token, localize3) {
    if (token === "ho") {
      var hours = date.getHours() % 12;
      if (hours === 0)
      hours = 12;
      return localize3.ordinalNumber(hours, { unit: "hour" });
    }
    return lightFormatters.h(date, token);
  },
  H: function H(date, token, localize3) {
    if (token === "Ho") {
      return localize3.ordinalNumber(date.getHours(), { unit: "hour" });
    }
    return lightFormatters.H(date, token);
  },
  K: function K(date, token, localize3) {
    var hours = date.getHours() % 12;
    if (token === "Ko") {
      return localize3.ordinalNumber(hours, { unit: "hour" });
    }
    return addLeadingZeros(hours, token.length);
  },
  k: function k(date, token, localize3) {
    var hours = date.getHours();
    if (hours === 0)
    hours = 24;
    if (token === "ko") {
      return localize3.ordinalNumber(hours, { unit: "hour" });
    }
    return addLeadingZeros(hours, token.length);
  },
  m: function m(date, token, localize3) {
    if (token === "mo") {
      return localize3.ordinalNumber(date.getMinutes(), { unit: "minute" });
    }
    return lightFormatters.m(date, token);
  },
  s: function s(date, token, localize3) {
    if (token === "so") {
      return localize3.ordinalNumber(date.getSeconds(), { unit: "second" });
    }
    return lightFormatters.s(date, token);
  },
  S: function S(date, token) {
    return lightFormatters.S(date, token);
  },
  X: function X(date, token, _localize) {
    var timezoneOffset = date.getTimezoneOffset();
    if (timezoneOffset === 0) {
      return "Z";
    }
    switch (token) {
      case "X":
        return formatTimezoneWithOptionalMinutes(timezoneOffset);
      case "XXXX":
      case "XX":
        return formatTimezone(timezoneOffset);
      case "XXXXX":
      case "XXX":
      default:
        return formatTimezone(timezoneOffset, ":");
    }
  },
  x: function x(date, token, _localize) {
    var timezoneOffset = date.getTimezoneOffset();
    switch (token) {
      case "x":
        return formatTimezoneWithOptionalMinutes(timezoneOffset);
      case "xxxx":
      case "xx":
        return formatTimezone(timezoneOffset);
      case "xxxxx":
      case "xxx":
      default:
        return formatTimezone(timezoneOffset, ":");
    }
  },
  O: function O(date, token, _localize) {
    var timezoneOffset = date.getTimezoneOffset();
    switch (token) {
      case "O":
      case "OO":
      case "OOO":
        return "GMT" + formatTimezoneShort(timezoneOffset, ":");
      case "OOOO":
      default:
        return "GMT" + formatTimezone(timezoneOffset, ":");
    }
  },
  z: function z(date, token, _localize) {
    var timezoneOffset = date.getTimezoneOffset();
    switch (token) {
      case "z":
      case "zz":
      case "zzz":
        return "GMT" + formatTimezoneShort(timezoneOffset, ":");
      case "zzzz":
      default:
        return "GMT" + formatTimezone(timezoneOffset, ":");
    }
  },
  t: function t(date, token, _localize) {
    var timestamp = Math.trunc(+date / 1000);
    return addLeadingZeros(timestamp, token.length);
  },
  T: function T(date, token, _localize) {
    return addLeadingZeros(+date, token.length);
  }
};

// ../../../../../../tmp/date-fns-jalali/_lib/format/longFormatters.js
var dateLongFormatter = function dateLongFormatter(pattern, formatLong3) {
  switch (pattern) {
    case "P":
      return formatLong3.date({ width: "short" });
    case "PP":
      return formatLong3.date({ width: "medium" });
    case "PPP":
      return formatLong3.date({ width: "long" });
    case "PPPP":
    default:
      return formatLong3.date({ width: "full" });
  }
};
var timeLongFormatter = function timeLongFormatter(pattern, formatLong3) {
  switch (pattern) {
    case "p":
      return formatLong3.time({ width: "short" });
    case "pp":
      return formatLong3.time({ width: "medium" });
    case "ppp":
      return formatLong3.time({ width: "long" });
    case "pppp":
    default:
      return formatLong3.time({ width: "full" });
  }
};
var dateTimeLongFormatter = function dateTimeLongFormatter(pattern, formatLong3) {
  var matchResult = pattern.match(/(P+)(p+)?/) || [];
  var datePattern = matchResult[1];
  var timePattern = matchResult[2];
  if (!timePattern) {
    return dateLongFormatter(pattern, formatLong3);
  }
  var dateTimeFormat;
  switch (datePattern) {
    case "P":
      dateTimeFormat = formatLong3.dateTime({ width: "short" });
      break;
    case "PP":
      dateTimeFormat = formatLong3.dateTime({ width: "medium" });
      break;
    case "PPP":
      dateTimeFormat = formatLong3.dateTime({ width: "long" });
      break;
    case "PPPP":
    default:
      dateTimeFormat = formatLong3.dateTime({ width: "full" });
      break;
  }
  return dateTimeFormat.replace("{{date}}", dateLongFormatter(datePattern, formatLong3)).replace("{{time}}", timeLongFormatter(timePattern, formatLong3));
};
var longFormatters = {
  p: timeLongFormatter,
  P: dateTimeLongFormatter
};

// ../../../../../../tmp/date-fns-jalali/_lib/protectedTokens.js
function isProtectedDayOfYearToken(token) {
  return dayOfYearTokenRE.test(token);
}
function isProtectedWeekYearToken(token) {
  return weekYearTokenRE.test(token);
}
function warnOrThrowProtectedError(token, format, input) {
  var _message = message(token, format, input);
  console.warn(_message);
  if (throwTokens.includes(token))
  throw new RangeError(_message);
}
function message(token, format, input) {
  var subject = token[0] === "Y" ? "years" : "days of the month";
  return "Use `".concat(token.toLowerCase(), "` instead of `").concat(token, "` (in `").concat(format, "`) for formatting ").concat(subject, " to the input `").concat(input, "`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md");
}
var dayOfYearTokenRE = /^D+$/;
var weekYearTokenRE = /^Y+$/;
var throwTokens = ["D", "DD", "YY", "YYYY"];

// ../../../../../../tmp/date-fns-jalali/format.js
function format(date, formatStr, options) {var _ref13, _options$locale5, _ref14, _ref15, _ref16, _options$firstWeekCon3, _options$locale6, _defaultOptions7$loca, _ref17, _ref18, _ref19, _options$weekStartsOn3, _options$locale7, _defaultOptions7$loca2;
  var defaultOptions7 = getDefaultOptions();
  var locale = (_ref13 = (_options$locale5 = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale5 !== void 0 ? _options$locale5 : defaultOptions7.locale) !== null && _ref13 !== void 0 ? _ref13 : faIR;
  var firstWeekContainsDate = (_ref14 = (_ref15 = (_ref16 = (_options$firstWeekCon3 = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon3 !== void 0 ? _options$firstWeekCon3 : options === null || options === void 0 || (_options$locale6 = options.locale) === null || _options$locale6 === void 0 || (_options$locale6 = _options$locale6.options) === null || _options$locale6 === void 0 ? void 0 : _options$locale6.firstWeekContainsDate) !== null && _ref16 !== void 0 ? _ref16 : defaultOptions7.firstWeekContainsDate) !== null && _ref15 !== void 0 ? _ref15 : (_defaultOptions7$loca = defaultOptions7.locale) === null || _defaultOptions7$loca === void 0 || (_defaultOptions7$loca = _defaultOptions7$loca.options) === null || _defaultOptions7$loca === void 0 ? void 0 : _defaultOptions7$loca.firstWeekContainsDate) !== null && _ref14 !== void 0 ? _ref14 : 1;
  var weekStartsOn = (_ref17 = (_ref18 = (_ref19 = (_options$weekStartsOn3 = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn3 !== void 0 ? _options$weekStartsOn3 : options === null || options === void 0 || (_options$locale7 = options.locale) === null || _options$locale7 === void 0 || (_options$locale7 = _options$locale7.options) === null || _options$locale7 === void 0 ? void 0 : _options$locale7.weekStartsOn) !== null && _ref19 !== void 0 ? _ref19 : defaultOptions7.weekStartsOn) !== null && _ref18 !== void 0 ? _ref18 : (_defaultOptions7$loca2 = defaultOptions7.locale) === null || _defaultOptions7$loca2 === void 0 || (_defaultOptions7$loca2 = _defaultOptions7$loca2.options) === null || _defaultOptions7$loca2 === void 0 ? void 0 : _defaultOptions7$loca2.weekStartsOn) !== null && _ref17 !== void 0 ? _ref17 : 6;
  var originalDate = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  if (!isValid(originalDate)) {
    throw new RangeError("Invalid time value");
  }
  var parts = formatStr.match(longFormattingTokensRegExp).map(function (substring) {
    var firstCharacter = substring[0];
    if (firstCharacter === "p" || firstCharacter === "P") {
      var longFormatter = longFormatters[firstCharacter];
      return longFormatter(substring, locale.formatLong);
    }
    return substring;
  }).join("").match(formattingTokensRegExp).map(function (substring) {
    if (substring === "''") {
      return { isToken: false, value: "'" };
    }
    var firstCharacter = substring[0];
    if (firstCharacter === "'") {
      return { isToken: false, value: cleanEscapedString(substring) };
    }
    if (formatters[firstCharacter]) {
      return { isToken: true, value: substring };
    }
    if (firstCharacter.match(unescapedLatinCharacterRegExp)) {
      throw new RangeError("Format string contains an unescaped latin alphabet character `" + firstCharacter + "`");
    }
    return { isToken: false, value: substring };
  });
  if (locale.localize.preprocessor) {
    parts = locale.localize.preprocessor(originalDate, parts);
  }
  var formatterOptions = {
    firstWeekContainsDate: firstWeekContainsDate,
    weekStartsOn: weekStartsOn,
    locale: locale
  };
  return parts.map(function (part) {
    if (!part.isToken)
    return part.value;
    var token = part.value;
    if (!(options !== null && options !== void 0 && options.useAdditionalWeekYearTokens) && isProtectedWeekYearToken(token) || !(options !== null && options !== void 0 && options.useAdditionalDayOfYearTokens) && isProtectedDayOfYearToken(token)) {
      warnOrThrowProtectedError(token, formatStr, String(date));
    }
    var formatter = formatters[token[0]];
    return formatter(originalDate, token, locale.localize, formatterOptions);
  }).join("");
}
function cleanEscapedString(input) {
  var matched = input.match(escapedStringRegExp);
  if (!matched) {
    return input;
  }
  return matched[1].replace(doubleQuoteRegExp, "'");
}
var formattingTokensRegExp = /[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;
var longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
var escapedStringRegExp = /^'([^]*?)'?$/;
var doubleQuoteRegExp = /''/g;
var unescapedLatinCharacterRegExp = /[a-zA-Z]/;

// ../../../../../../tmp/date-fns-jalali/fp/format.js
var format3 = convertToFP(format, 2);
// ../../../../../../tmp/date-fns-jalali/formatDistance.js
function formatDistance3(laterDate, earlierDate, options) {var _ref20, _options$locale8;
  var defaultOptions8 = getDefaultOptions();
  var locale = (_ref20 = (_options$locale8 = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale8 !== void 0 ? _options$locale8 : defaultOptions8.locale) !== null && _ref20 !== void 0 ? _ref20 : faIR;
  var minutesInAlmostTwoDays = 2520;
  var comparison = compareAsc(laterDate, earlierDate);
  if (isNaN(comparison))
  throw new RangeError("Invalid time value");
  var localizeOptions = Object.assign({}, options, {
    addSuffix: options === null || options === void 0 ? void 0 : options.addSuffix,
    comparison: comparison
  });
  var _normalizeDates35 = normalizeDates.apply(void 0, [options === null || options === void 0 ? void 0 : options.in].concat(_toConsumableArray(comparison > 0 ? [earlierDate, laterDate] : [laterDate, earlierDate]))),_normalizeDates36 = _slicedToArray(_normalizeDates35, 2),laterDate_ = _normalizeDates36[0],earlierDate_ = _normalizeDates36[1];
  var seconds = differenceInSeconds(earlierDate_, laterDate_);
  var offsetInSeconds = (getTimezoneOffsetInMilliseconds(earlierDate_) - getTimezoneOffsetInMilliseconds(laterDate_)) / 1000;
  var minutes = Math.round((seconds - offsetInSeconds) / 60);
  var months;
  if (minutes < 2) {
    if (options !== null && options !== void 0 && options.includeSeconds) {
      if (seconds < 5) {
        return locale.formatDistance("lessThanXSeconds", 5, localizeOptions);
      } else if (seconds < 10) {
        return locale.formatDistance("lessThanXSeconds", 10, localizeOptions);
      } else if (seconds < 20) {
        return locale.formatDistance("lessThanXSeconds", 20, localizeOptions);
      } else if (seconds < 40) {
        return locale.formatDistance("halfAMinute", 0, localizeOptions);
      } else if (seconds < 60) {
        return locale.formatDistance("lessThanXMinutes", 1, localizeOptions);
      } else {
        return locale.formatDistance("xMinutes", 1, localizeOptions);
      }
    } else {
      if (minutes === 0) {
        return locale.formatDistance("lessThanXMinutes", 1, localizeOptions);
      } else {
        return locale.formatDistance("xMinutes", minutes, localizeOptions);
      }
    }
  } else if (minutes < 45) {
    return locale.formatDistance("xMinutes", minutes, localizeOptions);
  } else if (minutes < 90) {
    return locale.formatDistance("aboutXHours", 1, localizeOptions);
  } else if (minutes < minutesInDay) {
    var hours = Math.round(minutes / 60);
    return locale.formatDistance("aboutXHours", hours, localizeOptions);
  } else if (minutes < minutesInAlmostTwoDays) {
    return locale.formatDistance("xDays", 1, localizeOptions);
  } else if (minutes < minutesInMonth) {
    var _days = Math.round(minutes / minutesInDay);
    return locale.formatDistance("xDays", _days, localizeOptions);
  } else if (minutes < minutesInMonth * 2) {
    months = Math.round(minutes / minutesInMonth);
    return locale.formatDistance("aboutXMonths", months, localizeOptions);
  }
  months = differenceInMonths(earlierDate_, laterDate_);
  if (months < 12) {
    var nearestMonth = Math.round(minutes / minutesInMonth);
    return locale.formatDistance("xMonths", nearestMonth, localizeOptions);
  } else {
    var monthsSinceStartOfYear = months % 12;
    var years = Math.trunc(months / 12);
    if (monthsSinceStartOfYear < 3) {
      return locale.formatDistance("aboutXYears", years, localizeOptions);
    } else if (monthsSinceStartOfYear < 9) {
      return locale.formatDistance("overXYears", years, localizeOptions);
    } else {
      return locale.formatDistance("almostXYears", years + 1, localizeOptions);
    }
  }
}

// ../../../../../../tmp/date-fns-jalali/fp/formatDistance.js
var formatDistance5 = convertToFP(formatDistance3, 2);
// ../../../../../../tmp/date-fns-jalali/formatDistanceStrict.js
function formatDistanceStrict(laterDate, earlierDate, options) {var _ref21, _options$locale9, _options$roundingMeth;
  var defaultOptions9 = getDefaultOptions();
  var locale = (_ref21 = (_options$locale9 = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale9 !== void 0 ? _options$locale9 : defaultOptions9.locale) !== null && _ref21 !== void 0 ? _ref21 : faIR;
  var comparison = compareAsc(laterDate, earlierDate);
  if (isNaN(comparison)) {
    throw new RangeError("Invalid time value");
  }
  var localizeOptions = Object.assign({}, options, {
    addSuffix: options === null || options === void 0 ? void 0 : options.addSuffix,
    comparison: comparison
  });
  var _normalizeDates37 = normalizeDates.apply(void 0, [options === null || options === void 0 ? void 0 : options.in].concat(_toConsumableArray(comparison > 0 ? [earlierDate, laterDate] : [laterDate, earlierDate]))),_normalizeDates38 = _slicedToArray(_normalizeDates37, 2),laterDate_ = _normalizeDates38[0],earlierDate_ = _normalizeDates38[1];
  var roundingMethod = getRoundingMethod((_options$roundingMeth = options === null || options === void 0 ? void 0 : options.roundingMethod) !== null && _options$roundingMeth !== void 0 ? _options$roundingMeth : "round");
  var milliseconds = earlierDate_.getTime() - laterDate_.getTime();
  var minutes = milliseconds / millisecondsInMinute;
  var timezoneOffset = getTimezoneOffsetInMilliseconds(earlierDate_) - getTimezoneOffsetInMilliseconds(laterDate_);
  var dstNormalizedMinutes = (milliseconds - timezoneOffset) / millisecondsInMinute;
  var defaultUnit = options === null || options === void 0 ? void 0 : options.unit;
  var unit;
  if (!defaultUnit) {
    if (minutes < 1) {
      unit = "second";
    } else if (minutes < 60) {
      unit = "minute";
    } else if (minutes < minutesInDay) {
      unit = "hour";
    } else if (dstNormalizedMinutes < minutesInMonth) {
      unit = "day";
    } else if (dstNormalizedMinutes < minutesInYear) {
      unit = "month";
    } else {
      unit = "year";
    }
  } else {
    unit = defaultUnit;
  }
  if (unit === "second") {
    var seconds = roundingMethod(milliseconds / 1000);
    return locale.formatDistance("xSeconds", seconds, localizeOptions);
  } else if (unit === "minute") {
    var roundedMinutes = roundingMethod(minutes);
    return locale.formatDistance("xMinutes", roundedMinutes, localizeOptions);
  } else if (unit === "hour") {
    var hours = roundingMethod(minutes / 60);
    return locale.formatDistance("xHours", hours, localizeOptions);
  } else if (unit === "day") {
    var _days2 = roundingMethod(dstNormalizedMinutes / minutesInDay);
    return locale.formatDistance("xDays", _days2, localizeOptions);
  } else if (unit === "month") {
    var _months = roundingMethod(dstNormalizedMinutes / minutesInMonth);
    return _months === 12 && defaultUnit !== "month" ? locale.formatDistance("xYears", 1, localizeOptions) : locale.formatDistance("xMonths", _months, localizeOptions);
  } else {
    var years = roundingMethod(dstNormalizedMinutes / minutesInYear);
    return locale.formatDistance("xYears", years, localizeOptions);
  }
}

// ../../../../../../tmp/date-fns-jalali/fp/formatDistanceStrict.js
var formatDistanceStrict3 = convertToFP(formatDistanceStrict, 2);
// ../../../../../../tmp/date-fns-jalali/fp/formatDistanceStrictWithOptions.js
var _formatDistanceStrictWithOptions = convertToFP(formatDistanceStrict, 3);
// ../../../../../../tmp/date-fns-jalali/fp/formatDistanceWithOptions.js
var _formatDistanceWithOptions = convertToFP(formatDistance3, 3);
// ../../../../../../tmp/date-fns-jalali/formatDuration.js
function formatDuration(duration, options) {var _ref22, _options$locale10, _options$format, _options$zero, _options$delimiter;
  var defaultOptions10 = getDefaultOptions();
  var locale = (_ref22 = (_options$locale10 = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale10 !== void 0 ? _options$locale10 : defaultOptions10.locale) !== null && _ref22 !== void 0 ? _ref22 : faIR;
  var format4 = (_options$format = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format !== void 0 ? _options$format : defaultFormat;
  var zero = (_options$zero = options === null || options === void 0 ? void 0 : options.zero) !== null && _options$zero !== void 0 ? _options$zero : false;
  var delimiter = (_options$delimiter = options === null || options === void 0 ? void 0 : options.delimiter) !== null && _options$delimiter !== void 0 ? _options$delimiter : " ";
  if (!locale.formatDistance) {
    return "";
  }
  var result = format4.reduce(function (acc, unit) {
    var token = "x".concat(unit.replace(/(^.)/, function (m) {return m.toUpperCase();}));
    var value = duration[unit];
    if (value !== undefined && (zero || duration[unit])) {
      return acc.concat(locale.formatDistance(token, value));
    }
    return acc;
  }, []).join(delimiter);
  return result;
}
var defaultFormat = [
"years",
"months",
"weeks",
"days",
"hours",
"minutes",
"seconds"];


// ../../../../../../tmp/date-fns-jalali/fp/formatDuration.js
var formatDuration3 = convertToFP(formatDuration, 1);
// ../../../../../../tmp/date-fns-jalali/fp/formatDurationWithOptions.js
var _formatDurationWithOptions = convertToFP(formatDuration, 2);
// ../../../../../../tmp/date-fns-jalali/formatISO.js
function formatISO(date, options) {var _options$format2, _options$representati;
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  if (isNaN(+date_)) {
    throw new RangeError("Invalid time value");
  }
  var format4 = (_options$format2 = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format2 !== void 0 ? _options$format2 : "extended";
  var representation = (_options$representati = options === null || options === void 0 ? void 0 : options.representation) !== null && _options$representati !== void 0 ? _options$representati : "complete";
  var result = "";
  var tzOffset = "";
  var dateDelimiter = format4 === "extended" ? "-" : "";
  var timeDelimiter = format4 === "extended" ? ":" : "";
  if (representation !== "time") {
    var day = addLeadingZeros(date_.getDate(), 2);
    var month = addLeadingZeros(date_.getMonth() + 1, 2);
    var year = addLeadingZeros(date_.getFullYear(), 4);
    result = "".concat(year).concat(dateDelimiter).concat(month).concat(dateDelimiter).concat(day);
  }
  if (representation !== "date") {
    var offset = date_.getTimezoneOffset();
    if (offset !== 0) {
      var absoluteOffset = Math.abs(offset);
      var hourOffset = addLeadingZeros(Math.trunc(absoluteOffset / 60), 2);
      var minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);
      var sign = offset < 0 ? "+" : "-";
      tzOffset = "".concat(sign).concat(hourOffset, ":").concat(minuteOffset);
    } else {
      tzOffset = "Z";
    }
    var hour = addLeadingZeros(date_.getHours(), 2);
    var minute = addLeadingZeros(date_.getMinutes(), 2);
    var second = addLeadingZeros(date_.getSeconds(), 2);
    var separator = result === "" ? "" : "T";
    var time = [hour, minute, second].join(timeDelimiter);
    result = "".concat(result).concat(separator).concat(time).concat(tzOffset);
  }
  return result;
}

// ../../../../../../tmp/date-fns-jalali/fp/formatISO.js
var formatISO3 = convertToFP(formatISO, 1);
// ../../../../../../tmp/date-fns-jalali/formatISO9075.js
function formatISO9075(date, options) {var _options$format3, _options$representati2;
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  if (!isValid(date_)) {
    throw new RangeError("Invalid time value");
  }
  var format4 = (_options$format3 = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format3 !== void 0 ? _options$format3 : "extended";
  var representation = (_options$representati2 = options === null || options === void 0 ? void 0 : options.representation) !== null && _options$representati2 !== void 0 ? _options$representati2 : "complete";
  var result = "";
  var dateDelimiter = format4 === "extended" ? "-" : "";
  var timeDelimiter = format4 === "extended" ? ":" : "";
  if (representation !== "time") {
    var day = addLeadingZeros(date_.getDate(), 2);
    var month = addLeadingZeros(date_.getMonth() + 1, 2);
    var year = addLeadingZeros(date_.getFullYear(), 4);
    result = "".concat(year).concat(dateDelimiter).concat(month).concat(dateDelimiter).concat(day);
  }
  if (representation !== "date") {
    var hour = addLeadingZeros(date_.getHours(), 2);
    var minute = addLeadingZeros(date_.getMinutes(), 2);
    var second = addLeadingZeros(date_.getSeconds(), 2);
    var separator = result === "" ? "" : " ";
    result = "".concat(result).concat(separator).concat(hour).concat(timeDelimiter).concat(minute).concat(timeDelimiter).concat(second);
  }
  return result;
}

// ../../../../../../tmp/date-fns-jalali/fp/formatISO9075.js
var formatISO90753 = convertToFP(formatISO9075, 1);
// ../../../../../../tmp/date-fns-jalali/fp/formatISO9075WithOptions.js
var _formatISO9075WithOptions = convertToFP(formatISO9075, 2);
// ../../../../../../tmp/date-fns-jalali/formatISODuration.js
function formatISODuration(duration) {
  var _duration$years2 =






    duration.years,years = _duration$years2 === void 0 ? 0 : _duration$years2,_duration$months2 = duration.months,months = _duration$months2 === void 0 ? 0 : _duration$months2,_duration$days2 = duration.days,days = _duration$days2 === void 0 ? 0 : _duration$days2,_duration$hours2 = duration.hours,hours = _duration$hours2 === void 0 ? 0 : _duration$hours2,_duration$minutes2 = duration.minutes,minutes = _duration$minutes2 === void 0 ? 0 : _duration$minutes2,_duration$seconds2 = duration.seconds,seconds = _duration$seconds2 === void 0 ? 0 : _duration$seconds2;
  return "P".concat(years, "Y").concat(months, "M").concat(days, "DT").concat(hours, "H").concat(minutes, "M").concat(seconds, "S");
}

// ../../../../../../tmp/date-fns-jalali/fp/formatISODuration.js
var formatISODuration3 = convertToFP(formatISODuration, 1);
// ../../../../../../tmp/date-fns-jalali/fp/formatISOWithOptions.js
var _formatISOWithOptions = convertToFP(formatISO, 2);
// ../../../../../../tmp/date-fns-jalali/formatRFC3339.js
function formatRFC3339(date, options) {var _options$fractionDigi;
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  if (!isValid(date_)) {
    throw new RangeError("Invalid time value");
  }
  var fractionDigits = (_options$fractionDigi = options === null || options === void 0 ? void 0 : options.fractionDigits) !== null && _options$fractionDigi !== void 0 ? _options$fractionDigi : 0;
  var day = addLeadingZeros(date_.getDate(), 2);
  var month = addLeadingZeros(date_.getMonth() + 1, 2);
  var year = date_.getFullYear();
  var hour = addLeadingZeros(date_.getHours(), 2);
  var minute = addLeadingZeros(date_.getMinutes(), 2);
  var second = addLeadingZeros(date_.getSeconds(), 2);
  var fractionalSecond = "";
  if (fractionDigits > 0) {
    var _milliseconds = date_.getMilliseconds();
    var fractionalSeconds = Math.trunc(_milliseconds * Math.pow(10, fractionDigits - 3));
    fractionalSecond = "." + addLeadingZeros(fractionalSeconds, fractionDigits);
  }
  var offset = "";
  var tzOffset = date_.getTimezoneOffset();
  if (tzOffset !== 0) {
    var absoluteOffset = Math.abs(tzOffset);
    var hourOffset = addLeadingZeros(Math.trunc(absoluteOffset / 60), 2);
    var minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);
    var sign = tzOffset < 0 ? "+" : "-";
    offset = "".concat(sign).concat(hourOffset, ":").concat(minuteOffset);
  } else {
    offset = "Z";
  }
  return "".concat(year, "-").concat(month, "-").concat(day, "T").concat(hour, ":").concat(minute, ":").concat(second).concat(fractionalSecond).concat(offset);
}

// ../../../../../../tmp/date-fns-jalali/fp/formatRFC3339.js
var formatRFC33393 = convertToFP(formatRFC3339, 1);
// ../../../../../../tmp/date-fns-jalali/fp/formatRFC3339WithOptions.js
var _formatRFC3339WithOptions = convertToFP(formatRFC3339, 2);
// ../../../../../../tmp/date-fns-jalali/formatRFC7231.js
function formatRFC7231(date) {
  var _date = toDate(date);
  if (!isValid(_date)) {
    throw new RangeError("Invalid time value");
  }
  var dayName = days[_date.getUTCDay()];
  var dayOfMonth = addLeadingZeros(_date.getUTCDate(), 2);
  var monthName = months[_date.getUTCMonth()];
  var year = _date.getUTCFullYear();
  var hour = addLeadingZeros(_date.getUTCHours(), 2);
  var minute = addLeadingZeros(_date.getUTCMinutes(), 2);
  var second = addLeadingZeros(_date.getUTCSeconds(), 2);
  return "".concat(dayName, ", ").concat(dayOfMonth, " ").concat(monthName, " ").concat(year, " ").concat(hour, ":").concat(minute, ":").concat(second, " GMT");
}
var days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
var months = [
"Jan",
"Feb",
"Mar",
"Apr",
"May",
"Jun",
"Jul",
"Aug",
"Sep",
"Oct",
"Nov",
"Dec"];


// ../../../../../../tmp/date-fns-jalali/fp/formatRFC7231.js
var formatRFC72313 = convertToFP(formatRFC7231, 1);
// ../../../../../../tmp/date-fns-jalali/formatRelative.js
function formatRelative3(date, baseDate, options) {var _ref23, _options$locale11, _ref24, _ref25, _ref26, _options$weekStartsOn4, _options$locale12, _defaultOptions11$loc;
  var _normalizeDates39 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, date, baseDate),_normalizeDates40 = _slicedToArray(_normalizeDates39, 2),date_ = _normalizeDates40[0],baseDate_ = _normalizeDates40[1];
  var defaultOptions11 = getDefaultOptions();
  var locale = (_ref23 = (_options$locale11 = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale11 !== void 0 ? _options$locale11 : defaultOptions11.locale) !== null && _ref23 !== void 0 ? _ref23 : faIR;
  var weekStartsOn = (_ref24 = (_ref25 = (_ref26 = (_options$weekStartsOn4 = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn4 !== void 0 ? _options$weekStartsOn4 : options === null || options === void 0 || (_options$locale12 = options.locale) === null || _options$locale12 === void 0 || (_options$locale12 = _options$locale12.options) === null || _options$locale12 === void 0 ? void 0 : _options$locale12.weekStartsOn) !== null && _ref26 !== void 0 ? _ref26 : defaultOptions11.weekStartsOn) !== null && _ref25 !== void 0 ? _ref25 : (_defaultOptions11$loc = defaultOptions11.locale) === null || _defaultOptions11$loc === void 0 || (_defaultOptions11$loc = _defaultOptions11$loc.options) === null || _defaultOptions11$loc === void 0 ? void 0 : _defaultOptions11$loc.weekStartsOn) !== null && _ref24 !== void 0 ? _ref24 : 0;
  var diff = differenceInCalendarDays(date_, baseDate_);
  if (isNaN(diff)) {
    throw new RangeError("Invalid time value");
  }
  var token;
  if (diff < -6) {
    token = "other";
  } else if (diff < -1) {
    token = "lastWeek";
  } else if (diff < 0) {
    token = "yesterday";
  } else if (diff < 1) {
    token = "today";
  } else if (diff < 2) {
    token = "tomorrow";
  } else if (diff < 7) {
    token = "nextWeek";
  } else {
    token = "other";
  }
  var formatStr = locale.formatRelative(token, date_, baseDate_, {
    locale: locale,
    weekStartsOn: weekStartsOn
  });
  return format(date_, formatStr, { locale: locale, weekStartsOn: weekStartsOn });
}

// ../../../../../../tmp/date-fns-jalali/fp/formatRelative.js
var formatRelative5 = convertToFP(formatRelative3, 2);
// ../../../../../../tmp/date-fns-jalali/fp/formatRelativeWithOptions.js
var _formatRelativeWithOptions = convertToFP(formatRelative3, 3);
// ../../../../../../tmp/date-fns-jalali/fp/formatWithOptions.js
var _formatWithOptions = convertToFP(format, 3);
// ../../../../../../tmp/date-fns-jalali/fromUnixTime.js
function fromUnixTime(unixTime, options) {
  return toDate(unixTime * 1000, options === null || options === void 0 ? void 0 : options.in);
}

// ../../../../../../tmp/date-fns-jalali/fp/fromUnixTime.js
var fromUnixTime3 = convertToFP(fromUnixTime, 1);
// ../../../../../../tmp/date-fns-jalali/fp/fromUnixTimeWithOptions.js
var _fromUnixTimeWithOptions = convertToFP(fromUnixTime, 2);
// ../../../../../../tmp/date-fns-jalali/getDate.js
function getDate13(date, options) {
  return getDate(toDate(date, options === null || options === void 0 ? void 0 : options.in));
}

// ../../../../../../tmp/date-fns-jalali/fp/getDate.js
var getDate15 = convertToFP(getDate13, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getDateWithOptions.js
var _getDateWithOptions = convertToFP(getDate13, 2);
// ../../../../../../tmp/date-fns-jalali/getDay.js
function getDay(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay();
}

// ../../../../../../tmp/date-fns-jalali/fp/getDay.js
var getDay3 = convertToFP(getDay, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getDayOfYear.js
var getDayOfYear4 = convertToFP(getDayOfYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getDayOfYearWithOptions.js
var _getDayOfYearWithOptions = convertToFP(getDayOfYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/getDayWithOptions.js
var _getDayWithOptions = convertToFP(getDay, 2);
// ../../../../../../tmp/date-fns-jalali/getDaysInMonth.js
function getDaysInMonth(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(_date);
  var monthIndex = getMonth(_date);
  var lastDayOfMonth = constructFrom(_date, 0);
  setFullYear(lastDayOfMonth, year, monthIndex + 1, 0);
  lastDayOfMonth.setHours(0, 0, 0, 0);
  return getDate(lastDayOfMonth);
}

// ../../../../../../tmp/date-fns-jalali/fp/getDaysInMonth.js
var getDaysInMonth3 = convertToFP(getDaysInMonth, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getDaysInMonthWithOptions.js
var _getDaysInMonthWithOptions = convertToFP(getDaysInMonth, 2);
// ../../../../../../tmp/date-fns-jalali/_core/isLeapYear.js
function isLeapYear(year) {
  return isLeapJalaliYear(year);
}

// ../../../../../../tmp/date-fns-jalali/isLeapYear.js
function isLeapYear3(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(_date);
  return isLeapYear(year);
}

// ../../../../../../tmp/date-fns-jalali/getDaysInYear.js
function getDaysInYear(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  if (Number.isNaN(+_date))
  return NaN;
  return isLeapYear3(_date) ? 366 : 365;
}

// ../../../../../../tmp/date-fns-jalali/fp/getDaysInYear.js
var getDaysInYear3 = convertToFP(getDaysInYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getDaysInYearWithOptions.js
var _getDaysInYearWithOptions = convertToFP(getDaysInYear, 2);
// ../../../../../../tmp/date-fns-jalali/getDecade.js
function getDecade(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(_date);
  var decade = Math.floor(year / 10) * 10;
  return decade;
}

// ../../../../../../tmp/date-fns-jalali/fp/getDecade.js
var getDecade3 = convertToFP(getDecade, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getDecadeWithOptions.js
var _getDecadeWithOptions = convertToFP(getDecade, 2);
// ../../../../../../tmp/date-fns-jalali/getHours.js
function getHours(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getHours();
}

// ../../../../../../tmp/date-fns-jalali/fp/getHours.js
var getHours3 = convertToFP(getHours, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getHoursWithOptions.js
var _getHoursWithOptions = convertToFP(getHours, 2);
// ../../../../../../tmp/date-fns-jalali/getISODay.js
function getISODay(date, options) {
  var day = toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay();
  return day === 0 ? 7 : day;
}

// ../../../../../../tmp/date-fns-jalali/fp/getISODay.js
var getISODay3 = convertToFP(getISODay, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getISODayWithOptions.js
var _getISODayWithOptions = convertToFP(getISODay, 2);
// ../../../../../../tmp/date-fns-jalali/fp/getISOWeek.js
var getISOWeek4 = convertToFP(getISOWeek, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getISOWeekWithOptions.js
var _getISOWeekWithOptions = convertToFP(getISOWeek, 2);
// ../../../../../../tmp/date-fns-jalali/fp/getISOWeekYear.js
var getISOWeekYear8 = convertToFP(getISOWeekYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getISOWeekYearWithOptions.js
var _getISOWeekYearWithOptions = convertToFP(getISOWeekYear, 2);
// ../../../../../../tmp/date-fns-jalali/getISOWeeksInYear.js
function getISOWeeksInYear(date, options) {
  var thisYear = startOfISOWeekYear(date, options);
  var nextYear = startOfISOWeekYear(addWeeks(thisYear, 60));
  var diff = +nextYear - +thisYear;
  return Math.round(diff / millisecondsInWeek);
}

// ../../../../../../tmp/date-fns-jalali/fp/getISOWeeksInYear.js
var getISOWeeksInYear3 = convertToFP(getISOWeeksInYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getISOWeeksInYearWithOptions.js
var _getISOWeeksInYearWithOptions = convertToFP(getISOWeeksInYear, 2);
// ../../../../../../tmp/date-fns-jalali/getMilliseconds.js
function getMilliseconds(date) {
  return toDate(date).getMilliseconds();
}

// ../../../../../../tmp/date-fns-jalali/fp/getMilliseconds.js
var getMilliseconds3 = convertToFP(getMilliseconds, 1);
// ../../../../../../tmp/date-fns-jalali/getMinutes.js
function getMinutes(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getMinutes();
}

// ../../../../../../tmp/date-fns-jalali/fp/getMinutes.js
var getMinutes3 = convertToFP(getMinutes, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getMinutesWithOptions.js
var _getMinutesWithOptions = convertToFP(getMinutes, 2);
// ../../../../../../tmp/date-fns-jalali/getMonth.js
function getMonth15(date, options) {
  return getMonth(toDate(date, options === null || options === void 0 ? void 0 : options.in));
}

// ../../../../../../tmp/date-fns-jalali/fp/getMonth.js
var getMonth17 = convertToFP(getMonth15, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getMonthWithOptions.js
var _getMonthWithOptions = convertToFP(getMonth15, 2);
// ../../../../../../tmp/date-fns-jalali/getOverlappingDaysInIntervals.js
function getOverlappingDaysInIntervals(intervalLeft, intervalRight) {
  var _sort5 = [
    +toDate(intervalLeft.start),
    +toDate(intervalLeft.end)].
    sort(function (a, b) {return a - b;}),_sort6 = _slicedToArray(_sort5, 2),leftStart = _sort6[0],leftEnd = _sort6[1];
  var _sort7 = [
    +toDate(intervalRight.start),
    +toDate(intervalRight.end)].
    sort(function (a, b) {return a - b;}),_sort8 = _slicedToArray(_sort7, 2),rightStart = _sort8[0],rightEnd = _sort8[1];
  var isOverlapping = leftStart < rightEnd && rightStart < leftEnd;
  if (!isOverlapping)
  return 0;
  var overlapLeft = rightStart < leftStart ? leftStart : rightStart;
  var left = overlapLeft - getTimezoneOffsetInMilliseconds(overlapLeft);
  var overlapRight = rightEnd > leftEnd ? leftEnd : rightEnd;
  var right = overlapRight - getTimezoneOffsetInMilliseconds(overlapRight);
  return Math.ceil((right - left) / millisecondsInDay);
}

// ../../../../../../tmp/date-fns-jalali/fp/getOverlappingDaysInIntervals.js
var getOverlappingDaysInIntervals3 = convertToFP(getOverlappingDaysInIntervals, 2);
// ../../../../../../tmp/date-fns-jalali/fp/getQuarter.js
var getQuarter4 = convertToFP(getQuarter, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getQuarterWithOptions.js
var _getQuarterWithOptions = convertToFP(getQuarter, 2);
// ../../../../../../tmp/date-fns-jalali/getSeconds.js
function getSeconds(date) {
  return toDate(date).getSeconds();
}

// ../../../../../../tmp/date-fns-jalali/fp/getSeconds.js
var getSeconds3 = convertToFP(getSeconds, 1);
// ../../../../../../tmp/date-fns-jalali/getTime.js
function getTime(date) {
  return +toDate(date);
}

// ../../../../../../tmp/date-fns-jalali/fp/getTime.js
var getTime3 = convertToFP(getTime, 1);
// ../../../../../../tmp/date-fns-jalali/getUnixTime.js
function getUnixTime(date) {
  return Math.trunc(+toDate(date) / 1000);
}

// ../../../../../../tmp/date-fns-jalali/fp/getUnixTime.js
var getUnixTime3 = convertToFP(getUnixTime, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getWeek.js
var getWeek4 = convertToFP(getWeek, 1);
// ../../../../../../tmp/date-fns-jalali/getWeekOfMonth.js
function getWeekOfMonth(date, options) {var _ref27, _ref28, _ref29, _options$weekStartsOn5, _options$locale13, _defaultOptions12$loc;
  var defaultOptions12 = getDefaultOptions();
  var weekStartsOn = (_ref27 = (_ref28 = (_ref29 = (_options$weekStartsOn5 = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn5 !== void 0 ? _options$weekStartsOn5 : options === null || options === void 0 || (_options$locale13 = options.locale) === null || _options$locale13 === void 0 || (_options$locale13 = _options$locale13.options) === null || _options$locale13 === void 0 ? void 0 : _options$locale13.weekStartsOn) !== null && _ref29 !== void 0 ? _ref29 : defaultOptions12.weekStartsOn) !== null && _ref28 !== void 0 ? _ref28 : (_defaultOptions12$loc = defaultOptions12.locale) === null || _defaultOptions12$loc === void 0 || (_defaultOptions12$loc = _defaultOptions12$loc.options) === null || _defaultOptions12$loc === void 0 ? void 0 : _defaultOptions12$loc.weekStartsOn) !== null && _ref27 !== void 0 ? _ref27 : 6;
  var currentDayOfMonth = getDate13(toDate(date, options === null || options === void 0 ? void 0 : options.in));
  if (isNaN(currentDayOfMonth))
  return NaN;
  var startWeekDay = getDay(startOfMonth(date, options));
  var lastDayOfFirstWeek = weekStartsOn - startWeekDay;
  if (lastDayOfFirstWeek <= 0)
  lastDayOfFirstWeek += 7;
  var remainingDaysAfterFirstWeek = currentDayOfMonth - lastDayOfFirstWeek;
  return Math.ceil(remainingDaysAfterFirstWeek / 7) + 1;
}

// ../../../../../../tmp/date-fns-jalali/fp/getWeekOfMonth.js
var getWeekOfMonth3 = convertToFP(getWeekOfMonth, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getWeekOfMonthWithOptions.js
var _getWeekOfMonthWithOptions = convertToFP(getWeekOfMonth, 2);
// ../../../../../../tmp/date-fns-jalali/fp/getWeekWithOptions.js
var _getWeekWithOptions = convertToFP(getWeek, 2);
// ../../../../../../tmp/date-fns-jalali/fp/getWeekYear.js
var getWeekYear5 = convertToFP(getWeekYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getWeekYearWithOptions.js
var _getWeekYearWithOptions = convertToFP(getWeekYear, 2);
// ../../../../../../tmp/date-fns-jalali/lastDayOfMonth.js
function lastDayOfMonth(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var month = getMonth(_date);
  setFullYear(_date, getFullYear(_date), month + 1, 0);
  _date.setHours(0, 0, 0, 0);
  return toDate(_date, options === null || options === void 0 ? void 0 : options.in);
}

// ../../../../../../tmp/date-fns-jalali/getWeeksInMonth.js
function getWeeksInMonth(date, options) {
  var contextDate = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  return differenceInCalendarWeeks(lastDayOfMonth(contextDate, options), startOfMonth(contextDate, options), options) + 1;
}

// ../../../../../../tmp/date-fns-jalali/fp/getWeeksInMonth.js
var getWeeksInMonth3 = convertToFP(getWeeksInMonth, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getWeeksInMonthWithOptions.js
var _getWeeksInMonthWithOptions = convertToFP(getWeeksInMonth, 2);
// ../../../../../../tmp/date-fns-jalali/getYear.js
function getYear(date, options) {
  return getFullYear(toDate(date, options === null || options === void 0 ? void 0 : options.in));
}

// ../../../../../../tmp/date-fns-jalali/fp/getYear.js
var getYear3 = convertToFP(getYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/getYearWithOptions.js
var _getYearWithOptions = convertToFP(getYear, 2);
// ../../../../../../tmp/date-fns-jalali/hoursToMilliseconds.js
function hoursToMilliseconds(hours) {
  return Math.trunc(hours * millisecondsInHour);
}

// ../../../../../../tmp/date-fns-jalali/fp/hoursToMilliseconds.js
var hoursToMilliseconds3 = convertToFP(hoursToMilliseconds, 1);
// ../../../../../../tmp/date-fns-jalali/hoursToMinutes.js
function hoursToMinutes(hours) {
  return Math.trunc(hours * minutesInHour);
}

// ../../../../../../tmp/date-fns-jalali/fp/hoursToMinutes.js
var hoursToMinutes3 = convertToFP(hoursToMinutes, 1);
// ../../../../../../tmp/date-fns-jalali/hoursToSeconds.js
function hoursToSeconds(hours) {
  return Math.trunc(hours * secondsInHour);
}

// ../../../../../../tmp/date-fns-jalali/fp/hoursToSeconds.js
var hoursToSeconds3 = convertToFP(hoursToSeconds, 1);
// ../../../../../../tmp/date-fns-jalali/interval.js
function interval(start, end, options) {
  var _normalizeDates41 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, start, end),_normalizeDates42 = _slicedToArray(_normalizeDates41, 2),_start = _normalizeDates42[0],_end = _normalizeDates42[1];
  if (isNaN(+_start))
  throw new TypeError("Start date is invalid");
  if (isNaN(+_end))
  throw new TypeError("End date is invalid");
  if (options !== null && options !== void 0 && options.assertPositive && +_start > +_end)
  throw new TypeError("End date must be after start date");
  return { start: _start, end: _end };
}

// ../../../../../../tmp/date-fns-jalali/fp/interval.js
var interval3 = convertToFP(interval, 2);
// ../../../../../../tmp/date-fns-jalali/intervalToDuration.js
function intervalToDuration(interval4, options) {
  var _normalizeInterval9 = normalizeInterval(options === null || options === void 0 ? void 0 : options.in, interval4),start = _normalizeInterval9.start,end = _normalizeInterval9.end;
  var duration = {};
  var years = differenceInYears(end, start);
  if (years)
  duration.years = years;
  var remainingMonths = add(start, { years: duration.years });
  var months2 = differenceInMonths(end, remainingMonths);
  if (months2)
  duration.months = months2;
  var remainingDays = add(remainingMonths, { months: duration.months });
  var days2 = differenceInDays(end, remainingDays);
  if (days2)
  duration.days = days2;
  var remainingHours = add(remainingDays, { days: duration.days });
  var hours = differenceInHours(end, remainingHours);
  if (hours)
  duration.hours = hours;
  var remainingMinutes = add(remainingHours, { hours: duration.hours });
  var minutes = differenceInMinutes(end, remainingMinutes);
  if (minutes)
  duration.minutes = minutes;
  var remainingSeconds = add(remainingMinutes, { minutes: duration.minutes });
  var seconds = differenceInSeconds(end, remainingSeconds);
  if (seconds)
  duration.seconds = seconds;
  return duration;
}

// ../../../../../../tmp/date-fns-jalali/fp/intervalToDuration.js
var intervalToDuration3 = convertToFP(intervalToDuration, 1);
// ../../../../../../tmp/date-fns-jalali/fp/intervalToDurationWithOptions.js
var _intervalToDurationWithOptions = convertToFP(intervalToDuration, 2);
// ../../../../../../tmp/date-fns-jalali/fp/intervalWithOptions.js
var _intervalWithOptions = convertToFP(interval, 3);
// ../../../../../../tmp/date-fns-jalali/intlFormat.js
function intlFormat(date, formatOrLocale, localeOptions) {var _localeOptions;
  var formatOptions;
  if (isFormatOptions(formatOrLocale)) {
    formatOptions = formatOrLocale;
  } else {
    localeOptions = formatOrLocale;
  }
  return new Intl.DateTimeFormat((_localeOptions = localeOptions) === null || _localeOptions === void 0 ? void 0 : _localeOptions.locale, formatOptions).format(toDate(date));
}
function isFormatOptions(opts) {
  return opts !== undefined && !("locale" in opts);
}

// ../../../../../../tmp/date-fns-jalali/fp/intlFormat.js
var intlFormat3 = convertToFP(intlFormat, 3);
// ../../../../../../tmp/date-fns-jalali/intlFormatDistance.js
function intlFormatDistance(laterDate, earlierDate, options) {
  var value = 0;
  var unit;
  var _normalizeDates43 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates44 = _slicedToArray(_normalizeDates43, 2),laterDate_ = _normalizeDates44[0],earlierDate_ = _normalizeDates44[1];
  if (!(options !== null && options !== void 0 && options.unit)) {
    var diffInSeconds = differenceInSeconds(laterDate_, earlierDate_);
    if (Math.abs(diffInSeconds) < secondsInMinute) {
      value = differenceInSeconds(laterDate_, earlierDate_);
      unit = "second";
    } else if (Math.abs(diffInSeconds) < secondsInHour) {
      value = differenceInMinutes(laterDate_, earlierDate_);
      unit = "minute";
    } else if (Math.abs(diffInSeconds) < secondsInDay && Math.abs(differenceInCalendarDays(laterDate_, earlierDate_)) < 1) {
      value = differenceInHours(laterDate_, earlierDate_);
      unit = "hour";
    } else if (Math.abs(diffInSeconds) < secondsInWeek && (value = differenceInCalendarDays(laterDate_, earlierDate_)) && Math.abs(value) < 7) {
      unit = "day";
    } else if (Math.abs(diffInSeconds) < secondsInMonth) {
      value = differenceInCalendarWeeks(laterDate_, earlierDate_);
      unit = "week";
    } else if (Math.abs(diffInSeconds) < secondsInQuarter) {
      value = differenceInCalendarMonths(laterDate_, earlierDate_);
      unit = "month";
    } else if (Math.abs(diffInSeconds) < secondsInYear) {
      if (differenceInCalendarQuarters(laterDate_, earlierDate_) < 4) {
        value = differenceInCalendarQuarters(laterDate_, earlierDate_);
        unit = "quarter";
      } else {
        value = differenceInCalendarYears(laterDate_, earlierDate_);
        unit = "year";
      }
    } else {
      value = differenceInCalendarYears(laterDate_, earlierDate_);
      unit = "year";
    }
  } else {
    unit = options === null || options === void 0 ? void 0 : options.unit;
    if (unit === "second") {
      value = differenceInSeconds(laterDate_, earlierDate_);
    } else if (unit === "minute") {
      value = differenceInMinutes(laterDate_, earlierDate_);
    } else if (unit === "hour") {
      value = differenceInHours(laterDate_, earlierDate_);
    } else if (unit === "day") {
      value = differenceInCalendarDays(laterDate_, earlierDate_);
    } else if (unit === "week") {
      value = differenceInCalendarWeeks(laterDate_, earlierDate_);
    } else if (unit === "month") {
      value = differenceInCalendarMonths(laterDate_, earlierDate_);
    } else if (unit === "quarter") {
      value = differenceInCalendarQuarters(laterDate_, earlierDate_);
    } else if (unit === "year") {
      value = differenceInCalendarYears(laterDate_, earlierDate_);
    }
  }
  var rtf = new Intl.RelativeTimeFormat(options === null || options === void 0 ? void 0 : options.locale, _objectSpread({
    numeric: "auto" },
  options)
  );
  return rtf.format(value, unit);
}

// ../../../../../../tmp/date-fns-jalali/fp/intlFormatDistance.js
var intlFormatDistance3 = convertToFP(intlFormatDistance, 2);
// ../../../../../../tmp/date-fns-jalali/fp/intlFormatDistanceWithOptions.js
var _intlFormatDistanceWithOptions = convertToFP(intlFormatDistance, 3);
// ../../../../../../tmp/date-fns-jalali/isAfter.js
function isAfter(date, dateToCompare) {
  return +toDate(date) > +toDate(dateToCompare);
}

// ../../../../../../tmp/date-fns-jalali/fp/isAfter.js
var isAfter3 = convertToFP(isAfter, 2);
// ../../../../../../tmp/date-fns-jalali/isBefore.js
function isBefore(date, dateToCompare) {
  return +toDate(date) < +toDate(dateToCompare);
}

// ../../../../../../tmp/date-fns-jalali/fp/isBefore.js
var isBefore3 = convertToFP(isBefore, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isDate.js
var isDate4 = convertToFP(isDate, 1);
// ../../../../../../tmp/date-fns-jalali/isEqual.js
function isEqual(leftDate, rightDate) {
  return +toDate(leftDate) === +toDate(rightDate);
}

// ../../../../../../tmp/date-fns-jalali/fp/isEqual.js
var isEqual3 = convertToFP(isEqual, 2);
// ../../../../../../tmp/date-fns-jalali/isExists.js
function isExists(year, month, day) {
  var date = newDate(year, month, day);
  return getFullYear(date) === year && getMonth(date) === month && getDate(date) === day;
}

// ../../../../../../tmp/date-fns-jalali/fp/isExists.js
var isExists3 = convertToFP(isExists, 3);
// ../../../../../../tmp/date-fns-jalali/isFirstDayOfMonth.js
function isFirstDayOfMonth(date, options) {
  return getDate(toDate(date, options === null || options === void 0 ? void 0 : options.in)) === 1;
}

// ../../../../../../tmp/date-fns-jalali/fp/isFirstDayOfMonth.js
var isFirstDayOfMonth3 = convertToFP(isFirstDayOfMonth, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isFirstDayOfMonthWithOptions.js
var _isFirstDayOfMonthWithOptions = convertToFP(isFirstDayOfMonth, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isFriday.js
var isFriday4 = convertToFP(isFriday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isFridayWithOptions.js
var _isFridayWithOptions = convertToFP(isFriday, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isLastDayOfMonth.js
var isLastDayOfMonth4 = convertToFP(isLastDayOfMonth, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isLastDayOfMonthWithOptions.js
var _isLastDayOfMonthWithOptions = convertToFP(isLastDayOfMonth, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isLeapYear.js
var isLeapYear6 = convertToFP(isLeapYear3, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isLeapYearWithOptions.js
var _isLeapYearWithOptions = convertToFP(isLeapYear3, 2);
// ../../../../../../tmp/date-fns-jalali/getDefaultOptions.js
function getDefaultOptions2() {
  return Object.assign({}, getDefaultOptions());
}

// ../../../../../../tmp/date-fns-jalali/transpose.js
function transpose(date, constructor) {
  var date_ = isConstructor(constructor) ? new constructor(0) : constructFrom(constructor, 0);
  setFullYear(date_, getFullYear(date), getMonth(date), getDate(date));
  date_.setHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());
  return date_;
}
function isConstructor(constructor) {var _constructor$prototyp;
  return typeof constructor === "function" && ((_constructor$prototyp = constructor.prototype) === null || _constructor$prototyp === void 0 ? void 0 : _constructor$prototyp.constructor) === constructor;
}

// ../../../../../../tmp/date-fns-jalali/parse/_lib/Setter.js
var TIMEZONE_UNIT_PRIORITY = 10;var

Setter = /*#__PURE__*/function () {function Setter() {_classCallCheck(this, Setter);_defineProperty(this, "subPriority",
    0);}_createClass(Setter, [{ key: "validate", value:
    function validate(_utcDate, _options) {
      return true;
    } }]);return Setter;}();var


ValueSetter = /*#__PURE__*/function (_Setter2) {_inherits(ValueSetter, _Setter2);
  function ValueSetter(value, validateValue, setValue, priority, subPriority) {var _this;_classCallCheck(this, ValueSetter);
    _this = _callSuper(this, ValueSetter);
    _this.value = value;
    _this.validateValue = validateValue;
    _this.setValue = setValue;
    _this.priority = priority;
    if (subPriority) {
      _this.subPriority = subPriority;
    }return _this;
  }_createClass(ValueSetter, [{ key: "validate", value:
    function validate(date, options) {
      return this.validateValue(date, this.value, options);
    } }, { key: "set", value:
    function set(date, flags, options) {
      return this.setValue(date, flags, this.value, options);
    } }]);return ValueSetter;}(Setter);var


DateTimezoneSetter = /*#__PURE__*/function (_Setter3) {_inherits(DateTimezoneSetter, _Setter3);


  function DateTimezoneSetter(context, reference) {var _this2;_classCallCheck(this, DateTimezoneSetter);
    _this2 = _callSuper(this, DateTimezoneSetter);_defineProperty(_assertThisInitialized(_this2), "priority", TIMEZONE_UNIT_PRIORITY);_defineProperty(_assertThisInitialized(_this2), "subPriority", -1);
    _this2.context = context || function (date) {return constructFrom(reference, date);};return _this2;
  }_createClass(DateTimezoneSetter, [{ key: "set", value:
    function set(date, flags) {
      if (flags.timestampIsSet)
      return date;
      return constructFrom(date, transpose(date, this.context));
    } }]);return DateTimezoneSetter;}(Setter);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/Parser.js
var Parser = /*#__PURE__*/function () {function Parser() {_classCallCheck(this, Parser);}_createClass(Parser, [{ key: "run", value:
    function run(dateString, token, match3, options) {
      var result = this.parse(dateString, token, match3, options);
      if (!result) {
        return null;
      }
      return {
        setter: new ValueSetter(result.value, this.validate, this.set, this.priority, this.subPriority),
        rest: result.rest
      };
    } }, { key: "validate", value:
    function validate(_utcDate, _value, _options) {
      return true;
    } }]);return Parser;}();


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/EraParser.js
var EraParser = /*#__PURE__*/function (_Parser) {_inherits(EraParser, _Parser);function EraParser() {var _this3;_classCallCheck(this, EraParser);for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {args[_key7] = arguments[_key7];}_this3 = _callSuper(this, EraParser, [].concat(args));_defineProperty(_assertThisInitialized(_this3), "priority",
    140);_defineProperty(_assertThisInitialized(_this3), "incompatibleTokens",



















    ["R", "u", "t", "T"]);return _this3;}_createClass(EraParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "G":case "GG":case "GGG":return match3.era(dateString, { width: "abbreviated" }) || match3.era(dateString, { width: "narrow" });case "GGGGG":return match3.era(dateString, { width: "narrow" });case "GGGG":default:return match3.era(dateString, { width: "wide" }) || match3.era(dateString, { width: "abbreviated" }) || match3.era(dateString, { width: "narrow" });}} }, { key: "set", value: function set(date, flags, value) {flags.era = value;setFullYear(date, value, 0, 1);date.setHours(0, 0, 0, 0);return date;} }]);return EraParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/constants.js
var numericPatterns = {
  month: /^(1[0-2]|0?\d)/,
  date: /^(3[0-1]|[0-2]?\d)/,
  dayOfYear: /^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,
  week: /^(5[0-3]|[0-4]?\d)/,
  hour23h: /^(2[0-3]|[0-1]?\d)/,
  hour24h: /^(2[0-4]|[0-1]?\d)/,
  hour11h: /^(1[0-1]|0?\d)/,
  hour12h: /^(1[0-2]|0?\d)/,
  minute: /^[0-5]?\d/,
  second: /^[0-5]?\d/,
  singleDigit: /^\d/,
  twoDigits: /^\d{1,2}/,
  threeDigits: /^\d{1,3}/,
  fourDigits: /^\d{1,4}/,
  anyDigitsSigned: /^-?\d+/,
  singleDigitSigned: /^-?\d/,
  twoDigitsSigned: /^-?\d{1,2}/,
  threeDigitsSigned: /^-?\d{1,3}/,
  fourDigitsSigned: /^-?\d{1,4}/
};
var timezonePatterns = {
  basicOptionalMinutes: /^([+-])(\d{2})(\d{2})?|Z/,
  basic: /^([+-])(\d{2})(\d{2})|Z/,
  basicOptionalSeconds: /^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,
  extended: /^([+-])(\d{2}):(\d{2})|Z/,
  extendedOptionalSeconds: /^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/
};

// ../../../../../../tmp/date-fns-jalali/parse/_lib/utils.js
function mapValue(parseFnResult, mapFn) {
  if (!parseFnResult) {
    return parseFnResult;
  }
  return {
    value: mapFn(parseFnResult.value),
    rest: parseFnResult.rest
  };
}
function parseNumericPattern(pattern, dateString) {
  var matchResult = dateString.match(pattern);
  if (!matchResult) {
    return null;
  }
  return {
    value: parseInt(matchResult[0], 10),
    rest: dateString.slice(matchResult[0].length)
  };
}
function parseTimezonePattern(pattern, dateString) {
  var matchResult = dateString.match(pattern);
  if (!matchResult) {
    return null;
  }
  if (matchResult[0] === "Z") {
    return {
      value: 0,
      rest: dateString.slice(1)
    };
  }
  var sign = matchResult[1] === "+" ? 1 : -1;
  var hours = matchResult[2] ? parseInt(matchResult[2], 10) : 0;
  var minutes = matchResult[3] ? parseInt(matchResult[3], 10) : 0;
  var seconds = matchResult[5] ? parseInt(matchResult[5], 10) : 0;
  return {
    value: sign * (hours * millisecondsInHour + minutes * millisecondsInMinute + seconds * millisecondsInSecond),
    rest: dateString.slice(matchResult[0].length)
  };
}
function parseAnyDigitsSigned(dateString) {
  return parseNumericPattern(numericPatterns.anyDigitsSigned, dateString);
}
function parseNDigits(n, dateString) {
  switch (n) {
    case 1:
      return parseNumericPattern(numericPatterns.singleDigit, dateString);
    case 2:
      return parseNumericPattern(numericPatterns.twoDigits, dateString);
    case 3:
      return parseNumericPattern(numericPatterns.threeDigits, dateString);
    case 4:
      return parseNumericPattern(numericPatterns.fourDigits, dateString);
    default:
      return parseNumericPattern(new RegExp("^\\d{1," + n + "}"), dateString);
  }
}
function parseNDigitsSigned(n, dateString) {
  switch (n) {
    case 1:
      return parseNumericPattern(numericPatterns.singleDigitSigned, dateString);
    case 2:
      return parseNumericPattern(numericPatterns.twoDigitsSigned, dateString);
    case 3:
      return parseNumericPattern(numericPatterns.threeDigitsSigned, dateString);
    case 4:
      return parseNumericPattern(numericPatterns.fourDigitsSigned, dateString);
    default:
      return parseNumericPattern(new RegExp("^-?\\d{1," + n + "}"), dateString);
  }
}
function dayPeriodEnumToHours(dayPeriod) {
  switch (dayPeriod) {
    case "morning":
      return 4;
    case "evening":
      return 17;
    case "pm":
    case "noon":
    case "afternoon":
      return 12;
    case "am":
    case "midnight":
    case "night":
    default:
      return 0;
  }
}
function normalizeTwoDigitYear(twoDigitYear, currentYear) {
  var isCommonEra = currentYear > 0;
  var absCurrentYear = isCommonEra ? currentYear : 1 - currentYear;
  var result;
  if (absCurrentYear <= 50) {
    result = twoDigitYear || 100;
  } else {
    var rangeEnd = absCurrentYear + 50;
    var rangeEndCentury = Math.trunc(rangeEnd / 100) * 100;
    var isPreviousCentury = twoDigitYear >= rangeEnd % 100;
    result = twoDigitYear + rangeEndCentury - (isPreviousCentury ? 100 : 0);
  }
  return isCommonEra ? result : 1 - result;
}
function isLeapYearIndex(year) {
  return isLeapYear3(newDate(year, 0));
}

// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/YearParser.js
var YearParser = /*#__PURE__*/function (_Parser2) {_inherits(YearParser, _Parser2);function YearParser() {var _this4;_classCallCheck(this, YearParser);for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {args[_key8] = arguments[_key8];}_this4 = _callSuper(this, YearParser, [].concat(args));_defineProperty(_assertThisInitialized(_this4), "priority",
    130);_defineProperty(_assertThisInitialized(_this4), "incompatibleTokens",
    ["Y", "R", "u", "w", "I", "i", "e", "c", "t", "T"]);return _this4;}_createClass(YearParser, [{ key: "parse", value:
    function parse(dateString, token, match3) {
      var valueCallback = function valueCallback(year) {return {
          year: year,
          isTwoDigitYear: token === "yy"
        };};
      switch (token) {
        case "y":
          return mapValue(parseNDigits(4, dateString), valueCallback);
        case "yo":
          return mapValue(match3.ordinalNumber(dateString, {
            unit: "year"
          }), valueCallback);
        default:
          return mapValue(parseNDigits(token.length, dateString), valueCallback);
      }
    } }, { key: "validate", value:
    function validate(_date, value) {
      return value.isTwoDigitYear || value.year > 0;
    } }, { key: "set", value:
    function set(date, flags, value) {
      var currentYear = getFullYear(date);
      if (value.isTwoDigitYear) {
        var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);
        setFullYear(date, normalizedTwoDigitYear, 0, 1);
        date.setHours(0, 0, 0, 0);
        return date;
      }
      var year = !("era" in flags) || flags.era === 1 ? value.year : 1 - value.year;
      setFullYear(date, year, 0, 1);
      date.setHours(0, 0, 0, 0);
      return date;
    } }]);return YearParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/LocalWeekYearParser.js
var LocalWeekYearParser = /*#__PURE__*/function (_Parser3) {_inherits(LocalWeekYearParser, _Parser3);function LocalWeekYearParser() {var _this5;_classCallCheck(this, LocalWeekYearParser);for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {args[_key9] = arguments[_key9];}_this5 = _callSuper(this, LocalWeekYearParser, [].concat(args));_defineProperty(_assertThisInitialized(_this5), "priority",
    130);_defineProperty(_assertThisInitialized(_this5), "incompatibleTokens",
































    [
    "y",
    "R",
    "u",
    "Q",
    "q",
    "M",
    "L",
    "I",
    "d",
    "D",
    "i",
    "t",
    "T"]);return _this5;}_createClass(LocalWeekYearParser, [{ key: "parse", value: function parse(dateString, token, match3) {var valueCallback = function valueCallback(year) {return { year: year, isTwoDigitYear: token === "YY" };};switch (token) {case "Y":return mapValue(parseNDigits(4, dateString), valueCallback);case "Yo":return mapValue(match3.ordinalNumber(dateString, { unit: "year" }), valueCallback);default:return mapValue(parseNDigits(token.length, dateString), valueCallback);}} }, { key: "validate", value: function validate(_date, value) {return value.isTwoDigitYear || value.year > 0;} }, { key: "set", value: function set(date, flags, value, options) {var currentYear = getWeekYear(date, options);if (value.isTwoDigitYear) {var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);setFullYear(date, normalizedTwoDigitYear, 0, options.firstWeekContainsDate);date.setHours(0, 0, 0, 0);return startOfWeek(date, options);}var year = !("era" in flags) || flags.era === 1 ? value.year : 1 - value.year;setFullYear(date, year, 0, options.firstWeekContainsDate);date.setHours(0, 0, 0, 0);return startOfWeek(date, options);} }]);return LocalWeekYearParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/ISOWeekYearParser.js
var ISOWeekYearParser = /*#__PURE__*/function (_Parser4) {_inherits(ISOWeekYearParser, _Parser4);function ISOWeekYearParser() {var _this6;_classCallCheck(this, ISOWeekYearParser);for (var _len10 = arguments.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {args[_key10] = arguments[_key10];}_this6 = _callSuper(this, ISOWeekYearParser, [].concat(args));_defineProperty(_assertThisInitialized(_this6), "priority",
    130);_defineProperty(_assertThisInitialized(_this6), "incompatibleTokens",












    [
    "G",
    "y",
    "Y",
    "u",
    "Q",
    "q",
    "M",
    "L",
    "w",
    "d",
    "D",
    "e",
    "c",
    "t",
    "T"]);return _this6;}_createClass(ISOWeekYearParser, [{ key: "parse", value: function parse(dateString, token) {if (token === "R") {return parseNDigitsSigned(4, dateString);}return parseNDigitsSigned(token.length, dateString);} }, { key: "set", value: function set(date, _flags, value) {var firstWeekOfYear = constructFrom(date, 0);setFullYear(firstWeekOfYear, value, 0, 4);firstWeekOfYear.setHours(0, 0, 0, 0);return startOfISOWeek(firstWeekOfYear);} }]);return ISOWeekYearParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/ExtendedYearParser.js
var ExtendedYearParser = /*#__PURE__*/function (_Parser5) {_inherits(ExtendedYearParser, _Parser5);function ExtendedYearParser() {var _this7;_classCallCheck(this, ExtendedYearParser);for (var _len11 = arguments.length, args = new Array(_len11), _key11 = 0; _key11 < _len11; _key11++) {args[_key11] = arguments[_key11];}_this7 = _callSuper(this, ExtendedYearParser, [].concat(args));_defineProperty(_assertThisInitialized(_this7), "priority",
    130);_defineProperty(_assertThisInitialized(_this7), "incompatibleTokens",











    ["G", "y", "Y", "R", "w", "I", "i", "e", "c", "t", "T"]);return _this7;}_createClass(ExtendedYearParser, [{ key: "parse", value: function parse(dateString, token) {if (token === "u") {return parseNDigitsSigned(4, dateString);}return parseNDigitsSigned(token.length, dateString);} }, { key: "set", value: function set(date, _flags, value) {setFullYear(date, value, 0, 1);date.setHours(0, 0, 0, 0);return date;} }]);return ExtendedYearParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/QuarterParser.js
var QuarterParser = /*#__PURE__*/function (_Parser6) {_inherits(QuarterParser, _Parser6);function QuarterParser() {var _this8;_classCallCheck(this, QuarterParser);for (var _len12 = arguments.length, args = new Array(_len12), _key12 = 0; _key12 < _len12; _key12++) {args[_key12] = arguments[_key12];}_this8 = _callSuper(this, QuarterParser, [].concat(args));_defineProperty(_assertThisInitialized(_this8), "priority",
    120);_defineProperty(_assertThisInitialized(_this8), "incompatibleTokens",










































    [
    "Y",
    "R",
    "q",
    "M",
    "L",
    "w",
    "I",
    "d",
    "D",
    "i",
    "e",
    "c",
    "t",
    "T"]);return _this8;}_createClass(QuarterParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "Q":case "QQ":return parseNDigits(token.length, dateString);case "Qo":return match3.ordinalNumber(dateString, { unit: "quarter" });case "QQQ":return match3.quarter(dateString, { width: "abbreviated", context: "formatting" }) || match3.quarter(dateString, { width: "narrow", context: "formatting" });case "QQQQQ":return match3.quarter(dateString, { width: "narrow", context: "formatting" });case "QQQQ":default:return match3.quarter(dateString, { width: "wide", context: "formatting" }) || match3.quarter(dateString, { width: "abbreviated", context: "formatting" }) || match3.quarter(dateString, { width: "narrow", context: "formatting" });}} }, { key: "validate", value: function validate(_date, value) {return value >= 1 && value <= 4;} }, { key: "set", value: function set(date, _flags, value) {setMonth(date, (value - 1) * 3, 1);date.setHours(0, 0, 0, 0);return date;} }]);return QuarterParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/StandAloneQuarterParser.js
var StandAloneQuarterParser = /*#__PURE__*/function (_Parser7) {_inherits(StandAloneQuarterParser, _Parser7);function StandAloneQuarterParser() {var _this9;_classCallCheck(this, StandAloneQuarterParser);for (var _len13 = arguments.length, args = new Array(_len13), _key13 = 0; _key13 < _len13; _key13++) {args[_key13] = arguments[_key13];}_this9 = _callSuper(this, StandAloneQuarterParser, [].concat(args));_defineProperty(_assertThisInitialized(_this9), "priority",
    120);_defineProperty(_assertThisInitialized(_this9), "incompatibleTokens",










































    [
    "Y",
    "R",
    "Q",
    "M",
    "L",
    "w",
    "I",
    "d",
    "D",
    "i",
    "e",
    "c",
    "t",
    "T"]);return _this9;}_createClass(StandAloneQuarterParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "q":case "qq":return parseNDigits(token.length, dateString);case "qo":return match3.ordinalNumber(dateString, { unit: "quarter" });case "qqq":return match3.quarter(dateString, { width: "abbreviated", context: "standalone" }) || match3.quarter(dateString, { width: "narrow", context: "standalone" });case "qqqqq":return match3.quarter(dateString, { width: "narrow", context: "standalone" });case "qqqq":default:return match3.quarter(dateString, { width: "wide", context: "standalone" }) || match3.quarter(dateString, { width: "abbreviated", context: "standalone" }) || match3.quarter(dateString, { width: "narrow", context: "standalone" });}} }, { key: "validate", value: function validate(_date, value) {return value >= 1 && value <= 4;} }, { key: "set", value: function set(date, _flags, value) {setMonth(date, (value - 1) * 3, 1);date.setHours(0, 0, 0, 0);return date;} }]);return StandAloneQuarterParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/MonthParser.js
var MonthParser = /*#__PURE__*/function (_Parser8) {_inherits(MonthParser, _Parser8);function MonthParser() {var _this10;_classCallCheck(this, MonthParser);for (var _len14 = arguments.length, args = new Array(_len14), _key14 = 0; _key14 < _len14; _key14++) {args[_key14] = arguments[_key14];}_this10 = _callSuper(this, MonthParser, [].concat(args));_defineProperty(_assertThisInitialized(_this10), "incompatibleTokens",
    [
    "Y",
    "R",
    "q",
    "Q",
    "L",
    "w",
    "I",
    "D",
    "i",
    "e",
    "c",
    "t",
    "T"]);_defineProperty(_assertThisInitialized(_this10), "priority",

    110);return _this10;}_createClass(MonthParser, [{ key: "parse", value:
    function parse(dateString, token, match3) {
      var valueCallback = function valueCallback(value) {return value - 1;};
      switch (token) {
        case "M":
          return mapValue(parseNumericPattern(numericPatterns.month, dateString), valueCallback);
        case "MM":
          return mapValue(parseNDigits(2, dateString), valueCallback);
        case "Mo":
          return mapValue(match3.ordinalNumber(dateString, {
            unit: "month"
          }), valueCallback);
        case "MMM":
          return match3.month(dateString, {
            width: "abbreviated",
            context: "formatting"
          }) || match3.month(dateString, { width: "narrow", context: "formatting" });
        case "MMMMM":
          return match3.month(dateString, {
            width: "narrow",
            context: "formatting"
          });
        case "MMMM":
        default:
          return match3.month(dateString, { width: "wide", context: "formatting" }) || match3.month(dateString, {
            width: "abbreviated",
            context: "formatting"
          }) || match3.month(dateString, { width: "narrow", context: "formatting" });
      }
    } }, { key: "validate", value:
    function validate(_date, value) {
      return value >= 0 && value <= 11;
    } }, { key: "set", value:
    function set(date, _flags, value) {
      setMonth(date, value, 1);
      date.setHours(0, 0, 0, 0);
      return date;
    } }]);return MonthParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/StandAloneMonthParser.js
var StandAloneMonthParser = /*#__PURE__*/function (_Parser9) {_inherits(StandAloneMonthParser, _Parser9);function StandAloneMonthParser() {var _this11;_classCallCheck(this, StandAloneMonthParser);for (var _len15 = arguments.length, args = new Array(_len15), _key15 = 0; _key15 < _len15; _key15++) {args[_key15] = arguments[_key15];}_this11 = _callSuper(this, StandAloneMonthParser, [].concat(args));_defineProperty(_assertThisInitialized(_this11), "priority",
    110);_defineProperty(_assertThisInitialized(_this11), "incompatibleTokens",





































    [
    "Y",
    "R",
    "q",
    "Q",
    "M",
    "w",
    "I",
    "D",
    "i",
    "e",
    "c",
    "t",
    "T"]);return _this11;}_createClass(StandAloneMonthParser, [{ key: "parse", value: function parse(dateString, token, match3) {var valueCallback = function valueCallback(value) {return value - 1;};switch (token) {case "L":return mapValue(parseNumericPattern(numericPatterns.month, dateString), valueCallback);case "LL":return mapValue(parseNDigits(2, dateString), valueCallback);case "Lo":return mapValue(match3.ordinalNumber(dateString, { unit: "month" }), valueCallback);case "LLL":return match3.month(dateString, { width: "abbreviated", context: "standalone" }) || match3.month(dateString, { width: "narrow", context: "standalone" });case "LLLLL":return match3.month(dateString, { width: "narrow", context: "standalone" });case "LLLL":default:return match3.month(dateString, { width: "wide", context: "standalone" }) || match3.month(dateString, { width: "abbreviated", context: "standalone" }) || match3.month(dateString, { width: "narrow", context: "standalone" });}} }, { key: "validate", value: function validate(_date, value) {return value >= 0 && value <= 11;} }, { key: "set", value: function set(date, _flags, value) {setMonth(date, value, 1);date.setHours(0, 0, 0, 0);return date;} }]);return StandAloneMonthParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/setWeek.js
function setWeek(date, week, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var diff = getWeek(date_, options) - week;
  setDate(date_, getDate(date_) - diff * 7);
  return toDate(date_, options === null || options === void 0 ? void 0 : options.in);
}

// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/LocalWeekParser.js
var LocalWeekParser = /*#__PURE__*/function (_Parser10) {_inherits(LocalWeekParser, _Parser10);function LocalWeekParser() {var _this12;_classCallCheck(this, LocalWeekParser);for (var _len16 = arguments.length, args = new Array(_len16), _key16 = 0; _key16 < _len16; _key16++) {args[_key16] = arguments[_key16];}_this12 = _callSuper(this, LocalWeekParser, [].concat(args));_defineProperty(_assertThisInitialized(_this12), "priority",
    100);_defineProperty(_assertThisInitialized(_this12), "incompatibleTokens",
















    [
    "y",
    "R",
    "u",
    "q",
    "Q",
    "M",
    "L",
    "I",
    "d",
    "D",
    "i",
    "t",
    "T"]);return _this12;}_createClass(LocalWeekParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "w":return parseNumericPattern(numericPatterns.week, dateString);case "wo":return match3.ordinalNumber(dateString, { unit: "week" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(_date, value) {return value >= 1 && value <= 53;} }, { key: "set", value: function set(date, _flags, value, options) {return startOfWeek(setWeek(date, value, options), options);} }]);return LocalWeekParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/setISOWeek.js
function setISOWeek(date, week, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var diff = getISOWeek(_date, options) - week;
  _date.setDate(_date.getDate() - diff * 7);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/ISOWeekParser.js
var ISOWeekParser = /*#__PURE__*/function (_Parser11) {_inherits(ISOWeekParser, _Parser11);function ISOWeekParser() {var _this13;_classCallCheck(this, ISOWeekParser);for (var _len17 = arguments.length, args = new Array(_len17), _key17 = 0; _key17 < _len17; _key17++) {args[_key17] = arguments[_key17];}_this13 = _callSuper(this, ISOWeekParser, [].concat(args));_defineProperty(_assertThisInitialized(_this13), "priority",
    100);_defineProperty(_assertThisInitialized(_this13), "incompatibleTokens",
















    [
    "y",
    "Y",
    "u",
    "q",
    "Q",
    "M",
    "L",
    "w",
    "d",
    "D",
    "e",
    "c",
    "t",
    "T"]);return _this13;}_createClass(ISOWeekParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "I":return parseNumericPattern(numericPatterns.week, dateString);case "Io":return match3.ordinalNumber(dateString, { unit: "week" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(_date, value) {return value >= 1 && value <= 53;} }, { key: "set", value: function set(date, _flags, value) {return startOfISOWeek(setISOWeek(date, value));} }]);return ISOWeekParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/DateParser.js
var DAYS_IN_MONTH = [31, 31, 31, 31, 31, 31, 30, 30, 30, 30, 30, 29];
var DAYS_IN_MONTH_LEAP_YEAR = [
31,
31,
31,
31,
31,
31,
30,
30,
30,
30,
30,
30];var


DateParser = /*#__PURE__*/function (_Parser12) {_inherits(DateParser, _Parser12);function DateParser() {var _this14;_classCallCheck(this, DateParser);for (var _len18 = arguments.length, args = new Array(_len18), _key18 = 0; _key18 < _len18; _key18++) {args[_key18] = arguments[_key18];}_this14 = _callSuper(this, DateParser, [].concat(args));_defineProperty(_assertThisInitialized(_this14), "priority",
    90);_defineProperty(_assertThisInitialized(_this14), "subPriority",
    1);_defineProperty(_assertThisInitialized(_this14), "incompatibleTokens",

























    [
    "Y",
    "R",
    "q",
    "Q",
    "w",
    "I",
    "D",
    "i",
    "e",
    "c",
    "t",
    "T"]);return _this14;}_createClass(DateParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "d":return parseNumericPattern(numericPatterns.date, dateString);case "do":return match3.ordinalNumber(dateString, { unit: "date" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(date, value) {var year = getFullYear(date);var isLeapYear9 = isLeapYearIndex(year);var month = getMonth(date);if (isLeapYear9) {return value >= 1 && value <= DAYS_IN_MONTH_LEAP_YEAR[month];} else {return value >= 1 && value <= DAYS_IN_MONTH[month];}} }, { key: "set", value: function set(date, _flags, value) {setDate(date, value);date.setHours(0, 0, 0, 0);return date;} }]);return DateParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/DayOfYearParser.js
var DayOfYearParser = /*#__PURE__*/function (_Parser13) {_inherits(DayOfYearParser, _Parser13);function DayOfYearParser() {var _this15;_classCallCheck(this, DayOfYearParser);for (var _len19 = arguments.length, args = new Array(_len19), _key19 = 0; _key19 < _len19; _key19++) {args[_key19] = arguments[_key19];}_this15 = _callSuper(this, DayOfYearParser, [].concat(args));_defineProperty(_assertThisInitialized(_this15), "priority",
    90);_defineProperty(_assertThisInitialized(_this15), "subpriority",
    1);_defineProperty(_assertThisInitialized(_this15), "incompatibleTokens",

























    [
    "Y",
    "R",
    "q",
    "Q",
    "M",
    "L",
    "w",
    "I",
    "d",
    "E",
    "i",
    "e",
    "c",
    "t",
    "T"]);return _this15;}_createClass(DayOfYearParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "D":case "DD":return parseNumericPattern(numericPatterns.dayOfYear, dateString);case "Do":return match3.ordinalNumber(dateString, { unit: "date" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(date, value) {var year = getFullYear(date);var isLeapYear9 = isLeapYearIndex(year);if (isLeapYear9) {return value >= 1 && value <= 366;} else {return value >= 1 && value <= 365;}} }, { key: "set", value: function set(date, _flags, value) {setMonth(date, 0, value);date.setHours(0, 0, 0, 0);return date;} }]);return DayOfYearParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/setDay.js
function setDay(date, day, options) {var _ref30, _ref31, _ref32, _options$weekStartsOn6, _options$locale14, _defaultOptions14$loc;
  var defaultOptions14 = getDefaultOptions();
  var weekStartsOn = (_ref30 = (_ref31 = (_ref32 = (_options$weekStartsOn6 = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn6 !== void 0 ? _options$weekStartsOn6 : options === null || options === void 0 || (_options$locale14 = options.locale) === null || _options$locale14 === void 0 || (_options$locale14 = _options$locale14.options) === null || _options$locale14 === void 0 ? void 0 : _options$locale14.weekStartsOn) !== null && _ref32 !== void 0 ? _ref32 : defaultOptions14.weekStartsOn) !== null && _ref31 !== void 0 ? _ref31 : (_defaultOptions14$loc = defaultOptions14.locale) === null || _defaultOptions14$loc === void 0 || (_defaultOptions14$loc = _defaultOptions14$loc.options) === null || _defaultOptions14$loc === void 0 ? void 0 : _defaultOptions14$loc.weekStartsOn) !== null && _ref30 !== void 0 ? _ref30 : 6;
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var currentDay = date_.getDay();
  var remainder = day % 7;
  var dayIndex = (remainder + 7) % 7;
  var delta = 7 - weekStartsOn;
  var diff = day < 0 || day > 6 ? day - (currentDay + delta) % 7 : (dayIndex + delta) % 7 - (currentDay + delta) % 7;
  return addDays(date_, diff, options);
}

// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/DayParser.js
var DayParser = /*#__PURE__*/function (_Parser14) {_inherits(DayParser, _Parser14);function DayParser() {var _this16;_classCallCheck(this, DayParser);for (var _len20 = arguments.length, args = new Array(_len20), _key20 = 0; _key20 < _len20; _key20++) {args[_key20] = arguments[_key20];}_this16 = _callSuper(this, DayParser, [].concat(args));_defineProperty(_assertThisInitialized(_this16), "priority",
    90);_defineProperty(_assertThisInitialized(_this16), "incompatibleTokens",
































    ["D", "i", "e", "c", "t", "T"]);return _this16;}_createClass(DayParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "E":case "EE":case "EEE":return match3.day(dateString, { width: "abbreviated", context: "formatting" }) || match3.day(dateString, { width: "short", context: "formatting" }) || match3.day(dateString, { width: "narrow", context: "formatting" });case "EEEEE":return match3.day(dateString, { width: "narrow", context: "formatting" });case "EEEEEE":return match3.day(dateString, { width: "short", context: "formatting" }) || match3.day(dateString, { width: "narrow", context: "formatting" });case "EEEE":default:return match3.day(dateString, { width: "wide", context: "formatting" }) || match3.day(dateString, { width: "abbreviated", context: "formatting" }) || match3.day(dateString, { width: "short", context: "formatting" }) || match3.day(dateString, { width: "narrow", context: "formatting" });}} }, { key: "validate", value: function validate(_date, value) {return value >= 0 && value <= 6;} }, { key: "set", value: function set(date, _flags, value, options) {date = setDay(date, value, options);date.setHours(0, 0, 0, 0);return date;} }]);return DayParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/LocalDayParser.js
var LocalDayParser = /*#__PURE__*/function (_Parser15) {_inherits(LocalDayParser, _Parser15);function LocalDayParser() {var _this17;_classCallCheck(this, LocalDayParser);for (var _len21 = arguments.length, args = new Array(_len21), _key21 = 0; _key21 < _len21; _key21++) {args[_key21] = arguments[_key21];}_this17 = _callSuper(this, LocalDayParser, [].concat(args));_defineProperty(_assertThisInitialized(_this17), "priority",
    90);_defineProperty(_assertThisInitialized(_this17), "incompatibleTokens",









































    [
    "y",
    "R",
    "u",
    "q",
    "Q",
    "M",
    "L",
    "I",
    "d",
    "D",
    "E",
    "i",
    "c",
    "t",
    "T"]);return _this17;}_createClass(LocalDayParser, [{ key: "parse", value: function parse(dateString, token, match3, options) {var valueCallback = function valueCallback(value) {var wholeWeekDays = Math.floor((value - 1) / 7) * 7;return (value + options.weekStartsOn + 6 + 1) % 7 + wholeWeekDays;};switch (token) {case "e":case "ee":return mapValue(parseNDigits(token.length, dateString), valueCallback);case "eo":return mapValue(match3.ordinalNumber(dateString, { unit: "day" }), valueCallback);case "eee":return match3.day(dateString, { width: "abbreviated", context: "formatting" }) || match3.day(dateString, { width: "short", context: "formatting" }) || match3.day(dateString, { width: "narrow", context: "formatting" });case "eeeee":return match3.day(dateString, { width: "narrow", context: "formatting" });case "eeeeee":return match3.day(dateString, { width: "short", context: "formatting" }) || match3.day(dateString, { width: "narrow", context: "formatting" });case "eeee":default:return match3.day(dateString, { width: "wide", context: "formatting" }) || match3.day(dateString, { width: "abbreviated", context: "formatting" }) || match3.day(dateString, { width: "short", context: "formatting" }) || match3.day(dateString, { width: "narrow", context: "formatting" });}} }, { key: "validate", value: function validate(_date, value) {return value >= 0 && value <= 6;} }, { key: "set", value: function set(date, _flags, value, options) {date = setDay(date, value, options);date.setHours(0, 0, 0, 0);return date;} }]);return LocalDayParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/StandAloneLocalDayParser.js
var StandAloneLocalDayParser = /*#__PURE__*/function (_Parser16) {_inherits(StandAloneLocalDayParser, _Parser16);function StandAloneLocalDayParser() {var _this18;_classCallCheck(this, StandAloneLocalDayParser);for (var _len22 = arguments.length, args = new Array(_len22), _key22 = 0; _key22 < _len22; _key22++) {args[_key22] = arguments[_key22];}_this18 = _callSuper(this, StandAloneLocalDayParser, [].concat(args));_defineProperty(_assertThisInitialized(_this18), "priority",
    90);_defineProperty(_assertThisInitialized(_this18), "incompatibleTokens",









































    [
    "y",
    "R",
    "u",
    "q",
    "Q",
    "M",
    "L",
    "I",
    "d",
    "D",
    "E",
    "i",
    "e",
    "t",
    "T"]);return _this18;}_createClass(StandAloneLocalDayParser, [{ key: "parse", value: function parse(dateString, token, match3, options) {var valueCallback = function valueCallback(value) {var wholeWeekDays = Math.floor((value - 1) / 7) * 7;return (value + options.weekStartsOn + 6 + 1) % 7 + wholeWeekDays;};switch (token) {case "c":case "cc":return mapValue(parseNDigits(token.length, dateString), valueCallback);case "co":return mapValue(match3.ordinalNumber(dateString, { unit: "day" }), valueCallback);case "ccc":return match3.day(dateString, { width: "abbreviated", context: "standalone" }) || match3.day(dateString, { width: "short", context: "standalone" }) || match3.day(dateString, { width: "narrow", context: "standalone" });case "ccccc":return match3.day(dateString, { width: "narrow", context: "standalone" });case "cccccc":return match3.day(dateString, { width: "short", context: "standalone" }) || match3.day(dateString, { width: "narrow", context: "standalone" });case "cccc":default:return match3.day(dateString, { width: "wide", context: "standalone" }) || match3.day(dateString, { width: "abbreviated", context: "standalone" }) || match3.day(dateString, { width: "short", context: "standalone" }) || match3.day(dateString, { width: "narrow", context: "standalone" });}} }, { key: "validate", value: function validate(_date, value) {return value >= 0 && value <= 6;} }, { key: "set", value: function set(date, _flags, value, options) {date = setDay(date, value, options);date.setHours(0, 0, 0, 0);return date;} }]);return StandAloneLocalDayParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/setISODay.js
function setISODay(date, day, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var currentDay = getISODay(date_, options);
  var diff = day - currentDay;
  return addDays(date_, diff, options);
}

// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/ISODayParser.js
var ISODayParser = /*#__PURE__*/function (_Parser17) {_inherits(ISODayParser, _Parser17);function ISODayParser() {var _this19;_classCallCheck(this, ISODayParser);for (var _len23 = arguments.length, args = new Array(_len23), _key23 = 0; _key23 < _len23; _key23++) {args[_key23] = arguments[_key23];}_this19 = _callSuper(this, ISODayParser, [].concat(args));_defineProperty(_assertThisInitialized(_this19), "priority",
    90);_defineProperty(_assertThisInitialized(_this19), "incompatibleTokens",






























































    [
    "y",
    "Y",
    "u",
    "q",
    "Q",
    "M",
    "L",
    "w",
    "d",
    "D",
    "E",
    "e",
    "c",
    "t",
    "T"]);return _this19;}_createClass(ISODayParser, [{ key: "parse", value: function parse(dateString, token, match3) {var valueCallback = function valueCallback(value) {if (value === 0) {return 7;}return value;};switch (token) {case "i":case "ii":return parseNDigits(token.length, dateString);case "io":return match3.ordinalNumber(dateString, { unit: "day" });case "iii":return mapValue(match3.day(dateString, { width: "abbreviated", context: "formatting" }) || match3.day(dateString, { width: "short", context: "formatting" }) || match3.day(dateString, { width: "narrow", context: "formatting" }), valueCallback);case "iiiii":return mapValue(match3.day(dateString, { width: "narrow", context: "formatting" }), valueCallback);case "iiiiii":return mapValue(match3.day(dateString, { width: "short", context: "formatting" }) || match3.day(dateString, { width: "narrow", context: "formatting" }), valueCallback);case "iiii":default:return mapValue(match3.day(dateString, { width: "wide", context: "formatting" }) || match3.day(dateString, { width: "abbreviated", context: "formatting" }) || match3.day(dateString, { width: "short", context: "formatting" }) || match3.day(dateString, { width: "narrow", context: "formatting" }), valueCallback);}} }, { key: "validate", value: function validate(_date, value) {return value >= 1 && value <= 7;} }, { key: "set", value: function set(date, _flags, value) {date = setISODay(date, value);date.setHours(0, 0, 0, 0);return date;} }]);return ISODayParser;}(Parser);



// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/AMPMParser.js
var AMPMParser = /*#__PURE__*/function (_Parser18) {_inherits(AMPMParser, _Parser18);function AMPMParser() {var _this20;_classCallCheck(this, AMPMParser);for (var _len24 = arguments.length, args = new Array(_len24), _key24 = 0; _key24 < _len24; _key24++) {args[_key24] = arguments[_key24];}_this20 = _callSuper(this, AMPMParser, [].concat(args));_defineProperty(_assertThisInitialized(_this20), "priority",
    80);_defineProperty(_assertThisInitialized(_this20), "incompatibleTokens",



































    ["b", "B", "H", "k", "t", "T"]);return _this20;}_createClass(AMPMParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "a":case "aa":case "aaa":return match3.dayPeriod(dateString, { width: "abbreviated", context: "formatting" }) || match3.dayPeriod(dateString, { width: "narrow", context: "formatting" });case "aaaaa":return match3.dayPeriod(dateString, { width: "narrow", context: "formatting" });case "aaaa":default:return match3.dayPeriod(dateString, { width: "wide", context: "formatting" }) || match3.dayPeriod(dateString, { width: "abbreviated", context: "formatting" }) || match3.dayPeriod(dateString, { width: "narrow", context: "formatting" });}} }, { key: "set", value: function set(date, _flags, value) {date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);return date;} }]);return AMPMParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/AMPMMidnightParser.js
var AMPMMidnightParser = /*#__PURE__*/function (_Parser19) {_inherits(AMPMMidnightParser, _Parser19);function AMPMMidnightParser() {var _this21;_classCallCheck(this, AMPMMidnightParser);for (var _len25 = arguments.length, args = new Array(_len25), _key25 = 0; _key25 < _len25; _key25++) {args[_key25] = arguments[_key25];}_this21 = _callSuper(this, AMPMMidnightParser, [].concat(args));_defineProperty(_assertThisInitialized(_this21), "priority",
    80);_defineProperty(_assertThisInitialized(_this21), "incompatibleTokens",



































    ["a", "B", "H", "k", "t", "T"]);return _this21;}_createClass(AMPMMidnightParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "b":case "bb":case "bbb":return match3.dayPeriod(dateString, { width: "abbreviated", context: "formatting" }) || match3.dayPeriod(dateString, { width: "narrow", context: "formatting" });case "bbbbb":return match3.dayPeriod(dateString, { width: "narrow", context: "formatting" });case "bbbb":default:return match3.dayPeriod(dateString, { width: "wide", context: "formatting" }) || match3.dayPeriod(dateString, { width: "abbreviated", context: "formatting" }) || match3.dayPeriod(dateString, { width: "narrow", context: "formatting" });}} }, { key: "set", value: function set(date, _flags, value) {date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);return date;} }]);return AMPMMidnightParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/DayPeriodParser.js
var DayPeriodParser = /*#__PURE__*/function (_Parser20) {_inherits(DayPeriodParser, _Parser20);function DayPeriodParser() {var _this22;_classCallCheck(this, DayPeriodParser);for (var _len26 = arguments.length, args = new Array(_len26), _key26 = 0; _key26 < _len26; _key26++) {args[_key26] = arguments[_key26];}_this22 = _callSuper(this, DayPeriodParser, [].concat(args));_defineProperty(_assertThisInitialized(_this22), "priority",
    80);_defineProperty(_assertThisInitialized(_this22), "incompatibleTokens",



































    ["a", "b", "t", "T"]);return _this22;}_createClass(DayPeriodParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "B":case "BB":case "BBB":return match3.dayPeriod(dateString, { width: "abbreviated", context: "formatting" }) || match3.dayPeriod(dateString, { width: "narrow", context: "formatting" });case "BBBBB":return match3.dayPeriod(dateString, { width: "narrow", context: "formatting" });case "BBBB":default:return match3.dayPeriod(dateString, { width: "wide", context: "formatting" }) || match3.dayPeriod(dateString, { width: "abbreviated", context: "formatting" }) || match3.dayPeriod(dateString, { width: "narrow", context: "formatting" });}} }, { key: "set", value: function set(date, _flags, value) {date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);return date;} }]);return DayPeriodParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/Hour1to12Parser.js
var Hour1to12Parser = /*#__PURE__*/function (_Parser21) {_inherits(Hour1to12Parser, _Parser21);function Hour1to12Parser() {var _this23;_classCallCheck(this, Hour1to12Parser);for (var _len27 = arguments.length, args = new Array(_len27), _key27 = 0; _key27 < _len27; _key27++) {args[_key27] = arguments[_key27];}_this23 = _callSuper(this, Hour1to12Parser, [].concat(args));_defineProperty(_assertThisInitialized(_this23), "priority",
    70);_defineProperty(_assertThisInitialized(_this23), "incompatibleTokens",
























    ["H", "K", "k", "t", "T"]);return _this23;}_createClass(Hour1to12Parser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "h":return parseNumericPattern(numericPatterns.hour12h, dateString);case "ho":return match3.ordinalNumber(dateString, { unit: "hour" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(_date, value) {return value >= 1 && value <= 12;} }, { key: "set", value: function set(date, _flags, value) {var isPM = date.getHours() >= 12;if (isPM && value < 12) {date.setHours(value + 12, 0, 0, 0);} else if (!isPM && value === 12) {date.setHours(0, 0, 0, 0);} else {date.setHours(value, 0, 0, 0);}return date;} }]);return Hour1to12Parser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/Hour0to23Parser.js
var Hour0to23Parser = /*#__PURE__*/function (_Parser22) {_inherits(Hour0to23Parser, _Parser22);function Hour0to23Parser() {var _this24;_classCallCheck(this, Hour0to23Parser);for (var _len28 = arguments.length, args = new Array(_len28), _key28 = 0; _key28 < _len28; _key28++) {args[_key28] = arguments[_key28];}_this24 = _callSuper(this, Hour0to23Parser, [].concat(args));_defineProperty(_assertThisInitialized(_this24), "priority",
    70);_defineProperty(_assertThisInitialized(_this24), "incompatibleTokens",

















    ["a", "b", "h", "K", "k", "t", "T"]);return _this24;}_createClass(Hour0to23Parser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "H":return parseNumericPattern(numericPatterns.hour23h, dateString);case "Ho":return match3.ordinalNumber(dateString, { unit: "hour" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(_date, value) {return value >= 0 && value <= 23;} }, { key: "set", value: function set(date, _flags, value) {date.setHours(value, 0, 0, 0);return date;} }]);return Hour0to23Parser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/Hour0To11Parser.js
var Hour0To11Parser = /*#__PURE__*/function (_Parser23) {_inherits(Hour0To11Parser, _Parser23);function Hour0To11Parser() {var _this25;_classCallCheck(this, Hour0To11Parser);for (var _len29 = arguments.length, args = new Array(_len29), _key29 = 0; _key29 < _len29; _key29++) {args[_key29] = arguments[_key29];}_this25 = _callSuper(this, Hour0To11Parser, [].concat(args));_defineProperty(_assertThisInitialized(_this25), "priority",
    70);_defineProperty(_assertThisInitialized(_this25), "incompatibleTokens",






















    ["h", "H", "k", "t", "T"]);return _this25;}_createClass(Hour0To11Parser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "K":return parseNumericPattern(numericPatterns.hour11h, dateString);case "Ko":return match3.ordinalNumber(dateString, { unit: "hour" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(_date, value) {return value >= 0 && value <= 11;} }, { key: "set", value: function set(date, _flags, value) {var isPM = date.getHours() >= 12;if (isPM && value < 12) {date.setHours(value + 12, 0, 0, 0);} else {date.setHours(value, 0, 0, 0);}return date;} }]);return Hour0To11Parser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/Hour1To24Parser.js
var Hour1To24Parser = /*#__PURE__*/function (_Parser24) {_inherits(Hour1To24Parser, _Parser24);function Hour1To24Parser() {var _this26;_classCallCheck(this, Hour1To24Parser);for (var _len30 = arguments.length, args = new Array(_len30), _key30 = 0; _key30 < _len30; _key30++) {args[_key30] = arguments[_key30];}_this26 = _callSuper(this, Hour1To24Parser, [].concat(args));_defineProperty(_assertThisInitialized(_this26), "priority",
    70);_defineProperty(_assertThisInitialized(_this26), "incompatibleTokens",


















    ["a", "b", "h", "H", "K", "t", "T"]);return _this26;}_createClass(Hour1To24Parser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "k":return parseNumericPattern(numericPatterns.hour24h, dateString);case "ko":return match3.ordinalNumber(dateString, { unit: "hour" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(_date, value) {return value >= 1 && value <= 24;} }, { key: "set", value: function set(date, _flags, value) {var hours = value <= 24 ? value % 24 : value;date.setHours(hours, 0, 0, 0);return date;} }]);return Hour1To24Parser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/MinuteParser.js
var MinuteParser = /*#__PURE__*/function (_Parser25) {_inherits(MinuteParser, _Parser25);function MinuteParser() {var _this27;_classCallCheck(this, MinuteParser);for (var _len31 = arguments.length, args = new Array(_len31), _key31 = 0; _key31 < _len31; _key31++) {args[_key31] = arguments[_key31];}_this27 = _callSuper(this, MinuteParser, [].concat(args));_defineProperty(_assertThisInitialized(_this27), "priority",
    60);_defineProperty(_assertThisInitialized(_this27), "incompatibleTokens",

















    ["t", "T"]);return _this27;}_createClass(MinuteParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "m":return parseNumericPattern(numericPatterns.minute, dateString);case "mo":return match3.ordinalNumber(dateString, { unit: "minute" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(_date, value) {return value >= 0 && value <= 59;} }, { key: "set", value: function set(date, _flags, value) {date.setMinutes(value, 0, 0);return date;} }]);return MinuteParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/SecondParser.js
var SecondParser = /*#__PURE__*/function (_Parser26) {_inherits(SecondParser, _Parser26);function SecondParser() {var _this28;_classCallCheck(this, SecondParser);for (var _len32 = arguments.length, args = new Array(_len32), _key32 = 0; _key32 < _len32; _key32++) {args[_key32] = arguments[_key32];}_this28 = _callSuper(this, SecondParser, [].concat(args));_defineProperty(_assertThisInitialized(_this28), "priority",
    50);_defineProperty(_assertThisInitialized(_this28), "incompatibleTokens",

















    ["t", "T"]);return _this28;}_createClass(SecondParser, [{ key: "parse", value: function parse(dateString, token, match3) {switch (token) {case "s":return parseNumericPattern(numericPatterns.second, dateString);case "so":return match3.ordinalNumber(dateString, { unit: "second" });default:return parseNDigits(token.length, dateString);}} }, { key: "validate", value: function validate(_date, value) {return value >= 0 && value <= 59;} }, { key: "set", value: function set(date, _flags, value) {date.setSeconds(value, 0);return date;} }]);return SecondParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/FractionOfSecondParser.js
var FractionOfSecondParser = /*#__PURE__*/function (_Parser27) {_inherits(FractionOfSecondParser, _Parser27);function FractionOfSecondParser() {var _this29;_classCallCheck(this, FractionOfSecondParser);for (var _len33 = arguments.length, args = new Array(_len33), _key33 = 0; _key33 < _len33; _key33++) {args[_key33] = arguments[_key33];}_this29 = _callSuper(this, FractionOfSecondParser, [].concat(args));_defineProperty(_assertThisInitialized(_this29), "priority",
    30);_defineProperty(_assertThisInitialized(_this29), "incompatibleTokens",








    ["t", "T"]);return _this29;}_createClass(FractionOfSecondParser, [{ key: "parse", value: function parse(dateString, token) {var valueCallback = function valueCallback(value) {return Math.trunc(value * Math.pow(10, -token.length + 3));};return mapValue(parseNDigits(token.length, dateString), valueCallback);} }, { key: "set", value: function set(date, _flags, value) {date.setMilliseconds(value);return date;} }]);return FractionOfSecondParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/ISOTimezoneWithZParser.js
var ISOTimezoneWithZParser = /*#__PURE__*/function (_Parser28) {_inherits(ISOTimezoneWithZParser, _Parser28);function ISOTimezoneWithZParser() {var _this30;_classCallCheck(this, ISOTimezoneWithZParser);for (var _len34 = arguments.length, args = new Array(_len34), _key34 = 0; _key34 < _len34; _key34++) {args[_key34] = arguments[_key34];}_this30 = _callSuper(this, ISOTimezoneWithZParser, [].concat(args));_defineProperty(_assertThisInitialized(_this30), "priority",
    10);_defineProperty(_assertThisInitialized(_this30), "incompatibleTokens",




















    ["t", "T", "x"]);return _this30;}_createClass(ISOTimezoneWithZParser, [{ key: "parse", value: function parse(dateString, token) {switch (token) {case "X":return parseTimezonePattern(timezonePatterns.basicOptionalMinutes, dateString);case "XX":return parseTimezonePattern(timezonePatterns.basic, dateString);case "XXXX":return parseTimezonePattern(timezonePatterns.basicOptionalSeconds, dateString);case "XXXXX":return parseTimezonePattern(timezonePatterns.extendedOptionalSeconds, dateString);case "XXX":default:return parseTimezonePattern(timezonePatterns.extended, dateString);}} }, { key: "set", value: function set(date, flags, value) {if (flags.timestampIsSet) return date;return constructFrom(date, date.getTime() - getTimezoneOffsetInMilliseconds(date) - value);} }]);return ISOTimezoneWithZParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/ISOTimezoneParser.js
var ISOTimezoneParser = /*#__PURE__*/function (_Parser29) {_inherits(ISOTimezoneParser, _Parser29);function ISOTimezoneParser() {var _this31;_classCallCheck(this, ISOTimezoneParser);for (var _len35 = arguments.length, args = new Array(_len35), _key35 = 0; _key35 < _len35; _key35++) {args[_key35] = arguments[_key35];}_this31 = _callSuper(this, ISOTimezoneParser, [].concat(args));_defineProperty(_assertThisInitialized(_this31), "priority",
    10);_defineProperty(_assertThisInitialized(_this31), "incompatibleTokens",




















    ["t", "T", "X"]);return _this31;}_createClass(ISOTimezoneParser, [{ key: "parse", value: function parse(dateString, token) {switch (token) {case "x":return parseTimezonePattern(timezonePatterns.basicOptionalMinutes, dateString);case "xx":return parseTimezonePattern(timezonePatterns.basic, dateString);case "xxxx":return parseTimezonePattern(timezonePatterns.basicOptionalSeconds, dateString);case "xxxxx":return parseTimezonePattern(timezonePatterns.extendedOptionalSeconds, dateString);case "xxx":default:return parseTimezonePattern(timezonePatterns.extended, dateString);}} }, { key: "set", value: function set(date, flags, value) {if (flags.timestampIsSet) return date;return constructFrom(date, date.getTime() - getTimezoneOffsetInMilliseconds(date) - value);} }]);return ISOTimezoneParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/TimestampSecondsParser.js
var TimestampSecondsParser = /*#__PURE__*/function (_Parser30) {_inherits(TimestampSecondsParser, _Parser30);function TimestampSecondsParser() {var _this32;_classCallCheck(this, TimestampSecondsParser);for (var _len36 = arguments.length, args = new Array(_len36), _key36 = 0; _key36 < _len36; _key36++) {args[_key36] = arguments[_key36];}_this32 = _callSuper(this, TimestampSecondsParser, [].concat(args));_defineProperty(_assertThisInitialized(_this32), "priority",
    40);_defineProperty(_assertThisInitialized(_this32), "incompatibleTokens",






    "*");return _this32;}_createClass(TimestampSecondsParser, [{ key: "parse", value: function parse(dateString) {return parseAnyDigitsSigned(dateString);} }, { key: "set", value: function set(date, _flags, value) {return [constructFrom(date, value * 1000), { timestampIsSet: true }];} }]);return TimestampSecondsParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers/TimestampMillisecondsParser.js
var TimestampMillisecondsParser = /*#__PURE__*/function (_Parser31) {_inherits(TimestampMillisecondsParser, _Parser31);function TimestampMillisecondsParser() {var _this33;_classCallCheck(this, TimestampMillisecondsParser);for (var _len37 = arguments.length, args = new Array(_len37), _key37 = 0; _key37 < _len37; _key37++) {args[_key37] = arguments[_key37];}_this33 = _callSuper(this, TimestampMillisecondsParser, [].concat(args));_defineProperty(_assertThisInitialized(_this33), "priority",
    20);_defineProperty(_assertThisInitialized(_this33), "incompatibleTokens",






    "*");return _this33;}_createClass(TimestampMillisecondsParser, [{ key: "parse", value: function parse(dateString) {return parseAnyDigitsSigned(dateString);} }, { key: "set", value: function set(date, _flags, value) {return [constructFrom(date, value), { timestampIsSet: true }];} }]);return TimestampMillisecondsParser;}(Parser);


// ../../../../../../tmp/date-fns-jalali/parse/_lib/parsers.js
var parsers = {
  G: new EraParser(),
  y: new YearParser(),
  Y: new LocalWeekYearParser(),
  R: new ISOWeekYearParser(),
  u: new ExtendedYearParser(),
  Q: new QuarterParser(),
  q: new StandAloneQuarterParser(),
  M: new MonthParser(),
  L: new StandAloneMonthParser(),
  w: new LocalWeekParser(),
  I: new ISOWeekParser(),
  d: new DateParser(),
  D: new DayOfYearParser(),
  E: new DayParser(),
  e: new LocalDayParser(),
  c: new StandAloneLocalDayParser(),
  i: new ISODayParser(),
  a: new AMPMParser(),
  b: new AMPMMidnightParser(),
  B: new DayPeriodParser(),
  h: new Hour1to12Parser(),
  H: new Hour0to23Parser(),
  K: new Hour0To11Parser(),
  k: new Hour1To24Parser(),
  m: new MinuteParser(),
  s: new SecondParser(),
  S: new FractionOfSecondParser(),
  X: new ISOTimezoneWithZParser(),
  x: new ISOTimezoneParser(),
  t: new TimestampSecondsParser(),
  T: new TimestampMillisecondsParser()
};

// ../../../../../../tmp/date-fns-jalali/parse.js
function parse(dateStr, formatStr, referenceDate, options) {var _ref33, _options$locale15, _ref34, _ref35, _ref36, _options$firstWeekCon4, _options$locale16, _defaultOptions14$loc2, _ref37, _ref38, _ref39, _options$weekStartsOn7, _options$locale17, _defaultOptions14$loc3;
  var invalidDate = function invalidDate() {return constructFrom((options === null || options === void 0 ? void 0 : options.in) || referenceDate, NaN);};
  var defaultOptions14 = getDefaultOptions2();
  var locale = (_ref33 = (_options$locale15 = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale15 !== void 0 ? _options$locale15 : defaultOptions14.locale) !== null && _ref33 !== void 0 ? _ref33 : faIR;
  var firstWeekContainsDate = (_ref34 = (_ref35 = (_ref36 = (_options$firstWeekCon4 = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon4 !== void 0 ? _options$firstWeekCon4 : options === null || options === void 0 || (_options$locale16 = options.locale) === null || _options$locale16 === void 0 || (_options$locale16 = _options$locale16.options) === null || _options$locale16 === void 0 ? void 0 : _options$locale16.firstWeekContainsDate) !== null && _ref36 !== void 0 ? _ref36 : defaultOptions14.firstWeekContainsDate) !== null && _ref35 !== void 0 ? _ref35 : (_defaultOptions14$loc2 = defaultOptions14.locale) === null || _defaultOptions14$loc2 === void 0 || (_defaultOptions14$loc2 = _defaultOptions14$loc2.options) === null || _defaultOptions14$loc2 === void 0 ? void 0 : _defaultOptions14$loc2.firstWeekContainsDate) !== null && _ref34 !== void 0 ? _ref34 : 1;
  var weekStartsOn = (_ref37 = (_ref38 = (_ref39 = (_options$weekStartsOn7 = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn7 !== void 0 ? _options$weekStartsOn7 : options === null || options === void 0 || (_options$locale17 = options.locale) === null || _options$locale17 === void 0 || (_options$locale17 = _options$locale17.options) === null || _options$locale17 === void 0 ? void 0 : _options$locale17.weekStartsOn) !== null && _ref39 !== void 0 ? _ref39 : defaultOptions14.weekStartsOn) !== null && _ref38 !== void 0 ? _ref38 : (_defaultOptions14$loc3 = defaultOptions14.locale) === null || _defaultOptions14$loc3 === void 0 || (_defaultOptions14$loc3 = _defaultOptions14$loc3.options) === null || _defaultOptions14$loc3 === void 0 ? void 0 : _defaultOptions14$loc3.weekStartsOn) !== null && _ref37 !== void 0 ? _ref37 : 6;
  if (!formatStr)
  return dateStr ? invalidDate() : toDate(referenceDate, options === null || options === void 0 ? void 0 : options.in);
  var subFnOptions = {
    firstWeekContainsDate: firstWeekContainsDate,
    weekStartsOn: weekStartsOn,
    locale: locale
  };
  var setters = [new DateTimezoneSetter(options === null || options === void 0 ? void 0 : options.in, referenceDate)];
  var tokens = formatStr.match(longFormattingTokensRegExp2).map(function (substring) {
    var firstCharacter = substring[0];
    if (firstCharacter in longFormatters) {
      var longFormatter = longFormatters[firstCharacter];
      return longFormatter(substring, locale.formatLong);
    }
    return substring;
  }).join("").match(formattingTokensRegExp2);
  var usedTokens = [];var _iterator = _createForOfIteratorHelper(
      tokens),_step;try {var _loop = function _loop() {var token = _step.value;
        if (!(options !== null && options !== void 0 && options.useAdditionalWeekYearTokens) && isProtectedWeekYearToken(token)) {
          warnOrThrowProtectedError(token, formatStr, dateStr);
        }
        if (!(options !== null && options !== void 0 && options.useAdditionalDayOfYearTokens) && isProtectedDayOfYearToken(token)) {
          warnOrThrowProtectedError(token, formatStr, dateStr);
        }
        var firstCharacter = token[0];
        var parser = parsers[firstCharacter];
        if (parser) {
          var incompatibleTokens = parser.incompatibleTokens;
          if (Array.isArray(incompatibleTokens)) {
            var incompatibleToken = usedTokens.find(function (usedToken) {return incompatibleTokens.includes(usedToken.token) || usedToken.token === firstCharacter;});
            if (incompatibleToken) {
              throw new RangeError("The format string mustn't contain `".concat(incompatibleToken.fullToken, "` and `").concat(token, "` at the same time"));
            }
          } else if (parser.incompatibleTokens === "*" && usedTokens.length > 0) {
            throw new RangeError("The format string mustn't contain `".concat(token, "` and any other token at the same time"));
          }
          usedTokens.push({ token: firstCharacter, fullToken: token });
          var parseResult = parser.run(dateStr, token, locale.match, subFnOptions);
          if (!parseResult) {return { v:
              invalidDate() };
          }
          setters.push(parseResult.setter);
          dateStr = parseResult.rest;
        } else {
          if (firstCharacter.match(unescapedLatinCharacterRegExp2)) {
            throw new RangeError("Format string contains an unescaped latin alphabet character `" + firstCharacter + "`");
          }
          if (token === "''") {
            token = "'";
          } else if (firstCharacter === "'") {
            token = cleanEscapedString2(token);
          }
          if (dateStr.indexOf(token) === 0) {
            dateStr = dateStr.slice(token.length);
          } else {return { v:
              invalidDate() };
          }
        }
      },_ret;for (_iterator.s(); !(_step = _iterator.n()).done;) {_ret = _loop();if (_ret) return _ret.v;}} catch (err) {_iterator.e(err);} finally {_iterator.f();}
  if (dateStr.length > 0 && notWhitespaceRegExp.test(dateStr)) {
    return invalidDate();
  }
  var uniquePrioritySetters = setters.map(function (setter) {return setter.priority;}).sort(function (a, b) {return b - a;}).filter(function (priority, index, array) {return array.indexOf(priority) === index;}).map(function (priority) {return setters.filter(function (setter) {return setter.priority === priority;}).sort(function (a, b) {return b.subPriority - a.subPriority;});}).map(function (setterArray) {return setterArray[0];});
  var date = toDate(referenceDate, options === null || options === void 0 ? void 0 : options.in);
  if (isNaN(+date))
  return invalidDate();
  var flags = {};var _iterator2 = _createForOfIteratorHelper(
      uniquePrioritySetters),_step2;try {for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {var setter = _step2.value;
      if (!setter.validate(date, subFnOptions)) {
        return invalidDate();
      }
      var result = setter.set(date, flags, subFnOptions);
      if (Array.isArray(result)) {
        date = result[0];
        Object.assign(flags, result[1]);
      } else {
        date = result;
      }
    }} catch (err) {_iterator2.e(err);} finally {_iterator2.f();}
  return date;
}
function cleanEscapedString2(input) {
  return input.match(escapedStringRegExp2)[1].replace(doubleQuoteRegExp2, "'");
}
var formattingTokensRegExp2 = /[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;
var longFormattingTokensRegExp2 = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
var escapedStringRegExp2 = /^'([^]*?)'?$/;
var doubleQuoteRegExp2 = /''/g;
var notWhitespaceRegExp = /\S/;
var unescapedLatinCharacterRegExp2 = /[a-zA-Z]/;

// ../../../../../../tmp/date-fns-jalali/isMatch.js
function isMatch(dateStr, formatStr, options) {
  return isValid(parse(dateStr, formatStr, newDate(), options));
}

// ../../../../../../tmp/date-fns-jalali/fp/isMatch.js
var isMatch3 = convertToFP(isMatch, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isMatchWithOptions.js
var _isMatchWithOptions = convertToFP(isMatch, 3);
// ../../../../../../tmp/date-fns-jalali/isMonday.js
function isMonday(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay() === 1;
}

// ../../../../../../tmp/date-fns-jalali/fp/isMonday.js
var isMonday3 = convertToFP(isMonday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isMondayWithOptions.js
var _isMondayWithOptions = convertToFP(isMonday, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameDay.js
var isSameDay4 = convertToFP(isSameDay, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameDayWithOptions.js
var _isSameDayWithOptions = convertToFP(isSameDay, 3);
// ../../../../../../tmp/date-fns-jalali/startOfHour.js
function startOfHour(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setMinutes(0, 0, 0);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/isSameHour.js
function isSameHour(dateLeft, dateRight, options) {
  var _normalizeDates45 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, dateLeft, dateRight),_normalizeDates46 = _slicedToArray(_normalizeDates45, 2),dateLeft_ = _normalizeDates46[0],dateRight_ = _normalizeDates46[1];
  return +startOfHour(dateLeft_) === +startOfHour(dateRight_);
}

// ../../../../../../tmp/date-fns-jalali/fp/isSameHour.js
var isSameHour3 = convertToFP(isSameHour, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameHourWithOptions.js
var _isSameHourWithOptions = convertToFP(isSameHour, 3);
// ../../../../../../tmp/date-fns-jalali/isSameWeek.js
function isSameWeek(laterDate, earlierDate, options) {
  var _normalizeDates47 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates48 = _slicedToArray(_normalizeDates47, 2),laterDate_ = _normalizeDates48[0],earlierDate_ = _normalizeDates48[1];
  return +startOfWeek(laterDate_, options) === +startOfWeek(earlierDate_, options);
}

// ../../../../../../tmp/date-fns-jalali/isSameISOWeek.js
function isSameISOWeek(laterDate, earlierDate, options) {
  return isSameWeek(laterDate, earlierDate, _objectSpread(_objectSpread({}, options), {}, { weekStartsOn: 1 }));
}

// ../../../../../../tmp/date-fns-jalali/fp/isSameISOWeek.js
var isSameISOWeek3 = convertToFP(isSameISOWeek, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameISOWeekWithOptions.js
var _isSameISOWeekWithOptions = convertToFP(isSameISOWeek, 3);
// ../../../../../../tmp/date-fns-jalali/isSameISOWeekYear.js
function isSameISOWeekYear(laterDate, earlierDate, options) {
  var _normalizeDates49 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates50 = _slicedToArray(_normalizeDates49, 2),laterDate_ = _normalizeDates50[0],earlierDate_ = _normalizeDates50[1];
  return +startOfISOWeekYear(laterDate_) === +startOfISOWeekYear(earlierDate_);
}

// ../../../../../../tmp/date-fns-jalali/fp/isSameISOWeekYear.js
var isSameISOWeekYear3 = convertToFP(isSameISOWeekYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameISOWeekYearWithOptions.js
var _isSameISOWeekYearWithOptions = convertToFP(isSameISOWeekYear, 3);
// ../../../../../../tmp/date-fns-jalali/startOfMinute.js
function startOfMinute(date, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  date_.setSeconds(0, 0);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/isSameMinute.js
function isSameMinute(laterDate, earlierDate) {
  return +startOfMinute(laterDate) === +startOfMinute(earlierDate);
}

// ../../../../../../tmp/date-fns-jalali/fp/isSameMinute.js
var isSameMinute3 = convertToFP(isSameMinute, 2);
// ../../../../../../tmp/date-fns-jalali/isSameMonth.js
function isSameMonth(laterDate, earlierDate, options) {
  var _normalizeDates51 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates52 = _slicedToArray(_normalizeDates51, 2),laterDate_ = _normalizeDates52[0],earlierDate_ = _normalizeDates52[1];
  return getFullYear(laterDate_) === getFullYear(earlierDate_) && getMonth(laterDate_) === getMonth(earlierDate_);
}

// ../../../../../../tmp/date-fns-jalali/fp/isSameMonth.js
var isSameMonth3 = convertToFP(isSameMonth, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameMonthWithOptions.js
var _isSameMonthWithOptions = convertToFP(isSameMonth, 3);
// ../../../../../../tmp/date-fns-jalali/isSameQuarter.js
function isSameQuarter(laterDate, earlierDate, options) {
  var _normalizeDates53 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates54 = _slicedToArray(_normalizeDates53, 2),dateLeft_ = _normalizeDates54[0],dateRight_ = _normalizeDates54[1];
  return +startOfQuarter(dateLeft_) === +startOfQuarter(dateRight_);
}

// ../../../../../../tmp/date-fns-jalali/fp/isSameQuarter.js
var isSameQuarter3 = convertToFP(isSameQuarter, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameQuarterWithOptions.js
var _isSameQuarterWithOptions = convertToFP(isSameQuarter, 3);
// ../../../../../../tmp/date-fns-jalali/startOfSecond.js
function startOfSecond(date, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  date_.setMilliseconds(0);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/isSameSecond.js
function isSameSecond(laterDate, earlierDate) {
  return +startOfSecond(laterDate) === +startOfSecond(earlierDate);
}

// ../../../../../../tmp/date-fns-jalali/fp/isSameSecond.js
var isSameSecond3 = convertToFP(isSameSecond, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameWeek.js
var isSameWeek4 = convertToFP(isSameWeek, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameWeekWithOptions.js
var _isSameWeekWithOptions = convertToFP(isSameWeek, 3);
// ../../../../../../tmp/date-fns-jalali/isSameYear.js
function isSameYear(laterDate, earlierDate, options) {
  var _normalizeDates55 = normalizeDates(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate),_normalizeDates56 = _slicedToArray(_normalizeDates55, 2),laterDate_ = _normalizeDates56[0],earlierDate_ = _normalizeDates56[1];
  return getFullYear(laterDate_) === getFullYear(earlierDate_);
}

// ../../../../../../tmp/date-fns-jalali/fp/isSameYear.js
var isSameYear3 = convertToFP(isSameYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isSameYearWithOptions.js
var _isSameYearWithOptions = convertToFP(isSameYear, 3);
// ../../../../../../tmp/date-fns-jalali/isSaturday.js
function isSaturday(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay() === 6;
}

// ../../../../../../tmp/date-fns-jalali/fp/isSaturday.js
var isSaturday3 = convertToFP(isSaturday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isSaturdayWithOptions.js
var _isSaturdayWithOptions = convertToFP(isSaturday, 2);
// ../../../../../../tmp/date-fns-jalali/isSunday.js
function isSunday(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay() === 0;
}

// ../../../../../../tmp/date-fns-jalali/fp/isSunday.js
var isSunday3 = convertToFP(isSunday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isSundayWithOptions.js
var _isSundayWithOptions = convertToFP(isSunday, 2);
// ../../../../../../tmp/date-fns-jalali/isThursday.js
function isThursday(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay() === 4;
}

// ../../../../../../tmp/date-fns-jalali/fp/isThursday.js
var isThursday3 = convertToFP(isThursday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isThursdayWithOptions.js
var _isThursdayWithOptions = convertToFP(isThursday, 2);
// ../../../../../../tmp/date-fns-jalali/isTuesday.js
function isTuesday(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay() === 2;
}

// ../../../../../../tmp/date-fns-jalali/fp/isTuesday.js
var isTuesday3 = convertToFP(isTuesday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isTuesdayWithOptions.js
var _isTuesdayWithOptions = convertToFP(isTuesday, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isValid.js
var isValid9 = convertToFP(isValid, 1);
// ../../../../../../tmp/date-fns-jalali/isWednesday.js
function isWednesday(date, options) {
  return toDate(date, options === null || options === void 0 ? void 0 : options.in).getDay() === 3;
}

// ../../../../../../tmp/date-fns-jalali/fp/isWednesday.js
var isWednesday3 = convertToFP(isWednesday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isWednesdayWithOptions.js
var _isWednesdayWithOptions = convertToFP(isWednesday, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isWeekend.js
var isWeekend6 = convertToFP(isWeekend, 1);
// ../../../../../../tmp/date-fns-jalali/fp/isWeekendWithOptions.js
var _isWeekendWithOptions = convertToFP(isWeekend, 2);
// ../../../../../../tmp/date-fns-jalali/isWithinInterval.js
function isWithinInterval(date, interval5, options) {
  var time = +toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var _sort9 = [
    +toDate(interval5.start, options === null || options === void 0 ? void 0 : options.in),
    +toDate(interval5.end, options === null || options === void 0 ? void 0 : options.in)].
    sort(function (a, b) {return a - b;}),_sort10 = _slicedToArray(_sort9, 2),startTime = _sort10[0],endTime = _sort10[1];
  return time >= startTime && time <= endTime;
}

// ../../../../../../tmp/date-fns-jalali/fp/isWithinInterval.js
var isWithinInterval3 = convertToFP(isWithinInterval, 2);
// ../../../../../../tmp/date-fns-jalali/fp/isWithinIntervalWithOptions.js
var _isWithinIntervalWithOptions = convertToFP(isWithinInterval, 3);
// ../../../../../../tmp/date-fns-jalali/lastDayOfDecade.js
function lastDayOfDecade(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(_date);
  var decade = 9 + Math.floor(year / 10) * 10;
  setFullYear(_date, decade + 1, 0, 0);
  _date.setHours(0, 0, 0, 0);
  return toDate(_date, options === null || options === void 0 ? void 0 : options.in);
}

// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfDecade.js
var lastDayOfDecade3 = convertToFP(lastDayOfDecade, 1);
// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfDecadeWithOptions.js
var _lastDayOfDecadeWithOptions = convertToFP(lastDayOfDecade, 2);
// ../../../../../../tmp/date-fns-jalali/lastDayOfWeek.js
function lastDayOfWeek(date, options) {var _ref40, _ref41, _ref42, _options$weekStartsOn8, _options$locale18, _defaultOptions15$loc;
  var defaultOptions15 = getDefaultOptions();
  var weekStartsOn = (_ref40 = (_ref41 = (_ref42 = (_options$weekStartsOn8 = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn8 !== void 0 ? _options$weekStartsOn8 : options === null || options === void 0 || (_options$locale18 = options.locale) === null || _options$locale18 === void 0 || (_options$locale18 = _options$locale18.options) === null || _options$locale18 === void 0 ? void 0 : _options$locale18.weekStartsOn) !== null && _ref42 !== void 0 ? _ref42 : defaultOptions15.weekStartsOn) !== null && _ref41 !== void 0 ? _ref41 : (_defaultOptions15$loc = defaultOptions15.locale) === null || _defaultOptions15$loc === void 0 || (_defaultOptions15$loc = _defaultOptions15$loc.options) === null || _defaultOptions15$loc === void 0 ? void 0 : _defaultOptions15$loc.weekStartsOn) !== null && _ref40 !== void 0 ? _ref40 : 6;
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var day = _date.getDay();
  var diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);
  _date.setHours(0, 0, 0, 0);
  setDate(_date, getDate(_date) + diff);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/lastDayOfISOWeek.js
function lastDayOfISOWeek(date, options) {
  return lastDayOfWeek(date, _objectSpread(_objectSpread({}, options), {}, { weekStartsOn: 1 }));
}

// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfISOWeek.js
var lastDayOfISOWeek3 = convertToFP(lastDayOfISOWeek, 1);
// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfISOWeekWithOptions.js
var _lastDayOfISOWeekWithOptions = convertToFP(lastDayOfISOWeek, 2);
// ../../../../../../tmp/date-fns-jalali/lastDayOfISOWeekYear.js
function lastDayOfISOWeekYear(date, options) {
  var year = getISOWeekYear(date, options);
  var fourthOfJanuary = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);
  fourthOfJanuary.setFullYear(year + 1, 0, 4);
  fourthOfJanuary.setHours(0, 0, 0, 0);
  var date_ = startOfISOWeek(fourthOfJanuary, options);
  date_.setDate(date_.getDate() - 1);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfISOWeekYear.js
var lastDayOfISOWeekYear3 = convertToFP(lastDayOfISOWeekYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfISOWeekYearWithOptions.js
var _lastDayOfISOWeekYearWithOptions = convertToFP(lastDayOfISOWeekYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfMonth.js
var lastDayOfMonth4 = convertToFP(lastDayOfMonth, 1);
// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfMonthWithOptions.js
var _lastDayOfMonthWithOptions = convertToFP(lastDayOfMonth, 2);
// ../../../../../../tmp/date-fns-jalali/lastDayOfQuarter.js
function lastDayOfQuarter(date, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var currentMonth = getMonth(date_);
  var month = currentMonth - currentMonth % 3 + 3;
  setMonth(date_, month, 0);
  date_.setHours(0, 0, 0, 0);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfQuarter.js
var lastDayOfQuarter3 = convertToFP(lastDayOfQuarter, 1);
// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfQuarterWithOptions.js
var _lastDayOfQuarterWithOptions = convertToFP(lastDayOfQuarter, 2);
// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfWeek.js
var lastDayOfWeek4 = convertToFP(lastDayOfWeek, 1);
// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfWeekWithOptions.js
var _lastDayOfWeekWithOptions = convertToFP(lastDayOfWeek, 2);
// ../../../../../../tmp/date-fns-jalali/lastDayOfYear.js
function lastDayOfYear(date, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(date_);
  setFullYear(date_, year + 1, 0, 0);
  date_.setHours(0, 0, 0, 0);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfYear.js
var lastDayOfYear3 = convertToFP(lastDayOfYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/lastDayOfYearWithOptions.js
var _lastDayOfYearWithOptions = convertToFP(lastDayOfYear, 2);
// ../../../../../../tmp/date-fns-jalali/lightFormat.js
function lightFormat(date, formatStr) {
  var date_ = toDate(date);
  if (!isValid(date_)) {
    throw new RangeError("Invalid time value");
  }
  var tokens = formatStr.match(formattingTokensRegExp3);
  if (!tokens)
  return "";
  var result = tokens.map(function (substring) {
    if (substring === "''") {
      return "'";
    }
    var firstCharacter = substring[0];
    if (firstCharacter === "'") {
      return cleanEscapedString3(substring);
    }
    var formatter = lightFormatters[firstCharacter];
    if (formatter) {
      return formatter(date_, substring);
    }
    if (firstCharacter.match(unescapedLatinCharacterRegExp3)) {
      throw new RangeError("Format string contains an unescaped latin alphabet character `" + firstCharacter + "`");
    }
    return substring;
  }).join("");
  return result;
}
function cleanEscapedString3(input) {
  var matches = input.match(escapedStringRegExp3);
  if (!matches)
  return input;
  return matches[1].replace(doubleQuoteRegExp3, "'");
}
var formattingTokensRegExp3 = /(\w)\1*|''|'(''|[^'])+('|$)|./g;
var escapedStringRegExp3 = /^'([^]*?)'?$/;
var doubleQuoteRegExp3 = /''/g;
var unescapedLatinCharacterRegExp3 = /[a-zA-Z]/;

// ../../../../../../tmp/date-fns-jalali/fp/lightFormat.js
var lightFormat3 = convertToFP(lightFormat, 2);
// ../../../../../../tmp/date-fns-jalali/fp/max.js
var max4 = convertToFP(max, 1);
// ../../../../../../tmp/date-fns-jalali/fp/maxWithOptions.js
var _maxWithOptions = convertToFP(max, 2);
// ../../../../../../tmp/date-fns-jalali/milliseconds.js
function milliseconds(_ref43)







{var years = _ref43.years,months2 = _ref43.months,weeks = _ref43.weeks,days2 = _ref43.days,hours = _ref43.hours,minutes = _ref43.minutes,seconds = _ref43.seconds;
  var totalDays = 0;
  if (years)
  totalDays += years * daysInYear;
  if (months2)
  totalDays += months2 * (daysInYear / 12);
  if (weeks)
  totalDays += weeks * 7;
  if (days2)
  totalDays += days2;
  var totalSeconds = totalDays * 24 * 60 * 60;
  if (hours)
  totalSeconds += hours * 60 * 60;
  if (minutes)
  totalSeconds += minutes * 60;
  if (seconds)
  totalSeconds += seconds;
  return Math.trunc(totalSeconds * 1000);
}

// ../../../../../../tmp/date-fns-jalali/fp/milliseconds.js
var milliseconds3 = convertToFP(milliseconds, 1);
// ../../../../../../tmp/date-fns-jalali/millisecondsToHours.js
function millisecondsToHours(milliseconds4) {
  var hours = milliseconds4 / millisecondsInHour;
  return Math.trunc(hours);
}

// ../../../../../../tmp/date-fns-jalali/fp/millisecondsToHours.js
var millisecondsToHours3 = convertToFP(millisecondsToHours, 1);
// ../../../../../../tmp/date-fns-jalali/millisecondsToMinutes.js
function millisecondsToMinutes(milliseconds4) {
  var minutes = milliseconds4 / millisecondsInMinute;
  return Math.trunc(minutes);
}

// ../../../../../../tmp/date-fns-jalali/fp/millisecondsToMinutes.js
var millisecondsToMinutes3 = convertToFP(millisecondsToMinutes, 1);
// ../../../../../../tmp/date-fns-jalali/millisecondsToSeconds.js
function millisecondsToSeconds(milliseconds4) {
  var seconds = milliseconds4 / millisecondsInSecond;
  return Math.trunc(seconds);
}

// ../../../../../../tmp/date-fns-jalali/fp/millisecondsToSeconds.js
var millisecondsToSeconds3 = convertToFP(millisecondsToSeconds, 1);
// ../../../../../../tmp/date-fns-jalali/fp/min.js
var min4 = convertToFP(min, 1);
// ../../../../../../tmp/date-fns-jalali/fp/minWithOptions.js
var _minWithOptions = convertToFP(min, 2);
// ../../../../../../tmp/date-fns-jalali/minutesToHours.js
function minutesToHours(minutes) {
  var hours = minutes / minutesInHour;
  return Math.trunc(hours);
}

// ../../../../../../tmp/date-fns-jalali/fp/minutesToHours.js
var minutesToHours3 = convertToFP(minutesToHours, 1);
// ../../../../../../tmp/date-fns-jalali/minutesToMilliseconds.js
function minutesToMilliseconds(minutes) {
  return Math.trunc(minutes * millisecondsInMinute);
}

// ../../../../../../tmp/date-fns-jalali/fp/minutesToMilliseconds.js
var minutesToMilliseconds3 = convertToFP(minutesToMilliseconds, 1);
// ../../../../../../tmp/date-fns-jalali/minutesToSeconds.js
function minutesToSeconds(minutes) {
  return Math.trunc(minutes * secondsInMinute);
}

// ../../../../../../tmp/date-fns-jalali/fp/minutesToSeconds.js
var minutesToSeconds3 = convertToFP(minutesToSeconds, 1);
// ../../../../../../tmp/date-fns-jalali/monthsToQuarters.js
function monthsToQuarters(months2) {
  var quarters = months2 / monthsInQuarter;
  return Math.trunc(quarters);
}

// ../../../../../../tmp/date-fns-jalali/fp/monthsToQuarters.js
var monthsToQuarters3 = convertToFP(monthsToQuarters, 1);
// ../../../../../../tmp/date-fns-jalali/monthsToYears.js
function monthsToYears(months2) {
  var years = months2 / monthsInYear;
  return Math.trunc(years);
}

// ../../../../../../tmp/date-fns-jalali/fp/monthsToYears.js
var monthsToYears3 = convertToFP(monthsToYears, 1);
// ../../../../../../tmp/date-fns-jalali/nextDay.js
function nextDay(date, day, options) {
  var delta = day - getDay(date, options);
  if (delta <= 0)
  delta += 7;
  return addDays(date, delta, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/nextDay.js
var nextDay3 = convertToFP(nextDay, 2);
// ../../../../../../tmp/date-fns-jalali/fp/nextDayWithOptions.js
var _nextDayWithOptions = convertToFP(nextDay, 3);
// ../../../../../../tmp/date-fns-jalali/nextFriday.js
function nextFriday(date, options) {
  return nextDay(date, 5, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/nextFriday.js
var nextFriday3 = convertToFP(nextFriday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/nextFridayWithOptions.js
var _nextFridayWithOptions = convertToFP(nextFriday, 2);
// ../../../../../../tmp/date-fns-jalali/nextMonday.js
function nextMonday(date, options) {
  return nextDay(date, 1, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/nextMonday.js
var nextMonday3 = convertToFP(nextMonday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/nextMondayWithOptions.js
var _nextMondayWithOptions = convertToFP(nextMonday, 2);
// ../../../../../../tmp/date-fns-jalali/nextSaturday.js
function nextSaturday(date, options) {
  return nextDay(date, 6, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/nextSaturday.js
var nextSaturday3 = convertToFP(nextSaturday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/nextSaturdayWithOptions.js
var _nextSaturdayWithOptions = convertToFP(nextSaturday, 2);
// ../../../../../../tmp/date-fns-jalali/nextSunday.js
function nextSunday(date, options) {
  return nextDay(date, 0, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/nextSunday.js
var nextSunday3 = convertToFP(nextSunday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/nextSundayWithOptions.js
var _nextSundayWithOptions = convertToFP(nextSunday, 2);
// ../../../../../../tmp/date-fns-jalali/nextThursday.js
function nextThursday(date, options) {
  return nextDay(date, 4, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/nextThursday.js
var nextThursday3 = convertToFP(nextThursday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/nextThursdayWithOptions.js
var _nextThursdayWithOptions = convertToFP(nextThursday, 2);
// ../../../../../../tmp/date-fns-jalali/nextTuesday.js
function nextTuesday(date, options) {
  return nextDay(date, 2, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/nextTuesday.js
var nextTuesday3 = convertToFP(nextTuesday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/nextTuesdayWithOptions.js
var _nextTuesdayWithOptions = convertToFP(nextTuesday, 2);
// ../../../../../../tmp/date-fns-jalali/nextWednesday.js
function nextWednesday(date, options) {
  return nextDay(date, 3, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/nextWednesday.js
var nextWednesday3 = convertToFP(nextWednesday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/nextWednesdayWithOptions.js
var _nextWednesdayWithOptions = convertToFP(nextWednesday, 2);
// ../../../../../../tmp/date-fns-jalali/fp/parse.js
var parse4 = convertToFP(parse, 3);
// ../../../../../../tmp/date-fns-jalali/parseISO.js
function parseISO(argument, options) {var _options$additionalDi;
  var invalidDate = function invalidDate() {return constructFrom(options === null || options === void 0 ? void 0 : options.in, NaN);};
  var additionalDigits = (_options$additionalDi = options === null || options === void 0 ? void 0 : options.additionalDigits) !== null && _options$additionalDi !== void 0 ? _options$additionalDi : 2;
  var dateStrings = splitDateString(argument);
  var date;
  if (dateStrings.date) {
    var parseYearResult = parseYear(dateStrings.date, additionalDigits);
    date = parseDate(parseYearResult.restDateString, parseYearResult.year);
  }
  if (!date || isNaN(+date))
  return invalidDate();
  var timestamp = +date;
  var time = 0;
  var offset;
  if (dateStrings.time) {
    time = parseTime(dateStrings.time);
    if (isNaN(time))
    return invalidDate();
  }
  if (dateStrings.timezone) {
    offset = parseTimezone(dateStrings.timezone);
    if (isNaN(offset))
    return invalidDate();
  } else {
    var tmpDate = new Date(timestamp + time);
    var result = toDate(0, options === null || options === void 0 ? void 0 : options.in);
    result.setFullYear(tmpDate.getUTCFullYear(), tmpDate.getUTCMonth(), tmpDate.getUTCDate());
    result.setHours(tmpDate.getUTCHours(), tmpDate.getUTCMinutes(), tmpDate.getUTCSeconds(), tmpDate.getUTCMilliseconds());
    return result;
  }
  return toDate(timestamp + time + offset, options === null || options === void 0 ? void 0 : options.in);
}
function splitDateString(dateString) {
  var dateStrings = {};
  var array = dateString.split(patterns.dateTimeDelimiter);
  var timeString;
  if (array.length > 2) {
    return dateStrings;
  }
  if (/:/.test(array[0])) {
    timeString = array[0];
  } else {
    dateStrings.date = array[0];
    timeString = array[1];
    if (patterns.timeZoneDelimiter.test(dateStrings.date)) {
      dateStrings.date = dateString.split(patterns.timeZoneDelimiter)[0];
      timeString = dateString.substr(dateStrings.date.length, dateString.length);
    }
  }
  if (timeString) {
    var token = patterns.timezone.exec(timeString);
    if (token) {
      dateStrings.time = timeString.replace(token[1], "");
      dateStrings.timezone = token[1];
    } else {
      dateStrings.time = timeString;
    }
  }
  return dateStrings;
}
function parseYear(dateString, additionalDigits) {
  var regex = new RegExp("^(?:(\\d{4}|[+-]\\d{" + (4 + additionalDigits) + "})|(\\d{2}|[+-]\\d{" + (2 + additionalDigits) + "})$)");
  var captures = dateString.match(regex);
  if (!captures)
  return { year: NaN, restDateString: "" };
  var year = captures[1] ? parseInt(captures[1]) : null;
  var century = captures[2] ? parseInt(captures[2]) : null;
  return {
    year: century === null ? year : century * 100,
    restDateString: dateString.slice((captures[1] || captures[2]).length)
  };
}
function parseDate(dateString, year) {
  if (year === null)
  return new Date(NaN);
  var captures = dateString.match(dateRegex);
  if (!captures)
  return new Date(NaN);
  var isWeekDate = !!captures[4];
  var dayOfYear = parseDateUnit(captures[1]);
  var month = parseDateUnit(captures[2]) - 1;
  var day = parseDateUnit(captures[3]);
  var week = parseDateUnit(captures[4]);
  var dayOfWeek = parseDateUnit(captures[5]) - 1;
  if (isWeekDate) {
    if (!validateWeekDate(year, week, dayOfWeek)) {
      return new Date(NaN);
    }
    return dayOfISOWeekYear(year, week, dayOfWeek);
  } else {
    var date = new Date(0);
    if (!validateDate(year, month, day) || !validateDayOfYearDate(year, dayOfYear)) {
      return new Date(NaN);
    }
    date.setUTCFullYear(year, month, Math.max(dayOfYear, day));
    return date;
  }
}
function parseDateUnit(value) {
  return value ? parseInt(value) : 1;
}
function parseTime(timeString) {
  var captures = timeString.match(timeRegex);
  if (!captures)
  return NaN;
  var hours = parseTimeUnit(captures[1]);
  var minutes = parseTimeUnit(captures[2]);
  var seconds = parseTimeUnit(captures[3]);
  if (!validateTime(hours, minutes, seconds)) {
    return NaN;
  }
  return hours * millisecondsInHour + minutes * millisecondsInMinute + seconds * 1000;
}
function parseTimeUnit(value) {
  return value && parseFloat(value.replace(",", ".")) || 0;
}
function parseTimezone(timezoneString) {
  if (timezoneString === "Z")
  return 0;
  var captures = timezoneString.match(timezoneRegex);
  if (!captures)
  return 0;
  var sign = captures[1] === "+" ? -1 : 1;
  var hours = parseInt(captures[2]);
  var minutes = captures[3] && parseInt(captures[3]) || 0;
  if (!validateTimezone(hours, minutes)) {
    return NaN;
  }
  return sign * (hours * millisecondsInHour + minutes * millisecondsInMinute);
}
function dayOfISOWeekYear(isoWeekYear, week, day) {
  var date = new Date(0);
  date.setUTCFullYear(isoWeekYear, 0, 4);
  var fourthOfJanuaryDay = date.getUTCDay() || 7;
  var diff = (week - 1) * 7 + day + 1 - fourthOfJanuaryDay;
  date.setUTCDate(date.getUTCDate() + diff);
  return date;
}
function isLeapYearIndex2(year) {
  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;
}
function validateDate(year, month, date) {
  return month >= 0 && month <= 11 && date >= 1 && date <= (daysInMonths[month] || (isLeapYearIndex2(year) ? 29 : 28));
}
function validateDayOfYearDate(year, dayOfYear) {
  return dayOfYear >= 1 && dayOfYear <= (isLeapYearIndex2(year) ? 366 : 365);
}
function validateWeekDate(_year, week, day) {
  return week >= 1 && week <= 53 && day >= 0 && day <= 6;
}
function validateTime(hours, minutes, seconds) {
  if (hours === 24) {
    return minutes === 0 && seconds === 0;
  }
  return seconds >= 0 && seconds < 60 && minutes >= 0 && minutes < 60 && hours >= 0 && hours < 25;
}
function validateTimezone(_hours, minutes) {
  return minutes >= 0 && minutes <= 59;
}
var patterns = {
  dateTimeDelimiter: /[T ]/,
  timeZoneDelimiter: /[Z ]/i,
  timezone: /([Z+-].*)$/
};
var dateRegex = /^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/;
var timeRegex = /^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/;
var timezoneRegex = /^([+-])(\d{2})(?::?(\d{2}))?$/;
var daysInMonths = [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

// ../../../../../../tmp/date-fns-jalali/fp/parseISO.js
var parseISO3 = convertToFP(parseISO, 1);
// ../../../../../../tmp/date-fns-jalali/fp/parseISOWithOptions.js
var _parseISOWithOptions = convertToFP(parseISO, 2);
// ../../../../../../tmp/date-fns-jalali/parseJSON.js
function parseJSON(dateStr, options) {
  var parts = dateStr.match(/(\d{4})-(\d{2})-(\d{2})[T ](\d{2}):(\d{2}):(\d{2})(?:\.(\d{0,7}))?(?:Z|(.)(\d{2}):?(\d{2})?)?/);
  if (!parts)
  return toDate(NaN, options === null || options === void 0 ? void 0 : options.in);
  return toDate(Date.UTC(+parts[1], +parts[2] - 1, +parts[3], +parts[4] - (+parts[9] || 0) * (parts[8] == "-" ? -1 : 1), +parts[5] - (+parts[10] || 0) * (parts[8] == "-" ? -1 : 1), +parts[6], +((parts[7] || "0") + "00").substring(0, 3)), options === null || options === void 0 ? void 0 : options.in);
}

// ../../../../../../tmp/date-fns-jalali/fp/parseJSON.js
var parseJSON3 = convertToFP(parseJSON, 1);
// ../../../../../../tmp/date-fns-jalali/fp/parseJSONWithOptions.js
var _parseJSONWithOptions = convertToFP(parseJSON, 2);
// ../../../../../../tmp/date-fns-jalali/fp/parseWithOptions.js
var _parseWithOptions = convertToFP(parse, 4);
// ../../../../../../tmp/date-fns-jalali/subDays.js
function subDays(date, amount, options) {
  return addDays(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/previousDay.js
function previousDay(date, day, options) {
  var delta = getDay(date, options) - day;
  if (delta <= 0)
  delta += 7;
  return subDays(date, delta, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/previousDay.js
var previousDay3 = convertToFP(previousDay, 2);
// ../../../../../../tmp/date-fns-jalali/fp/previousDayWithOptions.js
var _previousDayWithOptions = convertToFP(previousDay, 3);
// ../../../../../../tmp/date-fns-jalali/previousFriday.js
function previousFriday(date, options) {
  return previousDay(date, 5, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/previousFriday.js
var previousFriday3 = convertToFP(previousFriday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/previousFridayWithOptions.js
var _previousFridayWithOptions = convertToFP(previousFriday, 2);
// ../../../../../../tmp/date-fns-jalali/previousMonday.js
function previousMonday(date, options) {
  return previousDay(date, 1, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/previousMonday.js
var previousMonday3 = convertToFP(previousMonday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/previousMondayWithOptions.js
var _previousMondayWithOptions = convertToFP(previousMonday, 2);
// ../../../../../../tmp/date-fns-jalali/previousSaturday.js
function previousSaturday(date, options) {
  return previousDay(date, 6, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/previousSaturday.js
var previousSaturday3 = convertToFP(previousSaturday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/previousSaturdayWithOptions.js
var _previousSaturdayWithOptions = convertToFP(previousSaturday, 2);
// ../../../../../../tmp/date-fns-jalali/previousSunday.js
function previousSunday(date, options) {
  return previousDay(date, 0, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/previousSunday.js
var previousSunday3 = convertToFP(previousSunday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/previousSundayWithOptions.js
var _previousSundayWithOptions = convertToFP(previousSunday, 2);
// ../../../../../../tmp/date-fns-jalali/previousThursday.js
function previousThursday(date, options) {
  return previousDay(date, 4, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/previousThursday.js
var previousThursday3 = convertToFP(previousThursday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/previousThursdayWithOptions.js
var _previousThursdayWithOptions = convertToFP(previousThursday, 2);
// ../../../../../../tmp/date-fns-jalali/previousTuesday.js
function previousTuesday(date, options) {
  return previousDay(date, 2, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/previousTuesday.js
var previousTuesday3 = convertToFP(previousTuesday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/previousTuesdayWithOptions.js
var _previousTuesdayWithOptions = convertToFP(previousTuesday, 2);
// ../../../../../../tmp/date-fns-jalali/previousWednesday.js
function previousWednesday(date, options) {
  return previousDay(date, 3, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/previousWednesday.js
var previousWednesday3 = convertToFP(previousWednesday, 1);
// ../../../../../../tmp/date-fns-jalali/fp/previousWednesdayWithOptions.js
var _previousWednesdayWithOptions = convertToFP(previousWednesday, 2);
// ../../../../../../tmp/date-fns-jalali/quartersToMonths.js
function quartersToMonths(quarters) {
  return Math.trunc(quarters * monthsInQuarter);
}

// ../../../../../../tmp/date-fns-jalali/fp/quartersToMonths.js
var quartersToMonths3 = convertToFP(quartersToMonths, 1);
// ../../../../../../tmp/date-fns-jalali/quartersToYears.js
function quartersToYears(quarters) {
  var years = quarters / quartersInYear;
  return Math.trunc(years);
}

// ../../../../../../tmp/date-fns-jalali/fp/quartersToYears.js
var quartersToYears3 = convertToFP(quartersToYears, 1);
// ../../../../../../tmp/date-fns-jalali/roundToNearestHours.js
function roundToNearestHours(date, options) {var _options$nearestTo, _options$roundingMeth2;
  var nearestTo = (_options$nearestTo = options === null || options === void 0 ? void 0 : options.nearestTo) !== null && _options$nearestTo !== void 0 ? _options$nearestTo : 1;
  if (nearestTo < 1 || nearestTo > 12)
  return constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, NaN);
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var fractionalMinutes = date_.getMinutes() / 60;
  var fractionalSeconds = date_.getSeconds() / 60 / 60;
  var fractionalMilliseconds = date_.getMilliseconds() / 1000 / 60 / 60;
  var hours = date_.getHours() + fractionalMinutes + fractionalSeconds + fractionalMilliseconds;
  var method = (_options$roundingMeth2 = options === null || options === void 0 ? void 0 : options.roundingMethod) !== null && _options$roundingMeth2 !== void 0 ? _options$roundingMeth2 : "round";
  var roundingMethod = getRoundingMethod(method);
  var roundedHours = roundingMethod(hours / nearestTo) * nearestTo;
  date_.setHours(roundedHours, 0, 0, 0);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/fp/roundToNearestHours.js
var roundToNearestHours3 = convertToFP(roundToNearestHours, 1);
// ../../../../../../tmp/date-fns-jalali/fp/roundToNearestHoursWithOptions.js
var _roundToNearestHoursWithOptions = convertToFP(roundToNearestHours, 2);
// ../../../../../../tmp/date-fns-jalali/roundToNearestMinutes.js
function roundToNearestMinutes(date, options) {var _options$nearestTo2, _options$roundingMeth3;
  var nearestTo = (_options$nearestTo2 = options === null || options === void 0 ? void 0 : options.nearestTo) !== null && _options$nearestTo2 !== void 0 ? _options$nearestTo2 : 1;
  if (nearestTo < 1 || nearestTo > 30)
  return constructFrom(date, NaN);
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var fractionalSeconds = date_.getSeconds() / 60;
  var fractionalMilliseconds = date_.getMilliseconds() / 1000 / 60;
  var minutes = date_.getMinutes() + fractionalSeconds + fractionalMilliseconds;
  var method = (_options$roundingMeth3 = options === null || options === void 0 ? void 0 : options.roundingMethod) !== null && _options$roundingMeth3 !== void 0 ? _options$roundingMeth3 : "round";
  var roundingMethod = getRoundingMethod(method);
  var roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;
  date_.setMinutes(roundedMinutes, 0, 0);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/fp/roundToNearestMinutes.js
var roundToNearestMinutes3 = convertToFP(roundToNearestMinutes, 1);
// ../../../../../../tmp/date-fns-jalali/fp/roundToNearestMinutesWithOptions.js
var _roundToNearestMinutesWithOptions = convertToFP(roundToNearestMinutes, 2);
// ../../../../../../tmp/date-fns-jalali/secondsToHours.js
function secondsToHours(seconds) {
  var hours = seconds / secondsInHour;
  return Math.trunc(hours);
}

// ../../../../../../tmp/date-fns-jalali/fp/secondsToHours.js
var secondsToHours3 = convertToFP(secondsToHours, 1);
// ../../../../../../tmp/date-fns-jalali/secondsToMilliseconds.js
function secondsToMilliseconds(seconds) {
  return seconds * millisecondsInSecond;
}

// ../../../../../../tmp/date-fns-jalali/fp/secondsToMilliseconds.js
var secondsToMilliseconds3 = convertToFP(secondsToMilliseconds, 1);
// ../../../../../../tmp/date-fns-jalali/secondsToMinutes.js
function secondsToMinutes(seconds) {
  var minutes = seconds / secondsInMinute;
  return Math.trunc(minutes);
}

// ../../../../../../tmp/date-fns-jalali/fp/secondsToMinutes.js
var secondsToMinutes3 = convertToFP(secondsToMinutes, 1);
// ../../../../../../tmp/date-fns-jalali/setMonth.js
function setMonth15(date, month, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(_date);
  var day = getDate(_date);
  var midMonth = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);
  setFullYear(midMonth, year, month, 15);
  midMonth.setHours(0, 0, 0, 0);
  var daysInMonth = getDaysInMonth(midMonth);
  setMonth(_date, month, Math.min(day, daysInMonth));
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/set.js
function set(date, values, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  if (isNaN(+_date))
  return constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, NaN);
  if (values.year != null)
  setFullYear(_date, values.year);
  if (values.month != null)
  _date = setMonth15(_date, values.month);
  if (values.date != null)
  setDate(_date, values.date);
  if (values.hours != null)
  _date.setHours(values.hours);
  if (values.minutes != null)
  _date.setMinutes(values.minutes);
  if (values.seconds != null)
  _date.setSeconds(values.seconds);
  if (values.milliseconds != null)
  _date.setMilliseconds(values.milliseconds);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/set.js
var set3 = convertToFP(set, 2);
// ../../../../../../tmp/date-fns-jalali/setDate.js
function setDate16(date, dayOfMonth, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  setDate(_date, dayOfMonth);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/setDate.js
var setDate18 = convertToFP(setDate16, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setDateWithOptions.js
var _setDateWithOptions = convertToFP(setDate16, 3);
// ../../../../../../tmp/date-fns-jalali/fp/setDay.js
var setDay6 = convertToFP(setDay, 2);
// ../../../../../../tmp/date-fns-jalali/setDayOfYear.js
function setDayOfYear(date, dayOfYear, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  setMonth(date_, 0);
  setDate(date_, dayOfYear);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/fp/setDayOfYear.js
var setDayOfYear3 = convertToFP(setDayOfYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setDayOfYearWithOptions.js
var _setDayOfYearWithOptions = convertToFP(setDayOfYear, 3);
// ../../../../../../tmp/date-fns-jalali/fp/setDayWithOptions.js
var _setDayWithOptions = convertToFP(setDay, 3);
// ../../../../../../tmp/date-fns-jalali/setHours.js
function setHours(date, hours, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setHours(hours);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/setHours.js
var setHours3 = convertToFP(setHours, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setHoursWithOptions.js
var _setHoursWithOptions = convertToFP(setHours, 3);
// ../../../../../../tmp/date-fns-jalali/fp/setISODay.js
var setISODay4 = convertToFP(setISODay, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setISODayWithOptions.js
var _setISODayWithOptions = convertToFP(setISODay, 3);
// ../../../../../../tmp/date-fns-jalali/fp/setISOWeek.js
var setISOWeek4 = convertToFP(setISOWeek, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setISOWeekWithOptions.js
var _setISOWeekWithOptions = convertToFP(setISOWeek, 3);
// ../../../../../../tmp/date-fns-jalali/fp/setISOWeekYear.js
var setISOWeekYear4 = convertToFP(setISOWeekYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setISOWeekYearWithOptions.js
var _setISOWeekYearWithOptions = convertToFP(setISOWeekYear, 3);
// ../../../../../../tmp/date-fns-jalali/setMilliseconds.js
function setMilliseconds(date, milliseconds4, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setMilliseconds(milliseconds4);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/setMilliseconds.js
var setMilliseconds3 = convertToFP(setMilliseconds, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setMillisecondsWithOptions.js
var _setMillisecondsWithOptions = convertToFP(setMilliseconds, 3);
// ../../../../../../tmp/date-fns-jalali/setMinutes.js
function setMinutes(date, minutes, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  date_.setMinutes(minutes);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/fp/setMinutes.js
var setMinutes3 = convertToFP(setMinutes, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setMinutesWithOptions.js
var _setMinutesWithOptions = convertToFP(setMinutes, 3);
// ../../../../../../tmp/date-fns-jalali/fp/setMonth.js
var setMonth19 = convertToFP(setMonth15, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setMonthWithOptions.js
var _setMonthWithOptions = convertToFP(setMonth15, 3);
// ../../../../../../tmp/date-fns-jalali/setQuarter.js
function setQuarter(date, quarter, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var oldQuarter = Math.trunc(getMonth(date_) / 3) + 1;
  var diff = quarter - oldQuarter;
  return setMonth15(date_, getMonth(date_) + diff * 3);
}

// ../../../../../../tmp/date-fns-jalali/fp/setQuarter.js
var setQuarter3 = convertToFP(setQuarter, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setQuarterWithOptions.js
var _setQuarterWithOptions = convertToFP(setQuarter, 3);
// ../../../../../../tmp/date-fns-jalali/setSeconds.js
function setSeconds(date, seconds, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  _date.setSeconds(seconds);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/setSeconds.js
var setSeconds3 = convertToFP(setSeconds, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setSecondsWithOptions.js
var _setSecondsWithOptions = convertToFP(setSeconds, 3);
// ../../../../../../tmp/date-fns-jalali/fp/setWeek.js
var setWeek4 = convertToFP(setWeek, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setWeekWithOptions.js
var _setWeekWithOptions = convertToFP(setWeek, 3);
// ../../../../../../tmp/date-fns-jalali/setWeekYear.js
function setWeekYear(date, weekYear, options) {var _ref44, _ref45, _ref46, _options$firstWeekCon5, _options$locale19, _defaultOptions16$loc;
  var defaultOptions16 = getDefaultOptions();
  var firstWeekContainsDate = (_ref44 = (_ref45 = (_ref46 = (_options$firstWeekCon5 = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon5 !== void 0 ? _options$firstWeekCon5 : options === null || options === void 0 || (_options$locale19 = options.locale) === null || _options$locale19 === void 0 || (_options$locale19 = _options$locale19.options) === null || _options$locale19 === void 0 ? void 0 : _options$locale19.firstWeekContainsDate) !== null && _ref46 !== void 0 ? _ref46 : defaultOptions16.firstWeekContainsDate) !== null && _ref45 !== void 0 ? _ref45 : (_defaultOptions16$loc = defaultOptions16.locale) === null || _defaultOptions16$loc === void 0 || (_defaultOptions16$loc = _defaultOptions16$loc.options) === null || _defaultOptions16$loc === void 0 ? void 0 : _defaultOptions16$loc.firstWeekContainsDate) !== null && _ref44 !== void 0 ? _ref44 : 1;
  var diff = differenceInCalendarDays(toDate(date, options === null || options === void 0 ? void 0 : options.in), startOfWeekYear(date, options), options);
  var firstWeek = constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, 0);
  setFullYear(firstWeek, weekYear, 0, firstWeekContainsDate);
  firstWeek.setHours(0, 0, 0, 0);
  var date_ = startOfWeekYear(firstWeek, options);
  setDate(date_, getDate(date_) + diff);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/fp/setWeekYear.js
var setWeekYear3 = convertToFP(setWeekYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setWeekYearWithOptions.js
var _setWeekYearWithOptions = convertToFP(setWeekYear, 3);
// ../../../../../../tmp/date-fns-jalali/fp/setWithOptions.js
var _setWithOptions = convertToFP(set, 3);
// ../../../../../../tmp/date-fns-jalali/setYear.js
function setYear(date, year, options) {
  var date_ = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  if (isNaN(+date_))
  return constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, NaN);
  setFullYear(date_, year);
  return date_;
}

// ../../../../../../tmp/date-fns-jalali/fp/setYear.js
var setYear3 = convertToFP(setYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/setYearWithOptions.js
var _setYearWithOptions = convertToFP(setYear, 3);
// ../../../../../../tmp/date-fns-jalali/fp/startOfDay.js
var startOfDay5 = convertToFP(startOfDay, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfDayWithOptions.js
var _startOfDayWithOptions = convertToFP(startOfDay, 2);
// ../../../../../../tmp/date-fns-jalali/startOfDecade.js
function startOfDecade(date, options) {
  var _date = toDate(date, options === null || options === void 0 ? void 0 : options.in);
  var year = getFullYear(_date);
  var decade = Math.floor(year / 10) * 10;
  setFullYear(_date, decade, 0, 1);
  _date.setHours(0, 0, 0, 0);
  return _date;
}

// ../../../../../../tmp/date-fns-jalali/fp/startOfDecade.js
var startOfDecade3 = convertToFP(startOfDecade, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfDecadeWithOptions.js
var _startOfDecadeWithOptions = convertToFP(startOfDecade, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfHour.js
var startOfHour4 = convertToFP(startOfHour, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfHourWithOptions.js
var _startOfHourWithOptions = convertToFP(startOfHour, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfISOWeek.js
var startOfISOWeek11 = convertToFP(startOfISOWeek, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfISOWeekWithOptions.js
var _startOfISOWeekWithOptions = convertToFP(startOfISOWeek, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfISOWeekYear.js
var startOfISOWeekYear7 = convertToFP(startOfISOWeekYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfISOWeekYearWithOptions.js
var _startOfISOWeekYearWithOptions = convertToFP(startOfISOWeekYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfMinute.js
var startOfMinute4 = convertToFP(startOfMinute, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfMinuteWithOptions.js
var _startOfMinuteWithOptions = convertToFP(startOfMinute, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfMonth.js
var startOfMonth6 = convertToFP(startOfMonth, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfMonthWithOptions.js
var _startOfMonthWithOptions = convertToFP(startOfMonth, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfQuarter.js
var startOfQuarter5 = convertToFP(startOfQuarter, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfQuarterWithOptions.js
var _startOfQuarterWithOptions = convertToFP(startOfQuarter, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfSecond.js
var startOfSecond4 = convertToFP(startOfSecond, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfSecondWithOptions.js
var _startOfSecondWithOptions = convertToFP(startOfSecond, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfWeek.js
var startOfWeek12 = convertToFP(startOfWeek, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfWeekWithOptions.js
var _startOfWeekWithOptions = convertToFP(startOfWeek, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfWeekYear.js
var startOfWeekYear5 = convertToFP(startOfWeekYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfWeekYearWithOptions.js
var _startOfWeekYearWithOptions = convertToFP(startOfWeekYear, 2);
// ../../../../../../tmp/date-fns-jalali/fp/startOfYear.js
var startOfYear5 = convertToFP(startOfYear, 1);
// ../../../../../../tmp/date-fns-jalali/fp/startOfYearWithOptions.js
var _startOfYearWithOptions = convertToFP(startOfYear, 2);
// ../../../../../../tmp/date-fns-jalali/subMonths.js
function subMonths(date, amount, options) {
  return addMonths(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/sub.js
function sub(date, duration, options) {
  var _duration$years3 =







    duration.years,years = _duration$years3 === void 0 ? 0 : _duration$years3,_duration$months3 = duration.months,months2 = _duration$months3 === void 0 ? 0 : _duration$months3,_duration$weeks2 = duration.weeks,weeks = _duration$weeks2 === void 0 ? 0 : _duration$weeks2,_duration$days3 = duration.days,days2 = _duration$days3 === void 0 ? 0 : _duration$days3,_duration$hours3 = duration.hours,hours = _duration$hours3 === void 0 ? 0 : _duration$hours3,_duration$minutes3 = duration.minutes,minutes = _duration$minutes3 === void 0 ? 0 : _duration$minutes3,_duration$seconds3 = duration.seconds,seconds = _duration$seconds3 === void 0 ? 0 : _duration$seconds3;
  var withoutMonths = subMonths(date, months2 + years * 12, options);
  var withoutDays = subDays(withoutMonths, days2 + weeks * 7, options);
  var minutesToSub = minutes + hours * 60;
  var secondsToSub = seconds + minutesToSub * 60;
  var msToSub = secondsToSub * 1000;
  return constructFrom((options === null || options === void 0 ? void 0 : options.in) || date, +withoutDays - msToSub);
}

// ../../../../../../tmp/date-fns-jalali/fp/sub.js
var sub3 = convertToFP(sub, 2);
// ../../../../../../tmp/date-fns-jalali/subBusinessDays.js
function subBusinessDays(date, amount, options) {
  return addBusinessDays(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/subBusinessDays.js
var subBusinessDays3 = convertToFP(subBusinessDays, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subBusinessDaysWithOptions.js
var _subBusinessDaysWithOptions = convertToFP(subBusinessDays, 3);
// ../../../../../../tmp/date-fns-jalali/fp/subDays.js
var subDays5 = convertToFP(subDays, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subDaysWithOptions.js
var _subDaysWithOptions = convertToFP(subDays, 3);
// ../../../../../../tmp/date-fns-jalali/subHours.js
function subHours(date, amount, options) {
  return addHours(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/subHours.js
var subHours3 = convertToFP(subHours, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subHoursWithOptions.js
var _subHoursWithOptions = convertToFP(subHours, 3);
// ../../../../../../tmp/date-fns-jalali/fp/subISOWeekYears.js
var subISOWeekYears4 = convertToFP(subISOWeekYears, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subISOWeekYearsWithOptions.js
var _subISOWeekYearsWithOptions = convertToFP(subISOWeekYears, 3);
// ../../../../../../tmp/date-fns-jalali/subMilliseconds.js
function subMilliseconds(date, amount, options) {
  return addMilliseconds(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/subMilliseconds.js
var subMilliseconds3 = convertToFP(subMilliseconds, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subMillisecondsWithOptions.js
var _subMillisecondsWithOptions = convertToFP(subMilliseconds, 3);
// ../../../../../../tmp/date-fns-jalali/subMinutes.js
function subMinutes(date, amount, options) {
  return addMinutes(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/subMinutes.js
var subMinutes3 = convertToFP(subMinutes, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subMinutesWithOptions.js
var _subMinutesWithOptions = convertToFP(subMinutes, 3);
// ../../../../../../tmp/date-fns-jalali/fp/subMonths.js
var subMonths4 = convertToFP(subMonths, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subMonthsWithOptions.js
var _subMonthsWithOptions = convertToFP(subMonths, 3);
// ../../../../../../tmp/date-fns-jalali/subQuarters.js
function subQuarters(date, amount, options) {
  return addQuarters(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/subQuarters.js
var subQuarters3 = convertToFP(subQuarters, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subQuartersWithOptions.js
var _subQuartersWithOptions = convertToFP(subQuarters, 3);
// ../../../../../../tmp/date-fns-jalali/subSeconds.js
function subSeconds(date, amount, options) {
  return addSeconds(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/subSeconds.js
var subSeconds3 = convertToFP(subSeconds, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subSecondsWithOptions.js
var _subSecondsWithOptions = convertToFP(subSeconds, 3);
// ../../../../../../tmp/date-fns-jalali/subWeeks.js
function subWeeks(date, amount, options) {
  return addWeeks(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/subWeeks.js
var subWeeks3 = convertToFP(subWeeks, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subWeeksWithOptions.js
var _subWeeksWithOptions = convertToFP(subWeeks, 3);
// ../../../../../../tmp/date-fns-jalali/fp/subWithOptions.js
var _subWithOptions = convertToFP(sub, 3);
// ../../../../../../tmp/date-fns-jalali/subYears.js
function subYears(date, amount, options) {
  return addYears(date, -amount, options);
}

// ../../../../../../tmp/date-fns-jalali/fp/subYears.js
var subYears3 = convertToFP(subYears, 2);
// ../../../../../../tmp/date-fns-jalali/fp/subYearsWithOptions.js
var _subYearsWithOptions = convertToFP(subYears, 3);
// ../../../../../../tmp/date-fns-jalali/fp/toDate.js
var toDate108 = convertToFP(toDate, 2);
// ../../../../../../tmp/date-fns-jalali/fp/transpose.js
var transpose4 = convertToFP(transpose, 2);
// ../../../../../../tmp/date-fns-jalali/weeksToDays.js
function weeksToDays(weeks) {
  return Math.trunc(weeks * daysInWeek);
}

// ../../../../../../tmp/date-fns-jalali/fp/weeksToDays.js
var weeksToDays3 = convertToFP(weeksToDays, 1);
// ../../../../../../tmp/date-fns-jalali/yearsToDays.js
function yearsToDays(years) {
  return Math.trunc(years * daysInYear);
}

// ../../../../../../tmp/date-fns-jalali/fp/yearsToDays.js
var yearsToDays3 = convertToFP(yearsToDays, 1);
// ../../../../../../tmp/date-fns-jalali/yearsToMonths.js
function yearsToMonths(years) {
  return Math.trunc(years * monthsInYear);
}

// ../../../../../../tmp/date-fns-jalali/fp/yearsToMonths.js
var yearsToMonths3 = convertToFP(yearsToMonths, 1);
// ../../../../../../tmp/date-fns-jalali/yearsToQuarters.js
function yearsToQuarters(years) {
  return Math.trunc(years * quartersInYear);
}

// ../../../../../../tmp/date-fns-jalali/fp/yearsToQuarters.js
var yearsToQuarters3 = convertToFP(yearsToQuarters, 1);
// ../../../../../../tmp/date-fns-jalali/fp/cdn.js
window.dateFnsJalali = _objectSpread(_objectSpread({},
window.dateFnsJalali), {}, {
  fp: exports_fp });


//# debugId=9690E52E33A8447D64756E2164756E21

//# sourceMappingURL=cdn.js.map
})();