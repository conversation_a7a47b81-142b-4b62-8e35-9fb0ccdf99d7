(()=>{var J;function O(B){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},O(B)}function K(B,G){var H=Object.keys(B);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(B);G&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(B,Y).enumerable})),H.push.apply(H,X)}return H}function A(B){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?K(Object(H),!0).forEach(function(X){x(B,X,H[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(H)):K(Object(H)).forEach(function(X){Object.defineProperty(B,X,Object.getOwnPropertyDescriptor(H,X))})}return B}function x(B,G,H){if(G=N(G),G in B)Object.defineProperty(B,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else B[G]=H;return B}function N(B){var G=z(B,"string");return O(G)=="symbol"?G:String(G)}function z(B,G){if(O(B)!="object"||!B)return B;var H=B[Symbol.toPrimitive];if(H!==void 0){var X=H.call(B,G||"default");if(O(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var W=Object.defineProperty,HB=function B(G,H){for(var X in H)W(G,X,{get:H[X],enumerable:!0,configurable:!0,set:function Y(Z){return H[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062B\u0627\u0646\u06CC\u0647",other:"\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062B\u0627\u0646\u06CC\u0647"},xSeconds:{one:"1 \u062B\u0627\u0646\u06CC\u0647",other:"{{count}} \u062B\u0627\u0646\u06CC\u0647"},halfAMinute:"\u0646\u06CC\u0645 \u062F\u0642\u06CC\u0642\u0647",lessThanXMinutes:{one:"\u06A9\u0645\u062A\u0631 \u0627\u0632 \u06CC\u06A9 \u062F\u0642\u06CC\u0642\u0647",other:"\u06A9\u0645\u062A\u0631 \u0627\u0632 {{count}} \u062F\u0642\u06CC\u0642\u0647"},xMinutes:{one:"1 \u062F\u0642\u06CC\u0642\u0647",other:"{{count}} \u062F\u0642\u06CC\u0642\u0647"},aboutXHours:{one:"\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0639\u062A",other:"\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0639\u062A"},xHours:{one:"1 \u0633\u0627\u0639\u062A",other:"{{count}} \u0633\u0627\u0639\u062A"},xDays:{one:"1 \u0631\u0648\u0632",other:"{{count}} \u0631\u0648\u0632"},aboutXWeeks:{one:"\u062D\u062F\u0648\u062F 1 \u0647\u0641\u062A\u0647",other:"\u062D\u062F\u0648\u062F {{count}} \u0647\u0641\u062A\u0647"},xWeeks:{one:"1 \u0647\u0641\u062A\u0647",other:"{{count}} \u0647\u0641\u062A\u0647"},aboutXMonths:{one:"\u062D\u062F\u0648\u062F 1 \u0645\u0627\u0647",other:"\u062D\u062F\u0648\u062F {{count}} \u0645\u0627\u0647"},xMonths:{one:"1 \u0645\u0627\u0647",other:"{{count}} \u0645\u0627\u0647"},aboutXYears:{one:"\u062D\u062F\u0648\u062F 1 \u0633\u0627\u0644",other:"\u062D\u062F\u0648\u062F {{count}} \u0633\u0627\u0644"},xYears:{one:"1 \u0633\u0627\u0644",other:"{{count}} \u0633\u0627\u0644"},overXYears:{one:"\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 1 \u0633\u0627\u0644",other:"\u0628\u06CC\u0634\u062A\u0631 \u0627\u0632 {{count}} \u0633\u0627\u0644"},almostXYears:{one:"\u0646\u0632\u062F\u06CC\u06A9 1 \u0633\u0627\u0644",other:"\u0646\u0632\u062F\u06CC\u06A9 {{count}} \u0633\u0627\u0644"}},S=function B(G,H,X){var Y,Z=D[G];if(typeof Z==="string")Y=Z;else if(H===1)Y=Z.one;else Y=Z.other.replace("{{count}}",H.toString());if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"\u062F\u0631 "+Y;else return Y+" \u0642\u0628\u0644";return Y};function $(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):B.defaultWidth,X=B.formats[H]||B.formats[B.defaultWidth];return X}}var M={full:"EEEE do MMMM y",long:"do MMMM y",medium:"d MMM y",short:"yyyy/MM/dd"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} '\u062F\u0631' {{time}}",long:"{{date}} '\u062F\u0631' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee '\u06AF\u0630\u0634\u062A\u0647 \u062F\u0631' p",yesterday:"'\u062F\u06CC\u0631\u0648\u0632 \u062F\u0631' p",today:"'\u0627\u0645\u0631\u0648\u0632 \u062F\u0631' p",tomorrow:"'\u0641\u0631\u062F\u0627 \u062F\u0631' p",nextWeek:"eeee '\u062F\u0631' p",other:"P"},w=function B(G,H,X,Y){return j[G]};function Q(B){return function(G,H){var X=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",Y;if(X==="formatting"&&B.formattingValues){var Z=B.defaultFormattingWidth||B.defaultWidth,T=H!==null&&H!==void 0&&H.width?String(H.width):Z;Y=B.formattingValues[T]||B.formattingValues[Z]}else{var U=B.defaultWidth,I=H!==null&&H!==void 0&&H.width?String(H.width):B.defaultWidth;Y=B.values[I]||B.values[U]}var C=B.argumentCallback?B.argumentCallback(G):G;return Y[C]}}var _={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0647.","\u0628.\u0647."],wide:["\u0642\u0628\u0644 \u0627\u0632 \u0647\u062C\u0631\u062A","\u0628\u0639\u062F \u0627\u0632 \u0647\u062C\u0631\u062A"]},f={narrow:["1","2","3","4"],abbreviated:["\u0633\u200C\u06451","\u0633\u200C\u06452","\u0633\u200C\u06453","\u0633\u200C\u06454"],wide:["\u0633\u0647\u200C\u0645\u0627\u0647\u0647 1","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 2","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 3","\u0633\u0647\u200C\u0645\u0627\u0647\u0647 4"]},v={narrow:["\u0641\u0631","\u0627\u0631","\u062E\u0631","\u062A\u06CC","\u0645\u0631","\u0634\u0647","\u0645\u0647","\u0622\u0628","\u0622\u0630","\u062F\u06CC","\u0628\u0647","\u0627\u0633"],abbreviated:["\u0641\u0631\u0648","\u0627\u0631\u062F","\u062E\u0631\u062F","\u062A\u06CC\u0631","\u0645\u0631\u062F","\u0634\u0647\u0631","\u0645\u0647\u0631","\u0622\u0628\u0627","\u0622\u0630\u0631","\u062F\u06CC","\u0628\u0647\u0645","\u0627\u0633\u0641"],wide:["\u0641\u0631\u0648\u0631\u062F\u06CC\u0646","\u0627\u0631\u062F\u06CC\u0628\u0647\u0634\u062A","\u062E\u0631\u062F\u0627\u062F","\u062A\u06CC\u0631","\u0645\u0631\u062F\u0627\u062F","\u0634\u0647\u0631\u06CC\u0648\u0631","\u0645\u0647\u0631","\u0622\u0628\u0627\u0646","\u0622\u0630\u0631","\u062F\u06CC","\u0628\u0647\u0645\u0646","\u0627\u0633\u0641\u0646\u062F"]},F={narrow:["\u06CC","\u062F","\u0633","\u0686","\u067E","\u062C","\u0634"],short:["1\u0634","2\u0634","3\u0634","4\u0634","5\u0634","\u062C","\u0634"],abbreviated:["\u06CC\u06A9\u200C\u0634\u0646\u0628\u0647","\u062F\u0648\u0634\u0646\u0628\u0647","\u0633\u0647\u200C\u0634\u0646\u0628\u0647","\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647","\u067E\u0646\u062C\u200C\u0634\u0646\u0628\u0647","\u062C\u0645\u0639\u0647","\u0634\u0646\u0628\u0647"],wide:["\u06CC\u06A9\u200C\u0634\u0646\u0628\u0647","\u062F\u0648\u0634\u0646\u0628\u0647","\u0633\u0647\u200C\u0634\u0646\u0628\u0647","\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647","\u067E\u0646\u062C\u200C\u0634\u0646\u0628\u0647","\u062C\u0645\u0639\u0647","\u0634\u0646\u0628\u0647"]},P={narrow:{am:"\u0642",pm:"\u0628",midnight:"\u0646",noon:"\u0638",morning:"\u0635",afternoon:"\u0628.\u0638.",evening:"\u0639",night:"\u0634"},abbreviated:{am:"\u0642.\u0638.",pm:"\u0628.\u0638.",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"},wide:{am:"\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",pm:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"}},k={narrow:{am:"\u0642",pm:"\u0628",midnight:"\u0646",noon:"\u0638",morning:"\u0635",afternoon:"\u0628.\u0638.",evening:"\u0639",night:"\u0634"},abbreviated:{am:"\u0642.\u0638.",pm:"\u0628.\u0638.",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"},wide:{am:"\u0642\u0628\u0644\u200C\u0627\u0632\u0638\u0647\u0631",pm:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",midnight:"\u0646\u06CC\u0645\u0647\u200C\u0634\u0628",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u062D",afternoon:"\u0628\u0639\u062F\u0627\u0632\u0638\u0647\u0631",evening:"\u0639\u0635\u0631",night:"\u0634\u0628"}},b=function B(G,H){var X=Number(G);return X+"-\u0627\u0645"},h={ordinalNumber:b,era:Q({values:_,defaultWidth:"wide"}),quarter:Q({values:f,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:F,defaultWidth:"wide"}),dayPeriod:Q({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function q(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.width,Y=X&&B.matchPatterns[X]||B.matchPatterns[B.defaultMatchWidth],Z=G.match(Y);if(!Z)return null;var T=Z[0],U=X&&B.parsePatterns[X]||B.parsePatterns[B.defaultParseWidth],I=Array.isArray(U)?c(U,function(E){return E.test(T)}):m(U,function(E){return E.test(T)}),C;C=B.valueCallback?B.valueCallback(I):I,C=H.valueCallback?H.valueCallback(C):C;var GB=G.slice(T.length);return{value:C,rest:GB}}}function m(B,G){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&G(B[H]))return H;return}function c(B,G){for(var H=0;H<B.length;H++)if(G(B[H]))return H;return}function y(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=G.match(B.matchPattern);if(!X)return null;var Y=X[0],Z=G.match(B.parsePattern);if(!Z)return null;var T=B.valueCallback?B.valueCallback(Z[0]):Z[0];T=H.valueCallback?H.valueCallback(T):T;var U=G.slice(Y.length);return{value:T,rest:U}}}var p=/^(\d+)(-?ام)?/i,d=/\d+/i,g={narrow:/^(ق|ب)/i,abbreviated:/^(ق\.?\s?ه\.?|ب\.?\s?ه\.?|ه\.?)/i,wide:/^(قبل از هجرت|هجری شمسی|بعد از هجرت)/i},u={any:[/^قبل/i,/^بعد/i]},l={narrow:/^[1234]/i,abbreviated:/^(ف|Q|س‌م)[1234]/i,wide:/^(فصل|quarter|سه‌ماهه) [1234](-ام|ام)?/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^(فر|ار|خر|تی|مر|شه|مه|آب|آذ|دی|به|اس)/i,abbreviated:/^(فرو|ارد|خرد|تیر|مرد|شهر|مهر|آبا|آذر|دی|بهم|اسف)/i,wide:/^(فروردین|اردیبهشت|خرداد|تیر|مرداد|شهریور|مهر|آبان|آذر|دی|بهمن|اسفند)/i},s={narrow:[/^فر/i,/^ار/i,/^خر/i,/^تی/i,/^مر/i,/^شه/i,/^مه/i,/^آب/i,/^آذ/i,/^دی/i,/^به/i,/^اس/i],any:[/^فر/i,/^ار/i,/^خر/i,/^تی/i,/^مر/i,/^شه/i,/^مه/i,/^آب/i,/^آذ/i,/^دی/i,/^به/i,/^اس/i]},o={narrow:/^[شیدسچپج]/i,short:/^(ش|ج|1ش|2ش|3ش|4ش|5ش)/i,abbreviated:/^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i,wide:/^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i},r={narrow:[/^ی/i,/^دو/i,/^س/i,/^چ/i,/^پ/i,/^ج/i,/^ش/i],any:[/^(ی|1ش|یکشنبه)/i,/^(د|2ش|دوشنبه)/i,/^(س|3ش|سه‌شنبه)/i,/^(چ|4ش|چهارشنبه)/i,/^(پ|5ش|پنجشنبه)/i,/^(ج|جمعه)/i,/^(ش|شنبه)/i]},e={narrow:/^(ب|ق|ن|ظ|ص|ب.ظ.|ع|ش)/i,any:/^(ق.ظ.|ب.ظ.|قبل‌ازظهر|نیمه‌شب|ظهر|صبح|بعدازظهر|عصر|شب)/i},a={any:{am:/^(ق|ق.ظ.|قبل‌ازظهر)/i,pm:/^(ب|ب.ظ.|بعدازظهر)/i,midnight:/^(‌نیمه‌شب|ن)/i,noon:/^(ظ|ظهر)/i,morning:/^(ص|صبح)/i,afternoon:/^(ب|ب.ظ.|بعدازظهر)/i,evening:/^(ع|عصر)/i,night:/^(ش|شب)/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function B(G){return parseInt(G,10)}}),era:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},BB={code:"fa-IR",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:6,firstWeekContainsDate:1}};window.dateFnsJalali=A(A({},window.dateFnsJalali),{},{locale:A(A({},(J=window.dateFnsJalali)===null||J===void 0?void 0:J.locale),{},{faIR:BB})})})();

//# debugId=566125E8C1E1738164756E2164756E21
