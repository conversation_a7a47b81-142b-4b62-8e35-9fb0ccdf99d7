(()=>{var J;function O(G){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},O(G)}function x(G,H){var X=Object.keys(G);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(G);H&&(Y=Y.filter(function(Z){return Object.getOwnPropertyDescriptor(G,Z).enumerable})),X.push.apply(X,Y)}return X}function q(G){for(var H=1;H<arguments.length;H++){var X=arguments[H]!=null?arguments[H]:{};H%2?x(Object(X),!0).forEach(function(Y){E(G,Y,X[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(X)):x(Object(X)).forEach(function(Y){Object.defineProperty(G,Y,Object.getOwnPropertyDescriptor(X,Y))})}return G}function E(G,H,X){if(H=N(H),H in G)Object.defineProperty(G,H,{value:X,enumerable:!0,configurable:!0,writable:!0});else G[H]=X;return G}function N(G){var H=z(G,"string");return O(H)=="symbol"?H:String(H)}function z(G,H){if(O(G)!="object"||!G)return G;var X=G[Symbol.toPrimitive];if(X!==void 0){var Y=X.call(G,H||"default");if(O(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,XG=function G(H,X){for(var Y in X)W(H,Y,{get:X[Y],enumerable:!0,configurable:!0,set:function Z(C){return X[Y]=function(){return C}}})},D={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},M=function G(H,X,Y){var Z,C=D[H];if(typeof C==="string")Z=C;else if(X===1)Z=C.one;else Z=C.other.replace("{{count}}",X.toString());if(Y!==null&&Y!==void 0&&Y.addSuffix)if(Y.comparison&&Y.comparison>0)return"in "+Z;else return Z+" ago";return Z};function $(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=H.width?String(H.width):G.defaultWidth,Y=G.formats[X]||G.formats[G.defaultWidth];return Y}}var S={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},L={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:S,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},w=function G(H,X,Y,Z){return j[H]};function Q(G){return function(H,X){var Y=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",Z;if(Y==="formatting"&&G.formattingValues){var C=G.defaultFormattingWidth||G.defaultWidth,T=X!==null&&X!==void 0&&X.width?String(X.width):C;Z=G.formattingValues[T]||G.formattingValues[C]}else{var B=G.defaultWidth,A=X!==null&&X!==void 0&&X.width?String(X.width):G.defaultWidth;Z=G.values[A]||G.values[B]}var I=G.argumentCallback?G.argumentCallback(H):H;return Z[I]}}var _={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},F={narrow:["F","O","K","T","M","S","M","A","A","D","B","E"],abbreviated:["Far","Ord","Kho","Tir","Mor","Sha","Meh","Aba","Aza","Day","Bah","Esf"],wide:["Farvardin","Ordibehesht","Khordad","Tir","Mordad","Sharivar","Mehr","Aban","Azar","Day","Bahman","Esfand"]},v={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},P={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},k={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},h=function G(H,X){var Y=Number(H),Z=Y%100;if(Z>20||Z<10)switch(Z%10){case 1:return Y+"st";case 2:return Y+"nd";case 3:return Y+"rd"}return Y+"th"},b={ordinalNumber:h,era:Q({values:_,defaultWidth:"wide"}),quarter:Q({values:f,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:Q({values:F,defaultWidth:"wide"}),day:Q({values:v,defaultWidth:"wide"}),dayPeriod:Q({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function U(G){return function(H){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=X.width,Z=Y&&G.matchPatterns[Y]||G.matchPatterns[G.defaultMatchWidth],C=H.match(Z);if(!C)return null;var T=C[0],B=Y&&G.parsePatterns[Y]||G.parsePatterns[G.defaultParseWidth],A=Array.isArray(B)?m(B,function(K){return K.test(T)}):c(B,function(K){return K.test(T)}),I;I=G.valueCallback?G.valueCallback(A):A,I=X.valueCallback?X.valueCallback(I):I;var HG=H.slice(T.length);return{value:I,rest:HG}}}function c(G,H){for(var X in G)if(Object.prototype.hasOwnProperty.call(G,X)&&H(G[X]))return X;return}function m(G,H){for(var X=0;X<G.length;X++)if(H(G[X]))return X;return}function y(G){return function(H){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=H.match(G.matchPattern);if(!Y)return null;var Z=Y[0],C=H.match(G.parsePattern);if(!C)return null;var T=G.valueCallback?G.valueCallback(C[0]):C[0];T=X.valueCallback?X.valueCallback(T):T;var B=H.slice(Z.length);return{value:T,rest:B}}}var p=/^(\d+)(th|st|nd|rd)?/i,d=/\d+/i,g={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},u={any:[/^b/i,/^(a|c)/i]},l={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[foktmsadbe]/i,abbreviated:/^(far|ord|kho|tir|mor|sha|meh|aba|aza|day|bah|esf)/i,wide:/^(farvardin|ordibehesht|khordad|tir|mordad|sharivar|mehr|aban|azar|day|bahman|esfand)/i},s={narrow:[/^f/i,/^o/i,/^k/i,/^t/i,/^m/i,/^s/i,/^m/i,/^a/i,/^a/i,/^d/i,/^b/i,/^e/i],any:[/^f/i,/^o/i,/^kh/i,/^t/i,/^mo/i,/^s/i,/^me/i,/^ab/i,/^az/i,/^d/i,/^b/i,/^e/i]},o={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},r={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},a={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function G(H){return parseInt(H,10)}}),era:U({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:U({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:U({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:U({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:U({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},GG={code:"en-US",formatDistance:M,formatLong:V,formatRelative:w,localize:b,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFnsJalali=q(q({},window.dateFnsJalali),{},{locale:q(q({},(J=window.dateFnsJalali)===null||J===void 0?void 0:J.locale),{},{enUS:GG})})})();

//# debugId=D6D74C28D6D1CEEA64756E2164756E21
