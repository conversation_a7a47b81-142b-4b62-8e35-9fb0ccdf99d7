"use strict";
exports.lastDayOfMonth = lastDayOfMonth;
var _index = require("./toDate.cjs");

var _index2 = require("./_core/getMonth.cjs");
var _index3 = require("./_core/getFullYear.cjs");
var _index4 = require("./_core/setFullYear.cjs");

/**
 * The {@link lastDayOfMonth} function options.
 */

/**
 * @name lastDayOfMonth
 * @category Month Helpers
 * @summary Return the last day of a month for the given date.
 *
 * @description
 * Return the last day of a month for the given date.
 * The result will be in the local timezone.
 *
 * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).
 * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.
 *
 * @param date - The original date
 * @param options - An object with options
 *
 * @returns The last day of a month
 *
 * @example
 * // The last day of a month for 2 September 2014 11:55:00:
 * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))
 * //=> Tue Sep 30 2014 00:00:00
 */
function lastDayOfMonth(date, options) {
  const _date = (0, _index.toDate)(date, options?.in);
  const month = (0, _index2.getMonth)(_date);
  (0, _index4.setFullYear)(
    _date,
    (0, _index3.getFullYear)(_date),
    month + 1,
    0,
  );
  _date.setHours(0, 0, 0, 0);
  return (0, _index.toDate)(_date, options?.in);
}
