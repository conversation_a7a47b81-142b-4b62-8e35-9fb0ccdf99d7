{"name": "geist", "version": "1.5.1", "description": "Geist is a new font family for Vercel, created by Vercel in collaboration with Basement Studio.", "main": "./dist/font.js", "type": "module", "module": "./dist/font.js", "types": "./font.d.ts", "scripts": {"prepare": "cp ../../LICENSE.TXT ."}, "bugs": {"url": "https://github.com/vercel/geist-font/issues"}, "homepage": "https://vercel.com/font", "repository": {"type": "git", "url": "git+https://github.com/vercel/geist-font", "directory": "packages/next"}, "keywords": ["geist", "geist mono", "geist sans", "vercel font"], "exports": {"./font": {"types": "./dist/index.d.ts", "default": "./dist/font.js"}, "./font/mono": {"types": "./dist/index.d.ts", "default": "./dist/mono.js"}, "./font/mono-non-variable": {"types": "./dist/index.d.ts", "default": "./dist/mono-non-variable.js"}, "./font/sans": {"types": "./dist/index.d.ts", "default": "./dist/sans.js"}, "./font/sans-non-variable": {"types": "./dist/index.d.ts", "default": "./dist/sans-non-variable.js"}}, "license": "SIL OPEN FONT LICENSE", "peerDependencies": {"next": ">=13.2.0"}}