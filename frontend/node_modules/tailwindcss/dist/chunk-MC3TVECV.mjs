import{a as Et,b as L,c as H,d as E,e as at,f as de,g as Ue,h as Pt}from"./chunk-G32FJCSR.mjs";import{a as Rt}from"./chunk-HTB5LLOP.mjs";var Kt="4.1.14";var Ne=92,Ie=47,<PERSON>=42,Ot=34,_t=39,oi=58,ze=59,ne=10,Me=13,Se=32,Fe=9,Dt=123,lt=125,ft=40,jt=41,ai=91,li=93,Ut=45,st=64,si=33;function me(r,t){let i=t?.from?{file:t.from,code:r}:null;r[0]==="\uFEFF"&&(r=" "+r.slice(1));let e=[],o=[],s=[],l=null,d=null,u="",f="",g=0,m;for(let p=0;p<r.length;p++){let w=r.charCodeAt(p);if(!(w===Me&&(m=r.charCodeAt(p+1),m===ne)))if(w===Ne)u===""&&(g=p),u+=r.slice(p,p+2),p+=1;else if(w===Ie&&r.charCodeAt(p+1)===Le){let v=p;for(let x=p+2;x<r.length;x++)if(m=r.charCodeAt(x),m===Ne)x+=1;else if(m===Le&&r.charCodeAt(x+1)===Ie){p=x+1;break}let y=r.slice(v,p+1);if(y.charCodeAt(2)===si){let x=We(y.slice(2,-2));o.push(x),i&&(x.src=[i,v,p+1],x.dst=[i,v,p+1])}}else if(w===_t||w===Ot){let v=It(r,p,w);u+=r.slice(p,v+1),p=v}else{if((w===Se||w===ne||w===Fe)&&(m=r.charCodeAt(p+1))&&(m===Se||m===ne||m===Fe||m===Me&&(m=r.charCodeAt(p+2))&&m==ne))continue;if(w===ne){if(u.length===0)continue;m=u.charCodeAt(u.length-1),m!==Se&&m!==ne&&m!==Fe&&(u+=" ")}else if(w===Ut&&r.charCodeAt(p+1)===Ut&&u.length===0){let v="",y=p,x=-1;for(let C=p+2;C<r.length;C++)if(m=r.charCodeAt(C),m===Ne)C+=1;else if(m===_t||m===Ot)C=It(r,C,m);else if(m===Ie&&r.charCodeAt(C+1)===Le){for(let b=C+2;b<r.length;b++)if(m=r.charCodeAt(b),m===Ne)b+=1;else if(m===Le&&r.charCodeAt(b+1)===Ie){C=b+1;break}}else if(x===-1&&m===oi)x=u.length+C-y;else if(m===ze&&v.length===0){u+=r.slice(y,C),p=C;break}else if(m===ft)v+=")";else if(m===ai)v+="]";else if(m===Dt)v+="}";else if((m===lt||r.length-1===C)&&v.length===0){p=C-1,u+=r.slice(y,C);break}else(m===jt||m===li||m===lt)&&v.length>0&&r[C]===v[v.length-1]&&(v=v.slice(0,-1));let S=ut(u,x);if(!S)throw new Error("Invalid custom property, expected a value");i&&(S.src=[i,y,p],S.dst=[i,y,p]),l?l.nodes.push(S):e.push(S),u=""}else if(w===ze&&u.charCodeAt(0)===st)d=Te(u),i&&(d.src=[i,g,p],d.dst=[i,g,p]),l?l.nodes.push(d):e.push(d),u="",d=null;else if(w===ze&&f[f.length-1]!==")"){let v=ut(u);if(!v){if(u.length===0)continue;throw new Error(`Invalid declaration: \`${u.trim()}\``)}i&&(v.src=[i,g,p],v.dst=[i,g,p]),l?l.nodes.push(v):e.push(v),u=""}else if(w===Dt&&f[f.length-1]!==")")f+="}",d=Y(u.trim()),i&&(d.src=[i,g,p],d.dst=[i,g,p]),l&&l.nodes.push(d),s.push(l),l=d,u="",d=null;else if(w===lt&&f[f.length-1]!==")"){if(f==="")throw new Error("Missing opening {");if(f=f.slice(0,-1),u.length>0)if(u.charCodeAt(0)===st)d=Te(u),i&&(d.src=[i,g,p],d.dst=[i,g,p]),l?l.nodes.push(d):e.push(d),u="",d=null;else{let y=u.indexOf(":");if(l){let x=ut(u,y);if(!x)throw new Error(`Invalid declaration: \`${u.trim()}\``);i&&(x.src=[i,g,p],x.dst=[i,g,p]),l.nodes.push(x)}}let v=s.pop()??null;v===null&&l&&e.push(l),l=v,u="",d=null}else if(w===ft)f+=")",u+="(";else if(w===jt){if(f[f.length-1]!==")")throw new Error("Missing opening (");f=f.slice(0,-1),u+=")"}else{if(u.length===0&&(w===Se||w===ne||w===Fe))continue;u===""&&(g=p),u+=String.fromCharCode(w)}}}if(u.charCodeAt(0)===st){let p=Te(u);i&&(p.src=[i,g,r.length],p.dst=[i,g,r.length]),e.push(p)}if(f.length>0&&l){if(l.kind==="rule")throw new Error(`Missing closing } at ${l.selector}`);if(l.kind==="at-rule")throw new Error(`Missing closing } at ${l.name} ${l.params}`)}return o.length>0?o.concat(e):e}function Te(r,t=[]){let i=r,e="";for(let o=5;o<r.length;o++){let s=r.charCodeAt(o);if(s===Se||s===ft){i=r.slice(0,o),e=r.slice(o);break}}return z(i.trim(),e.trim(),t)}function ut(r,t=r.indexOf(":")){if(t===-1)return null;let i=r.indexOf("!important",t+1);return n(r.slice(0,t).trim(),r.slice(t+1,i===-1?r.length:i).trim(),i!==-1)}function It(r,t,i){let e;for(let o=t+1;o<r.length;o++)if(e=r.charCodeAt(o),e===Ne)o+=1;else{if(e===i)return o;if(e===ze&&(r.charCodeAt(o+1)===ne||r.charCodeAt(o+1)===Me&&r.charCodeAt(o+2)===ne))throw new Error(`Unterminated string: ${r.slice(t,o+1)+String.fromCharCode(i)}`);if(e===ne||e===Me&&r.charCodeAt(o+1)===ne)throw new Error(`Unterminated string: ${r.slice(t,o)+String.fromCharCode(i)}`)}return t}function fe(r){if(arguments.length===0)throw new TypeError("`CSS.escape` requires an argument.");let t=String(r),i=t.length,e=-1,o,s="",l=t.charCodeAt(0);if(i===1&&l===45)return"\\"+t;for(;++e<i;){if(o=t.charCodeAt(e),o===0){s+="\uFFFD";continue}if(o>=1&&o<=31||o===127||e===0&&o>=48&&o<=57||e===1&&o>=48&&o<=57&&l===45){s+="\\"+o.toString(16)+" ";continue}if(o>=128||o===45||o===95||o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122){s+=t.charAt(e);continue}s+="\\"+t.charAt(e)}return s}function ge(r){return r.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,t=>t.length>2?String.fromCodePoint(Number.parseInt(t.slice(1).trim(),16)):t[1])}var Ft=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-decoration-color","--text-decoration-thickness","--text-indent","--text-shadow","--text-underline-offset"]],["--grid-column",["--grid-column-start","--grid-column-end"]],["--grid-row",["--grid-row-start","--grid-row-end"]]]);function Lt(r,t){return(Ft.get(t)??[]).some(i=>r===i||r.startsWith(`${i}-`))}var Be=class{constructor(t=new Map,i=new Set([])){this.values=t;this.keyframes=i}prefix=null;get size(){return this.values.size}add(t,i,e=0,o){if(t.endsWith("-*")){if(i!=="initial")throw new Error(`Invalid theme value \`${i}\` for namespace \`${t}\``);t==="--*"?this.values.clear():this.clearNamespace(t.slice(0,-2),0)}if(e&4){let s=this.values.get(t);if(s&&!(s.options&4))return}i==="initial"?this.values.delete(t):this.values.set(t,{value:i,options:e,src:o})}keysInNamespaces(t){let i=[];for(let e of t){let o=`${e}-`;for(let s of this.values.keys())s.startsWith(o)&&s.indexOf("--",2)===-1&&(Lt(s,e)||i.push(s.slice(o.length)))}return i}get(t){for(let i of t){let e=this.values.get(i);if(e)return e.value}return null}hasDefault(t){return(this.getOptions(t)&4)===4}getOptions(t){return t=ge(this.#r(t)),this.values.get(t)?.options??0}entries(){return this.prefix?Array.from(this.values,t=>(t[0]=this.prefixKey(t[0]),t)):this.values.entries()}prefixKey(t){return this.prefix?`--${this.prefix}-${t.slice(2)}`:t}#r(t){return this.prefix?`--${t.slice(3+this.prefix.length)}`:t}clearNamespace(t,i){let e=Ft.get(t)??[];e:for(let o of this.values.keys())if(o.startsWith(t)){if(i!==0&&(this.getOptions(o)&i)!==i)continue;for(let s of e)if(o.startsWith(s))continue e;this.values.delete(o)}}#e(t,i){for(let e of i){let o=t!==null?`${e}-${t}`:e;if(!this.values.has(o))if(t!==null&&t.includes(".")){if(o=`${e}-${t.replaceAll(".","_")}`,!this.values.has(o))continue}else continue;if(!Lt(o,e))return o}return null}#t(t){let i=this.values.get(t);if(!i)return null;let e=null;return i.options&2&&(e=i.value),`var(${fe(this.prefixKey(t))}${e?`, ${e}`:""})`}markUsedVariable(t){let i=ge(this.#r(t)),e=this.values.get(i);if(!e)return!1;let o=e.options&16;return e.options|=16,!o}resolve(t,i,e=0){let o=this.#e(t,i);if(!o)return null;let s=this.values.get(o);return(e|s.options)&1?s.value:this.#t(o)}resolveValue(t,i){let e=this.#e(t,i);return e?this.values.get(e).value:null}resolveWith(t,i,e=[]){let o=this.#e(t,i);if(!o)return null;let s={};for(let d of e){let u=`${o}${d}`,f=this.values.get(u);f&&(f.options&1?s[d]=f.value:s[d]=this.#t(u))}let l=this.values.get(o);return l.options&1?[l.value,s]:[this.#t(o),s]}namespace(t){let i=new Map,e=`${t}-`;for(let[o,s]of this.values)o===t?i.set(null,s.value):o.startsWith(`${e}-`)?i.set(o.slice(t.length),s.value):o.startsWith(e)&&i.set(o.slice(e.length),s.value);return i}addKeyframes(t){this.keyframes.add(t)}getKeyframes(){return Array.from(this.keyframes)}};var W=class extends Map{constructor(i){super();this.factory=i}get(i){let e=super.get(i);return e===void 0&&(e=this.factory(i,this),this.set(i,e)),e}};function pt(r){return{kind:"word",value:r}}function ui(r,t){return{kind:"function",value:r,nodes:t}}function fi(r){return{kind:"separator",value:r}}function ee(r,t,i=null){for(let e=0;e<r.length;e++){let o=r[e],s=!1,l=0,d=t(o,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(r.splice(e,1),l=0):u.length===1?(r[e]=u[0],l=1):(r.splice(e,1,...u),l=u.length):r[e]=u)}})??0;if(s){d===0?e--:e+=l-1;continue}if(d===2)return 2;if(d!==1&&o.kind==="function"&&ee(o.nodes,t,o)===2)return 2}}function Z(r){let t="";for(let i of r)switch(i.kind){case"word":case"separator":{t+=i.value;break}case"function":t+=i.value+"("+Z(i.nodes)+")"}return t}var zt=92,ci=41,Mt=58,Wt=44,pi=34,Bt=61,qt=62,Gt=60,Jt=10,di=40,mi=39,Ht=47,Yt=32,Zt=9;function J(r){r=r.replaceAll(`\r
`,`
`);let t=[],i=[],e=null,o="",s;for(let l=0;l<r.length;l++){let d=r.charCodeAt(l);switch(d){case zt:{o+=r[l]+r[l+1],l++;break}case Mt:case Wt:case Bt:case qt:case Gt:case Jt:case Ht:case Yt:case Zt:{if(o.length>0){let m=pt(o);e?e.nodes.push(m):t.push(m),o=""}let u=l,f=l+1;for(;f<r.length&&(s=r.charCodeAt(f),!(s!==Mt&&s!==Wt&&s!==Bt&&s!==qt&&s!==Gt&&s!==Jt&&s!==Ht&&s!==Yt&&s!==Zt));f++);l=f-1;let g=fi(r.slice(u,f));e?e.nodes.push(g):t.push(g);break}case mi:case pi:{let u=l;for(let f=l+1;f<r.length;f++)if(s=r.charCodeAt(f),s===zt)f+=1;else if(s===d){l=f;break}o+=r.slice(u,l+1);break}case di:{let u=ui(o,[]);o="",e?e.nodes.push(u):t.push(u),i.push(u),e=u;break}case ci:{let u=i.pop();if(o.length>0){let f=pt(o);u?.nodes.push(f),o=""}i.length>0?e=i[i.length-1]:e=null;break}default:o+=String.fromCharCode(d)}}return o.length>0&&t.push(pt(o)),t}function qe(r){let t=[];return ee(J(r),i=>{if(!(i.kind!=="function"||i.value!=="var"))return ee(i.nodes,e=>{e.kind!=="word"||e.value[0]!=="-"||e.value[1]!=="-"||t.push(e.value)}),1}),t}var hi=64;function B(r,t=[]){return{kind:"rule",selector:r,nodes:t}}function z(r,t="",i=[]){return{kind:"at-rule",name:r,params:t,nodes:i}}function Y(r,t=[]){return r.charCodeAt(0)===hi?Te(r,t):B(r,t)}function n(r,t,i=!1){return{kind:"declaration",property:r,value:t,important:i}}function We(r){return{kind:"comment",value:r}}function ae(r,t){return{kind:"context",context:r,nodes:t}}function F(r){return{kind:"at-root",nodes:r}}function j(r,t,i=[],e={}){for(let o=0;o<r.length;o++){let s=r[o],l=i[i.length-1]??null;if(s.kind==="context"){if(j(s.nodes,t,i,{...e,...s.context})===2)return 2;continue}i.push(s);let d=!1,u=0,f=t(s,{parent:l,context:e,path:i,replaceWith(g){d||(d=!0,Array.isArray(g)?g.length===0?(r.splice(o,1),u=0):g.length===1?(r[o]=g[0],u=1):(r.splice(o,1,...g),u=g.length):(r[o]=g,u=1))}})??0;if(i.pop(),d){f===0?o--:o+=u-1;continue}if(f===2)return 2;if(f!==1&&"nodes"in s){i.push(s);let g=j(s.nodes,t,i,e);if(i.pop(),g===2)return 2}}}function Ge(r,t,i=[],e={}){for(let o=0;o<r.length;o++){let s=r[o],l=i[i.length-1]??null;if(s.kind==="rule"||s.kind==="at-rule")i.push(s),Ge(s.nodes,t,i,e),i.pop();else if(s.kind==="context"){Ge(s.nodes,t,i,{...e,...s.context});continue}i.push(s),t(s,{parent:l,context:e,path:i,replaceWith(d){Array.isArray(d)?d.length===0?r.splice(o,1):d.length===1?r[o]=d[0]:r.splice(o,1,...d):r[o]=d,o+=d.length-1}}),i.pop()}}function ve(r,t,i=3){let e=[],o=new Set,s=new W(()=>new Set),l=new W(()=>new Set),d=new Set,u=new Set,f=[],g=[],m=new W(()=>new Set);function p(v,y,x={},S=0){if(v.kind==="declaration"){if(v.property==="--tw-sort"||v.value===void 0||v.value===null)return;if(x.theme&&v.property[0]==="-"&&v.property[1]==="-"){if(v.value==="initial"){v.value=void 0;return}x.keyframes||s.get(y).add(v)}if(v.value.includes("var("))if(x.theme&&v.property[0]==="-"&&v.property[1]==="-")for(let C of qe(v.value))m.get(C).add(v.property);else t.trackUsedVariables(v.value);if(v.property==="animation")for(let C of Qt(v.value))u.add(C);i&2&&v.value.includes("color-mix(")&&l.get(y).add(v),y.push(v)}else if(v.kind==="rule"){let C=[];for(let R of v.nodes)p(R,C,x,S+1);let b={},O=new Set;for(let R of C){if(R.kind!=="declaration")continue;let _=`${R.property}:${R.value}:${R.important}`;b[_]??=[],b[_].push(R)}for(let R in b)for(let _=0;_<b[R].length-1;++_)O.add(b[R][_]);if(O.size>0&&(C=C.filter(R=>!O.has(R))),C.length===0)return;v.selector==="&"?y.push(...C):y.push({...v,nodes:C})}else if(v.kind==="at-rule"&&v.name==="@property"&&S===0){if(o.has(v.params))return;if(i&1){let b=v.params,O=null,R=!1;for(let D of v.nodes)D.kind==="declaration"&&(D.property==="initial-value"?O=D.value:D.property==="inherits"&&(R=D.value==="true"));let _=n(b,O??"initial");_.src=v.src,R?f.push(_):g.push(_)}o.add(v.params);let C={...v,nodes:[]};for(let b of v.nodes)p(b,C.nodes,x,S+1);y.push(C)}else if(v.kind==="at-rule"){v.name==="@keyframes"&&(x={...x,keyframes:!0});let C={...v,nodes:[]};for(let b of v.nodes)p(b,C.nodes,x,S+1);v.name==="@keyframes"&&x.theme&&d.add(C),(C.nodes.length>0||C.name==="@layer"||C.name==="@charset"||C.name==="@custom-media"||C.name==="@namespace"||C.name==="@import")&&y.push(C)}else if(v.kind==="at-root")for(let C of v.nodes){let b=[];p(C,b,x,0);for(let O of b)e.push(O)}else if(v.kind==="context"){if(v.context.reference)return;for(let C of v.nodes)p(C,y,{...x,...v.context},S)}else v.kind==="comment"&&y.push(v)}let w=[];for(let v of r)p(v,w,{},0);e:for(let[v,y]of s)for(let x of y){if(Xt(x.property,t.theme,m)){if(x.property.startsWith(t.theme.prefixKey("--animate-")))for(let b of Qt(x.value))u.add(b);continue}let C=v.indexOf(x);if(v.splice(C,1),v.length===0){let b=vi(w,O=>O.kind==="rule"&&O.nodes===v);if(!b||b.length===0)continue e;b.unshift({kind:"at-root",nodes:w});do{let O=b.pop();if(!O)break;let R=b[b.length-1];if(!R||R.kind!=="at-root"&&R.kind!=="at-rule")break;let _=R.nodes.indexOf(O);if(_===-1)break;R.nodes.splice(_,1)}while(!0);continue e}}for(let v of d)if(!u.has(v.params)){let y=e.indexOf(v);e.splice(y,1)}if(w=w.concat(e),i&2)for(let[v,y]of l)for(let x of y){let S=v.indexOf(x);if(S===-1||x.value==null)continue;let C=J(x.value),b=!1;if(ee(C,(_,{replaceWith:D})=>{if(_.kind!=="function"||_.value!=="color-mix")return;let G=!1,K=!1;if(ee(_.nodes,(U,{replaceWith:q})=>{if(U.kind=="word"&&U.value.toLowerCase()==="currentcolor"){K=!0,b=!0;return}let M=U,re=null,a=new Set;do{if(M.kind!=="function"||M.value!=="var")return;let c=M.nodes[0];if(!c||c.kind!=="word")return;let h=c.value;if(a.has(h)){G=!0;return}if(a.add(h),b=!0,re=t.theme.resolveValue(null,[c.value]),!re){G=!0;return}if(re.toLowerCase()==="currentcolor"){K=!0;return}re.startsWith("var(")?M=J(re)[0]:M=null}while(M);q({kind:"word",value:re})}),G||K){let U=_.nodes.findIndex(M=>M.kind==="separator"&&M.value.trim().includes(","));if(U===-1)return;let q=_.nodes.length>U?_.nodes[U+1]:null;if(!q)return;D(q)}else if(b){let U=_.nodes[2];U.kind==="word"&&(U.value==="oklab"||U.value==="oklch"||U.value==="lab"||U.value==="lch")&&(U.value="srgb")}}),!b)continue;let O={...x,value:Z(C)},R=Y("@supports (color: color-mix(in lab, red, red))",[x]);R.src=x.src,v.splice(S,1,O,R)}if(i&1){let v=[];if(f.length>0){let y=Y(":root, :host",f);y.src=f[0].src,v.push(y)}if(g.length>0){let y=Y("*, ::before, ::after, ::backdrop",g);y.src=g[0].src,v.push(y)}if(v.length>0){let y=w.findIndex(C=>!(C.kind==="comment"||C.kind==="at-rule"&&(C.name==="@charset"||C.name==="@import"))),x=z("@layer","properties",[]);x.src=v[0].src,w.splice(y<0?w.length:y,0,x);let S=Y("@layer properties",[z("@supports","((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))",v)]);S.src=v[0].src,S.nodes[0].src=v[0].src,w.push(S)}}return w}function ie(r,t){let i=0,e={file:null,code:""};function o(l,d=0){let u="",f="  ".repeat(d);if(l.kind==="declaration"){if(u+=`${f}${l.property}: ${l.value}${l.important?" !important":""};
`,t){i+=f.length;let g=i;i+=l.property.length,i+=2,i+=l.value?.length??0,l.important&&(i+=11);let m=i;i+=2,l.dst=[e,g,m]}}else if(l.kind==="rule"){if(u+=`${f}${l.selector} {
`,t){i+=f.length;let g=i;i+=l.selector.length,i+=1;let m=i;l.dst=[e,g,m],i+=2}for(let g of l.nodes)u+=o(g,d+1);u+=`${f}}
`,t&&(i+=f.length,i+=2)}else if(l.kind==="at-rule"){if(l.nodes.length===0){let g=`${f}${l.name} ${l.params};
`;if(t){i+=f.length;let m=i;i+=l.name.length,i+=1,i+=l.params.length;let p=i;i+=2,l.dst=[e,m,p]}return g}if(u+=`${f}${l.name}${l.params?` ${l.params} `:" "}{
`,t){i+=f.length;let g=i;i+=l.name.length,l.params&&(i+=1,i+=l.params.length),i+=1;let m=i;l.dst=[e,g,m],i+=2}for(let g of l.nodes)u+=o(g,d+1);u+=`${f}}
`,t&&(i+=f.length,i+=2)}else if(l.kind==="comment"){if(u+=`${f}/*${l.value}*/
`,t){i+=f.length;let g=i;i+=2+l.value.length+2;let m=i;l.dst=[e,g,m],i+=1}}else if(l.kind==="context"||l.kind==="at-root")return"";return u}let s="";for(let l of r)s+=o(l,0);return e.code=s,s}function vi(r,t){let i=[];return j(r,(e,{path:o})=>{if(t(e))return i=[...o],2}),i}function Xt(r,t,i,e=new Set){if(e.has(r)||(e.add(r),t.getOptions(r)&24))return!0;{let s=i.get(r)??[];for(let l of s)if(Xt(l,t,i,e))return!0}return!1}function Qt(r){return r.split(/[\s,]+/)}function ce(r){if(r.indexOf("(")===-1)return ke(r);let t=J(r);return mt(t),r=Z(t),r=Et(r),r}function ke(r,t=!1){let i="";for(let e=0;e<r.length;e++){let o=r[e];o==="\\"&&r[e+1]==="_"?(i+="_",e+=1):o==="_"&&!t?i+=" ":i+=o}return i}function mt(r){for(let t of r)switch(t.kind){case"function":{if(t.value==="url"||t.value.endsWith("_url")){t.value=ke(t.value);break}if(t.value==="var"||t.value.endsWith("_var")||t.value==="theme"||t.value.endsWith("_theme")){t.value=ke(t.value);for(let i=0;i<t.nodes.length;i++){if(i==0&&t.nodes[i].kind==="word"){t.nodes[i].value=ke(t.nodes[i].value,!0);continue}mt([t.nodes[i]])}break}t.value=ke(t.value),mt(t.nodes);break}case"separator":case"word":{t.value=ke(t.value);break}default:wi(t)}}function wi(r){throw new Error(`Unexpected value: ${r}`)}var gt=new Uint8Array(256);function se(r){let t=0,i=r.length;for(let e=0;e<i;e++){let o=r.charCodeAt(e);switch(o){case 92:e+=1;break;case 39:case 34:for(;++e<i;){let s=r.charCodeAt(e);if(s===92){e+=1;continue}if(s===o)break}break;case 40:gt[t]=41,t++;break;case 91:gt[t]=93,t++;break;case 123:break;case 93:case 125:case 41:if(t===0)return!1;t>0&&o===gt[t-1]&&t--;break;case 59:if(t===0)return!1;break}}return!0}var yi=58,er=45,tr=97,rr=122;function*ir(r,t){let i=L(r,":");if(t.theme.prefix){if(i.length===1||i[0]!==t.theme.prefix)return null;i.shift()}let e=i.pop(),o=[];for(let m=i.length-1;m>=0;--m){let p=t.parseVariant(i[m]);if(p===null)return;o.push(p)}let s=!1;e[e.length-1]==="!"?(s=!0,e=e.slice(0,-1)):e[0]==="!"&&(s=!0,e=e.slice(1)),t.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:o,important:s,raw:r});let[l,d=null,u]=L(e,"/");if(u)return;let f=d===null?null:ht(d);if(d!==null&&f===null)return;if(l[0]==="["){if(l[l.length-1]!=="]")return;let m=l.charCodeAt(1);if(m!==er&&!(m>=tr&&m<=rr))return;l=l.slice(1,-1);let p=l.indexOf(":");if(p===-1||p===0||p===l.length-1)return;let w=l.slice(0,p),v=ce(l.slice(p+1));if(!se(v))return;yield{kind:"arbitrary",property:w,value:v,modifier:f,variants:o,important:s,raw:r};return}let g;if(l[l.length-1]==="]"){let m=l.indexOf("-[");if(m===-1)return;let p=l.slice(0,m);if(!t.utilities.has(p,"functional"))return;let w=l.slice(m+1);g=[[p,w]]}else if(l[l.length-1]===")"){let m=l.indexOf("-(");if(m===-1)return;let p=l.slice(0,m);if(!t.utilities.has(p,"functional"))return;let w=l.slice(m+2,-1),v=L(w,":"),y=null;if(v.length===2&&(y=v[0],w=v[1]),w[0]!=="-"||w[1]!=="-"||!se(w))return;g=[[p,y===null?`[var(${w})]`:`[${y}:var(${w})]`]]}else g=or(l,m=>t.utilities.has(m,"functional"));for(let[m,p]of g){let w={kind:"functional",root:m,modifier:f,value:null,variants:o,important:s,raw:r};if(p===null){yield w;continue}{let v=p.indexOf("[");if(v!==-1){if(p[p.length-1]!=="]")return;let x=ce(p.slice(v+1,-1));if(!se(x))continue;let S="";for(let C=0;C<x.length;C++){let b=x.charCodeAt(C);if(b===yi){S=x.slice(0,C),x=x.slice(C+1);break}if(!(b===er||b>=tr&&b<=rr))break}if(x.length===0||x.trim().length===0)continue;w.value={kind:"arbitrary",dataType:S||null,value:x}}else{let x=d===null||w.modifier?.kind==="arbitrary"?null:`${p}/${d}`;w.value={kind:"named",value:p,fraction:x}}}yield w}}function ht(r){if(r[0]==="["&&r[r.length-1]==="]"){let t=ce(r.slice(1,-1));return!se(t)||t.length===0||t.trim().length===0?null:{kind:"arbitrary",value:t}}return r[0]==="("&&r[r.length-1]===")"?(r=r.slice(1,-1),r[0]!=="-"||r[1]!=="-"||!se(r)?null:(r=`var(${r})`,{kind:"arbitrary",value:ce(r)})):{kind:"named",value:r}}function nr(r,t){if(r[0]==="["&&r[r.length-1]==="]"){if(r[1]==="@"&&r.includes("&"))return null;let i=ce(r.slice(1,-1));if(!se(i)||i.length===0||i.trim().length===0)return null;let e=i[0]===">"||i[0]==="+"||i[0]==="~";return!e&&i[0]!=="@"&&!i.includes("&")&&(i=`&:is(${i})`),{kind:"arbitrary",selector:i,relative:e}}{let[i,e=null,o]=L(r,"/");if(o)return null;let s=or(i,l=>t.variants.has(l));for(let[l,d]of s)switch(t.variants.kind(l)){case"static":return d!==null||e!==null?null:{kind:"static",root:l};case"functional":{let u=e===null?null:ht(e);if(e!==null&&u===null)return null;if(d===null)return{kind:"functional",root:l,modifier:u,value:null};if(d[d.length-1]==="]"){if(d[0]!=="[")continue;let f=ce(d.slice(1,-1));return!se(f)||f.length===0||f.trim().length===0?null:{kind:"functional",root:l,modifier:u,value:{kind:"arbitrary",value:f}}}if(d[d.length-1]===")"){if(d[0]!=="(")continue;let f=ce(d.slice(1,-1));return!se(f)||f.length===0||f.trim().length===0||f[0]!=="-"||f[1]!=="-"?null:{kind:"functional",root:l,modifier:u,value:{kind:"arbitrary",value:`var(${f})`}}}return{kind:"functional",root:l,modifier:u,value:{kind:"named",value:d}}}case"compound":{if(d===null)return null;let u=t.parseVariant(d);if(u===null||!t.variants.compoundsWith(l,u))return null;let f=e===null?null:ht(e);return e!==null&&f===null?null:{kind:"compound",root:l,modifier:f,variant:u}}}}return null}function*or(r,t){t(r)&&(yield[r,null]);let i=r.lastIndexOf("-");for(;i>0;){let e=r.slice(0,i);if(t(e)){let o=[e,r.slice(i+1)];if(o[1]===""||o[0]==="@"&&t("@")&&r[i]==="-")break;yield o}i=r.lastIndexOf("-",i-1)}r[0]==="@"&&t("@")&&(yield["@",r.slice(1)])}function ar(r,t){let i=[];for(let o of t.variants)i.unshift(He(o));r.theme.prefix&&i.unshift(r.theme.prefix);let e="";if(t.kind==="static"&&(e+=t.root),t.kind==="functional"&&(e+=t.root,t.value))if(t.value.kind==="arbitrary"){if(t.value!==null){let o=wt(t.value.value),s=o?t.value.value.slice(4,-1):t.value.value,[l,d]=o?["(",")"]:["[","]"];t.value.dataType?e+=`-${l}${t.value.dataType}:${be(s)}${d}`:e+=`-${l}${be(s)}${d}`}}else t.value.kind==="named"&&(e+=`-${t.value.value}`);return t.kind==="arbitrary"&&(e+=`[${t.property}:${be(t.value)}]`),(t.kind==="arbitrary"||t.kind==="functional")&&(e+=lr(t.modifier)),t.important&&(e+="!"),i.push(e),i.join(":")}function lr(r){if(r===null)return"";let t=wt(r.value),i=t?r.value.slice(4,-1):r.value,[e,o]=t?["(",")"]:["[","]"];return r.kind==="arbitrary"?`/${e}${be(i)}${o}`:r.kind==="named"?`/${r.value}`:""}function He(r){if(r.kind==="static")return r.root;if(r.kind==="arbitrary")return`[${be(xi(r.selector))}]`;let t="";if(r.kind==="functional"){t+=r.root;let i=r.root!=="@";if(r.value)if(r.value.kind==="arbitrary"){let e=wt(r.value.value),o=e?r.value.value.slice(4,-1):r.value.value,[s,l]=e?["(",")"]:["[","]"];t+=`${i?"-":""}${s}${be(o)}${l}`}else r.value.kind==="named"&&(t+=`${i?"-":""}${r.value.value}`)}return r.kind==="compound"&&(t+=r.root,t+="-",t+=He(r.variant)),(r.kind==="functional"||r.kind==="compound")&&(t+=lr(r.modifier)),t}var ki=new W(r=>{let t=J(r),i=new Set;return ee(t,(e,{parent:o})=>{let s=o===null?t:o.nodes??[];if(e.kind==="word"&&(e.value==="+"||e.value==="-"||e.value==="*"||e.value==="/")){let l=s.indexOf(e)??-1;if(l===-1)return;let d=s[l-1];if(d?.kind!=="separator"||d.value!==" ")return;let u=s[l+1];if(u?.kind!=="separator"||u.value!==" ")return;i.add(d),i.add(u)}else e.kind==="separator"&&e.value.trim()==="/"?e.value="/":e.kind==="separator"&&e.value.length>0&&e.value.trim()===""?(s[0]===e||s[s.length-1]===e)&&i.add(e):e.kind==="separator"&&e.value.trim()===","&&(e.value=",")}),i.size>0&&ee(t,(e,{replaceWith:o})=>{i.has(e)&&(i.delete(e),o([]))}),vt(t),Z(t)});function be(r){return ki.get(r)}var bi=new W(r=>{let t=J(r);return t.length===3&&t[0].kind==="word"&&t[0].value==="&"&&t[1].kind==="separator"&&t[1].value===":"&&t[2].kind==="function"&&t[2].value==="is"?Z(t[2].nodes):r});function xi(r){return bi.get(r)}function vt(r){for(let t of r)switch(t.kind){case"function":{if(t.value==="url"||t.value.endsWith("_url")){t.value=Ee(t.value);break}if(t.value==="var"||t.value.endsWith("_var")||t.value==="theme"||t.value.endsWith("_theme")){t.value=Ee(t.value);for(let i=0;i<t.nodes.length;i++)vt([t.nodes[i]]);break}t.value=Ee(t.value),vt(t.nodes);break}case"separator":t.value=Ee(t.value);break;case"word":{(t.value[0]!=="-"||t.value[1]!=="-")&&(t.value=Ee(t.value));break}default:Ci(t)}}var Ai=new W(r=>{let t=J(r);return t.length===1&&t[0].kind==="function"&&t[0].value==="var"});function wt(r){return Ai.get(r)}function Ci(r){throw new Error(`Unexpected value: ${r}`)}function Ee(r){return r.replaceAll("_",String.raw`\_`).replaceAll(" ","_")}function we(r,t,i){if(r===t)return 0;let e=r.indexOf("("),o=t.indexOf("("),s=e===-1?r.replace(/[\d.]+/g,""):r.slice(0,e),l=o===-1?t.replace(/[\d.]+/g,""):t.slice(0,o),d=(s===l?0:s<l?-1:1)||(i==="asc"?parseInt(r)-parseInt(t):parseInt(t)-parseInt(r));return Number.isNaN(d)?r<t?-1:1:d}var Vi=new Set(["inset","inherit","initial","revert","unset"]),sr=/^-?(\d+|\.\d+)(.*?)$/g;function Re(r,t){return L(r,",").map(e=>{e=e.trim();let o=L(e," ").filter(f=>f.trim()!==""),s=null,l=null,d=null;for(let f of o)Vi.has(f)||(sr.test(f)?(l===null?l=f:d===null&&(d=f),sr.lastIndex=0):s===null&&(s=f));if(l===null||d===null)return e;let u=t(s??"currentcolor");return s!==null?e.replace(s,u):`${e} ${u}`}).join(", ")}var Ni=/^-?[a-z][a-zA-Z0-9/%._-]*$/,Si=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,Ze=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],yt=class{utilities=new W(()=>[]);completions=new Map;static(t,i){this.utilities.get(t).push({kind:"static",compileFn:i})}functional(t,i,e){this.utilities.get(t).push({kind:"functional",compileFn:i,options:e})}has(t,i){return this.utilities.has(t)&&this.utilities.get(t).some(e=>e.kind===i)}get(t){return this.utilities.has(t)?this.utilities.get(t):[]}getCompletions(t){return this.completions.get(t)?.()??[]}suggest(t,i){let e=this.completions.get(t);e?this.completions.set(t,()=>[...e?.(),...i?.()]):this.completions.set(t,i)}keys(t){let i=[];for(let[e,o]of this.utilities.entries())for(let s of o)if(s.kind===t){i.push(e);break}return i}};function V(r,t,i){return z("@property",r,[n("syntax",i?`"${i}"`:'"*"'),n("inherits","false"),...t?[n("initial-value",t)]:[]])}function Q(r,t){if(t===null)return r;let i=Number(t);return Number.isNaN(i)||(t=`${i*100}%`),t==="100%"?r:`color-mix(in oklab, ${r} ${t}, transparent)`}function fr(r,t){let i=Number(t);return Number.isNaN(i)||(t=`${i*100}%`),`oklab(from ${r} l a b / ${t})`}function X(r,t,i){if(!t)return r;if(t.kind==="arbitrary")return Q(r,t.value);let e=i.resolve(t.value,["--opacity"]);return e?Q(r,e):Ue(t.value)?Q(r,`${t.value}%`):null}function te(r,t,i){let e=null;switch(r.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentcolor";break}default:{e=t.resolve(r.value.value,i);break}}return e?X(e,r.modifier,t):null}var cr=/(\d+)_(\d+)/g;function pr(r){let t=new yt;function i(a,c){function*h(k){for(let $ of r.keysInNamespaces(k))yield $.replace(cr,(P,N,T)=>`${N}.${T}`)}let A=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];t.suggest(a,()=>{let k=[];for(let $ of c()){if(typeof $=="string"){k.push({values:[$],modifiers:[]});continue}let P=[...$.values??[],...h($.valueThemeKeys??[])],N=[...$.modifiers??[],...h($.modifierThemeKeys??[])];$.supportsFractions&&P.push(...A),$.hasDefaultValue&&P.unshift(null),k.push({supportsNegative:$.supportsNegative,values:P,modifiers:N})}return k})}function e(a,c){t.static(a,()=>c.map(h=>typeof h=="function"?h():n(h[0],h[1])))}function o(a,c){function h({negative:A}){return k=>{let $=null,P=null;if(k.value)if(k.value.kind==="arbitrary"){if(k.modifier)return;$=k.value.value,P=k.value.dataType}else{if($=r.resolve(k.value.fraction??k.value.value,c.themeKeys??[]),$===null&&c.supportsFractions&&k.value.fraction){let[N,T]=L(k.value.fraction,"/");if(!E(N)||!E(T))return;$=`calc(${k.value.fraction} * 100%)`}if($===null&&A&&c.handleNegativeBareValue){if($=c.handleNegativeBareValue(k.value),!$?.includes("/")&&k.modifier)return;if($!==null)return c.handle($,null)}if($===null&&c.handleBareValue&&($=c.handleBareValue(k.value),!$?.includes("/")&&k.modifier))return;if($===null&&!A&&c.staticValues&&!k.modifier){let N=c.staticValues[k.value.value];if(N)return N}}else{if(k.modifier)return;$=c.defaultValue!==void 0?c.defaultValue:r.resolve(null,c.themeKeys??[])}if($!==null)return c.handle(A?`calc(${$} * -1)`:$,P)}}if(c.supportsNegative&&t.functional(`-${a}`,h({negative:!0})),t.functional(a,h({negative:!1})),i(a,()=>[{supportsNegative:c.supportsNegative,valueThemeKeys:c.themeKeys??[],hasDefaultValue:c.defaultValue!==void 0&&c.defaultValue!==null,supportsFractions:c.supportsFractions}]),c.staticValues&&Object.keys(c.staticValues).length>0){let A=Object.keys(c.staticValues);i(a,()=>[{values:A}])}}function s(a,c){t.functional(a,h=>{if(!h.value)return;let A=null;if(h.value.kind==="arbitrary"?(A=h.value.value,A=X(A,h.modifier,r)):A=te(h,r,c.themeKeys),A!==null)return c.handle(A)}),i(a,()=>[{values:["current","inherit","transparent"],valueThemeKeys:c.themeKeys,modifiers:Array.from({length:21},(h,A)=>`${A*5}`)}])}function l(a,c,h,{supportsNegative:A=!1,supportsFractions:k=!1,staticValues:$}={}){A&&t.static(`-${a}-px`,()=>h("-1px")),t.static(`${a}-px`,()=>h("1px")),o(a,{themeKeys:c,supportsFractions:k,supportsNegative:A,defaultValue:null,handleBareValue:({value:P})=>{let N=r.resolve(null,["--spacing"]);return!N||!de(P)?null:`calc(${N} * ${P})`},handleNegativeBareValue:({value:P})=>{let N=r.resolve(null,["--spacing"]);return!N||!de(P)?null:`calc(${N} * -${P})`},handle:h,staticValues:$}),i(a,()=>[{values:r.get(["--spacing"])?Ze:[],supportsNegative:A,supportsFractions:k,valueThemeKeys:c}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip-path","inset(50%)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip-path","none"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[a,c]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${a}-auto`,[[c,"auto"]]),e(`${a}-full`,[[c,"100%"]]),e(`-${a}-full`,[[c,"-100%"]]),l(a,["--inset","--spacing"],h=>[n(c,h)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),o("z",{supportsNegative:!0,handleBareValue:({value:a})=>E(a)?a:null,themeKeys:["--z-index"],handle:a=>[n("z-index",a)],staticValues:{auto:[n("z-index","auto")]}}),i("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),o("order",{supportsNegative:!0,handleBareValue:({value:a})=>E(a)?a:null,themeKeys:["--order"],handle:a=>[n("order",a)],staticValues:{first:[n("order","-9999")],last:[n("order","9999")]}}),i("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(a,c)=>`${c+1}`),valueThemeKeys:["--order"]}]),o("col",{supportsNegative:!0,handleBareValue:({value:a})=>E(a)?a:null,themeKeys:["--grid-column"],handle:a=>[n("grid-column",a)],staticValues:{auto:[n("grid-column","auto")]}}),o("col-span",{handleBareValue:({value:a})=>E(a)?a:null,handle:a=>[n("grid-column",`span ${a} / span ${a}`)],staticValues:{full:[n("grid-column","1 / -1")]}}),o("col-start",{supportsNegative:!0,handleBareValue:({value:a})=>E(a)?a:null,themeKeys:["--grid-column-start"],handle:a=>[n("grid-column-start",a)],staticValues:{auto:[n("grid-column-start","auto")]}}),o("col-end",{supportsNegative:!0,handleBareValue:({value:a})=>E(a)?a:null,themeKeys:["--grid-column-end"],handle:a=>[n("grid-column-end",a)],staticValues:{auto:[n("grid-column-end","auto")]}}),i("col-span",()=>[{values:Array.from({length:12},(a,c)=>`${c+1}`),valueThemeKeys:[]}]),i("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(a,c)=>`${c+1}`),valueThemeKeys:["--grid-column-start"]}]),i("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(a,c)=>`${c+1}`),valueThemeKeys:["--grid-column-end"]}]),o("row",{supportsNegative:!0,handleBareValue:({value:a})=>E(a)?a:null,themeKeys:["--grid-row"],handle:a=>[n("grid-row",a)],staticValues:{auto:[n("grid-row","auto")]}}),o("row-span",{themeKeys:[],handleBareValue:({value:a})=>E(a)?a:null,handle:a=>[n("grid-row",`span ${a} / span ${a}`)],staticValues:{full:[n("grid-row","1 / -1")]}}),o("row-start",{supportsNegative:!0,handleBareValue:({value:a})=>E(a)?a:null,themeKeys:["--grid-row-start"],handle:a=>[n("grid-row-start",a)],staticValues:{auto:[n("grid-row-start","auto")]}}),o("row-end",{supportsNegative:!0,handleBareValue:({value:a})=>E(a)?a:null,themeKeys:["--grid-row-end"],handle:a=>[n("grid-row-end",a)],staticValues:{auto:[n("grid-row-end","auto")]}}),i("row-span",()=>[{values:Array.from({length:12},(a,c)=>`${c+1}`),valueThemeKeys:[]}]),i("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(a,c)=>`${c+1}`),valueThemeKeys:["--grid-row-start"]}]),i("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(a,c)=>`${c+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[a,c]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${a}-auto`,[[c,"auto"]]),l(a,["--margin","--spacing"],h=>[n(c,h)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),o("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:a})=>E(a)?a:null,handle:a=>[n("overflow","hidden"),n("display","-webkit-box"),n("-webkit-box-orient","vertical"),n("-webkit-line-clamp",a)],staticValues:{none:[n("overflow","visible"),n("display","block"),n("-webkit-box-orient","horizontal"),n("-webkit-line-clamp","unset")]}}),i("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),o("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:a})=>{if(a===null)return null;let[c,h]=L(a,"/");return!E(c)||!E(h)?null:a},handle:a=>[n("aspect-ratio",a)],staticValues:{auto:[n("aspect-ratio","auto")],square:[n("aspect-ratio","1 / 1")]}});for(let[a,c]of[["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${a}`,[["--tw-sort","size"],["width",c],["height",c]]),e(`w-${a}`,[["width",c]]),e(`h-${a}`,[["height",c]]),e(`min-w-${a}`,[["min-width",c]]),e(`min-h-${a}`,[["min-height",c]]),e(`max-w-${a}`,[["max-width",c]]),e(`max-h-${a}`,[["max-height",c]]);e("size-auto",[["--tw-sort","size"],["width","auto"],["height","auto"]]),e("w-auto",[["width","auto"]]),e("h-auto",[["height","auto"]]),e("min-w-auto",[["min-width","auto"]]),e("min-h-auto",[["min-height","auto"]]),e("h-lh",[["height","1lh"]]),e("min-h-lh",[["min-height","1lh"]]),e("max-h-lh",[["max-height","1lh"]]),e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),l("size",["--size","--spacing"],a=>[n("--tw-sort","size"),n("width",a),n("height",a)],{supportsFractions:!0});for(let[a,c,h]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])l(a,c,A=>[n(h,A)],{supportsFractions:!0});t.static("container",()=>{let a=[...r.namespace("--breakpoint").values()];a.sort((h,A)=>we(h,A,"asc"));let c=[n("--tw-sort","--tw-container-component"),n("width","100%")];for(let h of a)c.push(z("@media",`(width >= ${h})`,[n("max-width",h)]));return c}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),t.functional("flex",a=>{if(a.value){if(a.value.kind==="arbitrary")return a.modifier?void 0:[n("flex",a.value.value)];if(a.value.fraction){let[c,h]=L(a.value.fraction,"/");return!E(c)||!E(h)?void 0:[n("flex",`calc(${a.value.fraction} * 100%)`)]}if(E(a.value.value))return a.modifier?void 0:[n("flex",a.value.value)]}}),i("flex",()=>[{supportsFractions:!0},{values:Array.from({length:12},(a,c)=>`${c+1}`)}]),o("shrink",{defaultValue:"1",handleBareValue:({value:a})=>E(a)?a:null,handle:a=>[n("flex-shrink",a)]}),o("grow",{defaultValue:"1",handleBareValue:({value:a})=>E(a)?a:null,handle:a=>[n("flex-grow",a)]}),i("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),i("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),l("basis",["--flex-basis","--spacing","--container"],a=>[n("flex-basis",a)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let d=()=>F([V("--tw-border-spacing-x","0","<length>"),V("--tw-border-spacing-y","0","<length>")]);l("border-spacing",["--border-spacing","--spacing"],a=>[d(),n("--tw-border-spacing-x",a),n("--tw-border-spacing-y",a),n("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),l("border-spacing-x",["--border-spacing","--spacing"],a=>[d(),n("--tw-border-spacing-x",a),n("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),l("border-spacing-y",["--border-spacing","--spacing"],a=>[d(),n("--tw-border-spacing-y",a),n("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),o("origin",{themeKeys:["--transform-origin"],handle:a=>[n("transform-origin",a)],staticValues:{center:[n("transform-origin","center")],top:[n("transform-origin","top")],"top-right":[n("transform-origin","100% 0")],right:[n("transform-origin","100%")],"bottom-right":[n("transform-origin","100% 100%")],bottom:[n("transform-origin","bottom")],"bottom-left":[n("transform-origin","0 100%")],left:[n("transform-origin","0")],"top-left":[n("transform-origin","0 0")]}}),o("perspective-origin",{themeKeys:["--perspective-origin"],handle:a=>[n("perspective-origin",a)],staticValues:{center:[n("perspective-origin","center")],top:[n("perspective-origin","top")],"top-right":[n("perspective-origin","100% 0")],right:[n("perspective-origin","100%")],"bottom-right":[n("perspective-origin","100% 100%")],bottom:[n("perspective-origin","bottom")],"bottom-left":[n("perspective-origin","0 100%")],left:[n("perspective-origin","0")],"top-left":[n("perspective-origin","0 0")]}}),o("perspective",{themeKeys:["--perspective"],handle:a=>[n("perspective",a)],staticValues:{none:[n("perspective","none")]}});let u=()=>F([V("--tw-translate-x","0"),V("--tw-translate-y","0"),V("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[u,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[u,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),l("translate",["--translate","--spacing"],a=>[u(),n("--tw-translate-x",a),n("--tw-translate-y",a),n("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let a of["x","y"])e(`-translate-${a}-full`,[u,[`--tw-translate-${a}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${a}-full`,[u,[`--tw-translate-${a}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),l(`translate-${a}`,["--translate","--spacing"],c=>[u(),n(`--tw-translate-${a}`,c),n("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});l("translate-z",["--translate","--spacing"],a=>[u(),n("--tw-translate-z",a),n("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("translate-3d",[u,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let f=()=>F([V("--tw-scale-x","1"),V("--tw-scale-y","1"),V("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function g({negative:a}){return c=>{if(!c.value||c.modifier)return;let h;return c.value.kind==="arbitrary"?(h=c.value.value,h=a?`calc(${h} * -1)`:h,[n("scale",h)]):(h=r.resolve(c.value.value,["--scale"]),!h&&E(c.value.value)&&(h=`${c.value.value}%`),h?(h=a?`calc(${h} * -1)`:h,[f(),n("--tw-scale-x",h),n("--tw-scale-y",h),n("--tw-scale-z",h),n("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}t.functional("-scale",g({negative:!0})),t.functional("scale",g({negative:!1})),i("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let a of["x","y","z"])o(`scale-${a}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:c})=>E(c)?`${c}%`:null,handle:c=>[f(),n(`--tw-scale-${a}`,c),n("scale",`var(--tw-scale-x) var(--tw-scale-y)${a==="z"?" var(--tw-scale-z)":""}`)]}),i(`scale-${a}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[f,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function m({negative:a}){return c=>{if(!c.value||c.modifier)return;let h;if(c.value.kind==="arbitrary"){h=c.value.value;let A=c.value.dataType??H(h,["angle","vector"]);if(A==="vector")return[n("rotate",`${h} var(--tw-rotate)`)];if(A!=="angle")return[n("rotate",a?`calc(${h} * -1)`:h)]}else if(h=r.resolve(c.value.value,["--rotate"]),!h&&E(c.value.value)&&(h=`${c.value.value}deg`),!h)return;return[n("rotate",a?`calc(${h} * -1)`:h)]}}t.functional("-rotate",m({negative:!0})),t.functional("rotate",m({negative:!1})),i("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let a=["var(--tw-rotate-x,)","var(--tw-rotate-y,)","var(--tw-rotate-z,)","var(--tw-skew-x,)","var(--tw-skew-y,)"].join(" "),c=()=>F([V("--tw-rotate-x"),V("--tw-rotate-y"),V("--tw-rotate-z"),V("--tw-skew-x"),V("--tw-skew-y")]);for(let h of["x","y","z"])o(`rotate-${h}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:A})=>E(A)?`${A}deg`:null,handle:A=>[c(),n(`--tw-rotate-${h}`,`rotate${h.toUpperCase()}(${A})`),n("transform",a)]}),i(`rotate-${h}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);o("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>E(h)?`${h}deg`:null,handle:h=>[c(),n("--tw-skew-x",`skewX(${h})`),n("--tw-skew-y",`skewY(${h})`),n("transform",a)]}),o("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>E(h)?`${h}deg`:null,handle:h=>[c(),n("--tw-skew-x",`skewX(${h})`),n("transform",a)]}),o("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>E(h)?`${h}deg`:null,handle:h=>[c(),n("--tw-skew-y",`skewY(${h})`),n("transform",a)]}),i("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),t.functional("transform",h=>{if(h.modifier)return;let A=null;if(h.value?h.value.kind==="arbitrary"&&(A=h.value.value):A=a,A!==null)return[c(),n("transform",A)]}),i("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",a]]),e("transform-gpu",[["transform",`translateZ(0) ${a}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let a of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${a}`,[["cursor",a]]);o("cursor",{themeKeys:["--cursor"],handle:a=>[n("cursor",a)]});for(let a of["auto","none","manipulation"])e(`touch-${a}`,[["touch-action",a]]);let p=()=>F([V("--tw-pan-x"),V("--tw-pan-y"),V("--tw-pinch-zoom")]);for(let a of["x","left","right"])e(`touch-pan-${a}`,[p,["--tw-pan-x",`pan-${a}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let a of["y","up","down"])e(`touch-pan-${a}`,[p,["--tw-pan-y",`pan-${a}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[p,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let a of["none","text","all","auto"])e(`select-${a}`,[["-webkit-user-select",a],["user-select",a]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let w=()=>F([V("--tw-scroll-snap-strictness","proximity","*")]);for(let a of["x","y","both"])e(`snap-${a}`,[w,["scroll-snap-type",`${a} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[w,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[w,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[a,c]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])l(a,["--scroll-margin","--spacing"],h=>[n(c,h)],{supportsNegative:!0});for(let[a,c]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])l(a,["--scroll-padding","--spacing"],h=>[n(c,h)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),o("list",{themeKeys:["--list-style-type"],handle:a=>[n("list-style-type",a)],staticValues:{none:[n("list-style-type","none")],disc:[n("list-style-type","disc")],decimal:[n("list-style-type","decimal")]}}),o("list-image",{themeKeys:["--list-style-image"],handle:a=>[n("list-style-image",a)],staticValues:{none:[n("list-style-image","none")]}}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),o("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:a})=>E(a)?a:null,handle:a=>[n("columns",a)],staticValues:{auto:[n("columns","auto")]}}),i("columns",()=>[{values:Array.from({length:12},(a,c)=>`${c+1}`),valueThemeKeys:["--columns","--container"]}]);for(let a of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${a}`,[["break-before",a]]);for(let a of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${a}`,[["break-inside",a]]);for(let a of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${a}`,[["break-after",a]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),o("auto-cols",{themeKeys:["--grid-auto-columns"],handle:a=>[n("grid-auto-columns",a)],staticValues:{auto:[n("grid-auto-columns","auto")],min:[n("grid-auto-columns","min-content")],max:[n("grid-auto-columns","max-content")],fr:[n("grid-auto-columns","minmax(0, 1fr)")]}}),o("auto-rows",{themeKeys:["--grid-auto-rows"],handle:a=>[n("grid-auto-rows",a)],staticValues:{auto:[n("grid-auto-rows","auto")],min:[n("grid-auto-rows","min-content")],max:[n("grid-auto-rows","max-content")],fr:[n("grid-auto-rows","minmax(0, 1fr)")]}}),o("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:a})=>at(a)?`repeat(${a}, minmax(0, 1fr))`:null,handle:a=>[n("grid-template-columns",a)],staticValues:{none:[n("grid-template-columns","none")],subgrid:[n("grid-template-columns","subgrid")]}}),o("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:a})=>at(a)?`repeat(${a}, minmax(0, 1fr))`:null,handle:a=>[n("grid-template-rows",a)],staticValues:{none:[n("grid-template-rows","none")],subgrid:[n("grid-template-rows","subgrid")]}}),i("grid-cols",()=>[{values:Array.from({length:12},(a,c)=>`${c+1}`),valueThemeKeys:["--grid-template-columns"]}]),i("grid-rows",()=>[{values:Array.from({length:12},(a,c)=>`${c+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-center-safe",[["place-content","safe center"]]),e("place-content-end-safe",[["place-content","safe end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-center-safe",[["place-items","safe center"]]),e("place-items-end-safe",[["place-items","safe end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-center-safe",[["align-content","safe center"]]),e("content-end-safe",[["align-content","safe flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-center-safe",[["align-items","safe center"]]),e("items-end-safe",[["align-items","safe flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-baseline-last",[["align-items","last baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-center-safe",[["justify-content","safe center"]]),e("justify-end-safe",[["justify-content","safe flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-center-safe",[["justify-items","safe center"]]),e("justify-items-end-safe",[["justify-items","safe end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),l("gap",["--gap","--spacing"],a=>[n("gap",a)]),l("gap-x",["--gap","--spacing"],a=>[n("column-gap",a)]),l("gap-y",["--gap","--spacing"],a=>[n("row-gap",a)]),l("space-x",["--space","--spacing"],a=>[F([V("--tw-space-x-reverse","0")]),B(":where(& > :not(:last-child))",[n("--tw-sort","row-gap"),n("--tw-space-x-reverse","0"),n("margin-inline-start",`calc(${a} * var(--tw-space-x-reverse))`),n("margin-inline-end",`calc(${a} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),l("space-y",["--space","--spacing"],a=>[F([V("--tw-space-y-reverse","0")]),B(":where(& > :not(:last-child))",[n("--tw-sort","column-gap"),n("--tw-space-y-reverse","0"),n("margin-block-start",`calc(${a} * var(--tw-space-y-reverse))`),n("margin-block-end",`calc(${a} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>F([V("--tw-space-x-reverse","0")]),()=>B(":where(& > :not(:last-child))",[n("--tw-sort","row-gap"),n("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>F([V("--tw-space-y-reverse","0")]),()=>B(":where(& > :not(:last-child))",[n("--tw-sort","column-gap"),n("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),s("accent",{themeKeys:["--accent-color","--color"],handle:a=>[n("accent-color",a)]}),s("caret",{themeKeys:["--caret-color","--color"],handle:a=>[n("caret-color",a)]}),s("divide",{themeKeys:["--divide-color","--border-color","--color"],handle:a=>[B(":where(& > :not(:last-child))",[n("--tw-sort","divide-color"),n("border-color",a)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-end-safe",[["place-self","safe end"]]),e("place-self-center-safe",[["place-self","safe center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-end-safe",[["align-self","safe flex-end"]]),e("self-center-safe",[["align-self","safe center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("self-baseline-last",[["align-self","last baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-end-safe",[["justify-self","safe flex-end"]]),e("justify-self-center-safe",[["justify-self","safe center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let a of["auto","hidden","clip","visible","scroll"])e(`overflow-${a}`,[["overflow",a]]),e(`overflow-x-${a}`,[["overflow-x",a]]),e(`overflow-y-${a}`,[["overflow-y",a]]);for(let a of["auto","contain","none"])e(`overscroll-${a}`,[["overscroll-behavior",a]]),e(`overscroll-x-${a}`,[["overscroll-behavior-x",a]]),e(`overscroll-y-${a}`,[["overscroll-behavior-y",a]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]),e("wrap-anywhere",[["overflow-wrap","anywhere"]]),e("wrap-break-word",[["overflow-wrap","break-word"]]),e("wrap-normal",[["overflow-wrap","normal"]]);for(let[a,c]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])o(a,{themeKeys:["--radius"],handle:h=>c.map(A=>n(A,h)),staticValues:{none:c.map(h=>n(h,"0")),full:c.map(h=>n(h,"calc(infinity * 1px)"))}});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let c=function(h,A){t.functional(h,k=>{if(!k.value){if(k.modifier)return;let $=r.get(["--default-border-width"])??"1px",P=A.width($);return P?[a(),...P]:void 0}if(k.value.kind==="arbitrary"){let $=k.value.value;switch(k.value.dataType??H($,["color","line-width","length"])){case"line-width":case"length":{if(k.modifier)return;let N=A.width($);return N?[a(),...N]:void 0}default:return $=X($,k.modifier,r),$===null?void 0:A.color($)}}{let $=te(k,r,["--border-color","--color"]);if($)return A.color($)}{if(k.modifier)return;let $=r.resolve(k.value.value,["--border-width"]);if($){let P=A.width($);return P?[a(),...P]:void 0}if(E(k.value.value)){let P=A.width(`${k.value.value}px`);return P?[a(),...P]:void 0}}}),i(h,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(k,$)=>`${$*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var G=c;let a=()=>F([V("--tw-border-style","solid")]);c("border",{width:h=>[n("border-style","var(--tw-border-style)"),n("border-width",h)],color:h=>[n("border-color",h)]}),c("border-x",{width:h=>[n("border-inline-style","var(--tw-border-style)"),n("border-inline-width",h)],color:h=>[n("border-inline-color",h)]}),c("border-y",{width:h=>[n("border-block-style","var(--tw-border-style)"),n("border-block-width",h)],color:h=>[n("border-block-color",h)]}),c("border-s",{width:h=>[n("border-inline-start-style","var(--tw-border-style)"),n("border-inline-start-width",h)],color:h=>[n("border-inline-start-color",h)]}),c("border-e",{width:h=>[n("border-inline-end-style","var(--tw-border-style)"),n("border-inline-end-width",h)],color:h=>[n("border-inline-end-color",h)]}),c("border-t",{width:h=>[n("border-top-style","var(--tw-border-style)"),n("border-top-width",h)],color:h=>[n("border-top-color",h)]}),c("border-r",{width:h=>[n("border-right-style","var(--tw-border-style)"),n("border-right-width",h)],color:h=>[n("border-right-color",h)]}),c("border-b",{width:h=>[n("border-bottom-style","var(--tw-border-style)"),n("border-bottom-width",h)],color:h=>[n("border-bottom-color",h)]}),c("border-l",{width:h=>[n("border-left-style","var(--tw-border-style)"),n("border-left-width",h)],color:h=>[n("border-left-color",h)]}),o("divide-x",{defaultValue:r.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>E(h)?`${h}px`:null,handle:h=>[F([V("--tw-divide-x-reverse","0")]),B(":where(& > :not(:last-child))",[n("--tw-sort","divide-x-width"),a(),n("--tw-divide-x-reverse","0"),n("border-inline-style","var(--tw-border-style)"),n("border-inline-start-width",`calc(${h} * var(--tw-divide-x-reverse))`),n("border-inline-end-width",`calc(${h} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),o("divide-y",{defaultValue:r.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>E(h)?`${h}px`:null,handle:h=>[F([V("--tw-divide-y-reverse","0")]),B(":where(& > :not(:last-child))",[n("--tw-sort","divide-y-width"),a(),n("--tw-divide-y-reverse","0"),n("border-bottom-style","var(--tw-border-style)"),n("border-top-style","var(--tw-border-style)"),n("border-top-width",`calc(${h} * var(--tw-divide-y-reverse))`),n("border-bottom-width",`calc(${h} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),i("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),i("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>F([V("--tw-divide-x-reverse","0")]),()=>B(":where(& > :not(:last-child))",[n("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>F([V("--tw-divide-y-reverse","0")]),()=>B(":where(& > :not(:last-child))",[n("--tw-divide-y-reverse","1")])]);for(let h of["solid","dashed","dotted","double","none"])e(`divide-${h}`,[()=>B(":where(& > :not(:last-child))",[n("--tw-sort","divide-style"),n("--tw-border-style",h),n("border-style",h)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),o("bg-size",{handle(a){if(a)return[n("background-size",a)]}}),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-top",[["background-position","top"]]),e("bg-top-left",[["background-position","left top"]]),e("bg-top-right",[["background-position","right top"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-bottom-left",[["background-position","left bottom"]]),e("bg-bottom-right",[["background-position","right bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-right",[["background-position","right"]]),e("bg-center",[["background-position","center"]]),o("bg-position",{handle(a){if(a)return[n("background-position",a)]}}),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let h=function($){let P="in oklab";if($?.kind==="named")switch($.value){case"longer":case"shorter":case"increasing":case"decreasing":P=`in oklch ${$.value} hue`;break;default:P=`in ${$.value}`}else $?.kind==="arbitrary"&&(P=$.value);return P},A=function({negative:$}){return P=>{if(!P.value)return;if(P.value.kind==="arbitrary"){if(P.modifier)return;let I=P.value.value;switch(P.value.dataType??H(I,["angle"])){case"angle":return I=$?`calc(${I} * -1)`:`${I}`,[n("--tw-gradient-position",I),n("background-image",`linear-gradient(var(--tw-gradient-stops,${I}))`)];default:return $?void 0:[n("--tw-gradient-position",I),n("background-image",`linear-gradient(var(--tw-gradient-stops,${I}))`)]}}let N=P.value.value;if(!$&&c.has(N))N=c.get(N);else if(E(N))N=$?`calc(${N}deg * -1)`:`${N}deg`;else return;let T=h(P.modifier);return[n("--tw-gradient-position",`${N}`),Y("@supports (background-image: linear-gradient(in lab, red, red))",[n("--tw-gradient-position",`${N} ${T}`)]),n("background-image","linear-gradient(var(--tw-gradient-stops))")]}},k=function({negative:$}){return P=>{if(P.value?.kind==="arbitrary"){if(P.modifier)return;let I=P.value.value;return[n("--tw-gradient-position",I),n("background-image",`conic-gradient(var(--tw-gradient-stops,${I}))`)]}let N=h(P.modifier);if(!P.value)return[n("--tw-gradient-position",N),n("background-image","conic-gradient(var(--tw-gradient-stops))")];let T=P.value.value;if(E(T))return T=$?`calc(${T}deg * -1)`:`${T}deg`,[n("--tw-gradient-position",`from ${T} ${N}`),n("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var K=h,U=A,q=k;let a=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],c=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);t.functional("-bg-linear",A({negative:!0})),t.functional("bg-linear",A({negative:!1})),i("bg-linear",()=>[{values:[...c.keys()],modifiers:a},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:a}]),t.functional("-bg-conic",k({negative:!0})),t.functional("bg-conic",k({negative:!1})),i("bg-conic",()=>[{hasDefaultValue:!0,modifiers:a},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:a}]),t.functional("bg-radial",$=>{if(!$.value){let P=h($.modifier);return[n("--tw-gradient-position",P),n("background-image","radial-gradient(var(--tw-gradient-stops))")]}if($.value.kind==="arbitrary"){if($.modifier)return;let P=$.value.value;return[n("--tw-gradient-position",P),n("background-image",`radial-gradient(var(--tw-gradient-stops,${P}))`)]}}),i("bg-radial",()=>[{hasDefaultValue:!0,modifiers:a}])}t.functional("bg",a=>{if(a.value){if(a.value.kind==="arbitrary"){let c=a.value.value;switch(a.value.dataType??H(c,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return a.modifier?void 0:[n("background-position",c)];case"bg-size":case"length":case"size":return a.modifier?void 0:[n("background-size",c)];case"image":case"url":return a.modifier?void 0:[n("background-image",c)];default:return c=X(c,a.modifier,r),c===null?void 0:[n("background-color",c)]}}{let c=te(a,r,["--background-color","--color"]);if(c)return[n("background-color",c)]}{if(a.modifier)return;let c=r.resolve(a.value.value,["--background-image"]);if(c)return[n("background-image",c)]}}}),i("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(a,c)=>`${c*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let v=()=>F([V("--tw-gradient-position"),V("--tw-gradient-from","#0000","<color>"),V("--tw-gradient-via","#0000","<color>"),V("--tw-gradient-to","#0000","<color>"),V("--tw-gradient-stops"),V("--tw-gradient-via-stops"),V("--tw-gradient-from-position","0%","<length-percentage>"),V("--tw-gradient-via-position","50%","<length-percentage>"),V("--tw-gradient-to-position","100%","<length-percentage>")]);function y(a,c){t.functional(a,h=>{if(h.value){if(h.value.kind==="arbitrary"){let A=h.value.value;switch(h.value.dataType??H(A,["color","length","percentage"])){case"length":case"percentage":return h.modifier?void 0:c.position(A);default:return A=X(A,h.modifier,r),A===null?void 0:c.color(A)}}{let A=te(h,r,["--background-color","--color"]);if(A)return c.color(A)}{if(h.modifier)return;let A=r.resolve(h.value.value,["--gradient-color-stop-positions"]);if(A)return c.position(A);if(h.value.value[h.value.value.length-1]==="%"&&E(h.value.value.slice(0,-1)))return c.position(h.value.value)}}}),i(a,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)},{values:Array.from({length:21},(h,A)=>`${A*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}y("from",{color:a=>[v(),n("--tw-sort","--tw-gradient-from"),n("--tw-gradient-from",a),n("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:a=>[v(),n("--tw-gradient-from-position",a)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),y("via",{color:a=>[v(),n("--tw-sort","--tw-gradient-via"),n("--tw-gradient-via",a),n("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),n("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:a=>[v(),n("--tw-gradient-via-position",a)]}),y("to",{color:a=>[v(),n("--tw-sort","--tw-gradient-to"),n("--tw-gradient-to",a),n("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:a=>[v(),n("--tw-gradient-to-position",a)]}),e("mask-none",[["mask-image","none"]]),t.functional("mask",a=>{if(!a.value||a.modifier||a.value.kind!=="arbitrary")return;let c=a.value.value;switch(a.value.dataType??H(c,["image","percentage","position","bg-size","length","url"])){case"percentage":case"position":return a.modifier?void 0:[n("mask-position",c)];case"bg-size":case"length":case"size":return[n("mask-size",c)];case"image":case"url":default:return[n("mask-image",c)]}}),e("mask-add",[["mask-composite","add"]]),e("mask-subtract",[["mask-composite","subtract"]]),e("mask-intersect",[["mask-composite","intersect"]]),e("mask-exclude",[["mask-composite","exclude"]]),e("mask-alpha",[["mask-mode","alpha"]]),e("mask-luminance",[["mask-mode","luminance"]]),e("mask-match",[["mask-mode","match-source"]]),e("mask-type-alpha",[["mask-type","alpha"]]),e("mask-type-luminance",[["mask-type","luminance"]]),e("mask-auto",[["mask-size","auto"]]),e("mask-cover",[["mask-size","cover"]]),e("mask-contain",[["mask-size","contain"]]),o("mask-size",{handle(a){if(a)return[n("mask-size",a)]}}),e("mask-top",[["mask-position","top"]]),e("mask-top-left",[["mask-position","left top"]]),e("mask-top-right",[["mask-position","right top"]]),e("mask-bottom",[["mask-position","bottom"]]),e("mask-bottom-left",[["mask-position","left bottom"]]),e("mask-bottom-right",[["mask-position","right bottom"]]),e("mask-left",[["mask-position","left"]]),e("mask-right",[["mask-position","right"]]),e("mask-center",[["mask-position","center"]]),o("mask-position",{handle(a){if(a)return[n("mask-position",a)]}}),e("mask-repeat",[["mask-repeat","repeat"]]),e("mask-no-repeat",[["mask-repeat","no-repeat"]]),e("mask-repeat-x",[["mask-repeat","repeat-x"]]),e("mask-repeat-y",[["mask-repeat","repeat-y"]]),e("mask-repeat-round",[["mask-repeat","round"]]),e("mask-repeat-space",[["mask-repeat","space"]]),e("mask-clip-border",[["mask-clip","border-box"]]),e("mask-clip-padding",[["mask-clip","padding-box"]]),e("mask-clip-content",[["mask-clip","content-box"]]),e("mask-clip-fill",[["mask-clip","fill-box"]]),e("mask-clip-stroke",[["mask-clip","stroke-box"]]),e("mask-clip-view",[["mask-clip","view-box"]]),e("mask-no-clip",[["mask-clip","no-clip"]]),e("mask-origin-border",[["mask-origin","border-box"]]),e("mask-origin-padding",[["mask-origin","padding-box"]]),e("mask-origin-content",[["mask-origin","content-box"]]),e("mask-origin-fill",[["mask-origin","fill-box"]]),e("mask-origin-stroke",[["mask-origin","stroke-box"]]),e("mask-origin-view",[["mask-origin","view-box"]]);let x=()=>F([V("--tw-mask-linear","linear-gradient(#fff, #fff)"),V("--tw-mask-radial","linear-gradient(#fff, #fff)"),V("--tw-mask-conic","linear-gradient(#fff, #fff)")]);function S(a,c){t.functional(a,h=>{if(h.value){if(h.value.kind==="arbitrary"){let A=h.value.value;switch(h.value.dataType??H(A,["length","percentage","color"])){case"color":return A=X(A,h.modifier,r),A===null?void 0:c.color(A);case"percentage":return h.modifier||!E(A.slice(0,-1))?void 0:c.position(A);default:return h.modifier?void 0:c.position(A)}}{let A=te(h,r,["--background-color","--color"]);if(A)return c.color(A)}{if(h.modifier)return;let A=H(h.value.value,["number","percentage"]);if(!A)return;switch(A){case"number":{let k=r.resolve(null,["--spacing"]);return!k||!de(h.value.value)?void 0:c.position(`calc(${k} * ${h.value.value})`)}case"percentage":return E(h.value.value.slice(0,-1))?c.position(h.value.value):void 0;default:return}}}}),i(a,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)},{values:Array.from({length:21},(h,A)=>`${A*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}]),i(a,()=>[{values:Array.from({length:21},(h,A)=>`${A*5}%`)},{values:r.get(["--spacing"])?Ze:[]},{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)}])}let C=()=>F([V("--tw-mask-left","linear-gradient(#fff, #fff)"),V("--tw-mask-right","linear-gradient(#fff, #fff)"),V("--tw-mask-bottom","linear-gradient(#fff, #fff)"),V("--tw-mask-top","linear-gradient(#fff, #fff)")]);function b(a,c,h){S(a,{color(A){let k=[x(),C(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let $ of["top","right","bottom","left"])h[$]&&(k.push(n(`--tw-mask-${$}`,`linear-gradient(to ${$}, var(--tw-mask-${$}-from-color) var(--tw-mask-${$}-from-position), var(--tw-mask-${$}-to-color) var(--tw-mask-${$}-to-position))`)),k.push(F([V(`--tw-mask-${$}-from-position`,"0%"),V(`--tw-mask-${$}-to-position`,"100%"),V(`--tw-mask-${$}-from-color`,"black"),V(`--tw-mask-${$}-to-color`,"transparent")])),k.push(n(`--tw-mask-${$}-${c}-color`,A)));return k},position(A){let k=[x(),C(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let $ of["top","right","bottom","left"])h[$]&&(k.push(n(`--tw-mask-${$}`,`linear-gradient(to ${$}, var(--tw-mask-${$}-from-color) var(--tw-mask-${$}-from-position), var(--tw-mask-${$}-to-color) var(--tw-mask-${$}-to-position))`)),k.push(F([V(`--tw-mask-${$}-from-position`,"0%"),V(`--tw-mask-${$}-to-position`,"100%"),V(`--tw-mask-${$}-from-color`,"black"),V(`--tw-mask-${$}-to-color`,"transparent")])),k.push(n(`--tw-mask-${$}-${c}-position`,A)));return k}})}b("mask-x-from","from",{top:!1,right:!0,bottom:!1,left:!0}),b("mask-x-to","to",{top:!1,right:!0,bottom:!1,left:!0}),b("mask-y-from","from",{top:!0,right:!1,bottom:!0,left:!1}),b("mask-y-to","to",{top:!0,right:!1,bottom:!0,left:!1}),b("mask-t-from","from",{top:!0,right:!1,bottom:!1,left:!1}),b("mask-t-to","to",{top:!0,right:!1,bottom:!1,left:!1}),b("mask-r-from","from",{top:!1,right:!0,bottom:!1,left:!1}),b("mask-r-to","to",{top:!1,right:!0,bottom:!1,left:!1}),b("mask-b-from","from",{top:!1,right:!1,bottom:!0,left:!1}),b("mask-b-to","to",{top:!1,right:!1,bottom:!0,left:!1}),b("mask-l-from","from",{top:!1,right:!1,bottom:!1,left:!0}),b("mask-l-to","to",{top:!1,right:!1,bottom:!1,left:!0});let O=()=>F([V("--tw-mask-linear-position","0deg"),V("--tw-mask-linear-from-position","0%"),V("--tw-mask-linear-to-position","100%"),V("--tw-mask-linear-from-color","black"),V("--tw-mask-linear-to-color","transparent")]);o("mask-linear",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(a){return E(a.value)?`calc(1deg * ${a.value})`:null},handleNegativeBareValue(a){return E(a.value)?`calc(1deg * -${a.value})`:null},handle:a=>[x(),O(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"),n("--tw-mask-linear-position",a)]}),i("mask-linear",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),S("mask-linear-from",{color:a=>[x(),O(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),n("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),n("--tw-mask-linear-from-color",a)],position:a=>[x(),O(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),n("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),n("--tw-mask-linear-from-position",a)]}),S("mask-linear-to",{color:a=>[x(),O(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),n("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),n("--tw-mask-linear-to-color",a)],position:a=>[x(),O(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),n("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),n("--tw-mask-linear-to-position",a)]});let R=()=>F([V("--tw-mask-radial-from-position","0%"),V("--tw-mask-radial-to-position","100%"),V("--tw-mask-radial-from-color","black"),V("--tw-mask-radial-to-color","transparent"),V("--tw-mask-radial-shape","ellipse"),V("--tw-mask-radial-size","farthest-corner"),V("--tw-mask-radial-position","center")]);e("mask-circle",[["--tw-mask-radial-shape","circle"]]),e("mask-ellipse",[["--tw-mask-radial-shape","ellipse"]]),e("mask-radial-closest-side",[["--tw-mask-radial-size","closest-side"]]),e("mask-radial-farthest-side",[["--tw-mask-radial-size","farthest-side"]]),e("mask-radial-closest-corner",[["--tw-mask-radial-size","closest-corner"]]),e("mask-radial-farthest-corner",[["--tw-mask-radial-size","farthest-corner"]]),e("mask-radial-at-top",[["--tw-mask-radial-position","top"]]),e("mask-radial-at-top-left",[["--tw-mask-radial-position","top left"]]),e("mask-radial-at-top-right",[["--tw-mask-radial-position","top right"]]),e("mask-radial-at-bottom",[["--tw-mask-radial-position","bottom"]]),e("mask-radial-at-bottom-left",[["--tw-mask-radial-position","bottom left"]]),e("mask-radial-at-bottom-right",[["--tw-mask-radial-position","bottom right"]]),e("mask-radial-at-left",[["--tw-mask-radial-position","left"]]),e("mask-radial-at-right",[["--tw-mask-radial-position","right"]]),e("mask-radial-at-center",[["--tw-mask-radial-position","center"]]),o("mask-radial-at",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:a=>[n("--tw-mask-radial-position",a)]}),o("mask-radial",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:a=>[x(),R(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"),n("--tw-mask-radial-size",a)]}),S("mask-radial-from",{color:a=>[x(),R(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),n("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),n("--tw-mask-radial-from-color",a)],position:a=>[x(),R(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),n("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),n("--tw-mask-radial-from-position",a)]}),S("mask-radial-to",{color:a=>[x(),R(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),n("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),n("--tw-mask-radial-to-color",a)],position:a=>[x(),R(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),n("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),n("--tw-mask-radial-to-position",a)]});let _=()=>F([V("--tw-mask-conic-position","0deg"),V("--tw-mask-conic-from-position","0%"),V("--tw-mask-conic-to-position","100%"),V("--tw-mask-conic-from-color","black"),V("--tw-mask-conic-to-color","transparent")]);o("mask-conic",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(a){return E(a.value)?`calc(1deg * ${a.value})`:null},handleNegativeBareValue(a){return E(a.value)?`calc(1deg * -${a.value})`:null},handle:a=>[x(),_(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"),n("--tw-mask-conic-position",a)]}),i("mask-conic",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),S("mask-conic-from",{color:a=>[x(),_(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),n("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),n("--tw-mask-conic-from-color",a)],position:a=>[x(),_(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),n("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),n("--tw-mask-conic-from-position",a)]}),S("mask-conic-to",{color:a=>[x(),_(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),n("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),n("--tw-mask-conic-to-color",a)],position:a=>[x(),_(),n("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),n("mask-composite","intersect"),n("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),n("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),n("--tw-mask-conic-to-position",a)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let a of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${a}`,[["background-blend-mode",a]]),e(`mix-blend-${a}`,[["mix-blend-mode",a]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),t.functional("fill",a=>{if(!a.value)return;if(a.value.kind==="arbitrary"){let h=X(a.value.value,a.modifier,r);return h===null?void 0:[n("fill",h)]}let c=te(a,r,["--fill","--color"]);if(c)return[n("fill",c)]}),i("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(a,c)=>`${c*5}`)}]),e("stroke-none",[["stroke","none"]]),t.functional("stroke",a=>{if(a.value){if(a.value.kind==="arbitrary"){let c=a.value.value;switch(a.value.dataType??H(c,["color","number","length","percentage"])){case"number":case"length":case"percentage":return a.modifier?void 0:[n("stroke-width",c)];default:return c=X(a.value.value,a.modifier,r),c===null?void 0:[n("stroke",c)]}}{let c=te(a,r,["--stroke","--color"]);if(c)return[n("stroke",c)]}{let c=r.resolve(a.value.value,["--stroke-width"]);if(c)return[n("stroke-width",c)];if(E(a.value.value))return[n("stroke-width",a.value.value)]}}}),i("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(a,c)=>`${c*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),o("object",{themeKeys:["--object-position"],handle:a=>[n("object-position",a)],staticValues:{top:[n("object-position","top")],"top-left":[n("object-position","left top")],"top-right":[n("object-position","right top")],bottom:[n("object-position","bottom")],"bottom-left":[n("object-position","left bottom")],"bottom-right":[n("object-position","right bottom")],left:[n("object-position","left")],right:[n("object-position","right")],center:[n("object-position","center")]}});for(let[a,c]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])l(a,["--padding","--spacing"],h=>[n(c,h)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),l("indent",["--text-indent","--spacing"],a=>[n("text-indent",a)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),o("align",{themeKeys:[],handle:a=>[n("vertical-align",a)]}),t.functional("font",a=>{if(!(!a.value||a.modifier)){if(a.value.kind==="arbitrary"){let c=a.value.value;switch(a.value.dataType??H(c,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[n("font-family",c)];default:return[F([V("--tw-font-weight")]),n("--tw-font-weight",c),n("font-weight",c)]}}{let c=r.resolveWith(a.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(c){let[h,A={}]=c;return[n("font-family",h),n("font-feature-settings",A["--font-feature-settings"]),n("font-variation-settings",A["--font-variation-settings"])]}}{let c=r.resolve(a.value.value,["--font-weight"]);if(c)return[F([V("--tw-font-weight")]),n("--tw-font-weight",c),n("font-weight",c)]}}}),i("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),o("font-stretch",{handleBareValue:({value:a})=>{if(!a.endsWith("%"))return null;let c=Number(a.slice(0,-1));return!E(c)||Number.isNaN(c)||c<50||c>200?null:a},handle:a=>[n("font-stretch",a)]}),i("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),s("placeholder",{themeKeys:["--background-color","--color"],handle:a=>[B("&::placeholder",[n("--tw-sort","placeholder-color"),n("color",a)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),t.functional("decoration",a=>{if(a.value){if(a.value.kind==="arbitrary"){let c=a.value.value;switch(a.value.dataType??H(c,["color","length","percentage"])){case"length":case"percentage":return a.modifier?void 0:[n("text-decoration-thickness",c)];default:return c=X(c,a.modifier,r),c===null?void 0:[n("text-decoration-color",c)]}}{let c=r.resolve(a.value.value,["--text-decoration-thickness"]);if(c)return a.modifier?void 0:[n("text-decoration-thickness",c)];if(E(a.value.value))return a.modifier?void 0:[n("text-decoration-thickness",`${a.value.value}px`)]}{let c=te(a,r,["--text-decoration-color","--color"]);if(c)return[n("text-decoration-color",c)]}}}),i("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(a,c)=>`${c*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),o("animate",{themeKeys:["--animate"],handle:a=>[n("animation",a)],staticValues:{none:[n("animation","none")]}});{let a=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),c=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),h=()=>F([V("--tw-blur"),V("--tw-brightness"),V("--tw-contrast"),V("--tw-grayscale"),V("--tw-hue-rotate"),V("--tw-invert"),V("--tw-opacity"),V("--tw-saturate"),V("--tw-sepia"),V("--tw-drop-shadow"),V("--tw-drop-shadow-color"),V("--tw-drop-shadow-alpha","100%","<percentage>"),V("--tw-drop-shadow-size")]),A=()=>F([V("--tw-backdrop-blur"),V("--tw-backdrop-brightness"),V("--tw-backdrop-contrast"),V("--tw-backdrop-grayscale"),V("--tw-backdrop-hue-rotate"),V("--tw-backdrop-invert"),V("--tw-backdrop-opacity"),V("--tw-backdrop-saturate"),V("--tw-backdrop-sepia")]);t.functional("filter",k=>{if(!k.modifier){if(k.value===null)return[h(),n("filter",a)];if(k.value.kind==="arbitrary")return[n("filter",k.value.value)];switch(k.value.value){case"none":return[n("filter","none")]}}}),t.functional("backdrop-filter",k=>{if(!k.modifier){if(k.value===null)return[A(),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)];if(k.value.kind==="arbitrary")return[n("-webkit-backdrop-filter",k.value.value),n("backdrop-filter",k.value.value)];switch(k.value.value){case"none":return[n("-webkit-backdrop-filter","none"),n("backdrop-filter","none")]}}}),o("blur",{themeKeys:["--blur"],handle:k=>[h(),n("--tw-blur",`blur(${k})`),n("filter",a)],staticValues:{none:[h(),n("--tw-blur"," "),n("filter",a)]}}),o("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:k=>[A(),n("--tw-backdrop-blur",`blur(${k})`),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)],staticValues:{none:[A(),n("--tw-backdrop-blur"," "),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)]}}),o("brightness",{themeKeys:["--brightness"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[h(),n("--tw-brightness",`brightness(${k})`),n("filter",a)]}),o("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[A(),n("--tw-backdrop-brightness",`brightness(${k})`),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)]}),i("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),i("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),o("contrast",{themeKeys:["--contrast"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[h(),n("--tw-contrast",`contrast(${k})`),n("filter",a)]}),o("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[A(),n("--tw-backdrop-contrast",`contrast(${k})`),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)]}),i("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),i("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),o("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[h(),n("--tw-grayscale",`grayscale(${k})`),n("filter",a)]}),o("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[A(),n("--tw-backdrop-grayscale",`grayscale(${k})`),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)]}),i("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),i("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),o("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:k})=>E(k)?`${k}deg`:null,handle:k=>[h(),n("--tw-hue-rotate",`hue-rotate(${k})`),n("filter",a)]}),o("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:k})=>E(k)?`${k}deg`:null,handle:k=>[A(),n("--tw-backdrop-hue-rotate",`hue-rotate(${k})`),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)]}),i("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),i("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),o("invert",{themeKeys:["--invert"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[h(),n("--tw-invert",`invert(${k})`),n("filter",a)]}),o("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[A(),n("--tw-backdrop-invert",`invert(${k})`),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)]}),i("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),i("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),o("saturate",{themeKeys:["--saturate"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[h(),n("--tw-saturate",`saturate(${k})`),n("filter",a)]}),o("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,handle:k=>[A(),n("--tw-backdrop-saturate",`saturate(${k})`),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)]}),i("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),i("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),o("sepia",{themeKeys:["--sepia"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[h(),n("--tw-sepia",`sepia(${k})`),n("filter",a)]}),o("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:k})=>E(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[A(),n("--tw-backdrop-sepia",`sepia(${k})`),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)]}),i("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),i("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[h,["--tw-drop-shadow"," "],["filter",a]]),t.functional("drop-shadow",k=>{let $;if(k.modifier&&(k.modifier.kind==="arbitrary"?$=k.modifier.value:E(k.modifier.value)&&($=`${k.modifier.value}%`)),!k.value){let P=r.get(["--drop-shadow"]),N=r.resolve(null,["--drop-shadow"]);return P===null||N===null?void 0:[h(),n("--tw-drop-shadow-alpha",$),...Ye("--tw-drop-shadow-size",P,$,T=>`var(--tw-drop-shadow-color, ${T})`),n("--tw-drop-shadow",L(N,",").map(T=>`drop-shadow(${T})`).join(" ")),n("filter",a)]}if(k.value.kind==="arbitrary"){let P=k.value.value;switch(k.value.dataType??H(P,["color"])){case"color":return P=X(P,k.modifier,r),P===null?void 0:[h(),n("--tw-drop-shadow-color",Q(P,"var(--tw-drop-shadow-alpha)")),n("--tw-drop-shadow","var(--tw-drop-shadow-size)")];default:return k.modifier&&!$?void 0:[h(),n("--tw-drop-shadow-alpha",$),...Ye("--tw-drop-shadow-size",P,$,T=>`var(--tw-drop-shadow-color, ${T})`),n("--tw-drop-shadow","var(--tw-drop-shadow-size)"),n("filter",a)]}}{let P=r.get([`--drop-shadow-${k.value.value}`]),N=r.resolve(k.value.value,["--drop-shadow"]);if(P&&N)return k.modifier&&!$?void 0:$?[h(),n("--tw-drop-shadow-alpha",$),...Ye("--tw-drop-shadow-size",P,$,T=>`var(--tw-drop-shadow-color, ${T})`),n("--tw-drop-shadow","var(--tw-drop-shadow-size)"),n("filter",a)]:[h(),n("--tw-drop-shadow-alpha",$),...Ye("--tw-drop-shadow-size",P,$,T=>`var(--tw-drop-shadow-color, ${T})`),n("--tw-drop-shadow",L(N,",").map(T=>`drop-shadow(${T})`).join(" ")),n("filter",a)]}{let P=te(k,r,["--drop-shadow-color","--color"]);if(P)return P==="inherit"?[h(),n("--tw-drop-shadow-color","inherit"),n("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:[h(),n("--tw-drop-shadow-color",Q(P,"var(--tw-drop-shadow-alpha)")),n("--tw-drop-shadow","var(--tw-drop-shadow-size)")]}}),i("drop-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--drop-shadow-color","--color"],modifiers:Array.from({length:21},(k,$)=>`${$*5}`)},{valueThemeKeys:["--drop-shadow"]}]),o("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:k})=>Ue(k)?`${k}%`:null,handle:k=>[A(),n("--tw-backdrop-opacity",`opacity(${k})`),n("-webkit-backdrop-filter",c),n("backdrop-filter",c)]}),i("backdrop-opacity",()=>[{values:Array.from({length:21},(k,$)=>`${$*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let a=`var(--tw-ease, ${r.resolve(null,["--default-transition-timing-function"])??"ease"})`,c=`var(--tw-duration, ${r.resolve(null,["--default-transition-duration"])??"0s"})`;o("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events",themeKeys:["--transition-property"],handle:h=>[n("transition-property",h),n("transition-timing-function",a),n("transition-duration",c)],staticValues:{none:[n("transition-property","none")],all:[n("transition-property","all"),n("transition-timing-function",a),n("transition-duration",c)],colors:[n("transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"),n("transition-timing-function",a),n("transition-duration",c)],opacity:[n("transition-property","opacity"),n("transition-timing-function",a),n("transition-duration",c)],shadow:[n("transition-property","box-shadow"),n("transition-timing-function",a),n("transition-duration",c)],transform:[n("transition-property","transform, translate, scale, rotate"),n("transition-timing-function",a),n("transition-duration",c)]}}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),o("delay",{handleBareValue:({value:h})=>E(h)?`${h}ms`:null,themeKeys:["--transition-delay"],handle:h=>[n("transition-delay",h)]});{let h=()=>F([V("--tw-duration")]);e("duration-initial",[h,["--tw-duration","initial"]]),t.functional("duration",A=>{if(A.modifier||!A.value)return;let k=null;if(A.value.kind==="arbitrary"?k=A.value.value:(k=r.resolve(A.value.fraction??A.value.value,["--transition-duration"]),k===null&&E(A.value.value)&&(k=`${A.value.value}ms`)),k!==null)return[h(),n("--tw-duration",k),n("transition-duration",k)]})}i("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),i("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let a=()=>F([V("--tw-ease")]);o("ease",{themeKeys:["--ease"],handle:c=>[a(),n("--tw-ease",c),n("transition-timing-function",c)],staticValues:{initial:[a(),n("--tw-ease","initial")],linear:[a(),n("--tw-ease","linear"),n("transition-timing-function","linear")]}})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),o("will-change",{themeKeys:[],handle:a=>[n("will-change",a)]}),e("content-none",[["--tw-content","none"],["content","none"]]),o("content",{themeKeys:[],handle:a=>[F([V("--tw-content",'""')]),n("--tw-content",a),n("content","var(--tw-content)")]});{let a="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",c=()=>F([V("--tw-contain-size"),V("--tw-contain-layout"),V("--tw-contain-paint"),V("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[c,["--tw-contain-size","size"],["contain",a]]),e("contain-inline-size",[c,["--tw-contain-size","inline-size"],["contain",a]]),e("contain-layout",[c,["--tw-contain-layout","layout"],["contain",a]]),e("contain-paint",[c,["--tw-contain-paint","paint"],["contain",a]]),e("contain-style",[c,["--tw-contain-style","style"],["contain",a]]),o("contain",{themeKeys:[],handle:h=>[n("contain",h)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),l("leading",["--leading","--spacing"],a=>[F([V("--tw-leading")]),n("--tw-leading",a),n("line-height",a)],{staticValues:{none:[F([V("--tw-leading")]),n("--tw-leading","1"),n("line-height","1")]}}),o("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:a=>[F([V("--tw-tracking")]),n("--tw-tracking",a),n("letter-spacing",a)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let a="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",c=()=>F([V("--tw-ordinal"),V("--tw-slashed-zero"),V("--tw-numeric-figure"),V("--tw-numeric-spacing"),V("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[c,["--tw-ordinal","ordinal"],["font-variant-numeric",a]]),e("slashed-zero",[c,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",a]]),e("lining-nums",[c,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",a]]),e("oldstyle-nums",[c,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",a]]),e("proportional-nums",[c,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",a]]),e("tabular-nums",[c,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",a]]),e("diagonal-fractions",[c,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",a]]),e("stacked-fractions",[c,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",a]])}{let a=()=>F([V("--tw-outline-style","solid")]);t.static("outline-hidden",()=>[n("--tw-outline-style","none"),n("outline-style","none"),z("@media","(forced-colors: active)",[n("outline","2px solid transparent"),n("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),t.functional("outline",c=>{if(c.value===null){if(c.modifier)return;let h=r.get(["--default-outline-width"])??"1px";return[a(),n("outline-style","var(--tw-outline-style)"),n("outline-width",h)]}if(c.value.kind==="arbitrary"){let h=c.value.value;switch(c.value.dataType??H(h,["color","length","number","percentage"])){case"length":case"number":case"percentage":return c.modifier?void 0:[a(),n("outline-style","var(--tw-outline-style)"),n("outline-width",h)];default:return h=X(h,c.modifier,r),h===null?void 0:[n("outline-color",h)]}}{let h=te(c,r,["--outline-color","--color"]);if(h)return[n("outline-color",h)]}{if(c.modifier)return;let h=r.resolve(c.value.value,["--outline-width"]);if(h)return[a(),n("outline-style","var(--tw-outline-style)"),n("outline-width",h)];if(E(c.value.value))return[a(),n("outline-style","var(--tw-outline-style)"),n("outline-width",`${c.value.value}px`)]}}),i("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(c,h)=>`${h*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),o("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:c})=>E(c)?`${c}px`:null,handle:c=>[n("outline-offset",c)]}),i("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}o("opacity",{themeKeys:["--opacity"],handleBareValue:({value:a})=>Ue(a)?`${a}%`:null,handle:a=>[n("opacity",a)]}),i("opacity",()=>[{values:Array.from({length:21},(a,c)=>`${c*5}`),valueThemeKeys:["--opacity"]}]),o("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:a})=>E(a)?`${a}px`:null,handle:a=>[n("text-underline-offset",a)],staticValues:{auto:[n("text-underline-offset","auto")]}}),i("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),t.functional("text",a=>{if(a.value){if(a.value.kind==="arbitrary"){let c=a.value.value;switch(a.value.dataType??H(c,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(a.modifier){let A=a.modifier.kind==="arbitrary"?a.modifier.value:r.resolve(a.modifier.value,["--leading"]);if(!A&&de(a.modifier.value)){let k=r.resolve(null,["--spacing"]);if(!k)return null;A=`calc(${k} * ${a.modifier.value})`}return!A&&a.modifier.value==="none"&&(A="1"),A?[n("font-size",c),n("line-height",A)]:null}return[n("font-size",c)]}default:return c=X(c,a.modifier,r),c===null?void 0:[n("color",c)]}}{let c=te(a,r,["--text-color","--color"]);if(c)return[n("color",c)]}{let c=r.resolveWith(a.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(c){let[h,A={}]=Array.isArray(c)?c:[c];if(a.modifier){let k=a.modifier.kind==="arbitrary"?a.modifier.value:r.resolve(a.modifier.value,["--leading"]);if(!k&&de(a.modifier.value)){let P=r.resolve(null,["--spacing"]);if(!P)return null;k=`calc(${P} * ${a.modifier.value})`}if(!k&&a.modifier.value==="none"&&(k="1"),!k)return null;let $=[n("font-size",h)];return k&&$.push(n("line-height",k)),$}return typeof A=="string"?[n("font-size",h),n("line-height",A)]:[n("font-size",h),n("line-height",A["--line-height"]?`var(--tw-leading, ${A["--line-height"]})`:void 0),n("letter-spacing",A["--letter-spacing"]?`var(--tw-tracking, ${A["--letter-spacing"]})`:void 0),n("font-weight",A["--font-weight"]?`var(--tw-font-weight, ${A["--font-weight"]})`:void 0)]}}}}),i("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(a,c)=>`${c*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);let D=()=>F([V("--tw-text-shadow-color"),V("--tw-text-shadow-alpha","100%","<percentage>")]);e("text-shadow-initial",[D,["--tw-text-shadow-color","initial"]]),t.functional("text-shadow",a=>{let c;if(a.modifier&&(a.modifier.kind==="arbitrary"?c=a.modifier.value:E(a.modifier.value)&&(c=`${a.modifier.value}%`)),!a.value){let h=r.get(["--text-shadow"]);return h===null?void 0:[D(),n("--tw-text-shadow-alpha",c),...ue("text-shadow",h,c,A=>`var(--tw-text-shadow-color, ${A})`)]}if(a.value.kind==="arbitrary"){let h=a.value.value;switch(a.value.dataType??H(h,["color"])){case"color":return h=X(h,a.modifier,r),h===null?void 0:[D(),n("--tw-text-shadow-color",Q(h,"var(--tw-text-shadow-alpha)"))];default:return[D(),n("--tw-text-shadow-alpha",c),...ue("text-shadow",h,c,k=>`var(--tw-text-shadow-color, ${k})`)]}}switch(a.value.value){case"none":return a.modifier?void 0:[D(),n("text-shadow","none")];case"inherit":return a.modifier?void 0:[D(),n("--tw-text-shadow-color","inherit")]}{let h=r.get([`--text-shadow-${a.value.value}`]);if(h)return[D(),n("--tw-text-shadow-alpha",c),...ue("text-shadow",h,c,A=>`var(--tw-text-shadow-color, ${A})`)]}{let h=te(a,r,["--text-shadow-color","--color"]);if(h)return[D(),n("--tw-text-shadow-color",Q(h,"var(--tw-text-shadow-alpha)"))]}}),i("text-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-shadow-color","--color"],modifiers:Array.from({length:21},(a,c)=>`${c*5}`)},{values:["none"]},{valueThemeKeys:["--text-shadow"],modifiers:Array.from({length:21},(a,c)=>`${c*5}`),hasDefaultValue:r.get(["--text-shadow"])!==null}]);{let k=function(N){return`var(--tw-ring-inset,) 0 0 0 calc(${N} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${A})`},$=function(N){return`inset 0 0 0 ${N} var(--tw-inset-ring-color, currentcolor)`};var M=k,re=$;let a=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),c="0 0 #0000",h=()=>F([V("--tw-shadow",c),V("--tw-shadow-color"),V("--tw-shadow-alpha","100%","<percentage>"),V("--tw-inset-shadow",c),V("--tw-inset-shadow-color"),V("--tw-inset-shadow-alpha","100%","<percentage>"),V("--tw-ring-color"),V("--tw-ring-shadow",c),V("--tw-inset-ring-color"),V("--tw-inset-ring-shadow",c),V("--tw-ring-inset"),V("--tw-ring-offset-width","0px","<length>"),V("--tw-ring-offset-color","#fff"),V("--tw-ring-offset-shadow",c)]);e("shadow-initial",[h,["--tw-shadow-color","initial"]]),t.functional("shadow",N=>{let T;if(N.modifier&&(N.modifier.kind==="arbitrary"?T=N.modifier.value:E(N.modifier.value)&&(T=`${N.modifier.value}%`)),!N.value){let I=r.get(["--shadow"]);return I===null?void 0:[h(),n("--tw-shadow-alpha",T),...ue("--tw-shadow",I,T,oe=>`var(--tw-shadow-color, ${oe})`),n("box-shadow",a)]}if(N.value.kind==="arbitrary"){let I=N.value.value;switch(N.value.dataType??H(I,["color"])){case"color":return I=X(I,N.modifier,r),I===null?void 0:[h(),n("--tw-shadow-color",Q(I,"var(--tw-shadow-alpha)"))];default:return[h(),n("--tw-shadow-alpha",T),...ue("--tw-shadow",I,T,ot=>`var(--tw-shadow-color, ${ot})`),n("box-shadow",a)]}}switch(N.value.value){case"none":return N.modifier?void 0:[h(),n("--tw-shadow",c),n("box-shadow",a)];case"inherit":return N.modifier?void 0:[h(),n("--tw-shadow-color","inherit")]}{let I=r.get([`--shadow-${N.value.value}`]);if(I)return[h(),n("--tw-shadow-alpha",T),...ue("--tw-shadow",I,T,oe=>`var(--tw-shadow-color, ${oe})`),n("box-shadow",a)]}{let I=te(N,r,["--box-shadow-color","--color"]);if(I)return[h(),n("--tw-shadow-color",Q(I,"var(--tw-shadow-alpha)"))]}}),i("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`)},{values:["none"]},{valueThemeKeys:["--shadow"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`),hasDefaultValue:r.get(["--shadow"])!==null}]),e("inset-shadow-initial",[h,["--tw-inset-shadow-color","initial"]]),t.functional("inset-shadow",N=>{let T;if(N.modifier&&(N.modifier.kind==="arbitrary"?T=N.modifier.value:E(N.modifier.value)&&(T=`${N.modifier.value}%`)),!N.value){let I=r.get(["--inset-shadow"]);return I===null?void 0:[h(),n("--tw-inset-shadow-alpha",T),...ue("--tw-inset-shadow",I,T,oe=>`var(--tw-inset-shadow-color, ${oe})`),n("box-shadow",a)]}if(N.value.kind==="arbitrary"){let I=N.value.value;switch(N.value.dataType??H(I,["color"])){case"color":return I=X(I,N.modifier,r),I===null?void 0:[h(),n("--tw-inset-shadow-color",Q(I,"var(--tw-inset-shadow-alpha)"))];default:return[h(),n("--tw-inset-shadow-alpha",T),...ue("--tw-inset-shadow",I,T,ot=>`var(--tw-inset-shadow-color, ${ot})`,"inset "),n("box-shadow",a)]}}switch(N.value.value){case"none":return N.modifier?void 0:[h(),n("--tw-inset-shadow",c),n("box-shadow",a)];case"inherit":return N.modifier?void 0:[h(),n("--tw-inset-shadow-color","inherit")]}{let I=r.get([`--inset-shadow-${N.value.value}`]);if(I)return[h(),n("--tw-inset-shadow-alpha",T),...ue("--tw-inset-shadow",I,T,oe=>`var(--tw-inset-shadow-color, ${oe})`),n("box-shadow",a)]}{let I=te(N,r,["--box-shadow-color","--color"]);if(I)return[h(),n("--tw-inset-shadow-color",Q(I,"var(--tw-inset-shadow-alpha)"))]}}),i("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`)},{values:["none"]},{valueThemeKeys:["--inset-shadow"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`),hasDefaultValue:r.get(["--inset-shadow"])!==null}]),e("ring-inset",[h,["--tw-ring-inset","inset"]]);let A=r.get(["--default-ring-color"])??"currentcolor";t.functional("ring",N=>{if(!N.value){if(N.modifier)return;let T=r.get(["--default-ring-width"])??"1px";return[h(),n("--tw-ring-shadow",k(T)),n("box-shadow",a)]}if(N.value.kind==="arbitrary"){let T=N.value.value;switch(N.value.dataType??H(T,["color","length"])){case"length":return N.modifier?void 0:[h(),n("--tw-ring-shadow",k(T)),n("box-shadow",a)];default:return T=X(T,N.modifier,r),T===null?void 0:[n("--tw-ring-color",T)]}}{let T=te(N,r,["--ring-color","--color"]);if(T)return[n("--tw-ring-color",T)]}{if(N.modifier)return;let T=r.resolve(N.value.value,["--ring-width"]);if(T===null&&E(N.value.value)&&(T=`${N.value.value}px`),T)return[h(),n("--tw-ring-shadow",k(T)),n("box-shadow",a)]}}),i("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),t.functional("inset-ring",N=>{if(!N.value)return N.modifier?void 0:[h(),n("--tw-inset-ring-shadow",$("1px")),n("box-shadow",a)];if(N.value.kind==="arbitrary"){let T=N.value.value;switch(N.value.dataType??H(T,["color","length"])){case"length":return N.modifier?void 0:[h(),n("--tw-inset-ring-shadow",$(T)),n("box-shadow",a)];default:return T=X(T,N.modifier,r),T===null?void 0:[n("--tw-inset-ring-color",T)]}}{let T=te(N,r,["--ring-color","--color"]);if(T)return[n("--tw-inset-ring-color",T)]}{if(N.modifier)return;let T=r.resolve(N.value.value,["--ring-width"]);if(T===null&&E(N.value.value)&&(T=`${N.value.value}px`),T)return[h(),n("--tw-inset-ring-shadow",$(T)),n("box-shadow",a)]}}),i("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(N,T)=>`${T*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let P="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";t.functional("ring-offset",N=>{if(N.value){if(N.value.kind==="arbitrary"){let T=N.value.value;switch(N.value.dataType??H(T,["color","length"])){case"length":return N.modifier?void 0:[n("--tw-ring-offset-width",T),n("--tw-ring-offset-shadow",P)];default:return T=X(T,N.modifier,r),T===null?void 0:[n("--tw-ring-offset-color",T)]}}{let T=r.resolve(N.value.value,["--ring-offset-width"]);if(T)return N.modifier?void 0:[n("--tw-ring-offset-width",T),n("--tw-ring-offset-shadow",P)];if(E(N.value.value))return N.modifier?void 0:[n("--tw-ring-offset-width",`${N.value.value}px`),n("--tw-ring-offset-shadow",P)]}{let T=te(N,r,["--ring-offset-color","--color"]);if(T)return[n("--tw-ring-offset-color",T)]}}})}return i("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(a,c)=>`${c*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),t.functional("@container",a=>{let c=null;if(a.value===null?c="inline-size":a.value.kind==="arbitrary"?c=a.value.value:a.value.kind==="named"&&a.value.value==="normal"?c="normal":!1,c!==null)return a.modifier?[n("container-type",c),n("container-name",a.modifier.value)]:[n("container-type",c)]}),i("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),t}var kt=["number","integer","ratio","percentage"];function dr(r){let t=r.params;return Si.test(t)?i=>{let e={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};j(r.nodes,o=>{if(o.kind!=="declaration"||!o.value||!o.value.includes("--value(")&&!o.value.includes("--modifier("))return;let s=J(o.value);ee(s,l=>{if(l.kind!=="function")return;if(l.value==="--spacing"&&!(e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return ee(l.nodes,u=>{if(u.kind!=="function"||u.value!=="--value"&&u.value!=="--modifier")return;let f=u.value;for(let g of u.nodes)if(g.kind==="word"){if(g.value==="integer")e[f].usedSpacingInteger||=!0;else if(g.value==="number"&&(e[f].usedSpacingNumber||=!0,e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return 2}}),0;if(l.value!=="--value"&&l.value!=="--modifier")return;let d=L(Z(l.nodes),",");for(let[u,f]of d.entries())f=f.replace(/\\\*/g,"*"),f=f.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),f=f.replace(/\s+/g,""),f=f.replace(/(-\*){2,}/g,"-*"),f[0]==="-"&&f[1]==="-"&&!f.includes("-*")&&(f+="-*"),d[u]=f;l.nodes=J(d.join(","));for(let u of l.nodes)if(u.kind==="word"&&(u.value[0]==='"'||u.value[0]==="'")&&u.value[0]===u.value[u.value.length-1]){let f=u.value.slice(1,-1);e[l.value].literals.add(f)}else if(u.kind==="word"&&u.value[0]==="-"&&u.value[1]==="-"){let f=u.value.replace(/-\*.*$/g,"");e[l.value].themeKeys.add(f)}else if(u.kind==="word"&&!(u.value[0]==="["&&u.value[u.value.length-1]==="]")&&!kt.includes(u.value)){console.warn(`Unsupported bare value data type: "${u.value}".
Only valid data types are: ${kt.map(y=>`"${y}"`).join(", ")}.
`);let f=u.value,g=structuredClone(l),m="\xB6";ee(g.nodes,(y,{replaceWith:x})=>{y.kind==="word"&&y.value===f&&x({kind:"word",value:m})});let p="^".repeat(Z([u]).length),w=Z([g]).indexOf(m),v=["```css",Z([l])," ".repeat(w)+p,"```"].join(`
`);console.warn(v)}}),o.value=Z(s)}),i.utilities.functional(t.slice(0,-2),o=>{let s=structuredClone(r),l=o.value,d=o.modifier;if(l===null)return;let u=!1,f=!1,g=!1,m=!1,p=new Map,w=!1;if(j([s],(v,{parent:y,replaceWith:x})=>{if(y?.kind!=="rule"&&y?.kind!=="at-rule"||v.kind!=="declaration"||!v.value)return;let S=J(v.value);(ee(S,(b,{replaceWith:O})=>{if(b.kind==="function"){if(b.value==="--value"){u=!0;let R=ur(l,b,i);return R?(f=!0,R.ratio?w=!0:p.set(v,y),O(R.nodes),1):(u||=!1,x([]),2)}else if(b.value==="--modifier"){if(d===null)return x([]),2;g=!0;let R=ur(d,b,i);return R?(m=!0,O(R.nodes),1):(g||=!1,x([]),2)}}})??0)===0&&(v.value=Z(S))}),u&&!f||g&&!m||w&&m||d&&!w&&!m)return null;if(w)for(let[v,y]of p){let x=y.nodes.indexOf(v);x!==-1&&y.nodes.splice(x,1)}return s.nodes}),i.utilities.suggest(t.slice(0,-2),()=>{let o=[],s=[];for(let[l,{literals:d,usedSpacingNumber:u,usedSpacingInteger:f,themeKeys:g}]of[[o,e["--value"]],[s,e["--modifier"]]]){for(let m of d)l.push(m);if(u)l.push(...Ze);else if(f)for(let m of Ze)E(m)&&l.push(m);for(let m of i.theme.keysInNamespaces(g))l.push(m.replace(cr,(p,w,v)=>`${w}.${v}`))}return[{values:o,modifiers:s}]})}:Ni.test(t)?i=>{i.utilities.static(t,()=>structuredClone(r.nodes))}:null}function ur(r,t,i){for(let e of t.nodes){if(r.kind==="named"&&e.kind==="word"&&(e.value[0]==="'"||e.value[0]==='"')&&e.value[e.value.length-1]===e.value[0]&&e.value.slice(1,-1)===r.value)return{nodes:J(r.value)};if(r.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let o=e.value;if(o.endsWith("-*")){o=o.slice(0,-2);let s=i.theme.resolve(r.value,[o]);if(s)return{nodes:J(s)}}else{let s=o.split("-*");if(s.length<=1)continue;let l=[s.shift()],d=i.theme.resolveWith(r.value,l,s);if(d){let[,u={}]=d;{let f=u[s.pop()];if(f)return{nodes:J(f)}}}}}else if(r.kind==="named"&&e.kind==="word"){if(!kt.includes(e.value))continue;let o=e.value==="ratio"&&"fraction"in r?r.fraction:r.value;if(!o)continue;let s=H(o,[e.value]);if(s===null)continue;if(s==="ratio"){let[l,d]=L(o,"/");if(!E(l)||!E(d))continue}else{if(s==="number"&&!de(o))continue;if(s==="percentage"&&!E(o.slice(0,-1)))continue}return{nodes:J(o),ratio:s==="ratio"}}else if(r.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let o=e.value.slice(1,-1);if(o==="*")return{nodes:J(r.value)};if("dataType"in r&&r.dataType&&r.dataType!==o)continue;if("dataType"in r&&r.dataType)return{nodes:J(r.value)};if(H(r.value,[o])!==null)return{nodes:J(r.value)}}}}function ue(r,t,i,e,o=""){let s=!1,l=Re(t,u=>i==null?e(u):u.startsWith("current")?e(Q(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(fr(u,i))));function d(u){return o?L(u,",").map(f=>o+f).join(","):u}return s?[n(r,d(Re(t,e))),Y("@supports (color: lab(from red l a b))",[n(r,d(l))])]:[n(r,d(l))]}function Ye(r,t,i,e,o=""){let s=!1,l=L(t,",").map(d=>Re(d,u=>i==null?e(u):u.startsWith("current")?e(Q(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(fr(u,i))))).map(d=>`drop-shadow(${d})`).join(" ");return s?[n(r,o+L(t,",").map(d=>`drop-shadow(${Re(d,e)})`).join(" ")),Y("@supports (color: lab(from red l a b))",[n(r,o+l)])]:[n(r,o+l)]}var bt={"--alpha":Ti,"--spacing":Ei,"--theme":Ri,theme:Pi};function Ti(r,t,i,...e){let[o,s]=L(i,"/").map(l=>l.trim());if(!o||!s)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${o||"var(--my-color)"} / ${s||"50%"})\``);if(e.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${o||"var(--my-color)"} / ${s||"50%"})\``);return Q(o,s)}function Ei(r,t,i,...e){if(!i)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(e.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${e.length+1}.`);let o=r.theme.resolve(null,["--spacing"]);if(!o)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${o} * ${i})`}function Ri(r,t,i,...e){if(!i.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let o=!1;i.endsWith(" inline")&&(o=!0,i=i.slice(0,-7)),t.kind==="at-rule"&&(o=!0);let s=r.resolveThemeValue(i,o);if(!s){if(e.length>0)return e.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(e.length===0)return s;let l=e.join(", ");if(l==="initial")return s;if(s==="initial")return l;if(s.startsWith("var(")||s.startsWith("theme(")||s.startsWith("--theme(")){let d=J(s);return Oi(d,l),Z(d)}return s}function Pi(r,t,i,...e){i=Ki(i);let o=r.resolveThemeValue(i);if(!o&&e.length>0)return e.join(", ");if(!o)throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return o}var mr=new RegExp(Object.keys(bt).map(r=>`${r}\\(`).join("|"));function xe(r,t){let i=0;return j(r,e=>{if(e.kind==="declaration"&&e.value&&mr.test(e.value)){i|=8,e.value=gr(e.value,e,t);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&mr.test(e.params)&&(i|=8,e.params=gr(e.params,e,t))}),i}function gr(r,t,i){let e=J(r);return ee(e,(o,{replaceWith:s})=>{if(o.kind==="function"&&o.value in bt){let l=L(Z(o.nodes).trim(),",").map(u=>u.trim()),d=bt[o.value](i,t,...l);return s(J(d))}}),Z(e)}function Ki(r){if(r[0]!=="'"&&r[0]!=='"')return r;let t="",i=r[0];for(let e=1;e<r.length-1;e++){let o=r[e],s=r[e+1];o==="\\"&&(s===i||s==="\\")?(t+=s,e++):t+=o}return t}function Oi(r,t){ee(r,i=>{if(i.kind==="function"&&!(i.value!=="var"&&i.value!=="theme"&&i.value!=="--theme"))if(i.nodes.length===1)i.nodes.push({kind:"word",value:`, ${t}`});else{let e=i.nodes[i.nodes.length-1];e.kind==="word"&&e.value==="initial"&&(e.value=t)}})}function Qe(r,t){let i=r.length,e=t.length,o=i<e?i:e;for(let s=0;s<o;s++){let l=r.charCodeAt(s),d=t.charCodeAt(s);if(l>=48&&l<=57&&d>=48&&d<=57){let u=s,f=s+1,g=s,m=s+1;for(l=r.charCodeAt(f);l>=48&&l<=57;)l=r.charCodeAt(++f);for(d=t.charCodeAt(m);d>=48&&d<=57;)d=t.charCodeAt(++m);let p=r.slice(u,f),w=t.slice(g,m),v=Number(p)-Number(w);if(v)return v;if(p<w)return-1;if(p>w)return 1;continue}if(l!==d)return l-d}return r.length-t.length}var _i=/^\d+\/\d+$/;function hr(r){let t=new W(o=>({name:o,utility:o,fraction:!1,modifiers:[]}));for(let o of r.utilities.keys("static")){let s=t.get(o);s.fraction=!1,s.modifiers=[]}for(let o of r.utilities.keys("functional")){let s=r.utilities.getCompletions(o);for(let l of s)for(let d of l.values){let u=d!==null&&_i.test(d),f=d===null?o:`${o}-${d}`,g=t.get(f);if(g.utility=o,g.fraction||=u,g.modifiers.push(...l.modifiers),l.supportsNegative){let m=t.get(`-${f}`);m.utility=`-${o}`,m.fraction||=u,m.modifiers.push(...l.modifiers)}g.modifiers=Array.from(new Set(g.modifiers))}}if(t.size===0)return[];let i=Array.from(t.values());return i.sort((o,s)=>Qe(o.name,s.name)),Di(i)}function Di(r){let t=[],i=null,e=new Map,o=new W(()=>[]);for(let l of r){let{utility:d,fraction:u}=l;i||(i={utility:d,items:[]},e.set(d,i)),d!==i.utility&&(t.push(i),i={utility:d,items:[]},e.set(d,i)),u?o.get(d).push(l):i.items.push(l)}i&&t[t.length-1]!==i&&t.push(i);for(let[l,d]of o){let u=e.get(l);u&&u.items.push(...d)}let s=[];for(let l of t)for(let d of l.items)s.push([d.name,{modifiers:d.modifiers}]);return s}function vr(r){let t=[];for(let[e,o]of r.variants.entries()){let d=function({value:u,modifier:f}={}){let g=e;u&&(g+=s?`-${u}`:u),f&&(g+=`/${f}`);let m=r.parseVariant(g);if(!m)return[];let p=B(".__placeholder__",[]);if(Ce(p,m,r.variants)===null)return[];let w=[];return Ge(p.nodes,(v,{path:y})=>{if(v.kind!=="rule"&&v.kind!=="at-rule"||v.nodes.length>0)return;y.sort((C,b)=>{let O=C.kind==="at-rule",R=b.kind==="at-rule";return O&&!R?-1:!O&&R?1:0});let x=y.flatMap(C=>C.kind==="rule"?C.selector==="&"?[]:[C.selector]:C.kind==="at-rule"?[`${C.name} ${C.params}`]:[]),S="";for(let C=x.length-1;C>=0;C--)S=S===""?x[C]:`${x[C]} { ${S} }`;w.push(S)}),w};var i=d;if(o.kind==="arbitrary")continue;let s=e!=="@",l=r.variants.getCompletions(e);switch(o.kind){case"static":{t.push({name:e,values:l,isArbitrary:!1,hasDash:s,selectors:d});break}case"functional":{t.push({name:e,values:l,isArbitrary:!0,hasDash:s,selectors:d});break}case"compound":{t.push({name:e,values:l,isArbitrary:!0,hasDash:s,selectors:d});break}}}return t}function wr(r,t){let{astNodes:i,nodeSorting:e}=pe(Array.from(t),r),o=new Map(t.map(l=>[l,null])),s=0n;for(let l of i){let d=e.get(l)?.candidate;d&&o.set(d,o.get(d)??s++)}return t.map(l=>[l,o.get(l)??null])}var Xe=/^@?[a-z0-9][a-zA-Z0-9_-]*(?<![_-])$/;var xt=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(t,i,{compounds:e,order:o}={}){this.set(t,{kind:"static",applyFn:i,compoundsWith:0,compounds:e??2,order:o})}fromAst(t,i,e){let o=[],s=!1;j(i,l=>{l.kind==="rule"?o.push(l.selector):l.kind==="at-rule"&&l.name==="@variant"?s=!0:l.kind==="at-rule"&&l.name!=="@slot"&&o.push(`${l.name} ${l.params}`)}),this.static(t,l=>{let d=structuredClone(i);s&&Ct(d,e),At(d,l.nodes),l.nodes=d},{compounds:ye(o)})}functional(t,i,{compounds:e,order:o}={}){this.set(t,{kind:"functional",applyFn:i,compoundsWith:0,compounds:e??2,order:o})}compound(t,i,e,{compounds:o,order:s}={}){this.set(t,{kind:"compound",applyFn:e,compoundsWith:i,compounds:o??2,order:s})}group(t,i){this.groupOrder=this.nextOrder(),i&&this.compareFns.set(this.groupOrder,i),t(),this.groupOrder=null}has(t){return this.variants.has(t)}get(t){return this.variants.get(t)}kind(t){return this.variants.get(t)?.kind}compoundsWith(t,i){let e=this.variants.get(t),o=typeof i=="string"?this.variants.get(i):i.kind==="arbitrary"?{compounds:ye([i.selector])}:this.variants.get(i.root);return!(!e||!o||e.kind!=="compound"||o.compounds===0||e.compoundsWith===0||(e.compoundsWith&o.compounds)===0)}suggest(t,i){this.completions.set(t,i)}getCompletions(t){return this.completions.get(t)?.()??[]}compare(t,i){if(t===i)return 0;if(t===null)return-1;if(i===null)return 1;if(t.kind==="arbitrary"&&i.kind==="arbitrary")return t.selector<i.selector?-1:1;if(t.kind==="arbitrary")return 1;if(i.kind==="arbitrary")return-1;let e=this.variants.get(t.root).order,o=this.variants.get(i.root).order,s=e-o;if(s!==0)return s;if(t.kind==="compound"&&i.kind==="compound"){let f=this.compare(t.variant,i.variant);return f!==0?f:t.modifier&&i.modifier?t.modifier.value<i.modifier.value?-1:1:t.modifier?1:i.modifier?-1:0}let l=this.compareFns.get(e);if(l!==void 0)return l(t,i);if(t.root!==i.root)return t.root<i.root?-1:1;let d=t.value,u=i.value;return d===null?-1:u===null||d.kind==="arbitrary"&&u.kind!=="arbitrary"?1:d.kind!=="arbitrary"&&u.kind==="arbitrary"||d.value<u.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(t,{kind:i,applyFn:e,compounds:o,compoundsWith:s,order:l}){let d=this.variants.get(t);d?Object.assign(d,{kind:i,applyFn:e,compounds:o}):(l===void 0&&(this.lastOrder=this.nextOrder(),l=this.lastOrder),this.variants.set(t,{kind:i,applyFn:e,order:l,compoundsWith:s,compounds:o}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function ye(r){let t=0;for(let i of r){if(i[0]==="@"){if(!i.startsWith("@media")&&!i.startsWith("@supports")&&!i.startsWith("@container"))return 0;t|=1;continue}if(i.includes("::"))return 0;t|=2}return t}function kr(r){let t=new xt;function i(f,g,{compounds:m}={}){m=m??ye(g),t.static(f,p=>{p.nodes=g.map(w=>Y(w,p.nodes))},{compounds:m})}i("*",[":is(& > *)"],{compounds:0}),i("**",[":is(& *)"],{compounds:0});function e(f,g){return g.map(m=>{m=m.trim();let p=L(m," ");return p[0]==="not"?p.slice(1).join(" "):f==="@container"?p[0][0]==="("?`not ${m}`:p[1]==="not"?`${p[0]} ${p.slice(2).join(" ")}`:`${p[0]} not ${p.slice(1).join(" ")}`:`not ${m}`})}let o=["@media","@supports","@container"];function s(f){for(let g of o){if(g!==f.name)continue;let m=L(f.params,",");return m.length>1?null:(m=e(f.name,m),z(f.name,m.join(", ")))}return null}function l(f){return f.includes("::")?null:`&:not(${L(f,",").map(m=>(m=m.replaceAll("&","*"),m)).join(", ")})`}t.compound("not",3,(f,g)=>{if(g.variant.kind==="arbitrary"&&g.variant.relative||g.modifier)return null;let m=!1;if(j([f],(p,{path:w})=>{if(p.kind!=="rule"&&p.kind!=="at-rule")return 0;if(p.nodes.length>0)return 0;let v=[],y=[];for(let S of w)S.kind==="at-rule"?v.push(S):S.kind==="rule"&&y.push(S);if(v.length>1)return 2;if(y.length>1)return 2;let x=[];for(let S of y){let C=l(S.selector);if(!C)return m=!1,2;x.push(B(C,[]))}for(let S of v){let C=s(S);if(!C)return m=!1,2;x.push(C)}return Object.assign(f,B("&",x)),m=!0,1}),f.kind==="rule"&&f.selector==="&"&&f.nodes.length===1&&Object.assign(f,f.nodes[0]),!m)return null}),t.suggest("not",()=>Array.from(t.keys()).filter(f=>t.compoundsWith("not",f))),t.compound("group",2,(f,g)=>{if(g.variant.kind==="arbitrary"&&g.variant.relative)return null;let m=g.modifier?`:where(.${r.prefix?`${r.prefix}\\:`:""}group\\/${g.modifier.value})`:`:where(.${r.prefix?`${r.prefix}\\:`:""}group)`,p=!1;if(j([f],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let x of v.slice(0,-1))if(x.kind==="rule")return p=!1,2;let y=w.selector.replaceAll("&",m);L(y,",").length>1&&(y=`:is(${y})`),w.selector=`&:is(${y} *)`,p=!0}),!p)return null}),t.suggest("group",()=>Array.from(t.keys()).filter(f=>t.compoundsWith("group",f))),t.compound("peer",2,(f,g)=>{if(g.variant.kind==="arbitrary"&&g.variant.relative)return null;let m=g.modifier?`:where(.${r.prefix?`${r.prefix}\\:`:""}peer\\/${g.modifier.value})`:`:where(.${r.prefix?`${r.prefix}\\:`:""}peer)`,p=!1;if(j([f],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let x of v.slice(0,-1))if(x.kind==="rule")return p=!1,2;let y=w.selector.replaceAll("&",m);L(y,",").length>1&&(y=`:is(${y})`),w.selector=`&:is(${y} ~ *)`,p=!0}),!p)return null}),t.suggest("peer",()=>Array.from(t.keys()).filter(f=>t.compoundsWith("peer",f))),i("first-letter",["&::first-letter"]),i("first-line",["&::first-line"]),i("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),i("selection",["& *::selection","&::selection"]),i("file",["&::file-selector-button"]),i("placeholder",["&::placeholder"]),i("backdrop",["&::backdrop"]),i("details-content",["&::details-content"]);{let f=function(){return F([z("@property","--tw-content",[n("syntax",'"*"'),n("initial-value",'""'),n("inherits","false")])])};var d=f;t.static("before",g=>{g.nodes=[B("&::before",[f(),n("content","var(--tw-content)"),...g.nodes])]},{compounds:0}),t.static("after",g=>{g.nodes=[B("&::after",[f(),n("content","var(--tw-content)"),...g.nodes])]},{compounds:0})}i("first",["&:first-child"]),i("last",["&:last-child"]),i("only",["&:only-child"]),i("odd",["&:nth-child(odd)"]),i("even",["&:nth-child(even)"]),i("first-of-type",["&:first-of-type"]),i("last-of-type",["&:last-of-type"]),i("only-of-type",["&:only-of-type"]),i("visited",["&:visited"]),i("target",["&:target"]),i("open",["&:is([open], :popover-open, :open)"]),i("default",["&:default"]),i("checked",["&:checked"]),i("indeterminate",["&:indeterminate"]),i("placeholder-shown",["&:placeholder-shown"]),i("autofill",["&:autofill"]),i("optional",["&:optional"]),i("required",["&:required"]),i("valid",["&:valid"]),i("invalid",["&:invalid"]),i("user-valid",["&:user-valid"]),i("user-invalid",["&:user-invalid"]),i("in-range",["&:in-range"]),i("out-of-range",["&:out-of-range"]),i("read-only",["&:read-only"]),i("empty",["&:empty"]),i("focus-within",["&:focus-within"]),t.static("hover",f=>{f.nodes=[B("&:hover",[z("@media","(hover: hover)",f.nodes)])]}),i("focus",["&:focus"]),i("focus-visible",["&:focus-visible"]),i("active",["&:active"]),i("enabled",["&:enabled"]),i("disabled",["&:disabled"]),i("inert",["&:is([inert], [inert] *)"]),t.compound("in",2,(f,g)=>{if(g.modifier)return null;let m=!1;if(j([f],(p,{path:w})=>{if(p.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return m=!1,2;p.selector=`:where(${p.selector.replaceAll("&","*")}) &`,m=!0}),!m)return null}),t.suggest("in",()=>Array.from(t.keys()).filter(f=>t.compoundsWith("in",f))),t.compound("has",2,(f,g)=>{if(g.modifier)return null;let m=!1;if(j([f],(p,{path:w})=>{if(p.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return m=!1,2;p.selector=`&:has(${p.selector.replaceAll("&","*")})`,m=!0}),!m)return null}),t.suggest("has",()=>Array.from(t.keys()).filter(f=>t.compoundsWith("has",f))),t.functional("aria",(f,g)=>{if(!g.value||g.modifier)return null;g.value.kind==="arbitrary"?f.nodes=[B(`&[aria-${yr(g.value.value)}]`,f.nodes)]:f.nodes=[B(`&[aria-${g.value.value}="true"]`,f.nodes)]}),t.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),t.functional("data",(f,g)=>{if(!g.value||g.modifier)return null;f.nodes=[B(`&[data-${yr(g.value.value)}]`,f.nodes)]}),t.functional("nth",(f,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!E(g.value.value))return null;f.nodes=[B(`&:nth-child(${g.value.value})`,f.nodes)]}),t.functional("nth-last",(f,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!E(g.value.value))return null;f.nodes=[B(`&:nth-last-child(${g.value.value})`,f.nodes)]}),t.functional("nth-of-type",(f,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!E(g.value.value))return null;f.nodes=[B(`&:nth-of-type(${g.value.value})`,f.nodes)]}),t.functional("nth-last-of-type",(f,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!E(g.value.value))return null;f.nodes=[B(`&:nth-last-of-type(${g.value.value})`,f.nodes)]}),t.functional("supports",(f,g)=>{if(!g.value||g.modifier)return null;let m=g.value.value;if(m===null)return null;if(/^[\w-]*\s*\(/.test(m)){let p=m.replace(/\b(and|or|not)\b/g," $1 ");f.nodes=[z("@supports",p,f.nodes)];return}m.includes(":")||(m=`${m}: var(--tw)`),(m[0]!=="("||m[m.length-1]!==")")&&(m=`(${m})`),f.nodes=[z("@supports",m,f.nodes)]},{compounds:1}),i("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),i("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),i("contrast-more",["@media (prefers-contrast: more)"]),i("contrast-less",["@media (prefers-contrast: less)"]);{let f=function(g,m,p,w){if(g===m)return 0;let v=w.get(g);if(v===null)return p==="asc"?-1:1;let y=w.get(m);return y===null?p==="asc"?1:-1:we(v,y,p)};var u=f;{let g=r.namespace("--breakpoint"),m=new W(p=>{switch(p.kind){case"static":return r.resolveValue(p.root,["--breakpoint"])??null;case"functional":{if(!p.value||p.modifier)return null;let w=null;return p.value.kind==="arbitrary"?w=p.value.value:p.value.kind==="named"&&(w=r.resolveValue(p.value.value,["--breakpoint"])),!w||w.includes("var(")?null:w}case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("max",(p,w)=>{if(w.modifier)return null;let v=m.get(w);if(v===null)return null;p.nodes=[z("@media",`(width < ${v})`,p.nodes)]},{compounds:1})},(p,w)=>f(p,w,"desc",m)),t.suggest("max",()=>Array.from(g.keys()).filter(p=>p!==null)),t.group(()=>{for(let[p,w]of r.namespace("--breakpoint"))p!==null&&t.static(p,v=>{v.nodes=[z("@media",`(width >= ${w})`,v.nodes)]},{compounds:1});t.functional("min",(p,w)=>{if(w.modifier)return null;let v=m.get(w);if(v===null)return null;p.nodes=[z("@media",`(width >= ${v})`,p.nodes)]},{compounds:1})},(p,w)=>f(p,w,"asc",m)),t.suggest("min",()=>Array.from(g.keys()).filter(p=>p!==null))}{let g=r.namespace("--container"),m=new W(p=>{switch(p.kind){case"functional":{if(p.value===null)return null;let w=null;return p.value.kind==="arbitrary"?w=p.value.value:p.value.kind==="named"&&(w=r.resolveValue(p.value.value,["--container"])),!w||w.includes("var(")?null:w}case"static":case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("@max",(p,w)=>{let v=m.get(w);if(v===null)return null;p.nodes=[z("@container",w.modifier?`${w.modifier.value} (width < ${v})`:`(width < ${v})`,p.nodes)]},{compounds:1})},(p,w)=>f(p,w,"desc",m)),t.suggest("@max",()=>Array.from(g.keys()).filter(p=>p!==null)),t.group(()=>{t.functional("@",(p,w)=>{let v=m.get(w);if(v===null)return null;p.nodes=[z("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,p.nodes)]},{compounds:1}),t.functional("@min",(p,w)=>{let v=m.get(w);if(v===null)return null;p.nodes=[z("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,p.nodes)]},{compounds:1})},(p,w)=>f(p,w,"asc",m)),t.suggest("@min",()=>Array.from(g.keys()).filter(p=>p!==null)),t.suggest("@",()=>Array.from(g.keys()).filter(p=>p!==null))}}return i("portrait",["@media (orientation: portrait)"]),i("landscape",["@media (orientation: landscape)"]),i("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),i("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),i("dark",["@media (prefers-color-scheme: dark)"]),i("starting",["@starting-style"]),i("print",["@media print"]),i("forced-colors",["@media (forced-colors: active)"]),i("inverted-colors",["@media (inverted-colors: inverted)"]),i("pointer-none",["@media (pointer: none)"]),i("pointer-coarse",["@media (pointer: coarse)"]),i("pointer-fine",["@media (pointer: fine)"]),i("any-pointer-none",["@media (any-pointer: none)"]),i("any-pointer-coarse",["@media (any-pointer: coarse)"]),i("any-pointer-fine",["@media (any-pointer: fine)"]),i("noscript",["@media (scripting: none)"]),t}function yr(r){if(r.includes("=")){let[t,...i]=L(r,"="),e=i.join("=").trim();if(e[0]==="'"||e[0]==='"')return r;if(e.length>1){let o=e[e.length-1];if(e[e.length-2]===" "&&(o==="i"||o==="I"||o==="s"||o==="S"))return`${t}="${e.slice(0,-2)}" ${o}`}return`${t}="${e}"`}return r}function At(r,t){j(r,(i,{replaceWith:e})=>{if(i.kind==="at-rule"&&i.name==="@slot")e(t);else if(i.kind==="at-rule"&&(i.name==="@keyframes"||i.name==="@property"))return Object.assign(i,F([z(i.name,i.params,i.nodes)])),1})}function Ct(r,t){let i=0;return j(r,(e,{replaceWith:o})=>{if(e.kind!=="at-rule"||e.name!=="@variant")return;let s=B("&",e.nodes),l=e.params,d=t.parseVariant(l);if(d===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${l}`);if(Ce(s,d,t.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${l}`);o(s),i|=32}),i}function br(r){let t=pr(r),i=kr(r),e=new W(u=>nr(u,d)),o=new W(u=>Array.from(ir(u,d))),s=new W(u=>new W(f=>{let g=xr(f,d,u);try{xe(g.map(({node:m})=>m),d)}catch{return[]}return g})),l=new W(u=>{for(let f of qe(u))r.markUsedVariable(f)}),d={theme:r,utilities:t,variants:i,invalidCandidates:new Set,important:!1,candidatesToCss(u){let f=[];for(let g of u){let m=!1,{astNodes:p}=pe([g],this,{onInvalidCandidate(){m=!0}});p=ve(p,d,0),p.length===0||m?f.push(null):f.push(ie(p))}return f},getClassOrder(u){return wr(this,u)},getClassList(){return hr(this)},getVariants(){return vr(this)},parseCandidate(u){return o.get(u)},parseVariant(u){return e.get(u)},compileAstNodes(u,f=1){return s.get(f).get(u)},printCandidate(u){return ar(d,u)},printVariant(u){return He(u)},getVariantOrder(){let u=Array.from(e.values());u.sort((p,w)=>this.variants.compare(p,w));let f=new Map,g,m=0;for(let p of u)p!==null&&(g!==void 0&&this.variants.compare(g,p)!==0&&m++,f.set(p,m),g=p);return f},resolveThemeValue(u,f=!0){let g=u.lastIndexOf("/"),m=null;g!==-1&&(m=u.slice(g+1).trim(),u=u.slice(0,g).trim());let p=r.resolve(null,[u],f?1:0)??void 0;return m&&p?Q(p,m):p},trackUsedVariables(u){l.get(u)}};return d}var Vt=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","mask-image","--tw-mask-top","--tw-mask-top-from-color","--tw-mask-top-from-position","--tw-mask-top-to-color","--tw-mask-top-to-position","--tw-mask-right","--tw-mask-right-from-color","--tw-mask-right-from-position","--tw-mask-right-to-color","--tw-mask-right-to-position","--tw-mask-bottom","--tw-mask-bottom-from-color","--tw-mask-bottom-from-position","--tw-mask-bottom-to-color","--tw-mask-bottom-to-position","--tw-mask-left","--tw-mask-left-from-color","--tw-mask-left-from-position","--tw-mask-left-to-color","--tw-mask-left-to-position","--tw-mask-linear","--tw-mask-linear-position","--tw-mask-linear-from-color","--tw-mask-linear-from-position","--tw-mask-linear-to-color","--tw-mask-linear-to-position","--tw-mask-radial","--tw-mask-radial-shape","--tw-mask-radial-size","--tw-mask-radial-position","--tw-mask-radial-from-color","--tw-mask-radial-from-position","--tw-mask-radial-to-color","--tw-mask-radial-to-position","--tw-mask-conic","--tw-mask-conic-position","--tw-mask-conic-from-color","--tw-mask-conic-from-position","--tw-mask-conic-to-color","--tw-mask-conic-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","mask-composite","mask-mode","mask-type","mask-size","mask-clip","mask-position","mask-repeat","mask-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function pe(r,t,{onInvalidCandidate:i,respectImportant:e}={}){let o=new Map,s=[],l=new Map;for(let f of r){if(t.invalidCandidates.has(f)){i?.(f);continue}let g=t.parseCandidate(f);if(g.length===0){i?.(f);continue}l.set(f,g)}let d=0;(e??!0)&&(d|=1);let u=t.getVariantOrder();for(let[f,g]of l){let m=!1;for(let p of g){let w=t.compileAstNodes(p,d);if(w.length!==0){m=!0;for(let{node:v,propertySort:y}of w){let x=0n;for(let S of p.variants)x|=1n<<BigInt(u.get(S));o.set(v,{properties:y,variants:x,candidate:f}),s.push(v)}}}m||i?.(f)}return s.sort((f,g)=>{let m=o.get(f),p=o.get(g);if(m.variants-p.variants!==0n)return Number(m.variants-p.variants);let w=0;for(;w<m.properties.order.length&&w<p.properties.order.length&&m.properties.order[w]===p.properties.order[w];)w+=1;return(m.properties.order[w]??1/0)-(p.properties.order[w]??1/0)||p.properties.count-m.properties.count||Qe(m.candidate,p.candidate)}),{astNodes:s,nodeSorting:o}}function xr(r,t,i){let e=ji(r,t);if(e.length===0)return[];let o=t.important&&!!(i&1),s=[],l=`.${fe(r.raw)}`;for(let d of e){let u=Ui(d);(r.important||o)&&Cr(d);let f={kind:"rule",selector:l,nodes:d};for(let g of r.variants)if(Ce(f,g,t.variants)===null)return[];s.push({node:f,propertySort:u})}return s}function Ce(r,t,i,e=0){if(t.kind==="arbitrary"){if(t.relative&&e===0)return null;r.nodes=[Y(t.selector,r.nodes)];return}let{applyFn:o}=i.get(t.root);if(t.kind==="compound"){let l=z("@slot");if(Ce(l,t.variant,i,e+1)===null||t.root==="not"&&l.nodes.length>1)return null;for(let u of l.nodes)if(u.kind!=="rule"&&u.kind!=="at-rule"||o(u,t)===null)return null;j(l.nodes,u=>{if((u.kind==="rule"||u.kind==="at-rule")&&u.nodes.length<=0)return u.nodes=r.nodes,1}),r.nodes=l.nodes;return}if(o(r,t)===null)return null}function Ar(r){let t=r.options?.types??[];return t.length>1&&t.includes("any")}function ji(r,t){if(r.kind==="arbitrary"){let l=r.value;return r.modifier&&(l=X(l,r.modifier,t.theme)),l===null?[]:[[n(r.property,l)]]}let i=t.utilities.get(r.root)??[],e=[],o=i.filter(l=>!Ar(l));for(let l of o){if(l.kind!==r.kind)continue;let d=l.compileFn(r);if(d!==void 0){if(d===null)return e;e.push(d)}}if(e.length>0)return e;let s=i.filter(l=>Ar(l));for(let l of s){if(l.kind!==r.kind)continue;let d=l.compileFn(r);if(d!==void 0){if(d===null)return e;e.push(d)}}return e}function Cr(r){for(let t of r)t.kind!=="at-root"&&(t.kind==="declaration"?t.important=!0:(t.kind==="rule"||t.kind==="at-rule")&&Cr(t.nodes))}function Ui(r){let t=new Set,i=0,e=r.slice(),o=!1;for(;e.length>0;){let s=e.shift();if(s.kind==="declaration"){if(s.value===void 0||(i++,o))continue;if(s.property==="--tw-sort"){let d=Vt.indexOf(s.value??"");if(d!==-1){t.add(d),o=!0;continue}}let l=Vt.indexOf(s.property);l!==-1&&t.add(l)}else if(s.kind==="rule"||s.kind==="at-rule")for(let l of s.nodes)e.push(l)}return{order:Array.from(t).sort((s,l)=>s-l),count:i}}function Ke(r,t){let i=0,e=Y("&",r),o=new Set,s=new W(()=>new Set),l=new W(()=>new Set);j([e],(m,{parent:p,path:w})=>{if(m.kind==="at-rule"){if(m.name==="@keyframes")return j(m.nodes,v=>{if(v.kind==="at-rule"&&v.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(m.name==="@utility"){let v=m.params.replace(/-\*$/,"");l.get(v).add(m),j(m.nodes,y=>{if(!(y.kind!=="at-rule"||y.name!=="@apply")){o.add(m);for(let x of Vr(y,t))s.get(m).add(x)}});return}if(m.name==="@apply"){if(p===null)return;i|=1,o.add(p);for(let v of Vr(m,t))for(let y of w)y!==m&&o.has(y)&&s.get(y).add(v)}}});let d=new Set,u=[],f=new Set;function g(m,p=[]){if(!d.has(m)){if(f.has(m)){let w=p[(p.indexOf(m)+1)%p.length];throw m.kind==="at-rule"&&m.name==="@utility"&&w.kind==="at-rule"&&w.name==="@utility"&&j(m.nodes,v=>{if(v.kind!=="at-rule"||v.name!=="@apply")return;let y=v.params.split(/\s+/g);for(let x of y)for(let S of t.parseCandidate(x))switch(S.kind){case"arbitrary":break;case"static":case"functional":if(w.params.replace(/-\*$/,"")===S.root)throw new Error(`You cannot \`@apply\` the \`${x}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${ie([m])}
Relies on:

${ie([w])}`)}f.add(m);for(let w of s.get(m))for(let v of l.get(w))p.push(m),g(v,p),p.pop();d.add(m),f.delete(m),u.push(m)}}for(let m of o)g(m);for(let m of u)"nodes"in m&&j(m.nodes,(p,{replaceWith:w})=>{if(p.kind!=="at-rule"||p.name!=="@apply")return;let v=p.params.split(/(\s+)/g),y={},x=0;for(let[S,C]of v.entries())S%2===0&&(y[C]=x),x+=C.length;{let S=Object.keys(y),C=pe(S,t,{respectImportant:!1,onInvalidCandidate:_=>{if(t.theme.prefix&&!_.startsWith(t.theme.prefix))throw new Error(`Cannot apply unprefixed utility class \`${_}\`. Did you mean \`${t.theme.prefix}:${_}\`?`);if(t.invalidCandidates.has(_))throw new Error(`Cannot apply utility class \`${_}\` because it has been explicitly disabled: https://tailwindcss.com/docs/detecting-classes-in-source-files#explicitly-excluding-classes`);let D=L(_,":");if(D.length>1){let G=D.pop();if(t.candidatesToCss([G])[0]){let K=t.candidatesToCss(D.map(q=>`${q}:[--tw-variant-check:1]`)),U=D.filter((q,M)=>K[M]===null);if(U.length>0){if(U.length===1)throw new Error(`Cannot apply utility class \`${_}\` because the ${U.map(q=>`\`${q}\``)} variant does not exist.`);{let q=new Intl.ListFormat("en",{style:"long",type:"conjunction"});throw new Error(`Cannot apply utility class \`${_}\` because the ${q.format(U.map(M=>`\`${M}\``))} variants do not exist.`)}}}}throw t.theme.size===0?new Error(`Cannot apply unknown utility class \`${_}\`. Are you using CSS modules or similar and missing \`@reference\`? https://tailwindcss.com/docs/functions-and-directives#reference-directive`):new Error(`Cannot apply unknown utility class \`${_}\``)}}),b=p.src,O=C.astNodes.map(_=>{let D=C.nodeSorting.get(_)?.candidate,G=D?y[D]:void 0;if(_=structuredClone(_),!b||!D||G===void 0)return j([_],U=>{U.src=b}),_;let K=[b[0],b[1],b[2]];return K[1]+=7+G,K[2]=K[1]+D.length,j([_],U=>{U.src=K}),_}),R=[];for(let _ of O)if(_.kind==="rule")for(let D of _.nodes)R.push(D);else R.push(_);w(R)}});return i}function*Vr(r,t){for(let i of r.params.split(/\s+/g))for(let e of t.parseCandidate(i))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function $t(r,t,i,e=0,o=!1){let s=0,l=[];return j(r,(d,{replaceWith:u})=>{if(d.kind==="at-rule"&&(d.name==="@import"||d.name==="@reference")){let f=Ii(J(d.params));if(f===null)return;d.name==="@reference"&&(f.media="reference"),s|=2;let{uri:g,layer:m,media:p,supports:w}=f;if(g.startsWith("data:")||g.startsWith("http://")||g.startsWith("https://"))return;let v=ae({},[]);return l.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${g}\` in \`${t}\`)`);let y=await i(g,t),x=me(y.content,{from:o?y.path:void 0});await $t(x,y.base,i,e+1,o),v.nodes=Li(d,[ae({base:y.base},x)],m,p,w)})()),u(v),1}}),l.length>0&&await Promise.all(l),s}function Ii(r){let t,i=null,e=null,o=null;for(let s=0;s<r.length;s++){let l=r[s];if(l.kind!=="separator"){if(l.kind==="word"&&!t){if(!l.value||l.value[0]!=='"'&&l.value[0]!=="'")return null;t=l.value.slice(1,-1);continue}if(l.kind==="function"&&l.value.toLowerCase()==="url"||!t)return null;if((l.kind==="word"||l.kind==="function")&&l.value.toLowerCase()==="layer"){if(i)return null;if(o)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in l?i=Z(l.nodes):i="";continue}if(l.kind==="function"&&l.value.toLowerCase()==="supports"){if(o)return null;o=Z(l.nodes);continue}e=Z(r.slice(s));break}}return t?{uri:t,layer:i,media:e,supports:o}:null}function Li(r,t,i,e,o){let s=t;if(i!==null){let l=z("@layer",i,s);l.src=r.src,s=[l]}if(e!==null){let l=z("@media",e,s);l.src=r.src,s=[l]}if(o!==null){let l=z("@supports",o[0]==="("?o:`(${o})`,s);l.src=r.src,s=[l]}return s}function Ve(r,t=null){return Array.isArray(r)&&r.length===2&&typeof r[1]=="object"&&typeof r[1]!==null?t?r[1][t]??null:r[0]:Array.isArray(r)&&t===null?r.join(", "):typeof r=="string"&&t===null?r:null}function $r(r,{theme:t},i){for(let e of i){let o=et([e]);o&&r.theme.clearNamespace(`--${o}`,4)}for(let[e,o]of Fi(t)){if(typeof o!="string"&&typeof o!="number")continue;if(typeof o=="string"&&(o=o.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof o=="number"||typeof o=="string")){let l=typeof o=="string"?parseFloat(o):o;l>=0&&l<=1&&(o=l*100+"%")}let s=et(e);s&&r.theme.add(`--${s}`,""+o,7)}if(Object.hasOwn(t,"fontFamily")){let e=5;{let o=Ve(t.fontFamily.sans);o&&r.theme.hasDefault("--font-sans")&&(r.theme.add("--default-font-family",o,e),r.theme.add("--default-font-feature-settings",Ve(t.fontFamily.sans,"fontFeatureSettings")??"normal",e),r.theme.add("--default-font-variation-settings",Ve(t.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let o=Ve(t.fontFamily.mono);o&&r.theme.hasDefault("--font-mono")&&(r.theme.add("--default-mono-font-family",o,e),r.theme.add("--default-mono-font-feature-settings",Ve(t.fontFamily.mono,"fontFeatureSettings")??"normal",e),r.theme.add("--default-mono-font-variation-settings",Ve(t.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return t}function Fi(r){let t=[];return Nr(r,[],(i,e)=>{if(Mi(i))return t.push([e,i]),1;if(Wi(i)){t.push([e,i[0]]);for(let o of Reflect.ownKeys(i[1]))t.push([[...e,`-${o}`],i[1][o]]);return 1}if(Array.isArray(i)&&i.every(o=>typeof o=="string"))return e[0]==="fontSize"?(t.push([e,i[0]]),i.length>=2&&t.push([[...e,"-line-height"],i[1]])):t.push([e,i.join(", ")]),1}),t}var zi=/^[a-zA-Z0-9-_%/\.]+$/;function et(r){if(r[0]==="container")return null;r=structuredClone(r),r[0]==="animation"&&(r[0]="animate"),r[0]==="aspectRatio"&&(r[0]="aspect"),r[0]==="borderRadius"&&(r[0]="radius"),r[0]==="boxShadow"&&(r[0]="shadow"),r[0]==="colors"&&(r[0]="color"),r[0]==="containers"&&(r[0]="container"),r[0]==="fontFamily"&&(r[0]="font"),r[0]==="fontSize"&&(r[0]="text"),r[0]==="letterSpacing"&&(r[0]="tracking"),r[0]==="lineHeight"&&(r[0]="leading"),r[0]==="maxWidth"&&(r[0]="container"),r[0]==="screens"&&(r[0]="breakpoint"),r[0]==="transitionTimingFunction"&&(r[0]="ease");for(let t of r)if(!zi.test(t))return null;return r.map((t,i,e)=>t==="1"&&i!==e.length-1?"":t).map(t=>t.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(i,e,o)=>`${e}-${o.toLowerCase()}`)).filter((t,i)=>t!=="DEFAULT"||i!==r.length-1).join("-")}function Mi(r){return typeof r=="number"||typeof r=="string"}function Wi(r){if(!Array.isArray(r)||r.length!==2||typeof r[0]!="string"&&typeof r[0]!="number"||r[1]===void 0||r[1]===null||typeof r[1]!="object")return!1;for(let t of Reflect.ownKeys(r[1]))if(typeof t!="string"||typeof r[1][t]!="string"&&typeof r[1][t]!="number")return!1;return!0}function Nr(r,t=[],i){for(let e of Reflect.ownKeys(r)){let o=r[e];if(o==null)continue;let s=[...t,e],l=i(o,s)??0;if(l!==1){if(l===2)return 2;if(!(!Array.isArray(o)&&typeof o!="object")&&Nr(o,s,i)===2)return 2}}}function tt(r){let t=[];for(let i of L(r,".")){if(!i.includes("[")){t.push(i);continue}let e=0;for(;;){let o=i.indexOf("[",e),s=i.indexOf("]",o);if(o===-1||s===-1)break;o>e&&t.push(i.slice(e,o)),t.push(i.slice(o+1,s)),e=s+1}e<=i.length-1&&t.push(i.slice(e))}return t}function $e(r){if(Object.prototype.toString.call(r)!=="[object Object]")return!1;let t=Object.getPrototypeOf(r);return t===null||Object.getPrototypeOf(t)===null}function Oe(r,t,i,e=[]){for(let o of t)if(o!=null)for(let s of Reflect.ownKeys(o)){e.push(s);let l=i(r[s],o[s],e);l!==void 0?r[s]=l:!$e(r[s])||!$e(o[s])?r[s]=o[s]:r[s]=Oe({},[r[s],o[s]],i,e),e.pop()}return r}function rt(r,t,i){return function(o,s){let l=o.lastIndexOf("/"),d=null;l!==-1&&(d=o.slice(l+1).trim(),o=o.slice(0,l).trim());let u=(()=>{let f=tt(o),[g,m]=Bi(r.theme,f),p=i(Sr(t()??{},f)??null);if(typeof p=="string"&&(p=p.replace("<alpha-value>","1")),typeof g!="object")return typeof m!="object"&&m&4?p??g:g;if(p!==null&&typeof p=="object"&&!Array.isArray(p)){let w=Oe({},[p],(v,y)=>y);if(g===null&&Object.hasOwn(p,"__CSS_VALUES__")){let v={};for(let y in p.__CSS_VALUES__)v[y]=p[y],delete w[y];g=v}for(let v in g)v!=="__CSS_VALUES__"&&(p?.__CSS_VALUES__?.[v]&4&&Sr(w,v.split("-"))!==void 0||(w[ge(v)]=g[v]));return w}if(Array.isArray(g)&&Array.isArray(m)&&Array.isArray(p)){let w=g[0],v=g[1];m[0]&4&&(w=p[0]??w);for(let y of Object.keys(v))m[1][y]&4&&(v[y]=p[1][y]??v[y]);return[w,v]}return g??p})();return d&&typeof u=="string"&&(u=Q(u,d)),u??s}}function Bi(r,t){if(t.length===1&&t[0].startsWith("--"))return[r.get([t[0]]),r.getOptions(t[0])];let i=et(t),e=new Map,o=new W(()=>new Map),s=r.namespace(`--${i}`);if(s.size===0)return[null,0];let l=new Map;for(let[g,m]of s){if(!g||!g.includes("--")){e.set(g,m),l.set(g,r.getOptions(g?`--${i}-${g}`:`--${i}`));continue}let p=g.indexOf("--"),w=g.slice(0,p),v=g.slice(p+2);v=v.replace(/-([a-z])/g,(y,x)=>x.toUpperCase()),o.get(w===""?null:w).set(v,[m,r.getOptions(`--${i}${g}`)])}let d=r.getOptions(`--${i}`);for(let[g,m]of o){let p=e.get(g);if(typeof p!="string")continue;let w={},v={};for(let[y,[x,S]]of m)w[y]=x,v[y]=S;e.set(g,[p,w]),l.set(g,[d,v])}let u={},f={};for(let[g,m]of e)Tr(u,[g??"DEFAULT"],m);for(let[g,m]of l)Tr(f,[g??"DEFAULT"],m);return t[t.length-1]==="DEFAULT"?[u?.DEFAULT??null,f.DEFAULT??0]:"DEFAULT"in u&&Object.keys(u).length===1?[u.DEFAULT,f.DEFAULT??0]:(u.__CSS_VALUES__=f,[u,f])}function Sr(r,t){for(let i=0;i<t.length;++i){let e=t[i];if(r?.[e]===void 0){if(t[i+1]===void 0)return;t[i+1]=`${e}-${t[i+1]}`;continue}r=r[e]}return r}function Tr(r,t,i){for(let e of t.slice(0,-1))r[e]===void 0&&(r[e]={}),r=r[e];r[t[t.length-1]]=i}function qi(r){return{kind:"combinator",value:r}}function Gi(r,t){return{kind:"function",value:r,nodes:t}}function _e(r){return{kind:"selector",value:r}}function Ji(r){return{kind:"separator",value:r}}function Hi(r){return{kind:"value",value:r}}function De(r,t,i=null){for(let e=0;e<r.length;e++){let o=r[e],s=!1,l=0,d=t(o,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(r.splice(e,1),l=0):u.length===1?(r[e]=u[0],l=1):(r.splice(e,1,...u),l=u.length):(r[e]=u,l=1))}})??0;if(s){d===0?e--:e+=l-1;continue}if(d===2)return 2;if(d!==1&&o.kind==="function"&&De(o.nodes,t,o)===2)return 2}}function je(r){let t="";for(let i of r)switch(i.kind){case"combinator":case"selector":case"separator":case"value":{t+=i.value;break}case"function":t+=i.value+"("+je(i.nodes)+")"}return t}var Er=92,Yi=93,Rr=41,Zi=58,Pr=44,Qi=34,Xi=46,Kr=62,Or=10,en=35,_r=91,Dr=40,jr=43,tn=39,Ur=32,Ir=9,Lr=126;function it(r){r=r.replaceAll(`\r
`,`
`);let t=[],i=[],e=null,o="",s;for(let l=0;l<r.length;l++){let d=r.charCodeAt(l);switch(d){case Pr:case Kr:case Or:case Ur:case jr:case Ir:case Lr:{if(o.length>0){let p=_e(o);e?e.nodes.push(p):t.push(p),o=""}let u=l,f=l+1;for(;f<r.length&&(s=r.charCodeAt(f),!(s!==Pr&&s!==Kr&&s!==Or&&s!==Ur&&s!==jr&&s!==Ir&&s!==Lr));f++);l=f-1;let g=r.slice(u,f),m=g.trim()===","?Ji(g):qi(g);e?e.nodes.push(m):t.push(m);break}case Dr:{let u=Gi(o,[]);if(o="",u.value!==":not"&&u.value!==":where"&&u.value!==":has"&&u.value!==":is"){let f=l+1,g=0;for(let p=l+1;p<r.length;p++){if(s=r.charCodeAt(p),s===Dr){g++;continue}if(s===Rr){if(g===0){l=p;break}g--}}let m=l;u.nodes.push(Hi(r.slice(f,m))),o="",l=m,e?e.nodes.push(u):t.push(u);break}e?e.nodes.push(u):t.push(u),i.push(u),e=u;break}case Rr:{let u=i.pop();if(o.length>0){let f=_e(o);u.nodes.push(f),o=""}i.length>0?e=i[i.length-1]:e=null;break}case Xi:case Zi:case en:{if(o.length>0){let u=_e(o);e?e.nodes.push(u):t.push(u)}o=String.fromCharCode(d);break}case _r:{if(o.length>0){let g=_e(o);e?e.nodes.push(g):t.push(g)}o="";let u=l,f=0;for(let g=l+1;g<r.length;g++){if(s=r.charCodeAt(g),s===_r){f++;continue}if(s===Yi){if(f===0){l=g;break}f--}}o+=r.slice(u,l+1);break}case tn:case Qi:{let u=l;for(let f=l+1;f<r.length;f++)if(s=r.charCodeAt(f),s===Er)f+=1;else if(s===d){l=f;break}o+=r.slice(u,l+1);break}case Er:{let u=r.charCodeAt(l+1);o+=String.fromCharCode(d)+String.fromCharCode(u),l+=1;break}default:o+=String.fromCharCode(d)}}return o.length>0&&t.push(_e(o)),t}var Fr=/^[a-z@][a-zA-Z0-9/%._-]*$/;function Nt({designSystem:r,ast:t,resolvedConfig:i,featuresRef:e,referenceMode:o,src:s}){let l={addBase(d){if(o)return;let u=le(d);e.current|=xe(u,r);let f=z("@layer","base",u);j([f],g=>{g.src=s}),t.push(f)},addVariant(d,u){if(!Xe.test(d))throw new Error(`\`addVariant('${d}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes, or underscore characters and start with a lowercase letter or number.`);if(typeof u=="string"){if(u.includes(":merge("))return}else if(Array.isArray(u)){if(u.some(g=>g.includes(":merge(")))return}else if(typeof u=="object"){let g=function(m,p){return Object.entries(m).some(([w,v])=>w.includes(p)||typeof v=="object"&&g(v,p))};var f=g;if(g(u,":merge("))return}typeof u=="string"||Array.isArray(u)?r.variants.static(d,g=>{g.nodes=zr(u,g.nodes)},{compounds:ye(typeof u=="string"?[u]:u)}):typeof u=="object"&&r.variants.fromAst(d,le(u),r)},matchVariant(d,u,f){function g(p,w,v){let y=u(p,{modifier:w?.value??null});return zr(y,v)}try{let p=u("a",{modifier:null});if(typeof p=="string"&&p.includes(":merge("))return;if(Array.isArray(p)&&p.some(w=>w.includes(":merge(")))return}catch{}let m=Object.keys(f?.values??{});r.variants.group(()=>{r.variants.functional(d,(p,w)=>{if(!w.value){if(f?.values&&"DEFAULT"in f.values){p.nodes=g(f.values.DEFAULT,w.modifier,p.nodes);return}return null}if(w.value.kind==="arbitrary")p.nodes=g(w.value.value,w.modifier,p.nodes);else if(w.value.kind==="named"&&f?.values){let v=f.values[w.value.value];if(typeof v!="string")return null;p.nodes=g(v,w.modifier,p.nodes)}else return null})},(p,w)=>{if(p.kind!=="functional"||w.kind!=="functional")return 0;let v=p.value?p.value.value:"DEFAULT",y=w.value?w.value.value:"DEFAULT",x=f?.values?.[v]??v,S=f?.values?.[y]??y;if(f&&typeof f.sort=="function")return f.sort({value:x,modifier:p.modifier?.value??null},{value:S,modifier:w.modifier?.value??null});let C=m.indexOf(v),b=m.indexOf(y);return C=C===-1?m.length:C,b=b===-1?m.length:b,C!==b?C-b:x<S?-1:1}),r.variants.suggest(d,()=>Object.keys(f?.values??{}).filter(p=>p!=="DEFAULT"))},addUtilities(d){d=Array.isArray(d)?d:[d];let u=d.flatMap(g=>Object.entries(g));u=u.flatMap(([g,m])=>L(g,",").map(p=>[p.trim(),m]));let f=new W(()=>[]);for(let[g,m]of u){if(g.startsWith("@keyframes ")){if(!o){let v=Y(g,le(m));j([v],y=>{y.src=s}),t.push(v)}continue}let p=it(g),w=!1;if(De(p,v=>{if(v.kind==="selector"&&v.value[0]==="."&&Fr.test(v.value.slice(1))){let y=v.value;v.value="&";let x=je(p),S=y.slice(1),C=x==="&"?le(m):[Y(x,le(m))];f.get(S).push(...C),w=!0,v.value=y;return}if(v.kind==="function"&&v.value===":not")return 1}),!w)throw new Error(`\`addUtilities({ '${g}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[g,m]of f)r.theme.prefix&&j(m,p=>{if(p.kind==="rule"){let w=it(p.selector);De(w,v=>{v.kind==="selector"&&v.value[0]==="."&&(v.value=`.${r.theme.prefix}\\:${v.value.slice(1)}`)}),p.selector=je(w)}}),r.utilities.static(g,p=>{let w=structuredClone(m);return Mr(w,g,p.raw),e.current|=Ke(w,r),w})},matchUtilities(d,u){let f=u?.type?Array.isArray(u?.type)?u.type:[u.type]:["any"];for(let[m,p]of Object.entries(d)){let w=function({negative:v}){return y=>{if(y.value?.kind==="arbitrary"&&f.length>0&&!f.includes("any")&&(y.value.dataType&&!f.includes(y.value.dataType)||!y.value.dataType&&!H(y.value.value,f)))return;let x=f.includes("color"),S=null,C=!1;{let R=u?.values??{};x&&(R=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentcolor"},R)),y.value?y.value.kind==="arbitrary"?S=y.value.value:y.value.fraction&&R[y.value.fraction]?(S=R[y.value.fraction],C=!0):R[y.value.value]?S=R[y.value.value]:R.__BARE_VALUE__&&(S=R.__BARE_VALUE__(y.value)??null,C=(y.value.fraction!==null&&S?.includes("/"))??!1):S=R.DEFAULT??null}if(S===null)return;let b;{let R=u?.modifiers??null;y.modifier?R==="any"||y.modifier.kind==="arbitrary"?b=y.modifier.value:R?.[y.modifier.value]?b=R[y.modifier.value]:x&&!Number.isNaN(Number(y.modifier.value))?b=`${y.modifier.value}%`:b=null:b=null}if(y.modifier&&b===null&&!C)return y.value?.kind==="arbitrary"?null:void 0;x&&b!==null&&(S=Q(S,b)),v&&(S=`calc(${S} * -1)`);let O=le(p(S,{modifier:b}));return Mr(O,m,y.raw),e.current|=Ke(O,r),O}};var g=w;if(!Fr.test(m))throw new Error(`\`matchUtilities({ '${m}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);u?.supportsNegativeValues&&r.utilities.functional(`-${m}`,w({negative:!0}),{types:f}),r.utilities.functional(m,w({negative:!1}),{types:f}),r.utilities.suggest(m,()=>{let v=u?.values??{},y=new Set(Object.keys(v));y.delete("__BARE_VALUE__"),y.delete("__CSS_VALUES__"),y.has("DEFAULT")&&(y.delete("DEFAULT"),y.add(null));let x=u?.modifiers??{},S=x==="any"?[]:Object.keys(x);return[{supportsNegative:u?.supportsNegativeValues??!1,values:Array.from(y),modifiers:S}]})}},addComponents(d,u){this.addUtilities(d,u)},matchComponents(d,u){this.matchUtilities(d,u)},theme:rt(r,()=>i.theme??{},d=>d),prefix(d){return d},config(d,u){let f=i;if(!d)return f;let g=tt(d);for(let m=0;m<g.length;++m){let p=g[m];if(f[p]===void 0)return u;f=f[p]}return f??u}};return l.addComponents=l.addComponents.bind(l),l.matchComponents=l.matchComponents.bind(l),l}function le(r){let t=[];r=Array.isArray(r)?r:[r];let i=r.flatMap(e=>Object.entries(e));for(let[e,o]of i)if(o!=null&&o!==!1)if(typeof o!="object"){if(!e.startsWith("--")){if(o==="@slot"){t.push(Y(e,[z("@slot")]));continue}e=e.replace(/([A-Z])/g,"-$1").toLowerCase()}t.push(n(e,String(o)))}else if(Array.isArray(o))for(let s of o)typeof s=="string"?t.push(n(e,s)):t.push(Y(e,le(s)));else t.push(Y(e,le(o)));return t}function zr(r,t){return(typeof r=="string"?[r]:r).flatMap(e=>{if(e.trim().endsWith("}")){let o=e.replace("}","{@slot}}"),s=me(o);return At(s,t),s}else return Y(e,t)})}function Mr(r,t,i){j(r,e=>{if(e.kind==="rule"){let o=it(e.selector);De(o,s=>{s.kind==="selector"&&s.value===`.${t}`&&(s.value=`.${fe(i)}`)}),e.selector=je(o)}})}function Wr(r,t,i){for(let e of nn(t))r.theme.addKeyframes(e)}function nn(r){let t=[];if("keyframes"in r.theme)for(let[i,e]of Object.entries(r.theme.keyframes))t.push(z("@keyframes",i,le(e)));return t}function Br(r){return{theme:{...Pt,colors:({theme:t})=>t("color",{}),extend:{fontSize:({theme:t})=>({...t("text",{})}),boxShadow:({theme:t})=>({...t("shadow",{})}),animation:({theme:t})=>({...t("animate",{})}),aspectRatio:({theme:t})=>({...t("aspect",{})}),borderRadius:({theme:t})=>({...t("radius",{})}),screens:({theme:t})=>({...t("breakpoint",{})}),letterSpacing:({theme:t})=>({...t("tracking",{})}),lineHeight:({theme:t})=>({...t("leading",{})}),transitionDuration:{DEFAULT:r.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:r.get(["--default-transition-timing-function"])??null},maxWidth:({theme:t})=>({...t("container",{})})}}}}var on={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function Tt(r,t){let i={design:r,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(on)};for(let o of t)St(i,o);for(let o of i.configs)"darkMode"in o&&o.darkMode!==void 0&&(i.result.darkMode=o.darkMode??null),"prefix"in o&&o.prefix!==void 0&&(i.result.prefix=o.prefix??""),"blocklist"in o&&o.blocklist!==void 0&&(i.result.blocklist=o.blocklist??[]),"important"in o&&o.important!==void 0&&(i.result.important=o.important??!1);let e=ln(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:e}}function an(r,t){if(Array.isArray(r)&&$e(r[0]))return r.concat(t);if(Array.isArray(t)&&$e(t[0])&&$e(r))return[r,...t];if(Array.isArray(t))return t}function St(r,{config:t,base:i,path:e,reference:o,src:s}){let l=[];for(let f of t.plugins??[])"__isOptionsFunction"in f?l.push({...f(),reference:o,src:s}):"handler"in f?l.push({...f,reference:o,src:s}):l.push({handler:f,reference:o,src:s});if(Array.isArray(t.presets)&&t.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let f of t.presets??[])St(r,{path:e,base:i,config:f,reference:o,src:s});for(let f of l)r.plugins.push(f),f.config&&St(r,{path:e,base:i,config:f.config,reference:!!f.reference,src:f.src??s});let d=t.content??[],u=Array.isArray(d)?d:d.files;for(let f of u)r.content.files.push(typeof f=="object"?f:{base:i,pattern:f});r.configs.push(t)}function ln(r){let t=new Set,i=rt(r.design,()=>r.theme,o),e=Object.assign(i,{theme:i,colors:Rt});function o(s){return typeof s=="function"?s(e)??null:s??null}for(let s of r.configs){let l=s.theme??{},d=l.extend??{};for(let u in l)u!=="extend"&&t.add(u);Object.assign(r.theme,l);for(let u in d)r.extend[u]??=[],r.extend[u].push(d[u])}delete r.theme.extend;for(let s in r.extend){let l=[r.theme[s],...r.extend[s]];r.theme[s]=()=>{let d=l.map(o);return Oe({},d,an)}}for(let s in r.theme)r.theme[s]=o(r.theme[s]);if(r.theme.screens&&typeof r.theme.screens=="object")for(let s of Object.keys(r.theme.screens)){let l=r.theme.screens[s];l&&typeof l=="object"&&("raw"in l||"max"in l||"min"in l&&(r.theme.screens[s]=l.min))}return t}function qr(r,t){let i=r.theme.container||{};if(typeof i!="object"||i===null)return;let e=sn(i,t);e.length!==0&&t.utilities.static("container",()=>structuredClone(e))}function sn({center:r,padding:t,screens:i},e){let o=[],s=null;if(r&&o.push(n("margin-inline","auto")),(typeof t=="string"||typeof t=="object"&&t!==null&&"DEFAULT"in t)&&o.push(n("padding-inline",typeof t=="string"?t:t.DEFAULT)),typeof i=="object"&&i!==null){s=new Map;let l=Array.from(e.theme.namespace("--breakpoint").entries());if(l.sort((d,u)=>we(d[1],u[1],"asc")),l.length>0){let[d]=l[0];o.push(z("@media",`(width >= --theme(--breakpoint-${d}))`,[n("max-width","none")]))}for(let[d,u]of Object.entries(i)){if(typeof u=="object")if("min"in u)u=u.min;else continue;s.set(d,z("@media",`(width >= ${u})`,[n("max-width",u)]))}}if(typeof t=="object"&&t!==null){let l=Object.entries(t).filter(([d])=>d!=="DEFAULT").map(([d,u])=>[d,e.theme.resolveValue(d,["--breakpoint"]),u]).filter(Boolean);l.sort((d,u)=>we(d[1],u[1],"asc"));for(let[d,,u]of l)if(s&&s.has(d))s.get(d).nodes.push(n("padding-inline",u));else{if(s)continue;o.push(z("@media",`(width >= theme(--breakpoint-${d}))`,[n("padding-inline",u)]))}}if(s)for(let[,l]of s)o.push(l);return o}function Gr({addVariant:r,config:t}){let i=t("darkMode",null),[e,o=".dark"]=Array.isArray(i)?i:[i];if(e==="variant"){let s;if(Array.isArray(o)||typeof o=="function"?s=o:typeof o=="string"&&(s=[o]),Array.isArray(s))for(let l of s)l===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):l.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));o=s}e===null||(e==="selector"?r("dark",`&:where(${o}, ${o} *)`):e==="media"?r("dark","@media (prefers-color-scheme: dark)"):e==="variant"?r("dark",o):e==="class"&&r("dark",`&:is(${o} *)`))}function Jr(r){for(let[t,i]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])r.utilities.static(`bg-gradient-to-${t}`,()=>[n("--tw-gradient-position",`to ${i} in oklab`),n("background-image","linear-gradient(var(--tw-gradient-stops))")]);r.utilities.static("bg-left-top",()=>[n("background-position","left top")]),r.utilities.static("bg-right-top",()=>[n("background-position","right top")]),r.utilities.static("bg-left-bottom",()=>[n("background-position","left bottom")]),r.utilities.static("bg-right-bottom",()=>[n("background-position","right bottom")]),r.utilities.static("object-left-top",()=>[n("object-position","left top")]),r.utilities.static("object-right-top",()=>[n("object-position","right top")]),r.utilities.static("object-left-bottom",()=>[n("object-position","left bottom")]),r.utilities.static("object-right-bottom",()=>[n("object-position","right bottom")]),r.utilities.functional("max-w-screen",t=>{if(!t.value||t.value.kind==="arbitrary")return;let i=r.theme.resolve(t.value.value,["--breakpoint"]);if(i)return[n("max-width",i)]}),r.utilities.static("overflow-ellipsis",()=>[n("text-overflow","ellipsis")]),r.utilities.static("decoration-slice",()=>[n("-webkit-box-decoration-break","slice"),n("box-decoration-break","slice")]),r.utilities.static("decoration-clone",()=>[n("-webkit-box-decoration-break","clone"),n("box-decoration-break","clone")]),r.utilities.functional("flex-shrink",t=>{if(!t.modifier){if(!t.value)return[n("flex-shrink","1")];if(t.value.kind==="arbitrary")return[n("flex-shrink",t.value.value)];if(E(t.value.value))return[n("flex-shrink",t.value.value)]}}),r.utilities.functional("flex-grow",t=>{if(!t.modifier){if(!t.value)return[n("flex-grow","1")];if(t.value.kind==="arbitrary")return[n("flex-grow",t.value.value)];if(E(t.value.value))return[n("flex-grow",t.value.value)]}}),r.utilities.static("order-none",()=>[n("order","0")])}function Hr(r,t){let i=r.theme.screens||{},e=t.variants.get("min")?.order??0,o=[];for(let[l,d]of Object.entries(i)){let p=function(w){t.variants.static(l,v=>{v.nodes=[z("@media",m,v.nodes)]},{order:w})};var s=p;let u=t.variants.get(l),f=t.theme.resolveValue(l,["--breakpoint"]);if(u&&f&&!t.theme.hasDefault(`--breakpoint-${l}`))continue;let g=!0;typeof d=="string"&&(g=!1);let m=un(d);g?o.push(p):p(e)}if(o.length!==0){for(let[,l]of t.variants.variants)l.order>e&&(l.order+=o.length);t.variants.compareFns=new Map(Array.from(t.variants.compareFns).map(([l,d])=>(l>e&&(l+=o.length),[l,d])));for(let[l,d]of o.entries())d(e+l+1)}}function un(r){return(Array.isArray(r)?r:[r]).map(i=>typeof i=="string"?{min:i}:i&&typeof i=="object"?i:null).map(i=>{if(i===null)return null;if("raw"in i)return i.raw;let e="";return i.max!==void 0&&(e+=`${i.max} >= `),e+="width",i.min!==void 0&&(e+=` >= ${i.min}`),`(${e})`}).filter(Boolean).join(", ")}function Yr(r,t){let i=r.theme.aria||{},e=r.theme.supports||{},o=r.theme.data||{};if(Object.keys(i).length>0){let s=t.variants.get("aria"),l=s?.applyFn,d=s?.compounds;t.variants.functional("aria",(u,f)=>{let g=f.value;return g&&g.kind==="named"&&g.value in i?l?.(u,{...f,value:{kind:"arbitrary",value:i[g.value]}}):l?.(u,f)},{compounds:d})}if(Object.keys(e).length>0){let s=t.variants.get("supports"),l=s?.applyFn,d=s?.compounds;t.variants.functional("supports",(u,f)=>{let g=f.value;return g&&g.kind==="named"&&g.value in e?l?.(u,{...f,value:{kind:"arbitrary",value:e[g.value]}}):l?.(u,f)},{compounds:d})}if(Object.keys(o).length>0){let s=t.variants.get("data"),l=s?.applyFn,d=s?.compounds;t.variants.functional("data",(u,f)=>{let g=f.value;return g&&g.kind==="named"&&g.value in o?l?.(u,{...f,value:{kind:"arbitrary",value:o[g.value]}}):l?.(u,f)},{compounds:d})}}var fn=/^[a-z]+$/;async function Qr({designSystem:r,base:t,ast:i,loadModule:e,sources:o}){let s=0,l=[],d=[];j(i,(m,{parent:p,replaceWith:w,context:v})=>{if(m.kind==="at-rule"){if(m.name==="@plugin"){if(p!==null)throw new Error("`@plugin` cannot be nested.");let y=m.params.slice(1,-1);if(y.length===0)throw new Error("`@plugin` must have a path.");let x={};for(let S of m.nodes??[]){if(S.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${ie([S])}

\`@plugin\` options must be a flat list of declarations.`);if(S.value===void 0)continue;let C=S.value,b=L(C,",").map(O=>{if(O=O.trim(),O==="null")return null;if(O==="true")return!0;if(O==="false")return!1;if(Number.isNaN(Number(O))){if(O[0]==='"'&&O[O.length-1]==='"'||O[0]==="'"&&O[O.length-1]==="'")return O.slice(1,-1);if(O[0]==="{"&&O[O.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${ie([S]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(O);return O});x[S.property]=b.length===1?b[0]:b}l.push([{id:y,base:v.base,reference:!!v.reference,src:m.src},Object.keys(x).length>0?x:null]),w([]),s|=4;return}if(m.name==="@config"){if(m.nodes.length>0)throw new Error("`@config` cannot have a body.");if(p!==null)throw new Error("`@config` cannot be nested.");d.push({id:m.params.slice(1,-1),base:v.base,reference:!!v.reference,src:m.src}),w([]),s|=4;return}}}),Jr(r);let u=r.resolveThemeValue;if(r.resolveThemeValue=function(p,w){return p.startsWith("--")?u(p,w):(s|=Zr({designSystem:r,base:t,ast:i,sources:o,configs:[],pluginDetails:[]}),r.resolveThemeValue(p,w))},!l.length&&!d.length)return 0;let[f,g]=await Promise.all([Promise.all(d.map(async({id:m,base:p,reference:w,src:v})=>{let y=await e(m,p,"config");return{path:m,base:y.base,config:y.module,reference:w,src:v}})),Promise.all(l.map(async([{id:m,base:p,reference:w,src:v},y])=>{let x=await e(m,p,"plugin");return{path:m,base:x.base,plugin:x.module,options:y,reference:w,src:v}}))]);return s|=Zr({designSystem:r,base:t,ast:i,sources:o,configs:f,pluginDetails:g}),s}function Zr({designSystem:r,base:t,ast:i,sources:e,configs:o,pluginDetails:s}){let l=0,u=[...s.map(y=>{if(!y.options)return{config:{plugins:[y.plugin]},base:y.base,reference:y.reference,src:y.src};if("__isOptionsFunction"in y.plugin)return{config:{plugins:[y.plugin(y.options)]},base:y.base,reference:y.reference,src:y.src};throw new Error(`The plugin "${y.path}" does not accept options`)}),...o],{resolvedConfig:f}=Tt(r,[{config:Br(r.theme),base:t,reference:!0,src:void 0},...u,{config:{plugins:[Gr]},base:t,reference:!0,src:void 0}]),{resolvedConfig:g,replacedThemeKeys:m}=Tt(r,u),p={designSystem:r,ast:i,resolvedConfig:f,featuresRef:{set current(y){l|=y}}},w=Nt({...p,referenceMode:!1,src:void 0}),v=r.resolveThemeValue;r.resolveThemeValue=function(x,S){if(x[0]==="-"&&x[1]==="-")return v(x,S);let C=w.theme(x,void 0);if(Array.isArray(C)&&C.length===2)return C[0];if(Array.isArray(C))return C.join(", ");if(typeof C=="string")return C};for(let{handler:y,reference:x,src:S}of f.plugins){let C=Nt({...p,referenceMode:x??!1,src:S});y(C)}if($r(r,g,m),Wr(r,g,m),Yr(g,r),Hr(g,r),qr(g,r),!r.theme.prefix&&f.prefix){if(f.prefix.endsWith("-")&&(f.prefix=f.prefix.slice(0,-1),console.warn(`The prefix "${f.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!fn.test(f.prefix))throw new Error(`The prefix "${f.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);r.theme.prefix=f.prefix}if(!r.important&&f.important===!0&&(r.important=!0),typeof f.important=="string"){let y=f.important;j(i,(x,{replaceWith:S,parent:C})=>{if(x.kind==="at-rule"&&!(x.name!=="@tailwind"||x.params!=="utilities"))return C?.kind==="rule"&&C.selector===y?2:(S(B(y,[x])),2)})}for(let y of f.blocklist)r.invalidCandidates.add(y);for(let y of f.content.files){if("raw"in y)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(y,null,2)}

This feature is not currently supported.`);let x=!1;y.pattern[0]=="!"&&(x=!0,y.pattern=y.pattern.slice(1)),e.push({...y,negated:x})}return l}function Xr(r){let t=[0];for(let o=0;o<r.length;o++)r.charCodeAt(o)===10&&t.push(o+1);function i(o){let s=0,l=t.length;for(;l>0;){let u=(l|0)>>1,f=s+u;t[f]<=o?(s=f+1,l=l-u-1):l=u}s-=1;let d=o-t[s];return{line:s+1,column:d}}function e({line:o,column:s}){o-=1,o=Math.min(Math.max(o,0),t.length-1);let l=t[o],d=t[o+1]??l;return Math.min(Math.max(l+s,0),d)}return{find:i,findOffset:e}}function ei({ast:r}){let t=new W(o=>Xr(o.code)),i=new W(o=>({url:o.file,content:o.code,ignore:!1})),e={file:null,sources:[],mappings:[]};j(r,o=>{if(!o.src||!o.dst)return;let s=i.get(o.src[0]);if(!s.content)return;let l=t.get(o.src[0]),d=t.get(o.dst[0]),u=s.content.slice(o.src[1],o.src[2]),f=0;for(let p of u.split(`
`)){if(p.trim()!==""){let w=l.find(o.src[1]+f),v=d.find(o.dst[1]);e.mappings.push({name:null,originalPosition:{source:s,...w},generatedPosition:v})}f+=p.length,f+=1}let g=l.find(o.src[2]),m=d.find(o.dst[2]);e.mappings.push({name:null,originalPosition:{source:s,...g},generatedPosition:m})});for(let o of t.keys())e.sources.push(i.get(o));return e.mappings.sort((o,s)=>o.generatedPosition.line-s.generatedPosition.line||o.generatedPosition.column-s.generatedPosition.column||(o.originalPosition?.line??0)-(s.originalPosition?.line??0)||(o.originalPosition?.column??0)-(s.originalPosition?.column??0)),e}var ti=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/;function nt(r){let t=r.indexOf("{");if(t===-1)return[r];let i=[],e=r.slice(0,t),o=r.slice(t),s=0,l=o.lastIndexOf("}");for(let m=0;m<o.length;m++){let p=o[m];if(p==="{")s++;else if(p==="}"&&(s--,s===0)){l=m;break}}if(l===-1)throw new Error(`The pattern \`${r}\` is not balanced.`);let d=o.slice(1,l),u=o.slice(l+1),f;cn(d)?f=pn(d):f=L(d,","),f=f.flatMap(m=>nt(m));let g=nt(u);for(let m of g)for(let p of f)i.push(e+p+m);return i}function cn(r){return ti.test(r)}function pn(r){let t=r.match(ti);if(!t)return[r];let[,i,e,o]=t,s=o?parseInt(o,10):void 0,l=[];if(/^-?\d+$/.test(i)&&/^-?\d+$/.test(e)){let d=parseInt(i,10),u=parseInt(e,10);if(s===void 0&&(s=d<=u?1:-1),s===0)throw new Error("Step cannot be zero in sequence expansion.");let f=d<u;f&&s<0&&(s=-s),!f&&s>0&&(s=-s);for(let g=d;f?g<=u:g>=u;g+=s)l.push(g.toString())}return l}function ri(r,t){let i=new Set,e=new Set,o=[];function s(l,d=[]){if(r.has(l)&&!i.has(l)){e.has(l)&&t.onCircularDependency?.(d,l),e.add(l);for(let u of r.get(l)??[])d.push(l),s(u,d),d.pop();i.add(l),e.delete(l),o.push(l)}}for(let l of r.keys())s(l);return o}var dn=/^[a-z]+$/,dt=(o=>(o[o.None=0]="None",o[o.AtProperty=1]="AtProperty",o[o.ColorMix=2]="ColorMix",o[o.All=3]="All",o))(dt||{});function mn(){throw new Error("No `loadModule` function provided to `compile`")}function gn(){throw new Error("No `loadStylesheet` function provided to `compile`")}function hn(r){let t=0,i=null;for(let e of L(r," "))e==="reference"?t|=2:e==="inline"?t|=1:e==="default"?t|=4:e==="static"?t|=8:e.startsWith("prefix(")&&e.endsWith(")")&&(i=e.slice(7,-1));return[t,i]}var Ae=(u=>(u[u.None=0]="None",u[u.AtApply=1]="AtApply",u[u.AtImport=2]="AtImport",u[u.JsPluginCompat=4]="JsPluginCompat",u[u.ThemeFunction=8]="ThemeFunction",u[u.Utilities=16]="Utilities",u[u.Variants=32]="Variants",u[u.AtTheme=64]="AtTheme",u))(Ae||{});async function ii(r,{base:t="",from:i,loadModule:e=mn,loadStylesheet:o=gn}={}){let s=0;r=[ae({base:t},r)],s|=await $t(r,t,o,0,i!==void 0);let l=null,d=new Be,u=new Map,f=new Map,g=[],m=null,p=null,w=[],v=[],y=[],x=[],S=null;j(r,(b,{parent:O,replaceWith:R,context:_})=>{if(b.kind==="at-rule"){if(b.name==="@tailwind"&&(b.params==="utilities"||b.params.startsWith("utilities"))){if(p!==null){R([]);return}if(_.reference){R([]);return}let D=L(b.params," ");for(let G of D)if(G.startsWith("source(")){let K=G.slice(7,-1);if(K==="none"){S=K;continue}if(K[0]==='"'&&K[K.length-1]!=='"'||K[0]==="'"&&K[K.length-1]!=="'"||K[0]!=="'"&&K[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");S={base:_.sourceBase??_.base,pattern:K.slice(1,-1)}}p=b,s|=16}if(b.name==="@utility"){if(O!==null)throw new Error("`@utility` cannot be nested.");if(b.nodes.length===0)throw new Error(`\`@utility ${b.params}\` is empty. Utilities should include at least one property.`);let D=dr(b);if(D===null){if(!b.params.endsWith("-*")){if(b.params.endsWith("*"))throw new Error(`\`@utility ${b.params}\` defines an invalid utility name. A functional utility must end in \`-*\`.`);if(b.params.includes("*"))throw new Error(`\`@utility ${b.params}\` defines an invalid utility name. The dynamic portion marked by \`-*\` must appear once at the end.`)}throw new Error(`\`@utility ${b.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`)}g.push(D)}if(b.name==="@source"){if(b.nodes.length>0)throw new Error("`@source` cannot have a body.");if(O!==null)throw new Error("`@source` cannot be nested.");let D=!1,G=!1,K=b.params;if(K[0]==="n"&&K.startsWith("not ")&&(D=!0,K=K.slice(4)),K[0]==="i"&&K.startsWith("inline(")&&(G=!0,K=K.slice(7,-1)),K[0]==='"'&&K[K.length-1]!=='"'||K[0]==="'"&&K[K.length-1]!=="'"||K[0]!=="'"&&K[0]!=='"')throw new Error("`@source` paths must be quoted.");let U=K.slice(1,-1);if(G){let q=D?x:y,M=L(U," ");for(let re of M)for(let a of nt(re))q.push(a)}else v.push({base:_.base,pattern:U,negated:D});R([]);return}if(b.name==="@variant"&&(O===null?b.nodes.length===0?b.name="@custom-variant":(j(b.nodes,D=>{if(D.kind==="at-rule"&&D.name==="@slot")return b.name="@custom-variant",2}),b.name==="@variant"&&w.push(b)):w.push(b)),b.name==="@custom-variant"){if(O!==null)throw new Error("`@custom-variant` cannot be nested.");R([]);let[D,G]=L(b.params," ");if(!Xe.test(D))throw new Error(`\`@custom-variant ${D}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes, or underscore characters and start with a lowercase letter or number.`);if(b.nodes.length>0&&G)throw new Error(`\`@custom-variant ${D}\` cannot have both a selector and a body.`);if(b.nodes.length===0){if(!G)throw new Error(`\`@custom-variant ${D}\` has no selector or body.`);let K=L(G.slice(1,-1),",");if(K.length===0||K.some(M=>M.trim()===""))throw new Error(`\`@custom-variant ${D} (${K.join(",")})\` selector is invalid.`);let U=[],q=[];for(let M of K)M=M.trim(),M[0]==="@"?U.push(M):q.push(M);u.set(D,M=>{M.variants.static(D,re=>{let a=[];q.length>0&&a.push(B(q.join(", "),re.nodes));for(let c of U)a.push(Y(c,re.nodes));re.nodes=a},{compounds:ye([...q,...U])})}),f.set(D,new Set);return}else{let K=new Set;j(b.nodes,U=>{U.kind==="at-rule"&&U.name==="@variant"&&K.add(U.params)}),u.set(D,U=>{U.variants.fromAst(D,b.nodes,U)}),f.set(D,K);return}}if(b.name==="@media"){let D=L(b.params," "),G=[];for(let K of D)if(K.startsWith("source(")){let U=K.slice(7,-1);j(b.nodes,(q,{replaceWith:M})=>{if(q.kind==="at-rule"&&q.name==="@tailwind"&&q.params==="utilities")return q.params+=` source(${U})`,M([ae({sourceBase:_.base},[q])]),2})}else if(K.startsWith("theme(")){let U=K.slice(6,-1),q=U.includes("reference");j(b.nodes,M=>{if(M.kind!=="at-rule"){if(q)throw new Error('Files imported with `@import "\u2026" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "\u2026";` instead.');return 0}if(M.name==="@theme")return M.params+=" "+U,1})}else if(K.startsWith("prefix(")){let U=K.slice(7,-1);j(b.nodes,q=>{if(q.kind==="at-rule"&&q.name==="@theme")return q.params+=` prefix(${U})`,1})}else K==="important"?l=!0:K==="reference"?b.nodes=[ae({reference:!0},b.nodes)]:G.push(K);G.length>0?b.params=G.join(" "):D.length>0&&R(b.nodes)}if(b.name==="@theme"){let[D,G]=hn(b.params);if(s|=64,_.reference&&(D|=2),G){if(!dn.test(G))throw new Error(`The prefix "${G}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);d.prefix=G}return j(b.nodes,K=>{if(K.kind==="at-rule"&&K.name==="@keyframes")return d.addKeyframes(K),1;if(K.kind==="comment")return;if(K.kind==="declaration"&&K.property.startsWith("--")){d.add(ge(K.property),K.value??"",D,K.src);return}let U=ie([z(b.name,b.params,[K])]).split(`
`).map((q,M,re)=>`${M===0||M>=re.length-2?" ":">"} ${q}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${U}`)}),m?R([]):(m=B(":root, :host",[]),m.src=b.src,R([m])),1}}});let C=br(d);if(l&&(C.important=l),x.length>0)for(let b of x)C.invalidCandidates.add(b);s|=await Qr({designSystem:C,base:t,ast:r,loadModule:e,sources:v});for(let b of u.keys())C.variants.static(b,()=>{});for(let b of ri(f,{onCircularDependency(O,R){let _=ie(O.map((D,G)=>z("@custom-variant",D,[z("@variant",O[G+1]??R,[])]))).replaceAll(";"," { \u2026 }").replace(`@custom-variant ${R} {`,`@custom-variant ${R} { /* \u2190 */`);throw new Error(`Circular dependency detected in custom variants:

${_}`)}}))u.get(b)?.(C);for(let b of g)b(C);if(m){let b=[];for(let[R,_]of C.theme.entries()){if(_.options&2)continue;let D=n(fe(R),_.value);D.src=_.src,b.push(D)}let O=C.theme.getKeyframes();for(let R of O)r.push(ae({theme:!0},[F([R])]));m.nodes=[ae({theme:!0},b)]}if(s|=Ct(r,C),s|=xe(r,C),s|=Ke(r,C),p){let b=p;b.kind="context",b.context={}}return j(r,(b,{replaceWith:O})=>{if(b.kind==="at-rule")return b.name==="@utility"&&O([]),1}),{designSystem:C,ast:r,sources:v,root:S,utilitiesNode:p,features:s,inlineCandidates:y}}async function vn(r,t={}){let{designSystem:i,ast:e,sources:o,root:s,utilitiesNode:l,features:d,inlineCandidates:u}=await ii(r,t);e.unshift(We(`! tailwindcss v${Kt} | MIT License | https://tailwindcss.com `));function f(v){i.invalidCandidates.add(v)}let g=new Set,m=null,p=0,w=!1;for(let v of u)i.invalidCandidates.has(v)||(g.add(v),w=!0);return{sources:o,root:s,features:d,build(v){if(d===0)return r;if(!l)return m??=ve(e,i,t.polyfills),m;let y=w,x=!1;w=!1;let S=g.size;for(let b of v)if(!i.invalidCandidates.has(b))if(b[0]==="-"&&b[1]==="-"){let O=i.theme.markUsedVariable(b);y||=O,x||=O}else g.add(b),y||=g.size!==S;if(!y)return m??=ve(e,i,t.polyfills),m;let C=pe(g,i,{onInvalidCandidate:f}).astNodes;return t.from&&j(C,b=>{b.src??=l.src}),!x&&p===C.length?(m??=ve(e,i,t.polyfills),m):(p=C.length,l.nodes=C,m=ve(e,i,t.polyfills),m)}}}async function Ol(r,t={}){let i=me(r,{from:t.from}),e=await vn(i,t),o=i,s=r;return{...e,build(l){let d=e.build(l);return d===o||(s=ie(d,!!t.from),o=d),s},buildSourceMap(){return ei({ast:o})}}}async function _l(r,t={}){return(await ii(me(r),t)).designSystem}function wn(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}export{dt as a,Ae as b,vn as c,Ol as d,_l as e,wn as f};
