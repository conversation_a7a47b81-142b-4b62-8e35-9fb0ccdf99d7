"use strict";var Ci=Object.defineProperty;var $i=(e,r)=>{for(var i in r)Ci(e,i,{get:r[i],enumerable:!0})};var vt={};$i(vt,{Features:()=>Ae,Polyfills:()=>tt,__unstable__loadDesignSystem:()=>po,compile:()=>fo,compileAst:()=>Ai,default:()=>We});var Wt="4.1.14";var Pe=92,Be=47,qe=42,Bt=34,qt=39,Vi=58,Ge=59,oe=10,Ye=13,Oe=32,He=9,Ht=123,kt=125,xt=40,Gt=41,Si=91,Ei=93,Yt=45,yt=64,Ti=33;function ve(e,r){let i=r?.from?{file:r.from,code:e}:null;e[0]==="\uFEFF"&&(e=" "+e.slice(1));let t=[],n=[],s=[],a=null,p=null,u="",c="",m=0,g;for(let d=0;d<e.length;d++){let w=e.charCodeAt(d);if(!(w===Ye&&(g=e.charCodeAt(d+1),g===oe)))if(w===Pe)u===""&&(m=d),u+=e.slice(d,d+2),d+=1;else if(w===Be&&e.charCodeAt(d+1)===qe){let v=d;for(let x=d+2;x<e.length;x++)if(g=e.charCodeAt(x),g===Pe)x+=1;else if(g===qe&&e.charCodeAt(x+1)===Be){d=x+1;break}let k=e.slice(v,d+1);if(k.charCodeAt(2)===Ti){let x=Ze(k.slice(2,-2));n.push(x),i&&(x.src=[i,v,d+1],x.dst=[i,v,d+1])}}else if(w===qt||w===Bt){let v=Zt(e,d,w);u+=e.slice(d,v+1),d=v}else{if((w===Oe||w===oe||w===He)&&(g=e.charCodeAt(d+1))&&(g===Oe||g===oe||g===He||g===Ye&&(g=e.charCodeAt(d+2))&&g==oe))continue;if(w===oe){if(u.length===0)continue;g=u.charCodeAt(u.length-1),g!==Oe&&g!==oe&&g!==He&&(u+=" ")}else if(w===Yt&&e.charCodeAt(d+1)===Yt&&u.length===0){let v="",k=d,x=-1;for(let C=d+2;C<e.length;C++)if(g=e.charCodeAt(C),g===Pe)C+=1;else if(g===qt||g===Bt)C=Zt(e,C,g);else if(g===Be&&e.charCodeAt(C+1)===qe){for(let b=C+2;b<e.length;b++)if(g=e.charCodeAt(b),g===Pe)b+=1;else if(g===qe&&e.charCodeAt(b+1)===Be){C=b+1;break}}else if(x===-1&&g===Vi)x=u.length+C-k;else if(g===Ge&&v.length===0){u+=e.slice(k,C),d=C;break}else if(g===xt)v+=")";else if(g===Si)v+="]";else if(g===Ht)v+="}";else if((g===kt||e.length-1===C)&&v.length===0){d=C-1,u+=e.slice(k,C);break}else(g===Gt||g===Ei||g===kt)&&v.length>0&&e[C]===v[v.length-1]&&(v=v.slice(0,-1));let S=bt(u,x);if(!S)throw new Error("Invalid custom property, expected a value");i&&(S.src=[i,k,d],S.dst=[i,k,d]),a?a.nodes.push(S):t.push(S),u=""}else if(w===Ge&&u.charCodeAt(0)===yt)p=_e(u),i&&(p.src=[i,m,d],p.dst=[i,m,d]),a?a.nodes.push(p):t.push(p),u="",p=null;else if(w===Ge&&c[c.length-1]!==")"){let v=bt(u);if(!v){if(u.length===0)continue;throw new Error(`Invalid declaration: \`${u.trim()}\``)}i&&(v.src=[i,m,d],v.dst=[i,m,d]),a?a.nodes.push(v):t.push(v),u=""}else if(w===Ht&&c[c.length-1]!==")")c+="}",p=Y(u.trim()),i&&(p.src=[i,m,d],p.dst=[i,m,d]),a&&a.nodes.push(p),s.push(a),a=p,u="",p=null;else if(w===kt&&c[c.length-1]!==")"){if(c==="")throw new Error("Missing opening {");if(c=c.slice(0,-1),u.length>0)if(u.charCodeAt(0)===yt)p=_e(u),i&&(p.src=[i,m,d],p.dst=[i,m,d]),a?a.nodes.push(p):t.push(p),u="",p=null;else{let k=u.indexOf(":");if(a){let x=bt(u,k);if(!x)throw new Error(`Invalid declaration: \`${u.trim()}\``);i&&(x.src=[i,m,d],x.dst=[i,m,d]),a.nodes.push(x)}}let v=s.pop()??null;v===null&&a&&t.push(a),a=v,u="",p=null}else if(w===xt)c+=")",u+="(";else if(w===Gt){if(c[c.length-1]!==")")throw new Error("Missing opening (");c=c.slice(0,-1),u+=")"}else{if(u.length===0&&(w===Oe||w===oe||w===He))continue;u===""&&(m=d),u+=String.fromCharCode(w)}}}if(u.charCodeAt(0)===yt){let d=_e(u);i&&(d.src=[i,m,e.length],d.dst=[i,m,e.length]),t.push(d)}if(c.length>0&&a){if(a.kind==="rule")throw new Error(`Missing closing } at ${a.selector}`);if(a.kind==="at-rule")throw new Error(`Missing closing } at ${a.name} ${a.params}`)}return n.length>0?n.concat(t):t}function _e(e,r=[]){let i=e,t="";for(let n=5;n<e.length;n++){let s=e.charCodeAt(n);if(s===Oe||s===xt){i=e.slice(0,n),t=e.slice(n);break}}return F(i.trim(),t.trim(),r)}function bt(e,r=e.indexOf(":")){if(r===-1)return null;let i=e.indexOf("!important",r+1);return o(e.slice(0,r).trim(),e.slice(r+1,i===-1?e.length:i).trim(),i!==-1)}function Zt(e,r,i){let t;for(let n=r+1;n<e.length;n++)if(t=e.charCodeAt(n),t===Pe)n+=1;else{if(t===i)return n;if(t===Ge&&(e.charCodeAt(n+1)===oe||e.charCodeAt(n+1)===Ye&&e.charCodeAt(n+2)===oe))throw new Error(`Unterminated string: ${e.slice(r,n+1)+String.fromCharCode(i)}`);if(t===oe||t===Ye&&e.charCodeAt(n+1)===oe)throw new Error(`Unterminated string: ${e.slice(r,n)+String.fromCharCode(i)}`)}return r}function de(e){if(arguments.length===0)throw new TypeError("`CSS.escape` requires an argument.");let r=String(e),i=r.length,t=-1,n,s="",a=r.charCodeAt(0);if(i===1&&a===45)return"\\"+r;for(;++t<i;){if(n=r.charCodeAt(t),n===0){s+="\uFFFD";continue}if(n>=1&&n<=31||n===127||t===0&&n>=48&&n<=57||t===1&&n>=48&&n<=57&&a===45){s+="\\"+n.toString(16)+" ";continue}if(n>=128||n===45||n===95||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122){s+=r.charAt(t);continue}s+="\\"+r.charAt(t)}return s}function we(e){return e.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,r=>r.length>2?String.fromCodePoint(Number.parseInt(r.slice(1).trim(),16)):r[1])}var Qt=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-decoration-color","--text-decoration-thickness","--text-indent","--text-shadow","--text-underline-offset"]],["--grid-column",["--grid-column-start","--grid-column-end"]],["--grid-row",["--grid-row-start","--grid-row-end"]]]);function Jt(e,r){return(Qt.get(r)??[]).some(i=>e===i||e.startsWith(`${i}-`))}var Je=class{constructor(r=new Map,i=new Set([])){this.values=r;this.keyframes=i}prefix=null;get size(){return this.values.size}add(r,i,t=0,n){if(r.endsWith("-*")){if(i!=="initial")throw new Error(`Invalid theme value \`${i}\` for namespace \`${r}\``);r==="--*"?this.values.clear():this.clearNamespace(r.slice(0,-2),0)}if(t&4){let s=this.values.get(r);if(s&&!(s.options&4))return}i==="initial"?this.values.delete(r):this.values.set(r,{value:i,options:t,src:n})}keysInNamespaces(r){let i=[];for(let t of r){let n=`${t}-`;for(let s of this.values.keys())s.startsWith(n)&&s.indexOf("--",2)===-1&&(Jt(s,t)||i.push(s.slice(n.length)))}return i}get(r){for(let i of r){let t=this.values.get(i);if(t)return t.value}return null}hasDefault(r){return(this.getOptions(r)&4)===4}getOptions(r){return r=we(this.#r(r)),this.values.get(r)?.options??0}entries(){return this.prefix?Array.from(this.values,r=>(r[0]=this.prefixKey(r[0]),r)):this.values.entries()}prefixKey(r){return this.prefix?`--${this.prefix}-${r.slice(2)}`:r}#r(r){return this.prefix?`--${r.slice(3+this.prefix.length)}`:r}clearNamespace(r,i){let t=Qt.get(r)??[];e:for(let n of this.values.keys())if(n.startsWith(r)){if(i!==0&&(this.getOptions(n)&i)!==i)continue;for(let s of t)if(n.startsWith(s))continue e;this.values.delete(n)}}#e(r,i){for(let t of i){let n=r!==null?`${t}-${r}`:t;if(!this.values.has(n))if(r!==null&&r.includes(".")){if(n=`${t}-${r.replaceAll(".","_")}`,!this.values.has(n))continue}else continue;if(!Jt(n,t))return n}return null}#t(r){let i=this.values.get(r);if(!i)return null;let t=null;return i.options&2&&(t=i.value),`var(${de(this.prefixKey(r))}${t?`, ${t}`:""})`}markUsedVariable(r){let i=we(this.#r(r)),t=this.values.get(i);if(!t)return!1;let n=t.options&16;return t.options|=16,!n}resolve(r,i,t=0){let n=this.#e(r,i);if(!n)return null;let s=this.values.get(n);return(t|s.options)&1?s.value:this.#t(n)}resolveValue(r,i){let t=this.#e(r,i);return t?this.values.get(t).value:null}resolveWith(r,i,t=[]){let n=this.#e(r,i);if(!n)return null;let s={};for(let p of t){let u=`${n}${p}`,c=this.values.get(u);c&&(c.options&1?s[p]=c.value:s[p]=this.#t(u))}let a=this.values.get(n);return a.options&1?[a.value,s]:[this.#t(n),s]}namespace(r){let i=new Map,t=`${r}-`;for(let[n,s]of this.values)n===r?i.set(null,s.value):n.startsWith(`${t}-`)?i.set(n.slice(r.length),s.value):n.startsWith(t)&&i.set(n.slice(t.length),s.value);return i}addKeyframes(r){this.keyframes.add(r)}getKeyframes(){return Array.from(this.keyframes)}};var W=class extends Map{constructor(i){super();this.factory=i}get(i){let t=super.get(i);return t===void 0&&(t=this.factory(i,this),this.set(i,t)),t}};function Ct(e){return{kind:"word",value:e}}function Ri(e,r){return{kind:"function",value:e,nodes:r}}function Pi(e){return{kind:"separator",value:e}}function ee(e,r,i=null){for(let t=0;t<e.length;t++){let n=e[t],s=!1,a=0,p=r(n,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(e.splice(t,1),a=0):u.length===1?(e[t]=u[0],a=1):(e.splice(t,1,...u),a=u.length):e[t]=u)}})??0;if(s){p===0?t--:t+=a-1;continue}if(p===2)return 2;if(p!==1&&n.kind==="function"&&ee(n.nodes,r,n)===2)return 2}}function J(e){let r="";for(let i of e)switch(i.kind){case"word":case"separator":{r+=i.value;break}case"function":r+=i.value+"("+J(i.nodes)+")"}return r}var Xt=92,Oi=41,er=58,tr=44,_i=34,rr=61,ir=62,nr=60,or=10,Di=40,Ki=39,lr=47,ar=32,sr=9;function G(e){e=e.replaceAll(`\r
`,`
`);let r=[],i=[],t=null,n="",s;for(let a=0;a<e.length;a++){let p=e.charCodeAt(a);switch(p){case Xt:{n+=e[a]+e[a+1],a++;break}case er:case tr:case rr:case ir:case nr:case or:case lr:case ar:case sr:{if(n.length>0){let g=Ct(n);t?t.nodes.push(g):r.push(g),n=""}let u=a,c=a+1;for(;c<e.length&&(s=e.charCodeAt(c),!(s!==er&&s!==tr&&s!==rr&&s!==ir&&s!==nr&&s!==or&&s!==lr&&s!==ar&&s!==sr));c++);a=c-1;let m=Pi(e.slice(u,c));t?t.nodes.push(m):r.push(m);break}case Ki:case _i:{let u=a;for(let c=a+1;c<e.length;c++)if(s=e.charCodeAt(c),s===Xt)c+=1;else if(s===p){a=c;break}n+=e.slice(u,a+1);break}case Di:{let u=Ri(n,[]);n="",t?t.nodes.push(u):r.push(u),i.push(u),t=u;break}case Oi:{let u=i.pop();if(n.length>0){let c=Ct(n);u?.nodes.push(c),n=""}i.length>0?t=i[i.length-1]:t=null;break}default:n+=String.fromCharCode(p)}}return n.length>0&&r.push(Ct(n)),r}function Qe(e){let r=[];return ee(G(e),i=>{if(!(i.kind!=="function"||i.value!=="var"))return ee(i.nodes,t=>{t.kind!=="word"||t.value[0]!=="-"||t.value[1]!=="-"||r.push(t.value)}),1}),r}var Li=64;function B(e,r=[]){return{kind:"rule",selector:e,nodes:r}}function F(e,r="",i=[]){return{kind:"at-rule",name:e,params:r,nodes:i}}function Y(e,r=[]){return e.charCodeAt(0)===Li?_e(e,r):B(e,r)}function o(e,r,i=!1){return{kind:"declaration",property:e,value:r,important:i}}function Ze(e){return{kind:"comment",value:e}}function se(e,r){return{kind:"context",context:e,nodes:r}}function z(e){return{kind:"at-root",nodes:e}}function L(e,r,i=[],t={}){for(let n=0;n<e.length;n++){let s=e[n],a=i[i.length-1]??null;if(s.kind==="context"){if(L(s.nodes,r,i,{...t,...s.context})===2)return 2;continue}i.push(s);let p=!1,u=0,c=r(s,{parent:a,context:t,path:i,replaceWith(m){p||(p=!0,Array.isArray(m)?m.length===0?(e.splice(n,1),u=0):m.length===1?(e[n]=m[0],u=1):(e.splice(n,1,...m),u=m.length):(e[n]=m,u=1))}})??0;if(i.pop(),p){c===0?n--:n+=u-1;continue}if(c===2)return 2;if(c!==1&&"nodes"in s){i.push(s);let m=L(s.nodes,r,i,t);if(i.pop(),m===2)return 2}}}function Xe(e,r,i=[],t={}){for(let n=0;n<e.length;n++){let s=e[n],a=i[i.length-1]??null;if(s.kind==="rule"||s.kind==="at-rule")i.push(s),Xe(s.nodes,r,i,t),i.pop();else if(s.kind==="context"){Xe(s.nodes,r,i,{...t,...s.context});continue}i.push(s),r(s,{parent:a,context:t,path:i,replaceWith(p){Array.isArray(p)?p.length===0?e.splice(n,1):p.length===1?e[n]=p[0]:e.splice(n,1,...p):e[n]=p,n+=p.length-1}}),i.pop()}}function ye(e,r,i=3){let t=[],n=new Set,s=new W(()=>new Set),a=new W(()=>new Set),p=new Set,u=new Set,c=[],m=[],g=new W(()=>new Set);function d(v,k,x={},S=0){if(v.kind==="declaration"){if(v.property==="--tw-sort"||v.value===void 0||v.value===null)return;if(x.theme&&v.property[0]==="-"&&v.property[1]==="-"){if(v.value==="initial"){v.value=void 0;return}x.keyframes||s.get(k).add(v)}if(v.value.includes("var("))if(x.theme&&v.property[0]==="-"&&v.property[1]==="-")for(let C of Qe(v.value))g.get(C).add(v.property);else r.trackUsedVariables(v.value);if(v.property==="animation")for(let C of ur(v.value))u.add(C);i&2&&v.value.includes("color-mix(")&&a.get(k).add(v),k.push(v)}else if(v.kind==="rule"){let C=[];for(let R of v.nodes)d(R,C,x,S+1);let b={},_=new Set;for(let R of C){if(R.kind!=="declaration")continue;let D=`${R.property}:${R.value}:${R.important}`;b[D]??=[],b[D].push(R)}for(let R in b)for(let D=0;D<b[R].length-1;++D)_.add(b[R][D]);if(_.size>0&&(C=C.filter(R=>!_.has(R))),C.length===0)return;v.selector==="&"?k.push(...C):k.push({...v,nodes:C})}else if(v.kind==="at-rule"&&v.name==="@property"&&S===0){if(n.has(v.params))return;if(i&1){let b=v.params,_=null,R=!1;for(let K of v.nodes)K.kind==="declaration"&&(K.property==="initial-value"?_=K.value:K.property==="inherits"&&(R=K.value==="true"));let D=o(b,_??"initial");D.src=v.src,R?c.push(D):m.push(D)}n.add(v.params);let C={...v,nodes:[]};for(let b of v.nodes)d(b,C.nodes,x,S+1);k.push(C)}else if(v.kind==="at-rule"){v.name==="@keyframes"&&(x={...x,keyframes:!0});let C={...v,nodes:[]};for(let b of v.nodes)d(b,C.nodes,x,S+1);v.name==="@keyframes"&&x.theme&&p.add(C),(C.nodes.length>0||C.name==="@layer"||C.name==="@charset"||C.name==="@custom-media"||C.name==="@namespace"||C.name==="@import")&&k.push(C)}else if(v.kind==="at-root")for(let C of v.nodes){let b=[];d(C,b,x,0);for(let _ of b)t.push(_)}else if(v.kind==="context"){if(v.context.reference)return;for(let C of v.nodes)d(C,k,{...x,...v.context},S)}else v.kind==="comment"&&k.push(v)}let w=[];for(let v of e)d(v,w,{},0);e:for(let[v,k]of s)for(let x of k){if(cr(x.property,r.theme,g)){if(x.property.startsWith(r.theme.prefixKey("--animate-")))for(let b of ur(x.value))u.add(b);continue}let C=v.indexOf(x);if(v.splice(C,1),v.length===0){let b=Ii(w,_=>_.kind==="rule"&&_.nodes===v);if(!b||b.length===0)continue e;b.unshift({kind:"at-root",nodes:w});do{let _=b.pop();if(!_)break;let R=b[b.length-1];if(!R||R.kind!=="at-root"&&R.kind!=="at-rule")break;let D=R.nodes.indexOf(_);if(D===-1)break;R.nodes.splice(D,1)}while(!0);continue e}}for(let v of p)if(!u.has(v.params)){let k=t.indexOf(v);t.splice(k,1)}if(w=w.concat(t),i&2)for(let[v,k]of a)for(let x of k){let S=v.indexOf(x);if(S===-1||x.value==null)continue;let C=G(x.value),b=!1;if(ee(C,(D,{replaceWith:K})=>{if(D.kind!=="function"||D.value!=="color-mix")return;let H=!1,O=!1;if(ee(D.nodes,(I,{replaceWith:q})=>{if(I.kind=="word"&&I.value.toLowerCase()==="currentcolor"){O=!0,b=!0;return}let M=I,ie=null,l=new Set;do{if(M.kind!=="function"||M.value!=="var")return;let f=M.nodes[0];if(!f||f.kind!=="word")return;let h=f.value;if(l.has(h)){H=!0;return}if(l.add(h),b=!0,ie=r.theme.resolveValue(null,[f.value]),!ie){H=!0;return}if(ie.toLowerCase()==="currentcolor"){O=!0;return}ie.startsWith("var(")?M=G(ie)[0]:M=null}while(M);q({kind:"word",value:ie})}),H||O){let I=D.nodes.findIndex(M=>M.kind==="separator"&&M.value.trim().includes(","));if(I===-1)return;let q=D.nodes.length>I?D.nodes[I+1]:null;if(!q)return;K(q)}else if(b){let I=D.nodes[2];I.kind==="word"&&(I.value==="oklab"||I.value==="oklch"||I.value==="lab"||I.value==="lch")&&(I.value="srgb")}}),!b)continue;let _={...x,value:J(C)},R=Y("@supports (color: color-mix(in lab, red, red))",[x]);R.src=x.src,v.splice(S,1,_,R)}if(i&1){let v=[];if(c.length>0){let k=Y(":root, :host",c);k.src=c[0].src,v.push(k)}if(m.length>0){let k=Y("*, ::before, ::after, ::backdrop",m);k.src=m[0].src,v.push(k)}if(v.length>0){let k=w.findIndex(C=>!(C.kind==="comment"||C.kind==="at-rule"&&(C.name==="@charset"||C.name==="@import"))),x=F("@layer","properties",[]);x.src=v[0].src,w.splice(k<0?w.length:k,0,x);let S=Y("@layer properties",[F("@supports","((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))",v)]);S.src=v[0].src,S.nodes[0].src=v[0].src,w.push(S)}}return w}function ne(e,r){let i=0,t={file:null,code:""};function n(a,p=0){let u="",c="  ".repeat(p);if(a.kind==="declaration"){if(u+=`${c}${a.property}: ${a.value}${a.important?" !important":""};
`,r){i+=c.length;let m=i;i+=a.property.length,i+=2,i+=a.value?.length??0,a.important&&(i+=11);let g=i;i+=2,a.dst=[t,m,g]}}else if(a.kind==="rule"){if(u+=`${c}${a.selector} {
`,r){i+=c.length;let m=i;i+=a.selector.length,i+=1;let g=i;a.dst=[t,m,g],i+=2}for(let m of a.nodes)u+=n(m,p+1);u+=`${c}}
`,r&&(i+=c.length,i+=2)}else if(a.kind==="at-rule"){if(a.nodes.length===0){let m=`${c}${a.name} ${a.params};
`;if(r){i+=c.length;let g=i;i+=a.name.length,i+=1,i+=a.params.length;let d=i;i+=2,a.dst=[t,g,d]}return m}if(u+=`${c}${a.name}${a.params?` ${a.params} `:" "}{
`,r){i+=c.length;let m=i;i+=a.name.length,a.params&&(i+=1,i+=a.params.length),i+=1;let g=i;a.dst=[t,m,g],i+=2}for(let m of a.nodes)u+=n(m,p+1);u+=`${c}}
`,r&&(i+=c.length,i+=2)}else if(a.kind==="comment"){if(u+=`${c}/*${a.value}*/
`,r){i+=c.length;let m=i;i+=2+a.value.length+2;let g=i;a.dst=[t,m,g],i+=1}}else if(a.kind==="context"||a.kind==="at-root")return"";return u}let s="";for(let a of e)s+=n(a,0);return t.code=s,s}function Ii(e,r){let i=[];return L(e,(t,{path:n})=>{if(r(t))return i=[...n],2}),i}function cr(e,r,i,t=new Set){if(t.has(e)||(t.add(e),r.getOptions(e)&24))return!0;{let s=i.get(e)??[];for(let a of s)if(cr(a,r,i,t))return!0}return!1}function ur(e){return e.split(/[\s,]+/)}var $t=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"];function De(e){return e.indexOf("(")!==-1&&$t.some(r=>e.includes(`${r}(`))}function fr(e){if(!$t.some(s=>e.includes(s)))return e;let r="",i=[],t=null,n=null;for(let s=0;s<e.length;s++){let a=e.charCodeAt(s);if(a>=48&&a<=57||t!==null&&(a===37||a>=97&&a<=122||a>=65&&a<=90)?t=s:(n=t,t=null),a===40){r+=e[s];let p=s;for(let c=s-1;c>=0;c--){let m=e.charCodeAt(c);if(m>=48&&m<=57)p=c;else if(m>=97&&m<=122)p=c;else break}let u=e.slice(p,s);if($t.includes(u)){i.unshift(!0);continue}else if(i[0]&&u===""){i.unshift(!0);continue}i.unshift(!1);continue}else if(a===41)r+=e[s],i.shift();else if(a===44&&i[0]){r+=", ";continue}else{if(a===32&&i[0]&&r.charCodeAt(r.length-1)===32)continue;if((a===43||a===42||a===47||a===45)&&i[0]){let p=r.trimEnd(),u=p.charCodeAt(p.length-1),c=p.charCodeAt(p.length-2),m=e.charCodeAt(s+1);if((u===101||u===69)&&c>=48&&c<=57){r+=e[s];continue}else if(u===43||u===42||u===47||u===45){r+=e[s];continue}else if(u===40||u===44){r+=e[s];continue}else e.charCodeAt(s-1)===32?r+=`${e[s]} `:u>=48&&u<=57||m>=48&&m<=57||u===41||m===40||m===43||m===42||m===47||m===45||n!==null&&n===s-1?r+=` ${e[s]} `:r+=e[s]}else r+=e[s]}}return r}function me(e){if(e.indexOf("(")===-1)return Ne(e);let r=G(e);return Nt(r),e=J(r),e=fr(e),e}function Ne(e,r=!1){let i="";for(let t=0;t<e.length;t++){let n=e[t];n==="\\"&&e[t+1]==="_"?(i+="_",t+=1):n==="_"&&!r?i+=" ":i+=n}return i}function Nt(e){for(let r of e)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=Ne(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=Ne(r.value);for(let i=0;i<r.nodes.length;i++){if(i==0&&r.nodes[i].kind==="word"){r.nodes[i].value=Ne(r.nodes[i].value,!0);continue}Nt([r.nodes[i]])}break}r.value=Ne(r.value),Nt(r.nodes);break}case"separator":case"word":{r.value=Ne(r.value);break}default:ji(r)}}function ji(e){throw new Error(`Unexpected value: ${e}`)}var Vt=new Uint8Array(256);function ce(e){let r=0,i=e.length;for(let t=0;t<i;t++){let n=e.charCodeAt(t);switch(n){case 92:t+=1;break;case 39:case 34:for(;++t<i;){let s=e.charCodeAt(t);if(s===92){t+=1;continue}if(s===n)break}break;case 40:Vt[r]=41,r++;break;case 91:Vt[r]=93,r++;break;case 123:break;case 93:case 125:case 41:if(r===0)return!1;r>0&&n===Vt[r-1]&&r--;break;case 59:if(r===0)return!1;break}}return!0}var rt=new Uint8Array(256);function U(e,r){let i=0,t=[],n=0,s=e.length,a=r.charCodeAt(0);for(let p=0;p<s;p++){let u=e.charCodeAt(p);if(i===0&&u===a){t.push(e.slice(n,p)),n=p+1;continue}switch(u){case 92:p+=1;break;case 39:case 34:for(;++p<s;){let c=e.charCodeAt(p);if(c===92){p+=1;continue}if(c===u)break}break;case 40:rt[i]=41,i++;break;case 91:rt[i]=93,i++;break;case 123:rt[i]=125,i++;break;case 93:case 125:case 41:i>0&&u===rt[i-1]&&i--;break}}return t.push(e.slice(n)),t}var zi=58,pr=45,dr=97,mr=122;function*gr(e,r){let i=U(e,":");if(r.theme.prefix){if(i.length===1||i[0]!==r.theme.prefix)return null;i.shift()}let t=i.pop(),n=[];for(let g=i.length-1;g>=0;--g){let d=r.parseVariant(i[g]);if(d===null)return;n.push(d)}let s=!1;t[t.length-1]==="!"?(s=!0,t=t.slice(0,-1)):t[0]==="!"&&(s=!0,t=t.slice(1)),r.utilities.has(t,"static")&&!t.includes("[")&&(yield{kind:"static",root:t,variants:n,important:s,raw:e});let[a,p=null,u]=U(t,"/");if(u)return;let c=p===null?null:St(p);if(p!==null&&c===null)return;if(a[0]==="["){if(a[a.length-1]!=="]")return;let g=a.charCodeAt(1);if(g!==pr&&!(g>=dr&&g<=mr))return;a=a.slice(1,-1);let d=a.indexOf(":");if(d===-1||d===0||d===a.length-1)return;let w=a.slice(0,d),v=me(a.slice(d+1));if(!ce(v))return;yield{kind:"arbitrary",property:w,value:v,modifier:c,variants:n,important:s,raw:e};return}let m;if(a[a.length-1]==="]"){let g=a.indexOf("-[");if(g===-1)return;let d=a.slice(0,g);if(!r.utilities.has(d,"functional"))return;let w=a.slice(g+1);m=[[d,w]]}else if(a[a.length-1]===")"){let g=a.indexOf("-(");if(g===-1)return;let d=a.slice(0,g);if(!r.utilities.has(d,"functional"))return;let w=a.slice(g+2,-1),v=U(w,":"),k=null;if(v.length===2&&(k=v[0],w=v[1]),w[0]!=="-"||w[1]!=="-"||!ce(w))return;m=[[d,k===null?`[var(${w})]`:`[${k}:var(${w})]`]]}else m=vr(a,g=>r.utilities.has(g,"functional"));for(let[g,d]of m){let w={kind:"functional",root:g,modifier:c,value:null,variants:n,important:s,raw:e};if(d===null){yield w;continue}{let v=d.indexOf("[");if(v!==-1){if(d[d.length-1]!=="]")return;let x=me(d.slice(v+1,-1));if(!ce(x))continue;let S="";for(let C=0;C<x.length;C++){let b=x.charCodeAt(C);if(b===zi){S=x.slice(0,C),x=x.slice(C+1);break}if(!(b===pr||b>=dr&&b<=mr))break}if(x.length===0||x.trim().length===0)continue;w.value={kind:"arbitrary",dataType:S||null,value:x}}else{let x=p===null||w.modifier?.kind==="arbitrary"?null:`${d}/${p}`;w.value={kind:"named",value:d,fraction:x}}}yield w}}function St(e){if(e[0]==="["&&e[e.length-1]==="]"){let r=me(e.slice(1,-1));return!ce(r)||r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:r}}return e[0]==="("&&e[e.length-1]===")"?(e=e.slice(1,-1),e[0]!=="-"||e[1]!=="-"||!ce(e)?null:(e=`var(${e})`,{kind:"arbitrary",value:me(e)})):{kind:"named",value:e}}function hr(e,r){if(e[0]==="["&&e[e.length-1]==="]"){if(e[1]==="@"&&e.includes("&"))return null;let i=me(e.slice(1,-1));if(!ce(i)||i.length===0||i.trim().length===0)return null;let t=i[0]===">"||i[0]==="+"||i[0]==="~";return!t&&i[0]!=="@"&&!i.includes("&")&&(i=`&:is(${i})`),{kind:"arbitrary",selector:i,relative:t}}{let[i,t=null,n]=U(e,"/");if(n)return null;let s=vr(i,a=>r.variants.has(a));for(let[a,p]of s)switch(r.variants.kind(a)){case"static":return p!==null||t!==null?null:{kind:"static",root:a};case"functional":{let u=t===null?null:St(t);if(t!==null&&u===null)return null;if(p===null)return{kind:"functional",root:a,modifier:u,value:null};if(p[p.length-1]==="]"){if(p[0]!=="[")continue;let c=me(p.slice(1,-1));return!ce(c)||c.length===0||c.trim().length===0?null:{kind:"functional",root:a,modifier:u,value:{kind:"arbitrary",value:c}}}if(p[p.length-1]===")"){if(p[0]!=="(")continue;let c=me(p.slice(1,-1));return!ce(c)||c.length===0||c.trim().length===0||c[0]!=="-"||c[1]!=="-"?null:{kind:"functional",root:a,modifier:u,value:{kind:"arbitrary",value:`var(${c})`}}}return{kind:"functional",root:a,modifier:u,value:{kind:"named",value:p}}}case"compound":{if(p===null)return null;let u=r.parseVariant(p);if(u===null||!r.variants.compoundsWith(a,u))return null;let c=t===null?null:St(t);return t!==null&&c===null?null:{kind:"compound",root:a,modifier:c,variant:u}}}}return null}function*vr(e,r){r(e)&&(yield[e,null]);let i=e.lastIndexOf("-");for(;i>0;){let t=e.slice(0,i);if(r(t)){let n=[t,e.slice(i+1)];if(n[1]===""||n[0]==="@"&&r("@")&&e[i]==="-")break;yield n}i=e.lastIndexOf("-",i-1)}e[0]==="@"&&r("@")&&(yield["@",e.slice(1)])}function wr(e,r){let i=[];for(let n of r.variants)i.unshift(it(n));e.theme.prefix&&i.unshift(e.theme.prefix);let t="";if(r.kind==="static"&&(t+=r.root),r.kind==="functional"&&(t+=r.root,r.value))if(r.value.kind==="arbitrary"){if(r.value!==null){let n=Tt(r.value.value),s=n?r.value.value.slice(4,-1):r.value.value,[a,p]=n?["(",")"]:["[","]"];r.value.dataType?t+=`-${a}${r.value.dataType}:${Ve(s)}${p}`:t+=`-${a}${Ve(s)}${p}`}}else r.value.kind==="named"&&(t+=`-${r.value.value}`);return r.kind==="arbitrary"&&(t+=`[${r.property}:${Ve(r.value)}]`),(r.kind==="arbitrary"||r.kind==="functional")&&(t+=kr(r.modifier)),r.important&&(t+="!"),i.push(t),i.join(":")}function kr(e){if(e===null)return"";let r=Tt(e.value),i=r?e.value.slice(4,-1):e.value,[t,n]=r?["(",")"]:["[","]"];return e.kind==="arbitrary"?`/${t}${Ve(i)}${n}`:e.kind==="named"?`/${e.value}`:""}function it(e){if(e.kind==="static")return e.root;if(e.kind==="arbitrary")return`[${Ve(Wi(e.selector))}]`;let r="";if(e.kind==="functional"){r+=e.root;let i=e.root!=="@";if(e.value)if(e.value.kind==="arbitrary"){let t=Tt(e.value.value),n=t?e.value.value.slice(4,-1):e.value.value,[s,a]=t?["(",")"]:["[","]"];r+=`${i?"-":""}${s}${Ve(n)}${a}`}else e.value.kind==="named"&&(r+=`${i?"-":""}${e.value.value}`)}return e.kind==="compound"&&(r+=e.root,r+="-",r+=it(e.variant)),(e.kind==="functional"||e.kind==="compound")&&(r+=kr(e.modifier)),r}var Fi=new W(e=>{let r=G(e),i=new Set;return ee(r,(t,{parent:n})=>{let s=n===null?r:n.nodes??[];if(t.kind==="word"&&(t.value==="+"||t.value==="-"||t.value==="*"||t.value==="/")){let a=s.indexOf(t)??-1;if(a===-1)return;let p=s[a-1];if(p?.kind!=="separator"||p.value!==" ")return;let u=s[a+1];if(u?.kind!=="separator"||u.value!==" ")return;i.add(p),i.add(u)}else t.kind==="separator"&&t.value.trim()==="/"?t.value="/":t.kind==="separator"&&t.value.length>0&&t.value.trim()===""?(s[0]===t||s[s.length-1]===t)&&i.add(t):t.kind==="separator"&&t.value.trim()===","&&(t.value=",")}),i.size>0&&ee(r,(t,{replaceWith:n})=>{i.has(t)&&(i.delete(t),n([]))}),Et(r),J(r)});function Ve(e){return Fi.get(e)}var Mi=new W(e=>{let r=G(e);return r.length===3&&r[0].kind==="word"&&r[0].value==="&"&&r[1].kind==="separator"&&r[1].value===":"&&r[2].kind==="function"&&r[2].value==="is"?J(r[2].nodes):e});function Wi(e){return Mi.get(e)}function Et(e){for(let r of e)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=Ke(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=Ke(r.value);for(let i=0;i<r.nodes.length;i++)Et([r.nodes[i]]);break}r.value=Ke(r.value),Et(r.nodes);break}case"separator":r.value=Ke(r.value);break;case"word":{(r.value[0]!=="-"||r.value[1]!=="-")&&(r.value=Ke(r.value));break}default:qi(r)}}var Bi=new W(e=>{let r=G(e);return r.length===1&&r[0].kind==="function"&&r[0].value==="var"});function Tt(e){return Bi.get(e)}function qi(e){throw new Error(`Unexpected value: ${e}`)}function Ke(e){return e.replaceAll("_",String.raw`\_`).replaceAll(" ","_")}function be(e,r,i){if(e===r)return 0;let t=e.indexOf("("),n=r.indexOf("("),s=t===-1?e.replace(/[\d.]+/g,""):e.slice(0,t),a=n===-1?r.replace(/[\d.]+/g,""):r.slice(0,n),p=(s===a?0:s<a?-1:1)||(i==="asc"?parseInt(e)-parseInt(r):parseInt(r)-parseInt(e));return Number.isNaN(p)?e<r?-1:1:p}var Hi=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),Gi=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i;function yr(e){return e.charCodeAt(0)===35||Gi.test(e)||Hi.has(e.toLowerCase())}var Yi={color:yr,length:nt,percentage:Rt,ratio:un,number:xr,integer:T,url:br,position:pn,"bg-size":dn,"line-width":Ji,image:en,"family-name":rn,"generic-name":tn,"absolute-size":nn,"relative-size":on,angle:hn,vector:wn};function Z(e,r){if(e.startsWith("var("))return null;for(let i of r)if(Yi[i]?.(e))return i;return null}var Zi=/^url\(.*\)$/;function br(e){return Zi.test(e)}function Ji(e){return U(e," ").every(r=>nt(r)||xr(r)||r==="thin"||r==="medium"||r==="thick")}var Qi=/^(?:element|image|cross-fade|image-set)\(/,Xi=/^(repeating-)?(conic|linear|radial)-gradient\(/;function en(e){let r=0;for(let i of U(e,","))if(!i.startsWith("var(")){if(br(i)){r+=1;continue}if(Xi.test(i)){r+=1;continue}if(Qi.test(i)){r+=1;continue}return!1}return r>0}function tn(e){return e==="serif"||e==="sans-serif"||e==="monospace"||e==="cursive"||e==="fantasy"||e==="system-ui"||e==="ui-serif"||e==="ui-sans-serif"||e==="ui-monospace"||e==="ui-rounded"||e==="math"||e==="emoji"||e==="fangsong"}function rn(e){let r=0;for(let i of U(e,",")){let t=i.charCodeAt(0);if(t>=48&&t<=57)return!1;i.startsWith("var(")||(r+=1)}return r>0}function nn(e){return e==="xx-small"||e==="x-small"||e==="small"||e==="medium"||e==="large"||e==="x-large"||e==="xx-large"||e==="xxx-large"}function on(e){return e==="larger"||e==="smaller"}var fe=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,ln=new RegExp(`^${fe.source}$`);function xr(e){return ln.test(e)||De(e)}var an=new RegExp(`^${fe.source}%$`);function Rt(e){return an.test(e)||De(e)}var sn=new RegExp(`^${fe.source}s*/s*${fe.source}$`);function un(e){return sn.test(e)||De(e)}var cn=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],fn=new RegExp(`^${fe.source}(${cn.join("|")})$`);function nt(e){return fn.test(e)||De(e)}function pn(e){let r=0;for(let i of U(e," ")){if(i==="center"||i==="top"||i==="right"||i==="bottom"||i==="left"){r+=1;continue}if(!i.startsWith("var(")){if(nt(i)||Rt(i)){r+=1;continue}return!1}}return r>0}function dn(e){let r=0;for(let i of U(e,",")){if(i==="cover"||i==="contain"){r+=1;continue}let t=U(i," ");if(t.length!==1&&t.length!==2)return!1;if(t.every(n=>n==="auto"||nt(n)||Rt(n))){r+=1;continue}}return r>0}var mn=["deg","rad","grad","turn"],gn=new RegExp(`^${fe.source}(${mn.join("|")})$`);function hn(e){return gn.test(e)}var vn=new RegExp(`^${fe.source} +${fe.source} +${fe.source}$`);function wn(e){return vn.test(e)}function T(e){let r=Number(e);return Number.isInteger(r)&&r>=0&&String(r)===String(e)}function Pt(e){let r=Number(e);return Number.isInteger(r)&&r>0&&String(r)===String(e)}function xe(e){return Ar(e,.25)}function ot(e){return Ar(e,.25)}function Ar(e,r){let i=Number(e);return i>=0&&i%r===0&&String(i)===String(e)}var kn=new Set(["inset","inherit","initial","revert","unset"]),Cr=/^-?(\d+|\.\d+)(.*?)$/g;function Ue(e,r){return U(e,",").map(t=>{t=t.trim();let n=U(t," ").filter(c=>c.trim()!==""),s=null,a=null,p=null;for(let c of n)kn.has(c)||(Cr.test(c)?(a===null?a=c:p===null&&(p=c),Cr.lastIndex=0):s===null&&(s=c));if(a===null||p===null)return t;let u=r(s??"currentcolor");return s!==null?t.replace(s,u):`${t} ${u}`}).join(", ")}var bn=/^-?[a-z][a-zA-Z0-9/%._-]*$/,xn=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,at=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],Ot=class{utilities=new W(()=>[]);completions=new Map;static(r,i){this.utilities.get(r).push({kind:"static",compileFn:i})}functional(r,i,t){this.utilities.get(r).push({kind:"functional",compileFn:i,options:t})}has(r,i){return this.utilities.has(r)&&this.utilities.get(r).some(t=>t.kind===i)}get(r){return this.utilities.has(r)?this.utilities.get(r):[]}getCompletions(r){return this.completions.get(r)?.()??[]}suggest(r,i){let t=this.completions.get(r);t?this.completions.set(r,()=>[...t?.(),...i?.()]):this.completions.set(r,i)}keys(r){let i=[];for(let[t,n]of this.utilities.entries())for(let s of n)if(s.kind===r){i.push(t);break}return i}};function $(e,r,i){return F("@property",e,[o("syntax",i?`"${i}"`:'"*"'),o("inherits","false"),...r?[o("initial-value",r)]:[]])}function Q(e,r){if(r===null)return e;let i=Number(r);return Number.isNaN(i)||(r=`${i*100}%`),r==="100%"?e:`color-mix(in oklab, ${e} ${r}, transparent)`}function Nr(e,r){let i=Number(r);return Number.isNaN(i)||(r=`${i*100}%`),`oklab(from ${e} l a b / ${r})`}function X(e,r,i){if(!r)return e;if(r.kind==="arbitrary")return Q(e,r.value);let t=i.resolve(r.value,["--opacity"]);return t?Q(e,t):ot(r.value)?Q(e,`${r.value}%`):null}function te(e,r,i){let t=null;switch(e.value.value){case"inherit":{t="inherit";break}case"transparent":{t="transparent";break}case"current":{t="currentcolor";break}default:{t=r.resolve(e.value.value,i);break}}return t?X(t,e.modifier,r):null}var Vr=/(\d+)_(\d+)/g;function Sr(e){let r=new Ot;function i(l,f){function*h(y){for(let N of e.keysInNamespaces(y))yield N.replace(Vr,(P,V,E)=>`${V}.${E}`)}let A=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];r.suggest(l,()=>{let y=[];for(let N of f()){if(typeof N=="string"){y.push({values:[N],modifiers:[]});continue}let P=[...N.values??[],...h(N.valueThemeKeys??[])],V=[...N.modifiers??[],...h(N.modifierThemeKeys??[])];N.supportsFractions&&P.push(...A),N.hasDefaultValue&&P.unshift(null),y.push({supportsNegative:N.supportsNegative,values:P,modifiers:V})}return y})}function t(l,f){r.static(l,()=>f.map(h=>typeof h=="function"?h():o(h[0],h[1])))}function n(l,f){function h({negative:A}){return y=>{let N=null,P=null;if(y.value)if(y.value.kind==="arbitrary"){if(y.modifier)return;N=y.value.value,P=y.value.dataType}else{if(N=e.resolve(y.value.fraction??y.value.value,f.themeKeys??[]),N===null&&f.supportsFractions&&y.value.fraction){let[V,E]=U(y.value.fraction,"/");if(!T(V)||!T(E))return;N=`calc(${y.value.fraction} * 100%)`}if(N===null&&A&&f.handleNegativeBareValue){if(N=f.handleNegativeBareValue(y.value),!N?.includes("/")&&y.modifier)return;if(N!==null)return f.handle(N,null)}if(N===null&&f.handleBareValue&&(N=f.handleBareValue(y.value),!N?.includes("/")&&y.modifier))return;if(N===null&&!A&&f.staticValues&&!y.modifier){let V=f.staticValues[y.value.value];if(V)return V}}else{if(y.modifier)return;N=f.defaultValue!==void 0?f.defaultValue:e.resolve(null,f.themeKeys??[])}if(N!==null)return f.handle(A?`calc(${N} * -1)`:N,P)}}if(f.supportsNegative&&r.functional(`-${l}`,h({negative:!0})),r.functional(l,h({negative:!1})),i(l,()=>[{supportsNegative:f.supportsNegative,valueThemeKeys:f.themeKeys??[],hasDefaultValue:f.defaultValue!==void 0&&f.defaultValue!==null,supportsFractions:f.supportsFractions}]),f.staticValues&&Object.keys(f.staticValues).length>0){let A=Object.keys(f.staticValues);i(l,()=>[{values:A}])}}function s(l,f){r.functional(l,h=>{if(!h.value)return;let A=null;if(h.value.kind==="arbitrary"?(A=h.value.value,A=X(A,h.modifier,e)):A=te(h,e,f.themeKeys),A!==null)return f.handle(A)}),i(l,()=>[{values:["current","inherit","transparent"],valueThemeKeys:f.themeKeys,modifiers:Array.from({length:21},(h,A)=>`${A*5}`)}])}function a(l,f,h,{supportsNegative:A=!1,supportsFractions:y=!1,staticValues:N}={}){A&&r.static(`-${l}-px`,()=>h("-1px")),r.static(`${l}-px`,()=>h("1px")),n(l,{themeKeys:f,supportsFractions:y,supportsNegative:A,defaultValue:null,handleBareValue:({value:P})=>{let V=e.resolve(null,["--spacing"]);return!V||!xe(P)?null:`calc(${V} * ${P})`},handleNegativeBareValue:({value:P})=>{let V=e.resolve(null,["--spacing"]);return!V||!xe(P)?null:`calc(${V} * -${P})`},handle:h,staticValues:N}),i(l,()=>[{values:e.get(["--spacing"])?at:[],supportsNegative:A,supportsFractions:y,valueThemeKeys:f}])}t("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip-path","inset(50%)"],["white-space","nowrap"],["border-width","0"]]),t("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip-path","none"],["white-space","normal"]]),t("pointer-events-none",[["pointer-events","none"]]),t("pointer-events-auto",[["pointer-events","auto"]]),t("visible",[["visibility","visible"]]),t("invisible",[["visibility","hidden"]]),t("collapse",[["visibility","collapse"]]),t("static",[["position","static"]]),t("fixed",[["position","fixed"]]),t("absolute",[["position","absolute"]]),t("relative",[["position","relative"]]),t("sticky",[["position","sticky"]]);for(let[l,f]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])t(`${l}-auto`,[[f,"auto"]]),t(`${l}-full`,[[f,"100%"]]),t(`-${l}-full`,[[f,"-100%"]]),a(l,["--inset","--spacing"],h=>[o(f,h)],{supportsNegative:!0,supportsFractions:!0});t("isolate",[["isolation","isolate"]]),t("isolation-auto",[["isolation","auto"]]),n("z",{supportsNegative:!0,handleBareValue:({value:l})=>T(l)?l:null,themeKeys:["--z-index"],handle:l=>[o("z-index",l)],staticValues:{auto:[o("z-index","auto")]}}),i("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),n("order",{supportsNegative:!0,handleBareValue:({value:l})=>T(l)?l:null,themeKeys:["--order"],handle:l=>[o("order",l)],staticValues:{first:[o("order","-9999")],last:[o("order","9999")]}}),i("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(l,f)=>`${f+1}`),valueThemeKeys:["--order"]}]),n("col",{supportsNegative:!0,handleBareValue:({value:l})=>T(l)?l:null,themeKeys:["--grid-column"],handle:l=>[o("grid-column",l)],staticValues:{auto:[o("grid-column","auto")]}}),n("col-span",{handleBareValue:({value:l})=>T(l)?l:null,handle:l=>[o("grid-column",`span ${l} / span ${l}`)],staticValues:{full:[o("grid-column","1 / -1")]}}),n("col-start",{supportsNegative:!0,handleBareValue:({value:l})=>T(l)?l:null,themeKeys:["--grid-column-start"],handle:l=>[o("grid-column-start",l)],staticValues:{auto:[o("grid-column-start","auto")]}}),n("col-end",{supportsNegative:!0,handleBareValue:({value:l})=>T(l)?l:null,themeKeys:["--grid-column-end"],handle:l=>[o("grid-column-end",l)],staticValues:{auto:[o("grid-column-end","auto")]}}),i("col-span",()=>[{values:Array.from({length:12},(l,f)=>`${f+1}`),valueThemeKeys:[]}]),i("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(l,f)=>`${f+1}`),valueThemeKeys:["--grid-column-start"]}]),i("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(l,f)=>`${f+1}`),valueThemeKeys:["--grid-column-end"]}]),n("row",{supportsNegative:!0,handleBareValue:({value:l})=>T(l)?l:null,themeKeys:["--grid-row"],handle:l=>[o("grid-row",l)],staticValues:{auto:[o("grid-row","auto")]}}),n("row-span",{themeKeys:[],handleBareValue:({value:l})=>T(l)?l:null,handle:l=>[o("grid-row",`span ${l} / span ${l}`)],staticValues:{full:[o("grid-row","1 / -1")]}}),n("row-start",{supportsNegative:!0,handleBareValue:({value:l})=>T(l)?l:null,themeKeys:["--grid-row-start"],handle:l=>[o("grid-row-start",l)],staticValues:{auto:[o("grid-row-start","auto")]}}),n("row-end",{supportsNegative:!0,handleBareValue:({value:l})=>T(l)?l:null,themeKeys:["--grid-row-end"],handle:l=>[o("grid-row-end",l)],staticValues:{auto:[o("grid-row-end","auto")]}}),i("row-span",()=>[{values:Array.from({length:12},(l,f)=>`${f+1}`),valueThemeKeys:[]}]),i("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(l,f)=>`${f+1}`),valueThemeKeys:["--grid-row-start"]}]),i("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(l,f)=>`${f+1}`),valueThemeKeys:["--grid-row-end"]}]),t("float-start",[["float","inline-start"]]),t("float-end",[["float","inline-end"]]),t("float-right",[["float","right"]]),t("float-left",[["float","left"]]),t("float-none",[["float","none"]]),t("clear-start",[["clear","inline-start"]]),t("clear-end",[["clear","inline-end"]]),t("clear-right",[["clear","right"]]),t("clear-left",[["clear","left"]]),t("clear-both",[["clear","both"]]),t("clear-none",[["clear","none"]]);for(let[l,f]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])t(`${l}-auto`,[[f,"auto"]]),a(l,["--margin","--spacing"],h=>[o(f,h)],{supportsNegative:!0});t("box-border",[["box-sizing","border-box"]]),t("box-content",[["box-sizing","content-box"]]),n("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:l})=>T(l)?l:null,handle:l=>[o("overflow","hidden"),o("display","-webkit-box"),o("-webkit-box-orient","vertical"),o("-webkit-line-clamp",l)],staticValues:{none:[o("overflow","visible"),o("display","block"),o("-webkit-box-orient","horizontal"),o("-webkit-line-clamp","unset")]}}),i("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),t("block",[["display","block"]]),t("inline-block",[["display","inline-block"]]),t("inline",[["display","inline"]]),t("hidden",[["display","none"]]),t("inline-flex",[["display","inline-flex"]]),t("table",[["display","table"]]),t("inline-table",[["display","inline-table"]]),t("table-caption",[["display","table-caption"]]),t("table-cell",[["display","table-cell"]]),t("table-column",[["display","table-column"]]),t("table-column-group",[["display","table-column-group"]]),t("table-footer-group",[["display","table-footer-group"]]),t("table-header-group",[["display","table-header-group"]]),t("table-row-group",[["display","table-row-group"]]),t("table-row",[["display","table-row"]]),t("flow-root",[["display","flow-root"]]),t("flex",[["display","flex"]]),t("grid",[["display","grid"]]),t("inline-grid",[["display","inline-grid"]]),t("contents",[["display","contents"]]),t("list-item",[["display","list-item"]]),t("field-sizing-content",[["field-sizing","content"]]),t("field-sizing-fixed",[["field-sizing","fixed"]]),n("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:l})=>{if(l===null)return null;let[f,h]=U(l,"/");return!T(f)||!T(h)?null:l},handle:l=>[o("aspect-ratio",l)],staticValues:{auto:[o("aspect-ratio","auto")],square:[o("aspect-ratio","1 / 1")]}});for(let[l,f]of[["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])t(`size-${l}`,[["--tw-sort","size"],["width",f],["height",f]]),t(`w-${l}`,[["width",f]]),t(`h-${l}`,[["height",f]]),t(`min-w-${l}`,[["min-width",f]]),t(`min-h-${l}`,[["min-height",f]]),t(`max-w-${l}`,[["max-width",f]]),t(`max-h-${l}`,[["max-height",f]]);t("size-auto",[["--tw-sort","size"],["width","auto"],["height","auto"]]),t("w-auto",[["width","auto"]]),t("h-auto",[["height","auto"]]),t("min-w-auto",[["min-width","auto"]]),t("min-h-auto",[["min-height","auto"]]),t("h-lh",[["height","1lh"]]),t("min-h-lh",[["min-height","1lh"]]),t("max-h-lh",[["max-height","1lh"]]),t("w-screen",[["width","100vw"]]),t("min-w-screen",[["min-width","100vw"]]),t("max-w-screen",[["max-width","100vw"]]),t("h-screen",[["height","100vh"]]),t("min-h-screen",[["min-height","100vh"]]),t("max-h-screen",[["max-height","100vh"]]),t("max-w-none",[["max-width","none"]]),t("max-h-none",[["max-height","none"]]),a("size",["--size","--spacing"],l=>[o("--tw-sort","size"),o("width",l),o("height",l)],{supportsFractions:!0});for(let[l,f,h]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])a(l,f,A=>[o(h,A)],{supportsFractions:!0});r.static("container",()=>{let l=[...e.namespace("--breakpoint").values()];l.sort((h,A)=>be(h,A,"asc"));let f=[o("--tw-sort","--tw-container-component"),o("width","100%")];for(let h of l)f.push(F("@media",`(width >= ${h})`,[o("max-width",h)]));return f}),t("flex-auto",[["flex","auto"]]),t("flex-initial",[["flex","0 auto"]]),t("flex-none",[["flex","none"]]),r.functional("flex",l=>{if(l.value){if(l.value.kind==="arbitrary")return l.modifier?void 0:[o("flex",l.value.value)];if(l.value.fraction){let[f,h]=U(l.value.fraction,"/");return!T(f)||!T(h)?void 0:[o("flex",`calc(${l.value.fraction} * 100%)`)]}if(T(l.value.value))return l.modifier?void 0:[o("flex",l.value.value)]}}),i("flex",()=>[{supportsFractions:!0},{values:Array.from({length:12},(l,f)=>`${f+1}`)}]),n("shrink",{defaultValue:"1",handleBareValue:({value:l})=>T(l)?l:null,handle:l=>[o("flex-shrink",l)]}),n("grow",{defaultValue:"1",handleBareValue:({value:l})=>T(l)?l:null,handle:l=>[o("flex-grow",l)]}),i("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),i("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),t("basis-auto",[["flex-basis","auto"]]),t("basis-full",[["flex-basis","100%"]]),a("basis",["--flex-basis","--spacing","--container"],l=>[o("flex-basis",l)],{supportsFractions:!0}),t("table-auto",[["table-layout","auto"]]),t("table-fixed",[["table-layout","fixed"]]),t("caption-top",[["caption-side","top"]]),t("caption-bottom",[["caption-side","bottom"]]),t("border-collapse",[["border-collapse","collapse"]]),t("border-separate",[["border-collapse","separate"]]);let p=()=>z([$("--tw-border-spacing-x","0","<length>"),$("--tw-border-spacing-y","0","<length>")]);a("border-spacing",["--border-spacing","--spacing"],l=>[p(),o("--tw-border-spacing-x",l),o("--tw-border-spacing-y",l),o("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-x",["--border-spacing","--spacing"],l=>[p(),o("--tw-border-spacing-x",l),o("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-y",["--border-spacing","--spacing"],l=>[p(),o("--tw-border-spacing-y",l),o("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),n("origin",{themeKeys:["--transform-origin"],handle:l=>[o("transform-origin",l)],staticValues:{center:[o("transform-origin","center")],top:[o("transform-origin","top")],"top-right":[o("transform-origin","100% 0")],right:[o("transform-origin","100%")],"bottom-right":[o("transform-origin","100% 100%")],bottom:[o("transform-origin","bottom")],"bottom-left":[o("transform-origin","0 100%")],left:[o("transform-origin","0")],"top-left":[o("transform-origin","0 0")]}}),n("perspective-origin",{themeKeys:["--perspective-origin"],handle:l=>[o("perspective-origin",l)],staticValues:{center:[o("perspective-origin","center")],top:[o("perspective-origin","top")],"top-right":[o("perspective-origin","100% 0")],right:[o("perspective-origin","100%")],"bottom-right":[o("perspective-origin","100% 100%")],bottom:[o("perspective-origin","bottom")],"bottom-left":[o("perspective-origin","0 100%")],left:[o("perspective-origin","0")],"top-left":[o("perspective-origin","0 0")]}}),n("perspective",{themeKeys:["--perspective"],handle:l=>[o("perspective",l)],staticValues:{none:[o("perspective","none")]}});let u=()=>z([$("--tw-translate-x","0"),$("--tw-translate-y","0"),$("--tw-translate-z","0")]);t("translate-none",[["translate","none"]]),t("-translate-full",[u,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),t("translate-full",[u,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a("translate",["--translate","--spacing"],l=>[u(),o("--tw-translate-x",l),o("--tw-translate-y",l),o("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let l of["x","y"])t(`-translate-${l}-full`,[u,[`--tw-translate-${l}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),t(`translate-${l}-full`,[u,[`--tw-translate-${l}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a(`translate-${l}`,["--translate","--spacing"],f=>[u(),o(`--tw-translate-${l}`,f),o("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});a("translate-z",["--translate","--spacing"],l=>[u(),o("--tw-translate-z",l),o("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),t("translate-3d",[u,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let c=()=>z([$("--tw-scale-x","1"),$("--tw-scale-y","1"),$("--tw-scale-z","1")]);t("scale-none",[["scale","none"]]);function m({negative:l}){return f=>{if(!f.value||f.modifier)return;let h;return f.value.kind==="arbitrary"?(h=f.value.value,h=l?`calc(${h} * -1)`:h,[o("scale",h)]):(h=e.resolve(f.value.value,["--scale"]),!h&&T(f.value.value)&&(h=`${f.value.value}%`),h?(h=l?`calc(${h} * -1)`:h,[c(),o("--tw-scale-x",h),o("--tw-scale-y",h),o("--tw-scale-z",h),o("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}r.functional("-scale",m({negative:!0})),r.functional("scale",m({negative:!1})),i("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let l of["x","y","z"])n(`scale-${l}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:f})=>T(f)?`${f}%`:null,handle:f=>[c(),o(`--tw-scale-${l}`,f),o("scale",`var(--tw-scale-x) var(--tw-scale-y)${l==="z"?" var(--tw-scale-z)":""}`)]}),i(`scale-${l}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);t("scale-3d",[c,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),t("rotate-none",[["rotate","none"]]);function g({negative:l}){return f=>{if(!f.value||f.modifier)return;let h;if(f.value.kind==="arbitrary"){h=f.value.value;let A=f.value.dataType??Z(h,["angle","vector"]);if(A==="vector")return[o("rotate",`${h} var(--tw-rotate)`)];if(A!=="angle")return[o("rotate",l?`calc(${h} * -1)`:h)]}else if(h=e.resolve(f.value.value,["--rotate"]),!h&&T(f.value.value)&&(h=`${f.value.value}deg`),!h)return;return[o("rotate",l?`calc(${h} * -1)`:h)]}}r.functional("-rotate",g({negative:!0})),r.functional("rotate",g({negative:!1})),i("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let l=["var(--tw-rotate-x,)","var(--tw-rotate-y,)","var(--tw-rotate-z,)","var(--tw-skew-x,)","var(--tw-skew-y,)"].join(" "),f=()=>z([$("--tw-rotate-x"),$("--tw-rotate-y"),$("--tw-rotate-z"),$("--tw-skew-x"),$("--tw-skew-y")]);for(let h of["x","y","z"])n(`rotate-${h}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:A})=>T(A)?`${A}deg`:null,handle:A=>[f(),o(`--tw-rotate-${h}`,`rotate${h.toUpperCase()}(${A})`),o("transform",l)]}),i(`rotate-${h}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);n("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>T(h)?`${h}deg`:null,handle:h=>[f(),o("--tw-skew-x",`skewX(${h})`),o("--tw-skew-y",`skewY(${h})`),o("transform",l)]}),n("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>T(h)?`${h}deg`:null,handle:h=>[f(),o("--tw-skew-x",`skewX(${h})`),o("transform",l)]}),n("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>T(h)?`${h}deg`:null,handle:h=>[f(),o("--tw-skew-y",`skewY(${h})`),o("transform",l)]}),i("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),r.functional("transform",h=>{if(h.modifier)return;let A=null;if(h.value?h.value.kind==="arbitrary"&&(A=h.value.value):A=l,A!==null)return[f(),o("transform",A)]}),i("transform",()=>[{hasDefaultValue:!0}]),t("transform-cpu",[["transform",l]]),t("transform-gpu",[["transform",`translateZ(0) ${l}`]]),t("transform-none",[["transform","none"]])}t("transform-flat",[["transform-style","flat"]]),t("transform-3d",[["transform-style","preserve-3d"]]),t("transform-content",[["transform-box","content-box"]]),t("transform-border",[["transform-box","border-box"]]),t("transform-fill",[["transform-box","fill-box"]]),t("transform-stroke",[["transform-box","stroke-box"]]),t("transform-view",[["transform-box","view-box"]]),t("backface-visible",[["backface-visibility","visible"]]),t("backface-hidden",[["backface-visibility","hidden"]]);for(let l of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])t(`cursor-${l}`,[["cursor",l]]);n("cursor",{themeKeys:["--cursor"],handle:l=>[o("cursor",l)]});for(let l of["auto","none","manipulation"])t(`touch-${l}`,[["touch-action",l]]);let d=()=>z([$("--tw-pan-x"),$("--tw-pan-y"),$("--tw-pinch-zoom")]);for(let l of["x","left","right"])t(`touch-pan-${l}`,[d,["--tw-pan-x",`pan-${l}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let l of["y","up","down"])t(`touch-pan-${l}`,[d,["--tw-pan-y",`pan-${l}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);t("touch-pinch-zoom",[d,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let l of["none","text","all","auto"])t(`select-${l}`,[["-webkit-user-select",l],["user-select",l]]);t("resize-none",[["resize","none"]]),t("resize-x",[["resize","horizontal"]]),t("resize-y",[["resize","vertical"]]),t("resize",[["resize","both"]]),t("snap-none",[["scroll-snap-type","none"]]);let w=()=>z([$("--tw-scroll-snap-strictness","proximity","*")]);for(let l of["x","y","both"])t(`snap-${l}`,[w,["scroll-snap-type",`${l} var(--tw-scroll-snap-strictness)`]]);t("snap-mandatory",[w,["--tw-scroll-snap-strictness","mandatory"]]),t("snap-proximity",[w,["--tw-scroll-snap-strictness","proximity"]]),t("snap-align-none",[["scroll-snap-align","none"]]),t("snap-start",[["scroll-snap-align","start"]]),t("snap-end",[["scroll-snap-align","end"]]),t("snap-center",[["scroll-snap-align","center"]]),t("snap-normal",[["scroll-snap-stop","normal"]]),t("snap-always",[["scroll-snap-stop","always"]]);for(let[l,f]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])a(l,["--scroll-margin","--spacing"],h=>[o(f,h)],{supportsNegative:!0});for(let[l,f]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])a(l,["--scroll-padding","--spacing"],h=>[o(f,h)]);t("list-inside",[["list-style-position","inside"]]),t("list-outside",[["list-style-position","outside"]]),n("list",{themeKeys:["--list-style-type"],handle:l=>[o("list-style-type",l)],staticValues:{none:[o("list-style-type","none")],disc:[o("list-style-type","disc")],decimal:[o("list-style-type","decimal")]}}),n("list-image",{themeKeys:["--list-style-image"],handle:l=>[o("list-style-image",l)],staticValues:{none:[o("list-style-image","none")]}}),t("appearance-none",[["appearance","none"]]),t("appearance-auto",[["appearance","auto"]]),t("scheme-normal",[["color-scheme","normal"]]),t("scheme-dark",[["color-scheme","dark"]]),t("scheme-light",[["color-scheme","light"]]),t("scheme-light-dark",[["color-scheme","light dark"]]),t("scheme-only-dark",[["color-scheme","only dark"]]),t("scheme-only-light",[["color-scheme","only light"]]),n("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:l})=>T(l)?l:null,handle:l=>[o("columns",l)],staticValues:{auto:[o("columns","auto")]}}),i("columns",()=>[{values:Array.from({length:12},(l,f)=>`${f+1}`),valueThemeKeys:["--columns","--container"]}]);for(let l of["auto","avoid","all","avoid-page","page","left","right","column"])t(`break-before-${l}`,[["break-before",l]]);for(let l of["auto","avoid","avoid-page","avoid-column"])t(`break-inside-${l}`,[["break-inside",l]]);for(let l of["auto","avoid","all","avoid-page","page","left","right","column"])t(`break-after-${l}`,[["break-after",l]]);t("grid-flow-row",[["grid-auto-flow","row"]]),t("grid-flow-col",[["grid-auto-flow","column"]]),t("grid-flow-dense",[["grid-auto-flow","dense"]]),t("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),t("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),n("auto-cols",{themeKeys:["--grid-auto-columns"],handle:l=>[o("grid-auto-columns",l)],staticValues:{auto:[o("grid-auto-columns","auto")],min:[o("grid-auto-columns","min-content")],max:[o("grid-auto-columns","max-content")],fr:[o("grid-auto-columns","minmax(0, 1fr)")]}}),n("auto-rows",{themeKeys:["--grid-auto-rows"],handle:l=>[o("grid-auto-rows",l)],staticValues:{auto:[o("grid-auto-rows","auto")],min:[o("grid-auto-rows","min-content")],max:[o("grid-auto-rows","max-content")],fr:[o("grid-auto-rows","minmax(0, 1fr)")]}}),n("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:l})=>Pt(l)?`repeat(${l}, minmax(0, 1fr))`:null,handle:l=>[o("grid-template-columns",l)],staticValues:{none:[o("grid-template-columns","none")],subgrid:[o("grid-template-columns","subgrid")]}}),n("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:l})=>Pt(l)?`repeat(${l}, minmax(0, 1fr))`:null,handle:l=>[o("grid-template-rows",l)],staticValues:{none:[o("grid-template-rows","none")],subgrid:[o("grid-template-rows","subgrid")]}}),i("grid-cols",()=>[{values:Array.from({length:12},(l,f)=>`${f+1}`),valueThemeKeys:["--grid-template-columns"]}]),i("grid-rows",()=>[{values:Array.from({length:12},(l,f)=>`${f+1}`),valueThemeKeys:["--grid-template-rows"]}]),t("flex-row",[["flex-direction","row"]]),t("flex-row-reverse",[["flex-direction","row-reverse"]]),t("flex-col",[["flex-direction","column"]]),t("flex-col-reverse",[["flex-direction","column-reverse"]]),t("flex-wrap",[["flex-wrap","wrap"]]),t("flex-nowrap",[["flex-wrap","nowrap"]]),t("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),t("place-content-center",[["place-content","center"]]),t("place-content-start",[["place-content","start"]]),t("place-content-end",[["place-content","end"]]),t("place-content-center-safe",[["place-content","safe center"]]),t("place-content-end-safe",[["place-content","safe end"]]),t("place-content-between",[["place-content","space-between"]]),t("place-content-around",[["place-content","space-around"]]),t("place-content-evenly",[["place-content","space-evenly"]]),t("place-content-baseline",[["place-content","baseline"]]),t("place-content-stretch",[["place-content","stretch"]]),t("place-items-center",[["place-items","center"]]),t("place-items-start",[["place-items","start"]]),t("place-items-end",[["place-items","end"]]),t("place-items-center-safe",[["place-items","safe center"]]),t("place-items-end-safe",[["place-items","safe end"]]),t("place-items-baseline",[["place-items","baseline"]]),t("place-items-stretch",[["place-items","stretch"]]),t("content-normal",[["align-content","normal"]]),t("content-center",[["align-content","center"]]),t("content-start",[["align-content","flex-start"]]),t("content-end",[["align-content","flex-end"]]),t("content-center-safe",[["align-content","safe center"]]),t("content-end-safe",[["align-content","safe flex-end"]]),t("content-between",[["align-content","space-between"]]),t("content-around",[["align-content","space-around"]]),t("content-evenly",[["align-content","space-evenly"]]),t("content-baseline",[["align-content","baseline"]]),t("content-stretch",[["align-content","stretch"]]),t("items-center",[["align-items","center"]]),t("items-start",[["align-items","flex-start"]]),t("items-end",[["align-items","flex-end"]]),t("items-center-safe",[["align-items","safe center"]]),t("items-end-safe",[["align-items","safe flex-end"]]),t("items-baseline",[["align-items","baseline"]]),t("items-baseline-last",[["align-items","last baseline"]]),t("items-stretch",[["align-items","stretch"]]),t("justify-normal",[["justify-content","normal"]]),t("justify-center",[["justify-content","center"]]),t("justify-start",[["justify-content","flex-start"]]),t("justify-end",[["justify-content","flex-end"]]),t("justify-center-safe",[["justify-content","safe center"]]),t("justify-end-safe",[["justify-content","safe flex-end"]]),t("justify-between",[["justify-content","space-between"]]),t("justify-around",[["justify-content","space-around"]]),t("justify-evenly",[["justify-content","space-evenly"]]),t("justify-baseline",[["justify-content","baseline"]]),t("justify-stretch",[["justify-content","stretch"]]),t("justify-items-normal",[["justify-items","normal"]]),t("justify-items-center",[["justify-items","center"]]),t("justify-items-start",[["justify-items","start"]]),t("justify-items-end",[["justify-items","end"]]),t("justify-items-center-safe",[["justify-items","safe center"]]),t("justify-items-end-safe",[["justify-items","safe end"]]),t("justify-items-stretch",[["justify-items","stretch"]]),a("gap",["--gap","--spacing"],l=>[o("gap",l)]),a("gap-x",["--gap","--spacing"],l=>[o("column-gap",l)]),a("gap-y",["--gap","--spacing"],l=>[o("row-gap",l)]),a("space-x",["--space","--spacing"],l=>[z([$("--tw-space-x-reverse","0")]),B(":where(& > :not(:last-child))",[o("--tw-sort","row-gap"),o("--tw-space-x-reverse","0"),o("margin-inline-start",`calc(${l} * var(--tw-space-x-reverse))`),o("margin-inline-end",`calc(${l} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),a("space-y",["--space","--spacing"],l=>[z([$("--tw-space-y-reverse","0")]),B(":where(& > :not(:last-child))",[o("--tw-sort","column-gap"),o("--tw-space-y-reverse","0"),o("margin-block-start",`calc(${l} * var(--tw-space-y-reverse))`),o("margin-block-end",`calc(${l} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),t("space-x-reverse",[()=>z([$("--tw-space-x-reverse","0")]),()=>B(":where(& > :not(:last-child))",[o("--tw-sort","row-gap"),o("--tw-space-x-reverse","1")])]),t("space-y-reverse",[()=>z([$("--tw-space-y-reverse","0")]),()=>B(":where(& > :not(:last-child))",[o("--tw-sort","column-gap"),o("--tw-space-y-reverse","1")])]),t("accent-auto",[["accent-color","auto"]]),s("accent",{themeKeys:["--accent-color","--color"],handle:l=>[o("accent-color",l)]}),s("caret",{themeKeys:["--caret-color","--color"],handle:l=>[o("caret-color",l)]}),s("divide",{themeKeys:["--divide-color","--border-color","--color"],handle:l=>[B(":where(& > :not(:last-child))",[o("--tw-sort","divide-color"),o("border-color",l)])]}),t("place-self-auto",[["place-self","auto"]]),t("place-self-start",[["place-self","start"]]),t("place-self-end",[["place-self","end"]]),t("place-self-center",[["place-self","center"]]),t("place-self-end-safe",[["place-self","safe end"]]),t("place-self-center-safe",[["place-self","safe center"]]),t("place-self-stretch",[["place-self","stretch"]]),t("self-auto",[["align-self","auto"]]),t("self-start",[["align-self","flex-start"]]),t("self-end",[["align-self","flex-end"]]),t("self-center",[["align-self","center"]]),t("self-end-safe",[["align-self","safe flex-end"]]),t("self-center-safe",[["align-self","safe center"]]),t("self-stretch",[["align-self","stretch"]]),t("self-baseline",[["align-self","baseline"]]),t("self-baseline-last",[["align-self","last baseline"]]),t("justify-self-auto",[["justify-self","auto"]]),t("justify-self-start",[["justify-self","flex-start"]]),t("justify-self-end",[["justify-self","flex-end"]]),t("justify-self-center",[["justify-self","center"]]),t("justify-self-end-safe",[["justify-self","safe flex-end"]]),t("justify-self-center-safe",[["justify-self","safe center"]]),t("justify-self-stretch",[["justify-self","stretch"]]);for(let l of["auto","hidden","clip","visible","scroll"])t(`overflow-${l}`,[["overflow",l]]),t(`overflow-x-${l}`,[["overflow-x",l]]),t(`overflow-y-${l}`,[["overflow-y",l]]);for(let l of["auto","contain","none"])t(`overscroll-${l}`,[["overscroll-behavior",l]]),t(`overscroll-x-${l}`,[["overscroll-behavior-x",l]]),t(`overscroll-y-${l}`,[["overscroll-behavior-y",l]]);t("scroll-auto",[["scroll-behavior","auto"]]),t("scroll-smooth",[["scroll-behavior","smooth"]]),t("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),t("text-ellipsis",[["text-overflow","ellipsis"]]),t("text-clip",[["text-overflow","clip"]]),t("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),t("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),t("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),t("whitespace-normal",[["white-space","normal"]]),t("whitespace-nowrap",[["white-space","nowrap"]]),t("whitespace-pre",[["white-space","pre"]]),t("whitespace-pre-line",[["white-space","pre-line"]]),t("whitespace-pre-wrap",[["white-space","pre-wrap"]]),t("whitespace-break-spaces",[["white-space","break-spaces"]]),t("text-wrap",[["text-wrap","wrap"]]),t("text-nowrap",[["text-wrap","nowrap"]]),t("text-balance",[["text-wrap","balance"]]),t("text-pretty",[["text-wrap","pretty"]]),t("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),t("break-words",[["overflow-wrap","break-word"]]),t("break-all",[["word-break","break-all"]]),t("break-keep",[["word-break","keep-all"]]),t("wrap-anywhere",[["overflow-wrap","anywhere"]]),t("wrap-break-word",[["overflow-wrap","break-word"]]),t("wrap-normal",[["overflow-wrap","normal"]]);for(let[l,f]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])n(l,{themeKeys:["--radius"],handle:h=>f.map(A=>o(A,h)),staticValues:{none:f.map(h=>o(h,"0")),full:f.map(h=>o(h,"calc(infinity * 1px)"))}});t("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),t("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),t("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),t("border-double",[["--tw-border-style","double"],["border-style","double"]]),t("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),t("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let f=function(h,A){r.functional(h,y=>{if(!y.value){if(y.modifier)return;let N=e.get(["--default-border-width"])??"1px",P=A.width(N);return P?[l(),...P]:void 0}if(y.value.kind==="arbitrary"){let N=y.value.value;switch(y.value.dataType??Z(N,["color","line-width","length"])){case"line-width":case"length":{if(y.modifier)return;let V=A.width(N);return V?[l(),...V]:void 0}default:return N=X(N,y.modifier,e),N===null?void 0:A.color(N)}}{let N=te(y,e,["--border-color","--color"]);if(N)return A.color(N)}{if(y.modifier)return;let N=e.resolve(y.value.value,["--border-width"]);if(N){let P=A.width(N);return P?[l(),...P]:void 0}if(T(y.value.value)){let P=A.width(`${y.value.value}px`);return P?[l(),...P]:void 0}}}),i(h,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(y,N)=>`${N*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var H=f;let l=()=>z([$("--tw-border-style","solid")]);f("border",{width:h=>[o("border-style","var(--tw-border-style)"),o("border-width",h)],color:h=>[o("border-color",h)]}),f("border-x",{width:h=>[o("border-inline-style","var(--tw-border-style)"),o("border-inline-width",h)],color:h=>[o("border-inline-color",h)]}),f("border-y",{width:h=>[o("border-block-style","var(--tw-border-style)"),o("border-block-width",h)],color:h=>[o("border-block-color",h)]}),f("border-s",{width:h=>[o("border-inline-start-style","var(--tw-border-style)"),o("border-inline-start-width",h)],color:h=>[o("border-inline-start-color",h)]}),f("border-e",{width:h=>[o("border-inline-end-style","var(--tw-border-style)"),o("border-inline-end-width",h)],color:h=>[o("border-inline-end-color",h)]}),f("border-t",{width:h=>[o("border-top-style","var(--tw-border-style)"),o("border-top-width",h)],color:h=>[o("border-top-color",h)]}),f("border-r",{width:h=>[o("border-right-style","var(--tw-border-style)"),o("border-right-width",h)],color:h=>[o("border-right-color",h)]}),f("border-b",{width:h=>[o("border-bottom-style","var(--tw-border-style)"),o("border-bottom-width",h)],color:h=>[o("border-bottom-color",h)]}),f("border-l",{width:h=>[o("border-left-style","var(--tw-border-style)"),o("border-left-width",h)],color:h=>[o("border-left-color",h)]}),n("divide-x",{defaultValue:e.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>T(h)?`${h}px`:null,handle:h=>[z([$("--tw-divide-x-reverse","0")]),B(":where(& > :not(:last-child))",[o("--tw-sort","divide-x-width"),l(),o("--tw-divide-x-reverse","0"),o("border-inline-style","var(--tw-border-style)"),o("border-inline-start-width",`calc(${h} * var(--tw-divide-x-reverse))`),o("border-inline-end-width",`calc(${h} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),n("divide-y",{defaultValue:e.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>T(h)?`${h}px`:null,handle:h=>[z([$("--tw-divide-y-reverse","0")]),B(":where(& > :not(:last-child))",[o("--tw-sort","divide-y-width"),l(),o("--tw-divide-y-reverse","0"),o("border-bottom-style","var(--tw-border-style)"),o("border-top-style","var(--tw-border-style)"),o("border-top-width",`calc(${h} * var(--tw-divide-y-reverse))`),o("border-bottom-width",`calc(${h} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),i("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),i("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),t("divide-x-reverse",[()=>z([$("--tw-divide-x-reverse","0")]),()=>B(":where(& > :not(:last-child))",[o("--tw-divide-x-reverse","1")])]),t("divide-y-reverse",[()=>z([$("--tw-divide-y-reverse","0")]),()=>B(":where(& > :not(:last-child))",[o("--tw-divide-y-reverse","1")])]);for(let h of["solid","dashed","dotted","double","none"])t(`divide-${h}`,[()=>B(":where(& > :not(:last-child))",[o("--tw-sort","divide-style"),o("--tw-border-style",h),o("border-style",h)])])}t("bg-auto",[["background-size","auto"]]),t("bg-cover",[["background-size","cover"]]),t("bg-contain",[["background-size","contain"]]),n("bg-size",{handle(l){if(l)return[o("background-size",l)]}}),t("bg-fixed",[["background-attachment","fixed"]]),t("bg-local",[["background-attachment","local"]]),t("bg-scroll",[["background-attachment","scroll"]]),t("bg-top",[["background-position","top"]]),t("bg-top-left",[["background-position","left top"]]),t("bg-top-right",[["background-position","right top"]]),t("bg-bottom",[["background-position","bottom"]]),t("bg-bottom-left",[["background-position","left bottom"]]),t("bg-bottom-right",[["background-position","right bottom"]]),t("bg-left",[["background-position","left"]]),t("bg-right",[["background-position","right"]]),t("bg-center",[["background-position","center"]]),n("bg-position",{handle(l){if(l)return[o("background-position",l)]}}),t("bg-repeat",[["background-repeat","repeat"]]),t("bg-no-repeat",[["background-repeat","no-repeat"]]),t("bg-repeat-x",[["background-repeat","repeat-x"]]),t("bg-repeat-y",[["background-repeat","repeat-y"]]),t("bg-repeat-round",[["background-repeat","round"]]),t("bg-repeat-space",[["background-repeat","space"]]),t("bg-none",[["background-image","none"]]);{let h=function(N){let P="in oklab";if(N?.kind==="named")switch(N.value){case"longer":case"shorter":case"increasing":case"decreasing":P=`in oklch ${N.value} hue`;break;default:P=`in ${N.value}`}else N?.kind==="arbitrary"&&(P=N.value);return P},A=function({negative:N}){return P=>{if(!P.value)return;if(P.value.kind==="arbitrary"){if(P.modifier)return;let j=P.value.value;switch(P.value.dataType??Z(j,["angle"])){case"angle":return j=N?`calc(${j} * -1)`:`${j}`,[o("--tw-gradient-position",j),o("background-image",`linear-gradient(var(--tw-gradient-stops,${j}))`)];default:return N?void 0:[o("--tw-gradient-position",j),o("background-image",`linear-gradient(var(--tw-gradient-stops,${j}))`)]}}let V=P.value.value;if(!N&&f.has(V))V=f.get(V);else if(T(V))V=N?`calc(${V}deg * -1)`:`${V}deg`;else return;let E=h(P.modifier);return[o("--tw-gradient-position",`${V}`),Y("@supports (background-image: linear-gradient(in lab, red, red))",[o("--tw-gradient-position",`${V} ${E}`)]),o("background-image","linear-gradient(var(--tw-gradient-stops))")]}},y=function({negative:N}){return P=>{if(P.value?.kind==="arbitrary"){if(P.modifier)return;let j=P.value.value;return[o("--tw-gradient-position",j),o("background-image",`conic-gradient(var(--tw-gradient-stops,${j}))`)]}let V=h(P.modifier);if(!P.value)return[o("--tw-gradient-position",V),o("background-image","conic-gradient(var(--tw-gradient-stops))")];let E=P.value.value;if(T(E))return E=N?`calc(${E}deg * -1)`:`${E}deg`,[o("--tw-gradient-position",`from ${E} ${V}`),o("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var O=h,I=A,q=y;let l=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],f=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);r.functional("-bg-linear",A({negative:!0})),r.functional("bg-linear",A({negative:!1})),i("bg-linear",()=>[{values:[...f.keys()],modifiers:l},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:l}]),r.functional("-bg-conic",y({negative:!0})),r.functional("bg-conic",y({negative:!1})),i("bg-conic",()=>[{hasDefaultValue:!0,modifiers:l},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:l}]),r.functional("bg-radial",N=>{if(!N.value){let P=h(N.modifier);return[o("--tw-gradient-position",P),o("background-image","radial-gradient(var(--tw-gradient-stops))")]}if(N.value.kind==="arbitrary"){if(N.modifier)return;let P=N.value.value;return[o("--tw-gradient-position",P),o("background-image",`radial-gradient(var(--tw-gradient-stops,${P}))`)]}}),i("bg-radial",()=>[{hasDefaultValue:!0,modifiers:l}])}r.functional("bg",l=>{if(l.value){if(l.value.kind==="arbitrary"){let f=l.value.value;switch(l.value.dataType??Z(f,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return l.modifier?void 0:[o("background-position",f)];case"bg-size":case"length":case"size":return l.modifier?void 0:[o("background-size",f)];case"image":case"url":return l.modifier?void 0:[o("background-image",f)];default:return f=X(f,l.modifier,e),f===null?void 0:[o("background-color",f)]}}{let f=te(l,e,["--background-color","--color"]);if(f)return[o("background-color",f)]}{if(l.modifier)return;let f=e.resolve(l.value.value,["--background-image"]);if(f)return[o("background-image",f)]}}}),i("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(l,f)=>`${f*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let v=()=>z([$("--tw-gradient-position"),$("--tw-gradient-from","#0000","<color>"),$("--tw-gradient-via","#0000","<color>"),$("--tw-gradient-to","#0000","<color>"),$("--tw-gradient-stops"),$("--tw-gradient-via-stops"),$("--tw-gradient-from-position","0%","<length-percentage>"),$("--tw-gradient-via-position","50%","<length-percentage>"),$("--tw-gradient-to-position","100%","<length-percentage>")]);function k(l,f){r.functional(l,h=>{if(h.value){if(h.value.kind==="arbitrary"){let A=h.value.value;switch(h.value.dataType??Z(A,["color","length","percentage"])){case"length":case"percentage":return h.modifier?void 0:f.position(A);default:return A=X(A,h.modifier,e),A===null?void 0:f.color(A)}}{let A=te(h,e,["--background-color","--color"]);if(A)return f.color(A)}{if(h.modifier)return;let A=e.resolve(h.value.value,["--gradient-color-stop-positions"]);if(A)return f.position(A);if(h.value.value[h.value.value.length-1]==="%"&&T(h.value.value.slice(0,-1)))return f.position(h.value.value)}}}),i(l,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)},{values:Array.from({length:21},(h,A)=>`${A*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}k("from",{color:l=>[v(),o("--tw-sort","--tw-gradient-from"),o("--tw-gradient-from",l),o("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:l=>[v(),o("--tw-gradient-from-position",l)]}),t("via-none",[["--tw-gradient-via-stops","initial"]]),k("via",{color:l=>[v(),o("--tw-sort","--tw-gradient-via"),o("--tw-gradient-via",l),o("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),o("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:l=>[v(),o("--tw-gradient-via-position",l)]}),k("to",{color:l=>[v(),o("--tw-sort","--tw-gradient-to"),o("--tw-gradient-to",l),o("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:l=>[v(),o("--tw-gradient-to-position",l)]}),t("mask-none",[["mask-image","none"]]),r.functional("mask",l=>{if(!l.value||l.modifier||l.value.kind!=="arbitrary")return;let f=l.value.value;switch(l.value.dataType??Z(f,["image","percentage","position","bg-size","length","url"])){case"percentage":case"position":return l.modifier?void 0:[o("mask-position",f)];case"bg-size":case"length":case"size":return[o("mask-size",f)];case"image":case"url":default:return[o("mask-image",f)]}}),t("mask-add",[["mask-composite","add"]]),t("mask-subtract",[["mask-composite","subtract"]]),t("mask-intersect",[["mask-composite","intersect"]]),t("mask-exclude",[["mask-composite","exclude"]]),t("mask-alpha",[["mask-mode","alpha"]]),t("mask-luminance",[["mask-mode","luminance"]]),t("mask-match",[["mask-mode","match-source"]]),t("mask-type-alpha",[["mask-type","alpha"]]),t("mask-type-luminance",[["mask-type","luminance"]]),t("mask-auto",[["mask-size","auto"]]),t("mask-cover",[["mask-size","cover"]]),t("mask-contain",[["mask-size","contain"]]),n("mask-size",{handle(l){if(l)return[o("mask-size",l)]}}),t("mask-top",[["mask-position","top"]]),t("mask-top-left",[["mask-position","left top"]]),t("mask-top-right",[["mask-position","right top"]]),t("mask-bottom",[["mask-position","bottom"]]),t("mask-bottom-left",[["mask-position","left bottom"]]),t("mask-bottom-right",[["mask-position","right bottom"]]),t("mask-left",[["mask-position","left"]]),t("mask-right",[["mask-position","right"]]),t("mask-center",[["mask-position","center"]]),n("mask-position",{handle(l){if(l)return[o("mask-position",l)]}}),t("mask-repeat",[["mask-repeat","repeat"]]),t("mask-no-repeat",[["mask-repeat","no-repeat"]]),t("mask-repeat-x",[["mask-repeat","repeat-x"]]),t("mask-repeat-y",[["mask-repeat","repeat-y"]]),t("mask-repeat-round",[["mask-repeat","round"]]),t("mask-repeat-space",[["mask-repeat","space"]]),t("mask-clip-border",[["mask-clip","border-box"]]),t("mask-clip-padding",[["mask-clip","padding-box"]]),t("mask-clip-content",[["mask-clip","content-box"]]),t("mask-clip-fill",[["mask-clip","fill-box"]]),t("mask-clip-stroke",[["mask-clip","stroke-box"]]),t("mask-clip-view",[["mask-clip","view-box"]]),t("mask-no-clip",[["mask-clip","no-clip"]]),t("mask-origin-border",[["mask-origin","border-box"]]),t("mask-origin-padding",[["mask-origin","padding-box"]]),t("mask-origin-content",[["mask-origin","content-box"]]),t("mask-origin-fill",[["mask-origin","fill-box"]]),t("mask-origin-stroke",[["mask-origin","stroke-box"]]),t("mask-origin-view",[["mask-origin","view-box"]]);let x=()=>z([$("--tw-mask-linear","linear-gradient(#fff, #fff)"),$("--tw-mask-radial","linear-gradient(#fff, #fff)"),$("--tw-mask-conic","linear-gradient(#fff, #fff)")]);function S(l,f){r.functional(l,h=>{if(h.value){if(h.value.kind==="arbitrary"){let A=h.value.value;switch(h.value.dataType??Z(A,["length","percentage","color"])){case"color":return A=X(A,h.modifier,e),A===null?void 0:f.color(A);case"percentage":return h.modifier||!T(A.slice(0,-1))?void 0:f.position(A);default:return h.modifier?void 0:f.position(A)}}{let A=te(h,e,["--background-color","--color"]);if(A)return f.color(A)}{if(h.modifier)return;let A=Z(h.value.value,["number","percentage"]);if(!A)return;switch(A){case"number":{let y=e.resolve(null,["--spacing"]);return!y||!xe(h.value.value)?void 0:f.position(`calc(${y} * ${h.value.value})`)}case"percentage":return T(h.value.value.slice(0,-1))?f.position(h.value.value):void 0;default:return}}}}),i(l,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)},{values:Array.from({length:21},(h,A)=>`${A*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}]),i(l,()=>[{values:Array.from({length:21},(h,A)=>`${A*5}%`)},{values:e.get(["--spacing"])?at:[]},{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)}])}let C=()=>z([$("--tw-mask-left","linear-gradient(#fff, #fff)"),$("--tw-mask-right","linear-gradient(#fff, #fff)"),$("--tw-mask-bottom","linear-gradient(#fff, #fff)"),$("--tw-mask-top","linear-gradient(#fff, #fff)")]);function b(l,f,h){S(l,{color(A){let y=[x(),C(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let N of["top","right","bottom","left"])h[N]&&(y.push(o(`--tw-mask-${N}`,`linear-gradient(to ${N}, var(--tw-mask-${N}-from-color) var(--tw-mask-${N}-from-position), var(--tw-mask-${N}-to-color) var(--tw-mask-${N}-to-position))`)),y.push(z([$(`--tw-mask-${N}-from-position`,"0%"),$(`--tw-mask-${N}-to-position`,"100%"),$(`--tw-mask-${N}-from-color`,"black"),$(`--tw-mask-${N}-to-color`,"transparent")])),y.push(o(`--tw-mask-${N}-${f}-color`,A)));return y},position(A){let y=[x(),C(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let N of["top","right","bottom","left"])h[N]&&(y.push(o(`--tw-mask-${N}`,`linear-gradient(to ${N}, var(--tw-mask-${N}-from-color) var(--tw-mask-${N}-from-position), var(--tw-mask-${N}-to-color) var(--tw-mask-${N}-to-position))`)),y.push(z([$(`--tw-mask-${N}-from-position`,"0%"),$(`--tw-mask-${N}-to-position`,"100%"),$(`--tw-mask-${N}-from-color`,"black"),$(`--tw-mask-${N}-to-color`,"transparent")])),y.push(o(`--tw-mask-${N}-${f}-position`,A)));return y}})}b("mask-x-from","from",{top:!1,right:!0,bottom:!1,left:!0}),b("mask-x-to","to",{top:!1,right:!0,bottom:!1,left:!0}),b("mask-y-from","from",{top:!0,right:!1,bottom:!0,left:!1}),b("mask-y-to","to",{top:!0,right:!1,bottom:!0,left:!1}),b("mask-t-from","from",{top:!0,right:!1,bottom:!1,left:!1}),b("mask-t-to","to",{top:!0,right:!1,bottom:!1,left:!1}),b("mask-r-from","from",{top:!1,right:!0,bottom:!1,left:!1}),b("mask-r-to","to",{top:!1,right:!0,bottom:!1,left:!1}),b("mask-b-from","from",{top:!1,right:!1,bottom:!0,left:!1}),b("mask-b-to","to",{top:!1,right:!1,bottom:!0,left:!1}),b("mask-l-from","from",{top:!1,right:!1,bottom:!1,left:!0}),b("mask-l-to","to",{top:!1,right:!1,bottom:!1,left:!0});let _=()=>z([$("--tw-mask-linear-position","0deg"),$("--tw-mask-linear-from-position","0%"),$("--tw-mask-linear-to-position","100%"),$("--tw-mask-linear-from-color","black"),$("--tw-mask-linear-to-color","transparent")]);n("mask-linear",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(l){return T(l.value)?`calc(1deg * ${l.value})`:null},handleNegativeBareValue(l){return T(l.value)?`calc(1deg * -${l.value})`:null},handle:l=>[x(),_(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"),o("--tw-mask-linear-position",l)]}),i("mask-linear",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),S("mask-linear-from",{color:l=>[x(),_(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),o("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),o("--tw-mask-linear-from-color",l)],position:l=>[x(),_(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),o("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),o("--tw-mask-linear-from-position",l)]}),S("mask-linear-to",{color:l=>[x(),_(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),o("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),o("--tw-mask-linear-to-color",l)],position:l=>[x(),_(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),o("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),o("--tw-mask-linear-to-position",l)]});let R=()=>z([$("--tw-mask-radial-from-position","0%"),$("--tw-mask-radial-to-position","100%"),$("--tw-mask-radial-from-color","black"),$("--tw-mask-radial-to-color","transparent"),$("--tw-mask-radial-shape","ellipse"),$("--tw-mask-radial-size","farthest-corner"),$("--tw-mask-radial-position","center")]);t("mask-circle",[["--tw-mask-radial-shape","circle"]]),t("mask-ellipse",[["--tw-mask-radial-shape","ellipse"]]),t("mask-radial-closest-side",[["--tw-mask-radial-size","closest-side"]]),t("mask-radial-farthest-side",[["--tw-mask-radial-size","farthest-side"]]),t("mask-radial-closest-corner",[["--tw-mask-radial-size","closest-corner"]]),t("mask-radial-farthest-corner",[["--tw-mask-radial-size","farthest-corner"]]),t("mask-radial-at-top",[["--tw-mask-radial-position","top"]]),t("mask-radial-at-top-left",[["--tw-mask-radial-position","top left"]]),t("mask-radial-at-top-right",[["--tw-mask-radial-position","top right"]]),t("mask-radial-at-bottom",[["--tw-mask-radial-position","bottom"]]),t("mask-radial-at-bottom-left",[["--tw-mask-radial-position","bottom left"]]),t("mask-radial-at-bottom-right",[["--tw-mask-radial-position","bottom right"]]),t("mask-radial-at-left",[["--tw-mask-radial-position","left"]]),t("mask-radial-at-right",[["--tw-mask-radial-position","right"]]),t("mask-radial-at-center",[["--tw-mask-radial-position","center"]]),n("mask-radial-at",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:l=>[o("--tw-mask-radial-position",l)]}),n("mask-radial",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:l=>[x(),R(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"),o("--tw-mask-radial-size",l)]}),S("mask-radial-from",{color:l=>[x(),R(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),o("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),o("--tw-mask-radial-from-color",l)],position:l=>[x(),R(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),o("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),o("--tw-mask-radial-from-position",l)]}),S("mask-radial-to",{color:l=>[x(),R(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),o("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),o("--tw-mask-radial-to-color",l)],position:l=>[x(),R(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),o("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),o("--tw-mask-radial-to-position",l)]});let D=()=>z([$("--tw-mask-conic-position","0deg"),$("--tw-mask-conic-from-position","0%"),$("--tw-mask-conic-to-position","100%"),$("--tw-mask-conic-from-color","black"),$("--tw-mask-conic-to-color","transparent")]);n("mask-conic",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(l){return T(l.value)?`calc(1deg * ${l.value})`:null},handleNegativeBareValue(l){return T(l.value)?`calc(1deg * -${l.value})`:null},handle:l=>[x(),D(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"),o("--tw-mask-conic-position",l)]}),i("mask-conic",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),S("mask-conic-from",{color:l=>[x(),D(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),o("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),o("--tw-mask-conic-from-color",l)],position:l=>[x(),D(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),o("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),o("--tw-mask-conic-from-position",l)]}),S("mask-conic-to",{color:l=>[x(),D(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),o("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),o("--tw-mask-conic-to-color",l)],position:l=>[x(),D(),o("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),o("mask-composite","intersect"),o("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),o("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),o("--tw-mask-conic-to-position",l)]}),t("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),t("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),t("bg-clip-text",[["background-clip","text"]]),t("bg-clip-border",[["background-clip","border-box"]]),t("bg-clip-padding",[["background-clip","padding-box"]]),t("bg-clip-content",[["background-clip","content-box"]]),t("bg-origin-border",[["background-origin","border-box"]]),t("bg-origin-padding",[["background-origin","padding-box"]]),t("bg-origin-content",[["background-origin","content-box"]]);for(let l of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])t(`bg-blend-${l}`,[["background-blend-mode",l]]),t(`mix-blend-${l}`,[["mix-blend-mode",l]]);t("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),t("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),t("fill-none",[["fill","none"]]),r.functional("fill",l=>{if(!l.value)return;if(l.value.kind==="arbitrary"){let h=X(l.value.value,l.modifier,e);return h===null?void 0:[o("fill",h)]}let f=te(l,e,["--fill","--color"]);if(f)return[o("fill",f)]}),i("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(l,f)=>`${f*5}`)}]),t("stroke-none",[["stroke","none"]]),r.functional("stroke",l=>{if(l.value){if(l.value.kind==="arbitrary"){let f=l.value.value;switch(l.value.dataType??Z(f,["color","number","length","percentage"])){case"number":case"length":case"percentage":return l.modifier?void 0:[o("stroke-width",f)];default:return f=X(l.value.value,l.modifier,e),f===null?void 0:[o("stroke",f)]}}{let f=te(l,e,["--stroke","--color"]);if(f)return[o("stroke",f)]}{let f=e.resolve(l.value.value,["--stroke-width"]);if(f)return[o("stroke-width",f)];if(T(l.value.value))return[o("stroke-width",l.value.value)]}}}),i("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(l,f)=>`${f*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),t("object-contain",[["object-fit","contain"]]),t("object-cover",[["object-fit","cover"]]),t("object-fill",[["object-fit","fill"]]),t("object-none",[["object-fit","none"]]),t("object-scale-down",[["object-fit","scale-down"]]),n("object",{themeKeys:["--object-position"],handle:l=>[o("object-position",l)],staticValues:{top:[o("object-position","top")],"top-left":[o("object-position","left top")],"top-right":[o("object-position","right top")],bottom:[o("object-position","bottom")],"bottom-left":[o("object-position","left bottom")],"bottom-right":[o("object-position","right bottom")],left:[o("object-position","left")],right:[o("object-position","right")],center:[o("object-position","center")]}});for(let[l,f]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])a(l,["--padding","--spacing"],h=>[o(f,h)]);t("text-left",[["text-align","left"]]),t("text-center",[["text-align","center"]]),t("text-right",[["text-align","right"]]),t("text-justify",[["text-align","justify"]]),t("text-start",[["text-align","start"]]),t("text-end",[["text-align","end"]]),a("indent",["--text-indent","--spacing"],l=>[o("text-indent",l)],{supportsNegative:!0}),t("align-baseline",[["vertical-align","baseline"]]),t("align-top",[["vertical-align","top"]]),t("align-middle",[["vertical-align","middle"]]),t("align-bottom",[["vertical-align","bottom"]]),t("align-text-top",[["vertical-align","text-top"]]),t("align-text-bottom",[["vertical-align","text-bottom"]]),t("align-sub",[["vertical-align","sub"]]),t("align-super",[["vertical-align","super"]]),n("align",{themeKeys:[],handle:l=>[o("vertical-align",l)]}),r.functional("font",l=>{if(!(!l.value||l.modifier)){if(l.value.kind==="arbitrary"){let f=l.value.value;switch(l.value.dataType??Z(f,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[o("font-family",f)];default:return[z([$("--tw-font-weight")]),o("--tw-font-weight",f),o("font-weight",f)]}}{let f=e.resolveWith(l.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(f){let[h,A={}]=f;return[o("font-family",h),o("font-feature-settings",A["--font-feature-settings"]),o("font-variation-settings",A["--font-variation-settings"])]}}{let f=e.resolve(l.value.value,["--font-weight"]);if(f)return[z([$("--tw-font-weight")]),o("--tw-font-weight",f),o("font-weight",f)]}}}),i("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),t("uppercase",[["text-transform","uppercase"]]),t("lowercase",[["text-transform","lowercase"]]),t("capitalize",[["text-transform","capitalize"]]),t("normal-case",[["text-transform","none"]]),t("italic",[["font-style","italic"]]),t("not-italic",[["font-style","normal"]]),t("underline",[["text-decoration-line","underline"]]),t("overline",[["text-decoration-line","overline"]]),t("line-through",[["text-decoration-line","line-through"]]),t("no-underline",[["text-decoration-line","none"]]),t("font-stretch-normal",[["font-stretch","normal"]]),t("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),t("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),t("font-stretch-condensed",[["font-stretch","condensed"]]),t("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),t("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),t("font-stretch-expanded",[["font-stretch","expanded"]]),t("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),t("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),n("font-stretch",{handleBareValue:({value:l})=>{if(!l.endsWith("%"))return null;let f=Number(l.slice(0,-1));return!T(f)||Number.isNaN(f)||f<50||f>200?null:l},handle:l=>[o("font-stretch",l)]}),i("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),s("placeholder",{themeKeys:["--background-color","--color"],handle:l=>[B("&::placeholder",[o("--tw-sort","placeholder-color"),o("color",l)])]}),t("decoration-solid",[["text-decoration-style","solid"]]),t("decoration-double",[["text-decoration-style","double"]]),t("decoration-dotted",[["text-decoration-style","dotted"]]),t("decoration-dashed",[["text-decoration-style","dashed"]]),t("decoration-wavy",[["text-decoration-style","wavy"]]),t("decoration-auto",[["text-decoration-thickness","auto"]]),t("decoration-from-font",[["text-decoration-thickness","from-font"]]),r.functional("decoration",l=>{if(l.value){if(l.value.kind==="arbitrary"){let f=l.value.value;switch(l.value.dataType??Z(f,["color","length","percentage"])){case"length":case"percentage":return l.modifier?void 0:[o("text-decoration-thickness",f)];default:return f=X(f,l.modifier,e),f===null?void 0:[o("text-decoration-color",f)]}}{let f=e.resolve(l.value.value,["--text-decoration-thickness"]);if(f)return l.modifier?void 0:[o("text-decoration-thickness",f)];if(T(l.value.value))return l.modifier?void 0:[o("text-decoration-thickness",`${l.value.value}px`)]}{let f=te(l,e,["--text-decoration-color","--color"]);if(f)return[o("text-decoration-color",f)]}}}),i("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(l,f)=>`${f*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),n("animate",{themeKeys:["--animate"],handle:l=>[o("animation",l)],staticValues:{none:[o("animation","none")]}});{let l=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),f=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),h=()=>z([$("--tw-blur"),$("--tw-brightness"),$("--tw-contrast"),$("--tw-grayscale"),$("--tw-hue-rotate"),$("--tw-invert"),$("--tw-opacity"),$("--tw-saturate"),$("--tw-sepia"),$("--tw-drop-shadow"),$("--tw-drop-shadow-color"),$("--tw-drop-shadow-alpha","100%","<percentage>"),$("--tw-drop-shadow-size")]),A=()=>z([$("--tw-backdrop-blur"),$("--tw-backdrop-brightness"),$("--tw-backdrop-contrast"),$("--tw-backdrop-grayscale"),$("--tw-backdrop-hue-rotate"),$("--tw-backdrop-invert"),$("--tw-backdrop-opacity"),$("--tw-backdrop-saturate"),$("--tw-backdrop-sepia")]);r.functional("filter",y=>{if(!y.modifier){if(y.value===null)return[h(),o("filter",l)];if(y.value.kind==="arbitrary")return[o("filter",y.value.value)];switch(y.value.value){case"none":return[o("filter","none")]}}}),r.functional("backdrop-filter",y=>{if(!y.modifier){if(y.value===null)return[A(),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)];if(y.value.kind==="arbitrary")return[o("-webkit-backdrop-filter",y.value.value),o("backdrop-filter",y.value.value)];switch(y.value.value){case"none":return[o("-webkit-backdrop-filter","none"),o("backdrop-filter","none")]}}}),n("blur",{themeKeys:["--blur"],handle:y=>[h(),o("--tw-blur",`blur(${y})`),o("filter",l)],staticValues:{none:[h(),o("--tw-blur"," "),o("filter",l)]}}),n("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:y=>[A(),o("--tw-backdrop-blur",`blur(${y})`),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)],staticValues:{none:[A(),o("--tw-backdrop-blur"," "),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)]}}),n("brightness",{themeKeys:["--brightness"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,handle:y=>[h(),o("--tw-brightness",`brightness(${y})`),o("filter",l)]}),n("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,handle:y=>[A(),o("--tw-backdrop-brightness",`brightness(${y})`),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)]}),i("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),i("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),n("contrast",{themeKeys:["--contrast"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,handle:y=>[h(),o("--tw-contrast",`contrast(${y})`),o("filter",l)]}),n("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,handle:y=>[A(),o("--tw-backdrop-contrast",`contrast(${y})`),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)]}),i("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),i("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),n("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[h(),o("--tw-grayscale",`grayscale(${y})`),o("filter",l)]}),n("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[A(),o("--tw-backdrop-grayscale",`grayscale(${y})`),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)]}),i("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),i("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),n("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:y})=>T(y)?`${y}deg`:null,handle:y=>[h(),o("--tw-hue-rotate",`hue-rotate(${y})`),o("filter",l)]}),n("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:y})=>T(y)?`${y}deg`:null,handle:y=>[A(),o("--tw-backdrop-hue-rotate",`hue-rotate(${y})`),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)]}),i("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),i("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),n("invert",{themeKeys:["--invert"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[h(),o("--tw-invert",`invert(${y})`),o("filter",l)]}),n("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[A(),o("--tw-backdrop-invert",`invert(${y})`),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)]}),i("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),i("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),n("saturate",{themeKeys:["--saturate"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,handle:y=>[h(),o("--tw-saturate",`saturate(${y})`),o("filter",l)]}),n("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,handle:y=>[A(),o("--tw-backdrop-saturate",`saturate(${y})`),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)]}),i("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),i("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),n("sepia",{themeKeys:["--sepia"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[h(),o("--tw-sepia",`sepia(${y})`),o("filter",l)]}),n("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:y})=>T(y)?`${y}%`:null,defaultValue:"100%",handle:y=>[A(),o("--tw-backdrop-sepia",`sepia(${y})`),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)]}),i("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),i("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),t("drop-shadow-none",[h,["--tw-drop-shadow"," "],["filter",l]]),r.functional("drop-shadow",y=>{let N;if(y.modifier&&(y.modifier.kind==="arbitrary"?N=y.modifier.value:T(y.modifier.value)&&(N=`${y.modifier.value}%`)),!y.value){let P=e.get(["--drop-shadow"]),V=e.resolve(null,["--drop-shadow"]);return P===null||V===null?void 0:[h(),o("--tw-drop-shadow-alpha",N),...lt("--tw-drop-shadow-size",P,N,E=>`var(--tw-drop-shadow-color, ${E})`),o("--tw-drop-shadow",U(V,",").map(E=>`drop-shadow(${E})`).join(" ")),o("filter",l)]}if(y.value.kind==="arbitrary"){let P=y.value.value;switch(y.value.dataType??Z(P,["color"])){case"color":return P=X(P,y.modifier,e),P===null?void 0:[h(),o("--tw-drop-shadow-color",Q(P,"var(--tw-drop-shadow-alpha)")),o("--tw-drop-shadow","var(--tw-drop-shadow-size)")];default:return y.modifier&&!N?void 0:[h(),o("--tw-drop-shadow-alpha",N),...lt("--tw-drop-shadow-size",P,N,E=>`var(--tw-drop-shadow-color, ${E})`),o("--tw-drop-shadow","var(--tw-drop-shadow-size)"),o("filter",l)]}}{let P=e.get([`--drop-shadow-${y.value.value}`]),V=e.resolve(y.value.value,["--drop-shadow"]);if(P&&V)return y.modifier&&!N?void 0:N?[h(),o("--tw-drop-shadow-alpha",N),...lt("--tw-drop-shadow-size",P,N,E=>`var(--tw-drop-shadow-color, ${E})`),o("--tw-drop-shadow","var(--tw-drop-shadow-size)"),o("filter",l)]:[h(),o("--tw-drop-shadow-alpha",N),...lt("--tw-drop-shadow-size",P,N,E=>`var(--tw-drop-shadow-color, ${E})`),o("--tw-drop-shadow",U(V,",").map(E=>`drop-shadow(${E})`).join(" ")),o("filter",l)]}{let P=te(y,e,["--drop-shadow-color","--color"]);if(P)return P==="inherit"?[h(),o("--tw-drop-shadow-color","inherit"),o("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:[h(),o("--tw-drop-shadow-color",Q(P,"var(--tw-drop-shadow-alpha)")),o("--tw-drop-shadow","var(--tw-drop-shadow-size)")]}}),i("drop-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--drop-shadow-color","--color"],modifiers:Array.from({length:21},(y,N)=>`${N*5}`)},{valueThemeKeys:["--drop-shadow"]}]),n("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:y})=>ot(y)?`${y}%`:null,handle:y=>[A(),o("--tw-backdrop-opacity",`opacity(${y})`),o("-webkit-backdrop-filter",f),o("backdrop-filter",f)]}),i("backdrop-opacity",()=>[{values:Array.from({length:21},(y,N)=>`${N*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let l=`var(--tw-ease, ${e.resolve(null,["--default-transition-timing-function"])??"ease"})`,f=`var(--tw-duration, ${e.resolve(null,["--default-transition-duration"])??"0s"})`;n("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events",themeKeys:["--transition-property"],handle:h=>[o("transition-property",h),o("transition-timing-function",l),o("transition-duration",f)],staticValues:{none:[o("transition-property","none")],all:[o("transition-property","all"),o("transition-timing-function",l),o("transition-duration",f)],colors:[o("transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"),o("transition-timing-function",l),o("transition-duration",f)],opacity:[o("transition-property","opacity"),o("transition-timing-function",l),o("transition-duration",f)],shadow:[o("transition-property","box-shadow"),o("transition-timing-function",l),o("transition-duration",f)],transform:[o("transition-property","transform, translate, scale, rotate"),o("transition-timing-function",l),o("transition-duration",f)]}}),t("transition-discrete",[["transition-behavior","allow-discrete"]]),t("transition-normal",[["transition-behavior","normal"]]),n("delay",{handleBareValue:({value:h})=>T(h)?`${h}ms`:null,themeKeys:["--transition-delay"],handle:h=>[o("transition-delay",h)]});{let h=()=>z([$("--tw-duration")]);t("duration-initial",[h,["--tw-duration","initial"]]),r.functional("duration",A=>{if(A.modifier||!A.value)return;let y=null;if(A.value.kind==="arbitrary"?y=A.value.value:(y=e.resolve(A.value.fraction??A.value.value,["--transition-duration"]),y===null&&T(A.value.value)&&(y=`${A.value.value}ms`)),y!==null)return[h(),o("--tw-duration",y),o("transition-duration",y)]})}i("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),i("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let l=()=>z([$("--tw-ease")]);n("ease",{themeKeys:["--ease"],handle:f=>[l(),o("--tw-ease",f),o("transition-timing-function",f)],staticValues:{initial:[l(),o("--tw-ease","initial")],linear:[l(),o("--tw-ease","linear"),o("transition-timing-function","linear")]}})}t("will-change-auto",[["will-change","auto"]]),t("will-change-scroll",[["will-change","scroll-position"]]),t("will-change-contents",[["will-change","contents"]]),t("will-change-transform",[["will-change","transform"]]),n("will-change",{themeKeys:[],handle:l=>[o("will-change",l)]}),t("content-none",[["--tw-content","none"],["content","none"]]),n("content",{themeKeys:[],handle:l=>[z([$("--tw-content",'""')]),o("--tw-content",l),o("content","var(--tw-content)")]});{let l="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",f=()=>z([$("--tw-contain-size"),$("--tw-contain-layout"),$("--tw-contain-paint"),$("--tw-contain-style")]);t("contain-none",[["contain","none"]]),t("contain-content",[["contain","content"]]),t("contain-strict",[["contain","strict"]]),t("contain-size",[f,["--tw-contain-size","size"],["contain",l]]),t("contain-inline-size",[f,["--tw-contain-size","inline-size"],["contain",l]]),t("contain-layout",[f,["--tw-contain-layout","layout"],["contain",l]]),t("contain-paint",[f,["--tw-contain-paint","paint"],["contain",l]]),t("contain-style",[f,["--tw-contain-style","style"],["contain",l]]),n("contain",{themeKeys:[],handle:h=>[o("contain",h)]})}t("forced-color-adjust-none",[["forced-color-adjust","none"]]),t("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),a("leading",["--leading","--spacing"],l=>[z([$("--tw-leading")]),o("--tw-leading",l),o("line-height",l)],{staticValues:{none:[z([$("--tw-leading")]),o("--tw-leading","1"),o("line-height","1")]}}),n("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:l=>[z([$("--tw-tracking")]),o("--tw-tracking",l),o("letter-spacing",l)]}),t("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),t("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let l="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",f=()=>z([$("--tw-ordinal"),$("--tw-slashed-zero"),$("--tw-numeric-figure"),$("--tw-numeric-spacing"),$("--tw-numeric-fraction")]);t("normal-nums",[["font-variant-numeric","normal"]]),t("ordinal",[f,["--tw-ordinal","ordinal"],["font-variant-numeric",l]]),t("slashed-zero",[f,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",l]]),t("lining-nums",[f,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",l]]),t("oldstyle-nums",[f,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",l]]),t("proportional-nums",[f,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",l]]),t("tabular-nums",[f,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",l]]),t("diagonal-fractions",[f,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",l]]),t("stacked-fractions",[f,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",l]])}{let l=()=>z([$("--tw-outline-style","solid")]);r.static("outline-hidden",()=>[o("--tw-outline-style","none"),o("outline-style","none"),F("@media","(forced-colors: active)",[o("outline","2px solid transparent"),o("outline-offset","2px")])]),t("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),t("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),t("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),t("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),t("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),r.functional("outline",f=>{if(f.value===null){if(f.modifier)return;let h=e.get(["--default-outline-width"])??"1px";return[l(),o("outline-style","var(--tw-outline-style)"),o("outline-width",h)]}if(f.value.kind==="arbitrary"){let h=f.value.value;switch(f.value.dataType??Z(h,["color","length","number","percentage"])){case"length":case"number":case"percentage":return f.modifier?void 0:[l(),o("outline-style","var(--tw-outline-style)"),o("outline-width",h)];default:return h=X(h,f.modifier,e),h===null?void 0:[o("outline-color",h)]}}{let h=te(f,e,["--outline-color","--color"]);if(h)return[o("outline-color",h)]}{if(f.modifier)return;let h=e.resolve(f.value.value,["--outline-width"]);if(h)return[l(),o("outline-style","var(--tw-outline-style)"),o("outline-width",h)];if(T(f.value.value))return[l(),o("outline-style","var(--tw-outline-style)"),o("outline-width",`${f.value.value}px`)]}}),i("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(f,h)=>`${h*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),n("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:f})=>T(f)?`${f}px`:null,handle:f=>[o("outline-offset",f)]}),i("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}n("opacity",{themeKeys:["--opacity"],handleBareValue:({value:l})=>ot(l)?`${l}%`:null,handle:l=>[o("opacity",l)]}),i("opacity",()=>[{values:Array.from({length:21},(l,f)=>`${f*5}`),valueThemeKeys:["--opacity"]}]),n("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:l})=>T(l)?`${l}px`:null,handle:l=>[o("text-underline-offset",l)],staticValues:{auto:[o("text-underline-offset","auto")]}}),i("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),r.functional("text",l=>{if(l.value){if(l.value.kind==="arbitrary"){let f=l.value.value;switch(l.value.dataType??Z(f,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(l.modifier){let A=l.modifier.kind==="arbitrary"?l.modifier.value:e.resolve(l.modifier.value,["--leading"]);if(!A&&xe(l.modifier.value)){let y=e.resolve(null,["--spacing"]);if(!y)return null;A=`calc(${y} * ${l.modifier.value})`}return!A&&l.modifier.value==="none"&&(A="1"),A?[o("font-size",f),o("line-height",A)]:null}return[o("font-size",f)]}default:return f=X(f,l.modifier,e),f===null?void 0:[o("color",f)]}}{let f=te(l,e,["--text-color","--color"]);if(f)return[o("color",f)]}{let f=e.resolveWith(l.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(f){let[h,A={}]=Array.isArray(f)?f:[f];if(l.modifier){let y=l.modifier.kind==="arbitrary"?l.modifier.value:e.resolve(l.modifier.value,["--leading"]);if(!y&&xe(l.modifier.value)){let P=e.resolve(null,["--spacing"]);if(!P)return null;y=`calc(${P} * ${l.modifier.value})`}if(!y&&l.modifier.value==="none"&&(y="1"),!y)return null;let N=[o("font-size",h)];return y&&N.push(o("line-height",y)),N}return typeof A=="string"?[o("font-size",h),o("line-height",A)]:[o("font-size",h),o("line-height",A["--line-height"]?`var(--tw-leading, ${A["--line-height"]})`:void 0),o("letter-spacing",A["--letter-spacing"]?`var(--tw-tracking, ${A["--letter-spacing"]})`:void 0),o("font-weight",A["--font-weight"]?`var(--tw-font-weight, ${A["--font-weight"]})`:void 0)]}}}}),i("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(l,f)=>`${f*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);let K=()=>z([$("--tw-text-shadow-color"),$("--tw-text-shadow-alpha","100%","<percentage>")]);t("text-shadow-initial",[K,["--tw-text-shadow-color","initial"]]),r.functional("text-shadow",l=>{let f;if(l.modifier&&(l.modifier.kind==="arbitrary"?f=l.modifier.value:T(l.modifier.value)&&(f=`${l.modifier.value}%`)),!l.value){let h=e.get(["--text-shadow"]);return h===null?void 0:[K(),o("--tw-text-shadow-alpha",f),...pe("text-shadow",h,f,A=>`var(--tw-text-shadow-color, ${A})`)]}if(l.value.kind==="arbitrary"){let h=l.value.value;switch(l.value.dataType??Z(h,["color"])){case"color":return h=X(h,l.modifier,e),h===null?void 0:[K(),o("--tw-text-shadow-color",Q(h,"var(--tw-text-shadow-alpha)"))];default:return[K(),o("--tw-text-shadow-alpha",f),...pe("text-shadow",h,f,y=>`var(--tw-text-shadow-color, ${y})`)]}}switch(l.value.value){case"none":return l.modifier?void 0:[K(),o("text-shadow","none")];case"inherit":return l.modifier?void 0:[K(),o("--tw-text-shadow-color","inherit")]}{let h=e.get([`--text-shadow-${l.value.value}`]);if(h)return[K(),o("--tw-text-shadow-alpha",f),...pe("text-shadow",h,f,A=>`var(--tw-text-shadow-color, ${A})`)]}{let h=te(l,e,["--text-shadow-color","--color"]);if(h)return[K(),o("--tw-text-shadow-color",Q(h,"var(--tw-text-shadow-alpha)"))]}}),i("text-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-shadow-color","--color"],modifiers:Array.from({length:21},(l,f)=>`${f*5}`)},{values:["none"]},{valueThemeKeys:["--text-shadow"],modifiers:Array.from({length:21},(l,f)=>`${f*5}`),hasDefaultValue:e.get(["--text-shadow"])!==null}]);{let y=function(V){return`var(--tw-ring-inset,) 0 0 0 calc(${V} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${A})`},N=function(V){return`inset 0 0 0 ${V} var(--tw-inset-ring-color, currentcolor)`};var M=y,ie=N;let l=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),f="0 0 #0000",h=()=>z([$("--tw-shadow",f),$("--tw-shadow-color"),$("--tw-shadow-alpha","100%","<percentage>"),$("--tw-inset-shadow",f),$("--tw-inset-shadow-color"),$("--tw-inset-shadow-alpha","100%","<percentage>"),$("--tw-ring-color"),$("--tw-ring-shadow",f),$("--tw-inset-ring-color"),$("--tw-inset-ring-shadow",f),$("--tw-ring-inset"),$("--tw-ring-offset-width","0px","<length>"),$("--tw-ring-offset-color","#fff"),$("--tw-ring-offset-shadow",f)]);t("shadow-initial",[h,["--tw-shadow-color","initial"]]),r.functional("shadow",V=>{let E;if(V.modifier&&(V.modifier.kind==="arbitrary"?E=V.modifier.value:T(V.modifier.value)&&(E=`${V.modifier.value}%`)),!V.value){let j=e.get(["--shadow"]);return j===null?void 0:[h(),o("--tw-shadow-alpha",E),...pe("--tw-shadow",j,E,ae=>`var(--tw-shadow-color, ${ae})`),o("box-shadow",l)]}if(V.value.kind==="arbitrary"){let j=V.value.value;switch(V.value.dataType??Z(j,["color"])){case"color":return j=X(j,V.modifier,e),j===null?void 0:[h(),o("--tw-shadow-color",Q(j,"var(--tw-shadow-alpha)"))];default:return[h(),o("--tw-shadow-alpha",E),...pe("--tw-shadow",j,E,wt=>`var(--tw-shadow-color, ${wt})`),o("box-shadow",l)]}}switch(V.value.value){case"none":return V.modifier?void 0:[h(),o("--tw-shadow",f),o("box-shadow",l)];case"inherit":return V.modifier?void 0:[h(),o("--tw-shadow-color","inherit")]}{let j=e.get([`--shadow-${V.value.value}`]);if(j)return[h(),o("--tw-shadow-alpha",E),...pe("--tw-shadow",j,E,ae=>`var(--tw-shadow-color, ${ae})`),o("box-shadow",l)]}{let j=te(V,e,["--box-shadow-color","--color"]);if(j)return[h(),o("--tw-shadow-color",Q(j,"var(--tw-shadow-alpha)"))]}}),i("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(V,E)=>`${E*5}`)},{values:["none"]},{valueThemeKeys:["--shadow"],modifiers:Array.from({length:21},(V,E)=>`${E*5}`),hasDefaultValue:e.get(["--shadow"])!==null}]),t("inset-shadow-initial",[h,["--tw-inset-shadow-color","initial"]]),r.functional("inset-shadow",V=>{let E;if(V.modifier&&(V.modifier.kind==="arbitrary"?E=V.modifier.value:T(V.modifier.value)&&(E=`${V.modifier.value}%`)),!V.value){let j=e.get(["--inset-shadow"]);return j===null?void 0:[h(),o("--tw-inset-shadow-alpha",E),...pe("--tw-inset-shadow",j,E,ae=>`var(--tw-inset-shadow-color, ${ae})`),o("box-shadow",l)]}if(V.value.kind==="arbitrary"){let j=V.value.value;switch(V.value.dataType??Z(j,["color"])){case"color":return j=X(j,V.modifier,e),j===null?void 0:[h(),o("--tw-inset-shadow-color",Q(j,"var(--tw-inset-shadow-alpha)"))];default:return[h(),o("--tw-inset-shadow-alpha",E),...pe("--tw-inset-shadow",j,E,wt=>`var(--tw-inset-shadow-color, ${wt})`,"inset "),o("box-shadow",l)]}}switch(V.value.value){case"none":return V.modifier?void 0:[h(),o("--tw-inset-shadow",f),o("box-shadow",l)];case"inherit":return V.modifier?void 0:[h(),o("--tw-inset-shadow-color","inherit")]}{let j=e.get([`--inset-shadow-${V.value.value}`]);if(j)return[h(),o("--tw-inset-shadow-alpha",E),...pe("--tw-inset-shadow",j,E,ae=>`var(--tw-inset-shadow-color, ${ae})`),o("box-shadow",l)]}{let j=te(V,e,["--box-shadow-color","--color"]);if(j)return[h(),o("--tw-inset-shadow-color",Q(j,"var(--tw-inset-shadow-alpha)"))]}}),i("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(V,E)=>`${E*5}`)},{values:["none"]},{valueThemeKeys:["--inset-shadow"],modifiers:Array.from({length:21},(V,E)=>`${E*5}`),hasDefaultValue:e.get(["--inset-shadow"])!==null}]),t("ring-inset",[h,["--tw-ring-inset","inset"]]);let A=e.get(["--default-ring-color"])??"currentcolor";r.functional("ring",V=>{if(!V.value){if(V.modifier)return;let E=e.get(["--default-ring-width"])??"1px";return[h(),o("--tw-ring-shadow",y(E)),o("box-shadow",l)]}if(V.value.kind==="arbitrary"){let E=V.value.value;switch(V.value.dataType??Z(E,["color","length"])){case"length":return V.modifier?void 0:[h(),o("--tw-ring-shadow",y(E)),o("box-shadow",l)];default:return E=X(E,V.modifier,e),E===null?void 0:[o("--tw-ring-color",E)]}}{let E=te(V,e,["--ring-color","--color"]);if(E)return[o("--tw-ring-color",E)]}{if(V.modifier)return;let E=e.resolve(V.value.value,["--ring-width"]);if(E===null&&T(V.value.value)&&(E=`${V.value.value}px`),E)return[h(),o("--tw-ring-shadow",y(E)),o("box-shadow",l)]}}),i("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(V,E)=>`${E*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),r.functional("inset-ring",V=>{if(!V.value)return V.modifier?void 0:[h(),o("--tw-inset-ring-shadow",N("1px")),o("box-shadow",l)];if(V.value.kind==="arbitrary"){let E=V.value.value;switch(V.value.dataType??Z(E,["color","length"])){case"length":return V.modifier?void 0:[h(),o("--tw-inset-ring-shadow",N(E)),o("box-shadow",l)];default:return E=X(E,V.modifier,e),E===null?void 0:[o("--tw-inset-ring-color",E)]}}{let E=te(V,e,["--ring-color","--color"]);if(E)return[o("--tw-inset-ring-color",E)]}{if(V.modifier)return;let E=e.resolve(V.value.value,["--ring-width"]);if(E===null&&T(V.value.value)&&(E=`${V.value.value}px`),E)return[h(),o("--tw-inset-ring-shadow",N(E)),o("box-shadow",l)]}}),i("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(V,E)=>`${E*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let P="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";r.functional("ring-offset",V=>{if(V.value){if(V.value.kind==="arbitrary"){let E=V.value.value;switch(V.value.dataType??Z(E,["color","length"])){case"length":return V.modifier?void 0:[o("--tw-ring-offset-width",E),o("--tw-ring-offset-shadow",P)];default:return E=X(E,V.modifier,e),E===null?void 0:[o("--tw-ring-offset-color",E)]}}{let E=e.resolve(V.value.value,["--ring-offset-width"]);if(E)return V.modifier?void 0:[o("--tw-ring-offset-width",E),o("--tw-ring-offset-shadow",P)];if(T(V.value.value))return V.modifier?void 0:[o("--tw-ring-offset-width",`${V.value.value}px`),o("--tw-ring-offset-shadow",P)]}{let E=te(V,e,["--ring-offset-color","--color"]);if(E)return[o("--tw-ring-offset-color",E)]}}})}return i("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(l,f)=>`${f*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),r.functional("@container",l=>{let f=null;if(l.value===null?f="inline-size":l.value.kind==="arbitrary"?f=l.value.value:l.value.kind==="named"&&l.value.value==="normal"?f="normal":!1,f!==null)return l.modifier?[o("container-type",f),o("container-name",l.modifier.value)]:[o("container-type",f)]}),i("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),r}var _t=["number","integer","ratio","percentage"];function Er(e){let r=e.params;return xn.test(r)?i=>{let t={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};L(e.nodes,n=>{if(n.kind!=="declaration"||!n.value||!n.value.includes("--value(")&&!n.value.includes("--modifier("))return;let s=G(n.value);ee(s,a=>{if(a.kind!=="function")return;if(a.value==="--spacing"&&!(t["--modifier"].usedSpacingNumber&&t["--value"].usedSpacingNumber))return ee(a.nodes,u=>{if(u.kind!=="function"||u.value!=="--value"&&u.value!=="--modifier")return;let c=u.value;for(let m of u.nodes)if(m.kind==="word"){if(m.value==="integer")t[c].usedSpacingInteger||=!0;else if(m.value==="number"&&(t[c].usedSpacingNumber||=!0,t["--modifier"].usedSpacingNumber&&t["--value"].usedSpacingNumber))return 2}}),0;if(a.value!=="--value"&&a.value!=="--modifier")return;let p=U(J(a.nodes),",");for(let[u,c]of p.entries())c=c.replace(/\\\*/g,"*"),c=c.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),c=c.replace(/\s+/g,""),c=c.replace(/(-\*){2,}/g,"-*"),c[0]==="-"&&c[1]==="-"&&!c.includes("-*")&&(c+="-*"),p[u]=c;a.nodes=G(p.join(","));for(let u of a.nodes)if(u.kind==="word"&&(u.value[0]==='"'||u.value[0]==="'")&&u.value[0]===u.value[u.value.length-1]){let c=u.value.slice(1,-1);t[a.value].literals.add(c)}else if(u.kind==="word"&&u.value[0]==="-"&&u.value[1]==="-"){let c=u.value.replace(/-\*.*$/g,"");t[a.value].themeKeys.add(c)}else if(u.kind==="word"&&!(u.value[0]==="["&&u.value[u.value.length-1]==="]")&&!_t.includes(u.value)){console.warn(`Unsupported bare value data type: "${u.value}".
Only valid data types are: ${_t.map(k=>`"${k}"`).join(", ")}.
`);let c=u.value,m=structuredClone(a),g="\xB6";ee(m.nodes,(k,{replaceWith:x})=>{k.kind==="word"&&k.value===c&&x({kind:"word",value:g})});let d="^".repeat(J([u]).length),w=J([m]).indexOf(g),v=["```css",J([a])," ".repeat(w)+d,"```"].join(`
`);console.warn(v)}}),n.value=J(s)}),i.utilities.functional(r.slice(0,-2),n=>{let s=structuredClone(e),a=n.value,p=n.modifier;if(a===null)return;let u=!1,c=!1,m=!1,g=!1,d=new Map,w=!1;if(L([s],(v,{parent:k,replaceWith:x})=>{if(k?.kind!=="rule"&&k?.kind!=="at-rule"||v.kind!=="declaration"||!v.value)return;let S=G(v.value);(ee(S,(b,{replaceWith:_})=>{if(b.kind==="function"){if(b.value==="--value"){u=!0;let R=$r(a,b,i);return R?(c=!0,R.ratio?w=!0:d.set(v,k),_(R.nodes),1):(u||=!1,x([]),2)}else if(b.value==="--modifier"){if(p===null)return x([]),2;m=!0;let R=$r(p,b,i);return R?(g=!0,_(R.nodes),1):(m||=!1,x([]),2)}}})??0)===0&&(v.value=J(S))}),u&&!c||m&&!g||w&&g||p&&!w&&!g)return null;if(w)for(let[v,k]of d){let x=k.nodes.indexOf(v);x!==-1&&k.nodes.splice(x,1)}return s.nodes}),i.utilities.suggest(r.slice(0,-2),()=>{let n=[],s=[];for(let[a,{literals:p,usedSpacingNumber:u,usedSpacingInteger:c,themeKeys:m}]of[[n,t["--value"]],[s,t["--modifier"]]]){for(let g of p)a.push(g);if(u)a.push(...at);else if(c)for(let g of at)T(g)&&a.push(g);for(let g of i.theme.keysInNamespaces(m))a.push(g.replace(Vr,(d,w,v)=>`${w}.${v}`))}return[{values:n,modifiers:s}]})}:bn.test(r)?i=>{i.utilities.static(r,()=>structuredClone(e.nodes))}:null}function $r(e,r,i){for(let t of r.nodes){if(e.kind==="named"&&t.kind==="word"&&(t.value[0]==="'"||t.value[0]==='"')&&t.value[t.value.length-1]===t.value[0]&&t.value.slice(1,-1)===e.value)return{nodes:G(e.value)};if(e.kind==="named"&&t.kind==="word"&&t.value[0]==="-"&&t.value[1]==="-"){let n=t.value;if(n.endsWith("-*")){n=n.slice(0,-2);let s=i.theme.resolve(e.value,[n]);if(s)return{nodes:G(s)}}else{let s=n.split("-*");if(s.length<=1)continue;let a=[s.shift()],p=i.theme.resolveWith(e.value,a,s);if(p){let[,u={}]=p;{let c=u[s.pop()];if(c)return{nodes:G(c)}}}}}else if(e.kind==="named"&&t.kind==="word"){if(!_t.includes(t.value))continue;let n=t.value==="ratio"&&"fraction"in e?e.fraction:e.value;if(!n)continue;let s=Z(n,[t.value]);if(s===null)continue;if(s==="ratio"){let[a,p]=U(n,"/");if(!T(a)||!T(p))continue}else{if(s==="number"&&!xe(n))continue;if(s==="percentage"&&!T(n.slice(0,-1)))continue}return{nodes:G(n),ratio:s==="ratio"}}else if(e.kind==="arbitrary"&&t.kind==="word"&&t.value[0]==="["&&t.value[t.value.length-1]==="]"){let n=t.value.slice(1,-1);if(n==="*")return{nodes:G(e.value)};if("dataType"in e&&e.dataType&&e.dataType!==n)continue;if("dataType"in e&&e.dataType)return{nodes:G(e.value)};if(Z(e.value,[n])!==null)return{nodes:G(e.value)}}}}function pe(e,r,i,t,n=""){let s=!1,a=Ue(r,u=>i==null?t(u):u.startsWith("current")?t(Q(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),t(Nr(u,i))));function p(u){return n?U(u,",").map(c=>n+c).join(","):u}return s?[o(e,p(Ue(r,t))),Y("@supports (color: lab(from red l a b))",[o(e,p(a))])]:[o(e,p(a))]}function lt(e,r,i,t,n=""){let s=!1,a=U(r,",").map(p=>Ue(p,u=>i==null?t(u):u.startsWith("current")?t(Q(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),t(Nr(u,i))))).map(p=>`drop-shadow(${p})`).join(" ");return s?[o(e,n+U(r,",").map(p=>`drop-shadow(${Ue(p,t)})`).join(" ")),Y("@supports (color: lab(from red l a b))",[o(e,n+a)])]:[o(e,n+a)]}var Dt={"--alpha":An,"--spacing":Cn,"--theme":$n,theme:Nn};function An(e,r,i,...t){let[n,s]=U(i,"/").map(a=>a.trim());if(!n||!s)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);if(t.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);return Q(n,s)}function Cn(e,r,i,...t){if(!i)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(t.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${t.length+1}.`);let n=e.theme.resolve(null,["--spacing"]);if(!n)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${n} * ${i})`}function $n(e,r,i,...t){if(!i.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let n=!1;i.endsWith(" inline")&&(n=!0,i=i.slice(0,-7)),r.kind==="at-rule"&&(n=!0);let s=e.resolveThemeValue(i,n);if(!s){if(t.length>0)return t.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(t.length===0)return s;let a=t.join(", ");if(a==="initial")return s;if(s==="initial")return a;if(s.startsWith("var(")||s.startsWith("theme(")||s.startsWith("--theme(")){let p=G(s);return Sn(p,a),J(p)}return s}function Nn(e,r,i,...t){i=Vn(i);let n=e.resolveThemeValue(i);if(!n&&t.length>0)return t.join(", ");if(!n)throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return n}var Tr=new RegExp(Object.keys(Dt).map(e=>`${e}\\(`).join("|"));function Se(e,r){let i=0;return L(e,t=>{if(t.kind==="declaration"&&t.value&&Tr.test(t.value)){i|=8,t.value=Rr(t.value,t,r);return}t.kind==="at-rule"&&(t.name==="@media"||t.name==="@custom-media"||t.name==="@container"||t.name==="@supports")&&Tr.test(t.params)&&(i|=8,t.params=Rr(t.params,t,r))}),i}function Rr(e,r,i){let t=G(e);return ee(t,(n,{replaceWith:s})=>{if(n.kind==="function"&&n.value in Dt){let a=U(J(n.nodes).trim(),",").map(u=>u.trim()),p=Dt[n.value](i,r,...a);return s(G(p))}}),J(t)}function Vn(e){if(e[0]!=="'"&&e[0]!=='"')return e;let r="",i=e[0];for(let t=1;t<e.length-1;t++){let n=e[t],s=e[t+1];n==="\\"&&(s===i||s==="\\")?(r+=s,t++):r+=n}return r}function Sn(e,r){ee(e,i=>{if(i.kind==="function"&&!(i.value!=="var"&&i.value!=="theme"&&i.value!=="--theme"))if(i.nodes.length===1)i.nodes.push({kind:"word",value:`, ${r}`});else{let t=i.nodes[i.nodes.length-1];t.kind==="word"&&t.value==="initial"&&(t.value=r)}})}function st(e,r){let i=e.length,t=r.length,n=i<t?i:t;for(let s=0;s<n;s++){let a=e.charCodeAt(s),p=r.charCodeAt(s);if(a>=48&&a<=57&&p>=48&&p<=57){let u=s,c=s+1,m=s,g=s+1;for(a=e.charCodeAt(c);a>=48&&a<=57;)a=e.charCodeAt(++c);for(p=r.charCodeAt(g);p>=48&&p<=57;)p=r.charCodeAt(++g);let d=e.slice(u,c),w=r.slice(m,g),v=Number(d)-Number(w);if(v)return v;if(d<w)return-1;if(d>w)return 1;continue}if(a!==p)return a-p}return e.length-r.length}var En=/^\d+\/\d+$/;function Pr(e){let r=new W(n=>({name:n,utility:n,fraction:!1,modifiers:[]}));for(let n of e.utilities.keys("static")){let s=r.get(n);s.fraction=!1,s.modifiers=[]}for(let n of e.utilities.keys("functional")){let s=e.utilities.getCompletions(n);for(let a of s)for(let p of a.values){let u=p!==null&&En.test(p),c=p===null?n:`${n}-${p}`,m=r.get(c);if(m.utility=n,m.fraction||=u,m.modifiers.push(...a.modifiers),a.supportsNegative){let g=r.get(`-${c}`);g.utility=`-${n}`,g.fraction||=u,g.modifiers.push(...a.modifiers)}m.modifiers=Array.from(new Set(m.modifiers))}}if(r.size===0)return[];let i=Array.from(r.values());return i.sort((n,s)=>st(n.name,s.name)),Tn(i)}function Tn(e){let r=[],i=null,t=new Map,n=new W(()=>[]);for(let a of e){let{utility:p,fraction:u}=a;i||(i={utility:p,items:[]},t.set(p,i)),p!==i.utility&&(r.push(i),i={utility:p,items:[]},t.set(p,i)),u?n.get(p).push(a):i.items.push(a)}i&&r[r.length-1]!==i&&r.push(i);for(let[a,p]of n){let u=t.get(a);u&&u.items.push(...p)}let s=[];for(let a of r)for(let p of a.items)s.push([p.name,{modifiers:p.modifiers}]);return s}function Or(e){let r=[];for(let[t,n]of e.variants.entries()){let p=function({value:u,modifier:c}={}){let m=t;u&&(m+=s?`-${u}`:u),c&&(m+=`/${c}`);let g=e.parseVariant(m);if(!g)return[];let d=B(".__placeholder__",[]);if(Ee(d,g,e.variants)===null)return[];let w=[];return Xe(d.nodes,(v,{path:k})=>{if(v.kind!=="rule"&&v.kind!=="at-rule"||v.nodes.length>0)return;k.sort((C,b)=>{let _=C.kind==="at-rule",R=b.kind==="at-rule";return _&&!R?-1:!_&&R?1:0});let x=k.flatMap(C=>C.kind==="rule"?C.selector==="&"?[]:[C.selector]:C.kind==="at-rule"?[`${C.name} ${C.params}`]:[]),S="";for(let C=x.length-1;C>=0;C--)S=S===""?x[C]:`${x[C]} { ${S} }`;w.push(S)}),w};var i=p;if(n.kind==="arbitrary")continue;let s=t!=="@",a=e.variants.getCompletions(t);switch(n.kind){case"static":{r.push({name:t,values:a,isArbitrary:!1,hasDash:s,selectors:p});break}case"functional":{r.push({name:t,values:a,isArbitrary:!0,hasDash:s,selectors:p});break}case"compound":{r.push({name:t,values:a,isArbitrary:!0,hasDash:s,selectors:p});break}}}return r}function _r(e,r){let{astNodes:i,nodeSorting:t}=ge(Array.from(r),e),n=new Map(r.map(a=>[a,null])),s=0n;for(let a of i){let p=t.get(a)?.candidate;p&&n.set(p,n.get(p)??s++)}return r.map(a=>[a,n.get(a)??null])}var ut=/^@?[a-z0-9][a-zA-Z0-9_-]*(?<![_-])$/;var Kt=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(r,i,{compounds:t,order:n}={}){this.set(r,{kind:"static",applyFn:i,compoundsWith:0,compounds:t??2,order:n})}fromAst(r,i,t){let n=[],s=!1;L(i,a=>{a.kind==="rule"?n.push(a.selector):a.kind==="at-rule"&&a.name==="@variant"?s=!0:a.kind==="at-rule"&&a.name!=="@slot"&&n.push(`${a.name} ${a.params}`)}),this.static(r,a=>{let p=structuredClone(i);s&&Lt(p,t),Ut(p,a.nodes),a.nodes=p},{compounds:Ce(n)})}functional(r,i,{compounds:t,order:n}={}){this.set(r,{kind:"functional",applyFn:i,compoundsWith:0,compounds:t??2,order:n})}compound(r,i,t,{compounds:n,order:s}={}){this.set(r,{kind:"compound",applyFn:t,compoundsWith:i,compounds:n??2,order:s})}group(r,i){this.groupOrder=this.nextOrder(),i&&this.compareFns.set(this.groupOrder,i),r(),this.groupOrder=null}has(r){return this.variants.has(r)}get(r){return this.variants.get(r)}kind(r){return this.variants.get(r)?.kind}compoundsWith(r,i){let t=this.variants.get(r),n=typeof i=="string"?this.variants.get(i):i.kind==="arbitrary"?{compounds:Ce([i.selector])}:this.variants.get(i.root);return!(!t||!n||t.kind!=="compound"||n.compounds===0||t.compoundsWith===0||(t.compoundsWith&n.compounds)===0)}suggest(r,i){this.completions.set(r,i)}getCompletions(r){return this.completions.get(r)?.()??[]}compare(r,i){if(r===i)return 0;if(r===null)return-1;if(i===null)return 1;if(r.kind==="arbitrary"&&i.kind==="arbitrary")return r.selector<i.selector?-1:1;if(r.kind==="arbitrary")return 1;if(i.kind==="arbitrary")return-1;let t=this.variants.get(r.root).order,n=this.variants.get(i.root).order,s=t-n;if(s!==0)return s;if(r.kind==="compound"&&i.kind==="compound"){let c=this.compare(r.variant,i.variant);return c!==0?c:r.modifier&&i.modifier?r.modifier.value<i.modifier.value?-1:1:r.modifier?1:i.modifier?-1:0}let a=this.compareFns.get(t);if(a!==void 0)return a(r,i);if(r.root!==i.root)return r.root<i.root?-1:1;let p=r.value,u=i.value;return p===null?-1:u===null||p.kind==="arbitrary"&&u.kind!=="arbitrary"?1:p.kind!=="arbitrary"&&u.kind==="arbitrary"||p.value<u.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(r,{kind:i,applyFn:t,compounds:n,compoundsWith:s,order:a}){let p=this.variants.get(r);p?Object.assign(p,{kind:i,applyFn:t,compounds:n}):(a===void 0&&(this.lastOrder=this.nextOrder(),a=this.lastOrder),this.variants.set(r,{kind:i,applyFn:t,order:a,compoundsWith:s,compounds:n}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function Ce(e){let r=0;for(let i of e){if(i[0]==="@"){if(!i.startsWith("@media")&&!i.startsWith("@supports")&&!i.startsWith("@container"))return 0;r|=1;continue}if(i.includes("::"))return 0;r|=2}return r}function Kr(e){let r=new Kt;function i(c,m,{compounds:g}={}){g=g??Ce(m),r.static(c,d=>{d.nodes=m.map(w=>Y(w,d.nodes))},{compounds:g})}i("*",[":is(& > *)"],{compounds:0}),i("**",[":is(& *)"],{compounds:0});function t(c,m){return m.map(g=>{g=g.trim();let d=U(g," ");return d[0]==="not"?d.slice(1).join(" "):c==="@container"?d[0][0]==="("?`not ${g}`:d[1]==="not"?`${d[0]} ${d.slice(2).join(" ")}`:`${d[0]} not ${d.slice(1).join(" ")}`:`not ${g}`})}let n=["@media","@supports","@container"];function s(c){for(let m of n){if(m!==c.name)continue;let g=U(c.params,",");return g.length>1?null:(g=t(c.name,g),F(c.name,g.join(", ")))}return null}function a(c){return c.includes("::")?null:`&:not(${U(c,",").map(g=>(g=g.replaceAll("&","*"),g)).join(", ")})`}r.compound("not",3,(c,m)=>{if(m.variant.kind==="arbitrary"&&m.variant.relative||m.modifier)return null;let g=!1;if(L([c],(d,{path:w})=>{if(d.kind!=="rule"&&d.kind!=="at-rule")return 0;if(d.nodes.length>0)return 0;let v=[],k=[];for(let S of w)S.kind==="at-rule"?v.push(S):S.kind==="rule"&&k.push(S);if(v.length>1)return 2;if(k.length>1)return 2;let x=[];for(let S of k){let C=a(S.selector);if(!C)return g=!1,2;x.push(B(C,[]))}for(let S of v){let C=s(S);if(!C)return g=!1,2;x.push(C)}return Object.assign(c,B("&",x)),g=!0,1}),c.kind==="rule"&&c.selector==="&"&&c.nodes.length===1&&Object.assign(c,c.nodes[0]),!g)return null}),r.suggest("not",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("not",c))),r.compound("group",2,(c,m)=>{if(m.variant.kind==="arbitrary"&&m.variant.relative)return null;let g=m.modifier?`:where(.${e.prefix?`${e.prefix}\\:`:""}group\\/${m.modifier.value})`:`:where(.${e.prefix?`${e.prefix}\\:`:""}group)`,d=!1;if(L([c],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let x of v.slice(0,-1))if(x.kind==="rule")return d=!1,2;let k=w.selector.replaceAll("&",g);U(k,",").length>1&&(k=`:is(${k})`),w.selector=`&:is(${k} *)`,d=!0}),!d)return null}),r.suggest("group",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("group",c))),r.compound("peer",2,(c,m)=>{if(m.variant.kind==="arbitrary"&&m.variant.relative)return null;let g=m.modifier?`:where(.${e.prefix?`${e.prefix}\\:`:""}peer\\/${m.modifier.value})`:`:where(.${e.prefix?`${e.prefix}\\:`:""}peer)`,d=!1;if(L([c],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let x of v.slice(0,-1))if(x.kind==="rule")return d=!1,2;let k=w.selector.replaceAll("&",g);U(k,",").length>1&&(k=`:is(${k})`),w.selector=`&:is(${k} ~ *)`,d=!0}),!d)return null}),r.suggest("peer",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("peer",c))),i("first-letter",["&::first-letter"]),i("first-line",["&::first-line"]),i("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),i("selection",["& *::selection","&::selection"]),i("file",["&::file-selector-button"]),i("placeholder",["&::placeholder"]),i("backdrop",["&::backdrop"]),i("details-content",["&::details-content"]);{let c=function(){return z([F("@property","--tw-content",[o("syntax",'"*"'),o("initial-value",'""'),o("inherits","false")])])};var p=c;r.static("before",m=>{m.nodes=[B("&::before",[c(),o("content","var(--tw-content)"),...m.nodes])]},{compounds:0}),r.static("after",m=>{m.nodes=[B("&::after",[c(),o("content","var(--tw-content)"),...m.nodes])]},{compounds:0})}i("first",["&:first-child"]),i("last",["&:last-child"]),i("only",["&:only-child"]),i("odd",["&:nth-child(odd)"]),i("even",["&:nth-child(even)"]),i("first-of-type",["&:first-of-type"]),i("last-of-type",["&:last-of-type"]),i("only-of-type",["&:only-of-type"]),i("visited",["&:visited"]),i("target",["&:target"]),i("open",["&:is([open], :popover-open, :open)"]),i("default",["&:default"]),i("checked",["&:checked"]),i("indeterminate",["&:indeterminate"]),i("placeholder-shown",["&:placeholder-shown"]),i("autofill",["&:autofill"]),i("optional",["&:optional"]),i("required",["&:required"]),i("valid",["&:valid"]),i("invalid",["&:invalid"]),i("user-valid",["&:user-valid"]),i("user-invalid",["&:user-invalid"]),i("in-range",["&:in-range"]),i("out-of-range",["&:out-of-range"]),i("read-only",["&:read-only"]),i("empty",["&:empty"]),i("focus-within",["&:focus-within"]),r.static("hover",c=>{c.nodes=[B("&:hover",[F("@media","(hover: hover)",c.nodes)])]}),i("focus",["&:focus"]),i("focus-visible",["&:focus-visible"]),i("active",["&:active"]),i("enabled",["&:enabled"]),i("disabled",["&:disabled"]),i("inert",["&:is([inert], [inert] *)"]),r.compound("in",2,(c,m)=>{if(m.modifier)return null;let g=!1;if(L([c],(d,{path:w})=>{if(d.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return g=!1,2;d.selector=`:where(${d.selector.replaceAll("&","*")}) &`,g=!0}),!g)return null}),r.suggest("in",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("in",c))),r.compound("has",2,(c,m)=>{if(m.modifier)return null;let g=!1;if(L([c],(d,{path:w})=>{if(d.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return g=!1,2;d.selector=`&:has(${d.selector.replaceAll("&","*")})`,g=!0}),!g)return null}),r.suggest("has",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("has",c))),r.functional("aria",(c,m)=>{if(!m.value||m.modifier)return null;m.value.kind==="arbitrary"?c.nodes=[B(`&[aria-${Dr(m.value.value)}]`,c.nodes)]:c.nodes=[B(`&[aria-${m.value.value}="true"]`,c.nodes)]}),r.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),r.functional("data",(c,m)=>{if(!m.value||m.modifier)return null;c.nodes=[B(`&[data-${Dr(m.value.value)}]`,c.nodes)]}),r.functional("nth",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!T(m.value.value))return null;c.nodes=[B(`&:nth-child(${m.value.value})`,c.nodes)]}),r.functional("nth-last",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!T(m.value.value))return null;c.nodes=[B(`&:nth-last-child(${m.value.value})`,c.nodes)]}),r.functional("nth-of-type",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!T(m.value.value))return null;c.nodes=[B(`&:nth-of-type(${m.value.value})`,c.nodes)]}),r.functional("nth-last-of-type",(c,m)=>{if(!m.value||m.modifier||m.value.kind==="named"&&!T(m.value.value))return null;c.nodes=[B(`&:nth-last-of-type(${m.value.value})`,c.nodes)]}),r.functional("supports",(c,m)=>{if(!m.value||m.modifier)return null;let g=m.value.value;if(g===null)return null;if(/^[\w-]*\s*\(/.test(g)){let d=g.replace(/\b(and|or|not)\b/g," $1 ");c.nodes=[F("@supports",d,c.nodes)];return}g.includes(":")||(g=`${g}: var(--tw)`),(g[0]!=="("||g[g.length-1]!==")")&&(g=`(${g})`),c.nodes=[F("@supports",g,c.nodes)]},{compounds:1}),i("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),i("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),i("contrast-more",["@media (prefers-contrast: more)"]),i("contrast-less",["@media (prefers-contrast: less)"]);{let c=function(m,g,d,w){if(m===g)return 0;let v=w.get(m);if(v===null)return d==="asc"?-1:1;let k=w.get(g);return k===null?d==="asc"?1:-1:be(v,k,d)};var u=c;{let m=e.namespace("--breakpoint"),g=new W(d=>{switch(d.kind){case"static":return e.resolveValue(d.root,["--breakpoint"])??null;case"functional":{if(!d.value||d.modifier)return null;let w=null;return d.value.kind==="arbitrary"?w=d.value.value:d.value.kind==="named"&&(w=e.resolveValue(d.value.value,["--breakpoint"])),!w||w.includes("var(")?null:w}case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("max",(d,w)=>{if(w.modifier)return null;let v=g.get(w);if(v===null)return null;d.nodes=[F("@media",`(width < ${v})`,d.nodes)]},{compounds:1})},(d,w)=>c(d,w,"desc",g)),r.suggest("max",()=>Array.from(m.keys()).filter(d=>d!==null)),r.group(()=>{for(let[d,w]of e.namespace("--breakpoint"))d!==null&&r.static(d,v=>{v.nodes=[F("@media",`(width >= ${w})`,v.nodes)]},{compounds:1});r.functional("min",(d,w)=>{if(w.modifier)return null;let v=g.get(w);if(v===null)return null;d.nodes=[F("@media",`(width >= ${v})`,d.nodes)]},{compounds:1})},(d,w)=>c(d,w,"asc",g)),r.suggest("min",()=>Array.from(m.keys()).filter(d=>d!==null))}{let m=e.namespace("--container"),g=new W(d=>{switch(d.kind){case"functional":{if(d.value===null)return null;let w=null;return d.value.kind==="arbitrary"?w=d.value.value:d.value.kind==="named"&&(w=e.resolveValue(d.value.value,["--container"])),!w||w.includes("var(")?null:w}case"static":case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("@max",(d,w)=>{let v=g.get(w);if(v===null)return null;d.nodes=[F("@container",w.modifier?`${w.modifier.value} (width < ${v})`:`(width < ${v})`,d.nodes)]},{compounds:1})},(d,w)=>c(d,w,"desc",g)),r.suggest("@max",()=>Array.from(m.keys()).filter(d=>d!==null)),r.group(()=>{r.functional("@",(d,w)=>{let v=g.get(w);if(v===null)return null;d.nodes=[F("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,d.nodes)]},{compounds:1}),r.functional("@min",(d,w)=>{let v=g.get(w);if(v===null)return null;d.nodes=[F("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,d.nodes)]},{compounds:1})},(d,w)=>c(d,w,"asc",g)),r.suggest("@min",()=>Array.from(m.keys()).filter(d=>d!==null)),r.suggest("@",()=>Array.from(m.keys()).filter(d=>d!==null))}}return i("portrait",["@media (orientation: portrait)"]),i("landscape",["@media (orientation: landscape)"]),i("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),i("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),i("dark",["@media (prefers-color-scheme: dark)"]),i("starting",["@starting-style"]),i("print",["@media print"]),i("forced-colors",["@media (forced-colors: active)"]),i("inverted-colors",["@media (inverted-colors: inverted)"]),i("pointer-none",["@media (pointer: none)"]),i("pointer-coarse",["@media (pointer: coarse)"]),i("pointer-fine",["@media (pointer: fine)"]),i("any-pointer-none",["@media (any-pointer: none)"]),i("any-pointer-coarse",["@media (any-pointer: coarse)"]),i("any-pointer-fine",["@media (any-pointer: fine)"]),i("noscript",["@media (scripting: none)"]),r}function Dr(e){if(e.includes("=")){let[r,...i]=U(e,"="),t=i.join("=").trim();if(t[0]==="'"||t[0]==='"')return e;if(t.length>1){let n=t[t.length-1];if(t[t.length-2]===" "&&(n==="i"||n==="I"||n==="s"||n==="S"))return`${r}="${t.slice(0,-2)}" ${n}`}return`${r}="${t}"`}return e}function Ut(e,r){L(e,(i,{replaceWith:t})=>{if(i.kind==="at-rule"&&i.name==="@slot")t(r);else if(i.kind==="at-rule"&&(i.name==="@keyframes"||i.name==="@property"))return Object.assign(i,z([F(i.name,i.params,i.nodes)])),1})}function Lt(e,r){let i=0;return L(e,(t,{replaceWith:n})=>{if(t.kind!=="at-rule"||t.name!=="@variant")return;let s=B("&",t.nodes),a=t.params,p=r.parseVariant(a);if(p===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${a}`);if(Ee(s,p,r.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${a}`);n(s),i|=32}),i}function Ur(e){let r=Sr(e),i=Kr(e),t=new W(u=>hr(u,p)),n=new W(u=>Array.from(gr(u,p))),s=new W(u=>new W(c=>{let m=Lr(c,p,u);try{Se(m.map(({node:g})=>g),p)}catch{return[]}return m})),a=new W(u=>{for(let c of Qe(u))e.markUsedVariable(c)}),p={theme:e,utilities:r,variants:i,invalidCandidates:new Set,important:!1,candidatesToCss(u){let c=[];for(let m of u){let g=!1,{astNodes:d}=ge([m],this,{onInvalidCandidate(){g=!0}});d=ye(d,p,0),d.length===0||g?c.push(null):c.push(ne(d))}return c},getClassOrder(u){return _r(this,u)},getClassList(){return Pr(this)},getVariants(){return Or(this)},parseCandidate(u){return n.get(u)},parseVariant(u){return t.get(u)},compileAstNodes(u,c=1){return s.get(c).get(u)},printCandidate(u){return wr(p,u)},printVariant(u){return it(u)},getVariantOrder(){let u=Array.from(t.values());u.sort((d,w)=>this.variants.compare(d,w));let c=new Map,m,g=0;for(let d of u)d!==null&&(m!==void 0&&this.variants.compare(m,d)!==0&&g++,c.set(d,g),m=d);return c},resolveThemeValue(u,c=!0){let m=u.lastIndexOf("/"),g=null;m!==-1&&(g=u.slice(m+1).trim(),u=u.slice(0,m).trim());let d=e.resolve(null,[u],c?1:0)??void 0;return g&&d?Q(d,g):d},trackUsedVariables(u){a.get(u)}};return p}var It=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","mask-image","--tw-mask-top","--tw-mask-top-from-color","--tw-mask-top-from-position","--tw-mask-top-to-color","--tw-mask-top-to-position","--tw-mask-right","--tw-mask-right-from-color","--tw-mask-right-from-position","--tw-mask-right-to-color","--tw-mask-right-to-position","--tw-mask-bottom","--tw-mask-bottom-from-color","--tw-mask-bottom-from-position","--tw-mask-bottom-to-color","--tw-mask-bottom-to-position","--tw-mask-left","--tw-mask-left-from-color","--tw-mask-left-from-position","--tw-mask-left-to-color","--tw-mask-left-to-position","--tw-mask-linear","--tw-mask-linear-position","--tw-mask-linear-from-color","--tw-mask-linear-from-position","--tw-mask-linear-to-color","--tw-mask-linear-to-position","--tw-mask-radial","--tw-mask-radial-shape","--tw-mask-radial-size","--tw-mask-radial-position","--tw-mask-radial-from-color","--tw-mask-radial-from-position","--tw-mask-radial-to-color","--tw-mask-radial-to-position","--tw-mask-conic","--tw-mask-conic-position","--tw-mask-conic-from-color","--tw-mask-conic-from-position","--tw-mask-conic-to-color","--tw-mask-conic-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","mask-composite","mask-mode","mask-type","mask-size","mask-clip","mask-position","mask-repeat","mask-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function ge(e,r,{onInvalidCandidate:i,respectImportant:t}={}){let n=new Map,s=[],a=new Map;for(let c of e){if(r.invalidCandidates.has(c)){i?.(c);continue}let m=r.parseCandidate(c);if(m.length===0){i?.(c);continue}a.set(c,m)}let p=0;(t??!0)&&(p|=1);let u=r.getVariantOrder();for(let[c,m]of a){let g=!1;for(let d of m){let w=r.compileAstNodes(d,p);if(w.length!==0){g=!0;for(let{node:v,propertySort:k}of w){let x=0n;for(let S of d.variants)x|=1n<<BigInt(u.get(S));n.set(v,{properties:k,variants:x,candidate:c}),s.push(v)}}}g||i?.(c)}return s.sort((c,m)=>{let g=n.get(c),d=n.get(m);if(g.variants-d.variants!==0n)return Number(g.variants-d.variants);let w=0;for(;w<g.properties.order.length&&w<d.properties.order.length&&g.properties.order[w]===d.properties.order[w];)w+=1;return(g.properties.order[w]??1/0)-(d.properties.order[w]??1/0)||d.properties.count-g.properties.count||st(g.candidate,d.candidate)}),{astNodes:s,nodeSorting:n}}function Lr(e,r,i){let t=Rn(e,r);if(t.length===0)return[];let n=r.important&&!!(i&1),s=[],a=`.${de(e.raw)}`;for(let p of t){let u=Pn(p);(e.important||n)&&jr(p);let c={kind:"rule",selector:a,nodes:p};for(let m of e.variants)if(Ee(c,m,r.variants)===null)return[];s.push({node:c,propertySort:u})}return s}function Ee(e,r,i,t=0){if(r.kind==="arbitrary"){if(r.relative&&t===0)return null;e.nodes=[Y(r.selector,e.nodes)];return}let{applyFn:n}=i.get(r.root);if(r.kind==="compound"){let a=F("@slot");if(Ee(a,r.variant,i,t+1)===null||r.root==="not"&&a.nodes.length>1)return null;for(let u of a.nodes)if(u.kind!=="rule"&&u.kind!=="at-rule"||n(u,r)===null)return null;L(a.nodes,u=>{if((u.kind==="rule"||u.kind==="at-rule")&&u.nodes.length<=0)return u.nodes=e.nodes,1}),e.nodes=a.nodes;return}if(n(e,r)===null)return null}function Ir(e){let r=e.options?.types??[];return r.length>1&&r.includes("any")}function Rn(e,r){if(e.kind==="arbitrary"){let a=e.value;return e.modifier&&(a=X(a,e.modifier,r.theme)),a===null?[]:[[o(e.property,a)]]}let i=r.utilities.get(e.root)??[],t=[],n=i.filter(a=>!Ir(a));for(let a of n){if(a.kind!==e.kind)continue;let p=a.compileFn(e);if(p!==void 0){if(p===null)return t;t.push(p)}}if(t.length>0)return t;let s=i.filter(a=>Ir(a));for(let a of s){if(a.kind!==e.kind)continue;let p=a.compileFn(e);if(p!==void 0){if(p===null)return t;t.push(p)}}return t}function jr(e){for(let r of e)r.kind!=="at-root"&&(r.kind==="declaration"?r.important=!0:(r.kind==="rule"||r.kind==="at-rule")&&jr(r.nodes))}function Pn(e){let r=new Set,i=0,t=e.slice(),n=!1;for(;t.length>0;){let s=t.shift();if(s.kind==="declaration"){if(s.value===void 0||(i++,n))continue;if(s.property==="--tw-sort"){let p=It.indexOf(s.value??"");if(p!==-1){r.add(p),n=!0;continue}}let a=It.indexOf(s.property);a!==-1&&r.add(a)}else if(s.kind==="rule"||s.kind==="at-rule")for(let a of s.nodes)t.push(a)}return{order:Array.from(r).sort((s,a)=>s-a),count:i}}function Ie(e,r){let i=0,t=Y("&",e),n=new Set,s=new W(()=>new Set),a=new W(()=>new Set);L([t],(g,{parent:d,path:w})=>{if(g.kind==="at-rule"){if(g.name==="@keyframes")return L(g.nodes,v=>{if(v.kind==="at-rule"&&v.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(g.name==="@utility"){let v=g.params.replace(/-\*$/,"");a.get(v).add(g),L(g.nodes,k=>{if(!(k.kind!=="at-rule"||k.name!=="@apply")){n.add(g);for(let x of zr(k,r))s.get(g).add(x)}});return}if(g.name==="@apply"){if(d===null)return;i|=1,n.add(d);for(let v of zr(g,r))for(let k of w)k!==g&&n.has(k)&&s.get(k).add(v)}}});let p=new Set,u=[],c=new Set;function m(g,d=[]){if(!p.has(g)){if(c.has(g)){let w=d[(d.indexOf(g)+1)%d.length];throw g.kind==="at-rule"&&g.name==="@utility"&&w.kind==="at-rule"&&w.name==="@utility"&&L(g.nodes,v=>{if(v.kind!=="at-rule"||v.name!=="@apply")return;let k=v.params.split(/\s+/g);for(let x of k)for(let S of r.parseCandidate(x))switch(S.kind){case"arbitrary":break;case"static":case"functional":if(w.params.replace(/-\*$/,"")===S.root)throw new Error(`You cannot \`@apply\` the \`${x}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${ne([g])}
Relies on:

${ne([w])}`)}c.add(g);for(let w of s.get(g))for(let v of a.get(w))d.push(g),m(v,d),d.pop();p.add(g),c.delete(g),u.push(g)}}for(let g of n)m(g);for(let g of u)"nodes"in g&&L(g.nodes,(d,{replaceWith:w})=>{if(d.kind!=="at-rule"||d.name!=="@apply")return;let v=d.params.split(/(\s+)/g),k={},x=0;for(let[S,C]of v.entries())S%2===0&&(k[C]=x),x+=C.length;{let S=Object.keys(k),C=ge(S,r,{respectImportant:!1,onInvalidCandidate:D=>{if(r.theme.prefix&&!D.startsWith(r.theme.prefix))throw new Error(`Cannot apply unprefixed utility class \`${D}\`. Did you mean \`${r.theme.prefix}:${D}\`?`);if(r.invalidCandidates.has(D))throw new Error(`Cannot apply utility class \`${D}\` because it has been explicitly disabled: https://tailwindcss.com/docs/detecting-classes-in-source-files#explicitly-excluding-classes`);let K=U(D,":");if(K.length>1){let H=K.pop();if(r.candidatesToCss([H])[0]){let O=r.candidatesToCss(K.map(q=>`${q}:[--tw-variant-check:1]`)),I=K.filter((q,M)=>O[M]===null);if(I.length>0){if(I.length===1)throw new Error(`Cannot apply utility class \`${D}\` because the ${I.map(q=>`\`${q}\``)} variant does not exist.`);{let q=new Intl.ListFormat("en",{style:"long",type:"conjunction"});throw new Error(`Cannot apply utility class \`${D}\` because the ${q.format(I.map(M=>`\`${M}\``))} variants do not exist.`)}}}}throw r.theme.size===0?new Error(`Cannot apply unknown utility class \`${D}\`. Are you using CSS modules or similar and missing \`@reference\`? https://tailwindcss.com/docs/functions-and-directives#reference-directive`):new Error(`Cannot apply unknown utility class \`${D}\``)}}),b=d.src,_=C.astNodes.map(D=>{let K=C.nodeSorting.get(D)?.candidate,H=K?k[K]:void 0;if(D=structuredClone(D),!b||!K||H===void 0)return L([D],I=>{I.src=b}),D;let O=[b[0],b[1],b[2]];return O[1]+=7+H,O[2]=O[1]+K.length,L([D],I=>{I.src=O}),D}),R=[];for(let D of _)if(D.kind==="rule")for(let K of D.nodes)R.push(K);else R.push(D);w(R)}});return i}function*zr(e,r){for(let i of e.params.split(/\s+/g))for(let t of r.parseCandidate(i))switch(t.kind){case"arbitrary":break;case"static":case"functional":yield t.root;break;default:}}async function jt(e,r,i,t=0,n=!1){let s=0,a=[];return L(e,(p,{replaceWith:u})=>{if(p.kind==="at-rule"&&(p.name==="@import"||p.name==="@reference")){let c=On(G(p.params));if(c===null)return;p.name==="@reference"&&(c.media="reference"),s|=2;let{uri:m,layer:g,media:d,supports:w}=c;if(m.startsWith("data:")||m.startsWith("http://")||m.startsWith("https://"))return;let v=se({},[]);return a.push((async()=>{if(t>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${m}\` in \`${r}\`)`);let k=await i(m,r),x=ve(k.content,{from:n?k.path:void 0});await jt(x,k.base,i,t+1,n),v.nodes=_n(p,[se({base:k.base},x)],g,d,w)})()),u(v),1}}),a.length>0&&await Promise.all(a),s}function On(e){let r,i=null,t=null,n=null;for(let s=0;s<e.length;s++){let a=e[s];if(a.kind!=="separator"){if(a.kind==="word"&&!r){if(!a.value||a.value[0]!=='"'&&a.value[0]!=="'")return null;r=a.value.slice(1,-1);continue}if(a.kind==="function"&&a.value.toLowerCase()==="url"||!r)return null;if((a.kind==="word"||a.kind==="function")&&a.value.toLowerCase()==="layer"){if(i)return null;if(n)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in a?i=J(a.nodes):i="";continue}if(a.kind==="function"&&a.value.toLowerCase()==="supports"){if(n)return null;n=J(a.nodes);continue}t=J(e.slice(s));break}}return r?{uri:r,layer:i,media:t,supports:n}:null}function _n(e,r,i,t,n){let s=r;if(i!==null){let a=F("@layer",i,s);a.src=e.src,s=[a]}if(t!==null){let a=F("@media",t,s);a.src=e.src,s=[a]}if(n!==null){let a=F("@supports",n[0]==="("?n:`(${n})`,s);a.src=e.src,s=[a]}return s}function Te(e,r=null){return Array.isArray(e)&&e.length===2&&typeof e[1]=="object"&&typeof e[1]!==null?r?e[1][r]??null:e[0]:Array.isArray(e)&&r===null?e.join(", "):typeof e=="string"&&r===null?e:null}function Fr(e,{theme:r},i){for(let t of i){let n=ct([t]);n&&e.theme.clearNamespace(`--${n}`,4)}for(let[t,n]of Dn(r)){if(typeof n!="string"&&typeof n!="number")continue;if(typeof n=="string"&&(n=n.replace(/<alpha-value>/g,"1")),t[0]==="opacity"&&(typeof n=="number"||typeof n=="string")){let a=typeof n=="string"?parseFloat(n):n;a>=0&&a<=1&&(n=a*100+"%")}let s=ct(t);s&&e.theme.add(`--${s}`,""+n,7)}if(Object.hasOwn(r,"fontFamily")){let t=5;{let n=Te(r.fontFamily.sans);n&&e.theme.hasDefault("--font-sans")&&(e.theme.add("--default-font-family",n,t),e.theme.add("--default-font-feature-settings",Te(r.fontFamily.sans,"fontFeatureSettings")??"normal",t),e.theme.add("--default-font-variation-settings",Te(r.fontFamily.sans,"fontVariationSettings")??"normal",t))}{let n=Te(r.fontFamily.mono);n&&e.theme.hasDefault("--font-mono")&&(e.theme.add("--default-mono-font-family",n,t),e.theme.add("--default-mono-font-feature-settings",Te(r.fontFamily.mono,"fontFeatureSettings")??"normal",t),e.theme.add("--default-mono-font-variation-settings",Te(r.fontFamily.mono,"fontVariationSettings")??"normal",t))}}return r}function Dn(e){let r=[];return Mr(e,[],(i,t)=>{if(Un(i))return r.push([t,i]),1;if(Ln(i)){r.push([t,i[0]]);for(let n of Reflect.ownKeys(i[1]))r.push([[...t,`-${n}`],i[1][n]]);return 1}if(Array.isArray(i)&&i.every(n=>typeof n=="string"))return t[0]==="fontSize"?(r.push([t,i[0]]),i.length>=2&&r.push([[...t,"-line-height"],i[1]])):r.push([t,i.join(", ")]),1}),r}var Kn=/^[a-zA-Z0-9-_%/\.]+$/;function ct(e){if(e[0]==="container")return null;e=structuredClone(e),e[0]==="animation"&&(e[0]="animate"),e[0]==="aspectRatio"&&(e[0]="aspect"),e[0]==="borderRadius"&&(e[0]="radius"),e[0]==="boxShadow"&&(e[0]="shadow"),e[0]==="colors"&&(e[0]="color"),e[0]==="containers"&&(e[0]="container"),e[0]==="fontFamily"&&(e[0]="font"),e[0]==="fontSize"&&(e[0]="text"),e[0]==="letterSpacing"&&(e[0]="tracking"),e[0]==="lineHeight"&&(e[0]="leading"),e[0]==="maxWidth"&&(e[0]="container"),e[0]==="screens"&&(e[0]="breakpoint"),e[0]==="transitionTimingFunction"&&(e[0]="ease");for(let r of e)if(!Kn.test(r))return null;return e.map((r,i,t)=>r==="1"&&i!==t.length-1?"":r).map(r=>r.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(i,t,n)=>`${t}-${n.toLowerCase()}`)).filter((r,i)=>r!=="DEFAULT"||i!==e.length-1).join("-")}function Un(e){return typeof e=="number"||typeof e=="string"}function Ln(e){if(!Array.isArray(e)||e.length!==2||typeof e[0]!="string"&&typeof e[0]!="number"||e[1]===void 0||e[1]===null||typeof e[1]!="object")return!1;for(let r of Reflect.ownKeys(e[1]))if(typeof r!="string"||typeof e[1][r]!="string"&&typeof e[1][r]!="number")return!1;return!0}function Mr(e,r=[],i){for(let t of Reflect.ownKeys(e)){let n=e[t];if(n==null)continue;let s=[...r,t],a=i(n,s)??0;if(a!==1){if(a===2)return 2;if(!(!Array.isArray(n)&&typeof n!="object")&&Mr(n,s,i)===2)return 2}}}function ft(e){let r=[];for(let i of U(e,".")){if(!i.includes("[")){r.push(i);continue}let t=0;for(;;){let n=i.indexOf("[",t),s=i.indexOf("]",n);if(n===-1||s===-1)break;n>t&&r.push(i.slice(t,n)),r.push(i.slice(n+1,s)),t=s+1}t<=i.length-1&&r.push(i.slice(t))}return r}function Re(e){if(Object.prototype.toString.call(e)!=="[object Object]")return!1;let r=Object.getPrototypeOf(e);return r===null||Object.getPrototypeOf(r)===null}function je(e,r,i,t=[]){for(let n of r)if(n!=null)for(let s of Reflect.ownKeys(n)){t.push(s);let a=i(e[s],n[s],t);a!==void 0?e[s]=a:!Re(e[s])||!Re(n[s])?e[s]=n[s]:e[s]=je({},[e[s],n[s]],i,t),t.pop()}return e}function pt(e,r,i){return function(n,s){let a=n.lastIndexOf("/"),p=null;a!==-1&&(p=n.slice(a+1).trim(),n=n.slice(0,a).trim());let u=(()=>{let c=ft(n),[m,g]=In(e.theme,c),d=i(Wr(r()??{},c)??null);if(typeof d=="string"&&(d=d.replace("<alpha-value>","1")),typeof m!="object")return typeof g!="object"&&g&4?d??m:m;if(d!==null&&typeof d=="object"&&!Array.isArray(d)){let w=je({},[d],(v,k)=>k);if(m===null&&Object.hasOwn(d,"__CSS_VALUES__")){let v={};for(let k in d.__CSS_VALUES__)v[k]=d[k],delete w[k];m=v}for(let v in m)v!=="__CSS_VALUES__"&&(d?.__CSS_VALUES__?.[v]&4&&Wr(w,v.split("-"))!==void 0||(w[we(v)]=m[v]));return w}if(Array.isArray(m)&&Array.isArray(g)&&Array.isArray(d)){let w=m[0],v=m[1];g[0]&4&&(w=d[0]??w);for(let k of Object.keys(v))g[1][k]&4&&(v[k]=d[1][k]??v[k]);return[w,v]}return m??d})();return p&&typeof u=="string"&&(u=Q(u,p)),u??s}}function In(e,r){if(r.length===1&&r[0].startsWith("--"))return[e.get([r[0]]),e.getOptions(r[0])];let i=ct(r),t=new Map,n=new W(()=>new Map),s=e.namespace(`--${i}`);if(s.size===0)return[null,0];let a=new Map;for(let[m,g]of s){if(!m||!m.includes("--")){t.set(m,g),a.set(m,e.getOptions(m?`--${i}-${m}`:`--${i}`));continue}let d=m.indexOf("--"),w=m.slice(0,d),v=m.slice(d+2);v=v.replace(/-([a-z])/g,(k,x)=>x.toUpperCase()),n.get(w===""?null:w).set(v,[g,e.getOptions(`--${i}${m}`)])}let p=e.getOptions(`--${i}`);for(let[m,g]of n){let d=t.get(m);if(typeof d!="string")continue;let w={},v={};for(let[k,[x,S]]of g)w[k]=x,v[k]=S;t.set(m,[d,w]),a.set(m,[p,v])}let u={},c={};for(let[m,g]of t)Br(u,[m??"DEFAULT"],g);for(let[m,g]of a)Br(c,[m??"DEFAULT"],g);return r[r.length-1]==="DEFAULT"?[u?.DEFAULT??null,c.DEFAULT??0]:"DEFAULT"in u&&Object.keys(u).length===1?[u.DEFAULT,c.DEFAULT??0]:(u.__CSS_VALUES__=c,[u,c])}function Wr(e,r){for(let i=0;i<r.length;++i){let t=r[i];if(e?.[t]===void 0){if(r[i+1]===void 0)return;r[i+1]=`${t}-${r[i+1]}`;continue}e=e[t]}return e}function Br(e,r,i){for(let t of r.slice(0,-1))e[t]===void 0&&(e[t]={}),e=e[t];e[r[r.length-1]]=i}function jn(e){return{kind:"combinator",value:e}}function zn(e,r){return{kind:"function",value:e,nodes:r}}function ze(e){return{kind:"selector",value:e}}function Fn(e){return{kind:"separator",value:e}}function Mn(e){return{kind:"value",value:e}}function Fe(e,r,i=null){for(let t=0;t<e.length;t++){let n=e[t],s=!1,a=0,p=r(n,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(e.splice(t,1),a=0):u.length===1?(e[t]=u[0],a=1):(e.splice(t,1,...u),a=u.length):(e[t]=u,a=1))}})??0;if(s){p===0?t--:t+=a-1;continue}if(p===2)return 2;if(p!==1&&n.kind==="function"&&Fe(n.nodes,r,n)===2)return 2}}function Me(e){let r="";for(let i of e)switch(i.kind){case"combinator":case"selector":case"separator":case"value":{r+=i.value;break}case"function":r+=i.value+"("+Me(i.nodes)+")"}return r}var qr=92,Wn=93,Hr=41,Bn=58,Gr=44,qn=34,Hn=46,Yr=62,Zr=10,Gn=35,Jr=91,Qr=40,Xr=43,Yn=39,ei=32,ti=9,ri=126;function dt(e){e=e.replaceAll(`\r
`,`
`);let r=[],i=[],t=null,n="",s;for(let a=0;a<e.length;a++){let p=e.charCodeAt(a);switch(p){case Gr:case Yr:case Zr:case ei:case Xr:case ti:case ri:{if(n.length>0){let d=ze(n);t?t.nodes.push(d):r.push(d),n=""}let u=a,c=a+1;for(;c<e.length&&(s=e.charCodeAt(c),!(s!==Gr&&s!==Yr&&s!==Zr&&s!==ei&&s!==Xr&&s!==ti&&s!==ri));c++);a=c-1;let m=e.slice(u,c),g=m.trim()===","?Fn(m):jn(m);t?t.nodes.push(g):r.push(g);break}case Qr:{let u=zn(n,[]);if(n="",u.value!==":not"&&u.value!==":where"&&u.value!==":has"&&u.value!==":is"){let c=a+1,m=0;for(let d=a+1;d<e.length;d++){if(s=e.charCodeAt(d),s===Qr){m++;continue}if(s===Hr){if(m===0){a=d;break}m--}}let g=a;u.nodes.push(Mn(e.slice(c,g))),n="",a=g,t?t.nodes.push(u):r.push(u);break}t?t.nodes.push(u):r.push(u),i.push(u),t=u;break}case Hr:{let u=i.pop();if(n.length>0){let c=ze(n);u.nodes.push(c),n=""}i.length>0?t=i[i.length-1]:t=null;break}case Hn:case Bn:case Gn:{if(n.length>0){let u=ze(n);t?t.nodes.push(u):r.push(u)}n=String.fromCharCode(p);break}case Jr:{if(n.length>0){let m=ze(n);t?t.nodes.push(m):r.push(m)}n="";let u=a,c=0;for(let m=a+1;m<e.length;m++){if(s=e.charCodeAt(m),s===Jr){c++;continue}if(s===Wn){if(c===0){a=m;break}c--}}n+=e.slice(u,a+1);break}case Yn:case qn:{let u=a;for(let c=a+1;c<e.length;c++)if(s=e.charCodeAt(c),s===qr)c+=1;else if(s===p){a=c;break}n+=e.slice(u,a+1);break}case qr:{let u=e.charCodeAt(a+1);n+=String.fromCharCode(p)+String.fromCharCode(u),a+=1;break}default:n+=String.fromCharCode(p)}}return n.length>0&&r.push(ze(n)),r}var ii=/^[a-z@][a-zA-Z0-9/%._-]*$/;function zt({designSystem:e,ast:r,resolvedConfig:i,featuresRef:t,referenceMode:n,src:s}){let a={addBase(p){if(n)return;let u=ue(p);t.current|=Se(u,e);let c=F("@layer","base",u);L([c],m=>{m.src=s}),r.push(c)},addVariant(p,u){if(!ut.test(p))throw new Error(`\`addVariant('${p}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes, or underscore characters and start with a lowercase letter or number.`);if(typeof u=="string"){if(u.includes(":merge("))return}else if(Array.isArray(u)){if(u.some(m=>m.includes(":merge(")))return}else if(typeof u=="object"){let m=function(g,d){return Object.entries(g).some(([w,v])=>w.includes(d)||typeof v=="object"&&m(v,d))};var c=m;if(m(u,":merge("))return}typeof u=="string"||Array.isArray(u)?e.variants.static(p,m=>{m.nodes=ni(u,m.nodes)},{compounds:Ce(typeof u=="string"?[u]:u)}):typeof u=="object"&&e.variants.fromAst(p,ue(u),e)},matchVariant(p,u,c){function m(d,w,v){let k=u(d,{modifier:w?.value??null});return ni(k,v)}try{let d=u("a",{modifier:null});if(typeof d=="string"&&d.includes(":merge("))return;if(Array.isArray(d)&&d.some(w=>w.includes(":merge(")))return}catch{}let g=Object.keys(c?.values??{});e.variants.group(()=>{e.variants.functional(p,(d,w)=>{if(!w.value){if(c?.values&&"DEFAULT"in c.values){d.nodes=m(c.values.DEFAULT,w.modifier,d.nodes);return}return null}if(w.value.kind==="arbitrary")d.nodes=m(w.value.value,w.modifier,d.nodes);else if(w.value.kind==="named"&&c?.values){let v=c.values[w.value.value];if(typeof v!="string")return null;d.nodes=m(v,w.modifier,d.nodes)}else return null})},(d,w)=>{if(d.kind!=="functional"||w.kind!=="functional")return 0;let v=d.value?d.value.value:"DEFAULT",k=w.value?w.value.value:"DEFAULT",x=c?.values?.[v]??v,S=c?.values?.[k]??k;if(c&&typeof c.sort=="function")return c.sort({value:x,modifier:d.modifier?.value??null},{value:S,modifier:w.modifier?.value??null});let C=g.indexOf(v),b=g.indexOf(k);return C=C===-1?g.length:C,b=b===-1?g.length:b,C!==b?C-b:x<S?-1:1}),e.variants.suggest(p,()=>Object.keys(c?.values??{}).filter(d=>d!=="DEFAULT"))},addUtilities(p){p=Array.isArray(p)?p:[p];let u=p.flatMap(m=>Object.entries(m));u=u.flatMap(([m,g])=>U(m,",").map(d=>[d.trim(),g]));let c=new W(()=>[]);for(let[m,g]of u){if(m.startsWith("@keyframes ")){if(!n){let v=Y(m,ue(g));L([v],k=>{k.src=s}),r.push(v)}continue}let d=dt(m),w=!1;if(Fe(d,v=>{if(v.kind==="selector"&&v.value[0]==="."&&ii.test(v.value.slice(1))){let k=v.value;v.value="&";let x=Me(d),S=k.slice(1),C=x==="&"?ue(g):[Y(x,ue(g))];c.get(S).push(...C),w=!0,v.value=k;return}if(v.kind==="function"&&v.value===":not")return 1}),!w)throw new Error(`\`addUtilities({ '${m}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[m,g]of c)e.theme.prefix&&L(g,d=>{if(d.kind==="rule"){let w=dt(d.selector);Fe(w,v=>{v.kind==="selector"&&v.value[0]==="."&&(v.value=`.${e.theme.prefix}\\:${v.value.slice(1)}`)}),d.selector=Me(w)}}),e.utilities.static(m,d=>{let w=structuredClone(g);return oi(w,m,d.raw),t.current|=Ie(w,e),w})},matchUtilities(p,u){let c=u?.type?Array.isArray(u?.type)?u.type:[u.type]:["any"];for(let[g,d]of Object.entries(p)){let w=function({negative:v}){return k=>{if(k.value?.kind==="arbitrary"&&c.length>0&&!c.includes("any")&&(k.value.dataType&&!c.includes(k.value.dataType)||!k.value.dataType&&!Z(k.value.value,c)))return;let x=c.includes("color"),S=null,C=!1;{let R=u?.values??{};x&&(R=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentcolor"},R)),k.value?k.value.kind==="arbitrary"?S=k.value.value:k.value.fraction&&R[k.value.fraction]?(S=R[k.value.fraction],C=!0):R[k.value.value]?S=R[k.value.value]:R.__BARE_VALUE__&&(S=R.__BARE_VALUE__(k.value)??null,C=(k.value.fraction!==null&&S?.includes("/"))??!1):S=R.DEFAULT??null}if(S===null)return;let b;{let R=u?.modifiers??null;k.modifier?R==="any"||k.modifier.kind==="arbitrary"?b=k.modifier.value:R?.[k.modifier.value]?b=R[k.modifier.value]:x&&!Number.isNaN(Number(k.modifier.value))?b=`${k.modifier.value}%`:b=null:b=null}if(k.modifier&&b===null&&!C)return k.value?.kind==="arbitrary"?null:void 0;x&&b!==null&&(S=Q(S,b)),v&&(S=`calc(${S} * -1)`);let _=ue(d(S,{modifier:b}));return oi(_,g,k.raw),t.current|=Ie(_,e),_}};var m=w;if(!ii.test(g))throw new Error(`\`matchUtilities({ '${g}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);u?.supportsNegativeValues&&e.utilities.functional(`-${g}`,w({negative:!0}),{types:c}),e.utilities.functional(g,w({negative:!1}),{types:c}),e.utilities.suggest(g,()=>{let v=u?.values??{},k=new Set(Object.keys(v));k.delete("__BARE_VALUE__"),k.delete("__CSS_VALUES__"),k.has("DEFAULT")&&(k.delete("DEFAULT"),k.add(null));let x=u?.modifiers??{},S=x==="any"?[]:Object.keys(x);return[{supportsNegative:u?.supportsNegativeValues??!1,values:Array.from(k),modifiers:S}]})}},addComponents(p,u){this.addUtilities(p,u)},matchComponents(p,u){this.matchUtilities(p,u)},theme:pt(e,()=>i.theme??{},p=>p),prefix(p){return p},config(p,u){let c=i;if(!p)return c;let m=ft(p);for(let g=0;g<m.length;++g){let d=m[g];if(c[d]===void 0)return u;c=c[d]}return c??u}};return a.addComponents=a.addComponents.bind(a),a.matchComponents=a.matchComponents.bind(a),a}function ue(e){let r=[];e=Array.isArray(e)?e:[e];let i=e.flatMap(t=>Object.entries(t));for(let[t,n]of i)if(n!=null&&n!==!1)if(typeof n!="object"){if(!t.startsWith("--")){if(n==="@slot"){r.push(Y(t,[F("@slot")]));continue}t=t.replace(/([A-Z])/g,"-$1").toLowerCase()}r.push(o(t,String(n)))}else if(Array.isArray(n))for(let s of n)typeof s=="string"?r.push(o(t,s)):r.push(Y(t,ue(s)));else r.push(Y(t,ue(n)));return r}function ni(e,r){return(typeof e=="string"?[e]:e).flatMap(t=>{if(t.trim().endsWith("}")){let n=t.replace("}","{@slot}}"),s=ve(n);return Ut(s,r),s}else return Y(t,r)})}function oi(e,r,i){L(e,t=>{if(t.kind==="rule"){let n=dt(t.selector);Fe(n,s=>{s.kind==="selector"&&s.value===`.${r}`&&(s.value=`.${de(i)}`)}),t.selector=Me(n)}})}function li(e,r,i){for(let t of Jn(r))e.theme.addKeyframes(t)}function Jn(e){let r=[];if("keyframes"in e.theme)for(let[i,t]of Object.entries(e.theme.keyframes))r.push(F("@keyframes",i,ue(t)));return r}var mt={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function $e(e){return{__BARE_VALUE__:e}}var le=$e(e=>{if(T(e.value))return e.value}),re=$e(e=>{if(T(e.value))return`${e.value}%`}),he=$e(e=>{if(T(e.value))return`${e.value}px`}),ai=$e(e=>{if(T(e.value))return`${e.value}ms`}),gt=$e(e=>{if(T(e.value))return`${e.value}deg`}),Qn=$e(e=>{if(e.fraction===null)return;let[r,i]=U(e.fraction,"/");if(!(!T(r)||!T(i)))return e.fraction}),si=$e(e=>{if(T(Number(e.value)))return`repeat(${e.value}, minmax(0, 1fr))`}),ui={accentColor:({theme:e})=>e("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...Qn},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>({...e("brightness"),...re}),backdropContrast:({theme:e})=>({...e("contrast"),...re}),backdropGrayscale:({theme:e})=>({...e("grayscale"),...re}),backdropHueRotate:({theme:e})=>({...e("hueRotate"),...gt}),backdropInvert:({theme:e})=>({...e("invert"),...re}),backdropOpacity:({theme:e})=>({...e("opacity"),...re}),backdropSaturate:({theme:e})=>({...e("saturate"),...re}),backdropSepia:({theme:e})=>({...e("sepia"),...re}),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>e("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...he},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...re},caretColor:({theme:e})=>e("colors"),colors:()=>({...mt}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...le},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...re},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>({...e("borderWidth"),...he}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...e("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...le},flexShrink:{0:"0",DEFAULT:"1",...le},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...re},grayscale:{0:"0",DEFAULT:"100%",...re},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...le},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...le},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...le},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...le},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...si},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...si},height:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...gt},inset:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),invert:{0:"0",DEFAULT:"100%",...re},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...le},maxHeight:({theme:e})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),maxWidth:({theme:e})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...e("spacing")}),minHeight:({theme:e})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),minWidth:({theme:e})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...re},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...le},outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),ringColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...gt},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...re},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...re},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:e})=>e("spacing"),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%",...re},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...gt},space:({theme:e})=>e("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:e})=>({none:"none",...e("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...le},supports:{},data:{},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},textIndent:({theme:e})=>e("spacing"),textOpacity:({theme:e})=>e("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...ai},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...ai},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),size:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),width:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...le}};function ci(e){return{theme:{...ui,colors:({theme:r})=>r("color",{}),extend:{fontSize:({theme:r})=>({...r("text",{})}),boxShadow:({theme:r})=>({...r("shadow",{})}),animation:({theme:r})=>({...r("animate",{})}),aspectRatio:({theme:r})=>({...r("aspect",{})}),borderRadius:({theme:r})=>({...r("radius",{})}),screens:({theme:r})=>({...r("breakpoint",{})}),letterSpacing:({theme:r})=>({...r("tracking",{})}),lineHeight:({theme:r})=>({...r("leading",{})}),transitionDuration:{DEFAULT:e.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:e.get(["--default-transition-timing-function"])??null},maxWidth:({theme:r})=>({...r("container",{})})}}}}var Xn={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function Mt(e,r){let i={design:e,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(Xn)};for(let n of r)Ft(i,n);for(let n of i.configs)"darkMode"in n&&n.darkMode!==void 0&&(i.result.darkMode=n.darkMode??null),"prefix"in n&&n.prefix!==void 0&&(i.result.prefix=n.prefix??""),"blocklist"in n&&n.blocklist!==void 0&&(i.result.blocklist=n.blocklist??[]),"important"in n&&n.important!==void 0&&(i.result.important=n.important??!1);let t=to(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:t}}function eo(e,r){if(Array.isArray(e)&&Re(e[0]))return e.concat(r);if(Array.isArray(r)&&Re(r[0])&&Re(e))return[e,...r];if(Array.isArray(r))return r}function Ft(e,{config:r,base:i,path:t,reference:n,src:s}){let a=[];for(let c of r.plugins??[])"__isOptionsFunction"in c?a.push({...c(),reference:n,src:s}):"handler"in c?a.push({...c,reference:n,src:s}):a.push({handler:c,reference:n,src:s});if(Array.isArray(r.presets)&&r.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let c of r.presets??[])Ft(e,{path:t,base:i,config:c,reference:n,src:s});for(let c of a)e.plugins.push(c),c.config&&Ft(e,{path:t,base:i,config:c.config,reference:!!c.reference,src:c.src??s});let p=r.content??[],u=Array.isArray(p)?p:p.files;for(let c of u)e.content.files.push(typeof c=="object"?c:{base:i,pattern:c});e.configs.push(r)}function to(e){let r=new Set,i=pt(e.design,()=>e.theme,n),t=Object.assign(i,{theme:i,colors:mt});function n(s){return typeof s=="function"?s(t)??null:s??null}for(let s of e.configs){let a=s.theme??{},p=a.extend??{};for(let u in a)u!=="extend"&&r.add(u);Object.assign(e.theme,a);for(let u in p)e.extend[u]??=[],e.extend[u].push(p[u])}delete e.theme.extend;for(let s in e.extend){let a=[e.theme[s],...e.extend[s]];e.theme[s]=()=>{let p=a.map(n);return je({},p,eo)}}for(let s in e.theme)e.theme[s]=n(e.theme[s]);if(e.theme.screens&&typeof e.theme.screens=="object")for(let s of Object.keys(e.theme.screens)){let a=e.theme.screens[s];a&&typeof a=="object"&&("raw"in a||"max"in a||"min"in a&&(e.theme.screens[s]=a.min))}return r}function fi(e,r){let i=e.theme.container||{};if(typeof i!="object"||i===null)return;let t=ro(i,r);t.length!==0&&r.utilities.static("container",()=>structuredClone(t))}function ro({center:e,padding:r,screens:i},t){let n=[],s=null;if(e&&n.push(o("margin-inline","auto")),(typeof r=="string"||typeof r=="object"&&r!==null&&"DEFAULT"in r)&&n.push(o("padding-inline",typeof r=="string"?r:r.DEFAULT)),typeof i=="object"&&i!==null){s=new Map;let a=Array.from(t.theme.namespace("--breakpoint").entries());if(a.sort((p,u)=>be(p[1],u[1],"asc")),a.length>0){let[p]=a[0];n.push(F("@media",`(width >= --theme(--breakpoint-${p}))`,[o("max-width","none")]))}for(let[p,u]of Object.entries(i)){if(typeof u=="object")if("min"in u)u=u.min;else continue;s.set(p,F("@media",`(width >= ${u})`,[o("max-width",u)]))}}if(typeof r=="object"&&r!==null){let a=Object.entries(r).filter(([p])=>p!=="DEFAULT").map(([p,u])=>[p,t.theme.resolveValue(p,["--breakpoint"]),u]).filter(Boolean);a.sort((p,u)=>be(p[1],u[1],"asc"));for(let[p,,u]of a)if(s&&s.has(p))s.get(p).nodes.push(o("padding-inline",u));else{if(s)continue;n.push(F("@media",`(width >= theme(--breakpoint-${p}))`,[o("padding-inline",u)]))}}if(s)for(let[,a]of s)n.push(a);return n}function pi({addVariant:e,config:r}){let i=r("darkMode",null),[t,n=".dark"]=Array.isArray(i)?i:[i];if(t==="variant"){let s;if(Array.isArray(n)||typeof n=="function"?s=n:typeof n=="string"&&(s=[n]),Array.isArray(s))for(let a of s)a===".dark"?(t=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):a.includes("&")||(t=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));n=s}t===null||(t==="selector"?e("dark",`&:where(${n}, ${n} *)`):t==="media"?e("dark","@media (prefers-color-scheme: dark)"):t==="variant"?e("dark",n):t==="class"&&e("dark",`&:is(${n} *)`))}function di(e){for(let[r,i]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])e.utilities.static(`bg-gradient-to-${r}`,()=>[o("--tw-gradient-position",`to ${i} in oklab`),o("background-image","linear-gradient(var(--tw-gradient-stops))")]);e.utilities.static("bg-left-top",()=>[o("background-position","left top")]),e.utilities.static("bg-right-top",()=>[o("background-position","right top")]),e.utilities.static("bg-left-bottom",()=>[o("background-position","left bottom")]),e.utilities.static("bg-right-bottom",()=>[o("background-position","right bottom")]),e.utilities.static("object-left-top",()=>[o("object-position","left top")]),e.utilities.static("object-right-top",()=>[o("object-position","right top")]),e.utilities.static("object-left-bottom",()=>[o("object-position","left bottom")]),e.utilities.static("object-right-bottom",()=>[o("object-position","right bottom")]),e.utilities.functional("max-w-screen",r=>{if(!r.value||r.value.kind==="arbitrary")return;let i=e.theme.resolve(r.value.value,["--breakpoint"]);if(i)return[o("max-width",i)]}),e.utilities.static("overflow-ellipsis",()=>[o("text-overflow","ellipsis")]),e.utilities.static("decoration-slice",()=>[o("-webkit-box-decoration-break","slice"),o("box-decoration-break","slice")]),e.utilities.static("decoration-clone",()=>[o("-webkit-box-decoration-break","clone"),o("box-decoration-break","clone")]),e.utilities.functional("flex-shrink",r=>{if(!r.modifier){if(!r.value)return[o("flex-shrink","1")];if(r.value.kind==="arbitrary")return[o("flex-shrink",r.value.value)];if(T(r.value.value))return[o("flex-shrink",r.value.value)]}}),e.utilities.functional("flex-grow",r=>{if(!r.modifier){if(!r.value)return[o("flex-grow","1")];if(r.value.kind==="arbitrary")return[o("flex-grow",r.value.value)];if(T(r.value.value))return[o("flex-grow",r.value.value)]}}),e.utilities.static("order-none",()=>[o("order","0")])}function mi(e,r){let i=e.theme.screens||{},t=r.variants.get("min")?.order??0,n=[];for(let[a,p]of Object.entries(i)){let d=function(w){r.variants.static(a,v=>{v.nodes=[F("@media",g,v.nodes)]},{order:w})};var s=d;let u=r.variants.get(a),c=r.theme.resolveValue(a,["--breakpoint"]);if(u&&c&&!r.theme.hasDefault(`--breakpoint-${a}`))continue;let m=!0;typeof p=="string"&&(m=!1);let g=io(p);m?n.push(d):d(t)}if(n.length!==0){for(let[,a]of r.variants.variants)a.order>t&&(a.order+=n.length);r.variants.compareFns=new Map(Array.from(r.variants.compareFns).map(([a,p])=>(a>t&&(a+=n.length),[a,p])));for(let[a,p]of n.entries())p(t+a+1)}}function io(e){return(Array.isArray(e)?e:[e]).map(i=>typeof i=="string"?{min:i}:i&&typeof i=="object"?i:null).map(i=>{if(i===null)return null;if("raw"in i)return i.raw;let t="";return i.max!==void 0&&(t+=`${i.max} >= `),t+="width",i.min!==void 0&&(t+=` >= ${i.min}`),`(${t})`}).filter(Boolean).join(", ")}function gi(e,r){let i=e.theme.aria||{},t=e.theme.supports||{},n=e.theme.data||{};if(Object.keys(i).length>0){let s=r.variants.get("aria"),a=s?.applyFn,p=s?.compounds;r.variants.functional("aria",(u,c)=>{let m=c.value;return m&&m.kind==="named"&&m.value in i?a?.(u,{...c,value:{kind:"arbitrary",value:i[m.value]}}):a?.(u,c)},{compounds:p})}if(Object.keys(t).length>0){let s=r.variants.get("supports"),a=s?.applyFn,p=s?.compounds;r.variants.functional("supports",(u,c)=>{let m=c.value;return m&&m.kind==="named"&&m.value in t?a?.(u,{...c,value:{kind:"arbitrary",value:t[m.value]}}):a?.(u,c)},{compounds:p})}if(Object.keys(n).length>0){let s=r.variants.get("data"),a=s?.applyFn,p=s?.compounds;r.variants.functional("data",(u,c)=>{let m=c.value;return m&&m.kind==="named"&&m.value in n?a?.(u,{...c,value:{kind:"arbitrary",value:n[m.value]}}):a?.(u,c)},{compounds:p})}}var no=/^[a-z]+$/;async function vi({designSystem:e,base:r,ast:i,loadModule:t,sources:n}){let s=0,a=[],p=[];L(i,(g,{parent:d,replaceWith:w,context:v})=>{if(g.kind==="at-rule"){if(g.name==="@plugin"){if(d!==null)throw new Error("`@plugin` cannot be nested.");let k=g.params.slice(1,-1);if(k.length===0)throw new Error("`@plugin` must have a path.");let x={};for(let S of g.nodes??[]){if(S.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${ne([S])}

\`@plugin\` options must be a flat list of declarations.`);if(S.value===void 0)continue;let C=S.value,b=U(C,",").map(_=>{if(_=_.trim(),_==="null")return null;if(_==="true")return!0;if(_==="false")return!1;if(Number.isNaN(Number(_))){if(_[0]==='"'&&_[_.length-1]==='"'||_[0]==="'"&&_[_.length-1]==="'")return _.slice(1,-1);if(_[0]==="{"&&_[_.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${ne([S]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(_);return _});x[S.property]=b.length===1?b[0]:b}a.push([{id:k,base:v.base,reference:!!v.reference,src:g.src},Object.keys(x).length>0?x:null]),w([]),s|=4;return}if(g.name==="@config"){if(g.nodes.length>0)throw new Error("`@config` cannot have a body.");if(d!==null)throw new Error("`@config` cannot be nested.");p.push({id:g.params.slice(1,-1),base:v.base,reference:!!v.reference,src:g.src}),w([]),s|=4;return}}}),di(e);let u=e.resolveThemeValue;if(e.resolveThemeValue=function(d,w){return d.startsWith("--")?u(d,w):(s|=hi({designSystem:e,base:r,ast:i,sources:n,configs:[],pluginDetails:[]}),e.resolveThemeValue(d,w))},!a.length&&!p.length)return 0;let[c,m]=await Promise.all([Promise.all(p.map(async({id:g,base:d,reference:w,src:v})=>{let k=await t(g,d,"config");return{path:g,base:k.base,config:k.module,reference:w,src:v}})),Promise.all(a.map(async([{id:g,base:d,reference:w,src:v},k])=>{let x=await t(g,d,"plugin");return{path:g,base:x.base,plugin:x.module,options:k,reference:w,src:v}}))]);return s|=hi({designSystem:e,base:r,ast:i,sources:n,configs:c,pluginDetails:m}),s}function hi({designSystem:e,base:r,ast:i,sources:t,configs:n,pluginDetails:s}){let a=0,u=[...s.map(k=>{if(!k.options)return{config:{plugins:[k.plugin]},base:k.base,reference:k.reference,src:k.src};if("__isOptionsFunction"in k.plugin)return{config:{plugins:[k.plugin(k.options)]},base:k.base,reference:k.reference,src:k.src};throw new Error(`The plugin "${k.path}" does not accept options`)}),...n],{resolvedConfig:c}=Mt(e,[{config:ci(e.theme),base:r,reference:!0,src:void 0},...u,{config:{plugins:[pi]},base:r,reference:!0,src:void 0}]),{resolvedConfig:m,replacedThemeKeys:g}=Mt(e,u),d={designSystem:e,ast:i,resolvedConfig:c,featuresRef:{set current(k){a|=k}}},w=zt({...d,referenceMode:!1,src:void 0}),v=e.resolveThemeValue;e.resolveThemeValue=function(x,S){if(x[0]==="-"&&x[1]==="-")return v(x,S);let C=w.theme(x,void 0);if(Array.isArray(C)&&C.length===2)return C[0];if(Array.isArray(C))return C.join(", ");if(typeof C=="string")return C};for(let{handler:k,reference:x,src:S}of c.plugins){let C=zt({...d,referenceMode:x??!1,src:S});k(C)}if(Fr(e,m,g),li(e,m,g),gi(m,e),mi(m,e),fi(m,e),!e.theme.prefix&&c.prefix){if(c.prefix.endsWith("-")&&(c.prefix=c.prefix.slice(0,-1),console.warn(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!no.test(c.prefix))throw new Error(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);e.theme.prefix=c.prefix}if(!e.important&&c.important===!0&&(e.important=!0),typeof c.important=="string"){let k=c.important;L(i,(x,{replaceWith:S,parent:C})=>{if(x.kind==="at-rule"&&!(x.name!=="@tailwind"||x.params!=="utilities"))return C?.kind==="rule"&&C.selector===k?2:(S(B(k,[x])),2)})}for(let k of c.blocklist)e.invalidCandidates.add(k);for(let k of c.content.files){if("raw"in k)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(k,null,2)}

This feature is not currently supported.`);let x=!1;k.pattern[0]=="!"&&(x=!0,k.pattern=k.pattern.slice(1)),t.push({...k,negated:x})}return a}function wi(e){let r=[0];for(let n=0;n<e.length;n++)e.charCodeAt(n)===10&&r.push(n+1);function i(n){let s=0,a=r.length;for(;a>0;){let u=(a|0)>>1,c=s+u;r[c]<=n?(s=c+1,a=a-u-1):a=u}s-=1;let p=n-r[s];return{line:s+1,column:p}}function t({line:n,column:s}){n-=1,n=Math.min(Math.max(n,0),r.length-1);let a=r[n],p=r[n+1]??a;return Math.min(Math.max(a+s,0),p)}return{find:i,findOffset:t}}function ki({ast:e}){let r=new W(n=>wi(n.code)),i=new W(n=>({url:n.file,content:n.code,ignore:!1})),t={file:null,sources:[],mappings:[]};L(e,n=>{if(!n.src||!n.dst)return;let s=i.get(n.src[0]);if(!s.content)return;let a=r.get(n.src[0]),p=r.get(n.dst[0]),u=s.content.slice(n.src[1],n.src[2]),c=0;for(let d of u.split(`
`)){if(d.trim()!==""){let w=a.find(n.src[1]+c),v=p.find(n.dst[1]);t.mappings.push({name:null,originalPosition:{source:s,...w},generatedPosition:v})}c+=d.length,c+=1}let m=a.find(n.src[2]),g=p.find(n.dst[2]);t.mappings.push({name:null,originalPosition:{source:s,...m},generatedPosition:g})});for(let n of r.keys())t.sources.push(i.get(n));return t.mappings.sort((n,s)=>n.generatedPosition.line-s.generatedPosition.line||n.generatedPosition.column-s.generatedPosition.column||(n.originalPosition?.line??0)-(s.originalPosition?.line??0)||(n.originalPosition?.column??0)-(s.originalPosition?.column??0)),t}var yi=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/;function ht(e){let r=e.indexOf("{");if(r===-1)return[e];let i=[],t=e.slice(0,r),n=e.slice(r),s=0,a=n.lastIndexOf("}");for(let g=0;g<n.length;g++){let d=n[g];if(d==="{")s++;else if(d==="}"&&(s--,s===0)){a=g;break}}if(a===-1)throw new Error(`The pattern \`${e}\` is not balanced.`);let p=n.slice(1,a),u=n.slice(a+1),c;oo(p)?c=lo(p):c=U(p,","),c=c.flatMap(g=>ht(g));let m=ht(u);for(let g of m)for(let d of c)i.push(t+d+g);return i}function oo(e){return yi.test(e)}function lo(e){let r=e.match(yi);if(!r)return[e];let[,i,t,n]=r,s=n?parseInt(n,10):void 0,a=[];if(/^-?\d+$/.test(i)&&/^-?\d+$/.test(t)){let p=parseInt(i,10),u=parseInt(t,10);if(s===void 0&&(s=p<=u?1:-1),s===0)throw new Error("Step cannot be zero in sequence expansion.");let c=p<u;c&&s<0&&(s=-s),!c&&s>0&&(s=-s);for(let m=p;c?m<=u:m>=u;m+=s)a.push(m.toString())}return a}function bi(e,r){let i=new Set,t=new Set,n=[];function s(a,p=[]){if(e.has(a)&&!i.has(a)){t.has(a)&&r.onCircularDependency?.(p,a),t.add(a);for(let u of e.get(a)??[])p.push(a),s(u,p),p.pop();i.add(a),t.delete(a),n.push(a)}}for(let a of e.keys())s(a);return n}var ao=/^[a-z]+$/,tt=(n=>(n[n.None=0]="None",n[n.AtProperty=1]="AtProperty",n[n.ColorMix=2]="ColorMix",n[n.All=3]="All",n))(tt||{});function so(){throw new Error("No `loadModule` function provided to `compile`")}function uo(){throw new Error("No `loadStylesheet` function provided to `compile`")}function co(e){let r=0,i=null;for(let t of U(e," "))t==="reference"?r|=2:t==="inline"?r|=1:t==="default"?r|=4:t==="static"?r|=8:t.startsWith("prefix(")&&t.endsWith(")")&&(i=t.slice(7,-1));return[r,i]}var Ae=(u=>(u[u.None=0]="None",u[u.AtApply=1]="AtApply",u[u.AtImport=2]="AtImport",u[u.JsPluginCompat=4]="JsPluginCompat",u[u.ThemeFunction=8]="ThemeFunction",u[u.Utilities=16]="Utilities",u[u.Variants=32]="Variants",u[u.AtTheme=64]="AtTheme",u))(Ae||{});async function xi(e,{base:r="",from:i,loadModule:t=so,loadStylesheet:n=uo}={}){let s=0;e=[se({base:r},e)],s|=await jt(e,r,n,0,i!==void 0);let a=null,p=new Je,u=new Map,c=new Map,m=[],g=null,d=null,w=[],v=[],k=[],x=[],S=null;L(e,(b,{parent:_,replaceWith:R,context:D})=>{if(b.kind==="at-rule"){if(b.name==="@tailwind"&&(b.params==="utilities"||b.params.startsWith("utilities"))){if(d!==null){R([]);return}if(D.reference){R([]);return}let K=U(b.params," ");for(let H of K)if(H.startsWith("source(")){let O=H.slice(7,-1);if(O==="none"){S=O;continue}if(O[0]==='"'&&O[O.length-1]!=='"'||O[0]==="'"&&O[O.length-1]!=="'"||O[0]!=="'"&&O[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");S={base:D.sourceBase??D.base,pattern:O.slice(1,-1)}}d=b,s|=16}if(b.name==="@utility"){if(_!==null)throw new Error("`@utility` cannot be nested.");if(b.nodes.length===0)throw new Error(`\`@utility ${b.params}\` is empty. Utilities should include at least one property.`);let K=Er(b);if(K===null){if(!b.params.endsWith("-*")){if(b.params.endsWith("*"))throw new Error(`\`@utility ${b.params}\` defines an invalid utility name. A functional utility must end in \`-*\`.`);if(b.params.includes("*"))throw new Error(`\`@utility ${b.params}\` defines an invalid utility name. The dynamic portion marked by \`-*\` must appear once at the end.`)}throw new Error(`\`@utility ${b.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`)}m.push(K)}if(b.name==="@source"){if(b.nodes.length>0)throw new Error("`@source` cannot have a body.");if(_!==null)throw new Error("`@source` cannot be nested.");let K=!1,H=!1,O=b.params;if(O[0]==="n"&&O.startsWith("not ")&&(K=!0,O=O.slice(4)),O[0]==="i"&&O.startsWith("inline(")&&(H=!0,O=O.slice(7,-1)),O[0]==='"'&&O[O.length-1]!=='"'||O[0]==="'"&&O[O.length-1]!=="'"||O[0]!=="'"&&O[0]!=='"')throw new Error("`@source` paths must be quoted.");let I=O.slice(1,-1);if(H){let q=K?x:k,M=U(I," ");for(let ie of M)for(let l of ht(ie))q.push(l)}else v.push({base:D.base,pattern:I,negated:K});R([]);return}if(b.name==="@variant"&&(_===null?b.nodes.length===0?b.name="@custom-variant":(L(b.nodes,K=>{if(K.kind==="at-rule"&&K.name==="@slot")return b.name="@custom-variant",2}),b.name==="@variant"&&w.push(b)):w.push(b)),b.name==="@custom-variant"){if(_!==null)throw new Error("`@custom-variant` cannot be nested.");R([]);let[K,H]=U(b.params," ");if(!ut.test(K))throw new Error(`\`@custom-variant ${K}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes, or underscore characters and start with a lowercase letter or number.`);if(b.nodes.length>0&&H)throw new Error(`\`@custom-variant ${K}\` cannot have both a selector and a body.`);if(b.nodes.length===0){if(!H)throw new Error(`\`@custom-variant ${K}\` has no selector or body.`);let O=U(H.slice(1,-1),",");if(O.length===0||O.some(M=>M.trim()===""))throw new Error(`\`@custom-variant ${K} (${O.join(",")})\` selector is invalid.`);let I=[],q=[];for(let M of O)M=M.trim(),M[0]==="@"?I.push(M):q.push(M);u.set(K,M=>{M.variants.static(K,ie=>{let l=[];q.length>0&&l.push(B(q.join(", "),ie.nodes));for(let f of I)l.push(Y(f,ie.nodes));ie.nodes=l},{compounds:Ce([...q,...I])})}),c.set(K,new Set);return}else{let O=new Set;L(b.nodes,I=>{I.kind==="at-rule"&&I.name==="@variant"&&O.add(I.params)}),u.set(K,I=>{I.variants.fromAst(K,b.nodes,I)}),c.set(K,O);return}}if(b.name==="@media"){let K=U(b.params," "),H=[];for(let O of K)if(O.startsWith("source(")){let I=O.slice(7,-1);L(b.nodes,(q,{replaceWith:M})=>{if(q.kind==="at-rule"&&q.name==="@tailwind"&&q.params==="utilities")return q.params+=` source(${I})`,M([se({sourceBase:D.base},[q])]),2})}else if(O.startsWith("theme(")){let I=O.slice(6,-1),q=I.includes("reference");L(b.nodes,M=>{if(M.kind!=="at-rule"){if(q)throw new Error('Files imported with `@import "\u2026" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "\u2026";` instead.');return 0}if(M.name==="@theme")return M.params+=" "+I,1})}else if(O.startsWith("prefix(")){let I=O.slice(7,-1);L(b.nodes,q=>{if(q.kind==="at-rule"&&q.name==="@theme")return q.params+=` prefix(${I})`,1})}else O==="important"?a=!0:O==="reference"?b.nodes=[se({reference:!0},b.nodes)]:H.push(O);H.length>0?b.params=H.join(" "):K.length>0&&R(b.nodes)}if(b.name==="@theme"){let[K,H]=co(b.params);if(s|=64,D.reference&&(K|=2),H){if(!ao.test(H))throw new Error(`The prefix "${H}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);p.prefix=H}return L(b.nodes,O=>{if(O.kind==="at-rule"&&O.name==="@keyframes")return p.addKeyframes(O),1;if(O.kind==="comment")return;if(O.kind==="declaration"&&O.property.startsWith("--")){p.add(we(O.property),O.value??"",K,O.src);return}let I=ne([F(b.name,b.params,[O])]).split(`
`).map((q,M,ie)=>`${M===0||M>=ie.length-2?" ":">"} ${q}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${I}`)}),g?R([]):(g=B(":root, :host",[]),g.src=b.src,R([g])),1}}});let C=Ur(p);if(a&&(C.important=a),x.length>0)for(let b of x)C.invalidCandidates.add(b);s|=await vi({designSystem:C,base:r,ast:e,loadModule:t,sources:v});for(let b of u.keys())C.variants.static(b,()=>{});for(let b of bi(c,{onCircularDependency(_,R){let D=ne(_.map((K,H)=>F("@custom-variant",K,[F("@variant",_[H+1]??R,[])]))).replaceAll(";"," { \u2026 }").replace(`@custom-variant ${R} {`,`@custom-variant ${R} { /* \u2190 */`);throw new Error(`Circular dependency detected in custom variants:

${D}`)}}))u.get(b)?.(C);for(let b of m)b(C);if(g){let b=[];for(let[R,D]of C.theme.entries()){if(D.options&2)continue;let K=o(de(R),D.value);K.src=D.src,b.push(K)}let _=C.theme.getKeyframes();for(let R of _)e.push(se({theme:!0},[z([R])]));g.nodes=[se({theme:!0},b)]}if(s|=Lt(e,C),s|=Se(e,C),s|=Ie(e,C),d){let b=d;b.kind="context",b.context={}}return L(e,(b,{replaceWith:_})=>{if(b.kind==="at-rule")return b.name==="@utility"&&_([]),1}),{designSystem:C,ast:e,sources:v,root:S,utilitiesNode:d,features:s,inlineCandidates:k}}async function Ai(e,r={}){let{designSystem:i,ast:t,sources:n,root:s,utilitiesNode:a,features:p,inlineCandidates:u}=await xi(e,r);t.unshift(Ze(`! tailwindcss v${Wt} | MIT License | https://tailwindcss.com `));function c(v){i.invalidCandidates.add(v)}let m=new Set,g=null,d=0,w=!1;for(let v of u)i.invalidCandidates.has(v)||(m.add(v),w=!0);return{sources:n,root:s,features:p,build(v){if(p===0)return e;if(!a)return g??=ye(t,i,r.polyfills),g;let k=w,x=!1;w=!1;let S=m.size;for(let b of v)if(!i.invalidCandidates.has(b))if(b[0]==="-"&&b[1]==="-"){let _=i.theme.markUsedVariable(b);k||=_,x||=_}else m.add(b),k||=m.size!==S;if(!k)return g??=ye(t,i,r.polyfills),g;let C=ge(m,i,{onInvalidCandidate:c}).astNodes;return r.from&&L(C,b=>{b.src??=a.src}),!x&&d===C.length?(g??=ye(t,i,r.polyfills),g):(d=C.length,a.nodes=C,g=ye(t,i,r.polyfills),g)}}}async function fo(e,r={}){let i=ve(e,{from:r.from}),t=await Ai(i,r),n=i,s=e;return{...t,build(a){let p=t.build(a);return p===n||(s=ne(p,!!r.from),n=p),s},buildSourceMap(){return ki({ast:n})}}}async function po(e,r={}){return(await xi(ve(e),r)).designSystem}function We(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}for(let e in vt)e!=="default"&&(We[e]=vt[e]);module.exports=We;
