"use strict";var e=require("react"),t=e=>"checkbox"===e.type,r=e=>e instanceof Date,s=e=>null==e;const a=e=>"object"==typeof e;var i=e=>!s(e)&&!Array.isArray(e)&&a(e)&&!r(e),n=e=>i(e)&&e.target?t(e.target)?e.target.checked:e.target.value:e,o=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),l="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function u(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else{if(l&&(e instanceof Blob||s)||!r&&!i(e))return e;if(t=r?[]:Object.create(Object.getPrototypeOf(e)),r||(e=>{const t=e.constructor&&e.constructor.prototype;return i(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=u(e[r]));else t=e}return t}var d=e=>/^\w*$/.test(e),c=e=>void 0===e,f=e=>Array.isArray(e)?e.filter(Boolean):[],m=e=>f(e.replace(/["|']|\]/g,"").split(/\.|\[/)),y=(e,t,r)=>{if(!t||!i(e))return r;const a=(d(t)?[t]:m(t)).reduce((e,t)=>s(e)?e:e[t],e);return c(a)||a===e?c(e[t])?r:e[t]:a},g=e=>"boolean"==typeof e,p=(e,t,r)=>{let s=-1;const a=d(t)?[t]:m(t),n=a.length,o=n-1;for(;++s<n;){const t=a[s];let n=r;if(s!==o){const r=e[t];n=i(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=n,e=e[t]}};const _="blur",b="focusout",h="change",v="onBlur",V="onChange",F="onSubmit",x="onTouched",A="all",S="max",w="min",k="maxLength",D="minLength",C="pattern",E="required",O="validate",M=e.createContext(null);M.displayName="HookFormContext";const j=()=>e.useContext(M);var U=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==A&&(t._proxyFormState[a]=!s||A),r&&(r[a]=!0),e[a]}});return a};const T="undefined"!=typeof window?e.useLayoutEffect:e.useEffect;function N(t){const r=j(),{control:s=r.control,disabled:a,name:i,exact:n}=t||{},[o,l]=e.useState(s._formState),u=e.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return T(()=>s._subscribe({name:i,formState:u.current,exact:n,callback:e=>{!a&&l({...s._formState,...e})}}),[i,a,n]),e.useEffect(()=>{u.current.isValid&&s._setValid(!0)},[s]),e.useMemo(()=>U(o,s,u.current,!1),[o,s])}var R=e=>"string"==typeof e,B=(e,t,r,s,a)=>R(e)?(s&&t.watch.add(e),y(r,e,a)):Array.isArray(e)?e.map(e=>(s&&t.watch.add(e),y(r,e))):(s&&(t.watchAll=!0),r),L=e=>s(e)||!a(e);function P(e,t,s=new WeakSet){if(L(e)||L(t))return e===t;if(r(e)&&r(t))return e.getTime()===t.getTime();const a=Object.keys(e),n=Object.keys(t);if(a.length!==n.length)return!1;if(s.has(e)||s.has(t))return!0;s.add(e),s.add(t);for(const o of a){const a=e[o];if(!n.includes(o))return!1;if("ref"!==o){const e=t[o];if(r(a)&&r(e)||i(a)&&i(e)||Array.isArray(a)&&Array.isArray(e)?!P(a,e,s):a!==e)return!1}}return!0}function W(t){const r=j(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o,compute:l}=t||{},u=e.useRef(i),d=e.useRef(l),c=e.useRef(void 0);d.current=l;const f=e.useMemo(()=>s._getWatch(a,u.current),[s,a]),[m,y]=e.useState(d.current?d.current(f):f);return T(()=>s._subscribe({name:a,formState:{values:!0},exact:o,callback:e=>{if(!n){const t=B(a,s._names,e.values||s._formValues,!1,u.current);if(d.current){const e=d.current(t);P(e,c.current)||(y(e),c.current=e)}else y(t)}}}),[s,n,a,o]),e.useEffect(()=>s._removeUnmounted()),m}function I(t){const r=j(),{name:s,disabled:a,control:i=r.control,shouldUnregister:l,defaultValue:d}=t,f=o(i._names.array,s),m=e.useMemo(()=>y(i._formValues,s,y(i._defaultValues,s,d)),[i,s,d]),b=W({control:i,name:s,defaultValue:m,exact:!0}),v=N({control:i,name:s,exact:!0}),V=e.useRef(t),F=e.useRef(void 0),x=e.useRef(i.register(s,{...t.rules,value:b,...g(t.disabled)?{disabled:t.disabled}:{}}));V.current=t;const A=e.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!y(v.errors,s)},isDirty:{enumerable:!0,get:()=>!!y(v.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!y(v.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!y(v.validatingFields,s)},error:{enumerable:!0,get:()=>y(v.errors,s)}}),[v,s]),S=e.useCallback(e=>x.current.onChange({target:{value:n(e),name:s},type:h}),[s]),w=e.useCallback(()=>x.current.onBlur({target:{value:y(i._formValues,s),name:s},type:_}),[s,i._formValues]),k=e.useCallback(e=>{const t=y(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[i._fields,s]),D=e.useMemo(()=>({name:s,value:b,...g(a)||v.disabled?{disabled:v.disabled||a}:{},onChange:S,onBlur:w,ref:k}),[s,a,v.disabled,S,w,k,b]);return e.useEffect(()=>{const e=i._options.shouldUnregister||l,t=F.current;t&&t!==s&&!f&&i.unregister(t),i.register(s,{...V.current.rules,...g(V.current.disabled)?{disabled:V.current.disabled}:{}});const r=(e,t)=>{const r=y(i._fields,e);r&&r._f&&(r._f.mount=t)};if(r(s,!0),e){const e=u(y(i._options.defaultValues,s,V.current.defaultValue));p(i._defaultValues,s,e),c(y(i._formValues,s))&&p(i._formValues,s,e)}return!f&&i.register(s),F.current=s,()=>{(f?e&&!i._state.action:e)?i.unregister(s):r(s,!1)}},[s,i,f,l]),e.useEffect(()=>{i._setDisabledField({disabled:a,name:s})},[a,s,i]),e.useMemo(()=>({field:D,formState:v,fieldState:A}),[D,v,A])}const q=e=>{const t={};for(const r of Object.keys(e))if(a(e[r])&&null!==e[r]){const s=q(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},$="post";var H=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},J=e=>Array.isArray(e)?e:[e],z=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}};function G(e,t){const r={};for(const s in e)if(e.hasOwnProperty(s)){const a=e[s],n=t[s];if(a&&i(a)&&n){const e=G(a,n);i(e)&&(r[s]=e)}else e[s]&&(r[s]=n)}return r}var K=e=>i(e)&&!Object.keys(e).length,Q=e=>"file"===e.type,X=e=>"function"==typeof e,Y=e=>{if(!l)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Z=e=>"select-multiple"===e.type,ee=e=>"radio"===e.type,te=e=>Y(e)&&e.isConnected;function re(e,t){const r=Array.isArray(t)?t:d(t)?[t]:m(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=c(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,n=r[a];return s&&delete s[n],0!==a&&(i(s)&&K(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!c(e[t]))return!1;return!0}(s))&&re(e,r.slice(0,-1)),e}function se(e){return Array.isArray(e)||i(e)&&!(e=>{for(const t in e)if(X(e[t]))return!0;return!1})(e)}function ae(e,t={}){for(const r in e)se(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ae(e[r],t[r])):c(e[r])||(t[r]=!0);return t}function ie(e,t,r){r||(r=ae(t));for(const a in e)se(e[a])?c(t)||L(r[a])?r[a]=ae(e[a],Array.isArray(e[a])?[]:{}):ie(e[a],s(t)?{}:t[a],r[a]):r[a]=!P(e[a],t[a]);return r}const ne={value:!1,isValid:!1},oe={value:!0,isValid:!0};var le=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!c(e[0].attributes.value)?c(e[0].value)||""===e[0].value?oe:{value:e[0].value,isValid:!0}:oe:ne}return ne},ue=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>c(e)?e:t?""===e?NaN:e?+e:e:r&&R(e)?new Date(e):s?s(e):e;const de={isValid:!1,value:null};var ce=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,de):de;function fe(e){const r=e.ref;return Q(r)?r.files:ee(r)?ce(e.refs).value:Z(r)?[...r.selectedOptions].map(({value:e})=>e):t(r)?le(e.refs).value:ue(c(r.value)?e.ref.value:r.value,e)}var me=e=>e instanceof RegExp,ye=e=>c(e)?e:me(e)?e.source:i(e)?me(e.value)?e.value.source:e.value:e,ge=e=>({isOnSubmit:!e||e===F,isOnBlur:e===v,isOnChange:e===V,isOnAll:e===A,isOnTouch:e===x});const pe="AsyncFunction";var _e=e=>!!e&&!!e.validate&&!!(X(e.validate)&&e.validate.constructor.name===pe||i(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===pe)),be=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));const he=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=y(e,a);if(r){const{_f:e,...n}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(he(n,t))break}else if(i(n)&&he(n,t))break}}};function ve(e,t,r){const s=y(e,r);if(s||d(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=y(t,s),n=y(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};if(n&&n.root&&n.root.type)return{name:`${s}.root`,error:n.root};a.pop()}return{name:r}}var Ve=(e,t,r)=>{const s=J(y(e,r));return p(s,"root",t[r]),p(e,r,s),e};function Fe(e,t,r="validate"){if(R(e)||Array.isArray(e)&&e.every(R)||g(e)&&!e)return{type:r,message:R(e)?e:"",ref:t}}var xe=e=>i(e)&&!me(e)?e:{value:e,message:""},Ae=async(e,r,a,n,o,l)=>{const{ref:u,refs:d,required:f,maxLength:m,minLength:p,min:_,max:b,pattern:h,validate:v,name:V,valueAsNumber:F,mount:x}=e._f,A=y(a,V);if(!x||r.has(V))return{};const M=d?d[0]:u,j=e=>{o&&M.reportValidity&&(M.setCustomValidity(g(e)?"":e||""),M.reportValidity())},U={},T=ee(u),N=t(u),B=T||N,L=(F||Q(u))&&c(u.value)&&c(A)||Y(u)&&""===u.value||""===A||Array.isArray(A)&&!A.length,P=H.bind(null,V,n,U),W=(e,t,r,s=k,a=D)=>{const i=e?t:r;U[V]={type:e?s:a,message:i,ref:u,...P(e?s:a,i)}};if(l?!Array.isArray(A)||!A.length:f&&(!B&&(L||s(A))||g(A)&&!A||N&&!le(d).isValid||T&&!ce(d).isValid)){const{value:e,message:t}=R(f)?{value:!!f,message:f}:xe(f);if(e&&(U[V]={type:E,message:t,ref:M,...P(E,t)},!n))return j(t),U}if(!(L||s(_)&&s(b))){let e,t;const r=xe(b),a=xe(_);if(s(A)||isNaN(A)){const s=u.valueAsDate||new Date(A),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==u.type,o="week"==u.type;R(r.value)&&A&&(e=n?i(A)>i(r.value):o?A>r.value:s>new Date(r.value)),R(a.value)&&A&&(t=n?i(A)<i(a.value):o?A<a.value:s<new Date(a.value))}else{const i=u.valueAsNumber||(A?+A:A);s(r.value)||(e=i>r.value),s(a.value)||(t=i<a.value)}if((e||t)&&(W(!!e,r.message,a.message,S,w),!n))return j(U[V].message),U}if((m||p)&&!L&&(R(A)||l&&Array.isArray(A))){const e=xe(m),t=xe(p),r=!s(e.value)&&A.length>+e.value,a=!s(t.value)&&A.length<+t.value;if((r||a)&&(W(r,e.message,t.message),!n))return j(U[V].message),U}if(h&&!L&&R(A)){const{value:e,message:t}=xe(h);if(me(e)&&!A.match(e)&&(U[V]={type:C,message:t,ref:u,...P(C,t)},!n))return j(t),U}if(v)if(X(v)){const e=Fe(await v(A,a),M);if(e&&(U[V]={...e,...P(O,e.message)},!n))return j(e.message),U}else if(i(v)){let e={};for(const t in v){if(!K(e)&&!n)break;const r=Fe(await v[t](A,a),M,t);r&&(e={...r,...P(t,r.message)},j(r.message),n&&(U[V]=e))}if(!K(e)&&(U[V]={ref:M,...e},!n))return U}return j(!0),U};const Se={mode:F,reValidateMode:V,shouldFocusError:!0};function we(e={}){let a,d={...Se,...e},m={submitCount:0,isDirty:!1,isReady:!1,isLoading:X(d.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:d.errors||{},disabled:d.disabled||!1},h={},v=(i(d.defaultValues)||i(d.values))&&u(d.defaultValues||d.values)||{},V=d.shouldUnregister?{}:u(v),F={action:!1,mount:!1,watch:!1},x={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},S=0;const w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let k={...w};const D={array:z(),state:z()},C=d.criteriaMode===A,E=async e=>{if(!d.disabled&&(w.isValid||k.isValid||e)){const e=d.resolver?K((await T()).errors):await N(h,!0);e!==m.isValid&&D.state.next({isValid:e})}},O=(e,t)=>{!d.disabled&&(w.isValidating||w.validatingFields||k.isValidating||k.validatingFields)&&((e||Array.from(x.mount)).forEach(e=>{e&&(t?p(m.validatingFields,e,t):re(m.validatingFields,e))}),D.state.next({validatingFields:m.validatingFields,isValidating:!K(m.validatingFields)}))},M=(e,t,r,s)=>{const a=y(h,e);if(a){const i=y(V,e,c(r)?y(v,e):r);c(i)||s&&s.defaultChecked||t?p(V,e,t?i:fe(a._f)):I(e,i),F.mount&&E()}},j=(e,t,r,s,a)=>{let i=!1,n=!1;const o={name:e};if(!d.disabled){if(!r||s){(w.isDirty||k.isDirty)&&(n=m.isDirty,m.isDirty=o.isDirty=L(),i=n!==o.isDirty);const r=P(y(v,e),t);n=!!y(m.dirtyFields,e),r?re(m.dirtyFields,e):p(m.dirtyFields,e,!0),o.dirtyFields=m.dirtyFields,i=i||(w.dirtyFields||k.dirtyFields)&&n!==!r}if(r){const t=y(m.touchedFields,e);t||(p(m.touchedFields,e,r),o.touchedFields=m.touchedFields,i=i||(w.touchedFields||k.touchedFields)&&t!==r)}i&&a&&D.state.next(o)}return i?o:{}},U=(e,t,r,s)=>{const i=y(m.errors,e),n=(w.isValid||k.isValid)&&g(t)&&m.isValid!==t;var o;if(d.delayError&&r?(o=()=>((e,t)=>{p(m.errors,e,t),D.state.next({errors:m.errors})})(e,r),a=e=>{clearTimeout(S),S=setTimeout(o,e)},a(d.delayError)):(clearTimeout(S),a=null,r?p(m.errors,e,r):re(m.errors,e)),(r?!P(i,r):i)||!K(s)||n){const r={...s,...n&&g(t)?{isValid:t}:{},errors:m.errors,name:e};m={...m,...r},D.state.next(r)}},T=async e=>{O(e,!0);const t=await d.resolver(V,d.context,((e,t,r,s)=>{const a={};for(const r of e){const e=y(t,r);e&&p(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||x.mount,h,d.criteriaMode,d.shouldUseNativeValidation));return O(e),t},N=async(e,t,r={valid:!0})=>{for(const s in e){const a=e[s];if(a){const{_f:e,...s}=a;if(e){const s=x.array.has(e.name),i=a._f&&_e(a._f);i&&w.validatingFields&&O([e.name],!0);const n=await Ae(a,x.disabled,V,C,d.shouldUseNativeValidation&&!t,s);if(i&&w.validatingFields&&O([e.name]),n[e.name]&&(r.valid=!1,t))break;!t&&(y(n,e.name)?s?Ve(m.errors,n,e.name):p(m.errors,e.name,n[e.name]):re(m.errors,e.name))}!K(s)&&await N(s,t,r)}}return r.valid},L=(e,t)=>!d.disabled&&(e&&t&&p(V,e,t),!P(ne(),v)),W=(e,t,r)=>B(e,x,{...F.mount?V:c(t)?v:R(e)?{[e]:t}:t},r,t),I=(e,r,a={})=>{const i=y(h,e);let n=r;if(i){const a=i._f;a&&(!a.disabled&&p(V,e,ue(r,a)),n=Y(a.ref)&&s(r)?"":r,Z(a.ref)?[...a.ref.options].forEach(e=>e.selected=n.includes(e.value)):a.refs?t(a.ref)?a.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(n)?e.checked=!!n.find(t=>t===e.value):e.checked=n===e.value||!!n)}):a.refs.forEach(e=>e.checked=e.value===n):Q(a.ref)?a.ref.value="":(a.ref.value=n,a.ref.type||D.state.next({name:e,values:u(V)})))}(a.shouldDirty||a.shouldTouch)&&j(e,n,a.shouldTouch,a.shouldDirty,!0),a.shouldValidate&&ae(e)},q=(e,t,s)=>{for(const a in t){if(!t.hasOwnProperty(a))return;const n=t[a],o=e+"."+a,l=y(h,o);(x.array.has(e)||i(n)||l&&!l._f)&&!r(n)?q(o,n,s):I(o,n,s)}},$=(e,t,r={})=>{const a=y(h,e),i=x.array.has(e),n=u(t);p(V,e,n),i?(D.array.next({name:e,values:u(V)}),(w.isDirty||w.dirtyFields||k.isDirty||k.dirtyFields)&&r.shouldDirty&&D.state.next({name:e,dirtyFields:ie(v,V),isDirty:L(e,n)})):!a||a._f||s(n)?I(e,n,r):q(e,n,r),be(e,x)&&D.state.next({...m,name:e}),D.state.next({name:F.mount?e:void 0,values:u(V)})},H=async e=>{F.mount=!0;const t=e.target;let s=t.name,i=!0;const o=y(h,s),l=e=>{i=Number.isNaN(e)||r(e)&&isNaN(e.getTime())||P(e,y(V,s,e))},c=ge(d.mode),f=ge(d.reValidateMode);if(o){let r,v;const F=t.type?fe(o._f):n(e),A=e.type===_||e.type===b,S=!((g=o._f).mount&&(g.required||g.min||g.max||g.maxLength||g.minLength||g.pattern||g.validate)||d.resolver||y(m.errors,s)||o._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(A,y(m.touchedFields,s),m.isSubmitted,f,c),M=be(s,x,A);p(V,s,F),A?t&&t.readOnly||(o._f.onBlur&&o._f.onBlur(e),a&&a(0)):o._f.onChange&&o._f.onChange(e);const R=j(s,F,A),B=!K(R)||M;if(!A&&D.state.next({name:s,type:e.type,values:u(V)}),S)return(w.isValid||k.isValid)&&("onBlur"===d.mode?A&&E():A||E()),B&&D.state.next({name:s,...M?{}:R});if(!A&&M&&D.state.next({...m}),d.resolver){const{errors:e}=await T([s]);if(l(F),i){const t=ve(m.errors,h,s),a=ve(e,h,t.name||s);r=a.error,s=a.name,v=K(e)}}else O([s],!0),r=(await Ae(o,x.disabled,V,C,d.shouldUseNativeValidation))[s],O([s]),l(F),i&&(r?v=!1:(w.isValid||k.isValid)&&(v=await N(h,!0)));i&&(o._f.deps&&(!Array.isArray(o._f.deps)||o._f.deps.length>0)&&ae(o._f.deps),U(s,v,r,R))}var g},se=(e,t)=>{if(y(m.errors,t)&&e.focus)return e.focus(),1},ae=async(e,t={})=>{let r,s;const a=J(e);if(d.resolver){const t=await(async e=>{const{errors:t}=await T(e);if(e)for(const r of e){const e=y(t,r);e?p(m.errors,r,e):re(m.errors,r)}else m.errors=t;return t})(c(e)?e:a);r=K(t),s=e?!a.some(e=>y(t,e)):r}else e?(s=(await Promise.all(a.map(async e=>{const t=y(h,e);return await N(t&&t._f?{[e]:t}:t)}))).every(Boolean),(s||m.isValid)&&E()):s=r=await N(h);return D.state.next({...!R(e)||(w.isValid||k.isValid)&&r!==m.isValid?{}:{name:e},...d.resolver||!e?{isValid:r}:{},errors:m.errors}),t.shouldFocus&&!s&&he(h,se,e?a:x.mount),s},ne=(e,t)=>{let r={...F.mount?V:v};return t&&(r=G(t.dirtyFields?m.dirtyFields:m.touchedFields,r)),c(e)?r:R(e)?y(r,e):e.map(e=>y(r,e))},oe=(e,t)=>({invalid:!!y((t||m).errors,e),isDirty:!!y((t||m).dirtyFields,e),error:y((t||m).errors,e),isValidating:!!y(m.validatingFields,e),isTouched:!!y((t||m).touchedFields,e)}),le=(e,t,r)=>{const s=(y(h,e,{_f:{}})._f||{}).ref,a=y(m.errors,e)||{},{ref:i,message:n,type:o,...l}=a;p(m.errors,e,{...l,...t,ref:s}),D.state.next({name:e,errors:m.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},de=e=>D.state.subscribe({next:t=>{var r,s,a;r=e.name,s=t.name,a=e.exact,r&&s&&r!==s&&!J(r).some(e=>e&&(a?e===s:e.startsWith(s)||s.startsWith(e)))||!((e,t,r,s)=>{r(e);const{name:a,...i}=e;return K(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!s||A))})(t,e.formState||w,De,e.reRenderRoot)||e.callback({values:{...V},...m,...t,defaultValues:v})}}).unsubscribe,ce=(e,t={})=>{for(const r of e?J(e):x.mount)x.mount.delete(r),x.array.delete(r),t.keepValue||(re(h,r),re(V,r)),!t.keepError&&re(m.errors,r),!t.keepDirty&&re(m.dirtyFields,r),!t.keepTouched&&re(m.touchedFields,r),!t.keepIsValidating&&re(m.validatingFields,r),!d.shouldUnregister&&!t.keepDefaultValue&&re(v,r);D.state.next({values:u(V)}),D.state.next({...m,...t.keepDirty?{isDirty:L()}:{}}),!t.keepIsValid&&E()},me=({disabled:e,name:t})=>{(g(e)&&F.mount||e||x.disabled.has(t))&&(e?x.disabled.add(t):x.disabled.delete(t))},pe=(e,r={})=>{let s=y(h,e);const a=g(r.disabled)||g(d.disabled);return p(h,e,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:e}},name:e,mount:!0,...r}}),x.mount.add(e),s?me({disabled:g(r.disabled)?r.disabled:d.disabled,name:e}):M(e,!0,r.value),{...a?{disabled:r.disabled||d.disabled}:{},...d.progressive?{required:!!r.required,min:ye(r.min),max:ye(r.max),minLength:ye(r.minLength),maxLength:ye(r.maxLength),pattern:ye(r.pattern)}:{},name:e,onChange:H,onBlur:H,ref:a=>{if(a){pe(e,r),s=y(h,e);const i=c(a.value)&&a.querySelectorAll&&a.querySelectorAll("input,select,textarea")[0]||a,n=(e=>ee(e)||t(e))(i),o=s._f.refs||[];if(n?o.find(e=>e===i):i===s._f.ref)return;p(h,e,{_f:{...s._f,...n?{refs:[...o.filter(te),i,...Array.isArray(y(v,e))?[{}]:[]],ref:{type:i.type,name:e}}:{ref:i}}}),M(e,!1,void 0,i)}else s=y(h,e,{}),s._f&&(s._f.mount=!1),(d.shouldUnregister||r.shouldUnregister)&&(!o(x.array,e)||!F.action)&&x.unMount.add(e)}}},Fe=()=>d.shouldFocusError&&he(h,se,x.mount),xe=(e,t)=>async r=>{let s;r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist());let a=u(V);if(D.state.next({isSubmitting:!0}),d.resolver){const{errors:e,values:t}=await T();m.errors=e,a=u(t)}else await N(h);if(x.disabled.size)for(const e of x.disabled)re(a,e);if(re(m.errors,"root"),K(m.errors)){D.state.next({errors:{}});try{await e(a,r)}catch(e){s=e}}else t&&await t({...m.errors},r),Fe(),setTimeout(Fe);if(D.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:K(m.errors)&&!s,submitCount:m.submitCount+1,errors:m.errors}),s)throw s},we=(e,t={})=>{const r=e?u(e):v,s=u(r),a=K(e),i=a?v:s;if(t.keepDefaultValues||(v=r),!t.keepValues){if(t.keepDirtyValues){const e=new Set([...x.mount,...Object.keys(ie(v,V))]);for(const t of Array.from(e))y(m.dirtyFields,t)?p(i,t,y(V,t)):$(t,y(i,t))}else{if(l&&c(e))for(const e of x.mount){const t=y(h,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(Y(e)){const t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(const e of x.mount)$(e,y(i,e));else h={}}V=d.shouldUnregister?t.keepDefaultValues?u(v):{}:u(i),D.array.next({values:{...i}}),D.state.next({values:{...i}})}x={mount:t.keepDirtyValues?x.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},F.mount=!w.isValid||!!t.keepIsValid||!!t.keepDirtyValues,F.watch=!!d.shouldUnregister,D.state.next({submitCount:t.keepSubmitCount?m.submitCount:0,isDirty:!a&&(t.keepDirty?m.isDirty:!(!t.keepDefaultValues||P(e,v))),isSubmitted:!!t.keepIsSubmitted&&m.isSubmitted,dirtyFields:a?{}:t.keepDirtyValues?t.keepDefaultValues&&V?ie(v,V):m.dirtyFields:t.keepDefaultValues&&e?ie(v,e):t.keepDirty?m.dirtyFields:{},touchedFields:t.keepTouched?m.touchedFields:{},errors:t.keepErrors?m.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&m.isSubmitSuccessful,isSubmitting:!1,defaultValues:v})},ke=(e,t)=>we(X(e)?e(V):e,t),De=e=>{m={...m,...e}},Ce={control:{register:pe,unregister:ce,getFieldState:oe,handleSubmit:xe,setError:le,_subscribe:de,_runSchema:T,_focusError:Fe,_getWatch:W,_getDirty:L,_setValid:E,_setFieldArray:(e,t=[],r,s,a=!0,i=!0)=>{if(s&&r&&!d.disabled){if(F.action=!0,i&&Array.isArray(y(h,e))){const t=r(y(h,e),s.argA,s.argB);a&&p(h,e,t)}if(i&&Array.isArray(y(m.errors,e))){const t=r(y(m.errors,e),s.argA,s.argB);a&&p(m.errors,e,t),((e,t)=>{!f(y(e,t)).length&&re(e,t)})(m.errors,e)}if((w.touchedFields||k.touchedFields)&&i&&Array.isArray(y(m.touchedFields,e))){const t=r(y(m.touchedFields,e),s.argA,s.argB);a&&p(m.touchedFields,e,t)}(w.dirtyFields||k.dirtyFields)&&(m.dirtyFields=ie(v,V)),D.state.next({name:e,isDirty:L(e,t),dirtyFields:m.dirtyFields,errors:m.errors,isValid:m.isValid})}else p(V,e,t)},_setDisabledField:me,_setErrors:e=>{m.errors=e,D.state.next({errors:m.errors,isValid:!1})},_getFieldArray:e=>f(y(F.mount?V:v,e,d.shouldUnregister?y(v,e,[]):[])),_reset:we,_resetDefaultValues:()=>X(d.defaultValues)&&d.defaultValues().then(e=>{ke(e,d.resetOptions),D.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(const e of x.unMount){const t=y(h,e);t&&(t._f.refs?t._f.refs.every(e=>!te(e)):!te(t._f.ref))&&ce(e)}x.unMount=new Set},_disableForm:e=>{g(e)&&(D.state.next({disabled:e}),he(h,(t,r)=>{const s=y(h,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach(t=>{t.disabled=s._f.disabled||e}))},0,!1))},_subjects:D,_proxyFormState:w,get _fields(){return h},get _formValues(){return V},get _state(){return F},set _state(e){F=e},get _defaultValues(){return v},get _names(){return x},set _names(e){x=e},get _formState(){return m},get _options(){return d},set _options(e){d={...d,...e}}},subscribe:e=>(F.mount=!0,k={...k,...e.formState},de({...e,formState:k})),trigger:ae,register:pe,handleSubmit:xe,watch:(e,t)=>X(e)?D.state.subscribe({next:r=>"values"in r&&e(W(void 0,t),r)}):W(e,t,!0),setValue:$,getValues:ne,reset:ke,resetField:(e,t={})=>{y(h,e)&&(c(t.defaultValue)?$(e,u(y(v,e))):($(e,t.defaultValue),p(v,e,u(t.defaultValue))),t.keepTouched||re(m.touchedFields,e),t.keepDirty||(re(m.dirtyFields,e),m.isDirty=t.defaultValue?L(e,u(y(v,e))):L()),t.keepError||(re(m.errors,e),w.isValid&&E()),D.state.next({...m}))},clearErrors:e=>{e&&J(e).forEach(e=>re(m.errors,e)),D.state.next({errors:e?m.errors:{}})},unregister:ce,setError:le,setFocus:(e,t={})=>{const r=y(h,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&X(e.select)&&e.select())}},getFieldState:oe};return{...Ce,formControl:Ce}}var ke=()=>{if("undefined"!=typeof crypto&&crypto.randomUUID)return crypto.randomUUID();const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)})},De=(e,t,r={})=>r.shouldFocus||c(r.shouldFocus)?r.focusName||`${e}.${c(r.focusIndex)?t:r.focusIndex}.`:"",Ce=(e,t)=>[...e,...J(t)],Ee=e=>Array.isArray(e)?e.map(()=>{}):void 0;function Oe(e,t,r){return[...e.slice(0,t),...J(r),...e.slice(t)]}var Me=(e,t,r)=>Array.isArray(e)?(c(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],je=(e,t)=>[...J(t),...J(e)];var Ue=(e,t)=>c(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return f(s).length?s:[]}(e,J(t).sort((e,t)=>e-t)),Te=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]},Ne=(e,t,r)=>(e[t]=r,e);exports.Controller=e=>e.render(I(e)),exports.Form=function(t){const r=j(),[s,a]=e.useState(!1),{control:i=r.control,onSubmit:n,children:o,action:l,method:u=$,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:g,...p}=t,_=async e=>{let r=!1,s="";await i.handleSubmit(async t=>{const a=new FormData;let o="";try{o=JSON.stringify(t)}catch(e){}const m=q(i._formValues);for(const e in m)a.append(e,m[e]);if(n&&await n({data:t,event:e,method:u,formData:a,formDataJson:o}),l)try{const e=[d&&d["Content-Type"],c].some(e=>e&&e.includes("json")),t=await fetch(String(l),{method:u,headers:{...d,...c&&"multipart/form-data"!==c?{"Content-Type":c}:{}},body:e?o:a});t&&(g?!g(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}})(e),r&&t.control&&(t.control._subjects.state.next({isSubmitSuccessful:!1}),t.control.setError("root.server",{type:s}))};return e.useEffect(()=>{a(!0)},[]),m?e.createElement(e.Fragment,null,m({submit:_})):e.createElement("form",{noValidate:s,action:l,method:u,encType:c,onSubmit:_,...p},o)},exports.FormProvider=t=>{const{children:r,...s}=t;return e.createElement(M.Provider,{value:s},r)},exports.Watch=({control:e,names:t,render:r})=>r(W({control:e,name:t})),exports.appendErrors=H,exports.createFormControl=we,exports.get=y,exports.set=p,exports.useController=I,exports.useFieldArray=function(t){const r=j(),{control:s=r.control,name:a,keyName:i="id",shouldUnregister:n,rules:o}=t,[l,d]=e.useState(s._getFieldArray(a)),c=e.useRef(s._getFieldArray(a).map(ke)),f=e.useRef(!1);s._names.array.add(a),e.useMemo(()=>o&&l.length>=0&&s.register(a,o),[s,a,l.length,o]),T(()=>s._subjects.array.subscribe({next:({values:e,name:t})=>{if(t===a||!t){const t=y(e,a);Array.isArray(t)&&(d(t),c.current=t.map(ke))}}}).unsubscribe,[s,a]);const m=e.useCallback(e=>{f.current=!0,s._setFieldArray(a,e)},[s,a]);return e.useEffect(()=>{if(s._state.action=!1,be(a,s._names)&&s._subjects.state.next({...s._formState}),f.current&&(!ge(s._options.mode).isOnSubmit||s._formState.isSubmitted)&&!ge(s._options.reValidateMode).isOnSubmit)if(s._options.resolver)s._runSchema([a]).then(e=>{const t=y(e.errors,a),r=y(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?p(s._formState.errors,a,t):re(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))});else{const e=y(s._fields,a);!e||!e._f||ge(s._options.reValidateMode).isOnSubmit&&ge(s._options.mode).isOnSubmit||Ae(e,s._names.disabled,s._formValues,s._options.criteriaMode===A,s._options.shouldUseNativeValidation,!0).then(e=>!K(e)&&s._subjects.state.next({errors:Ve(s._formState.errors,e,a)}))}s._subjects.state.next({name:a,values:u(s._formValues)}),s._names.focus&&he(s._fields,(e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1}),s._names.focus="",s._setValid(),f.current=!1},[l,a,s]),e.useEffect(()=>(!y(s._formValues,a)&&s._setFieldArray(a),()=>{s._options.shouldUnregister||n?s.unregister(a):((e,t)=>{const r=y(s._fields,e);r&&r._f&&(r._f.mount=t)})(a,!1)}),[a,s,i,n]),{swap:e.useCallback((e,t)=>{const r=s._getFieldArray(a);Te(r,e,t),Te(c.current,e,t),m(r),d(r),s._setFieldArray(a,r,Te,{argA:e,argB:t},!1)},[m,a,s]),move:e.useCallback((e,t)=>{const r=s._getFieldArray(a);Me(r,e,t),Me(c.current,e,t),m(r),d(r),s._setFieldArray(a,r,Me,{argA:e,argB:t},!1)},[m,a,s]),prepend:e.useCallback((e,t)=>{const r=J(u(e)),i=je(s._getFieldArray(a),r);s._names.focus=De(a,0,t),c.current=je(c.current,r.map(ke)),m(i),d(i),s._setFieldArray(a,i,je,{argA:Ee(e)})},[m,a,s]),append:e.useCallback((e,t)=>{const r=J(u(e)),i=Ce(s._getFieldArray(a),r);s._names.focus=De(a,i.length-1,t),c.current=Ce(c.current,r.map(ke)),m(i),d(i),s._setFieldArray(a,i,Ce,{argA:Ee(e)})},[m,a,s]),remove:e.useCallback(e=>{const t=Ue(s._getFieldArray(a),e);c.current=Ue(c.current,e),m(t),d(t),!Array.isArray(y(s._fields,a))&&p(s._fields,a,void 0),s._setFieldArray(a,t,Ue,{argA:e})},[m,a,s]),insert:e.useCallback((e,t,r)=>{const i=J(u(t)),n=Oe(s._getFieldArray(a),e,i);s._names.focus=De(a,e,r),c.current=Oe(c.current,e,i.map(ke)),m(n),d(n),s._setFieldArray(a,n,Oe,{argA:e,argB:Ee(t)})},[m,a,s]),update:e.useCallback((e,t)=>{const r=u(t),i=Ne(s._getFieldArray(a),e,r);c.current=[...i].map((t,r)=>t&&r!==e?c.current[r]:ke()),m(i),d([...i]),s._setFieldArray(a,i,Ne,{argA:e,argB:r},!0,!1)},[m,a,s]),replace:e.useCallback(e=>{const t=J(u(e));c.current=t.map(ke),m([...t]),d([...t]),s._setFieldArray(a,[...t],e=>e,{},!0,!1)},[m,a,s]),fields:e.useMemo(()=>l.map((e,t)=>({...e,[i]:c.current[t]||ke()})),[l,i])}},exports.useForm=function(t={}){const r=e.useRef(void 0),s=e.useRef(void 0),[a,i]=e.useState({isDirty:!1,isValidating:!1,isLoading:X(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,isReady:!1,defaultValues:X(t.defaultValues)?void 0:t.defaultValues});if(!r.current)if(t.formControl)r.current={...t.formControl,formState:a},t.defaultValues&&!X(t.defaultValues)&&t.formControl.reset(t.defaultValues,t.resetOptions);else{const{formControl:e,...s}=we(t);r.current={...s,formState:a}}const n=r.current.control;return n._options=t,T(()=>{const e=n._subscribe({formState:n._proxyFormState,callback:()=>i({...n._formState}),reRenderRoot:!0});return i(e=>({...e,isReady:!0})),n._formState.isReady=!0,e},[n]),e.useEffect(()=>n._disableForm(t.disabled),[n,t.disabled]),e.useEffect(()=>{t.mode&&(n._options.mode=t.mode),t.reValidateMode&&(n._options.reValidateMode=t.reValidateMode)},[n,t.mode,t.reValidateMode]),e.useEffect(()=>{t.errors&&(n._setErrors(t.errors),n._focusError())},[n,t.errors]),e.useEffect(()=>{t.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})},[n,t.shouldUnregister]),e.useEffect(()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}},[n,a.isDirty]),e.useEffect(()=>{t.values&&!P(t.values,s.current)?(n._reset(t.values,{keepFieldsRef:!0,...n._options.resetOptions}),s.current=t.values,i(e=>({...e}))):n._resetDefaultValues()},[n,t.values]),e.useEffect(()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),r.current.formState=U(a,n),r.current},exports.useFormContext=j,exports.useFormState=N,exports.useWatch=W;
//# sourceMappingURL=index.cjs.js.map
