{"version": 3, "file": "index.umd.js", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/isNameInFieldArray.ts", "../src/logic/getNodeParentName.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/isPlainObject.ts", "../src/utils/isKey.ts", "../src/utils/isUndefined.ts", "../src/utils/compact.ts", "../src/utils/stringToPath.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/useIsomorphicLayoutEffect.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/utils/convertToArrayPayload.ts", "../src/utils/createSubject.ts", "../src/utils/extractFormValues.ts", "../src/utils/isEmptyObject.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/isRadioInput.ts", "../src/utils/live.ts", "../src/utils/unset.ts", "../src/logic/getDirtyFields.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getRadioValue.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/utils/isRegex.ts", "../src/logic/getRuleValue.ts", "../src/logic/getValidationModes.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/shouldRenderFormState.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/logic/createFormControl.ts", "../src/logic/hasValidation.ts", "../src/logic/skipValidation.ts", "../src/logic/shouldSubscribeByName.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/logic/unsetEmptyArray.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/update.ts", "../src/watch.tsx", "../src/useFieldArray.ts", "../src/useForm.ts"], "sourcesContent": ["import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "import type { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : Object.create(Object.getPrototypeOf(data));\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default (value: string) => /^\\w*$/.test(value);\n", "export default (val: unknown): val is undefined => val === undefined;\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import isKey from './isKey';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = (isKey(path) ? [path] : stringToPath(path)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof T & object],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "import type { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport type { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\nHookFormContext.displayName = 'HookFormContext';\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import React from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport type {\n  FieldValues,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import type { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName),\n        get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import type { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(\n  object1: any,\n  object2: any,\n  _internal_visited = new WeakSet(),\n) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n    return true;\n  }\n  _internal_visited.add(object1);\n  _internal_visited.add(object2);\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2, _internal_visited)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport deepEqual from './utils/deepEqual';\nimport type {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name?: undefined;\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute?: undefined;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute?: undefined;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and compute function to produce state update\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   compute: (formValues) => formValues.fieldA\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n  TComputeValue = unknown,\n>(props: {\n  name?: undefined;\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute: (formValues: TFieldValues) => TComputeValue;\n}): TComputeValue;\n/**\n * Custom hook to subscribe to field change and compute function to produce state update\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n *   compute: (fieldValue) => fieldValue === \"data\" ? fieldValue : null,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n  TComputeValue = unknown,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute: (\n    fieldValue: FieldPathValue<TFieldValues, TFieldName>,\n  ) => TComputeValue;\n}): TComputeValue;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute?: undefined;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and compute function to produce state update\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: 0\n *   },\n *   compute: ([fieldAValue, fieldBValue]) => fieldB === 2 ? fieldA : null,\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n  TComputeValue = unknown,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute: (\n    fieldValue: FieldPathValues<TFieldValues, TFieldNames>,\n  ) => TComputeValue;\n}): TComputeValue;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext<TFieldValues>();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n    compute,\n  } = props || {};\n  const _defaultValue = React.useRef(defaultValue);\n  const _compute = React.useRef(compute);\n  const _computeFormValues = React.useRef(undefined);\n\n  _compute.current = compute;\n\n  const defaultValueMemo = React.useMemo(\n    () =>\n      control._getWatch(\n        name as InternalFieldName,\n        _defaultValue.current as DeepPartialSkipArrayKey<TFieldValues>,\n      ),\n    [control, name],\n  );\n\n  const [value, updateValue] = React.useState(\n    _compute.current ? _compute.current(defaultValueMemo) : defaultValueMemo,\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) => {\n          if (!disabled) {\n            const formValues = generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            );\n\n            if (_compute.current) {\n              const computedFormValues = _compute.current(formValues);\n\n              if (!deepEqual(computedFormValues, _computeFormValues.current)) {\n                updateValue(computedFormValues);\n                _computeFormValues.current = computedFormValues;\n              }\n            } else {\n              updateValue(formValues);\n            }\n          }\n        },\n      }),\n    [control, disabled, name, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport type {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const {\n    name,\n    disabled,\n    control = methods.control,\n    shouldUnregister,\n    defaultValue,\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n\n  const defaultValueMemo = React.useMemo(\n    () =>\n      get(\n        control._formValues,\n        name,\n        get(control._defaultValues, name, defaultValue),\n      ),\n    [control, name, defaultValue],\n  );\n\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: defaultValueMemo,\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n\n  const _previousNameRef = React.useRef<string | undefined>(undefined);\n\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  _props.current = props;\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus && elm.focus(),\n          select: () => elm.select && elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n    const previousName = _previousNameRef.current;\n\n    if (previousName && previousName !== name && !isArrayField) {\n      control.unregister(previousName as FieldPath<TFieldValues>);\n    }\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(\n        get(control._options.defaultValues, name, _props.current.defaultValue),\n      );\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    _previousNameRef.current = name;\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import type { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import type { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport type { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType && encType !== 'multipart/form-data'\n                ? { 'Content-Type': encType }\n                : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import type {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import type { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import isObject from './isObject';\n\nexport default function extractFormValues<\n  T extends object,\n  K extends Record<string, unknown>,\n>(fieldsState: T, formValues: K) {\n  const values: Record<string, unknown> = {};\n\n  for (const key in fieldsState) {\n    if (fieldsState.hasOwnProperty(key)) {\n      const fieldState = fieldsState[key];\n      const fieldValue = formValues[key];\n\n      if (fieldState && isObject(fieldState) && fieldValue) {\n        const nestedFieldsState = extractFormValues(\n          fieldState,\n          fieldValue as K,\n        );\n\n        if (isObject(nestedFieldsState)) {\n          values[key] = nestedFieldsState;\n        }\n      } else if (fieldsState[key]) {\n        values[key] = fieldValue;\n      }\n    }\n  }\n\n  return values;\n}\n", "import type { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import type { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction isTraversable(value: any): boolean {\n  return Array.isArray(value) || (isObject(value) && !objectHasFunction(value));\n}\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  for (const key in data) {\n    if (isTraversable(data[key])) {\n      fields[key] = Array.isArray(data[key]) ? [] : {};\n      markFieldsDirty(data[key], fields[key]);\n    } else if (!isUndefined(data[key])) {\n      fields[key] = true;\n    }\n  }\n\n  return fields;\n}\n\nexport default function getDirtyFields<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues?: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  if (!dirtyFieldsFromValues) {\n    dirtyFieldsFromValues = markFieldsDirty(formValues);\n  }\n\n  for (const key in data) {\n    if (isTraversable(data[key])) {\n      if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n        dirtyFieldsFromValues[key] = markFieldsDirty(\n          data[key],\n          Array.isArray(data[key]) ? [] : {},\n        );\n      } else {\n        getDirtyFields(\n          data[key],\n          isNullOrUndefined(formValues) ? {} : formValues[key],\n          dirtyFieldsFromValues[key],\n        );\n      }\n    } else {\n      dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import type { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import type { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import type {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import type {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import type { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import type { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import type { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import type { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport type {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import type {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import type { <PERSON><PERSON>rror, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isString from '../utils/isString';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isString(result) ||\n    (Array.isArray(result) && result.every(isString)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isString(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import type { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport type {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isString(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport type {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  GetValuesConfig,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormSubscribe,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport extractFormValues from '../utils/extractFormValues';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([_f.name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([_f.name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach((checkboxRef) => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(\n                    (data: string) => data === checkboxRef.value,\n                  );\n                } else {\n                  checkboxRef.checked =\n                    fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState, name });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(\n      _options.reValidateMode,\n    );\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        if (!target || !target.readOnly) {\n          field._f.onBlur && field._f.onBlur(event);\n          delayErrorCallback && delayErrorCallback(0);\n        }\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          (!Array.isArray(field._f.deps) || field._f.deps.length > 0) &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n    config?: GetValuesConfig,\n  ) => {\n    let values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    if (config) {\n      values = extractFormValues(\n        config.dirtyFields ? _formState.dirtyFields : _formState.touchedFields,\n        values,\n      );\n    }\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            'values' in payload &&\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n          type?: EventType;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n            defaultValues:\n              _defaultValues as FormState<TFieldValues>['defaultValues'],\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFormSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues:\n        | TFieldValues\n        | TTransformedValues\n        | Record<string, never> = cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = cloneObject(values);\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          unset(fieldValues, name);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        if (keepStateOptions.keepFieldsRef) {\n          for (const fieldName of _names.mount) {\n            setValue(\n              fieldName as FieldPath<TFieldValues>,\n              get(values, fieldName),\n            );\n          }\n        } else {\n          _fields = {};\n        }\n      }\n\n      _formValues = _options.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? (cloneObject(_defaultValues) as TFieldValues)\n          : ({} as TFieldValues)\n        : (cloneObject(values) as TFieldValues);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n      defaultValues: _defaultValues as FormState<TFieldValues>['defaultValues'],\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "import type { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import type { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | readonly string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import type { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "export default () => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import type { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import { type ReactNode } from 'react';\n\nimport type { Control, FieldPath, FieldPathValue, FieldValues } from './types';\nimport { useWatch } from './useWatch';\n\ntype GetValues<\n  TFieldValues extends FieldValues,\n  TFieldNames extends readonly FieldPath<TFieldValues>[] = readonly [],\n> = TFieldNames extends readonly [\n  infer Name extends FieldPath<TFieldValues>,\n  ...infer RestFieldNames,\n]\n  ? RestFieldNames extends readonly FieldPath<TFieldValues>[]\n    ? readonly [\n        FieldPathValue<TFieldValues, Name>,\n        ...GetValues<TFieldValues, RestFieldNames>,\n      ]\n    : never\n  : TFieldNames extends readonly [infer Name extends FieldPath<TFieldValues>]\n    ? readonly [FieldPathValue<TFieldValues, Name>]\n    : TFieldNames extends readonly []\n      ? readonly []\n      : never;\n\nexport type WatchProps<\n  TFieldNames extends readonly FieldPath<TFieldValues>[],\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n> = {\n  control: Control<TFieldValues, TContext, TTransformedValues>;\n  names: TFieldNames;\n  render: (values: GetValues<TFieldValues, TFieldNames>) => ReactNode;\n};\n\n/**\n * Watch component that subscribes to form field changes and re-renders when watched fields update.\n *\n * @param control - The form control object from useForm\n * @param names - Array of field names to watch for changes\n * @param render - The function that receives watched values and returns ReactNode\n * @returns The result of calling render function with watched values\n *\n * @example\n * The `Watch` component only re-render when the values of `foo`, `bar`, and `baz.qux` change.\n * The types of `foo`, `bar`, and `baz.qux` are precisely inferred.\n *\n * ```tsx\n * const { control } = useForm();\n *\n * <Watch\n *   control={control}\n *   names={['foo', 'bar', 'baz.qux']}\n *   render={([foo, bar, baz_qux]) => <div>{foo}{bar}{baz_qux}</div>}\n * />\n * ```\n */\nexport const Watch = <\n  TFieldNames extends readonly FieldPath<TFieldValues>[],\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>({\n  control,\n  names,\n  render,\n}: WatchProps<TFieldNames, TFieldValues, TContext, TTransformedValues>) =>\n  render(\n    useWatch({ control, name: names }) as GetValues<TFieldValues, TFieldNames>,\n  );\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport type {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n\n  const _actioned = React.useRef(false);\n\n  control._names.array.add(name);\n\n  React.useMemo(\n    () =>\n      rules &&\n      fields.length >= 0 &&\n      (control as Control<TFieldValues, any, TTransformedValues>).register(\n        name as FieldPath<TFieldValues>,\n        rules as RegisterOptions<TFieldValues>,\n      ),\n    [control, name, fields.length, rules],\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === name || !fieldArrayName) {\n            const fieldValues = get(values, name);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control, name],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport type {\n  FieldValues,\n  FormState,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    if (props.formControl) {\n      _formControl.current = {\n        ...props.formControl,\n        formState,\n      };\n\n      if (props.defaultValues && !isFunction(props.defaultValues)) {\n        props.formControl.reset(props.defaultValues, props.resetOptions);\n      }\n    } else {\n      const { formControl, ...rest } = createFormControl(props);\n\n      _formControl.current = {\n        ...rest,\n        formState,\n      };\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, {\n        keepFieldsRef: true,\n        ...control._options.resetOptions,\n      });\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "isNameInFieldArray", "names", "name", "has", "substring", "search", "getNodeParentName", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Blob", "Object", "create", "getPrototypeOf", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isPlainObject", "key", "is<PERSON>ey", "test", "isUndefined", "val", "undefined", "compact", "filter", "Boolean", "stringToPath", "input", "replace", "split", "get", "object", "path", "defaultValue", "result", "reduce", "isBoolean", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "createContext", "displayName", "useFormContext", "useContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "defineProperty", "_key", "_proxyFormState", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useFormState", "props", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "useRef", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_subscribe", "current", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "isPrimitive", "deepEqual", "object1", "object2", "_internal_visited", "WeakSet", "getTime", "keys1", "keys", "keys2", "val1", "includes", "val2", "useWatch", "compute", "_defaultValue", "_compute", "_computeFormValues", "defaultValueMemo", "_getWatch", "updateValue", "values", "_formValues", "computedFormValues", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_previousNameRef", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "onChange", "useCallback", "onBlur", "ref", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "previousName", "unregister", "updateMounted", "mount", "_state", "action", "_setDisabledField", "flatten", "obj", "output", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observers", "next", "observer", "subscribe", "push", "unsubscribe", "o", "extractFormValues", "fieldsState", "fieldValue", "nestedFieldsState", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "live", "isConnected", "unset", "paths", "childObject", "updatePath", "slice", "baseGet", "isEmptyArray", "isTraversable", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "getDirty<PERSON>ields", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "isCheckBox", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "validate", "find", "validateFunction", "isWatched", "isBlurEvent", "some", "watchName", "startsWith", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "root", "pop", "updateFieldArrayRootError", "fieldArrayErrors", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "async", "disabled<PERSON>ieldN<PERSON>s", "shouldUseNativeValidation", "isFieldArray", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "inputValue", "inputRef", "isRadio", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "valueNumber", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "reValidateMode", "shouldFocusError", "createFormControl", "delayError<PERSON><PERSON><PERSON>", "submitCount", "isReady", "isSubmitted", "isSubmitting", "isSubmitSuccessful", "Set", "unMount", "timer", "_proxySubscribeFormState", "_subjects", "state", "shouldDisplayAllAssociatedErrors", "criteriaMode", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "_getDirty", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updateErrors", "wait", "clearTimeout", "setTimeout", "updatedFormState", "context", "getResolverOptions", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "getV<PERSON>ues", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "deps", "skipValidation", "watched", "readOnly", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "executeSchemaAndUpdateState", "Promise", "all", "shouldFocus", "config", "getFieldState", "setError", "currentError", "currentRef", "restOfErrorTree", "signalName", "currentName", "formStateData", "shouldRenderFormState", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "field<PERSON><PERSON><PERSON>", "size", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepFieldsRef", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "argA", "argB", "unsetEmptyArray", "_setErrors", "_getFieldArray", "_resetDefaultValues", "then", "resetOptions", "_disableForm", "payload", "reset<PERSON>ield", "clearErrors", "inputName", "setFocus", "shouldSelect", "formControl", "generateId", "crypto", "randomUUID", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeArrayAt", "indexes", "i", "temp", "removeAtIndexes", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "render", "mounted", "setMounted", "onSubmit", "children", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "response", "fetch", "String", "body", "status", "createElement", "Fragment", "noValidate", "Provider", "keyName", "setFields", "ids", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "existingError", "swap", "move", "prepend", "prependValue", "appendValue", "remove", "insertValue", "insertAt", "update", "item", "_formControl", "_values", "sub"], "mappings": "uRAEA,IAAAA,EAAgBC,GACG,aAAjBA,EAAQC,KCHVC,EAAgBC,GAAkCA,aAAiBC,KCAnEC,EAAgBF,GAAuD,MAATA,ECGvD,MAAMG,EAAgBH,GACV,iBAAVA,EAET,IAAAI,EAAkCJ,IAC/BE,EAAkBF,KAClBK,MAAMC,QAAQN,IACfG,EAAaH,KACZD,EAAaC,GCLhBO,EAAgBC,GACdJ,EAASI,IAAWA,EAAgBC,OAChCb,EAAiBY,EAAgBC,QAC9BD,EAAgBC,OAAOC,QACvBF,EAAgBC,OAAOT,MAC1BQ,ECNNG,EAAe,CAACC,EAA+BC,IAC7CD,EAAME,ICLO,CAACD,GACdA,EAAKE,UAAU,EAAGF,EAAKG,OAAO,iBAAmBH,EDIvCI,CAAkBJ,IEL9BK,EAAiC,oBAAXC,aACU,IAAvBA,OAAOC,aACM,oBAAbC,SCEK,SAAUC,EAAeC,GACrC,IAAIC,EACJ,MAAMlB,EAAUD,MAAMC,QAAQiB,GACxBE,EACgB,oBAAbC,UAA2BH,aAAgBG,SAEpD,GAAIH,aAAgBtB,KAClBuB,EAAO,IAAIvB,KAAKsB,OACX,IACHL,IAAUK,aAAgBI,MAAQF,KACnCnB,IAAWF,EAASmB,GAcrB,OAAOA,EAVP,GAFAC,EAAOlB,EAAU,GAAKsB,OAAOC,OAAOD,OAAOE,eAAeP,IAErDjB,GChBM,CAACyB,IACd,MAAMC,EACJD,EAAWE,aAAeF,EAAWE,YAAYC,UAEnD,OACE9B,EAAS4B,IAAkBA,EAAcG,eAAe,kBDWvCC,CAAcb,GAG7B,IAAK,MAAMc,KAAOd,EACZA,EAAKY,eAAeE,KACtBb,EAAKa,GAAOf,EAAYC,EAAKc,UAJjCb,EAAOD,CAUX,CAEA,OAAOC,CACT,CEhCA,IAAAc,EAAgBtC,GAAkB,QAAQuC,KAAKvC,GCA/CwC,EAAgBC,QAA2CC,IAARD,ECAnDE,EAAwB3C,GACtBK,MAAMC,QAAQN,GAASA,EAAM4C,OAAOC,SAAW,GCCjDC,EAAgBC,GACdJ,EAAQI,EAAMC,QAAQ,YAAa,IAAIC,MAAM,UCG/CC,EAAe,CACbC,EACAC,EACAC,KAEA,IAAKD,IAAShD,EAAS+C,GACrB,OAAOE,EAGT,MAAMC,GAAUhB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,IAAOG,OACzD,CAACD,EAAQjB,IACPnC,EAAkBoD,GAAUA,EAASA,EAAOjB,GAC9Cc,GAGF,OAAOX,EAAYc,IAAWA,IAAWH,EACrCX,EAAYW,EAAOC,IACjBC,EACAF,EAAOC,GACTE,GCzBNE,EAAgBxD,GAAsD,kBAAVA,ECM5DyD,EAAe,CACbN,EACAC,EACApD,KAEA,IAAI0D,GAAQ,EACZ,MAAMC,EAAWrB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,GAC/CQ,EAASD,EAASC,OAClBC,EAAYD,EAAS,EAE3B,OAASF,EAAQE,GAAQ,CACvB,MAAMvB,EAAMsB,EAASD,GACrB,IAAII,EAAW9D,EAEf,GAAI0D,IAAUG,EAAW,CACvB,MAAME,EAAWZ,EAAOd,GACxByB,EACE1D,EAAS2D,IAAa1D,MAAMC,QAAQyD,GAChCA,EACCC,OAAOL,EAASD,EAAQ,IAEvB,CAAA,EADA,EAEV,CAEA,GAAY,cAARrB,GAA+B,gBAARA,GAAiC,cAARA,EAClD,OAGFc,EAAOd,GAAOyB,EACdX,EAASA,EAAOd,EAClB,GCpCK,MAAM4B,EACL,OADKA,EAEA,WAFAA,EAGH,SAGGC,EACH,SADGA,EAED,WAFCA,EAGD,WAHCA,EAIA,YAJAA,EAKN,MAGMC,EACN,MADMA,EAEN,MAFMA,EAGA,YAHAA,EAIA,YAJAA,EAKF,UALEA,EAMD,WANCA,EAOD,WCjBNC,EAAkBC,EAAMC,cAAoC,MAClEF,EAAgBG,YAAc,kBAgCvB,MAAMC,EAAiB,IAK5BH,EAAMI,WAAWL,GCvCnB,IAAAM,EAAe,CAKbC,EACAC,EACAC,EACAC,GAAS,KAET,MAAMxB,EAAS,CACbyB,cAAeH,EAAQI,gBAGzB,IAAK,MAAM3C,KAAOsC,EAChB/C,OAAOqD,eAAe3B,EAAQjB,EAAK,CACjCa,IAAK,KACH,MAAMgC,EAAO7C,EAOb,OALIuC,EAAQO,gBAAgBD,KAAUhB,IACpCU,EAAQO,gBAAgBD,IAASJ,GAAUZ,GAG7CW,IAAwBA,EAAoBK,IAAQ,GAC7CP,EAAUO,MAKvB,OAAO5B,GC9BF,MAAM8B,EACO,oBAAXjE,OAAyBkD,EAAMgB,gBAAkBhB,EAAMiB,UCsC1D,SAAUC,EAIdC,GAEA,MAAMC,EAAUjB,KACVI,QAAEA,EAAUa,EAAQb,QAAOc,SAAEA,EAAQ7E,KAAEA,EAAI8E,MAAEA,GAAUH,GAAS,CAAA,GAC/Db,EAAWiB,GAAmBvB,EAAMwB,SAASjB,EAAQkB,YACtDC,EAAuB1B,EAAM2B,OAAO,CACxCC,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,SAAS,EACTC,QAAQ,IAwBV,OArBApB,EACE,IACER,EAAQ6B,WAAW,CACjB5F,OACA8D,UAAWoB,EAAqBW,QAChCf,QACAgB,SAAWhC,KACRe,GACCE,EAAgB,IACXhB,EAAQkB,cACRnB,OAIb,CAAC9D,EAAM6E,EAAUC,IAGnBtB,EAAMiB,UAAU,KACdS,EAAqBW,QAAQH,SAAW3B,EAAQgC,WAAU,IACzD,CAAChC,IAEGP,EAAMwC,QACX,IACEnC,EACEC,EACAC,EACAmB,EAAqBW,SACrB,GAEJ,CAAC/B,EAAWC,GAEhB,CC5FA,IAAAkC,EAAgB9G,GAAqD,iBAAVA,ECI3D+G,EAAe,CACbnG,EACAoG,EACAC,EACAC,EACA7D,IAEIyD,EAASlG,IACXsG,GAAYF,EAAOG,MAAMC,IAAIxG,GACtBsC,EAAI+D,EAAYrG,EAAOyC,IAG5BhD,MAAMC,QAAQM,GACTA,EAAMyG,IACVC,IACCJ,GAAYF,EAAOG,MAAMC,IAAIE,GAC7BpE,EAAI+D,EAAYK,MAKtBJ,IAAaF,EAAOO,UAAW,GAExBN,GCtBTO,EAAgBxH,GACdE,EAAkBF,KAAWG,EAAaH,GCD9B,SAAUyH,EACtBC,EACAC,EACAC,EAAoB,IAAIC,SAExB,GAAIL,EAAYE,IAAYF,EAAYG,GACtC,OAAOD,IAAYC,EAGrB,GAAI5H,EAAa2H,IAAY3H,EAAa4H,GACxC,OAAOD,EAAQI,YAAcH,EAAQG,UAGvC,MAAMC,EAAQnG,OAAOoG,KAAKN,GACpBO,EAAQrG,OAAOoG,KAAKL,GAE1B,GAAII,EAAMnE,SAAWqE,EAAMrE,OACzB,OAAO,EAGT,GAAIgE,EAAkB9G,IAAI4G,IAAYE,EAAkB9G,IAAI6G,GAC1D,OAAO,EAETC,EAAkBR,IAAIM,GACtBE,EAAkBR,IAAIO,GAEtB,IAAK,MAAMtF,KAAO0F,EAAO,CACvB,MAAMG,EAAOR,EAAQrF,GAErB,IAAK4F,EAAME,SAAS9F,GAClB,OAAO,EAGT,GAAY,QAARA,EAAe,CACjB,MAAM+F,EAAOT,EAAQtF,GAErB,GACGtC,EAAamI,IAASnI,EAAaqI,IACnChI,EAAS8H,IAAS9H,EAASgI,IAC3B/H,MAAMC,QAAQ4H,IAAS7H,MAAMC,QAAQ8H,IACjCX,EAAUS,EAAME,EAAMR,GACvBM,IAASE,EAEb,OAAO,CAEX,CACF,CAEA,OAAO,CACT,CCyMM,SAAUC,EACd7C,GAEA,MAAMC,EAAUjB,KACVI,QACJA,EAAUa,EAAQb,QAAO/D,KACzBA,EAAIwC,aACJA,EAAYqC,SACZA,EAAQC,MACRA,EAAK2C,QACLA,GACE9C,GAAS,CAAA,EACP+C,EAAgBlE,EAAM2B,OAAO3C,GAC7BmF,EAAWnE,EAAM2B,OAAOsC,GACxBG,EAAqBpE,EAAM2B,YAAOtD,GAExC8F,EAAS9B,QAAU4B,EAEnB,MAAMI,EAAmBrE,EAAMwC,QAC7B,IACEjC,EAAQ+D,UACN9H,EACA0H,EAAc7B,SAElB,CAAC9B,EAAS/D,KAGLb,EAAO4I,GAAevE,EAAMwB,SACjC2C,EAAS9B,QAAU8B,EAAS9B,QAAQgC,GAAoBA,GAuC1D,OApCAtD,EACE,IACER,EAAQ6B,WAAW,CACjB5F,OACA8D,UAAW,CACTkE,QAAQ,GAEVlD,QACAgB,SAAWhC,IACT,IAAKe,EAAU,CACb,MAAMuB,EAAaF,EACjBlG,EACA+D,EAAQoC,OACRrC,EAAUkE,QAAUjE,EAAQkE,aAC5B,EACAP,EAAc7B,SAGhB,GAAI8B,EAAS9B,QAAS,CACpB,MAAMqC,EAAqBP,EAAS9B,QAAQO,GAEvCQ,EAAUsB,EAAoBN,EAAmB/B,WACpDkC,EAAYG,GACZN,EAAmB/B,QAAUqC,EAEjC,MACEH,EAAY3B,EAEhB,KAGN,CAACrC,EAASc,EAAU7E,EAAM8E,IAG5BtB,EAAMiB,UAAU,IAAMV,EAAQoE,oBAEvBhJ,CACT,CCnRM,SAAUiJ,EAKdzD,GAEA,MAAMC,EAAUjB,KACV3D,KACJA,EAAI6E,SACJA,EAAQd,QACRA,EAAUa,EAAQb,QAAOsE,iBACzBA,EAAgB7F,aAChBA,GACEmC,EACE2D,EAAexI,EAAmBiE,EAAQoC,OAAOoC,MAAOvI,GAExD6H,EAAmBrE,EAAMwC,QAC7B,IACE3D,EACE0B,EAAQkE,YACRjI,EACAqC,EAAI0B,EAAQI,eAAgBnE,EAAMwC,IAEtC,CAACuB,EAAS/D,EAAMwC,IAGZrD,EAAQqI,EAAS,CACrBzD,UACA/D,OACAwC,aAAcqF,EACd/C,OAAO,IAGHhB,EAAYY,EAAa,CAC7BX,UACA/D,OACA8E,OAAO,IAGH0D,EAAShF,EAAM2B,OAAOR,GAEtB8D,EAAmBjF,EAAM2B,YAA2BtD,GAEpD6G,EAAiBlF,EAAM2B,OAC3BpB,EAAQ4E,SAAS3I,EAAM,IAClB2E,EAAMiE,MACTzJ,WACIwD,EAAUgC,EAAME,UAAY,CAAEA,SAAUF,EAAME,UAAa,MAInE2D,EAAO3C,QAAUlB,EAEjB,MAAMkE,EAAarF,EAAMwC,QACvB,IACEjF,OAAO+H,iBACL,GACA,CACEC,QAAS,CACPC,YAAY,EACZ3G,IAAK,MAAQA,EAAIyB,EAAU6B,OAAQ3F,IAErCoF,QAAS,CACP4D,YAAY,EACZ3G,IAAK,MAAQA,EAAIyB,EAAUwB,YAAatF,IAE1CiJ,UAAW,CACTD,YAAY,EACZ3G,IAAK,MAAQA,EAAIyB,EAAUyB,cAAevF,IAE5CyF,aAAc,CACZuD,YAAY,EACZ3G,IAAK,MAAQA,EAAIyB,EAAU0B,iBAAkBxF,IAE/CkJ,MAAO,CACLF,YAAY,EACZ3G,IAAK,IAAMA,EAAIyB,EAAU6B,OAAQ3F,MAIzC,CAAC8D,EAAW9D,IAGRmJ,EAAW3F,EAAM4F,YACpBzJ,GACC+I,EAAe7C,QAAQsD,SAAS,CAC9BvJ,OAAQ,CACNT,MAAOO,EAAcC,GACrBK,KAAMA,GAERf,KAAMmE,IAEV,CAACpD,IAGGqJ,EAAS7F,EAAM4F,YACnB,IACEV,EAAe7C,QAAQwD,OAAO,CAC5BzJ,OAAQ,CACNT,MAAOkD,EAAI0B,EAAQkE,YAAajI,GAChCA,KAAMA,GAERf,KAAMmE,IAEV,CAACpD,EAAM+D,EAAQkE,cAGXqB,EAAM9F,EAAM4F,YACfG,IACC,MAAMC,EAAQnH,EAAI0B,EAAQ0F,QAASzJ,GAE/BwJ,GAASD,IACXC,EAAME,GAAGJ,IAAM,CACbK,MAAO,IAAMJ,EAAII,OAASJ,EAAII,QAC9BC,OAAQ,IAAML,EAAIK,QAAUL,EAAIK,SAChCC,kBAAoBC,GAClBP,EAAIM,kBAAkBC,GACxBC,eAAgB,IAAMR,EAAIQ,oBAIhC,CAAChG,EAAQ0F,QAASzJ,IAGdwJ,EAAQhG,EAAMwC,QAClB,KAAA,CACEhG,OACAb,WACIwD,EAAUkC,IAAaf,EAAUe,SACjC,CAAEA,SAAUf,EAAUe,UAAYA,GAClC,GACJsE,WACAE,SACAC,QAEF,CAACtJ,EAAM6E,EAAUf,EAAUe,SAAUsE,EAAUE,EAAQC,EAAKnK,IA6D9D,OA1DAqE,EAAMiB,UAAU,KACd,MAAMuF,EACJjG,EAAQkG,SAAS5B,kBAAoBA,EACjC6B,EAAezB,EAAiB5C,QAElCqE,GAAgBA,IAAiBlK,IAASsI,GAC5CvE,EAAQoG,WAAWD,GAGrBnG,EAAQ4E,SAAS3I,EAAM,IAClBwI,EAAO3C,QAAQ+C,SACdjG,EAAU6F,EAAO3C,QAAQhB,UACzB,CAAEA,SAAU2D,EAAO3C,QAAQhB,UAC3B,KAGN,MAAMuF,EAAgB,CAACpK,EAAyBb,KAC9C,MAAMqK,EAAenH,EAAI0B,EAAQ0F,QAASzJ,GAEtCwJ,GAASA,EAAME,KACjBF,EAAME,GAAGW,MAAQlL,IAMrB,GAFAiL,EAAcpK,GAAM,GAEhBgK,EAAwB,CAC1B,MAAM7K,EAAQsB,EACZ4B,EAAI0B,EAAQkG,SAAS/F,cAAelE,EAAMwI,EAAO3C,QAAQrD,eAE3DI,EAAImB,EAAQI,eAAgBnE,EAAMb,GAC9BwC,EAAYU,EAAI0B,EAAQkE,YAAajI,KACvC4C,EAAImB,EAAQkE,YAAajI,EAAMb,EAEnC,CAMA,OAJCmJ,GAAgBvE,EAAQ4E,SAAS3I,GAElCyI,EAAiB5C,QAAU7F,EAEpB,MAEHsI,EACI0B,IAA2BjG,EAAQuG,OAAOC,OAC1CP,GAEFjG,EAAQoG,WAAWnK,GACnBoK,EAAcpK,GAAM,KAEzB,CAACA,EAAM+D,EAASuE,EAAcD,IAEjC7E,EAAMiB,UAAU,KACdV,EAAQyG,kBAAkB,CACxB3F,WACA7E,UAED,CAAC6E,EAAU7E,EAAM+D,IAEbP,EAAMwC,QACX,KAAA,CACEwD,QACA1F,YACA+E,eAEF,CAACW,EAAO1F,EAAW+E,GAEvB,CChNA,MCzCa4B,EAAWC,IACtB,MAAMC,EAAsB,CAAA,EAE5B,IAAK,MAAMnJ,KAAOT,OAAOoG,KAAKuD,GAC5B,GAAIpL,EAAaoL,EAAIlJ,KAAsB,OAAbkJ,EAAIlJ,GAAe,CAC/C,MAAMoJ,EAASH,EAAQC,EAAIlJ,IAE3B,IAAK,MAAMqJ,KAAa9J,OAAOoG,KAAKyD,GAClCD,EAAO,GAAGnJ,KAAOqJ,KAAeD,EAAOC,EAE3C,MACEF,EAAOnJ,GAAOkJ,EAAIlJ,GAItB,OAAOmJ,GCbHG,EAAe,OCArB,IAAAC,EAAe,CACb/K,EACAgL,EACArF,EACA1G,EACA6K,IAEAkB,EACI,IACKrF,EAAO3F,GACViL,MAAO,IACDtF,EAAO3F,IAAS2F,EAAO3F,GAAOiL,MAAQtF,EAAO3F,GAAOiL,MAAQ,CAAA,EAChEhM,CAACA,GAAO6K,IAAW,IAGvB,CAAA,ECrBNoB,EAAmB/L,GAAcK,MAAMC,QAAQN,GAASA,EAAQ,CAACA,GCgBjEgM,EAAe,KACb,IAAIC,EAA4B,GAqBhC,MAAO,CACL,aAAIC,GACF,OAAOD,CACT,EACAE,KAvBYnM,IACZ,IAAK,MAAMoM,KAAYH,EACrBG,EAASD,MAAQC,EAASD,KAAKnM,IAsBjCqM,UAlBiBD,IACjBH,EAAWK,KAAKF,GACT,CACLG,YAAa,KACXN,EAAaA,EAAWrJ,OAAQ4J,GAAMA,IAAMJ,MAehDG,YAVkB,KAClBN,EAAa,MCjCH,SAAUQ,EAGtBC,EAAgBzF,GAChB,MAAM4B,EAAkC,CAAA,EAExC,IAAK,MAAMxG,KAAOqK,EAChB,GAAIA,EAAYvK,eAAeE,GAAM,CACnC,MAAMqH,EAAagD,EAAYrK,GACzBsK,EAAa1F,EAAW5E,GAE9B,GAAIqH,GAActJ,EAASsJ,IAAeiD,EAAY,CACpD,MAAMC,EAAoBH,EACxB/C,EACAiD,GAGEvM,EAASwM,KACX/D,EAAOxG,GAAOuK,EAElB,MAAWF,EAAYrK,KACrBwG,EAAOxG,GAAOsK,EAElB,CAGF,OAAO9D,CACT,CCzBA,IAAAgE,EAAgB7M,GACdI,EAASJ,KAAW4B,OAAOoG,KAAKhI,GAAO4D,OCHzCkJ,EAAgBjN,GACG,SAAjBA,EAAQC,KCHViN,EAAgB/M,GACG,mBAAVA,ECCTgN,EAAgBhN,IACd,IAAKkB,EACH,OAAO,EAGT,MAAM+L,EAAQjN,EAAUA,EAAsBkN,cAA6B,EAC3E,OACElN,aACCiN,GAASA,EAAME,YAAcF,EAAME,YAAY/L,YAAcA,cCRlEgM,GAAgBvN,GACG,oBAAjBA,EAAQC,KCDVuN,GAAgBxN,GACG,UAAjBA,EAAQC,KCCVwN,GAAgBnD,GAAa6C,EAAc7C,IAAQA,EAAIoD,YCsBzC,SAAUC,GAAMrK,EAAaC,GACzC,MAAMqK,EAAQpN,MAAMC,QAAQ8C,GACxBA,EACAd,EAAMc,GACJ,CAACA,GACDN,EAAaM,GAEbsK,EAA+B,IAAjBD,EAAM7J,OAAeT,EA3B3C,SAAiBA,EAAawK,GAC5B,MAAM/J,EAAS+J,EAAWC,MAAM,GAAG,GAAIhK,OACvC,IAAIF,EAAQ,EAEZ,KAAOA,EAAQE,GACbT,EAASX,EAAYW,GAAUO,IAAUP,EAAOwK,EAAWjK,MAG7D,OAAOP,CACT,CAkBoD0K,CAAQ1K,EAAQsK,GAE5D/J,EAAQ+J,EAAM7J,OAAS,EACvBvB,EAAMoL,EAAM/J,GAclB,OAZIgK,UACKA,EAAYrL,GAIT,IAAVqB,IACEtD,EAASsN,IAAgBb,EAAca,IACtCrN,MAAMC,QAAQoN,IA5BrB,SAAsBnC,GACpB,IAAK,MAAMlJ,KAAOkJ,EAChB,GAAIA,EAAIpJ,eAAeE,KAASG,EAAY+I,EAAIlJ,IAC9C,OAAO,EAGX,OAAO,CACT,CAqBqCyL,CAAaJ,KAE9CF,GAAMrK,EAAQsK,EAAMG,MAAM,GAAG,IAGxBzK,CACT,CC5CA,SAAS4K,GAAc/N,GACrB,OAAOK,MAAMC,QAAQN,IAAWI,EAASJ,KCN5B,CAAIuB,IACjB,IAAK,MAAMc,KAAOd,EAChB,GAAIwL,EAAWxL,EAAKc,IAClB,OAAO,EAGX,OAAO,GDA6C2L,CAAkBhO,EACxE,CAEA,SAASiO,GAAmB1M,EAAS2M,EAA8B,IACjE,IAAK,MAAM7L,KAAOd,EACZwM,GAAcxM,EAAKc,KACrB6L,EAAO7L,GAAOhC,MAAMC,QAAQiB,EAAKc,IAAQ,GAAK,CAAA,EAC9C4L,GAAgB1M,EAAKc,GAAM6L,EAAO7L,KACxBG,EAAYjB,EAAKc,MAC3B6L,EAAO7L,IAAO,GAIlB,OAAO6L,CACT,CAEc,SAAUC,GACtB5M,EACA0F,EACAmH,GAKKA,IACHA,EAAwBH,GAAgBhH,IAG1C,IAAK,MAAM5E,KAAOd,EACZwM,GAAcxM,EAAKc,IACjBG,EAAYyE,IAAeO,EAAY4G,EAAsB/L,IAC/D+L,EAAsB/L,GAAO4L,GAC3B1M,EAAKc,GACLhC,MAAMC,QAAQiB,EAAKc,IAAQ,GAAK,CAAA,GAGlC8L,GACE5M,EAAKc,GACLnC,EAAkB+G,GAAc,CAAA,EAAKA,EAAW5E,GAChD+L,EAAsB/L,IAI1B+L,EAAsB/L,IAAQoF,EAAUlG,EAAKc,GAAM4E,EAAW5E,IAIlE,OAAO+L,CACT,CEjDA,MAAMC,GAAqC,CACzCrO,OAAO,EACPuG,SAAS,GAGL+H,GAAc,CAAEtO,OAAO,EAAMuG,SAAS,GAE5C,IAAAgI,GAAgBC,IACd,GAAInO,MAAMC,QAAQkO,GAAU,CAC1B,GAAIA,EAAQ5K,OAAS,EAAG,CACtB,MAAMiF,EAAS2F,EACZ5L,OAAQ6L,GAAWA,GAAUA,EAAO/N,UAAY+N,EAAO/I,UACvD2B,IAAKoH,GAAWA,EAAOzO,OAC1B,MAAO,CAAEA,MAAO6I,EAAQtC,UAAWsC,EAAOjF,OAC5C,CAEA,OAAO4K,EAAQ,GAAG9N,UAAY8N,EAAQ,GAAG9I,SAErC8I,EAAQ,GAAGE,aAAelM,EAAYgM,EAAQ,GAAGE,WAAW1O,OAC1DwC,EAAYgM,EAAQ,GAAGxO,QAA+B,KAArBwO,EAAQ,GAAGxO,MAC1CsO,GACA,CAAEtO,MAAOwO,EAAQ,GAAGxO,MAAOuG,SAAS,GACtC+H,GACFD,EACN,CAEA,OAAOA,IC7BTM,GAAe,CACb3O,GACE4O,gBAAeC,cAAaC,gBAE9BtM,EAAYxC,GACRA,EACA4O,EACY,KAAV5O,EACE+O,IACA/O,GACGA,EACDA,EACJ6O,GAAe/H,EAAS9G,GACtB,IAAIC,KAAKD,GACT8O,EACEA,EAAW9O,GACXA,ECfZ,MAAMgP,GAAkC,CACtCzI,SAAS,EACTvG,MAAO,MAGT,IAAAiP,GAAgBT,GACdnO,MAAMC,QAAQkO,GACVA,EAAQjL,OACN,CAAC2L,EAAUT,IACTA,GAAUA,EAAO/N,UAAY+N,EAAO/I,SAChC,CACEa,SAAS,EACTvG,MAAOyO,EAAOzO,OAEhBkP,EACNF,IAEFA,GCXQ,SAAUG,GAAc5E,GACpC,MAAMJ,EAAMI,EAAGJ,IAEf,OAAI2C,EAAY3C,GACPA,EAAIiF,MAGT/B,GAAalD,GACR8E,GAAc1E,EAAG8E,MAAMrP,MAG5BoN,GAAiBjD,GACZ,IAAIA,EAAImF,iBAAiBjI,IAAI,EAAGrH,WAAYA,GAGjDuP,EAAWpF,GACNoE,GAAiBhE,EAAG8E,MAAMrP,MAG5B2O,GAAgBnM,EAAY2H,EAAInK,OAASuK,EAAGJ,IAAInK,MAAQmK,EAAInK,MAAOuK,EAC5E,CCpBA,ICXAiF,GAAgBxP,GAAoCA,aAAiByP,OCSrEC,GACEC,GAEAnN,EAAYmN,GACRA,EACAH,GAAQG,GACNA,EAAKC,OACLxP,EAASuP,GACPH,GAAQG,EAAK3P,OACX2P,EAAK3P,MAAM4P,OACXD,EAAK3P,MACP2P,ECjBVE,GAAgBC,IAAW,CACzBC,YAAaD,GAAQA,IAAS5L,EAC9B8L,SAAUF,IAAS5L,EACnB+L,WAAYH,IAAS5L,EACrBgM,QAASJ,IAAS5L,EAClBiM,UAAWL,IAAS5L,ICJtB,MAAMkM,GAAiB,gBAEvB,IAAAC,GAAgBC,KACZA,KACAA,EAAeC,aAEdxD,EAAWuD,EAAeC,WACzBD,EAAeC,SAAStO,YAAYpB,OAASuP,IAC9ChQ,EAASkQ,EAAeC,WACvB3O,OAAOiH,OAAOyH,EAAeC,UAAUC,KACpCC,GACCA,EAAiBxO,YAAYpB,OAASuP,KCbhDM,GAAe,CACb7P,EACAmG,EACA2J,KAECA,IACA3J,EAAOO,UACNP,EAAOG,MAAMrG,IAAID,IACjB,IAAImG,EAAOG,OAAOyJ,KACfC,GACChQ,EAAKiQ,WAAWD,IAChB,SAAStO,KAAK1B,EAAK+M,MAAMiD,EAAUjN,WCT3C,MAAMmN,GAAwB,CAC5B7C,EACA9C,EACA4F,EACAC,KAEA,IAAK,MAAM5O,KAAO2O,GAAepP,OAAOoG,KAAKkG,GAAS,CACpD,MAAM7D,EAAQnH,EAAIgL,EAAQ7L,GAE1B,GAAIgI,EAAO,CACT,MAAME,GAAEA,KAAO2G,GAAiB7G,EAEhC,GAAIE,EAAI,CACN,GAAIA,EAAG8E,MAAQ9E,EAAG8E,KAAK,IAAMjE,EAAOb,EAAG8E,KAAK,GAAIhN,KAAS4O,EACvD,OAAO,EACF,GAAI1G,EAAGJ,KAAOiB,EAAOb,EAAGJ,IAAKI,EAAG1J,QAAUoQ,EAC/C,OAAO,EAEP,GAAIF,GAAsBG,EAAc9F,GACtC,KAGN,MAAO,GAAIhL,EAAS8Q,IACdH,GAAsBG,EAA2B9F,GACnD,KAGN,CACF,GC5BY,SAAU+F,GACtB3K,EACA8D,EACAzJ,GAKA,MAAMkJ,EAAQ7G,EAAIsD,EAAQ3F,GAE1B,GAAIkJ,GAASzH,EAAMzB,GACjB,MAAO,CACLkJ,QACAlJ,QAIJ,MAAMD,EAAQC,EAAKoC,MAAM,KAEzB,KAAOrC,EAAMgD,QAAQ,CACnB,MAAM0D,EAAY1G,EAAMwQ,KAAK,KACvB/G,EAAQnH,EAAIoH,EAAShD,GACrB+J,EAAanO,EAAIsD,EAAQc,GAE/B,GAAI+C,IAAUhK,MAAMC,QAAQ+J,IAAUxJ,IAASyG,EAC7C,MAAO,CAAEzG,QAGX,GAAIwQ,GAAcA,EAAWvR,KAC3B,MAAO,CACLe,KAAMyG,EACNyC,MAAOsH,GAIX,GAAIA,GAAcA,EAAWC,MAAQD,EAAWC,KAAKxR,KACnD,MAAO,CACLe,KAAM,GAAGyG,SACTyC,MAAOsH,EAAWC,MAItB1Q,EAAM2Q,KACR,CAEA,MAAO,CACL1Q,OAEJ,CC3CA,ICCA2Q,GAAe,CACbhL,EACAuD,EACAlJ,KAEA,MAAM4Q,EAAmB1F,EAAsB7I,EAAIsD,EAAQ3F,IAG3D,OAFA4C,EAAIgO,EAAkB,OAAQ1H,EAAMlJ,IACpC4C,EAAI+C,EAAQ3F,EAAM4Q,GACXjL,GCdK,SAAUkL,GACtBpO,EACA6G,EACArK,EAAO,YAEP,GACEgH,EAASxD,IACRjD,MAAMC,QAAQgD,IAAWA,EAAOqO,MAAM7K,IACtCtD,EAAUF,KAAYA,EAEvB,MAAO,CACLxD,OACA6K,QAAS7D,EAASxD,GAAUA,EAAS,GACrC6G,MAGN,CChBA,IAAAyH,GAAgBC,GACdzR,EAASyR,KAAoBrC,GAAQqC,GACjCA,EACA,CACE7R,MAAO6R,EACPlH,QAAS,ICuBjBmH,GAAeC,MACb1H,EACA2H,EACA/K,EACA4E,EACAoG,EACAC,KAEA,MAAM/H,IACJA,EAAGkF,KACHA,EAAI8C,SACJA,EAAQC,UACRA,EAASC,UACTA,EAASC,IACTA,EAAGC,IACHA,EAAGC,QACHA,EAAOjC,SACPA,EAAQ1P,KACRA,EAAI+N,cACJA,EAAa1D,MACbA,GACEb,EAAME,GACJkI,EAA+BvP,EAAI+D,EAAYpG,GACrD,IAAKqK,GAAS8G,EAAmBlR,IAAID,GACnC,MAAO,CAAA,EAET,MAAM6R,EAA6BrD,EAAOA,EAAK,GAAMlF,EAC/CO,EAAqBC,IACrBsH,GAA6BS,EAAS9H,iBACxC8H,EAAShI,kBAAkBlH,EAAUmH,GAAW,GAAKA,GAAW,IAChE+H,EAAS9H,mBAGPb,EAA6B,CAAA,EAC7B4I,EAAUtF,GAAalD,GACvBoF,EAAa3P,EAAgBuK,GAC7ByI,EAAoBD,GAAWpD,EAC/BsD,GACFjE,GAAiB9B,EAAY3C,KAC7B3H,EAAY2H,EAAInK,QAChBwC,EAAYiQ,IACbzF,EAAc7C,IAAsB,KAAdA,EAAInK,OACZ,KAAfyS,GACCpS,MAAMC,QAAQmS,KAAgBA,EAAW7O,OACtCkP,EAAoBlH,EAAamH,KACrC,KACAlS,EACAgL,EACA9B,GAEIiJ,EAAmB,CACvBC,EACAC,EACAC,EACAC,EAAmBjP,EACnBkP,EAAmBlP,KAEnB,MAAMwG,EAAUsI,EAAYC,EAAmBC,EAC/CpJ,EAAMlJ,GAAQ,CACZf,KAAMmT,EAAYG,EAAUC,EAC5B1I,UACAR,SACG2I,EAAkBG,EAAYG,EAAUC,EAAS1I,KAIxD,GACEuH,GACK7R,MAAMC,QAAQmS,KAAgBA,EAAW7O,OAC1CuO,KACGS,IAAsBC,GAAW3S,EAAkBuS,KACnDjP,EAAUiP,KAAgBA,GAC1BlD,IAAehB,GAAiBc,GAAM9I,SACtCoM,IAAY1D,GAAcI,GAAM9I,SACvC,CACA,MAAMvG,MAAEA,EAAK2K,QAAEA,GAAY7D,EAASqL,GAChC,CAAEnS,QAASmS,EAAUxH,QAASwH,GAC9BP,GAAmBO,GAEvB,GAAInS,IACF+J,EAAMlJ,GAAQ,CACZf,KAAMqE,EACNwG,UACAR,IAAKuI,KACFI,EAAkB3O,EAAiCwG,KAEnDkB,GAEH,OADAnB,EAAkBC,GACXZ,CAGb,CAEA,KAAK8I,GAAa3S,EAAkBoS,IAASpS,EAAkBqS,IAAO,CACpE,IAAIU,EACAK,EACJ,MAAMC,EAAY3B,GAAmBW,GAC/BiB,EAAY5B,GAAmBU,GAErC,GAAKpS,EAAkBuS,IAAgBzO,MAAMyO,GAUtC,CACL,MAAMgB,EACHtJ,EAAyB0E,aAAe,IAAI5O,KAAKwS,GAC9CiB,EAAqBC,GACzB,IAAI1T,MAAK,IAAIA,MAAO2T,eAAiB,IAAMD,GACvCE,EAAqB,QAAZ1J,EAAIrK,KACbgU,EAAqB,QAAZ3J,EAAIrK,KAEfgH,EAASyM,EAAUvT,QAAUyS,IAC/BQ,EAAYY,EACRH,EAAkBjB,GAAciB,EAAkBH,EAAUvT,OAC5D8T,EACErB,EAAac,EAAUvT,MACvByT,EAAY,IAAIxT,KAAKsT,EAAUvT,QAGnC8G,EAAS0M,EAAUxT,QAAUyS,IAC/Ba,EAAYO,EACRH,EAAkBjB,GAAciB,EAAkBF,EAAUxT,OAC5D8T,EACErB,EAAae,EAAUxT,MACvByT,EAAY,IAAIxT,KAAKuT,EAAUxT,OAEzC,KAjCoE,CAClE,MAAM+T,EACH5J,EAAyByE,gBACzB6D,GAAcA,EAAaA,GACzBvS,EAAkBqT,EAAUvT,SAC/BiT,EAAYc,EAAcR,EAAUvT,OAEjCE,EAAkBsT,EAAUxT,SAC/BsT,EAAYS,EAAcP,EAAUxT,MAExC,CAyBA,IAAIiT,GAAaK,KACfN,IACIC,EACFM,EAAU5I,QACV6I,EAAU7I,QACVxG,EACAA,IAEG0H,GAEH,OADAnB,EAAkBX,EAAMlJ,GAAO8J,SACxBZ,CAGb,CAEA,IACGqI,GAAaC,KACbQ,IACA/L,EAAS2L,IAAgBP,GAAgB7R,MAAMC,QAAQmS,IACxD,CACA,MAAMuB,EAAkBpC,GAAmBQ,GACrC6B,EAAkBrC,GAAmBS,GACrCY,GACH/S,EAAkB8T,EAAgBhU,QACnCyS,EAAW7O,QAAUoQ,EAAgBhU,MACjCsT,GACHpT,EAAkB+T,EAAgBjU,QACnCyS,EAAW7O,QAAUqQ,EAAgBjU,MAEvC,IAAIiT,GAAaK,KACfN,EACEC,EACAe,EAAgBrJ,QAChBsJ,EAAgBtJ,UAEbkB,GAEH,OADAnB,EAAkBX,EAAMlJ,GAAO8J,SACxBZ,CAGb,CAEA,GAAIyI,IAAYK,GAAW/L,EAAS2L,GAAa,CAC/C,MAAQzS,MAAOkU,EAAYvJ,QAAEA,GAAYiH,GAAmBY,GAE5D,GAAIhD,GAAQ0E,KAAkBzB,EAAW0B,MAAMD,KAC7CnK,EAAMlJ,GAAQ,CACZf,KAAMqE,EACNwG,UACAR,SACG2I,EAAkB3O,EAAgCwG,KAElDkB,GAEH,OADAnB,EAAkBC,GACXZ,CAGb,CAEA,GAAIwG,EACF,GAAIxD,EAAWwD,GAAW,CACxB,MACM6D,EAAgB1C,SADDnB,EAASkC,EAAYxL,GACKyL,GAE/C,GAAI0B,IACFrK,EAAMlJ,GAAQ,IACTuT,KACAtB,EACD3O,EACAiQ,EAAczJ,WAGbkB,GAEH,OADAnB,EAAkB0J,EAAczJ,SACzBZ,CAGb,MAAO,GAAI3J,EAASmQ,GAAW,CAC7B,IAAI8D,EAAmB,CAAA,EAEvB,IAAK,MAAMhS,KAAOkO,EAAU,CAC1B,IAAK1D,EAAcwH,KAAsBxI,EACvC,MAGF,MAAMuI,EAAgB1C,SACdnB,EAASlO,GAAKoQ,EAAYxL,GAChCyL,EACArQ,GAGE+R,IACFC,EAAmB,IACdD,KACAtB,EAAkBzQ,EAAK+R,EAAczJ,UAG1CD,EAAkB0J,EAAczJ,SAE5BkB,IACF9B,EAAMlJ,GAAQwT,GAGpB,CAEA,IAAKxH,EAAcwH,KACjBtK,EAAMlJ,GAAQ,CACZsJ,IAAKuI,KACF2B,IAEAxI,GACH,OAAO9B,CAGb,CAIF,OADAW,GAAkB,GACXX,GChMT,MAAMuK,GAAiB,CACrBxE,KAAM5L,EACNqQ,eAAgBrQ,EAChBsQ,kBAAkB,GAGd,SAAUC,GAKdjP,EAAkE,IAUlE,IAwCIkP,EAxCA5J,EAAW,IACVwJ,MACA9O,GAEDM,EAAsC,CACxC6O,YAAa,EACb1O,SAAS,EACT2O,SAAS,EACT1O,UAAW6G,EAAWjC,EAAS/F,eAC/BuB,cAAc,EACduO,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBxO,SAAS,EACTH,cAAe,CAAA,EACfD,YAAa,CAAA,EACbE,iBAAkB,CAAA,EAClBG,OAAQsE,EAAStE,QAAU,CAAA,EAC3Bd,SAAUoF,EAASpF,WAAY,GAE7B4E,EAAqB,CAAA,EACrBtF,GACF5E,EAAS0K,EAAS/F,gBAAkB3E,EAAS0K,EAASjC,UAClDvH,EAAYwJ,EAAS/F,eAAiB+F,EAASjC,SAC/C,CAAA,EACFC,EAAcgC,EAAS5B,iBACtB,CAAA,EACA5H,EAAY0D,GACbmG,EAAS,CACXC,QAAQ,EACRF,OAAO,EACP/D,OAAO,GAELH,EAAgB,CAClBkE,MAAO,IAAI8J,IACXtP,SAAU,IAAIsP,IACdC,QAAS,IAAID,IACb5L,MAAO,IAAI4L,IACX7N,MAAO,IAAI6N,KAGTE,EAAQ,EACZ,MAAM/P,EAAiC,CACrCc,SAAS,EACTE,aAAa,EACbE,kBAAkB,EAClBD,eAAe,EACfE,cAAc,EACdC,SAAS,EACTC,QAAQ,GAEV,IAAI2O,EAA2B,IAC1BhQ,GAEL,MAAMiQ,EAAoC,CACxChM,MAAO4C,IACPqJ,MAAOrJ,KAGHsJ,EACJxK,EAASyK,eAAiBrR,EAStB0C,EAAYmL,MAAOyD,IACvB,IACG1K,EAASpF,WACTP,EAAgBoB,SACf4O,EAAyB5O,SACzBiP,GACF,CACA,MAAMjP,EAAUuE,EAAS2K,SACrB5I,SAAqB6I,KAAclP,cAC7BmP,EAAyBrL,GAAS,GAExC/D,IAAYT,EAAWS,SACzB6O,EAAUC,MAAMlJ,KAAK,CACnB5F,WAGN,GAGIqP,EAAsB,CAAChV,EAAkB0F,MAE1CwE,EAASpF,WACTP,EAAgBmB,cACfnB,EAAgBkB,kBAChB8O,EAAyB7O,cACzB6O,EAAyB9O,qBAE1BzF,GAASP,MAAMwV,KAAK7O,EAAOkE,QAAQ4K,QAASjV,IACvCA,IACFyF,EACI7C,EAAIqC,EAAWO,iBAAkBxF,EAAMyF,GACvCkH,GAAM1H,EAAWO,iBAAkBxF,MAI3CuU,EAAUC,MAAMlJ,KAAK,CACnB9F,iBAAkBP,EAAWO,iBAC7BC,cAAeuG,EAAc/G,EAAWO,sBA8ExC0P,EAAsB,CAC1BlV,EACAmV,EACAhW,EACAmK,KAEA,MAAME,EAAenH,EAAIoH,EAASzJ,GAElC,GAAIwJ,EAAO,CACT,MAAMhH,EAAeH,EACnB4F,EACAjI,EACA2B,EAAYxC,GAASkD,EAAI8B,EAAgBnE,GAAQb,GAGnDwC,EAAYa,IACX8G,GAAQA,EAAyB8L,gBAClCD,EACIvS,EACEqF,EACAjI,EACAmV,EAAuB3S,EAAe8L,GAAc9E,EAAME,KAE5D2L,EAAcrV,EAAMwC,GAExB8H,EAAOD,OAAStE,GAClB,GAGIuP,EAAsB,CAC1BtV,EACA8L,EACAgE,EACAyF,EACAC,KAIA,IAAIC,GAAoB,EACpBC,GAAkB,EACtB,MAAM/K,EAA8D,CAClE3K,QAGF,IAAKiK,EAASpF,SAAU,CACtB,IAAKiL,GAAeyF,EAAa,EAC3BjR,EAAgBc,SAAWkP,EAAyBlP,WACtDsQ,EAAkBzQ,EAAWG,QAC7BH,EAAWG,QAAUuF,EAAOvF,QAAUuQ,IACtCF,EAAoBC,IAAoB/K,EAAOvF,SAGjD,MAAMwQ,EAAyBhP,EAC7BvE,EAAI8B,EAAgBnE,GACpB8L,GAGF4J,IAAoBrT,EAAI4C,EAAWK,YAAatF,GAChD4V,EACIjJ,GAAM1H,EAAWK,YAAatF,GAC9B4C,EAAIqC,EAAWK,YAAatF,GAAM,GACtC2K,EAAOrF,YAAcL,EAAWK,YAChCmQ,EACEA,IACEnR,EAAgBgB,aAChBgP,EAAyBhP,cACzBoQ,KAAqBE,CAC3B,CAEA,GAAI9F,EAAa,CACf,MAAM+F,EAAyBxT,EAAI4C,EAAWM,cAAevF,GAExD6V,IACHjT,EAAIqC,EAAWM,cAAevF,EAAM8P,GACpCnF,EAAOpF,cAAgBN,EAAWM,cAClCkQ,EACEA,IACEnR,EAAgBiB,eAChB+O,EAAyB/O,gBACzBsQ,IAA2B/F,EAEnC,CAEA2F,GAAqBD,GAAgBjB,EAAUC,MAAMlJ,KAAKX,EAC5D,CAEA,OAAO8K,EAAoB9K,EAAS,CAAA,GAGhCmL,EAAsB,CAC1B9V,EACA0F,EACAwD,EACAL,KAMA,MAAMkN,EAAqB1T,EAAI4C,EAAWU,OAAQ3F,GAC5C2U,GACHrQ,EAAgBoB,SAAW4O,EAAyB5O,UACrD/C,EAAU+C,IACVT,EAAWS,UAAYA,EAhOzB,IAAqBI,EA6OrB,GAXImE,EAAS+L,YAAc9M,GAlONpD,EAmOW,IAzHb,EAAC9F,EAAyBkJ,KAC7CtG,EAAIqC,EAAWU,OAAQ3F,EAAMkJ,GAC7BqL,EAAUC,MAAMlJ,KAAK,CACnB3F,OAAQV,EAAWU,UAsHiBsQ,CAAajW,EAAMkJ,GAAvD2K,EAlODqC,IACCC,aAAa9B,GACbA,EAAQ+B,WAAWtQ,EAAUoQ,IAiO7BrC,EAAmB5J,EAAS+L,cAE5BG,aAAa9B,GACbR,EAAqB,KACrB3K,EACItG,EAAIqC,EAAWU,OAAQ3F,EAAMkJ,GAC7ByD,GAAM1H,EAAWU,OAAQ3F,KAI5BkJ,GAAStC,EAAUmP,EAAoB7M,GAAS6M,KAChD/J,EAAcnD,IACf8L,EACA,CACA,MAAM0B,EAAmB,IACpBxN,KACC8L,GAAqBhS,EAAU+C,GAAW,CAAEA,WAAY,GAC5DC,OAAQV,EAAWU,OACnB3F,QAGFiF,EAAa,IACRA,KACAoR,GAGL9B,EAAUC,MAAMlJ,KAAK+K,EACvB,GAGIxB,EAAa3D,MAAOlR,IACxB+U,EAAoB/U,GAAM,GAC1B,MAAMyC,QAAewH,EAAS2K,SAC5B3M,EACAgC,EAASqM,Qb3aA,EACbnG,EACA1G,EACAiL,EACAtD,KAEA,MAAM/D,EAAiD,CAAA,EAEvD,IAAK,MAAMrN,KAAQmQ,EAAa,CAC9B,MAAM3G,EAAenH,EAAIoH,EAASzJ,GAElCwJ,GAAS5G,EAAIyK,EAAQrN,EAAMwJ,EAAME,GACnC,CAEA,MAAO,CACLgL,eACA3U,MAAO,IAAIoQ,GACX9C,SACA+D,8Ba0ZEmF,CACEvW,GAAQmG,EAAOkE,MACfZ,EACAQ,EAASyK,aACTzK,EAASmH,4BAIb,OADA2D,EAAoB/U,GACbyC,GAoBHqS,EAA2B5D,MAC/B7D,EACAmJ,EACAF,EAEI,CACFG,OAAO,MAGT,IAAK,MAAMzW,KAAQqN,EAAQ,CACzB,MAAM7D,EAAQ6D,EAAOrN,GAErB,GAAIwJ,EAAO,CACT,MAAME,GAAEA,KAAOoC,GAAetC,EAE9B,GAAIE,EAAI,CACN,MAAMgN,EAAmBvQ,EAAOoC,MAAMtI,IAAIyJ,EAAG1J,MACvC2W,EACJnN,EAAME,IAAM8F,GAAsBhG,EAAgBE,IAEhDiN,GAAqBrS,EAAgBkB,kBACvCuP,EAAoB,CAACrL,EAAG1J,OAAO,GAGjC,MAAM4W,QAAmB3F,GACvBzH,EACArD,EAAOtB,SACPoD,EACAwM,EACAxK,EAASmH,4BAA8BoF,EACvCE,GAOF,GAJIC,GAAqBrS,EAAgBkB,kBACvCuP,EAAoB,CAACrL,EAAG1J,OAGtB4W,EAAWlN,EAAG1J,QAChBsW,EAAQG,OAAQ,EACZD,GACF,OAIHA,IACEnU,EAAIuU,EAAYlN,EAAG1J,MAChB0W,EACE/F,GACE1L,EAAWU,OACXiR,EACAlN,EAAG1J,MAEL4C,EAAIqC,EAAWU,OAAQ+D,EAAG1J,KAAM4W,EAAWlN,EAAG1J,OAChD2M,GAAM1H,EAAWU,OAAQ+D,EAAG1J,MACpC,EAECgM,EAAcF,UACNgJ,EACLhJ,EACA0K,EACAF,EAEN,CACF,CAEA,OAAOA,EAAQG,OAiBXd,EAAwB,CAAC3V,EAAMU,KAClCuJ,EAASpF,WACT7E,GAAQU,GAAQkC,EAAIqF,EAAajI,EAAMU,IACvCkG,EAAUiQ,KAAa1S,IAEpB2D,EAAyC,CAC7C/H,EACAyC,EACA6D,IAEAH,EACEnG,EACAoG,EACA,IACMmE,EAAOD,MACPpC,EACAtG,EAAYa,GACV2B,EACA8B,EAASlG,GACP,CAAEA,CAACA,GAAQyC,GACXA,GAEV6D,EACA7D,GAcE6S,EAAgB,CACpBrV,EACAb,EACAwO,EAA0B,CAAA,KAE1B,MAAMnE,EAAenH,EAAIoH,EAASzJ,GAClC,IAAI8L,EAAsB3M,EAE1B,GAAIqK,EAAO,CACT,MAAMiG,EAAiBjG,EAAME,GAEzB+F,KACDA,EAAe5K,UACdjC,EAAIqF,EAAajI,EAAM8N,GAAgB3O,EAAOsQ,IAEhD3D,EACEK,EAAcsD,EAAenG,MAAQjK,EAAkBF,GACnD,GACAA,EAEFoN,GAAiBkD,EAAenG,KAClC,IAAImG,EAAenG,IAAIqE,SAASsH,QAC7B6B,GACEA,EAAUC,SACTjL,EACAxE,SAASwP,EAAU3X,QAEhBsQ,EAAejB,KACpBzP,EAAgB0Q,EAAenG,KACjCmG,EAAejB,KAAKyG,QAAS+B,IACtBA,EAAY5B,gBAAmB4B,EAAYnS,WAC1CrF,MAAMC,QAAQqM,GAChBkL,EAAYnX,UAAYiM,EAAW6D,KAChCjP,GAAiBA,IAASsW,EAAY7X,OAGzC6X,EAAYnX,QACViM,IAAekL,EAAY7X,SAAW2M,KAK9C2D,EAAejB,KAAKyG,QACjBgC,GACEA,EAASpX,QAAUoX,EAAS9X,QAAU2M,GAGpCG,EAAYwD,EAAenG,KACpCmG,EAAenG,IAAInK,MAAQ,IAE3BsQ,EAAenG,IAAInK,MAAQ2M,EAEtB2D,EAAenG,IAAIrK,MACtBsV,EAAUC,MAAMlJ,KAAK,CACnBtL,OACAgI,OAAQvH,EAAYwH,MAK9B,EAEC0F,EAAQ4H,aAAe5H,EAAQuJ,cAC9B5B,EACEtV,EACA8L,EACA6B,EAAQuJ,YACRvJ,EAAQ4H,aACR,GAGJ5H,EAAQwJ,gBAAkBC,GAAQpX,IAG9BqX,EAAY,CAKhBrX,EACAb,EACAwO,KAEA,IAAK,MAAM2J,KAAYnY,EAAO,CAC5B,IAAKA,EAAMmC,eAAegW,GACxB,OAEF,MAAMxL,EAAa3M,EAAMmY,GACnB7Q,EAAYzG,EAAO,IAAMsX,EACzB9N,EAAQnH,EAAIoH,EAAShD,IAE1BN,EAAOoC,MAAMtI,IAAID,IAChBT,EAASuM,IACRtC,IAAUA,EAAME,MAClBxK,EAAa4M,GACVuL,EAAU5Q,EAAWqF,EAAY6B,GACjC0H,EAAc5O,EAAWqF,EAAY6B,EAC3C,GAGI4J,EAA0C,CAC9CvX,EACAb,EACAwO,EAAU,CAAA,KAEV,MAAMnE,EAAQnH,EAAIoH,EAASzJ,GACrBqR,EAAelL,EAAOoC,MAAMtI,IAAID,GAChCwX,EAAa/W,EAAYtB,GAE/ByD,EAAIqF,EAAajI,EAAMwX,GAEnBnG,GACFkD,EAAUhM,MAAM+C,KAAK,CACnBtL,OACAgI,OAAQvH,EAAYwH,MAInB3D,EAAgBc,SACfd,EAAgBgB,aAChBgP,EAAyBlP,SACzBkP,EAAyBhP,cAC3BqI,EAAQ4H,aAERhB,EAAUC,MAAMlJ,KAAK,CACnBtL,OACAsF,YAAagI,GAAenJ,EAAgB8D,GAC5C7C,QAASuQ,EAAU3V,EAAMwX,OAI7BhO,GAAUA,EAAME,IAAOrK,EAAkBmY,GAErCnC,EAAcrV,EAAMwX,EAAY7J,GADhC0J,EAAUrX,EAAMwX,EAAY7J,GAIlCkC,GAAU7P,EAAMmG,IAAWoO,EAAUC,MAAMlJ,KAAK,IAAKrG,EAAYjF,SACjEuU,EAAUC,MAAMlJ,KAAK,CACnBtL,KAAMsK,EAAOD,MAAQrK,OAAO6B,EAC5BmG,OAAQvH,EAAYwH,MAIlBkB,EAA0B+H,MAAOvR,IACrC2K,EAAOD,OAAQ,EACf,MAAMzK,EAASD,EAAMC,OACrB,IAAII,EAAeJ,EAAOI,KACtByX,GAAsB,EAC1B,MAAMjO,EAAenH,EAAIoH,EAASzJ,GAC5B0X,EAA8B5L,IAClC2L,EACEE,OAAOxU,MAAM2I,IACZ5M,EAAa4M,IAAe3I,MAAM2I,EAAW7E,YAC9CL,EAAUkF,EAAYzJ,EAAI4F,EAAajI,EAAM8L,KAE3C8L,EAA6B5I,GAAmB/E,EAASgF,MACzD4I,EAA4B7I,GAChC/E,EAASyJ,gBAGX,GAAIlK,EAAO,CACT,IAAIN,EACAxD,EACJ,MAAMoG,EAAalM,EAAOX,KACtBqP,GAAc9E,EAAME,IACpBhK,EAAcC,GACZmQ,EACJnQ,EAAMV,OAASmE,GAAezD,EAAMV,OAASmE,EACzC0U,KChvBInK,EDivBQnE,EAAME,IChvBpBW,QACPsD,EAAQ2D,UACP3D,EAAQ8D,KACR9D,EAAQ+D,KACR/D,EAAQ4D,WACR5D,EAAQ6D,WACR7D,EAAQgE,SACRhE,EAAQ+B,WD0uBDzF,EAAS2K,UACTvS,EAAI4C,EAAWU,OAAQ3F,IACvBwJ,EAAME,GAAGqO,OEpvBL,EACbjI,EACA7G,EACA+K,EACAN,EAIAzE,KAEIA,EAAKI,WAEG2E,GAAe/E,EAAKK,YACrBrG,GAAa6G,IACbkE,EAAcN,EAAevE,SAAWF,EAAKE,WAC9CW,IACCkE,EAAcN,EAAetE,WAAaH,EAAKG,aACjDU,GFouBHkI,CACElI,EACAzN,EAAI4C,EAAWM,cAAevF,GAC9BiF,EAAW+O,YACX6D,EACAD,GAEEK,EAAUpI,GAAU7P,EAAMmG,EAAQ2J,GAExClN,EAAIqF,EAAajI,EAAM8L,GAEnBgE,EACGlQ,GAAWA,EAAOsY,WACrB1O,EAAME,GAAGL,QAAUG,EAAME,GAAGL,OAAO1J,GACnCkU,GAAsBA,EAAmB,IAElCrK,EAAME,GAAGP,UAClBK,EAAME,GAAGP,SAASxJ,GAGpB,MAAMkJ,EAAayM,EAAoBtV,EAAM8L,EAAYgE,GAEnD0F,GAAgBxJ,EAAcnD,IAAeoP,EASnD,IAPCnI,GACCyE,EAAUC,MAAMlJ,KAAK,CACnBtL,OACAf,KAAMU,EAAMV,KACZ+I,OAAQvH,EAAYwH,KAGpB6P,EAWF,OAVIxT,EAAgBoB,SAAW4O,EAAyB5O,WAChC,WAAlBuE,EAASgF,KACPa,GACF/J,IAEQ+J,GACV/J,KAKFyP,GACAjB,EAAUC,MAAMlJ,KAAK,CAAEtL,UAAUiY,EAAU,CAAA,EAAKpP,IAMpD,IAFCiH,GAAemI,GAAW1D,EAAUC,MAAMlJ,KAAK,IAAKrG,IAEjDgF,EAAS2K,SAAU,CACrB,MAAMjP,OAAEA,SAAiBkP,EAAW,CAAC7U,IAIrC,GAFA0X,EAA2B5L,GAEvB2L,EAAqB,CACvB,MAAMU,EAA4B7H,GAChCrL,EAAWU,OACX8D,EACAzJ,GAEIoY,EAAoB9H,GACxB3K,EACA8D,EACA0O,EAA0BnY,MAAQA,GAGpCkJ,EAAQkP,EAAkBlP,MAC1BlJ,EAAOoY,EAAkBpY,KAEzB0F,EAAUsG,EAAcrG,EAC1B,CACF,MACEoP,EAAoB,CAAC/U,IAAO,GAC5BkJ,SACQ+H,GACJzH,EACArD,EAAOtB,SACPoD,EACAwM,EACAxK,EAASmH,4BAEXpR,GACF+U,EAAoB,CAAC/U,IAErB0X,EAA2B5L,GAEvB2L,IACEvO,EACFxD,GAAU,GAEVpB,EAAgBoB,SAChB4O,EAAyB5O,WAEzBA,QAAgBoP,EAAyBrL,GAAS,KAKpDgO,IACFjO,EAAME,GAAGqO,QACLvY,MAAMC,QAAQ+J,EAAME,GAAGqO,OAASvO,EAAME,GAAGqO,KAAKhV,OAAS,IACzDqU,GACE5N,EAAME,GAAGqO,MAIbjC,EAAoB9V,EAAM0F,EAASwD,EAAOL,GAE9C,CCl2BW,IAAC8E,GDq2BR0K,EAAc,CAAC/O,EAAU9H,KAC7B,GAAIa,EAAI4C,EAAWU,OAAQnE,IAAQ8H,EAAIK,MAErC,OADAL,EAAIK,QACG,GAKLyN,GAAwClG,MAAOlR,EAAM2N,EAAU,CAAA,KACnE,IAAIjI,EACA8N,EACJ,MAAM8E,EAAapN,EAAsBlL,GAEzC,GAAIiK,EAAS2K,SAAU,CACrB,MAAMjP,OAnb0BuL,OAAOnR,IACzC,MAAM4F,OAAEA,SAAiBkP,EAAW9U,GAEpC,GAAIA,EACF,IAAK,MAAMC,KAAQD,EAAO,CACxB,MAAMmJ,EAAQ7G,EAAIsD,EAAQ3F,GAC1BkJ,EACItG,EAAIqC,EAAWU,OAAQ3F,EAAMkJ,GAC7ByD,GAAM1H,EAAWU,OAAQ3F,EAC/B,MAEAiF,EAAWU,OAASA,EAGtB,OAAOA,GAqagB4S,CACnB5W,EAAY3B,GAAQA,EAAOsY,GAG7B5S,EAAUsG,EAAcrG,GACxB6N,EAAmBxT,GACdsY,EAAWvI,KAAM/P,GAASqC,EAAIsD,EAAQ3F,IACvC0F,CACN,MAAW1F,GACTwT,SACQgF,QAAQC,IACZH,EAAW9R,IAAI0K,MAAOzK,IACpB,MAAM+C,EAAQnH,EAAIoH,EAAShD,GAC3B,aAAaqO,EACXtL,GAASA,EAAME,GAAK,CAAEjD,CAACA,GAAY+C,GAAUA,OAInDsH,MAAM9O,UACLwR,GAAqBvO,EAAWS,UAAYK,KAE/CyN,EAAmB9N,QAAgBoP,EAAyBrL,GAqB9D,OAlBA8K,EAAUC,MAAMlJ,KAAK,KACdrF,EAASjG,KACZsE,EAAgBoB,SAAW4O,EAAyB5O,UACpDA,IAAYT,EAAWS,QACrB,CAAA,EACA,CAAE1F,WACFiK,EAAS2K,WAAa5U,EAAO,CAAE0F,WAAY,GAC/CC,OAAQV,EAAWU,SAGrBgI,EAAQ+K,cACLlF,GACDtD,GACEzG,EACA4O,EACArY,EAAOsY,EAAanS,EAAOkE,OAGxBmJ,GAGHqD,GAA4C,CAChDyB,EAGAK,KAEA,IAAI3Q,EAAS,IACPsC,EAAOD,MAAQpC,EAAc9D,GAUnC,OAPIwU,IACF3Q,EAAS4D,EACP+M,EAAOrT,YAAcL,EAAWK,YAAcL,EAAWM,cACzDyC,IAIGrG,EAAY2W,GACftQ,EACA/B,EAASqS,GACPjW,EAAI2F,EAAQsQ,GACZA,EAAW9R,IAAKxG,GAASqC,EAAI2F,EAAQhI,KAGvC4Y,GAAoD,CACxD5Y,EACA8D,KAAS,CAETiF,UAAW1G,GAAKyB,GAAamB,GAAYU,OAAQ3F,GACjDoF,UAAW/C,GAAKyB,GAAamB,GAAYK,YAAatF,GACtDkJ,MAAO7G,GAAKyB,GAAamB,GAAYU,OAAQ3F,GAC7CyF,eAAgBpD,EAAI4C,EAAWO,iBAAkBxF,GACjDiJ,YAAa5G,GAAKyB,GAAamB,GAAYM,cAAevF,KActD6Y,GAA0C,CAAC7Y,EAAMkJ,EAAOyE,KAC5D,MAAMrE,GAAOjH,EAAIoH,EAASzJ,EAAM,CAAE0J,GAAI,KAAMA,IAAM,CAAA,GAAIJ,IAChDwP,EAAezW,EAAI4C,EAAWU,OAAQ3F,IAAS,CAAA,GAG7CsJ,IAAKyP,EAAUjP,QAAEA,EAAO7K,KAAEA,KAAS+Z,GAAoBF,EAE/DlW,EAAIqC,EAAWU,OAAQ3F,EAAM,IACxBgZ,KACA9P,EACHI,QAGFiL,EAAUC,MAAMlJ,KAAK,CACnBtL,OACA2F,OAAQV,EAAWU,OACnBD,SAAS,IAGXiI,GAAWA,EAAQ+K,aAAepP,GAAOA,EAAIK,OAASL,EAAIK,SA6BtD/D,GAA2CjB,GAC/C4P,EAAUC,MAAMhJ,UAAU,CACxBF,KACExH,IGjgCO,IACb9D,EACAiZ,EACAnU,EAFA9E,EHugC8B2E,EAAM3E,KGtgCpCiZ,EHsgC0CnV,EAAU9D,KGrgCpD8E,EHqgC0DH,EAAMG,MGngC/D9E,GACAiZ,GACDjZ,IAASiZ,IACT/N,EAAsBlL,GAAM+P,KACzBmJ,GACCA,IACCpU,EACGoU,IAAgBD,EAChBC,EAAYjJ,WAAWgJ,IACvBA,EAAWhJ,WAAWiJ,ORPjB,EACbC,EAIA7U,EACAS,EACAd,KAEAc,EAAgBoU,GAChB,MAAMnZ,KAAEA,KAAS8D,GAAcqV,EAE/B,OACEnN,EAAclI,IACd/C,OAAOoG,KAAKrD,GAAWf,QAAUhC,OAAOoG,KAAK7C,GAAiBvB,QAC9DhC,OAAOoG,KAAKrD,GAAW6L,KACpBnO,GACC8C,EAAgB9C,OACdyC,GAAUZ,KKg/BV+V,CACEtV,EACCa,EAAMb,WAA+BQ,EACtC+U,GACA1U,EAAM2U,eAGR3U,EAAMmB,SAAS,CACbkC,OAAQ,IAAKC,MACVhD,KACAnB,EACHI,cACEC,OAIPuH,YAcCvB,GAA8C,CAACnK,EAAM2N,EAAU,CAAA,KACnE,IAAK,MAAMlH,KAAazG,EAAOkL,EAAsBlL,GAAQmG,EAAOkE,MAClElE,EAAOkE,MAAMkP,OAAO9S,GACpBN,EAAOoC,MAAMgR,OAAO9S,GAEfkH,EAAQ6L,YACX7M,GAAMlD,EAAShD,GACfkG,GAAM1E,EAAaxB,KAGpBkH,EAAQ8L,WAAa9M,GAAM1H,EAAWU,OAAQc,IAC9CkH,EAAQ+L,WAAa/M,GAAM1H,EAAWK,YAAamB,IACnDkH,EAAQgM,aAAehN,GAAM1H,EAAWM,cAAekB,IACvDkH,EAAQiM,kBACPjN,GAAM1H,EAAWO,iBAAkBiB,IACpCwD,EAAS5B,mBACPsF,EAAQkM,kBACTlN,GAAMxI,EAAgBsC,GAG1B8N,EAAUC,MAAMlJ,KAAK,CACnBtD,OAAQvH,EAAYwH,KAGtBsM,EAAUC,MAAMlJ,KAAK,IAChBrG,KACE0I,EAAQ+L,UAAiB,CAAEtU,QAASuQ,KAAhB,CAAA,KAG1BhI,EAAQmM,aAAe/T,KAGpByE,GAAgE,EACpE3F,WACA7E,YAGG2C,EAAUkC,IAAayF,EAAOD,OAC7BxF,GACFsB,EAAOtB,SAAS5E,IAAID,MAEpB6E,EAAWsB,EAAOtB,SAAS0B,IAAIvG,GAAQmG,EAAOtB,SAAS0U,OAAOvZ,KAI5D2I,GAA0C,CAAC3I,EAAM2N,EAAU,CAAA,KAC/D,IAAInE,EAAQnH,EAAIoH,EAASzJ,GACzB,MAAM+Z,EACJpX,EAAUgL,EAAQ9I,WAAalC,EAAUsH,EAASpF,UAwBpD,OAtBAjC,EAAI6G,EAASzJ,EAAM,IACbwJ,GAAS,CAAA,EACbE,GAAI,IACEF,GAASA,EAAME,GAAKF,EAAME,GAAK,CAAEJ,IAAK,CAAEtJ,SAC5CA,OACAqK,OAAO,KACJsD,KAGPxH,EAAOkE,MAAM9D,IAAIvG,GAEbwJ,EACFgB,GAAkB,CAChB3F,SAAUlC,EAAUgL,EAAQ9I,UACxB8I,EAAQ9I,SACRoF,EAASpF,SACb7E,SAGFkV,EAAoBlV,GAAM,EAAM2N,EAAQxO,OAGnC,IACD4a,EACA,CAAElV,SAAU8I,EAAQ9I,UAAYoF,EAASpF,UACzC,MACAoF,EAAS+P,YACT,CACE1I,WAAY3D,EAAQ2D,SACpBG,IAAK5C,GAAalB,EAAQ8D,KAC1BC,IAAK7C,GAAalB,EAAQ+D,KAC1BF,UAAW3C,GAAqBlB,EAAQ6D,WACxCD,UAAW1C,GAAalB,EAAQ4D,WAChCI,QAAS9C,GAAalB,EAAQgE,UAEhC,GACJ3R,OACAmJ,WACAE,OAAQF,EACRG,IAAMA,IACJ,GAAIA,EAAK,CACPX,GAAS3I,EAAM2N,GACfnE,EAAQnH,EAAIoH,EAASzJ,GAErB,MAAMia,EAAWtY,EAAY2H,EAAInK,QAC7BmK,EAAI4Q,kBACD5Q,EAAI4Q,iBAAiB,yBAAyB,IAEjD5Q,EACE6Q,EIvoCD,CAAC7Q,GACdkD,GAAalD,IAAQvK,EAAgBuK,GJsoCLyI,CAAkBkI,GACpCzL,EAAOhF,EAAME,GAAG8E,MAAQ,GAE9B,GACE2L,EACI3L,EAAKmB,KAAM/B,GAAgBA,IAAWqM,GACtCA,IAAazQ,EAAME,GAAGJ,IAE1B,OAGF1G,EAAI6G,EAASzJ,EAAM,CACjB0J,GAAI,IACCF,EAAME,MACLyQ,EACA,CACE3L,KAAM,IACDA,EAAKzM,OAAO0K,IACfwN,KACIza,MAAMC,QAAQ4C,EAAI8B,EAAgBnE,IAAS,CAAC,IAAM,IAExDsJ,IAAK,CAAErK,KAAMgb,EAAShb,KAAMe,SAE9B,CAAEsJ,IAAK2Q,MAIf/E,EAAoBlV,GAAM,OAAO6B,EAAWoY,EAC9C,MACEzQ,EAAQnH,EAAIoH,EAASzJ,EAAM,CAAA,GAEvBwJ,EAAME,KACRF,EAAME,GAAGW,OAAQ,IAGlBJ,EAAS5B,kBAAoBsF,EAAQtF,qBAClCvI,EAAmBqG,EAAOoC,MAAOvI,KAASsK,EAAOC,SACnDpE,EAAOiO,QAAQ7N,IAAIvG,MAMvBoa,GAAc,IAClBnQ,EAAS0J,kBACTzD,GAAsBzG,EAAS4O,EAAalS,EAAOkE,OAyB/CgQ,GACJ,CAACC,EAASC,IAAcrJ,MAAOsJ,IAC7B,IAAIC,EACAD,IACFA,EAAEE,gBAAkBF,EAAEE,iBACrBF,EAA+BG,SAC7BH,EAA+BG,WAEpC,IAAIC,EAGwBna,EAAYwH,GAMxC,GAJAsM,EAAUC,MAAMlJ,KAAK,CACnB2I,cAAc,IAGZhK,EAAS2K,SAAU,CACrB,MAAMjP,OAAEA,EAAMqC,OAAEA,SAAiB6M,IACjC5P,EAAWU,OAASA,EACpBiV,EAAcna,EAAYuH,EAC5B,YACQ8M,EAAyBrL,GAGjC,GAAItD,EAAOtB,SAASgW,KAClB,IAAK,MAAM7a,KAAQmG,EAAOtB,SACxB8H,GAAMiO,EAAa5a,GAMvB,GAFA2M,GAAM1H,EAAWU,OAAQ,QAErBqG,EAAc/G,EAAWU,QAAS,CACpC4O,EAAUC,MAAMlJ,KAAK,CACnB3F,OAAQ,CAAA,IAEV,UACQ2U,EAAQM,EAAmCJ,EACnD,CAAE,MAAOtR,GACPuR,EAAevR,CACjB,CACF,MACMqR,SACIA,EAAU,IAAKtV,EAAWU,QAAU6U,GAE5CJ,KACAhE,WAAWgE,IAUb,GAPA7F,EAAUC,MAAMlJ,KAAK,CACnB0I,aAAa,EACbC,cAAc,EACdC,mBAAoBlI,EAAc/G,EAAWU,UAAY8U,EACzD3G,YAAa7O,EAAW6O,YAAc,EACtCnO,OAAQV,EAAWU,SAEjB8U,EACF,MAAMA,GAoCNK,GAAqC,CACzC1U,EACA2U,EAAmB,CAAA,KAEnB,MAAMC,EAAgB5U,EAAa3F,EAAY2F,GAAcjC,EACvD8W,EAAqBxa,EAAYua,GACjCE,EAAqBlP,EAAc5F,GACnC4B,EAASkT,EAAqB/W,EAAiB8W,EAMrD,GAJKF,EAAiBI,oBACpBhX,EAAiB6W,IAGdD,EAAiBK,WAAY,CAChC,GAAIL,EAAiBM,gBAAiB,CACpC,MAAMC,EAAgB,IAAInH,IAAI,IACzBhO,EAAOkE,SACPtJ,OAAOoG,KAAKmG,GAAenJ,EAAgB8D,MAEhD,IAAK,MAAMxB,KAAajH,MAAMwV,KAAKsG,GACjCjZ,EAAI4C,EAAWK,YAAamB,GACxB7D,EAAIoF,EAAQvB,EAAWpE,EAAI4F,EAAaxB,IACxC8Q,EACE9Q,EACApE,EAAI2F,EAAQvB,GAGtB,KAAO,CACL,GAAIpG,GAASsB,EAAYyE,GACvB,IAAK,MAAMpG,KAAQmG,EAAOkE,MAAO,CAC/B,MAAMb,EAAQnH,EAAIoH,EAASzJ,GAC3B,GAAIwJ,GAASA,EAAME,GAAI,CACrB,MAAM+F,EAAiBjQ,MAAMC,QAAQ+J,EAAME,GAAG8E,MAC1ChF,EAAME,GAAG8E,KAAK,GACdhF,EAAME,GAAGJ,IAEb,GAAI6C,EAAcsD,GAAiB,CACjC,MAAM8L,EAAO9L,EAAe+L,QAAQ,QACpC,GAAID,EAAM,CACRA,EAAKE,QACL,KACF,CACF,CACF,CACF,CAGF,GAAIV,EAAiBW,cACnB,IAAK,MAAMjV,KAAaN,EAAOkE,MAC7BkN,EACE9Q,EACApE,EAAI2F,EAAQvB,SAIhBgD,EAAU,CAAA,CAEd,CAEAxB,EAAcgC,EAAS5B,iBACnB0S,EAAiBI,kBACd1a,EAAY0D,GACZ,CAAA,EACF1D,EAAYuH,GAEjBuM,EAAUhM,MAAM+C,KAAK,CACnBtD,OAAQ,IAAKA,KAGfuM,EAAUC,MAAMlJ,KAAK,CACnBtD,OAAQ,IAAKA,IAEjB,CAEA7B,EAAS,CACPkE,MAAO0Q,EAAiBM,gBAAkBlV,EAAOkE,MAAQ,IAAI8J,IAC7DC,QAAS,IAAID,IACb5L,MAAO,IAAI4L,IACXtP,SAAU,IAAIsP,IACd7N,MAAO,IAAI6N,IACXzN,UAAU,EACViD,MAAO,IAGTW,EAAOD,OACJ/F,EAAgBoB,WACfqV,EAAiBjB,eACjBiB,EAAiBM,gBAErB/Q,EAAOhE,QAAU2D,EAAS5B,iBAE1BkM,EAAUC,MAAMlJ,KAAK,CACnBwI,YAAaiH,EAAiBY,gBAC1B1W,EAAW6O,YACX,EACJ1O,SAAS8V,IAELH,EAAiBrB,UACfzU,EAAWG,WAET2V,EAAiBI,mBAChBvU,EAAUR,EAAYjC,KAE/B6P,cAAa+G,EAAiBa,iBAC1B3W,EAAW+O,YAEf1O,YAAa4V,EACT,CAAA,EACAH,EAAiBM,gBACfN,EAAiBI,mBAAqBlT,EACpCqF,GAAenJ,EAAgB8D,GAC/BhD,EAAWK,YACbyV,EAAiBI,mBAAqB/U,EACpCkH,GAAenJ,EAAgBiC,GAC/B2U,EAAiBrB,UACfzU,EAAWK,YACX,CAAA,EACVC,cAAewV,EAAiBpB,YAC5B1U,EAAWM,cACX,CAAA,EACJI,OAAQoV,EAAiBc,WAAa5W,EAAWU,OAAS,CAAA,EAC1DuO,qBAAoB6G,EAAiBe,wBACjC7W,EAAWiP,mBAEfD,cAAc,EACd/P,cAAeC,KAIbsX,GAAoC,CAACrV,EAAY2U,IACrDD,GACE5O,EAAW9F,GACNA,EAAwB6B,GACzB7B,EACJ2U,GAqBE1B,GACJhD,IAEApR,EAAa,IACRA,KACAoR,IAaDzR,GAAU,CACdb,QAAS,CACP4E,YACAwB,cACAyO,iBACAyB,gBACAxB,YACAjT,cACAiP,aACAuF,eACAtS,YACA6N,YACA5P,YACAgW,eA1wC0C,CAC5C/b,EACAgI,EAAS,GACTgU,EACAC,EACAC,GAAkB,EAClBC,GAA6B,KAE7B,GAAIF,GAAQD,IAAW/R,EAASpF,SAAU,CAExC,GADAyF,EAAOC,QAAS,EACZ4R,GAA8B3c,MAAMC,QAAQ4C,EAAIoH,EAASzJ,IAAQ,CACnE,MAAM4a,EAAcoB,EAAO3Z,EAAIoH,EAASzJ,GAAOic,EAAKG,KAAMH,EAAKI,MAC/DH,GAAmBtZ,EAAI6G,EAASzJ,EAAM4a,EACxC,CAEA,GACEuB,GACA3c,MAAMC,QAAQ4C,EAAI4C,EAAWU,OAAQ3F,IACrC,CACA,MAAM2F,EAASqW,EACb3Z,EAAI4C,EAAWU,OAAQ3F,GACvBic,EAAKG,KACLH,EAAKI,MAEPH,GAAmBtZ,EAAIqC,EAAWU,OAAQ3F,EAAM2F,GKpPzC,EAAI2D,EAAQtJ,MACxB8B,EAAQO,EAAIiH,EAAKtJ,IAAO+C,QAAU4J,GAAMrD,EAAKtJ,ILoPxCsc,CAAgBrX,EAAWU,OAAQ3F,EACrC,CAEA,IACGsE,EAAgBiB,eACf+O,EAAyB/O,gBAC3B4W,GACA3c,MAAMC,QAAQ4C,EAAI4C,EAAWM,cAAevF,IAC5C,CACA,MAAMuF,EAAgByW,EACpB3Z,EAAI4C,EAAWM,cAAevF,GAC9Bic,EAAKG,KACLH,EAAKI,MAEPH,GAAmBtZ,EAAIqC,EAAWM,cAAevF,EAAMuF,EACzD,EAEIjB,EAAgBgB,aAAegP,EAAyBhP,eAC1DL,EAAWK,YAAcgI,GAAenJ,EAAgB8D,IAG1DsM,EAAUC,MAAMlJ,KAAK,CACnBtL,OACAoF,QAASuQ,EAAU3V,EAAMgI,GACzB1C,YAAaL,EAAWK,YACxBK,OAAQV,EAAWU,OACnBD,QAAST,EAAWS,SAExB,MACE9C,EAAIqF,EAAajI,EAAMgI,IAqtCvBwC,qBACA+R,WA3sCgB5W,IAClBV,EAAWU,OAASA,EACpB4O,EAAUC,MAAMlJ,KAAK,CACnB3F,OAAQV,EAAWU,OACnBD,SAAS,KAwsCT8W,eA76BFxc,GAEA8B,EACEO,EACEiI,EAAOD,MAAQpC,EAAc9D,EAC7BnE,EACAiK,EAAS5B,iBAAmBhG,EAAI8B,EAAgBnE,EAAM,IAAM,KAw6B9D8a,UACA2B,oBA3BwB,IAC1BvQ,EAAWjC,EAAS/F,gBACnB+F,EAAS/F,gBAA6BwY,KAAM1U,IAC3CyT,GAAMzT,EAAQiC,EAAS0S,cACvBpI,EAAUC,MAAMlJ,KAAK,CACnBjG,WAAW,MAuBb8C,iBAz9BqB,KACvB,IAAK,MAAMnI,KAAQmG,EAAOiO,QAAS,CACjC,MAAM5K,EAAenH,EAAIoH,EAASzJ,GAElCwJ,IACGA,EAAME,GAAG8E,KACNhF,EAAME,GAAG8E,KAAKsC,MAAOxH,IAASmD,GAAKnD,KAClCmD,GAAKjD,EAAME,GAAGJ,OACnBa,GAAWnK,EACf,CAEAmG,EAAOiO,QAAU,IAAID,KA+8BnByI,aAtTkB/X,IAChBlC,EAAUkC,KACZ0P,EAAUC,MAAMlJ,KAAK,CAAEzG,aACvBqL,GACEzG,EACA,CAACH,EAAKtJ,KACJ,MAAMqQ,EAAsBhO,EAAIoH,EAASzJ,GACrCqQ,IACF/G,EAAIzE,SAAWwL,EAAa3G,GAAG7E,UAAYA,EAEvCrF,MAAMC,QAAQ4Q,EAAa3G,GAAG8E,OAChC6B,EAAa3G,GAAG8E,KAAKyG,QAASpD,IAC5BA,EAAShN,SAAWwL,EAAa3G,GAAG7E,UAAYA,MAKxD,GACA,KAqSF0P,YACAjQ,kBACA,WAAImF,GACF,OAAOA,CACT,EACA,eAAIxB,GACF,OAAOA,CACT,EACA,UAAIqC,GACF,OAAOA,CACT,EACA,UAAIA,CAAOnL,GACTmL,EAASnL,CACX,EACA,kBAAIgF,GACF,OAAOA,CACT,EACA,UAAIgC,GACF,OAAOA,CACT,EACA,UAAIA,CAAOhH,GACTgH,EAAShH,CACX,EACA,cAAI8F,GACF,OAAOA,CACT,EACA,YAAIgF,GACF,OAAOA,CACT,EACA,YAAIA,CAAS9K,GACX8K,EAAW,IACNA,KACA9K,EAEP,GAEFqM,UAzfiD7G,IACjD2F,EAAOD,OAAQ,EACfiK,EAA2B,IACtBA,KACA3P,EAAMb,WAEJ8B,GAAW,IACbjB,EACHb,UAAWwQ,KAkfb8C,WACAzO,YACA0R,gBACA/T,MApjBwC,CACxCtG,EAIAwC,IAEA0J,EAAWlM,GACPuU,EAAUC,MAAMhJ,UAAU,CACxBF,KAAOuR,GACL,WAAYA,GACZ7c,EACE8H,OAAUjG,EAAWW,GACrBqa,KAON/U,EACE9H,EACAwC,GACA,GA8hBN+U,WACAV,aACA4E,SACAqB,WA9QkD,CAAC9c,EAAM2N,EAAU,CAAA,KAC/DtL,EAAIoH,EAASzJ,KACX2B,EAAYgM,EAAQnL,cACtB+U,EAASvX,EAAMS,EAAY4B,EAAI8B,EAAgBnE,MAE/CuX,EACEvX,EACA2N,EAAQnL,cAEVI,EAAIuB,EAAgBnE,EAAMS,EAAYkN,EAAQnL,gBAG3CmL,EAAQgM,aACXhN,GAAM1H,EAAWM,cAAevF,GAG7B2N,EAAQ+L,YACX/M,GAAM1H,EAAWK,YAAatF,GAC9BiF,EAAWG,QAAUuI,EAAQnL,aACzBmT,EAAU3V,EAAMS,EAAY4B,EAAI8B,EAAgBnE,KAChD2V,KAGDhI,EAAQ8L,YACX9M,GAAM1H,EAAWU,OAAQ3F,GACzBsE,EAAgBoB,SAAWK,KAG7BwO,EAAUC,MAAMlJ,KAAK,IAAKrG,MAmP5B8X,YA1lBqD/c,IACrDA,GACEkL,EAAsBlL,GAAMiV,QAAS+H,GACnCrQ,GAAM1H,EAAWU,OAAQqX,IAG7BzI,EAAUC,MAAMlJ,KAAK,CACnB3F,OAAQ3F,EAAOiF,EAAWU,OAAS,CAAA,KAolBrCwE,cACA0O,YACAoE,SAzG8C,CAACjd,EAAM2N,EAAU,CAAA,KAC/D,MAAMnE,EAAQnH,EAAIoH,EAASzJ,GACrByP,EAAiBjG,GAASA,EAAME,GAEtC,GAAI+F,EAAgB,CAClB,MAAMwK,EAAWxK,EAAejB,KAC5BiB,EAAejB,KAAK,GACpBiB,EAAenG,IAEf2Q,EAAStQ,QACXsQ,EAAStQ,QACTgE,EAAQuP,cACNhR,EAAW+N,EAASrQ,SACpBqQ,EAASrQ,SAEf,GA2FAgP,kBAGF,MAAO,IACFhU,GACHuY,YAAavY,GAEjB,CM1iDA,IAAAwY,GAAe,KACb,GAAsB,oBAAXC,QAA0BA,OAAOC,WAC1C,OAAOD,OAAOC,aAGhB,MAAMC,EACmB,oBAAhBC,YAA8Bpe,KAAKqe,MAA4B,IAApBD,YAAYC,MAEhE,MAAO,uCAAuCtb,QAAQ,QAAUub,IAC9D,MAAMC,GAAqB,GAAhBC,KAAKC,SAAgBN,GAAK,GAAK,EAE1C,OAAa,KAALG,EAAWC,EAAS,EAAJA,EAAW,GAAKG,SAAS,OCRrDC,GAAe,CACb/d,EACA6C,EACA8K,EAAiC,CAAA,IAEjCA,EAAQ+K,aAAe/W,EAAYgM,EAAQ+K,aACvC/K,EAAQqQ,WACR,GAAGhe,KAAQ2B,EAAYgM,EAAQsQ,YAAcpb,EAAQ8K,EAAQsQ,cAC7D,GCTNC,GAAe,CAAIxd,EAAWvB,IAAwB,IACjDuB,KACAwK,EAAsB/L,ICJ3Bgf,GAAmBhf,GACjBK,MAAMC,QAAQN,GAASA,EAAMqH,IAAI,aAAmB3E,ECOxC,SAAUuc,GACtB1d,EACAmC,EACA1D,GAEA,MAAO,IACFuB,EAAKqM,MAAM,EAAGlK,MACdqI,EAAsB/L,MACtBuB,EAAKqM,MAAMlK,GAElB,CChBA,IAAAwb,GAAe,CACb3d,EACAsU,EACAsJ,IAEK9e,MAAMC,QAAQiB,IAIfiB,EAAYjB,EAAK4d,MACnB5d,EAAK4d,QAAMzc,GAEbnB,EAAK6d,OAAOD,EAAI,EAAG5d,EAAK6d,OAAOvJ,EAAM,GAAG,IAEjCtU,GARE,GCNX8d,GAAe,CAAI9d,EAAWvB,IAAwB,IACjD+L,EAAsB/L,MACtB+L,EAAsBxK,ICY3B,IAAA+d,GAAe,CAAI/d,EAAWmC,IAC5BlB,EAAYkB,GACR,GAdN,SAA4BnC,EAAWge,GACrC,IAAIC,EAAI,EACR,MAAMC,EAAO,IAAIle,GAEjB,IAAK,MAAMmC,KAAS6b,EAClBE,EAAKL,OAAO1b,EAAQ8b,EAAG,GACvBA,IAGF,OAAO7c,EAAQ8c,GAAM7b,OAAS6b,EAAO,EACvC,CAKMC,CACEne,EACCwK,EAAsBrI,GAAoBic,KAAK,CAACC,EAAGC,IAAMD,EAAIC,ICrBtEC,GAAe,CAAIve,EAAWwe,EAAgBC,MAC3Cze,EAAKwe,GAASxe,EAAKye,IAAW,CAACze,EAAKye,GAASze,EAAKwe,KCDrDE,GAAe,CAAIxE,EAAkB/X,EAAe1D,KAClDyb,EAAY/X,GAAS1D,EACdyb,gBjDgDPjW,GAEAA,EAAM0a,OAAOjX,EAAuDzD,WEtBtE,SAGEA,GACA,MAAMC,EAAUjB,KACT2b,EAASC,GAAc/b,EAAMwB,UAAS,IACvCjB,QACJA,EAAUa,EAAQb,QAAOyb,SACzBA,EAAQC,SACRA,EAAQlV,OACRA,EAAMyR,OACNA,EAASlR,EAAY4U,QACrBA,EAAOC,QACPA,EAAOC,QACPA,EAAOP,OACPA,EAAMQ,UACNA,EAASC,eACTA,KACGC,GACDpb,EAEEqb,EAAS9O,MAAOvR,IACpB,IAAIsgB,GAAW,EACXhhB,EAAO,SAEL8E,EAAQsW,aAAanJ,MAAOxQ,IAChC,MAAMwf,EAAW,IAAIC,SACrB,IAAIC,EAAe,GAEnB,IACEA,EAAeC,KAAKC,UAAU5f,EAChC,CAAE,MAAA6f,GAAO,CAET,MAAMC,EAAoB/V,EAAQ1G,EAAQkE,aAE1C,IAAK,MAAMzG,KAAOgf,EAChBN,EAASO,OAAOjf,EAAKgf,EAAkBhf,IAazC,GAVIge,SACIA,EAAS,CACb9e,OACAf,QACAqc,SACAkE,WACAE,iBAIA7V,EACF,IACE,MAAMmW,EAAgC,CACpChB,GAAWA,EAAQ,gBACnBC,GACA5P,KAAM5Q,GAAUA,GAASA,EAAMmI,SAAS,SAEpCqZ,QAAiBC,MAAMC,OAAOtW,GAAS,CAC3CyR,SACA0D,QAAS,IACJA,KACCC,GAAuB,wBAAZA,EACX,CAAE,eAAgBA,GAClB,IAENmB,KAAMJ,EAAgCN,EAAeF,IAIrDS,IACCb,GACIA,EAAea,EAASI,QACzBJ,EAASI,OAAS,KAAOJ,EAASI,QAAU,MAEhDd,GAAW,EACXL,GAAWA,EAAQ,CAAEe,aACrB1hB,EAAO4hB,OAAOF,EAASI,SAEvBlB,GAAaA,EAAU,CAAEc,YAE7B,CAAE,MAAOzX,GACP+W,GAAW,EACXL,GAAWA,EAAQ,CAAE1W,SACvB,GAzDEnF,CA2DHpE,GAECsgB,GAAYtb,EAAMZ,UACpBY,EAAMZ,QAAQwQ,UAAUC,MAAMlJ,KAAK,CACjC4I,oBAAoB,IAEtBvP,EAAMZ,QAAQ8U,SAAS,cAAe,CACpC5Z,WASN,OAJAuE,EAAMiB,UAAU,KACd8a,GAAW,IACV,IAEIF,EACL7b,EAAAwd,cAAAxd,EAAAyd,SAAA,KACG5B,EAAO,CACNW,YAIJxc,EAAAwd,cAAA,OAAA,CACEE,WAAY5B,EACZ/U,OAAQA,EACRyR,OAAQA,EACR2D,QAASA,EACTH,SAAUQ,KACND,GAEHN,EAGP,iBZjEE9a,IAEA,MAAM8a,SAAEA,KAAa/e,GAASiE,EAC9B,OACEnB,EAAAwd,cAACzd,EAAgB4d,SAAQ,CAAChiB,MAAOuB,GAC9B+e,Y4D/Bc,EAMnB1b,UACAhE,QACAsf,YAEAA,EACE7X,EAAS,CAAEzD,UAAS/D,KAAMD,+FCYxB,SAOJ4E,GAOA,MAAMC,EAAUjB,KACVI,QACJA,EAAUa,EAAQb,QAAO/D,KACzBA,EAAIohB,QACJA,EAAU,KAAI/Y,iBACdA,EAAgBO,MAChBA,GACEjE,GACG0I,EAAQgU,GAAa7d,EAAMwB,SAASjB,EAAQyY,eAAexc,IAC5DshB,EAAM9d,EAAM2B,OAChBpB,EAAQyY,eAAexc,GAAMwG,IAAI4W,KAG7BmE,EAAY/d,EAAM2B,QAAO,GAE/BpB,EAAQoC,OAAOoC,MAAMhC,IAAIvG,GAEzBwD,EAAMwC,QACJ,IACE4C,GACAyE,EAAOtK,QAAU,GAChBgB,EAA2D4E,SAC1D3I,EACA4I,GAEJ,CAAC7E,EAAS/D,EAAMqN,EAAOtK,OAAQ6F,IAGjCrE,EACE,IACER,EAAQwQ,UAAUhM,MAAMiD,UAAU,CAChCF,KAAM,EACJtD,SACAhI,KAAMwhB,MAKN,GAAIA,IAAmBxhB,IAASwhB,EAAgB,CAC9C,MAAM5G,EAAcvY,EAAI2F,EAAQhI,GAC5BR,MAAMC,QAAQmb,KAChByG,EAAUzG,GACV0G,EAAIzb,QAAU+U,EAAYpU,IAAI4W,IAElC,KAED1R,YACL,CAAC3H,EAAS/D,IAGZ,MAAMyhB,EAAeje,EAAM4F,YAMvBsY,IAEAH,EAAU1b,SAAU,EACpB9B,EAAQgY,eAAe/b,EAAM0hB,IAE/B,CAAC3d,EAAS/D,IAqRZ,OA5GAwD,EAAMiB,UAAU,KAQd,GAPAV,EAAQuG,OAAOC,QAAS,EAExBsF,GAAU7P,EAAM+D,EAAQoC,SACtBpC,EAAQwQ,UAAUC,MAAMlJ,KAAK,IACxBvH,EAAQkB,aAIbsc,EAAU1b,WACRmJ,GAAmBjL,EAAQkG,SAASgF,MAAMC,YAC1CnL,EAAQkB,WAAW+O,eACpBhF,GAAmBjL,EAAQkG,SAASyJ,gBAAgBxE,WAErD,GAAInL,EAAQkG,SAAS2K,SACnB7Q,EAAQ8Q,WAAW,CAAC7U,IAAO0c,KAAMja,IAC/B,MAAMyG,EAAQ7G,EAAII,EAAOkD,OAAQ3F,GAC3B2hB,EAAgBtf,EAAI0B,EAAQkB,WAAWU,OAAQ3F,IAGnD2hB,GACMzY,GAASyY,EAAc1iB,MACxBiK,IACEyY,EAAc1iB,OAASiK,EAAMjK,MAC5B0iB,EAAc7X,UAAYZ,EAAMY,SACpCZ,GAASA,EAAMjK,QAEnBiK,EACItG,EAAImB,EAAQkB,WAAWU,OAAQ3F,EAAMkJ,GACrCyD,GAAM5I,EAAQkB,WAAWU,OAAQ3F,GACrC+D,EAAQwQ,UAAUC,MAAMlJ,KAAK,CAC3B3F,OAAQ5B,EAAQkB,WAAWU,gBAI5B,CACL,MAAM6D,EAAenH,EAAI0B,EAAQ0F,QAASzJ,IAExCwJ,IACAA,EAAME,IAEJsF,GAAmBjL,EAAQkG,SAASyJ,gBAAgBxE,YACpDF,GAAmBjL,EAAQkG,SAASgF,MAAMC,YAG5C+B,GACEzH,EACAzF,EAAQoC,OAAOtB,SACfd,EAAQkE,YACRlE,EAAQkG,SAASyK,eAAiBrR,EAClCU,EAAQkG,SAASmH,2BACjB,GACAsL,KACCxT,IACE8C,EAAc9C,IACfnF,EAAQwQ,UAAUC,MAAMlJ,KAAK,CAC3B3F,OAAQgL,GACN5M,EAAQkB,WAAWU,OACnBuD,EACAlJ,KAKZ,CAGF+D,EAAQwQ,UAAUC,MAAMlJ,KAAK,CAC3BtL,OACAgI,OAAQvH,EAAYsD,EAAQkE,eAG9BlE,EAAQoC,OAAOwD,OACbuG,GAAsBnM,EAAQ0F,QAAS,CAACH,EAAK9H,KAC3C,GACEuC,EAAQoC,OAAOwD,OACfnI,EAAIyO,WAAWlM,EAAQoC,OAAOwD,QAC9BL,EAAIK,MAGJ,OADAL,EAAIK,QACG,IAKb5F,EAAQoC,OAAOwD,MAAQ,GAEvB5F,EAAQgC,YACRwb,EAAU1b,SAAU,GACnB,CAACwH,EAAQrN,EAAM+D,IAElBP,EAAMiB,UAAU,MACbpC,EAAI0B,EAAQkE,YAAajI,IAAS+D,EAAQgY,eAAe/b,GAEnD,KAQL+D,EAAQkG,SAAS5B,kBAAoBA,EACjCtE,EAAQoG,WAAWnK,GARD,EAACA,EAAyBb,KAC9C,MAAMqK,EAAenH,EAAI0B,EAAQ0F,QAASzJ,GACtCwJ,GAASA,EAAME,KACjBF,EAAME,GAAGW,MAAQlL,IAMjBiL,CAAcpK,GAAM,KAEzB,CAACA,EAAM+D,EAASqd,EAAS/Y,IAErB,CACLuZ,KAAMpe,EAAM4F,YAlMD,CAAC8V,EAAgBC,KAC5B,MAAMuC,EAA0B3d,EAAQyY,eAAexc,GACvDif,GAAYyC,EAAyBxC,EAAQC,GAC7CF,GAAYqC,EAAIzb,QAASqZ,EAAQC,GACjCsC,EAAaC,GACbL,EAAUK,GACV3d,EAAQgY,eACN/b,EACA0hB,EACAzC,GACA,CACE7C,KAAM8C,EACN7C,KAAM8C,IAER,IAoL4B,CAACsC,EAAczhB,EAAM+D,IACnD8d,KAAMre,EAAM4F,YAjLD,CAAC4L,EAAcsJ,KAC1B,MAAMoD,EAA0B3d,EAAQyY,eAAexc,GACvDqe,GAAYqD,EAAyB1M,EAAMsJ,GAC3CD,GAAYiD,EAAIzb,QAASmP,EAAMsJ,GAC/BmD,EAAaC,GACbL,EAAUK,GACV3d,EAAQgY,eACN/b,EACA0hB,EACArD,GACA,CACEjC,KAAMpH,EACNqH,KAAMiC,IAER,IAmK4B,CAACmD,EAAczhB,EAAM+D,IACnD+d,QAASte,EAAM4F,YA7PD,CACdjK,EAGAwO,KAEA,MAAMoU,EAAe7W,EAAsBzK,EAAYtB,IACjDuiB,EAA0BlD,GAC9Bza,EAAQyY,eAAexc,GACvB+hB,GAEFhe,EAAQoC,OAAOwD,MAAQoU,GAAkB/d,EAAM,EAAG2N,GAClD2T,EAAIzb,QAAU2Y,GAAU8C,EAAIzb,QAASkc,EAAavb,IAAI4W,KACtDqE,EAAaC,GACbL,EAAUK,GACV3d,EAAQgY,eAAe/b,EAAM0hB,EAAyBlD,GAAW,CAC/DpC,KAAM+B,GAAehf,MA6Oa,CAACsiB,EAAczhB,EAAM+D,IACzD0c,OAAQjd,EAAM4F,YAtRD,CACbjK,EAGAwO,KAEA,MAAMqU,EAAc9W,EAAsBzK,EAAYtB,IAChDuiB,EAA0BxD,GAC9Bna,EAAQyY,eAAexc,GACvBgiB,GAEFje,EAAQoC,OAAOwD,MAAQoU,GACrB/d,EACA0hB,EAAwB3e,OAAS,EACjC4K,GAEF2T,EAAIzb,QAAUqY,GAASoD,EAAIzb,QAASmc,EAAYxb,IAAI4W,KACpDqE,EAAaC,GACbL,EAAUK,GACV3d,EAAQgY,eAAe/b,EAAM0hB,EAAyBxD,GAAU,CAC9D9B,KAAM+B,GAAehf,MAkQW,CAACsiB,EAAczhB,EAAM+D,IACvDke,OAAQze,EAAM4F,YA3OAvG,IACd,MAAM6e,EAEAjD,GAAc1a,EAAQyY,eAAexc,GAAO6C,GAClDye,EAAIzb,QAAU4Y,GAAc6C,EAAIzb,QAAShD,GACzC4e,EAAaC,GACbL,EAAUK,IACTliB,MAAMC,QAAQ4C,EAAI0B,EAAQ0F,QAASzJ,KAClC4C,EAAImB,EAAQ0F,QAASzJ,OAAM6B,GAC7BkC,EAAQgY,eAAe/b,EAAM0hB,EAAyBjD,GAAe,CACnErC,KAAMvZ,KAiO0B,CAAC4e,EAAczhB,EAAM+D,IACvDqa,OAAQ5a,EAAM4F,YA9ND,CACbvG,EACA1D,EAGAwO,KAEA,MAAMuU,EAAchX,EAAsBzK,EAAYtB,IAChDuiB,EAA0BS,GAC9Bpe,EAAQyY,eAAexc,GACvB6C,EACAqf,GAEFne,EAAQoC,OAAOwD,MAAQoU,GAAkB/d,EAAM6C,EAAO8K,GACtD2T,EAAIzb,QAAUsc,GAASb,EAAIzb,QAAShD,EAAOqf,EAAY1b,IAAI4W,KAC3DqE,EAAaC,GACbL,EAAUK,GACV3d,EAAQgY,eAAe/b,EAAM0hB,EAAyBS,GAAU,CAC9D/F,KAAMvZ,EACNwZ,KAAM8B,GAAehf,MA2MW,CAACsiB,EAAczhB,EAAM+D,IACvDqe,OAAQ5e,EAAM4F,YApKD,CACbvG,EACA1D,KAEA,MAAM4I,EAActH,EAAYtB,GAC1BuiB,EAA0BtC,GAC9Brb,EAAQyY,eAENxc,GACF6C,EACAkF,GAEFuZ,EAAIzb,QAAU,IAAI6b,GAAyBlb,IAAI,CAAC6b,EAAM1D,IACnD0D,GAAQ1D,IAAM9b,EAAuBye,EAAIzb,QAAQ8Y,GAA3BvB,MAEzBqE,EAAaC,GACbL,EAAU,IAAIK,IACd3d,EAAQgY,eACN/b,EACA0hB,EACAtC,GACA,CACEhD,KAAMvZ,EACNwZ,KAAMtU,IAER,GACA,IA0IgC,CAAC0Z,EAAczhB,EAAM+D,IACvD5B,QAASqB,EAAM4F,YAtIfjK,IAIA,MAAMuiB,EAA0BxW,EAAsBzK,EAAYtB,IAClEmiB,EAAIzb,QAAU6b,EAAwBlb,IAAI4W,IAC1CqE,EAAa,IAAIC,IACjBL,EAAU,IAAIK,IACd3d,EAAQgY,eACN/b,EACA,IAAI0hB,GACAhhB,GAAeA,EACnB,IACA,GACA,IAwHkC,CAAC+gB,EAAczhB,EAAM+D,IACzDsJ,OAAQ7J,EAAMwC,QACZ,IACEqH,EAAO7G,IAAI,CAACgD,EAAO3G,KAAK,IACnB2G,EACH4X,CAACA,GAAUE,EAAIzb,QAAQhD,IAAUua,QAErC,CAAC/P,EAAQ+T,IAGf,YCvZM,SAKJzc,EAAkE,IAElE,MAAM2d,EAAe9e,EAAM2B,YAEzBtD,GACI0gB,EAAU/e,EAAM2B,YAA4BtD,IAC3CiC,EAAWiB,GAAmBvB,EAAMwB,SAAkC,CAC3EI,SAAS,EACTK,cAAc,EACdJ,UAAW6G,EAAWvH,EAAMT,eAC5B8P,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBxO,SAAS,EACToO,YAAa,EACbxO,YAAa,CAAA,EACbC,cAAe,CAAA,EACfC,iBAAkB,CAAA,EAClBG,OAAQhB,EAAMgB,QAAU,CAAA,EACxBd,SAAUF,EAAME,WAAY,EAC5BkP,SAAS,EACT7P,cAAegI,EAAWvH,EAAMT,oBAC5BrC,EACA8C,EAAMT,gBAGZ,IAAKoe,EAAazc,QAChB,GAAIlB,EAAMwY,YACRmF,EAAazc,QAAU,IAClBlB,EAAMwY,YACTrZ,aAGEa,EAAMT,gBAAkBgI,EAAWvH,EAAMT,gBAC3CS,EAAMwY,YAAY1B,MAAM9W,EAAMT,cAAeS,EAAMgY,kBAEhD,CACL,MAAMQ,YAAEA,KAAgB4C,GAASnM,GAAkBjP,GAEnD2d,EAAazc,QAAU,IAClBka,EACHjc,YAEJ,CAGF,MAAMC,EAAUue,EAAazc,QAAQ9B,QAwFrC,OAvFAA,EAAQkG,SAAWtF,EAEnBJ,EAA0B,KACxB,MAAMie,EAAMze,EAAQ6B,WAAW,CAC7B9B,UAAWC,EAAQO,gBACnBwB,SAAU,IAAMf,EAAgB,IAAKhB,EAAQkB,aAC7CqU,cAAc,IAUhB,OAPAvU,EAAiBrE,IAAI,IAChBA,EACHqT,SAAS,KAGXhQ,EAAQkB,WAAW8O,SAAU,EAEtByO,GACN,CAACze,IAEJP,EAAMiB,UACJ,IAAMV,EAAQ6Y,aAAajY,EAAME,UACjC,CAACd,EAASY,EAAME,WAGlBrB,EAAMiB,UAAU,KACVE,EAAMsK,OACRlL,EAAQkG,SAASgF,KAAOtK,EAAMsK,MAE5BtK,EAAM+O,iBACR3P,EAAQkG,SAASyJ,eAAiB/O,EAAM+O,iBAEzC,CAAC3P,EAASY,EAAMsK,KAAMtK,EAAM+O,iBAE/BlQ,EAAMiB,UAAU,KACVE,EAAMgB,SACR5B,EAAQwY,WAAW5X,EAAMgB,QACzB5B,EAAQqW,gBAET,CAACrW,EAASY,EAAMgB,SAEnBnC,EAAMiB,UAAU,KACdE,EAAM0D,kBACJtE,EAAQwQ,UAAUC,MAAMlJ,KAAK,CAC3BtD,OAAQjE,EAAQ+D,eAEnB,CAAC/D,EAASY,EAAM0D,mBAEnB7E,EAAMiB,UAAU,KACd,GAAIV,EAAQO,gBAAgBc,QAAS,CACnC,MAAMA,EAAUrB,EAAQ4R,YACpBvQ,IAAYtB,EAAUsB,SACxBrB,EAAQwQ,UAAUC,MAAMlJ,KAAK,CAC3BlG,WAGN,GACC,CAACrB,EAASD,EAAUsB,UAEvB5B,EAAMiB,UAAU,KACVE,EAAMqD,SAAWpB,EAAUjC,EAAMqD,OAAQua,EAAQ1c,UACnD9B,EAAQ+W,OAAOnW,EAAMqD,OAAQ,CAC3B0T,eAAe,KACZ3X,EAAQkG,SAAS0S,eAEtB4F,EAAQ1c,QAAUlB,EAAMqD,OACxBjD,EAAiByP,IAAK,IAAWA,MAEjCzQ,EAAQ0Y,uBAET,CAAC1Y,EAASY,EAAMqD,SAEnBxE,EAAMiB,UAAU,KACTV,EAAQuG,OAAOD,QAClBtG,EAAQgC,YACRhC,EAAQuG,OAAOD,OAAQ,GAGrBtG,EAAQuG,OAAOhE,QACjBvC,EAAQuG,OAAOhE,OAAQ,EACvBvC,EAAQwQ,UAAUC,MAAMlJ,KAAK,IAAKvH,EAAQkB,cAG5ClB,EAAQoE,qBAGVma,EAAazc,QAAQ/B,UAAYD,EAAkBC,EAAWC,GAEvDue,EAAazc,OACtB"}