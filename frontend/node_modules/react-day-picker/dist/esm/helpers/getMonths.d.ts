import type { DateLib } from "../classes/DateLib.js";
import { CalendarMonth } from "../classes/index.js";
import type { DayPickerProps } from "../types/index.js";
/**
 * Returns the months to display in the calendar.
 *
 * This function generates `CalendarMonth` objects for each month to be
 * displayed, including their weeks and days, based on the provided display
 * months and dates.
 *
 * @param displayMonths The months (as dates) to display in the calendar.
 * @param dates The dates to display in the calendar.
 * @param props Options from the DayPicker props context.
 * @param dateLib The date library to use for date manipulation.
 * @returns An array of `CalendarMonth` objects representing the months to
 *   display.
 */
export declare function getMonths(displayMonths: Date[], dates: Date[], props: Pick<DayPickerProps, "broadcastCalendar" | "fixedWeeks" | "ISOWeek" | "reverseMonths">, dateLib: DateLib): CalendarMonth[];
