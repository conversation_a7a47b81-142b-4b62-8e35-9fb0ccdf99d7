import { type DateLib } from "../classes/DateLib.js";
/**
 * Formats the year for the dropdown option label.
 *
 * @param year The year to format.
 * @param dateLib The date library to use for formatting. Defaults to
 *   `defaultDateLib`.
 * @returns The formatted year as a string.
 * @group Formatters
 * @see https://daypicker.dev/docs/translation#custom-formatters
 */
export declare function formatYearDropdown(year: Date, dateLib?: DateLib): string;
/**
 * @private
 * @deprecated Use `formatYearDropdown` instead.
 * @group Formatters
 */
export declare const formatYearCaption: typeof formatYearDropdown;
