"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaptionLabel = CaptionLabel;
const react_1 = __importDefault(require("react"));
/**
 * Render the label in the month caption.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function CaptionLabel(props) {
    return react_1.default.createElement("span", { ...props });
}
//# sourceMappingURL=CaptionLabel.js.map