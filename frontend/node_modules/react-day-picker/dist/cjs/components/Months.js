"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Months = Months;
const react_1 = __importDefault(require("react"));
/**
 * Render a container wrapping the month grids.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function Months(props) {
    return react_1.default.createElement("div", { ...props });
}
//# sourceMappingURL=Months.js.map