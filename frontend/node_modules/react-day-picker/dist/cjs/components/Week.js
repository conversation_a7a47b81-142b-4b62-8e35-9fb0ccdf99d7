"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Week = Week;
const react_1 = __importDefault(require("react"));
/**
 * Render a table row representing a week in the calendar.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function Week(props) {
    const { week, ...trProps } = props;
    return react_1.default.createElement("tr", { ...trProps });
}
//# sourceMappingURL=Week.js.map