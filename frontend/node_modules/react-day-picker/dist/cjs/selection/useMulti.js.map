{"version": 3, "file": "useMulti.js", "sourceRoot": "", "sources": ["../../../src/selection/useMulti.tsx"], "names": [], "mappings": ";;AAoBA,4BA8DC;AA/ED,4EAAsE;AAQtE;;;;;;;;GAQG;AACH,SAAgB,QAAQ,CACtB,KAAQ,EACR,OAAgB;IAEhB,MAAM,EACJ,QAAQ,EAAE,iBAAiB,EAC3B,QAAQ,EACR,QAAQ,EACT,GAAG,KAAmB,CAAC;IAExB,MAAM,CAAC,kBAAkB,EAAE,WAAW,CAAC,GAAG,IAAA,0CAAkB,EAC1D,iBAAiB,EACjB,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CACzC,CAAC;IAEF,MAAM,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,CAAC;IAEpE,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IAE9B,MAAM,UAAU,GAAG,CAAC,IAAU,EAAE,EAAE;QAChC,OAAO,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC;IAC5D,CAAC,CAAC;IAEF,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAmB,CAAC;IAEzC,MAAM,MAAM,GAAG,CACb,WAAiB,EACjB,SAAoB,EACpB,CAAyC,EACzC,EAAE;QACF,IAAI,QAAQ,GAAuB,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5B,IAAI,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC7B,gCAAgC;gBAChC,OAAO;YACT,CAAC;YACD,IAAI,QAAQ,IAAI,QAAQ,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,6CAA6C;gBAC7C,OAAO;YACT,CAAC;YACD,QAAQ,GAAG,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YACN,IAAI,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC7B,iDAAiD;gBACjD,QAAQ,GAAG,CAAC,WAAW,CAAC,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,gCAAgC;gBAChC,QAAQ,GAAG,CAAC,GAAG,QAAQ,EAAE,WAAW,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,WAAW,CAAC,QAAQ,CAAC,CAAC;QACxB,CAAC;QACD,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAChD,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;IAEF,OAAO;QACL,QAAQ;QACR,MAAM;QACN,UAAU;KACK,CAAC;AACpB,CAAC"}