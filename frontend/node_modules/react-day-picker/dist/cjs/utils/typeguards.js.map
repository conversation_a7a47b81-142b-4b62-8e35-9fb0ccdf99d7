{"version": 3, "file": "typeguards.js", "sourceRoot": "", "sources": ["../../../src/utils/typeguards.ts"], "names": [], "mappings": ";;AAgBA,wCAOC;AASD,kCAEC;AASD,0CAEC;AASD,4CAEC;AASD,0CAEC;AAUD,oCAKC;AAzED;;;;;;GAMG;AACH,SAAgB,cAAc,CAAC,OAAgB;IAC7C,OAAO,OAAO,CACZ,OAAO;QACL,OAAO,OAAO,KAAK,QAAQ;QAC3B,QAAQ,IAAI,OAAO;QACnB,OAAO,IAAI,OAAO,CACrB,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,WAAW,CAAC,KAAc;IACxC,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC;AACxE,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,CAAC,CAAC;AACzE,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,gBAAgB,CAAC,KAAc;IAC7C,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAI,KAAK,CAAC,CAAC;AAC1E,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,WAAW,IAAI,KAAK,CAAC,CAAC;AAC7E,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,YAAY,CAC1B,KAAc,EACd,OAAgB;IAEhB,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC7D,CAAC"}