import type { DateLib } from "../classes/DateLib.js";
import type { DateAfter, DateBefore, DateInterval, DateRange, DayOfWeek } from "../types/index.js";
/**
 * Checks if the given value is of type {@link DateInterval}.
 *
 * @param matcher - The value to check.
 * @returns `true` if the value is a {@link DateInterval}, otherwise `false`.
 * @group Utilities
 */
export declare function isDateInterval(matcher: unknown): matcher is DateInterval;
/**
 * Checks if the given value is of type {@link DateRange}.
 *
 * @param value - The value to check.
 * @returns `true` if the value is a {@link DateRange}, otherwise `false`.
 * @group Utilities
 */
export declare function isDateRange(value: unknown): value is DateRange;
/**
 * Checks if the given value is of type {@link DateAfter}.
 *
 * @param value - The value to check.
 * @returns `true` if the value is a {@link DateAfter}, otherwise `false`.
 * @group Utilities
 */
export declare function isDateAfterType(value: unknown): value is DateAfter;
/**
 * Checks if the given value is of type {@link DateBefore}.
 *
 * @param value - The value to check.
 * @returns `true` if the value is a {@link DateBefore}, otherwise `false`.
 * @group Utilities
 */
export declare function isDateBeforeType(value: unknown): value is DateBefore;
/**
 * Checks if the given value is of type {@link DayOfWeek}.
 *
 * @param value - The value to check.
 * @returns `true` if the value is a {@link DayOfWeek}, otherwise `false`.
 * @group Utilities
 */
export declare function isDayOfWeekType(value: unknown): value is DayOfWeek;
/**
 * Checks if the given value is an array of valid dates.
 *
 * @private
 * @param value - The value to check.
 * @param dateLib - The date utility library instance.
 * @returns `true` if the value is an array of valid dates, otherwise `false`.
 */
export declare function isDatesArray(value: unknown, dateLib: DateLib): value is Date[];
