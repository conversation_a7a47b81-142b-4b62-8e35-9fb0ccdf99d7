"use strict";
exports.frCH = void 0;
var _index = require("./fr/_lib/formatDistance.cjs");
var _index2 = require("./fr/_lib/localize.cjs");
var _index3 = require("./fr/_lib/match.cjs");

var _index4 = require("./fr-CH/_lib/formatLong.cjs");
var _index5 = require("./fr-CH/_lib/formatRelative.cjs"); // Same as fr
// Unique for fr-CH
/**
 * @category Locales
 * @summary French locale (Switzerland).
 * @language French
 * @iso-639-2 fra
 * <AUTHOR> [@izeau](https://github.com/izeau)
 * <AUTHOR> [@fbonzon](https://github.com/fbonzon)
 * <AUTHOR> [@vanvuongngo](https://github.com/vanvuongngo)
 * <AUTHOR> [@dcbn](https://github.com/dcbn)
 */
const frCH = (exports.frCH = {
  code: "fr-CH",
  formatDistance: _index.formatDistance,
  formatLong: _index4.formatLong,
  formatRelative: _index5.formatRelative,
  localize: _index2.localize,
  match: _index3.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
