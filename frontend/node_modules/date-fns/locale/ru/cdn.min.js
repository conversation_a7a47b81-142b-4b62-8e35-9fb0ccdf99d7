(()=>{var R;function W(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);C&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),G.push.apply(G,J)}return G}function M(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?W(Object(G),!0).forEach(function(J){L(B,J,G[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):W(Object(G)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(G,J))})}return B}function L(B,C,G){if(C=v(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function v(B){var C=F(B,"string");return E(C)=="symbol"?C:String(C)}function F(B,C){if(E(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var J=G.call(B,C||"default");if(E(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}function b(B,C){return _(B)||k(B,C)||h(B,C)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(B,C){if(!B)return;if(typeof B==="string")return $(B,C);var G=Object.prototype.toString.call(B).slice(8,-1);if(G==="Object"&&B.constructor)G=B.constructor.name;if(G==="Map"||G==="Set")return Array.from(B);if(G==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(G))return $(B,C)}function $(B,C){if(C==null||C>B.length)C=B.length;for(var G=0,J=new Array(C);G<C;G++)J[G]=B[G];return J}function k(B,C){var G=B==null?null:typeof Symbol!="undefined"&&B[Symbol.iterator]||B["@@iterator"];if(G!=null){var J,X,Z,U,H=[],q=!0,Y=!1;try{if(Z=(G=G.call(B)).next,C===0){if(Object(G)!==G)return;q=!1}else for(;!(q=(J=Z.call(G)).done)&&(H.push(J.value),H.length!==C);q=!0);}catch(K){Y=!0,X=K}finally{try{if(!q&&G.return!=null&&(U=G.return(),Object(U)!==U))return}finally{if(Y)throw X}}return H}}function _(B){if(Array.isArray(B))return B}function E(B){return E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},E(B)}var g=Object.defineProperty,LB=function B(C,G){for(var J in G)g(C,J,{get:G[J],enumerable:!0,configurable:!0,set:function X(Z){return G[J]=function(){return Z}}})};function A(B,C){if(B.one!==void 0&&C===1)return B.one;var G=C%10,J=C%100;if(G===1&&J!==11)return B.singularNominative.replace("{{count}}",String(C));else if(G>=2&&G<=4&&(J<10||J>20))return B.singularGenitive.replace("{{count}}",String(C));else return B.pluralGenitive.replace("{{count}}",String(C))}function Q(B){return function(C,G){if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)if(B.future)return A(B.future,C);else return"\u0447\u0435\u0440\u0435\u0437 "+A(B.regular,C);else if(B.past)return A(B.past,C);else return A(B.regular,C)+" \u043D\u0430\u0437\u0430\u0434";else return A(B.regular,C)}}var m={lessThanXSeconds:Q({regular:{one:"\u043C\u0435\u043D\u044C\u0448\u0435 \u0441\u0435\u043A\u0443\u043D\u0434\u044B",singularNominative:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",singularGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434",pluralGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},future:{one:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularNominative:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),xSeconds:Q({regular:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0430",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},past:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443 \u043D\u0430\u0437\u0430\u0434",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B \u043D\u0430\u0437\u0430\u0434",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u043D\u0430\u0437\u0430\u0434"},future:{singularNominative:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),halfAMinute:function B(C,G){if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)return"\u0447\u0435\u0440\u0435\u0437 \u043F\u043E\u043B\u043C\u0438\u043D\u0443\u0442\u044B";else return"\u043F\u043E\u043B\u043C\u0438\u043D\u0443\u0442\u044B \u043D\u0430\u0437\u0430\u0434";return"\u043F\u043E\u043B\u043C\u0438\u043D\u0443\u0442\u044B"},lessThanXMinutes:Q({regular:{one:"\u043C\u0435\u043D\u044C\u0448\u0435 \u043C\u0438\u043D\u0443\u0442\u044B",singularNominative:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u043C\u0438\u043D\u0443\u0442\u044B",singularGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u043C\u0438\u043D\u0443\u0442",pluralGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u043C\u0438\u043D\u0443\u0442"},future:{one:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 \u043C\u0438\u043D\u0443\u0442\u0443",singularNominative:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442\u0443",singularGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442\u044B",pluralGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442"}}),xMinutes:Q({regular:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0430",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u044B",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442"},past:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0443 \u043D\u0430\u0437\u0430\u0434",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u044B \u043D\u0430\u0437\u0430\u0434",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u043D\u0430\u0437\u0430\u0434"},future:{singularNominative:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442\u0443",singularGenitive:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442\u044B",pluralGenitive:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442"}}),aboutXHours:Q({regular:{singularNominative:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0447\u0430\u0441\u0430",singularGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0447\u0430\u0441\u043E\u0432",pluralGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0447\u0430\u0441\u043E\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0447\u0430\u0441",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0447\u0430\u0441\u0430",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0447\u0430\u0441\u043E\u0432"}}),xHours:Q({regular:{singularNominative:"{{count}} \u0447\u0430\u0441",singularGenitive:"{{count}} \u0447\u0430\u0441\u0430",pluralGenitive:"{{count}} \u0447\u0430\u0441\u043E\u0432"}}),xDays:Q({regular:{singularNominative:"{{count}} \u0434\u0435\u043D\u044C",singularGenitive:"{{count}} \u0434\u043D\u044F",pluralGenitive:"{{count}} \u0434\u043D\u0435\u0439"}}),aboutXWeeks:Q({regular:{singularNominative:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043D\u0435\u0434\u0435\u043B\u0438",singularGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043D\u0435\u0434\u0435\u043B\u044C",pluralGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043D\u0435\u0434\u0435\u043B\u044C"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043D\u0435\u0434\u0435\u043B\u044E",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043D\u0435\u0434\u0435\u043B\u0438",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043D\u0435\u0434\u0435\u043B\u044C"}}),xWeeks:Q({regular:{singularNominative:"{{count}} \u043D\u0435\u0434\u0435\u043B\u044F",singularGenitive:"{{count}} \u043D\u0435\u0434\u0435\u043B\u0438",pluralGenitive:"{{count}} \u043D\u0435\u0434\u0435\u043B\u044C"}}),aboutXMonths:Q({regular:{singularNominative:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043C\u0435\u0441\u044F\u0446\u0430",singularGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043C\u0435\u0441\u044F\u0446\u0435\u0432",pluralGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043C\u0435\u0441\u044F\u0446\u0435\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446\u0430",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446\u0435\u0432"}}),xMonths:Q({regular:{singularNominative:"{{count}} \u043C\u0435\u0441\u044F\u0446",singularGenitive:"{{count}} \u043C\u0435\u0441\u044F\u0446\u0430",pluralGenitive:"{{count}} \u043C\u0435\u0441\u044F\u0446\u0435\u0432"}}),aboutXYears:Q({regular:{singularNominative:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0433\u043E\u0434\u0430",singularGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043B\u0435\u0442",pluralGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043B\u0435\u0442"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043B\u0435\u0442"}}),xYears:Q({regular:{singularNominative:"{{count}} \u0433\u043E\u0434",singularGenitive:"{{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"{{count}} \u043B\u0435\u0442"}}),overXYears:Q({regular:{singularNominative:"\u0431\u043E\u043B\u044C\u0448\u0435 {{count}} \u0433\u043E\u0434\u0430",singularGenitive:"\u0431\u043E\u043B\u044C\u0448\u0435 {{count}} \u043B\u0435\u0442",pluralGenitive:"\u0431\u043E\u043B\u044C\u0448\u0435 {{count}} \u043B\u0435\u0442"},future:{singularNominative:"\u0431\u043E\u043B\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u0431\u043E\u043B\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"\u0431\u043E\u043B\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u043B\u0435\u0442"}}),almostXYears:Q({regular:{singularNominative:"\u043F\u043E\u0447\u0442\u0438 {{count}} \u0433\u043E\u0434",singularGenitive:"\u043F\u043E\u0447\u0442\u0438 {{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"\u043F\u043E\u0447\u0442\u0438 {{count}} \u043B\u0435\u0442"},future:{singularNominative:"\u043F\u043E\u0447\u0442\u0438 \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u043F\u043E\u0447\u0442\u0438 \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"\u043F\u043E\u0447\u0442\u0438 \u0447\u0435\u0440\u0435\u0437 {{count}} \u043B\u0435\u0442"}})},y=function B(C,G,J){return m[C](G,J)};function x(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,J=B.formats[G]||B.formats[B.defaultWidth];return J}}var c={full:"EEEE, d MMMM y '\u0433.'",long:"d MMMM y '\u0433.'",medium:"d MMM y '\u0433.'",short:"dd.MM.y"},p={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},u={any:"{{date}}, {{time}}"},d={date:x({formats:c,defaultWidth:"full"}),time:x({formats:p,defaultWidth:"full"}),dateTime:x({formats:u,defaultWidth:"any"})},vB=7,l=365.2425,i=Math.pow(10,8)*24*60*60*1000,FB=-i,bB=604800000,fB=86400000,hB=60000,kB=3600000,_B=1000,gB=525600,mB=43200,yB=1440,cB=60,pB=3,uB=12,dB=4,s=3600,lB=60,j=s*24,iB=j*7,r=j*l,n=r/12,sB=n*3,O=Symbol.for("constructDateFrom");function T(B,C){if(typeof B==="function")return B(C);if(B&&E(B)==="object"&&O in B)return B[O](C);if(B instanceof Date)return new B.constructor(C);return new Date(C)}function o(B){for(var C=arguments.length,G=new Array(C>1?C-1:0),J=1;J<C;J++)G[J-1]=arguments[J];var X=T.bind(null,B||G.find(function(Z){return E(Z)==="object"}));return G.map(X)}function a(){return P}function rB(B){P=B}var P={};function t(B,C){return T(C||B,B)}function z(B,C){var G,J,X,Z,U,H,q=a(),Y=(G=(J=(X=(Z=C===null||C===void 0?void 0:C.weekStartsOn)!==null&&Z!==void 0?Z:C===null||C===void 0||(U=C.locale)===null||U===void 0||(U=U.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&X!==void 0?X:q.weekStartsOn)!==null&&J!==void 0?J:(H=q.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&G!==void 0?G:0,K=t(B,C===null||C===void 0?void 0:C.in),N=K.getDay(),wB=(N<Y?7:0)+N-Y;return K.setDate(K.getDate()-wB),K.setHours(0,0,0,0),K}function D(B,C,G){var J=o(G===null||G===void 0?void 0:G.in,B,C),X=b(J,2),Z=X[0],U=X[1];return+z(Z,G)===+z(U,G)}function e(B){var C=S[B];switch(B){case 0:return"'\u0432 \u043F\u0440\u043E\u0448\u043B\u043E\u0435 "+C+" \u0432' p";case 1:case 2:case 4:return"'\u0432 \u043F\u0440\u043E\u0448\u043B\u044B\u0439 "+C+" \u0432' p";case 3:case 5:case 6:return"'\u0432 \u043F\u0440\u043E\u0448\u043B\u0443\u044E "+C+" \u0432' p"}}function w(B){var C=S[B];if(B===2)return"'\u0432\u043E "+C+" \u0432' p";else return"'\u0432 "+C+" \u0432' p"}function BB(B){var C=S[B];switch(B){case 0:return"'\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0435 "+C+" \u0432' p";case 1:case 2:case 4:return"'\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439 "+C+" \u0432' p";case 3:case 5:case 6:return"'\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E "+C+" \u0432' p"}}var S=["\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u0435\u0434\u0443","\u0447\u0435\u0442\u0432\u0435\u0440\u0433","\u043F\u044F\u0442\u043D\u0438\u0446\u0443","\u0441\u0443\u0431\u0431\u043E\u0442\u0443"],CB={lastWeek:function B(C,G,J){var X=C.getDay();if(D(C,G,J))return w(X);else return e(X)},yesterday:"'\u0432\u0447\u0435\u0440\u0430 \u0432' p",today:"'\u0441\u0435\u0433\u043E\u0434\u043D\u044F \u0432' p",tomorrow:"'\u0437\u0430\u0432\u0442\u0440\u0430 \u0432' p",nextWeek:function B(C,G,J){var X=C.getDay();if(D(C,G,J))return w(X);else return BB(X)},other:"P"},GB=function B(C,G,J,X){var Z=CB[C];if(typeof Z==="function")return Z(G,J,X);return Z};function V(B){return function(C,G){var J=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",X;if(J==="formatting"&&B.formattingValues){var Z=B.defaultFormattingWidth||B.defaultWidth,U=G!==null&&G!==void 0&&G.width?String(G.width):Z;X=B.formattingValues[U]||B.formattingValues[Z]}else{var H=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;X=B.values[q]||B.values[H]}var Y=B.argumentCallback?B.argumentCallback(C):C;return X[Y]}}var JB={narrow:["\u0434\u043E \u043D.\u044D.","\u043D.\u044D."],abbreviated:["\u0434\u043E \u043D. \u044D.","\u043D. \u044D."],wide:["\u0434\u043E \u043D\u0430\u0448\u0435\u0439 \u044D\u0440\u044B","\u043D\u0430\u0448\u0435\u0439 \u044D\u0440\u044B"]},XB={narrow:["1","2","3","4"],abbreviated:["1-\u0439 \u043A\u0432.","2-\u0439 \u043A\u0432.","3-\u0439 \u043A\u0432.","4-\u0439 \u043A\u0432."],wide:["1-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","2-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","3-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","4-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]},ZB={narrow:["\u042F","\u0424","\u041C","\u0410","\u041C","\u0418","\u0418","\u0410","\u0421","\u041E","\u041D","\u0414"],abbreviated:["\u044F\u043D\u0432.","\u0444\u0435\u0432.","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440.","\u043C\u0430\u0439","\u0438\u044E\u043D\u044C","\u0438\u044E\u043B\u044C","\u0430\u0432\u0433.","\u0441\u0435\u043D\u0442.","\u043E\u043A\u0442.","\u043D\u043E\u044F\u0431.","\u0434\u0435\u043A."],wide:["\u044F\u043D\u0432\u0430\u0440\u044C","\u0444\u0435\u0432\u0440\u0430\u043B\u044C","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0435\u043B\u044C","\u043C\u0430\u0439","\u0438\u044E\u043D\u044C","\u0438\u044E\u043B\u044C","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044C","\u043E\u043A\u0442\u044F\u0431\u0440\u044C","\u043D\u043E\u044F\u0431\u0440\u044C","\u0434\u0435\u043A\u0430\u0431\u0440\u044C"]},UB={narrow:["\u042F","\u0424","\u041C","\u0410","\u041C","\u0418","\u0418","\u0410","\u0421","\u041E","\u041D","\u0414"],abbreviated:["\u044F\u043D\u0432.","\u0444\u0435\u0432.","\u043C\u0430\u0440.","\u0430\u043F\u0440.","\u043C\u0430\u044F","\u0438\u044E\u043D.","\u0438\u044E\u043B.","\u0430\u0432\u0433.","\u0441\u0435\u043D\u0442.","\u043E\u043A\u0442.","\u043D\u043E\u044F\u0431.","\u0434\u0435\u043A."],wide:["\u044F\u043D\u0432\u0430\u0440\u044F","\u0444\u0435\u0432\u0440\u0430\u043B\u044F","\u043C\u0430\u0440\u0442\u0430","\u0430\u043F\u0440\u0435\u043B\u044F","\u043C\u0430\u044F","\u0438\u044E\u043D\u044F","\u0438\u044E\u043B\u044F","\u0430\u0432\u0433\u0443\u0441\u0442\u0430","\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044F","\u043E\u043A\u0442\u044F\u0431\u0440\u044F","\u043D\u043E\u044F\u0431\u0440\u044F","\u0434\u0435\u043A\u0430\u0431\u0440\u044F"]},HB={narrow:["\u0412","\u041F","\u0412","\u0421","\u0427","\u041F","\u0421"],short:["\u0432\u0441","\u043F\u043D","\u0432\u0442","\u0441\u0440","\u0447\u0442","\u043F\u0442","\u0441\u0431"],abbreviated:["\u0432\u0441\u043A","\u043F\u043D\u0434","\u0432\u0442\u0440","\u0441\u0440\u0434","\u0447\u0442\u0432","\u043F\u0442\u043D","\u0441\u0443\u0431"],wide:["\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0435\u0440\u0433","\u043F\u044F\u0442\u043D\u0438\u0446\u0430","\u0441\u0443\u0431\u0431\u043E\u0442\u0430"]},QB={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D.",noon:"\u043F\u043E\u043B\u0434.",morning:"\u0443\u0442\u0440\u043E",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u044C"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D.",noon:"\u043F\u043E\u043B\u0434.",morning:"\u0443\u0442\u0440\u043E",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u044C"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D\u043E\u0447\u044C",noon:"\u043F\u043E\u043B\u0434\u0435\u043D\u044C",morning:"\u0443\u0442\u0440\u043E",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447\u0435\u0440",night:"\u043D\u043E\u0447\u044C"}},YB={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D.",noon:"\u043F\u043E\u043B\u0434.",morning:"\u0443\u0442\u0440\u0430",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0438"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D.",noon:"\u043F\u043E\u043B\u0434.",morning:"\u0443\u0442\u0440\u0430",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0438"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D\u043E\u0447\u044C",noon:"\u043F\u043E\u043B\u0434\u0435\u043D\u044C",morning:"\u0443\u0442\u0440\u0430",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447\u0435\u0440\u0430",night:"\u043D\u043E\u0447\u0438"}},qB=function B(C,G){var J=Number(C),X=G===null||G===void 0?void 0:G.unit,Z;if(X==="date")Z="-\u0435";else if(X==="week"||X==="minute"||X==="second")Z="-\u044F";else Z="-\u0439";return J+Z},KB={ordinalNumber:qB,era:V({values:JB,defaultWidth:"wide"}),quarter:V({values:XB,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:V({values:ZB,defaultWidth:"wide",formattingValues:UB,defaultFormattingWidth:"wide"}),day:V({values:HB,defaultWidth:"wide"}),dayPeriod:V({values:QB,defaultWidth:"any",formattingValues:YB,defaultFormattingWidth:"wide"})};function I(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.width,X=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Z=C.match(X);if(!Z)return null;var U=Z[0],H=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(H)?NB(H,function(N){return N.test(U)}):EB(H,function(N){return N.test(U)}),Y;Y=B.valueCallback?B.valueCallback(q):q,Y=G.valueCallback?G.valueCallback(Y):Y;var K=C.slice(U.length);return{value:Y,rest:K}}}function EB(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function NB(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function AB(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=C.match(B.matchPattern);if(!J)return null;var X=J[0],Z=C.match(B.parsePattern);if(!Z)return null;var U=B.valueCallback?B.valueCallback(Z[0]):Z[0];U=G.valueCallback?G.valueCallback(U):U;var H=C.slice(X.length);return{value:U,rest:H}}}var VB=/^(\d+)(-?(е|я|й|ое|ье|ая|ья|ый|ой|ий|ый))?/i,IB=/\d+/i,MB={narrow:/^((до )?н\.?\s?э\.?)/i,abbreviated:/^((до )?н\.?\s?э\.?)/i,wide:/^(до нашей эры|нашей эры|наша эра)/i},RB={any:[/^д/i,/^н/i]},xB={narrow:/^[1234]/i,abbreviated:/^[1234](-?[ыои]?й?)? кв.?/i,wide:/^[1234](-?[ыои]?й?)? квартал/i},SB={any:[/1/i,/2/i,/3/i,/4/i]},WB={narrow:/^[яфмаисонд]/i,abbreviated:/^(янв|фев|март?|апр|ма[йя]|июн[ья]?|июл[ья]?|авг|сент?|окт|нояб?|дек)\.?/i,wide:/^(январ[ья]|феврал[ья]|марта?|апрел[ья]|ма[йя]|июн[ья]|июл[ья]|августа?|сентябр[ья]|октябр[ья]|октябр[ья]|ноябр[ья]|декабр[ья])/i},$B={narrow:[/^я/i,/^ф/i,/^м/i,/^а/i,/^м/i,/^и/i,/^и/i,/^а/i,/^с/i,/^о/i,/^н/i,/^я/i],any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^ма[йя]/i,/^июн/i,/^июл/i,/^ав/i,/^с/i,/^о/i,/^н/i,/^д/i]},jB={narrow:/^[впсч]/i,short:/^(вс|во|пн|по|вт|ср|чт|че|пт|пя|сб|су)\.?/i,abbreviated:/^(вск|вос|пнд|пон|втр|вто|срд|сре|чтв|чет|птн|пят|суб).?/i,wide:/^(воскресень[ея]|понедельника?|вторника?|сред[аы]|четверга?|пятниц[аы]|суббот[аы])/i},OB={narrow:[/^в/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^в[ос]/i,/^п[он]/i,/^в/i,/^ср/i,/^ч/i,/^п[ят]/i,/^с[уб]/i]},TB={narrow:/^([дп]п|полн\.?|полд\.?|утр[оа]|день|дня|веч\.?|ноч[ьи])/i,abbreviated:/^([дп]п|полн\.?|полд\.?|утр[оа]|день|дня|веч\.?|ноч[ьи])/i,wide:/^([дп]п|полночь|полдень|утр[оа]|день|дня|вечера?|ноч[ьи])/i},PB={any:{am:/^дп/i,pm:/^пп/i,midnight:/^полн/i,noon:/^полд/i,morning:/^у/i,afternoon:/^д[ен]/i,evening:/^в/i,night:/^н/i}},zB={ordinalNumber:AB({matchPattern:VB,parsePattern:IB,valueCallback:function B(C){return parseInt(C,10)}}),era:I({matchPatterns:MB,defaultMatchWidth:"wide",parsePatterns:RB,defaultParseWidth:"any"}),quarter:I({matchPatterns:xB,defaultMatchWidth:"wide",parsePatterns:SB,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:I({matchPatterns:WB,defaultMatchWidth:"wide",parsePatterns:$B,defaultParseWidth:"any"}),day:I({matchPatterns:jB,defaultMatchWidth:"wide",parsePatterns:OB,defaultParseWidth:"any"}),dayPeriod:I({matchPatterns:TB,defaultMatchWidth:"wide",parsePatterns:PB,defaultParseWidth:"any"})},DB={code:"ru",formatDistance:y,formatLong:d,formatRelative:GB,localize:KB,match:zB,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=M(M({},window.dateFns),{},{locale:M(M({},(R=window.dateFns)===null||R===void 0?void 0:R.locale),{},{ru:DB})})})();

//# debugId=58E4BE041168580D64756E2164756E21
