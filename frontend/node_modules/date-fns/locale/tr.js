import { formatDistance } from "./tr/_lib/formatDistance.js";
import { formatLong } from "./tr/_lib/formatLong.js";
import { formatRelative } from "./tr/_lib/formatRelative.js";
import { localize } from "./tr/_lib/localize.js";
import { match } from "./tr/_lib/match.js";

/**
 * @category Locales
 * @summary Turkish locale.
 * @language Turkish
 * @iso-639-2 tur
 * <AUTHOR> [@alpcanaydin](https://github.com/alpcanaydin)
 * <AUTHOR> [@berkaey](https://github.com/berkaey)
 * <AUTHOR> [@bulutfatih](https://github.com/bulutfatih)
 * <AUTHOR> [@dbtek](https://github.com/dbtek)
 * <AUTHOR> [@ikayar](https://github.com/ikayar)
 *
 *
 */
export const tr = {
  code: "tr",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default tr;
