{"version": 3, "sources": ["lib/locale/az/cdn.js"], "sourcesContent": ["(() => {\nvar _window$dateFns;function _typeof(o) {\"@babel/helpers - typeof\";return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, \"string\");return \"symbol\" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if (\"object\" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || \"default\");if (\"object\" != _typeof(i)) return i;throw new TypeError(\"@@toPrimitive must return a primitive value.\");}return (\"string\" === r ? String : Number)(t);}var __defProp = Object.defineProperty;\nvar __export = function __export(target, all) {\n  for (var name in all)\n  __defProp(target, name, {\n    get: all[name],\n    enumerable: true,\n    configurable: true,\n    set: function set(newValue) {return all[name] = function () {return newValue;};}\n  });\n};\n\n// lib/locale/az/_lib/formatDistance.js\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"bir saniy\\u0259d\\u0259n az\",\n    other: \"{{count}} bir saniy\\u0259d\\u0259n az\"\n  },\n  xSeconds: {\n    one: \"1 saniy\\u0259\",\n    other: \"{{count}} saniy\\u0259\"\n  },\n  halfAMinute: \"yar\\u0131m d\\u0259qiq\\u0259\",\n  lessThanXMinutes: {\n    one: \"bir d\\u0259qiq\\u0259d\\u0259n az\",\n    other: \"{{count}} bir d\\u0259qiq\\u0259d\\u0259n az\"\n  },\n  xMinutes: {\n    one: \"bir d\\u0259qiq\\u0259\",\n    other: \"{{count}} d\\u0259qiq\\u0259\"\n  },\n  aboutXHours: {\n    one: \"t\\u0259xmin\\u0259n 1 saat\",\n    other: \"t\\u0259xmin\\u0259n {{count}} saat\"\n  },\n  xHours: {\n    one: \"1 saat\",\n    other: \"{{count}} saat\"\n  },\n  xDays: {\n    one: \"1 g\\xFCn\",\n    other: \"{{count}} g\\xFCn\"\n  },\n  aboutXWeeks: {\n    one: \"t\\u0259xmin\\u0259n 1 h\\u0259ft\\u0259\",\n    other: \"t\\u0259xmin\\u0259n {{count}} h\\u0259ft\\u0259\"\n  },\n  xWeeks: {\n    one: \"1 h\\u0259ft\\u0259\",\n    other: \"{{count}} h\\u0259ft\\u0259\"\n  },\n  aboutXMonths: {\n    one: \"t\\u0259xmin\\u0259n 1 ay\",\n    other: \"t\\u0259xmin\\u0259n {{count}} ay\"\n  },\n  xMonths: {\n    one: \"1 ay\",\n    other: \"{{count}} ay\"\n  },\n  aboutXYears: {\n    one: \"t\\u0259xmin\\u0259n 1 il\",\n    other: \"t\\u0259xmin\\u0259n {{count}} il\"\n  },\n  xYears: {\n    one: \"1 il\",\n    other: \"{{count}} il\"\n  },\n  overXYears: {\n    one: \"1 ild\\u0259n \\xE7ox\",\n    other: \"{{count}} ild\\u0259n \\xE7ox\"\n  },\n  almostXYears: {\n    one: \"dem\\u0259k olar ki 1 il\",\n    other: \"dem\\u0259k olar ki {{count}} il\"\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" sonra\";\n    } else {\n      return result + \" \\u0259vv\\u0259l\";\n    }\n  }\n  return result;\n};\n\n// lib/locale/_lib/buildFormatLongFn.js\nfunction buildFormatLongFn(args) {\n  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n\n// lib/locale/az/_lib/formatLong.js\nvar dateFormats = {\n  full: \"EEEE, do MMMM y 'il'\",\n  long: \"do MMMM y 'il'\",\n  medium: \"d MMM y 'il'\",\n  short: \"dd.MM.yyyy\"\n};\nvar timeFormats = {\n  full: \"H:mm:ss zzzz\",\n  long: \"H:mm:ss z\",\n  medium: \"H:mm:ss\",\n  short: \"H:mm\"\n};\nvar dateTimeFormats = {\n  full: \"{{date}} {{time}} - 'd\\u0259'\",\n  long: \"{{date}} {{time}} - 'd\\u0259'\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\"\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};\n\n// lib/locale/az/_lib/formatRelative.js\nvar formatRelativeLocale = {\n  lastWeek: \"'sonuncu' eeee p -'d\\u0259'\",\n  yesterday: \"'d\\xFCn\\u0259n' p -'d\\u0259'\",\n  today: \"'bug\\xFCn' p -'d\\u0259'\",\n  tomorrow: \"'sabah' p -'d\\u0259'\",\n  nextWeek: \"eeee p -'d\\u0259'\",\n  other: \"P\"\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};\n\n// lib/locale/_lib/buildLocalizeFn.js\nfunction buildLocalizeFn(args) {\n  return function (value, options) {\n    var context = options !== null && options !== void 0 && options.context ? String(options.context) : \"standalone\";\n    var valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(value) : value;\n    return valuesArray[index];\n  };\n}\n\n// lib/locale/az/_lib/localize.js\nvar eraValues = {\n  narrow: [\"e.\\u0259\", \"b.e\"],\n  abbreviated: [\"e.\\u0259\", \"b.e\"],\n  wide: [\"eram\\u0131zdan \\u0259vv\\u0259l\", \"bizim era\"]\n};\nvar quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1ci kvartal\", \"2ci kvartal\", \"3c\\xFC kvartal\", \"4c\\xFC kvartal\"]\n};\nvar monthValues = {\n  narrow: [\"Y\", \"F\", \"M\", \"A\", \"M\", \"\\u0130\", \"\\u0130\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n  \"Yan\",\n  \"Fev\",\n  \"Mar\",\n  \"Apr\",\n  \"May\",\n  \"\\u0130yun\",\n  \"\\u0130yul\",\n  \"Avq\",\n  \"Sen\",\n  \"Okt\",\n  \"Noy\",\n  \"Dek\"],\n\n  wide: [\n  \"Yanvar\",\n  \"Fevral\",\n  \"Mart\",\n  \"Aprel\",\n  \"May\",\n  \"\\u0130yun\",\n  \"\\u0130yul\",\n  \"Avqust\",\n  \"Sentyabr\",\n  \"Oktyabr\",\n  \"Noyabr\",\n  \"Dekabr\"]\n\n};\nvar dayValues = {\n  narrow: [\"B.\", \"B.e\", \"\\xC7.a\", \"\\xC7.\", \"C.a\", \"C.\", \"\\u015E.\"],\n  short: [\"B.\", \"B.e\", \"\\xC7.a\", \"\\xC7.\", \"C.a\", \"C.\", \"\\u015E.\"],\n  abbreviated: [\"Baz\", \"Baz.e\", \"\\xC7\\u0259r.a\", \"\\xC7\\u0259r\", \"C\\xFCm.a\", \"C\\xFCm\", \"\\u015E\\u0259\"],\n  wide: [\n  \"Bazar\",\n  \"Bazar ert\\u0259si\",\n  \"\\xC7\\u0259r\\u015F\\u0259nb\\u0259 ax\\u015Fam\\u0131\",\n  \"\\xC7\\u0259r\\u015F\\u0259nb\\u0259\",\n  \"C\\xFCm\\u0259 ax\\u015Fam\\u0131\",\n  \"C\\xFCm\\u0259\",\n  \"\\u015E\\u0259nb\\u0259\"]\n\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"gec\\u0259yar\\u0131\",\n    noon: \"g\\xFCn\",\n    morning: \"s\\u0259h\\u0259r\",\n    afternoon: \"g\\xFCnd\\xFCz\",\n    evening: \"ax\\u015Fam\",\n    night: \"gec\\u0259\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"gec\\u0259yar\\u0131\",\n    noon: \"g\\xFCn\",\n    morning: \"s\\u0259h\\u0259r\",\n    afternoon: \"g\\xFCnd\\xFCz\",\n    evening: \"ax\\u015Fam\",\n    night: \"gec\\u0259\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gec\\u0259yar\\u0131\",\n    noon: \"g\\xFCn\",\n    morning: \"s\\u0259h\\u0259r\",\n    afternoon: \"g\\xFCnd\\xFCz\",\n    evening: \"ax\\u015Fam\",\n    night: \"gec\\u0259\"\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"gec\\u0259yar\\u0131\",\n    noon: \"g\\xFCn\",\n    morning: \"s\\u0259h\\u0259r\",\n    afternoon: \"g\\xFCnd\\xFCz\",\n    evening: \"ax\\u015Fam\",\n    night: \"gec\\u0259\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"gec\\u0259yar\\u0131\",\n    noon: \"g\\xFCn\",\n    morning: \"s\\u0259h\\u0259r\",\n    afternoon: \"g\\xFCnd\\xFCz\",\n    evening: \"ax\\u015Fam\",\n    night: \"gec\\u0259\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"gec\\u0259yar\\u0131\",\n    noon: \"g\\xFCn\",\n    morning: \"s\\u0259h\\u0259r\",\n    afternoon: \"g\\xFCnd\\xFCz\",\n    evening: \"ax\\u015Fam\",\n    night: \"gec\\u0259\"\n  }\n};\nvar suffixes = {\n  1: \"-inci\",\n  5: \"-inci\",\n  8: \"-inci\",\n  70: \"-inci\",\n  80: \"-inci\",\n  2: \"-nci\",\n  7: \"-nci\",\n  20: \"-nci\",\n  50: \"-nci\",\n  3: \"-\\xFCnc\\xFC\",\n  4: \"-\\xFCnc\\xFC\",\n  100: \"-\\xFCnc\\xFC\",\n  6: \"-nc\\u0131\",\n  9: \"-uncu\",\n  10: \"-uncu\",\n  30: \"-uncu\",\n  60: \"-\\u0131nc\\u0131\",\n  90: \"-\\u0131nc\\u0131\"\n};\nvar getSuffix = function getSuffix(number) {\n  if (number === 0) {\n    return number + \"-\\u0131nc\\u0131\";\n  }\n  var a = number % 10;\n  var b = number % 100 - a;\n  var c = number >= 100 ? 100 : null;\n  if (suffixes[a]) {\n    return suffixes[a];\n  } else if (suffixes[b]) {\n    return suffixes[b];\n  } else if (c !== null) {\n    return suffixes[c];\n  }\n  return \"\";\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var suffix = getSuffix(number);\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};\n\n// lib/locale/_lib/buildMatchFn.js\nfunction buildMatchFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return;\n}\n\n// lib/locale/_lib/buildMatchPatternFn.js\nfunction buildMatchPatternFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult)\n    return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult)\n    return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\n\n// lib/locale/az/_lib/match.js\nvar matchOrdinalNumberPattern = /^(\\d+)(-?(ci|inci|nci|uncu|üncü|ncı))?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(b|a)$/i,\n  abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)$/i,\n  wide: /^(bizim eradan əvvəl|bizim era)$/i\n};\nvar parseEraPatterns = {\n  any: [/^b$/i, /^(a|c)$/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]$/i,\n  abbreviated: /^K[1234]$/i,\n  wide: /^[1234](ci)? kvartal$/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[(?-i)yfmaisond]$/i,\n  abbreviated: /^(Yan|Fev|Mar|Apr|May|İyun|İyul|Avq|Sen|Okt|Noy|Dek)$/i,\n  wide: /^(Yanvar|Fevral|Mart|Aprel|May|İyun|İyul|Avgust|Sentyabr|Oktyabr|Noyabr|Dekabr)$/i\n};\nvar parseMonthPatterns = {\n  narrow: [\n  /^[(?-i)y]$/i,\n  /^[(?-i)f]$/i,\n  /^[(?-i)m]$/i,\n  /^[(?-i)a]$/i,\n  /^[(?-i)m]$/i,\n  /^[(?-i)i]$/i,\n  /^[(?-i)i]$/i,\n  /^[(?-i)a]$/i,\n  /^[(?-i)s]$/i,\n  /^[(?-i)o]$/i,\n  /^[(?-i)n]$/i,\n  /^[(?-i)d]$/i],\n\n  abbreviated: [\n  /^Yan$/i,\n  /^Fev$/i,\n  /^Mar$/i,\n  /^Apr$/i,\n  /^May$/i,\n  /^İyun$/i,\n  /^İyul$/i,\n  /^Avg$/i,\n  /^Sen$/i,\n  /^Okt$/i,\n  /^Noy$/i,\n  /^Dek$/i],\n\n  wide: [\n  /^Yanvar$/i,\n  /^Fevral$/i,\n  /^Mart$/i,\n  /^Aprel$/i,\n  /^May$/i,\n  /^İyun$/i,\n  /^İyul$/i,\n  /^Avgust$/i,\n  /^Sentyabr$/i,\n  /^Oktyabr$/i,\n  /^Noyabr$/i,\n  /^Dekabr$/i]\n\n};\nvar matchDayPatterns = {\n  narrow: /^(B\\.|B\\.e|Ç\\.a|Ç\\.|C\\.a|C\\.|Ş\\.)$/i,\n  short: /^(B\\.|B\\.e|Ç\\.a|Ç\\.|C\\.a|C\\.|Ş\\.)$/i,\n  abbreviated: /^(Baz\\.e|Çər|Çər\\.a|Cüm|Cüm\\.a|Şə)$/i,\n  wide: /^(Bazar|Bazar ertəsi|Çərşənbə axşamı|Çərşənbə|Cümə axşamı|Cümə|Şənbə)$/i\n};\nvar parseDayPatterns = {\n  narrow: [\n  /^B\\.$/i,\n  /^B\\.e$/i,\n  /^Ç\\.a$/i,\n  /^Ç\\.$/i,\n  /^C\\.a$/i,\n  /^C\\.$/i,\n  /^Ş\\.$/i],\n\n  abbreviated: [\n  /^Baz$/i,\n  /^Baz\\.e$/i,\n  /^Çər\\.a$/i,\n  /^Çər$/i,\n  /^Cüm\\.a$/i,\n  /^Cüm$/i,\n  /^Şə$/i],\n\n  wide: [\n  /^Bazar$/i,\n  /^Bazar ertəsi$/i,\n  /^Çərşənbə axşamı$/i,\n  /^Çərşənbə$/i,\n  /^Cümə axşamı$/i,\n  /^Cümə$/i,\n  /^Şənbə$/i],\n\n  any: [\n  /^B\\.$/i,\n  /^B\\.e$/i,\n  /^Ç\\.a$/i,\n  /^Ç\\.$/i,\n  /^C\\.a$/i,\n  /^C\\.$/i,\n  /^Ş\\.$/i]\n\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(a|p|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i,\n  any: /^(am|pm|a\\.m\\.|p\\.m\\.|AM|PM|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^a$/i,\n    pm: /^p$/i,\n    midnight: /^gecəyarı$/i,\n    noon: /^gün$/i,\n    morning: /səhər$/i,\n    afternoon: /gündüz$/i,\n    evening: /axşam$/i,\n    night: /gecə$/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {return parseInt(value, 10);}\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: function valueCallback(index) {return index + 1;}\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"narrow\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};\n\n// lib/locale/az.js\nvar az = {\n  code: \"az\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1\n  }\n};\n\n// lib/locale/az/cdn.js\nwindow.dateFns = _objectSpread(_objectSpread({},\nwindow.dateFns), {}, {\n  locale: _objectSpread(_objectSpread({}, (_window$dateFns =\n  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {\n    az: az }) });\n\n\n\n//# debugId=FF93ABFDC44DD4BA64756E2164756E21\n\n//# sourceMappingURL=cdn.js.map\n})();"], "mappings": "AAAA,CAAC,IAAM,CACP,IAAI,EAAgB,SAAS,CAAO,CAAC,EAAG,CAA2B,OAAO,SAA+B,QAArB,mBAAkD,OAAO,UAA1B,iBAA8C,CAAC,EAAG,CAAC,cAAc,WAAe,CAAC,EAAG,CAAC,OAAO,UAA0B,QAArB,YAA+B,EAAE,cAAgB,QAAU,IAAM,OAAO,UAAY,gBAAkB,GAAK,EAAQ,CAAC,EAAG,SAAS,CAAO,CAAC,EAAG,EAAG,CAAC,IAAI,EAAI,OAAO,KAAK,CAAC,EAAE,GAAI,OAAO,sBAAuB,CAAC,IAAI,EAAI,OAAO,sBAAsB,CAAC,EAAE,IAAM,EAAI,EAAE,eAAgB,CAAC,EAAG,CAAC,OAAO,OAAO,yBAAyB,EAAG,CAAC,EAAE,WAAY,GAAI,EAAE,KAAK,MAAM,EAAG,CAAC,EAAG,OAAO,EAAG,SAAS,CAAa,CAAC,EAAG,CAAC,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAC,IAAI,EAAY,UAAU,IAAlB,KAAuB,UAAU,GAAK,CAAC,EAAE,EAAI,EAAI,EAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,gBAAiB,CAAC,EAAG,CAAC,EAAgB,EAAG,EAAG,EAAE,EAAE,EAAG,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAI,EAAQ,OAAO,CAAC,CAAC,EAAE,gBAAiB,CAAC,EAAG,CAAC,OAAO,eAAe,EAAG,EAAG,OAAO,yBAAyB,EAAG,CAAC,CAAC,EAAG,EAAG,OAAO,EAAG,SAAS,CAAe,CAAC,EAAK,EAAK,EAAO,CAA2B,GAA1B,EAAM,EAAe,CAAG,EAAM,KAAO,EAAM,OAAO,eAAe,EAAK,EAAK,CAAE,MAAO,EAAO,WAAY,GAAM,aAAc,GAAM,SAAU,EAAK,CAAC,MAAU,GAAI,GAAO,EAAO,OAAO,EAAK,SAAS,CAAc,CAAC,EAAG,CAAC,IAAI,EAAI,EAAa,EAAG,QAAQ,EAAE,OAAmB,EAAQ,CAAC,GAArB,SAAyB,EAAI,OAAO,CAAC,EAAG,SAAS,CAAY,CAAC,EAAG,EAAG,CAAC,GAAgB,EAAQ,CAAC,GAArB,WAA2B,EAAG,OAAO,EAAE,IAAI,EAAI,EAAE,OAAO,aAAa,GAAe,IAAN,OAAS,CAAC,IAAI,EAAI,EAAE,KAAK,EAAG,GAAK,SAAS,EAAE,GAAgB,EAAQ,CAAC,GAArB,SAAwB,OAAO,EAAE,MAAM,IAAI,UAAU,8CAA8C,EAAG,OAAqB,IAAb,SAAiB,OAAS,QAAQ,CAAC,EAAG,IAAI,EAAY,OAAO,eACroD,YAAoB,CAAQ,CAAC,EAAQ,EAAK,CAC5C,QAAS,KAAQ,EACjB,EAAU,EAAQ,EAAM,CACtB,IAAK,EAAI,GACT,WAAY,GACZ,aAAc,GACd,aAAc,CAAG,CAAC,EAAU,CAAC,OAAO,EAAI,WAAiB,EAAG,CAAC,OAAO,GACtE,CAAC,GAIC,EAAuB,CACzB,iBAAkB,CAChB,IAAK,6BACL,MAAO,sCACT,EACA,SAAU,CACR,IAAK,gBACL,MAAO,uBACT,EACA,YAAa,8BACb,iBAAkB,CAChB,IAAK,kCACL,MAAO,2CACT,EACA,SAAU,CACR,IAAK,uBACL,MAAO,4BACT,EACA,YAAa,CACX,IAAK,4BACL,MAAO,mCACT,EACA,OAAQ,CACN,IAAK,SACL,MAAO,gBACT,EACA,MAAO,CACL,IAAK,WACL,MAAO,kBACT,EACA,YAAa,CACX,IAAK,uCACL,MAAO,8CACT,EACA,OAAQ,CACN,IAAK,oBACL,MAAO,2BACT,EACA,aAAc,CACZ,IAAK,0BACL,MAAO,iCACT,EACA,QAAS,CACP,IAAK,OACL,MAAO,cACT,EACA,YAAa,CACX,IAAK,0BACL,MAAO,iCACT,EACA,OAAQ,CACN,IAAK,OACL,MAAO,cACT,EACA,WAAY,CACV,IAAK,sBACL,MAAO,6BACT,EACA,aAAc,CACZ,IAAK,0BACL,MAAO,iCACT,CACF,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAS,CAClE,IAAI,EACA,EAAa,EAAqB,GACtC,UAAW,IAAe,SACxB,EAAS,UACA,IAAU,EACnB,EAAS,EAAW,QAEpB,GAAS,EAAW,MAAM,QAAQ,YAAa,OAAO,CAAK,CAAC,EAE9D,GAAI,IAAY,MAAQ,IAAiB,QAAK,EAAQ,UACpD,GAAI,EAAQ,YAAc,EAAQ,WAAa,EAC7C,OAAO,EAAS,aAEhB,QAAO,EAAS,mBAGpB,OAAO,GAIT,SAAS,CAAiB,CAAC,EAAM,CAC/B,eAAgB,EAAG,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACjG,EAAQ,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACrD,EAAS,EAAK,QAAQ,IAAU,EAAK,QAAQ,EAAK,cACtD,OAAO,GAKX,IAAI,EAAc,CAChB,KAAM,uBACN,KAAM,iBACN,OAAQ,eACR,MAAO,YACT,EACI,EAAc,CAChB,KAAM,eACN,KAAM,YACN,OAAQ,UACR,MAAO,MACT,EACI,EAAkB,CACpB,KAAM,gCACN,KAAM,gCACN,OAAQ,qBACR,MAAO,oBACT,EACI,EAAa,CACf,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,SAAU,EAAkB,CAC1B,QAAS,EACT,aAAc,MAChB,CAAC,CACH,EAGI,EAAuB,CACzB,SAAU,8BACV,UAAW,+BACX,MAAO,0BACP,SAAU,uBACV,SAAU,oBACV,MAAO,GACT,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAW,EAAU,CAAC,OAAO,EAAqB,IAG7G,SAAS,CAAe,CAAC,EAAM,CAC7B,eAAgB,CAAC,EAAO,EAAS,CAC/B,IAAI,EAAU,IAAY,MAAQ,IAAiB,QAAK,EAAQ,QAAU,OAAO,EAAQ,OAAO,EAAI,aAChG,EACJ,GAAI,IAAY,cAAgB,EAAK,iBAAkB,CACrD,IAAI,EAAe,EAAK,wBAA0B,EAAK,aACnD,EAAQ,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAC9F,EAAc,EAAK,iBAAiB,IAAU,EAAK,iBAAiB,OAC/D,CACL,IAAI,EAAgB,EAAK,aACrB,EAAS,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACpG,EAAc,EAAK,OAAO,IAAW,EAAK,OAAO,GAEnD,IAAI,EAAQ,EAAK,iBAAmB,EAAK,iBAAiB,CAAK,EAAI,EACnE,OAAO,EAAY,IAKvB,IAAI,EAAY,CACd,OAAQ,CAAC,WAAY,KAAK,EAC1B,YAAa,CAAC,WAAY,KAAK,EAC/B,KAAM,CAAC,iCAAkC,WAAW,CACtD,EACI,EAAgB,CAClB,OAAQ,CAAC,IAAK,IAAK,IAAK,GAAG,EAC3B,YAAa,CAAC,KAAM,KAAM,KAAM,IAAI,EACpC,KAAM,CAAC,cAAe,cAAe,iBAAkB,gBAAgB,CACzE,EACI,EAAc,CAChB,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,SAAU,SAAU,IAAK,IAAK,IAAK,IAAK,GAAG,EAC7E,YAAa,CACb,MACA,MACA,MACA,MACA,MACA,YACA,YACA,MACA,MACA,MACA,MACA,KAAK,EAEL,KAAM,CACN,SACA,SACA,OACA,QACA,MACA,YACA,YACA,SACA,WACA,UACA,SACA,QAAQ,CAEV,EACI,EAAY,CACd,OAAQ,CAAC,KAAM,MAAO,SAAU,QAAS,MAAO,KAAM,SAAS,EAC/D,MAAO,CAAC,KAAM,MAAO,SAAU,QAAS,MAAO,KAAM,SAAS,EAC9D,YAAa,CAAC,MAAO,QAAS,gBAAiB,cAAe,WAAY,SAAU,cAAc,EAClG,KAAM,CACN,QACA,oBACA,mDACA,kCACA,gCACA,eACA,sBAAsB,CAExB,EACI,EAAkB,CACpB,OAAQ,CACN,GAAI,KACJ,GAAI,KACJ,SAAU,qBACV,KAAM,SACN,QAAS,kBACT,UAAW,eACX,QAAS,aACT,MAAO,WACT,EACA,YAAa,CACX,GAAI,KACJ,GAAI,KACJ,SAAU,qBACV,KAAM,SACN,QAAS,kBACT,UAAW,eACX,QAAS,aACT,MAAO,WACT,EACA,KAAM,CACJ,GAAI,OACJ,GAAI,OACJ,SAAU,qBACV,KAAM,SACN,QAAS,kBACT,UAAW,eACX,QAAS,aACT,MAAO,WACT,CACF,EACI,EAA4B,CAC9B,OAAQ,CACN,GAAI,IACJ,GAAI,IACJ,SAAU,qBACV,KAAM,SACN,QAAS,kBACT,UAAW,eACX,QAAS,aACT,MAAO,WACT,EACA,YAAa,CACX,GAAI,KACJ,GAAI,KACJ,SAAU,qBACV,KAAM,SACN,QAAS,kBACT,UAAW,eACX,QAAS,aACT,MAAO,WACT,EACA,KAAM,CACJ,GAAI,OACJ,GAAI,OACJ,SAAU,qBACV,KAAM,SACN,QAAS,kBACT,UAAW,eACX,QAAS,aACT,MAAO,WACT,CACF,EACI,EAAW,CACb,EAAG,QACH,EAAG,QACH,EAAG,QACH,GAAI,QACJ,GAAI,QACJ,EAAG,OACH,EAAG,OACH,GAAI,OACJ,GAAI,OACJ,EAAG,cACH,EAAG,cACH,IAAK,cACL,EAAG,YACH,EAAG,QACH,GAAI,QACJ,GAAI,QACJ,GAAI,kBACJ,GAAI,iBACN,EACI,WAAqB,CAAS,CAAC,EAAQ,CACzC,GAAI,IAAW,EACb,OAAO,EAAS,kBAElB,IAAI,EAAI,EAAS,GACb,EAAI,EAAS,IAAM,EACnB,EAAI,GAAU,IAAM,IAAM,KAC9B,GAAI,EAAS,GACX,OAAO,EAAS,WACP,EAAS,GAClB,OAAO,EAAS,WACP,IAAM,KACf,OAAO,EAAS,GAElB,MAAO,IAEL,WAAyB,CAAa,CAAC,EAAa,EAAU,CAChE,IAAI,EAAS,OAAO,CAAW,EAC3B,EAAS,EAAU,CAAM,EAC7B,OAAO,EAAS,GAEd,EAAW,CACb,cAAe,EACf,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,QAAS,EAAgB,CACvB,OAAQ,EACR,aAAc,OACd,0BAA2B,CAAgB,CAAC,EAAS,CAAC,OAAO,EAAU,EACzE,CAAC,EACD,MAAO,EAAgB,CACrB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,UAAW,EAAgB,CACzB,OAAQ,EACR,aAAc,OACd,iBAAkB,EAClB,uBAAwB,MAC1B,CAAC,CACH,EAGA,SAAS,CAAY,CAAC,EAAM,CAC1B,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAQ,EAAQ,MAChB,EAAe,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC7E,EAAc,EAAO,MAAM,CAAY,EAC3C,IAAK,EACH,OAAO,KAET,IAAI,EAAgB,EAAY,GAC5B,EAAgB,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC9E,EAAM,MAAM,QAAQ,CAAa,EAAI,EAAU,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EAAI,EAAQ,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EACzM,EACJ,EAAQ,EAAK,cAAgB,EAAK,cAAc,CAAG,EAAI,EACvD,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,GAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,EAAK,GAGtC,SAAS,CAAO,CAAC,EAAQ,EAAW,CAClC,QAAS,KAAO,EACd,GAAI,OAAO,UAAU,eAAe,KAAK,EAAQ,CAAG,GAAK,EAAU,EAAO,EAAI,EAC5E,OAAO,EAGX,OAEF,SAAS,CAAS,CAAC,EAAO,EAAW,CACnC,QAAS,EAAM,EAAG,EAAM,EAAM,OAAQ,IACpC,GAAI,EAAU,EAAM,EAAI,EACtB,OAAO,EAGX,OAIF,SAAS,CAAmB,CAAC,EAAM,CACjC,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAgB,EAAY,GAC5B,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAQ,EAAK,cAAgB,EAAK,cAAc,EAAY,EAAE,EAAI,EAAY,GAClF,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,EAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,CAAK,GAKtC,IAAI,EAA4B,0CAC5B,EAA4B,OAC5B,EAAmB,CACrB,OAAQ,WACR,YAAa,8DACb,KAAM,mCACR,EACI,EAAmB,CACrB,IAAK,CAAC,OAAQ,UAAU,CAC1B,EACI,EAAuB,CACzB,OAAQ,YACR,YAAa,aACb,KAAM,wBACR,EACI,EAAuB,CACzB,IAAK,CAAC,KAAM,KAAM,KAAM,IAAI,CAC9B,EACI,EAAqB,CACvB,OAAQ,sBACR,YAAa,yDACb,KAAM,mFACR,EACI,EAAqB,CACvB,OAAQ,CACR,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,aAAa,EAEb,YAAa,CACb,SACA,SACA,SACA,SACA,SACA,UACA,UACA,SACA,SACA,SACA,SACA,QAAQ,EAER,KAAM,CACN,YACA,YACA,UACA,WACA,SACA,UACA,UACA,YACA,cACA,aACA,YACA,WAAW,CAEb,EACI,EAAmB,CACrB,OAAQ,sCACR,MAAO,sCACP,YAAa,uCACb,KAAM,yEACR,EACI,EAAmB,CACrB,OAAQ,CACR,SACA,UACA,UACA,SACA,UACA,SACA,QAAO,EAEP,YAAa,CACb,SACA,YACA,YACA,SACA,YACA,SACA,OAAM,EAEN,KAAM,CACN,WACA,kBACA,qBACA,cACA,iBACA,UACA,UAAS,EAET,IAAK,CACL,SACA,UACA,UACA,SACA,UACA,SACA,QAAO,CAET,EACI,EAAyB,CAC3B,OAAQ,gDACR,IAAK,qEACP,EACI,GAAyB,CAC3B,IAAK,CACH,GAAI,OACJ,GAAI,OACJ,SAAU,cACV,KAAM,SACN,QAAS,UACT,UAAW,WACX,QAAS,UACT,MAAO,QACT,CACF,EACI,GAAQ,CACV,cAAe,EAAoB,CACjC,aAAc,EACd,aAAc,EACd,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,SAAS,EAAO,EAAE,EACzE,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,QAAS,EAAa,CACpB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,MACnB,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,EAAQ,EAC/D,CAAC,EACD,MAAO,EAAa,CAClB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,QACrB,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,UAAW,EAAa,CACtB,cAAe,EACf,kBAAmB,MACnB,cAAe,GACf,kBAAmB,KACrB,CAAC,CACH,EAGI,GAAK,CACP,KAAM,KACN,eAAgB,EAChB,WAAY,EACZ,eAAgB,EAChB,SAAU,EACV,MAAO,GACP,QAAS,CACP,aAAc,EACd,sBAAuB,CACzB,CACF,EAGA,OAAO,QAAU,EAAc,EAAc,CAAC,EAC9C,OAAO,OAAO,EAAG,CAAC,EAAG,CACnB,OAAQ,EAAc,EAAc,CAAC,GAAI,EACzC,OAAO,WAAa,MAAQ,IAAyB,OAAS,OAAI,EAAgB,MAAM,EAAG,CAAC,EAAG,CAC7F,GAAI,EAAG,CAAC,CAAE,CAAC,IAOZ", "debugId": "32216761BDC32DF164756E2164756E21", "names": []}