(()=>{var T;function Q(B){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(X){return typeof X}:function(X){return X&&typeof Symbol=="function"&&X.constructor===Symbol&&X!==Symbol.prototype?"symbol":typeof X},Q(B)}function z(B,X){var J=Object.keys(B);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(B);X&&(Y=Y.filter(function(Z){return Object.getOwnPropertyDescriptor(B,Z).enumerable})),J.push.apply(J,Y)}return J}function $(B){for(var X=1;X<arguments.length;X++){var J=arguments[X]!=null?arguments[X]:{};X%2?z(Object(J),!0).forEach(function(Y){E(B,Y,J[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(J)):z(Object(J)).forEach(function(Y){Object.defineProperty(B,Y,Object.getOwnPropertyDescriptor(J,Y))})}return B}function E(B,X,J){if(X=W(X),X in B)Object.defineProperty(B,X,{value:J,enumerable:!0,configurable:!0,writable:!0});else B[X]=J;return B}function W(B){var X=D(B,"string");return Q(X)=="symbol"?X:String(X)}function D(B,X){if(Q(B)!="object"||!B)return B;var J=B[Symbol.toPrimitive];if(J!==void 0){var Y=J.call(B,X||"default");if(Q(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(X==="string"?String:Number)(B)}var V=Object.defineProperty,XB=function B(X,J){for(var Y in J)V(X,Y,{get:J[Y],enumerable:!0,configurable:!0,set:function Z(G){return J[Y]=function(){return G}}})},x={lessThanXSeconds:{one:"moins d\u2019une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d\u2019une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d\u2019un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu\u2019un an",other:"presque {{count}} ans"}},N=function B(X,J,Y){var Z,G=x[X];if(typeof G==="string")Z=G;else if(J===1)Z=G.one;else Z=G.other.replace("{{count}}",String(J));if(Y!==null&&Y!==void 0&&Y.addSuffix)if(Y.comparison&&Y.comparison>0)return"dans "+Z;else return"il y a "+Z;return Z};function K(B){return function(){var X=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=X.width?String(X.width):B.defaultWidth,Y=B.formats[J]||B.formats[B.defaultWidth];return Y}}var R={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},S={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},M={full:"{{date}} '\xE0' {{time}}",long:"{{date}} '\xE0' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:K({formats:R,defaultWidth:"full"}),time:K({formats:S,defaultWidth:"full"}),dateTime:K({formats:M,defaultWidth:"full"})},j={lastWeek:"eeee 'dernier \xE0' p",yesterday:"'hier \xE0' p",today:"'aujourd\u2019hui \xE0' p",tomorrow:"'demain \xE0' p'",nextWeek:"eeee 'prochain \xE0' p",other:"P"},w=function B(X,J,Y,Z){return j[X]};function U(B){return function(X,J){var Y=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Z;if(Y==="formatting"&&B.formattingValues){var G=B.defaultFormattingWidth||B.defaultWidth,H=J!==null&&J!==void 0&&J.width?String(J.width):G;Z=B.formattingValues[H]||B.formattingValues[G]}else{var C=B.defaultWidth,A=J!==null&&J!==void 0&&J.width?String(J.width):B.defaultWidth;Z=B.values[A]||B.values[C]}var I=B.argumentCallback?B.argumentCallback(X):X;return Z[I]}}var _={narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant J\xE9sus-Christ","apr\xE8s J\xE9sus-Christ"]},F={narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2\xE8me trim.","3\xE8me trim.","4\xE8me trim."],wide:["1er trimestre","2\xE8me trimestre","3\xE8me trimestre","4\xE8me trimestre"]},P={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","f\xE9vr.","mars","avr.","mai","juin","juil.","ao\xFBt","sept.","oct.","nov.","d\xE9c."],wide:["janvier","f\xE9vrier","mars","avril","mai","juin","juillet","ao\xFBt","septembre","octobre","novembre","d\xE9cembre"]},v={narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},k={narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"apr\xE8s-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l\u2019apr\xE8s-midi",evening:"du soir",night:"du matin"}},f=function B(X,J){var Y=Number(X),Z=J===null||J===void 0?void 0:J.unit;if(Y===0)return"0";var G=["year","week","hour","minute","second"],H;if(Y===1)H=Z&&G.includes(Z)?"\xE8re":"er";else H="\xE8me";return Y+H},b=["MMM","MMMM"],h={preprocessor:function B(X,J){if(X.getDate()===1)return J;var Y=J.some(function(Z){return Z.isToken&&b.includes(Z.value)});if(!Y)return J;return J.map(function(Z){return Z.isToken&&Z.value==="do"?{isToken:!0,value:"d"}:Z})},ordinalNumber:f,era:U({values:_,defaultWidth:"wide"}),quarter:U({values:F,defaultWidth:"wide",argumentCallback:function B(X){return X-1}}),month:U({values:P,defaultWidth:"wide"}),day:U({values:v,defaultWidth:"wide"}),dayPeriod:U({values:k,defaultWidth:"wide"})};function q(B){return function(X){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=J.width,Z=Y&&B.matchPatterns[Y]||B.matchPatterns[B.defaultMatchWidth],G=X.match(Z);if(!G)return null;var H=G[0],C=Y&&B.parsePatterns[Y]||B.parsePatterns[B.defaultParseWidth],A=Array.isArray(C)?y(C,function(O){return O.test(H)}):m(C,function(O){return O.test(H)}),I;I=B.valueCallback?B.valueCallback(A):A,I=J.valueCallback?J.valueCallback(I):I;var JB=X.slice(H.length);return{value:I,rest:JB}}}function m(B,X){for(var J in B)if(Object.prototype.hasOwnProperty.call(B,J)&&X(B[J]))return J;return}function y(B,X){for(var J=0;J<B.length;J++)if(X(B[J]))return J;return}function c(B){return function(X){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=X.match(B.matchPattern);if(!Y)return null;var Z=Y[0],G=X.match(B.parsePattern);if(!G)return null;var H=B.valueCallback?B.valueCallback(G[0]):G[0];H=J.valueCallback?J.valueCallback(H):H;var C=X.slice(Z.length);return{value:H,rest:C}}}var g=/^(\d+)(ième|ère|ème|er|e)?/i,d=/\d+/i,u={narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},l={any:[/^av/i,/^ap/i]},p={narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},s={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},o={narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},e={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},r={narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},a={any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},t={ordinalNumber:c({matchPattern:g,parsePattern:d,valueCallback:function B(X){return parseInt(X)}}),era:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(X){return X+1}}),month:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},BB={code:"fr",formatDistance:N,formatLong:L,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=$($({},window.dateFns),{},{locale:$($({},(T=window.dateFns)===null||T===void 0?void 0:T.locale),{},{fr:BB})})})();

//# debugId=5F4D53CF00C30DA664756E2164756E21
