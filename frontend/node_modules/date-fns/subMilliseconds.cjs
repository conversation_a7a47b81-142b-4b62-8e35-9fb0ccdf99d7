"use strict";
exports.subMilliseconds = subMilliseconds;
var _index = require("./addMilliseconds.cjs");

/**
 * The {@link subMilliseconds} function options.
 */

/**
 * Subtract the specified number of milliseconds from the given date.
 *
 * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).
 * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.
 *
 * @param date - The date to be changed
 * @param amount - The amount of milliseconds to be subtracted.
 * @param options - An object with options
 *
 * @returns The new date with the milliseconds subtracted
 */
function subMilliseconds(date, amount, options) {
  return (0, _index.addMilliseconds)(date, -amount, options);
}
