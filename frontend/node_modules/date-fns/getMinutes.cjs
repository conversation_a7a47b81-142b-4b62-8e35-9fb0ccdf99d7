"use strict";
exports.getMinutes = getMinutes;
var _index = require("./toDate.cjs");

/**
 * The {@link getMinutes} function options.
 */

/**
 * @name getMinutes
 * @category Minute Helpers
 * @summary Get the minutes of the given date.
 *
 * @description
 * Get the minutes of the given date.
 *
 * @param date - The given date
 * @param options - The options
 *
 * @returns The minutes
 *
 * @example
 * // Get the minutes of 29 February 2012 11:45:05:
 * const result = getMinutes(new Date(2012, 1, 29, 11, 45, 5))
 * //=> 45
 */
function getMinutes(date, options) {
  return (0, _index.toDate)(date, options?.in).getMinutes();
}
