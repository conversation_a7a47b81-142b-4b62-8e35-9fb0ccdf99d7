/**
 * @license lucide-react v0.454.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const MonitorCog = createLucideIcon("MonitorCog", [
  ["path", { d: "M12 17v4", key: "1riwvh" }],
  ["path", { d: "m15.2 4.9-.9-.4", key: "12wd2u" }],
  ["path", { d: "m15.2 7.1-.9.4", key: "1r2vl7" }],
  ["path", { d: "m16.9 3.2-.4-.9", key: "3zbo91" }],
  ["path", { d: "m16.9 8.8-.4.9", key: "1qr2dn" }],
  ["path", { d: "m19.5 2.3-.4.9", key: "1rjrkq" }],
  ["path", { d: "m19.5 9.7-.4-.9", key: "heryx5" }],
  ["path", { d: "m21.7 4.5-.9.4", key: "17fqt1" }],
  ["path", { d: "m21.7 7.5-.9-.4", key: "14zyni" }],
  ["path", { d: "M22 13v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7", key: "1tnzv8" }],
  ["path", { d: "M8 21h8", key: "1ev6f3" }],
  ["circle", { cx: "18", cy: "6", r: "3", key: "1h7g24" }]
]);

export { MonitorCog as default };
//# sourceMappingURL=monitor-cog.js.map
