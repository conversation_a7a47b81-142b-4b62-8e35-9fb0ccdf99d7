/**
 * @license lucide-react v0.454.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const ChartBarIncreasing = createLucideIcon("ChartBarIncreasing", [
  ["path", { d: "M3 3v16a2 2 0 0 0 2 2h16", key: "c24i48" }],
  ["path", { d: "M7 11h8", key: "1feolt" }],
  ["path", { d: "M7 16h12", key: "wsnu98" }],
  ["path", { d: "M7 6h3", key: "w9rmul" }]
]);

export { ChartBarIncreasing as default };
//# sourceMappingURL=chart-bar-increasing.js.map
