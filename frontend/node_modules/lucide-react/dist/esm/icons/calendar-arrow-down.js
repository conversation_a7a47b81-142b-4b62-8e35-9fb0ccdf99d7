/**
 * @license lucide-react v0.454.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const CalendarArrowDown = createLucideIcon("CalendarArrowDown", [
  ["path", { d: "m14 18 4 4 4-4", key: "1waygx" }],
  ["path", { d: "M16 2v4", key: "4m81vk" }],
  ["path", { d: "M18 14v8", key: "irew45" }],
  [
    "path",
    { d: "M21 11.354V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7.343", key: "bse4f3" }
  ],
  ["path", { d: "M3 10h18", key: "8toen8" }],
  ["path", { d: "M8 2v4", key: "1cmpym" }]
]);

export { CalendarArrowDown as default };
//# sourceMappingURL=calendar-arrow-down.js.map
