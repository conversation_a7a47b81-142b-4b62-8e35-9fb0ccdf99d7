import activeElement from './activeElement';
import addClass from './addClass';
import addEventListener from './addEventListener';
import animate from './animate';
import { cancel as cancelAnimationFrame, request as requestAnimationFrame } from './animationFrame';
import attribute from './attribute';
import childElements from './childElements';
import clear from './clear';
import closest from './closest';
import contains from './contains';
import childNodes from './childNodes';
import style from './css';
import filter from './filterEventHandler';
import getComputedStyle from './getComputedStyle';
import hasClass from './hasClass';
import height from './height';
import insertAfter from './insertAfter';
import isInput from './isInput';
import isVisible from './isVisible';
import listen from './listen';
import matches from './matches';
import nextUntil from './nextUntil';
import offset from './offset';
import offsetParent from './offsetParent';
import ownerDocument from './ownerDocument';
import ownerWindow from './ownerWindow';
import parents from './parents';
import position from './position';
import prepend from './prepend';
import querySelectorAll from './querySelectorAll';
import remove from './remove';
import removeClass from './removeClass';
import removeEventListener from './removeEventListener';
import scrollbarSize from './scrollbarSize';
import scrollLeft from './scrollLeft';
import scrollParent from './scrollParent';
import scrollTo from './scrollTo';
import scrollTop from './scrollTop';
import siblings from './siblings';
import text from './text';
import toggleClass from './toggleClass';
import transitionEnd from './transitionEnd';
import triggerEvent from './triggerEvent';
import width from './width';
export { addEventListener, removeEventListener, triggerEvent, animate, filter, listen, style, getComputedStyle, attribute, activeElement, ownerDocument, ownerWindow, requestAnimationFrame, cancelAnimationFrame, matches, height, width, offset, offsetParent, position, contains, scrollbarSize, scrollLeft, scrollParent, scrollTo, scrollTop, querySelectorAll, closest, addClass, removeClass, hasClass, toggleClass, transitionEnd, childNodes, childElements, nextUntil, parents, siblings, clear, insertAfter, isInput, isVisible, prepend, remove, text, };
declare const _default: {
    addEventListener: typeof addEventListener;
    removeEventListener: typeof removeEventListener;
    triggerEvent: typeof triggerEvent;
    animate: typeof animate;
    filter: typeof filter;
    listen: typeof listen;
    style: typeof style;
    getComputedStyle: typeof getComputedStyle;
    attribute: typeof attribute;
    activeElement: typeof activeElement;
    ownerDocument: typeof ownerDocument;
    ownerWindow: typeof ownerWindow;
    requestAnimationFrame: typeof globalThis.requestAnimationFrame;
    cancelAnimationFrame: (id: number) => void;
    matches: typeof matches;
    height: typeof height;
    width: typeof width;
    offset: typeof offset;
    offsetParent: typeof offsetParent;
    position: typeof position;
    contains: typeof contains;
    scrollbarSize: typeof scrollbarSize;
    scrollLeft: {
        (node: Element): number;
        (node: Element, val: number): undefined;
    };
    scrollParent: typeof scrollParent;
    scrollTo: typeof scrollTo;
    scrollTop: {
        (node: Element): number;
        (node: Element, val: number): undefined;
    };
    querySelectorAll: typeof querySelectorAll;
    closest: typeof closest;
    addClass: typeof addClass;
    removeClass: typeof removeClass;
    hasClass: typeof hasClass;
    toggleClass: typeof toggleClass;
    transitionEnd: typeof transitionEnd;
    childNodes: typeof childNodes;
    childElements: typeof childElements;
    nextUntil: typeof nextUntil;
    parents: typeof parents;
    siblings: typeof siblings;
    clear: typeof clear;
    insertAfter: typeof insertAfter;
    isInput: typeof isInput;
    isVisible: typeof isVisible;
    prepend: typeof prepend;
    remove: typeof remove;
    text: typeof text;
};
export default _default;
