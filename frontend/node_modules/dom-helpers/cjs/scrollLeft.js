"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

exports.__esModule = true;
exports.default = void 0;

var _getScrollAccessor = _interopRequireDefault(require("./getScrollAccessor"));

/**
 * Gets or sets the scroll left position of a given element.
 * 
 * @param node the element
 * @param val the position to set
 */
var _default = (0, _getScrollAccessor.default)('pageXOffset');

exports.default = _default;
module.exports = exports["default"];