/*! For license information please see RechartsScale.min.js.LICENSE.txt */
(()=>{var r={499:(r,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getTickValuesFixedDomain=e.getTickValues=e.getNiceTickValues=void 0;var n=u(t(887)),i=t(380),o=u(t(189));function u(r){return r&&r.__esModule?r:{default:r}}function s(r){return function(r){if(Array.isArray(r))return c(r)}(r)||function(r){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(r))return Array.from(r)}(r)||f(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(r,e){return function(r){if(Array.isArray(r))return r}(r)||function(r,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(r)){var t=[],n=!0,i=!1,o=void 0;try{for(var u,s=r[Symbol.iterator]();!(n=(u=s.next()).done)&&(t.push(u.value),!e||t.length!==e);n=!0);}catch(r){i=!0,o=r}finally{try{n||null==s.return||s.return()}finally{if(i)throw o}}return t}}(r,e)||f(r,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(r,e){if(r){if("string"==typeof r)return c(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?c(r,e):void 0}}function c(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function l(r){var e=a(r,2),t=e[0],n=e[1],i=t,o=n;return t>n&&(i=n,o=t),[i,o]}function d(r,e,t){if(r.lte(0))return new n.default(0);var i=o.default.getDigitCount(r.toNumber()),u=new n.default(10).pow(i),s=r.div(u),a=1!==i?.05:.1,f=new n.default(Math.ceil(s.div(a).toNumber())).add(t).mul(a).mul(u);return e?f:new n.default(Math.ceil(f))}function h(r,e,t){var u=1,s=new n.default(r);if(!s.isint()&&t){var a=Math.abs(r);a<1?(u=new n.default(10).pow(o.default.getDigitCount(r)-1),s=new n.default(Math.floor(s.div(u).toNumber())).mul(u)):a>1&&(s=new n.default(Math.floor(r)))}else 0===r?s=new n.default(Math.floor((e-1)/2)):t||(s=new n.default(Math.floor(r)));var f=Math.floor((e-1)/2);return(0,i.compose)((0,i.map)((function(r){return s.add(new n.default(r-f).mul(u)).toNumber()})),i.range)(0,e)}function v(r,e,t,i){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-r)/(t-1)))return{step:new n.default(0),tickMin:new n.default(0),tickMax:new n.default(0)};var u,s=d(new n.default(e).sub(r).div(t-1),i,o);u=r<=0&&e>=0?new n.default(0):(u=new n.default(r).add(e).div(2)).sub(new n.default(u).mod(s));var a=Math.ceil(u.sub(r).div(s).toNumber()),f=Math.ceil(new n.default(e).sub(u).div(s).toNumber()),c=a+f+1;return c>t?v(r,e,t,i,o+1):(c<t&&(f=e>0?f+(t-c):f,a=e>0?a:a+(t-c)),{step:s,tickMin:u.sub(new n.default(a).mul(s)),tickMax:u.add(new n.default(f).mul(s))})}var p=(0,i.memoize)((function(r){var e=a(r,2),t=e[0],u=e[1],f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,c=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],d=Math.max(f,2),p=l([t,u]),g=a(p,2),m=g[0],w=g[1];if(m===-1/0||w===1/0){var y=w===1/0?[m].concat(s((0,i.range)(0,f-1).map((function(){return 1/0})))):[].concat(s((0,i.range)(0,f-1).map((function(){return-1/0}))),[w]);return t>u?(0,i.reverse)(y):y}if(m===w)return h(m,f,c);var b=v(m,w,d,c),N=b.step,E=b.tickMin,M=b.tickMax,x=o.default.rangeStep(E,M.add(new n.default(.1).mul(N)),N);return t>u?(0,i.reverse)(x):x}));e.getNiceTickValues=p;var g=(0,i.memoize)((function(r){var e=a(r,2),t=e[0],o=e[1],u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,s=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],f=Math.max(u,2),c=l([t,o]),v=a(c,2),p=v[0],g=v[1];if(p===-1/0||g===1/0)return[t,o];if(p===g)return h(p,u,s);var m=d(new n.default(g).sub(p).div(f-1),s,0),w=(0,i.compose)((0,i.map)((function(r){return new n.default(p).add(new n.default(r).mul(m)).toNumber()})),i.range),y=w(0,f).filter((function(r){return r>=p&&r<=g}));return t>o?(0,i.reverse)(y):y}));e.getTickValues=g;var m=(0,i.memoize)((function(r,e){var t=a(r,2),u=t[0],f=t[1],c=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],h=l([u,f]),v=a(h,2),p=v[0],g=v[1];if(p===-1/0||g===1/0)return[u,f];if(p===g)return[p];var m=Math.max(e,2),w=d(new n.default(g).sub(p).div(m-1),c,0),y=[].concat(s(o.default.rangeStep(new n.default(p),new n.default(g).sub(new n.default(.99).mul(w)),w)),[g]);return u>f?(0,i.reverse)(y):y}));e.getTickValuesFixedDomain=m},579:(r,e,t)=>{"use strict";t(499)},189:(r,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,i=(n=t(887))&&n.__esModule?n:{default:n},o=t(380),u={rangeStep:function(r,e,t){for(var n=new i.default(r),o=0,u=[];n.lt(e)&&o<1e5;)u.push(n.toNumber()),n=n.add(t),o++;return u},getDigitCount:function(r){return 0===r?1:Math.floor(new i.default(r).abs().log(10).toNumber())+1},interpolateNumber:(0,o.curry)((function(r,e,t){var n=+r;return n+t*(+e-n)})),uninterpolateNumber:(0,o.curry)((function(r,e,t){var n=e-+r;return(t-r)/(n||1/0)})),uninterpolateTruncation:(0,o.curry)((function(r,e,t){var n=e-+r;return n=n||1/0,Math.max(0,Math.min(1,(t-r)/n))}))};e.default=u},380:(r,e)=>{"use strict";function t(r){return function(r){if(Array.isArray(r))return n(r)}(r)||function(r){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(r))return Array.from(r)}(r)||function(r,e){if(r){if("string"==typeof r)return n(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?n(r,e):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}Object.defineProperty(e,"__esModule",{value:!0}),e.memoize=e.reverse=e.compose=e.map=e.range=e.curry=e.PLACE_HOLDER=void 0;var i=function(r){return r},o={"@@functional/placeholder":!0};e.PLACE_HOLDER=o;var u=function(r){return r===o},s=function(r){return function e(){return 0===arguments.length||1===arguments.length&&u(arguments.length<=0?void 0:arguments[0])?e:r.apply(void 0,arguments)}},a=function r(e,n){return 1===e?n:s((function(){for(var i=arguments.length,a=new Array(i),f=0;f<i;f++)a[f]=arguments[f];var c=a.filter((function(r){return r!==o})).length;return c>=e?n.apply(void 0,a):r(e-c,s((function(){for(var r=arguments.length,e=new Array(r),i=0;i<r;i++)e[i]=arguments[i];var o=a.map((function(r){return u(r)?e.shift():r}));return n.apply(void 0,t(o).concat(e))})))}))},f=function(r){return a(r.length,r)};e.curry=f,e.range=function(r,e){for(var t=[],n=r;n<e;++n)t[n-r]=n;return t};var c=f((function(r,e){return Array.isArray(e)?e.map(r):Object.keys(e).map((function(r){return e[r]})).map(r)}));e.map=c,e.compose=function(){for(var r=arguments.length,e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];if(!e.length)return i;var n=e.reverse(),o=n[0],u=n.slice(1);return function(){return u.reduce((function(r,e){return e(r)}),o.apply(void 0,arguments))}},e.reverse=function(r){return Array.isArray(r)?r.reverse():r.split("").reverse.join("")},e.memoize=function(r){var e=null,t=null;return function(){for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every((function(r,t){return r===e[t]}))?t:(e=i,t=r.apply(void 0,i))}}},887:function(r,e,t){var n;!function(i){"use strict";var o,u=1e9,s={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},a=!0,f="[DecimalError] ",c=f+"Invalid argument: ",l=f+"Exponent out of range: ",d=Math.floor,h=Math.pow,v=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,p=1e7,g=d(1286742750677284.5),m={};function w(r,e){var t,n,i,o,u,s,f,c,l=r.constructor,d=l.precision;if(!r.s||!e.s)return e.s||(e=new l(r)),a?D(e,d):e;if(f=r.d,c=e.d,u=r.e,i=e.e,f=f.slice(),o=u-i){for(o<0?(n=f,o=-o,s=c.length):(n=c,i=u,s=f.length),o>(s=(u=Math.ceil(d/7))>s?u+1:s+1)&&(o=s,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((s=f.length)-(o=c.length)<0&&(o=s,n=c,c=f,f=n),t=0;o;)t=(f[--o]=f[o]+c[o]+t)/p|0,f[o]%=p;for(t&&(f.unshift(t),++i),s=f.length;0==f[--s];)f.pop();return e.d=f,e.e=i,a?D(e,d):e}function y(r,e,t){if(r!==~~r||r<e||r>t)throw Error(c+r)}function b(r){var e,t,n,i=r.length-1,o="",u=r[0];if(i>0){for(o+=u,e=1;e<i;e++)(t=7-(n=r[e]+"").length)&&(o+=A(t)),o+=n;(t=7-(n=(u=r[e])+"").length)&&(o+=A(t))}else if(0===u)return"0";for(;u%10==0;)u/=10;return o+u}m.absoluteValue=m.abs=function(){var r=new this.constructor(this);return r.s&&(r.s=1),r},m.comparedTo=m.cmp=function(r){var e,t,n,i,o=this;if(r=new o.constructor(r),o.s!==r.s)return o.s||-r.s;if(o.e!==r.e)return o.e>r.e^o.s<0?1:-1;for(e=0,t=(n=o.d.length)<(i=r.d.length)?n:i;e<t;++e)if(o.d[e]!==r.d[e])return o.d[e]>r.d[e]^o.s<0?1:-1;return n===i?0:n>i^o.s<0?1:-1},m.decimalPlaces=m.dp=function(){var r=this,e=r.d.length-1,t=7*(e-r.e);if(e=r.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},m.dividedBy=m.div=function(r){return N(this,new this.constructor(r))},m.dividedToIntegerBy=m.idiv=function(r){var e=this.constructor;return D(N(this,new e(r),0,1),e.precision)},m.equals=m.eq=function(r){return!this.cmp(r)},m.exponent=function(){return M(this)},m.greaterThan=m.gt=function(r){return this.cmp(r)>0},m.greaterThanOrEqualTo=m.gte=function(r){return this.cmp(r)>=0},m.isInteger=m.isint=function(){return this.e>this.d.length-2},m.isNegative=m.isneg=function(){return this.s<0},m.isPositive=m.ispos=function(){return this.s>0},m.isZero=function(){return 0===this.s},m.lessThan=m.lt=function(r){return this.cmp(r)<0},m.lessThanOrEqualTo=m.lte=function(r){return this.cmp(r)<1},m.logarithm=m.log=function(r){var e,t=this,n=t.constructor,i=n.precision,u=i+5;if(void 0===r)r=new n(10);else if((r=new n(r)).s<1||r.eq(o))throw Error(f+"NaN");if(t.s<1)throw Error(f+(t.s?"NaN":"-Infinity"));return t.eq(o)?new n(0):(a=!1,e=N(O(t,u),O(r,u),u),a=!0,D(e,i))},m.minus=m.sub=function(r){var e=this;return r=new e.constructor(r),e.s==r.s?L(e,r):w(e,(r.s=-r.s,r))},m.modulo=m.mod=function(r){var e,t=this,n=t.constructor,i=n.precision;if(!(r=new n(r)).s)throw Error(f+"NaN");return t.s?(a=!1,e=N(t,r,0,1).times(r),a=!0,t.minus(e)):D(new n(t),i)},m.naturalExponential=m.exp=function(){return E(this)},m.naturalLogarithm=m.ln=function(){return O(this)},m.negated=m.neg=function(){var r=new this.constructor(this);return r.s=-r.s||0,r},m.plus=m.add=function(r){var e=this;return r=new e.constructor(r),e.s==r.s?w(e,r):L(e,(r.s=-r.s,r))},m.precision=m.sd=function(r){var e,t,n,i=this;if(void 0!==r&&r!==!!r&&1!==r&&0!==r)throw Error(c+r);if(e=M(i)+1,t=7*(n=i.d.length-1)+1,n=i.d[n]){for(;n%10==0;n/=10)t--;for(n=i.d[0];n>=10;n/=10)t++}return r&&e>t?e:t},m.squareRoot=m.sqrt=function(){var r,e,t,n,i,o,u,s=this,c=s.constructor;if(s.s<1){if(!s.s)return new c(0);throw Error(f+"NaN")}for(r=M(s),a=!1,0==(i=Math.sqrt(+s))||i==1/0?(((e=b(s.d)).length+r)%2==0&&(e+="0"),i=Math.sqrt(e),r=d((r+1)/2)-(r<0||r%2),n=new c(e=i==1/0?"5e"+r:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+r)):n=new c(i.toString()),i=u=(t=c.precision)+3;;)if(n=(o=n).plus(N(s,o,u+2)).times(.5),b(o.d).slice(0,u)===(e=b(n.d)).slice(0,u)){if(e=e.slice(u-3,u+1),i==u&&"4999"==e){if(D(o,t+1,0),o.times(o).eq(s)){n=o;break}}else if("9999"!=e)break;u+=4}return a=!0,D(n,t)},m.times=m.mul=function(r){var e,t,n,i,o,u,s,f,c,l=this,d=l.constructor,h=l.d,v=(r=new d(r)).d;if(!l.s||!r.s)return new d(0);for(r.s*=l.s,t=l.e+r.e,(f=h.length)<(c=v.length)&&(o=h,h=v,v=o,u=f,f=c,c=u),o=[],n=u=f+c;n--;)o.push(0);for(n=c;--n>=0;){for(e=0,i=f+n;i>n;)s=o[i]+v[n]*h[i-n-1]+e,o[i--]=s%p|0,e=s/p|0;o[i]=(o[i]+e)%p|0}for(;!o[--u];)o.pop();return e?++t:o.shift(),r.d=o,r.e=t,a?D(r,d.precision):r},m.toDecimalPlaces=m.todp=function(r,e){var t=this,n=t.constructor;return t=new n(t),void 0===r?t:(y(r,0,u),void 0===e?e=n.rounding:y(e,0,8),D(t,r+M(t)+1,e))},m.toExponential=function(r,e){var t,n=this,i=n.constructor;return void 0===r?t=S(n,!0):(y(r,0,u),void 0===e?e=i.rounding:y(e,0,8),t=S(n=D(new i(n),r+1,e),!0,r+1)),t},m.toFixed=function(r,e){var t,n,i=this,o=i.constructor;return void 0===r?S(i):(y(r,0,u),void 0===e?e=o.rounding:y(e,0,8),t=S((n=D(new o(i),r+M(i)+1,e)).abs(),!1,r+M(n)+1),i.isneg()&&!i.isZero()?"-"+t:t)},m.toInteger=m.toint=function(){var r=this,e=r.constructor;return D(new e(r),M(r)+1,e.rounding)},m.toNumber=function(){return+this},m.toPower=m.pow=function(r){var e,t,n,i,u,s,c=this,l=c.constructor,h=+(r=new l(r));if(!r.s)return new l(o);if(!(c=new l(c)).s){if(r.s<1)throw Error(f+"Infinity");return c}if(c.eq(o))return c;if(n=l.precision,r.eq(o))return D(c,n);if(s=(e=r.e)>=(t=r.d.length-1),u=c.s,s){if((t=h<0?-h:h)<=9007199254740991){for(i=new l(o),e=Math.ceil(n/7+4),a=!1;t%2&&k((i=i.times(c)).d,e),0!==(t=d(t/2));)k((c=c.times(c)).d,e);return a=!0,r.s<0?new l(o).div(i):D(i,n)}}else if(u<0)throw Error(f+"NaN");return u=u<0&&1&r.d[Math.max(e,t)]?-1:1,c.s=1,a=!1,i=r.times(O(c,n+12)),a=!0,(i=E(i)).s=u,i},m.toPrecision=function(r,e){var t,n,i=this,o=i.constructor;return void 0===r?n=S(i,(t=M(i))<=o.toExpNeg||t>=o.toExpPos):(y(r,1,u),void 0===e?e=o.rounding:y(e,0,8),n=S(i=D(new o(i),r,e),r<=(t=M(i))||t<=o.toExpNeg,r)),n},m.toSignificantDigits=m.tosd=function(r,e){var t=this.constructor;return void 0===r?(r=t.precision,e=t.rounding):(y(r,1,u),void 0===e?e=t.rounding:y(e,0,8)),D(new t(this),r,e)},m.toString=m.valueOf=m.val=m.toJSON=function(){var r=this,e=M(r),t=r.constructor;return S(r,e<=t.toExpNeg||e>=t.toExpPos)};var N=function(){function r(r,e){var t,n=0,i=r.length;for(r=r.slice();i--;)t=r[i]*e+n,r[i]=t%p|0,n=t/p|0;return n&&r.unshift(n),r}function e(r,e,t,n){var i,o;if(t!=n)o=t>n?1:-1;else for(i=o=0;i<t;i++)if(r[i]!=e[i]){o=r[i]>e[i]?1:-1;break}return o}function t(r,e,t){for(var n=0;t--;)r[t]-=n,n=r[t]<e[t]?1:0,r[t]=n*p+r[t]-e[t];for(;!r[0]&&r.length>1;)r.shift()}return function(n,i,o,u){var s,a,c,l,d,h,v,g,m,w,y,b,N,E,x,A,O,_,L=n.constructor,S=n.s==i.s?1:-1,k=n.d,P=i.d;if(!n.s)return new L(n);if(!i.s)throw Error(f+"Division by zero");for(a=n.e-i.e,O=P.length,x=k.length,g=(v=new L(S)).d=[],c=0;P[c]==(k[c]||0);)++c;if(P[c]>(k[c]||0)&&--a,(b=null==o?o=L.precision:u?o+(M(n)-M(i))+1:o)<0)return new L(0);if(b=b/7+2|0,c=0,1==O)for(l=0,P=P[0],b++;(c<x||l)&&b--;c++)N=l*p+(k[c]||0),g[c]=N/P|0,l=N%P|0;else{for((l=p/(P[0]+1)|0)>1&&(P=r(P,l),k=r(k,l),O=P.length,x=k.length),E=O,w=(m=k.slice(0,O)).length;w<O;)m[w++]=0;(_=P.slice()).unshift(0),A=P[0],P[1]>=p/2&&++A;do{l=0,(s=e(P,m,O,w))<0?(y=m[0],O!=w&&(y=y*p+(m[1]||0)),(l=y/A|0)>1?(l>=p&&(l=p-1),1==(s=e(d=r(P,l),m,h=d.length,w=m.length))&&(l--,t(d,O<h?_:P,h))):(0==l&&(s=l=1),d=P.slice()),(h=d.length)<w&&d.unshift(0),t(m,d,w),-1==s&&(s=e(P,m,O,w=m.length))<1&&(l++,t(m,O<w?_:P,w)),w=m.length):0===s&&(l++,m=[0]),g[c++]=l,s&&m[0]?m[w++]=k[E]||0:(m=[k[E]],w=1)}while((E++<x||void 0!==m[0])&&b--)}return g[0]||g.shift(),v.e=a,D(v,u?o+M(v)+1:o)}}();function E(r,e){var t,n,i,u,s,f=0,c=0,d=r.constructor,v=d.precision;if(M(r)>16)throw Error(l+M(r));if(!r.s)return new d(o);for(null==e?(a=!1,s=v):s=e,u=new d(.03125);r.abs().gte(.1);)r=r.times(u),c+=5;for(s+=Math.log(h(2,c))/Math.LN10*2+5|0,t=n=i=new d(o),d.precision=s;;){if(n=D(n.times(r),s),t=t.times(++f),b((u=i.plus(N(n,t,s))).d).slice(0,s)===b(i.d).slice(0,s)){for(;c--;)i=D(i.times(i),s);return d.precision=v,null==e?(a=!0,D(i,v)):i}i=u}}function M(r){for(var e=7*r.e,t=r.d[0];t>=10;t/=10)e++;return e}function x(r,e,t){if(e>r.LN10.sd())throw a=!0,t&&(r.precision=t),Error(f+"LN10 precision limit exceeded");return D(new r(r.LN10),e)}function A(r){for(var e="";r--;)e+="0";return e}function O(r,e){var t,n,i,u,s,c,l,d,h,v=1,p=r,g=p.d,m=p.constructor,w=m.precision;if(p.s<1)throw Error(f+(p.s?"NaN":"-Infinity"));if(p.eq(o))return new m(0);if(null==e?(a=!1,d=w):d=e,p.eq(10))return null==e&&(a=!0),x(m,d);if(d+=10,m.precision=d,n=(t=b(g)).charAt(0),u=M(p),!(Math.abs(u)<15e14))return l=x(m,d+2,w).times(u+""),p=O(new m(n+"."+t.slice(1)),d-10).plus(l),m.precision=w,null==e?(a=!0,D(p,w)):p;for(;n<7&&1!=n||1==n&&t.charAt(1)>3;)n=(t=b((p=p.times(r)).d)).charAt(0),v++;for(u=M(p),n>1?(p=new m("0."+t),u++):p=new m(n+"."+t.slice(1)),c=s=p=N(p.minus(o),p.plus(o),d),h=D(p.times(p),d),i=3;;){if(s=D(s.times(h),d),b((l=c.plus(N(s,new m(i),d))).d).slice(0,d)===b(c.d).slice(0,d))return c=c.times(2),0!==u&&(c=c.plus(x(m,d+2,w).times(u+""))),c=N(c,new m(v),d),m.precision=w,null==e?(a=!0,D(c,w)):c;c=l,i+=2}}function _(r,e){var t,n,i;for((t=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(t<0&&(t=n),t+=+e.slice(n+1),e=e.substring(0,n)):t<0&&(t=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t=t-n-1,r.e=d(t/7),r.d=[],n=(t+1)%7,t<0&&(n+=7),n<i){for(n&&r.d.push(+e.slice(0,n)),i-=7;n<i;)r.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(r.d.push(+e),a&&(r.e>g||r.e<-g))throw Error(l+t)}else r.s=0,r.e=0,r.d=[0];return r}function D(r,e,t){var n,i,o,u,s,f,c,v,m=r.d;for(u=1,o=m[0];o>=10;o/=10)u++;if((n=e-u)<0)n+=7,i=e,c=m[v=0];else{if((v=Math.ceil((n+1)/7))>=(o=m.length))return r;for(c=o=m[v],u=1;o>=10;o/=10)u++;i=(n%=7)-7+u}if(void 0!==t&&(s=c/(o=h(10,u-i-1))%10|0,f=e<0||void 0!==m[v+1]||c%o,f=t<4?(s||f)&&(0==t||t==(r.s<0?3:2)):s>5||5==s&&(4==t||f||6==t&&(n>0?i>0?c/h(10,u-i):0:m[v-1])%10&1||t==(r.s<0?8:7))),e<1||!m[0])return f?(o=M(r),m.length=1,e=e-o-1,m[0]=h(10,(7-e%7)%7),r.e=d(-e/7)||0):(m.length=1,m[0]=r.e=r.s=0),r;if(0==n?(m.length=v,o=1,v--):(m.length=v+1,o=h(10,7-n),m[v]=i>0?(c/h(10,u-i)%h(10,i)|0)*o:0),f)for(;;){if(0==v){(m[0]+=o)==p&&(m[0]=1,++r.e);break}if(m[v]+=o,m[v]!=p)break;m[v--]=0,o=1}for(n=m.length;0===m[--n];)m.pop();if(a&&(r.e>g||r.e<-g))throw Error(l+M(r));return r}function L(r,e){var t,n,i,o,u,s,f,c,l,d,h=r.constructor,v=h.precision;if(!r.s||!e.s)return e.s?e.s=-e.s:e=new h(r),a?D(e,v):e;if(f=r.d,d=e.d,n=e.e,c=r.e,f=f.slice(),u=c-n){for((l=u<0)?(t=f,u=-u,s=d.length):(t=d,n=c,s=f.length),u>(i=Math.max(Math.ceil(v/7),s)+2)&&(u=i,t.length=1),t.reverse(),i=u;i--;)t.push(0);t.reverse()}else{for((l=(i=f.length)<(s=d.length))&&(s=i),i=0;i<s;i++)if(f[i]!=d[i]){l=f[i]<d[i];break}u=0}for(l&&(t=f,f=d,d=t,e.s=-e.s),s=f.length,i=d.length-s;i>0;--i)f[s++]=0;for(i=d.length;i>u;){if(f[--i]<d[i]){for(o=i;o&&0===f[--o];)f[o]=p-1;--f[o],f[i]+=p}f[i]-=d[i]}for(;0===f[--s];)f.pop();for(;0===f[0];f.shift())--n;return f[0]?(e.d=f,e.e=n,a?D(e,v):e):new h(0)}function S(r,e,t){var n,i=M(r),o=b(r.d),u=o.length;return e?(t&&(n=t-u)>0?o=o.charAt(0)+"."+o.slice(1)+A(n):u>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+A(-i-1)+o,t&&(n=t-u)>0&&(o+=A(n))):i>=u?(o+=A(i+1-u),t&&(n=t-i-1)>0&&(o=o+"."+A(n))):((n=i+1)<u&&(o=o.slice(0,n)+"."+o.slice(n)),t&&(n=t-u)>0&&(i+1===u&&(o+="."),o+=A(n))),r.s<0?"-"+o:o}function k(r,e){if(r.length>e)return r.length=e,!0}function P(r){if(!r||"object"!=typeof r)throw Error(f+"Object expected");var e,t,n,i=["precision",1,u,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=r[t=i[e]])){if(!(d(n)===n&&n>=i[e+1]&&n<=i[e+2]))throw Error(c+t+": "+n);this[t]=n}if(void 0!==(n=r[t="LN10"])){if(n!=Math.LN10)throw Error(c+t+": "+n);this[t]=new this(n)}return this}(s=function r(e){var t,n,i;function o(r){var e=this;if(!(e instanceof o))return new o(r);if(e.constructor=o,r instanceof o)return e.s=r.s,e.e=r.e,void(e.d=(r=r.d)?r.slice():r);if("number"==typeof r){if(0*r!=0)throw Error(c+r);if(r>0)e.s=1;else{if(!(r<0))return e.s=0,e.e=0,void(e.d=[0]);r=-r,e.s=-1}return r===~~r&&r<1e7?(e.e=0,void(e.d=[r])):_(e,r.toString())}if("string"!=typeof r)throw Error(c+r);if(45===r.charCodeAt(0)?(r=r.slice(1),e.s=-1):e.s=1,!v.test(r))throw Error(c+r);_(e,r)}if(o.prototype=m,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=r,o.config=o.set=P,void 0===e&&(e={}),e)for(i=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<i.length;)e.hasOwnProperty(n=i[t++])||(e[n]=this[n]);return o.config(e),o}(s)).default=s.Decimal=s,o=new s(1),void 0===(n=function(){return s}.call(e,t,e,r))||(r.exports=n)}()}},e={};!function t(n){if(e[n])return e[n].exports;var i=e[n]={exports:{}};return r[n].call(i.exports,i,i.exports,t),i.exports}(579)})();