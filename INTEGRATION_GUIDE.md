# Frontend-Backend Integration Guide

## ✅ **Integration Complete!**

The frontend has been successfully integrated with the real backend API. Here's what has been implemented:

### **🔗 API Integration**

**1. API Client (`lib/api-client.ts`)**
- **Axios-based HTTP client** with automatic token management
- **Request/Response interceptors** for authentication and error handling
- **TypeScript interfaces** matching backend models
- **Automatic token refresh** and logout on 401 errors
- **Comprehensive CRUD operations** for all entities

**2. Real-time Service (`lib/realtime-service.ts`)**
- **Socket.io client** with Redis adapter support
- **Role-based namespaces** (student, lecturer, admin, finance, registrar)
- **Automatic reconnection** with exponential backoff
- **Event filtering** based on user permissions
- **Custom event handling** for different data types

**3. React Hooks (`lib/hooks.ts`)**
- **Custom hooks** for all API operations
- **Loading states** and error handling
- **Pagination support** for list endpoints
- **Real-time event hooks** for live updates
- **Optimized re-renders** with proper dependencies

### **🔄 Data Flow**

**Authentication Flow:**
```
Frontend Login → API Client → Backend Auth → JWT Tokens → Local Storage → Socket Connection
```

**Real-time Updates:**
```
Backend Event → Redis → Socket.io → Frontend → Custom Events → React Components
```

**CRUD Operations:**
```
React Component → Custom Hook → API Client → Backend API → Database → Response → UI Update
```

### **📊 Available API Endpoints**

**Authentication:**
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh` - Token refresh
- `POST /api/auth/logout` - User logout

**Users:**
- `GET /api/users` - List users (Admin)
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create user (Admin)
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (Admin)
- `GET /api/users/profile` - Get current user profile
- `PUT /api/users/profile` - Update profile

**Courses:**
- `GET /api/courses` - List courses
- `GET /api/courses/:id` - Get course details
- `POST /api/courses` - Create course (Lecturer/Admin)
- `PUT /api/courses/:id` - Update course
- `DELETE /api/courses/:id` - Delete course (Admin)
- `POST /api/courses/:id/enroll` - Enroll in course (Student)
- `DELETE /api/courses/:id/enroll` - Drop course (Student)

**Grades:**
- `GET /api/grades` - List grades
- `GET /api/grades/:id` - Get grade details
- `POST /api/grades` - Create grade (Lecturer/Admin)
- `PUT /api/grades/:id` - Update grade
- `DELETE /api/grades/:id` - Delete grade
- `GET /api/grades/course/:courseId` - Get course grades
- `GET /api/grades/student/:studentId` - Get student grades

**Assignments:**
- `GET /api/assignments` - List assignments
- `GET /api/assignments/:id` - Get assignment details
- `POST /api/assignments` - Create assignment (Lecturer/Admin)
- `PUT /api/assignments/:id` - Update assignment
- `DELETE /api/assignments/:id` - Delete assignment
- `POST /api/assignments/:id/submit` - Submit assignment (Student)
- `PUT /api/assignments/:id/grade` - Grade assignment (Lecturer/Admin)

**Attendance:**
- `GET /api/attendance` - List attendance records
- `GET /api/attendance/:id` - Get attendance details
- `POST /api/attendance` - Mark attendance (Lecturer/Admin)
- `PUT /api/attendance/:id` - Update attendance
- `DELETE /api/attendance/:id` - Delete attendance
- `GET /api/attendance/session/:sessionId` - Get session attendance
- `GET /api/attendance/student/:studentId` - Get student attendance
- `POST /api/attendance/bulk` - Bulk mark attendance

**Notifications:**
- `GET /api/notifications` - List notifications
- `GET /api/notifications/unread` - Get unread count
- `GET /api/notifications/:id` - Get notification details
- `POST /api/notifications` - Create notification (Admin/Lecturer)
- `PUT /api/notifications/:id/read` - Mark as read
- `PUT /api/notifications/read-all` - Mark all as read
- `DELETE /api/notifications/:id` - Delete notification

**Admin:**
- `GET /api/admin/dashboard` - Admin dashboard statistics
- `GET /api/admin/users` - Admin user management
- `GET /api/admin/courses` - Admin course management
- `GET /api/admin/departments` - Admin department management
- `GET /api/admin/finance` - Financial overview
- `GET /api/admin/reports` - Various reports
- `POST /api/admin/broadcast` - System-wide broadcast
- `GET /api/admin/system-health` - System health status

### **⚡ Real-time Events**

**User Events:**
- `user_created`, `user_updated`, `user_deleted`, `profile_updated`

**Course Events:**
- `course_created`, `course_updated`, `course_enrolled`, `course_dropped`

**Grade Events:**
- `grade_created`, `grade_updated`, `grade_deleted`

**Assignment Events:**
- `assignment_created`, `assignment_submitted`, `assignment_graded`

**Attendance Events:**
- `attendance_marked`, `attendance_updated`

**Notification Events:**
- `notification_created`, `notification_read`

### **🎯 Usage Examples**

**1. Login with Real API:**
```typescript
import { useAuth } from '@/lib/hooks';

function LoginPage() {
  const { login, loading, error } = useAuth();
  
  const handleLogin = async (email: string, password: string) => {
    try {
      await login(email, password);
      // User is automatically redirected based on role
    } catch (err) {
      console.error('Login failed:', err);
    }
  };
}
```

**2. Fetch Courses with Pagination:**
```typescript
import { useCourses } from '@/lib/hooks';

function CoursesPage() {
  const { data: courses, pagination, loading, error, updateParams } = useCourses({
    page: 1,
    limit: 20,
    department: 'Computer Science'
  });
  
  const handlePageChange = (page: number) => {
    updateParams({ page });
  };
}
```

**3. Real-time Notifications:**
```typescript
import { useRealtimeEvents } from '@/lib/hooks';

function Dashboard() {
  const events = useRealtimeEvents();
  
  useEffect(() => {
    // Events are automatically updated in real-time
    console.log('Latest events:', events);
  }, [events]);
}
```

**4. Submit Assignment:**
```typescript
import { useAssignmentSubmission } from '@/lib/hooks';

function AssignmentPage({ assignmentId }: { assignmentId: string }) {
  const { submit, loading, error } = useAssignmentSubmission(assignmentId);
  
  const handleSubmit = async (files: File[], comments?: string) => {
    try {
      await submit(files, comments);
      // Assignment submitted successfully
    } catch (err) {
      console.error('Submission failed:', err);
    }
  };
}
```

### **🚀 Getting Started**

**1. Start the Backend:**
```bash
cd backend
npm install
cp env.example .env
# Edit .env with your configuration
npm run docker:dev
```

**2. Start the Frontend:**
```bash
cd frontend
npm install
cp env.example .env.local
# Edit .env.local with your configuration
npm run dev
```

**3. Access the Application:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- Health Check: http://localhost:3001/health

### **🔧 Configuration**

**Environment Variables:**
```env
# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
NEXT_PUBLIC_ENABLE_REALTIME=true

# Backend (.env)
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/university_portal
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key
```

### **📱 Features Available**

**✅ Real-time Features:**
- Live grade updates
- Instant notifications
- Real-time attendance tracking
- Live course enrollment updates
- System announcements

**✅ CRUD Operations:**
- Full user management
- Course enrollment system
- Grade management
- Assignment submission and grading
- Attendance tracking
- Notification system

**✅ Security Features:**
- JWT authentication
- Role-based access control
- MFA support for staff
- Rate limiting
- Input validation

**✅ Performance Features:**
- Pagination for all lists
- Caching with Redis
- Optimized database queries
- Real-time updates
- Error handling

The frontend is now fully integrated with the backend API and ready for production use! 🎉
