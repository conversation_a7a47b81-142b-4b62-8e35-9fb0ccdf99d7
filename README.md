# University Portal

A comprehensive university management system with a modern React frontend and high-performance Node.js backend. This system provides role-based access for students, lecturers, and administrators with real-time features and enterprise-grade scalability.

## 🏗️ Project Structure

```
university-portal/
├── frontend/          # Next.js React application
│   ├── app/          # Next.js App Router pages
│   ├── components/   # Reusable React components
│   ├── lib/         # Utilities and stores
│   ├── hooks/       # Custom React hooks
│   ├── public/      # Static assets
│   └── styles/      # Global styles
├── backend/          # Node.js API server
│   ├── src/         # Source code
│   ├── k8s/         # Kubernetes manifests
│   ├── helm/        # Helm charts
│   ├── load-tests/  # Performance testing
│   └── docker-compose.yml
└── README.md        # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js 20+
- pnpm (recommended) or npm
- Docker & Docker Compose
- MongoDB 7+
- Redis 7+

### Development Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd university-portal
```

2. **Start the backend**
```bash
cd backend
cp env.example .env
# Edit .env with your configuration
npm install
npm run docker:dev
```

3. **Start the frontend**
```bash
cd ../frontend
npm install
npm run dev
```

4. **Access the application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- Health Check: http://localhost:3001/health

## 📱 Frontend (Next.js)

The frontend is a modern React application built with Next.js 15, TypeScript, and Tailwind CSS.

### Features
- **Role-based dashboards** for students, lecturers, and admins
- **Real-time notifications** and updates
- **Progressive Web App** (PWA) support
- **Dark/Light theme** toggle
- **Responsive design** with mobile-first approach
- **Modern UI components** with shadcn/ui

### Tech Stack
- Next.js 15 with App Router
- React 19 with TypeScript
- Tailwind CSS 4.1.9
- shadcn/ui component library
- Zustand for state management
- React Hook Form with Zod validation

### Getting Started
```bash
cd frontend
npm install
npm run dev
```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests

## 🔧 Backend (Node.js)

The backend is a high-performance API server designed to handle 25,000 concurrent students with p95 latency < 200ms.

### Features
- **High Performance**: 5k RPS with p95 latency < 200ms
- **Scalable Architecture**: MongoDB sharding + Redis clustering
- **Security First**: JWT authentication, MFA, rate limiting
- **Real-time Features**: Socket.io with Redis adapter
- **Comprehensive Monitoring**: Prometheus + Jaeger + logging
- **Production Ready**: Docker + Kubernetes + Helm

### Tech Stack
- Node.js 20 LTS with Express 4
- MongoDB 7 sharded cluster
- Redis 7 cluster
- JWT authentication with MFA
- Socket.io for real-time features
- Prometheus + Jaeger monitoring

### Getting Started
```bash
cd backend
cp env.example .env
# Edit .env with your configuration
npm install
npm run docker:dev
```

### Available Scripts
- `npm run dev` - Start development server
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run load:test` - Run load tests
- `npm run docker:dev` - Start with Docker Compose
- `npm run docker:prod` - Start production with Docker Compose

## 🔐 Authentication

The system supports role-based authentication with the following roles:

- **Student**: Access to courses, grades, assignments, timetable
- **Lecturer**: Course management, attendance, grading, schedule
- **Admin**: User management, system administration, reports
- **Finance**: Financial management and reporting
- **Registrar**: Academic administration

### Login Credentials (Development)
- Student: `<EMAIL>` / `password123`
- Lecturer: `<EMAIL>` / `password123`
- Admin: `<EMAIL>` / `password123`

## 🚀 Deployment

### Docker Deployment
```bash
# Development
cd backend && npm run docker:dev
cd frontend && npm run dev

# Production
cd backend && npm run docker:prod
cd frontend && npm run build && npm start
```

### Kubernetes Deployment
```bash
# Apply Kubernetes manifests
kubectl apply -f backend/k8s/

# Or use Helm
helm install university-portal-api backend/helm/university-portal-api
```

## 📊 Performance

### Load Testing
```bash
cd backend
npm run load:test
```

### Performance Targets
- **Concurrent Users**: 25,000 students
- **Throughput**: 5,000 RPS peak
- **Latency**: p95 < 200ms
- **Availability**: Zero downtime deployments

## 🧪 Testing

### Frontend Testing
```bash
cd frontend
npm test
npm run test:coverage
```

### Backend Testing
```bash
cd backend
npm test
npm run test:coverage
```

### Load Testing
```bash
cd backend
npm run load:test
```

## 📚 Documentation

- **Frontend**: [frontend/README.md](frontend/README.md)
- **Backend**: [backend/README.md](backend/README.md)
- **API Documentation**: Available at `/api/docs` when running

## 🔧 Configuration

### Environment Variables

#### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
```

#### Backend (.env)
```env
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/university_portal
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
```

## 🛠️ Development

### Project Structure
- **Frontend**: Modern React app with Next.js App Router
- **Backend**: Microservices-ready Node.js API
- **Database**: MongoDB with sharding for scalability
- **Cache**: Redis cluster for performance
- **Monitoring**: Prometheus + Grafana + Jaeger

### Adding Features
1. **Frontend**: Add pages in `frontend/app/`, components in `frontend/components/`
2. **Backend**: Add routes in `backend/src/routes/`, models in `backend/src/models/`
3. **Database**: Update models and run migrations
4. **Testing**: Add tests for new features
5. **Documentation**: Update README files

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: GitHub Issues
- **Discussions**: GitHub Discussions
- **Email**: <EMAIL>
- **Documentation**: [docs.university-portal.com](https://docs.university-portal.com)

## 🏆 Features

### Frontend Features
- ✅ Role-based dashboards (Student, Lecturer, Admin)
- ✅ Real-time notifications
- ✅ Progressive Web App (PWA)
- ✅ Dark/Light theme support
- ✅ Responsive design
- ✅ Modern UI with shadcn/ui
- ✅ Form validation with Zod
- ✅ State management with Zustand

### Backend Features
- ✅ High-performance API (5k RPS, p95 < 200ms)
- ✅ MongoDB sharding for scalability
- ✅ Redis clustering for caching
- ✅ JWT authentication with MFA
- ✅ Real-time features with Socket.io
- ✅ Comprehensive monitoring
- ✅ Docker + Kubernetes deployment
- ✅ Load testing with k6

---

Built with ❤️ by the University Portal Team
